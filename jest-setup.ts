import {jest} from '@jest/globals';

// Mock fetch globally for tests
global.fetch = jest.fn();

/**
 * To fix: @link https://stackoverflow.com/questions/73432367/skip-import-react-from-react-from-test-file-using-jest
 */
import React from 'react';

global.React = React;

/**
 * react-native-gesture-handler
 */
import 'react-native-gesture-handler/jestSetup';

/**
 * react-native-unistyles
 */
import 'mocks/unistyles';

jest.mock('react-native/Libraries/EventEmitter/NativeEventEmitter');

// Setup MSW server for API mocking
import {server} from './__mocks__/msw-node';

afterEach(() => {
  server.resetHandlers();
});

afterAll(() => {
  server.close();
});

// Global test refs for UI components
declare global {
  var _MSB_TEST_POPUP: any;
  var _MSB_TEST_TOAST: any;
  var _MSB_TEST_BOTTOM_SHEET: any;
}

global._MSB_TEST_POPUP = {
  current: {
    show: jest.fn(),
    hide: jest.fn(),
  },
};

global._MSB_TEST_TOAST = {
  current: {
    show: jest.fn(),
    hide: jest.fn(),
  },
};

global._MSB_TEST_BOTTOM_SHEET = {
  current: {
    show: jest.fn(),
    hide: jest.fn(),
    snapToIndex: jest.fn(),
    close: jest.fn(),
  },
};

/**
 * Configure testing-library
 */
import {configure as configureReactNative} from '@testing-library/react-native';
configureReactNative({
  asyncUtilTimeout: 60000,
  defaultIncludeHiddenElements: false,
  concurrentRoot: true,
});

/**
 * Configure react-native-reanimated
 */
import {setUpTests} from 'react-native-reanimated';
setUpTests();

jest.mock('react-native-reanimated', () => {
  const originalModule: any = jest.requireActual('react-native-reanimated');

  return {
    __esModule: true,
    ...originalModule,
    /**
     * Mock for custom scrollable view from react-native-reanimated
     * Ex: Animated.FlatList
     */
    useAnimatedScrollHandler: jest.fn(),
  };
});

/**
 * Mock @gorhom/bottom-sheet
 */
jest.mock('@gorhom/bottom-sheet', () => ({
  __esModule: true,
  ...require('@gorhom/bottom-sheet/mock'),
}));

/**
 * Mock react-native-safe-area-context
 */
jest.mock('react-native-safe-area-context', () => {
  const inset = {top: 0, right: 0, bottom: 0, left: 0};
  const originalModule: any = jest.requireActual('react-native-safe-area-context');
  return {
    __esModule: true,
    ...originalModule,
    SafeAreaProvider: jest.fn(({children}) => children),
    SafeAreaConsumer: jest.fn(({children}) => children(inset)),
    useSafeAreaInsets: jest.fn(() => inset),
    useSafeAreaFrame: jest.fn(() => ({x: 0, y: 0, width: 390, height: 844})),
  };
});

/**
 * Mock react-native-async-storage
 */
jest.mock('@react-native-async-storage/async-storage', () =>
  require('@react-native-async-storage/async-storage/jest/async-storage-mock'),
);

/**
 * Mock clipboard
 */
// @ts-ignore
import mockClipboard from '@react-native-clipboard/clipboard/jest/clipboard-mock';

jest.mock('@react-native-clipboard/clipboard', () => mockClipboard);

jest.mock('react-native-linear-gradient', () => 'LinearGradient');

/**
 * Mock react-native-snap-carousel
 */
jest.mock('react-native-snap-carousel', () => {
  const {View} = require('react-native');
  return {
    __esModule: true,
    default: View,
    Carousel: View,
  };
});

/**
 * HTTP requests are now handled by MSW server
 */

beforeAll(() => {
  // Setup MSW server
  server.listen({
    onUnhandledRequest: 'warn',
  });

  // Setup Jest timers
  jest.useFakeTimers();
  jest.spyOn(global, 'setTimeout');
});

afterEach(() => {
  jest.clearAllMocks();
});

afterAll(() => {
  jest.useRealTimers();
});

/**
 * !Important: Need to mock from setup file for jest instead from mockup module from <rootDir>/__mocks__/*
 */
jest.mock('zustand', () => {
  return jest.requireActual('mocks/zustand');
});

jest.mock('react-native-webview', () => {
  const {View} = require('react-native');
  return {
    __esModule: true,
    default: View,
    WebView: View,
  };
});

/**
 * Repack
 */
jest.mock('@callstack/repack/client', () => ({
  Federated: jest.fn(),
}));

/**
 * Mock react-native-pdf
 */
jest.mock('react-native-pdf', () => {
  const {View} = require('react-native');
  return {
    __esModule: true,
    default: View,
    Pdf: View,
  };
});

/**
 * Mock react-native-animatable
 */
jest.mock('react-native-animatable', () => {
  const {View} = require('react-native');
  const React = require('react');

  return {
    __esModule: true,
    View: React.forwardRef((props: any, ref: any) => React.createElement(View, {...props, ref}, props.children)),
  };
});

/**
 * Mock react-native-modal
 */
jest.mock('react-native-modal', () => {
  const {View} = require('react-native');
  const React = require('react');

  return {
    __esModule: true,
    default: React.forwardRef((props: any, ref: any) => {
      if (!props.isVisible) return null;
      return React.createElement(View, {...props, ref}, props.children);
    }),
  };
});

jest.mock('react-native-blob-util', () => {
  return {
    __esModule: true,
    default: {
      DocumentDir: jest.fn(),
      config: jest.fn(() => ({
        fetch: jest.fn(() => ({
          progress: jest.fn<() => Promise<boolean>>().mockResolvedValue(true),
        })),
      })),
      fs: {
        cp: jest.fn<() => Promise<boolean>>().mockResolvedValue(true),
        dirs: {
          CacheDir: '/mockCacheDir',
        },
        unlink: jest.fn(),
      },
    },
  };
});

/**
 * Mock msb-shared-component
 */
jest.mock('msb-shared-component', () => {
  const {View, Text, TouchableOpacity} = require('react-native');
  const React = require('react');

  return {
    __esModule: true,
    MSBTextBase: React.forwardRef((props: any, ref: any) =>
      React.createElement(Text, {...props, ref}, props.content || props.children),
    ),
    MSBIcon: (props: any) => React.createElement(View, props),
    MSBIconSize: {
      Small: 16,
      Medium: 24,
      Large: 32,
      SIZE_24: 24,
    },
    MSBIcons: {
      IconDeleteCancelClose: 'icon-delete-cancel-close',
      IconCheck: 'icon-check',
      IconClose: 'icon-close',
      IconArrowLeft: 'icon-arrow-left',
      IconArrowRight: 'icon-arrow-right',
    },
    MSBButton: React.forwardRef((props: any, ref: any) =>
      React.createElement(TouchableOpacity, {...props, ref}, props.children),
    ),
    MSBGroupButton: (props: any) =>
      React.createElement(View, props, [
        React.createElement(
          TouchableOpacity,
          {
            key: 'cancel',
            testID: props.testIDClose,
            onPress: props.onClose,
          },
          React.createElement(Text, {}, props.cancelBtnText || 'Cancel'),
        ),
        React.createElement(
          TouchableOpacity,
          {
            key: 'confirm',
            testID: props.testIDConfirm,
            onPress: props.onConfirm,
          },
          React.createElement(Text, {}, props.confirmBtnText || 'Confirm'),
        ),
      ]),
    currentTheme: jest.fn((colorTheme: any) => ({
      Typography: {
        base_regular: {fontSize: 16, fontWeight: '400'},
        title_bold: {fontSize: 18, fontWeight: 'bold'},
      },
      ColorGlobal: {
        NeutralWhite: '#FFFFFF',
        NeutralGray300: '#D1D5DB',
        NeutralGray700: '#374151',
        NeutralGray800: '#1F2937',
        NeutralGray900: '#111827',
        Neutral800: '#1F2937',
        ...colorTheme,
      },
      SizeGlobal: {
        Small: 16,
        Medium: 24,
        Large: 32,
      },
    })),
    defaultColor: {
      Primary: '#007AFF',
      Secondary: '#5856D6',
    },
    themeMFirst: {
      Primary: '#FF3B30',
      Secondary: '#FF9500',
    },
    useMSBStyles: jest.fn((styleSheet: any) => ({
      styles: typeof styleSheet === 'function' ? styleSheet() : styleSheet,
      theme: {
        Typography: {
          base_regular: {fontSize: 16, fontWeight: '400'},
          title_bold: {fontSize: 18, fontWeight: 'bold'},
        },
        ColorGlobal: {
          NeutralWhite: '#FFFFFF',
          NeutralGray300: '#D1D5DB',
          NeutralGray700: '#374151',
          NeutralGray800: '#1F2937',
          NeutralGray900: '#111827',
          SemanticError: '#EF4444',
          SemanticSuccess: '#10B981',
        },
        SizeGlobal: {
          Size100: 4,
          Size200: 8,
          Size300: 12,
          Size400: 16,
          Size2400: 96,
        },
      },
    })),
    createMSBStyleSheet: jest.fn((styleFunction: any) => styleFunction),
  };
});
