{"name": "PaymentModule", "version": "0.0.2", "private": true, "scripts": {"gen:msbtemplate": "node scripts/MSBTemplate.mjs", "lint": "eslint .", "type:check": "yarn tsc --noEmit --skipLib<PERSON><PERSON>ck", "test": "jest --verbose --coverage", "test:watch": "jest --watch --verbose", "test:silent": "jest --silent --coverage", "test:detailed": "jest --verbose --coverage --no-cache", "test:coverage": "jest --coverage --watchAll=false", "test:ci": "jest --coverage --watchAll=false --passWithNoTests", "test:debug": "jest --verbose", "test:clear-cache": "jest --clear<PERSON>ache", "start": "react-native start --reset-cache --port 9400 --config rspack.local.config.mjs", "start:dev": "ENV=dev react-native start --reset-cache --port 9400 --config rspack.local.config.mjs", "start:sit": "ENV=sit react-native start --reset-cache --port 9400 --config rspack.local.config.mjs", "start:uat": "ENV=uat react-native start --reset-cache --port 9400 --config rspack.local.config.mjs", "start:pilot": "ENV=pilot react-native start --reset-cache --port 9400 --config rspack.local.config.mjs", "start:v1": "react-native start --reset-cache --port 9400", "start:dev:v1": "ENV=dev react-native start --reset-cache --port 9400", "start:sit:v1": "ENV=sit react-native start --reset-cache --port 9400", "start:uat:v1": "ENV=uat react-native start --reset-cache --port 9400", "start:pilot:v1": "ENV=pilot react-native start --reset-cache --port 9400", "bundle:ios": "react-native bundle --reset-cache --platform ios --entry-file index.js --dev false --minify false", "bundle:android": "react-native bundle --reset-cache --platform android --entry-file index.js --dev false --minify false", "bundle:ios:dev": "ENV=dev react-native bundle --reset-cache --platform ios --entry-file index.js --dev false --minify false", "bundle:android:dev": "ENV=dev react-native bundle --reset-cache --platform android --entry-file index.js --dev false --minify false", "bundle:ios:sit": "ENV=sit react-native bundle --reset-cache --platform ios --entry-file index.js --dev false --minify false", "bundle:android:sit": "ENV=sit react-native bundle --reset-cache --platform android --entry-file index.js --dev false --minify false", "bundle:ios:uat": "ENV=uat react-native bundle --reset-cache --platform ios --entry-file index.js --dev false --minify false", "bundle:android:uat": "ENV=uat react-native bundle --reset-cache --platform android --entry-file index.js --dev false --minify false", "bundle:ios:pilot": "ENV=pilot react-native bundle --reset-cache --platform ios --entry-file index.js --dev false --minify false", "bundle:android:pilot": "ENV=pilot react-native bundle --reset-cache --platform android --entry-file index.js --dev false --minify false", "bundle:ios:prod": "ENV=prod react-native bundle --reset-cache --platform ios --entry-file index.js --dev false --minify false", "bundle:android:prod": "ENV=prod react-native bundle --reset-cache --platform android --entry-file index.js --dev false --minify false", "build": "yarn bundle:ios && yarn bundle:android", "build:dev": "yarn bundle:ios:dev && yarn bundle:android:dev", "build:sit": "yarn bundle:ios:sit && yarn bundle:android:sit", "build:uat": "yarn bundle:ios:uat && yarn bundle:android:uat", "build:pilot": "yarn bundle:ios:pilot && yarn bundle:android:pilot", "build:prod": "yarn bundle:ios:prod && yarn bundle:android:prod", "align-deps": "rnx-align-deps --write", "check-deps": "rnx-align-deps", "format": "npx prettier --write \"src/**/*.{ts,tsx}\"", "i": "yarn && yarn align-deps && yarn && yarn check-deps", "prepare": "husky"}, "dependencies": {"@d11/react-native-fast-image": "8.9.2", "@gorhom/bottom-sheet": "5.1.1", "@module-federation/enhanced": "0.11.3", "@notifee/react-native": "9.1.8", "@react-native-async-storage/async-storage": "2.1.2", "@react-native-camera-roll/camera-roll": "7.10.0", "@react-native-clipboard/clipboard": "1.16.2", "@react-native-community/datetimepicker": "8.3.0", "@react-native-community/geolocation": "3.4.0", "@react-native-community/netinfo": "11.4.1", "@react-native-firebase/analytics": "21.14.0", "@react-native-firebase/app": "21.14.0", "@react-native-firebase/crashlytics": "21.14.0", "@react-native-firebase/messaging": "21.14.0", "@react-navigation/bottom-tabs": "7.2.0", "@react-navigation/native": "7.0.15", "@react-navigation/native-stack": "7.2.0", "@shopify/flash-list": "1.7.3", "crc": "^4.3.2", "dotenv-webpack": "8.1.0", "i18n-js": "3.9.2", "jail-monkey": "2.8.3", "jwt-decode": "4.0.0", "moment": "2.30.1", "msb-communication-lib": "1.1.0", "msb-host-shared-module": "2.0.0", "msb-shared-component": "1.1.9", "react": "19.0.1", "react-hook-form": "7.57.0", "react-native": "0.78.23", "react-native-blob-util": "0.21.2", "react-native-config": "1.5.5", "react-native-contacts": "8.0.5", "react-native-device-info": "14.0.4", "react-native-edge-to-edge": "1.6.0", "react-native-exit-app": "2.0.0", "react-native-fs": "2.20.0", "react-native-gesture-handler": "2.24.0", "react-native-image-crop-picker": "0.42.0", "react-native-image-picker": "8.2.0", "react-native-keyboard-accessory": "^0.1.16", "react-native-keyboard-controller": "1.17.1", "react-native-linear-gradient": "2.8.3", "react-native-maps": "1.20.1", "react-native-modal": "14.0.3", "react-native-modal-datetime-picker": "18.0.0", "react-native-nfc-manager": "3.16.1", "react-native-pager-view": "6.7.0", "react-native-pdf": "6.7.7", "react-native-permissions": "5.2.5", "react-native-qrcode-svg": "6.3.15", "react-native-reanimated": "3.16.7", "react-native-reanimated-carousel": "4.0.2", "react-native-render-html": "6.3.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "4.10.0", "react-native-select-contact": "1.6.3", "react-native-share": "12.0.9", "react-native-snap-carousel": "3.9.2", "react-native-svg": "15.11.2", "react-native-tab-view": "^4.0.5", "react-native-unistyles": "2.32.1", "react-native-view-shot": "4.0.3", "react-native-vision-camera": "4.6.4", "react-native-webview": "13.13.2", "remove-accents": "^0.5.0", "rn-qr-generator": "1.4.3", "rxjs": "7.8.1", "zod": "3.24.4", "zustand": "5.0.4"}, "devDependencies": {"@babel/core": "7.25.2", "@babel/preset-env": "7.26.9", "@babel/runtime": "7.25.0", "@callstack/repack": "5.0.7", "@callstack/repack-plugin-reanimated": "5.0.6", "@react-native-community/cli": "15.0.1", "@react-native-community/cli-platform-android": "15.0.1", "@react-native-community/cli-platform-ios": "15.0.1", "@react-native/babel-preset": "0.78.2", "@react-native/eslint-config": "0.78.2", "@react-native/metro-config": "0.78.2", "@react-native/typescript-config": "0.78.2", "@rnx-kit/align-deps": "^3.0.4", "@rspack/core": "1.3.2", "@swc/helpers": "0.5.15", "@testing-library/react-hooks": "8.0.1", "@testing-library/react-native": "13.2.0", "@types/i18n-js": "3.8.9", "@types/jest": "29.5.14", "@types/react": "19.0.0", "@types/react-test-renderer": "19.0.0", "babel-jest": "29.7.0", "eslint": "8.57.1", "husky": "^9.1.7", "jest": "29.7.0", "lint-staged": "^15.5.1", "msb-digibank-sdk": "git+https://library:<EMAIL>/digital-banking-platform/ibretail/mobile/super-app/library/msb-digibank-sdk.git#version/1.3.26", "msw": "2.8.2", "prettier": "2.8.8", "react-native-devsettings": "1.0.5", "react-test-renderer": "19.0.1", "ts-jest": "29.3.2", "ts-morph": "25.0.1", "typescript": "5.8.2"}, "engines": {"node": ">=18"}, "resolutions": {"nanoid": "3.3.8"}, "lint-staged": {"src/**/*.{js,jsx,ts,tsx}": ["prettier --write"]}, "rnx-kit": {"kitType": "app", "alignDeps": {"presets": ["./node_modules/msb-digibank-sdk/preset"], "requirements": ["react-native@0.78.23"], "capabilities": ["super-app"]}}, "packageManager": "yarn@4.3.1+sha512.af78262d7d125afbfeed740602ace8c5e4405cd7f4735c08feb327286b2fdb2390fbca01589bfd1f50b1240548b74806767f5a063c94b67e431aabd0d86f7774"}