const {createJsWithBabelPreset} = require('ts-jest');

const jsWithBabelPreset = createJsWithBabelPreset({
  tsconfig: 'tsconfig.spec.json',
  babelConfig: true,
});

const config = {
  preset: 'react-native',
  transform: jsWithBabelPreset.transform,
  setupFilesAfterEnv: ['<rootDir>/jest-setup.ts', '<rootDir>/src/__test__/setup/jestSetup.ts'],
  collectCoverageFrom: [
    '<rootDir>/src/**/*.{ts,tsx}',
    '!<rootDir>/src/**/*.d.ts',
    '!<rootDir>/src/**/__test__/**',
    '!<rootDir>/src/**/__tests__/**',
    '!<rootDir>/src/**/*.test.{ts,tsx}',
    '!<rootDir>/src/**/*.spec.{ts,tsx}',
    '!<rootDir>/src/**/index.{ts,tsx}',
    '!<rootDir>/src/assets/**',
    '!<rootDir>/src/constants/**',
  ],
  coverageDirectory: '<rootDir>/coverage',
  coverageReporters: ['text', 'lcov', 'html', 'json'],
  // Coverage thresholds temporarily disabled to see current coverage
  // coverageThreshold: {
  //   global: {
  //     statements: 80,
  //     branches: 75,
  //     functions: 80,
  //     lines: 80,
  //   },
  //   './src/core/': {
  //     statements: 90,
  //     branches: 85,
  //     functions: 90,
  //     lines: 90,
  //   },
  //   './src/utils/': {
  //     statements: 85,
  //     branches: 80,
  //     functions: 85,
  //     lines: 85,
  //   },
  //   './src/data/': {
  //     statements: 80,
  //     branches: 75,
  //     functions: 80,
  //     lines: 80,
  //   },
  //   './src/domain/': {
  //     statements: 80,
  //     branches: 75,
  //     functions: 80,
  //     lines: 80,
  //   },
  // },
  transformIgnorePatterns: [
    '/node_modules/(?!(react-native-webview|react-native-keyboard-controller|msb-host-shared-module|@gorhom/bottom-sheet|react-native-url-polyfill|react-native-iphone-x-helper|react-native-keyboard-aware-scroll-view|@react-native|react-native|msb-shared-component|react-native-reanimated|react-native-linear-gradient|react-native-modal-datetime-picker|@react-native-community/datetimepicker|@d11/react-native-fast-image|@react-navigation|react-native-reanimated-skeleton|react-native-modal|react-native-animatable|react-native-snap-carousel)/).*/',
  ],
  testEnvironment: 'node',
  testEnvironmentOptions: {
    customExportConditions: ['node'],
  },
  moduleFileExtensions: ['ts', 'tsx', 'js', 'jsx', 'json'],
  moduleNameMapper: {
    /**
     * Mock the module that is used in the app
     */
    '^msb-shared-component$': '<rootDir>/node_modules/msb-shared-component/dist/index.js',
    '^msb-host-shared-module$': '<rootDir>/__mocks__/msb-host-shared-module.ts',
    '^msb-communication-lib$': '<rootDir>/__mocks__/msb-communication-lib.tsx',
    '^react-native-edge-to-edge$': '<rootDir>/__mocks__/react-native-edge-to-edge.ts',
    '^react-native-keyboard-controller$': '<rootDir>/__mocks__/react-native-keyboard-controller.ts',
    /**
     * Module alias similar to tsconfig.paths
     */
    '^mocks/(.*)$': '<rootDir>/__mocks__/$1',
    '^@/(.*)$': '<rootDir>/src/$1',
    '^@core/(.*)$': '<rootDir>/src/core/$1',
    '^@data/(.*)$': '<rootDir>/src/data/$1',
    '^@models/(.*)$': '<rootDir>/src/data/models/$1',
    '^@entities/(.*)$': '<rootDir>/src/domain/entities/$1',
    '^@domain/(.*)$': '<rootDir>/src/domain/$1',
    '^@use-cases/(.*)$': '<rootDir>/src/domain/use-cases/$1',
    '^@di/(.*)$': '<rootDir>/src/di/$1',
    '^@locales$': '<rootDir>/src/locales/index',
    '^@images$': '<rootDir>/src/assets/images/index',
    '^@assets/(.*)$': '<rootDir>/src/assets/$1',
    '^@presentation/(.*)$': '<rootDir>/src/presentation/$1',
    '^@components/(.*)$': '<rootDir>/src/presentation/components/$1',
    '^@hooks/(.*)$': '<rootDir>/src/presentation/hooks/$1',
    '^@utils/(.*)$': '<rootDir>/src/utils/$1',
    '^@utils$': '<rootDir>/src/utils/index',
    '^@constants/(.*)$': '<rootDir>/src/constants/$1',
    '^@constants$': '<rootDir>/src/constants/index',
    '^@app-navigation/(.*)$': '<rootDir>/src/presentation/navigation/$1',
    '^@app-screens/(.*)$': '<rootDir>/src/presentation/screens/$1',
    '^@view-models/(.*)$': '<rootDir>/src/presentation/view-models/$1',
    '^@store/(.*)$': '<rootDir>/src/presentation/store/$1',
  },
  clearMocks: true,
  testTimeout: 20000,
  testMatch: [
    '<rootDir>/src/**/__test__/**/*.{ts,tsx}',
    '<rootDir>/src/**/__tests__/**/*.{ts,tsx}',
    '<rootDir>/src/**/*.test.{ts,tsx}',
    '<rootDir>/src/**/*.spec.{ts,tsx}',
  ],
  testPathIgnorePatterns: ['/node_modules/', '/coverage/', '/dist/', '/build/'],
  watchPathIgnorePatterns: ['/node_modules/', '/coverage/', '/dist/', '/build/'],
  verbose: true,
  collectCoverage: false, // Set to true when running coverage
  maxWorkers: '50%',
  cacheDirectory: '<rootDir>/.jest-cache',
  errorOnDeprecated: true,
  bail: false,
  forceExit: true,
  detectOpenHandles: true,
  resetMocks: true,
  restoreMocks: true,
  globals: {
    __DEV__: true,
  },
};

module.exports = config;
