import {jest} from '@jest/globals';
import {ReactWrapper} from 'enzyme';
import {RenderResult} from '@testing-library/react-native';

/**
 * Test utilities for payment module testing
 */
export class TestUtils {
  /**
   * Wait for async operations to complete
   */
  static async waitForAsync(ms: number = 0): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Wait for next tick
   */
  static async waitForNextTick(): Promise<void> {
    return new Promise(resolve => process.nextTick(resolve));
  }

  /**
   * Wait for multiple async operations
   */
  static async waitForAll(promises: Promise<any>[]): Promise<any[]> {
    return Promise.all(promises);
  }

  /**
   * Create a mock function with specific return value
   */
  static createMockFunction<T = any>(returnValue?: T): jest.MockedFunction<any> {
    return jest.fn().mockReturnValue(returnValue);
  }

  /**
   * Create a mock function that resolves to a value
   */
  static createMockAsyncFunction<T = any>(resolveValue?: T): jest.MockedFunction<any> {
    return jest.fn().mockResolvedValue(resolveValue);
  }

  /**
   * Create a mock function that rejects with an error
   */
  static createMockRejectedFunction(error: Error): jest.MockedFunction<any> {
    return jest.fn().mockRejectedValue(error);
  }

  /**
   * Create a mock function with multiple return values
   */
  static createMockFunctionWithSequence<T = any>(values: T[]): jest.MockedFunction<any> {
    const mockFn = jest.fn();
    values.forEach(value => mockFn.mockReturnValueOnce(value));
    return mockFn;
  }

  /**
   * Create a mock function with multiple resolved values
   */
  static createMockAsyncFunctionWithSequence<T = any>(values: T[]): jest.MockedFunction<any> {
    const mockFn = jest.fn();
    values.forEach(value => mockFn.mockResolvedValueOnce(value));
    return mockFn;
  }

  /**
   * Verify that a mock function was called with specific arguments
   */
  static expectMockCalledWith(mockFn: jest.MockedFunction<any>, ...args: any[]): void {
    expect(mockFn).toHaveBeenCalledWith(...args);
  }

  /**
   * Verify that a mock function was called a specific number of times
   */
  static expectMockCalledTimes(mockFn: jest.MockedFunction<any>, times: number): void {
    expect(mockFn).toHaveBeenCalledTimes(times);
  }

  /**
   * Verify that a mock function was not called
   */
  static expectMockNotCalled(mockFn: jest.MockedFunction<any>): void {
    expect(mockFn).not.toHaveBeenCalled();
  }

  /**
   * Reset all mocks
   */
  static resetAllMocks(): void {
    jest.clearAllMocks();
  }

  /**
   * Mock console methods to avoid noise in tests
   */
  static mockConsole(): {
    log: jest.MockedFunction<any>;
    warn: jest.MockedFunction<any>;
    error: jest.MockedFunction<any>;
    info: jest.MockedFunction<any>;
  } {
    const originalConsole = global.console;
    const mockConsole = {
      log: jest.fn(),
      warn: jest.fn(),
      error: jest.fn(),
      info: jest.fn(),
    };

    global.console = mockConsole as any;

    return {
      ...mockConsole,
      restore: () => {
        global.console = originalConsole;
      },
    } as any;
  }

  /**
   * Mock Date.now() to return a fixed timestamp
   */
  static mockDateNow(timestamp: number = Date.now()): jest.MockedFunction<any> {
    const originalDateNow = Date.now;
    const mockDateNow = jest.fn().mockReturnValue(timestamp);
    Date.now = mockDateNow;

    return {
      ...mockDateNow,
      restore: () => {
        Date.now = originalDateNow;
      },
    } as any;
  }

  /**
   * Mock setTimeout and clearTimeout
   */
  static mockTimers(): void {
    jest.useFakeTimers();
  }

  /**
   * Restore real timers
   */
  static restoreTimers(): void {
    jest.useRealTimers();
  }

  /**
   * Advance timers by specified time
   */
  static advanceTimers(ms: number): void {
    jest.advanceTimersByTime(ms);
  }

  /**
   * Run all pending timers
   */
  static runAllTimers(): void {
    jest.runAllTimers();
  }

  /**
   * Create a spy on an object method
   */
  static createSpy<T extends object, K extends keyof T>(object: T, method: K): jest.SpyInstance<any, any> {
    return jest.spyOn(object, method);
  }

  /**
   * Create a spy that calls through to the original implementation
   */
  static createSpyCallThrough<T extends object, K extends keyof T>(object: T, method: K): jest.SpyInstance<any, any> {
    return jest.spyOn(object, method).mockImplementation(object[method] as any);
  }

  /**
   * Verify that an element exists in the rendered component
   */
  static expectElementToExist(result: RenderResult, testId: string): void {
    expect(result.getByTestId(testId)).toBeTruthy();
  }

  /**
   * Verify that an element does not exist in the rendered component
   */
  static expectElementNotToExist(result: RenderResult, testId: string): void {
    expect(result.queryByTestId(testId)).toBeNull();
  }

  /**
   * Verify that text exists in the rendered component
   */
  static expectTextToExist(result: RenderResult, text: string): void {
    expect(result.getByText(text)).toBeTruthy();
  }

  /**
   * Verify that text does not exist in the rendered component
   */
  static expectTextNotToExist(result: RenderResult, text: string): void {
    expect(result.queryByText(text)).toBeNull();
  }

  /**
   * Get element by test ID with error handling
   */
  static getElementByTestId(result: RenderResult, testId: string): any {
    try {
      return result.getByTestId(testId);
    } catch (error) {
      throw new Error(`Element with testId "${testId}" not found`);
    }
  }

  /**
   * Get element by text with error handling
   */
  static getElementByText(result: RenderResult, text: string): any {
    try {
      return result.getByText(text);
    } catch (error) {
      throw new Error(`Element with text "${text}" not found`);
    }
  }

  /**
   * Simulate user input on a text input
   */
  static simulateTextInput(result: RenderResult, testId: string, value: string): void {
    const input = TestUtils.getElementByTestId(result, testId);
    // Simulate text input change
    input.props.onChangeText?.(value);
  }

  /**
   * Simulate button press
   */
  static simulateButtonPress(result: RenderResult, testId: string): void {
    const button = TestUtils.getElementByTestId(result, testId);
    button.props.onPress?.();
  }

  /**
   * Simulate component focus
   */
  static simulateFocus(result: RenderResult, testId: string): void {
    const element = TestUtils.getElementByTestId(result, testId);
    element.props.onFocus?.();
  }

  /**
   * Simulate component blur
   */
  static simulateBlur(result: RenderResult, testId: string): void {
    const element = TestUtils.getElementByTestId(result, testId);
    element.props.onBlur?.();
  }

  /**
   * Create a mock navigation object
   */
  static createMockNavigation(): any {
    return {
      navigate: jest.fn(),
      goBack: jest.fn(),
      push: jest.fn(),
      pop: jest.fn(),
      popToTop: jest.fn(),
      replace: jest.fn(),
      reset: jest.fn(),
      setParams: jest.fn(),
      dispatch: jest.fn(),
      isFocused: jest.fn().mockReturnValue(true),
      canGoBack: jest.fn().mockReturnValue(true),
      getId: jest.fn().mockReturnValue('test-screen'),
      getParent: jest.fn(),
      getState: jest.fn().mockReturnValue({
        index: 0,
        routes: [{name: 'TestScreen', key: 'test-key'}],
      }),
      addListener: jest.fn(),
      removeListener: jest.fn(),
    };
  }

  /**
   * Create a mock route object
   */
  static createMockRoute(params: any = {}): any {
    return {
      key: 'test-route-key',
      name: 'TestScreen',
      params,
    };
  }

  /**
   * Create a mock ref object
   */
  static createMockRef<T = any>(current: T | null = null): React.RefObject<T> {
    return {
      current,
    };
  }

  /**
   * Verify async operation completion
   */
  static async expectAsync<T>(asyncOperation: () => Promise<T>, expectedResult?: T): Promise<T> {
    const result = await asyncOperation();
    if (expectedResult !== undefined) {
      expect(result).toEqual(expectedResult);
    }
    return result;
  }

  /**
   * Verify async operation throws error
   */
  static async expectAsyncToThrow(asyncOperation: () => Promise<any>, expectedError?: string | Error): Promise<void> {
    try {
      await asyncOperation();
      throw new Error('Expected async operation to throw, but it did not');
    } catch (error) {
      if (expectedError) {
        if (typeof expectedError === 'string') {
          expect((error as Error).message).toContain(expectedError);
        } else {
          expect(error).toEqual(expectedError);
        }
      }
    }
  }

  /**
   * Create a performance measurement wrapper
   */
  static measurePerformance<T>(operation: () => T, maxDuration: number = 1000): T {
    const startTime = performance.now();
    const result = operation();
    const endTime = performance.now();
    const duration = endTime - startTime;

    expect(duration).toBeLessThan(maxDuration);
    return result;
  }

  /**
   * Create an async performance measurement wrapper
   */
  static async measureAsyncPerformance<T>(operation: () => Promise<T>, maxDuration: number = 1000): Promise<T> {
    const startTime = performance.now();
    const result = await operation();
    const endTime = performance.now();
    const duration = endTime - startTime;

    expect(duration).toBeLessThan(maxDuration);
    return result;
  }

  /**
   * Create a memory usage measurement wrapper
   */
  static measureMemoryUsage<T>(operation: () => T): T {
    const initialMemory = process.memoryUsage();
    const result = operation();
    const finalMemory = process.memoryUsage();

    const memoryDiff = {
      rss: finalMemory.rss - initialMemory.rss,
      heapTotal: finalMemory.heapTotal - initialMemory.heapTotal,
      heapUsed: finalMemory.heapUsed - initialMemory.heapUsed,
      external: finalMemory.external - initialMemory.external,
    };

    // Log memory usage for debugging
    console.log('Memory usage difference:', memoryDiff);

    return result;
  }

  /**
   * Create a test data generator
   */
  static generateTestData<T>(generator: (index: number) => T, count: number): T[] {
    return Array.from({length: count}, (_, index) => generator(index));
  }

  /**
   * Create a random string for testing
   */
  static generateRandomString(length: number = 10): string {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  }

  /**
   * Create a random number within range
   */
  static generateRandomNumber(min: number = 0, max: number = 100): number {
    return Math.floor(Math.random() * (max - min + 1)) + min;
  }

  /**
   * Create a random boolean
   */
  static generateRandomBoolean(): boolean {
    return Math.random() < 0.5;
  }

  /**
   * Create a random date within range
   */
  static generateRandomDate(start: Date = new Date(2020, 0, 1), end: Date = new Date()): Date {
    return new Date(start.getTime() + Math.random() * (end.getTime() - start.getTime()));
  }

  /**
   * Validate test environment setup
   */
  static validateTestEnvironment(): void {
    expect(typeof jest).toBe('object');
    expect(typeof expect).toBe('function');
    expect(typeof describe).toBe('function');
    expect(typeof it).toBe('function');
    expect(typeof beforeEach).toBe('function');
    expect(typeof afterEach).toBe('function');
  }

  /**
   * Create a test timeout wrapper
   */
  static withTimeout<T>(operation: () => Promise<T>, timeout: number = 5000): Promise<T> {
    return Promise.race([
      operation(),
      new Promise<never>((_, reject) =>
        setTimeout(() => reject(new Error(`Operation timed out after ${timeout}ms`)), timeout),
      ),
    ]);
  }

  /**
   * Create a retry wrapper for flaky tests
   */
  static async withRetry<T>(operation: () => Promise<T>, maxRetries: number = 3, delay: number = 100): Promise<T> {
    let lastError: Error;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error as Error;
        if (attempt < maxRetries) {
          await TestUtils.waitForAsync(delay);
        }
      }
    }

    throw lastError!;
  }
}

/**
 * Common test assertions and matchers
 */
export class TestAssertions {
  /**
   * Assert that a value is defined and not null
   */
  static assertDefined<T>(value: T | null | undefined): asserts value is T {
    expect(value).toBeDefined();
    expect(value).not.toBeNull();
  }

  /**
   * Assert that a value is a string and not empty
   */
  static assertNonEmptyString(value: any): asserts value is string {
    expect(typeof value).toBe('string');
    expect(value.length).toBeGreaterThan(0);
  }

  /**
   * Assert that a value is a positive number
   */
  static assertPositiveNumber(value: any): asserts value is number {
    expect(typeof value).toBe('number');
    expect(value).toBeGreaterThan(0);
  }

  /**
   * Assert that a value is a valid array
   */
  static assertValidArray<T>(value: any): asserts value is T[] {
    expect(Array.isArray(value)).toBe(true);
  }

  /**
   * Assert that an array is not empty
   */
  static assertNonEmptyArray<T>(value: any): asserts value is T[] {
    TestAssertions.assertValidArray(value);
    expect(value.length).toBeGreaterThan(0);
  }

  /**
   * Assert that a value is a valid object
   */
  static assertValidObject(value: any): asserts value is object {
    expect(typeof value).toBe('object');
    expect(value).not.toBeNull();
  }

  /**
   * Assert that an object has specific properties
   */
  static assertObjectHasProperties(obj: any, properties: string[]): void {
    TestAssertions.assertValidObject(obj);
    properties.forEach(prop => {
      expect(obj).toHaveProperty(prop);
    });
  }

  /**
   * Assert that a function throws a specific error
   */
  static assertThrows(fn: () => void, expectedError?: string | RegExp): void {
    if (expectedError) {
      expect(fn).toThrow(expectedError);
    } else {
      expect(fn).toThrow();
    }
  }

  /**
   * Assert that an async function rejects with a specific error
   */
  static async assertRejects(fn: () => Promise<any>, expectedError?: string | RegExp): Promise<void> {
    if (expectedError) {
      await expect(fn()).rejects.toThrow(expectedError);
    } else {
      await expect(fn()).rejects.toThrow();
    }
  }

  /**
   * Assert that a value matches a specific type
   */
  static assertType<T>(value: any, typeName: string): asserts value is T {
    expect(typeof value).toBe(typeName);
  }

  /**
   * Assert that a value is within a specific range
   */
  static assertInRange(value: number, min: number, max: number): void {
    expect(value).toBeGreaterThanOrEqual(min);
    expect(value).toBeLessThanOrEqual(max);
  }

  /**
   * Assert that a date is valid
   */
  static assertValidDate(value: any): asserts value is Date {
    expect(value instanceof Date).toBe(true);
    expect(isNaN(value.getTime())).toBe(false);
  }

  /**
   * Assert that a string matches a pattern
   */
  static assertStringMatches(value: string, pattern: RegExp): void {
    expect(value).toMatch(pattern);
  }

  /**
   * Assert that an array contains specific items
   */
  static assertArrayContains<T>(array: T[], items: T[]): void {
    items.forEach(item => {
      expect(array).toContain(item);
    });
  }

  /**
   * Assert that an array does not contain specific items
   */
  static assertArrayNotContains<T>(array: T[], items: T[]): void {
    items.forEach(item => {
      expect(array).not.toContain(item);
    });
  }
}
