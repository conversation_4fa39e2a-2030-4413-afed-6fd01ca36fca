import {CategoryModel} from '../../domain/entities/category-list/CategoryListModel';
import {ProviderModel} from '../../domain/entities/provider-list/ProviderListModel';
import {GetBillDetailModel} from '../../domain/entities/get-bill-detail/GetBillDetailModel';
import {AccountModel} from '../../domain/entities/my-bill-contact-list/MyBillContactListModel';
import {IBillContact} from '../../domain/entities/IBillContact';
import {PaymentOrderRequest} from '../../data/models/payment-order/PaymentOrderRequest';
import {ValidateModel} from '../../domain/entities/validate/ValidateModel';
import {PaymentInfoModel, QRPaymentInfoModel} from '../../navigation/types';
import {BaseResponse} from '../../core/BaseResponse';
import {MSBCustomError} from '../../core/MSBCustomError';
import {MSBErrorCode} from '../../core/MSBErrorCode';

export class MockDataFactory {
  // Category Mock Data
  static createMockCategories(): CategoryModel[] {
    return [
      new CategoryModel('UTILITY', 'Utilities', 'Tiện ích', 'utility-icon', true, 1),
      new CategoryModel('TELECOM', 'Telecommunications', 'Viễn thông', 'telecom-icon', true, 2),
      new CategoryModel('INSURANCE', 'Insurance', 'Bảo hiểm', 'insurance-icon', true, 3),
      new CategoryModel('EDUCATION', 'Education', 'Giáo dục', 'education-icon', true, 4),
      new CategoryModel('TRANSPORT', 'Transportation', 'Giao thông', 'transport-icon', false, 5),
    ];
  }

  static createMockCategory(overrides: Partial<CategoryModel> = {}): CategoryModel {
    const defaultCategory = new CategoryModel(
      'UTILITY',
      'Utilities',
      'Tiện ích',
      'utility-icon',
      true,
      1
    );
    return Object.assign(defaultCategory, overrides);
  }

  // Provider Mock Data
  static createMockProviders(): ProviderModel[] {
    return [
      new ProviderModel('EVN', 'EVN001', 'EVN Electric', 'Vietnam Electricity', true, false, 'evn-logo'),
      new ProviderModel('VIETTEL', 'VT001', 'Viettel', 'Viettel Telecom', true, false, 'viettel-logo'),
      new ProviderModel('MOBIFONE', 'MF001', 'MobiFone', 'MobiFone Corp', true, false, 'mobifone-logo'),
      new ProviderModel('VINAPHONE', 'VN001', 'VinaPhone', 'VinaPhone Corp', true, false, 'vinaphone-logo'),
      new ProviderModel('SACOMBANK', 'SCB001', 'Sacombank', 'Saigon Thuong Tin Bank', false, true, 'scb-logo'),
    ];
  }

  static createMockProvider(overrides: Partial<ProviderModel> = {}): ProviderModel {
    const defaultProvider = new ProviderModel(
      'EVN',
      'EVN001',
      'EVN Electric',
      'Vietnam Electricity',
      true,
      false,
      'evn-logo'
    );
    return Object.assign(defaultProvider, overrides);
  }

  // Bill Detail Mock Data
  static createMockBillDetail(overrides: Partial<GetBillDetailModel> = {}): GetBillDetailModel {
    const defaultBillDetail = new GetBillDetailModel(
      'BILL001',
      'Electric Bill March 2024',
      150000,
      'VND',
      '2024-03-15',
      'ACTIVE'
    );
    return Object.assign(defaultBillDetail, overrides);
  }

  static createMockBillDetails(): GetBillDetailModel[] {
    return [
      new GetBillDetailModel('BILL001', 'Electric Bill March 2024', 150000, 'VND', '2024-03-15', 'ACTIVE'),
      new GetBillDetailModel('BILL002', 'Water Bill March 2024', 80000, 'VND', '2024-03-15', 'ACTIVE'),
      new GetBillDetailModel('BILL003', 'Internet Bill March 2024', 300000, 'VND', '2024-03-15', 'PENDING'),
      new GetBillDetailModel('BILL004', 'Mobile Bill March 2024', 200000, 'VND', '2024-03-15', 'OVERDUE'),
    ];
  }

  // Account Mock Data
  static createMockAccount(overrides: Partial<AccountModel> = {}): AccountModel {
    const defaultAccount = new AccountModel(
      'ACC001',
      'Nguyen Van A',
      '**********',
      'MSB Bank',
      'MSB',
      'ACTIVE',
      1000000
    );
    return Object.assign(defaultAccount, overrides);
  }

  static createMockAccounts(): AccountModel[] {
    return [
      new AccountModel('ACC001', 'Nguyen Van A', '**********', 'MSB Bank', 'MSB', 'ACTIVE', 1000000),
      new AccountModel('ACC002', 'Tran Thi B', '**********', 'MSB Bank', 'MSB', 'ACTIVE', 2500000),
      new AccountModel('ACC003', 'Le Van C', '**********', 'Vietcombank', 'VCB', 'ACTIVE', 500000),
      new AccountModel('ACC004', 'Pham Thi D', '**********', 'Techcombank', 'TCB', 'INACTIVE', 0),
    ];
  }

  // Bill Contact Mock Data
  static createMockBillContact(overrides: Partial<IBillContact> = {}): IBillContact {
    const defaultContact: IBillContact = {
      id: 'CONTACT001',
      name: 'Electric Bill - Home',
      customerCode: 'CUST001',
      contractNumber: 'CONTRACT001',
      address: '123 Main Street, District 1, Ho Chi Minh City',
      phoneNumber: '**********',
      email: '<EMAIL>',
      categoryCode: 'UTILITY',
      providerCode: 'EVN',
      isActive: true,
      isFavorite: false,
      createdDate: '2024-01-15',
      lastPaymentDate: '2024-02-15',
    };
    return Object.assign(defaultContact, overrides);
  }

  static createMockBillContacts(): IBillContact[] {
    return [
      {
        id: 'CONTACT001',
        name: 'Electric Bill - Home',
        customerCode: 'CUST001',
        contractNumber: 'CONTRACT001',
        address: '123 Main Street, District 1, Ho Chi Minh City',
        phoneNumber: '**********',
        email: '<EMAIL>',
        categoryCode: 'UTILITY',
        providerCode: 'EVN',
        isActive: true,
        isFavorite: true,
        createdDate: '2024-01-15',
        lastPaymentDate: '2024-02-15',
      },
      {
        id: 'CONTACT002',
        name: 'Water Bill - Home',
        customerCode: 'CUST002',
        contractNumber: 'CONTRACT002',
        address: '123 Main Street, District 1, Ho Chi Minh City',
        phoneNumber: '**********',
        email: '<EMAIL>',
        categoryCode: 'UTILITY',
        providerCode: 'SAWACO',
        isActive: true,
        isFavorite: false,
        createdDate: '2024-01-20',
        lastPaymentDate: '2024-02-20',
      },
      {
        id: 'CONTACT003',
        name: 'Mobile Bill - Personal',
        customerCode: 'CUST003',
        contractNumber: 'CONTRACT003',
        address: '456 Second Street, District 2, Ho Chi Minh City',
        phoneNumber: '**********',
        email: '<EMAIL>',
        categoryCode: 'TELECOM',
        providerCode: 'VIETTEL',
        isActive: true,
        isFavorite: true,
        createdDate: '2024-02-01',
        lastPaymentDate: '2024-03-01',
      },
    ];
  }

  // Payment Order Request Mock Data
  static createMockPaymentOrderRequest(overrides: Partial<PaymentOrderRequest> = {}): PaymentOrderRequest {
    const defaultRequest = new PaymentOrderRequest();
    defaultRequest.amount = 150000;
    defaultRequest.currency = 'VND';
    defaultRequest.customerCode = 'CUST001';
    defaultRequest.contractNumber = 'CONTRACT001';
    defaultRequest.serviceCode = 'EVN';
    defaultRequest.categoryCode = 'UTILITY';
    defaultRequest.originatorAccountNo = '**********';
    defaultRequest.remark = 'Electric bill payment';
    
    return Object.assign(defaultRequest, overrides);
  }

  // Validate Model Mock Data
  static createMockValidateModel(overrides: Partial<ValidateModel> = {}): ValidateModel {
    const defaultValidate = new ValidateModel(
      'VALID001',
      'Validation successful',
      true,
      150000,
      'VND',
      'Electric Bill March 2024'
    );
    return Object.assign(defaultValidate, overrides);
  }

  // QR Payment Info Mock Data
  static createMockQRPaymentInfo(overrides: Partial<QRPaymentInfoModel> = {}): QRPaymentInfoModel {
    const defaultQRInfo: QRPaymentInfoModel = {
      accountNo: '**********',
      bankCode: 'MSB',
      remark: 'QR Payment',
      service: 'QR_PAYMENT',
      qrType: 'DYNAMIC',
      amount: 100000,
      serviceCode: 'QR001',
      storeId: 'STORE001',
      merchantName: 'Coffee Shop',
      payType: 'INSTANT',
      qrFormat: 'EMV',
      quantity: 1,
      expiredTime: '2024-03-15T23:59:59',
      qrContent: 'qr_content_string',
    };
    return Object.assign(defaultQRInfo, overrides);
  }

  // Payment Info Model Mock Data
  static createMockPaymentInfo(overrides: Partial<PaymentInfoModel> = {}): PaymentInfoModel {
    const defaultPaymentInfo: PaymentInfoModel = {
      title: 'Electric Bill Payment',
      categoryName: 'Utilities',
      billInfo: MockDataFactory.createMockBillDetail(),
      paymentValidate: MockDataFactory.createMockPaymentOrderRequest(),
      originatorAccount: {
        identification: 'ID123456',
        name: 'Nguyen Van A',
        accountNo: '**********',
        bankName: 'MSB Bank',
        bankCode: 'MSB',
      },
      paymentResultType: 'SUCCESS',
      contractName: 'Electric Contract 001',
      provider: MockDataFactory.createMockProvider(),
      additionalInfo: 'Monthly electric bill payment',
      qrPaymentInfo: MockDataFactory.createMockQRPaymentInfo(),
    };
    return Object.assign(defaultPaymentInfo, overrides);
  }

  // Base Response Mock Data
  static createMockSuccessResponse<T>(data: T): BaseResponse<T> {
    return {
      errors: null,
      ...data,
    } as BaseResponse<T>;
  }

  static createMockErrorResponse<T>(error: MSBCustomError): BaseResponse<T> {
    return {
      errors: [error],
    } as BaseResponse<T>;
  }

  // MSB Custom Error Mock Data
  static createMockError(overrides: Partial<MSBCustomError> = {}): MSBCustomError {
    const defaultError = new MSBCustomError(
      MSBErrorCode.NETWORK_ERROR,
      'Network connection failed',
      'Please check your internet connection'
    );
    return Object.assign(defaultError, overrides);
  }

  static createMockErrors(): MSBCustomError[] {
    return [
      new MSBCustomError(MSBErrorCode.NETWORK_ERROR, 'Network Error', 'Connection failed'),
      new MSBCustomError(MSBErrorCode.VALIDATION_ERROR, 'Validation Error', 'Invalid input data'),
      new MSBCustomError(MSBErrorCode.SERVER_ERROR, 'Server Error', 'Internal server error'),
      new MSBCustomError(MSBErrorCode.TIMEOUT_ERROR, 'Timeout Error', 'Request timeout'),
      new MSBCustomError(MSBErrorCode.UNAUTHORIZED, 'Unauthorized', 'Access denied'),
    ];
  }

  // HTTP Response Mock Data
  static createMockHttpResponse(status: number, data: any, headers: Record<string, string> = {}) {
    return {
      status,
      data,
      headers: {
        'content-type': 'application/json',
        ...headers,
      },
      statusText: status === 200 ? 'OK' : 'Error',
    };
  }

  // API Response Mock Data
  static createMockApiSuccessResponse<T>(data: T) {
    return {
      status: 'SUCCESS',
      data,
      message: 'Operation completed successfully',
      timestamp: new Date().toISOString(),
    };
  }

  static createMockApiErrorResponse(error: string, code: string = 'ERROR') {
    return {
      status: 'ERROR',
      error: {
        code,
        message: error,
        details: 'Error details',
      },
      timestamp: new Date().toISOString(),
    };
  }

  // Pagination Mock Data
  static createMockPaginatedResponse<T>(items: T[], page: number = 1, pageSize: number = 10) {
    const startIndex = (page - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    const paginatedItems = items.slice(startIndex, endIndex);

    return {
      items: paginatedItems,
      pagination: {
        page,
        pageSize,
        totalItems: items.length,
        totalPages: Math.ceil(items.length / pageSize),
        hasNext: endIndex < items.length,
        hasPrevious: page > 1,
      },
    };
  }

  // Date Mock Data
  static createMockDates() {
    const now = new Date();
    return {
      now: now.toISOString(),
      yesterday: new Date(now.getTime() - 24 * 60 * 60 * 1000).toISOString(),
      tomorrow: new Date(now.getTime() + 24 * 60 * 60 * 1000).toISOString(),
      lastWeek: new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000).toISOString(),
      nextWeek: new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000).toISOString(),
      lastMonth: new Date(now.getFullYear(), now.getMonth() - 1, now.getDate()).toISOString(),
      nextMonth: new Date(now.getFullYear(), now.getMonth() + 1, now.getDate()).toISOString(),
    };
  }

  // Form Data Mock Data
  static createMockFormData() {
    return {
      customerCode: 'CUST001',
      contractNumber: 'CONTRACT001',
      amount: 150000,
      phoneNumber: '**********',
      email: '<EMAIL>',
      address: '123 Main Street, District 1, Ho Chi Minh City',
      remark: 'Payment for services',
    };
  }

  // Navigation Mock Data
  static createMockNavigationParams() {
    return {
      paymentInfo: MockDataFactory.createMockPaymentInfo(),
      category: MockDataFactory.createMockCategory(),
      account: MockDataFactory.createMockAccount(),
      contact: MockDataFactory.createMockBillContact(),
    };
  }

  // Test Scenarios
  static createTestScenarios() {
    return {
      successfulPayment: {
        category: MockDataFactory.createMockCategory(),
        provider: MockDataFactory.createMockProvider(),
        billDetail: MockDataFactory.createMockBillDetail(),
        account: MockDataFactory.createMockAccount(),
        paymentInfo: MockDataFactory.createMockPaymentInfo({paymentResultType: 'SUCCESS'}),
      },
      failedPayment: {
        category: MockDataFactory.createMockCategory(),
        provider: MockDataFactory.createMockProvider(),
        billDetail: MockDataFactory.createMockBillDetail(),
        account: MockDataFactory.createMockAccount({balance: 0}),
        paymentInfo: MockDataFactory.createMockPaymentInfo({paymentResultType: 'FAILED'}),
        error: MockDataFactory.createMockError({code: MSBErrorCode.INSUFFICIENT_BALANCE}),
      },
      pendingPayment: {
        category: MockDataFactory.createMockCategory(),
        provider: MockDataFactory.createMockProvider(),
        billDetail: MockDataFactory.createMockBillDetail(),
        account: MockDataFactory.createMockAccount(),
        paymentInfo: MockDataFactory.createMockPaymentInfo({paymentResultType: 'PENDING'}),
      },
      qrPayment: {
        qrInfo: MockDataFactory.createMockQRPaymentInfo(),
        paymentInfo: MockDataFactory.createMockPaymentInfo({
          title: 'QR Payment',
          categoryName: 'QR Payment',
        }),
      },
    };
  }
}
