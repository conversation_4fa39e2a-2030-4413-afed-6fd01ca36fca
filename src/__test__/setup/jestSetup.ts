import 'react-native-gesture-handler/jestSetup';
import {jest} from '@jest/globals';

// Mock React Native modules
jest.mock('react-native', () => {
  const RN = jest.requireActual('react-native');

  return {
    ...RN,
    Platform: {
      OS: 'ios',
      select: jest.fn(obj => obj.ios || obj.default),
    },
    Dimensions: {
      get: jest.fn(() => ({
        width: 375,
        height: 812,
        scale: 2,
        fontScale: 1,
      })),
      addEventListener: jest.fn(),
      removeEventListener: jest.fn(),
    },
    Alert: {
      alert: jest.fn(),
    },
    Linking: {
      openURL: jest.fn(),
      canOpenURL: jest.fn(() => Promise.resolve(true)),
    },
    StatusBar: {
      setBarStyle: jest.fn(),
      setBackgroundColor: jest.fn(),
    },
    Keyboard: {
      addListener: jest.fn(),
      removeListener: jest.fn(),
      dismiss: jest.fn(),
    },
    BackHandler: {
      addEventListener: jest.fn(),
      removeEventListener: jest.fn(),
      exitApp: jest.fn(),
    },
    PermissionsAndroid: {
      request: jest.fn(() => Promise.resolve('granted')),
      check: jest.fn(() => Promise.resolve(true)),
      PERMISSIONS: {},
      RESULTS: {
        GRANTED: 'granted',
        DENIED: 'denied',
        NEVER_ASK_AGAIN: 'never_ask_again',
      },
    },
    AppState: {
      currentState: 'active',
      addEventListener: jest.fn(),
      removeEventListener: jest.fn(),
    },
    NetInfo: {
      fetch: jest.fn(() =>
        Promise.resolve({
          isConnected: true,
          type: 'wifi',
        }),
      ),
      addEventListener: jest.fn(),
    },
  };
});

// Mock React Navigation
jest.mock('@react-navigation/native', () => ({
  useNavigation: () => ({
    navigate: jest.fn(),
    goBack: jest.fn(),
    push: jest.fn(),
    pop: jest.fn(),
    popToTop: jest.fn(),
    replace: jest.fn(),
    reset: jest.fn(),
    setParams: jest.fn(),
    dispatch: jest.fn(),
    isFocused: jest.fn(() => true),
    canGoBack: jest.fn(() => true),
    getId: jest.fn(() => 'test-screen'),
    getParent: jest.fn(),
    getState: jest.fn(() => ({
      index: 0,
      routes: [{name: 'TestScreen', key: 'test-key'}],
    })),
    addListener: jest.fn(),
    removeListener: jest.fn(),
  }),
  useRoute: () => ({
    key: 'test-route-key',
    name: 'TestScreen',
    params: {},
  }),
  useFocusEffect: jest.fn(),
  useIsFocused: jest.fn(() => true),
  NavigationContainer: ({children}: any) => children,
  createNavigationContainerRef: jest.fn(),
}));

jest.mock('@react-navigation/native-stack', () => ({
  createNativeStackNavigator: jest.fn(() => ({
    Navigator: ({children}: any) => children,
    Screen: ({children}: any) => children,
  })),
}));

// Mock MSB Communication Lib
jest.mock('msb-communication-lib', () => ({
  i18n: {
    t: jest.fn((key: string) => key),
  },
  I18nPath: jest.fn(),
}));

jest.mock('msb-communication-lib/dist/locales/LocaleContext', () => ({
  useLocale: jest.fn(() => ({
    locale: 'vi',
  })),
}));

// Mock MSB Host Shared Module
jest.mock('msb-host-shared-module', () => ({
  hostSharedModule: {
    d: {
      domainService: {
        showPopup: jest.fn(),
        hidePopup: jest.fn(),
        showBottomSheet: jest.fn(),
        hideBottomSheet: jest.fn(),
        showLoading: jest.fn(),
        hideLoading: jest.fn(),
      },
      navigationService: {
        navigate: jest.fn(),
        goBack: jest.fn(),
        reset: jest.fn(),
      },
      storageService: {
        getItem: jest.fn(),
        setItem: jest.fn(),
        removeItem: jest.fn(),
        clear: jest.fn(),
      },
    },
  },
}));

// Mock MSB Shared Components
jest.mock('msb-shared-component', () => ({
  MSBPage: jest.fn(({children, testID}: any) => ({
    type: 'MSBPage',
    props: {children, testID},
  })),
  MSBTextBase: jest.fn(({children, testID}: any) => ({
    type: 'MSBTextBase',
    props: {children, testID},
  })),
  MSBInputBase: jest.fn(({testID, value, onChangeText, placeholder}: any) => ({
    type: 'MSBInputBase',
    props: {testID, value, onChangeText, placeholder},
  })),
  MSBButton: jest.fn(({children, testID, onPress, disabled}: any) => ({
    type: 'MSBButton',
    props: {children, testID, onPress, disabled},
  })),
  MSBSelection: jest.fn(({testID, disabled, label, onChange, childrenContent}: any) => ({
    type: 'MSBSelection',
    props: {testID, disabled, label, onChange, childrenContent},
  })),
  MSBFastImage: jest.fn(({nameImage, style, folder, testID}: any) => ({
    type: 'MSBFastImage',
    props: {testID: testID || 'msb-fast-image', nameImage, style, folder},
  })),
  MSBLoadingItemSkeleton: jest.fn(({loading}: any) => ({
    type: 'MSBLoadingItemSkeleton',
    props: {loading},
  })),
  MSBFolderImage: {
    LOGO_TOPUP: 'LOGO_TOPUP',
    LOGO_BILLING: 'LOGO_BILLING',
    ICON_SVG: 'ICON_SVG',
  },
  createMSBStyleSheet: jest.fn(styleFunction => styleFunction({theme: {}})),
  useMSBStyles: jest.fn(() => ({
    styles: {},
    theme: {},
  })),
  memo: (component: any) => component,
}));

// Mock React Native Gesture Handler
jest.mock('react-native-gesture-handler', () => ({
  FlatList: jest.fn(({data, renderItem, keyExtractor}: any) => ({
    type: 'FlatList',
    props: {data, renderItem, keyExtractor},
  })),
  ScrollView: jest.fn(({children}: any) => ({
    type: 'ScrollView',
    props: {children},
  })),
  TouchableOpacity: jest.fn(({children, onPress, testID}: any) => ({
    type: 'TouchableOpacity',
    props: {children, onPress, testID},
  })),
  PanGestureHandler: jest.fn(({children}: any) => children),
  State: {
    BEGAN: 'BEGAN',
    ACTIVE: 'ACTIVE',
    END: 'END',
    CANCELLED: 'CANCELLED',
    FAILED: 'FAILED',
    UNDETERMINED: 'UNDETERMINED',
  },
}));

// Mock Async Storage
jest.mock('@react-native-async-storage/async-storage', () => ({
  getItem: jest.fn(() => Promise.resolve(null)),
  setItem: jest.fn(() => Promise.resolve()),
  removeItem: jest.fn(() => Promise.resolve()),
  clear: jest.fn(() => Promise.resolve()),
  getAllKeys: jest.fn(() => Promise.resolve([])),
  multiGet: jest.fn(() => Promise.resolve([])),
  multiSet: jest.fn(() => Promise.resolve()),
  multiRemove: jest.fn(() => Promise.resolve()),
}));

// Mock React Native Safe Area Context
jest.mock('react-native-safe-area-context', () => ({
  SafeAreaProvider: ({children}: any) => children,
  SafeAreaView: ({children}: any) => children,
  useSafeAreaInsets: () => ({
    top: 44,
    bottom: 34,
    left: 0,
    right: 0,
  }),
}));

// Mock React Native Reanimated
jest.mock('react-native-reanimated', () => ({
  default: {
    View: ({children}: any) => children,
    Text: ({children}: any) => children,
    ScrollView: ({children}: any) => children,
    createAnimatedComponent: (component: any) => component,
  },
  useSharedValue: jest.fn(() => ({value: 0})),
  useAnimatedStyle: jest.fn(() => ({})),
  withTiming: jest.fn(value => value),
  withSpring: jest.fn(value => value),
  runOnJS: jest.fn(fn => fn),
  interpolate: jest.fn(),
}));

// Mock React Native Device Info
jest.mock('react-native-device-info', () => ({
  getVersion: jest.fn(() => '1.0.0'),
  getBuildNumber: jest.fn(() => '1'),
  getSystemVersion: jest.fn(() => '14.0'),
  getModel: jest.fn(() => 'iPhone'),
  getDeviceId: jest.fn(() => 'test-device-id'),
  isEmulator: jest.fn(() => Promise.resolve(false)),
}));

// Global test setup
beforeEach(() => {
  jest.clearAllMocks();
});

// Global error handling
global.console = {
  ...console,
  error: jest.fn(),
  warn: jest.fn(),
  log: jest.fn(),
  info: jest.fn(),
  debug: jest.fn(),
};

// Mock global fetch
global.fetch = jest.fn(() =>
  Promise.resolve({
    ok: true,
    status: 200,
    json: () => Promise.resolve({}),
    text: () => Promise.resolve(''),
    headers: new Headers(),
  }),
) as jest.MockedFunction<typeof fetch>;

// Mock XMLHttpRequest
global.XMLHttpRequest = jest.fn(() => ({
  open: jest.fn(),
  send: jest.fn(),
  setRequestHeader: jest.fn(),
  addEventListener: jest.fn(),
  removeEventListener: jest.fn(),
  readyState: 4,
  status: 200,
  responseText: '{}',
})) as any;

// Mock WebSocket
global.WebSocket = jest.fn(() => ({
  send: jest.fn(),
  close: jest.fn(),
  addEventListener: jest.fn(),
  removeEventListener: jest.fn(),
  readyState: 1,
})) as any;

// Mock performance
global.performance = {
  now: jest.fn(() => Date.now()),
  mark: jest.fn(),
  measure: jest.fn(),
  getEntriesByName: jest.fn(() => []),
  getEntriesByType: jest.fn(() => []),
  clearMarks: jest.fn(),
  clearMeasures: jest.fn(),
} as any;

// Mock IntersectionObserver
global.IntersectionObserver = jest.fn(() => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn(),
})) as any;

// Mock ResizeObserver
global.ResizeObserver = jest.fn(() => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn(),
})) as any;

// Mock requestAnimationFrame
global.requestAnimationFrame = jest.fn(cb => setTimeout(cb, 16));
global.cancelAnimationFrame = jest.fn(id => clearTimeout(id));

// Mock localStorage
const localStorageMock = {
  getItem: jest.fn(() => null),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
  length: 0,
  key: jest.fn(() => null),
};
global.localStorage = localStorageMock;

// Mock sessionStorage
global.sessionStorage = localStorageMock;

// Suppress specific warnings in tests
const originalWarn = console.warn;
console.warn = (...args) => {
  if (
    typeof args[0] === 'string' &&
    (args[0].includes('componentWillReceiveProps') ||
      args[0].includes('componentWillMount') ||
      args[0].includes('ReactDOM.render'))
  ) {
    return;
  }
  originalWarn.call(console, ...args);
};

// Set test timeout
jest.setTimeout(10000);

export {};
