import {describe, it, expect, jest, beforeEach} from '@jest/globals';
import {translations, translate} from '../i18n';
import viTranslations from '../vi.json';
import enTranslations from '../en.json';

// Mock msb-communication-lib
jest.mock('msb-communication-lib', () => ({
  i18n: {
    t: jest.fn((key: string) => {
      // Mock implementation that returns the key for testing
      return key;
    }),
  },
  I18nPath: jest.fn(),
}));

describe('i18n System', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('translations object', () => {
    it('should export translations object with vi and en', () => {
      expect(translations).toBeDefined();
      expect(translations).toHaveProperty('vi');
      expect(translations).toHaveProperty('en');
    });

    it('should have vi translations matching imported vi.json', () => {
      expect(translations.vi).toEqual(viTranslations);
    });

    it('should have en translations matching imported en.json', () => {
      expect(translations.en).toEqual(enTranslations);
    });

    it('should have consistent structure between vi and en', () => {
      const viKeys = Object.keys(translations.vi);
      const enKeys = Object.keys(translations.en);

      // Check that both languages have the same top-level keys
      expect(viKeys.sort()).toEqual(enKeys.sort());
    });
  });

  describe('translate function', () => {
    it('should be defined and callable', () => {
      expect(translate).toBeDefined();
      expect(typeof translate).toBe('function');
    });

    it('should call i18n.t when invoked', () => {
      const {i18n} = require('msb-communication-lib');
      
      translate('paymentHome.title');
      
      expect(i18n.t).toHaveBeenCalledWith('paymentHome.title');
    });

    it('should handle nested translation keys', () => {
      const {i18n} = require('msb-communication-lib');
      
      translate('paymentHome.title');
      translate('billingTab.saved');
      translate('components.providerList.searchPlaceholder');
      
      expect(i18n.t).toHaveBeenCalledWith('paymentHome.title');
      expect(i18n.t).toHaveBeenCalledWith('billingTab.saved');
      expect(i18n.t).toHaveBeenCalledWith('components.providerList.searchPlaceholder');
    });

    it('should handle simple translation keys', () => {
      const {i18n} = require('msb-communication-lib');
      
      translate('welcome');
      translate('greeting');
      translate('vnd');
      
      expect(i18n.t).toHaveBeenCalledWith('welcome');
      expect(i18n.t).toHaveBeenCalledWith('greeting');
      expect(i18n.t).toHaveBeenCalledWith('vnd');
    });
  });

  describe('translation content validation', () => {
    describe('Vietnamese translations', () => {
      it('should have required payment home translations', () => {
        expect(translations.vi.paymentHome).toBeDefined();
        expect(translations.vi.paymentHome.title).toBe('Thanh toán & nạp tiền');
        expect(translations.vi.paymentHome.topup).toBe('Nạp tiền');
        expect(translations.vi.paymentHome.billing).toBe('Thanh toán');
        expect(translations.vi.paymentHome.selectService).toBe('Chọn dịch vụ');
        expect(translations.vi.paymentHome.automaticRewards).toBe('Thanh toán tự động');
        expect(translations.vi.paymentHome.allCategory).toBe('Tất cả dịch vụ');
      });

      it('should have required billing tab translations', () => {
        expect(translations.vi.billingTab).toBeDefined();
        expect(translations.vi.billingTab.saved).toBe('Đã lưu');
        expect(translations.vi.billingTab.recent).toBe('Gần đây');
        expect(translations.vi.billingTab.search).toBe('Tên, số HĐ, mã khách hàng');
        expect(translations.vi.billingTab.titleEmpty).toBe('Danh sách hoá đơn trống');
      });

      it('should have required component translations', () => {
        expect(translations.vi.components).toBeDefined();
        expect(translations.vi.components.providerList).toBeDefined();
        expect(translations.vi.components.providerList.searchPlaceholder).toBe('Nhập nội dung');
        expect(translations.vi.components.providerList.noResultsTitle).toBe('Không tìm thấy kết quả');
      });

      it('should have required common translations', () => {
        expect(translations.vi.welcome).toBeDefined();
        expect(translations.vi.greeting).toBeDefined();
        expect(translations.vi.vnd).toBe('VND');
        expect(translations.vi.code).toBe('Mã: ');
        expect(translations.vi.edit).toBe('Chỉnh sửa');
        expect(translations.vi.delete).toBe('Xoá');
        expect(translations.vi.close).toBe('Đóng');
      });

      it('should have error message translations', () => {
        expect(translations.vi.oops).toBe('Có gián đoạn tạm thời');
        expect(translations.vi.errorOccurred).toBe('Quý khách vui lòng thử lại sau, xin lỗi vì sự bất tiện này');
      });
    });

    describe('English translations', () => {
      it('should have required payment home translations', () => {
        expect(translations.en.paymentHome).toBeDefined();
        expect(translations.en.paymentHome.title).toBe('Thanh toán và nạp tiền');
        expect(translations.en.paymentHome.topup).toBe('Nạp tiền');
        expect(translations.en.paymentHome.billing).toBe('Thanh toán');
      });

      it('should have required component translations', () => {
        expect(translations.en.components).toBeDefined();
        expect(translations.en.components.providerList).toBeDefined();
        expect(translations.en.components.providerList.searchPlaceholder).toBe('Enter content');
        expect(translations.en.components.providerList.noResultsTitle).toBe('No results found');
        expect(translations.en.components.providerList.noResultsMessage).toBe('Please search with different keywords');
      });

      it('should have required common translations', () => {
        expect(translations.en.vnd).toBe('VND');
        expect(translations.en.code).toBe('Code: ');
        expect(translations.en.close).toBe('Đóng');
      });
    });
  });

  describe('translation structure consistency', () => {
    it('should have consistent paymentHome structure', () => {
      const viPaymentHome = translations.vi.paymentHome;
      const enPaymentHome = translations.en.paymentHome;

      expect(Object.keys(viPaymentHome).sort()).toEqual(Object.keys(enPaymentHome).sort());
    });

    it('should have consistent billingTab structure', () => {
      const viBillingTab = translations.vi.billingTab;
      const enBillingTab = translations.en.billingTab;

      expect(Object.keys(viBillingTab).sort()).toEqual(Object.keys(enBillingTab).sort());
    });

    it('should have consistent components structure', () => {
      const viComponents = translations.vi.components;
      const enComponents = translations.en.components;

      expect(Object.keys(viComponents).sort()).toEqual(Object.keys(enComponents).sort());
      
      if (viComponents.providerList && enComponents.providerList) {
        expect(Object.keys(viComponents.providerList).sort()).toEqual(
          Object.keys(enComponents.providerList).sort()
        );
      }
    });

    it('should have consistent bottomTab structure', () => {
      const viBottomTab = translations.vi.bottomTab;
      const enBottomTab = translations.en.bottomTab;

      expect(Object.keys(viBottomTab).sort()).toEqual(Object.keys(enBottomTab).sort());
    });
  });

  describe('translation completeness', () => {
    it('should not have empty string values in vi translations', () => {
      const checkForEmptyStrings = (obj: any, path = ''): string[] => {
        const emptyKeys: string[] = [];
        
        for (const [key, value] of Object.entries(obj)) {
          const currentPath = path ? `${path}.${key}` : key;
          
          if (typeof value === 'string' && value.trim() === '') {
            emptyKeys.push(currentPath);
          } else if (typeof value === 'object' && value !== null) {
            emptyKeys.push(...checkForEmptyStrings(value, currentPath));
          }
        }
        
        return emptyKeys;
      };

      const emptyKeys = checkForEmptyStrings(translations.vi);
      expect(emptyKeys).toEqual([]);
    });

    it('should not have empty string values in en translations', () => {
      const checkForEmptyStrings = (obj: any, path = ''): string[] => {
        const emptyKeys: string[] = [];
        
        for (const [key, value] of Object.entries(obj)) {
          const currentPath = path ? `${path}.${key}` : key;
          
          if (typeof value === 'string' && value.trim() === '') {
            emptyKeys.push(currentPath);
          } else if (typeof value === 'object' && value !== null) {
            emptyKeys.push(...checkForEmptyStrings(value, currentPath));
          }
        }
        
        return emptyKeys;
      };

      const emptyKeys = checkForEmptyStrings(translations.en);
      expect(emptyKeys).toEqual([]);
    });

    it('should have all required payment-related keys', () => {
      const requiredKeys = [
        'paymentHome.title',
        'paymentHome.billing',
        'paymentHome.topup',
        'billingTab.saved',
        'billingTab.recent',
      ];

      requiredKeys.forEach(key => {
        const keys = key.split('.');
        let viValue = translations.vi;
        let enValue = translations.en;

        keys.forEach(k => {
          viValue = viValue[k];
          enValue = enValue[k];
        });

        expect(viValue).toBeDefined();
        expect(enValue).toBeDefined();
        expect(typeof viValue).toBe('string');
        expect(typeof enValue).toBe('string');
      });
    });
  });

  describe('special characters and formatting', () => {
    it('should handle Vietnamese special characters correctly', () => {
      expect(translations.vi.paymentHome.title).toContain('ả');
      expect(translations.vi.billingTab.titleEmpty).toContain('ó');
      expect(translations.vi.close).toBe('Đóng');
    });

    it('should handle newline characters in translations', () => {
      expect(translations.vi.billingTab.contentEmpty).toContain('\n');
      expect(translations.en.billingTab.contentEmpty).toContain('\n');
    });

    it('should handle special punctuation correctly', () => {
      expect(translations.vi.code).toBe('Mã: ');
      expect(translations.en.code).toBe('Code: ');
    });
  });

  describe('error handling', () => {
    it('should handle missing translation gracefully', () => {
      const {i18n} = require('msb-communication-lib');
      
      // Mock i18n.t to return the key when translation is missing
      i18n.t.mockImplementation((key: string) => key);
      
      const result = translate('nonexistent.key');
      expect(result).toBe('nonexistent.key');
    });

    it('should handle undefined keys gracefully', () => {
      const {i18n} = require('msb-communication-lib');
      
      i18n.t.mockImplementation((key: string) => key || 'undefined');
      
      const result = translate(undefined as any);
      expect(i18n.t).toHaveBeenCalledWith(undefined);
    });

    it('should handle null keys gracefully', () => {
      const {i18n} = require('msb-communication-lib');
      
      i18n.t.mockImplementation((key: string) => key || 'null');
      
      const result = translate(null as any);
      expect(i18n.t).toHaveBeenCalledWith(null);
    });
  });

  describe('performance considerations', () => {
    it('should handle rapid translation calls efficiently', () => {
      const {i18n} = require('msb-communication-lib');
      
      const startTime = performance.now();
      
      // Simulate rapid translation calls
      for (let i = 0; i < 1000; i++) {
        translate('paymentHome.title');
        translate('billingTab.saved');
        translate('components.providerList.searchPlaceholder');
      }
      
      const endTime = performance.now();
      
      expect(endTime - startTime).toBeLessThan(100); // Should complete quickly
      expect(i18n.t).toHaveBeenCalledTimes(3000);
    });

    it('should not cause memory leaks with frequent calls', () => {
      const {i18n} = require('msb-communication-lib');
      
      // Simulate frequent translation calls
      for (let i = 0; i < 100; i++) {
        translate(`dynamic.key.${i}`);
      }
      
      expect(i18n.t).toHaveBeenCalledTimes(100);
      // Should not throw or cause performance issues
      expect(true).toBe(true);
    });
  });

  describe('integration scenarios', () => {
    it('should work correctly in component context', () => {
      const {i18n} = require('msb-communication-lib');
      
      // Simulate component using translations
      const componentTranslations = {
        title: translate('paymentHome.title'),
        billing: translate('paymentHome.billing'),
        saved: translate('billingTab.saved'),
      };
      
      expect(i18n.t).toHaveBeenCalledWith('paymentHome.title');
      expect(i18n.t).toHaveBeenCalledWith('paymentHome.billing');
      expect(i18n.t).toHaveBeenCalledWith('billingTab.saved');
    });

    it('should maintain type safety with I18nKeys', () => {
      // This test verifies that the type system works correctly
      // In a real TypeScript environment, invalid keys would cause compile errors
      expect(() => {
        translate('paymentHome.title');
        translate('billingTab.saved');
        translate('components.providerList.searchPlaceholder');
      }).not.toThrow();
    });
  });
});
