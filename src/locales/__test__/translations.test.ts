import {describe, it, expect} from '@jest/globals';
import viTranslations from '../vi.json';
import enTranslations from '../en.json';

describe('Translation Files', () => {
  describe('Vietnamese translations (vi.json)', () => {
    it('should be valid JSON', () => {
      expect(viTranslations).toBeDefined();
      expect(typeof viTranslations).toBe('object');
    });

    it('should have required top-level keys', () => {
      const requiredKeys = [
        'welcome',
        'greeting',
        'vnd',
        'code',
        'edit',
        'delete',
        'bottomTab',
        'paymentHome',
        'billingTab',
        'oops',
        'errorOccurred',
        'close',
        'components',
      ];

      requiredKeys.forEach(key => {
        expect(viTranslations).toHaveProperty(key);
      });
    });

    it('should have complete paymentHome section', () => {
      expect(viTranslations.paymentHome).toBeDefined();
      expect(viTranslations.paymentHome.title).toBe('Thanh toán & nạp tiền');
      expect(viTranslations.paymentHome.topup).toBe('Nạp tiền');
      expect(viTranslations.paymentHome.billing).toBe('Thanh toán');
      expect(viTranslations.paymentHome.selectService).toBe('Chọn dịch vụ');
      expect(viTranslations.paymentHome.automaticRewards).toBe('Thanh toán tự động');
      expect(viTranslations.paymentHome.allCategory).toBe('Tất cả dịch vụ');
    });

    it('should have complete billingTab section', () => {
      expect(viTranslations.billingTab).toBeDefined();
      expect(viTranslations.billingTab.saved).toBe('Đã lưu');
      expect(viTranslations.billingTab.recent).toBe('Gần đây');
      expect(viTranslations.billingTab.search).toBe('Tên, số HĐ, mã khách hàng');
      expect(viTranslations.billingTab.titleSaved).toBe('Đã lưu');
      expect(viTranslations.billingTab.titleRecent).toBe('Gần đây');
      expect(viTranslations.billingTab.hintSearch).toBe('Tên, số HĐ, mã khách hàng');
      expect(viTranslations.billingTab.headerMyAccount).toBe('Tài khoản của tôi');
      expect(viTranslations.billingTab.headerFavorite).toBe('Yêu thích');
      expect(viTranslations.billingTab.titleEmpty).toBe('Danh sách hoá đơn trống');
      expect(viTranslations.billingTab.contentEmpty).toContain('Lưu thông tin hóa đơn');
      expect(viTranslations.billingTab.titleEmptyContactSystemError).toBe('Có lỗi tải trang');
      expect(viTranslations.billingTab.contentEmptyContactSystemError).toContain('Có lỗi không mong muốn');
      expect(viTranslations.billingTab.titleEmptyContactFiltered).toBe('Không có kết quả');
      expect(viTranslations.billingTab.contentEmptyContactFiltered).toBe('Vui lòng tìm với từ khoá khác');
      expect(viTranslations.billingTab.titleEmptyRecent).toBe('Chưa có dữ liệu');
      expect(viTranslations.billingTab.contentEmptyRecent).toBe('Chưa có hoá đơn thanh toán gần đây');
    });

    it('should have complete bottomTab section', () => {
      expect(viTranslations.bottomTab).toBeDefined();
      expect(viTranslations.bottomTab.home).toBe('Trang Chủ');
      expect(viTranslations.bottomTab.setting).toBe('Cài đặt');
      expect(viTranslations.bottomTab.asset).toBe('Tài sản');
    });

    it('should have complete components section', () => {
      expect(viTranslations.components).toBeDefined();
      expect(viTranslations.components.providerList).toBeDefined();
      expect(viTranslations.components.providerList.searchPlaceholder).toBe('Nhập nội dung');
      expect(viTranslations.components.providerList.noResultsTitle).toBe('Không tìm thấy kết quả');
      expect(viTranslations.components.providerList.noResultsMessage).toBe('Vui lòng tìm với từ khoá khác');
      expect(viTranslations.components.providerList.systemErrorTitle).toBe('Hệ thống gián đoạn');
      expect(viTranslations.components.providerList.systemErrorMessage).toBe('Quý khách vui lòng thử lại. Xin lỗi vì sự bất tiện này.');
    });

    it('should have proper Vietnamese characters', () => {
      expect(viTranslations.paymentHome.title).toContain('ả');
      expect(viTranslations.billingTab.titleEmpty).toContain('ó');
      expect(viTranslations.close).toBe('Đóng');
      expect(viTranslations.edit).toBe('Chỉnh sửa');
      expect(viTranslations.delete).toBe('Xoá');
    });

    it('should have consistent formatting', () => {
      expect(viTranslations.code).toBe('Mã: ');
      expect(viTranslations.vnd).toBe('VND');
    });

    it('should handle multiline content correctly', () => {
      expect(viTranslations.billingTab.contentEmpty).toContain('\n');
      expect(viTranslations.billingTab.contentEmptyContactSystemError).toContain('\n');
    });
  });

  describe('English translations (en.json)', () => {
    it('should be valid JSON', () => {
      expect(enTranslations).toBeDefined();
      expect(typeof enTranslations).toBe('object');
    });

    it('should have required top-level keys', () => {
      const requiredKeys = [
        'welcome',
        'greeting',
        'code',
        'vnd',
        'edit',
        'delete',
        'bottomTab',
        'paymentHome',
        'billingTab',
        'oops',
        'errorOccurred',
        'close',
        'components',
      ];

      requiredKeys.forEach(key => {
        expect(enTranslations).toHaveProperty(key);
      });
    });

    it('should have complete paymentHome section', () => {
      expect(enTranslations.paymentHome).toBeDefined();
      expect(enTranslations.paymentHome.title).toBe('Thanh toán và nạp tiền');
      expect(enTranslations.paymentHome.topup).toBe('Nạp tiền');
      expect(enTranslations.paymentHome.billing).toBe('Thanh toán');
      expect(enTranslations.paymentHome.selectService).toBe('Chọn dịch vụ');
      expect(enTranslations.paymentHome.automaticRewards).toBe('Thanh toán tự động');
      expect(enTranslations.paymentHome.allCategory).toBe('Tất cả dịch vụ');
    });

    it('should have complete billingTab section', () => {
      expect(enTranslations.billingTab).toBeDefined();
      expect(enTranslations.billingTab.saved).toBe('Đã lưu');
      expect(enTranslations.billingTab.recent).toBe('Gần đây');
      expect(enTranslations.billingTab.search).toBe('Tên, số HĐ, mã khách hàng');
      expect(enTranslations.billingTab.titleSaved).toBe('Đã lưu');
      expect(enTranslations.billingTab.titleRecent).toBe('Gần đây');
      expect(enTranslations.billingTab.hintSearch).toBe('Nhập nội dung');
    });

    it('should have complete components section with English content', () => {
      expect(enTranslations.components).toBeDefined();
      expect(enTranslations.components.providerList).toBeDefined();
      expect(enTranslations.components.providerList.searchPlaceholder).toBe('Enter content');
      expect(enTranslations.components.providerList.noResultsTitle).toBe('No results found');
      expect(enTranslations.components.providerList.noResultsMessage).toBe('Please search with different keywords');
      expect(enTranslations.components.providerList.systemErrorTitle).toBe('System interrupted');
      expect(enTranslations.components.providerList.systemErrorMessage).toBe('Please try again. Sorry for the inconvenience.');
    });

    it('should have proper English formatting', () => {
      expect(enTranslations.code).toBe('Code: ');
      expect(enTranslations.vnd).toBe('VND');
    });

    it('should handle multiline content correctly', () => {
      expect(enTranslations.billingTab.contentEmpty).toContain('\n');
      expect(enTranslations.billingTab.contentEmptyContactSystemError).toContain('\n');
    });
  });

  describe('Translation consistency between languages', () => {
    it('should have same structure in both languages', () => {
      const getKeys = (obj: any, prefix = ''): string[] => {
        const keys: string[] = [];
        for (const [key, value] of Object.entries(obj)) {
          const fullKey = prefix ? `${prefix}.${key}` : key;
          if (typeof value === 'object' && value !== null) {
            keys.push(...getKeys(value, fullKey));
          } else {
            keys.push(fullKey);
          }
        }
        return keys.sort();
      };

      const viKeys = getKeys(viTranslations);
      const enKeys = getKeys(enTranslations);

      expect(viKeys).toEqual(enKeys);
    });

    it('should have consistent paymentHome structure', () => {
      const viKeys = Object.keys(viTranslations.paymentHome).sort();
      const enKeys = Object.keys(enTranslations.paymentHome).sort();
      expect(viKeys).toEqual(enKeys);
    });

    it('should have consistent billingTab structure', () => {
      const viKeys = Object.keys(viTranslations.billingTab).sort();
      const enKeys = Object.keys(enTranslations.billingTab).sort();
      expect(viKeys).toEqual(enKeys);
    });

    it('should have consistent components structure', () => {
      const viKeys = Object.keys(viTranslations.components).sort();
      const enKeys = Object.keys(enTranslations.components).sort();
      expect(viKeys).toEqual(enKeys);

      const viProviderKeys = Object.keys(viTranslations.components.providerList).sort();
      const enProviderKeys = Object.keys(enTranslations.components.providerList).sort();
      expect(viProviderKeys).toEqual(enProviderKeys);
    });

    it('should have consistent bottomTab structure', () => {
      const viKeys = Object.keys(viTranslations.bottomTab).sort();
      const enKeys = Object.keys(enTranslations.bottomTab).sort();
      expect(viKeys).toEqual(enKeys);
    });
  });

  describe('Translation quality checks', () => {
    it('should not have empty strings in Vietnamese translations', () => {
      const checkEmptyStrings = (obj: any, path = ''): string[] => {
        const emptyKeys: string[] = [];
        for (const [key, value] of Object.entries(obj)) {
          const currentPath = path ? `${path}.${key}` : key;
          if (typeof value === 'string' && value.trim() === '') {
            emptyKeys.push(currentPath);
          } else if (typeof value === 'object' && value !== null) {
            emptyKeys.push(...checkEmptyStrings(value, currentPath));
          }
        }
        return emptyKeys;
      };

      const emptyKeys = checkEmptyStrings(viTranslations);
      expect(emptyKeys).toEqual([]);
    });

    it('should not have empty strings in English translations', () => {
      const checkEmptyStrings = (obj: any, path = ''): string[] => {
        const emptyKeys: string[] = [];
        for (const [key, value] of Object.entries(obj)) {
          const currentPath = path ? `${path}.${key}` : key;
          if (typeof value === 'string' && value.trim() === '') {
            emptyKeys.push(currentPath);
          } else if (typeof value === 'object' && value !== null) {
            emptyKeys.push(...checkEmptyStrings(value, currentPath));
          }
        }
        return emptyKeys;
      };

      const emptyKeys = checkEmptyStrings(enTranslations);
      expect(emptyKeys).toEqual([]);
    });

    it('should have reasonable string lengths', () => {
      const checkStringLengths = (obj: any, maxLength = 200): string[] => {
        const longKeys: string[] = [];
        for (const [key, value] of Object.entries(obj)) {
          if (typeof value === 'string' && value.length > maxLength) {
            longKeys.push(`${key}: ${value.length} characters`);
          } else if (typeof value === 'object' && value !== null) {
            longKeys.push(...checkStringLengths(value, maxLength));
          }
        }
        return longKeys;
      };

      const viLongKeys = checkStringLengths(viTranslations);
      const enLongKeys = checkStringLengths(enTranslations);

      // Most UI strings should be reasonably short
      expect(viLongKeys.length).toBeLessThan(5);
      expect(enLongKeys.length).toBeLessThan(5);
    });

    it('should not have placeholder text in production translations', () => {
      const checkPlaceholders = (obj: any): string[] => {
        const placeholderKeys: string[] = [];
        for (const [key, value] of Object.entries(obj)) {
          if (typeof value === 'string') {
            const lowerValue = value.toLowerCase();
            if (lowerValue.includes('todo') || 
                lowerValue.includes('placeholder') || 
                lowerValue.includes('lorem ipsum') ||
                lowerValue.includes('test')) {
              placeholderKeys.push(key);
            }
          } else if (typeof value === 'object' && value !== null) {
            placeholderKeys.push(...checkPlaceholders(value));
          }
        }
        return placeholderKeys;
      };

      const viPlaceholders = checkPlaceholders(viTranslations);
      const enPlaceholders = checkPlaceholders(enTranslations);

      expect(viPlaceholders).toEqual([]);
      expect(enPlaceholders).toEqual([]);
    });
  });

  describe('Special content validation', () => {
    it('should handle currency formatting consistently', () => {
      expect(viTranslations.vnd).toBe('VND');
      expect(enTranslations.vnd).toBe('VND');
    });

    it('should handle error messages appropriately', () => {
      expect(viTranslations.oops).toBeTruthy();
      expect(viTranslations.errorOccurred).toBeTruthy();
      expect(enTranslations.oops).toBeTruthy();
      expect(enTranslations.errorOccurred).toBeTruthy();

      expect(viTranslations.oops).toContain('gián đoạn');
      expect(viTranslations.errorOccurred).toContain('thử lại');
    });

    it('should handle action buttons consistently', () => {
      expect(viTranslations.edit).toBe('Chỉnh sửa');
      expect(viTranslations.delete).toBe('Xoá');
      expect(viTranslations.close).toBe('Đóng');
      
      expect(enTranslations.edit).toBe('Chỉnh sửa');
      expect(enTranslations.delete).toBe('Xoá');
      expect(enTranslations.close).toBe('Đóng');
    });

    it('should handle search and filter text appropriately', () => {
      expect(viTranslations.billingTab.search).toContain('số HĐ');
      expect(viTranslations.billingTab.hintSearch).toContain('mã khách hàng');
      expect(enTranslations.billingTab.hintSearch).toBe('Nhập nội dung');
      expect(enTranslations.components.providerList.searchPlaceholder).toBe('Enter content');
    });

    it('should handle empty state messages appropriately', () => {
      expect(viTranslations.billingTab.titleEmpty).toBe('Danh sách hoá đơn trống');
      expect(viTranslations.billingTab.contentEmpty).toContain('Lưu thông tin hóa đơn');
      expect(viTranslations.billingTab.titleEmptyRecent).toBe('Chưa có dữ liệu');
      expect(viTranslations.billingTab.contentEmptyRecent).toBe('Chưa có hoá đơn thanh toán gần đây');
    });
  });

  describe('File integrity', () => {
    it('should have valid JSON syntax in vi.json', () => {
      expect(() => JSON.stringify(viTranslations)).not.toThrow();
    });

    it('should have valid JSON syntax in en.json', () => {
      expect(() => JSON.stringify(enTranslations)).not.toThrow();
    });

    it('should be serializable and deserializable', () => {
      const viSerialized = JSON.stringify(viTranslations);
      const viDeserialized = JSON.parse(viSerialized);
      expect(viDeserialized).toEqual(viTranslations);

      const enSerialized = JSON.stringify(enTranslations);
      const enDeserialized = JSON.parse(enSerialized);
      expect(enDeserialized).toEqual(enTranslations);
    });
  });
});
