import React from 'react';
import {describe, it, expect, beforeEach, jest} from '@jest/globals';
import {
  render,
  screen,
  fireEvent,
  waitFor,
  MSB_POPUP_CONFIRM_TEST_ID,
  MSB_POPUP_CANCEL_TEST_ID,
  BOTTOM_SHEET_CONTAINER_TEST_ID,
} from './test-utils';
import {View, Text, TouchableOpacity} from 'react-native';
import {hostSharedModule} from 'msb-host-shared-module';

// Mock component for demonstration
const PaymentConfirmation = ({
  amount,
  recipient,
  onConfirm,
  onCancel,
}: {
  amount: number;
  recipient: string;
  onConfirm: () => void;
  onCancel: () => void;
}) => {
  const handleShowPopup = () => {
    hostSharedModule.d.domainService.showPopup({
      title: 'Confirm Payment',
      content: `Are you sure you want to pay ${amount.toLocaleString()} VND to ${recipient}?`,
      confirmBtnText: 'Confirm',
      cancelBtnText: 'Cancel',
      onConfirm: () => {
        hostSharedModule.d.domainService.hidePopup();
        onConfirm();
      },
      onCancel: () => {
        hostSharedModule.d.domainService.hidePopup();
        onCancel();
      },
    });
  };

  const handleShowToast = (type: 'success' | 'error', message: string) => {
    hostSharedModule.d.domainService.showToast({
      type,
      message,
      duration: 3000,
    });
  };

  const handleShowBottomSheet = () => {
    hostSharedModule.d.domainService.showBottomSheet({
      children: (
        <View style={{padding: 20}}>
          <Text>Payment Details</Text>
          <Text>Amount: {amount.toLocaleString()} VND</Text>
          <Text>Recipient: {recipient}</Text>
          <TouchableOpacity
            testID="bottom-sheet-confirm"
            onPress={() => {
              hostSharedModule.d.domainService.hideBottomSheet();
              handleShowPopup();
            }}>
            <Text>Proceed to Payment</Text>
          </TouchableOpacity>
        </View>
      ),
      onClose: () => {
        console.log('Bottom sheet closed');
      },
    });
  };

  return (
    <View testID="payment-confirmation">
      <Text>Payment Confirmation</Text>
      <Text testID="amount-text">{amount.toLocaleString()} VND</Text>
      <Text testID="recipient-text">{recipient}</Text>

      <TouchableOpacity testID="show-popup-btn" onPress={handleShowPopup}>
        <Text>Show Popup</Text>
      </TouchableOpacity>

      <TouchableOpacity
        testID="show-success-toast-btn"
        onPress={() => handleShowToast('success', 'Payment successful!')}>
        <Text>Show Success Toast</Text>
      </TouchableOpacity>

      <TouchableOpacity testID="show-error-toast-btn" onPress={() => handleShowToast('error', 'Payment failed!')}>
        <Text>Show Error Toast</Text>
      </TouchableOpacity>

      <TouchableOpacity testID="show-bottom-sheet-btn" onPress={handleShowBottomSheet}>
        <Text>Show Bottom Sheet</Text>
      </TouchableOpacity>
    </View>
  );
};

describe('PaymentConfirmation', () => {
  const mockOnConfirm = jest.fn();
  const mockOnCancel = jest.fn();

  const defaultProps = {
    amount: 250000,
    recipient: 'EVN - Electricity Vietnam',
    onConfirm: mockOnConfirm,
    onCancel: mockOnCancel,
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should render payment confirmation with correct data', () => {
    render(<PaymentConfirmation {...defaultProps} />);

    expect(screen.getByTestId('payment-confirmation')).toBeTruthy();
    expect(screen.getByTestId('amount-text')).toHaveTextContent('250,000 VND');
    expect(screen.getByTestId('recipient-text')).toHaveTextContent('EVN - Electricity Vietnam');
  });

  it('should show popup when popup button is pressed', async () => {
    render(<PaymentConfirmation {...defaultProps} />);

    fireEvent.press(screen.getByTestId('show-popup-btn'));

    await waitFor(() => {
      expect(screen.getByTestId('msb.popup')).toBeTruthy();
    });

    expect(screen.getByText('Confirm Payment')).toBeTruthy();
    expect(screen.getByText('Are you sure you want to pay 250,000 VND to EVN - Electricity Vietnam?')).toBeTruthy();
  });

  it('should handle popup confirm action', async () => {
    render(<PaymentConfirmation {...defaultProps} />);

    fireEvent.press(screen.getByTestId('show-popup-btn'));

    await waitFor(() => {
      expect(screen.getByTestId(MSB_POPUP_CONFIRM_TEST_ID)).toBeTruthy();
    });

    fireEvent.press(screen.getByTestId(MSB_POPUP_CONFIRM_TEST_ID));

    expect(mockOnConfirm).toHaveBeenCalledTimes(1);
    expect(hostSharedModule.d.domainService.hidePopup).toHaveBeenCalled();
  });

  it('should handle popup cancel action', async () => {
    render(<PaymentConfirmation {...defaultProps} />);

    fireEvent.press(screen.getByTestId('show-popup-btn'));

    await waitFor(() => {
      expect(screen.getByTestId(MSB_POPUP_CANCEL_TEST_ID)).toBeTruthy();
    });

    fireEvent.press(screen.getByTestId(MSB_POPUP_CANCEL_TEST_ID));

    expect(mockOnCancel).toHaveBeenCalledTimes(1);
    expect(hostSharedModule.d.domainService.hidePopup).toHaveBeenCalled();
  });

  it('should show success toast', async () => {
    render(<PaymentConfirmation {...defaultProps} />);

    fireEvent.press(screen.getByTestId('show-success-toast-btn'));

    await waitFor(() => {
      expect(screen.getByTestId('msb.toast')).toBeTruthy();
    });

    expect(screen.getByText('Payment successful!')).toBeTruthy();
    expect(hostSharedModule.d.domainService.showToast).toHaveBeenCalledWith({
      type: 'success',
      message: 'Payment successful!',
      duration: 3000,
    });
  });

  it('should show error toast', async () => {
    render(<PaymentConfirmation {...defaultProps} />);

    fireEvent.press(screen.getByTestId('show-error-toast-btn'));

    await waitFor(() => {
      expect(screen.getByTestId('msb.toast')).toBeTruthy();
    });

    expect(screen.getByText('Payment failed!')).toBeTruthy();
    expect(hostSharedModule.d.domainService.showToast).toHaveBeenCalledWith({
      type: 'error',
      message: 'Payment failed!',
      duration: 3000,
    });
  });

  it('should show bottom sheet with payment details', async () => {
    render(<PaymentConfirmation {...defaultProps} />);

    fireEvent.press(screen.getByTestId('show-bottom-sheet-btn'));

    await waitFor(() => {
      expect(screen.getByTestId(BOTTOM_SHEET_CONTAINER_TEST_ID)).toBeTruthy();
    });

    expect(screen.getByText('Payment Details')).toBeTruthy();
    expect(screen.getByText('Amount: 250,000 VND')).toBeTruthy();
    expect(screen.getByText('Recipient: EVN - Electricity Vietnam')).toBeTruthy();
  });

  it('should handle bottom sheet to popup flow', async () => {
    render(<PaymentConfirmation {...defaultProps} />);

    // Show bottom sheet
    fireEvent.press(screen.getByTestId('show-bottom-sheet-btn'));

    await waitFor(() => {
      expect(screen.getByTestId('bottom-sheet-confirm')).toBeTruthy();
    });

    // Press confirm in bottom sheet
    fireEvent.press(screen.getByTestId('bottom-sheet-confirm'));

    // Should hide bottom sheet and show popup
    expect(hostSharedModule.d.domainService.hideBottomSheet).toHaveBeenCalled();
    expect(hostSharedModule.d.domainService.showPopup).toHaveBeenCalled();
  });

  it('should verify domain service calls', () => {
    render(<PaymentConfirmation {...defaultProps} />);

    // Test popup
    fireEvent.press(screen.getByTestId('show-popup-btn'));
    expect(hostSharedModule.d.domainService.showPopup).toHaveBeenCalledWith(
      expect.objectContaining({
        title: 'Confirm Payment',
        confirmBtnText: 'Confirm',
        cancelBtnText: 'Cancel',
      }),
    );

    // Test toast
    fireEvent.press(screen.getByTestId('show-success-toast-btn'));
    expect(hostSharedModule.d.domainService.showToast).toHaveBeenCalledWith({
      type: 'success',
      message: 'Payment successful!',
      duration: 3000,
    });

    // Test bottom sheet
    fireEvent.press(screen.getByTestId('show-bottom-sheet-btn'));
    expect(hostSharedModule.d.domainService.showBottomSheet).toHaveBeenCalledWith(
      expect.objectContaining({
        children: expect.anything(),
        onClose: expect.any(Function),
      }),
    );
  });
});
