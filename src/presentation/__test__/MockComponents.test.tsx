import React from 'react';
import {describe, it, expect, beforeEach, jest} from '@jest/globals';
import {
  render,
  screen,
  fireEvent,
  waitFor,
  MSB_POPUP_CONFIRM_TEST_ID,
  MSB_POPUP_CANCEL_TEST_ID,
  BOTTOM_SHEET_CONTAINER_TEST_ID,
} from './test-utils';
import {View, Text, TouchableOpacity} from 'react-native';
import {hostSharedModule} from 'msb-host-shared-module';

// Simple test component to demonstrate mock UI components
const MockComponentDemo = () => {
  const handleShowPopup = () => {
    hostSharedModule.d.domainService.showPopup({
      title: 'Test Popup',
      content: 'This is a test popup message',
      confirmBtnText: 'OK',
      cancelBtnText: 'Cancel',
      onConfirm: () => {
        console.log('Popup confirmed');
        hostSharedModule.d.domainService.hidePopup();
      },
      onCancel: () => {
        console.log('Popup cancelled');
        hostSharedModule.d.domainService.hidePopup();
      },
    });
  };

  const handleShowToast = (type: 'success' | 'error') => {
    hostSharedModule.d.domainService.showToast({
      type,
      message: `This is a ${type} toast message`,
      duration: 3000,
    });
  };

  const handleShowBottomSheet = () => {
    hostSharedModule.d.domainService.showBottomSheet({
      children: (
        <View style={{padding: 20}}>
          <Text>Bottom Sheet Content</Text>
          <Text>This is a test bottom sheet</Text>
          <TouchableOpacity
            testID="bottom-sheet-close-btn"
            onPress={() => hostSharedModule.d.domainService.hideBottomSheet()}>
            <Text>Close</Text>
          </TouchableOpacity>
        </View>
      ),
      onClose: () => {
        console.log('Bottom sheet closed');
      },
    });
  };

  return (
    <View testID="mock-component-demo">
      <Text>Mock Component Demo</Text>
      
      <TouchableOpacity testID="show-popup-btn" onPress={handleShowPopup}>
        <Text>Show Popup</Text>
      </TouchableOpacity>
      
      <TouchableOpacity testID="show-success-toast-btn" onPress={() => handleShowToast('success')}>
        <Text>Show Success Toast</Text>
      </TouchableOpacity>
      
      <TouchableOpacity testID="show-error-toast-btn" onPress={() => handleShowToast('error')}>
        <Text>Show Error Toast</Text>
      </TouchableOpacity>
      
      <TouchableOpacity testID="show-bottom-sheet-btn" onPress={handleShowBottomSheet}>
        <Text>Show Bottom Sheet</Text>
      </TouchableOpacity>
    </View>
  );
};

describe('Mock Components Integration', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should render demo component', () => {
    render(<MockComponentDemo />);
    
    expect(screen.getByTestId('mock-component-demo')).toBeTruthy();
    expect(screen.getByText('Mock Component Demo')).toBeTruthy();
    expect(screen.getByTestId('show-popup-btn')).toBeTruthy();
    expect(screen.getByTestId('show-success-toast-btn')).toBeTruthy();
    expect(screen.getByTestId('show-error-toast-btn')).toBeTruthy();
    expect(screen.getByTestId('show-bottom-sheet-btn')).toBeTruthy();
  });

  it('should show and interact with popup', async () => {
    render(<MockComponentDemo />);

    // Show popup
    fireEvent.press(screen.getByTestId('show-popup-btn'));

    await waitFor(() => {
      expect(screen.getByTestId('msb.popup')).toBeTruthy();
    });

    expect(screen.getByText('Test Popup')).toBeTruthy();
    expect(screen.getByText('This is a test popup message')).toBeTruthy();
    expect(screen.getByTestId(MSB_POPUP_CONFIRM_TEST_ID)).toBeTruthy();
    expect(screen.getByTestId(MSB_POPUP_CANCEL_TEST_ID)).toBeTruthy();
  });

  it('should handle popup confirm action', async () => {
    render(<MockComponentDemo />);

    fireEvent.press(screen.getByTestId('show-popup-btn'));

    await waitFor(() => {
      expect(screen.getByTestId(MSB_POPUP_CONFIRM_TEST_ID)).toBeTruthy();
    });

    fireEvent.press(screen.getByTestId(MSB_POPUP_CONFIRM_TEST_ID));

    expect(hostSharedModule.d.domainService.hidePopup).toHaveBeenCalled();
  });

  it('should handle popup cancel action', async () => {
    render(<MockComponentDemo />);

    fireEvent.press(screen.getByTestId('show-popup-btn'));

    await waitFor(() => {
      expect(screen.getByTestId(MSB_POPUP_CANCEL_TEST_ID)).toBeTruthy();
    });

    fireEvent.press(screen.getByTestId(MSB_POPUP_CANCEL_TEST_ID));

    expect(hostSharedModule.d.domainService.hidePopup).toHaveBeenCalled();
  });

  it('should show success toast', async () => {
    render(<MockComponentDemo />);

    fireEvent.press(screen.getByTestId('show-success-toast-btn'));

    await waitFor(() => {
      expect(screen.getByTestId('msb.toast')).toBeTruthy();
    });

    expect(screen.getByText('This is a success toast message')).toBeTruthy();
    expect(hostSharedModule.d.domainService.showToast).toHaveBeenCalledWith({
      type: 'success',
      message: 'This is a success toast message',
      duration: 3000,
    });
  });

  it('should show error toast', async () => {
    render(<MockComponentDemo />);

    fireEvent.press(screen.getByTestId('show-error-toast-btn'));

    await waitFor(() => {
      expect(screen.getByTestId('msb.toast')).toBeTruthy();
    });

    expect(screen.getByText('This is a error toast message')).toBeTruthy();
    expect(hostSharedModule.d.domainService.showToast).toHaveBeenCalledWith({
      type: 'error',
      message: 'This is a error toast message',
      duration: 3000,
    });
  });

  it('should show bottom sheet', async () => {
    render(<MockComponentDemo />);

    fireEvent.press(screen.getByTestId('show-bottom-sheet-btn'));

    await waitFor(() => {
      expect(screen.getByTestId(BOTTOM_SHEET_CONTAINER_TEST_ID)).toBeTruthy();
    });

    expect(screen.getByText('Bottom Sheet Content')).toBeTruthy();
    expect(screen.getByText('This is a test bottom sheet')).toBeTruthy();
    expect(screen.getByTestId('bottom-sheet-close-btn')).toBeTruthy();
  });

  it('should handle bottom sheet close action', async () => {
    render(<MockComponentDemo />);

    fireEvent.press(screen.getByTestId('show-bottom-sheet-btn'));

    await waitFor(() => {
      expect(screen.getByTestId('bottom-sheet-close-btn')).toBeTruthy();
    });

    fireEvent.press(screen.getByTestId('bottom-sheet-close-btn'));

    expect(hostSharedModule.d.domainService.hideBottomSheet).toHaveBeenCalled();
  });

  it('should verify domain service calls', () => {
    render(<MockComponentDemo />);

    // Test popup
    fireEvent.press(screen.getByTestId('show-popup-btn'));
    expect(hostSharedModule.d.domainService.showPopup).toHaveBeenCalledWith(
      expect.objectContaining({
        title: 'Test Popup',
        content: 'This is a test popup message',
        confirmBtnText: 'OK',
        cancelBtnText: 'Cancel',
      })
    );

    // Test toast
    fireEvent.press(screen.getByTestId('show-success-toast-btn'));
    expect(hostSharedModule.d.domainService.showToast).toHaveBeenCalledWith({
      type: 'success',
      message: 'This is a success toast message',
      duration: 3000,
    });

    // Test bottom sheet
    fireEvent.press(screen.getByTestId('show-bottom-sheet-btn'));
    expect(hostSharedModule.d.domainService.showBottomSheet).toHaveBeenCalledWith(
      expect.objectContaining({
        children: expect.anything(),
        onClose: expect.any(Function),
      })
    );
  });
});
