import React, {ReactElement, useEffect, useRef} from 'react';
import {render, RenderOptions} from '@testing-library/react-native';
import {SafeAreaProvider} from 'react-native-safe-area-context';
import {GestureHandlerRootView} from 'react-native-gesture-handler';
import MSBBottomSheet, {BOTTOM_SHEET_CONTAINER_TEST_ID} from '../../../__mocks__/bottom-sheet/MSBBottomSheet';
import MSBPopup, {MSB_POPUP_CANCEL_TEST_ID, MSB_POPUP_CONFIRM_TEST_ID} from '../../../__mocks__/popup/MSBPopup';
import MSBToast from '../../../__mocks__/toast/MSBToast';
import {BottomSheetActions, PopupActions, ToastActions} from 'msb-host-shared-module';

// Mock screen dimensions
export const mockScreenDimensions = {
  width: 390,
  height: 844,
  scale: 3,
  fontScale: 1,
};

// Mock window dimensions
export const mockWindowDimensions = {
  width: 390,
  height: 844,
  scale: 3,
  fontScale: 1,
};

// Custom render function with providers
const AllTheProviders = ({children}: {children: React.ReactNode}) => {
  const bottomSheetRef = useRef<BottomSheetActions>(null);
  const popupRef = useRef<PopupActions>(null);
  const toastRef = useRef<ToastActions>(null);

  useEffect(() => {
    // Setup global refs for testing
    global._MSB_TEST_BOTTOM_SHEET = bottomSheetRef;
    global._MSB_TEST_POPUP = popupRef;
    global._MSB_TEST_TOAST = toastRef;
  }, []);

  return (
    <GestureHandlerRootView style={{flex: 1}}>
      <SafeAreaProvider
        initialMetrics={{
          frame: {x: 0, y: 0, width: 390, height: 844},
          insets: {top: 47, left: 0, right: 0, bottom: 34},
        }}>
        {children}
        <MSBBottomSheet ref={bottomSheetRef} />
        <MSBPopup ref={popupRef} />
        <MSBToast ref={toastRef} />
      </SafeAreaProvider>
    </GestureHandlerRootView>
  );
};

const customRender = (ui: ReactElement, options?: Omit<RenderOptions, 'wrapper'>) =>
  render(ui, {wrapper: AllTheProviders, ...options});

// Test helper functions
export const createMockNavigation = () => ({
  navigate: jest.fn(),
  goBack: jest.fn(),
  reset: jest.fn(),
  setParams: jest.fn(),
  dispatch: jest.fn(),
  setOptions: jest.fn(),
  isFocused: jest.fn(() => true),
  canGoBack: jest.fn(() => false),
  getId: jest.fn(() => 'test-screen'),
  getParent: jest.fn(),
  getState: jest.fn(),
});

export const createMockRoute = (params = {}) => ({
  key: 'test-route-key',
  name: 'TestScreen',
  params,
  path: undefined,
});

export const waitForNextUpdate = () => new Promise(resolve => setTimeout(resolve, 0));

export const flushPromises = () => new Promise(resolve => setImmediate(resolve));

// Mock async storage helpers
export const mockAsyncStorage = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
  getAllKeys: jest.fn(),
  multiGet: jest.fn(),
  multiSet: jest.fn(),
  multiRemove: jest.fn(),
};

// Mock dimensions
export const mockDimensions = {
  get: jest.fn((dimension: 'window' | 'screen') => {
    if (dimension === 'window') return mockWindowDimensions;
    return mockScreenDimensions;
  }),
  addEventListener: jest.fn(),
  removeEventListener: jest.fn(),
};

// Mock keyboard
export const mockKeyboard = {
  addListener: jest.fn(),
  removeListener: jest.fn(),
  removeAllListeners: jest.fn(),
  dismiss: jest.fn(),
  scheduleLayoutAnimation: jest.fn(),
};

// Mock alert
export const mockAlert = {
  alert: jest.fn(),
  prompt: jest.fn(),
};

// Mock linking
export const mockLinking = {
  openURL: jest.fn(),
  canOpenURL: jest.fn(() => Promise.resolve(true)),
  getInitialURL: jest.fn(() => Promise.resolve(null)),
  addEventListener: jest.fn(),
  removeEventListener: jest.fn(),
};

// Mock permission
export const mockPermissions = {
  check: jest.fn(() => Promise.resolve('granted')),
  request: jest.fn(() => Promise.resolve('granted')),
  requestMultiple: jest.fn(() => Promise.resolve({})),
  checkMultiple: jest.fn(() => Promise.resolve({})),
};

// Mock haptic feedback
export const mockHapticFeedback = {
  impact: jest.fn(),
  notification: jest.fn(),
  selection: jest.fn(),
};

// Mock device info
export const mockDeviceInfo = {
  getDeviceId: jest.fn(() => 'mock-device-id'),
  getSystemName: jest.fn(() => 'iOS'),
  getSystemVersion: jest.fn(() => '17.0'),
  getModel: jest.fn(() => 'iPhone'),
  getBrand: jest.fn(() => 'Apple'),
  getBundleId: jest.fn(() => 'com.msb.payment'),
  getVersion: jest.fn(() => '1.0.0'),
  getBuildNumber: jest.fn(() => '1'),
};

// Export mock component test IDs and utilities
export {BOTTOM_SHEET_CONTAINER_TEST_ID, MSB_POPUP_CANCEL_TEST_ID, MSB_POPUP_CONFIRM_TEST_ID};

// Mock payment data helpers
export const mockPaymentMethods = [
  {
    id: 'bank_transfer',
    name: 'Bank Transfer',
    type: 'BANK_TRANSFER',
    enabled: true,
  },
  {
    id: 'credit_card',
    name: 'Credit Card',
    type: 'CREDIT_CARD',
    enabled: true,
  },
];

export const mockBillProviders = [
  {
    id: 'evn',
    name: 'EVN - Electricity Vietnam',
    code: 'EVN',
    categoryId: 'electricity',
  },
  {
    id: 'viettel',
    name: 'Viettel Mobile',
    code: 'VIETTEL',
    categoryId: 'mobile',
  },
];

export const mockTransactionHistory = [
  {
    id: 'TXN123',
    type: 'BILL_PAYMENT',
    status: 'SUCCESS',
    amount: 250000,
    currency: 'VND',
    timestamp: '2024-07-02T10:30:00Z',
    description: 'EVN Electricity Bill Payment',
  },
];

// Re-export everything from testing-library
export * from '@testing-library/react-native';

// Override the default render with our custom render
export {customRender as render};
