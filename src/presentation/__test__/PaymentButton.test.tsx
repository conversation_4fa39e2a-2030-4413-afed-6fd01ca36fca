import React from 'react';
import {describe, it, expect, jest} from '@jest/globals';
import {render, fireEvent, screen} from './test-utils';
import {Text, TouchableOpacity, StyleSheet} from 'react-native';

// Example component
interface PaymentButtonProps {
  amount: number;
  currency?: string;
  onPress: () => void;
  disabled?: boolean;
  loading?: boolean;
}

const PaymentButton: React.FC<PaymentButtonProps> = ({
  amount,
  currency = 'VND',
  onPress,
  disabled = false,
  loading = false,
}) => {
  const formatAmount = (amount: number, currency: string) => {
    if (currency === 'VND') {
      return `${amount.toLocaleString('vi-VN')} ₫`;
    }
    return `${currency} ${amount}`;
  };

  return (
    <TouchableOpacity
      style={[styles.button, disabled && styles.disabled]}
      onPress={onPress}
      disabled={disabled || loading}
      testID="payment-button">
      <Text style={[styles.text, disabled && styles.disabledText]}>
        {loading ? 'Processing...' : `Pay ${formatAmount(amount, currency)}`}
      </Text>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  button: {
    backgroundColor: '#007AFF',
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  disabled: {
    backgroundColor: '#CCCCCC',
  },
  text: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  disabledText: {
    color: '#666666',
  },
});

// Tests
describe('PaymentButton', () => {
  const mockOnPress = jest.fn();

  beforeEach(() => {
    mockOnPress.mockClear();
  });

  it('should render with correct amount and currency', () => {
    render(<PaymentButton amount={100000} currency="VND" onPress={mockOnPress} />);

    expect(screen.getByText('Pay 100.000 ₫')).toBeTruthy();
  });

  it('should render with USD currency', () => {
    render(<PaymentButton amount={100} currency="USD" onPress={mockOnPress} />);

    expect(screen.getByText('Pay USD 100')).toBeTruthy();
  });

  it('should call onPress when button is pressed', () => {
    render(<PaymentButton amount={50000} onPress={mockOnPress} />);

    const button = screen.getByTestId('payment-button');
    fireEvent.press(button);

    expect(mockOnPress).toHaveBeenCalledTimes(1);
  });

  it('should not call onPress when disabled', () => {
    render(<PaymentButton amount={50000} onPress={mockOnPress} disabled={true} />);

    const button = screen.getByTestId('payment-button');
    fireEvent.press(button);

    expect(mockOnPress).not.toHaveBeenCalled();
  });

  it('should show loading state', () => {
    render(<PaymentButton amount={50000} onPress={mockOnPress} loading={true} />);

    expect(screen.getByText('Processing...')).toBeTruthy();
  });

  it('should not call onPress when loading', () => {
    render(<PaymentButton amount={50000} onPress={mockOnPress} loading={true} />);

    const button = screen.getByTestId('payment-button');
    fireEvent.press(button);

    expect(mockOnPress).not.toHaveBeenCalled();
  });

  it('should have correct accessibility properties', () => {
    render(<PaymentButton amount={50000} onPress={mockOnPress} />);

    const button = screen.getByTestId('payment-button');
    expect(button.props.accessible).toBe(true);
  });

  it('should handle large amounts correctly', () => {
    render(<PaymentButton amount={1000000} currency="VND" onPress={mockOnPress} />);

    expect(screen.getByText('Pay 1.000.000 ₫')).toBeTruthy();
  });
});
