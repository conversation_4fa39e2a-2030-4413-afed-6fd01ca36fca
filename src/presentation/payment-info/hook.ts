import {RouteProp, useNavigation, useRoute, NavigationProp} from '@react-navigation/native';

import ScreenNames from '../../commons/ScreenNames';
import {PaymentStackParamList} from '../../navigation/PaymentStack';
import {useCallback, useEffect, useMemo, useState} from 'react';
import {DIContainer} from '../../di/DIContainer';
import {showErrorPopup} from '../../utils/PopupUtils';
import {SourceAccountModel} from '../../domain/entities/source-account-list/SourceAccountListModel';
import {hostSharedModule} from 'msb-host-shared-module';
import {BillValidateRequest} from '../../data/models/bill-validate/BillValidateRequest';
import {PAYMENT_TYPE} from '../../commons/Constants';
import {Configs} from '../../commons/Configs';
import {translate} from '../../locales/i18n';
import PaymentInfoUtils from './utils/Utils';
import {MSBErrorCode} from '../../core/MSBErrorCode';

const usePaymentInfo = (
  renderSourceAccountList: (accountList: SourceAccountModel[], onSelectAccount: () => void) => React.ReactNode,
  renderBiometricAuthentication?: () => React.ReactNode,
  renderIdentification?: () => React.ReactNode,
) => {
  const route = useRoute<RouteProp<PaymentStackParamList, 'PaymentInfoScreen'>>();
  const navigation = useNavigation<NavigationProp<PaymentStackParamList, 'PaymentInfoScreen'>>();
  const {paymentInfo} = route.params;

  // const paymentInfo = _paymentInfo;

  // state
  const [sourceAcc, setSourceAcc] = useState<SourceAccountModel[]>(); // danh sách tài khoản nguồn
  const [sourceAccDefault, setSourceAccDefault] = useState<SourceAccountModel>(); // tài khoản nguồn: default
  const [isLoadingSourceAccount, setLoadingSourceAccount] = useState<boolean>(false);
  const [isLoading, setLoading] = useState<boolean>(false);

  const onContinue = () => {
    billValidate();
    // navigation.navigate(ScreenNames.PaymentConfirmScreen);
  };

  const totalAmount = useMemo(() => {
    const _totalAmount = paymentInfo?.billInfo?.billList?.reduce((sum, bill) => sum + (bill.amount || 0), 0);
    return _totalAmount;
  }, [paymentInfo]);

  const goPaymentConfirm = useCallback(
    (billValidateInfo?: BillValidateRequest, id?: string) => {
      console.log('billValidateInfo', billValidateInfo);

      navigation.navigate(ScreenNames.PaymentConfirmScreen, {
        paymentInfo: {
          ...paymentInfo,
          paymentValidate: {...billValidateInfo, id: id ?? ''},
          originatorAccount: {
            identification: sourceAccDefault?.id ?? '',
            name: sourceAccDefault?.name ?? '',
            accountNo: sourceAccDefault?.BBAN ?? '',
            bankName: 'MSB',
            bankCode: Configs.MSB_BANKID,
          },
        },
      });
    },
    [navigation, paymentInfo, sourceAccDefault],
  );

  const billValidate = useCallback(async () => {
    setLoading(true);
    const requestedExecutionDate: string = new Date().toISOString();
    const summary = {
      totalAmount: totalAmount,
      debitAmount: totalAmount,
      billQuantity: paymentInfo?.billInfo?.billList?.length,
      cashbackAmount: 0,
      discountAmount: 0,
    };
    const params: BillValidateRequest = {
      originatorAccount: {
        identification: {
          identification: sourceAccDefault?.id ?? '',
          schemeName: 'ID',
        },
      },
      requestedExecutionDate,
      paymentType: PAYMENT_TYPE.BILLING_ACCOUNT,
      transferTransactionInformation: {
        instructedAmount: {
          amount: totalAmount?.toString() ?? '',
          currencyCode: 'VND',
        },
        counterparty: {
          name: paymentInfo?.billInfo?.customerInfo?.name ?? '',
        },
        counterpartyAccount: {
          identification: {
            identification: paymentInfo.billInfo?.billCode ?? '',
            schemeName: 'IBAN',
          },
        },
        additions: {
          bpQueryRef: paymentInfo?.billInfo?.queryRef ?? '',
          bpBillList: JSON.stringify(
            paymentInfo?.billInfo?.billList?.map(e => ({id: e.id, amount: e.amount, period: e.period})),
          ),
          bpSummary: JSON.stringify(summary),
          bpServiceCode: paymentInfo?.billInfo?.service?.code ?? '',
          cifNo: paymentInfo?.billInfo?.customerInfo?.cif ?? '',
          bpCategory: paymentInfo.billInfo?.getCategoryCode?.() ?? '',
          // bpAccountingNumber: sourceAccDefault?.BBAN ?? '',
        },
      },
    };
    console.log('request params', params);
    const result = await DIContainer.getInstance().getBillValidateUseCase().execute(params);
    setLoading(false);
    console.log('result', result);
    if (result.status === 'SUCCESS') {
      goPaymentConfirm(params, result.data?.id ?? '');
    } else if (result.status === 'ERROR') {
      const errorKey = result?.error?.code;
      switch (errorKey) {
        case MSBErrorCode.FTES0001: // Gói truy vấn
          PaymentInfoUtils.checkIBMB();
          break;
        case MSBErrorCode.FTES0008: // Sinh trắc học
          if (renderBiometricAuthentication) {
            PaymentInfoUtils.checkBiometricAuthentication(renderBiometricAuthentication());
          }
          break;
        case MSBErrorCode.FTES0009: // Giấy tờ tuỳ thân
          if (renderIdentification) {
            PaymentInfoUtils.checkIdentification(renderIdentification());
          }
          break;
        case MSBErrorCode.BMS0017: // Tài khoản thụ hưởng không hợp lệ
          PaymentInfoUtils.userNotValid(() => navigation.goBack());
          break;
        default:
          PaymentInfoUtils.checkErrorSystem('', () => navigation.goBack(), errorKey);
          break;
      }
    }
  }, [
    goPaymentConfirm,
    paymentInfo,
    sourceAccDefault,
    totalAmount,
    navigation,
    renderBiometricAuthentication,
    renderIdentification,
  ]);

  const getSourceAccountList = useCallback(async () => {
    setLoadingSourceAccount(true);
    const result = await DIContainer.getInstance().getSourceAccountListUseCase().execute();
    setLoadingSourceAccount(false);
    if (result.status === 'ERROR') {
      showErrorPopup(result.error);
      return;
    }
    const sourceAccount: SourceAccountModel[] = (result?.data?.data ?? []).filter(
      item => item?.userPreferences?.visible !== false,
    );
    const sourceAccountDefault = sourceAccount?.find(arrangement => arrangement?.isDefault === 'Y');
    setSourceAcc(sourceAccount);
    setSourceAccDefault(sourceAccountDefault);
  }, []);

  // show bottom sheet chọn tài khoản nguồn
  const openSelectAccount = () => {
    hostSharedModule.d.domainService?.showBottomSheet({
      header: translate('paymentInfor.sourceAccount'),
      children: renderSourceAccountList(sourceAcc!, onSelectAccount),
    });
  };

  // chọn tài khoản nguồn
  const onSelectAccount = (sourceAccountDefault?: SourceAccountModel) => {
    hostSharedModule.d.domainService?.hideBottomSheet();
    setSourceAccDefault(sourceAccountDefault);
  };

  const goHome = () => {
    hostSharedModule.d.domainService?.showPopup({
      iconType: 'WARNING',
      title: translate('paymentConfirm.endOfTransaction'),
      content: translate('paymentConfirm.endOfTransactionDescription'),
      cancelBtnText: translate('paymentConfirm.endOfTransaction'),
      confirmBtnText: translate('paymentConfirm.close'),
      onCancel: () =>
        navigation?.reset({
          index: 0,
          routes: [
            {
              name: 'SegmentStack',
            },
          ],
        }),
    });
  };

  useEffect(() => {
    getSourceAccountList();
  }, [getSourceAccountList]);

  return {
    onContinue,
    paymentInfo,
    openSelectAccount,
    onSelectAccount,
    goHome,
    // state
    sourceAcc,
    sourceAccDefault,
    isLoadingSourceAccount,
    isLoading,
    totalAmount,
  };
};

export type Props = ReturnType<typeof usePaymentInfo>;

export default usePaymentInfo;
