import {
  MSBAmountInput,
  MSBButton,
  MSBFastImage,
  MSBFolderImage,
  MSBLoadingItemSkeleton,
  MSBPage,
  MSBScrollView,
  MSBTextBase,
  MSBTouchable,
  SizeGlobal,
  Tpg,
  useMSBStyles,
} from 'msb-shared-component';
import React from 'react';
import {View} from 'react-native';

import useQRPaymentInfo from './hook.ts';
import {translate} from '../../locales/i18n.ts';
import {makeStyle} from './styles.ts';
import {Federated} from '@callstack/repack/client';
import SourceAccountList from '../../components/source-account-list/index.tsx';
import PaymentBillInfo from '../../components/payment-bill-info/index.tsx';
import TransferContentInput from '../../components/transfer-content-input/index.tsx';
import Utils from '../../utils/Utils.ts';
import BiometricAuthentication from './components/biometric-authentication/index.tsx';
import AmountSuggest from './components/amount-suggest/index.tsx';
import {useSafeAreaInsets} from 'react-native-safe-area-context';

/**
 * Khi ở build time sẽ sử dụng MFv1 nên là không thể sử dụng dynamic import của MFv2
 * => Sử dụng {@link Federated.importModule} để bundle
 *
 * Lưu ý!: không tách biệt điều kiện process.env.NODE_ENV === 'production' ra hàm riêng biệt vì lúc build sẽ làm cho dynamic import() được bundle vào
 * => gây ra lỗi lúc build
 *
 * Không thể sử dụng được dynamic import() từ hàm
 * => !Nên mỗi khi load module thì phải định nghĩa lại như dưới
 */
const SourceAccount = React.lazy(() =>
  process.env.MF_VERSION !== 'v2' || process.env.NODE_ENV === 'production'
    ? Federated.importModule('TransferModule', './SourceAccount')
    : import('TransferModule/SourceAccount'),
);
const QRPaymentInfoScreen = () => {
  const {
    onContinue,
    paymentInfo,
    sourceAccDefault,
    isLoadingSourceAccount,
    onSelectAccount,
    isLoading,
    goQrScreen,
    amountErrorMessage,
    disableTransferBtn,
    amount,
    remark, // nội dung chuyển khoanr
    setRemark,
    amountOnChangeText,
    amountSuggestList,
    fillAmountSuggest,
    inputAmountRef,
    isAmountFocused,
    setAmountFocused,
    sourceAcc,
  } = useQRPaymentInfo(
    accountList => (
      <SourceAccountList accSelected={sourceAccDefault} accountList={accountList!} onSelectAcount={onSelectAccount} />
    ),
    () => (
      <BiometricAuthentication content={translate('qrPaymentInfo.qrPaymentInfoValidate.biometricAuthentication')} />
    ),
    () => <BiometricAuthentication content={translate('qrPaymentInfo.qrPaymentInfoValidate.identification')} />,
    // (transferLimits, onChangeTransferLimits) => (
    //   <TransferLimits limits={transferLimits} onChangeTransferLimits={onChangeTransferLimits} />
    // ),
  );

  const {styles, theme} = useMSBStyles(makeStyle);
  const {SizeAlias} = theme;
  const insets = useSafeAreaInsets();
  const insetsBottom = insets?.bottom > 0 ? insets.bottom : SizeAlias.SpacingMedium;

  return (
    <MSBPage
      isScrollable={false}
      headerProps={{
        hasBack: true,
        rightButtons: [{icon: 'tone-qr', onPress: goQrScreen}],
        title: `${translate('qrPaymentInfo.payment')}`,
      }}
      style={{paddingBottom: 0}}>
      <MSBScrollView
        keyboardShouldPersistTaps="handled"
        showsVerticalScrollIndicator={false}
        style={styles.container}
        contentContainerStyle={styles.scrollViewContentContainer}>
        <PaymentBillInfo
          style={styles.accountInfo}
          merchantName={paymentInfo?.merchantName ?? ''}
          storeId={paymentInfo?.storeId ?? ''}
        />

        <View style={styles.contentContainer}>
          {isLoadingSourceAccount ? (
            <MSBLoadingItemSkeleton loading={isLoadingSourceAccount} />
          ) : (
            <SourceAccount
              title={translate('paymentInfor.sourceAccount')}
              onSelectAccount={onSelectAccount}
              sourceAccounts={sourceAcc}
              accNo={sourceAccDefault?.BBAN}
              hasCreditCard={false}
            />
          )}
          <MSBAmountInput
            inputRef={inputAmountRef}
            testID={'transfer.transferInfoScreen.enterAmount'}
            value={amount}
            placeholder={translate('qrPaymentInfo.amountPlaceholder')}
            containerStyle={styles.amountInput}
            maxLength={12}
            onFocus={() => setAmountFocused(true)}
            onBlur={() => setAmountFocused(false)}
            disabled={!Utils.isEmpty(paymentInfo?.amount)}
            onChangeText={amountOnChangeText}
            contentLabel={<AmountLabel openBottomSheetTransferLimits={() => {}} />}
            errorContent={amountErrorMessage}
            label={''}
            isDisableRemoveIcon={!Utils.isEmpty(paymentInfo?.amount)}
          />
          <TransferContentInput
            testID={'transfer.transferInfoScreen.enterContent'}
            containerStyle={styles.amountInput}
            label={translate('common.content')}
            placeholder={translate('common.contentPlaceholder')}
            disabled={!Utils.isEmpty(paymentInfo?.remark)}
            value={remark}
            onChangeText={(text: string) => {
              setRemark(text);
            }}
            isDisableRemoveIcon={!Utils.isEmpty(paymentInfo?.remark)}
          />
        </View>
      </MSBScrollView>
      <MSBButton
        testID={'payment.transferInfoScreen.pressToContinue'}
        label={'Tiếp tục'}
        isLoading={isLoading}
        style={styles.btnContinue(insetsBottom)}
        onPress={onContinue}
        disabled={disableTransferBtn}
      />
      {/* <AmountSuggest amountSuggest={amountSuggestList} onPress={fillAmountSuggest} /> */}
      {isAmountFocused && <AmountSuggest amountSuggest={amountSuggestList} onPress={fillAmountSuggest} />}
    </MSBPage>
  );
};

const AmountLabel = ({openBottomSheetTransferLimits}: {openBottomSheetTransferLimits: () => void}) => {
  const {styles} = useMSBStyles(makeStyle);
  return (
    <View style={styles.flexDirectionRow}>
      <MSBTextBase content={translate('qrPaymentInfo.amount')} type={Tpg.small_medium} style={styles.contentLabel} />
      <MSBTouchable onPress={openBottomSheetTransferLimits}>
        <MSBFastImage
          folder={MSBFolderImage.ICON_SVG}
          nameImage="info-tooltip-circle"
          style={{width: SizeGlobal.Size500, height: SizeGlobal.Size500}}
        />
      </MSBTouchable>
    </View>
  );
};
export default QRPaymentInfoScreen;
