import {RouteProp, useNavigation, useRoute, NavigationProp} from '@react-navigation/native';

import ScreenNames from '../../commons/ScreenNames';
import {PaymentStackParamList} from '../../navigation/PaymentStack';
import {useCallback, useEffect, useRef, useState} from 'react';
import {DIContainer} from '../../di/DIContainer';
import {showCommonPopup} from '../../utils/PopupUtils';
import {SourceAccountModel} from '../../domain/entities/source-account-list/SourceAccountListModel';
import {hostSharedModule} from 'msb-host-shared-module';
import {BillValidateRequest} from '../../data/models/bill-validate/BillValidateRequest';
import {translate} from '../../locales/i18n';
import {GetBillDetailRequest} from '../../data/models/get-bill-detail/GetBillDetailRequest';
import {GetBillDetailModel} from '../../domain/entities/get-bill-detail/GetBillDetailModel';
import Utils from '../../utils/Utils';
import {ACCOUNT_TYPE, ERROR_KEY, PAYMENT_TYPE} from '../../commons/Constants';
import {Configs} from '../../commons/Configs';
import FormatUtils from '../../utils/FormatUtils';
import QrPaymentInfoUtils from './utils/Utils.ts';
import {TextInput} from 'react-native';

const useQRPaymentInfo = (
  renderSourceAccountList: (accountList: SourceAccountModel[], onSelectAccount: () => void) => React.ReactNode,
  renderBiometricAuthentication: () => React.ReactNode,
  renderIdentification: () => React.ReactNode,
) => {
  const route = useRoute<RouteProp<PaymentStackParamList, 'QRPaymentInfoScreen'>>();
  const navigation = useNavigation<NavigationProp<PaymentStackParamList, 'QRPaymentInfoScreen'>>();
  const {paymentInfo} = route.params;
  // console.log('paymentInfo', paymentInfo);

  // const paymentInfo = _paymentInfo;

  // state
  const [sourceAcc, setSourceAcc] = useState<SourceAccountModel[]>(); // danh sách tài khoản nguồn
  const [sourceAccDefault, setSourceAccDefault] = useState<SourceAccountModel>(); // tài khoản nguồn: default
  const [isLoadingSourceAccount, setLoadingSourceAccount] = useState<boolean>(false);
  const [isLoading, setLoading] = useState<boolean>(false);
  const [amountErrorMessage, setAmountErrorMessage] = useState<string>(''); //
  const [amount, setAmount] = useState<string>(''); // số tiền
  const [remark, setRemark] = useState<string>(''); // nội dung chuyển khoản
  const [disableTransferBtn, setDisableTransferBtn] = useState<boolean>(true); // check trạng thái enabel/disable button tiếp tục
  const [amountSuggestList, setAmountSuggestList] = useState(['100,000', '200,000', '500,000', 'Xong']);
  const [isAmountFocused, setAmountFocused] = useState(false);

  const inputAmountRef = useRef<TextInput>(null);

  const onContinue = () => {
    getPaymentBill();
  };

  const goPaymentConfirm = useCallback(
    (billValidateInfo?: BillValidateRequest, id?: string) => {
      navigation.navigate(ScreenNames.PaymentConfirmScreen, {
        paymentInfo: {
          title: translate('qrPaymentInfo.payment'),
          paymentValidate: {...billValidateInfo, id: id ?? ''},
          originatorAccount: {
            identification: sourceAccDefault?.id ?? '',
            name: sourceAccDefault?.name ?? '',
            accountNo: sourceAccDefault?.BBAN ?? '',
            bankName: 'MSB',
            bankCode: Configs.MSB_BANKID,
          },
          qrPaymentInfo: {...paymentInfo, remark},
        },
      });
    },
    [navigation, paymentInfo, remark, sourceAccDefault?.BBAN, sourceAccDefault?.id, sourceAccDefault?.name],
  );

  const billValidate = useCallback(
    async (qrPaymentBill: GetBillDetailModel | undefined) => {
      const qrContent = {
        payType: paymentInfo?.payType,
        note: '',
        item: [
          {
            qrInfor: paymentInfo?.qrContent,
            quantity: '1',
            note: remark,
          },
        ],
        payCode: '',
      };
      // setLoading(true);
      const requestedExecutionDate: string = new Date().toISOString();
      const summary = {
        totalAmount: FormatUtils.formattedNumber(amount).toString(),
        debitAmount: FormatUtils.formattedNumber(amount).toString(),
        billQuantity: qrPaymentBill?.billList?.length,
        cashbackAmount: 0,
        discountAmount: 0,
      };
      const params: BillValidateRequest = {
        originatorAccount: {
          identification: {
            identification: sourceAccDefault?.id ?? '',
            schemeName: 'ID',
          },
        },
        requestedExecutionDate,
        paymentType: PAYMENT_TYPE.QR_PAYMENT,
        transferTransactionInformation: {
          instructedAmount: {
            amount: FormatUtils.formattedNumber(amount).toString(),
            currencyCode: 'VND',
          },
          counterparty: {
            name: qrPaymentBill?.customerInfo?.name ?? '',
          },
          counterpartyAccount: {
            identification: {
              identification: qrPaymentBill?.billCode ?? '',
              schemeName: 'IBAN',
            },
          },
          additions: {
            bpQueryRef: qrPaymentBill?.queryRef ?? '',
            bpBillList: JSON.stringify(
              qrPaymentBill?.billList?.map(e => ({id: e.id, amount: e.amount, period: e.period})),
            ),
            bpSummary: JSON.stringify(summary),
            bpServiceCode: qrPaymentBill?.service?.code ?? '',
            cifNo: qrPaymentBill?.customerInfo?.cif ?? '',
            bpCategory: qrPaymentBill?.getCategoryCode?.() ?? '',
            // bpAccountingNumber: sourceAccDefault?.BBAN ?? '',
            bpQrContent: JSON.stringify(qrContent),
            bpTranSeqCount: qrPaymentBill?.tranSeqCount,
          },
        },
      };
      console.log('request params', params);
      const result = await DIContainer.getInstance().getBillValidateUseCase().execute(params);
      setLoading(false);
      console.log('result', result);

      if (result.status === 'SUCCESS') {
        goPaymentConfirm(params, result.data?.id ?? '');
      } else if (result.status === 'ERROR') {
        const errorKey = result?.error?.code;
        switch (errorKey) {
          case ERROR_KEY.BPE0004: // Gói truy vấn
            QrPaymentInfoUtils.checkIBMB();
            break;
          case ERROR_KEY.BPE0005: // Sinh trắc học
            QrPaymentInfoUtils.checkBiometricAuthentication(renderBiometricAuthentication(), () =>
              Utils.undevelopedFeature(),
            );
            break;
          case ERROR_KEY.BPE0006: // Giấy tờ tuỳ thân
            QrPaymentInfoUtils.checkIdentification(renderIdentification(), () => Utils.undevelopedFeature());
            break;
          default:
            QrPaymentInfoUtils.checkErrorSystem('', () => navigation.goBack());
            break;
        }
      }
    },
    [
      amount,
      goPaymentConfirm,
      navigation,
      paymentInfo,
      remark,
      renderBiometricAuthentication,
      renderIdentification,
      sourceAccDefault,
    ],
  );

  const getSourceAccountList = useCallback(async () => {
    setLoadingSourceAccount(true);
    const result = await DIContainer.getInstance().getSourceAccountListUseCase().execute();
    setLoadingSourceAccount(false);
    if (result.status === 'ERROR') {
      showCommonPopup(result.error);
      return;
    }
    const sourceAccount: SourceAccountModel[] = (result?.data?.data ?? []).filter(
      item => item?.userPreferences?.visible !== false,
    );
    const sourceAccountDefault = sourceAccount?.find(arrangement => arrangement?.isDefault === 'Y');
    setSourceAcc(sourceAccount);
    setSourceAccDefault(sourceAccountDefault);
  }, []);

  // show bottom sheet chọn tài khoản nguồn
  const openSelectAccount = () => {
    hostSharedModule.d.domainService?.showBottomSheet({
      header: translate('paymentInfor.sourceAccount'),
      children: renderSourceAccountList(sourceAcc!, onSelectAccount),
    });
  };

  // chọn tài khoản nguồn
  const onSelectAccount = (sourceAccountDefault?: SourceAccountModel) => {
    hostSharedModule.d.domainService?.hideBottomSheet();
    setSourceAccDefault(sourceAccountDefault);
  };

  const goHome = () => {
    hostSharedModule.d.domainService?.showPopup({
      iconType: 'WARNING',
      title: translate('paymentConfirm.endOfTransaction'),
      content: translate('paymentConfirm.endOfTransactionDescription'),
      cancelBtnText: translate('paymentConfirm.endOfTransaction'),
      confirmBtnText: translate('paymentConfirm.close'),
      onCancel: () =>
        navigation?.reset({
          index: 0,
          routes: [
            {
              name: 'SegmentStack',
            },
          ],
        }),
    });
  };

  const getPaymentBill = useCallback(async () => {
    Utils.showLoading();

    const qrContent = {
      payType: paymentInfo?.payType,
      note: '',
      item: [
        {
          qrInfor: paymentInfo?.qrContent,
          quantity: '1',
          note: remark,
        },
      ],
      payCode: '',
    };
    const request: GetBillDetailRequest = {
      serviceCode: paymentInfo?.serviceCode ?? '',
      accountingType: ACCOUNT_TYPE.ACCT,
      qrContent: JSON.stringify(qrContent),
      amount: FormatUtils.formattedNumber(amount),
    };
    try {
      const result = await DIContainer.getInstance().getGetBillDetailUseCase().execute(request);
      console.log('result =======', result);
      if (result?.status === 'SUCCESS') {
        billValidate(result.data);
      }
    } catch (error: any) {
    } finally {
      Utils.hideLoading();
    }
  }, [amount, billValidate, paymentInfo?.payType, paymentInfo?.qrContent, paymentInfo?.serviceCode, remark]);

  const goQrScreen = () => {
    navigation.goBack();
  };

  const amountOnChangeText = (text: string) => {
    setAmount(text);
    const amountSuggest = FormatUtils.generateAmountSuggestions(FormatUtils.formattedNumber(text).toString());
    setAmountSuggestList(amountSuggest);
  };

  const fillAmountSuggest = (itemAmount: string) => {
    setAmount(itemAmount);
  };

  const handleinputAmountRefFocus = useCallback(() => {
    if (inputAmountRef.current) {
      inputAmountRef.current.focus();
      setAmountFocused(Utils.isEmpty(paymentInfo));
    }
  }, [paymentInfo]);

  useEffect(() => {
    if (amountErrorMessage === '' && FormatUtils.formattedNumber(amount) > 0) {
      setDisableTransferBtn(false);
    } else {
      setDisableTransferBtn(true);
    }
  }, [amountErrorMessage, amount]);

  // useEffect(() => {
  //
  // }, [getPaymentBill]);

  useEffect(() => {
    getSourceAccountList();
  }, [getSourceAccountList]);

  // auto fucus vào số tiền
  useEffect(() => {
    setTimeout(() => {
      handleinputAmountRefFocus();
    }, 700);
  }, [handleinputAmountRefFocus]);

  const validateAmount = useCallback(() => {
    const availableBalance = sourceAccDefault?.availableBalance;
    const _amount = FormatUtils.formattedNumber(amount);
    if (!Utils.isEmpty(availableBalance) && _amount > Number(availableBalance)) {
      setAmountErrorMessage(translate('qrPaymentInfo.qrPaymentInfoValidate.availableBalanceError'));
    } else {
      setAmountErrorMessage('');
    }
  }, [amount, sourceAccDefault]);

  // validate amount
  useEffect(() => {
    validateAmount();
  }, [validateAmount]);

  useEffect(() => {
    setRemark(Utils.transferContent(sourceAccDefault?.name ?? ''));
    if (paymentInfo) {
      if (!Utils.isEmpty(paymentInfo?.amount) && (paymentInfo?.amount ?? 0) > 0) {
        setAmount(FormatUtils.formatPrice(FormatUtils.formattedNumber(paymentInfo?.amount?.toString() || '')));
      }
    }
  }, [paymentInfo, sourceAccDefault?.name]);

  return {
    onContinue,
    paymentInfo,
    openSelectAccount,
    onSelectAccount,
    goHome,
    // state
    sourceAcc,
    sourceAccDefault,
    isLoadingSourceAccount,
    isLoading,
    amountErrorMessage,
    amount,
    disableTransferBtn,
    remark, // nội dung chuyển khoanr
    setRemark,
    goQrScreen,
    amountOnChangeText,
    fillAmountSuggest,
    amountSuggestList,
    inputAmountRef,
    isAmountFocused,
    setAmountFocused,
  };
};

export type Props = ReturnType<typeof useQRPaymentInfo>;

export default useQRPaymentInfo;
