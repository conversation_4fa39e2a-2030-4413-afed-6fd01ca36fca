import React from 'react';
import {View} from 'react-native';
import {
  createMSBStyleSheet,
  getSize,
  MSBFastImage,
  MSBFolderImage,
  MSBTextBase,
  Tpg,
  // MSBTouchable,
  useMSBStyles,
} from 'msb-shared-component';

import {BiometricAuthenticationProps} from './types';
import {BottomSheetScrollView} from '@gorhom/bottom-sheet';
import {translate} from '../../../../locales/i18n';

const BiometricAuthentication = ({style, content}: BiometricAuthenticationProps) => {
  const {styles} = useMSBStyles(makeStyle);
  return (
    <View style={[styles.container, style]}>
      <BottomSheetScrollView contentContainerStyle={styles.contentContainer} showsVerticalScrollIndicator={false}>
        <View style={styles.content}>
          <MSBFastImage
            style={styles.icPhoneWarning}
            resizeMode="cover"
            nameImage={'identification-deny'}
            folder={MSBFolderImage.IMAGES}
          />
          <MSBTextBase style={styles.txtIDInvalid}>{content}</MSBTextBase>
          <View style={styles.benefitContainer}>
            <View style={styles.imgContainer}>
              <MSBFastImage style={styles.icPhoneSuccess} nameImage={'id-card'} />
            </View>
            <View style={styles.boxContainer}>
              <MSBTextBase style={styles.txtIDBenefit}>{translate('qrPaymentInfo.updatingIdentityOnline')}</MSBTextBase>
            </View>
          </View>
        </View>
      </BottomSheetScrollView>
    </View>
  );
};

export default BiometricAuthentication;
const makeStyle = createMSBStyleSheet(({ColorAlias, ColorDataView, ColorGlobal, SizeGlobal}) => {
  return {
    benefitContainer: {
      alignItems: 'center',
      backgroundColor: '#F7F8F9', // FIX ME: chưa có token
      borderRadius: SizeGlobal.Size400,
      flexDirection: 'row',
      marginTop: SizeGlobal.Size400,
      padding: SizeGlobal.Size400,
      width: '100%',
    },
    boxContainer: {
      flex: 1,
      marginLeft: SizeGlobal.Size400,
    },
    container: {
      flex: 1,
      paddingHorizontal: SizeGlobal.Size400,
    },
    content: {
      alignItems: 'center',
      flex: 1,
    },
    contentContainer: {
      paddingBottom: SizeGlobal.Size400,
    },
    icPhoneSuccess: {
      height: getSize(58),
      width: getSize(88),
      borderRadius: SizeGlobal.Size200,
    },
    icPhoneWarning: {
      height: getSize(144),
      marginVertical: SizeGlobal.Size1200,
      width: getSize(144),
    },
    imgContainer: {
      alignItems: 'center',
      borderRadius: SizeGlobal.Size300,
      height: getSize(100),
      justifyContent: 'center',
      backgroundColor: ColorGlobal.NeutralWhite,
      width: getSize(100),
    },
    txtIDBenefit: {
      ...Tpg.small_regular,
      color: ColorAlias.TextPrimary,
    },
    txtIDInvalid: {
      ...Tpg.base_medium,
      color: ColorDataView.TextMain,
      textAlign: 'center',
    },
    txtSeeDetail: {
      ...Tpg.small_medium,
      color: ColorGlobal.Blue500,
      textDecorationLine: 'underline',
    },
    txtUpdateIDOnline: {
      ...Tpg.small_semiBold,
      color: ColorAlias.TextPrimary,
    },
  };
});
