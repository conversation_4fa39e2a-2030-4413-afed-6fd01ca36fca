import React from 'react';
import {View, FlatList, Keyboard} from 'react-native';
import {createMSBStyleSheet, MSBTextBase, MSBTouchable, Tpg, useMSBStyles} from 'msb-shared-component';

import {AmountSuggestProps} from './types';
import {KeyboardToolbar} from 'react-native-keyboard-controller';
import DimensionUtils from '../../../../utils/DimensionUtils';

const AmountSuggest = ({amountSuggest, style, onPress}: AmountSuggestProps) => {
  const {styles} = useMSBStyles(makeStyle);
  const content = (
    <View style={[styles.keyboardAccessory, style]}>
      <FlatList
        keyboardShouldPersistTaps="always"
        data={amountSuggest}
        horizontal
        keyExtractor={(_, index) => index.toString()}
        renderItem={({item, index}) => (
          <Item item={item} index={index} length={amountSuggest.length} onPress={onPress} />
        )}
        showsHorizontalScrollIndicator={false}
        scrollEnabled={false}
        style={styles.flatList}
        ItemSeparatorComponent={() => <View style={styles.separator} />}
      />
    </View>
  );

  return <KeyboardToolbar content={content} showArrows={false} doneText={null} />;
};

const Item: React.FC<{item: string; index: number; length: number; onPress: (item: string) => void}> = ({
  item,
  index,
  length,
  onPress,
}) => {
  const {styles} = useMSBStyles(makeStyle);
  return (
    <MSBTouchable
      onPress={() => {
        Keyboard.dismiss();
        if (index !== length - 1) {
          onPress(item);
        }
      }}
      style={[styles.itemContainer, {width: DimensionUtils.getWindowWidth() / length}]}>
      <MSBTextBase style={index === length - 1 ? styles.txtDone : styles.txtTitleItem} numberOfLines={2}>
        {item}
      </MSBTextBase>
    </MSBTouchable>
  );
};

export default AmountSuggest;
const makeStyle = createMSBStyleSheet(({ColorAlias, ColorButton, ColorCard, ColorField}) => {
  return {
    flatList: {
      width: '100%',
    },
    itemContainer: {
      alignItems: 'center',
      flex: 1,
      justifyContent: 'center',
    },
    keyboardAccessory: {
      backgroundColor: ColorAlias.SurfaceDisable,
      height: 40,
      width: '100%',
    },
    separator: {
      backgroundColor: ColorCard.BorderDivider,
      height: 25,
      marginTop: 9,
      width: 1,
    },
    txtDone: {
      ...Tpg.base_medium,
      color: ColorButton.TextTertiaryDefault,
      textAlign: 'center',
    },
    txtTitleItem: {
      ...Tpg.small_medium,
      color: ColorField.TextFilled,
      textAlign: 'center',
    },
  };
});
