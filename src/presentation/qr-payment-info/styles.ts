import {Color<PERSON>lias, ColorLabelCaption, createMSBStyleSheet} from 'msb-shared-component';

export const makeStyle = createMSBStyleSheet(({<PERSON>ze<PERSON><PERSON><PERSON>, Typography, Shadow}) => {
  return {
    accountInfo: {
      backgroundColor: ColorAlias.BackgroundWhite,
      borderRadius: SizeAlias.SpacingXSmall,
      paddingHorizontal: SizeAlias.SpacingSmall,
      paddingVertical: SizeAlias.SpacingXSmall,
      ...Shadow.center,
    },
    amountInput: {
      ...Typography?.base_medium,
      marginTop: SizeAlias.SpacingSmall,
    },
    btnContinue: mrBottom => ({
      marginBottom: mrBottom,
      marginHorizontal: 16,
    }),
    container: {
      flex: 1,
      padding: 16,
    },

    contentContainerScrollView: {
      padding: SizeAlias.SpacingSmall,
    },

    contentContainer: {
      backgroundColor: ColorAlias.BackgroundWhite,
      borderRadius: Size<PERSON>lias.Radius3,
      marginTop: SizeAlias.SpacingXLarge,
      padding: SizeAlias.SpacingSmall,
      ...Shadow.center,
    },
    contentLabel: {
      color: ColorLabelCaption.TextMain,
      marginRight: SizeAlias.Spacing4xSmall,
      paddingBottom: SizeAlias.Spacing4xSmall,
    },
    flexDirectionRow: {
      flexDirection: 'row',
    },
    imageBackground: {
      flex: 1,
      resizeMode: 'stretch',
    },
    scrollViewContentContainer: {
      paddingBottom: 50,
    },
  };
});
