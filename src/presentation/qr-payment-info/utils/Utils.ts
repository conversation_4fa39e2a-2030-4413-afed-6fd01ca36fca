import {hostSharedModule, PopupType} from 'msb-host-shared-module';
import {ReactNode} from 'react';
import Utils from '../../../utils/Utils';
import {translate} from '../../../locales/i18n';

// chec gói truy vấn
const checkIBMB = (onConfirm?: () => void) => {
  Utils.showPopup({
    iconType: PopupType.WARNING,
    title: translate('qrPaymentInfo.qrPaymentInfoValidate.checkIBMB'),
    content: translate('qrPaymentInfo.qrPaymentInfoValidate.checkIBMBDescription'),
    confirmBtnText: translate('common.close'),
    onConfirm,
  });
};

// chưa thu nhập sinh trắc học
const checkBiometricAuthentication = (
  BiometricAuthentication: ReactNode,
  onConfirm?: () => void,
  onCancel?: () => void,
) => {
  hostSharedModule.d.domainService?.showBottomSheet({
    header: translate('qrPaymentInfo.qrPaymentInfoValidate.transferNotYetExecuted'),
    children: BiometricAuthentication,
    cancelBtnText: translate('qrPaymentInfo.qrPaymentInfoValidate.doItLater'),
    confirmBtnText: translate('qrPaymentInfo.qrPaymentInfoValidate.updateNow'),
    onCancel,
    onConfirm,
    enableDynamicSizing: false,
  });
};
// giấy tờ tuỳ thân hết hạn
const checkIdentification = (Identification: ReactNode, onConfirm?: () => void, onCancel?: () => void) => {
  hostSharedModule.d.domainService?.showBottomSheet({
    header: translate('qrPaymentInfo.qrPaymentInfoValidate.transferNotYetExecuted'),
    children: Identification,
    cancelBtnText: translate('qrPaymentInfo.qrPaymentInfoValidate.doItLater'),
    confirmBtnText: translate('qrPaymentInfo.qrPaymentInfoValidate.updateNow'),
    onCancel,
    onConfirm,
    enableDynamicSizing: false,
  });
};

// số tiền giao dịch vượt quá hạn mức còn lại trong ngayf
const checkDailyAvailableLimit = (dailyAvailableLimit: string, onConfirm: () => void) => {
  Utils.showPopup({
    iconType: PopupType.WARNING,
    title: translate('qrPaymentInfo.qrPaymentInfoValidate.availableBalanceDaily'),
    content: translate('qrPaymentInfo.qrPaymentInfoValidate.availableBalanceDaily', {
      amount: dailyAvailableLimit,
    }),
    cancelBtnText: translate('common.close'),
    confirmBtnText: translate('qrPaymentInfo.qrPaymentInfoValidate.limitSetup'),
    onConfirm,
  });
};

// số dư tài khoản không đủ
const checkAvailableBalance = (onConfirm: () => void) => {
  Utils.showPopup({
    iconType: PopupType.WARNING,
    title: translate('qrPaymentInfo.qrPaymentInfoValidate.availableBalance'),
    content: translate('qrPaymentInfo.qrPaymentInfoValidate.availableBalanceDescription'),
    confirmBtnText: translate('common.close'),
    onConfirm,
  });
};

// Có gián đoạn tạm thời
const checkErrorSystem = (errorMessage?: string, onConfirm?: () => void) => {
  Utils.showPopup({
    iconType: PopupType.WARNING,
    title: translate('common.oops'),
    content: errorMessage || translate('common.errorOccurred'),
    confirmBtnText: translate('common.close'),
    onConfirm,
  });
};

export default {
  checkIBMB,
  checkBiometricAuthentication,
  checkIdentification,
  checkAvailableBalance,
  checkDailyAvailableLimit,
  checkErrorSystem,
};
