import {describe, it, expect, jest, beforeEach} from '@jest/globals';
import {renderHook, act, waitFor} from '@testing-library/react-native';
import usePaymentHome from '../hook';
import {DIContainer} from '../../../di/DIContainer';
import {CategoryModel} from '../../../domain/entities/category-list/CategoryListModel';
import {ResultState} from '../../../core/ResultState';
import {CustomError, ErrorCategory} from '../../../core/MSBCustomError';

// Mock dependencies
jest.mock('../../../di/DIContainer');
jest.mock('../../../utils/PopupUtils', () => ({
  showErrorPopup: jest.fn(),
}));
jest.mock('msb-host-shared-module', () => ({
  hostSharedModule: {
    d: {
      domainService: {
        undevelopedFeature: jest.fn(),
      },
    },
  },
}));
jest.mock('@react-navigation/native', () => ({
  useNavigation: () => ({
    navigate: jest.fn(),
  }),
}));

import {showErrorPopup} from '../../../utils/PopupUtils';
import {hostSharedModule} from 'msb-host-shared-module';

const mockDIContainer = DIContainer as jest.Mocked<typeof DIContainer>;
const mockShowErrorPopup = showErrorPopup as jest.MockedFunction<typeof showErrorPopup>;
const mockUndevelopedFeature = hostSharedModule.d.domainService?.undevelopedFeature as jest.MockedFunction<any>;

describe('usePaymentHome', () => {
  let mockGetInstance: jest.MockedFunction<any>;
  let mockGetCategoryListUseCase: jest.MockedFunction<any>;
  let mockGetCustomerProfileUseCase: jest.MockedFunction<any>;
  let mockGetValidateCustomerUseCase: jest.MockedFunction<any>;
  let mockNavigation: jest.MockedFunction<any>;

  beforeEach(() => {
    jest.clearAllMocks();

    // Mock use cases
    mockGetCategoryListUseCase = {
      execute: jest.fn(),
    };
    mockGetCustomerProfileUseCase = {
      execute: jest.fn(),
    };
    mockGetValidateCustomerUseCase = {
      execute: jest.fn(),
    };

    // Mock DI container
    mockGetInstance = jest.fn().mockReturnValue({
      getCategoryListUseCase: () => mockGetCategoryListUseCase,
      getCustomerProfileUseCase: () => mockGetCustomerProfileUseCase,
      getValidateCustomerUseCase: () => mockGetValidateCustomerUseCase,
    });
    mockDIContainer.getInstance = mockGetInstance;

    // Mock navigation
    mockNavigation = {
      navigate: jest.fn(),
    };
    
    // Mock useNavigation hook
    const mockUseNavigation = require('@react-navigation/native').useNavigation;
    mockUseNavigation.mockReturnValue(mockNavigation);
  });

  describe('initialization', () => {
    it('should initialize with default state', () => {
      const {result} = renderHook(() => usePaymentHome());

      expect(result.current.categories).toBeUndefined();
      expect(result.current.isLoadingCate).toBe(false);
      expect(result.current.isBlocked).toBe(false);
    });

    it('should call checkCustomerDetail and getCategories on mount', async () => {
      const mockCustomerResult: ResultState<any> = {
        status: 'SUCCESS',
        data: {serviceGroup: 'NORMAL'},
      };

      const mockCategoriesResult: ResultState<CategoryModel[]> = {
        status: 'SUCCESS',
        data: [
          new CategoryModel('1', '1', 'UTILITY', 'Utilities', 'Utility bills', true, []),
          new CategoryModel('2', '2', 'TELECOM', 'Telecom', 'Telecom services', true, []),
        ],
      };

      mockGetCustomerProfileUseCase.execute.mockResolvedValue(mockCustomerResult);
      mockGetCategoryListUseCase.execute.mockResolvedValue(mockCategoriesResult);

      renderHook(() => usePaymentHome());

      await waitFor(() => {
        expect(mockGetCustomerProfileUseCase.execute).toHaveBeenCalledTimes(1);
        expect(mockGetCategoryListUseCase.execute).toHaveBeenCalledTimes(1);
      });
    });
  });

  describe('getCategories', () => {
    it('should load categories successfully', async () => {
      const mockCategories = [
        new CategoryModel('1', '1', 'UTILITY', 'Utilities', 'Utility bills', true, []),
        new CategoryModel('2', '2', 'TELECOM', 'Telecom', 'Telecom services', true, []),
      ];

      const mockResult: ResultState<CategoryModel[]> = {
        status: 'SUCCESS',
        data: mockCategories,
      };

      mockGetCategoryListUseCase.execute.mockResolvedValue(mockResult);
      mockGetCustomerProfileUseCase.execute.mockResolvedValue({
        status: 'SUCCESS',
        data: {serviceGroup: 'NORMAL'},
      });

      const {result} = renderHook(() => usePaymentHome());

      await waitFor(() => {
        expect(result.current.categories).toEqual(mockCategories);
        expect(result.current.isLoadingCate).toBe(false);
      });
    });

    it('should handle category loading error', async () => {
      const mockError = new CustomError(
        'CATEGORY_ERROR',
        ErrorCategory.API,
        'Category Error',
        'Failed to load categories',
        true
      );

      const mockResult: ResultState<CategoryModel[]> = {
        status: 'ERROR',
        error: mockError,
      };

      mockGetCategoryListUseCase.execute.mockResolvedValue(mockResult);
      mockGetCustomerProfileUseCase.execute.mockResolvedValue({
        status: 'SUCCESS',
        data: {serviceGroup: 'NORMAL'},
      });

      const {result} = renderHook(() => usePaymentHome());

      await waitFor(() => {
        expect(mockShowErrorPopup).toHaveBeenCalledWith(mockError);
        expect(result.current.categories).toBeUndefined();
        expect(result.current.isLoadingCate).toBe(false);
      });
    });

    it('should set loading state during category fetch', async () => {
      let resolvePromise: (value: any) => void;
      const promise = new Promise(resolve => {
        resolvePromise = resolve;
      });

      mockGetCategoryListUseCase.execute.mockReturnValue(promise);
      mockGetCustomerProfileUseCase.execute.mockResolvedValue({
        status: 'SUCCESS',
        data: {serviceGroup: 'NORMAL'},
      });

      const {result} = renderHook(() => usePaymentHome());

      // Initially loading should be true
      await waitFor(() => {
        expect(result.current.isLoadingCate).toBe(true);
      });

      // Resolve the promise
      act(() => {
        resolvePromise!({
          status: 'SUCCESS',
          data: [],
        });
      });

      await waitFor(() => {
        expect(result.current.isLoadingCate).toBe(false);
      });
    });
  });

  describe('checkCustomerDetail', () => {
    it('should set user as blocked when serviceGroup is M_INQUIRY', async () => {
      const mockResult: ResultState<any> = {
        status: 'SUCCESS',
        data: {serviceGroup: 'M_INQUIRY'},
      };

      mockGetCustomerProfileUseCase.execute.mockResolvedValue(mockResult);
      mockGetCategoryListUseCase.execute.mockResolvedValue({
        status: 'SUCCESS',
        data: [],
      });

      const {result} = renderHook(() => usePaymentHome());

      await waitFor(() => {
        expect(result.current.isBlocked).toBe(true);
      });
    });

    it('should set user as not blocked when serviceGroup is not M_INQUIRY', async () => {
      const mockResult: ResultState<any> = {
        status: 'SUCCESS',
        data: {serviceGroup: 'NORMAL'},
      };

      mockGetCustomerProfileUseCase.execute.mockResolvedValue(mockResult);
      mockGetCategoryListUseCase.execute.mockResolvedValue({
        status: 'SUCCESS',
        data: [],
      });

      const {result} = renderHook(() => usePaymentHome());

      await waitFor(() => {
        expect(result.current.isBlocked).toBe(false);
      });
    });

    it('should handle customer detail loading error', async () => {
      const mockError = new CustomError(
        'CUSTOMER_ERROR',
        ErrorCategory.API,
        'Customer Error',
        'Failed to load customer details',
        true
      );

      const mockResult: ResultState<any> = {
        status: 'ERROR',
        error: mockError,
      };

      mockGetCustomerProfileUseCase.execute.mockResolvedValue(mockResult);
      mockGetCategoryListUseCase.execute.mockResolvedValue({
        status: 'SUCCESS',
        data: [],
      });

      const {result} = renderHook(() => usePaymentHome());

      await waitFor(() => {
        expect(mockShowErrorPopup).toHaveBeenCalledWith(mockError);
        expect(result.current.isBlocked).toBe(false); // Default state
      });
    });
  });

  describe('gotoPaymentBill', () => {
    const mockCategory = new CategoryModel('1', '1', 'UTILITY', 'Utilities', 'Utility bills', true, []);

    beforeEach(() => {
      mockGetCustomerProfileUseCase.execute.mockResolvedValue({
        status: 'SUCCESS',
        data: {serviceGroup: 'NORMAL'},
      });
      mockGetCategoryListUseCase.execute.mockResolvedValue({
        status: 'SUCCESS',
        data: [],
      });
    });

    it('should navigate to SaveBillContactScreen when isAddContactFlow is true', async () => {
      const {result} = renderHook(() => usePaymentHome());

      await act(async () => {
        await result.current.gotoPaymentBill(mockCategory, true);
      });

      expect(mockNavigation.navigate).toHaveBeenCalledWith('SaveBillContactScreen', {category: mockCategory});
    });

    it('should navigate to PaymentPhoneScreen for mobile recharge category', async () => {
      const mobileCategory = new CategoryModel('MB-MR', '1', 'MB-MR', 'Mobile Recharge', 'Mobile recharge', true, []);
      
      mockGetValidateCustomerUseCase.execute.mockResolvedValue({
        status: 'SUCCESS',
        data: {},
      });

      const {result} = renderHook(() => usePaymentHome());

      await act(async () => {
        await result.current.gotoPaymentBill(mobileCategory, false);
      });

      expect(mockGetValidateCustomerUseCase.execute).toHaveBeenCalledTimes(1);
      expect(mockNavigation.navigate).toHaveBeenCalledWith('PaymentPhoneScreen', {category: mobileCategory});
    });

    it('should navigate to PaymentBillScreen for other categories', async () => {
      mockGetValidateCustomerUseCase.execute.mockResolvedValue({
        status: 'SUCCESS',
        data: {},
      });

      const {result} = renderHook(() => usePaymentHome());

      await act(async () => {
        await result.current.gotoPaymentBill(mockCategory, false);
      });

      expect(mockGetValidateCustomerUseCase.execute).toHaveBeenCalledTimes(1);
      expect(mockNavigation.navigate).toHaveBeenCalledWith('PaymentBillScreen', {category: mockCategory});
    });

    it('should show error popup when customer validation fails', async () => {
      const mockError = new CustomError(
        'VALIDATION_ERROR',
        ErrorCategory.VALIDATION,
        'Validation Error',
        'Customer validation failed',
        false
      );

      mockGetValidateCustomerUseCase.execute.mockResolvedValue({
        status: 'ERROR',
        error: mockError,
      });

      const {result} = renderHook(() => usePaymentHome());

      await act(async () => {
        await result.current.gotoPaymentBill(mockCategory, false);
      });

      expect(mockShowErrorPopup).toHaveBeenCalledWith(mockError);
      expect(mockNavigation.navigate).not.toHaveBeenCalled();
    });

    it('should handle different category types correctly', async () => {
      const categories = [
        new CategoryModel('UTILITY', '1', 'UTILITY', 'Utilities', 'Utility bills', true, []),
        new CategoryModel('TELECOM', '2', 'TELECOM', 'Telecom', 'Telecom services', true, []),
        new CategoryModel('INSURANCE', '3', 'INSURANCE', 'Insurance', 'Insurance payments', true, []),
      ];

      mockGetValidateCustomerUseCase.execute.mockResolvedValue({
        status: 'SUCCESS',
        data: {},
      });

      const {result} = renderHook(() => usePaymentHome());

      for (const category of categories) {
        await act(async () => {
          await result.current.gotoPaymentBill(category, false);
        });

        expect(mockNavigation.navigate).toHaveBeenCalledWith('PaymentBillScreen', {category});
      }

      expect(mockGetValidateCustomerUseCase.execute).toHaveBeenCalledTimes(3);
      expect(mockNavigation.navigate).toHaveBeenCalledTimes(3);
    });
  });

  describe('undevelopedFeature', () => {
    it('should call hostSharedModule undevelopedFeature', () => {
      const {result} = renderHook(() => usePaymentHome());

      act(() => {
        result.current.undevelopedFeature();
      });

      expect(mockUndevelopedFeature).toHaveBeenCalledTimes(1);
    });
  });

  describe('error handling', () => {
    it('should handle network errors gracefully', async () => {
      const networkError = new CustomError(
        'NETWORK_ERROR',
        ErrorCategory.NETWORK,
        'Network Error',
        'Unable to connect to server',
        true
      );

      mockGetCustomerProfileUseCase.execute.mockResolvedValue({
        status: 'ERROR',
        error: networkError,
      });
      mockGetCategoryListUseCase.execute.mockResolvedValue({
        status: 'ERROR',
        error: networkError,
      });

      const {result} = renderHook(() => usePaymentHome());

      await waitFor(() => {
        expect(mockShowErrorPopup).toHaveBeenCalledWith(networkError);
        expect(result.current.categories).toBeUndefined();
        expect(result.current.isBlocked).toBe(false);
      });
    });

    it('should handle API errors gracefully', async () => {
      const apiError = new CustomError(
        'API_ERROR',
        ErrorCategory.API,
        'API Error',
        'Internal server error',
        false
      );

      mockGetCustomerProfileUseCase.execute.mockResolvedValue({
        status: 'ERROR',
        error: apiError,
      });
      mockGetCategoryListUseCase.execute.mockResolvedValue({
        status: 'ERROR',
        error: apiError,
      });

      const {result} = renderHook(() => usePaymentHome());

      await waitFor(() => {
        expect(mockShowErrorPopup).toHaveBeenCalledWith(apiError);
        expect(result.current.categories).toBeUndefined();
        expect(result.current.isBlocked).toBe(false);
      });
    });
  });

  describe('integration scenarios', () => {
    it('should handle complete initialization flow', async () => {
      const mockCategories = [
        new CategoryModel('1', '1', 'UTILITY', 'Utilities', 'Utility bills', true, []),
        new CategoryModel('2', '2', 'TELECOM', 'Telecom', 'Telecom services', true, []),
      ];

      const mockCustomerResult: ResultState<any> = {
        status: 'SUCCESS',
        data: {serviceGroup: 'NORMAL'},
      };

      const mockCategoriesResult: ResultState<CategoryModel[]> = {
        status: 'SUCCESS',
        data: mockCategories,
      };

      mockGetCustomerProfileUseCase.execute.mockResolvedValue(mockCustomerResult);
      mockGetCategoryListUseCase.execute.mockResolvedValue(mockCategoriesResult);

      const {result} = renderHook(() => usePaymentHome());

      await waitFor(() => {
        expect(result.current.categories).toEqual(mockCategories);
        expect(result.current.isBlocked).toBe(false);
        expect(result.current.isLoadingCate).toBe(false);
      });

      expect(mockGetCustomerProfileUseCase.execute).toHaveBeenCalledTimes(1);
      expect(mockGetCategoryListUseCase.execute).toHaveBeenCalledTimes(1);
    });

    it('should maintain hook interface contract', async () => {
      mockGetCustomerProfileUseCase.execute.mockResolvedValue({
        status: 'SUCCESS',
        data: {serviceGroup: 'NORMAL'},
      });
      mockGetCategoryListUseCase.execute.mockResolvedValue({
        status: 'SUCCESS',
        data: [],
      });

      const {result} = renderHook(() => usePaymentHome());

      await waitFor(() => {
        // Verify the hook returns the expected interface
        expect(result.current).toHaveProperty('categories');
        expect(result.current).toHaveProperty('isLoadingCate');
        expect(result.current).toHaveProperty('isBlocked');
        expect(result.current).toHaveProperty('gotoPaymentBill');
        expect(result.current).toHaveProperty('undevelopedFeature');

        expect(typeof result.current.gotoPaymentBill).toBe('function');
        expect(typeof result.current.undevelopedFeature).toBe('function');
        expect(typeof result.current.isLoadingCate).toBe('boolean');
        expect(typeof result.current.isBlocked).toBe('boolean');
      });
    });
  });
});
