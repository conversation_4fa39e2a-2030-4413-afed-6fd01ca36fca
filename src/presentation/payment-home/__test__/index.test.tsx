import React from 'react';
import {describe, it, expect, jest, beforeEach} from '@jest/globals';
import {render, fireEvent, waitFor} from '@testing-library/react-native';
import PaymentHomeScreen from '../index';
import usePaymentHome from '../hook';
import {CategoryModel} from '../../../domain/entities/category-list/CategoryListModel';

// Mock dependencies
jest.mock('../hook');
jest.mock('../../../locales/i18n', () => ({
  translate: jest.fn((key: string) => key),
}));
jest.mock('msb-shared-component', () => ({
  MSBPage: ({children, headerProps, style}: any) => (
    <div testID="msb-page" data-header-title={headerProps?.title} style={style}>
      {children}
    </div>
  ),
  useMSBStyles: jest.fn(() => ({
    styles: {
      container: {flex: 1},
      contactCard: {marginTop: 20},
    },
  })),
}));
jest.mock('react-native', () => ({
  View: ({children, style, testID}: any) => (
    <div testID={testID} style={style}>
      {children}
    </div>
  ),
}));
jest.mock('@react-navigation/native', () => ({
  useNavigation: () => ({
    navigate: jest.fn(),
  }),
}));
jest.mock('@gorhom/bottom-sheet', () => ({
  BottomSheetView: ({children}: any) => <div testID="bottom-sheet-view">{children}</div>,
}));
jest.mock('msb-host-shared-module', () => ({
  hostSharedModule: {
    d: {
      domainService: {
        undevelopedFeature: jest.fn(),
      },
    },
  },
}));
jest.mock('../components/BillContainerTab.tsx', () => ({
  __esModule: true,
  default: ({isBlocked, onAddNewContact}: any) => (
    <div testID="bill-container-tab" data-blocked={isBlocked}>
      <button testID="add-new-contact-btn" onPress={onAddNewContact}>
        Add New Contact
      </button>
    </div>
  ),
}));
jest.mock('../components/feature-section/index.tsx', () => ({
  __esModule: true,
  default: ({categories, onSelect, onOpenAllCategory, onOpenAutomaticRewards, loading}: any) => (
    <div testID="feature-section" data-loading={loading}>
      {categories?.map((category: CategoryModel, index: number) => (
        <button
          key={index}
          testID={`category-${category.id}`}
          onPress={() => onSelect(category)}
        >
          {category.categoryName}
        </button>
      ))}
      <button testID="open-all-category-btn" onPress={onOpenAllCategory}>
        Open All Categories
      </button>
      <button testID="automatic-rewards-btn" onPress={onOpenAutomaticRewards}>
        Automatic Rewards
      </button>
    </div>
  ),
}));
jest.mock('../components/category-selection/index.tsx', () => ({
  __esModule: true,
  default: ({categories, onSelect}: any) => (
    <div testID="category-selection">
      {categories?.map((category: CategoryModel, index: number) => (
        <button
          key={index}
          testID={`category-selection-${category.id}`}
          onPress={() => onSelect(category)}
        >
          {category.categoryName}
        </button>
      ))}
    </div>
  ),
}));
jest.mock('../components/hook.tsx', () => ({
  BeneficiaryProvider: ({children}: any) => (
    <div testID="beneficiary-provider">{children}</div>
  ),
}));

const mockUsePaymentHome = usePaymentHome as jest.MockedFunction<typeof usePaymentHome>;

describe('PaymentHomeScreen', () => {
  let mockHook: ReturnType<typeof usePaymentHome>;

  beforeEach(() => {
    jest.clearAllMocks();

    // Default mock hook return
    mockHook = {
      categories: [
        new CategoryModel('1', '1', 'UTILITY', 'Utilities', 'Utility bills', true, []),
        new CategoryModel('2', '2', 'TELECOM', 'Telecom', 'Telecom services', true, []),
        new CategoryModel('3', '3', 'INSURANCE', 'Insurance', 'Insurance payments', true, []),
      ],
      isLoadingCate: false,
      isBlocked: false,
      gotoPaymentBill: jest.fn(),
      undevelopedFeature: jest.fn(),
    };

    mockUsePaymentHome.mockReturnValue(mockHook);
  });

  describe('rendering', () => {
    it('should render PaymentHomeScreen correctly', () => {
      const {getByTestId} = render(<PaymentHomeScreen />);

      expect(getByTestId('msb-page')).toBeTruthy();
      expect(getByTestId('beneficiary-provider')).toBeTruthy();
      expect(getByTestId('feature-section')).toBeTruthy();
      expect(getByTestId('bill-container-tab')).toBeTruthy();
    });

    it('should render with correct header title', () => {
      const {getByTestId} = render(<PaymentHomeScreen />);

      const msbPage = getByTestId('msb-page');
      expect(msbPage.getAttribute('data-header-title')).toBe('paymentHome.title');
    });

    it('should render categories in feature section', () => {
      const {getByTestId} = render(<PaymentHomeScreen />);

      expect(getByTestId('category-1')).toBeTruthy();
      expect(getByTestId('category-2')).toBeTruthy();
      expect(getByTestId('category-3')).toBeTruthy();
    });

    it('should render only first 3 categories in feature section', () => {
      const manyCategories = Array.from({length: 10}, (_, i) => 
        new CategoryModel(`${i}`, `${i}`, `CAT${i}`, `Category ${i}`, `Description ${i}`, true, [])
      );

      mockHook.categories = manyCategories;
      mockUsePaymentHome.mockReturnValue(mockHook);

      const {getByTestId, queryByTestId} = render(<PaymentHomeScreen />);

      // Should render first 3 categories
      expect(getByTestId('category-0')).toBeTruthy();
      expect(getByTestId('category-1')).toBeTruthy();
      expect(getByTestId('category-2')).toBeTruthy();

      // Should not render 4th category and beyond
      expect(queryByTestId('category-3')).toBeFalsy();
      expect(queryByTestId('category-4')).toBeFalsy();
    });

    it('should handle empty categories array', () => {
      mockHook.categories = [];
      mockUsePaymentHome.mockReturnValue(mockHook);

      const {getByTestId} = render(<PaymentHomeScreen />);

      expect(getByTestId('feature-section')).toBeTruthy();
      // No category buttons should be rendered
      expect(getByTestId('feature-section').children).toHaveLength(2); // Only action buttons
    });

    it('should handle undefined categories', () => {
      mockHook.categories = undefined;
      mockUsePaymentHome.mockReturnValue(mockHook);

      const {getByTestId} = render(<PaymentHomeScreen />);

      expect(getByTestId('feature-section')).toBeTruthy();
      // No category buttons should be rendered
      expect(getByTestId('feature-section').children).toHaveLength(2); // Only action buttons
    });
  });

  describe('loading states', () => {
    it('should pass loading state to feature section', () => {
      mockHook.isLoadingCate = true;
      mockUsePaymentHome.mockReturnValue(mockHook);

      const {getByTestId} = render(<PaymentHomeScreen />);

      const featureSection = getByTestId('feature-section');
      expect(featureSection.getAttribute('data-loading')).toBe('true');
    });

    it('should pass not loading state to feature section', () => {
      mockHook.isLoadingCate = false;
      mockUsePaymentHome.mockReturnValue(mockHook);

      const {getByTestId} = render(<PaymentHomeScreen />);

      const featureSection = getByTestId('feature-section');
      expect(featureSection.getAttribute('data-loading')).toBe('false');
    });
  });

  describe('blocked state', () => {
    it('should pass blocked state to bill container tab', () => {
      mockHook.isBlocked = true;
      mockUsePaymentHome.mockReturnValue(mockHook);

      const {getByTestId} = render(<PaymentHomeScreen />);

      const billContainerTab = getByTestId('bill-container-tab');
      expect(billContainerTab.getAttribute('data-blocked')).toBe('true');
    });

    it('should pass not blocked state to bill container tab', () => {
      mockHook.isBlocked = false;
      mockUsePaymentHome.mockReturnValue(mockHook);

      const {getByTestId} = render(<PaymentHomeScreen />);

      const billContainerTab = getByTestId('bill-container-tab');
      expect(billContainerTab.getAttribute('data-blocked')).toBe('false');
    });
  });

  describe('user interactions', () => {
    it('should call gotoPaymentBill when category is selected', () => {
      const {getByTestId} = render(<PaymentHomeScreen />);

      fireEvent.press(getByTestId('category-1'));

      expect(mockHook.gotoPaymentBill).toHaveBeenCalledWith(mockHook.categories![0]);
      expect(mockHook.gotoPaymentBill).toHaveBeenCalledTimes(1);
    });

    it('should call gotoPaymentBill for different categories', () => {
      const {getByTestId} = render(<PaymentHomeScreen />);

      fireEvent.press(getByTestId('category-2'));
      fireEvent.press(getByTestId('category-3'));

      expect(mockHook.gotoPaymentBill).toHaveBeenCalledWith(mockHook.categories![1]);
      expect(mockHook.gotoPaymentBill).toHaveBeenCalledWith(mockHook.categories![2]);
      expect(mockHook.gotoPaymentBill).toHaveBeenCalledTimes(2);
    });

    it('should call undevelopedFeature when automatic rewards is pressed', () => {
      const {getByTestId} = render(<PaymentHomeScreen />);

      fireEvent.press(getByTestId('automatic-rewards-btn'));

      expect(mockHook.undevelopedFeature).toHaveBeenCalledTimes(1);
    });

    it('should handle add new contact button press', () => {
      const {getByTestId} = render(<PaymentHomeScreen />);

      // This should trigger the bottom sheet to show
      fireEvent.press(getByTestId('add-new-contact-btn'));

      // The onAddNewContact callback should be called
      // In the actual implementation, this would show the bottom sheet
      expect(getByTestId('add-new-contact-btn')).toBeTruthy();
    });
  });

  describe('component structure', () => {
    it('should have correct component hierarchy', () => {
      const {getByTestId} = render(<PaymentHomeScreen />);

      const msbPage = getByTestId('msb-page');
      const beneficiaryProvider = getByTestId('beneficiary-provider');
      const featureSection = getByTestId('feature-section');
      const billContainerTab = getByTestId('bill-container-tab');

      expect(msbPage).toContainElement(beneficiaryProvider);
      expect(beneficiaryProvider).toContainElement(featureSection);
      expect(beneficiaryProvider).toContainElement(billContainerTab);
    });

    it('should apply correct styles', () => {
      const {getByTestId} = render(<PaymentHomeScreen />);

      const msbPage = getByTestId('msb-page');
      expect(msbPage.style).toEqual({flex: 1});
    });
  });

  describe('error handling', () => {
    it('should handle hook errors gracefully', () => {
      // Mock hook to throw error
      mockUsePaymentHome.mockImplementation(() => {
        throw new Error('Hook error');
      });

      expect(() => render(<PaymentHomeScreen />)).toThrow('Hook error');
    });

    it('should handle missing hook properties', () => {
      mockUsePaymentHome.mockReturnValue({
        ...mockHook,
        categories: undefined,
        gotoPaymentBill: undefined as any,
      });

      const {getByTestId} = render(<PaymentHomeScreen />);

      expect(getByTestId('feature-section')).toBeTruthy();
      // Should not crash even with undefined properties
    });
  });

  describe('accessibility', () => {
    it('should have accessible elements', () => {
      const {getByTestId} = render(<PaymentHomeScreen />);

      // All major interactive elements should have testIDs
      expect(getByTestId('feature-section')).toBeTruthy();
      expect(getByTestId('bill-container-tab')).toBeTruthy();
      expect(getByTestId('category-1')).toBeTruthy();
      expect(getByTestId('open-all-category-btn')).toBeTruthy();
      expect(getByTestId('automatic-rewards-btn')).toBeTruthy();
    });
  });

  describe('performance considerations', () => {
    it('should handle large category lists efficiently', () => {
      const largeCategories = Array.from({length: 100}, (_, i) => 
        new CategoryModel(`${i}`, `${i}`, `CAT${i}`, `Category ${i}`, `Description ${i}`, true, [])
      );

      mockHook.categories = largeCategories;
      mockUsePaymentHome.mockReturnValue(mockHook);

      const startTime = performance.now();
      const {getByTestId} = render(<PaymentHomeScreen />);
      const endTime = performance.now();

      expect(getByTestId('feature-section')).toBeTruthy();
      expect(endTime - startTime).toBeLessThan(100); // Should render quickly
    });

    it('should handle rapid state changes', async () => {
      const {rerender} = render(<PaymentHomeScreen />);

      // Simulate rapid loading state changes
      for (let i = 0; i < 10; i++) {
        mockHook.isLoadingCate = i % 2 === 0;
        mockUsePaymentHome.mockReturnValue(mockHook);
        rerender(<PaymentHomeScreen />);
      }

      await waitFor(() => {
        expect(mockUsePaymentHome).toHaveBeenCalled();
      });
    });
  });

  describe('integration scenarios', () => {
    it('should handle complete user flow', async () => {
      const {getByTestId} = render(<PaymentHomeScreen />);

      // User selects a category
      fireEvent.press(getByTestId('category-1'));
      expect(mockHook.gotoPaymentBill).toHaveBeenCalledWith(mockHook.categories![0]);

      // User tries automatic rewards
      fireEvent.press(getByTestId('automatic-rewards-btn'));
      expect(mockHook.undevelopedFeature).toHaveBeenCalledTimes(1);

      // User adds new contact
      fireEvent.press(getByTestId('add-new-contact-btn'));
      // Should not crash
    });

    it('should maintain component state consistency', () => {
      const {getByTestId, rerender} = render(<PaymentHomeScreen />);

      // Initial state
      expect(getByTestId('feature-section').getAttribute('data-loading')).toBe('false');
      expect(getByTestId('bill-container-tab').getAttribute('data-blocked')).toBe('false');

      // Update state
      mockHook.isLoadingCate = true;
      mockHook.isBlocked = true;
      mockUsePaymentHome.mockReturnValue(mockHook);
      rerender(<PaymentHomeScreen />);

      expect(getByTestId('feature-section').getAttribute('data-loading')).toBe('true');
      expect(getByTestId('bill-container-tab').getAttribute('data-blocked')).toBe('true');
    });
  });
});
