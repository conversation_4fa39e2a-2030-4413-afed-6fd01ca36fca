import { CategoryListModel, CategoryModel } from '../CategoryListModel';

describe('CategoryModel', () => {
  describe('constructor', () => {
    it('should create CategoryModel instance with all parameters', () => {
      const children: CategoryListModel = [];
      const model = new CategoryModel(
        'CAT_001',
        '1',
        'ELECTRIC',
        'Electric Bills',
        'Electricity utility bills',
        true,
        children
      );

      expect(model).toBeInstanceOf(CategoryModel);
      expect(model.id).toBe('CAT_001');
      expect(model.order).toBe('1');
      expect(model.categoryCode).toBe('ELECTRIC');
      expect(model.categoryName).toBe('Electric Bills');
      expect(model.description).toBe('Electricity utility bills');
      expect(model.active).toBe(true);
      expect(model.children).toBe(children);
    });

    it('should create multiple distinct instances', () => {
      const children1: CategoryListModel = [];
      const children2: CategoryListModel = [];
      const model1 = new CategoryModel('CAT_001', '1', 'ELECTRIC', 'Electric', 'Desc1', true, children1);
      const model2 = new CategoryModel('CAT_002', '2', 'WATER', 'Water', 'Desc2', false, children2);

      expect(model1).not.toBe(model2);
      expect(model1.id).toBe('CAT_001');
      expect(model2.id).toBe('CAT_002');
      expect(model1.categoryCode).toBe('ELECTRIC');
      expect(model2.categoryCode).toBe('WATER');
    });

    it('should handle empty string parameters', () => {
      const children: CategoryListModel = [];
      const model = new CategoryModel('', '', '', '', '', false, children);

      expect(model.id).toBe('');
      expect(model.order).toBe('');
      expect(model.categoryCode).toBe('');
      expect(model.categoryName).toBe('');
      expect(model.description).toBe('');
      expect(model.active).toBe(false);
    });

    it('should handle Vietnamese characters in parameters', () => {
      const children: CategoryListModel = [];
      const model = new CategoryModel(
        'ĐIỆN_001',
        '1',
        'ĐIỆN',
        'Hóa đơn điện',
        'Hóa đơn tiền điện hàng tháng',
        true,
        children
      );

      expect(model.id).toBe('ĐIỆN_001');
      expect(model.categoryCode).toBe('ĐIỆN');
      expect(model.categoryName).toBe('Hóa đơn điện');
      expect(model.description).toBe('Hóa đơn tiền điện hàng tháng');
    });
  });

  describe('parameter handling', () => {
    it('should handle null parameters', () => {
      const model = new CategoryModel(
        null as any,
        null as any,
        null as any,
        null as any,
        null as any,
        null as any,
        null as any
      );

      expect(model.id).toBeNull();
      expect(model.order).toBeNull();
      expect(model.categoryCode).toBeNull();
      expect(model.categoryName).toBeNull();
      expect(model.description).toBeNull();
      expect(model.active).toBeNull();
      expect(model.children).toBeNull();
    });

    it('should handle undefined parameters', () => {
      const model = new CategoryModel(
        undefined as any,
        undefined as any,
        undefined as any,
        undefined as any,
        undefined as any,
        undefined as any,
        undefined as any
      );

      expect(model.id).toBeUndefined();
      expect(model.order).toBeUndefined();
      expect(model.categoryCode).toBeUndefined();
      expect(model.categoryName).toBeUndefined();
      expect(model.description).toBeUndefined();
      expect(model.active).toBeUndefined();
      expect(model.children).toBeUndefined();
    });

    it('should handle special characters in string parameters', () => {
      const children: CategoryListModel = [];
      const model = new CategoryModel(
        'CAT_WITH-SPECIAL.CHARS!@#',
        'ORDER_WITH-SPECIAL.CHARS!@#',
        'CODE_WITH-SPECIAL.CHARS!@#',
        'NAME_WITH-SPECIAL.CHARS!@#',
        'DESC_WITH-SPECIAL.CHARS!@#',
        true,
        children
      );

      expect(model.id).toBe('CAT_WITH-SPECIAL.CHARS!@#');
      expect(model.order).toBe('ORDER_WITH-SPECIAL.CHARS!@#');
      expect(model.categoryCode).toBe('CODE_WITH-SPECIAL.CHARS!@#');
      expect(model.categoryName).toBe('NAME_WITH-SPECIAL.CHARS!@#');
      expect(model.description).toBe('DESC_WITH-SPECIAL.CHARS!@#');
    });

    it('should handle numeric string parameters', () => {
      const children: CategoryListModel = [];
      const model = new CategoryModel('123', '456', '789', '101112', '131415', true, children);

      expect(model.id).toBe('123');
      expect(model.order).toBe('456');
      expect(model.categoryCode).toBe('789');
      expect(model.categoryName).toBe('101112');
      expect(model.description).toBe('131415');
    });
  });

  describe('children handling', () => {
    it('should handle empty children array', () => {
      const children: CategoryListModel = [];
      const model = new CategoryModel('CAT_001', '1', 'ELECTRIC', 'Electric', 'Description', true, children);

      expect(model.children).toEqual([]);
      expect(model.children.length).toBe(0);
    });

    it('should handle children array with CategoryModel instances', () => {
      const subCategory1 = new CategoryModel('SUB_001', '1.1', 'ELECTRIC_HOME', 'Home Electric', 'Home electricity', true, []);
      const subCategory2 = new CategoryModel('SUB_002', '1.2', 'ELECTRIC_BUSINESS', 'Business Electric', 'Business electricity', true, []);
      const children: CategoryListModel = [subCategory1, subCategory2];
      
      const model = new CategoryModel('CAT_001', '1', 'ELECTRIC', 'Electric Bills', 'Electricity bills', true, children);

      expect(model.children).toHaveLength(2);
      expect(model.children[0]).toBe(subCategory1);
      expect(model.children[1]).toBe(subCategory2);
      expect(model.children[0].id).toBe('SUB_001');
      expect(model.children[1].id).toBe('SUB_002');
    });

    it('should handle nested children (recursive structure)', () => {
      const grandChild = new CategoryModel('GRAND_001', '1.1.1', 'ELECTRIC_HOME_BASIC', 'Basic Home Electric', 'Basic home electricity', true, []);
      const subCategory = new CategoryModel('SUB_001', '1.1', 'ELECTRIC_HOME', 'Home Electric', 'Home electricity', true, [grandChild]);
      const children: CategoryListModel = [subCategory];
      
      const model = new CategoryModel('CAT_001', '1', 'ELECTRIC', 'Electric Bills', 'Electricity bills', true, children);

      expect(model.children).toHaveLength(1);
      expect(model.children[0].children).toHaveLength(1);
      expect(model.children[0].children[0].id).toBe('GRAND_001');
      expect(model.children[0].children[0].categoryCode).toBe('ELECTRIC_HOME_BASIC');
    });
  });

  describe('property access', () => {
    it('should allow property modification after creation', () => {
      const children: CategoryListModel = [];
      const model = new CategoryModel('INITIAL', 'INITIAL', 'INITIAL', 'INITIAL', 'INITIAL', false, children);
      
      model.id = 'MODIFIED';
      model.order = 'MODIFIED';
      model.categoryCode = 'MODIFIED';
      model.categoryName = 'MODIFIED';
      model.description = 'MODIFIED';
      model.active = true;
      model.children = [new CategoryModel('NEW_CHILD', '1', 'CHILD', 'Child', 'Child desc', true, [])];

      expect(model.id).toBe('MODIFIED');
      expect(model.order).toBe('MODIFIED');
      expect(model.categoryCode).toBe('MODIFIED');
      expect(model.categoryName).toBe('MODIFIED');
      expect(model.description).toBe('MODIFIED');
      expect(model.active).toBe(true);
      expect(model.children).toHaveLength(1);
    });

    it('should enumerate constructor properties', () => {
      const children: CategoryListModel = [];
      const model = new CategoryModel('CAT_001', '1', 'ELECTRIC', 'Electric', 'Description', true, children);
      const keys = Object.keys(model);

      expect(keys).toContain('id');
      expect(keys).toContain('order');
      expect(keys).toContain('categoryCode');
      expect(keys).toContain('categoryName');
      expect(keys).toContain('description');
      expect(keys).toContain('active');
      expect(keys).toContain('children');
    });

    it('should support property deletion', () => {
      const children: CategoryListModel = [];
      const model = new CategoryModel('CAT_001', '1', 'ELECTRIC', 'Electric', 'Description', true, children);
      
      delete (model as any).id;
      delete (model as any).active;
      
      expect(model.id).toBeUndefined();
      expect(model.active).toBeUndefined();
    });
  });

  describe('type checking', () => {
    it('should be recognized as CategoryModel type', () => {
      const children: CategoryListModel = [];
      const model = new CategoryModel('CAT_001', '1', 'ELECTRIC', 'Electric', 'Description', true, children);
      
      expect(model instanceof CategoryModel).toBe(true);
    });

    it('should not be instance of other types', () => {
      const children: CategoryListModel = [];
      const model = new CategoryModel('CAT_001', '1', 'ELECTRIC', 'Electric', 'Description', true, children);
      
      expect(model instanceof Array).toBe(false);
      expect(model instanceof Object).toBe(true);
      expect(model instanceof String).toBe(false);
    });

    it('should have correct constructor name', () => {
      const children: CategoryListModel = [];
      const model = new CategoryModel('CAT_001', '1', 'ELECTRIC', 'Electric', 'Description', true, children);
      
      expect(model.constructor.name).toBe('CategoryModel');
    });
  });

  describe('serialization', () => {
    it('should be JSON serializable', () => {
      const children: CategoryListModel = [];
      const model = new CategoryModel('CAT_001', '1', 'ELECTRIC', 'Electric Bills', 'Electricity bills', true, children);
      
      const serialized = JSON.parse(JSON.stringify(model));
      expect(serialized).toEqual({
        id: 'CAT_001',
        order: '1',
        categoryCode: 'ELECTRIC',
        categoryName: 'Electric Bills',
        description: 'Electricity bills',
        active: true,
        children: []
      });
    });

    it('should serialize nested children correctly', () => {
      const subCategory = new CategoryModel('SUB_001', '1.1', 'ELECTRIC_HOME', 'Home Electric', 'Home electricity', true, []);
      const children: CategoryListModel = [subCategory];
      const model = new CategoryModel('CAT_001', '1', 'ELECTRIC', 'Electric Bills', 'Electricity bills', true, children);
      
      const serialized = JSON.parse(JSON.stringify(model));
      expect(serialized.children).toHaveLength(1);
      expect(serialized.children[0].id).toBe('SUB_001');
      expect(serialized.children[0].categoryCode).toBe('ELECTRIC_HOME');
    });

    it('should handle null values in serialization', () => {
      const model = new CategoryModel(null as any, null as any, null as any, null as any, null as any, null as any, null as any);
      
      const serialized = JSON.parse(JSON.stringify(model));
      expect(serialized).toEqual({
        id: null,
        order: null,
        categoryCode: null,
        categoryName: null,
        description: null,
        active: null,
        children: null
      });
    });
  });

  describe('equality and comparison', () => {
    it('should not be equal to other instances by reference', () => {
      const children1: CategoryListModel = [];
      const children2: CategoryListModel = [];
      const model1 = new CategoryModel('CAT_001', '1', 'ELECTRIC', 'Electric', 'Description', true, children1);
      const model2 = new CategoryModel('CAT_001', '1', 'ELECTRIC', 'Electric', 'Description', true, children2);
      
      expect(model1).not.toBe(model2);
    });

    it('should be equal to itself', () => {
      const children: CategoryListModel = [];
      const model = new CategoryModel('CAT_001', '1', 'ELECTRIC', 'Electric', 'Description', true, children);
      
      expect(model).toBe(model);
    });

    it('should have same serialized structure for same parameters', () => {
      const children1: CategoryListModel = [];
      const children2: CategoryListModel = [];
      const model1 = new CategoryModel('CAT_001', '1', 'ELECTRIC', 'Electric', 'Description', true, children1);
      const model2 = new CategoryModel('CAT_001', '1', 'ELECTRIC', 'Electric', 'Description', true, children2);
      
      expect(JSON.stringify(model1)).toBe(JSON.stringify(model2));
    });
  });

  describe('inheritance and extensibility', () => {
    it('should support inheritance patterns', () => {
      class ExtendedCategoryModel extends CategoryModel {
        public additionalField = 'extended';
      }
      
      const children: CategoryListModel = [];
      const extended = new ExtendedCategoryModel('CAT_001', '1', 'ELECTRIC', 'Electric', 'Description', true, children);
      
      expect(extended).toBeInstanceOf(CategoryModel);
      expect(extended).toBeInstanceOf(ExtendedCategoryModel);
      expect(extended.id).toBe('CAT_001');
      expect(extended.additionalField).toBe('extended');
    });

    it('should support method addition', () => {
      const children: CategoryListModel = [];
      const model = new CategoryModel('CAT_001', '1', 'ELECTRIC', 'Electric', 'Description', true, children);
      
      (model as any).getDisplayName = () => `${model.categoryName} (${model.categoryCode})`;
      expect(typeof (model as any).getDisplayName).toBe('function');
      expect((model as any).getDisplayName()).toBe('Electric (ELECTRIC)');
    });

    it('should support mixin patterns', () => {
      const mixin = { 
        isActive: function(this: CategoryModel) { 
          return this.active === true; 
        } 
      };
      
      const children: CategoryListModel = [];
      const model = new CategoryModel('CAT_001', '1', 'ELECTRIC', 'Electric', 'Description', true, children);
      Object.assign(model, mixin);
      
      expect((model as any).isActive()).toBe(true);
    });
  });

  describe('real-world scenarios', () => {
    it('should handle typical utility categories', () => {
      const categories = [
        { code: 'ELECTRIC', name: 'Electricity', desc: 'Electric utility bills' },
        { code: 'WATER', name: 'Water', desc: 'Water utility bills' },
        { code: 'GAS', name: 'Gas', desc: 'Gas utility bills' },
        { code: 'INTERNET', name: 'Internet', desc: 'Internet service bills' },
        { code: 'PHONE', name: 'Phone', desc: 'Phone service bills' }
      ];

      categories.forEach((cat, index) => {
        const children: CategoryListModel = [];
        const model = new CategoryModel(
          `CAT_${index + 1}`,
          (index + 1).toString(),
          cat.code,
          cat.name,
          cat.desc,
          true,
          children
        );
        
        expect(model.categoryCode).toBe(cat.code);
        expect(model.categoryName).toBe(cat.name);
        expect(model.description).toBe(cat.desc);
      });
    });

    it('should handle complex category hierarchy', () => {
      // Create a complex hierarchy: Utilities -> Electric -> Home/Business -> Basic/Premium
      const homeBasic = new CategoryModel('ELEC_HOME_BASIC', '1.1.1', 'ELECTRIC_HOME_BASIC', 'Basic Home Electric', 'Basic home electricity plan', true, []);
      const homePremium = new CategoryModel('ELEC_HOME_PREMIUM', '1.1.2', 'ELECTRIC_HOME_PREMIUM', 'Premium Home Electric', 'Premium home electricity plan', true, []);
      const businessBasic = new CategoryModel('ELEC_BUS_BASIC', '1.2.1', 'ELECTRIC_BUSINESS_BASIC', 'Basic Business Electric', 'Basic business electricity plan', true, []);
      
      const homeElectric = new CategoryModel('ELEC_HOME', '1.1', 'ELECTRIC_HOME', 'Home Electric', 'Home electricity services', true, [homeBasic, homePremium]);
      const businessElectric = new CategoryModel('ELEC_BUS', '1.2', 'ELECTRIC_BUSINESS', 'Business Electric', 'Business electricity services', true, [businessBasic]);
      
      const electric = new CategoryModel('ELECTRIC', '1', 'ELECTRIC', 'Electricity', 'All electricity services', true, [homeElectric, businessElectric]);
      
      expect(electric.children).toHaveLength(2);
      expect(electric.children[0].children).toHaveLength(2);
      expect(electric.children[1].children).toHaveLength(1);
      expect(electric.children[0].children[0].categoryCode).toBe('ELECTRIC_HOME_BASIC');
      expect(electric.children[0].children[1].categoryCode).toBe('ELECTRIC_HOME_PREMIUM');
      expect(electric.children[1].children[0].categoryCode).toBe('ELECTRIC_BUSINESS_BASIC');
    });
  });
});

describe('CategoryListModel', () => {
  describe('type definition', () => {
    it('should accept array of CategoryModel instances', () => {
      const category1 = new CategoryModel('CAT_001', '1', 'ELECTRIC', 'Electric', 'Electric bills', true, []);
      const category2 = new CategoryModel('CAT_002', '2', 'WATER', 'Water', 'Water bills', true, []);
      
      const categoryList: CategoryListModel = [category1, category2];
      
      expect(categoryList).toHaveLength(2);
      expect(categoryList[0]).toBeInstanceOf(CategoryModel);
      expect(categoryList[1]).toBeInstanceOf(CategoryModel);
      expect(categoryList[0].categoryCode).toBe('ELECTRIC');
      expect(categoryList[1].categoryCode).toBe('WATER');
    });

    it('should accept empty array', () => {
      const categoryList: CategoryListModel = [];
      
      expect(categoryList).toHaveLength(0);
      expect(Array.isArray(categoryList)).toBe(true);
    });

    it('should support array methods', () => {
      const category1 = new CategoryModel('CAT_001', '1', 'ELECTRIC', 'Electric', 'Electric bills', true, []);
      const category2 = new CategoryModel('CAT_002', '2', 'WATER', 'Water', 'Water bills', true, []);
      const category3 = new CategoryModel('CAT_003', '3', 'GAS', 'Gas', 'Gas bills', true, []);
      
      const categoryList: CategoryListModel = [category1, category2];
      
      // Test push
      categoryList.push(category3);
      expect(categoryList).toHaveLength(3);
      
      // Test filter
      const activeCategories = categoryList.filter(cat => cat.active);
      expect(activeCategories).toHaveLength(3);
      
      // Test map
      const categoryCodes = categoryList.map(cat => cat.categoryCode);
      expect(categoryCodes).toEqual(['ELECTRIC', 'WATER', 'GAS']);
      
      // Test find
      const electricCategory = categoryList.find(cat => cat.categoryCode === 'ELECTRIC');
      expect(electricCategory).toBe(category1);
    });

    it('should be JSON serializable', () => {
      const category1 = new CategoryModel('CAT_001', '1', 'ELECTRIC', 'Electric', 'Electric bills', true, []);
      const category2 = new CategoryModel('CAT_002', '2', 'WATER', 'Water', 'Water bills', false, []);
      
      const categoryList: CategoryListModel = [category1, category2];
      
      const serialized = JSON.parse(JSON.stringify(categoryList));
      expect(serialized).toHaveLength(2);
      expect(serialized[0].categoryCode).toBe('ELECTRIC');
      expect(serialized[1].categoryCode).toBe('WATER');
      expect(serialized[0].active).toBe(true);
      expect(serialized[1].active).toBe(false);
    });
  });
});
