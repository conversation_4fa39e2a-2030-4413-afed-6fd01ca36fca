import {
  BillValidateModel,
  OriginatorAccountModel,
  AccountIdentificationModel,
  TransferTransactionInformationModel,
  CounterpartyModel,
  CurrencyAmountModel,
  TransferAdditionsModel,
  OriginatorModel
} from '../BillValidateModel';

describe('BillValidateModel Interfaces', () => {
  describe('BillValidateModel', () => {
    it('should accept valid BillValidateModel object', () => {
      const mockBillValidate: BillValidateModel = {
        id: 'BILL_001',
        originatorAccount: {
          arrangementId: 'ARR_001',
          externalArrangementId: 'EXT_ARR_001',
          identification: {
            identification: 'ACC_001',
            schemeName: 'IBAN'
          }
        },
        instructionPriority: 'HIGH',
        requestedExecutionDate: '2024-01-01',
        paymentMode: 'INSTANT',
        paymentType: 'BILL_PAYMENT',
        transferTransactionInformation: {
          counterparty: {
            name: 'Electric Company',
            role: 'CREDITOR'
          },
          counterpartyAccount: {
            identification: 'ELEC_ACC_001',
            schemeName: 'BBAN'
          },
          instructedAmount: {
            amount: '100000',
            currencyCode: 'VND'
          },
          additions: {
            bpQueryRef: 'QUERY_REF_001',
            bpBillList: '[]',
            bpSummary: '{}',
            bpServiceCode: 'ELECTRIC',
            cifNo: 'CIF123456',
            bpCategory: 'UTILITY',
            bpAccountingNumber: 'ACC_NUM_001'
          }
        },
        originator: {
          name: 'John Doe',
          role: 'DEBTOR',
          postalAddress: {
            street: '123 Main St',
            city: 'Ho Chi Minh City',
            country: 'Vietnam'
          }
        },
        totalAmount: {
          amount: '100000',
          currencyCode: 'VND'
        },
        isIntraLegalEntityPaymentOrder: false,
        canApprove: true,
        finalApprover: false
      };

      expect(mockBillValidate.id).toBe('BILL_001');
      expect(mockBillValidate.originatorAccount.arrangementId).toBe('ARR_001');
      expect(mockBillValidate.transferTransactionInformation.counterparty.name).toBe('Electric Company');
      expect(mockBillValidate.totalAmount.amount).toBe('100000');
    });

    it('should handle Vietnamese characters in string fields', () => {
      const mockBillValidate: BillValidateModel = {
        id: 'HÓA_ĐƠN_001',
        originatorAccount: {
          arrangementId: 'TÀI_KHOẢN_001',
          externalArrangementId: 'TK_NGOÀI_001',
          identification: {
            identification: 'STK_001',
            schemeName: 'IBAN'
          }
        },
        instructionPriority: 'CAO',
        requestedExecutionDate: '2024-01-01',
        paymentMode: 'NGAY_LẬP_TỨC',
        paymentType: 'THANH_TOÁN_HÓA_ĐƠN',
        transferTransactionInformation: {
          counterparty: {
            name: 'Công ty Điện lực',
            role: 'NGƯỜI_NHẬN'
          },
          counterpartyAccount: {
            identification: 'TK_ĐIỆN_001',
            schemeName: 'BBAN'
          },
          instructedAmount: {
            amount: '100000',
            currencyCode: 'VND'
          },
          additions: {
            bpQueryRef: 'TRUY_VẤN_001',
            bpBillList: '[]',
            bpSummary: '{}',
            bpServiceCode: 'ĐIỆN',
            cifNo: 'CIF123456',
            bpCategory: 'TIỆN_ÍCH',
            bpAccountingNumber: 'SỐ_KẾ_TOÁN_001'
          }
        },
        originator: {
          name: 'Nguyễn Văn A',
          role: 'NGƯỜI_TRẢ',
          postalAddress: {
            street: '123 Đường Nguyễn Huệ',
            city: 'Thành phố Hồ Chí Minh',
            country: 'Việt Nam'
          }
        },
        totalAmount: {
          amount: '100000',
          currencyCode: 'VND'
        },
        isIntraLegalEntityPaymentOrder: false,
        canApprove: true,
        finalApprover: false
      };

      expect(mockBillValidate.transferTransactionInformation.counterparty.name).toBe('Công ty Điện lực');
      expect(mockBillValidate.originator.name).toBe('Nguyễn Văn A');
    });

    it('should handle boolean fields correctly', () => {
      const mockBillValidate: BillValidateModel = {
        id: 'BILL_001',
        originatorAccount: {} as OriginatorAccountModel,
        instructionPriority: 'HIGH',
        requestedExecutionDate: '2024-01-01',
        paymentMode: 'INSTANT',
        paymentType: 'BILL_PAYMENT',
        transferTransactionInformation: {} as TransferTransactionInformationModel,
        originator: {} as OriginatorModel,
        totalAmount: {} as CurrencyAmountModel,
        isIntraLegalEntityPaymentOrder: true,
        canApprove: false,
        finalApprover: true
      };

      expect(mockBillValidate.isIntraLegalEntityPaymentOrder).toBe(true);
      expect(mockBillValidate.canApprove).toBe(false);
      expect(mockBillValidate.finalApprover).toBe(true);
    });
  });

  describe('OriginatorAccountModel', () => {
    it('should accept valid OriginatorAccountModel object', () => {
      const mockOriginatorAccount: OriginatorAccountModel = {
        arrangementId: 'ARR_001',
        externalArrangementId: 'EXT_ARR_001',
        identification: {
          identification: 'ACC_001',
          schemeName: 'IBAN'
        }
      };

      expect(mockOriginatorAccount.arrangementId).toBe('ARR_001');
      expect(mockOriginatorAccount.identification.identification).toBe('ACC_001');
    });

    it('should handle empty string values', () => {
      const mockOriginatorAccount: OriginatorAccountModel = {
        arrangementId: '',
        externalArrangementId: '',
        identification: {
          identification: '',
          schemeName: ''
        }
      };

      expect(mockOriginatorAccount.arrangementId).toBe('');
      expect(mockOriginatorAccount.identification.schemeName).toBe('');
    });
  });

  describe('AccountIdentificationModel', () => {
    it('should accept valid AccountIdentificationModel object', () => {
      const mockAccountId: AccountIdentificationModel = {
        identification: 'ACC_123456',
        schemeName: 'IBAN'
      };

      expect(mockAccountId.identification).toBe('ACC_123456');
      expect(mockAccountId.schemeName).toBe('IBAN');
    });

    it('should handle different scheme names', () => {
      const schemes = ['IBAN', 'BBAN', 'UPIC', 'CLRG'];
      schemes.forEach(scheme => {
        const mockAccountId: AccountIdentificationModel = {
          identification: 'TEST_ID',
          schemeName: scheme
        };
        expect(mockAccountId.schemeName).toBe(scheme);
      });
    });
  });

  describe('TransferTransactionInformationModel', () => {
    it('should accept valid TransferTransactionInformationModel object', () => {
      const mockTransferInfo: TransferTransactionInformationModel = {
        counterparty: {
          name: 'Test Company',
          role: 'CREDITOR'
        },
        counterpartyAccount: {
          identification: 'COMP_ACC_001',
          schemeName: 'BBAN'
        },
        instructedAmount: {
          amount: '50000',
          currencyCode: 'VND'
        },
        additions: {
          bpQueryRef: 'QUERY_001',
          bpBillList: '[{"id": "BILL_001"}]',
          bpSummary: '{"total": 50000}',
          bpServiceCode: 'WATER',
          cifNo: 'CIF789012',
          bpCategory: 'UTILITY',
          bpAccountingNumber: 'ACC_002'
        }
      };

      expect(mockTransferInfo.counterparty.name).toBe('Test Company');
      expect(mockTransferInfo.instructedAmount.amount).toBe('50000');
      expect(mockTransferInfo.additions.bpServiceCode).toBe('WATER');
    });
  });

  describe('CounterpartyModel', () => {
    it('should accept valid CounterpartyModel object', () => {
      const mockCounterparty: CounterpartyModel = {
        name: 'Electric Utility Company',
        role: 'CREDITOR'
      };

      expect(mockCounterparty.name).toBe('Electric Utility Company');
      expect(mockCounterparty.role).toBe('CREDITOR');
    });

    it('should handle different roles', () => {
      const roles = ['CREDITOR', 'DEBTOR', 'AGENT', 'INTERMEDIARY'];
      roles.forEach(role => {
        const mockCounterparty: CounterpartyModel = {
          name: 'Test Entity',
          role: role
        };
        expect(mockCounterparty.role).toBe(role);
      });
    });
  });

  describe('CurrencyAmountModel', () => {
    it('should accept valid CurrencyAmountModel object', () => {
      const mockAmount: CurrencyAmountModel = {
        amount: '1000000',
        currencyCode: 'VND'
      };

      expect(mockAmount.amount).toBe('1000000');
      expect(mockAmount.currencyCode).toBe('VND');
    });

    it('should handle different currencies', () => {
      const currencies = ['VND', 'USD', 'EUR', 'JPY'];
      currencies.forEach(currency => {
        const mockAmount: CurrencyAmountModel = {
          amount: '100',
          currencyCode: currency
        };
        expect(mockAmount.currencyCode).toBe(currency);
      });
    });

    it('should handle decimal amounts as strings', () => {
      const mockAmount: CurrencyAmountModel = {
        amount: '1000.50',
        currencyCode: 'USD'
      };

      expect(mockAmount.amount).toBe('1000.50');
    });
  });

  describe('TransferAdditionsModel', () => {
    it('should accept valid TransferAdditionsModel object', () => {
      const mockAdditions: TransferAdditionsModel = {
        bpQueryRef: 'QUERY_REF_123',
        bpBillList: '[{"billId": "BILL_001", "amount": 50000}]',
        bpSummary: '{"totalBills": 1, "totalAmount": 50000}',
        bpServiceCode: 'ELECTRIC',
        cifNo: 'CIF123456789',
        bpCategory: 'UTILITY',
        bpAccountingNumber: 'ACC_NUM_123'
      };

      expect(mockAdditions.bpQueryRef).toBe('QUERY_REF_123');
      expect(mockAdditions.bpServiceCode).toBe('ELECTRIC');
      expect(mockAdditions.cifNo).toBe('CIF123456789');
    });

    it('should handle JSON strings in bill list and summary', () => {
      const billListJson = JSON.stringify([
        { billId: 'BILL_001', amount: 25000 },
        { billId: 'BILL_002', amount: 25000 }
      ]);
      const summaryJson = JSON.stringify({
        totalBills: 2,
        totalAmount: 50000,
        currency: 'VND'
      });

      const mockAdditions: TransferAdditionsModel = {
        bpQueryRef: 'QUERY_REF_123',
        bpBillList: billListJson,
        bpSummary: summaryJson,
        bpServiceCode: 'ELECTRIC',
        cifNo: 'CIF123456789',
        bpCategory: 'UTILITY',
        bpAccountingNumber: 'ACC_NUM_123'
      };

      expect(() => JSON.parse(mockAdditions.bpBillList)).not.toThrow();
      expect(() => JSON.parse(mockAdditions.bpSummary)).not.toThrow();
      
      const parsedBillList = JSON.parse(mockAdditions.bpBillList);
      const parsedSummary = JSON.parse(mockAdditions.bpSummary);
      
      expect(parsedBillList).toHaveLength(2);
      expect(parsedSummary.totalBills).toBe(2);
    });
  });

  describe('OriginatorModel', () => {
    it('should accept valid OriginatorModel object', () => {
      const mockOriginator: OriginatorModel = {
        name: 'John Smith',
        role: 'DEBTOR',
        postalAddress: {
          street: '456 Oak Avenue',
          city: 'Hanoi',
          postalCode: '100000',
          country: 'Vietnam'
        }
      };

      expect(mockOriginator.name).toBe('John Smith');
      expect(mockOriginator.role).toBe('DEBTOR');
      expect(mockOriginator.postalAddress.city).toBe('Hanoi');
    });

    it('should handle complex postal address objects', () => {
      const mockOriginator: OriginatorModel = {
        name: 'Jane Doe',
        role: 'DEBTOR',
        postalAddress: {
          addressLine1: '123 Main Street',
          addressLine2: 'Apartment 4B',
          district: 'District 1',
          city: 'Ho Chi Minh City',
          province: 'Ho Chi Minh',
          postalCode: '700000',
          country: 'Vietnam',
          coordinates: {
            latitude: 10.8231,
            longitude: 106.6297
          }
        }
      };

      expect(mockOriginator.postalAddress.addressLine1).toBe('123 Main Street');
      expect(mockOriginator.postalAddress.coordinates.latitude).toBe(10.8231);
    });

    it('should handle empty postal address object', () => {
      const mockOriginator: OriginatorModel = {
        name: 'Test User',
        role: 'DEBTOR',
        postalAddress: {}
      };

      expect(mockOriginator.postalAddress).toEqual({});
    });
  });

  describe('Interface Type Checking', () => {
    it('should validate complete BillValidateModel structure', () => {
      const createMockBillValidate = (): BillValidateModel => ({
        id: 'BILL_VALIDATE_001',
        originatorAccount: {
          arrangementId: 'ARR_001',
          externalArrangementId: 'EXT_ARR_001',
          identification: {
            identification: 'ACC_001',
            schemeName: 'IBAN'
          }
        },
        instructionPriority: 'NORMAL',
        requestedExecutionDate: '2024-12-31',
        paymentMode: 'BATCH',
        paymentType: 'UTILITY_PAYMENT',
        transferTransactionInformation: {
          counterparty: {
            name: 'Water Company Ltd',
            role: 'CREDITOR'
          },
          counterpartyAccount: {
            identification: 'WATER_ACC_001',
            schemeName: 'BBAN'
          },
          instructedAmount: {
            amount: '75000',
            currencyCode: 'VND'
          },
          additions: {
            bpQueryRef: 'WATER_QUERY_001',
            bpBillList: '[{"billId": "WATER_BILL_001"}]',
            bpSummary: '{"waterUsage": "15m3"}',
            bpServiceCode: 'WATER',
            cifNo: 'CIF987654321',
            bpCategory: 'UTILITY',
            bpAccountingNumber: 'WATER_ACC_NUM_001'
          }
        },
        originator: {
          name: 'Trần Thị B',
          role: 'DEBTOR',
          postalAddress: {
            street: '789 Lê Lợi',
            ward: 'Phường Bến Nghé',
            district: 'Quận 1',
            city: 'TP. Hồ Chí Minh'
          }
        },
        totalAmount: {
          amount: '75000',
          currencyCode: 'VND'
        },
        isIntraLegalEntityPaymentOrder: false,
        canApprove: true,
        finalApprover: false
      });

      const billValidate = createMockBillValidate();
      
      // Verify all required properties exist
      expect(billValidate).toHaveProperty('id');
      expect(billValidate).toHaveProperty('originatorAccount');
      expect(billValidate).toHaveProperty('instructionPriority');
      expect(billValidate).toHaveProperty('requestedExecutionDate');
      expect(billValidate).toHaveProperty('paymentMode');
      expect(billValidate).toHaveProperty('paymentType');
      expect(billValidate).toHaveProperty('transferTransactionInformation');
      expect(billValidate).toHaveProperty('originator');
      expect(billValidate).toHaveProperty('totalAmount');
      expect(billValidate).toHaveProperty('isIntraLegalEntityPaymentOrder');
      expect(billValidate).toHaveProperty('canApprove');
      expect(billValidate).toHaveProperty('finalApprover');
      
      // Verify nested structure
      expect(billValidate.originatorAccount).toHaveProperty('identification');
      expect(billValidate.transferTransactionInformation).toHaveProperty('counterparty');
      expect(billValidate.transferTransactionInformation).toHaveProperty('additions');
    });

    it('should handle serialization and deserialization', () => {
      const originalBillValidate: BillValidateModel = {
        id: 'SERIALIZE_TEST',
        originatorAccount: {
          arrangementId: 'ARR_SERIALIZE',
          externalArrangementId: 'EXT_SERIALIZE',
          identification: {
            identification: 'ACC_SERIALIZE',
            schemeName: 'IBAN'
          }
        },
        instructionPriority: 'HIGH',
        requestedExecutionDate: '2024-01-15',
        paymentMode: 'INSTANT',
        paymentType: 'BILL_PAYMENT',
        transferTransactionInformation: {
          counterparty: {
            name: 'Serialization Test Company',
            role: 'CREDITOR'
          },
          counterpartyAccount: {
            identification: 'SERIALIZE_ACC',
            schemeName: 'BBAN'
          },
          instructedAmount: {
            amount: '123456',
            currencyCode: 'VND'
          },
          additions: {
            bpQueryRef: 'SERIALIZE_QUERY',
            bpBillList: '[]',
            bpSummary: '{}',
            bpServiceCode: 'TEST',
            cifNo: 'CIF_SERIALIZE',
            bpCategory: 'TEST_CATEGORY',
            bpAccountingNumber: 'SERIALIZE_ACC_NUM'
          }
        },
        originator: {
          name: 'Test Serializer',
          role: 'DEBTOR',
          postalAddress: {
            test: 'address'
          }
        },
        totalAmount: {
          amount: '123456',
          currencyCode: 'VND'
        },
        isIntraLegalEntityPaymentOrder: true,
        canApprove: false,
        finalApprover: true
      };

      const serialized = JSON.stringify(originalBillValidate);
      const deserialized: BillValidateModel = JSON.parse(serialized);

      expect(deserialized.id).toBe(originalBillValidate.id);
      expect(deserialized.transferTransactionInformation.counterparty.name)
        .toBe(originalBillValidate.transferTransactionInformation.counterparty.name);
      expect(deserialized.isIntraLegalEntityPaymentOrder)
        .toBe(originalBillValidate.isIntraLegalEntityPaymentOrder);
    });
  });
});
