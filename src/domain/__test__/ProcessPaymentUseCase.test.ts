import {describe, it, expect, jest, beforeEach} from '@jest/globals';

// Example domain entities
interface PaymentRequest {
  amount: number;
  currency: string;
  methodId: string;
  userId: string;
}

interface PaymentResult {
  transactionId: string;
  status: 'SUCCESS' | 'FAILED' | 'PENDING';
  message: string;
}

// Example repository interface
interface PaymentRepository {
  processPayment(request: PaymentRequest): Promise<PaymentResult>;
  validatePaymentMethod(methodId: string): Promise<boolean>;
  checkUserBalance(userId: string, amount: number): Promise<boolean>;
}

// Example use case
class ProcessPaymentUseCase {
  constructor(private paymentRepository: PaymentRepository) {}

  async execute(request: PaymentRequest): Promise<PaymentResult> {
    // Validate input
    if (request.amount <= 0) {
      throw new Error('Amount must be greater than 0');
    }

    if (!request.methodId) {
      throw new Error('Payment method is required');
    }

    if (!request.userId) {
      throw new Error('User ID is required');
    }

    // Validate payment method
    const isValidMethod = await this.paymentRepository.validatePaymentMethod(request.methodId);
    if (!isValidMethod) {
      throw new Error('Invalid payment method');
    }

    // Check user balance (for certain payment methods)
    if (request.methodId === 'WALLET') {
      const hasSufficientBalance = await this.paymentRepository.checkUserBalance(
        request.userId,
        request.amount
      );
      if (!hasSufficientBalance) {
        throw new Error('Insufficient balance');
      }
    }

    // Process payment
    try {
      const result = await this.paymentRepository.processPayment(request);
      return result;
    } catch (error) {
      throw new Error(`Payment processing failed: ${error.message}`);
    }
  }
}

// Tests
describe('ProcessPaymentUseCase', () => {
  let useCase: ProcessPaymentUseCase;
  let mockRepository: jest.Mocked<PaymentRepository>;

  beforeEach(() => {
    mockRepository = {
      processPayment: jest.fn(),
      validatePaymentMethod: jest.fn(),
      checkUserBalance: jest.fn(),
    };
    useCase = new ProcessPaymentUseCase(mockRepository);
  });

  describe('execute', () => {
    const validRequest: PaymentRequest = {
      amount: 100000,
      currency: 'VND',
      methodId: 'CREDIT_CARD',
      userId: 'user123',
    };

    it('should process payment successfully', async () => {
      const expectedResult: PaymentResult = {
        transactionId: 'txn_123',
        status: 'SUCCESS',
        message: 'Payment processed successfully',
      };

      mockRepository.validatePaymentMethod.mockResolvedValue(true);
      mockRepository.processPayment.mockResolvedValue(expectedResult);

      const result = await useCase.execute(validRequest);

      expect(result).toEqual(expectedResult);
      expect(mockRepository.validatePaymentMethod).toHaveBeenCalledWith('CREDIT_CARD');
      expect(mockRepository.processPayment).toHaveBeenCalledWith(validRequest);
    });

    it('should throw error for invalid amount', async () => {
      const invalidRequest = { ...validRequest, amount: 0 };

      await expect(useCase.execute(invalidRequest)).rejects.toThrow('Amount must be greater than 0');
    });

    it('should throw error for negative amount', async () => {
      const invalidRequest = { ...validRequest, amount: -100 };

      await expect(useCase.execute(invalidRequest)).rejects.toThrow('Amount must be greater than 0');
    });

    it('should throw error for missing payment method', async () => {
      const invalidRequest = { ...validRequest, methodId: '' };

      await expect(useCase.execute(invalidRequest)).rejects.toThrow('Payment method is required');
    });

    it('should throw error for missing user ID', async () => {
      const invalidRequest = { ...validRequest, userId: '' };

      await expect(useCase.execute(invalidRequest)).rejects.toThrow('User ID is required');
    });

    it('should throw error for invalid payment method', async () => {
      mockRepository.validatePaymentMethod.mockResolvedValue(false);

      await expect(useCase.execute(validRequest)).rejects.toThrow('Invalid payment method');
      expect(mockRepository.validatePaymentMethod).toHaveBeenCalledWith('CREDIT_CARD');
    });

    it('should check balance for wallet payments', async () => {
      const walletRequest = { ...validRequest, methodId: 'WALLET' };

      mockRepository.validatePaymentMethod.mockResolvedValue(true);
      mockRepository.checkUserBalance.mockResolvedValue(true);
      mockRepository.processPayment.mockResolvedValue({
        transactionId: 'txn_123',
        status: 'SUCCESS',
        message: 'Payment processed successfully',
      });

      await useCase.execute(walletRequest);

      expect(mockRepository.checkUserBalance).toHaveBeenCalledWith('user123', 100000);
    });

    it('should throw error for insufficient wallet balance', async () => {
      const walletRequest = { ...validRequest, methodId: 'WALLET' };

      mockRepository.validatePaymentMethod.mockResolvedValue(true);
      mockRepository.checkUserBalance.mockResolvedValue(false);

      await expect(useCase.execute(walletRequest)).rejects.toThrow('Insufficient balance');
    });

    it('should handle repository errors', async () => {
      mockRepository.validatePaymentMethod.mockResolvedValue(true);
      mockRepository.processPayment.mockRejectedValue(new Error('Network error'));

      await expect(useCase.execute(validRequest)).rejects.toThrow('Payment processing failed: Network error');
    });

    it('should not check balance for non-wallet payments', async () => {
      mockRepository.validatePaymentMethod.mockResolvedValue(true);
      mockRepository.processPayment.mockResolvedValue({
        transactionId: 'txn_123',
        status: 'SUCCESS',
        message: 'Payment processed successfully',
      });

      await useCase.execute(validRequest);

      expect(mockRepository.checkUserBalance).not.toHaveBeenCalled();
    });
  });
});
