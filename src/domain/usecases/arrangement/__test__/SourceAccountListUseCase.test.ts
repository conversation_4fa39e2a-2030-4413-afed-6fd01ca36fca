import {describe, it, expect, jest, beforeEach} from '@jest/globals';
import {SourceAccountListUseCase} from '../SourceAccountListUseCase';
import {IArrangementRepository} from '../../../repositories/IArrangementRepository';
import {SourceAccountListRequest} from '../../../../data/models/source-account-list/SourceAccountListRequest';
import {SourceAccountListModel} from '../../../entities/source-account-list/SourceAccountListModel';
import {ResultState} from '../../../../core/ResultState';
import {CustomError, ErrorCategory} from '../../../../core/MSBCustomError';
import {BaseResponse} from '../../../../core/BaseResponse';

// Mock dependencies
jest.mock('../../../../utils/ExcecutionHandler', () => ({
  ExecutionHandler: {
    execute: jest.fn(),
  },
}));

import {ExecutionHandler} from '../../../../utils/ExcecutionHandler';

const mockExecutionHandler = ExecutionHandler as jest.Mocked<typeof ExecutionHandler>;

describe('SourceAccountListUseCase', () => {
  let sourceAccountListUseCase: SourceAccountListUseCase;
  let mockRepository: jest.Mocked<IArrangementRepository>;

  beforeEach(() => {
    jest.clearAllMocks();

    // Create mock repository
    mockRepository = {
      sourceAccountList: jest.fn(),
    };

    sourceAccountListUseCase = new SourceAccountListUseCase(mockRepository);
  });

  describe('constructor', () => {
    it('should create SourceAccountListUseCase with repository', () => {
      expect(sourceAccountListUseCase).toBeInstanceOf(SourceAccountListUseCase);
      expect(sourceAccountListUseCase['repository']).toBe(mockRepository);
    });
  });

  describe('execute', () => {
    const mockSourceAccountListModel: SourceAccountListModel = {
      accounts: [
        {
          id: 'ACC001',
          productKindName: 'Savings Account',
          legalEntityIds: ['ENTITY001'],
          productId: 'PROD001',
          productTypeName: 'Personal Savings',
          externalProductId: 'EXT_PROD001',
          externalArrangementId: 'EXT_ARR001',
          product: {
            externalId: 'EXT_PROD001',
            productKindName: 'Savings Account',
            typeName: 'Personal Savings',
          },
          isDefault: 'Y',
          cifNo: 'CIF001',
          name: 'Primary Savings Account',
          bookedBalance: 1000000,
          availableBalance: 950000,
          currency: 'VND',
          externalTransferAllowed: true,
          urgentTransferAllowed: false,
          accountOpeningDate: '2023-01-01',
          accountHolderNames: 'John Doe',
          bankAlias: 'MSB',
        },
      ],
      totalCount: 1,
    };

    const mockBaseResponse: BaseResponse<SourceAccountListModel> = mockSourceAccountListModel;

    it('should use default parameters when no request is provided', async () => {
      const mockResultState: ResultState<SourceAccountListModel> = {
        status: 'SUCCESS',
        data: mockSourceAccountListModel,
      };

      mockRepository.sourceAccountList.mockResolvedValue(mockBaseResponse);
      mockExecutionHandler.execute.mockImplementation(async (fn) => {
        await fn();
        return mockResultState;
      });

      await sourceAccountListUseCase.execute();

      const expectedDefaultParams: SourceAccountListRequest = {
        externalStateIds: ['ACTIVE'],
        externalProductKindIds: ['kind1', 'kind10'],
        currency: 'VND',
      };

      expect(mockRepository.sourceAccountList).toHaveBeenCalledWith(expectedDefaultParams);
      expect(mockRepository.sourceAccountList).toHaveBeenCalledTimes(1);
    });

    it('should use provided request when given', async () => {
      const customRequest: SourceAccountListRequest = {
        externalStateIds: ['ACTIVE', 'PENDING'],
        externalProductKindIds: ['kind2', 'kind3'],
        currency: 'USD',
      };

      const mockResultState: ResultState<SourceAccountListModel> = {
        status: 'SUCCESS',
        data: mockSourceAccountListModel,
      };

      mockRepository.sourceAccountList.mockResolvedValue(mockBaseResponse);
      mockExecutionHandler.execute.mockImplementation(async (fn) => {
        await fn();
        return mockResultState;
      });

      await sourceAccountListUseCase.execute(customRequest);

      expect(mockRepository.sourceAccountList).toHaveBeenCalledWith(customRequest);
      expect(mockRepository.sourceAccountList).toHaveBeenCalledTimes(1);
    });

    it('should call ExecutionHandler.execute with repository function', async () => {
      const mockResultState: ResultState<SourceAccountListModel> = {
        status: 'SUCCESS',
        data: mockSourceAccountListModel,
      };

      mockExecutionHandler.execute.mockResolvedValue(mockResultState);

      await sourceAccountListUseCase.execute();

      expect(mockExecutionHandler.execute).toHaveBeenCalledWith(
        expect.any(Function)
      );
      expect(mockExecutionHandler.execute).toHaveBeenCalledTimes(1);
    });

    it('should return SUCCESS result state when account list retrieval succeeds', async () => {
      const mockResultState: ResultState<SourceAccountListModel> = {
        status: 'SUCCESS',
        data: mockSourceAccountListModel,
      };

      mockRepository.sourceAccountList.mockResolvedValue(mockBaseResponse);
      mockExecutionHandler.execute.mockResolvedValue(mockResultState);

      const result = await sourceAccountListUseCase.execute();

      expect(result).toEqual(mockResultState);
      expect(result.status).toBe('SUCCESS');
      expect(result.data).toBe(mockSourceAccountListModel);
    });

    it('should return ERROR result state when account list retrieval fails', async () => {
      const mockError = new CustomError(
        'ACCOUNT_LIST_ERROR',
        ErrorCategory.API,
        'Account List Error',
        'Failed to retrieve account list',
        true
      );

      const mockResultState: ResultState<SourceAccountListModel> = {
        status: 'ERROR',
        error: mockError,
      };

      mockExecutionHandler.execute.mockResolvedValue(mockResultState);

      const result = await sourceAccountListUseCase.execute();

      expect(result).toEqual(mockResultState);
      expect(result.status).toBe('ERROR');
      if (result.status === 'ERROR') {
        expect(result.error).toBe(mockError);
      }
    });

    it('should handle empty account list', async () => {
      const emptyAccountListModel: SourceAccountListModel = {
        accounts: [],
        totalCount: 0,
      };

      const mockResultState: ResultState<SourceAccountListModel> = {
        status: 'SUCCESS',
        data: emptyAccountListModel,
      };

      mockRepository.sourceAccountList.mockResolvedValue(emptyAccountListModel);
      mockExecutionHandler.execute.mockResolvedValue(mockResultState);

      const result = await sourceAccountListUseCase.execute();

      expect(result.status).toBe('SUCCESS');
      expect(result.data?.accounts).toHaveLength(0);
      expect(result.data?.totalCount).toBe(0);
    });

    it('should handle multiple accounts in the list', async () => {
      const multipleAccountsModel: SourceAccountListModel = {
        accounts: [
          {
            id: 'ACC001',
            productKindName: 'Savings Account',
            legalEntityIds: ['ENTITY001'],
            productId: 'PROD001',
            productTypeName: 'Personal Savings',
            externalProductId: 'EXT_PROD001',
            externalArrangementId: 'EXT_ARR001',
            product: {
              externalId: 'EXT_PROD001',
              productKindName: 'Savings Account',
              typeName: 'Personal Savings',
            },
            isDefault: 'Y',
            cifNo: 'CIF001',
            name: 'Primary Savings Account',
            bookedBalance: 1000000,
            availableBalance: 950000,
            currency: 'VND',
            externalTransferAllowed: true,
            urgentTransferAllowed: false,
            accountOpeningDate: '2023-01-01',
            accountHolderNames: 'John Doe',
            bankAlias: 'MSB',
          },
          {
            id: 'ACC002',
            productKindName: 'Checking Account',
            legalEntityIds: ['ENTITY001'],
            productId: 'PROD002',
            productTypeName: 'Personal Checking',
            externalProductId: 'EXT_PROD002',
            externalArrangementId: 'EXT_ARR002',
            product: {
              externalId: 'EXT_PROD002',
              productKindName: 'Checking Account',
              typeName: 'Personal Checking',
            },
            isDefault: 'N',
            cifNo: 'CIF001',
            name: 'Secondary Checking Account',
            bookedBalance: 500000,
            availableBalance: 480000,
            currency: 'VND',
            externalTransferAllowed: true,
            urgentTransferAllowed: true,
            accountOpeningDate: '2023-02-01',
            accountHolderNames: 'John Doe',
            bankAlias: 'MSB',
          },
        ],
        totalCount: 2,
      };

      const mockResultState: ResultState<SourceAccountListModel> = {
        status: 'SUCCESS',
        data: multipleAccountsModel,
      };

      mockRepository.sourceAccountList.mockResolvedValue(multipleAccountsModel);
      mockExecutionHandler.execute.mockResolvedValue(mockResultState);

      const result = await sourceAccountListUseCase.execute();

      expect(result.status).toBe('SUCCESS');
      expect(result.data?.accounts).toHaveLength(2);
      expect(result.data?.totalCount).toBe(2);
    });

    it('should handle different currency requests', async () => {
      const currencies = ['VND', 'USD', 'EUR'];

      for (const currency of currencies) {
        const request: SourceAccountListRequest = {
          externalStateIds: ['ACTIVE'],
          externalProductKindIds: ['kind1'],
          currency,
        };

        const mockResultState: ResultState<SourceAccountListModel> = {
          status: 'SUCCESS',
          data: mockSourceAccountListModel,
        };

        mockRepository.sourceAccountList.mockResolvedValue(mockBaseResponse);
        mockExecutionHandler.execute.mockResolvedValue(mockResultState);

        await sourceAccountListUseCase.execute(request);

        expect(mockRepository.sourceAccountList).toHaveBeenCalledWith(
          expect.objectContaining({currency})
        );
      }

      expect(mockRepository.sourceAccountList).toHaveBeenCalledTimes(3);
    });

    it('should handle different product kind filters', async () => {
      const productKindFilters = [
        ['kind1', 'kind10'],
        ['kind2', 'kind3'],
        ['kind5'],
      ];

      for (const externalProductKindIds of productKindFilters) {
        const request: SourceAccountListRequest = {
          externalStateIds: ['ACTIVE'],
          externalProductKindIds,
          currency: 'VND',
        };

        const mockResultState: ResultState<SourceAccountListModel> = {
          status: 'SUCCESS',
          data: mockSourceAccountListModel,
        };

        mockRepository.sourceAccountList.mockResolvedValue(mockBaseResponse);
        mockExecutionHandler.execute.mockResolvedValue(mockResultState);

        await sourceAccountListUseCase.execute(request);

        expect(mockRepository.sourceAccountList).toHaveBeenCalledWith(
          expect.objectContaining({externalProductKindIds})
        );
      }

      expect(mockRepository.sourceAccountList).toHaveBeenCalledTimes(3);
    });
  });

  describe('Error handling scenarios', () => {
    it('should handle network errors', async () => {
      const networkError = new CustomError(
        'NETWORK_ERROR',
        ErrorCategory.NETWORK,
        'Network Error',
        'Unable to connect to server',
        true
      );

      const mockResultState: ResultState<SourceAccountListModel> = {
        status: 'ERROR',
        error: networkError,
      };

      mockExecutionHandler.execute.mockResolvedValue(mockResultState);

      const result = await sourceAccountListUseCase.execute();

      expect(result.status).toBe('ERROR');
      if (result.status === 'ERROR') {
        expect(result.error).toBe(networkError);
        expect(result.error.category).toBe(ErrorCategory.NETWORK);
        expect(result.error.retryable).toBe(true);
      }
    });

    it('should handle authentication errors', async () => {
      const authError = new CustomError(
        'UNAUTHORIZED',
        ErrorCategory.AUTHENTICATION,
        'Unauthorized',
        'User is not authenticated',
        false
      );

      const mockResultState: ResultState<SourceAccountListModel> = {
        status: 'ERROR',
        error: authError,
      };

      mockExecutionHandler.execute.mockResolvedValue(mockResultState);

      const result = await sourceAccountListUseCase.execute();

      expect(result.status).toBe('ERROR');
      if (result.status === 'ERROR') {
        expect(result.error).toBe(authError);
        expect(result.error.category).toBe(ErrorCategory.AUTHENTICATION);
        expect(result.error.retryable).toBe(false);
      }
    });

    it('should handle business logic errors', async () => {
      const businessError = new CustomError(
        'NO_ACCOUNTS_FOUND',
        ErrorCategory.BUSINESS,
        'No Accounts Found',
        'Customer has no active accounts',
        false
      );

      const mockResultState: ResultState<SourceAccountListModel> = {
        status: 'ERROR',
        error: businessError,
      };

      mockExecutionHandler.execute.mockResolvedValue(mockResultState);

      const result = await sourceAccountListUseCase.execute();

      expect(result.status).toBe('ERROR');
      if (result.status === 'ERROR') {
        expect(result.error).toBe(businessError);
        expect(result.error.category).toBe(ErrorCategory.BUSINESS);
      }
    });

    it('should handle API errors', async () => {
      const apiError = new CustomError(
        'API_ERROR',
        ErrorCategory.API,
        'API Error',
        'Internal server error',
        false
      );

      const mockResultState: ResultState<SourceAccountListModel> = {
        status: 'ERROR',
        error: apiError,
      };

      mockExecutionHandler.execute.mockResolvedValue(mockResultState);

      const result = await sourceAccountListUseCase.execute();

      expect(result.status).toBe('ERROR');
      if (result.status === 'ERROR') {
        expect(result.error).toBe(apiError);
        expect(result.error.category).toBe(ErrorCategory.API);
      }
    });
  });

  describe('Integration scenarios', () => {
    it('should handle complete source account list flow', async () => {
      const request: SourceAccountListRequest = {
        externalStateIds: ['ACTIVE'],
        externalProductKindIds: ['kind1', 'kind10'],
        currency: 'VND',
      };

      const accountListModel: SourceAccountListModel = {
        accounts: [
          {
            id: 'ACC999',
            productKindName: 'Premium Account',
            legalEntityIds: ['ENTITY999'],
            productId: 'PROD999',
            productTypeName: 'Premium Banking',
            externalProductId: 'EXT_PROD999',
            externalArrangementId: 'EXT_ARR999',
            product: {
              externalId: 'EXT_PROD999',
              productKindName: 'Premium Account',
              typeName: 'Premium Banking',
            },
            isDefault: 'Y',
            cifNo: 'CIF999',
            name: 'Premium Banking Account',
            bookedBalance: 5000000,
            availableBalance: 4800000,
            currency: 'VND',
            externalTransferAllowed: true,
            urgentTransferAllowed: true,
            accountOpeningDate: '2023-01-01',
            accountHolderNames: 'Premium Customer',
            bankAlias: 'MSB',
          },
        ],
        totalCount: 1,
      };

      const mockResultState: ResultState<SourceAccountListModel> = {
        status: 'SUCCESS',
        data: accountListModel,
      };

      mockRepository.sourceAccountList.mockResolvedValue(accountListModel);
      mockExecutionHandler.execute.mockImplementation(async (fn) => {
        await fn();
        return mockResultState;
      });

      const result = await sourceAccountListUseCase.execute(request);

      expect(mockRepository.sourceAccountList).toHaveBeenCalledWith(request);
      expect(mockExecutionHandler.execute).toHaveBeenCalledWith(expect.any(Function));
      expect(result).toEqual(mockResultState);
    });

    it('should maintain use case interface contract', async () => {
      const mockResultState: ResultState<SourceAccountListModel> = {
        status: 'SUCCESS',
        data: mockSourceAccountListModel,
      };

      mockExecutionHandler.execute.mockResolvedValue(mockResultState);

      const result = await sourceAccountListUseCase.execute();

      // Verify the use case interface contract
      expect(result).toHaveProperty('status');
      expect(['SUCCESS', 'ERROR']).toContain(result.status);
      
      if (result.status === 'SUCCESS') {
        expect(result).toHaveProperty('data');
        expect(result.data).toHaveProperty('accounts');
        expect(result.data).toHaveProperty('totalCount');
        expect(Array.isArray(result.data.accounts)).toBe(true);
        expect(typeof result.data.totalCount).toBe('number');
      } else {
        expect(result).toHaveProperty('error');
        expect(result.error).toBeInstanceOf(CustomError);
      }
    });
  });
});
