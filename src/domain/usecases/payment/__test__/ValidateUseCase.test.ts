import {describe, it, expect, jest, beforeEach} from '@jest/globals';
import {ValidateUseCase} from '../ValidateUseCase';
import {IPaymentRepository} from '../../../repositories/IPaymentRepository';
import {ValidateRequest} from '../../../../data/models/validate/ValidateRequest';
import {ValidateModel} from '../../../entities/validate/ValidateModel';
import {ResultState} from '../../../../core/ResultState';
import {CustomError, ErrorCategory} from '../../../../core/MSBCustomError';
import {BaseResponse} from '../../../../core/BaseResponse';

// Mock dependencies
jest.mock('../../../../utils/ExcecutionHandler', () => ({
  ExecutionHandler: {
    execute: jest.fn(),
  },
}));

import {ExecutionHandler} from '../../../../utils/ExcecutionHandler';

const mockExecutionHandler = ExecutionHandler as jest.Mocked<typeof ExecutionHandler>;

describe('ValidateUseCase', () => {
  let validateUseCase: ValidateUseCase;
  let mockRepository: jest.Mocked<IPaymentRepository>;

  beforeEach(() => {
    jest.clearAllMocks();

    // Create mock repository
    mockRepository = {
      validate: jest.fn(),
    };

    validateUseCase = new ValidateUseCase(mockRepository);
  });

  describe('constructor', () => {
    it('should create ValidateUseCase with repository', () => {
      expect(validateUseCase).toBeInstanceOf(ValidateUseCase);
      expect(validateUseCase['repository']).toBe(mockRepository);
    });
  });

  describe('execute', () => {
    const mockRequest: ValidateRequest = {
      originatorAccount: {
        identification: {
          identification: '**********',
          schemeName: 'ACCOUNT_NUMBER',
        },
      },
      requestedExecutionDate: '2023-12-01T10:00:00Z',
      paymentType: 'BILL_PAYMENT',
      transferTransactionInformation: {
        instructedAmount: {
          amount: '100000',
          currencyCode: 'VND',
        },
        counterparty: {
          name: 'Electric Company',
        },
        counterpartyAccount: {
          identification: {
            identification: 'EVN001',
            schemeName: 'PROVIDER_CODE',
          },
        },
        additions: {
          bpQueryRef: 'QR123',
          bpBillList: '[]',
          bpSummary: '{}',
          bpServiceCode: 'EVN',
          cifNo: 'CIF123',
          bpCategory: 'UTILITY',
        },
      },
    };

    const mockValidateModel = new ValidateModel();
    const mockBaseResponse: BaseResponse<ValidateModel> = mockValidateModel;

    it('should call ExecutionHandler.execute with repository function', async () => {
      const mockResultState: ResultState<ValidateModel> = {
        status: 'SUCCESS',
        data: mockValidateModel,
      };

      mockExecutionHandler.execute.mockResolvedValue(mockResultState);

      await validateUseCase.execute(mockRequest);

      expect(mockExecutionHandler.execute).toHaveBeenCalledWith(
        expect.any(Function)
      );
      expect(mockExecutionHandler.execute).toHaveBeenCalledTimes(1);
    });

    it('should return SUCCESS result state when validation succeeds', async () => {
      const mockResultState: ResultState<ValidateModel> = {
        status: 'SUCCESS',
        data: mockValidateModel,
      };

      mockRepository.validate.mockResolvedValue(mockBaseResponse);
      mockExecutionHandler.execute.mockResolvedValue(mockResultState);

      const result = await validateUseCase.execute(mockRequest);

      expect(result).toEqual(mockResultState);
      expect(result.status).toBe('SUCCESS');
      expect(result.data).toBe(mockValidateModel);
    });

    it('should return ERROR result state when validation fails', async () => {
      const mockError = new CustomError(
        'VALIDATION_ERROR',
        ErrorCategory.VALIDATION,
        'Validation Failed',
        'Invalid account number',
        false
      );

      const mockResultState: ResultState<ValidateModel> = {
        status: 'ERROR',
        error: mockError,
      };

      mockExecutionHandler.execute.mockResolvedValue(mockResultState);

      const result = await validateUseCase.execute(mockRequest);

      expect(result).toEqual(mockResultState);
      expect(result.status).toBe('ERROR');
      if (result.status === 'ERROR') {
        expect(result.error).toBe(mockError);
      }
    });

    it('should pass the correct repository function to ExecutionHandler', async () => {
      const mockResultState: ResultState<ValidateModel> = {
        status: 'SUCCESS',
        data: mockValidateModel,
      };

      mockRepository.validate.mockResolvedValue(mockBaseResponse);
      mockExecutionHandler.execute.mockImplementation(async (fn) => {
        // Call the function to verify it calls repository.validate
        await fn();
        return mockResultState;
      });

      await validateUseCase.execute(mockRequest);

      expect(mockRepository.validate).toHaveBeenCalledWith(mockRequest);
      expect(mockRepository.validate).toHaveBeenCalledTimes(1);
    });

    it('should handle different request types', async () => {
      const requests = [
        {
          ...mockRequest,
          paymentType: 'TRANSFER',
        },
        {
          ...mockRequest,
          transferTransactionInformation: {
            ...mockRequest.transferTransactionInformation,
            instructedAmount: {
              amount: '500000',
              currencyCode: 'USD',
            },
          },
        },
        {
          ...mockRequest,
          originatorAccount: {
            identification: {
              identification: '**********',
              schemeName: 'IBAN',
            },
          },
        },
      ];

      const mockResultState: ResultState<ValidateModel> = {
        status: 'SUCCESS',
        data: mockValidateModel,
      };

      mockExecutionHandler.execute.mockResolvedValue(mockResultState);

      for (const request of requests) {
        await validateUseCase.execute(request);
      }

      expect(mockExecutionHandler.execute).toHaveBeenCalledTimes(3);
    });

    it('should handle validation with complex request structure', async () => {
      const complexRequest: ValidateRequest = {
        originatorAccount: {
          identification: {
            identification: '****************',
            schemeName: 'CARD_NUMBER',
          },
        },
        requestedExecutionDate: '2023-12-15T14:30:00Z',
        paymentType: 'BILL_PAYMENT',
        transferTransactionInformation: {
          instructedAmount: {
            amount: '2500000',
            currencyCode: 'VND',
          },
          counterparty: {
            name: 'Water Supply Company',
          },
          counterpartyAccount: {
            identification: {
              identification: 'WATER001',
              schemeName: 'PROVIDER_CODE',
            },
          },
          additions: {
            bpQueryRef: 'QR456',
            bpBillList: JSON.stringify([
              {billId: 'BILL001', amount: 1000000},
              {billId: 'BILL002', amount: 1500000},
            ]),
            bpSummary: JSON.stringify({
              totalBills: 2,
              totalAmount: 2500000,
            }),
            bpServiceCode: 'WATER',
            cifNo: 'CIF456',
            bpCategory: 'UTILITY',
          },
        },
      };

      const mockResultState: ResultState<ValidateModel> = {
        status: 'SUCCESS',
        data: mockValidateModel,
      };

      mockExecutionHandler.execute.mockResolvedValue(mockResultState);

      const result = await validateUseCase.execute(complexRequest);

      expect(result).toEqual(mockResultState);
      expect(mockExecutionHandler.execute).toHaveBeenCalledWith(expect.any(Function));
    });
  });

  describe('Error handling scenarios', () => {
    const mockRequest: ValidateRequest = {
      originatorAccount: {
        identification: {
          identification: '**********',
          schemeName: 'ACCOUNT_NUMBER',
        },
      },
      requestedExecutionDate: '2023-12-01T10:00:00Z',
      paymentType: 'BILL_PAYMENT',
      transferTransactionInformation: {
        instructedAmount: {
          amount: '100000',
          currencyCode: 'VND',
        },
        counterparty: {
          name: 'Electric Company',
        },
        counterpartyAccount: {
          identification: {
            identification: 'EVN001',
            schemeName: 'PROVIDER_CODE',
          },
        },
        additions: {
          bpQueryRef: 'QR123',
          bpBillList: '[]',
          bpSummary: '{}',
          bpServiceCode: 'EVN',
          cifNo: 'CIF123',
          bpCategory: 'UTILITY',
        },
      },
    };

    it('should handle network errors', async () => {
      const networkError = new CustomError(
        'NETWORK_ERROR',
        ErrorCategory.NETWORK,
        'Network Error',
        'Unable to connect to server',
        true
      );

      const mockResultState: ResultState<ValidateModel> = {
        status: 'ERROR',
        error: networkError,
      };

      mockExecutionHandler.execute.mockResolvedValue(mockResultState);

      const result = await validateUseCase.execute(mockRequest);

      expect(result.status).toBe('ERROR');
      if (result.status === 'ERROR') {
        expect(result.error).toBe(networkError);
        expect(result.error.category).toBe(ErrorCategory.NETWORK);
        expect(result.error.retryable).toBe(true);
      }
    });

    it('should handle validation errors', async () => {
      const validationError = new CustomError(
        'INVALID_ACCOUNT',
        ErrorCategory.VALIDATION,
        'Invalid Account',
        'Account number format is invalid',
        false
      );

      const mockResultState: ResultState<ValidateModel> = {
        status: 'ERROR',
        error: validationError,
      };

      mockExecutionHandler.execute.mockResolvedValue(mockResultState);

      const result = await validateUseCase.execute(mockRequest);

      expect(result.status).toBe('ERROR');
      if (result.status === 'ERROR') {
        expect(result.error).toBe(validationError);
        expect(result.error.category).toBe(ErrorCategory.VALIDATION);
        expect(result.error.retryable).toBe(false);
      }
    });

    it('should handle business logic errors', async () => {
      const businessError = new CustomError(
        'INSUFFICIENT_FUNDS',
        ErrorCategory.BUSINESS,
        'Insufficient Funds',
        'Account does not have sufficient funds',
        false
      );

      const mockResultState: ResultState<ValidateModel> = {
        status: 'ERROR',
        error: businessError,
      };

      mockExecutionHandler.execute.mockResolvedValue(mockResultState);

      const result = await validateUseCase.execute(mockRequest);

      expect(result.status).toBe('ERROR');
      if (result.status === 'ERROR') {
        expect(result.error).toBe(businessError);
        expect(result.error.category).toBe(ErrorCategory.BUSINESS);
      }
    });

    it('should handle API errors', async () => {
      const apiError = new CustomError(
        'API_ERROR',
        ErrorCategory.API,
        'API Error',
        'Internal server error',
        false
      );

      const mockResultState: ResultState<ValidateModel> = {
        status: 'ERROR',
        error: apiError,
      };

      mockExecutionHandler.execute.mockResolvedValue(mockResultState);

      const result = await validateUseCase.execute(mockRequest);

      expect(result.status).toBe('ERROR');
      if (result.status === 'ERROR') {
        expect(result.error).toBe(apiError);
        expect(result.error.category).toBe(ErrorCategory.API);
      }
    });
  });

  describe('Integration scenarios', () => {
    it('should handle complete validation flow', async () => {
      const request: ValidateRequest = {
        originatorAccount: {
          identification: {
            identification: '****************',
            schemeName: 'ACCOUNT_NUMBER',
          },
        },
        requestedExecutionDate: '2023-12-31T23:59:59Z',
        paymentType: 'BILL_PAYMENT',
        transferTransactionInformation: {
          instructedAmount: {
            amount: '1000000',
            currencyCode: 'VND',
          },
          counterparty: {
            name: 'Internet Service Provider',
          },
          counterpartyAccount: {
            identification: {
              identification: 'ISP001',
              schemeName: 'PROVIDER_CODE',
            },
          },
          additions: {
            bpQueryRef: 'QR999',
            bpBillList: JSON.stringify([{billId: 'INTERNET_BILL', amount: 1000000}]),
            bpSummary: JSON.stringify({totalAmount: 1000000}),
            bpServiceCode: 'INTERNET',
            cifNo: 'CIF999',
            bpCategory: 'TELECOM',
          },
        },
      };

      const validateModel = new ValidateModel();
      const mockResultState: ResultState<ValidateModel> = {
        status: 'SUCCESS',
        data: validateModel,
      };

      mockRepository.validate.mockResolvedValue(validateModel);
      mockExecutionHandler.execute.mockImplementation(async (fn) => {
        await fn();
        return mockResultState;
      });

      const result = await validateUseCase.execute(request);

      expect(mockRepository.validate).toHaveBeenCalledWith(request);
      expect(mockExecutionHandler.execute).toHaveBeenCalledWith(expect.any(Function));
      expect(result).toEqual(mockResultState);
    });

    it('should maintain use case interface contract', async () => {
      const request: ValidateRequest = {
        originatorAccount: {
          identification: {
            identification: '**********',
            schemeName: 'ACCOUNT_NUMBER',
          },
        },
        requestedExecutionDate: '2023-12-01T10:00:00Z',
        paymentType: 'BILL_PAYMENT',
        transferTransactionInformation: {
          instructedAmount: {
            amount: '100000',
            currencyCode: 'VND',
          },
          counterparty: {
            name: 'Test Company',
          },
          counterpartyAccount: {
            identification: {
              identification: 'TEST001',
              schemeName: 'PROVIDER_CODE',
            },
          },
          additions: {
            bpQueryRef: 'QR123',
            bpBillList: '[]',
            bpSummary: '{}',
            bpServiceCode: 'TEST',
            cifNo: 'CIF123',
            bpCategory: 'TEST',
          },
        },
      };

      const mockResultState: ResultState<ValidateModel> = {
        status: 'SUCCESS',
        data: new ValidateModel(),
      };

      mockExecutionHandler.execute.mockResolvedValue(mockResultState);

      const result = await validateUseCase.execute(request);

      // Verify the use case interface contract
      expect(result).toHaveProperty('status');
      expect(['SUCCESS', 'ERROR']).toContain(result.status);
      
      if (result.status === 'SUCCESS') {
        expect(result).toHaveProperty('data');
        expect(result.data).toBeInstanceOf(ValidateModel);
      } else {
        expect(result).toHaveProperty('error');
        expect(result.error).toBeInstanceOf(CustomError);
      }
    });
  });
});
