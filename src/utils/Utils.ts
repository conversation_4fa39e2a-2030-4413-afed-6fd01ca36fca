import {hostSharedModule, ToastType} from 'msb-host-shared-module';
// import * as ProviderLogos from '../assets/images/remote/providers-icon';
// import Images from '../assets/images/Images';
import {SafeAny} from '../commons/Constants';
import {translate} from '../locales/i18n';
import {CustomIDomainService} from '../../@types/msb-host-shared-module';

const isEmpty = (value: SafeAny): boolean => {
  if (value === null || value === undefined) {
    return true;
  }
  if (typeof value === 'string' && value.trim() === '') {
    return true;
  }
  if (typeof value === 'number' && isNaN(value)) {
    return true;
  }
  if (Array.isArray(value) && value.length === 0) {
    return true;
  }
  if (typeof value === 'object' && Object.keys(value).length === 0) {
    return true;
  }

  return false;
};

const showToastError = (message: string) => {
  setTimeout(() => {
    hostSharedModule.d.domainService?.showToast({
      message,
      type: ToastType.ERROR, // TODO for toast failed type
    });
  }, 50);
};

const showToastSuccess = (message: string) => {
  setTimeout(() => {
    hostSharedModule.d.domainService?.showToast({
      message,
      type: ToastType.SUCCESS,
    });
  }, 50);
};

const showLoading = () => {
  hostSharedModule?.d?.domainService?.addSpinnerRequest();
};

const hideLoading = () => {
  hostSharedModule?.d?.domainService?.addSpinnerCompleted();
};

function isArrayValid<T>(arr: T[] | null | undefined): boolean {
  return Array.isArray(arr) && arr.length > 0;
}

const removeDiacritics = (str: string) => {
  return str.normalize('NFD').replace(/[\u0300-\u036f]/g, '');
};

const regexTransformToEnglishCharacter = (text: string): string => {
  if (!text) {
    return '';
  }
  return text
    .normalize('NFD')
    .replace(/[\u0300-\u036f]/g, '')
    .replace(/đ/g, 'd')
    .replace(/Đ/g, 'D');
};

const normalizeNumberAndAlphabet = (text: string): string => {
  // Chỉ cho phép số và chữ cái (loại bỏ ký tự đặc biệt)
  return text.replace(/[^a-zA-Z0-9]/g, '');
};

const regexTransferName = (text: string): string => {
  // Chỉ cho phép số và chữ cái, dấu cách (loại bỏ ký tự đặc biệt)
  return text.replace(/[^a-zA-Z\s]/g, '');
};

const removeEmoji = (str: string) => {
  return str.replace(/[\uD83C-\uDBFF\uDC00-\uDFFF]+/gu, '');
};

const regexNickName = (text: string): string => {
  // Chỉ cho phép số và chữ cái có dấu, dấu cách (loại bỏ ký tự đặc biệt)
  return text.replace(/[^a-zA-ZÀ-Ỵà-ỵ\s]/g, '');
};

const regexAccountNumberInput = (text: string): string => {
  return text.replace(/[^0-9a-zA-ZÀ-Ỵà-ỵ]/g, '');
};

// Loại bỏ khoảng trắng dư thừa giữa các từ, giữ lại 1 khoảng trắng
const normalizeSpaces = (text: string): string => {
  return text.replace(/\s+/g, ' ').trim();
};

const regexTransferContent = (text: string): string => {
  // Chỉ cho phép số và chữ cái tiếng anh, tiếng việt (loại bỏ ký tự đặc biệt ngoại trừ -/().+_,)
  return regexTransformToEnglishCharacter(text).replace(/[^0-9a-zA-Z\s().,!+|=]/g, '');
};

// nội dung chuyển tiền mặc định
const transferContent = (fullname: string): string => {
  return `${regexTransferContent(fullname)} ${translate('common.transfer')}`.trim();
};

const showPopup: CustomIDomainService['showPopup'] = data => {
  hostSharedModule?.d?.domainService?.showPopup(data);
};

const undevelopedFeature = () => {
  hostSharedModule.d.domainService?.undevelopedFeature();
};

export default {
  isEmpty,
  removeDiacritics,
  showToastError,
  isArrayValid,
  showToastSuccess,
  hideLoading,
  showLoading,
  normalizeNumberAndAlphabet,
  regexTransferName,
  regexTransformToEnglishCharacter,
  removeEmoji,
  regexNickName,
  regexAccountNumberInput,
  normalizeSpaces,
  regexTransferContent,
  transferContent,
  showPopup,
  undevelopedFeature,
};
