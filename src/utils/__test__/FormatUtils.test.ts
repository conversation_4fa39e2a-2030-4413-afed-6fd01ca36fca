import {describe, it, expect, jest} from '@jest/globals';
import FormatUtils from '../FormatUtils';

// Mock the translate function
jest.mock('../../locales/i18n', () => ({
  translate: jest.fn((key: string) => {
    const translations: Record<string, string> = {
      'utils.formatUtils.done': 'Xong',
      'utils.formatUtils.thousand': 'nghìn',
      'utils.formatUtils.million': 'triệu',
      'utils.formatUtils.billion': 'tỷ',
    };
    return translations[key] || key;
  }),
}));

describe('FormatUtils', () => {
  describe('formatPrice', () => {
    it('should format price with default currency', () => {
      expect(FormatUtils.formatPrice(1000000)).toBe('1,000,000 VND');
      expect(FormatUtils.formatPrice(500000)).toBe('500,000 VND');
      expect(FormatUtils.formatPrice(0)).toBe('0 VND');
    });

    it('should format price with custom currency', () => {
      expect(FormatUtils.formatPrice(1000000, 'USD')).toBe('1,000,000 USD');
      expect(FormatUtils.formatPrice(500000, 'EUR')).toBe('500,000 EUR');
    });

    it('should handle negative numbers', () => {
      expect(FormatUtils.formatPrice(-1000000)).toBe('-1,000,000 VND');
      expect(FormatUtils.formatPrice(-500000, 'USD')).toBe('-500,000 USD');
    });

    it('should handle decimal numbers', () => {
      expect(FormatUtils.formatPrice(1000000.5)).toBe('1,000,000.5 VND');
      expect(FormatUtils.formatPrice(999.99)).toBe('999.99 VND');
    });
  });

  describe('formatMoney', () => {
    it('should format money strings with commas', () => {
      expect(FormatUtils.formatMoney('1000000')).toBe('1,000,000');
      expect(FormatUtils.formatMoney('500000')).toBe('500,000');
      expect(FormatUtils.formatMoney('1234567')).toBe('1,234,567');
    });

    it('should handle empty or invalid strings', () => {
      expect(FormatUtils.formatMoney('')).toBe('');
      expect(FormatUtils.formatMoney('abc')).toBe('abc');
      expect(FormatUtils.formatMoney('0')).toBe('0');
    });

    it('should handle numbers with decimals', () => {
      expect(FormatUtils.formatMoney('1000000.50')).toBe('1,000,000.50');
      expect(FormatUtils.formatMoney('999.99')).toBe('999.99');
    });

    it('should handle negative numbers', () => {
      expect(FormatUtils.formatMoney('-1000000')).toBe('-1,000,000');
      expect(FormatUtils.formatMoney('-500000')).toBe('-500,000');
    });
  });

  describe('formattedNumber', () => {
    it('should format numbers with thousand separators', () => {
      expect(FormatUtils.formattedNumber(1000000)).toBe('1,000,000');
      expect(FormatUtils.formattedNumber(500000)).toBe('500,000');
      expect(FormatUtils.formattedNumber(1234567)).toBe('1,234,567');
    });

    it('should handle zero and small numbers', () => {
      expect(FormatUtils.formattedNumber(0)).toBe('0');
      expect(FormatUtils.formattedNumber(100)).toBe('100');
      expect(FormatUtils.formattedNumber(999)).toBe('999');
    });

    it('should handle negative numbers', () => {
      expect(FormatUtils.formattedNumber(-1000000)).toBe('-1,000,000');
      expect(FormatUtils.formattedNumber(-500000)).toBe('-500,000');
    });

    it('should handle decimal numbers', () => {
      expect(FormatUtils.formattedNumber(1000000.5)).toBe('1,000,000.5');
      expect(FormatUtils.formattedNumber(999.99)).toBe('999.99');
    });
  });

  describe('numberToWordsVi', () => {
    it('should convert numbers to Vietnamese words', () => {
      expect(FormatUtils.numberToWordsVi(0)).toBe('không');
      expect(FormatUtils.numberToWordsVi(1)).toBe('một');
      expect(FormatUtils.numberToWordsVi(10)).toBe('mười');
      expect(FormatUtils.numberToWordsVi(100)).toBe('một trăm');
    });

    it('should handle large numbers', () => {
      expect(FormatUtils.numberToWordsVi(1000)).toBe('một nghìn');
      expect(FormatUtils.numberToWordsVi(1000000)).toBe('một triệu');
      expect(FormatUtils.numberToWordsVi(1000000000)).toBe('một tỷ');
    });

    it('should handle complex numbers', () => {
      expect(FormatUtils.numberToWordsVi(123)).toBe('một trăm hai mười ba');
      expect(FormatUtils.numberToWordsVi(1234)).toBe('một nghìn hai trăm ba mười bốn');
    });

    it('should handle negative numbers', () => {
      expect(FormatUtils.numberToWordsVi(-100)).toBe('âm một trăm');
      expect(FormatUtils.numberToWordsVi(-1000)).toBe('âm một nghìn');
    });
  });

  describe('splitTransactions', () => {
    it('should split transaction arrays correctly', () => {
      const transactions = [
        {id: 1, date: '2023-01-01'},
        {id: 2, date: '2023-01-02'},
        {id: 3, date: '2023-01-03'},
        {id: 4, date: '2023-01-04'},
        {id: 5, date: '2023-01-05'},
      ];

      const result = FormatUtils.splitTransactions(transactions, 2);
      expect(result).toHaveLength(3);
      expect(result[0]).toHaveLength(2);
      expect(result[1]).toHaveLength(2);
      expect(result[2]).toHaveLength(1);
    });

    it('should handle empty arrays', () => {
      const result = FormatUtils.splitTransactions([], 3);
      expect(result).toEqual([]);
    });

    it('should handle arrays smaller than chunk size', () => {
      const transactions = [{id: 1}, {id: 2}];
      const result = FormatUtils.splitTransactions(transactions, 5);
      expect(result).toHaveLength(1);
      expect(result[0]).toHaveLength(2);
    });
  });

  describe('formatDateDDMMYYYY', () => {
    it('should format dates correctly', () => {
      const date = new Date('2023-12-25T10:30:00');
      expect(FormatUtils.formatDateDDMMYYYY(date)).toBe('25/12/2023');
    });

    it('should handle different date formats', () => {
      const date1 = new Date('2023-01-01T00:00:00');
      const date2 = new Date('2023-12-31T23:59:59');
      
      expect(FormatUtils.formatDateDDMMYYYY(date1)).toBe('01/01/2023');
      expect(FormatUtils.formatDateDDMMYYYY(date2)).toBe('31/12/2023');
    });

    it('should pad single digits with zeros', () => {
      const date = new Date('2023-01-05T10:30:00');
      expect(FormatUtils.formatDateDDMMYYYY(date)).toBe('05/01/2023');
    });
  });

  describe('formatDateHHMM', () => {
    it('should format time correctly', () => {
      const date = new Date('2023-12-25T10:30:00');
      expect(FormatUtils.formatDateHHMM(date)).toBe('10:30');
    });

    it('should handle different times', () => {
      const date1 = new Date('2023-01-01T00:00:00');
      const date2 = new Date('2023-12-31T23:59:59');
      
      expect(FormatUtils.formatDateHHMM(date1)).toBe('00:00');
      expect(FormatUtils.formatDateHHMM(date2)).toBe('23:59');
    });

    it('should pad single digits with zeros', () => {
      const date = new Date('2023-01-01T09:05:00');
      expect(FormatUtils.formatDateHHMM(date)).toBe('09:05');
    });
  });

  describe('formatRemittanceInformation', () => {
    it('should format remittance information correctly', () => {
      const result = FormatUtils.formatRemittanceInformation('John Doe', 'Payment for services');
      expect(result).toContain('John Doe');
      expect(result).toContain('Payment for services');
    });

    it('should handle empty parameters', () => {
      const result1 = FormatUtils.formatRemittanceInformation('', 'Payment');
      const result2 = FormatUtils.formatRemittanceInformation('John', '');
      const result3 = FormatUtils.formatRemittanceInformation('', '');
      
      expect(typeof result1).toBe('string');
      expect(typeof result2).toBe('string');
      expect(typeof result3).toBe('string');
    });
  });

  describe('generateAmountSuggestions', () => {
    it('should generate amount suggestions correctly', () => {
      const suggestions = FormatUtils.generateAmountSuggestions('100');
      
      expect(Array.isArray(suggestions)).toBe(true);
      expect(suggestions.length).toBeGreaterThan(0);
      expect(suggestions).toContain('Xong'); // Should contain the "done" option
    });

    it('should handle different amount formats', () => {
      const suggestions1 = FormatUtils.generateAmountSuggestions('1000');
      const suggestions2 = FormatUtils.generateAmountSuggestions('50000');
      
      expect(Array.isArray(suggestions1)).toBe(true);
      expect(Array.isArray(suggestions2)).toBe(true);
      expect(suggestions1.length).toBeGreaterThan(0);
      expect(suggestions2.length).toBeGreaterThan(0);
    });

    it('should handle empty or invalid input', () => {
      const suggestions1 = FormatUtils.generateAmountSuggestions('');
      const suggestions2 = FormatUtils.generateAmountSuggestions('abc');
      
      expect(Array.isArray(suggestions1)).toBe(true);
      expect(Array.isArray(suggestions2)).toBe(true);
    });
  });

  describe('removeVietnameseTones', () => {
    it('should remove Vietnamese tones correctly', () => {
      expect(FormatUtils.removeVietnameseTones('Nguyễn Văn Anh')).toBe('Nguyen Van Anh');
      expect(FormatUtils.removeVietnameseTones('Trần Thị Bình')).toBe('Tran Thi Binh');
      expect(FormatUtils.removeVietnameseTones('Lê Hoàng Đức')).toBe('Le Hoang Duc');
    });

    it('should handle strings without Vietnamese characters', () => {
      expect(FormatUtils.removeVietnameseTones('John Doe')).toBe('John Doe');
      expect(FormatUtils.removeVietnameseTones('123456')).toBe('123456');
      expect(FormatUtils.removeVietnameseTones('Test String')).toBe('Test String');
    });

    it('should handle empty strings', () => {
      expect(FormatUtils.removeVietnameseTones('')).toBe('');
    });

    it('should handle mixed content', () => {
      expect(FormatUtils.removeVietnameseTones('Nguyễn 123 Văn')).toBe('Nguyen 123 Van');
      expect(FormatUtils.removeVietnameseTones('Email: <EMAIL> Tên: Hồng')).toBe('Email: <EMAIL> Ten: Hong');
    });
  });

  describe('removeSpecialCharsAndEmoji', () => {
    it('should remove special characters and emojis', () => {
      expect(FormatUtils.removeSpecialCharsAndEmoji('Hello! 😀 World')).toBe('Hello World');
      expect(FormatUtils.removeSpecialCharsAndEmoji('Test@#$%^&*()')).toBe('Test');
      expect(FormatUtils.removeSpecialCharsAndEmoji('Nguyễn Văn A 🎉')).toBe('Nguyễn Văn A');
    });

    it('should handle strings without special characters', () => {
      expect(FormatUtils.removeSpecialCharsAndEmoji('Normal Text')).toBe('Normal Text');
      expect(FormatUtils.removeSpecialCharsAndEmoji('123456')).toBe('123456');
    });

    it('should handle empty strings', () => {
      expect(FormatUtils.removeSpecialCharsAndEmoji('')).toBe('');
    });

    it('should preserve Vietnamese characters', () => {
      expect(FormatUtils.removeSpecialCharsAndEmoji('Nguyễn Văn Anh')).toBe('Nguyễn Văn Anh');
      expect(FormatUtils.removeSpecialCharsAndEmoji('Trần Thị Bình')).toBe('Trần Thị Bình');
    });
  });
});
