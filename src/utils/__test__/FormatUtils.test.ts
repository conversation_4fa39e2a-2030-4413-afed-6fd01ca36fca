import {describe, it, expect, jest} from '@jest/globals';
import FormatUtils from '../FormatUtils';

// Mock the translate function
jest.mock('../../locales/i18n', () => ({
  translate: jest.fn((key: string) => {
    const translations: Record<string, string> = {
      'utils.formatUtils.done': 'Xong',
      'utils.formatUtils.noAmount': 'không',
      'utils.formatUtils.currencyVnd': 'đồng',
      'utils.formatUtils.currencyUsd': 'đô la',
      'utils.formatUtils.amountSuggestion1': '10,000',
      'utils.formatUtils.amountSuggestion2': '100,000',
      'utils.formatUtils.amountSuggestion3': '10,000,000',
    };
    return translations[key] || 'undefined';
  }),
}));

describe('FormatUtils', () => {
  describe('formatPrice', () => {
    it('should format price correctly', () => {
      expect(FormatUtils.formatPrice(1000000)).toBe('1,000,000');
      expect(FormatUtils.formatPrice(500000)).toBe('500,000');
      expect(FormatUtils.formatPrice(0)).toBe('0');
    });

    it('should handle null and undefined', () => {
      expect(FormatUtils.formatPrice(null)).toBe('0');
      expect(FormatUtils.formatPrice(undefined)).toBe('0');
    });

    it('should handle negative numbers', () => {
      expect(FormatUtils.formatPrice(-1000000)).toBe('-1,000,000');
      expect(FormatUtils.formatPrice(-500000)).toBe('-500,000');
    });

    it('should handle decimal numbers', () => {
      expect(FormatUtils.formatPrice(1000000.5)).toBe('1,000,000,5');
      expect(FormatUtils.formatPrice(999.99)).toBe('999,99');
    });

    it('should handle string inputs', () => {
      expect(FormatUtils.formatPrice('1000000')).toBe('1,000,000');
      expect(FormatUtils.formatPrice('abc')).toBe('0');
    });
  });

  describe('formatMoney', () => {
    it('should format money strings with commas', () => {
      expect(FormatUtils.formatMoney('1000000')).toBe('1,000,000');
      expect(FormatUtils.formatMoney('500000')).toBe('500,000');
      expect(FormatUtils.formatMoney('1234567')).toBe('1,234,567');
    });

    it('should handle empty or invalid strings', () => {
      expect(FormatUtils.formatMoney('')).toBe('0');
      expect(FormatUtils.formatMoney('abc')).toBe('0');
      expect(FormatUtils.formatMoney('0')).toBe('0');
    });

    it('should handle numbers with decimals', () => {
      expect(FormatUtils.formatMoney('1000000.50')).toBe('1,000,000.50');
      expect(FormatUtils.formatMoney('999.99')).toBe('999.99');
    });

    it('should handle negative numbers', () => {
      expect(FormatUtils.formatMoney('-1000000')).toBe('1,000,000');
      expect(FormatUtils.formatMoney('-500000')).toBe('500,000');
    });
  });

  describe('formattedNumber', () => {
    it('should convert formatted string to number', () => {
      expect(FormatUtils.formattedNumber('1,000,000')).toBe(1000000);
      expect(FormatUtils.formattedNumber('500,000')).toBe(500000);
      expect(FormatUtils.formattedNumber('1,234,567')).toBe(1234567);
    });

    it('should handle zero and small numbers', () => {
      expect(FormatUtils.formattedNumber('0')).toBe(0);
      expect(FormatUtils.formattedNumber('100')).toBe(100);
      expect(FormatUtils.formattedNumber('999')).toBe(999);
    });

    it('should handle negative numbers', () => {
      expect(FormatUtils.formattedNumber('-1,000,000')).toBe(-1000000);
      expect(FormatUtils.formattedNumber('-500,000')).toBe(-500000);
    });

    it('should handle decimal numbers', () => {
      expect(FormatUtils.formattedNumber('1,000,000.5')).toBe(1000000.5);
      expect(FormatUtils.formattedNumber('999.99')).toBe(999.99);
    });
  });

  describe('numberToWordsVi', () => {
    it('should convert numbers to Vietnamese words', () => {
      expect(FormatUtils.numberToWordsVi(0)).toBe('không');
      expect(FormatUtils.numberToWordsVi(1)).toBe('Một undefined');
      expect(FormatUtils.numberToWordsVi(10)).toBe('Mười undefined');
      expect(FormatUtils.numberToWordsVi(100)).toBe('Một trăm undefined');
    });

    it('should handle large numbers', () => {
      expect(FormatUtils.numberToWordsVi(1000)).toBe('Một nghìn undefined');
      expect(FormatUtils.numberToWordsVi(1000000)).toBe('Một triệu undefined');
      expect(FormatUtils.numberToWordsVi(1000000000)).toBe('Một tỷ undefined');
    });

    it('should handle complex numbers', () => {
      expect(FormatUtils.numberToWordsVi(123)).toBe('Một trăm hai mươi ba undefined');
      expect(FormatUtils.numberToWordsVi(1234)).toBe('Một nghìn hai trăm ba mươi bốn undefined');
    });

    it('should handle currency parameter', () => {
      expect(FormatUtils.numberToWordsVi(100, 'USD')).toBe('Một trăm undefined');
      expect(FormatUtils.numberToWordsVi(1000, 'VND')).toBe('Một nghìn undefined');
    });
  });

  describe('splitTransactions', () => {
    it('should split transaction amounts correctly', () => {
      const result = FormatUtils.splitTransactions(10000000, 5000000, 2000000);

      expect(result).toHaveProperty('totalTransaction');
      expect(result).toHaveProperty('transactions');
      expect(result).toHaveProperty('transactionItems');
      expect(Array.isArray(result.transactions)).toBe(true);
      expect(Array.isArray(result.transactionItems)).toBe(true);
      expect(result.totalTransaction).toBe(5);
      expect(result.transactions).toEqual([2000000, 2000000, 2000000, 2000000, 2000000]);
    });

    it('should handle zero amount', () => {
      const result = FormatUtils.splitTransactions(0, 5000000, 2000000);
      expect(result.totalTransaction).toBe(0);
      expect(result.transactions).toEqual([]);
      expect(result.transactionItems).toEqual([]);
    });

    it('should handle small amounts', () => {
      const result = FormatUtils.splitTransactions(1000000, 5000000, 2000000);
      expect(result.totalTransaction).toBe(1);
      expect(result.transactions).toEqual([3000000]);
    });
  });

  describe('formatDateDDMMYYYY', () => {
    it('should format dates correctly', () => {
      const isoString = '2023-12-25T10:30:00';
      expect(FormatUtils.formatDateDDMMYYYY(isoString)).toBe('25/12/2023');
    });

    it('should handle different date formats', () => {
      const isoString1 = '2023-01-01T00:00:00';
      const isoString2 = '2023-12-31T23:59:59';

      expect(FormatUtils.formatDateDDMMYYYY(isoString1)).toBe('01/01/2023');
      expect(FormatUtils.formatDateDDMMYYYY(isoString2)).toBe('31/12/2023');
    });

    it('should pad single digits with zeros', () => {
      const isoString = '2023-01-05T10:30:00';
      expect(FormatUtils.formatDateDDMMYYYY(isoString)).toBe('05/01/2023');
    });
  });

  describe('formatDateHHMM', () => {
    it('should format time correctly', () => {
      const isoString = '2023-12-25T10:30:00';
      expect(FormatUtils.formatDateHHMM(isoString)).toBe('10:30');
    });

    it('should handle different times', () => {
      const isoString1 = '2023-01-01T00:00:00';
      const isoString2 = '2023-12-31T23:59:59';

      expect(FormatUtils.formatDateHHMM(isoString1)).toBe('00:00');
      expect(FormatUtils.formatDateHHMM(isoString2)).toBe('23:59');
    });

    it('should pad single digits with zeros', () => {
      const isoString = '2023-01-01T09:05:00';
      expect(FormatUtils.formatDateHHMM(isoString)).toBe('09:05');
    });
  });

  describe('formatRemittanceInformation', () => {
    it('should format remittance information correctly', () => {
      const result = FormatUtils.formatRemittanceInformation('John Doe');
      expect(result).toBe('John Doe');
    });

    it('should remove special characters', () => {
      const result = FormatUtils.formatRemittanceInformation('John@#$%Doe!');
      expect(result).toBe('JohnDoe');
    });

    it('should handle empty parameters', () => {
      const result1 = FormatUtils.formatRemittanceInformation('');
      const result2 = FormatUtils.formatRemittanceInformation(null);
      const result3 = FormatUtils.formatRemittanceInformation(undefined);

      expect(result1).toBe('');
      expect(result2).toBe('');
      expect(result3).toBe('');
    });

    it('should preserve allowed characters', () => {
      const result = FormatUtils.formatRemittanceInformation('John-Doe/Test(123).+_,');
      expect(result).toBe('John-Doe/Test(123).+_,');
    });
  });

  describe('generateAmountSuggestions', () => {
    it('should generate amount suggestions correctly', () => {
      const suggestions = FormatUtils.generateAmountSuggestions('1');

      expect(Array.isArray(suggestions)).toBe(true);
      expect(suggestions.length).toBe(4);
      expect(suggestions).toContain(undefined); // Should contain the "done" option
      expect(suggestions[0]).toBe('10,000');
      expect(suggestions[1]).toBe('100,000');
      expect(suggestions[2]).toBe('1,000,000');
    });

    it('should handle different amount formats', () => {
      const suggestions1 = FormatUtils.generateAmountSuggestions('10');
      const suggestions2 = FormatUtils.generateAmountSuggestions('100');

      expect(Array.isArray(suggestions1)).toBe(true);
      expect(Array.isArray(suggestions2)).toBe(true);
      expect(suggestions1.length).toBe(4);
      expect(suggestions2.length).toBe(4);
      expect(suggestions1).toContain(undefined);
      expect(suggestions2).toContain(undefined);
      expect(suggestions1[0]).toBe('10,000');
      expect(suggestions1[1]).toBe('100,000');
      expect(suggestions1[2]).toBe('1,000,000');
    });

    it('should handle empty or invalid input', () => {
      const suggestions1 = FormatUtils.generateAmountSuggestions('');
      const suggestions2 = FormatUtils.generateAmountSuggestions('abc');

      expect(Array.isArray(suggestions1)).toBe(true);
      expect(Array.isArray(suggestions2)).toBe(true);
      expect(suggestions1).toEqual([undefined, undefined, undefined, undefined]);
      expect(suggestions2).toEqual([undefined, undefined, undefined, undefined]);
    });
  });

  describe('removeVietnameseTones', () => {
    it('should remove Vietnamese tones correctly', () => {
      expect(FormatUtils.removeVietnameseTones('Nguyễn Văn Anh')).toBe('Nguyen Van Anh');
      expect(FormatUtils.removeVietnameseTones('Trần Thị Bình')).toBe('Tran Thi Binh');
      expect(FormatUtils.removeVietnameseTones('Lê Hoàng Đức')).toBe('Le Hoang Duc');
    });

    it('should handle strings without Vietnamese characters', () => {
      expect(FormatUtils.removeVietnameseTones('John Doe')).toBe('John Doe');
      expect(FormatUtils.removeVietnameseTones('123456')).toBe('123456');
      expect(FormatUtils.removeVietnameseTones('Test String')).toBe('Test String');
    });

    it('should handle empty strings', () => {
      expect(FormatUtils.removeVietnameseTones('')).toBe('');
    });

    it('should handle mixed content', () => {
      expect(FormatUtils.removeVietnameseTones('Nguyễn 123 Văn')).toBe('Nguyen 123 Van');
      expect(FormatUtils.removeVietnameseTones('Email: <EMAIL> Tên: Hồng')).toBe(
        'Email: <EMAIL> Ten: Hong',
      );
    });
  });

  describe('removeSpecialCharsAndEmoji', () => {
    it('should remove special characters and emojis', () => {
      expect(FormatUtils.removeSpecialCharsAndEmoji('Hello! 😀 World')).toBe('Hello  World');
      expect(FormatUtils.removeSpecialCharsAndEmoji('Test@#$%^&*()')).toBe('Test');
      expect(FormatUtils.removeSpecialCharsAndEmoji('Nguyễn Văn A 🎉')).toBe('Nguyễn Văn A ');
    });

    it('should handle strings without special characters', () => {
      expect(FormatUtils.removeSpecialCharsAndEmoji('Normal Text')).toBe('Normal Text');
      expect(FormatUtils.removeSpecialCharsAndEmoji('123456')).toBe('');
    });

    it('should handle empty strings', () => {
      expect(FormatUtils.removeSpecialCharsAndEmoji('')).toBe('');
    });

    it('should preserve Vietnamese characters', () => {
      expect(FormatUtils.removeSpecialCharsAndEmoji('Nguyễn Văn Anh')).toBe('Nguyễn Văn Anh');
      expect(FormatUtils.removeSpecialCharsAndEmoji('Trần Thị Bình')).toBe('Trần Thị Bình');
    });
  });
});
