import {describe, it, expect} from '@jest/globals';

// Example utility functions for payment
export const formatCurrency = (amount: number, currency: string = 'VND'): string => {
  return new Intl.NumberFormat('vi-VN', {
    style: 'currency',
    currency: currency,
  }).format(amount);
};

export const validatePaymentAmount = (amount: number): boolean => {
  return amount > 0 && amount <= 1000000000; // Max 1 billion VND
};

export const generateTransactionId = (): string => {
  return `TXN_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
};

export const maskCardNumber = (cardNumber: string): string => {
  if (cardNumber.length < 4) return cardNumber;
  const lastFour = cardNumber.slice(-4);
  const masked = '*'.repeat(cardNumber.length - 4);
  return masked + lastFour;
};

// Tests
describe('PaymentUtils', () => {
  describe('formatCurrency', () => {
    it('should format VND currency correctly', () => {
      const result = formatCurrency(100000);
      expect(result).toContain('100.000');
      expect(result).toContain('₫');
    });

    it('should format USD currency correctly', () => {
      const result = formatCurrency(100, 'USD');
      expect(result).toContain('100');
      expect(result).toContain('$');
    });

    it('should handle zero amount', () => {
      const result = formatCurrency(0);
      expect(result).toContain('0');
    });
  });

  describe('validatePaymentAmount', () => {
    it('should return true for valid amounts', () => {
      expect(validatePaymentAmount(1000)).toBe(true);
      expect(validatePaymentAmount(500000)).toBe(true);
      expect(validatePaymentAmount(1000000)).toBe(true);
    });

    it('should return false for invalid amounts', () => {
      expect(validatePaymentAmount(0)).toBe(false);
      expect(validatePaymentAmount(-100)).toBe(false);
      expect(validatePaymentAmount(1000000001)).toBe(false);
    });
  });

  describe('generateTransactionId', () => {
    it('should generate unique transaction IDs', () => {
      const id1 = generateTransactionId();
      const id2 = generateTransactionId();
      
      expect(id1).toMatch(/^TXN_\d+_[a-z0-9]+$/);
      expect(id2).toMatch(/^TXN_\d+_[a-z0-9]+$/);
      expect(id1).not.toBe(id2);
    });

    it('should start with TXN_ prefix', () => {
      const id = generateTransactionId();
      expect(id).toMatch(/^TXN_/);
    });
  });

  describe('maskCardNumber', () => {
    it('should mask card number correctly', () => {
      const cardNumber = '1234567890123456';
      const masked = maskCardNumber(cardNumber);
      expect(masked).toBe('************3456');
    });

    it('should handle short card numbers', () => {
      const cardNumber = '123';
      const masked = maskCardNumber(cardNumber);
      expect(masked).toBe('123');
    });

    it('should handle exactly 4 digit card numbers', () => {
      const cardNumber = '1234';
      const masked = maskCardNumber(cardNumber);
      expect(masked).toBe('1234');
    });
  });
});
