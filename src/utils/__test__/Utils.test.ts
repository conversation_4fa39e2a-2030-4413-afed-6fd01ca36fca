import {describe, it, expect, jest, beforeEach} from '@jest/globals';
import Utils from '../Utils';
import {hostSharedModule} from 'msb-host-shared-module';
import * as i18n from '../../locales/i18n';

// Mock timers
jest.useFakeTimers();

// Mock dependencies
jest.mock('msb-host-shared-module', () => ({
  hostSharedModule: {
    d: {
      domainService: {
        showToast: jest.fn(),
        addSpinnerRequest: jest.fn(),
        addSpinnerCompleted: jest.fn(),
        showPopup: jest.fn(),
      },
    },
  },
  ToastType: {
    ERROR: 'ERROR',
    SUCCESS: 'SUCCESS',
  },
}));

jest.mock('../../locales/i18n', () => ({
  translate: jest.fn((key: string) => {
    const translations: Record<string, string> = {
      'common.transfer': 'chuyen tien',
      'error.network': 'Lỗi mạng',
      'success.operation': 'Thành công',
    };
    return translations[key] || key;
  }),
}));

describe('Utils', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    jest.clearAllTimers();

    // Mock translate function
    jest.spyOn(i18n, 'translate').mockImplementation((key: string) => {
      const translations: Record<string, string> = {
        'common.transfer': 'chuyen tien',
        'error.network': 'Lỗi mạng',
        'success.operation': 'Thành công',
      };
      return translations[key] || key;
    });
  });

  describe('isEmpty', () => {
    it('should return true for null and undefined', () => {
      expect(Utils.isEmpty(null)).toBe(true);
      expect(Utils.isEmpty(undefined)).toBe(true);
    });

    it('should return true for empty strings', () => {
      expect(Utils.isEmpty('')).toBe(true);
      expect(Utils.isEmpty('   ')).toBe(true);
      expect(Utils.isEmpty('\t\n')).toBe(true);
    });

    it('should return true for NaN numbers', () => {
      expect(Utils.isEmpty(NaN)).toBe(true);
    });

    it('should return true for empty arrays', () => {
      expect(Utils.isEmpty([])).toBe(true);
    });

    it('should return true for empty objects', () => {
      expect(Utils.isEmpty({})).toBe(true);
    });

    it('should return false for valid values', () => {
      expect(Utils.isEmpty('hello')).toBe(false);
      expect(Utils.isEmpty(0)).toBe(false);
      expect(Utils.isEmpty(false)).toBe(false);
      expect(Utils.isEmpty([1, 2, 3])).toBe(false);
      expect(Utils.isEmpty({key: 'value'})).toBe(false);
    });

    it('should return false for whitespace-containing strings with content', () => {
      expect(Utils.isEmpty(' hello ')).toBe(false);
      expect(Utils.isEmpty('hello world')).toBe(false);
    });
  });

  describe('removeDiacritics', () => {
    it('should remove Vietnamese diacritics', () => {
      expect(Utils.removeDiacritics('Nguyễn Văn Anh')).toBe('Nguyen Van Anh');
      expect(Utils.removeDiacritics('Trần Thị Bình')).toBe('Tran Thi Binh');
      expect(Utils.removeDiacritics('Lê Hoàng Đức')).toBe('Le Hoang Đuc'); // Note: removeDiacritics doesn't handle đ->d conversion
    });

    it('should handle strings without diacritics', () => {
      expect(Utils.removeDiacritics('John Doe')).toBe('John Doe');
      expect(Utils.removeDiacritics('123456')).toBe('123456');
      expect(Utils.removeDiacritics('Test String')).toBe('Test String');
    });

    it('should handle empty strings', () => {
      expect(Utils.removeDiacritics('')).toBe('');
    });

    it('should handle special characters', () => {
      expect(Utils.removeDiacritics('Nguyễ***********')).toBe('<EMAIL>');
      expect(Utils.removeDiacritics('Hồ Chí Minh - 2023')).toBe('Ho Chi Minh - 2023');
    });
  });

  describe('showToastError', () => {
    it('should call hostSharedModule showToast with error type', () => {
      const errorMessage = 'Test error message';
      Utils.showToastError(errorMessage);

      // Fast-forward time to trigger setTimeout
      jest.advanceTimersByTime(100);

      expect(hostSharedModule.d.domainService.showToast).toHaveBeenCalledWith({
        message: errorMessage,
        type: 'ERROR',
      });
    });

    it('should handle empty error messages', () => {
      Utils.showToastError('');

      // Fast-forward time to trigger setTimeout
      jest.advanceTimersByTime(100);

      expect(hostSharedModule.d.domainService.showToast).toHaveBeenCalledWith({
        message: '',
        type: 'ERROR',
      });
    });
  });

  describe('showToastSuccess', () => {
    it('should call hostSharedModule showToast with success type', () => {
      const successMessage = 'Operation successful';
      Utils.showToastSuccess(successMessage);

      // Fast-forward time to trigger setTimeout
      jest.advanceTimersByTime(100);

      expect(hostSharedModule.d.domainService.showToast).toHaveBeenCalledWith({
        message: successMessage,
        type: 'SUCCESS',
      });
    });

    it('should handle empty success messages', () => {
      Utils.showToastSuccess('');

      // Fast-forward time to trigger setTimeout
      jest.advanceTimersByTime(100);

      expect(hostSharedModule.d.domainService.showToast).toHaveBeenCalledWith({
        message: '',
        type: 'SUCCESS',
      });
    });
  });

  describe('showLoading', () => {
    it('should call hostSharedModule addSpinnerRequest', () => {
      Utils.showLoading();

      expect(hostSharedModule.d.domainService.addSpinnerRequest).toHaveBeenCalled();
    });
  });

  describe('hideLoading', () => {
    it('should call hostSharedModule addSpinnerCompleted', () => {
      Utils.hideLoading();

      expect(hostSharedModule.d.domainService.addSpinnerCompleted).toHaveBeenCalled();
    });
  });

  describe('isArrayValid', () => {
    it('should return true for valid arrays', () => {
      expect(Utils.isArrayValid([1, 2, 3])).toBe(true);
      expect(Utils.isArrayValid(['a', 'b', 'c'])).toBe(true);
      expect(Utils.isArrayValid([{id: 1}, {id: 2}])).toBe(true);
    });

    it('should return false for empty arrays', () => {
      expect(Utils.isArrayValid([])).toBe(false);
    });

    it('should return false for null and undefined', () => {
      expect(Utils.isArrayValid(null)).toBe(false);
      expect(Utils.isArrayValid(undefined)).toBe(false);
    });

    it('should return false for non-arrays', () => {
      expect(Utils.isArrayValid('string' as any)).toBe(false);
      expect(Utils.isArrayValid(123 as any)).toBe(false);
      expect(Utils.isArrayValid({} as any)).toBe(false);
    });
  });

  describe('normalizeNumberAndAlphabet', () => {
    it('should normalize Vietnamese characters to English', () => {
      expect(Utils.normalizeNumberAndAlphabet('Nguyễn123')).toBe('Nguyn123'); // Removes special chars including Vietnamese diacritics
      expect(Utils.normalizeNumberAndAlphabet('Trần456')).toBe('Trn456');
    });

    it('should preserve numbers and English characters', () => {
      expect(Utils.normalizeNumberAndAlphabet('John123')).toBe('John123');
      expect(Utils.normalizeNumberAndAlphabet('Test456')).toBe('Test456');
    });

    it('should handle empty strings', () => {
      expect(Utils.normalizeNumberAndAlphabet('')).toBe('');
    });

    it('should handle special characters', () => {
      expect(Utils.normalizeNumberAndAlphabet('Nguyễn@123')).toBe('Nguyn123'); // Removes @ and Vietnamese diacritics
    });
  });

  describe('regexTransferName', () => {
    it('should format transfer names correctly', () => {
      const result = Utils.regexTransferName('Nguyễn Văn Anh');
      expect(typeof result).toBe('string');
      expect(result.length).toBeGreaterThan(0);
    });

    it('should handle names with special characters', () => {
      const result = Utils.regexTransferName('Nguyễn@Văn#Anh');
      expect(typeof result).toBe('string');
    });

    it('should handle empty names', () => {
      const result = Utils.regexTransferName('');
      expect(typeof result).toBe('string');
    });
  });

  describe('regexTransformToEnglishCharacter', () => {
    it('should transform Vietnamese to English characters', () => {
      const result = Utils.regexTransformToEnglishCharacter('Nguyễn Văn Anh');
      expect(result).toBe('Nguyen Van Anh');
    });

    it('should handle strings without Vietnamese characters', () => {
      const result = Utils.regexTransformToEnglishCharacter('John Doe');
      expect(result).toBe('John Doe');
    });

    it('should handle empty strings', () => {
      const result = Utils.regexTransformToEnglishCharacter('');
      expect(result).toBe('');
    });
  });

  describe('removeEmoji', () => {
    it('should remove emojis from strings', () => {
      expect(Utils.removeEmoji('Hello 😀 World 🎉')).toBe('Hello  World ');
      expect(Utils.removeEmoji('Test 🚀 Message 💯')).toBe('Test  Message ');
    });

    it('should handle strings without emojis', () => {
      expect(Utils.removeEmoji('Hello World')).toBe('Hello World');
      expect(Utils.removeEmoji('123456')).toBe('123456');
    });

    it('should handle empty strings', () => {
      expect(Utils.removeEmoji('')).toBe('');
    });

    it('should preserve Vietnamese characters', () => {
      expect(Utils.removeEmoji('Nguyễn Văn Anh 😀')).toBe('Nguyễn Văn Anh ');
    });
  });

  describe('regexNickName', () => {
    it('should format nicknames correctly', () => {
      const result = Utils.regexNickName('Nguyễn Văn Anh');
      expect(typeof result).toBe('string');
      expect(result.length).toBeGreaterThan(0);
    });

    it('should handle special characters in nicknames', () => {
      const result = Utils.regexNickName('Nick@Name123');
      expect(typeof result).toBe('string');
    });

    it('should handle empty nicknames', () => {
      const result = Utils.regexNickName('');
      expect(typeof result).toBe('string');
    });
  });

  describe('regexAccountNumberInput', () => {
    it('should format account numbers correctly', () => {
      const result = Utils.regexAccountNumberInput('**********');
      expect(typeof result).toBe('string');
      expect(result.length).toBeGreaterThan(0);
    });

    it('should handle account numbers with special characters', () => {
      const result = Utils.regexAccountNumberInput('123-456-789');
      expect(typeof result).toBe('string');
    });

    it('should handle empty account numbers', () => {
      const result = Utils.regexAccountNumberInput('');
      expect(typeof result).toBe('string');
    });
  });

  describe('normalizeSpaces', () => {
    it('should normalize multiple spaces to single spaces', () => {
      expect(Utils.normalizeSpaces('Hello    World')).toBe('Hello World');
      expect(Utils.normalizeSpaces('Test   Multiple   Spaces')).toBe('Test Multiple Spaces');
    });

    it('should trim leading and trailing spaces', () => {
      expect(Utils.normalizeSpaces('  Hello World  ')).toBe('Hello World');
      expect(Utils.normalizeSpaces('\t\nHello World\t\n')).toBe('Hello World');
    });

    it('should handle strings with normal spacing', () => {
      expect(Utils.normalizeSpaces('Hello World')).toBe('Hello World');
      expect(Utils.normalizeSpaces('Normal Text')).toBe('Normal Text');
    });

    it('should handle empty strings', () => {
      expect(Utils.normalizeSpaces('')).toBe('');
      expect(Utils.normalizeSpaces('   ')).toBe('');
    });
  });

  describe('regexTransferContent', () => {
    it('should format transfer content correctly', () => {
      const result = Utils.regexTransferContent('Nguyễn Văn Anh');
      expect(typeof result).toBe('string');
      expect(result.length).toBeGreaterThan(0);
    });

    it('should handle names with special characters', () => {
      const result = Utils.regexTransferContent('Nguyễn@Văn#Anh');
      expect(typeof result).toBe('string');
    });

    it('should handle empty content', () => {
      const result = Utils.regexTransferContent('');
      expect(typeof result).toBe('string');
    });
  });

  describe('transferContent', () => {
    it('should generate transfer content with name and translation', () => {
      const result = Utils.transferContent('Nguyễn Văn Anh');
      expect(typeof result).toBe('string');
      expect(result).toBe('Nguyen Van Anh chuyen tien'); // Expected exact result
    });

    it('should handle empty names', () => {
      const result = Utils.transferContent('');
      expect(typeof result).toBe('string');
      expect(result).toBe('chuyen tien'); // Expected exact result for empty name
    });
  });

  describe('showPopup', () => {
    it('should call hostSharedModule showPopup with data', () => {
      const popupData = {
        title: 'Test Title',
        message: 'Test Message',
        actions: [],
      };

      Utils.showPopup(popupData);

      expect(hostSharedModule.d.domainService.showPopup).toHaveBeenCalledWith(popupData);
    });

    it('should handle empty popup data', () => {
      const popupData = {};

      Utils.showPopup(popupData);

      expect(hostSharedModule.d.domainService.showPopup).toHaveBeenCalledWith(popupData);
    });
  });
});
