import {describe, it, expect, jest, beforeEach} from '@jest/globals';

// Mock React Native modules
jest.mock('react-native', () => ({
  Dimensions: {
    get: jest.fn(() => ({
      width: 375,
      height: 812,
      scale: 3,
      fontScale: 1,
    })),
  },
  Platform: {
    OS: 'ios',
    isPad: false,
    isTV: false,
    select: jest.fn(),
  },
  StatusBar: {
    currentHeight: 24,
  },
}));

import {Dimensions, Platform, StatusBar} from 'react-native';
import DimensionUtils from '../DimensionUtils';

const mockDimensions = Dimensions as jest.Mocked<typeof Dimensions>;
const mockPlatform = Platform as jest.Mocked<typeof Platform>;

describe('DimensionUtils', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // Reset to default iOS values
    mockPlatform.OS = 'ios';
    mockPlatform.isPad = false;
    mockPlatform.isTV = false;
    mockPlatform.select.mockImplementation((options: any) => options.ios);
  });

  describe('Screen dimensions', () => {
    it('should get screen width and height', () => {
      mockDimensions.get.mockReturnValue({
        width: 375,
        height: 812,
        scale: 3,
        fontScale: 1,
      });

      expect(DimensionUtils.getScreenWidth()).toBe(375);
      expect(DimensionUtils.getScreenHeight()).toBe(812);
    });

    it('should handle different screen sizes', () => {
      // iPhone 14 Pro Max dimensions
      mockDimensions.get.mockReturnValue({
        width: 430,
        height: 932,
        scale: 3,
        fontScale: 1,
      });

      expect(DimensionUtils.getScreenWidth()).toBe(430);
      expect(DimensionUtils.getScreenHeight()).toBe(932);
    });
  });

  describe('iPhone X detection', () => {
    it('should detect iPhone X series devices', () => {
      // iPhone X dimensions
      mockDimensions.get.mockReturnValue({
        width: 375,
        height: 812,
        scale: 3,
        fontScale: 1,
      });
      mockPlatform.OS = 'ios';
      mockPlatform.isPad = false;
      mockPlatform.isTV = false;

      const isIPhoneX = DimensionUtils.isIPhoneX();
      expect(isIPhoneX).toBe(true);
    });

    it('should detect iPhone 14 Pro', () => {
      // iPhone 14 Pro dimensions
      mockDimensions.get.mockReturnValue({
        width: 393,
        height: 852,
        scale: 3,
        fontScale: 1,
      });
      mockPlatform.OS = 'ios';
      mockPlatform.isPad = false;
      mockPlatform.isTV = false;

      const isIPhoneX = DimensionUtils.isIPhoneX();
      expect(isIPhoneX).toBe(true);
    });

    it('should detect iPhone 16 series', () => {
      // iPhone 16 dimensions
      mockDimensions.get.mockReturnValue({
        width: 402,
        height: 874,
        scale: 3,
        fontScale: 1,
      });
      mockPlatform.OS = 'ios';
      mockPlatform.isPad = false;
      mockPlatform.isTV = false;

      const isIPhoneX = DimensionUtils.isIPhoneX();
      expect(isIPhoneX).toBe(true);
    });

    it('should not detect non-iPhone X devices as iPhone X', () => {
      // iPhone 8 dimensions
      mockDimensions.get.mockReturnValue({
        width: 375,
        height: 667,
        scale: 2,
        fontScale: 1,
      });
      mockPlatform.OS = 'ios';
      mockPlatform.isPad = false;
      mockPlatform.isTV = false;

      const isIPhoneX = DimensionUtils.isIPhoneX();
      expect(isIPhoneX).toBe(false);
    });

    it('should not detect iPad as iPhone X', () => {
      mockDimensions.get.mockReturnValue({
        width: 375,
        height: 812,
        scale: 3,
        fontScale: 1,
      });
      mockPlatform.OS = 'ios';
      mockPlatform.isPad = true;
      mockPlatform.isTV = false;

      const isIPhoneX = DimensionUtils.isIPhoneX();
      expect(isIPhoneX).toBe(false);
    });

    it('should not detect Apple TV as iPhone X', () => {
      mockDimensions.get.mockReturnValue({
        width: 375,
        height: 812,
        scale: 3,
        fontScale: 1,
      });
      mockPlatform.OS = 'ios';
      mockPlatform.isPad = false;
      mockPlatform.isTV = true;

      const isIPhoneX = DimensionUtils.isIPhoneX();
      expect(isIPhoneX).toBe(false);
    });

    it('should not detect Android devices as iPhone X', () => {
      mockDimensions.get.mockReturnValue({
        width: 375,
        height: 812,
        scale: 3,
        fontScale: 1,
      });
      mockPlatform.OS = 'android';

      const isIPhoneX = DimensionUtils.isIPhoneX();
      expect(isIPhoneX).toBe(false);
    });
  });

  describe('Padding calculations', () => {
    describe('iOS padding', () => {
      beforeEach(() => {
        mockPlatform.OS = 'ios';
        mockPlatform.isPad = false;
        mockPlatform.isTV = false;
      });

      it('should return iPhone X padding for iPhone X devices', () => {
        mockDimensions.get.mockReturnValue({
          width: 375,
          height: 812,
          scale: 3,
          fontScale: 1,
        });
        mockPlatform.select.mockImplementation((options: any) => options.ios);

        expect(DimensionUtils.getPaddingTopByDevice()).toBe(44);
        expect(DimensionUtils.getPaddingBottomByDevice()).toBe(34);
      });

      it('should return standard iOS padding for non-iPhone X devices', () => {
        mockDimensions.get.mockReturnValue({
          width: 375,
          height: 667,
          scale: 2,
          fontScale: 1,
        });
        mockPlatform.select.mockImplementation((options: any) => options.ios);

        expect(DimensionUtils.getPaddingTopByDevice()).toBe(20);
        expect(DimensionUtils.getPaddingBottomByDevice()).toBe(20);
      });
    });

    describe('Android padding', () => {
      beforeEach(() => {
        mockPlatform.OS = 'android';
      });

      it('should return Android padding with StatusBar height', () => {
        StatusBar.currentHeight = 24;
        mockPlatform.select.mockImplementation((options: any) => options.android);

        expect(DimensionUtils.getPaddingTopByDevice()).toBe(24);
        expect(DimensionUtils.getPaddingBottomByDevice()).toBe(20);
      });

      it('should handle missing StatusBar height', () => {
        (StatusBar as any).currentHeight = null;
        mockPlatform.select.mockImplementation((options: any) => options.android || 24);

        expect(DimensionUtils.getPaddingTopByDevice()).toBe(24);
        expect(DimensionUtils.getPaddingBottomByDevice()).toBe(20);
      });
    });

    describe('Other platforms', () => {
      it('should return default padding for unknown platforms', () => {
        mockPlatform.OS = 'web' as any;
        mockPlatform.select.mockImplementation((options: any) => options.default);

        expect(DimensionUtils.getPaddingTopByDevice()).toBe(0);
        expect(DimensionUtils.getPaddingBottomByDevice()).toBe(0);
      });
    });
  });

  describe('Responsive calculations', () => {
    it('should calculate responsive width', () => {
      mockDimensions.get.mockReturnValue({
        width: 375,
        height: 812,
        scale: 3,
        fontScale: 1,
      });

      const responsiveWidth = DimensionUtils.getResponsiveWidth(50); // 50% of screen width
      expect(responsiveWidth).toBe(187.5);
    });

    it('should calculate responsive height', () => {
      mockDimensions.get.mockReturnValue({
        width: 375,
        height: 812,
        scale: 3,
        fontScale: 1,
      });

      const responsiveHeight = DimensionUtils.getResponsiveHeight(25); // 25% of screen height
      expect(responsiveHeight).toBe(203);
    });

    it('should handle edge cases for responsive calculations', () => {
      mockDimensions.get.mockReturnValue({
        width: 375,
        height: 812,
        scale: 3,
        fontScale: 1,
      });

      expect(DimensionUtils.getResponsiveWidth(0)).toBe(0);
      expect(DimensionUtils.getResponsiveWidth(100)).toBe(375);
      expect(DimensionUtils.getResponsiveHeight(0)).toBe(0);
      expect(DimensionUtils.getResponsiveHeight(100)).toBe(812);
    });
  });

  describe('Safe area calculations', () => {
    it('should calculate safe area top for iPhone X', () => {
      mockDimensions.get.mockReturnValue({
        width: 375,
        height: 812,
        scale: 3,
        fontScale: 1,
      });
      mockPlatform.OS = 'ios';
      mockPlatform.isPad = false;
      mockPlatform.isTV = false;

      const safeAreaTop = DimensionUtils.getSafeAreaTop();
      expect(safeAreaTop).toBe(44);
    });

    it('should calculate safe area bottom for iPhone X', () => {
      mockDimensions.get.mockReturnValue({
        width: 375,
        height: 812,
        scale: 3,
        fontScale: 1,
      });
      mockPlatform.OS = 'ios';
      mockPlatform.isPad = false;
      mockPlatform.isTV = false;

      const safeAreaBottom = DimensionUtils.getSafeAreaBottom();
      expect(safeAreaBottom).toBe(34);
    });

    it('should calculate safe area for Android', () => {
      mockPlatform.OS = 'android';
      StatusBar.currentHeight = 24;

      const safeAreaTop = DimensionUtils.getSafeAreaTop();
      const safeAreaBottom = DimensionUtils.getSafeAreaBottom();

      expect(safeAreaTop).toBe(24);
      expect(safeAreaBottom).toBe(20);
    });
  });

  describe('Device type detection', () => {
    it('should detect tablet devices', () => {
      mockDimensions.get.mockReturnValue({
        width: 1024,
        height: 1366,
        scale: 2,
        fontScale: 1,
      });

      const isTablet = DimensionUtils.isTablet();
      expect(isTablet).toBe(true);
    });

    it('should detect phone devices', () => {
      mockDimensions.get.mockReturnValue({
        width: 375,
        height: 812,
        scale: 3,
        fontScale: 1,
      });

      const isTablet = DimensionUtils.isTablet();
      expect(isTablet).toBe(false);
    });
  });

  describe('Orientation detection', () => {
    it('should detect landscape orientation', () => {
      mockDimensions.get.mockReturnValue({
        width: 812,
        height: 375,
        scale: 3,
        fontScale: 1,
      });

      const isLandscape = DimensionUtils.isLandscape();
      expect(isLandscape).toBe(true);
    });

    it('should detect portrait orientation', () => {
      mockDimensions.get.mockReturnValue({
        width: 375,
        height: 812,
        scale: 3,
        fontScale: 1,
      });

      const isLandscape = DimensionUtils.isLandscape();
      expect(isLandscape).toBe(false);
    });
  });
});
