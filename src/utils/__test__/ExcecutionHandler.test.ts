import {describe, it, expect, jest, beforeEach} from '@jest/globals';

// Mock the createError function before any imports
const mockCreateError = jest.fn();

// Mock dependencies
jest.mock('msb-host-shared-module', () => ({
  hostSharedModule: {
    d: {
      domainService: {
        addSpinnerRequest: jest.fn(),
        addSpinnerCompleted: jest.fn(),
      },
    },
  },
}));

// Mock the MSBCustomError module completely
jest.mock('../../core/MSBCustomError', () => {
  // Get the actual module to preserve CustomError and ErrorCategory classes
  const actualModule = jest.requireActual('../../core/MSBCustomError') as any;

  return {
    // Preserve the actual classes
    CustomError: actualModule.CustomError,
    ErrorCategory: actualModule.ErrorCategory,
    // Replace createError with our mock
    createError: mockCreateError,
    // Preserve other exports if needed
    extractErrorFromResponse: actualModule.extractErrorFromResponse,
    isRetryable: actualModule.isRetryable,
    hasError: actualModule.hasError,
  };
});

// Import after mocking
import {ExecutionHandler} from '../ExcecutionHandler';
import {ResultState} from '../../core/ResultState';
import {BaseResponse, MSBError} from '../../core/BaseResponse';
import {hostSharedModule} from 'msb-host-shared-module';
import {CustomError, ErrorCategory} from '../../core/MSBCustomError';

// Mock console.log and console.error to avoid noise in tests
const originalConsoleLog = console.log;
const originalConsoleError = console.error;

describe('ExecutionHandler', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    console.log = jest.fn();
    console.error = jest.fn();

    // Setup mockCreateError to return CustomError instances
    mockCreateError.mockImplementation((...args: any[]) => {
      const code = args[0] as string | undefined;
      const errorCode = code || 'UNKNOWN_ERROR';
      return new CustomError(errorCode, ErrorCategory.UNKNOWN, 'Test Error', 'Test error message', false, []);
    });
  });

  afterAll(() => {
    console.log = originalConsoleLog;
    console.error = originalConsoleError;
  });

  describe('execute', () => {
    describe('Success scenarios', () => {
      it('should return SUCCESS state when fetch function succeeds', async () => {
        const mockData = {id: 1, name: 'Test User'};
        const mockResponse: BaseResponse<typeof mockData> = mockData;
        const fetchFunction = jest.fn().mockResolvedValue(mockResponse);

        const result = await ExecutionHandler.execute(fetchFunction);

        expect(result.status).toBe('SUCCESS');
        if (result.status === 'SUCCESS') {
          expect(result.data).toEqual(mockData);
        }
        expect(fetchFunction).toHaveBeenCalledTimes(1);
      });

      it('should return SUCCESS state with undefined data', async () => {
        const mockResponse: BaseResponse<any> = {data: undefined};
        const fetchFunction = jest.fn().mockResolvedValue(mockResponse);

        const result = await ExecutionHandler.execute(fetchFunction);

        expect(result.status).toBe('SUCCESS');
        if (result.status === 'SUCCESS') {
          expect(result.data).toEqual({data: undefined});
        }
      });

      it('should return SUCCESS state with null data', async () => {
        const mockResponse: BaseResponse<any> = {data: null};
        const fetchFunction = jest.fn().mockResolvedValue(mockResponse);

        const result = await ExecutionHandler.execute(fetchFunction);

        expect(result.status).toBe('SUCCESS');
        if (result.status === 'SUCCESS') {
          expect(result.data).toEqual({data: null});
        }
      });
    });

    describe('Error scenarios', () => {
      it('should return ERROR state when response has errors array', async () => {
        const mockError: MSBError = {
          key: 'TEST_ERROR',
          message: 'Test error message',
          context: ['test'],
        };
        const mockResponse: BaseResponse<any> = {
          errors: [mockError],
        };
        const fetchFunction = jest.fn().mockResolvedValue(mockResponse);

        const result = await ExecutionHandler.execute(fetchFunction);

        expect(result.status).toBe('ERROR');
        if (result.status === 'ERROR') {
          expect(result.error).toBeInstanceOf(CustomError);
          expect(result.error.code).toBe('TEST_ERROR');
        }
        expect(console.error).toHaveBeenCalledWith('❌ ExecutionHandler Null data Error');
      });

      it('should return ERROR state when response is null', async () => {
        const fetchFunction = jest.fn().mockResolvedValue(null);

        const result = await ExecutionHandler.execute(fetchFunction);

        expect(result.status).toBe('ERROR');
        if (result.status === 'ERROR') {
          expect(result.error).toBeInstanceOf(CustomError);
        }
        expect(console.error).toHaveBeenCalledWith('❌ ExecutionHandler Null data Error');
      });

      it('should return ERROR state when response is undefined', async () => {
        const fetchFunction = jest.fn().mockResolvedValue(undefined);

        const result = await ExecutionHandler.execute(fetchFunction);

        expect(result.status).toBe('ERROR');
        if (result.status === 'ERROR') {
          expect(result.error).toBeInstanceOf(CustomError);
        }
        expect(console.error).toHaveBeenCalledWith('❌ ExecutionHandler Null data Error');
      });

      it('should handle errors array with missing key', async () => {
        const mockError: MSBError = {
          message: 'Test error message',
          context: ['test'],
        };
        const mockResponse: BaseResponse<any> = {
          errors: [mockError],
        };
        const fetchFunction = jest.fn().mockResolvedValue(mockResponse);

        const result = await ExecutionHandler.execute(fetchFunction);

        expect(result.status).toBe('ERROR');
        if (result.status === 'ERROR') {
          expect(result.error).toBeInstanceOf(CustomError);
          expect(result.error.code).toBe('UNKNOWN_ERROR'); // Default when key is missing
        }
      });

      it('should handle empty errors array', async () => {
        const mockResponse: BaseResponse<any> = {
          errors: [],
        };
        const fetchFunction = jest.fn().mockResolvedValue(mockResponse);

        const result = await ExecutionHandler.execute(fetchFunction);

        expect(result.status).toBe('ERROR');
        if (result.status === 'ERROR') {
          expect(result.error).toBeInstanceOf(CustomError);
        }
      });
    });

    describe('Loading state management', () => {
      it('should show and hide loading when isUseLoading is true', async () => {
        const mockData = {id: 1, name: 'Test User'};
        const mockResponse: BaseResponse<typeof mockData> = mockData;
        const fetchFunction = jest.fn().mockResolvedValue(mockResponse);

        await ExecutionHandler.execute(fetchFunction, true);

        expect(hostSharedModule.d.domainService.addSpinnerRequest).toHaveBeenCalledTimes(1);
        expect(hostSharedModule.d.domainService.addSpinnerCompleted).toHaveBeenCalledTimes(1);
      });

      it('should not show loading when isUseLoading is false', async () => {
        const mockData = {id: 1, name: 'Test User'};
        const mockResponse: BaseResponse<typeof mockData> = mockData;
        const fetchFunction = jest.fn().mockResolvedValue(mockResponse);

        await ExecutionHandler.execute(fetchFunction, false);

        expect(hostSharedModule.d.domainService.addSpinnerRequest).not.toHaveBeenCalled();
        expect(hostSharedModule.d.domainService.addSpinnerCompleted).not.toHaveBeenCalled();
      });

      it('should not show loading when isUseLoading is null', async () => {
        const mockData = {id: 1, name: 'Test User'};
        const mockResponse: BaseResponse<typeof mockData> = mockData;
        const fetchFunction = jest.fn().mockResolvedValue(mockResponse);

        await ExecutionHandler.execute(fetchFunction, null);

        expect(hostSharedModule.d.domainService.addSpinnerRequest).not.toHaveBeenCalled();
        expect(hostSharedModule.d.domainService.addSpinnerCompleted).not.toHaveBeenCalled();
      });

      it('should not show loading when isUseLoading is undefined', async () => {
        const mockData = {id: 1, name: 'Test User'};
        const mockResponse: BaseResponse<typeof mockData> = mockData;
        const fetchFunction = jest.fn().mockResolvedValue(mockResponse);

        await ExecutionHandler.execute(fetchFunction, undefined);

        expect(hostSharedModule.d.domainService.addSpinnerRequest).not.toHaveBeenCalled();
        expect(hostSharedModule.d.domainService.addSpinnerCompleted).not.toHaveBeenCalled();
      });

      it('should hide loading even when error occurs', async () => {
        const mockResponse: BaseResponse<any> = {
          errors: [{key: 'TEST_ERROR', message: 'Test error', context: []}],
        };
        const fetchFunction = jest.fn().mockResolvedValue(mockResponse);

        await ExecutionHandler.execute(fetchFunction, true);

        expect(hostSharedModule.d.domainService.addSpinnerRequest).toHaveBeenCalledTimes(1);
        expect(hostSharedModule.d.domainService.addSpinnerCompleted).toHaveBeenCalledTimes(1);
      });
    });

    describe('Exception handling', () => {
      it('should handle fetch function throwing an exception', async () => {
        const fetchFunction = jest.fn().mockRejectedValue(new Error('Network error'));

        const result = await ExecutionHandler.execute(fetchFunction);

        expect(result.status).toBe('ERROR');
        if (result.status === 'ERROR') {
          expect(result.error).toBeInstanceOf(CustomError);
        }
      });

      it('should hide loading when exception occurs', async () => {
        const fetchFunction = jest.fn().mockRejectedValue(new Error('Network error'));

        await ExecutionHandler.execute(fetchFunction, true);

        expect(hostSharedModule.d.domainService.addSpinnerRequest).toHaveBeenCalledTimes(1);
        expect(hostSharedModule.d.domainService.addSpinnerCompleted).toHaveBeenCalledTimes(1);
      });
    });

    describe('Logging', () => {
      it('should log success response', async () => {
        const mockData = {id: 1, name: 'Test User'};
        const mockResponse: BaseResponse<typeof mockData> = mockData;
        const fetchFunction = jest.fn().mockResolvedValue(mockResponse);

        await ExecutionHandler.execute(fetchFunction);

        expect(console.log).toHaveBeenCalledWith('✅ ExecutionHandler with response=', mockResponse);
      });

      it('should log error when response has errors', async () => {
        const mockResponse: BaseResponse<any> = {
          errors: [{key: 'TEST_ERROR', message: 'Test error', context: []}],
        };
        const fetchFunction = jest.fn().mockResolvedValue(mockResponse);

        await ExecutionHandler.execute(fetchFunction);

        expect(console.error).toHaveBeenCalledWith('❌ ExecutionHandler Null data Error');
      });
    });

    describe('Generic type support', () => {
      interface User {
        id: number;
        name: string;
        email: string;
      }

      it('should support custom interface types', async () => {
        const mockUser: User = {
          id: 1,
          name: 'John Doe',
          email: '<EMAIL>',
        };
        const mockResponse: BaseResponse<User> = mockUser;
        const fetchFunction = jest.fn().mockResolvedValue(mockResponse);

        const result: ResultState<User> = await ExecutionHandler.execute(fetchFunction);

        expect(result.status).toBe('SUCCESS');
        if (result.status === 'SUCCESS') {
          expect(result.data?.id).toBe(1);
          expect(result.data?.name).toBe('John Doe');
          expect(result.data?.email).toBe('<EMAIL>');
        }
      });

      it('should support array types', async () => {
        const mockUsers: User[] = [
          {id: 1, name: 'User 1', email: '<EMAIL>'},
          {id: 2, name: 'User 2', email: '<EMAIL>'},
        ];
        const mockResponse: BaseResponse<User[]> = mockUsers;
        const fetchFunction = jest.fn().mockResolvedValue(mockResponse);

        const result: ResultState<User[]> = await ExecutionHandler.execute(fetchFunction);

        expect(result.status).toBe('SUCCESS');
        if (result.status === 'SUCCESS') {
          expect(Array.isArray(result.data)).toBe(true);
          expect(result.data).toHaveLength(2);
        }
      });
    });
  });
});
