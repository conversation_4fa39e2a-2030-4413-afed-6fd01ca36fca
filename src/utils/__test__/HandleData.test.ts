import {describe, it, expect, jest, beforeEach} from '@jest/globals';

// Mock the createError function before any imports
const mockCreateError = jest.fn();

// Mock the MSBCustomError module completely
jest.mock('../../core/MSBCustomError', () => {
  // Get the actual module to preserve CustomError and ErrorCategory classes
  const actualModule = jest.requireActual('../../core/MSBCustomError') as any;

  return {
    // Preserve the actual classes
    CustomError: actualModule.CustomError,
    ErrorCategory: actualModule.ErrorCategory,
    // Replace createError with our mock
    createError: mockCreateError,
    // Preserve other exports if needed
    extractErrorFromResponse: actualModule.extractErrorFromResponse,
    isRetryable: actualModule.isRetryable,
    hasError: actualModule.hasError,
  };
});

// Import after mocking
import {handleData} from '../HandleData';
import {CustomError, ErrorCategory} from '../../core/MSBCustomError';
import {MSBErrorCode} from '../../core/MSBErrorCode';

// Mock console.log to avoid noise in tests
const originalConsoleLog = console.log;

describe('HandleData', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    console.log = jest.fn();

    // Setup mockCreateError to throw CustomError
    mockCreateError.mockImplementation((...args: any[]) => {
      const code = args[0] as string | undefined;
      const errorCode = code || 'UNKNOWN_ERROR';
      const error = new CustomError(errorCode, ErrorCategory.UNKNOWN, 'Test Error', 'Test error message', false, []);
      throw error;
    });
  });

  afterAll(() => {
    console.log = originalConsoleLog;
  });

  describe('Success scenarios', () => {
    it('should handle successful response and map data correctly', async () => {
      const mockResponse = {
        id: 1,
        name: 'Test User',
        email: '<EMAIL>',
      };
      const mockRequest = Promise.resolve(mockResponse);
      const mapper = (response: any) => ({
        userId: response.id,
        userName: response.name,
        userEmail: response.email,
      });

      const result = await handleData(mockRequest, mapper);

      expect(result).toEqual({
        userId: 1,
        userName: 'Test User',
        userEmail: '<EMAIL>',
      });
      expect(console.log).toHaveBeenCalledWith('DATA:', mockResponse);
      expect(console.log).toHaveBeenCalledWith('✅✅✅✅✅✅✅✅✅✅✅Repository handleData Success', mockResponse);
    });

    it('should handle response with null data', async () => {
      const mockResponse = {data: null};
      const mockRequest = Promise.resolve(mockResponse);
      const mapper = (response: any) => response.data;

      const result = await handleData(mockRequest, mapper);

      expect(result).toBeNull();
      expect(console.log).toHaveBeenCalledWith('DATA:', mockResponse);
      expect(console.log).toHaveBeenCalledWith('✅✅✅✅✅✅✅✅✅✅✅Repository handleData Success', mockResponse);
    });

    it('should handle response with undefined data', async () => {
      const mockResponse = {data: undefined};
      const mockRequest = Promise.resolve(mockResponse);
      const mapper = (response: any) => response.data;

      const result = await handleData(mockRequest, mapper);

      expect(result).toBeUndefined();
      expect(console.log).toHaveBeenCalledWith('DATA:', mockResponse);
      expect(console.log).toHaveBeenCalledWith('✅✅✅✅✅✅✅✅✅✅✅Repository handleData Success', mockResponse);
    });

    it('should handle array responses', async () => {
      const mockResponse = [
        {id: 1, name: 'User 1'},
        {id: 2, name: 'User 2'},
      ];
      const mockRequest = Promise.resolve(mockResponse);
      const mapper = (response: any[]) =>
        response.map(user => ({
          userId: user.id,
          userName: user.name,
        }));

      const result = await handleData(mockRequest, mapper);

      expect(result).toEqual([
        {userId: 1, userName: 'User 1'},
        {userId: 2, userName: 'User 2'},
      ]);
    });

    it('should handle complex data transformation', async () => {
      const mockResponse = {
        users: [
          {id: 1, firstName: 'John', lastName: 'Doe'},
          {id: 2, firstName: 'Jane', lastName: 'Smith'},
        ],
        total: 2,
        page: 1,
      };
      const mockRequest = Promise.resolve(mockResponse);
      const mapper = (response: any) => ({
        userList: response.users.map((user: any) => `${user.firstName} ${user.lastName}`),
        count: response.total,
        currentPage: response.page,
      });

      const result = await handleData(mockRequest, mapper);

      expect(result).toEqual({
        userList: ['John Doe', 'Jane Smith'],
        count: 2,
        currentPage: 1,
      });
    });
  });

  describe('Error scenarios', () => {
    it('should throw CustomError when response has errors array', async () => {
      const mockResponse = {
        errors: [
          {
            key: 'VALIDATION_ERROR',
            message: 'Validation failed',
            context: ['field1'],
          },
        ],
      };
      const mockRequest = Promise.resolve(mockResponse);
      const mapper = (response: any) => response;

      await expect(handleData(mockRequest, mapper)).rejects.toThrow(CustomError);
      expect(console.log).toHaveBeenCalledWith(
        '❌❌❌❌❌❌❌❌❌❌Repository handleData Null data or response',
        mockResponse,
      );
    });

    it('should throw CustomError when response is null', async () => {
      const mockRequest = Promise.resolve(null);
      const mapper = (response: any) => response;

      await expect(handleData(mockRequest, mapper)).rejects.toThrow(CustomError);
      expect(console.log).toHaveBeenCalledWith('❌❌❌❌❌❌❌❌❌❌Repository handleData Null data or response', null);
    });

    it('should throw CustomError when response is undefined', async () => {
      const mockRequest = Promise.resolve(undefined);
      const mapper = (response: any) => response;

      await expect(handleData(mockRequest, mapper)).rejects.toThrow(CustomError);
      expect(console.log).toHaveBeenCalledWith(
        '❌❌❌❌❌❌❌❌❌❌Repository handleData Null data or response',
        undefined,
      );
    });

    it('should handle errors array with missing key', async () => {
      const mockResponse = {
        errors: [
          {
            message: 'Error without key',
            context: ['test'],
          },
        ],
      };
      const mockRequest = Promise.resolve(mockResponse);
      const mapper = (response: any) => response;

      await expect(handleData(mockRequest, mapper)).rejects.toThrow(CustomError);
    });

    it('should handle empty errors array', async () => {
      const mockResponse = {
        errors: [],
      };
      const mockRequest = Promise.resolve(mockResponse);
      const mapper = (response: any) => response;

      await expect(handleData(mockRequest, mapper)).rejects.toThrow(CustomError);
    });
  });

  describe('Mapper error scenarios', () => {
    it('should throw NOT_VALID_DATA_FORMAT error when mapper throws non-CustomError', async () => {
      const mockResponse = {id: 1, name: 'Test'};
      const mockRequest = Promise.resolve(mockResponse);
      const mapper = (response: any) => {
        throw new Error('Mapper error');
      };

      await expect(handleData(mockRequest, mapper)).rejects.toThrow(CustomError);
      expect(console.log).toHaveBeenCalledWith(
        '❌❌❌❌❌❌❌❌❌❌Repository handleData mapper data to DTO Error',
        expect.any(Error),
      );
    });

    it('should re-throw CustomError when mapper throws CustomError', async () => {
      const mockResponse = {id: 1, name: 'Test'};
      const mockRequest = Promise.resolve(mockResponse);
      const customError = new CustomError(
        'MAPPER_ERROR',
        ErrorCategory.VALIDATION,
        'Mapper Error',
        'Mapper validation failed',
        false,
      );
      const mapper = (response: any) => {
        throw customError;
      };

      await expect(handleData(mockRequest, mapper)).rejects.toThrow(customError);
      expect(console.log).toHaveBeenCalledWith(
        '❌❌❌❌❌❌❌❌❌❌Repository handleData mapper data to DTO Error',
        customError,
      );
    });

    it('should handle mapper returning null', async () => {
      const mockResponse = {id: 1, name: 'Test'};
      const mockRequest = Promise.resolve(mockResponse);
      const mapper = (response: any) => null;

      const result = await handleData(mockRequest, mapper);

      expect(result).toBeNull();
    });

    it('should handle mapper returning undefined', async () => {
      const mockResponse = {id: 1, name: 'Test'};
      const mockRequest = Promise.resolve(mockResponse);
      const mapper = (response: any) => undefined;

      const result = await handleData(mockRequest, mapper);

      expect(result).toBeUndefined();
    });
  });

  describe('Request promise rejection', () => {
    it('should handle request promise rejection', async () => {
      const mockRequest = Promise.reject(new Error('Network error'));
      const mapper = (response: any) => response;

      await expect(handleData(mockRequest, mapper)).rejects.toThrow(CustomError);
      expect(console.log).toHaveBeenCalledWith(
        '❌❌❌❌❌❌❌❌❌❌Repository handleData mapper data to DTO Error',
        expect.any(Error),
      );
    });

    it('should handle request promise rejection with CustomError', async () => {
      const customError = new CustomError(
        'NETWORK_ERROR',
        ErrorCategory.NETWORK,
        'Network Error',
        'Network connection failed',
        true,
      );
      const mockRequest = Promise.reject(customError);
      const mapper = (response: any) => response;

      await expect(handleData(mockRequest, mapper)).rejects.toThrow(customError);
    });
  });

  describe('Generic type support', () => {
    interface User {
      id: number;
      name: string;
      email: string;
    }

    interface UserDTO {
      userId: number;
      userName: string;
      userEmail: string;
    }

    it('should support generic types with proper typing', async () => {
      const mockResponse = {
        id: 1,
        name: 'John Doe',
        email: '<EMAIL>',
      };
      const mockRequest = Promise.resolve(mockResponse);
      const mapper = (response: User): UserDTO => ({
        userId: response.id,
        userName: response.name,
        userEmail: response.email,
      });

      const result: UserDTO = await handleData(mockRequest, mapper);

      expect(result.userId).toBe(1);
      expect(result.userName).toBe('John Doe');
      expect(result.userEmail).toBe('<EMAIL>');
    });

    it('should support array types', async () => {
      const mockResponse = [
        {id: 1, name: 'User 1', email: '<EMAIL>'},
        {id: 2, name: 'User 2', email: '<EMAIL>'},
      ];
      const mockRequest = Promise.resolve(mockResponse);
      const mapper = (response: User[]): UserDTO[] =>
        response.map(user => ({
          userId: user.id,
          userName: user.name,
          userEmail: user.email,
        }));

      const result: UserDTO[] = await handleData(mockRequest, mapper);

      expect(result).toHaveLength(2);
      expect(result[0].userId).toBe(1);
      expect(result[1].userId).toBe(2);
    });
  });

  describe('Edge cases', () => {
    it('should handle empty string response', async () => {
      const mockRequest = Promise.resolve('');
      const mapper = (response: string) => response.length;

      // Empty string is falsy, so it should throw CustomError
      await expect(handleData(mockRequest, mapper)).rejects.toThrow(CustomError);
      expect(console.log).toHaveBeenCalledWith('❌❌❌❌❌❌❌❌❌❌Repository handleData Null data or response', '');
    });

    it('should handle boolean response', async () => {
      const mockRequest = Promise.resolve(true);
      const mapper = (response: boolean) => !response;

      const result = await handleData(mockRequest, mapper);

      expect(result).toBe(false);
    });

    it('should handle number response', async () => {
      const mockRequest = Promise.resolve(42);
      const mapper = (response: number) => response * 2;

      const result = await handleData(mockRequest, mapper);

      expect(result).toBe(84);
    });
  });
});
