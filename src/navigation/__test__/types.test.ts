import {describe, it, expect} from '@jest/globals';
import {PaymentInfoModel, QRPaymentInfoModel} from '../types';
import {PaymentOrderRequest} from '../../data/models/payment-order/PaymentOrderRequest';
import {GetBillDetailModel} from '../../domain/entities/get-bill-detail/GetBillDetailModel';
import {ProviderModel} from '../../domain/entities/provider-list/ProviderListModel';

describe('Navigation Types', () => {
  describe('PaymentInfoModel', () => {
    it('should create PaymentInfoModel with all properties', () => {
      const mockBillDetail = new GetBillDetailModel(
        'BILL001',
        {
          code: 'ELECTRIC',
        },
        'QUERY_REF_001',
        {
          cif: 'CUST001',
          name: '<PERSON>',
          phone: '**********',
          acct: 'ACC001',
          address: '123 Main St',
        },
        [
          {
            id: 'BILL001',
            code: 'ELECTRIC_BILL',
            amount: 100000,
            custCode: 'CUST001',
            custName: '<PERSON>',
            period: '2024-03',
            fee: 5000,
            custAddress: '123 Main St',
          },
        ],
        '00',
        1,
        'Success',
        'TRACE001',
        'SUCCESS',
        null,
        1,
        '100000',
        'ACTIVE',
        'ACTIVE',
      );

      const mockProvider = new ProviderModel(
        1,
        'EVN001',
        'ELECTRIC',
        1,
        'EVN Electric',
        'EVN Electric',
        'EVN',
        'Vietnam Electricity',
        1,
        0,
        0,
        1,
        1,
        0,
        1,
        1,
        'Electric utility provider',
      );

      const mockPaymentValidate: PaymentOrderRequest = {
        transferTransactionInformation: {
          instructedAmount: {
            amount: '100000',
            currencyCode: 'VND',
          },
        },
      };

      const mockQRPaymentInfo: QRPaymentInfoModel = {
        accountNo: '**********',
        bankCode: 'MSB',
        remark: 'Payment for services',
        service: 'QR_PAYMENT',
        qrType: 'DYNAMIC',
        amount: 100000,
        serviceCode: 'QR001',
        storeId: 'STORE001',
        merchantName: 'Test Merchant',
        payType: 'INSTANT',
        qrFormat: 'EMV',
        quantity: 1,
        expiredTime: '2024-03-15T23:59:59',
        qrContent: 'qr_content_string',
      };

      const paymentInfo: PaymentInfoModel = {
        title: 'Electric Bill Payment',
        categoryName: 'Utilities',
        billInfo: mockBillDetail,
        paymentValidate: mockPaymentValidate,
        originatorAccount: {
          identification: 'ID123456',
          name: 'John Doe',
          accountNo: '**********',
          bankName: 'MSB Bank',
          bankCode: 'MSB',
        },
        paymentResultType: 'SUCCESS',
        contractName: 'Electric Contract 001',
        provider: mockProvider,
        additionalInfo: 'Additional payment information',
        qrPaymentInfo: mockQRPaymentInfo,
      };

      expect(paymentInfo.title).toBe('Electric Bill Payment');
      expect(paymentInfo.categoryName).toBe('Utilities');
      expect(paymentInfo.billInfo).toBe(mockBillDetail);
      expect(paymentInfo.paymentValidate).toBe(mockPaymentValidate);
      expect(paymentInfo.originatorAccount?.identification).toBe('ID123456');
      expect(paymentInfo.originatorAccount?.name).toBe('John Doe');
      expect(paymentInfo.originatorAccount?.accountNo).toBe('**********');
      expect(paymentInfo.originatorAccount?.bankName).toBe('MSB Bank');
      expect(paymentInfo.originatorAccount?.bankCode).toBe('MSB');
      expect(paymentInfo.paymentResultType).toBe('SUCCESS');
      expect(paymentInfo.contractName).toBe('Electric Contract 001');
      expect(paymentInfo.provider).toBe(mockProvider);
      expect(paymentInfo.additionalInfo).toBe('Additional payment information');
      expect(paymentInfo.qrPaymentInfo).toBe(mockQRPaymentInfo);
    });

    it('should create PaymentInfoModel with minimal properties', () => {
      const paymentInfo: PaymentInfoModel = {};

      expect(paymentInfo.title).toBeUndefined();
      expect(paymentInfo.categoryName).toBeUndefined();
      expect(paymentInfo.billInfo).toBeUndefined();
      expect(paymentInfo.paymentValidate).toBeUndefined();
      expect(paymentInfo.originatorAccount).toBeUndefined();
      expect(paymentInfo.paymentResultType).toBeUndefined();
      expect(paymentInfo.contractName).toBeUndefined();
      expect(paymentInfo.provider).toBeUndefined();
      expect(paymentInfo.additionalInfo).toBeUndefined();
      expect(paymentInfo.qrPaymentInfo).toBeUndefined();
    });

    it('should create PaymentInfoModel with partial properties', () => {
      const paymentInfo: PaymentInfoModel = {
        title: 'Water Bill',
        categoryName: 'Utilities',
        paymentResultType: 'PENDING',
      };

      expect(paymentInfo.title).toBe('Water Bill');
      expect(paymentInfo.categoryName).toBe('Utilities');
      expect(paymentInfo.paymentResultType).toBe('PENDING');
      expect(paymentInfo.billInfo).toBeUndefined();
      expect(paymentInfo.provider).toBeUndefined();
    });

    it('should handle null provider', () => {
      const paymentInfo: PaymentInfoModel = {
        title: 'Test Payment',
        provider: null,
      };

      expect(paymentInfo.title).toBe('Test Payment');
      expect(paymentInfo.provider).toBeNull();
    });

    it('should handle partial originatorAccount', () => {
      const paymentInfo: PaymentInfoModel = {
        originatorAccount: {
          accountNo: '**********',
          bankName: 'Test Bank',
        },
      };

      expect(paymentInfo.originatorAccount?.accountNo).toBe('**********');
      expect(paymentInfo.originatorAccount?.bankName).toBe('Test Bank');
      expect(paymentInfo.originatorAccount?.identification).toBeUndefined();
      expect(paymentInfo.originatorAccount?.name).toBeUndefined();
      expect(paymentInfo.originatorAccount?.bankCode).toBeUndefined();
    });

    it('should handle empty originatorAccount', () => {
      const paymentInfo: PaymentInfoModel = {
        originatorAccount: {},
      };

      expect(paymentInfo.originatorAccount).toEqual({});
    });
  });

  describe('QRPaymentInfoModel', () => {
    it('should create QRPaymentInfoModel with all properties', () => {
      const qrPaymentInfo: QRPaymentInfoModel = {
        accountNo: '**********123456',
        bankCode: 'MSB',
        remark: 'QR Payment for goods',
        service: 'QR_PAYMENT_SERVICE',
        qrType: 'DYNAMIC',
        amount: 250000,
        serviceCode: 'QR_SVC_001',
        storeId: 'STORE_12345',
        merchantName: 'ABC Electronics Store',
        payType: 'INSTANT_PAYMENT',
        qrFormat: 'EMV_QR',
        quantity: 2,
        expiredTime: '2024-03-15T23:59:59Z',
        qrContent: 'encoded_qr_content_string_here',
      };

      expect(qrPaymentInfo.accountNo).toBe('**********123456');
      expect(qrPaymentInfo.bankCode).toBe('MSB');
      expect(qrPaymentInfo.remark).toBe('QR Payment for goods');
      expect(qrPaymentInfo.service).toBe('QR_PAYMENT_SERVICE');
      expect(qrPaymentInfo.qrType).toBe('DYNAMIC');
      expect(qrPaymentInfo.amount).toBe(250000);
      expect(qrPaymentInfo.serviceCode).toBe('QR_SVC_001');
      expect(qrPaymentInfo.storeId).toBe('STORE_12345');
      expect(qrPaymentInfo.merchantName).toBe('ABC Electronics Store');
      expect(qrPaymentInfo.payType).toBe('INSTANT_PAYMENT');
      expect(qrPaymentInfo.qrFormat).toBe('EMV_QR');
      expect(qrPaymentInfo.quantity).toBe(2);
      expect(qrPaymentInfo.expiredTime).toBe('2024-03-15T23:59:59Z');
      expect(qrPaymentInfo.qrContent).toBe('encoded_qr_content_string_here');
    });

    it('should create QRPaymentInfoModel with minimal properties', () => {
      const qrPaymentInfo: QRPaymentInfoModel = {};

      expect(qrPaymentInfo.accountNo).toBeUndefined();
      expect(qrPaymentInfo.bankCode).toBeUndefined();
      expect(qrPaymentInfo.remark).toBeUndefined();
      expect(qrPaymentInfo.service).toBeUndefined();
      expect(qrPaymentInfo.qrType).toBeUndefined();
      expect(qrPaymentInfo.amount).toBeUndefined();
      expect(qrPaymentInfo.serviceCode).toBeUndefined();
      expect(qrPaymentInfo.storeId).toBeUndefined();
      expect(qrPaymentInfo.merchantName).toBeUndefined();
      expect(qrPaymentInfo.payType).toBeUndefined();
      expect(qrPaymentInfo.qrFormat).toBeUndefined();
      expect(qrPaymentInfo.quantity).toBeUndefined();
      expect(qrPaymentInfo.expiredTime).toBeUndefined();
      expect(qrPaymentInfo.qrContent).toBeUndefined();
    });

    it('should create QRPaymentInfoModel with partial properties', () => {
      const qrPaymentInfo: QRPaymentInfoModel = {
        accountNo: '**********',
        amount: 50000,
        merchantName: 'Coffee Shop',
        qrType: 'STATIC',
      };

      expect(qrPaymentInfo.accountNo).toBe('**********');
      expect(qrPaymentInfo.amount).toBe(50000);
      expect(qrPaymentInfo.merchantName).toBe('Coffee Shop');
      expect(qrPaymentInfo.qrType).toBe('STATIC');
      expect(qrPaymentInfo.bankCode).toBeUndefined();
      expect(qrPaymentInfo.service).toBeUndefined();
    });

    it('should handle zero amount', () => {
      const qrPaymentInfo: QRPaymentInfoModel = {
        amount: 0,
        quantity: 0,
      };

      expect(qrPaymentInfo.amount).toBe(0);
      expect(qrPaymentInfo.quantity).toBe(0);
    });

    it('should handle negative values', () => {
      const qrPaymentInfo: QRPaymentInfoModel = {
        amount: -1000,
        quantity: -1,
      };

      expect(qrPaymentInfo.amount).toBe(-1000);
      expect(qrPaymentInfo.quantity).toBe(-1);
    });

    it('should handle large numbers', () => {
      const qrPaymentInfo: QRPaymentInfoModel = {
        amount: *********,
        quantity: 1000,
      };

      expect(qrPaymentInfo.amount).toBe(*********);
      expect(qrPaymentInfo.quantity).toBe(1000);
    });

    it('should handle empty strings', () => {
      const qrPaymentInfo: QRPaymentInfoModel = {
        accountNo: '',
        bankCode: '',
        remark: '',
        service: '',
        qrType: '',
        serviceCode: '',
        storeId: '',
        merchantName: '',
        payType: '',
        qrFormat: '',
        expiredTime: '',
        qrContent: '',
      };

      expect(qrPaymentInfo.accountNo).toBe('');
      expect(qrPaymentInfo.bankCode).toBe('');
      expect(qrPaymentInfo.remark).toBe('');
      expect(qrPaymentInfo.service).toBe('');
      expect(qrPaymentInfo.qrType).toBe('');
      expect(qrPaymentInfo.serviceCode).toBe('');
      expect(qrPaymentInfo.storeId).toBe('');
      expect(qrPaymentInfo.merchantName).toBe('');
      expect(qrPaymentInfo.payType).toBe('');
      expect(qrPaymentInfo.qrFormat).toBe('');
      expect(qrPaymentInfo.expiredTime).toBe('');
      expect(qrPaymentInfo.qrContent).toBe('');
    });
  });

  describe('Type compatibility', () => {
    it('should allow PaymentInfoModel to contain QRPaymentInfoModel', () => {
      const qrInfo: QRPaymentInfoModel = {
        accountNo: '**********',
        amount: 100000,
        merchantName: 'Test Merchant',
      };

      const paymentInfo: PaymentInfoModel = {
        title: 'QR Payment',
        qrPaymentInfo: qrInfo,
      };

      expect(paymentInfo.qrPaymentInfo).toBe(qrInfo);
      expect(paymentInfo.qrPaymentInfo?.accountNo).toBe('**********');
      expect(paymentInfo.qrPaymentInfo?.amount).toBe(100000);
      expect(paymentInfo.qrPaymentInfo?.merchantName).toBe('Test Merchant');
    });

    it('should handle nested object updates', () => {
      const paymentInfo: PaymentInfoModel = {
        originatorAccount: {
          accountNo: '**********',
          name: 'Original Name',
        },
        qrPaymentInfo: {
          amount: 50000,
          merchantName: 'Original Merchant',
        },
      };

      // Update nested objects
      if (paymentInfo.originatorAccount) {
        paymentInfo.originatorAccount.name = 'Updated Name';
        paymentInfo.originatorAccount.bankCode = 'NEW_BANK';
      }

      if (paymentInfo.qrPaymentInfo) {
        paymentInfo.qrPaymentInfo.amount = 75000;
        paymentInfo.qrPaymentInfo.qrType = 'DYNAMIC';
      }

      expect(paymentInfo.originatorAccount?.name).toBe('Updated Name');
      expect(paymentInfo.originatorAccount?.bankCode).toBe('NEW_BANK');
      expect(paymentInfo.qrPaymentInfo?.amount).toBe(75000);
      expect(paymentInfo.qrPaymentInfo?.qrType).toBe('DYNAMIC');
    });

    it('should handle type assertions correctly', () => {
      const unknownPaymentInfo: unknown = {
        title: 'Test Payment',
        amount: 100000,
      };

      const paymentInfo = unknownPaymentInfo as PaymentInfoModel;
      expect(paymentInfo.title).toBe('Test Payment');

      const unknownQRInfo: unknown = {
        accountNo: '**********',
        amount: 50000,
      };

      const qrInfo = unknownQRInfo as QRPaymentInfoModel;
      expect(qrInfo.accountNo).toBe('**********');
      expect(qrInfo.amount).toBe(50000);
    });
  });

  describe('Real-world scenarios', () => {
    it('should handle electric bill payment scenario', () => {
      const electricBillPayment: PaymentInfoModel = {
        title: 'Electric Bill Payment',
        categoryName: 'Utilities',
        contractName: 'Electric Contract EVN-001',
        originatorAccount: {
          accountNo: '**********',
          name: 'Nguyen Van A',
          bankName: 'MSB Bank',
          bankCode: 'MSB',
        },
        paymentResultType: 'SUCCESS',
        additionalInfo: 'Monthly electric bill payment for March 2024',
      };

      expect(electricBillPayment.title).toBe('Electric Bill Payment');
      expect(electricBillPayment.categoryName).toBe('Utilities');
      expect(electricBillPayment.originatorAccount?.name).toBe('Nguyen Van A');
    });

    it('should handle QR payment scenario', () => {
      const qrPayment: PaymentInfoModel = {
        title: 'QR Payment',
        categoryName: 'QR Payment',
        qrPaymentInfo: {
          accountNo: '**********',
          bankCode: 'MSB',
          amount: 150000,
          merchantName: 'Coffee House',
          storeId: 'CH001',
          qrType: 'DYNAMIC',
          payType: 'INSTANT',
          remark: 'Coffee and pastry',
          expiredTime: '2024-03-15T18:00:00Z',
        },
        paymentResultType: 'PENDING',
      };

      expect(qrPayment.qrPaymentInfo?.merchantName).toBe('Coffee House');
      expect(qrPayment.qrPaymentInfo?.amount).toBe(150000);
      expect(qrPayment.paymentResultType).toBe('PENDING');
    });

    it('should handle mobile recharge scenario', () => {
      const mobileRecharge: PaymentInfoModel = {
        title: 'Mobile Recharge',
        categoryName: 'Telecom',
        contractName: 'Mobile Number: **********',
        originatorAccount: {
          accountNo: '**********',
          name: 'Tran Thi B',
          bankName: 'MSB Bank',
        },
        additionalInfo: 'Viettel prepaid recharge 100,000 VND',
        paymentResultType: 'SUCCESS',
      };

      expect(mobileRecharge.title).toBe('Mobile Recharge');
      expect(mobileRecharge.categoryName).toBe('Telecom');
      expect(mobileRecharge.contractName).toBe('Mobile Number: **********');
    });

    it('should handle failed payment scenario', () => {
      const failedPayment: PaymentInfoModel = {
        title: 'Water Bill Payment',
        categoryName: 'Utilities',
        paymentResultType: 'FAILED',
        additionalInfo: 'Payment failed due to insufficient balance',
        originatorAccount: {
          accountNo: '**********',
          name: 'Le Van C',
        },
      };

      expect(failedPayment.paymentResultType).toBe('FAILED');
      expect(failedPayment.additionalInfo).toContain('insufficient balance');
    });
  });
});
