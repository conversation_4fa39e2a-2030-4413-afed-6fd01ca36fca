import React from 'react';
import {describe, it, expect, jest, beforeEach} from '@jest/globals';
import {render} from '@testing-library/react-native';
import PaymentStack from '../PaymentStack';
import ScreenNames from '../../commons/ScreenNames';

// Mock dependencies
jest.mock('@react-navigation/native-stack', () => ({
  createNativeStackNavigator: jest.fn(() => ({
    Navigator: ({children, screenOptions, key}: any) => (
      <div testID="stack-navigator" data-screen-options={JSON.stringify(screenOptions)} data-key={key}>
        {children}
      </div>
    ),
    Screen: ({component, name, options}: any) => (
      <div
        testID={`stack-screen-${name}`}
        data-component={component.name}
        data-options={JSON.stringify(options)}
      >
        {name}
      </div>
    ),
  })),
}));

jest.mock('msb-communication-lib/dist/locales/LocaleContext', () => ({
  useLocale: jest.fn(() => ({
    locale: 'vi',
  })),
}));

// Mock screen components
jest.mock('../../presentation/payment-home', () => ({
  __esModule: true,
  default: () => <div testID="payment-home-page">PaymentHomePage</div>,
}));

jest.mock('../../presentation/payment-bill', () => ({
  __esModule: true,
  default: () => <div testID="payment-bill-screen">PaymentBillScreen</div>,
}));

jest.mock('../../presentation/payment-confirm', () => ({
  __esModule: true,
  default: () => <div testID="payment-confirm-screen">PaymentConfirmScreen</div>,
}));

jest.mock('../../presentation/payment-info', () => ({
  __esModule: true,
  default: () => <div testID="payment-info-screen">PaymentInfoScreen</div>,
}));

jest.mock('../../presentation/payment-result', () => ({
  __esModule: true,
  default: () => <div testID="payment-result-screen">PaymentResultScreen</div>,
}));

jest.mock('../../presentation/payment-result-detail', () => ({
  __esModule: true,
  default: () => <div testID="payment-result-detail-screen">PaymentResultDetailScreen</div>,
}));

jest.mock('../../presentation/payment-phone', () => ({
  __esModule: true,
  default: () => <div testID="payment-phone-screen">PaymentPhoneScreen</div>,
}));

jest.mock('../../presentation/savecontact', () => ({
  __esModule: true,
  default: () => <div testID="save-bill-contact-screen">SaveBillContactScreen</div>,
}));

jest.mock('../../presentation/editcontact', () => ({
  __esModule: true,
  default: () => <div testID="edit-bill-contact-screen">EditBillContactScreen</div>,
}));

jest.mock('../../presentation/bill-detail', () => ({
  __esModule: true,
  default: () => <div testID="bill-detail-screen">BillDetailScreen</div>,
}));

jest.mock('../../presentation/qr-payment-info', () => ({
  __esModule: true,
  default: () => <div testID="qr-payment-info-screen">QRPaymentInfoScreen</div>,
}));

const {useLocale} = require('msb-communication-lib/dist/locales/LocaleContext');

describe('PaymentStack', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('rendering', () => {
    it('should render PaymentStack with correct structure', () => {
      const {getByTestId} = render(<PaymentStack />);

      expect(getByTestId('stack-navigator')).toBeTruthy();
    });

    it('should render with correct screen options', () => {
      const {getByTestId} = render(<PaymentStack />);

      const navigator = getByTestId('stack-navigator');
      const screenOptions = JSON.parse(navigator.getAttribute('data-screen-options') || '{}');
      
      expect(screenOptions.animation).toBe('slide_from_right');
    });

    it('should render with locale key', () => {
      const {getByTestId} = render(<PaymentStack />);

      const navigator = getByTestId('stack-navigator');
      expect(navigator.getAttribute('data-key')).toBe('vi');
    });

    it('should update key when locale changes', () => {
      useLocale.mockReturnValue({locale: 'en'});
      
      const {getByTestId} = render(<PaymentStack />);

      const navigator = getByTestId('stack-navigator');
      expect(navigator.getAttribute('data-key')).toBe('en');
    });
  });

  describe('screen configuration', () => {
    it('should render PaymentHomePage screen correctly', () => {
      const {getByTestId} = render(<PaymentStack />);

      const screen = getByTestId(`stack-screen-${ScreenNames.PaymentHomePage}`);
      expect(screen).toBeTruthy();
      expect(screen.getAttribute('data-component')).toBe('PaymentHomePage');
      
      const options = JSON.parse(screen.getAttribute('data-options') || '{}');
      expect(options.headerShown).toBe(false);
    });

    it('should render BillDetailScreen screen correctly', () => {
      const {getByTestId} = render(<PaymentStack />);

      const screen = getByTestId(`stack-screen-${ScreenNames.BillDetailScreen}`);
      expect(screen).toBeTruthy();
      expect(screen.getAttribute('data-component')).toBe('BillDetailScreen');
      
      const options = JSON.parse(screen.getAttribute('data-options') || '{}');
      expect(options.headerShown).toBe(false);
    });

    it('should render PaymentBillScreen screen correctly', () => {
      const {getByTestId} = render(<PaymentStack />);

      const screen = getByTestId(`stack-screen-${ScreenNames.PaymentBillScreen}`);
      expect(screen).toBeTruthy();
      expect(screen.getAttribute('data-component')).toBe('PaymentBillScreen');
      
      const options = JSON.parse(screen.getAttribute('data-options') || '{}');
      expect(options.headerShown).toBe(false);
    });

    it('should render PaymentInfoScreen screen correctly', () => {
      const {getByTestId} = render(<PaymentStack />);

      const screen = getByTestId(`stack-screen-${ScreenNames.PaymentInfoScreen}`);
      expect(screen).toBeTruthy();
      expect(screen.getAttribute('data-component')).toBe('PaymentInfoScreen');
      
      const options = JSON.parse(screen.getAttribute('data-options') || '{}');
      expect(options.headerShown).toBe(false);
    });

    it('should render PaymentConfirmScreen screen correctly', () => {
      const {getByTestId} = render(<PaymentStack />);

      const screen = getByTestId(`stack-screen-${ScreenNames.PaymentConfirmScreen}`);
      expect(screen).toBeTruthy();
      expect(screen.getAttribute('data-component')).toBe('PaymentConfirmScreen');
      
      const options = JSON.parse(screen.getAttribute('data-options') || '{}');
      expect(options.headerShown).toBe(false);
    });

    it('should render PaymentResultScreen screen correctly', () => {
      const {getByTestId} = render(<PaymentStack />);

      const screen = getByTestId(`stack-screen-${ScreenNames.PaymentResultScreen}`);
      expect(screen).toBeTruthy();
      expect(screen.getAttribute('data-component')).toBe('PaymentResultScreen');
      
      const options = JSON.parse(screen.getAttribute('data-options') || '{}');
      expect(options.headerShown).toBe(false);
    });

    it('should render PaymentResultDetailScreen with special options', () => {
      const {getByTestId} = render(<PaymentStack />);

      const screen = getByTestId(`stack-screen-${ScreenNames.PaymentResultDetailScreen}`);
      expect(screen).toBeTruthy();
      expect(screen.getAttribute('data-component')).toBe('PaymentResultDetailScreen');
      
      const options = JSON.parse(screen.getAttribute('data-options') || '{}');
      expect(options.headerShown).toBe(false);
      expect(options.animation).toBe('slide_from_bottom');
      expect(options.gestureEnabled).toBe(false);
    });

    it('should render PaymentPhoneScreen screen correctly', () => {
      const {getByTestId} = render(<PaymentStack />);

      const screen = getByTestId(`stack-screen-${ScreenNames.PaymentPhoneScreen}`);
      expect(screen).toBeTruthy();
      expect(screen.getAttribute('data-component')).toBe('PaymentPhoneScreen');
      
      const options = JSON.parse(screen.getAttribute('data-options') || '{}');
      expect(options.headerShown).toBe(false);
    });

    it('should render SaveBillContactScreen screen correctly', () => {
      const {getByTestId} = render(<PaymentStack />);

      const screen = getByTestId(`stack-screen-${ScreenNames.SaveBillContactScreen}`);
      expect(screen).toBeTruthy();
      expect(screen.getAttribute('data-component')).toBe('SaveBillContactScreen');
      
      const options = JSON.parse(screen.getAttribute('data-options') || '{}');
      expect(options.headerShown).toBe(false);
    });

    it('should render EditBillContactScreen screen correctly', () => {
      const {getByTestId} = render(<PaymentStack />);

      const screen = getByTestId(`stack-screen-${ScreenNames.EditBillContactScreen}`);
      expect(screen).toBeTruthy();
      expect(screen.getAttribute('data-component')).toBe('EditBillContactScreen');
      
      const options = JSON.parse(screen.getAttribute('data-options') || '{}');
      expect(options.headerShown).toBe(false);
    });

    it('should render QRPaymentInfoScreen screen correctly', () => {
      const {getByTestId} = render(<PaymentStack />);

      const screen = getByTestId(`stack-screen-${ScreenNames.QRPaymentInfoScreen}`);
      expect(screen).toBeTruthy();
      expect(screen.getAttribute('data-component')).toBe('QRPaymentInfoScreen');
      
      const options = JSON.parse(screen.getAttribute('data-options') || '{}');
      expect(options.headerShown).toBe(false);
    });
  });

  describe('screen count and order', () => {
    it('should render all expected screens', () => {
      const {getByTestId} = render(<PaymentStack />);

      const expectedScreens = [
        ScreenNames.PaymentHomePage,
        ScreenNames.BillDetailScreen,
        ScreenNames.PaymentBillScreen,
        ScreenNames.PaymentInfoScreen,
        ScreenNames.PaymentConfirmScreen,
        ScreenNames.PaymentResultScreen,
        ScreenNames.PaymentResultDetailScreen,
        ScreenNames.PaymentPhoneScreen,
        ScreenNames.SaveBillContactScreen,
        ScreenNames.EditBillContactScreen,
        ScreenNames.QRPaymentInfoScreen,
      ];

      expectedScreens.forEach(screenName => {
        expect(getByTestId(`stack-screen-${screenName}`)).toBeTruthy();
      });
    });

    it('should have correct number of screens', () => {
      const {container} = render(<PaymentStack />);
      
      const screens = container.querySelectorAll('[data-testid^="stack-screen-"]');
      expect(screens).toHaveLength(11);
    });
  });

  describe('locale handling', () => {
    it('should handle different locales', () => {
      const locales = ['vi', 'en', 'zh'];
      
      locales.forEach(locale => {
        useLocale.mockReturnValue({locale});
        
        const {getByTestId} = render(<PaymentStack />);
        const navigator = getByTestId('stack-navigator');
        
        expect(navigator.getAttribute('data-key')).toBe(locale);
      });
    });

    it('should handle undefined locale', () => {
      useLocale.mockReturnValue({locale: undefined});
      
      const {getByTestId} = render(<PaymentStack />);
      const navigator = getByTestId('stack-navigator');
      
      expect(navigator.getAttribute('data-key')).toBe('null');
    });

    it('should handle null locale', () => {
      useLocale.mockReturnValue({locale: null});
      
      const {getByTestId} = render(<PaymentStack />);
      const navigator = getByTestId('stack-navigator');
      
      expect(navigator.getAttribute('data-key')).toBe('null');
    });
  });

  describe('navigation options consistency', () => {
    it('should have headerShown false for all screens except special cases', () => {
      const {container} = render(<PaymentStack />);
      
      const screens = container.querySelectorAll('[data-testid^="stack-screen-"]');
      
      screens.forEach(screen => {
        const options = JSON.parse(screen.getAttribute('data-options') || '{}');
        expect(options.headerShown).toBe(false);
      });
    });

    it('should have special animation for PaymentResultDetailScreen only', () => {
      const {getByTestId} = render(<PaymentStack />);
      
      const resultDetailScreen = getByTestId(`stack-screen-${ScreenNames.PaymentResultDetailScreen}`);
      const options = JSON.parse(resultDetailScreen.getAttribute('data-options') || '{}');
      
      expect(options.animation).toBe('slide_from_bottom');
      expect(options.gestureEnabled).toBe(false);
    });

    it('should not have special animation for other screens', () => {
      const {getByTestId} = render(<PaymentStack />);
      
      const regularScreens = [
        ScreenNames.PaymentHomePage,
        ScreenNames.PaymentBillScreen,
        ScreenNames.PaymentInfoScreen,
      ];
      
      regularScreens.forEach(screenName => {
        const screen = getByTestId(`stack-screen-${screenName}`);
        const options = JSON.parse(screen.getAttribute('data-options') || '{}');
        
        expect(options.animation).toBeUndefined();
        expect(options.gestureEnabled).toBeUndefined();
      });
    });
  });

  describe('error handling', () => {
    it('should handle missing useLocale hook', () => {
      useLocale.mockImplementation(() => {
        throw new Error('useLocale not available');
      });
      
      expect(() => render(<PaymentStack />)).toThrow('useLocale not available');
    });

    it('should handle malformed locale context', () => {
      useLocale.mockReturnValue({});
      
      const {getByTestId} = render(<PaymentStack />);
      const navigator = getByTestId('stack-navigator');
      
      expect(navigator.getAttribute('data-key')).toBe('null');
    });
  });

  describe('performance considerations', () => {
    it('should render efficiently with multiple locale changes', () => {
      const startTime = performance.now();
      
      const locales = ['vi', 'en', 'zh', 'ja', 'ko'];
      
      locales.forEach(locale => {
        useLocale.mockReturnValue({locale});
        const {unmount} = render(<PaymentStack />);
        unmount();
      });
      
      const endTime = performance.now();
      expect(endTime - startTime).toBeLessThan(100); // Should complete quickly
    });

    it('should not cause memory leaks with frequent re-renders', () => {
      const {rerender} = render(<PaymentStack />);
      
      // Simulate frequent re-renders
      for (let i = 0; i < 50; i++) {
        useLocale.mockReturnValue({locale: `locale-${i}`});
        rerender(<PaymentStack />);
      }
      
      // Should not throw or cause performance issues
      expect(true).toBe(true);
    });
  });

  describe('integration scenarios', () => {
    it('should maintain stack navigator structure', () => {
      const {getByTestId, container} = render(<PaymentStack />);
      
      const navigator = getByTestId('stack-navigator');
      const screens = container.querySelectorAll('[data-testid^="stack-screen-"]');
      
      expect(navigator).toBeTruthy();
      expect(screens.length).toBeGreaterThan(0);
      
      screens.forEach(screen => {
        expect(navigator).toContainElement(screen);
      });
    });

    it('should work with React Navigation ecosystem', () => {
      // This test verifies that the component structure is compatible
      // with React Navigation's expected patterns
      const {getByTestId} = render(<PaymentStack />);
      
      const navigator = getByTestId('stack-navigator');
      const screenOptions = JSON.parse(navigator.getAttribute('data-screen-options') || '{}');
      
      expect(screenOptions).toHaveProperty('animation');
      expect(navigator.getAttribute('data-key')).toBeTruthy();
    });
  });
});
