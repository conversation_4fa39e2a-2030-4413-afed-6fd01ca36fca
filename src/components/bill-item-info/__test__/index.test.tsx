import React from 'react';
import {describe, it, expect, jest, beforeEach} from '@jest/globals';
import {render} from '@testing-library/react-native';
import BillItemInfo from '../index';

// Mock dependencies
jest.mock('react-native', () => ({
  View: ({children, style, testID}: any) => (
    <div testID={testID} style={style}>
      {children}
    </div>
  ),
}));

jest.mock('msb-shared-component', () => ({
  MSBTextBase: ({content, style, testID}: any) => (
    <span testID={testID} style={style}>
      {content}
    </span>
  ),
  ColorDataView: {
    TextSub: '#666666',
    TextMain: '#000000',
  },
  createMSBStyleSheet: jest.fn((styleFunction) => styleFunction({
    Typography: {
      small_regular: {
        fontSize: 12,
        fontWeight: 'normal',
      },
      base_medium: {
        fontSize: 14,
        fontWeight: '500',
      },
    },
    SizeAlias: {
      Spacing4xSmall: 4,
    },
  })),
  useMSBStyles: jest.fn(() => ({
    styles: {
      container: {},
      txtTitle: {
        fontSize: 12,
        fontWeight: 'normal',
        color: '#666666',
      },
      txtValue: {
        fontSize: 14,
        fontWeight: '500',
        color: '#000000',
        marginTop: 4,
      },
    },
  })),
}));

describe('BillItemInfo', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('rendering', () => {
    it('should render with title and value', () => {
      const props = {
        title: 'Account Number',
        value: '**********',
      };

      const {getByText} = render(<BillItemInfo {...props} />);

      expect(getByText('Account Number')).toBeTruthy();
      expect(getByText('**********')).toBeTruthy();
    });

    it('should render with empty title and value', () => {
      const props = {
        title: '',
        value: '',
      };

      const {getByText} = render(<BillItemInfo {...props} />);

      expect(getByText('')).toBeTruthy();
    });

    it('should render with custom style', () => {
      const customStyle = {
        backgroundColor: 'red',
        padding: 10,
      };

      const props = {
        title: 'Test Title',
        value: 'Test Value',
        style: customStyle,
      };

      const {container} = render(<BillItemInfo {...props} />);
      const containerElement = container.firstChild as HTMLElement;

      expect(containerElement.style.backgroundColor).toBe('red');
      expect(containerElement.style.padding).toBe('10px');
    });

    it('should render with long title and value', () => {
      const props = {
        title: 'This is a very long title that might wrap to multiple lines',
        value: 'This is a very long value that contains a lot of information and might also wrap',
      };

      const {getByText} = render(<BillItemInfo {...props} />);

      expect(getByText(props.title)).toBeTruthy();
      expect(getByText(props.value)).toBeTruthy();
    });

    it('should render with special characters in title and value', () => {
      const props = {
        title: 'Số tài khoản / Account #',
        value: '1234-5678-9012-3456',
      };

      const {getByText} = render(<BillItemInfo {...props} />);

      expect(getByText('Số tài khoản / Account #')).toBeTruthy();
      expect(getByText('1234-5678-9012-3456')).toBeTruthy();
    });

    it('should render with numeric values', () => {
      const props = {
        title: 'Amount',
        value: '1,000,000 VND',
      };

      const {getByText} = render(<BillItemInfo {...props} />);

      expect(getByText('Amount')).toBeTruthy();
      expect(getByText('1,000,000 VND')).toBeTruthy();
    });
  });

  describe('styling', () => {
    it('should apply correct styles to title text', () => {
      const props = {
        title: 'Test Title',
        value: 'Test Value',
      };

      const {getByText} = render(<BillItemInfo {...props} />);
      const titleElement = getByText('Test Title');

      expect(titleElement.style.fontSize).toBe('12px');
      expect(titleElement.style.fontWeight).toBe('normal');
      expect(titleElement.style.color).toBe('#666666');
    });

    it('should apply correct styles to value text', () => {
      const props = {
        title: 'Test Title',
        value: 'Test Value',
      };

      const {getByText} = render(<BillItemInfo {...props} />);
      const valueElement = getByText('Test Value');

      expect(valueElement.style.fontSize).toBe('14px');
      expect(valueElement.style.fontWeight).toBe('500');
      expect(valueElement.style.color).toBe('#000000');
      expect(valueElement.style.marginTop).toBe('4px');
    });

    it('should merge custom style with default container style', () => {
      const customStyle = {
        marginBottom: 20,
        borderWidth: 1,
      };

      const props = {
        title: 'Test Title',
        value: 'Test Value',
        style: customStyle,
      };

      const {container} = render(<BillItemInfo {...props} />);
      const containerElement = container.firstChild as HTMLElement;

      expect(containerElement.style.marginBottom).toBe('20px');
      expect(containerElement.style.borderWidth).toBe('1px');
    });

    it('should handle array of styles', () => {
      const styles = [
        {backgroundColor: 'blue'},
        {padding: 15},
        {margin: 10},
      ];

      const props = {
        title: 'Test Title',
        value: 'Test Value',
        style: styles,
      };

      const {container} = render(<BillItemInfo {...props} />);
      const containerElement = container.firstChild as HTMLElement;

      expect(containerElement.style.backgroundColor).toBe('blue');
      expect(containerElement.style.padding).toBe('15px');
      expect(containerElement.style.margin).toBe('10px');
    });
  });

  describe('component structure', () => {
    it('should have correct component hierarchy', () => {
      const props = {
        title: 'Test Title',
        value: 'Test Value',
      };

      const {container, getByText} = render(<BillItemInfo {...props} />);
      const containerElement = container.firstChild as HTMLElement;
      const titleElement = getByText('Test Title');
      const valueElement = getByText('Test Value');

      expect(containerElement).toContainElement(titleElement);
      expect(containerElement).toContainElement(valueElement);
    });

    it('should render title before value', () => {
      const props = {
        title: 'Test Title',
        value: 'Test Value',
      };

      const {container} = render(<BillItemInfo {...props} />);
      const containerElement = container.firstChild as HTMLElement;
      const children = Array.from(containerElement.children);

      expect(children[0].textContent).toBe('Test Title');
      expect(children[1].textContent).toBe('Test Value');
    });
  });

  describe('edge cases', () => {
    it('should handle undefined title and value', () => {
      const props = {
        title: undefined as any,
        value: undefined as any,
      };

      expect(() => render(<BillItemInfo {...props} />)).not.toThrow();
    });

    it('should handle null title and value', () => {
      const props = {
        title: null as any,
        value: null as any,
      };

      expect(() => render(<BillItemInfo {...props} />)).not.toThrow();
    });

    it('should handle boolean values', () => {
      const props = {
        title: 'Status',
        value: 'true',
      };

      const {getByText} = render(<BillItemInfo {...props} />);

      expect(getByText('Status')).toBeTruthy();
      expect(getByText('true')).toBeTruthy();
    });

    it('should handle zero values', () => {
      const props = {
        title: 'Balance',
        value: '0',
      };

      const {getByText} = render(<BillItemInfo {...props} />);

      expect(getByText('Balance')).toBeTruthy();
      expect(getByText('0')).toBeTruthy();
    });

    it('should handle whitespace-only values', () => {
      const props = {
        title: '   ',
        value: '   ',
      };

      const {getByText} = render(<BillItemInfo {...props} />);

      expect(getByText('   ')).toBeTruthy();
    });
  });

  describe('accessibility', () => {
    it('should be accessible with screen readers', () => {
      const props = {
        title: 'Account Number',
        value: '**********',
      };

      const {getByText} = render(<BillItemInfo {...props} />);

      const titleElement = getByText('Account Number');
      const valueElement = getByText('**********');

      expect(titleElement).toBeTruthy();
      expect(valueElement).toBeTruthy();
    });

    it('should support custom accessibility props', () => {
      const props = {
        title: 'Account Number',
        value: '**********',
        accessibilityLabel: 'Account number is **********',
      };

      const {container} = render(<BillItemInfo {...props} />);
      const containerElement = container.firstChild as HTMLElement;

      expect(containerElement).toHaveProperty('accessibilityLabel', 'Account number is **********');
    });
  });

  describe('performance', () => {
    it('should handle frequent re-renders efficiently', () => {
      const initialProps = {
        title: 'Initial Title',
        value: 'Initial Value',
      };

      const {rerender} = render(<BillItemInfo {...initialProps} />);

      const startTime = performance.now();
      
      // Simulate frequent re-renders
      for (let i = 0; i < 100; i++) {
        rerender(<BillItemInfo title={`Title ${i}`} value={`Value ${i}`} />);
      }
      
      const endTime = performance.now();

      expect(endTime - startTime).toBeLessThan(100); // Should complete quickly
    });

    it('should not cause memory leaks with style changes', () => {
      const {rerender} = render(<BillItemInfo title="Title" value="Value" />);

      // Simulate frequent style changes
      for (let i = 0; i < 50; i++) {
        rerender(
          <BillItemInfo
            title="Title"
            value="Value"
            style={{backgroundColor: `rgb(${i}, ${i}, ${i})`}}
          />
        );
      }

      // Should not throw or cause performance issues
      expect(true).toBe(true);
    });
  });

  describe('real-world scenarios', () => {
    it('should display payment information correctly', () => {
      const props = {
        title: 'Merchant Name',
        value: 'ABC Electronics Store',
      };

      const {getByText} = render(<BillItemInfo {...props} />);

      expect(getByText('Merchant Name')).toBeTruthy();
      expect(getByText('ABC Electronics Store')).toBeTruthy();
    });

    it('should display account information correctly', () => {
      const props = {
        title: 'Account Number',
        value: '****************',
      };

      const {getByText} = render(<BillItemInfo {...props} />);

      expect(getByText('Account Number')).toBeTruthy();
      expect(getByText('****************')).toBeTruthy();
    });

    it('should display amount information correctly', () => {
      const props = {
        title: 'Amount',
        value: '1,500,000 VND',
      };

      const {getByText} = render(<BillItemInfo {...props} />);

      expect(getByText('Amount')).toBeTruthy();
      expect(getByText('1,500,000 VND')).toBeTruthy();
    });

    it('should display date information correctly', () => {
      const props = {
        title: 'Transaction Date',
        value: '15/03/2024 14:30:25',
      };

      const {getByText} = render(<BillItemInfo {...props} />);

      expect(getByText('Transaction Date')).toBeTruthy();
      expect(getByText('15/03/2024 14:30:25')).toBeTruthy();
    });

    it('should display status information correctly', () => {
      const props = {
        title: 'Status',
        value: 'Completed',
      };

      const {getByText} = render(<BillItemInfo {...props} />);

      expect(getByText('Status')).toBeTruthy();
      expect(getByText('Completed')).toBeTruthy();
    });
  });

  describe('integration scenarios', () => {
    it('should work correctly in a list of bill items', () => {
      const billItems = [
        {title: 'Merchant', value: 'ABC Store'},
        {title: 'Amount', value: '100,000 VND'},
        {title: 'Date', value: '15/03/2024'},
      ];

      const {getByText} = render(
        <div>
          {billItems.map((item, index) => (
            <BillItemInfo key={index} title={item.title} value={item.value} />
          ))}
        </div>
      );

      billItems.forEach(item => {
        expect(getByText(item.title)).toBeTruthy();
        expect(getByText(item.value)).toBeTruthy();
      });
    });

    it('should maintain component interface contract', () => {
      const requiredProps = {
        title: 'Test Title',
        value: 'Test Value',
      };

      const optionalProps = {
        style: {backgroundColor: 'blue'},
      };

      const {getByText, container} = render(<BillItemInfo {...requiredProps} {...optionalProps} />);

      expect(getByText('Test Title')).toBeTruthy();
      expect(getByText('Test Value')).toBeTruthy();
      
      const containerElement = container.firstChild as HTMLElement;
      expect(containerElement.style.backgroundColor).toBe('blue');
    });

    it('should handle dynamic content updates', () => {
      const {rerender, getByText} = render(<BillItemInfo title="Initial" value="Initial" />);

      expect(getByText('Initial')).toBeTruthy();

      rerender(<BillItemInfo title="Updated Title" value="Updated Value" />);

      expect(getByText('Updated Title')).toBeTruthy();
      expect(getByText('Updated Value')).toBeTruthy();
    });
  });
});
