import React from 'react';
import {describe, it, expect, jest, beforeEach} from '@jest/globals';
import {render, fireEvent} from '@testing-library/react-native';
import {ProviderItem} from '../ProviderItem';

// Mock dependencies
jest.mock('react-native', () => ({
  View: ({children, style, testID}: any) => (
    <div testID={testID} style={style}>
      {children}
    </div>
  ),
}));

jest.mock('../../highlight-text', () => {
  return ({text, search, style}: any) => (
    <span style={style} data-search={search}>
      {text}
    </span>
  );
});

jest.mock('msb-shared-component', () => ({
  MSBTouchable: ({children, onPress, style, testID}: any) => (
    <button testID={testID} style={style} onClick={onPress}>
      {children}
    </button>
  ),
  MSBIcon: ({folderIcon, icon, iconSize, iconColor, styleContainer}: any) => (
    <span style={styleContainer} data-folder={folderIcon} data-icon={icon} data-size={iconSize} data-color={iconColor}>
      {icon}
    </span>
  ),
  MSBFastImage: ({nameImage, style, folder}: any) => (
    <img style={style} src={`${folder}/${nameImage}`} alt={nameImage} />
  ),
  MSBFolderImage: {
    LOGO_TOPUP: 'logo-topup',
    LOGO_BILLING: 'logo-billing',
    ICON_SVG: 'icon-svg',
  },
  MSBIconSize: {
    SIZE_40: 40,
  },
  MSBIcons: {
    IconCheck: 'check',
  },
  ColorItem: {
    IconBrand: '#007AFF',
  },
  SizeGlobal: {
    Size800: 800,
  },
  createMSBStyleSheet: jest.fn(styleFunction =>
    styleFunction({
      Typography: {
        base_regular: {
          fontSize: 14,
          fontWeight: 'normal',
        },
        base_semiBold: {
          fontSize: 14,
          fontWeight: '600',
        },
        caption_regular: {
          fontSize: 12,
          fontWeight: 'normal',
        },
      },
      SizeAlias: {
        SpacingXSmall: 8,
        SpacingSmall: 12,
        Spacing4xSmall: 4,
      },
      SizeGlobal: {
        Size800: 800,
      },
      ColorDataView: {
        TextSub: '#666666',
      },
      ColorAlias: {
        TextInformation: '#000000',
        TextSuccess: '#00AA00',
      },
      ColorGlobal: {
        Neutral100: '#F0F0F0',
        NeutralWhite: '#FFFFFF',
      },
    }),
  ),
  useMSBStyles: jest.fn(() => ({
    styles: {
      itemContainerV1: {
        alignItems: 'center',
        flexDirection: 'column',
        paddingHorizontal: 8,
      },
      itemContainerV2: {
        alignItems: 'center',
        flexDirection: 'row',
        height: 72,
        padding: 4,
      },
      line: {
        backgroundColor: '#F0F0F0',
        height: 1,
        paddingHorizontal: 8,
        width: '100%',
      },
      logo: {
        height: 800,
        marginRight: 12,
        resizeMode: 'contain',
        width: 800,
      },
      shortName: {
        color: '#000000',
        fontSize: 14,
        fontWeight: '600',
      },
      shortNameCitad: {
        alignItems: 'center',
        flexDirection: 'row',
      },
      textContainer: {
        flex: 1,
      },
    },
    theme: {
      ColorItem: {
        IconBrand: '#007AFF',
      },
      SizeGlobal: {
        Size800: 800,
      },
    },
  })),
  getSize: jest.fn((size: number) => size),
}));

// Mock ProviderModel
const mockProvider = {
  serviceCode: 'VCB_TOPUP',
  subGroupId: 74,
  description: 'Vietcombank Topup',
  subgroupNameVn: 'Nạp tiền điện thoại Vietcombank',
  getName: () => 'VCB Topup',
  isTopup: () => true,
};

const mockBillingProvider = {
  serviceCode: 'EVN_BILLING',
  subGroupId: 123,
  description: 'EVN Electricity Bill',
  subgroupNameVn: 'Hóa đơn điện EVN',
  getName: () => 'EVN Electric',
  isTopup: () => false,
};

describe('ProviderItem', () => {
  const defaultProps = {
    item: mockProvider,
    highlight: '',
    onClick: jest.fn(),
    defaultValue: null,
    index: 0,
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('rendering', () => {
    it('should render provider item with basic information', () => {
      const {getByText} = render(<ProviderItem {...defaultProps} />);

      expect(getByText('VCB Topup')).toBeTruthy();
    });

    it('should render topup provider with correct icon folder', () => {
      const {container} = render(<ProviderItem {...defaultProps} />);
      const icon = container.querySelector('[data-folder="logo-topup"]');

      expect(icon).toBeTruthy();
      expect(icon?.getAttribute('data-icon')).toBe('74');
      expect(icon?.getAttribute('data-size')).toBe('40');
    });

    it('should render billing provider with correct icon folder', () => {
      const props = {
        ...defaultProps,
        item: mockBillingProvider,
      };

      const {container} = render(<ProviderItem {...props} />);
      const icon = container.querySelector('[data-folder="logo-billing"]');

      expect(icon).toBeTruthy();
      expect(icon?.getAttribute('data-icon')).toBe('123');
    });

    it('should render check icon for selected provider', () => {
      const props = {
        ...defaultProps,
        defaultValue: mockProvider,
      };

      const {container} = render(<ProviderItem {...props} />);
      const checkIcon = container.querySelector('[data-icon="check"]');

      expect(checkIcon).toBeTruthy();
      expect(checkIcon?.getAttribute('data-color')).toBe('#007AFF');
    });

    it('should not render check icon for non-selected provider', () => {
      const props = {
        ...defaultProps,
        defaultValue: mockBillingProvider,
      };

      const {container} = render(<ProviderItem {...props} />);
      const checkIcon = container.querySelector('[data-icon="check"]');

      expect(checkIcon).toBeFalsy();
    });

    it('should render highlight text with search term', () => {
      const props = {
        ...defaultProps,
        highlight: 'VCB',
      };

      const {container} = render(<ProviderItem {...props} />);
      const highlightText = container.querySelector('[data-search="VCB"]');

      expect(highlightText).toBeTruthy();
      expect(highlightText?.textContent).toBe('VCB Topup');
    });

    it('should render separator line', () => {
      const {container} = render(<ProviderItem {...defaultProps} />);
      const line = container.querySelector('[style*="background-color: rgb(240, 240, 240)"]');

      expect(line).toBeTruthy();
    });
  });

  describe('interaction', () => {
    it('should call onClick when provider is pressed', () => {
      const onClick = jest.fn();
      const props = {
        ...defaultProps,
        onClick,
      };

      const {getByTestId} = render(<ProviderItem {...props} />);
      const button = getByTestId('transfer.beneficiaryScreen.pressBank.0}');

      fireEvent.click(button);

      expect(onClick).toHaveBeenCalledWith(mockProvider);
    });

    it('should not throw error when onClick is undefined', () => {
      const props = {
        ...defaultProps,
        onClick: undefined,
      };

      const {getByTestId} = render(<ProviderItem {...props} />);
      const button = getByTestId('transfer.beneficiaryScreen.pressBank.0}');

      expect(() => fireEvent.click(button)).not.toThrow();
    });

    it('should use correct testID with index', () => {
      const props = {
        ...defaultProps,
        index: 5,
      };

      const {getByTestId} = render(<ProviderItem {...props} />);

      expect(getByTestId('transfer.beneficiaryScreen.pressBank.5}')).toBeTruthy();
    });
  });

  describe('provider types', () => {
    it('should handle topup provider correctly', () => {
      const {container} = render(<ProviderItem {...defaultProps} />);
      const icon = container.querySelector('[data-folder="logo-topup"]');

      expect(icon).toBeTruthy();
    });

    it('should handle billing provider correctly', () => {
      const props = {
        ...defaultProps,
        item: mockBillingProvider,
      };

      const {container} = render(<ProviderItem {...props} />);
      const icon = container.querySelector('[data-folder="logo-billing"]');

      expect(icon).toBeTruthy();
    });
  });

  describe('edge cases', () => {
    it('should handle provider with missing subGroupId', () => {
      const incompleteProvider = {
        ...mockProvider,
        subGroupId: undefined,
      };

      const props = {
        ...defaultProps,
        item: incompleteProvider,
      };

      const {container} = render(<ProviderItem {...props} />);
      const icon = container.querySelector('[data-icon=""]');

      expect(icon).toBeTruthy();
    });

    it('should handle provider with null subGroupId', () => {
      const incompleteProvider = {
        ...mockProvider,
        subGroupId: null,
      };

      const props = {
        ...defaultProps,
        item: incompleteProvider,
      };

      expect(() => render(<ProviderItem {...props} />)).not.toThrow();
    });

    it('should handle empty provider name', () => {
      const emptyNameProvider = {
        ...mockProvider,
        getName: () => '',
      };

      const props = {
        ...defaultProps,
        item: emptyNameProvider,
      };

      const {getByText} = render(<ProviderItem {...props} />);

      expect(getByText('')).toBeTruthy();
    });

    it('should handle undefined defaultValue', () => {
      const props = {
        ...defaultProps,
        defaultValue: undefined,
      };

      expect(() => render(<ProviderItem {...props} />)).not.toThrow();
    });

    it('should handle null defaultValue', () => {
      const props = {
        ...defaultProps,
        defaultValue: null,
      };

      expect(() => render(<ProviderItem {...props} />)).not.toThrow();
    });
  });

  describe('accessibility', () => {
    it('should have accessible testID', () => {
      const {getByTestId} = render(<ProviderItem {...defaultProps} />);

      expect(getByTestId('transfer.beneficiaryScreen.pressBank.0}')).toBeTruthy();
    });

    it('should be clickable', () => {
      const {getByRole} = render(<ProviderItem {...defaultProps} />);

      expect(getByRole('button')).toBeTruthy();
    });
  });

  describe('real-world scenarios', () => {
    it('should display Viettel topup provider correctly', () => {
      const viettelProvider = {
        serviceCode: 'VIETTEL_TOPUP',
        subGroupId: 1,
        description: 'Viettel Mobile Topup',
        subgroupNameVn: 'Nạp tiền điện thoại Viettel',
        getName: () => 'Viettel',
        isTopup: () => true,
      };

      const props = {
        ...defaultProps,
        item: viettelProvider,
        highlight: 'Viettel',
      };

      const {getByText, container} = render(<ProviderItem {...props} />);

      expect(getByText('Viettel')).toBeTruthy();
      expect(container.querySelector('[data-folder="logo-topup"]')).toBeTruthy();
      expect(container.querySelector('[data-search="Viettel"]')).toBeTruthy();
    });

    it('should display EVN billing provider correctly', () => {
      const evnProvider = {
        serviceCode: 'EVN_BILLING',
        subGroupId: 200,
        description: 'EVN Electricity Bill Payment',
        subgroupNameVn: 'Thanh toán hóa đơn điện EVN',
        getName: () => 'EVN Electricity',
        isTopup: () => false,
      };

      const props = {
        ...defaultProps,
        item: evnProvider,
        highlight: 'EVN',
      };

      const {getByText, container} = render(<ProviderItem {...props} />);

      expect(getByText('EVN Electricity')).toBeTruthy();
      expect(container.querySelector('[data-folder="logo-billing"]')).toBeTruthy();
      expect(container.querySelector('[data-search="EVN"]')).toBeTruthy();
    });

    it('should show selected state for current provider', () => {
      const selectedProvider = {
        serviceCode: 'SELECTED_PROVIDER',
        subGroupId: 999,
        description: 'Selected Provider',
        subgroupNameVn: 'Nhà cung cấp đã chọn',
        getName: () => 'Selected',
        isTopup: () => true,
      };

      const props = {
        ...defaultProps,
        item: selectedProvider,
        defaultValue: selectedProvider,
      };

      const {container} = render(<ProviderItem {...props} />);
      const checkIcon = container.querySelector('[data-icon="check"]');

      expect(checkIcon).toBeTruthy();
      expect(checkIcon?.getAttribute('data-color')).toBe('#007AFF');
    });
  });
});
