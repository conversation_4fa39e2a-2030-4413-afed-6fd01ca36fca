import React from 'react';
import {describe, it, expect, jest, beforeEach} from '@jest/globals';
import {render, fireEvent} from '@testing-library/react-native';
import ProviderList from '../ProviderList';

// Mock dependencies
jest.mock('react-native', () => ({
  View: ({children, style, testID}: any) => (
    <div testID={testID} style={style}>
      {children}
    </div>
  ),
  StyleProp: {},
  ScrollView: ({children}: any) => <div>{children}</div>,
}));

jest.mock('react-native-gesture-handler', () => ({
  FlatList: ({data, renderItem, keyExtractor, testID}: any) => (
    <div testID={testID}>
      {data?.map((item: any, index: number) => (
        <div key={keyExtractor ? keyExtractor(item) : index}>{renderItem({item, index})}</div>
      ))}
    </div>
  ),
}));

jest.mock('@gorhom/bottom-sheet', () => ({
  BottomSheetView: ({children, style}: any) => <div style={style}>{children}</div>,
}));

jest.mock('../../../utils/DimensionUtils', () => ({
  getWindowHeight: jest.fn(() => 800),
}));

jest.mock('../../../utils/Utils', () => ({
  isEmpty: jest.fn((value: any) => !value || value.length === 0),
}));

jest.mock('../../../locales/i18n', () => ({
  translate: jest.fn((key: string) => {
    const translations: {[key: string]: string} = {
      'components.providerList.searchPlaceholder': 'Tìm kiếm nhà cung cấp',
      'components.providerList.noResultsTitle': 'Không tìm thấy kết quả',
      'components.providerList.noResultsMessage': 'Vui lòng thử lại với từ khóa khác',
      'components.providerList.systemErrorTitle': 'Lỗi hệ thống',
      'components.providerList.systemErrorMessage': 'Không thể tải danh sách nhà cung cấp',
    };
    return translations[key] || key;
  }),
}));

jest.mock('./ProviderItem', () => ({
  ProviderItem: ({item, highlight, onClick, defaultValue, index}: any) => (
    <button
      testID={`provider-item-${index}`}
      onClick={() => onClick && onClick(item)}
      data-highlight={highlight}
      data-selected={item.serviceCode === defaultValue?.serviceCode}>
      {item.getName()}
    </button>
  ),
}));

jest.mock('msb-shared-component', () => ({
  MSBSearchInput: ({
    placeholder,
    value,
    onChangeText,
    testID,
    maxLength,
    isInputBottomSheet,
    containerSearchStyle,
  }: any) => (
    <input
      testID={testID}
      placeholder={placeholder}
      value={value}
      onChange={e => onChangeText && onChangeText(e.target.value)}
      maxLength={maxLength}
      data-bottom-sheet={isInputBottomSheet}
      style={containerSearchStyle}
    />
  ),
  MSBTextBase: ({content, style}: any) => <span style={style}>{content}</span>,
  MSBFastImage: ({nameImage, style, folder}: any) => (
    <img style={style} src={`${folder}/${nameImage}`} alt={nameImage} />
  ),
  MSBFolderImage: {
    IMAGES: 'images',
  },
  createMSBStyleSheet: jest.fn(styleFunction =>
    styleFunction({
      Typography: {
        base_semiBold: {
          fontSize: 14,
          fontWeight: '600',
        },
        small_regular: {
          fontSize: 12,
          fontWeight: 'normal',
        },
      },
      SizeAlias: {
        SpacingSmall: 12,
        SpacingXMSmall: 6,
        SpacingMedium: 16,
      },
      SizeGlobal: {
        Size1200: 1200,
        Size600: 600,
        Size2880: 2880,
      },
      ColorGlobal: {
        Neutral100: '#F0F0F0',
        NeutralWhite: '#FFFFFF',
      },
      ColorAlias: {
        BorderDefault: '#E0E0E0',
      },
      ColorField: {
        IconDefault: '#666666',
      },
      ColorDataView: {
        TextMain: '#000000',
      },
    }),
  ),
  useMSBStyles: jest.fn(() => ({
    styles: {
      containerSearchInput: {
        paddingHorizontal: 12,
        paddingVertical: 6,
      },
      containerEmpty: {
        alignItems: 'center',
        backgroundColor: '#FFFFFF',
        flexDirection: 'column',
        flex: 1,
        justifyContent: 'center',
        width: '100%',
      },
      image: {
        height: 2880,
        marginBottom: 16,
        width: 2880,
      },
      textAlign: {
        textAlign: 'center',
      },
    },
    theme: {
      Typography: {
        base_semiBold: {
          fontSize: 14,
          fontWeight: '600',
        },
        small_regular: {
          fontSize: 12,
          fontWeight: 'normal',
        },
      },
      ColorAlias: {
        BorderDefault: '#E0E0E0',
      },
    },
  })),
}));

// Mock provider data
const mockProviders = [
  {
    serviceCode: 'VCB_TOPUP',
    subGroupId: 74,
    description: 'Vietcombank Topup',
    subgroupNameVn: 'Nạp tiền điện thoại Vietcombank',
    getName: () => 'VCB Topup',
    isTopup: () => true,
  },
  {
    serviceCode: 'VIETTEL_TOPUP',
    subGroupId: 1,
    description: 'Viettel Mobile Topup',
    subgroupNameVn: 'Nạp tiền điện thoại Viettel',
    getName: () => 'Viettel',
    isTopup: () => true,
  },
  {
    serviceCode: 'EVN_BILLING',
    subGroupId: 200,
    description: 'EVN Electricity Bill',
    subgroupNameVn: 'Hóa đơn điện EVN',
    getName: () => 'EVN Electric',
    isTopup: () => false,
  },
];

describe('ProviderList', () => {
  const defaultProps = {
    list: mockProviders,
    onClick: jest.fn(),
    defaultValue: null,
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('rendering', () => {
    it('should render provider list with search input', () => {
      const {getByTestId, getByPlaceholderText} = render(<ProviderList {...defaultProps} />);

      expect(getByTestId('transfer.beneficiaryScreen.searchBank')).toBeTruthy();
      expect(getByPlaceholderText('Tìm kiếm nhà cung cấp')).toBeTruthy();
    });

    it('should render all providers when no search term', () => {
      const {getByText} = render(<ProviderList {...defaultProps} />);

      expect(getByText('VCB Topup')).toBeTruthy();
      expect(getByText('Viettel')).toBeTruthy();
      expect(getByText('EVN Electric')).toBeTruthy();
    });

    it('should render empty state when list is empty', () => {
      const props = {
        ...defaultProps,
        list: [],
      };

      const {getByText} = render(<ProviderList {...props} />);

      expect(getByText('Lỗi hệ thống')).toBeTruthy();
      expect(getByText('Không thể tải danh sách nhà cung cấp')).toBeTruthy();
    });

    it('should render system error when list is undefined', () => {
      const props = {
        ...defaultProps,
        list: undefined as any,
      };

      const {getByText} = render(<ProviderList {...props} />);

      expect(getByText('Lỗi hệ thống')).toBeTruthy();
    });

    it('should render selected provider with check mark', () => {
      const props = {
        ...defaultProps,
        defaultValue: mockProviders[0],
      };

      const {container} = render(<ProviderList {...props} />);
      const selectedItem = container.querySelector('[data-selected="true"]');

      expect(selectedItem).toBeTruthy();
    });
  });

  describe('search functionality', () => {
    it('should filter providers by description', () => {
      const {getByTestId, getByText, queryByText} = render(<ProviderList {...defaultProps} />);
      const searchInput = getByTestId('transfer.beneficiaryScreen.searchBank');

      fireEvent.change(searchInput, {target: {value: 'Vietcombank'}});

      expect(getByText('VCB Topup')).toBeTruthy();
      expect(queryByText('Viettel')).toBeFalsy();
      expect(queryByText('EVN Electric')).toBeFalsy();
    });

    it('should filter providers by Vietnamese name', () => {
      const {getByTestId, getByText, queryByText} = render(<ProviderList {...defaultProps} />);
      const searchInput = getByTestId('transfer.beneficiaryScreen.searchBank');

      fireEvent.change(searchInput, {target: {value: 'điện thoại'}});

      expect(getByText('VCB Topup')).toBeTruthy();
      expect(getByText('Viettel')).toBeTruthy();
      expect(queryByText('EVN Electric')).toBeFalsy();
    });

    it('should show no results when search has no matches', () => {
      const {getByTestId, getByText} = render(<ProviderList {...defaultProps} />);
      const searchInput = getByTestId('transfer.beneficiaryScreen.searchBank');

      fireEvent.change(searchInput, {target: {value: 'nonexistent'}});

      expect(getByText('Không tìm thấy kết quả')).toBeTruthy();
      expect(getByText('Vui lòng thử lại với từ khóa khác')).toBeTruthy();
    });

    it('should be case insensitive', () => {
      const {getByTestId, getByText} = render(<ProviderList {...defaultProps} />);
      const searchInput = getByTestId('transfer.beneficiaryScreen.searchBank');

      fireEvent.change(searchInput, {target: {value: 'VIETTEL'}});

      expect(getByText('Viettel')).toBeTruthy();
    });

    it('should handle search with leading/trailing spaces', () => {
      const {getByTestId, getByText} = render(<ProviderList {...defaultProps} />);
      const searchInput = getByTestId('transfer.beneficiaryScreen.searchBank');

      fireEvent.change(searchInput, {target: {value: '  Viettel  '}});

      expect(getByText('Viettel')).toBeTruthy();
    });

    it('should pass search term as highlight to provider items', () => {
      const {getByTestId, container} = render(<ProviderList {...defaultProps} />);
      const searchInput = getByTestId('transfer.beneficiaryScreen.searchBank');

      fireEvent.change(searchInput, {target: {value: 'Viettel'}});

      const highlightedItem = container.querySelector('[data-highlight="Viettel"]');
      expect(highlightedItem).toBeTruthy();
    });
  });

  describe('provider interaction', () => {
    it('should call onClick when provider is selected', () => {
      const onClick = jest.fn();
      const props = {
        ...defaultProps,
        onClick,
      };

      const {getByTestId} = render(<ProviderList {...props} />);
      const providerButton = getByTestId('provider-item-0');

      fireEvent.click(providerButton);

      expect(onClick).toHaveBeenCalledWith(mockProviders[0]);
    });

    it('should not throw error when onClick is undefined', () => {
      const props = {
        ...defaultProps,
        onClick: undefined,
      };

      const {getByTestId} = render(<ProviderList {...props} />);
      const providerButton = getByTestId('provider-item-0');

      expect(() => fireEvent.click(providerButton)).not.toThrow();
    });
  });

  describe('edge cases', () => {
    it('should handle providers with missing description', () => {
      const incompleteProviders = [
        {
          serviceCode: 'INCOMPLETE',
          subGroupId: 999,
          description: undefined,
          subgroupNameVn: 'Test Provider',
          getName: () => 'Incomplete',
          isTopup: () => true,
        },
      ];

      const props = {
        ...defaultProps,
        list: incompleteProviders,
      };

      expect(() => render(<ProviderList {...props} />)).not.toThrow();
    });

    it('should handle providers with missing Vietnamese name', () => {
      const incompleteProviders = [
        {
          serviceCode: 'INCOMPLETE',
          subGroupId: 999,
          description: 'Test Provider',
          subgroupNameVn: undefined,
          getName: () => 'Incomplete',
          isTopup: () => true,
        },
      ];

      const props = {
        ...defaultProps,
        list: incompleteProviders,
      };

      expect(() => render(<ProviderList {...props} />)).not.toThrow();
    });

    it('should handle empty search term', () => {
      const {getByTestId, getByText} = render(<ProviderList {...defaultProps} />);
      const searchInput = getByTestId('transfer.beneficiaryScreen.searchBank');

      fireEvent.change(searchInput, {target: {value: ''}});

      expect(getByText('VCB Topup')).toBeTruthy();
      expect(getByText('Viettel')).toBeTruthy();
      expect(getByText('EVN Electric')).toBeTruthy();
    });

    it('should handle null list', () => {
      const props = {
        ...defaultProps,
        list: null as any,
      };

      const {getByText} = render(<ProviderList {...props} />);

      expect(getByText('Lỗi hệ thống')).toBeTruthy();
    });
  });

  describe('accessibility', () => {
    it('should have accessible search input', () => {
      const {getByTestId} = render(<ProviderList {...defaultProps} />);

      expect(getByTestId('transfer.beneficiaryScreen.searchBank')).toBeTruthy();
    });

    it('should have proper search input configuration', () => {
      const {getByTestId} = render(<ProviderList {...defaultProps} />);
      const searchInput = getByTestId('transfer.beneficiaryScreen.searchBank');

      expect(searchInput.getAttribute('maxLength')).toBe('255');
      expect(searchInput.getAttribute('data-bottom-sheet')).toBe('true');
    });
  });

  describe('real-world scenarios', () => {
    it('should handle typical mobile topup search', () => {
      const {getByTestId, getByText, queryByText} = render(<ProviderList {...defaultProps} />);
      const searchInput = getByTestId('transfer.beneficiaryScreen.searchBank');

      fireEvent.change(searchInput, {target: {value: 'nạp tiền'}});

      expect(getByText('VCB Topup')).toBeTruthy();
      expect(getByText('Viettel')).toBeTruthy();
      expect(queryByText('EVN Electric')).toBeFalsy();
    });

    it('should handle bill payment search', () => {
      const {getByTestId, getByText, queryByText} = render(<ProviderList {...defaultProps} />);
      const searchInput = getByTestId('transfer.beneficiaryScreen.searchBank');

      fireEvent.change(searchInput, {target: {value: 'hóa đơn'}});

      expect(getByText('EVN Electric')).toBeTruthy();
      expect(queryByText('VCB Topup')).toBeFalsy();
      expect(queryByText('Viettel')).toBeFalsy();
    });

    it('should handle provider selection workflow', () => {
      const onClick = jest.fn();
      const props = {
        ...defaultProps,
        onClick,
        defaultValue: null,
      };

      const {getByTestId, container} = render(<ProviderList {...props} />);

      // Search for provider
      const searchInput = getByTestId('transfer.beneficiaryScreen.searchBank');
      fireEvent.change(searchInput, {target: {value: 'Viettel'}});

      // Select provider
      const providerButton = getByTestId('provider-item-0');
      fireEvent.click(providerButton);

      expect(onClick).toHaveBeenCalledWith(mockProviders[1]); // Viettel is filtered result
    });
  });
});
