import React from 'react';
import {describe, it, expect, jest, beforeEach} from '@jest/globals';
import {render, fireEvent} from '@testing-library/react-native';
import {SwipeableRow} from '../SwipeableRow';

// Mock dependencies
jest.mock('react-native', () => ({
  StyleSheet: {
    create: jest.fn((styles) => styles),
  },
  View: ({children, style, pointerEvents, testID}: any) => (
    <div testID={testID} style={style} data-pointer-events={pointerEvents}>
      {children}
    </div>
  ),
}));

jest.mock('react-native-gesture-handler', () => ({
  Gesture: {
    Pan: jest.fn(() => ({
      minPointers: jest.fn().mockReturnThis(),
      maxPointers: jest.fn().mockReturnThis(),
      activeOffsetX: jest.fn().mockReturnThis(),
      hitSlop: jest.fn().mockReturnThis(),
      onStart: jest.fn().mockReturnThis(),
      onUpdate: jest.fn().mockReturnThis(),
      onEnd: jest.fn().mockReturnThis(),
      simultaneousWithExternalGesture: jest.fn().mockReturnThis(),
      requireExternalGestureToFail: jest.fn().mockReturnThis(),
    })),
    Native: jest.fn(() => ({})),
  },
  GestureDetector: ({children, gesture}: any) => (
    <div data-gesture="pan" data-testid="gesture-detector">
      {children}
    </div>
  ),
}));

jest.mock('react-native-reanimated', () => ({
  useSharedValue: jest.fn((initialValue: number) => ({
    value: initialValue,
  })),
  useAnimatedStyle: jest.fn((styleFunction: () => any) => styleFunction()),
  withSpring: jest.fn((value: number) => value),
  interpolate: jest.fn((value: number, inputRange: number[], outputRange: number[]) => {
    // Simple linear interpolation for testing
    const [inputMin, inputMax] = inputRange;
    const [outputMin, outputMax] = outputRange;
    const ratio = (value - inputMin) / (inputMax - inputMin);
    return outputMin + ratio * (outputMax - outputMin);
  }),
  default: {
    View: ({children, style, testID}: any) => (
      <div testID={testID} style={style} data-animated="true">
        {children}
      </div>
    ),
  },
}));

jest.mock('msb-shared-component', () => ({
  SizeGlobal: {
    Size1200: 1200,
  },
}));

describe('SwipeableRow', () => {
  const mockRenderRightActions = jest.fn((translateX) => (
    <div data-testid="right-actions" data-translate-x={translateX?.value || 0}>
      <button>Delete</button>
      <button>Edit</button>
    </div>
  ));

  const defaultProps = {
    children: <div data-testid="swipeable-content">Swipeable Content</div>,
    renderRightActions: mockRenderRightActions,
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('rendering', () => {
    it('should render children content', () => {
      const {getByTestId} = render(<SwipeableRow {...defaultProps} />);

      expect(getByTestId('swipeable-content')).toBeTruthy();
      expect(getByTestId('swipeable-content').textContent).toBe('Swipeable Content');
    });

    it('should render with gesture detector when swipe is enabled', () => {
      const {getByTestId} = render(<SwipeableRow {...defaultProps} />);

      expect(getByTestId('gesture-detector')).toBeTruthy();
      expect(getByTestId('gesture-detector').getAttribute('data-gesture')).toBe('pan');
    });

    it('should render without gesture detector when swipe is disabled', () => {
      const props = {
        ...defaultProps,
        disabledSwipe: true,
      };

      const {queryByTestId, container} = render(<SwipeableRow {...props} />);

      expect(queryByTestId('gesture-detector')).toBeFalsy();
      expect(container.querySelector('[data-pointer-events="box-none"]')).toBeTruthy();
    });

    it('should render right actions', () => {
      const {getByTestId} = render(<SwipeableRow {...defaultProps} />);

      expect(getByTestId('right-actions')).toBeTruthy();
      expect(mockRenderRightActions).toHaveBeenCalled();
    });

    it('should pass translateX to renderRightActions', () => {
      render(<SwipeableRow {...defaultProps} />);

      expect(mockRenderRightActions).toHaveBeenCalledWith(
        expect.objectContaining({
          value: expect.any(Number),
        })
      );
    });
  });

  describe('swipe functionality', () => {
    it('should initialize with translateX value of 0', () => {
      const {useSharedValue} = require('react-native-reanimated');
      
      render(<SwipeableRow {...defaultProps} />);

      expect(useSharedValue).toHaveBeenCalledWith(0);
    });

    it('should create pan gesture with correct configuration', () => {
      const {Gesture} = require('react-native-gesture-handler');
      const mockPanGesture = {
        minPointers: jest.fn().mockReturnThis(),
        maxPointers: jest.fn().mockReturnThis(),
        activeOffsetX: jest.fn().mockReturnThis(),
        hitSlop: jest.fn().mockReturnThis(),
        onStart: jest.fn().mockReturnThis(),
        onUpdate: jest.fn().mockReturnThis(),
        onEnd: jest.fn().mockReturnThis(),
        simultaneousWithExternalGesture: jest.fn().mockReturnThis(),
        requireExternalGestureToFail: jest.fn().mockReturnThis(),
      };

      Gesture.Pan.mockReturnValue(mockPanGesture);

      render(<SwipeableRow {...defaultProps} />);

      expect(mockPanGesture.minPointers).toHaveBeenCalledWith(1);
      expect(mockPanGesture.maxPointers).toHaveBeenCalledWith(1);
      expect(mockPanGesture.activeOffsetX).toHaveBeenCalledWith([-10, 10]);
      expect(mockPanGesture.hitSlop).toHaveBeenCalledWith({horizontal: -20});
    });

    it('should apply animated styles to content', () => {
      const {useAnimatedStyle} = require('react-native-reanimated');
      
      render(<SwipeableRow {...defaultProps} />);

      expect(useAnimatedStyle).toHaveBeenCalled();
    });

    it('should apply opacity animation to right actions', () => {
      const {useAnimatedStyle, interpolate} = require('react-native-reanimated');
      
      render(<SwipeableRow {...defaultProps} />);

      // Should be called twice - once for main content, once for right actions
      expect(useAnimatedStyle).toHaveBeenCalledTimes(2);
      expect(interpolate).toHaveBeenCalled();
    });
  });

  describe('disabled swipe state', () => {
    it('should not render gesture detector when disabled', () => {
      const props = {
        ...defaultProps,
        disabledSwipe: true,
      };

      const {queryByTestId} = render(<SwipeableRow {...props} />);

      expect(queryByTestId('gesture-detector')).toBeFalsy();
    });

    it('should still render content when disabled', () => {
      const props = {
        ...defaultProps,
        disabledSwipe: true,
      };

      const {getByTestId} = render(<SwipeableRow {...props} />);

      expect(getByTestId('swipeable-content')).toBeTruthy();
    });

    it('should still render right actions when disabled', () => {
      const props = {
        ...defaultProps,
        disabledSwipe: true,
      };

      const {getByTestId} = render(<SwipeableRow {...props} />);

      expect(getByTestId('right-actions')).toBeTruthy();
      expect(mockRenderRightActions).toHaveBeenCalled();
    });

    it('should set pointer events to box-none when disabled', () => {
      const props = {
        ...defaultProps,
        disabledSwipe: true,
      };

      const {container} = render(<SwipeableRow {...props} />);
      const containerElement = container.querySelector('[data-pointer-events="box-none"]');

      expect(containerElement).toBeTruthy();
    });
  });

  describe('gesture handling', () => {
    it('should handle gesture start event', () => {
      const {Gesture} = require('react-native-gesture-handler');
      let onStartCallback: (() => void) | undefined;

      const mockPanGesture = {
        minPointers: jest.fn().mockReturnThis(),
        maxPointers: jest.fn().mockReturnThis(),
        activeOffsetX: jest.fn().mockReturnThis(),
        hitSlop: jest.fn().mockReturnThis(),
        onStart: jest.fn((callback) => {
          onStartCallback = callback;
          return mockPanGesture;
        }),
        onUpdate: jest.fn().mockReturnThis(),
        onEnd: jest.fn().mockReturnThis(),
        simultaneousWithExternalGesture: jest.fn().mockReturnThis(),
        requireExternalGestureToFail: jest.fn().mockReturnThis(),
      };

      Gesture.Pan.mockReturnValue(mockPanGesture);

      render(<SwipeableRow {...defaultProps} />);

      expect(onStartCallback).toBeDefined();
      expect(() => onStartCallback?.()).not.toThrow();
    });

    it('should handle gesture update event', () => {
      const {Gesture} = require('react-native-gesture-handler');
      let onUpdateCallback: ((event: any) => void) | undefined;

      const mockPanGesture = {
        minPointers: jest.fn().mockReturnThis(),
        maxPointers: jest.fn().mockReturnThis(),
        activeOffsetX: jest.fn().mockReturnThis(),
        hitSlop: jest.fn().mockReturnThis(),
        onStart: jest.fn().mockReturnThis(),
        onUpdate: jest.fn((callback) => {
          onUpdateCallback = callback;
          return mockPanGesture;
        }),
        onEnd: jest.fn().mockReturnThis(),
        simultaneousWithExternalGesture: jest.fn().mockReturnThis(),
        requireExternalGestureToFail: jest.fn().mockReturnThis(),
      };

      Gesture.Pan.mockReturnValue(mockPanGesture);

      render(<SwipeableRow {...defaultProps} />);

      expect(onUpdateCallback).toBeDefined();
      expect(() => onUpdateCallback?.({translationX: -50})).not.toThrow();
    });

    it('should handle gesture end event', () => {
      const {Gesture} = require('react-native-gesture-handler');
      let onEndCallback: (() => void) | undefined;

      const mockPanGesture = {
        minPointers: jest.fn().mockReturnThis(),
        maxPointers: jest.fn().mockReturnThis(),
        activeOffsetX: jest.fn().mockReturnThis(),
        hitSlop: jest.fn().mockReturnThis(),
        onStart: jest.fn().mockReturnThis(),
        onUpdate: jest.fn().mockReturnThis(),
        onEnd: jest.fn((callback) => {
          onEndCallback = callback;
          return mockPanGesture;
        }),
        simultaneousWithExternalGesture: jest.fn().mockReturnThis(),
        requireExternalGestureToFail: jest.fn().mockReturnThis(),
      };

      Gesture.Pan.mockReturnValue(mockPanGesture);

      render(<SwipeableRow {...defaultProps} />);

      expect(onEndCallback).toBeDefined();
      expect(() => onEndCallback?.()).not.toThrow();
    });
  });

  describe('edge cases', () => {
    it('should handle undefined renderRightActions', () => {
      const props = {
        children: <div>Content</div>,
        renderRightActions: undefined as any,
      };

      expect(() => render(<SwipeableRow {...props} />)).not.toThrow();
    });

    it('should handle null children', () => {
      const props = {
        children: null,
        renderRightActions: mockRenderRightActions,
      };

      expect(() => render(<SwipeableRow {...props} />)).not.toThrow();
    });

    it('should handle empty children', () => {
      const props = {
        children: <></>,
        renderRightActions: mockRenderRightActions,
      };

      expect(() => render(<SwipeableRow {...props} />)).not.toThrow();
    });
  });

  describe('styling', () => {
    it('should apply container styles', () => {
      const {container} = render(<SwipeableRow {...defaultProps} />);
      const containerElement = container.querySelector('[style*="justify-content: center"]');

      expect(containerElement).toBeTruthy();
    });

    it('should apply row styles to right actions container', () => {
      const {container} = render(<SwipeableRow {...defaultProps} />);
      const rowElement = container.querySelector('[style*="position: absolute"]');

      expect(rowElement).toBeTruthy();
    });

    it('should apply animated styles', () => {
      const {container} = render(<SwipeableRow {...defaultProps} />);
      const animatedElements = container.querySelectorAll('[data-animated="true"]');

      expect(animatedElements.length).toBeGreaterThan(0);
    });
  });

  describe('accessibility', () => {
    it('should maintain accessibility of children', () => {
      const accessibleChild = (
        <button aria-label="Accessible Button">
          Accessible Content
        </button>
      );

      const props = {
        children: accessibleChild,
        renderRightActions: mockRenderRightActions,
      };

      const {getByLabelText} = render(<SwipeableRow {...props} />);

      expect(getByLabelText('Accessible Button')).toBeTruthy();
    });

    it('should maintain accessibility of right actions', () => {
      const accessibleRightActions = jest.fn(() => (
        <button aria-label="Delete Action">Delete</button>
      ));

      const props = {
        ...defaultProps,
        renderRightActions: accessibleRightActions,
      };

      const {getByLabelText} = render(<SwipeableRow {...props} />);

      expect(getByLabelText('Delete Action')).toBeTruthy();
    });
  });

  describe('real-world scenarios', () => {
    it('should handle list item with delete action', () => {
      const deleteAction = jest.fn(() => (
        <button data-testid="delete-button">Delete</button>
      ));

      const listItem = (
        <div data-testid="list-item">
          <span>Item Title</span>
          <span>Item Description</span>
        </div>
      );

      const props = {
        children: listItem,
        renderRightActions: deleteAction,
      };

      const {getByTestId} = render(<SwipeableRow {...props} />);

      expect(getByTestId('list-item')).toBeTruthy();
      expect(getByTestId('delete-button')).toBeTruthy();
    });

    it('should handle contact item with multiple actions', () => {
      const contactActions = jest.fn(() => (
        <div data-testid="contact-actions">
          <button data-testid="edit-contact">Edit</button>
          <button data-testid="delete-contact">Delete</button>
          <button data-testid="favorite-contact">Favorite</button>
        </div>
      ));

      const contactItem = (
        <div data-testid="contact-item">
          <img src="avatar.jpg" alt="Contact Avatar" />
          <div>
            <span>John Doe</span>
            <span><EMAIL></span>
          </div>
        </div>
      );

      const props = {
        children: contactItem,
        renderRightActions: contactActions,
      };

      const {getByTestId} = render(<SwipeableRow {...props} />);

      expect(getByTestId('contact-item')).toBeTruthy();
      expect(getByTestId('contact-actions')).toBeTruthy();
      expect(getByTestId('edit-contact')).toBeTruthy();
      expect(getByTestId('delete-contact')).toBeTruthy();
      expect(getByTestId('favorite-contact')).toBeTruthy();
    });

    it('should handle disabled swipe for read-only items', () => {
      const readOnlyItem = (
        <div data-testid="read-only-item">
          <span>Read Only Content</span>
        </div>
      );

      const props = {
        children: readOnlyItem,
        renderRightActions: mockRenderRightActions,
        disabledSwipe: true,
      };

      const {getByTestId, queryByTestId} = render(<SwipeableRow {...props} />);

      expect(getByTestId('read-only-item')).toBeTruthy();
      expect(queryByTestId('gesture-detector')).toBeFalsy();
    });
  });
});
