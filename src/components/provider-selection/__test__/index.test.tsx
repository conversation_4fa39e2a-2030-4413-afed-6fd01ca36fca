import React from 'react';
import {describe, it, expect, jest, beforeEach} from '@jest/globals';
import {render, fireEvent, waitFor} from '@testing-library/react-native';
import MSBProviderSelection from '../index';
import {ProviderModel} from '../../../domain/entities/provider-list/ProviderListModel';

// Mock dependencies
jest.mock('../../../di/DIContainer', () => ({
  DIContainer: {
    getInstance: jest.fn(() => ({
      getProviderListUseCase: () => ({
        execute: jest.fn(),
      }),
    })),
  },
}));

jest.mock('../../../locales/i18n', () => ({
  translate: jest.fn((key: string) => {
    const translations: Record<string, string> = {
      'paymentBill.labelSelecboxProvider': 'Select Provider',
      'common.error': 'Error',
      'common.retry': 'Retry',
    };
    return translations[key] || key;
  }),
}));

jest.mock('../../../utils/PopupUtils', () => ({
  showCommonPopup: jest.fn(),
}));

jest.mock('msb-host-shared-module', () => ({
  hostSharedModule: {
    d: {
      domainService: {
        showBottomSheet: jest.fn(),
        hideBottomSheet: jest.fn(),
      },
    },
  },
}));

jest.mock('msb-shared-component', () => ({
  MSBSelection: ({
    testID,
    disabled,
    label,
    onChange,
    childrenContent,
  }: any) => (
    <div
      testID={testID}
      data-disabled={disabled}
      data-label={label}
      onClick={onChange}
    >
      {childrenContent}
    </div>
  ),
  MSBFastImage: ({nameImage, style, folder, testID}: any) => (
    <img
      testID={testID || 'msb-fast-image'}
      src={nameImage}
      style={style}
      data-folder={folder}
      alt={nameImage}
    />
  ),
  MSBFolderImage: {
    LOGO_TOPUP: 'LOGO_TOPUP',
    LOGO_BILLING: 'LOGO_BILLING',
    ICON_SVG: 'ICON_SVG',
  },
  MSBLoadingItemSkeleton: ({loading}: any) => (
    <div testID="loading-skeleton" data-loading={loading}>
      Loading...
    </div>
  ),
  createMSBStyleSheet: jest.fn((styleFunction) => styleFunction({
    theme: {},
  })),
  useMSBStyles: jest.fn(() => ({
    styles: {
      bankContainer: {},
      bankItem: {},
      bankLogo: {},
      bankLoading: {},
      bankLoadingItem: {},
    },
    theme: {},
  })),
  memo: (component: any) => component,
}));

jest.mock('react-native', () => ({
  View: ({children, style, testID}: any) => (
    <div testID={testID} style={style}>
      {children}
    </div>
  ),
  FlatList: ({data, renderItem, keyExtractor, getItemLayout}: any) => (
    <div testID="flat-list">
      {data?.map((item: any, index: number) => (
        <div key={keyExtractor ? keyExtractor(item, index) : index}>
          {renderItem({item, index})}
        </div>
      ))}
    </div>
  ),
}));

jest.mock('react-native-gesture-handler', () => ({
  FlatList: ({data, renderItem, keyExtractor}: any) => (
    <div testID="gesture-flat-list">
      {data?.map((item: any, index: number) => (
        <div key={keyExtractor ? keyExtractor(item, index) : index}>
          {renderItem({item, index})}
        </div>
      ))}
    </div>
  ),
}));

jest.mock('../../../data/models/provider-list/ProviderListRequest', () => ({
  ProviderListRequest: jest.fn(),
}));

jest.mock('../../provider-list/ProviderList', () => ({
  __esModule: true,
  default: ({list, onClick, defaultValue}: any) => (
    <div testID="provider-list">
      {list?.map((provider: ProviderModel, index: number) => (
        <button
          key={index}
          testID={`provider-item-${provider.serviceCode}`}
          onClick={() => onClick(provider)}
          data-selected={defaultValue?.serviceCode === provider.serviceCode}
        >
          {provider.partnerName}
        </button>
      ))}
    </div>
  ),
}));

const {DIContainer} = require('../../../di/DIContainer');
const {showCommonPopup} = require('../../../utils/PopupUtils');
const {hostSharedModule} = require('msb-host-shared-module');

describe('MSBProviderSelection', () => {
  let mockGetProviderListUseCase: jest.MockedFunction<any>;
  let mockOnSelected: jest.MockedFunction<any>;
  let mockProviders: ProviderModel[];

  beforeEach(() => {
    jest.clearAllMocks();

    // Mock providers
    mockProviders = [
      new ProviderModel('VIETTEL', 'VT', 'Viettel', 'Viettel Telecom', true, false, 'viettel'),
      new ProviderModel('MOBIFONE', 'MF', 'MobiFone', 'MobiFone Corp', true, false, 'mobifone'),
      new ProviderModel('VINAPHONE', 'VN', 'VinaPhone', 'VinaPhone Corp', true, false, 'vinaphone'),
    ];

    // Mock use case
    mockGetProviderListUseCase = {
      execute: jest.fn().mockResolvedValue({
        status: 'SUCCESS',
        data: {providers: mockProviders},
      }),
    };

    // Mock DI container
    DIContainer.getInstance.mockReturnValue({
      getProviderListUseCase: () => mockGetProviderListUseCase,
    });

    // Mock callback
    mockOnSelected = jest.fn();
  });

  describe('rendering', () => {
    it('should render with default props', async () => {
      const {getByTestId} = render(
        <MSBProviderSelection code="UTILITY" onSelected={mockOnSelected} />
      );

      expect(getByTestId('payment.beneficiaryScreen.selectProviderBeneficiary')).toBeTruthy();
    });

    it('should render with disabled state', async () => {
      const {getByTestId} = render(
        <MSBProviderSelection code="UTILITY" onSelected={mockOnSelected} disabled={true} />
      );

      const selection = getByTestId('payment.beneficiaryScreen.selectProviderBeneficiary');
      expect(selection.getAttribute('data-disabled')).toBe('true');
    });

    it('should render with enabled state', async () => {
      const {getByTestId} = render(
        <MSBProviderSelection code="UTILITY" onSelected={mockOnSelected} disabled={false} />
      );

      const selection = getByTestId('payment.beneficiaryScreen.selectProviderBeneficiary');
      expect(selection.getAttribute('data-disabled')).toBe('false');
    });

    it('should render with correct label', async () => {
      const {getByTestId} = render(
        <MSBProviderSelection code="UTILITY" onSelected={mockOnSelected} />
      );

      const selection = getByTestId('payment.beneficiaryScreen.selectProviderBeneficiary');
      expect(selection.getAttribute('data-label')).toBe('Select Provider');
    });

    it('should render with default value', async () => {
      const defaultProvider = mockProviders[0];
      
      const {getByTestId} = render(
        <MSBProviderSelection
          code="UTILITY"
          onSelected={mockOnSelected}
          defaultValue={{providerSelected: defaultProvider}}
        />
      );

      await waitFor(() => {
        expect(getByTestId('msb-fast-image')).toBeTruthy();
      });
    });
  });

  describe('provider loading', () => {
    it('should load providers on mount', async () => {
      render(<MSBProviderSelection code="UTILITY" onSelected={mockOnSelected} />);

      await waitFor(() => {
        expect(mockGetProviderListUseCase.execute).toHaveBeenCalledTimes(1);
      });
    });

    it('should show loading state while fetching providers', async () => {
      // Mock delayed response
      mockGetProviderListUseCase.execute.mockReturnValue(
        new Promise(resolve => setTimeout(() => resolve({
          status: 'SUCCESS',
          data: {providers: mockProviders},
        }), 100))
      );

      const {getByTestId} = render(
        <MSBProviderSelection code="UTILITY" onSelected={mockOnSelected} />
      );

      // Click to open bottom sheet
      fireEvent.click(getByTestId('payment.beneficiaryScreen.selectProviderBeneficiary'));

      await waitFor(() => {
        expect(getByTestId('loading-skeleton')).toBeTruthy();
      });
    });

    it('should display providers after successful load', async () => {
      const {getByTestId} = render(
        <MSBProviderSelection code="UTILITY" onSelected={mockOnSelected} />
      );

      await waitFor(() => {
        expect(mockGetProviderListUseCase.execute).toHaveBeenCalled();
      });

      // Click to open bottom sheet
      fireEvent.click(getByTestId('payment.beneficiaryScreen.selectProviderBeneficiary'));

      await waitFor(() => {
        expect(getByTestId('provider-list')).toBeTruthy();
        expect(getByTestId('provider-item-VIETTEL')).toBeTruthy();
        expect(getByTestId('provider-item-MOBIFONE')).toBeTruthy();
        expect(getByTestId('provider-item-VINAPHONE')).toBeTruthy();
      });
    });

    it('should handle provider loading error', async () => {
      const mockError = new Error('Network error');
      mockGetProviderListUseCase.execute.mockResolvedValue({
        status: 'ERROR',
        error: mockError,
      });

      render(<MSBProviderSelection code="UTILITY" onSelected={mockOnSelected} />);

      await waitFor(() => {
        expect(showCommonPopup).toHaveBeenCalledWith(
          'Error',
          mockError.message,
          'Retry',
          expect.any(Function)
        );
      });
    });

    it('should retry loading on error retry', async () => {
      const mockError = new Error('Network error');
      mockGetProviderListUseCase.execute
        .mockResolvedValueOnce({
          status: 'ERROR',
          error: mockError,
        })
        .mockResolvedValueOnce({
          status: 'SUCCESS',
          data: {providers: mockProviders},
        });

      render(<MSBProviderSelection code="UTILITY" onSelected={mockOnSelected} />);

      await waitFor(() => {
        expect(showCommonPopup).toHaveBeenCalled();
      });

      // Simulate retry button click
      const retryCallback = (showCommonPopup as jest.Mock).mock.calls[0][3];
      retryCallback();

      await waitFor(() => {
        expect(mockGetProviderListUseCase.execute).toHaveBeenCalledTimes(2);
      });
    });
  });

  describe('provider selection', () => {
    it('should call onSelected when provider is selected', async () => {
      const {getByTestId} = render(
        <MSBProviderSelection code="UTILITY" onSelected={mockOnSelected} />
      );

      await waitFor(() => {
        expect(mockGetProviderListUseCase.execute).toHaveBeenCalled();
      });

      // Click to open bottom sheet
      fireEvent.click(getByTestId('payment.beneficiaryScreen.selectProviderBeneficiary'));

      await waitFor(() => {
        expect(getByTestId('provider-item-VIETTEL')).toBeTruthy();
      });

      // Select a provider
      fireEvent.click(getByTestId('provider-item-VIETTEL'));

      expect(mockOnSelected).toHaveBeenCalledWith(mockProviders[0]);
      expect(hostSharedModule.d.domainService?.hideBottomSheet).toHaveBeenCalled();
    });

    it('should update selected provider display', async () => {
      const {getByTestId} = render(
        <MSBProviderSelection code="UTILITY" onSelected={mockOnSelected} />
      );

      await waitFor(() => {
        expect(mockGetProviderListUseCase.execute).toHaveBeenCalled();
      });

      // Click to open bottom sheet
      fireEvent.click(getByTestId('payment.beneficiaryScreen.selectProviderBeneficiary'));

      await waitFor(() => {
        expect(getByTestId('provider-item-VIETTEL')).toBeTruthy();
      });

      // Select a provider
      fireEvent.click(getByTestId('provider-item-VIETTEL'));

      await waitFor(() => {
        expect(getByTestId('msb-fast-image')).toBeTruthy();
      });
    });

    it('should handle multiple provider selections', async () => {
      const {getByTestId} = render(
        <MSBProviderSelection code="UTILITY" onSelected={mockOnSelected} />
      );

      await waitFor(() => {
        expect(mockGetProviderListUseCase.execute).toHaveBeenCalled();
      });

      // Select first provider
      fireEvent.click(getByTestId('payment.beneficiaryScreen.selectProviderBeneficiary'));
      await waitFor(() => {
        expect(getByTestId('provider-item-VIETTEL')).toBeTruthy();
      });
      fireEvent.click(getByTestId('provider-item-VIETTEL'));

      // Select second provider
      fireEvent.click(getByTestId('payment.beneficiaryScreen.selectProviderBeneficiary'));
      await waitFor(() => {
        expect(getByTestId('provider-item-MOBIFONE')).toBeTruthy();
      });
      fireEvent.click(getByTestId('provider-item-MOBIFONE'));

      expect(mockOnSelected).toHaveBeenCalledTimes(2);
      expect(mockOnSelected).toHaveBeenNthCalledWith(1, mockProviders[0]);
      expect(mockOnSelected).toHaveBeenNthCalledWith(2, mockProviders[1]);
    });
  });

  describe('bottom sheet management', () => {
    it('should open bottom sheet when selection is clicked', async () => {
      const {getByTestId} = render(
        <MSBProviderSelection code="UTILITY" onSelected={mockOnSelected} />
      );

      fireEvent.click(getByTestId('payment.beneficiaryScreen.selectProviderBeneficiary'));

      expect(hostSharedModule.d.domainService?.showBottomSheet).toHaveBeenCalled();
    });

    it('should close bottom sheet when provider is selected', async () => {
      const {getByTestId} = render(
        <MSBProviderSelection code="UTILITY" onSelected={mockOnSelected} />
      );

      await waitFor(() => {
        expect(mockGetProviderListUseCase.execute).toHaveBeenCalled();
      });

      fireEvent.click(getByTestId('payment.beneficiaryScreen.selectProviderBeneficiary'));
      
      await waitFor(() => {
        expect(getByTestId('provider-item-VIETTEL')).toBeTruthy();
      });

      fireEvent.click(getByTestId('provider-item-VIETTEL'));

      expect(hostSharedModule.d.domainService?.hideBottomSheet).toHaveBeenCalled();
    });

    it('should not open bottom sheet when disabled', () => {
      const {getByTestId} = render(
        <MSBProviderSelection code="UTILITY" onSelected={mockOnSelected} disabled={true} />
      );

      fireEvent.click(getByTestId('payment.beneficiaryScreen.selectProviderBeneficiary'));

      expect(hostSharedModule.d.domainService?.showBottomSheet).not.toHaveBeenCalled();
    });
  });

  describe('ref methods', () => {
    it('should expose resetSelected method', () => {
      const ref = React.createRef<any>();
      
      render(
        <MSBProviderSelection ref={ref} code="UTILITY" onSelected={mockOnSelected} />
      );

      expect(ref.current).toHaveProperty('resetSelected');
      expect(typeof ref.current.resetSelected).toBe('function');
    });

    it('should expose isBottomSheetOpen property', () => {
      const ref = React.createRef<any>();
      
      render(
        <MSBProviderSelection ref={ref} code="UTILITY" onSelected={mockOnSelected} />
      );

      expect(ref.current).toHaveProperty('isBottomSheetOpen');
      expect(typeof ref.current.isBottomSheetOpen).toBe('boolean');
    });

    it('should reset selected provider when resetSelected is called', async () => {
      const ref = React.createRef<any>();
      
      const {getByTestId} = render(
        <MSBProviderSelection ref={ref} code="UTILITY" onSelected={mockOnSelected} />
      );

      await waitFor(() => {
        expect(mockGetProviderListUseCase.execute).toHaveBeenCalled();
      });

      // Select a provider first
      fireEvent.click(getByTestId('payment.beneficiaryScreen.selectProviderBeneficiary'));
      await waitFor(() => {
        expect(getByTestId('provider-item-VIETTEL')).toBeTruthy();
      });
      fireEvent.click(getByTestId('provider-item-VIETTEL'));

      // Reset selection
      ref.current.resetSelected();

      // Verify selection is reset
      expect(ref.current.isBottomSheetOpen).toBe(false);
    });
  });

  describe('edge cases', () => {
    it('should handle empty provider list', async () => {
      mockGetProviderListUseCase.execute.mockResolvedValue({
        status: 'SUCCESS',
        data: {providers: []},
      });

      const {getByTestId} = render(
        <MSBProviderSelection code="UTILITY" onSelected={mockOnSelected} />
      );

      fireEvent.click(getByTestId('payment.beneficiaryScreen.selectProviderBeneficiary'));

      await waitFor(() => {
        expect(getByTestId('provider-list')).toBeTruthy();
      });
    });

    it('should handle null provider data', async () => {
      mockGetProviderListUseCase.execute.mockResolvedValue({
        status: 'SUCCESS',
        data: null,
      });

      const {getByTestId} = render(
        <MSBProviderSelection code="UTILITY" onSelected={mockOnSelected} />
      );

      await waitFor(() => {
        expect(mockGetProviderListUseCase.execute).toHaveBeenCalled();
      });

      fireEvent.click(getByTestId('payment.beneficiaryScreen.selectProviderBeneficiary'));

      // Should not crash
      expect(getByTestId('payment.beneficiaryScreen.selectProviderBeneficiary')).toBeTruthy();
    });

    it('should handle invalid default value', () => {
      expect(() => {
        render(
          <MSBProviderSelection
            code="UTILITY"
            onSelected={mockOnSelected}
            defaultValue={null as any}
          />
        );
      }).not.toThrow();
    });

    it('should handle missing code prop', async () => {
      const {getByTestId} = render(
        <MSBProviderSelection code="" onSelected={mockOnSelected} />
      );

      await waitFor(() => {
        expect(mockGetProviderListUseCase.execute).toHaveBeenCalled();
      });

      expect(getByTestId('payment.beneficiaryScreen.selectProviderBeneficiary')).toBeTruthy();
    });
  });

  describe('performance', () => {
    it('should handle rapid selection changes efficiently', async () => {
      const {getByTestId} = render(
        <MSBProviderSelection code="UTILITY" onSelected={mockOnSelected} />
      );

      await waitFor(() => {
        expect(mockGetProviderListUseCase.execute).toHaveBeenCalled();
      });

      const startTime = performance.now();

      // Simulate rapid selections
      for (let i = 0; i < 10; i++) {
        fireEvent.click(getByTestId('payment.beneficiaryScreen.selectProviderBeneficiary'));
        await waitFor(() => {
          expect(getByTestId('provider-item-VIETTEL')).toBeTruthy();
        });
        fireEvent.click(getByTestId('provider-item-VIETTEL'));
      }

      const endTime = performance.now();

      expect(endTime - startTime).toBeLessThan(1000); // Should complete quickly
      expect(mockOnSelected).toHaveBeenCalledTimes(10);
    });

    it('should memoize provider list rendering', async () => {
      const {rerender} = render(
        <MSBProviderSelection code="UTILITY" onSelected={mockOnSelected} />
      );

      await waitFor(() => {
        expect(mockGetProviderListUseCase.execute).toHaveBeenCalled();
      });

      // Re-render with same props
      rerender(<MSBProviderSelection code="UTILITY" onSelected={mockOnSelected} />);

      // Should not fetch providers again
      expect(mockGetProviderListUseCase.execute).toHaveBeenCalledTimes(1);
    });
  });
});
