import React from 'react';
import {describe, it, expect, jest, beforeEach} from '@jest/globals';
import {render} from '@testing-library/react-native';
import PaymentBillInfo from '../index';

// Mock dependencies
jest.mock('react-native', () => ({
  View: ({children, style, testID}: any) => (
    <div testID={testID} style={style}>
      {children}
    </div>
  ),
}));

jest.mock('../../../locales/i18n', () => ({
  translate: jest.fn((key: string) => {
    const translations: {[key: string]: string} = {
      'qrPaymentInfo.merchantName': 'Merchant Name',
    };
    return translations[key] || key;
  }),
}));

jest.mock('msb-shared-component', () => ({
  MSBTextBase: ({content, style, children, testID, numberOfLines}: any) => (
    <span testID={testID} style={style} data-number-of-lines={numberOfLines}>
      {content || children}
    </span>
  ),
  MSBFastImage: ({nameImage, style, folder, testID}: any) => (
    <img testID={testID} style={style} src={`${folder}/${nameImage}`} alt={nameImage} />
  ),
  MSBFolderImage: {
    ICON_SVG: 'icon-svg',
  },
  SizeGlobal: {
    Size800: 800,
    Size300: 300,
    Size100: 100,
  },
  SizeAlias: {
    Spacing4xSmall: 4,
  },
  createMSBStyleSheet: jest.fn(styleFunction =>
    styleFunction({
      ColorDataView: {
        TextSub: '#666666',
        TextMain: '#000000',
      },
      Typography: {
        small_regular: {
          fontSize: 12,
          fontWeight: 'normal',
        },
        base_medium: {
          fontSize: 14,
          fontWeight: '500',
        },
      },
      SizeGlobal: {
        Size300: 300,
        Size100: 100,
      },
    }),
  ),
  useMSBStyles: jest.fn(() => ({
    styles: {
      container: {},
      txtBank: {
        fontSize: 12,
        fontWeight: 'normal',
        color: '#666666',
      },
      customerInfo: {
        alignItems: 'center',
        flexDirection: 'row',
        paddingBottom: 4,
      },
      accInfo: {
        flex: 1,
        marginLeft: 300,
        marginTop: 100,
      },
      txtName: {
        fontSize: 14,
        fontWeight: '500',
        color: '#000000',
      },
      accNumber: {
        alignItems: 'center',
        flexDirection: 'row',
        marginTop: 4,
      },
    },
  })),
}));

describe('PaymentBillInfo', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('rendering', () => {
    it('should render with merchant name and store ID', () => {
      const props = {
        merchantName: 'ABC Electronics Store',
        storeId: 'STORE123',
      };

      const {getByText} = render(<PaymentBillInfo {...props} />);

      expect(getByText('Merchant Name')).toBeTruthy();
      expect(getByText('ABC Electronics Store')).toBeTruthy();
      expect(getByText('STORE123')).toBeTruthy();
    });

    it('should render with custom style', () => {
      const customStyle = {
        backgroundColor: 'red',
        padding: 10,
      };

      const props = {
        merchantName: 'Test Store',
        storeId: 'TEST123',
        style: customStyle,
      };

      const {container} = render(<PaymentBillInfo {...props} />);
      const containerElement = container.firstChild as HTMLElement;

      expect(containerElement.style.backgroundColor).toBe('red');
      expect(containerElement.style.padding).toBe('10px');
    });

    it('should render default icon', () => {
      const props = {
        merchantName: 'Test Store',
        storeId: 'TEST123',
      };

      const {container} = render(<PaymentBillInfo {...props} />);
      const imageElement = container.querySelector('img');

      expect(imageElement?.src).toContain('icon-svg/tone-bill');
      expect(imageElement?.alt).toBe('tone-bill');
    });

    it('should apply correct image dimensions', () => {
      const props = {
        merchantName: 'Test Store',
        storeId: 'TEST123',
      };

      const {container} = render(<PaymentBillInfo {...props} />);
      const imageElement = container.querySelector('img');

      expect(imageElement?.style.width).toBe('800px');
      expect(imageElement?.style.height).toBe('800px');
    });
  });

  describe('styling', () => {
    it('should apply correct styles to merchant name label', () => {
      const props = {
        merchantName: 'Test Store',
        storeId: 'TEST123',
      };

      const {getByText} = render(<PaymentBillInfo {...props} />);
      const labelElement = getByText('Merchant Name');

      expect(labelElement.style.fontSize).toBe('12px');
      expect(labelElement.style.fontWeight).toBe('normal');
      expect(labelElement.style.color).toBe('#666666');
    });

    it('should apply correct styles to merchant name value', () => {
      const props = {
        merchantName: 'Test Store',
        storeId: 'TEST123',
      };

      const {getByText} = render(<PaymentBillInfo {...props} />);
      const nameElement = getByText('Test Store');

      expect(nameElement.style.fontSize).toBe('14px');
      expect(nameElement.style.fontWeight).toBe('500');
      expect(nameElement.style.color).toBe('#000000');
    });

    it('should apply correct styles to store ID', () => {
      const props = {
        merchantName: 'Test Store',
        storeId: 'TEST123',
      };

      const {getByText} = render(<PaymentBillInfo {...props} />);
      const storeIdElement = getByText('TEST123');

      expect(storeIdElement.style.fontSize).toBe('12px');
      expect(storeIdElement.style.fontWeight).toBe('normal');
      expect(storeIdElement.style.color).toBe('#666666');
    });

    it('should apply numberOfLines to store ID', () => {
      const props = {
        merchantName: 'Test Store',
        storeId: 'TEST123',
      };

      const {getByText} = render(<PaymentBillInfo {...props} />);
      const storeIdElement = getByText('TEST123');

      expect(storeIdElement.getAttribute('data-number-of-lines')).toBe('1');
    });
  });

  describe('component structure', () => {
    it('should have correct component hierarchy', () => {
      const props = {
        merchantName: 'Test Store',
        storeId: 'TEST123',
      };

      const {container, getByText} = render(<PaymentBillInfo {...props} />);
      const containerElement = container.firstChild as HTMLElement;
      const labelElement = getByText('Merchant Name');
      const nameElement = getByText('Test Store');
      const storeIdElement = getByText('TEST123');
      const imageElement = container.querySelector('img');

      expect(containerElement).toContainElement(labelElement);
      expect(containerElement).toContainElement(nameElement);
      expect(containerElement).toContainElement(storeIdElement);
      expect(containerElement).toContainElement(imageElement);
    });

    it('should render label before customer info', () => {
      const props = {
        merchantName: 'Test Store',
        storeId: 'TEST123',
      };

      const {container} = render(<PaymentBillInfo {...props} />);
      const containerElement = container.firstChild as HTMLElement;
      const children = Array.from(containerElement.children);

      expect(children[0].textContent).toBe('Merchant Name');
    });
  });

  describe('edge cases', () => {
    it('should handle empty merchant name', () => {
      const props = {
        merchantName: '',
        storeId: 'TEST123',
      };

      const {getByText} = render(<PaymentBillInfo {...props} />);

      expect(getByText('Merchant Name')).toBeTruthy();
      expect(getByText('TEST123')).toBeTruthy();
    });

    it('should handle empty store ID', () => {
      const props = {
        merchantName: 'Test Store',
        storeId: '',
      };

      const {getByText} = render(<PaymentBillInfo {...props} />);

      expect(getByText('Merchant Name')).toBeTruthy();
      expect(getByText('Test Store')).toBeTruthy();
    });

    it('should handle long merchant name', () => {
      const props = {
        merchantName: 'This is a very long merchant name that might wrap to multiple lines',
        storeId: 'TEST123',
      };

      const {getByText} = render(<PaymentBillInfo {...props} />);

      expect(getByText('This is a very long merchant name that might wrap to multiple lines')).toBeTruthy();
    });

    it('should handle long store ID', () => {
      const props = {
        merchantName: 'Test Store',
        storeId: 'VERYLONGSTOREID123456789012345678901234567890',
      };

      const {getByText} = render(<PaymentBillInfo {...props} />);

      expect(getByText('VERYLONGSTOREID123456789012345678901234567890')).toBeTruthy();
    });

    it('should handle special characters in merchant name', () => {
      const props = {
        merchantName: 'Cửa hàng điện tử ABC & Co.',
        storeId: 'STORE-123',
      };

      const {getByText} = render(<PaymentBillInfo {...props} />);

      expect(getByText('Cửa hàng điện tử ABC & Co.')).toBeTruthy();
      expect(getByText('STORE-123')).toBeTruthy();
    });

    it('should handle special characters in store ID', () => {
      const props = {
        merchantName: 'Test Store',
        storeId: 'STORE_123-ABC@XYZ',
      };

      const {getByText} = render(<PaymentBillInfo {...props} />);

      expect(getByText('STORE_123-ABC@XYZ')).toBeTruthy();
    });
  });

  describe('real-world scenarios', () => {
    it('should display QR payment merchant info correctly', () => {
      const props = {
        merchantName: 'Vinmart+ Nguyễn Trãi',
        storeId: 'VM001234',
      };

      const {getByText} = render(<PaymentBillInfo {...props} />);

      expect(getByText('Merchant Name')).toBeTruthy();
      expect(getByText('Vinmart+ Nguyễn Trãi')).toBeTruthy();
      expect(getByText('VM001234')).toBeTruthy();
    });

    it('should display restaurant info correctly', () => {
      const props = {
        merchantName: 'Nhà hàng Hương Sen',
        storeId: 'REST789',
      };

      const {getByText} = render(<PaymentBillInfo {...props} />);

      expect(getByText('Nhà hàng Hương Sen')).toBeTruthy();
      expect(getByText('REST789')).toBeTruthy();
    });

    it('should display online store info correctly', () => {
      const props = {
        merchantName: 'Shopee Official Store',
        storeId: 'SHOPEE_12345',
      };

      const {getByText} = render(<PaymentBillInfo {...props} />);

      expect(getByText('Shopee Official Store')).toBeTruthy();
      expect(getByText('SHOPEE_12345')).toBeTruthy();
    });
  });
});
