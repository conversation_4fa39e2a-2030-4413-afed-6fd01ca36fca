import React from 'react';
import {describe, it, expect, jest, beforeEach} from '@jest/globals';
import {render, fireEvent} from '@testing-library/react-native';
import {RecentTab} from '../index';

// Mock dependencies
jest.mock('react-native', () => ({
  View: ({children, style, testID}: any) => (
    <div testID={testID} style={style}>
      {children}
    </div>
  ),
}));

jest.mock('react-native-gesture-handler', () => ({
  FlatList: ({data, renderItem, ListEmptyComponent, ItemSeparatorComponent, keyExtractor}: any) => (
    <div>
      {data && data.length > 0 ? (
        data.map((item: any, index: number) => (
          <div key={keyExtractor ? keyExtractor(item, index) : index}>
            {renderItem({item, index})}
            {ItemSeparatorComponent && index < data.length - 1 && ItemSeparatorComponent()}
          </div>
        ))
      ) : (
        ListEmptyComponent && ListEmptyComponent()
      )}
    </div>
  ),
}));

jest.mock('../../utils/Utils', () => ({
  regexTransformToEnglishCharacter: jest.fn((text: string) => text),
}));

jest.mock('../../locales/i18n', () => ({
  translate: jest.fn((key: string) => {
    const translations: {[key: string]: string} = {
      'components.recentTab.searchPlaceholder': 'Search recent contacts...',
      'billingTab.titleEmptyContactFiltered': 'No contacts found',
      'billingTab.contentEmptyContactFiltered': 'Try adjusting your search criteria',
    };
    return translations[key] || key;
  }),
}));

jest.mock('../contact-item', () => {
  return ({name, bankName, bankAlias, searchText, isTopup}: any) => (
    <div data-testid="contact-item">
      {name} - {bankName} - {bankAlias} - {searchText} - {isTopup ? 'topup' : 'billing'}
    </div>
  );
});

jest.mock('msb-shared-component', () => ({
  MSBSearchInput: ({value, placeholder, onChangeText, testID, maxLength}: any) => (
    <input
      testID={testID}
      value={value}
      placeholder={placeholder}
      maxLength={maxLength}
      onChange={(e) => onChangeText && onChangeText(e.target.value)}
    />
  ),
  MSBTextBase: ({content, style, testID}: any) => (
    <span testID={testID} style={style}>
      {content}
    </span>
  ),
  MSBTouchable: ({children, onPress, testID}: any) => (
    <button testID={testID} onClick={onPress}>
      {children}
    </button>
  ),
  MSBFastImage: ({nameImage, style, folder, testID}: any) => (
    <img 
      testID={testID} 
      style={style} 
      src={`${folder}/${nameImage}`}
      alt={nameImage}
    />
  ),
  MSBFolderImage: {
    IMAGES: 'images',
  },
  ColorAlias: {
    BackgroundWhite: '#FFFFFF',
  },
  ColorDataView: {
    TextMain: '#000000',
  },
  ColorItem: {
    BorderDivider: '#E0E0E0',
  },
  createMSBStyleSheet: jest.fn((styleFunction) => styleFunction({
    Typography: {
      small_medium: {
        fontSize: 12,
        fontWeight: '500',
      },
      base_semiBold: {
        fontSize: 14,
        fontWeight: '600',
      },
      small_regular: {
        fontSize: 12,
        fontWeight: 'normal',
      },
    },
    SizeAlias: {
      SpacingXSmall: 8,
      SpacingSmall: 12,
      Spacing2xSmall: 2,
      Spacing4xSmall: 4,
      Spacing2xLarge: 32,
    },
    SizeGlobal: {
      Size2880: 2880,
    },
  })),
  useMSBStyles: jest.fn(() => ({
    styles: {
      container: {
        flex: 1,
      },
      containerSearchInput: {
        paddingVertical: 8,
        paddingHorizontal: 12,
        borderBottomWidth: 1,
        borderBottomColor: '#E0E0E0',
      },
      separator: {
        backgroundColor: '#E0E0E0',
        height: 1,
        marginHorizontal: 12,
      },
      containerEmpty: {
        alignItems: 'center',
        flexDirection: 'column',
        flex: 1,
        justifyContent: 'center',
        width: '100%',
      },
      image: {
        height: 2880,
        width: 2880,
        marginVertical: 32,
      },
      textTitleEmpty: {
        fontSize: 14,
        fontWeight: '600',
      },
      textTitleEmptyDes: {
        fontSize: 12,
        fontWeight: 'normal',
        marginTop: 4,
      },
    },
  })),
}));

// Mock IBillContact
const mockContact = {
  id: '1',
  getCustomerName: () => 'John Doe',
  getBillCode: () => '**********',
  getSubtitle: () => 'ABC Bank',
  getIcon: () => 'abc-bank',
  isTopup: () => false,
  getSearchContent: () => 'John Doe ABC Bank **********',
};

const mockTopupContact = {
  id: '2',
  getCustomerName: () => 'Jane Smith',
  getBillCode: () => '**********',
  getSubtitle: () => 'XYZ Mobile',
  getIcon: () => 'xyz-mobile',
  isTopup: () => true,
  getSearchContent: () => 'Jane Smith XYZ Mobile **********',
};

describe('RecentTab', () => {
  const defaultProps = {
    recentContact: [mockContact, mockTopupContact],
    onSelect: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('rendering', () => {
    it('should render with recent contacts', () => {
      const {getByPlaceholderText, getAllByTestId} = render(<RecentTab {...defaultProps} />);

      expect(getByPlaceholderText('Search recent contacts...')).toBeTruthy();
      expect(getAllByTestId('contact-item')).toHaveLength(2);
    });

    it('should render search input with correct props', () => {
      const {getByPlaceholderText} = render(<RecentTab {...defaultProps} />);
      const searchInput = getByPlaceholderText('Search recent contacts...');

      expect(searchInput.getAttribute('maxLength')).toBe('255');
    });

    it('should render empty state when no contacts', () => {
      const props = {
        recentContact: [],
        onSelect: jest.fn(),
      };

      const {getByText, getByAltText} = render(<RecentTab {...props} />);

      expect(getByText('No contacts found')).toBeTruthy();
      expect(getByText('Try adjusting your search criteria')).toBeTruthy();
      expect(getByAltText('empty-data')).toBeTruthy();
    });

    it('should render contact items with correct data', () => {
      const {getAllByTestId} = render(<RecentTab {...defaultProps} />);
      const contactItems = getAllByTestId('contact-item');

      expect(contactItems[0].textContent).toContain('John Doe');
      expect(contactItems[0].textContent).toContain('abc-bank');
      expect(contactItems[0].textContent).toContain('ABC Bank');
      expect(contactItems[0].textContent).toContain('billing');

      expect(contactItems[1].textContent).toContain('Jane Smith');
      expect(contactItems[1].textContent).toContain('xyz-mobile');
      expect(contactItems[1].textContent).toContain('XYZ Mobile');
      expect(contactItems[1].textContent).toContain('topup');
    });
  });

  describe('search functionality', () => {
    it('should filter contacts based on search text', () => {
      const {getByPlaceholderText, getAllByTestId} = render(<RecentTab {...defaultProps} />);
      const searchInput = getByPlaceholderText('Search recent contacts...');

      fireEvent.change(searchInput, {target: {value: 'John'}});

      const contactItems = getAllByTestId('contact-item');
      expect(contactItems).toHaveLength(1);
      expect(contactItems[0].textContent).toContain('John Doe');
    });

    it('should show empty state when search yields no results', () => {
      const {getByPlaceholderText, getByText} = render(<RecentTab {...defaultProps} />);
      const searchInput = getByPlaceholderText('Search recent contacts...');

      fireEvent.change(searchInput, {target: {value: 'NonExistentContact'}});

      expect(getByText('No contacts found')).toBeTruthy();
    });

    it('should clear search and show all contacts', () => {
      const {getByPlaceholderText, getAllByTestId} = render(<RecentTab {...defaultProps} />);
      const searchInput = getByPlaceholderText('Search recent contacts...');

      // First search
      fireEvent.change(searchInput, {target: {value: 'John'}});
      expect(getAllByTestId('contact-item')).toHaveLength(1);

      // Clear search
      fireEvent.change(searchInput, {target: {value: ''}});
      expect(getAllByTestId('contact-item')).toHaveLength(2);
    });

    it('should pass search text to contact items', () => {
      const {getByPlaceholderText, getAllByTestId} = render(<RecentTab {...defaultProps} />);
      const searchInput = getByPlaceholderText('Search recent contacts...');

      fireEvent.change(searchInput, {target: {value: 'test search'}});

      const contactItems = getAllByTestId('contact-item');
      contactItems.forEach(item => {
        expect(item.textContent).toContain('test search');
      });
    });
  });

  describe('contact selection', () => {
    it('should call onSelect when contact is pressed', () => {
      const onSelect = jest.fn();
      const props = {
        recentContact: [mockContact],
        onSelect,
      };

      const {getByRole} = render(<RecentTab {...props} />);
      const contactButton = getByRole('button');

      fireEvent.click(contactButton);

      expect(onSelect).toHaveBeenCalledWith(mockContact);
    });

    it('should not throw error when onSelect is undefined', () => {
      const props = {
        recentContact: [mockContact],
        onSelect: undefined,
      };

      const {getByRole} = render(<RecentTab {...props} />);
      const contactButton = getByRole('button');

      expect(() => fireEvent.click(contactButton)).not.toThrow();
    });
  });

  describe('edge cases', () => {
    it('should handle contacts with missing data', () => {
      const incompleteContact = {
        id: '3',
        getCustomerName: () => null,
        getBillCode: () => '123456',
        getSubtitle: () => undefined,
        getIcon: () => '',
        isTopup: () => false,
        getSearchContent: () => '123456',
      };

      const props = {
        recentContact: [incompleteContact],
        onSelect: jest.fn(),
      };

      expect(() => render(<RecentTab {...props} />)).not.toThrow();
    });

    it('should handle large contact lists', () => {
      const largeContactList = Array.from({length: 100}, (_, i) => ({
        ...mockContact,
        id: i.toString(),
        getCustomerName: () => `Contact ${i}`,
        getSearchContent: () => `Contact ${i}`,
      }));

      const props = {
        recentContact: largeContactList,
        onSelect: jest.fn(),
      };

      const {getAllByTestId} = render(<RecentTab {...props} />);

      expect(getAllByTestId('contact-item')).toHaveLength(100);
    });

    it('should handle special characters in search', () => {
      const {getByPlaceholderText} = render(<RecentTab {...defaultProps} />);
      const searchInput = getByPlaceholderText('Search recent contacts...');

      expect(() => {
        fireEvent.change(searchInput, {target: {value: '@#$%^&*()'}});
      }).not.toThrow();
    });
  });

  describe('accessibility', () => {
    it('should have accessible search input', () => {
      const {getByTestId} = render(<RecentTab {...defaultProps} />);

      expect(getByTestId('payment.SavedTab.enterSearch')).toBeTruthy();
    });

    it('should have clickable contact items', () => {
      const {getAllByRole} = render(<RecentTab {...defaultProps} />);
      const buttons = getAllByRole('button');

      expect(buttons).toHaveLength(2);
    });
  });
});
