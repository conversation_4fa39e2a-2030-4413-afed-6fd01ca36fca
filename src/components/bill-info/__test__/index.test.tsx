import React from 'react';
import {describe, it, expect, jest, beforeEach} from '@jest/globals';
import {render} from '@testing-library/react-native';
import BillInfo from '../index';

// Mock dependencies
jest.mock('react-native', () => ({
  View: ({children, style, testID}: any) => (
    <div testID={testID} style={style}>
      {children}
    </div>
  ),
}));

jest.mock('msb-shared-component', () => ({
  MSBTextBase: ({content, style, testID}: any) => (
    <span testID={testID} style={style}>
      {content}
    </span>
  ),
  MSBFastImage: ({nameImage, style, folder, testID}: any) => (
    <img 
      testID={testID} 
      style={style} 
      src={`${folder}/${nameImage}`}
      alt={nameImage}
    />
  ),
  MSBFolderImage: {
    LOGO_BILLING: 'logo-billing',
    LOGO_TOPUP: 'logo-topup',
    ICON_SVG: 'icon-svg',
  },
  SizeAlias: {
    SpacingXSmall: 8,
    Spacing4xSmall: 4,
    Spacing2xSmall: 2,
  },
  createMSBStyleSheet: jest.fn((styleFunction) => styleFunction({
    SizeGlobal: {
      Size50: 50,
      Size100: 100,
      Size800: 800,
    },
    ColorGlobal: {
      NeutralWhite: '#FFFFFF',
    },
    ColorDataView: {
      TextSub: '#666666',
      TextMain: '#000000',
      IconDefault: '#999999',
    },
    Typography: {
      small_regular: {
        fontSize: 12,
        fontWeight: 'normal',
      },
      base_medium: {
        fontSize: 14,
        fontWeight: '500',
      },
    },
    SizeAlias: {
      SpacingXSmall: 8,
      Spacing4xSmall: 4,
      Spacing2xSmall: 2,
    },
  })),
  useMSBStyles: jest.fn(() => ({
    styles: {
      container: {
        backgroundColor: '#FFFFFF',
      },
      title: {
        fontSize: 12,
        fontWeight: 'normal',
        color: '#666666',
      },
      beneficiaryInfo: {
        alignItems: 'center',
        flexDirection: 'row',
        marginTop: 4,
      },
      icLogo: {
        height: 800,
        width: 800,
      },
      accInfo: {
        flex: 1,
        marginLeft: 8,
      },
      txtName: {
        fontSize: 14,
        fontWeight: '500',
        color: '#000000',
      },
      accNumber: {
        alignItems: 'center',
        flexDirection: 'row',
        marginTop: 4,
      },
      txtBank: {
        fontSize: 12,
        fontWeight: 'normal',
        color: '#999999',
      },
      dot: {
        backgroundColor: '#999999',
        borderRadius: 50,
        height: 100,
        marginHorizontal: 2,
        width: 100,
      },
      flex1: {
        flex: 1,
      },
    },
  })),
}));

describe('BillInfo', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('rendering', () => {
    it('should render with all required props', () => {
      const props = {
        title: 'Beneficiary Information',
        name: 'John Doe',
        bankName: 'ABC Bank',
        bankAlias: '**********',
        bankLogo: 'abc-bank-logo',
      };

      const {getByText} = render(<BillInfo {...props} />);

      expect(getByText('Beneficiary Information')).toBeTruthy();
      expect(getByText('John Doe')).toBeTruthy();
      expect(getByText('ABC Bank')).toBeTruthy();
      expect(getByText('**********')).toBeTruthy();
    });

    it('should render with uppercase name when isUppercase is true', () => {
      const props = {
        title: 'Beneficiary Information',
        name: 'john doe',
        bankName: 'ABC Bank',
        bankAlias: '**********',
        bankLogo: 'abc-bank-logo',
        isUppercase: true,
      };

      const {getByText} = render(<BillInfo {...props} />);

      expect(getByText('JOHN DOE')).toBeTruthy();
    });

    it('should not render bank information when isNotShowBankName is true', () => {
      const props = {
        title: 'Beneficiary Information',
        name: 'John Doe',
        bankName: 'ABC Bank',
        bankAlias: '**********',
        bankLogo: 'abc-bank-logo',
        isNotShowBankName: true,
      };

      const {getByText, queryByText} = render(<BillInfo {...props} />);

      expect(getByText('John Doe')).toBeTruthy();
      expect(queryByText('ABC Bank')).toBeNull();
      expect(queryByText('**********')).toBeNull();
    });

    it('should render with custom styles', () => {
      const customStyle = {
        backgroundColor: 'red',
        padding: 10,
      };

      const props = {
        title: 'Test Title',
        name: 'Test Name',
        bankName: 'Test Bank',
        bankAlias: '123456',
        bankLogo: 'test-logo',
        style: customStyle,
      };

      const {container} = render(<BillInfo {...props} />);
      const containerElement = container.firstChild as HTMLElement;

      expect(containerElement.style.backgroundColor).toBe('red');
      expect(containerElement.style.padding).toBe('10px');
    });
  });

  describe('styling', () => {
    it('should apply custom name style', () => {
      const customNameStyle = {
        color: 'blue',
        fontSize: 16,
      };

      const props = {
        title: 'Test Title',
        name: 'Test Name',
        bankName: 'Test Bank',
        bankAlias: '123456',
        bankLogo: 'test-logo',
        styleName: customNameStyle,
      };

      const {getByText} = render(<BillInfo {...props} />);
      const nameElement = getByText('Test Name');

      expect(nameElement.style.color).toBe('blue');
      expect(nameElement.style.fontSize).toBe('16px');
    });

    it('should apply custom bank name style', () => {
      const customBankNameStyle = {
        color: 'green',
        fontWeight: 'bold',
      };

      const props = {
        title: 'Test Title',
        name: 'Test Name',
        bankName: 'Test Bank',
        bankAlias: '123456',
        bankLogo: 'test-logo',
        styleBankName: customBankNameStyle,
      };

      const {getByText} = render(<BillInfo {...props} />);
      const bankNameElement = getByText('Test Bank');

      expect(bankNameElement.style.color).toBe('green');
      expect(bankNameElement.style.fontWeight).toBe('bold');
    });

    it('should apply custom account number style', () => {
      const customAccountStyle = {
        color: 'purple',
        textDecoration: 'underline',
      };

      const props = {
        title: 'Test Title',
        name: 'Test Name',
        bankName: 'Test Bank',
        bankAlias: '123456',
        bankLogo: 'test-logo',
        styleAccountNo: customAccountStyle,
      };

      const {getByText} = render(<BillInfo {...props} />);
      const accountElement = getByText('123456');

      expect(accountElement.style.color).toBe('purple');
      expect(accountElement.style.textDecoration).toBe('underline');
    });
  });

  describe('edge cases', () => {
    it('should handle undefined name', () => {
      const props = {
        title: 'Test Title',
        name: undefined,
        bankName: 'Test Bank',
        bankAlias: '123456',
        bankLogo: 'test-logo',
      };

      expect(() => render(<BillInfo {...props} />)).not.toThrow();
    });

    it('should handle null values', () => {
      const props = {
        title: 'Test Title',
        name: null as any,
        bankName: null as any,
        bankAlias: null as any,
        bankLogo: null as any,
      };

      expect(() => render(<BillInfo {...props} />)).not.toThrow();
    });

    it('should handle empty strings', () => {
      const props = {
        title: '',
        name: '',
        bankName: '',
        bankAlias: '',
        bankLogo: '',
      };

      const {getByText} = render(<BillInfo {...props} />);

      expect(getByText('')).toBeTruthy();
    });
  });
});
