import React from 'react';
import {describe, it, expect, jest, beforeEach} from '@jest/globals';
import {render} from '@testing-library/react-native';
import HighlightText from '../index';

// Mock dependencies
jest.mock('msb-shared-component', () => ({
  MSBTextBase: ({content, style, children, testID}: any) => (
    <span testID={testID} style={style}>
      {content || children}
    </span>
  ),
  ColorGlobal: {
    Brand500: '#007AFF',
  },
}));

describe('HighlightText', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('rendering without search', () => {
    it('should render text without highlighting when search is empty', () => {
      const props = {
        text: 'Hello World',
        search: '',
        style: {color: 'black'},
      };

      const {getByText} = render(<HighlightText {...props} />);

      expect(getByText('Hello World')).toBeTruthy();
    });

    it('should render text without highlighting when search is undefined', () => {
      const props = {
        text: 'Hello World',
        search: undefined as any,
        style: {color: 'black'},
      };

      const {getByText} = render(<HighlightText {...props} />);

      expect(getByText('Hello World')).toBeTruthy();
    });

    it('should render text without highlighting when search is null', () => {
      const props = {
        text: 'Hello World',
        search: null as any,
        style: {color: 'black'},
      };

      const {getByText} = render(<HighlightText {...props} />);

      expect(getByText('Hello World')).toBeTruthy();
    });
  });

  describe('rendering with search', () => {
    it('should highlight matching text', () => {
      const props = {
        text: 'Hello World',
        search: 'Hello',
        style: {color: 'black'},
      };

      const {container} = render(<HighlightText {...props} />);
      const highlightedElements = container.querySelectorAll('span');

      // Should have multiple spans for highlighted and non-highlighted parts
      expect(highlightedElements.length).toBeGreaterThan(1);
    });

    it('should be case insensitive', () => {
      const props = {
        text: 'Hello World',
        search: 'hello',
        style: {color: 'black'},
      };

      const {container} = render(<HighlightText {...props} />);
      const highlightedElements = container.querySelectorAll('span');

      expect(highlightedElements.length).toBeGreaterThan(1);
    });

    it('should highlight multiple occurrences', () => {
      const props = {
        text: 'Hello Hello World',
        search: 'Hello',
        style: {color: 'black'},
      };

      const {container} = render(<HighlightText {...props} />);
      const highlightedElements = container.querySelectorAll('span');

      // Should have multiple spans for multiple highlighted parts
      expect(highlightedElements.length).toBeGreaterThan(2);
    });

    it('should handle partial matches', () => {
      const props = {
        text: 'Hello World',
        search: 'ell',
        style: {color: 'black'},
      };

      const {container} = render(<HighlightText {...props} />);
      const highlightedElements = container.querySelectorAll('span');

      expect(highlightedElements.length).toBeGreaterThan(1);
    });

    it('should handle search term not found', () => {
      const props = {
        text: 'Hello World',
        search: 'xyz',
        style: {color: 'black'},
      };

      const {getByText} = render(<HighlightText {...props} />);

      expect(getByText('Hello World')).toBeTruthy();
    });
  });

  describe('special characters handling', () => {
    it('should handle special regex characters in search', () => {
      const props = {
        text: 'Price: $100.50',
        search: '$100',
        style: {color: 'black'},
      };

      const {container} = render(<HighlightText {...props} />);

      expect(() => render(<HighlightText {...props} />)).not.toThrow();
    });

    it('should handle parentheses in search', () => {
      const props = {
        text: 'Account (123456)',
        search: '(123456)',
        style: {color: 'black'},
      };

      expect(() => render(<HighlightText {...props} />)).not.toThrow();
    });

    it('should handle brackets in search', () => {
      const props = {
        text: 'Code [ABC123]',
        search: '[ABC123]',
        style: {color: 'black'},
      };

      expect(() => render(<HighlightText {...props} />)).not.toThrow();
    });

    it('should handle dots and asterisks in search', () => {
      const props = {
        text: 'File: test.txt',
        search: 'test.txt',
        style: {color: 'black'},
      };

      expect(() => render(<HighlightText {...props} />)).not.toThrow();
    });
  });

  describe('styling', () => {
    it('should apply custom style to text', () => {
      const customStyle = {
        color: 'red',
        fontSize: 16,
      };

      const props = {
        text: 'Hello World',
        search: '',
        style: customStyle,
      };

      const {getByText} = render(<HighlightText {...props} />);
      const textElement = getByText('Hello World');

      expect(textElement.style.color).toBe('red');
      expect(textElement.style.fontSize).toBe('16px');
    });

    it('should apply highlight color to matched text', () => {
      const props = {
        text: 'Hello World',
        search: 'Hello',
        style: {color: 'black'},
      };

      const {container} = render(<HighlightText {...props} />);
      const spans = container.querySelectorAll('span');

      // Find the highlighted span (should have brand color)
      const highlightedSpan = Array.from(spans).find(span => 
        span.style.color === '#007AFF' || span.textContent === 'Hello'
      );

      expect(highlightedSpan).toBeTruthy();
    });
  });

  describe('edge cases', () => {
    it('should handle empty text', () => {
      const props = {
        text: '',
        search: 'test',
        style: {color: 'black'},
      };

      expect(() => render(<HighlightText {...props} />)).not.toThrow();
    });

    it('should handle very long text', () => {
      const longText = 'Lorem ipsum '.repeat(100);
      const props = {
        text: longText,
        search: 'Lorem',
        style: {color: 'black'},
      };

      expect(() => render(<HighlightText {...props} />)).not.toThrow();
    });

    it('should handle unicode characters', () => {
      const props = {
        text: 'Nguyễn Văn A',
        search: 'Nguyễn',
        style: {color: 'black'},
      };

      const {container} = render(<HighlightText {...props} />);

      expect(container.textContent).toContain('Nguyễn Văn A');
    });

    it('should handle numbers in text and search', () => {
      const props = {
        text: 'Account **********',
        search: '123',
        style: {color: 'black'},
      };

      const {container} = render(<HighlightText {...props} />);
      const highlightedElements = container.querySelectorAll('span');

      expect(highlightedElements.length).toBeGreaterThan(1);
    });

    it('should handle whitespace in search', () => {
      const props = {
        text: 'Hello World Test',
        search: 'World Test',
        style: {color: 'black'},
      };

      const {container} = render(<HighlightText {...props} />);
      const highlightedElements = container.querySelectorAll('span');

      expect(highlightedElements.length).toBeGreaterThan(1);
    });
  });

  describe('performance', () => {
    it('should handle frequent re-renders efficiently', () => {
      const initialProps = {
        text: 'Hello World',
        search: 'Hello',
        style: {color: 'black'},
      };

      const {rerender} = render(<HighlightText {...initialProps} />);

      const startTime = performance.now();
      
      // Simulate frequent re-renders
      for (let i = 0; i < 100; i++) {
        rerender(<HighlightText text={`Hello World ${i}`} search="Hello" style={{color: 'black'}} />);
      }
      
      const endTime = performance.now();

      expect(endTime - startTime).toBeLessThan(100); // Should complete quickly
    });
  });

  describe('real-world scenarios', () => {
    it('should highlight bank names correctly', () => {
      const props = {
        text: 'Vietcombank - Vietnam Commercial Bank',
        search: 'Viet',
        style: {color: 'black'},
      };

      const {container} = render(<HighlightText {...props} />);
      const highlightedElements = container.querySelectorAll('span');

      expect(highlightedElements.length).toBeGreaterThan(1);
    });

    it('should highlight account numbers correctly', () => {
      const props = {
        text: 'Account: **********123456',
        search: '1234',
        style: {color: 'black'},
      };

      const {container} = render(<HighlightText {...props} />);
      const highlightedElements = container.querySelectorAll('span');

      expect(highlightedElements.length).toBeGreaterThan(1);
    });

    it('should highlight customer names correctly', () => {
      const props = {
        text: 'Nguyễn Văn A',
        search: 'Văn',
        style: {color: 'black'},
      };

      const {container} = render(<HighlightText {...props} />);
      const highlightedElements = container.querySelectorAll('span');

      expect(highlightedElements.length).toBeGreaterThan(1);
    });
  });
});
