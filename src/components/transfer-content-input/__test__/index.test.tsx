import React from 'react';
import {describe, it, expect, jest, beforeEach} from '@jest/globals';
import {render, fireEvent} from '@testing-library/react-native';
import TransferContentInput from '../index';

// Mock dependencies
jest.mock('../../utils/Utils', () => ({
  normalizeSpaces: jest.fn((text: string) => text.replace(/\s+/g, ' ').trim()),
  regexTransferContent: jest.fn((text: string) => text.replace(/[^a-zA-Z0-9\s]/g, '')),
}));

jest.mock('../../locales/i18n', () => ({
  translate: jest.fn((key: string) => {
    const translations: {[key: string]: string} = {
      'common.content': 'Nội dung chuyển khoản',
      'common.contentPlaceholder': 'Nhập nội dung chuyển khoản',
    };
    return translations[key] || key;
  }),
}));

jest.mock('msb-shared-component', () => ({
  MSBInputBase: ({
    label,
    value,
    onBlur,
    onChangeText,
    maxLength,
    disabled,
    isDisableRemoveIcon,
    placeholder,
    textInputProps,
    testID,
    ...restProps
  }: any) => (
    <div data-testid="msb-input-base" {...restProps}>
      <label>{label}</label>
      <input
        testID={testID}
        value={value}
        onBlur={onBlur}
        onChange={(e) => onChangeText && onChangeText(e.target.value)}
        maxLength={maxLength}
        disabled={disabled}
        placeholder={placeholder}
        data-keyboard-type={textInputProps?.keyboardType}
        data-return-key-label={textInputProps?.returnKeyLabel}
        data-return-key-type={textInputProps?.returnKeyType}
        data-disable-remove-icon={isDisableRemoveIcon}
      />
    </div>
  ),
}));

describe('TransferContentInput', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('rendering', () => {
    it('should render with default props', () => {
      const {getByDisplayValue, getByText} = render(
        <TransferContentInput value="" onChangeText={jest.fn()} />
      );

      expect(getByText('Nội dung chuyển khoản')).toBeTruthy();
      expect(getByDisplayValue('')).toBeTruthy();
    });

    it('should render with custom label and placeholder', () => {
      const {getByText, getByPlaceholderText} = render(
        <TransferContentInput
          value=""
          onChangeText={jest.fn()}
          label="Custom Label"
          placeholder="Custom Placeholder"
        />
      );

      expect(getByText('Custom Label')).toBeTruthy();
      expect(getByPlaceholderText('Custom Placeholder')).toBeTruthy();
    });

    it('should render with value', () => {
      const {getByDisplayValue} = render(
        <TransferContentInput value="Test content" onChangeText={jest.fn()} />
      );

      expect(getByDisplayValue('Test content')).toBeTruthy();
    });

    it('should render in disabled state', () => {
      const {container} = render(
        <TransferContentInput value="" onChangeText={jest.fn()} disabled={true} />
      );

      const input = container.querySelector('input');
      expect(input?.disabled).toBe(true);
      expect(input?.getAttribute('data-disable-remove-icon')).toBe('true');
    });

    it('should set correct input properties', () => {
      const {container} = render(
        <TransferContentInput value="" onChangeText={jest.fn()} />
      );

      const input = container.querySelector('input');
      expect(input?.getAttribute('maxLength')).toBe('140');
      expect(input?.getAttribute('data-keyboard-type')).toBe('ascii-capable');
      expect(input?.getAttribute('data-return-key-label')).toBe('Done');
      expect(input?.getAttribute('data-return-key-type')).toBe('done');
    });
  });

  describe('text input handling', () => {
    it('should call onChangeText when text changes', () => {
      const onChangeText = jest.fn();
      const {container} = render(
        <TransferContentInput value="" onChangeText={onChangeText} />
      );

      const input = container.querySelector('input');
      fireEvent.change(input!, {target: {value: 'New content'}});

      expect(onChangeText).toHaveBeenCalledWith('New content');
    });

    it('should not call onChangeText when disabled', () => {
      const onChangeText = jest.fn();
      const {container} = render(
        <TransferContentInput value="" onChangeText={onChangeText} disabled={true} />
      );

      const input = container.querySelector('input');
      fireEvent.change(input!, {target: {value: 'New content'}});

      // Input is disabled, so onChange shouldn't be triggered
      expect(input?.disabled).toBe(true);
    });

    it('should handle undefined onChangeText', () => {
      const {container} = render(
        <TransferContentInput value="" onChangeText={undefined} />
      );

      const input = container.querySelector('input');
      expect(() => {
        fireEvent.change(input!, {target: {value: 'New content'}});
      }).not.toThrow();
    });
  });

  describe('blur handling', () => {
    it('should normalize and clean content on blur', () => {
      const onChangeText = jest.fn();
      const {container} = render(
        <TransferContentInput value="Test   content   with   spaces!!!" onChangeText={onChangeText} />
      );

      const input = container.querySelector('input');
      fireEvent.blur(input!);

      expect(onChangeText).toHaveBeenCalledWith('Test content with spaces');
    });

    it('should handle empty content on blur', () => {
      const onChangeText = jest.fn();
      const {container} = render(
        <TransferContentInput value="" onChangeText={onChangeText} />
      );

      const input = container.querySelector('input');
      fireEvent.blur(input!);

      expect(onChangeText).toHaveBeenCalledWith('');
    });

    it('should handle special characters on blur', () => {
      const onChangeText = jest.fn();
      const {container} = render(
        <TransferContentInput value="Test@#$%^&*()content" onChangeText={onChangeText} />
      );

      const input = container.querySelector('input');
      fireEvent.blur(input!);

      expect(onChangeText).toHaveBeenCalledWith('Testcontent');
    });

    it('should not call onChangeText on blur when undefined', () => {
      const {container} = render(
        <TransferContentInput value="Test content" onChangeText={undefined} />
      );

      const input = container.querySelector('input');
      expect(() => {
        fireEvent.blur(input!);
      }).not.toThrow();
    });
  });

  describe('Utils integration', () => {
    it('should call Utils.normalizeSpaces and Utils.regexTransferContent on blur', () => {
      const Utils = require('../../utils/Utils');
      const onChangeText = jest.fn();
      const {container} = render(
        <TransferContentInput value="Test   content!!!" onChangeText={onChangeText} />
      );

      const input = container.querySelector('input');
      fireEvent.blur(input!);

      expect(Utils.regexTransferContent).toHaveBeenCalledWith('Test   content!!!');
      expect(Utils.normalizeSpaces).toHaveBeenCalledWith('Test   content');
    });

    it('should handle Utils functions returning different values', () => {
      const Utils = require('../../utils/Utils');
      Utils.regexTransferContent.mockReturnValue('Cleaned content');
      Utils.normalizeSpaces.mockReturnValue('Normalized content');

      const onChangeText = jest.fn();
      const {container} = render(
        <TransferContentInput value="Original content" onChangeText={onChangeText} />
      );

      const input = container.querySelector('input');
      fireEvent.blur(input!);

      expect(onChangeText).toHaveBeenCalledWith('Normalized content');
    });
  });

  describe('edge cases', () => {
    it('should handle very long content', () => {
      const longContent = 'a'.repeat(200);
      const onChangeText = jest.fn();
      const {getByDisplayValue} = render(
        <TransferContentInput value={longContent} onChangeText={onChangeText} />
      );

      expect(getByDisplayValue(longContent)).toBeTruthy();
    });

    it('should handle content with only spaces', () => {
      const onChangeText = jest.fn();
      const {container} = render(
        <TransferContentInput value="     " onChangeText={onChangeText} />
      );

      const input = container.querySelector('input');
      fireEvent.blur(input!);

      expect(onChangeText).toHaveBeenCalledWith('');
    });

    it('should handle content with unicode characters', () => {
      const onChangeText = jest.fn();
      const {container} = render(
        <TransferContentInput value="Tiếng Việt 中文 🎉" onChangeText={onChangeText} />
      );

      const input = container.querySelector('input');
      fireEvent.blur(input!);

      expect(onChangeText).toHaveBeenCalledWith('Ting Vit ');
    });

    it('should handle null/undefined value', () => {
      const onChangeText = jest.fn();
      expect(() => {
        render(<TransferContentInput value={null as any} onChangeText={onChangeText} />);
      }).not.toThrow();

      expect(() => {
        render(<TransferContentInput value={undefined as any} onChangeText={onChangeText} />);
      }).not.toThrow();
    });
  });

  describe('accessibility', () => {
    it('should pass through testID prop', () => {
      const {getByTestId} = render(
        <TransferContentInput
          value=""
          onChangeText={jest.fn()}
          testID="transfer-content-input"
        />
      );

      expect(getByTestId('transfer-content-input')).toBeTruthy();
    });

    it('should have proper keyboard configuration', () => {
      const {container} = render(
        <TransferContentInput value="" onChangeText={jest.fn()} />
      );

      const input = container.querySelector('input');
      expect(input?.getAttribute('data-keyboard-type')).toBe('ascii-capable');
      expect(input?.getAttribute('data-return-key-type')).toBe('done');
    });
  });

  describe('rest props', () => {
    it('should pass through additional props', () => {
      const {container} = render(
        <TransferContentInput
          value=""
          onChangeText={jest.fn()}
          customProp="custom-value"
          style={{backgroundColor: 'red'}}
        />
      );

      const wrapper = container.querySelector('[data-testid="msb-input-base"]');
      expect(wrapper?.getAttribute('customProp')).toBe('custom-value');
    });

    it('should override default props with rest props', () => {
      const customTextInputProps = {
        keyboardType: 'numeric',
        returnKeyType: 'next',
      };

      const {container} = render(
        <TransferContentInput
          value=""
          onChangeText={jest.fn()}
          textInputProps={customTextInputProps}
        />
      );

      const input = container.querySelector('input');
      expect(input?.getAttribute('data-keyboard-type')).toBe('numeric');
      expect(input?.getAttribute('data-return-key-type')).toBe('next');
    });
  });

  describe('real-world scenarios', () => {
    it('should handle typical transfer content', () => {
      const onChangeText = jest.fn();
      const {container} = render(
        <TransferContentInput value="Chuyen tien tra no" onChangeText={onChangeText} />
      );

      const input = container.querySelector('input');
      fireEvent.blur(input!);

      expect(onChangeText).toHaveBeenCalledWith('Chuyen tien tra no');
    });

    it('should clean up messy user input', () => {
      const onChangeText = jest.fn();
      const {container} = render(
        <TransferContentInput
          value="   Chuyển   tiền   trả   nợ!!!   @#$   "
          onChangeText={onChangeText}
        />
      );

      const input = container.querySelector('input');
      fireEvent.blur(input!);

      expect(onChangeText).toHaveBeenCalledWith('Chuyn tin tr n');
    });

    it('should handle payment reference numbers', () => {
      const onChangeText = jest.fn();
      const {container} = render(
        <TransferContentInput value="REF123456789" onChangeText={onChangeText} />
      );

      const input = container.querySelector('input');
      fireEvent.blur(input!);

      expect(onChangeText).toHaveBeenCalledWith('REF123456789');
    });
  });
});
