import React from 'react';
import {describe, it, expect, jest, beforeEach} from '@jest/globals';
import {render} from '@testing-library/react-native';
import ContactItem from '../index';

// Mock HighlightText component
jest.mock('../../highlight-text', () => {
  return ({text, search, style}: any) => (
    <span style={style}>
      {text}
    </span>
  );
});

// Mock dependencies
jest.mock('react-native', () => ({
  View: ({children, style, testID}: any) => (
    <div testID={testID} style={style}>
      {children}
    </div>
  ),
}));

jest.mock('msb-shared-component', () => ({
  MSBFastImage: ({nameImage, style, folder, testID}: any) => (
    <img 
      testID={testID} 
      style={style} 
      src={`${folder}/${nameImage}`}
      alt={nameImage}
    />
  ),
  MSBFolderImage: {
    LOGO_BILLING: 'logo-billing',
    LOGO_TOPUP: 'logo-topup',
    ICON_SVG: 'icon-svg',
  },
  ColorAlias: {
    BackgroundWhite: '#FFFFFF',
  },
  ColorItem: {
    TextSub: '#666666',
  },
  createMSBStyleSheet: jest.fn((styleFunction) => styleFunction({
    Typography: {
      small_regular: {
        fontSize: 12,
        fontWeight: 'normal',
      },
      base_medium: {
        fontSize: 14,
        fontWeight: '500',
      },
    },
    SizeAlias: {
      SpacingMedium: 16,
      SpacingSmall: 12,
      SpacingXSmall: 8,
      Spacing4xSmall: 4,
      Spacing2xSmall: 2,
    },
    ColorDataView: {
      TextSub: '#666666',
      TextMain: '#000000',
    },
    SizeGlobal: {
      Size50: 50,
      Size100: 100,
      Size800: 800,
    },
  })),
  useMSBStyles: jest.fn(() => ({
    styles: {
      container: {
        backgroundColor: '#FFFFFF',
        paddingVertical: 16,
        paddingHorizontal: 12,
      },
      beneficiaryInfo: {
        alignItems: 'center',
        flexDirection: 'row',
        marginTop: 4,
      },
      icLogo: {
        height: 800,
        width: 800,
      },
      accInfo: {
        flex: 1,
        marginLeft: 8,
      },
      txtName: {
        fontSize: 14,
        fontWeight: '500',
        color: '#000000',
      },
      accNumber: {
        alignItems: 'center',
        flexDirection: 'row',
        marginTop: 4,
      },
      txtBank: {
        fontSize: 12,
        fontWeight: 'normal',
        color: '#666666',
      },
      dot: {
        backgroundColor: '#666666',
        borderRadius: 50,
        height: 100,
        marginHorizontal: 2,
        width: 100,
      },
    },
  })),
});

describe('ContactItem', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('rendering', () => {
    it('should render with all required props', () => {
      const props = {
        name: 'John Doe',
        bankName: 'ABC Bank',
        bankAlias: '**********',
        searchText: '',
      };

      const {getByText} = render(<ContactItem {...props} />);

      expect(getByText('John Doe')).toBeTruthy();
      expect(getByText('ABC Bank')).toBeTruthy();
      expect(getByText('**********')).toBeTruthy();
    });

    it('should render with search text highlighting', () => {
      const props = {
        name: 'John Doe',
        bankName: 'ABC Bank',
        bankAlias: '**********',
        searchText: 'John',
      };

      const {getByText} = render(<ContactItem {...props} />);

      expect(getByText('John Doe')).toBeTruthy();
      expect(getByText('ABC Bank')).toBeTruthy();
      expect(getByText('**********')).toBeTruthy();
    });

    it('should render with topup logo when isTopup is true', () => {
      const props = {
        name: 'John Doe',
        bankName: 'ABC Bank',
        bankAlias: '**********',
        searchText: '',
        isTopup: true,
      };

      const {container} = render(<ContactItem {...props} />);
      const imageElement = container.querySelector('img');

      expect(imageElement?.src).toContain('logo-topup/ABC Bank');
    });

    it('should render with billing logo when isTopup is false', () => {
      const props = {
        name: 'John Doe',
        bankName: 'ABC Bank',
        bankAlias: '**********',
        searchText: '',
        isTopup: false,
      };

      const {container} = render(<ContactItem {...props} />);
      const imageElement = container.querySelector('img');

      expect(imageElement?.src).toContain('logo-billing/ABC Bank');
    });

    it('should render with custom style', () => {
      const customStyle = {
        backgroundColor: 'red',
        padding: 10,
      };

      const props = {
        name: 'John Doe',
        bankName: 'ABC Bank',
        bankAlias: '**********',
        searchText: '',
        style: customStyle,
      };

      const {container} = render(<ContactItem {...props} />);
      const containerElement = container.firstChild as HTMLElement;

      expect(containerElement.style.backgroundColor).toBe('red');
      expect(containerElement.style.padding).toBe('10px');
    });
  });

  describe('edge cases', () => {
    it('should handle undefined values', () => {
      const props = {
        name: undefined,
        bankName: undefined,
        bankAlias: undefined,
        searchText: undefined,
      };

      expect(() => render(<ContactItem {...props} />)).not.toThrow();
    });

    it('should handle null values', () => {
      const props = {
        name: null as any,
        bankName: null as any,
        bankAlias: null as any,
        searchText: null as any,
      };

      expect(() => render(<ContactItem {...props} />)).not.toThrow();
    });

    it('should handle empty strings', () => {
      const props = {
        name: '',
        bankName: '',
        bankAlias: '',
        searchText: '',
      };

      const {getByText} = render(<ContactItem {...props} />);

      expect(getByText('')).toBeTruthy();
    });

    it('should handle long text values', () => {
      const props = {
        name: 'This is a very long name that might wrap to multiple lines',
        bankName: 'This is a very long bank name',
        bankAlias: '****************************************',
        searchText: 'long',
      };

      const {getByText} = render(<ContactItem {...props} />);

      expect(getByText('This is a very long name that might wrap to multiple lines')).toBeTruthy();
      expect(getByText('This is a very long bank name')).toBeTruthy();
      expect(getByText('****************************************')).toBeTruthy();
    });

    it('should handle special characters', () => {
      const props = {
        name: 'Nguyễn Văn A',
        bankName: 'Ngân hàng ABC',
        bankAlias: '1234-5678-9012-3456',
        searchText: 'Nguyễn',
      };

      const {getByText} = render(<ContactItem {...props} />);

      expect(getByText('Nguyễn Văn A')).toBeTruthy();
      expect(getByText('Ngân hàng ABC')).toBeTruthy();
      expect(getByText('1234-5678-9012-3456')).toBeTruthy();
    });
  });

  describe('component structure', () => {
    it('should have correct component hierarchy', () => {
      const props = {
        name: 'John Doe',
        bankName: 'ABC Bank',
        bankAlias: '**********',
        searchText: '',
      };

      const {container, getByText} = render(<ContactItem {...props} />);
      const containerElement = container.firstChild as HTMLElement;
      const nameElement = getByText('John Doe');
      const bankNameElement = getByText('ABC Bank');
      const aliasElement = getByText('**********');

      expect(containerElement).toContainElement(nameElement);
      expect(containerElement).toContainElement(bankNameElement);
      expect(containerElement).toContainElement(aliasElement);
    });

    it('should render image before text content', () => {
      const props = {
        name: 'John Doe',
        bankName: 'ABC Bank',
        bankAlias: '**********',
        searchText: '',
      };

      const {container} = render(<ContactItem {...props} />);
      const imageElement = container.querySelector('img');
      const textElements = container.querySelectorAll('span');

      expect(imageElement).toBeTruthy();
      expect(textElements.length).toBeGreaterThan(0);
    });
  });

  describe('real-world scenarios', () => {
    it('should display contact information correctly', () => {
      const props = {
        name: 'Nguyễn Văn A',
        bankName: 'Vietcombank',
        bankAlias: '**********',
        searchText: '',
      };

      const {getByText} = render(<ContactItem {...props} />);

      expect(getByText('Nguyễn Văn A')).toBeTruthy();
      expect(getByText('Vietcombank')).toBeTruthy();
      expect(getByText('**********')).toBeTruthy();
    });

    it('should handle search functionality', () => {
      const props = {
        name: 'John Doe',
        bankName: 'ABC Bank',
        bankAlias: '**********',
        searchText: 'John',
      };

      const {getByText} = render(<ContactItem {...props} />);

      expect(getByText('John Doe')).toBeTruthy();
    });
  });
});
