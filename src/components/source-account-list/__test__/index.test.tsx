import React from 'react';
import {describe, it, expect, jest, beforeEach} from '@jest/globals';
import {render, fireEvent} from '@testing-library/react-native';
import SourceAccountList from '../index';

// Mock dependencies
jest.mock('react-native', () => ({
  View: ({children, style, testID}: any) => (
    <div testID={testID} style={style}>
      {children}
    </div>
  ),
  useWindowDimensions: () => ({
    width: 375,
    height: 812,
  }),
}));

jest.mock('react-native-tab-view', () => ({
  TabView: ({children, navigationState, renderScene, onIndexChange, renderTabBar, testID}: any) => (
    <div testID={testID}>
      {renderTabBar &&
        renderTabBar({
          navigationState,
          jumpTo: jest.fn(),
          position: {value: navigationState.index},
        })}
      {renderScene({route: navigationState.routes[navigationState.index]})}
    </div>
  ),
  TabBar: ({renderTabBarItem, navigationState}: any) => (
    <div>
      {navigationState.routes.map((route: any, index: number) =>
        renderTabBarItem({
          route,
          onPress: () => {},
          focused: index === navigationState.index,
        }),
      )}
    </div>
  ),
}));

jest.mock('react-native-gesture-handler', () => ({
  FlatList: ({data, renderItem, ItemSeparatorComponent, keyExtractor}: any) => (
    <div>
      {data &&
        data.map((item: any, index: number) => (
          <div key={keyExtractor ? keyExtractor(item, index) : index}>
            {renderItem({item, index})}
            {ItemSeparatorComponent && index < data.length - 1 && ItemSeparatorComponent()}
          </div>
        ))}
    </div>
  ),
}));

jest.mock('../../../utils/DimensionUtils', () => ({
  getWindowWidth: jest.fn(() => 375),
}));

jest.mock('../../utils/FormatUtils', () => ({
  formatPrice: jest.fn((amount: number) => {
    if (amount === undefined || amount === null) return '0';
    return amount.toLocaleString('vi-VN');
  }),
}));

jest.mock('../../locales/i18n', () => ({
  translate: jest.fn((key: string) => {
    const translations: {[key: string]: string} = {
      'components.sourceAccountList.currencyVnd': ' VND',
      'components.sourceAccountList.defaultAccount': 'Mặc định',
    };
    return translations[key] || key;
  }),
}));

jest.mock('msb-host-shared-module', () => ({
  hostSharedModule: {
    d: {
      domainService: {
        undevelopedFeature: jest.fn(),
      },
    },
  },
}));

jest.mock('msb-shared-component', () => ({
  MSBTouchable: ({children, onPress, style, testID}: any) => (
    <button testID={testID} style={style} onClick={onPress}>
      {children}
    </button>
  ),
  MSBTextBase: ({content, style, children, testID, numberOfLines}: any) => (
    <span testID={testID} style={style} data-number-of-lines={numberOfLines}>
      {content || children}
    </span>
  ),
  MSBIcon: ({icon, iconColor, iconSize}: any) => (
    <span data-icon={icon} style={{color: iconColor, fontSize: iconSize}}>
      {icon}
    </span>
  ),
  MSBFastImage: ({nameImage, style, folder, resizeMode}: any) => (
    <img style={style} src={`${folder}/${nameImage}`} alt={nameImage} data-resize-mode={resizeMode} />
  ),
  MSBIcons: {
    IconCheck: 'check',
  },
  MSBFolderImage: {
    IMAGES: 'images',
  },
  MAX_WIDTH: 375,
  SizeAlias: {
    Spacing4xSmall: 4,
    SpacingMedium: 16,
  },
  createMSBStyleSheet: jest.fn(styleFunction =>
    styleFunction({
      ColorGlobal: {
        NeutralWhite: '#FFFFFF',
        Brand500: '#007AFF',
        Neutral300: '#CCCCCC',
        Neutral100: '#F0F0F0',
        Neutral500: '#888888',
      },
      ColorToggle: {
        SurfaceDisable: '#E0E0E0',
      },
      ColorField: {
        BorderDefault: '#E0E0E0',
      },
      ColorDataView: {
        TextSub: '#666666',
        TextMain: '#000000',
      },
      ColorTag: {
        SurfaceBlue: '#E3F2FD',
        TextBlue: '#1976D2',
      },
      ColorItem: {
        IconBrand: '#007AFF',
      },
      SizeGlobal: {
        Size50: 50,
        Size100: 100,
        Size200: 200,
        Size300: 300,
        Size400: 400,
        Size800: 800,
        Size1000: 1000,
        Size1600: 1600,
      },
      Typography: {
        small_regular: {
          fontSize: 12,
          fontWeight: 'normal',
        },
        small_medium: {
          fontSize: 12,
          fontWeight: '500',
        },
        base_regular: {
          fontSize: 14,
          fontWeight: 'normal',
        },
        base_semiBold: {
          fontSize: 14,
          fontWeight: '600',
        },
        h4_semiBold: {
          fontSize: 18,
          fontWeight: '600',
        },
      },
    }),
  ),
  useMSBStyles: jest.fn(() => ({
    styles: {
      listContainer: {
        flex: 1,
        marginHorizontal: 400,
      },
      wrapItem: {
        alignItems: 'center',
        flexDirection: 'row',
        justifyContent: 'space-between',
      },
      itemContainer: {
        paddingVertical: 4,
        flex: 1,
      },
      accNumber: {
        alignItems: 'center',
        flexDirection: 'row',
      },
      txtBank: {
        fontSize: 12,
        fontWeight: 'normal',
        color: '#666666',
      },
      dot: {
        backgroundColor: '#E0E0E0',
        borderRadius: 50,
        height: 100,
        marginHorizontal: 200,
        width: 100,
      },
      txtName: {
        fontSize: 12,
        fontWeight: 'normal',
        color: '#666666',
        flex: 1,
        marginRight: 200,
      },
      txtAmount: {
        fontSize: 14,
        fontWeight: '600',
        color: '#000000',
        marginTop: 100,
      },
      txtCurrency: {
        fontSize: 14,
        fontWeight: '600',
        color: '#888888',
      },
      accDefault: {
        alignItems: 'center',
        backgroundColor: '#E3F2FD',
        borderRadius: 300,
        marginTop: 100,
        paddingVertical: 100,
        width: 93.75, // 375/4
      },
      txtDefault: {
        fontSize: 12,
        fontWeight: '500',
        color: '#1976D2',
      },
      separator: {
        backgroundColor: '#E0E0E0',
        height: 1,
      },
      tabBar: {
        backgroundColor: '#FFFFFF',
        elevation: 50,
        shadowColor: '#CCCCCC',
        shadowOffset: {width: 0, height: 1},
        shadowOpacity: 0.2,
        shadowRadius: 50,
        borderBottomWidth: 1,
        borderBottomColor: '#F0F0F0',
      },
      tabContainer: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        flex: 1,
      },
      tabItem: {
        alignItems: 'center',
        width: 187.5, // 375/2
        paddingVertical: 300,
      },
      tabText: {
        fontSize: 14,
        fontWeight: 'normal',
      },
      tabTextActive: {
        fontSize: 14,
        fontWeight: '600',
        color: '#007AFF',
      },
      tabIndicator: {
        backgroundColor: '#007AFF',
        bottom: 0,
        height: 50,
        position: 'absolute',
        width: '100%',
      },
      indicator: {
        height: 0,
      },
      emptyTabContent: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        padding: 16,
      },
      emptyTabText: {
        fontSize: 14,
        fontWeight: 'normal',
        color: '#888888',
      },
      cardRow: {
        flexDirection: 'row',
        alignItems: 'center',
      },
      cardImageContainer: {
        marginRight: 300,
      },
      cardImage: {
        width: 1600,
        height: 1000,
        borderRadius: 200,
        alignItems: 'center',
        justifyContent: 'center',
        overflow: 'hidden',
      },
      cardInfo: {
        flex: 1,
      },
      txtCardNumber: {
        fontSize: 12,
        fontWeight: 'normal',
        color: '#666666',
        marginBottom: 100,
      },
    },
    theme: {
      ColorItem: {
        IconBrand: '#007AFF',
      },
      SizeGlobal: {
        Size800: 800,
      },
    },
  })),
}));

// Mock SourceAccountModel
const mockAccount = {
  id: '1',
  BBAN: '**********',
  bankAlias: 'VCB',
  availableBalance: 1000000,
  isDefault: 'Y',
  userPreferences: {
    alias: 'My VCB Account',
  },
};

const mockAccount2 = {
  id: '2',
  BBAN: '**********',
  bankAlias: 'TCB',
  availableBalance: 500000,
  isDefault: 'N',
  userPreferences: null,
};

describe('SourceAccountList', () => {
  const defaultProps = {
    accountList: [mockAccount, mockAccount2],
    onSelectAcount: jest.fn(),
    accSelected: mockAccount,
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('rendering', () => {
    it('should render with account list', () => {
      const {getByText, getByTestId} = render(<SourceAccountList {...defaultProps} />);

      expect(getByTestId('transfer.sourceAccountList.changeTab')).toBeTruthy();
      expect(getByText('Tài khoản')).toBeTruthy();
      expect(getByText('Thẻ tín dụng')).toBeTruthy();
      expect(getByText('**********')).toBeTruthy();
      expect(getByText('My VCB Account')).toBeTruthy();
    });

    it('should render account tab by default', () => {
      const {getByText} = render(<SourceAccountList {...defaultProps} />);

      expect(getByText('**********')).toBeTruthy();
      expect(getByText('**********')).toBeTruthy();
    });

    it('should render default account badge', () => {
      const {getByText} = render(<SourceAccountList {...defaultProps} />);

      expect(getByText('Mặc định')).toBeTruthy();
    });

    it('should render check icon for selected account', () => {
      const {container} = render(<SourceAccountList {...defaultProps} />);
      const checkIcon = container.querySelector('[data-icon="check"]');

      expect(checkIcon).toBeTruthy();
    });

    it('should render account balance with currency', () => {
      const {getByText} = render(<SourceAccountList {...defaultProps} />);

      expect(getByText('1,000,000')).toBeTruthy();
      expect(getByText(' VND')).toBeTruthy();
    });

    it('should use bank alias when no user preference alias', () => {
      const {getByText} = render(<SourceAccountList {...defaultProps} />);

      expect(getByText('TCB')).toBeTruthy();
    });
  });

  describe('account selection', () => {
    it('should call onSelectAcount when account is pressed', () => {
      const onSelectAcount = jest.fn();
      const props = {
        ...defaultProps,
        onSelectAcount,
      };

      const {getByTestId} = render(<SourceAccountList {...props} />);
      const defaultAccountButton = getByTestId('transfer.sourceAccountList.selectAccount.default');

      fireEvent.click(defaultAccountButton);

      expect(onSelectAcount).toHaveBeenCalledWith(mockAccount);
    });

    it('should call onSelectAcount for non-default account', () => {
      const onSelectAcount = jest.fn();
      const props = {
        ...defaultProps,
        onSelectAcount,
      };

      const {getByTestId} = render(<SourceAccountList {...props} />);
      const accountButton = getByTestId('transfer.sourceAccountList.selectAccount.1');

      fireEvent.click(accountButton);

      expect(onSelectAcount).toHaveBeenCalledWith(mockAccount2);
    });

    it('should not throw error when onSelectAcount is undefined', () => {
      const props = {
        ...defaultProps,
        onSelectAcount: undefined,
      };

      const {getByTestId} = render(<SourceAccountList {...props} />);
      const accountButton = getByTestId('transfer.sourceAccountList.selectAccount.default');

      expect(() => fireEvent.click(accountButton)).not.toThrow();
    });
  });

  describe('edge cases', () => {
    it('should handle empty account list', () => {
      const props = {
        accountList: [],
        onSelectAcount: jest.fn(),
        accSelected: undefined,
      };

      expect(() => render(<SourceAccountList {...props} />)).not.toThrow();
    });

    it('should handle undefined account list', () => {
      const props = {
        accountList: undefined as any,
        onSelectAcount: jest.fn(),
        accSelected: undefined,
      };

      expect(() => render(<SourceAccountList {...props} />)).not.toThrow();
    });

    it('should handle account with missing properties', () => {
      const incompleteAccount = {
        id: '3',
        BBAN: undefined,
        bankAlias: undefined,
        availableBalance: undefined,
        isDefault: 'N',
        userPreferences: undefined,
      };

      const props = {
        accountList: [incompleteAccount],
        onSelectAcount: jest.fn(),
        accSelected: undefined,
      };

      expect(() => render(<SourceAccountList {...props} />)).not.toThrow();
    });

    it('should handle large account numbers', () => {
      const longAccount = {
        ...mockAccount,
        BBAN: '****************************************',
      };

      const props = {
        accountList: [longAccount],
        onSelectAcount: jest.fn(),
        accSelected: longAccount,
      };

      const {getByText} = render(<SourceAccountList {...props} />);

      expect(getByText('****************************************')).toBeTruthy();
    });

    it('should handle zero balance', () => {
      const zeroBalanceAccount = {
        ...mockAccount,
        availableBalance: 0,
      };

      const props = {
        accountList: [zeroBalanceAccount],
        onSelectAcount: jest.fn(),
        accSelected: zeroBalanceAccount,
      };

      const {getByText} = render(<SourceAccountList {...props} />);

      expect(getByText('0')).toBeTruthy();
    });
  });

  describe('accessibility', () => {
    it('should have accessible testIDs for accounts', () => {
      const {getByTestId} = render(<SourceAccountList {...defaultProps} />);

      expect(getByTestId('transfer.sourceAccountList.selectAccount.default')).toBeTruthy();
      expect(getByTestId('transfer.sourceAccountList.selectAccount.1')).toBeTruthy();
    });

    it('should have accessible tab view testID', () => {
      const {getByTestId} = render(<SourceAccountList {...defaultProps} />);

      expect(getByTestId('transfer.sourceAccountList.changeTab')).toBeTruthy();
    });

    it('should render clickable account items', () => {
      const {getAllByRole} = render(<SourceAccountList {...defaultProps} />);
      const buttons = getAllByRole('button');

      expect(buttons.length).toBeGreaterThan(0);
    });
  });

  describe('formatting', () => {
    it('should format large balances correctly', () => {
      const largeBalanceAccount = {
        ...mockAccount,
        availableBalance: **********,
      };

      const props = {
        accountList: [largeBalanceAccount],
        onSelectAcount: jest.fn(),
        accSelected: largeBalanceAccount,
      };

      const {getByText} = render(<SourceAccountList {...props} />);

      expect(getByText('1,234,567,890')).toBeTruthy();
    });

    it('should truncate long account names', () => {
      const longNameAccount = {
        ...mockAccount,
        userPreferences: {
          alias: 'Very Long Account Name That Should Be Truncated',
        },
      };

      const props = {
        accountList: [longNameAccount],
        onSelectAcount: jest.fn(),
        accSelected: longNameAccount,
      };

      const {container} = render(<SourceAccountList {...props} />);
      const nameElement = container.querySelector('[data-number-of-lines="1"]');

      expect(nameElement).toBeTruthy();
    });
  });
});
