import React from 'react';
import {render, fireEvent} from '@testing-library/react-native';
import {SwipeableBillItem, BillItem} from '../index';

// Mock all dependencies
jest.mock('react-native-reanimated', () => {
  const Reanimated = require('react-native-reanimated/mock');
  Reanimated.default.call = () => {};
  return {
    ...Reanimated,
    interpolate: jest.fn(() => 0),
    useAnimatedStyle: jest.fn(() => ({})),
  };
});

jest.mock('msb-shared-component', () => ({
  ColorGlobal: {White: '#FFFFFF'},
  SizeGlobal: {Size1600: 16, Size2400: 24, Size100: 1},
  MSBIcons: {IconEdit: 'edit', IconTrashDeleteBin: 'trash'},
  MSBFolderImage: {LOGO_TOPUP: 'topup', LOGO_BILLING: 'billing', ICON_SVG: 'svg'},
  MSBButton: jest.fn(({label, onPress, ...props}) => {
    const React = require('react');
    const {TouchableOpacity, Text} = require('react-native');
    return React.createElement(
      TouchableOpacity,
      {onPress, 'data-testid': 'msb-button', ...props},
      React.createElement(Text, null, label)
    );
  }),
  MSBFastImage: jest.fn(({nameImage, folder, ...props}) => {
    const React = require('react');
    const {Image} = require('react-native');
    return React.createElement(Image, {
      'data-testid': 'msb-fast-image',
      source: {uri: `${folder}/${nameImage}`},
      ...props
    });
  }),
  MSBIcon: jest.fn(({icon, onIconClick, ...props}) => {
    const React = require('react');
    const {TouchableOpacity, Text} = require('react-native');
    return React.createElement(
      TouchableOpacity,
      {onPress: onIconClick, 'data-testid': 'msb-icon', ...props},
      React.createElement(Text, null, icon)
    );
  }),
  MSBTextBase: jest.fn(({content, children, ...props}) => {
    const React = require('react');
    const {Text} = require('react-native');
    return React.createElement(Text, {'data-testid': 'msb-text-base', ...props}, content || children);
  }),
  MSBTouchable: jest.fn(({onPress, children, ...props}) => {
    const React = require('react');
    const {TouchableOpacity} = require('react-native');
    return React.createElement(TouchableOpacity, {onPress, 'data-testid': 'msb-touchable', ...props}, children);
  }),
  useMSBStyles: jest.fn(() => ({
    styles: {
      container: {},
      icon: {},
      subContainer: {},
      title: {},
      subTitleContainer: {},
      subTitle: {},
      dot: {},
      subTitleFlex: {},
      editAnimated: {},
      childViewAnimated: {},
      deleteAnimated: {},
    },
    theme: {Typography: {caption_regular: {fontSize: 12}}},
  })),
}));

jest.mock('../swiable-row/SwipeableRow', () => ({
  SwipeableRow: jest.fn(({children, renderRightActions, disabledSwipe, ...props}) => {
    const React = require('react');
    const {View} = require('react-native');
    return React.createElement(
      View,
      {'data-testid': 'swipeable-row', ...props},
      children,
      renderRightActions && React.createElement(View, {'data-testid': 'right-actions'})
    );
  }),
}));

jest.mock('../highlight-text', () => {
  return jest.fn(({text, search, ...props}) => {
    const React = require('react');
    const {Text} = require('react-native');
    return React.createElement(Text, {'data-testid': 'highlight-text', ...props}, text);
  });
});

jest.mock('../../../locales/i18n', () => ({
  translate: jest.fn((key: string) => {
    const translations: Record<string, string> = {
      'edit': 'Sửa',
      'delete': 'Xóa',
      'common.payment': 'Thanh toán',
    };
    return translations[key] || key;
  }),
}));

// Mock IBillContact
const createMockBillContact = (overrides = {}) => ({
  isEditable: jest.fn(() => false),
  getIcon: jest.fn(() => 'test-icon'),
  isTopup: jest.fn(() => false),
  getCustomerName: jest.fn(() => 'Test Customer'),
  getSubtitle: jest.fn(() => 'Test Subtitle'),
  getBillCode: jest.fn(() => 'BILL123'),
  getReminderStatus: jest.fn(() => 'ACTIVE'),
  ...overrides,
});

describe('BillItem', () => {
  const mockOnClick = jest.fn();
  const mockOnPressPayment = jest.fn();
  const mockItem = createMockBillContact();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('rendering', () => {
    it('should render basic bill item', () => {
      const {getByTestId, getAllByTestId} = render(
        <BillItem
          item={mockItem}
          highlight=""
          onClick={mockOnClick}
          onPressPayment={mockOnPressPayment}
        />
      );

      expect(getByTestId('msb-touchable')).toBeTruthy();
      expect(getAllByTestId('msb-fast-image')).toHaveLength(2); // icon + dot
      expect(getAllByTestId('highlight-text')).toHaveLength(3); // name + subtitle + bill code
    });

    it('should render with custom icon when available', () => {
      const customItem = createMockBillContact({
        getIcon: jest.fn(() => 'custom-icon'),
        isTopup: jest.fn(() => true),
      });

      render(<BillItem item={customItem} highlight="" onClick={mockOnClick} />);

      expect(customItem.getIcon).toHaveBeenCalled();
      expect(customItem.isTopup).toHaveBeenCalled();
    });

    it('should render default icon when no custom icon', () => {
      const noIconItem = createMockBillContact({
        getIcon: jest.fn(() => null),
      });

      const {getAllByTestId} = render(<BillItem item={noIconItem} highlight="" onClick={mockOnClick} />);

      expect(getAllByTestId('msb-fast-image')).toHaveLength(2); // default icon + dot
    });

    it('should render payment button when reminder is active', () => {
      const activeItem = createMockBillContact({
        getReminderStatus: jest.fn(() => 'ACTIVE'),
      });

      const {getByTestId} = render(
        <BillItem
          item={activeItem}
          highlight=""
          onClick={mockOnClick}
          onPressPayment={mockOnPressPayment}
        />
      );

      expect(getByTestId('msb-button')).toBeTruthy();
    });

    it('should not render payment button when reminder is inactive', () => {
      const inactiveItem = createMockBillContact({
        getReminderStatus: jest.fn(() => 'INACTIVE'),
      });

      const {queryByTestId} = render(
        <BillItem
          item={inactiveItem}
          highlight=""
          onClick={mockOnClick}
          onPressPayment={mockOnPressPayment}
        />
      );

      expect(queryByTestId('msb-button')).toBeNull();
    });

    it('should not render payment button when onPressPayment is not provided', () => {
      const {queryByTestId} = render(
        <BillItem item={mockItem} highlight="" onClick={mockOnClick} />
      );

      expect(queryByTestId('msb-button')).toBeNull();
    });
  });

  describe('interactions', () => {
    it('should call onClick when item is pressed', () => {
      const {getByTestId} = render(
        <BillItem item={mockItem} highlight="" onClick={mockOnClick} />
      );

      fireEvent.press(getByTestId('msb-touchable'));
      expect(mockOnClick).toHaveBeenCalledWith(mockItem);
    });

    it('should call onPressPayment when payment button is pressed', () => {
      const {getByTestId} = render(
        <BillItem
          item={mockItem}
          highlight=""
          onClick={mockOnClick}
          onPressPayment={mockOnPressPayment}
        />
      );

      fireEvent.press(getByTestId('msb-button'));
      expect(mockOnPressPayment).toHaveBeenCalledWith(mockItem);
    });
  });

  describe('highlight functionality', () => {
    it('should pass highlight text to HighlightText components', () => {
      const HighlightText = require('../highlight-text');
      const highlightTerm = 'test';

      render(<BillItem item={mockItem} highlight={highlightTerm} onClick={mockOnClick} />);

      expect(HighlightText).toHaveBeenCalledWith(
        expect.objectContaining({
          text: 'Test Customer',
          search: highlightTerm,
        }),
        {}
      );
    });
  });
});

describe('SwipeableBillItem', () => {
  const mockOnClick = jest.fn();
  const mockEditOnClick = jest.fn();
  const mockDeleteOnClick = jest.fn();
  const mockOnPressPayment = jest.fn();
  const mockItem = createMockBillContact();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('rendering', () => {
    it('should render swipeable bill item', () => {
      const {getByTestId} = render(
        <SwipeableBillItem
          item={mockItem}
          highlight=""
          onClick={mockOnClick}
          editOnClick={mockEditOnClick}
          deleteOnClick={mockDeleteOnClick}
          onPressPayment={mockOnPressPayment}
        />
      );

      expect(getByTestId('swipeable-row')).toBeTruthy();
    });

    it('should disable swipe when item is editable', () => {
      const editableItem = createMockBillContact({
        isEditable: jest.fn(() => true),
      });

      const SwipeableRow = require('../swiable-row/SwipeableRow').SwipeableRow;

      render(
        <SwipeableBillItem
          item={editableItem}
          highlight=""
          onClick={mockOnClick}
          editOnClick={mockEditOnClick}
          deleteOnClick={mockDeleteOnClick}
        />
      );

      expect(SwipeableRow).toHaveBeenCalledWith(
        expect.objectContaining({
          disabledSwipe: true,
        }),
        {}
      );
    });

    it('should enable swipe when item is not editable', () => {
      const nonEditableItem = createMockBillContact({
        isEditable: jest.fn(() => false),
      });

      const SwipeableRow = require('../swiable-row/SwipeableRow').SwipeableRow;

      render(
        <SwipeableBillItem
          item={nonEditableItem}
          highlight=""
          onClick={mockOnClick}
          editOnClick={mockEditOnClick}
          deleteOnClick={mockDeleteOnClick}
        />
      );

      expect(SwipeableRow).toHaveBeenCalledWith(
        expect.objectContaining({
          disabledSwipe: false,
        }),
        {}
      );
    });
  });

  describe('swipe actions', () => {
    it('should render right actions with edit and delete buttons', () => {
      const SwipeableRow = require('../swiable-row/SwipeableRow').SwipeableRow;

      render(
        <SwipeableBillItem
          item={mockItem}
          highlight=""
          onClick={mockOnClick}
          editOnClick={mockEditOnClick}
          deleteOnClick={mockDeleteOnClick}
        />
      );

      expect(SwipeableRow).toHaveBeenCalledWith(
        expect.objectContaining({
          renderRightActions: expect.any(Function),
        }),
        {}
      );
    });
  });

  describe('edge cases', () => {
    it('should handle missing optional callbacks', () => {
      expect(() => {
        render(
          <SwipeableBillItem
            item={mockItem}
            highlight=""
            onClick={mockOnClick}
          />
        );
      }).not.toThrow();
    });

    it('should handle item with missing methods', () => {
      const incompleteItem = {
        isEditable: jest.fn(() => false),
        getIcon: jest.fn(() => null),
        getCustomerName: jest.fn(() => null),
        getSubtitle: jest.fn(() => null),
        getBillCode: jest.fn(() => null),
      };

      expect(() => {
        render(
          <BillItem
            item={incompleteItem as any}
            highlight=""
            onClick={mockOnClick}
          />
        );
      }).not.toThrow();
    });
  });
});
