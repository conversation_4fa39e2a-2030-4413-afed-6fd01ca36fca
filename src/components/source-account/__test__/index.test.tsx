import React from 'react';
import {describe, it, expect, jest, beforeEach} from '@jest/globals';
import {render, fireEvent} from '@testing-library/react-native';
import SourceAccount from '../index';

// Mock dependencies
jest.mock('react-native', () => ({
  View: ({children, style, testID}: any) => (
    <div testID={testID} style={style}>
      {children}
    </div>
  ),
}));

jest.mock('../../../utils/FormatUtils', () => ({
  formatPrice: jest.fn((amount: number) => {
    if (amount === undefined || amount === null) return '0';
    return amount.toLocaleString('vi-VN');
  }),
}));

jest.mock('msb-shared-component', () => ({
  MSBTouchable: ({children, onPress, style, testID, activeOpacity}: any) => (
    <button testID={testID} style={style} onClick={onPress} data-active-opacity={activeOpacity}>
      {children}
    </button>
  ),
  MSBTextBase: ({content, style, children, testID}: any) => (
    <span testID={testID} style={style}>
      {content || children}
    </span>
  ),
  MSBIcon: ({folderIcon, icon, onIconClick, styleContainer}: any) => (
    <button style={styleContainer} onClick={onIconClick} data-icon={icon} data-folder={folderIcon}>
      {icon}
    </button>
  ),
  MSBFolderImage: {
    ICON_SVG: 'icon-svg',
  },
  ColorField: {
    IconDefault: '#999999',
    BorderDefault: '#E0E0E0',
  },
  ColorItem: {
    IconDot: '#CCCCCC',
    TextSub: '#666666',
    TextMain: '#000000',
  },
  ColorLabelCaption: {
    TextMain: '#333333',
  },
  Fonts: {
    msb_font_icon: 'MSBFontIcon',
  },
  createMSBStyleSheet: jest.fn(styleFunction =>
    styleFunction({
      ColorGlobal: {
        Neutral500: '#888888',
        Red500: '#FF0000',
      },
      SizeGlobal: {
        Size50: 50,
        Size100: 100,
        Size200: 200,
        Size400: 400,
        Size600: 600,
      },
      Typography: {
        small_medium: {
          fontSize: 12,
          fontWeight: '500',
        },
        small_regular: {
          fontSize: 12,
          fontWeight: 'normal',
        },
        base_semiBold: {
          fontSize: 14,
          fontWeight: '600',
        },
        base_regular: {
          fontSize: 14,
          fontWeight: 'normal',
        },
      },
    }),
  ),
  useMSBStyles: jest.fn(() => ({
    styles: {
      container: {},
      title: {
        fontSize: 12,
        fontWeight: '500',
        color: '#333333',
      },
      sourceAccInfo: {
        alignItems: 'center',
        borderColor: '#E0E0E0',
        borderRadius: 200,
        borderWidth: 1,
        flexDirection: 'row',
        marginTop: 100,
        padding: 400,
      },
      flex: {
        flex: 1,
      },
      accNumber: {
        alignItems: 'center',
        flexDirection: 'row',
        flex: 1,
      },
      txtAcc: {
        fontSize: 12,
        fontWeight: 'normal',
        color: '#666666',
      },
      dot: {
        backgroundColor: '#CCCCCC',
        borderRadius: 50,
        height: 100,
        marginHorizontal: 200,
        width: 100,
      },
      txtBank: {
        fontSize: 12,
        fontWeight: 'normal',
        color: '#666666',
        flex: 1,
      },
      txtAmount: {
        fontSize: 14,
        fontWeight: '600',
        color: '#000000',
        marginTop: 4,
      },
      txtCurrency: {
        fontSize: 14,
        fontWeight: 'normal',
        color: '#888888',
      },
      marginLeft16: {
        marginLeft: 400,
      },
      errorContainer: {
        borderColor: '#FF0000',
        borderWidth: 1,
      },
      errorTitle: {
        fontSize: 12,
        fontWeight: 'normal',
        color: '#FF0000',
        marginTop: 100,
      },
    },
  })),
  SizeAlias: {
    Spacing4xSmall: 4,
  },
}));

// Mock SourceAccountModel
const mockAccount = {
  BBAN: '**********',
  bankAlias: 'VCB',
  availableBalance: 1000000,
  accountNumber: '**********',
  bankName: 'Vietcombank',
  currency: 'VND',
};

describe('SourceAccount', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('rendering', () => {
    it('should render with account information', () => {
      const props = {
        title: 'Source Account',
        account: mockAccount,
        onSelectAccount: jest.fn(),
      };

      const {getByText} = render(<SourceAccount {...props} />);

      expect(getByText('Source Account')).toBeTruthy();
      expect(getByText('**********')).toBeTruthy();
      expect(getByText('VCB')).toBeTruthy();
      expect(getByText('1,000,000')).toBeTruthy();
      expect(getByText(' VND')).toBeTruthy();
    });

    it('should render without onSelectAccount callback', () => {
      const props = {
        title: 'Source Account',
        account: mockAccount,
      };

      const {getByText, queryByRole} = render(<SourceAccount {...props} />);

      expect(getByText('Source Account')).toBeTruthy();
      expect(getByText('**********')).toBeTruthy();

      // Should not render dropdown icon when no onSelectAccount
      const buttons = queryByRole('button');
      expect(buttons).toBeTruthy(); // Main touchable is still there
    });

    it('should render dropdown icon when onSelectAccount is provided', () => {
      const props = {
        title: 'Source Account',
        account: mockAccount,
        onSelectAccount: jest.fn(),
      };

      const {container} = render(<SourceAccount {...props} />);
      const iconButton = container.querySelector('[data-icon="down"]');

      expect(iconButton).toBeTruthy();
      expect(iconButton?.getAttribute('data-folder')).toBe('icon-svg');
    });

    it('should render error state', () => {
      const props = {
        title: 'Source Account',
        account: mockAccount,
        onSelectAccount: jest.fn(),
        errorTitle: 'Insufficient balance',
      };

      const {getByText} = render(<SourceAccount {...props} />);

      expect(getByText('Insufficient balance')).toBeTruthy();
    });

    it('should apply error styling when error is present', () => {
      const props = {
        title: 'Source Account',
        account: mockAccount,
        errorTitle: 'Error message',
      };

      const {getByText} = render(<SourceAccount {...props} />);
      const errorText = getByText('Error message');

      expect(errorText.style.color).toBe('#FF0000');
    });
  });

  describe('interaction', () => {
    it('should call onSelectAccount when main container is pressed', () => {
      const onSelectAccount = jest.fn();
      const props = {
        title: 'Source Account',
        account: mockAccount,
        onSelectAccount,
      };

      const {getByTestId} = render(<SourceAccount {...props} />);
      const container = getByTestId('payment.sourceAccount.selectSourceAccount');

      fireEvent.click(container);

      expect(onSelectAccount).toHaveBeenCalledTimes(1);
    });

    it('should call onSelectAccount when dropdown icon is pressed', () => {
      const onSelectAccount = jest.fn();
      const props = {
        title: 'Source Account',
        account: mockAccount,
        onSelectAccount,
      };

      const {container} = render(<SourceAccount {...props} />);
      const iconButton = container.querySelector('[data-icon="down"]');

      fireEvent.click(iconButton!);

      expect(onSelectAccount).toHaveBeenCalledTimes(1);
    });

    it('should not throw error when onSelectAccount is undefined', () => {
      const props = {
        title: 'Source Account',
        account: mockAccount,
      };

      const {getByTestId} = render(<SourceAccount {...props} />);
      const container = getByTestId('payment.sourceAccount.selectSourceAccount');

      expect(() => fireEvent.click(container)).not.toThrow();
    });

    it('should set correct activeOpacity based on onSelectAccount presence', () => {
      const propsWithCallback = {
        title: 'Source Account',
        account: mockAccount,
        onSelectAccount: jest.fn(),
      };

      const {getByTestId, rerender} = render(<SourceAccount {...propsWithCallback} />);
      let container = getByTestId('payment.sourceAccount.selectSourceAccount');

      expect(container.getAttribute('data-active-opacity')).toBe('0.8');

      const propsWithoutCallback = {
        title: 'Source Account',
        account: mockAccount,
      };

      rerender(<SourceAccount {...propsWithoutCallback} />);
      container = getByTestId('payment.sourceAccount.selectSourceAccount');

      expect(container.getAttribute('data-active-opacity')).toBe('1');
    });
  });

  describe('formatting', () => {
    it('should format balance correctly', () => {
      const props = {
        title: 'Source Account',
        account: {
          ...mockAccount,
          availableBalance: **********,
        },
        onSelectAccount: jest.fn(),
      };

      const {getByText} = render(<SourceAccount {...props} />);

      expect(getByText('1,234,567,890')).toBeTruthy();
    });

    it('should handle zero balance', () => {
      const props = {
        title: 'Source Account',
        account: {
          ...mockAccount,
          availableBalance: 0,
        },
      };

      const {getByText} = render(<SourceAccount {...props} />);

      expect(getByText('0')).toBeTruthy();
    });

    it('should handle undefined balance', () => {
      const props = {
        title: 'Source Account',
        account: {
          ...mockAccount,
          availableBalance: undefined as any,
        },
      };

      const {getByText} = render(<SourceAccount {...props} />);

      expect(getByText('0')).toBeTruthy();
    });
  });

  describe('edge cases', () => {
    it('should handle undefined account', () => {
      const props = {
        title: 'Source Account',
        account: undefined,
        onSelectAccount: jest.fn(),
      };

      expect(() => render(<SourceAccount {...props} />)).not.toThrow();
    });

    it('should handle missing account properties', () => {
      const incompleteAccount = {
        BBAN: undefined,
        bankAlias: undefined,
        availableBalance: undefined,
      };

      const props = {
        title: 'Source Account',
        account: incompleteAccount as any,
        onSelectAccount: jest.fn(),
      };

      expect(() => render(<SourceAccount {...props} />)).not.toThrow();
    });

    it('should handle empty title', () => {
      const props = {
        title: '',
        account: mockAccount,
        onSelectAccount: jest.fn(),
      };

      const {getByText} = render(<SourceAccount {...props} />);

      expect(getByText('')).toBeTruthy();
    });

    it('should handle long account numbers', () => {
      const props = {
        title: 'Source Account',
        account: {
          ...mockAccount,
          BBAN: '****************************************',
        },
        onSelectAccount: jest.fn(),
      };

      const {getByText} = render(<SourceAccount {...props} />);

      expect(getByText('****************************************')).toBeTruthy();
    });

    it('should handle long bank aliases', () => {
      const props = {
        title: 'Source Account',
        account: {
          ...mockAccount,
          bankAlias: 'Very Long Bank Alias Name',
        },
        onSelectAccount: jest.fn(),
      };

      const {getByText} = render(<SourceAccount {...props} />);

      expect(getByText('Very Long Bank Alias Name')).toBeTruthy();
    });
  });

  describe('accessibility', () => {
    it('should have accessible testID', () => {
      const props = {
        title: 'Source Account',
        account: mockAccount,
        onSelectAccount: jest.fn(),
      };

      const {getByTestId} = render(<SourceAccount {...props} />);

      expect(getByTestId('payment.sourceAccount.selectSourceAccount')).toBeTruthy();
    });

    it('should be clickable when onSelectAccount is provided', () => {
      const props = {
        title: 'Source Account',
        account: mockAccount,
        onSelectAccount: jest.fn(),
      };

      const {getByRole} = render(<SourceAccount {...props} />);

      expect(getByRole('button')).toBeTruthy();
    });
  });

  describe('real-world scenarios', () => {
    it('should display Vietcombank account correctly', () => {
      const vietcombankAccount = {
        BBAN: '**********',
        bankAlias: 'VCB',
        availableBalance: 5000000,
      };

      const props = {
        title: 'Tài khoản nguồn',
        account: vietcombankAccount,
        onSelectAccount: jest.fn(),
      };

      const {getByText} = render(<SourceAccount {...props} />);

      expect(getByText('Tài khoản nguồn')).toBeTruthy();
      expect(getByText('**********')).toBeTruthy();
      expect(getByText('VCB')).toBeTruthy();
      expect(getByText('5,000,000')).toBeTruthy();
    });

    it('should display insufficient balance error', () => {
      const props = {
        title: 'Source Account',
        account: mockAccount,
        onSelectAccount: jest.fn(),
        errorTitle: 'Số dư không đủ để thực hiện giao dịch',
      };

      const {getByText} = render(<SourceAccount {...props} />);

      expect(getByText('Số dư không đủ để thực hiện giao dịch')).toBeTruthy();
    });
  });
});
