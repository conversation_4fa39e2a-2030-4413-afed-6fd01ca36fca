import React from 'react';
import {describe, it, expect, jest, beforeEach} from '@jest/globals';
import {render, fireEvent} from '@testing-library/react-native';
import TransferAccountNumberInput from '../index';

// Mock dependencies
jest.mock('../../../locales/i18n', () => ({
  translate: jest.fn((key: string) => {
    const translations: Record<string, string> = {
      'common.accountNumber': 'Account Number',
      'common.accountNumberPlaceholder': 'Enter account number',
    };
    return translations[key] || key;
  }),
}));

jest.mock('msb-shared-component', () => ({
  MSBInputBase: ({
    label,
    placeholder,
    onChangeText,
    maxLength,
    disabled,
    isDisableRemoveIcon,
    value,
    testID,
    onFocus,
    onBlur,
    errorContent,
    inputRef,
    ...restProps
  }: any) => (
    <div
      testID={testID || 'msb-input-base'}
      data-label={label}
      data-placeholder={placeholder}
      data-max-length={maxLength}
      data-disabled={disabled}
      data-disable-remove-icon={isDisableRemoveIcon}
      data-error-content={errorContent}
      {...restProps}
    >
      <input
        testID="input-element"
        value={value}
        placeholder={placeholder}
        maxLength={maxLength}
        disabled={disabled}
        onChange={(e) => onChangeText?.(e.target.value)}
        onFocus={onFocus}
        onBlur={onBlur}
        ref={inputRef}
      />
    </div>
  ),
}));

describe('TransferAccountNumberInput', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('rendering', () => {
    it('should render with default props', () => {
      const {getByTestId} = render(<TransferAccountNumberInput />);

      const inputBase = getByTestId('msb-input-base');
      expect(inputBase).toBeTruthy();
      expect(inputBase.getAttribute('data-label')).toBe('Account Number');
      expect(inputBase.getAttribute('data-placeholder')).toBe('Enter account number');
      expect(inputBase.getAttribute('data-max-length')).toBe('34');
      expect(inputBase.getAttribute('data-disabled')).toBe('false');
      expect(inputBase.getAttribute('data-disable-remove-icon')).toBe('false');
    });

    it('should render with custom label', () => {
      const customLabel = 'Custom Account Number';
      const {getByTestId} = render(<TransferAccountNumberInput label={customLabel} />);

      const inputBase = getByTestId('msb-input-base');
      expect(inputBase.getAttribute('data-label')).toBe(customLabel);
    });

    it('should render with custom placeholder', () => {
      const customPlaceholder = 'Custom placeholder';
      const {getByTestId} = render(<TransferAccountNumberInput placeholder={customPlaceholder} />);

      const inputBase = getByTestId('msb-input-base');
      expect(inputBase.getAttribute('data-placeholder')).toBe(customPlaceholder);
    });

    it('should render with disabled state', () => {
      const {getByTestId} = render(<TransferAccountNumberInput disabled={true} />);

      const inputBase = getByTestId('msb-input-base');
      expect(inputBase.getAttribute('data-disabled')).toBe('true');
      expect(inputBase.getAttribute('data-disable-remove-icon')).toBe('true');
    });

    it('should render with enabled state', () => {
      const {getByTestId} = render(<TransferAccountNumberInput disabled={false} />);

      const inputBase = getByTestId('msb-input-base');
      expect(inputBase.getAttribute('data-disabled')).toBe('false');
      expect(inputBase.getAttribute('data-disable-remove-icon')).toBe('false');
    });

    it('should render with custom testID', () => {
      const customTestID = 'custom-test-id';
      const {getByTestId} = render(<TransferAccountNumberInput testID={customTestID} />);

      expect(getByTestId(customTestID)).toBeTruthy();
    });

    it('should render with value prop', () => {
      const testValue = '**********';
      const {getByTestId} = render(<TransferAccountNumberInput value={testValue} />);

      const input = getByTestId('input-element');
      expect(input.value).toBe(testValue);
    });

    it('should render with error content', () => {
      const errorMessage = 'Invalid account number';
      const {getByTestId} = render(<TransferAccountNumberInput errorContent={errorMessage} />);

      const inputBase = getByTestId('msb-input-base');
      expect(inputBase.getAttribute('data-error-content')).toBe(errorMessage);
    });
  });

  describe('user interactions', () => {
    it('should call onChangeText when text changes', () => {
      const mockOnChangeText = jest.fn();
      const {getByTestId} = render(<TransferAccountNumberInput onChangeText={mockOnChangeText} />);

      const input = getByTestId('input-element');
      fireEvent.change(input, {target: {value: '123456'}});

      expect(mockOnChangeText).toHaveBeenCalledWith('123456');
      expect(mockOnChangeText).toHaveBeenCalledTimes(1);
    });

    it('should call onFocus when input is focused', () => {
      const mockOnFocus = jest.fn();
      const {getByTestId} = render(<TransferAccountNumberInput onFocus={mockOnFocus} />);

      const input = getByTestId('input-element');
      fireEvent.focus(input);

      expect(mockOnFocus).toHaveBeenCalledTimes(1);
    });

    it('should call onBlur when input loses focus', () => {
      const mockOnBlur = jest.fn();
      const {getByTestId} = render(<TransferAccountNumberInput onBlur={mockOnBlur} />);

      const input = getByTestId('input-element');
      fireEvent.blur(input);

      expect(mockOnBlur).toHaveBeenCalledTimes(1);
    });

    it('should handle multiple text changes', () => {
      const mockOnChangeText = jest.fn();
      const {getByTestId} = render(<TransferAccountNumberInput onChangeText={mockOnChangeText} />);

      const input = getByTestId('input-element');
      
      fireEvent.change(input, {target: {value: '1'}});
      fireEvent.change(input, {target: {value: '12'}});
      fireEvent.change(input, {target: {value: '123'}});

      expect(mockOnChangeText).toHaveBeenCalledTimes(3);
      expect(mockOnChangeText).toHaveBeenNthCalledWith(1, '1');
      expect(mockOnChangeText).toHaveBeenNthCalledWith(2, '12');
      expect(mockOnChangeText).toHaveBeenNthCalledWith(3, '123');
    });

    it('should not call onChangeText when disabled', () => {
      const mockOnChangeText = jest.fn();
      const {getByTestId} = render(<TransferAccountNumberInput disabled={true} onChangeText={mockOnChangeText} />);

      const input = getByTestId('input-element');
      expect(input.disabled).toBe(true);
    });
  });

  describe('prop forwarding', () => {
    it('should forward all additional props to MSBInputBase', () => {
      const additionalProps = {
        autoFocus: true,
        secureTextEntry: false,
        keyboardType: 'numeric',
        returnKeyType: 'done',
      };

      const {getByTestId} = render(<TransferAccountNumberInput {...additionalProps} />);

      const inputBase = getByTestId('msb-input-base');
      expect(inputBase).toHaveProperty('autoFocus', true);
      expect(inputBase).toHaveProperty('secureTextEntry', false);
      expect(inputBase).toHaveProperty('keyboardType', 'numeric');
      expect(inputBase).toHaveProperty('returnKeyType', 'done');
    });

    it('should forward ref prop', () => {
      const mockRef = {current: null};
      const {getByTestId} = render(<TransferAccountNumberInput inputRef={mockRef} />);

      const input = getByTestId('input-element');
      expect(input.ref).toBe(mockRef);
    });

    it('should override default props with custom props', () => {
      const customProps = {
        label: 'Custom Label',
        placeholder: 'Custom Placeholder',
        maxLength: 20,
      };

      const {getByTestId} = render(<TransferAccountNumberInput {...customProps} />);

      const inputBase = getByTestId('msb-input-base');
      expect(inputBase.getAttribute('data-label')).toBe('Custom Label');
      expect(inputBase.getAttribute('data-placeholder')).toBe('Custom Placeholder');
      expect(inputBase.getAttribute('data-max-length')).toBe('20');
    });
  });

  describe('default behavior', () => {
    it('should have maxLength of 34 by default', () => {
      const {getByTestId} = render(<TransferAccountNumberInput />);

      const inputBase = getByTestId('msb-input-base');
      expect(inputBase.getAttribute('data-max-length')).toBe('34');
    });

    it('should use translated labels by default', () => {
      const {getByTestId} = render(<TransferAccountNumberInput />);

      const inputBase = getByTestId('msb-input-base');
      expect(inputBase.getAttribute('data-label')).toBe('Account Number');
      expect(inputBase.getAttribute('data-placeholder')).toBe('Enter account number');
    });

    it('should not be disabled by default', () => {
      const {getByTestId} = render(<TransferAccountNumberInput />);

      const inputBase = getByTestId('msb-input-base');
      expect(inputBase.getAttribute('data-disabled')).toBe('false');
      expect(inputBase.getAttribute('data-disable-remove-icon')).toBe('false');
    });
  });

  describe('edge cases', () => {
    it('should handle empty string value', () => {
      const {getByTestId} = render(<TransferAccountNumberInput value="" />);

      const input = getByTestId('input-element');
      expect(input.value).toBe('');
    });

    it('should handle null onChangeText', () => {
      const {getByTestId} = render(<TransferAccountNumberInput onChangeText={null as any} />);

      const input = getByTestId('input-element');
      expect(() => fireEvent.change(input, {target: {value: '123'}})).not.toThrow();
    });

    it('should handle undefined onChangeText', () => {
      const {getByTestId} = render(<TransferAccountNumberInput onChangeText={undefined} />);

      const input = getByTestId('input-element');
      expect(() => fireEvent.change(input, {target: {value: '123'}})).not.toThrow();
    });

    it('should handle very long input values', () => {
      const longValue = '1'.repeat(100);
      const mockOnChangeText = jest.fn();
      const {getByTestId} = render(<TransferAccountNumberInput onChangeText={mockOnChangeText} />);

      const input = getByTestId('input-element');
      fireEvent.change(input, {target: {value: longValue}});

      expect(mockOnChangeText).toHaveBeenCalledWith(longValue);
    });

    it('should handle special characters in input', () => {
      const specialValue = '!@#$%^&*()';
      const mockOnChangeText = jest.fn();
      const {getByTestId} = render(<TransferAccountNumberInput onChangeText={mockOnChangeText} />);

      const input = getByTestId('input-element');
      fireEvent.change(input, {target: {value: specialValue}});

      expect(mockOnChangeText).toHaveBeenCalledWith(specialValue);
    });
  });

  describe('accessibility', () => {
    it('should have accessible label', () => {
      const {getByTestId} = render(<TransferAccountNumberInput />);

      const inputBase = getByTestId('msb-input-base');
      expect(inputBase.getAttribute('data-label')).toBe('Account Number');
    });

    it('should have accessible placeholder', () => {
      const {getByTestId} = render(<TransferAccountNumberInput />);

      const inputBase = getByTestId('msb-input-base');
      expect(inputBase.getAttribute('data-placeholder')).toBe('Enter account number');
    });

    it('should support custom accessibility props', () => {
      const accessibilityProps = {
        accessibilityLabel: 'Custom accessibility label',
        accessibilityHint: 'Custom accessibility hint',
      };

      const {getByTestId} = render(<TransferAccountNumberInput {...accessibilityProps} />);

      const inputBase = getByTestId('msb-input-base');
      expect(inputBase).toHaveProperty('accessibilityLabel', 'Custom accessibility label');
      expect(inputBase).toHaveProperty('accessibilityHint', 'Custom accessibility hint');
    });
  });

  describe('performance', () => {
    it('should handle rapid text changes efficiently', () => {
      const mockOnChangeText = jest.fn();
      const {getByTestId} = render(<TransferAccountNumberInput onChangeText={mockOnChangeText} />);

      const input = getByTestId('input-element');
      
      // Simulate rapid typing
      const startTime = performance.now();
      for (let i = 0; i < 100; i++) {
        fireEvent.change(input, {target: {value: i.toString()}});
      }
      const endTime = performance.now();

      expect(mockOnChangeText).toHaveBeenCalledTimes(100);
      expect(endTime - startTime).toBeLessThan(100); // Should complete quickly
    });

    it('should not cause memory leaks with frequent re-renders', () => {
      const {rerender} = render(<TransferAccountNumberInput value="initial" />);

      // Simulate frequent re-renders
      for (let i = 0; i < 50; i++) {
        rerender(<TransferAccountNumberInput value={`value-${i}`} />);
      }

      // Should not throw or cause performance issues
      expect(true).toBe(true);
    });
  });

  describe('integration scenarios', () => {
    it('should work correctly in a form scenario', () => {
      const mockOnChangeText = jest.fn();
      const mockOnBlur = jest.fn();
      const mockOnFocus = jest.fn();

      const {getByTestId} = render(
        <TransferAccountNumberInput
          value="**********"
          onChangeText={mockOnChangeText}
          onFocus={mockOnFocus}
          onBlur={mockOnBlur}
          errorContent=""
        />
      );

      const input = getByTestId('input-element');

      // Simulate user interaction flow
      fireEvent.focus(input);
      fireEvent.change(input, {target: {value: '**********123'}});
      fireEvent.blur(input);

      expect(mockOnFocus).toHaveBeenCalledTimes(1);
      expect(mockOnChangeText).toHaveBeenCalledWith('**********123');
      expect(mockOnBlur).toHaveBeenCalledTimes(1);
    });

    it('should maintain component interface contract', () => {
      const requiredProps = {
        onChangeText: jest.fn(),
        value: '*********',
        disabled: false,
        label: 'Test Label',
        placeholder: 'Test Placeholder',
      };

      const {getByTestId} = render(<TransferAccountNumberInput {...requiredProps} />);

      const inputBase = getByTestId('msb-input-base');
      expect(inputBase.getAttribute('data-label')).toBe('Test Label');
      expect(inputBase.getAttribute('data-placeholder')).toBe('Test Placeholder');
      expect(inputBase.getAttribute('data-disabled')).toBe('false');
      expect(inputBase.getAttribute('data-max-length')).toBe('34');
    });
  });
});
