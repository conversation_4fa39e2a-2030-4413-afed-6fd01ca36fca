import {describe, it, expect} from '@jest/globals';
import {mapGetProfileResponseToModel} from '../GetProfileMapper';
import {GetProfileResponse} from '../../../models/get-profile/GetProfileResponse';
import {GetProfileModel} from '../../../../domain/entities/get-profile/GetProfileModel';
import {BaseResponse} from '../../../../core/BaseResponse';

describe('GetProfileMapper', () => {
  describe('mapGetProfileResponseToModel', () => {
    it('should create GetProfileModel instance with serviceGroup from BaseResponse', () => {
      const mockResponse: BaseResponse<GetProfileResponse> = {
        serviceGroup: 'PAYMENT_SERVICE',
        data: {
          userId: '12345',
          userName: '<PERSON>',
        },
      };

      const result = mapGetProfileResponseToModel(mockResponse);

      expect(result).toBeInstanceOf(GetProfileModel);
      expect(result.serviceGroup).toBe('PAYMENT_SERVICE');
    });

    it('should handle response with null serviceGroup', () => {
      const mockResponse: BaseResponse<GetProfileResponse> = {
        serviceGroup: null as any,
        data: {
          userId: '12345',
          userName: 'John Doe',
        },
      };

      const result = mapGetProfileResponseToModel(mockResponse);

      expect(result).toBeInstanceOf(GetProfileModel);
      expect(result.serviceGroup).toBeNull();
    });

    it('should handle response with undefined serviceGroup', () => {
      const mockResponse: BaseResponse<GetProfileResponse> = {
        serviceGroup: undefined as any,
        data: {
          userId: '12345',
          userName: 'John Doe',
        },
      };

      const result = mapGetProfileResponseToModel(mockResponse);

      expect(result).toBeInstanceOf(GetProfileModel);
      expect(result.serviceGroup).toBeUndefined();
    });

    it('should handle response with empty string serviceGroup', () => {
      const mockResponse: BaseResponse<GetProfileResponse> = {
        serviceGroup: '',
        data: {
          userId: '12345',
          userName: 'John Doe',
        },
      };

      const result = mapGetProfileResponseToModel(mockResponse);

      expect(result).toBeInstanceOf(GetProfileModel);
      expect(result.serviceGroup).toBe('');
    });

    it('should handle response with various serviceGroup values', () => {
      const serviceGroups = [
        'PAYMENT_SERVICE',
        'BILL_SERVICE',
        'TRANSFER_SERVICE',
        'ACCOUNT_SERVICE',
        'NOTIFICATION_SERVICE',
      ];

      serviceGroups.forEach(serviceGroup => {
        const mockResponse: BaseResponse<GetProfileResponse> = {
          serviceGroup,
          data: {
            userId: '12345',
            userName: 'Test User',
          },
        };

        const result = mapGetProfileResponseToModel(mockResponse);

        expect(result).toBeInstanceOf(GetProfileModel);
        expect(result.serviceGroup).toBe(serviceGroup);
      });
    });

    it('should handle response with complex data structure', () => {
      const mockResponse: BaseResponse<GetProfileResponse> = {
        serviceGroup: 'PAYMENT_SERVICE',
        data: {
          userId: '12345',
          userName: 'John Doe',
          email: '<EMAIL>',
          profile: {
            firstName: 'John',
            lastName: 'Doe',
            phoneNumber: '+***********',
            address: {
              street: '123 Main St',
              city: 'Ho Chi Minh City',
              country: 'Vietnam',
            },
          },
          preferences: {
            language: 'vi',
            currency: 'VND',
            notifications: true,
          },
        },
      };

      const result = mapGetProfileResponseToModel(mockResponse);

      expect(result).toBeInstanceOf(GetProfileModel);
      expect(result.serviceGroup).toBe('PAYMENT_SERVICE');
    });

    it('should handle response with null data', () => {
      const mockResponse: BaseResponse<GetProfileResponse> = {
        serviceGroup: 'PAYMENT_SERVICE',
        data: null as any,
      };

      const result = mapGetProfileResponseToModel(mockResponse);

      expect(result).toBeInstanceOf(GetProfileModel);
      expect(result.serviceGroup).toBe('PAYMENT_SERVICE');
    });

    it('should handle response with undefined data', () => {
      const mockResponse: BaseResponse<GetProfileResponse> = {
        serviceGroup: 'PAYMENT_SERVICE',
        data: undefined as any,
      };

      const result = mapGetProfileResponseToModel(mockResponse);

      expect(result).toBeInstanceOf(GetProfileModel);
      expect(result.serviceGroup).toBe('PAYMENT_SERVICE');
    });

    it('should handle response with empty data object', () => {
      const mockResponse: BaseResponse<GetProfileResponse> = {
        serviceGroup: 'PAYMENT_SERVICE',
        data: {},
      };

      const result = mapGetProfileResponseToModel(mockResponse);

      expect(result).toBeInstanceOf(GetProfileModel);
      expect(result.serviceGroup).toBe('PAYMENT_SERVICE');
    });

    it('should handle response with errors array', () => {
      const mockResponse: BaseResponse<GetProfileResponse> = {
        serviceGroup: 'PAYMENT_SERVICE',
        data: {
          userId: '12345',
          userName: 'John Doe',
        },
        errors: [
          {
            key: 'PROFILE_WARNING',
            message: 'Profile incomplete',
            context: [],
          },
        ],
      };

      const result = mapGetProfileResponseToModel(mockResponse);

      expect(result).toBeInstanceOf(GetProfileModel);
      expect(result.serviceGroup).toBe('PAYMENT_SERVICE');
    });

    it('should handle response with additional BaseResponse properties', () => {
      const mockResponse: BaseResponse<GetProfileResponse> = {
        serviceGroup: 'PAYMENT_SERVICE',
        data: {
          userId: '12345',
          userName: 'John Doe',
        },
        timestamp: new Date().toISOString(),
        requestId: 'REQ123456789',
        version: '1.0.0',
      } as any;

      const result = mapGetProfileResponseToModel(mockResponse);

      expect(result).toBeInstanceOf(GetProfileModel);
      expect(result.serviceGroup).toBe('PAYMENT_SERVICE');
    });

    it('should handle response with special characters in serviceGroup', () => {
      const mockResponse: BaseResponse<GetProfileResponse> = {
        serviceGroup: 'PAYMENT_SERVICE_V2.0_BETA',
        data: {
          userId: '12345',
          userName: 'John Doe',
        },
      };

      const result = mapGetProfileResponseToModel(mockResponse);

      expect(result).toBeInstanceOf(GetProfileModel);
      expect(result.serviceGroup).toBe('PAYMENT_SERVICE_V2.0_BETA');
    });

    it('should handle response with unicode characters in serviceGroup', () => {
      const mockResponse: BaseResponse<GetProfileResponse> = {
        serviceGroup: 'DỊCH_VỤ_THANH_TOÁN',
        data: {
          userId: '12345',
          userName: 'Nguyễn Văn A',
        },
      };

      const result = mapGetProfileResponseToModel(mockResponse);

      expect(result).toBeInstanceOf(GetProfileModel);
      expect(result.serviceGroup).toBe('DỊCH_VỤ_THANH_TOÁN');
    });
  });

  describe('Performance considerations', () => {
    it('should handle mapping quickly for normal responses', () => {
      const mockResponse: BaseResponse<GetProfileResponse> = {
        serviceGroup: 'PAYMENT_SERVICE',
        data: {
          userId: '12345',
          userName: 'John Doe',
        },
      };
      
      const startTime = performance.now();
      const result = mapGetProfileResponseToModel(mockResponse);
      const endTime = performance.now();
      
      expect(result).toBeInstanceOf(GetProfileModel);
      expect(endTime - startTime).toBeLessThan(10); // Should complete in less than 10ms
    });

    it('should handle multiple rapid mappings efficiently', () => {
      const mockResponse: BaseResponse<GetProfileResponse> = {
        serviceGroup: 'PAYMENT_SERVICE',
        data: {
          userId: '12345',
          userName: 'John Doe',
        },
      };
      const iterations = 1000;
      
      const startTime = performance.now();
      for (let i = 0; i < iterations; i++) {
        mapGetProfileResponseToModel(mockResponse);
      }
      const endTime = performance.now();
      
      expect(endTime - startTime).toBeLessThan(100); // Should complete 1000 mappings in less than 100ms
    });
  });

  describe('Memory management', () => {
    it('should not cause memory leaks with repeated mappings', () => {
      const mockResponse: BaseResponse<GetProfileResponse> = {
        serviceGroup: 'PAYMENT_SERVICE',
        data: {
          userId: '12345',
          userName: 'John Doe',
        },
      };
      
      // Perform many mappings to test for memory leaks
      for (let i = 0; i < 100; i++) {
        const result = mapGetProfileResponseToModel(mockResponse);
        expect(result).toBeInstanceOf(GetProfileModel);
      }
      
      // If we reach here without running out of memory, the test passes
      expect(true).toBe(true);
    });
  });

  describe('Type safety', () => {
    it('should maintain type safety with proper response structure', () => {
      const mockResponse: BaseResponse<GetProfileResponse> = {
        serviceGroup: 'PAYMENT_SERVICE',
        data: {
          userId: '12345',
          userName: 'John Doe',
        },
      };

      const result = mapGetProfileResponseToModel(mockResponse);

      expect(typeof result).toBe('object');
      expect(result).toBeInstanceOf(GetProfileModel);
      expect(typeof result.serviceGroup).toBe('string');
    });

    it('should handle response with mixed data types', () => {
      const mockResponse: BaseResponse<GetProfileResponse> = {
        serviceGroup: 'PAYMENT_SERVICE',
        data: {
          userId: '12345',
          userName: 'John Doe',
          numericProperty: 123,
          booleanProperty: true,
          arrayProperty: [1, 2, 3],
          objectProperty: {nested: 'value'},
          nullProperty: null,
          undefinedProperty: undefined,
        } as any,
      };

      const result = mapGetProfileResponseToModel(mockResponse);

      expect(result).toBeInstanceOf(GetProfileModel);
      expect(result.serviceGroup).toBe('PAYMENT_SERVICE');
    });
  });

  describe('Edge cases', () => {
    it('should handle response with very long serviceGroup string', () => {
      const longServiceGroup = 'SERVICE_' + 'A'.repeat(1000);
      const mockResponse: BaseResponse<GetProfileResponse> = {
        serviceGroup: longServiceGroup,
        data: {
          userId: '12345',
          userName: 'John Doe',
        },
      };

      const result = mapGetProfileResponseToModel(mockResponse);

      expect(result).toBeInstanceOf(GetProfileModel);
      expect(result.serviceGroup).toBe(longServiceGroup);
    });

    it('should handle response with numeric serviceGroup (edge case)', () => {
      const mockResponse: BaseResponse<GetProfileResponse> = {
        serviceGroup: 12345 as any,
        data: {
          userId: '12345',
          userName: 'John Doe',
        },
      };

      const result = mapGetProfileResponseToModel(mockResponse);

      expect(result).toBeInstanceOf(GetProfileModel);
      expect(result.serviceGroup).toBe(12345);
    });

    it('should handle response with boolean serviceGroup (edge case)', () => {
      const mockResponse: BaseResponse<GetProfileResponse> = {
        serviceGroup: true as any,
        data: {
          userId: '12345',
          userName: 'John Doe',
        },
      };

      const result = mapGetProfileResponseToModel(mockResponse);

      expect(result).toBeInstanceOf(GetProfileModel);
      expect(result.serviceGroup).toBe(true);
    });

    it('should handle response with object serviceGroup (edge case)', () => {
      const mockResponse: BaseResponse<GetProfileResponse> = {
        serviceGroup: {name: 'PAYMENT_SERVICE', version: '1.0'} as any,
        data: {
          userId: '12345',
          userName: 'John Doe',
        },
      };

      const result = mapGetProfileResponseToModel(mockResponse);

      expect(result).toBeInstanceOf(GetProfileModel);
      expect(result.serviceGroup).toEqual({name: 'PAYMENT_SERVICE', version: '1.0'});
    });
  });

  describe('Integration scenarios', () => {
    it('should work correctly in a typical get profile flow', () => {
      const mockResponse: BaseResponse<GetProfileResponse> = {
        serviceGroup: 'USER_PROFILE_SERVICE',
        data: {
          userId: 'USER123456789',
          userName: 'Nguyễn Văn A',
          email: '<EMAIL>',
          phoneNumber: '+***********',
          status: 'ACTIVE',
        },
      };

      const result = mapGetProfileResponseToModel(mockResponse);

      expect(result).toBeInstanceOf(GetProfileModel);
      expect(result.serviceGroup).toBe('USER_PROFILE_SERVICE');
    });

    it('should maintain consistency across multiple mappings', () => {
      const mockResponse: BaseResponse<GetProfileResponse> = {
        serviceGroup: 'PROFILE_SERVICE',
        data: {
          userId: 'USER001',
          userName: 'Test User',
        },
      };

      const result1 = mapGetProfileResponseToModel(mockResponse);
      const result2 = mapGetProfileResponseToModel(mockResponse);

      expect(result1).toBeInstanceOf(GetProfileModel);
      expect(result2).toBeInstanceOf(GetProfileModel);
      expect(result1.serviceGroup).toBe(result2.serviceGroup);
    });
  });

  describe('Real-world scenarios', () => {
    it('should handle successful profile retrieval response', () => {
      const mockResponse: BaseResponse<GetProfileResponse> = {
        serviceGroup: 'MSB_PROFILE_SERVICE',
        data: {
          userId: 'MSB123456789',
          userName: 'Trần Thị B',
          email: '<EMAIL>',
          phoneNumber: '+***********',
          customerType: 'PREMIUM',
          accountStatus: 'ACTIVE',
          lastLoginAt: '2023-12-01T10:30:00Z',
          preferences: {
            language: 'vi',
            currency: 'VND',
            notifications: {
              email: true,
              sms: true,
              push: true,
            },
          },
        },
      };

      const result = mapGetProfileResponseToModel(mockResponse);

      expect(result).toBeInstanceOf(GetProfileModel);
      expect(result.serviceGroup).toBe('MSB_PROFILE_SERVICE');
    });

    it('should handle profile response with errors', () => {
      const mockResponse: BaseResponse<GetProfileResponse> = {
        serviceGroup: 'MSB_PROFILE_SERVICE',
        data: {
          userId: 'MSB123456789',
          userName: 'Lê Văn C',
        },
        errors: [
          {
            key: 'PROFILE_INCOMPLETE',
            message: 'Thông tin hồ sơ chưa đầy đủ',
            context: ['email', 'phoneNumber'],
          },
        ],
      };

      const result = mapGetProfileResponseToModel(mockResponse);

      expect(result).toBeInstanceOf(GetProfileModel);
      expect(result.serviceGroup).toBe('MSB_PROFILE_SERVICE');
    });

    it('should handle profile response for different service groups', () => {
      const serviceGroups = [
        'MSB_PAYMENT_SERVICE',
        'MSB_BILL_SERVICE',
        'MSB_TRANSFER_SERVICE',
        'MSB_ACCOUNT_SERVICE',
      ];

      serviceGroups.forEach(serviceGroup => {
        const mockResponse: BaseResponse<GetProfileResponse> = {
          serviceGroup,
          data: {
            userId: 'MSB123456789',
            userName: 'Test User',
            serviceAccess: serviceGroup,
          },
        };

        const result = mapGetProfileResponseToModel(mockResponse);

        expect(result).toBeInstanceOf(GetProfileModel);
        expect(result.serviceGroup).toBe(serviceGroup);
      });
    });
  });
});
