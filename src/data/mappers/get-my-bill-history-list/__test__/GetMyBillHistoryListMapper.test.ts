import {describe, it, expect} from '@jest/globals';
import {mapGetMyBillHistoryListResponseToModel} from '../GetMyBillHistoryListMapper';
import {GetMyBillHistoryListResponse} from '../../../models/get-my-bill-history-list/GetMyBillHistoryListResponse';
import {BillHistoryModel} from '../../../../domain/entities/get-my-bill-history-list/GetMyBillHistoryListModel';

// Mock moment to ensure consistent test results
jest.mock('moment/moment', () => {
  const actualMoment = jest.requireActual('moment/moment');
  return (date?: any, format?: string) => {
    if (date === '2023-12-01T10:30:00Z') {
      return {
        format: (formatStr: string) => {
          if (formatStr === 'HH:mm') return '10:30';
          if (formatStr === 'DD/MM/YYYY') return '01/12/2023';
          return actualMoment(date, format).format(formatStr);
        },
      };
    }
    if (date === '2023-12-01T14:45:00Z') {
      return {
        format: (formatStr: string) => {
          if (formatStr === 'HH:mm') return '14:45';
          if (formatStr === 'DD/MM/YYYY') return '01/12/2023';
          return actualMoment(date, format).format(formatStr);
        },
      };
    }
    if (date === '2023-12-02T09:15:00Z') {
      return {
        format: (formatStr: string) => {
          if (formatStr === 'HH:mm') return '09:15';
          if (formatStr === 'DD/MM/YYYY') return '02/12/2023';
          return actualMoment(date, format).format(formatStr);
        },
      };
    }
    if (date === '2023-12-01' && format === 'YYYY-MM-DD') {
      return {
        format: (formatStr: string) => {
          if (formatStr === 'DD/MM/YYYY') return '01/12/2023';
          return actualMoment(date, format).format(formatStr);
        },
      };
    }
    if (date === '2023-12-02' && format === 'YYYY-MM-DD') {
      return {
        format: (formatStr: string) => {
          if (formatStr === 'DD/MM/YYYY') return '02/12/2023';
          return actualMoment(date, format).format(formatStr);
        },
      };
    }
    return actualMoment(date, format);
  };
});

describe('GetMyBillHistoryListMapper', () => {
  describe('mapGetMyBillHistoryListResponseToModel', () => {
    it('should create BillHistoryModel with grouped bills by date', () => {
      const mockResponse: GetMyBillHistoryListResponse = [
        {
          id: 'BILL_001',
          counterPartyName: 'EVN Ho Chi Minh City',
          customerName: 'Nguyen Van A',
          billCode: 'EVN001',
          description: 'Electric bill payment',
          content: 'Monthly electric bill',
          serviceCode: 'ELECTRIC',
          creditDebitIndicator: 'CRDT',
          transactionAmountCurrency: {
            amount: '500000',
            currencyCode: 'VND',
          },
          totalAmount: 500000,
          creationTime: '2023-12-01T10:30:00Z',
          paymentDate: '2023-12-01T10:30:00Z',
        },
        {
          id: 'BILL_002',
          counterPartyName: 'SAWACO',
          customerName: 'Nguyen Van A',
          billCode: 'SAWACO001',
          description: 'Water bill payment',
          content: 'Monthly water bill',
          serviceCode: 'WATER',
          creditDebitIndicator: 'DBIT',
          transactionAmountCurrency: {
            amount: '200000',
            currencyCode: 'VND',
          },
          totalAmount: 200000,
          creationTime: '2023-12-01T14:45:00Z',
          paymentDate: '2023-12-01T14:45:00Z',
        },
        {
          id: 'BILL_003',
          counterPartyName: 'Viettel',
          customerName: 'Nguyen Van A',
          billCode: 'VTT001',
          description: 'Internet bill payment',
          content: 'Monthly internet bill',
          serviceCode: 'INTERNET',
          creditDebitIndicator: 'DBIT',
          transactionAmountCurrency: {
            amount: '300000',
            currencyCode: 'VND',
          },
          totalAmount: 300000,
          creationTime: '2023-12-02T09:15:00Z',
          paymentDate: '2023-12-02T09:15:00Z',
        },
      ];

      const result = mapGetMyBillHistoryListResponseToModel(mockResponse);

      expect(result).toEqual({
        billHistoryDTO: [
          {
            title: '01/12/2023',
            id: '2023-12-01',
            data: [
              {
                id: 'BILL_001',
                transName: 'EVN Ho Chi Minh City',
                content: 'Electric bill payment',
                amount: '+500,000',
                transDate: '10:30',
                creationDate: '2023-12-01T10:30:00Z',
              },
              {
                id: 'BILL_002',
                transName: 'SAWACO',
                content: 'Water bill payment',
                amount: '-200,000',
                transDate: '14:45',
                creationDate: '2023-12-01T14:45:00Z',
              },
            ],
          },
          {
            title: '02/12/2023',
            id: '2023-12-02',
            data: [
              {
                id: 'BILL_003',
                transName: 'Viettel',
                content: 'Internet bill payment',
                amount: '-300,000',
                transDate: '09:15',
                creationDate: '2023-12-02T09:15:00Z',
              },
            ],
          },
        ],
        billHistory: mockResponse,
      });
    });

    it('should handle empty response array', () => {
      const mockResponse: GetMyBillHistoryListResponse = [];

      const result = mapGetMyBillHistoryListResponseToModel(mockResponse);

      expect(result).toEqual({
        billHistoryDTO: [],
        billHistory: [],
      });
    });

    it('should handle null response', () => {
      const mockResponse: GetMyBillHistoryListResponse = null as any;

      const result = mapGetMyBillHistoryListResponseToModel(mockResponse);

      expect(result).toEqual({
        billHistoryDTO: [],
        billHistory: null,
      });
    });

    it('should handle undefined response', () => {
      const mockResponse: GetMyBillHistoryListResponse = undefined as any;

      const result = mapGetMyBillHistoryListResponseToModel(mockResponse);

      expect(result).toEqual({
        billHistoryDTO: [],
        billHistory: undefined,
      });
    });

    it('should use fallback values for missing properties', () => {
      const mockResponse: GetMyBillHistoryListResponse = [
        {
          id: 'BILL_001',
          counterPartyName: null as any,
          customerName: null as any,
          billCode: 'FALLBACK001',
          description: null as any,
          content: null as any,
          serviceCode: 'UNKNOWN',
          creditDebitIndicator: 'DBIT',
          transactionAmountCurrency: null as any,
          totalAmount: null as any,
          creationTime: '2023-12-01T10:30:00Z',
          paymentDate: null as any,
        },
      ];

      const result = mapGetMyBillHistoryListResponseToModel(mockResponse);

      expect(result.billHistoryDTO).toHaveLength(1);
      expect(result.billHistoryDTO[0].data[0]).toEqual({
        id: 'BILL_001',
        transName: 'FALLBACK001', // Falls back to billCode
        content: 'Bill Payment - UNKNOWN', // Falls back to serviceCode
        amount: '-0', // Falls back to 0
        transDate: '10:30',
        creationDate: '2023-12-01T10:30:00Z',
      });
    });

    it('should use creationTime for date grouping when paymentDate is missing', () => {
      const mockResponse: GetMyBillHistoryListResponse = [
        {
          id: 'BILL_001',
          counterPartyName: 'Test Company',
          customerName: 'Test Customer',
          billCode: 'TEST001',
          description: 'Test bill',
          content: 'Test content',
          serviceCode: 'TEST',
          creditDebitIndicator: 'CRDT',
          transactionAmountCurrency: {
            amount: '100000',
            currencyCode: 'VND',
          },
          totalAmount: 100000,
          creationTime: '2023-12-01T10:30:00Z',
          paymentDate: null as any,
        },
      ];

      const result = mapGetMyBillHistoryListResponseToModel(mockResponse);

      expect(result.billHistoryDTO).toHaveLength(1);
      expect(result.billHistoryDTO[0].id).toBe('2023-12-01');
      expect(result.billHistoryDTO[0].title).toBe('01/12/2023');
    });

    it('should handle bills with different amount formats', () => {
      const mockResponse: GetMyBillHistoryListResponse = [
        {
          id: 'BILL_001',
          counterPartyName: 'Company A',
          customerName: 'Customer A',
          billCode: 'A001',
          description: 'Bill A',
          content: 'Content A',
          serviceCode: 'SERVICE_A',
          creditDebitIndicator: 'CRDT',
          transactionAmountCurrency: {
            amount: '1500000', // String amount
            currencyCode: 'VND',
          },
          totalAmount: null as any,
          creationTime: '2023-12-01T10:30:00Z',
          paymentDate: '2023-12-01T10:30:00Z',
        },
        {
          id: 'BILL_002',
          counterPartyName: 'Company B',
          customerName: 'Customer B',
          billCode: 'B001',
          description: 'Bill B',
          content: 'Content B',
          serviceCode: 'SERVICE_B',
          creditDebitIndicator: 'DBIT',
          transactionAmountCurrency: null as any,
          totalAmount: 2500000, // Number amount
          creationTime: '2023-12-01T14:45:00Z',
          paymentDate: '2023-12-01T14:45:00Z',
        },
      ];

      const result = mapGetMyBillHistoryListResponseToModel(mockResponse);

      expect(result.billHistoryDTO[0].data[0].amount).toBe('+1,500,000');
      expect(result.billHistoryDTO[0].data[1].amount).toBe('-2,500,000');
    });

    it('should handle bills with missing time information', () => {
      const mockResponse: GetMyBillHistoryListResponse = [
        {
          id: 'BILL_001',
          counterPartyName: 'Company A',
          customerName: 'Customer A',
          billCode: 'A001',
          description: 'Bill A',
          content: 'Content A',
          serviceCode: 'SERVICE_A',
          creditDebitIndicator: 'CRDT',
          transactionAmountCurrency: {
            amount: '100000',
            currencyCode: 'VND',
          },
          totalAmount: 100000,
          creationTime: null as any,
          paymentDate: null as any,
        },
      ];

      const result = mapGetMyBillHistoryListResponseToModel(mockResponse);

      expect(result.billHistoryDTO[0].data[0].transDate).toBe('');
      expect(result.billHistoryDTO[0].data[0].creationDate).toBeNull();
    });

    it('should format Vietnamese currency correctly', () => {
      const mockResponse: GetMyBillHistoryListResponse = [
        {
          id: 'BILL_001',
          counterPartyName: 'Test Company',
          customerName: 'Test Customer',
          billCode: 'TEST001',
          description: 'Test bill',
          content: 'Test content',
          serviceCode: 'TEST',
          creditDebitIndicator: 'CRDT',
          transactionAmountCurrency: {
            amount: '1234567',
            currencyCode: 'VND',
          },
          totalAmount: 1234567,
          creationTime: '2023-12-01T10:30:00Z',
          paymentDate: '2023-12-01T10:30:00Z',
        },
        {
          id: 'BILL_002',
          counterPartyName: 'Test Company 2',
          customerName: 'Test Customer 2',
          billCode: 'TEST002',
          description: 'Test bill 2',
          content: 'Test content 2',
          serviceCode: 'TEST',
          creditDebitIndicator: 'DBIT',
          transactionAmountCurrency: {
            amount: '*********',
            currencyCode: 'VND',
          },
          totalAmount: *********,
          creationTime: '2023-12-01T14:45:00Z',
          paymentDate: '2023-12-01T14:45:00Z',
        },
      ];

      const result = mapGetMyBillHistoryListResponseToModel(mockResponse);

      expect(result.billHistoryDTO[0].data[0].amount).toBe('+1,234,567');
      expect(result.billHistoryDTO[0].data[1].amount).toBe('-987,654,321');
    });

    it('should handle bills with Vietnamese characters', () => {
      const mockResponse: GetMyBillHistoryListResponse = [
        {
          id: 'BILL_001',
          counterPartyName: 'Công ty Điện lực TP.HCM',
          customerName: 'Nguyễn Văn A',
          billCode: 'EVN_HCM_001',
          description: 'Thanh toán tiền điện tháng 12/2023',
          content: 'Hóa đơn tiền điện hàng tháng',
          serviceCode: 'DIEN_LUC',
          creditDebitIndicator: 'DBIT',
          transactionAmountCurrency: {
            amount: '850000',
            currencyCode: 'VND',
          },
          totalAmount: 850000,
          creationTime: '2023-12-01T10:30:00Z',
          paymentDate: '2023-12-01T10:30:00Z',
        },
      ];

      const result = mapGetMyBillHistoryListResponseToModel(mockResponse);

      expect(result.billHistoryDTO[0].data[0]).toEqual({
        id: 'BILL_001',
        transName: 'Công ty Điện lực TP.HCM',
        content: 'Thanh toán tiền điện tháng 12/2023',
        amount: '-850,000',
        transDate: '10:30',
        creationDate: '2023-12-01T10:30:00Z',
      });
    });

    it('should handle invalid amount values gracefully', () => {
      const mockResponse: GetMyBillHistoryListResponse = [
        {
          id: 'BILL_001',
          counterPartyName: 'Test Company',
          customerName: 'Test Customer',
          billCode: 'TEST001',
          description: 'Test bill',
          content: 'Test content',
          serviceCode: 'TEST',
          creditDebitIndicator: 'CRDT',
          transactionAmountCurrency: {
            amount: 'invalid_amount',
            currencyCode: 'VND',
          },
          totalAmount: 'also_invalid' as any,
          creationTime: '2023-12-01T10:30:00Z',
          paymentDate: '2023-12-01T10:30:00Z',
        },
      ];

      const result = mapGetMyBillHistoryListResponseToModel(mockResponse);

      expect(result.billHistoryDTO[0].data[0].amount).toBe('+');
    });

    it('should preserve original response in billHistory property', () => {
      const mockResponse: GetMyBillHistoryListResponse = [
        {
          id: 'BILL_001',
          counterPartyName: 'Test Company',
          customerName: 'Test Customer',
          billCode: 'TEST001',
          description: 'Test bill',
          content: 'Test content',
          serviceCode: 'TEST',
          creditDebitIndicator: 'CRDT',
          transactionAmountCurrency: {
            amount: '100000',
            currencyCode: 'VND',
          },
          totalAmount: 100000,
          creationTime: '2023-12-01T10:30:00Z',
          paymentDate: '2023-12-01T10:30:00Z',
        },
      ];

      const result = mapGetMyBillHistoryListResponseToModel(mockResponse);

      expect(result.billHistory).toBe(mockResponse);
      expect(result.billHistory).toEqual(mockResponse);
    });
  });

  describe('Performance considerations', () => {
    it('should handle large arrays efficiently', () => {
      const largeBillList = Array.from({length: 1000}, (_, index) => ({
        id: `BILL_${index}`,
        counterPartyName: `Company ${index}`,
        customerName: `Customer ${index}`,
        billCode: `CODE_${index}`,
        description: `Bill ${index}`,
        content: `Content ${index}`,
        serviceCode: `SERVICE_${index}`,
        creditDebitIndicator: index % 2 === 0 ? 'CRDT' : 'DBIT',
        transactionAmountCurrency: {
          amount: `${(index + 1) * 1000}`,
          currencyCode: 'VND',
        },
        totalAmount: (index + 1) * 1000,
        creationTime: `2023-12-01T${String(index % 24).padStart(2, '0')}:00:00Z`,
        paymentDate: `2023-12-01T${String(index % 24).padStart(2, '0')}:00:00Z`,
      })) as GetMyBillHistoryListResponse;

      const startTime = performance.now();
      const result = mapGetMyBillHistoryListResponseToModel(largeBillList);
      const endTime = performance.now();

      expect(result.billHistoryDTO).toHaveLength(1); // All bills on same date
      expect(result.billHistoryDTO[0].data).toHaveLength(1000);
      expect(endTime - startTime).toBeLessThan(100); // Should complete in less than 100ms
    });

    it('should handle multiple rapid mappings efficiently', () => {
      const mockResponse: GetMyBillHistoryListResponse = [
        {
          id: 'BILL_001',
          counterPartyName: 'Test Company',
          customerName: 'Test Customer',
          billCode: 'TEST001',
          description: 'Test bill',
          content: 'Test content',
          serviceCode: 'TEST',
          creditDebitIndicator: 'CRDT',
          transactionAmountCurrency: {
            amount: '100000',
            currencyCode: 'VND',
          },
          totalAmount: 100000,
          creationTime: '2023-12-01T10:30:00Z',
          paymentDate: '2023-12-01T10:30:00Z',
        },
      ];
      const iterations = 1000;

      const startTime = performance.now();
      for (let i = 0; i < iterations; i++) {
        mapGetMyBillHistoryListResponseToModel(mockResponse);
      }
      const endTime = performance.now();

      expect(endTime - startTime).toBeLessThan(100); // Should complete 1000 mappings in less than 100ms
    });
  });

  describe('Memory management', () => {
    it('should not cause memory leaks with repeated mappings', () => {
      const mockResponse: GetMyBillHistoryListResponse = [
        {
          id: 'BILL_001',
          counterPartyName: 'Test Company',
          customerName: 'Test Customer',
          billCode: 'TEST001',
          description: 'Test bill',
          content: 'Test content',
          serviceCode: 'TEST',
          creditDebitIndicator: 'CRDT',
          transactionAmountCurrency: {
            amount: '100000',
            currencyCode: 'VND',
          },
          totalAmount: 100000,
          creationTime: '2023-12-01T10:30:00Z',
          paymentDate: '2023-12-01T10:30:00Z',
        },
      ];

      // Perform many mappings to test for memory leaks
      for (let i = 0; i < 100; i++) {
        const result = mapGetMyBillHistoryListResponseToModel(mockResponse);
        expect(result).toBeDefined();
      }

      // If we reach here without running out of memory, the test passes
      expect(true).toBe(true);
    });
  });
});
