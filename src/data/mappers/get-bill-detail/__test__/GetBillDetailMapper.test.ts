import {describe, it, expect} from '@jest/globals';
import {mapGetBillDetailResponseToModel} from '../GetBillDetailMapper';
import {GetBillDetailResponse} from '../../../models/get-bill-detail/GetBillDetailResponse';
import {GetBillDetailModel} from '../../../../domain/entities/get-bill-detail/GetBillDetailModel';

describe('GetBillDetailMapper', () => {
  describe('mapGetBillDetailResponseToModel', () => {
    it('should create GetBillDetailModel with all properties from response', () => {
      const mockResponse: GetBillDetailResponse = {
        billCode: 'BILL001',
        service: {
          code: 'ELECTRIC',
        },
        queryRef: 'REF123456',
        customerInfo: {
          cif: 'CIF001',
          phone: '+84123456789',
          acct: 'ACCT001',
          name: '<PERSON><PERSON><PERSON><PERSON>',
          address: '123 Main St, Ho Chi Minh City',
        },
        billList: [
          {
            id: 'BILL_ID_001',
            no: 'BILL_NO_001',
            amount: 500000,
            code: 'ELECTRIC_001',
            custCode: 'CUST001',
            custName: 'Nguyễn Văn A',
            period: '2023-12',
            fee: 5000,
            custAddress: '123 Main St',
          },
        ],
        partnerRespCode: '00',
        tranSeqCount: 1,
        partnerRespDesc: 'Success',
        partnerTraceSeq: 'TRACE001',
        result: 'SUCCESS',
        extendData: {additionalInfo: 'test'},
        paymentRule: 'IMMEDIATE',
      };

      const result = mapGetBillDetailResponseToModel(mockResponse);

      expect(result).toBeInstanceOf(GetBillDetailModel);
      expect(result.billCode).toBe('BILL001');
      expect(result.service?.code).toBe('ELECTRIC');
      expect(result.queryRef).toBe('REF123456');
      expect(result.customerInfo?.cif).toBe('CIF001');
      expect(result.customerInfo?.name).toBe('Nguyễn Văn A');
      expect(result.billList).toHaveLength(1);
      expect(result.billList?.[0].id).toBe('BILL_ID_001');
      expect(result.partnerRespCode).toBe('00');
      expect(result.tranSeqCount).toBe(1);
      expect(result.partnerRespDesc).toBe('Success');
      expect(result.partnerTraceSeq).toBe('TRACE001');
      expect(result.result).toBe('SUCCESS');
      expect(result.extendData).toEqual({additionalInfo: 'test'});
      expect(result.paymentRule).toBe('IMMEDIATE');
    });

    it('should handle response with null service', () => {
      const mockResponse: GetBillDetailResponse = {
        billCode: 'BILL001',
        service: null,
        queryRef: 'REF123456',
        customerInfo: null,
        billList: null,
        partnerRespCode: '00',
        tranSeqCount: 1,
        partnerRespDesc: 'Success',
        partnerTraceSeq: 'TRACE001',
        result: 'SUCCESS',
        extendData: null,
        paymentRule: 'IMMEDIATE',
      };

      const result = mapGetBillDetailResponseToModel(mockResponse);

      expect(result).toBeInstanceOf(GetBillDetailModel);
      expect(result.service).toBeUndefined();
      expect(result.customerInfo).toBeUndefined();
      expect(result.billList).toBeUndefined();
    });

    it('should handle response with undefined service', () => {
      const mockResponse: GetBillDetailResponse = {
        billCode: 'BILL001',
        service: undefined,
        queryRef: 'REF123456',
        customerInfo: undefined,
        billList: undefined,
        partnerRespCode: '00',
        tranSeqCount: 1,
        partnerRespDesc: 'Success',
        partnerTraceSeq: 'TRACE001',
        result: 'SUCCESS',
        extendData: undefined,
        paymentRule: 'IMMEDIATE',
      };

      const result = mapGetBillDetailResponseToModel(mockResponse);

      expect(result).toBeInstanceOf(GetBillDetailModel);
      expect(result.service).toBeUndefined();
      expect(result.customerInfo).toBeUndefined();
      expect(result.billList).toBeUndefined();
    });

    it('should handle response with empty billList array', () => {
      const mockResponse: GetBillDetailResponse = {
        billCode: 'BILL001',
        service: {code: 'WATER'},
        queryRef: 'REF123456',
        customerInfo: {
          cif: 'CIF001',
          phone: '+84123456789',
          acct: 'ACCT001',
          name: 'Test User',
          address: 'Test Address',
        },
        billList: [],
        partnerRespCode: '00',
        tranSeqCount: 0,
        partnerRespDesc: 'No bills found',
        partnerTraceSeq: 'TRACE001',
        result: 'SUCCESS',
        extendData: null,
        paymentRule: 'IMMEDIATE',
      };

      const result = mapGetBillDetailResponseToModel(mockResponse);

      expect(result).toBeInstanceOf(GetBillDetailModel);
      expect(result.billList).toEqual([]);
      expect(result.billList).toHaveLength(0);
    });

    it('should handle response with multiple bills in billList', () => {
      const mockResponse: GetBillDetailResponse = {
        billCode: 'BILL001',
        service: {code: 'ELECTRIC'},
        queryRef: 'REF123456',
        customerInfo: {
          cif: 'CIF001',
          phone: '+84123456789',
          acct: 'ACCT001',
          name: 'Test User',
          address: 'Test Address',
        },
        billList: [
          {
            id: 'BILL_ID_001',
            no: 'BILL_NO_001',
            amount: 500000,
            code: 'ELECTRIC_001',
            custCode: 'CUST001',
            custName: 'Customer 1',
            period: '2023-11',
            fee: 5000,
            custAddress: 'Address 1',
          },
          {
            id: 'BILL_ID_002',
            no: 'BILL_NO_002',
            amount: 600000,
            code: 'ELECTRIC_002',
            custCode: 'CUST002',
            custName: 'Customer 2',
            period: '2023-12',
            fee: 6000,
            custAddress: 'Address 2',
          },
        ],
        partnerRespCode: '00',
        tranSeqCount: 2,
        partnerRespDesc: 'Success',
        partnerTraceSeq: 'TRACE001',
        result: 'SUCCESS',
        extendData: null,
        paymentRule: 'IMMEDIATE',
      };

      const result = mapGetBillDetailResponseToModel(mockResponse);

      expect(result).toBeInstanceOf(GetBillDetailModel);
      expect(result.billList).toHaveLength(2);
      expect(result.billList?.[0].id).toBe('BILL_ID_001');
      expect(result.billList?.[0].amount).toBe(500000);
      expect(result.billList?.[1].id).toBe('BILL_ID_002');
      expect(result.billList?.[1].amount).toBe(600000);
    });

    it('should handle response with null values in required fields', () => {
      const mockResponse: GetBillDetailResponse = {
        billCode: null as any,
        service: null,
        queryRef: null as any,
        customerInfo: null,
        billList: null,
        partnerRespCode: null as any,
        tranSeqCount: null as any,
        partnerRespDesc: null as any,
        partnerTraceSeq: null as any,
        result: null as any,
        extendData: null,
        paymentRule: null as any,
      };

      const result = mapGetBillDetailResponseToModel(mockResponse);

      expect(result).toBeInstanceOf(GetBillDetailModel);
      expect(result.billCode).toBeNull();
      expect(result.service).toBeUndefined();
      expect(result.queryRef).toBeNull();
    });

    it('should handle response with undefined values in required fields', () => {
      const mockResponse: GetBillDetailResponse = {
        billCode: undefined as any,
        service: undefined,
        queryRef: undefined as any,
        customerInfo: undefined,
        billList: undefined,
        partnerRespCode: undefined as any,
        tranSeqCount: undefined as any,
        partnerRespDesc: undefined as any,
        partnerTraceSeq: undefined as any,
        result: undefined as any,
        extendData: undefined,
        paymentRule: undefined as any,
      };

      const result = mapGetBillDetailResponseToModel(mockResponse);

      expect(result).toBeInstanceOf(GetBillDetailModel);
      expect(result.billCode).toBeUndefined();
      expect(result.service).toBeUndefined();
      expect(result.queryRef).toBeUndefined();
    });

    it('should handle response with various service codes', () => {
      const serviceCodes = ['ELECTRIC', 'WATER', 'GAS', 'INTERNET', 'PHONE'];

      serviceCodes.forEach(serviceCode => {
        const mockResponse: GetBillDetailResponse = {
          billCode: 'BILL001',
          service: {code: serviceCode},
          queryRef: 'REF123456',
          customerInfo: null,
          billList: null,
          partnerRespCode: '00',
          tranSeqCount: 1,
          partnerRespDesc: 'Success',
          partnerTraceSeq: 'TRACE001',
          result: 'SUCCESS',
          extendData: null,
          paymentRule: 'IMMEDIATE',
        };

        const result = mapGetBillDetailResponseToModel(mockResponse);

        expect(result).toBeInstanceOf(GetBillDetailModel);
        expect(result.service?.code).toBe(serviceCode);
      });
    });

    it('should handle response with complex extendData', () => {
      const mockResponse: GetBillDetailResponse = {
        billCode: 'BILL001',
        service: {code: 'ELECTRIC'},
        queryRef: 'REF123456',
        customerInfo: null,
        billList: null,
        partnerRespCode: '00',
        tranSeqCount: 1,
        partnerRespDesc: 'Success',
        partnerTraceSeq: 'TRACE001',
        result: 'SUCCESS',
        extendData: {
          metadata: {
            version: '1.0',
            timestamp: '2023-12-01T10:00:00Z',
          },
          additionalFields: ['field1', 'field2'],
          nestedObject: {
            level1: {
              level2: 'deep value',
            },
          },
        },
        paymentRule: 'IMMEDIATE',
      };

      const result = mapGetBillDetailResponseToModel(mockResponse);

      expect(result).toBeInstanceOf(GetBillDetailModel);
      expect(result.extendData).toEqual(mockResponse.extendData);
    });

    it('should handle response with special characters in text fields', () => {
      const mockResponse: GetBillDetailResponse = {
        billCode: 'BILL_001@#$',
        service: {code: 'ELECTRIC_VN'},
        queryRef: 'REF_123-456_789',
        customerInfo: {
          cif: 'CIF_001@MSB',
          phone: '+84-123-456-789',
          acct: 'ACCT_001#VND',
          name: 'Nguyễn Văn A - Khách hàng VIP',
          address: '123 Đường ABC, Phường XYZ, Quận 1, TP.HCM',
        },
        billList: [
          {
            id: 'BILL_ID_001@#',
            no: 'BILL_NO_001-VN',
            amount: 1500000,
            code: 'ELECTRIC_001_VN',
            custCode: 'CUST_001@MSB',
            custName: 'Nguyễn Thị B - Công ty TNHH ABC',
            period: '2023-12-VN',
            fee: 15000,
            custAddress: '456 Đường DEF, Phường UVW',
          },
        ],
        partnerRespCode: '00_SUCCESS',
        tranSeqCount: 1,
        partnerRespDesc: 'Thành công - Success',
        partnerTraceSeq: 'TRACE_001_VN',
        result: 'SUCCESS_VN',
        extendData: {message: 'Thông tin bổ sung'},
        paymentRule: 'IMMEDIATE_VN',
      };

      const result = mapGetBillDetailResponseToModel(mockResponse);

      expect(result).toBeInstanceOf(GetBillDetailModel);
      expect(result.billCode).toBe('BILL_001@#$');
      expect(result.customerInfo?.name).toBe('Nguyễn Văn A - Khách hàng VIP');
      expect(result.billList?.[0].custName).toBe('Nguyễn Thị B - Công ty TNHH ABC');
      expect(result.partnerRespDesc).toBe('Thành công - Success');
    });
  });

  describe('Performance considerations', () => {
    it('should handle mapping quickly for normal responses', () => {
      const mockResponse: GetBillDetailResponse = {
        billCode: 'BILL001',
        service: {code: 'ELECTRIC'},
        queryRef: 'REF123456',
        customerInfo: {
          cif: 'CIF001',
          phone: '+84123456789',
          acct: 'ACCT001',
          name: 'Test User',
          address: 'Test Address',
        },
        billList: [
          {
            id: 'BILL_ID_001',
            no: 'BILL_NO_001',
            amount: 500000,
            code: 'ELECTRIC_001',
            custCode: 'CUST001',
            custName: 'Customer 1',
            period: '2023-12',
            fee: 5000,
            custAddress: 'Address 1',
          },
        ],
        partnerRespCode: '00',
        tranSeqCount: 1,
        partnerRespDesc: 'Success',
        partnerTraceSeq: 'TRACE001',
        result: 'SUCCESS',
        extendData: null,
        paymentRule: 'IMMEDIATE',
      };

      const startTime = performance.now();
      const result = mapGetBillDetailResponseToModel(mockResponse);
      const endTime = performance.now();

      expect(result).toBeInstanceOf(GetBillDetailModel);
      expect(endTime - startTime).toBeLessThan(10); // Should complete in less than 10ms
    });

    it('should handle multiple rapid mappings efficiently', () => {
      const mockResponse: GetBillDetailResponse = {
        billCode: 'BILL001',
        service: {code: 'ELECTRIC'},
        queryRef: 'REF123456',
        customerInfo: null,
        billList: null,
        partnerRespCode: '00',
        tranSeqCount: 1,
        partnerRespDesc: 'Success',
        partnerTraceSeq: 'TRACE001',
        result: 'SUCCESS',
        extendData: null,
        paymentRule: 'IMMEDIATE',
      };
      const iterations = 1000;

      const startTime = performance.now();
      for (let i = 0; i < iterations; i++) {
        mapGetBillDetailResponseToModel(mockResponse);
      }
      const endTime = performance.now();

      expect(endTime - startTime).toBeLessThan(100); // Should complete 1000 mappings in less than 100ms
    });

    it('should handle large billList arrays efficiently', () => {
      const largeBillList = Array.from({length: 100}, (_, index) => ({
        id: `BILL_ID_${index}`,
        no: `BILL_NO_${index}`,
        amount: 500000 + index * 1000,
        code: `ELECTRIC_${index}`,
        custCode: `CUST${index}`,
        custName: `Customer ${index}`,
        period: `2023-${(index % 12) + 1}`,
        fee: 5000 + index * 100,
        custAddress: `Address ${index}`,
      }));

      const mockResponse: GetBillDetailResponse = {
        billCode: 'BILL001',
        service: {code: 'ELECTRIC'},
        queryRef: 'REF123456',
        customerInfo: null,
        billList: largeBillList,
        partnerRespCode: '00',
        tranSeqCount: 100,
        partnerRespDesc: 'Success',
        partnerTraceSeq: 'TRACE001',
        result: 'SUCCESS',
        extendData: null,
        paymentRule: 'IMMEDIATE',
      };

      const startTime = performance.now();
      const result = mapGetBillDetailResponseToModel(mockResponse);
      const endTime = performance.now();

      expect(result).toBeInstanceOf(GetBillDetailModel);
      expect(result.billList).toHaveLength(100);
      expect(endTime - startTime).toBeLessThan(50); // Should complete in less than 50ms even with 100 bills
    });
  });

  describe('Memory management', () => {
    it('should not cause memory leaks with repeated mappings', () => {
      const mockResponse: GetBillDetailResponse = {
        billCode: 'BILL001',
        service: {code: 'ELECTRIC'},
        queryRef: 'REF123456',
        customerInfo: {
          cif: 'CIF001',
          phone: '+84123456789',
          acct: 'ACCT001',
          name: 'Test User',
          address: 'Test Address',
        },
        billList: [
          {
            id: 'BILL_ID_001',
            no: 'BILL_NO_001',
            amount: 500000,
            code: 'ELECTRIC_001',
            custCode: 'CUST001',
            custName: 'Customer 1',
            period: '2023-12',
            fee: 5000,
            custAddress: 'Address 1',
          },
        ],
        partnerRespCode: '00',
        tranSeqCount: 1,
        partnerRespDesc: 'Success',
        partnerTraceSeq: 'TRACE001',
        result: 'SUCCESS',
        extendData: null,
        paymentRule: 'IMMEDIATE',
      };

      // Perform many mappings to test for memory leaks
      for (let i = 0; i < 100; i++) {
        const result = mapGetBillDetailResponseToModel(mockResponse);
        expect(result).toBeInstanceOf(GetBillDetailModel);
      }

      // If we reach here without running out of memory, the test passes
      expect(true).toBe(true);
    });
  });

  describe('Type safety', () => {
    it('should maintain type safety with proper response structure', () => {
      const mockResponse: GetBillDetailResponse = {
        billCode: 'BILL001',
        service: {code: 'ELECTRIC'},
        queryRef: 'REF123456',
        customerInfo: {
          cif: 'CIF001',
          phone: '+84123456789',
          acct: 'ACCT001',
          name: 'Test User',
          address: 'Test Address',
        },
        billList: [
          {
            id: 'BILL_ID_001',
            no: 'BILL_NO_001',
            amount: 500000,
            code: 'ELECTRIC_001',
            custCode: 'CUST001',
            custName: 'Customer 1',
            period: '2023-12',
            fee: 5000,
            custAddress: 'Address 1',
          },
        ],
        partnerRespCode: '00',
        tranSeqCount: 1,
        partnerRespDesc: 'Success',
        partnerTraceSeq: 'TRACE001',
        result: 'SUCCESS',
        extendData: {test: 'data'},
        paymentRule: 'IMMEDIATE',
      };

      const result = mapGetBillDetailResponseToModel(mockResponse);

      expect(typeof result).toBe('object');
      expect(result).toBeInstanceOf(GetBillDetailModel);
      expect(typeof result.billCode).toBe('string');
      expect(typeof result.queryRef).toBe('string');
      expect(typeof result.tranSeqCount).toBe('number');
      expect(Array.isArray(result.billList)).toBe(true);
    });

    it('should handle response with mixed data types', () => {
      const mockResponse: GetBillDetailResponse = {
        billCode: 'BILL001',
        service: {code: 'ELECTRIC'},
        queryRef: 'REF123456',
        customerInfo: {
          cif: 'CIF001',
          phone: '+84123456789',
          acct: 'ACCT001',
          name: 'Test User',
          address: 'Test Address',
        },
        billList: [
          {
            id: 'BILL_ID_001',
            no: 'BILL_NO_001',
            amount: 500000,
            code: 'ELECTRIC_001',
            custCode: 'CUST001',
            custName: 'Customer 1',
            period: '2023-12',
            fee: 5000,
            custAddress: 'Address 1',
          },
        ],
        partnerRespCode: '00',
        tranSeqCount: 1,
        partnerRespDesc: 'Success',
        partnerTraceSeq: 'TRACE001',
        result: 'SUCCESS',
        extendData: {
          stringField: 'test',
          numberField: 123,
          booleanField: true,
          arrayField: [1, 2, 3],
          objectField: {nested: 'value'},
          nullField: null,
          undefinedField: undefined,
        },
        paymentRule: 'IMMEDIATE',
      };

      const result = mapGetBillDetailResponseToModel(mockResponse);

      expect(result).toBeInstanceOf(GetBillDetailModel);
      expect(result.extendData).toEqual(mockResponse.extendData);
    });
  });

  describe('Edge cases', () => {
    it('should handle response with very long string values', () => {
      const longString = 'A'.repeat(1000);
      const mockResponse: GetBillDetailResponse = {
        billCode: longString,
        service: {code: longString},
        queryRef: longString,
        customerInfo: {
          cif: longString,
          phone: longString,
          acct: longString,
          name: longString,
          address: longString,
        },
        billList: [
          {
            id: longString,
            no: longString,
            amount: 500000,
            code: longString,
            custCode: longString,
            custName: longString,
            period: longString,
            fee: 5000,
            custAddress: longString,
          },
        ],
        partnerRespCode: longString,
        tranSeqCount: 1,
        partnerRespDesc: longString,
        partnerTraceSeq: longString,
        result: longString,
        extendData: {longField: longString},
        paymentRule: longString,
      };

      const result = mapGetBillDetailResponseToModel(mockResponse);

      expect(result).toBeInstanceOf(GetBillDetailModel);
      expect(result.billCode).toBe(longString);
      expect(result.service?.code).toBe(longString);
    });

    it('should handle response with numeric values as strings (edge case)', () => {
      const mockResponse: GetBillDetailResponse = {
        billCode: 123 as any,
        service: {code: 456 as any},
        queryRef: 789 as any,
        customerInfo: null,
        billList: null,
        partnerRespCode: 0 as any,
        tranSeqCount: '1' as any,
        partnerRespDesc: 'Success',
        partnerTraceSeq: 'TRACE001',
        result: 'SUCCESS',
        extendData: null,
        paymentRule: 'IMMEDIATE',
      };

      const result = mapGetBillDetailResponseToModel(mockResponse);

      expect(result).toBeInstanceOf(GetBillDetailModel);
      expect(result.billCode).toBe(123);
      expect(result.service?.code).toBe(456);
      expect(result.queryRef).toBe(789);
    });

    it('should handle response with boolean values (edge case)', () => {
      const mockResponse: GetBillDetailResponse = {
        billCode: true as any,
        service: {code: false as any},
        queryRef: true as any,
        customerInfo: null,
        billList: null,
        partnerRespCode: false as any,
        tranSeqCount: 1,
        partnerRespDesc: 'Success',
        partnerTraceSeq: 'TRACE001',
        result: 'SUCCESS',
        extendData: null,
        paymentRule: 'IMMEDIATE',
      };

      const result = mapGetBillDetailResponseToModel(mockResponse);

      expect(result).toBeInstanceOf(GetBillDetailModel);
      expect(result.billCode).toBe(true);
      expect(result.service?.code).toBe(false);
      expect(result.queryRef).toBe(true);
    });

    it('should handle response with object values (edge case)', () => {
      const mockResponse: GetBillDetailResponse = {
        billCode: {id: 'BILL001'} as any,
        service: {code: {type: 'ELECTRIC'} as any},
        queryRef: {ref: 'REF123456'} as any,
        customerInfo: null,
        billList: null,
        partnerRespCode: {code: '00'} as any,
        tranSeqCount: 1,
        partnerRespDesc: 'Success',
        partnerTraceSeq: 'TRACE001',
        result: 'SUCCESS',
        extendData: null,
        paymentRule: 'IMMEDIATE',
      };

      const result = mapGetBillDetailResponseToModel(mockResponse);

      expect(result).toBeInstanceOf(GetBillDetailModel);
      expect(result.billCode).toEqual({id: 'BILL001'});
      expect(result.service?.code).toEqual({type: 'ELECTRIC'});
      expect(result.queryRef).toEqual({ref: 'REF123456'});
    });
  });

  describe('Integration scenarios', () => {
    it('should work correctly in a typical get bill detail flow', () => {
      const mockResponse: GetBillDetailResponse = {
        billCode: 'EVN_HCM_001',
        service: {code: 'ELECTRIC_EVN'},
        queryRef: 'REF_EVN_20231201_001',
        customerInfo: {
          cif: 'CIF_MSB_001',
          phone: '+84901234567',
          acct: 'MSB_ACCT_001',
          name: 'Công ty TNHH ABC',
          address: '123 Nguyễn Huệ, Quận 1, TP.HCM',
        },
        billList: [
          {
            id: 'EVN_BILL_001',
            no: 'PD01234567890',
            amount: 1250000,
            code: 'EVN_ELECTRIC_001',
            custCode: 'EVN_CUST_001',
            custName: 'Công ty TNHH ABC',
            period: '2023-11',
            fee: 12500,
            custAddress: '123 Nguyễn Huệ, Quận 1',
          },
        ],
        partnerRespCode: '00',
        tranSeqCount: 1,
        partnerRespDesc: 'Thành công',
        partnerTraceSeq: 'EVN_TRACE_20231201_001',
        result: 'SUCCESS',
        extendData: {
          meterNumber: 'MTR001234567',
          readingDate: '2023-11-30',
          previousReading: 1000,
          currentReading: 1250,
        },
        paymentRule: 'IMMEDIATE',
      };

      const result = mapGetBillDetailResponseToModel(mockResponse);

      expect(result).toBeInstanceOf(GetBillDetailModel);
      expect(result.billCode).toBe('EVN_HCM_001');
      expect(result.service?.code).toBe('ELECTRIC_EVN');
      expect(result.customerInfo?.name).toBe('Công ty TNHH ABC');
      expect(result.billList?.[0].amount).toBe(1250000);
      expect(result.extendData).toEqual(mockResponse.extendData);
    });

    it('should maintain consistency across multiple mappings', () => {
      const mockResponse: GetBillDetailResponse = {
        billCode: 'BILL001',
        service: {code: 'ELECTRIC'},
        queryRef: 'REF123456',
        customerInfo: {
          cif: 'CIF001',
          phone: '+84123456789',
          acct: 'ACCT001',
          name: 'Test User',
          address: 'Test Address',
        },
        billList: [
          {
            id: 'BILL_ID_001',
            no: 'BILL_NO_001',
            amount: 500000,
            code: 'ELECTRIC_001',
            custCode: 'CUST001',
            custName: 'Customer 1',
            period: '2023-12',
            fee: 5000,
            custAddress: 'Address 1',
          },
        ],
        partnerRespCode: '00',
        tranSeqCount: 1,
        partnerRespDesc: 'Success',
        partnerTraceSeq: 'TRACE001',
        result: 'SUCCESS',
        extendData: null,
        paymentRule: 'IMMEDIATE',
      };

      const result1 = mapGetBillDetailResponseToModel(mockResponse);
      const result2 = mapGetBillDetailResponseToModel(mockResponse);

      expect(result1).toBeInstanceOf(GetBillDetailModel);
      expect(result2).toBeInstanceOf(GetBillDetailModel);
      expect(result1.billCode).toBe(result2.billCode);
      expect(result1.service?.code).toBe(result2.service?.code);
      expect(result1.billList?.[0].amount).toBe(result2.billList?.[0].amount);
    });
  });

  describe('Real-world scenarios', () => {
    it('should handle successful electric bill detail response', () => {
      const mockResponse: GetBillDetailResponse = {
        billCode: 'EVN_001',
        service: {code: 'ELECTRIC'},
        queryRef: 'REF_EVN_001',
        customerInfo: {
          cif: 'CIF_EVN_001',
          phone: '+84901234567',
          acct: 'ACCT_EVN_001',
          name: 'Nguyễn Văn A',
          address: '123 Lê Lợi, Quận 1, TP.HCM',
        },
        billList: [
          {
            id: 'EVN_BILL_001',
            no: 'PD01234567890',
            amount: 850000,
            code: 'EVN_ELECTRIC_001',
            custCode: 'EVN_CUST_001',
            custName: 'Nguyễn Văn A',
            period: '2023-11',
            fee: 8500,
            custAddress: '123 Lê Lợi, Quận 1',
          },
        ],
        partnerRespCode: '00',
        tranSeqCount: 1,
        partnerRespDesc: 'Thành công',
        partnerTraceSeq: 'EVN_TRACE_001',
        result: 'SUCCESS',
        extendData: {
          meterNumber: 'MTR001234567',
          kwhUsed: 250,
        },
        paymentRule: 'IMMEDIATE',
      };

      const result = mapGetBillDetailResponseToModel(mockResponse);

      expect(result).toBeInstanceOf(GetBillDetailModel);
      expect(result.billCode).toBe('EVN_001');
      expect(result.service?.code).toBe('ELECTRIC');
      expect(result.billList?.[0].amount).toBe(850000);
    });

    it('should handle water bill detail response', () => {
      const mockResponse: GetBillDetailResponse = {
        billCode: 'SAWACO_001',
        service: {code: 'WATER'},
        queryRef: 'REF_SAWACO_001',
        customerInfo: {
          cif: 'CIF_SAWACO_001',
          phone: '+84987654321',
          acct: 'ACCT_SAWACO_001',
          name: 'Trần Thị B',
          address: '456 Nguyễn Trãi, Quận 5, TP.HCM',
        },
        billList: [
          {
            id: 'SAWACO_BILL_001',
            no: 'WB01234567890',
            amount: 320000,
            code: 'SAWACO_WATER_001',
            custCode: 'SAWACO_CUST_001',
            custName: 'Trần Thị B',
            period: '2023-11',
            fee: 3200,
            custAddress: '456 Nguyễn Trãi, Quận 5',
          },
        ],
        partnerRespCode: '00',
        tranSeqCount: 1,
        partnerRespDesc: 'Thành công',
        partnerTraceSeq: 'SAWACO_TRACE_001',
        result: 'SUCCESS',
        extendData: {
          meterNumber: 'WTR001234567',
          cubicMeterUsed: 32,
        },
        paymentRule: 'IMMEDIATE',
      };

      const result = mapGetBillDetailResponseToModel(mockResponse);

      expect(result).toBeInstanceOf(GetBillDetailModel);
      expect(result.billCode).toBe('SAWACO_001');
      expect(result.service?.code).toBe('WATER');
      expect(result.billList?.[0].amount).toBe(320000);
    });

    it('should handle failed bill detail response', () => {
      const mockResponse: GetBillDetailResponse = {
        billCode: 'INVALID_BILL',
        service: null,
        queryRef: 'REF_FAILED_001',
        customerInfo: null,
        billList: null,
        partnerRespCode: '01',
        tranSeqCount: 0,
        partnerRespDesc: 'Không tìm thấy hóa đơn',
        partnerTraceSeq: 'FAILED_TRACE_001',
        result: 'FAILED',
        extendData: {
          errorCode: 'BILL_NOT_FOUND',
          errorMessage: 'Bill not found in system',
        },
        paymentRule: null as any,
      };

      const result = mapGetBillDetailResponseToModel(mockResponse);

      expect(result).toBeInstanceOf(GetBillDetailModel);
      expect(result.billCode).toBe('INVALID_BILL');
      expect(result.service).toBeUndefined();
      expect(result.customerInfo).toBeUndefined();
      expect(result.billList).toBeUndefined();
      expect(result.result).toBe('FAILED');
    });

    it('should handle response with multiple bills for same customer', () => {
      const mockResponse: GetBillDetailResponse = {
        billCode: 'MULTI_BILL_001',
        service: {code: 'ELECTRIC'},
        queryRef: 'REF_MULTI_001',
        customerInfo: {
          cif: 'CIF_MULTI_001',
          phone: '+84123456789',
          acct: 'ACCT_MULTI_001',
          name: 'Lê Văn C',
          address: '789 Điện Biên Phủ, Quận 3, TP.HCM',
        },
        billList: [
          {
            id: 'BILL_001',
            no: 'PD01111111111',
            amount: 500000,
            code: 'ELECTRIC_001',
            custCode: 'CUST_001',
            custName: 'Lê Văn C',
            period: '2023-10',
            fee: 5000,
            custAddress: '789 Điện Biên Phủ, Quận 3',
          },
          {
            id: 'BILL_002',
            no: 'PD02222222222',
            amount: 600000,
            code: 'ELECTRIC_002',
            custCode: 'CUST_001',
            custName: 'Lê Văn C',
            period: '2023-11',
            fee: 6000,
            custAddress: '789 Điện Biên Phủ, Quận 3',
          },
        ],
        partnerRespCode: '00',
        tranSeqCount: 2,
        partnerRespDesc: 'Thành công',
        partnerTraceSeq: 'MULTI_TRACE_001',
        result: 'SUCCESS',
        extendData: {
          totalAmount: 1100000,
          totalFee: 11000,
        },
        paymentRule: 'IMMEDIATE',
      };

      const result = mapGetBillDetailResponseToModel(mockResponse);

      expect(result).toBeInstanceOf(GetBillDetailModel);
      expect(result.billList).toHaveLength(2);
      expect(result.billList?.[0].period).toBe('2023-10');
      expect(result.billList?.[1].period).toBe('2023-11');
      expect(result.tranSeqCount).toBe(2);
    });
  });
});
