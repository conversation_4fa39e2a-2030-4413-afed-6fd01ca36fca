import {describe, it, expect, jest} from '@jest/globals';
import {mapMyBillContactListResponseToModel} from '../MyBillContactListMapper';
import {
  MyBillContactListResponse,
  MyBillContactResponse,
  AccountResponse,
} from '../../../models/my-bill-contact-list/MyBillContactListResponse';
import {
  MyBillContactListModel,
  MyBillContactModel,
  AccountModel,
} from '../../../../domain/entities/my-bill-contact-list/MyBillContactListModel';

// Mock console.log to avoid test output pollution
const consoleSpy = jest.spyOn(console, 'log').mockImplementation(() => {});

describe('MyBillContactListMapper', () => {
  afterEach(() => {
    consoleSpy.mockClear();
  });

  afterAll(() => {
    consoleSpy.mockRestore();
  });

  describe('mapMyBillContactListResponseToModel', () => {
    it('should create MyBillContactListModel from response array', () => {
      const mockResponse: MyBillContactListResponse = [
        {
          id: 'CONTACT_001',
          name: 'EVN Ho Chi Minh City',
          alias: 'Điện lực TPHCM',
          category: 'ELECTRIC',
          activeStatus: 'ACTIVE',
          accessContextScope: 'PRIVATE',
          accounts: [
            {
              bankName: 'Maritime Bank',
              accountNumber: 'MSB00*********0',
              bankCode: 'MSB',
              accountType: 'SAVINGS',
              externalId: 'EXT_001',
              bankPostCode: '700000',
            },
            {
              bankName: 'Maritime Bank',
              accountNumber: 'MSB00*********1',
              bankCode: 'MSB',
              accountType: 'CURRENT',
              externalId: 'EXT_002',
              bankPostCode: '700000',
            },
          ],
          additions: {
            payableAmount: 500000,
            favoriteStatus: 'ACTIVE',
            reminderStatus: 'INACTIVE',
          },
        },
        {
          id: 'CONTACT_002',
          name: 'SAWACO',
          alias: 'Công ty cấp nước',
          category: 'WATER',
          activeStatus: 'ACTIVE',
          accessContextScope: 'PUBLIC',
          accounts: [
            {
              bankName: 'Maritime Bank',
              accountNumber: 'MSB00*********2',
              bankCode: 'MSB',
              accountType: 'SAVINGS',
              externalId: 'EXT_003',
              bankPostCode: '700000',
            },
          ],
          additions: {
            payableAmount: 200000,
            favoriteStatus: 'INACTIVE',
            reminderStatus: 'ACTIVE',
          },
        },
      ];

      const result = mapMyBillContactListResponseToModel(mockResponse);

      expect(result).toHaveLength(2);
      expect(result[0]).toBeInstanceOf(MyBillContactModel);
      expect(result[1]).toBeInstanceOf(MyBillContactModel);

      // Check first contact
      expect(result[0].id).toBe('CONTACT_001');
      expect(result[0].name).toBe('EVN Ho Chi Minh City');
      expect(result[0].alias).toBe('Điện lực TPHCM');
      expect(result[0].category).toBe('ELECTRIC');
      expect(result[0].activeStatus).toBe('ACTIVE');
      expect(result[0].accessContextScope).toBe('PRIVATE');
      expect(result[0].payableAmount).toBe(500000);
      expect(result[0].favoriteStatus).toBe('ACTIVE');
      expect(result[0].reminderStatus).toBe('INACTIVE');
      expect(result[0].accounts).toHaveLength(2);
      expect(result[0].accounts![0]).toBeInstanceOf(AccountModel);
      expect(result[0].accounts![0].bankName).toBe('Maritime Bank');
      expect(result[0].accounts![0].accountNumber).toBe('MSB00*********0');

      // Check second contact
      expect(result[1].id).toBe('CONTACT_002');
      expect(result[1].name).toBe('SAWACO');
      expect(result[1].alias).toBe('Công ty cấp nước');
      expect(result[1].category).toBe('WATER');
      expect(result[1].payableAmount).toBe(200000);
      expect(result[1].favoriteStatus).toBe('INACTIVE');
      expect(result[1].reminderStatus).toBe('ACTIVE');
      expect(result[1].accounts).toHaveLength(1);
    });

    it('should handle empty response array', () => {
      const mockResponse: MyBillContactListResponse = [];

      const result = mapMyBillContactListResponseToModel(mockResponse);

      expect(result).toEqual([]);
      expect(result).toHaveLength(0);
    });

    it('should handle contacts without accounts', () => {
      const mockResponse: MyBillContactListResponse = [
        {
          id: 'CONTACT_NO_ACCOUNTS',
          name: 'Contact Without Accounts',
          alias: 'No Accounts',
          category: 'OTHER',
          activeStatus: 'ACTIVE',
          accessContextScope: 'PRIVATE',
          accounts: undefined,
          additions: {
            payableAmount: 0,
            favoriteStatus: 'INACTIVE',
            reminderStatus: 'INACTIVE',
          },
        },
      ];

      const result = mapMyBillContactListResponseToModel(mockResponse);

      expect(result).toHaveLength(1);
      expect(result[0].accounts).toEqual([]);
      expect(result[0].id).toBe('CONTACT_NO_ACCOUNTS');
      expect(result[0].name).toBe('Contact Without Accounts');
    });

    it('should handle contacts with empty accounts array', () => {
      const mockResponse: MyBillContactListResponse = [
        {
          id: 'CONTACT_EMPTY_ACCOUNTS',
          name: 'Contact With Empty Accounts',
          alias: 'Empty Accounts',
          category: 'OTHER',
          activeStatus: 'ACTIVE',
          accessContextScope: 'PRIVATE',
          accounts: [],
          additions: {
            payableAmount: 0,
            favoriteStatus: 'INACTIVE',
            reminderStatus: 'INACTIVE',
          },
        },
      ];

      const result = mapMyBillContactListResponseToModel(mockResponse);

      expect(result).toHaveLength(1);
      expect(result[0].accounts).toEqual([]);
      expect(result[0].accounts).toHaveLength(0);
    });

    it('should handle contacts without additions', () => {
      const mockResponse: MyBillContactListResponse = [
        {
          id: 'CONTACT_NO_ADDITIONS',
          name: 'Contact Without Additions',
          alias: 'No Additions',
          category: 'OTHER',
          activeStatus: 'ACTIVE',
          accessContextScope: 'PRIVATE',
          accounts: [
            {
              bankName: 'Test Bank',
              accountNumber: 'TEST123',
              bankCode: 'TEST',
              accountType: 'SAVINGS',
              externalId: 'EXT_TEST',
              bankPostCode: '000000',
            },
          ],
          additions: undefined,
        },
      ];

      const result = mapMyBillContactListResponseToModel(mockResponse);

      expect(result).toHaveLength(1);
      expect(result[0].payableAmount).toBeUndefined();
      expect(result[0].favoriteStatus).toBeUndefined();
      expect(result[0].reminderStatus).toBeUndefined();
      expect(result[0].accounts).toHaveLength(1);
    });

    it('should log contact data to console for each contact', () => {
      const mockResponse: MyBillContactListResponse = [
        {
          id: 'CONTACT_LOG_1',
          name: 'Log Test 1',
          alias: 'Log 1',
          category: 'TEST',
          activeStatus: 'ACTIVE',
          accessContextScope: 'PRIVATE',
          accounts: [],
          additions: {
            payableAmount: 100000,
            favoriteStatus: 'ACTIVE',
            reminderStatus: 'ACTIVE',
          },
        },
        {
          id: 'CONTACT_LOG_2',
          name: 'Log Test 2',
          alias: 'Log 2',
          category: 'TEST',
          activeStatus: 'ACTIVE',
          accessContextScope: 'PRIVATE',
          accounts: [],
          additions: {
            payableAmount: 200000,
            favoriteStatus: 'INACTIVE',
            reminderStatus: 'INACTIVE',
          },
        },
      ];

      const result = mapMyBillContactListResponseToModel(mockResponse);

      expect(consoleSpy).toHaveBeenCalledTimes(2);
      expect(consoleSpy).toHaveBeenNthCalledWith(1, 'LOG CONTACT', result[0]);
      expect(consoleSpy).toHaveBeenNthCalledWith(2, 'LOG CONTACT', result[1]);
    });

    it('should handle Vietnamese characters correctly', () => {
      const mockResponse: MyBillContactListResponse = [
        {
          id: 'CONTACT_VN_001',
          name: 'Công ty Điện lực TP.HCM',
          alias: 'Điện lực TPHCM',
          category: 'ĐIỆN_LỰC',
          activeStatus: 'ACTIVE',
          accessContextScope: 'PRIVATE',
          accounts: [
            {
              bankName: 'Ngân hàng Hàng Hải Việt Nam',
              accountNumber: 'MSB00*********0',
              bankCode: 'MSB',
              accountType: 'TÀI_KHOẢN_TIẾT_KIỆM',
              externalId: 'EXT_VN_001',
              bankPostCode: '700000',
            },
          ],
          additions: {
            payableAmount: 850000,
            favoriteStatus: 'ACTIVE',
            reminderStatus: 'ACTIVE',
          },
        },
      ];

      const result = mapMyBillContactListResponseToModel(mockResponse);

      expect(result[0].name).toBe('Công ty Điện lực TP.HCM');
      expect(result[0].alias).toBe('Điện lực TPHCM');
      expect(result[0].category).toBe('ĐIỆN_LỰC');
      expect(result[0].accounts![0].bankName).toBe('Ngân hàng Hàng Hải Việt Nam');
      expect(result[0].accounts![0].accountType).toBe('TÀI_KHOẢN_TIẾT_KIỆM');
    });

    it('should handle null and undefined values', () => {
      const mockResponse: MyBillContactListResponse = [
        {
          id: null as any,
          name: undefined as any,
          alias: null as any,
          category: undefined as any,
          activeStatus: null as any,
          accessContextScope: undefined as any,
          accounts: [
            {
              bankName: null as any,
              accountNumber: undefined as any,
              bankCode: null as any,
              accountType: undefined as any,
              externalId: null as any,
              bankPostCode: undefined as any,
            },
          ],
          additions: {
            payableAmount: null as any,
            favoriteStatus: undefined as any,
            reminderStatus: null as any,
          },
        },
      ];

      const result = mapMyBillContactListResponseToModel(mockResponse);

      expect(result).toHaveLength(1);
      expect(result[0].id).toBeNull();
      expect(result[0].name).toBeUndefined();
      expect(result[0].alias).toBeNull();
      expect(result[0].category).toBeUndefined();
      expect(result[0].activeStatus).toBeNull();
      expect(result[0].accessContextScope).toBeUndefined();
      expect(result[0].payableAmount).toBeNull();
      expect(result[0].favoriteStatus).toBeUndefined();
      expect(result[0].reminderStatus).toBeNull();
      expect(result[0].accounts![0].bankName).toBeNull();
      expect(result[0].accounts![0].accountNumber).toBeUndefined();
    });

    it('should handle different data types correctly', () => {
      const mockResponse: MyBillContactListResponse = [
        {
          id: 123 as any, // Number instead of string
          name: true as any, // Boolean instead of string
          alias: ['alias'] as any, // Array instead of string
          category: {category: 'TEST'} as any, // Object instead of string
          activeStatus: 'ACTIVE',
          accessContextScope: 'PRIVATE',
          accounts: [
            {
              bankName: 'Valid Bank',
              accountNumber: ********* as any, // Number instead of string
              bankCode: true as any, // Boolean instead of string
              accountType: ['SAVINGS'] as any, // Array instead of string
              externalId: {id: 'EXT_001'} as any, // Object instead of string
              bankPostCode: 700000 as any, // Number instead of string
            },
          ],
          additions: {
            payableAmount: '500000' as any, // String instead of number
            favoriteStatus: 'ACTIVE',
            reminderStatus: 'INACTIVE',
          },
        },
      ];

      const result = mapMyBillContactListResponseToModel(mockResponse);

      expect(result[0].id).toBe(123);
      expect(result[0].name).toBe(true);
      expect(result[0].alias).toEqual(['alias']);
      expect(result[0].category).toEqual({category: 'TEST'});
      expect(result[0].payableAmount).toBe('500000');
      expect(result[0].accounts![0].accountNumber).toBe(*********);
      expect(result[0].accounts![0].bankCode).toBe(true);
      expect(result[0].accounts![0].accountType).toEqual(['SAVINGS']);
      expect(result[0].accounts![0].externalId).toEqual({id: 'EXT_001'});
      expect(result[0].accounts![0].bankPostCode).toBe(700000);
    });

    it('should handle large arrays efficiently', () => {
      const largeAccountList = Array.from({length: 100}, (_, index) => ({
        bankName: `Bank ${index}`,
        accountNumber: `ACC_${index}`,
        bankCode: `CODE_${index}`,
        accountType: index % 2 === 0 ? 'SAVINGS' : 'CURRENT',
        externalId: `EXT_${index}`,
        bankPostCode: `${700000 + index}`,
      })) as AccountResponse[];

      const largeContactList = Array.from({length: 100}, (_, index) => ({
        id: `CONTACT_${index}`,
        name: `Contact ${index}`,
        alias: `Alias ${index}`,
        category: `CATEGORY_${index % 5}`,
        activeStatus: index % 2 === 0 ? 'ACTIVE' : 'INACTIVE',
        accessContextScope: index % 2 === 0 ? 'PRIVATE' : 'PUBLIC',
        accounts: largeAccountList,
        additions: {
          payableAmount: (index + 1) * 1000,
          favoriteStatus: index % 2 === 0 ? 'ACTIVE' : 'INACTIVE',
          reminderStatus: index % 3 === 0 ? 'ACTIVE' : 'INACTIVE',
        },
      })) as MyBillContactListResponse;

      const startTime = performance.now();
      const result = mapMyBillContactListResponseToModel(largeContactList);
      const endTime = performance.now();

      expect(result).toHaveLength(100);
      expect(result[0].accounts).toHaveLength(100);
      expect(result[99].accounts).toHaveLength(100);
      expect(endTime - startTime).toBeLessThan(100); // Should complete in less than 100ms
    });
  });

  describe('Performance and Memory Management', () => {
    it('should handle repeated mappings efficiently', () => {
      const mockResponse: MyBillContactListResponse = [
        {
          id: 'PERF_TEST_001',
          name: 'Performance Test',
          alias: 'Perf Test',
          category: 'PERFORMANCE',
          activeStatus: 'ACTIVE',
          accessContextScope: 'PRIVATE',
          accounts: [
            {
              bankName: 'Test Bank',
              accountNumber: 'TEST123',
              bankCode: 'TEST',
              accountType: 'SAVINGS',
              externalId: 'EXT_TEST',
              bankPostCode: '000000',
            },
          ],
          additions: {
            payableAmount: 100000,
            favoriteStatus: 'ACTIVE',
            reminderStatus: 'ACTIVE',
          },
        },
      ];

      const iterations = 1000;
      const startTime = performance.now();

      for (let i = 0; i < iterations; i++) {
        mapMyBillContactListResponseToModel(mockResponse);
      }

      const endTime = performance.now();
      expect(endTime - startTime).toBeLessThan(100); // Should complete 1000 mappings in less than 100ms
    });

    it('should not cause memory leaks with repeated mappings', () => {
      const mockResponse: MyBillContactListResponse = [
        {
          id: 'MEMORY_TEST_001',
          name: 'Memory Test',
          alias: 'Memory Test',
          category: 'MEMORY',
          activeStatus: 'ACTIVE',
          accessContextScope: 'PRIVATE',
          accounts: [
            {
              bankName: 'Memory Bank',
              accountNumber: 'MEMORY123',
              bankCode: 'MEMORY',
              accountType: 'SAVINGS',
              externalId: 'EXT_MEMORY',
              bankPostCode: '000000',
            },
          ],
          additions: {
            payableAmount: 100000,
            favoriteStatus: 'ACTIVE',
            reminderStatus: 'ACTIVE',
          },
        },
      ];

      // Perform many mappings to test for memory leaks
      for (let i = 0; i < 100; i++) {
        const result = mapMyBillContactListResponseToModel(mockResponse);
        expect(result).toHaveLength(1);
        expect(result[0]).toBeInstanceOf(MyBillContactModel);
      }

      // If we reach here without running out of memory, the test passes
      expect(true).toBe(true);
    });
  });

  describe('Type Safety and Edge Cases', () => {
    it('should maintain type safety with complex nested structures', () => {
      const mockResponse: MyBillContactListResponse = [
        {
          id: 'TYPE_SAFETY_001',
          name: 'Type Safety Test',
          alias: 'Type Test',
          category: 'TYPE_TEST',
          activeStatus: 'ACTIVE',
          accessContextScope: 'PRIVATE',
          accounts: [
            {
              bankName: 'Type Bank',
              accountNumber: 'TYPE123',
              bankCode: 'TYPE',
              accountType: 'SAVINGS',
              externalId: 'EXT_TYPE',
              bankPostCode: '000000',
            },
          ],
          additions: {
            payableAmount: 100000,
            favoriteStatus: 'ACTIVE',
            reminderStatus: 'INACTIVE',
          },
        },
      ];

      const result = mapMyBillContactListResponseToModel(mockResponse);

      // Verify all properties exist and have correct structure
      expect(result[0]).toBeInstanceOf(MyBillContactModel);
      expect(result[0].accounts![0]).toBeInstanceOf(AccountModel);
      expect(typeof result[0].id).toBe('string');
      expect(typeof result[0].name).toBe('string');
      expect(typeof result[0].alias).toBe('string');
      expect(typeof result[0].category).toBe('string');
      expect(typeof result[0].activeStatus).toBe('string');
      expect(typeof result[0].accessContextScope).toBe('string');
      expect(typeof result[0].payableAmount).toBe('number');
      expect(typeof result[0].favoriteStatus).toBe('string');
      expect(typeof result[0].reminderStatus).toBe('string');
      expect(Array.isArray(result[0].accounts)).toBe(true);
    });

    it('should handle circular references gracefully', () => {
      const circularObject: any = {
        id: 'CIRCULAR_001',
        name: 'Circular Test',
        alias: 'Circular',
        category: 'CIRCULAR',
        activeStatus: 'ACTIVE',
        accessContextScope: 'PRIVATE',
        accounts: [
          {
            bankName: 'Circular Bank',
            accountNumber: 'CIRCULAR123',
            bankCode: 'CIRCULAR',
            accountType: 'SAVINGS',
            externalId: 'EXT_CIRCULAR',
            bankPostCode: '000000',
          },
        ],
        additions: {
          payableAmount: 100000,
          favoriteStatus: 'ACTIVE',
          reminderStatus: 'ACTIVE',
        },
      };

      // Create circular reference
      circularObject.self = circularObject;
      circularObject.accounts[0].parent = circularObject;

      const result = mapMyBillContactListResponseToModel([circularObject]);

      expect(result).toHaveLength(1);
      expect(result[0]).toBeInstanceOf(MyBillContactModel);
      expect(result[0].id).toBe('CIRCULAR_001');
    });
  });
});
