import {describe, it, expect} from '@jest/globals';
import {mapPaymentOrderStatusResponseToModel} from '../PaymentOrderStatusMapper';
import {PaymentOrderStatusResponse} from '../../../models/payment-order-status/PaymentOrderStatusResponse';
import {
  PaymentOrderStatusModel,
  PaymentOrderStatusAdditionalModel,
} from '../../../../domain/entities/payment-order-status/PaymentOrderStatusModel';

describe('PaymentOrderStatusMapper', () => {
  describe('mapPaymentOrderStatusResponseToModel', () => {
    it('should create PaymentOrderStatusModel with status and additional model', () => {
      const mockResponse: PaymentOrderStatusResponse = {
        status: 'SUCCESS',
        additions: {
          t24TraceCode: 'T24001234567',
        },
      };

      const result = mapPaymentOrderStatusResponseToModel(mockResponse);

      expect(result).toBeInstanceOf(PaymentOrderStatusModel);
      expect(result.bankStatus).toBe('SUCCESS');
      expect(result.additions).toBeInstanceOf(PaymentOrderStatusAdditionalModel);
      expect(result.additions.t24TraceCode).toBe('T24001234567');
    });

    it('should handle response with null status', () => {
      const mockResponse: PaymentOrderStatusResponse = {
        status: null as any,
        additions: {
          t24TraceCode: 'T24001234567',
        },
      };

      const result = mapPaymentOrderStatusResponseToModel(mockResponse);

      expect(result).toBeInstanceOf(PaymentOrderStatusModel);
      expect(result.bankStatus).toBe('');
      expect(result.additions).toBeInstanceOf(PaymentOrderStatusAdditionalModel);
    });

    it('should handle response with undefined status', () => {
      const mockResponse: PaymentOrderStatusResponse = {
        status: undefined as any,
        additions: {
          t24TraceCode: 'T24001234567',
        },
      };

      const result = mapPaymentOrderStatusResponseToModel(mockResponse);

      expect(result).toBeInstanceOf(PaymentOrderStatusModel);
      expect(result.bankStatus).toBe('');
      expect(result.additions).toBeInstanceOf(PaymentOrderStatusAdditionalModel);
    });

    it('should handle response with empty string status', () => {
      const mockResponse: PaymentOrderStatusResponse = {
        status: '',
        additions: {
          t24TraceCode: 'T24001234567',
        },
      };

      const result = mapPaymentOrderStatusResponseToModel(mockResponse);

      expect(result).toBeInstanceOf(PaymentOrderStatusModel);
      expect(result.bankStatus).toBe('');
      expect(result.additions).toBeInstanceOf(PaymentOrderStatusAdditionalModel);
    });

    it('should handle response with null additions', () => {
      const mockResponse: PaymentOrderStatusResponse = {
        status: 'PENDING',
        additions: null as any,
      };

      const result = mapPaymentOrderStatusResponseToModel(mockResponse);

      expect(result).toBeInstanceOf(PaymentOrderStatusModel);
      expect(result.bankStatus).toBe('PENDING');
      expect(result.additions).toBeInstanceOf(PaymentOrderStatusAdditionalModel);
      expect(result.additions.t24TraceCode).toBe('');
    });

    it('should handle response with undefined additions', () => {
      const mockResponse: PaymentOrderStatusResponse = {
        status: 'FAILED',
        additions: undefined as any,
      };

      const result = mapPaymentOrderStatusResponseToModel(mockResponse);

      expect(result).toBeInstanceOf(PaymentOrderStatusModel);
      expect(result.bankStatus).toBe('FAILED');
      expect(result.additions).toBeInstanceOf(PaymentOrderStatusAdditionalModel);
      expect(result.additions.t24TraceCode).toBe('');
    });

    it('should handle response with null t24TraceCode in additions', () => {
      const mockResponse: PaymentOrderStatusResponse = {
        status: 'SUCCESS',
        additions: {
          t24TraceCode: null as any,
        },
      };

      const result = mapPaymentOrderStatusResponseToModel(mockResponse);

      expect(result).toBeInstanceOf(PaymentOrderStatusModel);
      expect(result.bankStatus).toBe('SUCCESS');
      expect(result.additions).toBeInstanceOf(PaymentOrderStatusAdditionalModel);
      expect(result.additions.t24TraceCode).toBe('');
    });

    it('should handle response with undefined t24TraceCode in additions', () => {
      const mockResponse: PaymentOrderStatusResponse = {
        status: 'SUCCESS',
        additions: {
          t24TraceCode: undefined as any,
        },
      };

      const result = mapPaymentOrderStatusResponseToModel(mockResponse);

      expect(result).toBeInstanceOf(PaymentOrderStatusModel);
      expect(result.bankStatus).toBe('SUCCESS');
      expect(result.additions).toBeInstanceOf(PaymentOrderStatusAdditionalModel);
      expect(result.additions.t24TraceCode).toBe('');
    });

    it('should handle response with empty string t24TraceCode', () => {
      const mockResponse: PaymentOrderStatusResponse = {
        status: 'SUCCESS',
        additions: {
          t24TraceCode: '',
        },
      };

      const result = mapPaymentOrderStatusResponseToModel(mockResponse);

      expect(result).toBeInstanceOf(PaymentOrderStatusModel);
      expect(result.bankStatus).toBe('SUCCESS');
      expect(result.additions).toBeInstanceOf(PaymentOrderStatusAdditionalModel);
      expect(result.additions.t24TraceCode).toBe('');
    });

    it('should handle response with additional properties in additions', () => {
      const mockResponse: PaymentOrderStatusResponse = {
        status: 'SUCCESS',
        additions: {
          t24TraceCode: 'T24001234567',
          extraProperty: 'extra value',
          anotherProperty: 123,
        } as any,
      };

      const result = mapPaymentOrderStatusResponseToModel(mockResponse);

      expect(result).toBeInstanceOf(PaymentOrderStatusModel);
      expect(result.bankStatus).toBe('SUCCESS');
      expect(result.additions).toBeInstanceOf(PaymentOrderStatusAdditionalModel);
      expect(result.additions.t24TraceCode).toBe('T24001234567');
    });

    it('should handle response with various status values', () => {
      const statusValues = ['SUCCESS', 'FAILED', 'PENDING', 'PROCESSING', 'CANCELLED', 'TIMEOUT'];

      statusValues.forEach(status => {
        const mockResponse: PaymentOrderStatusResponse = {
          status,
          additions: {
            t24TraceCode: `T24${status}`,
          },
        };

        const result = mapPaymentOrderStatusResponseToModel(mockResponse);

        expect(result).toBeInstanceOf(PaymentOrderStatusModel);
        expect(result.bankStatus).toBe(status);
        expect(result.additions.t24TraceCode).toBe(`T24${status}`);
      });
    });

    it('should handle response with long t24TraceCode', () => {
      const longTraceCode = 'T24' + '**********'.repeat(10);
      const mockResponse: PaymentOrderStatusResponse = {
        status: 'SUCCESS',
        additions: {
          t24TraceCode: longTraceCode,
        },
      };

      const result = mapPaymentOrderStatusResponseToModel(mockResponse);

      expect(result).toBeInstanceOf(PaymentOrderStatusModel);
      expect(result.bankStatus).toBe('SUCCESS');
      expect(result.additions.t24TraceCode).toBe(longTraceCode);
    });

    it('should handle response with special characters in status and t24TraceCode', () => {
      const mockResponse: PaymentOrderStatusResponse = {
        status: 'SUCCESS_WITH_SPECIAL_CHARS',
        additions: {
          t24TraceCode: 'T24-001_234@567#890',
        },
      };

      const result = mapPaymentOrderStatusResponseToModel(mockResponse);

      expect(result).toBeInstanceOf(PaymentOrderStatusModel);
      expect(result.bankStatus).toBe('SUCCESS_WITH_SPECIAL_CHARS');
      expect(result.additions.t24TraceCode).toBe('T24-001_234@567#890');
    });

    it('should handle response with unicode characters', () => {
      const mockResponse: PaymentOrderStatusResponse = {
        status: 'THÀNH_CÔNG',
        additions: {
          t24TraceCode: 'T24_Việt_Nam_001',
        },
      };

      const result = mapPaymentOrderStatusResponseToModel(mockResponse);

      expect(result).toBeInstanceOf(PaymentOrderStatusModel);
      expect(result.bankStatus).toBe('THÀNH_CÔNG');
      expect(result.additions.t24TraceCode).toBe('T24_Việt_Nam_001');
    });
  });

  describe('Performance considerations', () => {
    it('should handle mapping quickly for normal responses', () => {
      const mockResponse: PaymentOrderStatusResponse = {
        status: 'SUCCESS',
        additions: {
          t24TraceCode: 'T24001234567',
        },
      };

      const startTime = performance.now();
      const result = mapPaymentOrderStatusResponseToModel(mockResponse);
      const endTime = performance.now();

      expect(result).toBeInstanceOf(PaymentOrderStatusModel);
      expect(endTime - startTime).toBeLessThan(10); // Should complete in less than 10ms
    });

    it('should handle multiple rapid mappings efficiently', () => {
      const mockResponse: PaymentOrderStatusResponse = {
        status: 'SUCCESS',
        additions: {
          t24TraceCode: 'T24001234567',
        },
      };
      const iterations = 1000;

      const startTime = performance.now();
      for (let i = 0; i < iterations; i++) {
        mapPaymentOrderStatusResponseToModel(mockResponse);
      }
      const endTime = performance.now();

      expect(endTime - startTime).toBeLessThan(100); // Should complete 1000 mappings in less than 100ms
    });
  });

  describe('Memory management', () => {
    it('should not cause memory leaks with repeated mappings', () => {
      const mockResponse: PaymentOrderStatusResponse = {
        status: 'SUCCESS',
        additions: {
          t24TraceCode: 'T24001234567',
        },
      };

      // Perform many mappings to test for memory leaks
      for (let i = 0; i < 100; i++) {
        const result = mapPaymentOrderStatusResponseToModel(mockResponse);
        expect(result).toBeInstanceOf(PaymentOrderStatusModel);
      }

      // If we reach here without running out of memory, the test passes
      expect(true).toBe(true);
    });
  });

  describe('Type safety', () => {
    it('should maintain type safety with proper response structure', () => {
      const mockResponse: PaymentOrderStatusResponse = {
        status: 'SUCCESS',
        additions: {
          t24TraceCode: 'T24001234567',
        },
      };

      const result = mapPaymentOrderStatusResponseToModel(mockResponse);

      expect(typeof result).toBe('object');
      expect(result).toBeInstanceOf(PaymentOrderStatusModel);
      expect(typeof result.bankStatus).toBe('string');
      expect(result.additions).toBeInstanceOf(PaymentOrderStatusAdditionalModel);
    });

    it('should handle response with mixed data types in additions', () => {
      const mockResponse: PaymentOrderStatusResponse = {
        status: 'SUCCESS',
        additions: {
          t24TraceCode: 'T24001234567',
          numericProperty: 123,
          booleanProperty: true,
          arrayProperty: [1, 2, 3],
          objectProperty: {nested: 'value'},
        } as any,
      };

      const result = mapPaymentOrderStatusResponseToModel(mockResponse);

      expect(result).toBeInstanceOf(PaymentOrderStatusModel);
      expect(result.additions.t24TraceCode).toBe('T24001234567');
    });
  });

  describe('Edge cases', () => {
    it('should handle response with very large status string', () => {
      const largeStatus = 'STATUS_' + 'A'.repeat(1000);
      const mockResponse: PaymentOrderStatusResponse = {
        status: largeStatus,
        additions: {
          t24TraceCode: 'T24001234567',
        },
      };

      const result = mapPaymentOrderStatusResponseToModel(mockResponse);

      expect(result).toBeInstanceOf(PaymentOrderStatusModel);
      expect(result.bankStatus).toBe(largeStatus);
    });

    it('should handle response with numeric status (edge case)', () => {
      const mockResponse: PaymentOrderStatusResponse = {
        status: 200 as any,
        additions: {
          t24TraceCode: 'T24001234567',
        },
      };

      const result = mapPaymentOrderStatusResponseToModel(mockResponse);

      expect(result).toBeInstanceOf(PaymentOrderStatusModel);
      expect(result.bankStatus).toBe(200);
    });

    it('should handle response with boolean status (edge case)', () => {
      const mockResponse: PaymentOrderStatusResponse = {
        status: true as any,
        additions: {
          t24TraceCode: 'T24001234567',
        },
      };

      const result = mapPaymentOrderStatusResponseToModel(mockResponse);

      expect(result).toBeInstanceOf(PaymentOrderStatusModel);
      expect(result.bankStatus).toBe(true);
    });

    it('should handle response with object status (edge case)', () => {
      const mockResponse: PaymentOrderStatusResponse = {
        status: {code: 'SUCCESS', message: 'OK'} as any,
        additions: {
          t24TraceCode: 'T24001234567',
        },
      };

      const result = mapPaymentOrderStatusResponseToModel(mockResponse);

      expect(result).toBeInstanceOf(PaymentOrderStatusModel);
      expect(result.bankStatus).toEqual({code: 'SUCCESS', message: 'OK'});
    });
  });

  describe('Integration scenarios', () => {
    it('should work correctly in a typical payment order status check flow', () => {
      const mockResponse: PaymentOrderStatusResponse = {
        status: 'COMPLETED',
        additions: {
          t24TraceCode: 'T24ORD20231201001',
        },
      };

      const result = mapPaymentOrderStatusResponseToModel(mockResponse);

      expect(result).toBeInstanceOf(PaymentOrderStatusModel);
      expect(result.bankStatus).toBe('COMPLETED');
      expect(result.additions.t24TraceCode).toBe('T24ORD20231201001');
    });

    it('should maintain consistency across multiple mappings', () => {
      const mockResponse: PaymentOrderStatusResponse = {
        status: 'PROCESSING',
        additions: {
          t24TraceCode: 'T24PROC001',
        },
      };

      const result1 = mapPaymentOrderStatusResponseToModel(mockResponse);
      const result2 = mapPaymentOrderStatusResponseToModel(mockResponse);

      expect(result1).toBeInstanceOf(PaymentOrderStatusModel);
      expect(result2).toBeInstanceOf(PaymentOrderStatusModel);
      expect(result1.bankStatus).toBe(result2.bankStatus);
      expect(result1.additions.t24TraceCode).toBe(result2.additions.t24TraceCode);
    });
  });

  describe('Real-world scenarios', () => {
    it('should handle successful payment status response', () => {
      const mockResponse: PaymentOrderStatusResponse = {
        status: 'SUCCESS',
        additions: {
          t24TraceCode: 'T24PAY20231201001',
        },
      };

      const result = mapPaymentOrderStatusResponseToModel(mockResponse);

      expect(result).toBeInstanceOf(PaymentOrderStatusModel);
      expect(result.bankStatus).toBe('SUCCESS');
      expect(result.additions.t24TraceCode).toBe('T24PAY20231201001');
    });

    it('should handle failed payment status response', () => {
      const mockResponse: PaymentOrderStatusResponse = {
        status: 'FAILED',
        additions: {
          t24TraceCode: 'T24FAIL20231201001',
        },
      };

      const result = mapPaymentOrderStatusResponseToModel(mockResponse);

      expect(result).toBeInstanceOf(PaymentOrderStatusModel);
      expect(result.bankStatus).toBe('FAILED');
      expect(result.additions.t24TraceCode).toBe('T24FAIL20231201001');
    });

    it('should handle pending payment status response', () => {
      const mockResponse: PaymentOrderStatusResponse = {
        status: 'PENDING',
        additions: {
          t24TraceCode: 'T24PEND20231201001',
        },
      };

      const result = mapPaymentOrderStatusResponseToModel(mockResponse);

      expect(result).toBeInstanceOf(PaymentOrderStatusModel);
      expect(result.bankStatus).toBe('PENDING');
      expect(result.additions.t24TraceCode).toBe('T24PEND20231201001');
    });

    it('should handle timeout payment status response', () => {
      const mockResponse: PaymentOrderStatusResponse = {
        status: 'TIMEOUT',
        additions: {
          t24TraceCode: 'T24TIME20231201001',
        },
      };

      const result = mapPaymentOrderStatusResponseToModel(mockResponse);

      expect(result).toBeInstanceOf(PaymentOrderStatusModel);
      expect(result.bankStatus).toBe('TIMEOUT');
      expect(result.additions.t24TraceCode).toBe('T24TIME20231201001');
    });

    it('should handle response without t24TraceCode in real scenario', () => {
      const mockResponse: PaymentOrderStatusResponse = {
        status: 'PROCESSING',
        additions: {} as any,
      };

      const result = mapPaymentOrderStatusResponseToModel(mockResponse);

      expect(result).toBeInstanceOf(PaymentOrderStatusModel);
      expect(result.bankStatus).toBe('PROCESSING');
      expect(result.additions.t24TraceCode).toBe('');
    });
  });
});
