import {describe, it, expect} from '@jest/globals';
import {
  mapSourceAccountListResponseToModel,
  mapUserPreferencesResponseToModel,
  mapProductKindResponseToModel,
  mapProductResponseToModel,
  mapSourceAccountResponseToModel,
} from '../SourceAccountListMapper';
import {SourceAccountListResponse} from '../../../models/source-account-list/SourceAccountListResponse';
import {SourceAccountListModel} from '../../../../domain/entities/source-account-list/SourceAccountListModel';

describe('SourceAccountListMapper', () => {
  describe('mapSourceAccountListResponseToModel', () => {
    it('should create SourceAccountListModel with all properties from response', () => {
      const mockResponse: SourceAccountListResponse = {
        totalCount: 2,
        data: [
          {
            id: 'ACCT_001',
            productKindName: 'Savings Account',
            legalEntityIds: ['ENTITY_001'],
            productId: 'PROD_001',
            productTypeName: 'Savings',
            externalProductId: 'EXT_PROD_001',
            externalArrangementId: 'EXT_ARR_001',
            userPreferences: {
              arrangementId: 'ARR_001',
              alias: 'My Savings',
              visible: true,
              favorite: true,
              additions: {customField: 'value1'},
            },
            product: {
              id: 'PROD_001',
              translations: {en: 'Savings Account', vi: 'Tài khoản tiết kiệm'},
              additions: {productInfo: 'info1'},
              externalId: 'EXT_001',
              externalTypeId: 'EXT_TYPE_001',
              typeName: 'Savings',
              productKind: {
                externalKindId: 'KIND_001',
                kindName: 'Deposit',
                kindUri: '/deposit',
                expectsChildren: false,
                additions: {kindInfo: 'info1'},
              },
            },
            state: 'ACTIVE',
            parentId: null,
            subscriptions: ['SUB_001'],
            isDefault: true,
            cifNo: 'CIF_001',
            virtualAccountInfos: [],
            additions: {accountInfo: 'info1'},
            name: 'John Doe Savings',
            bookedBalance: 1000000,
            availableBalance: 950000,
            creditLimit: 0,
            currency: 'VND',
            externalTransferAllowed: true,
            urgentTransferAllowed: false,
            accountOpeningDate: '2023-01-01',
            accountHolderNames: ['John Doe'],
            bankAlias: 'MSB',
            BBAN: 'MSB001234567890',
            IBAN: '*******************',
            BIC: 'MSBVVNVX',
          },
          {
            id: 'ACCT_002',
            productKindName: 'Current Account',
            legalEntityIds: ['ENTITY_002'],
            productId: 'PROD_002',
            productTypeName: 'Current',
            externalProductId: 'EXT_PROD_002',
            externalArrangementId: 'EXT_ARR_002',
            userPreferences: {
              arrangementId: 'ARR_002',
              alias: 'My Current',
              visible: true,
              favorite: false,
              additions: {customField: 'value2'},
            },
            product: {
              id: 'PROD_002',
              translations: {en: 'Current Account', vi: 'Tài khoản vãng lai'},
              additions: {productInfo: 'info2'},
              externalId: 'EXT_002',
              externalTypeId: 'EXT_TYPE_002',
              typeName: 'Current',
              productKind: {
                externalKindId: 'KIND_002',
                kindName: 'Current',
                kindUri: '/current',
                expectsChildren: true,
                additions: {kindInfo: 'info2'},
              },
            },
            state: 'ACTIVE',
            parentId: null,
            subscriptions: ['SUB_002'],
            isDefault: false,
            cifNo: 'CIF_002',
            virtualAccountInfos: [],
            additions: {accountInfo: 'info2'},
            name: 'Jane Smith Current',
            bookedBalance: 2000000,
            availableBalance: 1800000,
            creditLimit: 500000,
            currency: 'VND',
            externalTransferAllowed: true,
            urgentTransferAllowed: true,
            accountOpeningDate: '2023-02-01',
            accountHolderNames: ['Jane Smith'],
            bankAlias: 'MSB',
            BBAN: 'MSB001234567891',
            IBAN: '*******************',
            BIC: 'MSBVVNVX',
          },
        ],
      };

      const result = mapSourceAccountListResponseToModel(mockResponse);

      expect(result).toEqual({
        totalCount: 2,
        data: expect.arrayContaining([
          expect.objectContaining({
            id: 'ACCT_001',
            name: 'John Doe Savings',
            bookedBalance: 1000000,
          }),
          expect.objectContaining({
            id: 'ACCT_002',
            name: 'Jane Smith Current',
            bookedBalance: 2000000,
          }),
        ]),
      });
      expect(result.data).toHaveLength(2);
    });

    it('should filter out accounts with visible=false in userPreferences', () => {
      const mockResponse: SourceAccountListResponse = {
        totalCount: 3,
        data: [
          {
            id: 'ACCT_001',
            productKindName: 'Savings Account',
            legalEntityIds: ['ENTITY_001'],
            productId: 'PROD_001',
            productTypeName: 'Savings',
            externalProductId: 'EXT_PROD_001',
            externalArrangementId: 'EXT_ARR_001',
            userPreferences: {
              arrangementId: 'ARR_001',
              alias: 'Visible Account',
              visible: true,
              favorite: true,
              additions: {},
            },
            product: {
              id: 'PROD_001',
              translations: {},
              additions: {},
              externalId: 'EXT_001',
              externalTypeId: 'EXT_TYPE_001',
              typeName: 'Savings',
              productKind: {
                externalKindId: 'KIND_001',
                kindName: 'Deposit',
                kindUri: '/deposit',
                expectsChildren: false,
                additions: {},
              },
            },
            state: 'ACTIVE',
            parentId: null,
            subscriptions: [],
            isDefault: true,
            cifNo: 'CIF_001',
            virtualAccountInfos: [],
            additions: {},
            name: 'Visible Account',
            bookedBalance: 1000000,
            availableBalance: 950000,
            creditLimit: 0,
            currency: 'VND',
            externalTransferAllowed: true,
            urgentTransferAllowed: false,
            accountOpeningDate: '2023-01-01',
            accountHolderNames: ['User 1'],
            bankAlias: 'MSB',
            BBAN: 'MSB001234567890',
            IBAN: '*******************',
            BIC: 'MSBVVNVX',
          },
          {
            id: 'ACCT_002',
            productKindName: 'Hidden Account',
            legalEntityIds: ['ENTITY_002'],
            productId: 'PROD_002',
            productTypeName: 'Savings',
            externalProductId: 'EXT_PROD_002',
            externalArrangementId: 'EXT_ARR_002',
            userPreferences: {
              arrangementId: 'ARR_002',
              alias: 'Hidden Account',
              visible: false, // This should be filtered out
              favorite: false,
              additions: {},
            },
            product: {
              id: 'PROD_002',
              translations: {},
              additions: {},
              externalId: 'EXT_002',
              externalTypeId: 'EXT_TYPE_002',
              typeName: 'Savings',
              productKind: {
                externalKindId: 'KIND_002',
                kindName: 'Deposit',
                kindUri: '/deposit',
                expectsChildren: false,
                additions: {},
              },
            },
            state: 'ACTIVE',
            parentId: null,
            subscriptions: [],
            isDefault: false,
            cifNo: 'CIF_002',
            virtualAccountInfos: [],
            additions: {},
            name: 'Hidden Account',
            bookedBalance: 500000,
            availableBalance: 450000,
            creditLimit: 0,
            currency: 'VND',
            externalTransferAllowed: true,
            urgentTransferAllowed: false,
            accountOpeningDate: '2023-01-01',
            accountHolderNames: ['User 2'],
            bankAlias: 'MSB',
            BBAN: 'MSB001234567891',
            IBAN: '*******************',
            BIC: 'MSBVVNVX',
          },
          {
            id: 'ACCT_003',
            productKindName: 'Another Visible Account',
            legalEntityIds: ['ENTITY_003'],
            productId: 'PROD_003',
            productTypeName: 'Current',
            externalProductId: 'EXT_PROD_003',
            externalArrangementId: 'EXT_ARR_003',
            userPreferences: {
              arrangementId: 'ARR_003',
              alias: 'Another Visible',
              visible: true,
              favorite: false,
              additions: {},
            },
            product: {
              id: 'PROD_003',
              translations: {},
              additions: {},
              externalId: 'EXT_003',
              externalTypeId: 'EXT_TYPE_003',
              typeName: 'Current',
              productKind: {
                externalKindId: 'KIND_003',
                kindName: 'Current',
                kindUri: '/current',
                expectsChildren: false,
                additions: {},
              },
            },
            state: 'ACTIVE',
            parentId: null,
            subscriptions: [],
            isDefault: false,
            cifNo: 'CIF_003',
            virtualAccountInfos: [],
            additions: {},
            name: 'Another Visible Account',
            bookedBalance: 750000,
            availableBalance: 700000,
            creditLimit: 100000,
            currency: 'VND',
            externalTransferAllowed: true,
            urgentTransferAllowed: true,
            accountOpeningDate: '2023-03-01',
            accountHolderNames: ['User 3'],
            bankAlias: 'MSB',
            BBAN: 'MSB001234567892',
            IBAN: '*******************',
            BIC: 'MSBVVNVX',
          },
        ],
      };

      const result = mapSourceAccountListResponseToModel(mockResponse);

      expect(result.totalCount).toBe(3); // Original totalCount preserved
      expect(result.data).toHaveLength(2); // Only visible accounts
      expect(result.data.map(account => account.id)).toEqual(['ACCT_001', 'ACCT_003']);
      expect(result.data.map(account => account.id)).not.toContain('ACCT_002');
    });

    it('should return empty result when totalCount is 0', () => {
      const mockResponse: SourceAccountListResponse = {
        totalCount: 0,
        data: [],
      };

      const result = mapSourceAccountListResponseToModel(mockResponse);

      expect(result).toEqual({
        totalCount: 0,
        data: [],
      });
    });

    it('should return empty result when totalCount is null', () => {
      const mockResponse: SourceAccountListResponse = {
        totalCount: null as any,
        data: [],
      };

      const result = mapSourceAccountListResponseToModel(mockResponse);

      expect(result).toEqual({
        totalCount: 0,
        data: [],
      });
    });

    it('should return empty result when data is not an array', () => {
      const mockResponse: SourceAccountListResponse = {
        totalCount: 5,
        data: null as any,
      };

      const result = mapSourceAccountListResponseToModel(mockResponse);

      expect(result).toEqual({
        totalCount: 0,
        data: [],
      });
    });

    it('should return empty result when data is undefined', () => {
      const mockResponse: SourceAccountListResponse = {
        totalCount: 5,
        data: undefined as any,
      };

      const result = mapSourceAccountListResponseToModel(mockResponse);

      expect(result).toEqual({
        totalCount: 0,
        data: [],
      });
    });

    it('should handle accounts with undefined userPreferences', () => {
      const mockResponse: SourceAccountListResponse = {
        totalCount: 1,
        data: [
          {
            id: 'ACCT_001',
            productKindName: 'Savings Account',
            legalEntityIds: ['ENTITY_001'],
            productId: 'PROD_001',
            productTypeName: 'Savings',
            externalProductId: 'EXT_PROD_001',
            externalArrangementId: 'EXT_ARR_001',
            userPreferences: undefined,
            product: {
              id: 'PROD_001',
              translations: {},
              additions: {},
              externalId: 'EXT_001',
              externalTypeId: 'EXT_TYPE_001',
              typeName: 'Savings',
              productKind: {
                externalKindId: 'KIND_001',
                kindName: 'Deposit',
                kindUri: '/deposit',
                expectsChildren: false,
                additions: {},
              },
            },
            state: 'ACTIVE',
            parentId: null,
            subscriptions: [],
            isDefault: true,
            cifNo: 'CIF_001',
            virtualAccountInfos: [],
            additions: {},
            name: 'Account without preferences',
            bookedBalance: 1000000,
            availableBalance: 950000,
            creditLimit: 0,
            currency: 'VND',
            externalTransferAllowed: true,
            urgentTransferAllowed: false,
            accountOpeningDate: '2023-01-01',
            accountHolderNames: ['User 1'],
            bankAlias: 'MSB',
            BBAN: 'MSB001234567890',
            IBAN: '*******************',
            BIC: 'MSBVVNVX',
          },
        ],
      };

      const result = mapSourceAccountListResponseToModel(mockResponse);

      expect(result.totalCount).toBe(1);
      expect(result.data).toHaveLength(1);
      expect(result.data[0].id).toBe('ACCT_001');
      expect(result.data[0].userPreferences.visible).toBeUndefined();
    });
  });

  describe('mapUserPreferencesResponseToModel', () => {
    it('should map all userPreferences properties correctly', () => {
      const mockResponse = {
        arrangementId: 'ARR_001',
        alias: 'My Account',
        visible: true,
        favorite: true,
        additions: {customField: 'value'},
      };

      const result = mapUserPreferencesResponseToModel(mockResponse);

      expect(result).toEqual({
        arrangementId: 'ARR_001',
        alias: 'My Account',
        visible: true,
        favorite: true,
        additions: {customField: 'value'},
      });
    });

    it('should handle undefined userPreferences response', () => {
      const result = mapUserPreferencesResponseToModel(undefined);

      expect(result).toEqual({
        arrangementId: undefined,
        alias: undefined,
        visible: undefined,
        favorite: undefined,
        additions: undefined,
      });
    });

    it('should handle null userPreferences response', () => {
      const result = mapUserPreferencesResponseToModel(null as any);

      expect(result).toEqual({
        arrangementId: undefined,
        alias: undefined,
        visible: undefined,
        favorite: undefined,
        additions: undefined,
      });
    });

    it('should handle partial userPreferences response', () => {
      const mockResponse = {
        arrangementId: 'ARR_001',
        visible: false,
      } as any;

      const result = mapUserPreferencesResponseToModel(mockResponse);

      expect(result).toEqual({
        arrangementId: 'ARR_001',
        alias: undefined,
        visible: false,
        favorite: undefined,
        additions: undefined,
      });
    });
  });

  describe('mapProductKindResponseToModel', () => {
    it('should map all productKind properties correctly', () => {
      const mockResponse = {
        externalKindId: 'KIND_001',
        kindName: 'Deposit Account',
        kindUri: '/deposit',
        expectsChildren: false,
        additions: {kindInfo: 'Additional info'},
      };

      const result = mapProductKindResponseToModel(mockResponse);

      expect(result).toEqual({
        externalKindId: 'KIND_001',
        kindName: 'Deposit Account',
        kindUri: '/deposit',
        expectsChildren: false,
        additions: {kindInfo: 'Additional info'},
      });
    });

    it('should handle productKind with expectsChildren=true', () => {
      const mockResponse = {
        externalKindId: 'KIND_002',
        kindName: 'Investment Account',
        kindUri: '/investment',
        expectsChildren: true,
        additions: {},
      };

      const result = mapProductKindResponseToModel(mockResponse);

      expect(result).toEqual({
        externalKindId: 'KIND_002',
        kindName: 'Investment Account',
        kindUri: '/investment',
        expectsChildren: true,
        additions: {},
      });
    });

    it('should handle productKind with null additions', () => {
      const mockResponse = {
        externalKindId: 'KIND_003',
        kindName: 'Loan Account',
        kindUri: '/loan',
        expectsChildren: false,
        additions: null as any,
      };

      const result = mapProductKindResponseToModel(mockResponse);

      expect(result).toEqual({
        externalKindId: 'KIND_003',
        kindName: 'Loan Account',
        kindUri: '/loan',
        expectsChildren: false,
        additions: null,
      });
    });
  });

  describe('mapProductResponseToModel', () => {
    it('should map all product properties correctly', () => {
      const mockResponse = {
        id: 'PROD_001',
        translations: {en: 'Savings Account', vi: 'Tài khoản tiết kiệm'},
        additions: {productInfo: 'Additional product info'},
        externalId: 'EXT_PROD_001',
        externalTypeId: 'EXT_TYPE_001',
        typeName: 'Savings',
        productKind: {
          externalKindId: 'KIND_001',
          kindName: 'Deposit',
          kindUri: '/deposit',
          expectsChildren: false,
          additions: {kindInfo: 'Kind info'},
        },
      };

      const result = mapProductResponseToModel(mockResponse);

      expect(result).toEqual({
        id: 'PROD_001',
        translations: {en: 'Savings Account', vi: 'Tài khoản tiết kiệm'},
        additions: {productInfo: 'Additional product info'},
        externalId: 'EXT_PROD_001',
        externalTypeId: 'EXT_TYPE_001',
        typeName: 'Savings',
        productKind: {
          externalKindId: 'KIND_001',
          kindName: 'Deposit',
          kindUri: '/deposit',
          expectsChildren: false,
          additions: {kindInfo: 'Kind info'},
        },
      });
    });

    it('should handle product with empty translations', () => {
      const mockResponse = {
        id: 'PROD_002',
        translations: {},
        additions: {},
        externalId: 'EXT_PROD_002',
        externalTypeId: 'EXT_TYPE_002',
        typeName: 'Current',
        productKind: {
          externalKindId: 'KIND_002',
          kindName: 'Current',
          kindUri: '/current',
          expectsChildren: true,
          additions: {},
        },
      };

      const result = mapProductResponseToModel(mockResponse);

      expect(result).toEqual({
        id: 'PROD_002',
        translations: {},
        additions: {},
        externalId: 'EXT_PROD_002',
        externalTypeId: 'EXT_TYPE_002',
        typeName: 'Current',
        productKind: {
          externalKindId: 'KIND_002',
          kindName: 'Current',
          kindUri: '/current',
          expectsChildren: true,
          additions: {},
        },
      });
    });

    it('should handle product with null additions', () => {
      const mockResponse = {
        id: 'PROD_003',
        translations: {en: 'Credit Card'},
        additions: null as any,
        externalId: 'EXT_PROD_003',
        externalTypeId: 'EXT_TYPE_003',
        typeName: 'Credit',
        productKind: {
          externalKindId: 'KIND_003',
          kindName: 'Credit',
          kindUri: '/credit',
          expectsChildren: false,
          additions: null as any,
        },
      };

      const result = mapProductResponseToModel(mockResponse);

      expect(result).toEqual({
        id: 'PROD_003',
        translations: {en: 'Credit Card'},
        additions: null,
        externalId: 'EXT_PROD_003',
        externalTypeId: 'EXT_TYPE_003',
        typeName: 'Credit',
        productKind: {
          externalKindId: 'KIND_003',
          kindName: 'Credit',
          kindUri: '/credit',
          expectsChildren: false,
          additions: null,
        },
      });
    });
  });

  describe('mapSourceAccountResponseToModel', () => {
    it('should map all source account properties correctly', () => {
      const mockResponse = {
        id: 'ACCT_001',
        productKindName: 'Savings Account',
        legalEntityIds: ['ENTITY_001', 'ENTITY_002'],
        productId: 'PROD_001',
        productTypeName: 'Savings',
        externalProductId: 'EXT_PROD_001',
        externalArrangementId: 'EXT_ARR_001',
        userPreferences: {
          arrangementId: 'ARR_001',
          alias: 'My Savings',
          visible: true,
          favorite: true,
          additions: {customField: 'value'},
        },
        product: {
          id: 'PROD_001',
          translations: {en: 'Savings Account', vi: 'Tài khoản tiết kiệm'},
          additions: {productInfo: 'info'},
          externalId: 'EXT_001',
          externalTypeId: 'EXT_TYPE_001',
          typeName: 'Savings',
          productKind: {
            externalKindId: 'KIND_001',
            kindName: 'Deposit',
            kindUri: '/deposit',
            expectsChildren: false,
            additions: {kindInfo: 'info'},
          },
        },
        state: 'ACTIVE',
        parentId: 'PARENT_001',
        subscriptions: ['SUB_001', 'SUB_002'],
        isDefault: true,
        cifNo: 'CIF_001',
        virtualAccountInfos: [{id: 'VA_001', name: 'Virtual Account 1'}],
        additions: {accountInfo: 'Additional account info'},
        name: 'John Doe Savings Account',
        bookedBalance: 1500000,
        availableBalance: 1400000,
        creditLimit: 0,
        currency: 'VND',
        externalTransferAllowed: true,
        urgentTransferAllowed: false,
        accountOpeningDate: '2023-01-15',
        accountHolderNames: ['John Doe', 'Jane Doe'],
        bankAlias: 'MSB',
        BBAN: 'MSB001234567890',
        IBAN: '*******************',
        BIC: 'MSBVVNVX',
      };

      const result = mapSourceAccountResponseToModel(mockResponse);

      expect(result).toEqual({
        id: 'ACCT_001',
        productKindName: 'Savings Account',
        legalEntityIds: ['ENTITY_001', 'ENTITY_002'],
        productId: 'PROD_001',
        productTypeName: 'Savings',
        externalProductId: 'EXT_PROD_001',
        externalArrangementId: 'EXT_ARR_001',
        userPreferences: {
          arrangementId: 'ARR_001',
          alias: 'My Savings',
          visible: true,
          favorite: true,
          additions: {customField: 'value'},
        },
        product: {
          id: 'PROD_001',
          translations: {en: 'Savings Account', vi: 'Tài khoản tiết kiệm'},
          additions: {productInfo: 'info'},
          externalId: 'EXT_001',
          externalTypeId: 'EXT_TYPE_001',
          typeName: 'Savings',
          productKind: {
            externalKindId: 'KIND_001',
            kindName: 'Deposit',
            kindUri: '/deposit',
            expectsChildren: false,
            additions: {kindInfo: 'info'},
          },
        },
        state: 'ACTIVE',
        parentId: 'PARENT_001',
        subscriptions: ['SUB_001', 'SUB_002'],
        isDefault: true,
        cifNo: 'CIF_001',
        virtualAccountInfos: [{id: 'VA_001', name: 'Virtual Account 1'}],
        additions: {accountInfo: 'Additional account info'},
        name: 'John Doe Savings Account',
        bookedBalance: 1500000,
        availableBalance: 1400000,
        creditLimit: 0,
        currency: 'VND',
        externalTransferAllowed: true,
        urgentTransferAllowed: false,
        accountOpeningDate: '2023-01-15',
        accountHolderNames: ['John Doe', 'Jane Doe'],
        bankAlias: 'MSB',
        BBAN: 'MSB001234567890',
        IBAN: '*******************',
        BIC: 'MSBVVNVX',
      });
    });

    it('should handle source account with null optional fields', () => {
      const mockResponse = {
        id: 'ACCT_002',
        productKindName: 'Current Account',
        legalEntityIds: [],
        productId: 'PROD_002',
        productTypeName: 'Current',
        externalProductId: 'EXT_PROD_002',
        externalArrangementId: 'EXT_ARR_002',
        userPreferences: null as any,
        product: {
          id: 'PROD_002',
          translations: {},
          additions: null as any,
          externalId: 'EXT_002',
          externalTypeId: 'EXT_TYPE_002',
          typeName: 'Current',
          productKind: {
            externalKindId: 'KIND_002',
            kindName: 'Current',
            kindUri: '/current',
            expectsChildren: false,
            additions: null as any,
          },
        },
        state: 'INACTIVE',
        parentId: null,
        subscriptions: [],
        isDefault: false,
        cifNo: 'CIF_002',
        virtualAccountInfos: [],
        additions: null as any,
        name: 'Inactive Account',
        bookedBalance: 0,
        availableBalance: 0,
        creditLimit: null as any,
        currency: 'VND',
        externalTransferAllowed: false,
        urgentTransferAllowed: false,
        accountOpeningDate: null as any,
        accountHolderNames: [],
        bankAlias: null as any,
        BBAN: null as any,
        IBAN: null as any,
        BIC: null as any,
      };

      const result = mapSourceAccountResponseToModel(mockResponse);

      expect(result).toEqual({
        id: 'ACCT_002',
        productKindName: 'Current Account',
        legalEntityIds: [],
        productId: 'PROD_002',
        productTypeName: 'Current',
        externalProductId: 'EXT_PROD_002',
        externalArrangementId: 'EXT_ARR_002',
        userPreferences: {
          arrangementId: undefined,
          alias: undefined,
          visible: undefined,
          favorite: undefined,
          additions: undefined,
        },
        product: {
          id: 'PROD_002',
          translations: {},
          additions: null,
          externalId: 'EXT_002',
          externalTypeId: 'EXT_TYPE_002',
          typeName: 'Current',
          productKind: {
            externalKindId: 'KIND_002',
            kindName: 'Current',
            kindUri: '/current',
            expectsChildren: false,
            additions: null,
          },
        },
        state: 'INACTIVE',
        parentId: null,
        subscriptions: [],
        isDefault: false,
        cifNo: 'CIF_002',
        virtualAccountInfos: [],
        additions: null,
        name: 'Inactive Account',
        bookedBalance: 0,
        availableBalance: 0,
        creditLimit: null,
        currency: 'VND',
        externalTransferAllowed: false,
        urgentTransferAllowed: false,
        accountOpeningDate: null,
        accountHolderNames: [],
        bankAlias: null,
        BBAN: null,
        IBAN: null,
        BIC: null,
      });
    });
  });
});
