import {describe, it, expect, jest} from '@jest/globals';
import {mapMyBillListResponseToModel} from '../MyBillListMapper';
import {
  MyBillListResponse,
  MyBillContactResponse,
  AccountResponse,
} from '../../../models/my-bill-list/MyBillListResponse';
import {
  MyBillContactListModel,
  MyBillContactModel,
  AccountModel,
} from '../../../../domain/entities/my-bill-contact-list/MyBillContactListModel';

// Mock console.log to avoid test output pollution
const consoleSpy = jest.spyOn(console, 'log').mockImplementation(() => {});

describe('MyBillListMapper', () => {
  afterEach(() => {
    consoleSpy.mockClear();
  });

  afterAll(() => {
    consoleSpy.mockRestore();
  });

  describe('mapMyBillListResponseToModel', () => {
    it('should create MyBillContactListModel from response array', () => {
      const mockResponse: MyBillListResponse = [
        {
          id: 'BILL_001',
          name: 'E<PERSON> Ho Chi Minh City',
          alias: 'Điện lực TPHCM',
          category: 'ELECTRIC',
          activeStatus: 'ACTIVE',
          accessContextScope: 'PRIVATE',
          accounts: [
            {
              bankName: 'Maritime Bank',
              accountNumber: 'MSB00*********0',
              bankCode: 'MSB',
              accountType: 'SAVINGS',
              externalId: 'EXT_001',
              bankPostCode: '700000',
            },
            {
              bankName: 'Maritime Bank',
              accountNumber: 'MSB00*********1',
              bankCode: 'MSB',
              accountType: 'CURRENT',
              externalId: 'EXT_002',
              bankPostCode: '700000',
            },
          ],
        },
        {
          id: 'BILL_002',
          name: 'SAWACO',
          alias: 'Công ty cấp nước',
          category: 'WATER',
          activeStatus: 'ACTIVE',
          accessContextScope: 'PUBLIC',
          accounts: [
            {
              bankName: 'Maritime Bank',
              accountNumber: 'MSB00*********2',
              bankCode: 'MSB',
              accountType: 'SAVINGS',
              externalId: 'EXT_003',
              bankPostCode: '700000',
            },
          ],
        },
      ];

      const result = mapMyBillListResponseToModel(mockResponse);

      expect(result).toHaveLength(2);
      expect(result[0]).toBeInstanceOf(MyBillContactModel);
      expect(result[1]).toBeInstanceOf(MyBillContactModel);

      // Check first bill
      expect(result[0].id).toBe('BILL_001');
      expect(result[0].name).toBe('EVN Ho Chi Minh City');
      expect(result[0].alias).toBe('Điện lực TPHCM');
      expect(result[0].category).toBe('ELECTRIC');
      expect(result[0].activeStatus).toBe('ACTIVE');
      expect(result[0].accessContextScope).toBe('PRIVATE');
      expect(result[0].accounts).toHaveLength(2);
      expect(result[0].accounts![0]).toBeInstanceOf(AccountModel);
      expect(result[0].accounts![0].bankName).toBe('Maritime Bank');
      expect(result[0].accounts![0].accountNumber).toBe('MSB00*********0');

      // Check second bill
      expect(result[1].id).toBe('BILL_002');
      expect(result[1].name).toBe('SAWACO');
      expect(result[1].alias).toBe('Công ty cấp nước');
      expect(result[1].category).toBe('WATER');
      expect(result[1].accounts).toHaveLength(1);
    });

    it('should handle empty response array', () => {
      const mockResponse: MyBillListResponse = [];

      const result = mapMyBillListResponseToModel(mockResponse);

      expect(result).toEqual([]);
      expect(result).toHaveLength(0);
    });

    it('should handle bills without accounts', () => {
      const mockResponse: MyBillListResponse = [
        {
          id: 'BILL_NO_ACCOUNTS',
          name: 'Bill Without Accounts',
          alias: 'No Accounts',
          category: 'OTHER',
          activeStatus: 'ACTIVE',
          accessContextScope: 'PRIVATE',
          accounts: undefined,
        },
      ];

      const result = mapMyBillListResponseToModel(mockResponse);

      expect(result).toHaveLength(1);
      expect(result[0].accounts).toEqual([]);
      expect(result[0].id).toBe('BILL_NO_ACCOUNTS');
      expect(result[0].name).toBe('Bill Without Accounts');
    });

    it('should handle bills with empty accounts array', () => {
      const mockResponse: MyBillListResponse = [
        {
          id: 'BILL_EMPTY_ACCOUNTS',
          name: 'Bill With Empty Accounts',
          alias: 'Empty Accounts',
          category: 'OTHER',
          activeStatus: 'ACTIVE',
          accessContextScope: 'PRIVATE',
          accounts: [],
        },
      ];

      const result = mapMyBillListResponseToModel(mockResponse);

      expect(result).toHaveLength(1);
      expect(result[0].accounts).toEqual([]);
      expect(result[0].accounts).toHaveLength(0);
    });

    it('should log response and contact data to console', () => {
      const mockResponse: MyBillListResponse = [
        {
          id: 'BILL_LOG_1',
          name: 'Log Test 1',
          alias: 'Log 1',
          category: 'TEST',
          activeStatus: 'ACTIVE',
          accessContextScope: 'PRIVATE',
          accounts: [],
        },
        {
          id: 'BILL_LOG_2',
          name: 'Log Test 2',
          alias: 'Log 2',
          category: 'TEST',
          activeStatus: 'ACTIVE',
          accessContextScope: 'PRIVATE',
          accounts: [],
        },
      ];

      const result = mapMyBillListResponseToModel(mockResponse);

      // Should log response once at the beginning
      expect(consoleSpy).toHaveBeenCalledWith('-------------LOG RESPONSE', mockResponse);

      // Should log contact data and response for each bill (2 logs per bill)
      expect(consoleSpy).toHaveBeenCalledWith('LOG CONTACT', result[0], mockResponse[0]);
      expect(consoleSpy).toHaveBeenCalledWith('LOG RESPONSE', mockResponse[0]);
      expect(consoleSpy).toHaveBeenCalledWith('LOG CONTACT', result[1], mockResponse[1]);
      expect(consoleSpy).toHaveBeenCalledWith('LOG RESPONSE', mockResponse[1]);

      // Total: 1 initial log + 2 logs per bill * 2 bills = 5 logs
      expect(consoleSpy).toHaveBeenCalledTimes(5);
    });

    it('should handle Vietnamese characters correctly', () => {
      const mockResponse: MyBillListResponse = [
        {
          id: 'BILL_VN_001',
          name: 'Công ty Điện lực TP.HCM',
          alias: 'Điện lực TPHCM',
          category: 'ĐIỆN_LỰC',
          activeStatus: 'ACTIVE',
          accessContextScope: 'PRIVATE',
          accounts: [
            {
              bankName: 'Ngân hàng Hàng Hải Việt Nam',
              accountNumber: 'MSB00*********0',
              bankCode: 'MSB',
              accountType: 'TÀI_KHOẢN_TIẾT_KIỆM',
              externalId: 'EXT_VN_001',
              bankPostCode: '700000',
            },
          ],
        },
      ];

      const result = mapMyBillListResponseToModel(mockResponse);

      expect(result[0].name).toBe('Công ty Điện lực TP.HCM');
      expect(result[0].alias).toBe('Điện lực TPHCM');
      expect(result[0].category).toBe('ĐIỆN_LỰC');
      expect(result[0].accounts![0].bankName).toBe('Ngân hàng Hàng Hải Việt Nam');
      expect(result[0].accounts![0].accountType).toBe('TÀI_KHOẢN_TIẾT_KIỆM');
    });

    it('should handle null and undefined values', () => {
      const mockResponse: MyBillListResponse = [
        {
          id: null as any,
          name: undefined as any,
          alias: null as any,
          category: undefined as any,
          activeStatus: null as any,
          accessContextScope: undefined as any,
          accounts: [
            {
              bankName: null as any,
              accountNumber: undefined as any,
              bankCode: null as any,
              accountType: undefined as any,
              externalId: null as any,
              bankPostCode: undefined as any,
            },
          ],
        },
      ];

      const result = mapMyBillListResponseToModel(mockResponse);

      expect(result).toHaveLength(1);
      expect(result[0].id).toBeNull();
      expect(result[0].name).toBeUndefined();
      expect(result[0].alias).toBeNull();
      expect(result[0].category).toBeUndefined();
      expect(result[0].activeStatus).toBeNull();
      expect(result[0].accessContextScope).toBeUndefined();
      expect(result[0].accounts![0].bankName).toBeNull();
      expect(result[0].accounts![0].accountNumber).toBeUndefined();
    });

    it('should handle different data types correctly', () => {
      const mockResponse: MyBillListResponse = [
        {
          id: 123 as any, // Number instead of string
          name: true as any, // Boolean instead of string
          alias: ['alias'] as any, // Array instead of string
          category: {category: 'TEST'} as any, // Object instead of string
          activeStatus: 'ACTIVE',
          accessContextScope: 'PRIVATE',
          accounts: [
            {
              bankName: 'Valid Bank',
              accountNumber: ********* as any, // Number instead of string
              bankCode: true as any, // Boolean instead of string
              accountType: ['SAVINGS'] as any, // Array instead of string
              externalId: {id: 'EXT_001'} as any, // Object instead of string
              bankPostCode: 700000 as any, // Number instead of string
            },
          ],
        },
      ];

      const result = mapMyBillListResponseToModel(mockResponse);

      expect(result[0].id).toBe(123);
      expect(result[0].name).toBe(true);
      expect(result[0].alias).toEqual(['alias']);
      expect(result[0].category).toEqual({category: 'TEST'});
      expect(result[0].accounts![0].accountNumber).toBe(*********);
      expect(result[0].accounts![0].bankCode).toBe(true);
      expect(result[0].accounts![0].accountType).toEqual(['SAVINGS']);
      expect(result[0].accounts![0].externalId).toEqual({id: 'EXT_001'});
      expect(result[0].accounts![0].bankPostCode).toBe(700000);
    });

    it('should handle large arrays efficiently', () => {
      const largeAccountList = Array.from({length: 100}, (_, index) => ({
        bankName: `Bank ${index}`,
        accountNumber: `ACC_${index}`,
        bankCode: `CODE_${index}`,
        accountType: index % 2 === 0 ? 'SAVINGS' : 'CURRENT',
        externalId: `EXT_${index}`,
        bankPostCode: `${700000 + index}`,
      })) as AccountResponse[];

      const largeBillList = Array.from({length: 100}, (_, index) => ({
        id: `BILL_${index}`,
        name: `Bill ${index}`,
        alias: `Alias ${index}`,
        category: `CATEGORY_${index % 5}`,
        activeStatus: index % 2 === 0 ? 'ACTIVE' : 'INACTIVE',
        accessContextScope: index % 2 === 0 ? 'PRIVATE' : 'PUBLIC',
        accounts: largeAccountList,
      })) as MyBillListResponse;

      const startTime = performance.now();
      const result = mapMyBillListResponseToModel(largeBillList);
      const endTime = performance.now();

      expect(result).toHaveLength(100);
      expect(result[0].accounts).toHaveLength(100);
      expect(result[99].accounts).toHaveLength(100);
      expect(endTime - startTime).toBeLessThan(100); // Should complete in less than 100ms
    });

    it('should handle single item response array', () => {
      const mockResponse: MyBillListResponse = [
        {
          id: 'BILL_SINGLE',
          name: 'Single Bill',
          alias: 'Single',
          category: 'INTERNET',
          activeStatus: 'ACTIVE',
          accessContextScope: 'PRIVATE',
          accounts: [
            {
              bankName: 'Single Bank',
              accountNumber: 'SINGLE123',
              bankCode: 'SINGLE',
              accountType: 'SAVINGS',
              externalId: 'EXT_SINGLE',
              bankPostCode: '000000',
            },
          ],
        },
      ];

      const result = mapMyBillListResponseToModel(mockResponse);

      expect(result).toHaveLength(1);
      expect(result[0]).toBeInstanceOf(MyBillContactModel);
      expect(result[0].id).toBe('BILL_SINGLE');
      expect(result[0].name).toBe('Single Bill');
      expect(result[0].category).toBe('INTERNET');
      expect(result[0].accounts).toHaveLength(1);
    });
  });

  describe('Performance and Memory Management', () => {
    it('should handle repeated mappings efficiently', () => {
      const mockResponse: MyBillListResponse = [
        {
          id: 'PERF_TEST_001',
          name: 'Performance Test',
          alias: 'Perf Test',
          category: 'PERFORMANCE',
          activeStatus: 'ACTIVE',
          accessContextScope: 'PRIVATE',
          accounts: [
            {
              bankName: 'Test Bank',
              accountNumber: 'TEST123',
              bankCode: 'TEST',
              accountType: 'SAVINGS',
              externalId: 'EXT_TEST',
              bankPostCode: '000000',
            },
          ],
        },
      ];

      const iterations = 1000;
      const startTime = performance.now();

      for (let i = 0; i < iterations; i++) {
        mapMyBillListResponseToModel(mockResponse);
      }

      const endTime = performance.now();
      expect(endTime - startTime).toBeLessThan(100); // Should complete 1000 mappings in less than 100ms
    });

    it('should not cause memory leaks with repeated mappings', () => {
      const mockResponse: MyBillListResponse = [
        {
          id: 'MEMORY_TEST_001',
          name: 'Memory Test',
          alias: 'Memory Test',
          category: 'MEMORY',
          activeStatus: 'ACTIVE',
          accessContextScope: 'PRIVATE',
          accounts: [
            {
              bankName: 'Memory Bank',
              accountNumber: 'MEMORY123',
              bankCode: 'MEMORY',
              accountType: 'SAVINGS',
              externalId: 'EXT_MEMORY',
              bankPostCode: '000000',
            },
          ],
        },
      ];

      // Perform many mappings to test for memory leaks
      for (let i = 0; i < 100; i++) {
        const result = mapMyBillListResponseToModel(mockResponse);
        expect(result).toHaveLength(1);
        expect(result[0]).toBeInstanceOf(MyBillContactModel);
      }

      // If we reach here without running out of memory, the test passes
      expect(true).toBe(true);
    });
  });

  describe('Type Safety and Edge Cases', () => {
    it('should maintain type safety with complex nested structures', () => {
      const mockResponse: MyBillListResponse = [
        {
          id: 'TYPE_SAFETY_001',
          name: 'Type Safety Test',
          alias: 'Type Test',
          category: 'TYPE_TEST',
          activeStatus: 'ACTIVE',
          accessContextScope: 'PRIVATE',
          accounts: [
            {
              bankName: 'Type Bank',
              accountNumber: 'TYPE123',
              bankCode: 'TYPE',
              accountType: 'SAVINGS',
              externalId: 'EXT_TYPE',
              bankPostCode: '000000',
            },
          ],
        },
      ];

      const result = mapMyBillListResponseToModel(mockResponse);

      // Verify all properties exist and have correct structure
      expect(result[0]).toBeInstanceOf(MyBillContactModel);
      expect(result[0].accounts![0]).toBeInstanceOf(AccountModel);
      expect(typeof result[0].id).toBe('string');
      expect(typeof result[0].name).toBe('string');
      expect(typeof result[0].alias).toBe('string');
      expect(typeof result[0].category).toBe('string');
      expect(typeof result[0].activeStatus).toBe('string');
      expect(typeof result[0].accessContextScope).toBe('string');
      expect(Array.isArray(result[0].accounts)).toBe(true);
    });

    it('should handle circular references gracefully', () => {
      const circularObject: any = {
        id: 'CIRCULAR_001',
        name: 'Circular Test',
        alias: 'Circular',
        category: 'CIRCULAR',
        activeStatus: 'ACTIVE',
        accessContextScope: 'PRIVATE',
        accounts: [
          {
            bankName: 'Circular Bank',
            accountNumber: 'CIRCULAR123',
            bankCode: 'CIRCULAR',
            accountType: 'SAVINGS',
            externalId: 'EXT_CIRCULAR',
            bankPostCode: '000000',
          },
        ],
      };

      // Create circular reference
      circularObject.self = circularObject;
      circularObject.accounts[0].parent = circularObject;

      const result = mapMyBillListResponseToModel([circularObject]);

      expect(result).toHaveLength(1);
      expect(result[0]).toBeInstanceOf(MyBillContactModel);
      expect(result[0].id).toBe('CIRCULAR_001');
    });

    it('should handle special characters and symbols', () => {
      const mockResponse: MyBillListResponse = [
        {
          id: 'BILL_@#$%_001',
          name: 'Bill Name with Symbols !@#$%',
          alias: 'Alias_<>?:"{}|_+',
          category: 'CATEGORY_~`!@#$%^&*()_-+={}[]|\\:";\'<>?,./',
          activeStatus: 'ACTIVE',
          accessContextScope: 'PRIVATE',
          accounts: [
            {
              bankName: 'Bank !@#$%^&*()',
              accountNumber: 'ACC_!@#$%^&*()_001',
              bankCode: 'CODE_!@#',
              accountType: 'SAVINGS_!@#',
              externalId: 'EXT_!@#$%^&*()_001',
              bankPostCode: '700000_!@#',
            },
          ],
        },
      ];

      const result = mapMyBillListResponseToModel(mockResponse);

      expect(result[0].id).toBe('BILL_@#$%_001');
      expect(result[0].name).toBe('Bill Name with Symbols !@#$%');
      expect(result[0].alias).toBe('Alias_<>?:"{}|_+');
      expect(result[0].category).toBe('CATEGORY_~`!@#$%^&*()_-+={}[]|\\:";\'<>?,./');
      expect(result[0].accounts![0].bankName).toBe('Bank !@#$%^&*()');
      expect(result[0].accounts![0].accountNumber).toBe('ACC_!@#$%^&*()_001');
    });
  });
});
