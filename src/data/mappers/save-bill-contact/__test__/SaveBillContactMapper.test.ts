import {describe, it, expect} from '@jest/globals';
import {mapSaveBillContactResponseToModel} from '../SaveBillContactMapper';
import {SaveBillContactResponse} from '../../../models/save-bill-contact/SaveBillContactResponse';
import {SaveBillContactModel} from '../../../../domain/entities/save-bill-contact/SaveBillContactModel';

describe('SaveBillContactMapper', () => {
  describe('mapSaveBillContactResponseToModel', () => {
    it('should create SaveBillContactModel instance from SaveBillContactResponse', () => {
      const mockResponse: SaveBillContactResponse = {};

      const result = mapSaveBillContactResponseToModel(mockResponse);

      expect(result).toBeInstanceOf(SaveBillContactModel);
    });

    it('should handle empty SaveBillContactResponse', () => {
      const emptyResponse: SaveBillContactResponse = {};

      const result = mapSaveBillContactResponseToModel(emptyResponse);

      expect(result).toBeInstanceOf(SaveBillContactModel);
      expect(result).toBeDefined();
    });

    it('should handle null SaveBillContactResponse', () => {
      const nullResponse = null as any;

      const result = mapSaveBillContactResponseToModel(nullResponse);

      expect(result).toBeInstanceOf(SaveBillContactModel);
    });

    it('should handle undefined SaveBillContactResponse', () => {
      const undefinedResponse = undefined as any;

      const result = mapSaveBillContactResponseToModel(undefinedResponse);

      expect(result).toBeInstanceOf(SaveBillContactModel);
    });

    it('should handle response with additional properties', () => {
      const mockResponse = {
        extraProperty: 'value',
        anotherProperty: 123,
      } as any;

      const result = mapSaveBillContactResponseToModel(mockResponse);

      expect(result).toBeInstanceOf(SaveBillContactModel);
    });

    it('should handle response with nested objects', () => {
      const mockResponse = {
        nested: {
          property: 'value',
        },
      } as any;

      const result = mapSaveBillContactResponseToModel(mockResponse);

      expect(result).toBeInstanceOf(SaveBillContactModel);
    });

    it('should handle response with arrays', () => {
      const mockResponse = {
        arrayProperty: [1, 2, 3],
      } as any;

      const result = mapSaveBillContactResponseToModel(mockResponse);

      expect(result).toBeInstanceOf(SaveBillContactModel);
    });

    it('should handle response with boolean values', () => {
      const mockResponse = {
        booleanProperty: true,
        anotherBoolean: false,
      } as any;

      const result = mapSaveBillContactResponseToModel(mockResponse);

      expect(result).toBeInstanceOf(SaveBillContactModel);
    });

    it('should handle response with numeric values', () => {
      const mockResponse = {
        numericProperty: 42,
        floatProperty: 3.14,
        zeroProperty: 0,
        negativeProperty: -1,
      } as any;

      const result = mapSaveBillContactResponseToModel(mockResponse);

      expect(result).toBeInstanceOf(SaveBillContactModel);
    });

    it('should handle response with string values', () => {
      const mockResponse = {
        stringProperty: 'test',
        emptyString: '',
        unicodeString: '🎉',
      } as any;

      const result = mapSaveBillContactResponseToModel(mockResponse);

      expect(result).toBeInstanceOf(SaveBillContactModel);
    });

    it('should handle response with null properties', () => {
      const mockResponse = {
        nullProperty: null,
        undefinedProperty: undefined,
      } as any;

      const result = mapSaveBillContactResponseToModel(mockResponse);

      expect(result).toBeInstanceOf(SaveBillContactModel);
    });

    it('should handle response with date objects', () => {
      const mockResponse = {
        dateProperty: new Date(),
        dateString: '2023-01-01',
      } as any;

      const result = mapSaveBillContactResponseToModel(mockResponse);

      expect(result).toBeInstanceOf(SaveBillContactModel);
    });

    it('should handle response with function properties (edge case)', () => {
      const mockResponse = {
        functionProperty: () => 'test',
      } as any;

      const result = mapSaveBillContactResponseToModel(mockResponse);

      expect(result).toBeInstanceOf(SaveBillContactModel);
    });

    it('should handle response with Symbol properties (edge case)', () => {
      const symbolKey = Symbol('test');
      const mockResponse = {
        normalProperty: 'value',
        [symbolKey]: 'symbol value',
      } as any;

      const result = mapSaveBillContactResponseToModel(mockResponse);

      expect(result).toBeInstanceOf(SaveBillContactModel);
    });
  });

  describe('Performance considerations', () => {
    it('should handle mapping quickly for normal responses', () => {
      const mockResponse: SaveBillContactResponse = {};
      
      const startTime = performance.now();
      const result = mapSaveBillContactResponseToModel(mockResponse);
      const endTime = performance.now();
      
      expect(result).toBeInstanceOf(SaveBillContactModel);
      expect(endTime - startTime).toBeLessThan(10); // Should complete in less than 10ms
    });

    it('should handle multiple rapid mappings efficiently', () => {
      const mockResponse: SaveBillContactResponse = {};
      const iterations = 1000;
      
      const startTime = performance.now();
      for (let i = 0; i < iterations; i++) {
        mapSaveBillContactResponseToModel(mockResponse);
      }
      const endTime = performance.now();
      
      expect(endTime - startTime).toBeLessThan(100); // Should complete 1000 mappings in less than 100ms
    });
  });

  describe('Memory management', () => {
    it('should not cause memory leaks with repeated mappings', () => {
      const mockResponse: SaveBillContactResponse = {};
      
      // Perform many mappings to test for memory leaks
      for (let i = 0; i < 100; i++) {
        const result = mapSaveBillContactResponseToModel(mockResponse);
        expect(result).toBeInstanceOf(SaveBillContactModel);
      }
      
      // If we reach here without running out of memory, the test passes
      expect(true).toBe(true);
    });
  });

  describe('Type safety', () => {
    it('should maintain type safety with proper response structure', () => {
      const mockResponse: SaveBillContactResponse = {};

      const result = mapSaveBillContactResponseToModel(mockResponse);

      expect(typeof result).toBe('object');
      expect(result).toBeInstanceOf(SaveBillContactModel);
    });

    it('should handle response with mixed data types', () => {
      const mockResponse = {
        string: 'test',
        number: 123,
        boolean: true,
        array: [1, 2, 3],
        object: {nested: 'value'},
        nullValue: null,
        undefinedValue: undefined,
      } as any;

      const result = mapSaveBillContactResponseToModel(mockResponse);

      expect(result).toBeInstanceOf(SaveBillContactModel);
    });
  });

  describe('Edge cases', () => {
    it('should handle circular reference in response (edge case)', () => {
      const mockResponse: any = {
        property: 'value',
      };
      mockResponse.circular = mockResponse; // Create circular reference

      const result = mapSaveBillContactResponseToModel(mockResponse);

      expect(result).toBeInstanceOf(SaveBillContactModel);
    });

    it('should handle very large response objects', () => {
      const largeResponse: any = {};
      for (let i = 0; i < 1000; i++) {
        largeResponse[`property${i}`] = `value${i}`;
      }

      const result = mapSaveBillContactResponseToModel(largeResponse);

      expect(result).toBeInstanceOf(SaveBillContactModel);
    });

    it('should handle response with special characters', () => {
      const mockResponse = {
        specialChars: '!@#$%^&*()_+-=[]{}|;:,.<>?',
        unicode: '🎉🚀💯',
        vietnamese: 'Tiếng Việt có dấu',
      } as any;

      const result = mapSaveBillContactResponseToModel(mockResponse);

      expect(result).toBeInstanceOf(SaveBillContactModel);
    });
  });

  describe('Integration scenarios', () => {
    it('should work correctly in a typical save bill contact flow', () => {
      const mockResponse: SaveBillContactResponse = {};

      const result = mapSaveBillContactResponseToModel(mockResponse);

      expect(result).toBeInstanceOf(SaveBillContactModel);
      expect(result).toBeDefined();
    });

    it('should maintain consistency across multiple mappings', () => {
      const mockResponse: SaveBillContactResponse = {};

      const result1 = mapSaveBillContactResponseToModel(mockResponse);
      const result2 = mapSaveBillContactResponseToModel(mockResponse);

      expect(result1).toBeInstanceOf(SaveBillContactModel);
      expect(result2).toBeInstanceOf(SaveBillContactModel);
      expect(typeof result1).toBe(typeof result2);
    });
  });

  describe('Real-world scenarios', () => {
    it('should handle typical save bill contact response structure', () => {
      const mockResponse = {
        success: true,
        message: 'Bill contact saved successfully',
        contactId: '12345',
        timestamp: new Date().toISOString(),
      } as any;

      const result = mapSaveBillContactResponseToModel(mockResponse);

      expect(result).toBeInstanceOf(SaveBillContactModel);
    });

    it('should handle error response structure', () => {
      const mockResponse = {
        success: false,
        error: 'Failed to save bill contact',
        errorCode: 'SAVE_FAILED',
      } as any;

      const result = mapSaveBillContactResponseToModel(mockResponse);

      expect(result).toBeInstanceOf(SaveBillContactModel);
    });

    it('should handle response with contact details', () => {
      const mockResponse = {
        contactId: '12345',
        contactName: 'John Doe',
        phoneNumber: '+84123456789',
        billCode: 'BILL001',
        createdAt: new Date().toISOString(),
      } as any;

      const result = mapSaveBillContactResponseToModel(mockResponse);

      expect(result).toBeInstanceOf(SaveBillContactModel);
    });

    it('should handle response with validation results', () => {
      const mockResponse = {
        isValid: true,
        validationErrors: [],
        contactData: {
          name: 'Test Contact',
          phone: '0123456789',
        },
      } as any;

      const result = mapSaveBillContactResponseToModel(mockResponse);

      expect(result).toBeInstanceOf(SaveBillContactModel);
    });
  });
});
