import {describe, it, expect} from '@jest/globals';
import {mapBillValidateResponseToModel} from '../BillValidateMapper';
import {BillValidateResponse} from '../../../models/bill-validate/BillValidateResponse';
import {BillValidateModel} from '../../../../domain/entities/bill-validate/BillValidateModel';

describe('BillValidateMapper', () => {
  describe('mapBillValidateResponseToModel', () => {
    it('should map BillValidateResponse to BillValidateModel using spread operator', () => {
      const mockResponse: BillValidateResponse = {
        isValid: true,
        billCode: 'BILL001',
        amount: 100000,
        customerName: 'John Doe',
      };

      const result = mapBillValidateResponseToModel(mockResponse);

      expect(result).toEqual(mockResponse);
      expect(result.isValid).toBe(true);
      expect(result.billCode).toBe('BILL001');
      expect(result.amount).toBe(100000);
      expect(result.customerName).toBe('<PERSON>');
    });

    it('should handle empty BillValidateResponse', () => {
      const emptyResponse: BillValidateResponse = {};

      const result = mapBillValidateResponseToModel(emptyResponse);

      expect(result).toEqual({});
      expect(typeof result).toBe('object');
    });

    it('should handle null BillValidateResponse', () => {
      const nullResponse = null as any;

      const result = mapBillValidateResponseToModel(nullResponse);

      expect(result).toEqual({});
    });

    it('should handle undefined BillValidateResponse', () => {
      const undefinedResponse = undefined as any;

      const result = mapBillValidateResponseToModel(undefinedResponse);

      expect(result).toEqual({});
    });

    it('should preserve all properties from response', () => {
      const mockResponse: BillValidateResponse = {
        isValid: false,
        billCode: 'BILL002',
        amount: 250000,
        customerName: 'Jane Smith',
        errorMessage: 'Invalid bill code',
        validationDate: '2023-01-01',
      };

      const result = mapBillValidateResponseToModel(mockResponse);

      expect(result).toEqual(mockResponse);
      expect(result.isValid).toBe(false);
      expect(result.billCode).toBe('BILL002');
      expect(result.amount).toBe(250000);
      expect(result.customerName).toBe('Jane Smith');
      expect(result.errorMessage).toBe('Invalid bill code');
      expect(result.validationDate).toBe('2023-01-01');
    });

    it('should handle response with nested objects', () => {
      const mockResponse = {
        isValid: true,
        billDetails: {
          code: 'BILL003',
          type: 'ELECTRICITY',
          provider: 'EVN',
        },
        customer: {
          name: 'Test Customer',
          address: '123 Test St',
        },
      } as any;

      const result = mapBillValidateResponseToModel(mockResponse);

      expect(result).toEqual(mockResponse);
      expect(result.billDetails).toEqual(mockResponse.billDetails);
      expect(result.customer).toEqual(mockResponse.customer);
    });

    it('should handle response with arrays', () => {
      const mockResponse = {
        isValid: true,
        validationErrors: [],
        supportedPaymentMethods: ['BANK_TRANSFER', 'CREDIT_CARD'],
      } as any;

      const result = mapBillValidateResponseToModel(mockResponse);

      expect(result).toEqual(mockResponse);
      expect(Array.isArray(result.validationErrors)).toBe(true);
      expect(Array.isArray(result.supportedPaymentMethods)).toBe(true);
      expect(result.supportedPaymentMethods).toEqual(['BANK_TRANSFER', 'CREDIT_CARD']);
    });

    it('should handle response with boolean values', () => {
      const mockResponse = {
        isValid: true,
        isPaid: false,
        isOverdue: true,
        canPayPartial: false,
      } as any;

      const result = mapBillValidateResponseToModel(mockResponse);

      expect(result).toEqual(mockResponse);
      expect(result.isValid).toBe(true);
      expect(result.isPaid).toBe(false);
      expect(result.isOverdue).toBe(true);
      expect(result.canPayPartial).toBe(false);
    });

    it('should handle response with numeric values', () => {
      const mockResponse = {
        amount: 100000,
        fee: 5000,
        discount: 0,
        totalAmount: 105000,
        minAmount: 50000,
        maxAmount: 1000000,
      } as any;

      const result = mapBillValidateResponseToModel(mockResponse);

      expect(result).toEqual(mockResponse);
      expect(result.amount).toBe(100000);
      expect(result.fee).toBe(5000);
      expect(result.discount).toBe(0);
      expect(result.totalAmount).toBe(105000);
      expect(result.minAmount).toBe(50000);
      expect(result.maxAmount).toBe(1000000);
    });

    it('should handle response with string values', () => {
      const mockResponse = {
        billCode: 'BILL001',
        customerName: 'John Doe',
        providerName: 'EVN HCMC',
        currency: 'VND',
        status: 'PENDING',
        description: 'Electricity bill payment',
      } as any;

      const result = mapBillValidateResponseToModel(mockResponse);

      expect(result).toEqual(mockResponse);
      expect(result.billCode).toBe('BILL001');
      expect(result.customerName).toBe('John Doe');
      expect(result.providerName).toBe('EVN HCMC');
      expect(result.currency).toBe('VND');
      expect(result.status).toBe('PENDING');
      expect(result.description).toBe('Electricity bill payment');
    });

    it('should handle response with null and undefined properties', () => {
      const mockResponse = {
        billCode: 'BILL001',
        customerName: null,
        amount: undefined,
        description: '',
      } as any;

      const result = mapBillValidateResponseToModel(mockResponse);

      expect(result).toEqual(mockResponse);
      expect(result.billCode).toBe('BILL001');
      expect(result.customerName).toBeNull();
      expect(result.amount).toBeUndefined();
      expect(result.description).toBe('');
    });

    it('should handle response with date objects', () => {
      const testDate = new Date('2023-01-01');
      const mockResponse = {
        validationDate: testDate,
        dueDate: '2023-01-15',
        createdAt: testDate.toISOString(),
      } as any;

      const result = mapBillValidateResponseToModel(mockResponse);

      expect(result).toEqual(mockResponse);
      expect(result.validationDate).toBe(testDate);
      expect(result.dueDate).toBe('2023-01-15');
      expect(result.createdAt).toBe(testDate.toISOString());
    });

    it('should handle response with function properties (edge case)', () => {
      const mockFunction = () => 'test';
      const mockResponse = {
        billCode: 'BILL001',
        functionProperty: mockFunction,
      } as any;

      const result = mapBillValidateResponseToModel(mockResponse);

      expect(result).toEqual(mockResponse);
      expect(result.functionProperty).toBe(mockFunction);
    });

    it('should handle response with Symbol properties (edge case)', () => {
      const symbolKey = Symbol('test');
      const mockResponse = {
        billCode: 'BILL001',
        [symbolKey]: 'symbol value',
      } as any;

      const result = mapBillValidateResponseToModel(mockResponse);

      expect(result).toEqual(mockResponse);
      expect(result[symbolKey]).toBe('symbol value');
    });
  });

  describe('Performance considerations', () => {
    it('should handle mapping quickly for normal responses', () => {
      const mockResponse: BillValidateResponse = {
        isValid: true,
        billCode: 'BILL001',
        amount: 100000,
      };
      
      const startTime = performance.now();
      const result = mapBillValidateResponseToModel(mockResponse);
      const endTime = performance.now();
      
      expect(result).toEqual(mockResponse);
      expect(endTime - startTime).toBeLessThan(10); // Should complete in less than 10ms
    });

    it('should handle multiple rapid mappings efficiently', () => {
      const mockResponse: BillValidateResponse = {
        isValid: true,
        billCode: 'BILL001',
        amount: 100000,
      };
      const iterations = 1000;
      
      const startTime = performance.now();
      for (let i = 0; i < iterations; i++) {
        mapBillValidateResponseToModel(mockResponse);
      }
      const endTime = performance.now();
      
      expect(endTime - startTime).toBeLessThan(100); // Should complete 1000 mappings in less than 100ms
    });
  });

  describe('Memory management', () => {
    it('should not cause memory leaks with repeated mappings', () => {
      const mockResponse: BillValidateResponse = {
        isValid: true,
        billCode: 'BILL001',
        amount: 100000,
      };
      
      // Perform many mappings to test for memory leaks
      for (let i = 0; i < 100; i++) {
        const result = mapBillValidateResponseToModel(mockResponse);
        expect(result).toEqual(mockResponse);
      }
      
      // If we reach here without running out of memory, the test passes
      expect(true).toBe(true);
    });
  });

  describe('Type safety', () => {
    it('should maintain type safety with proper response structure', () => {
      const mockResponse: BillValidateResponse = {
        isValid: true,
        billCode: 'BILL001',
        amount: 100000,
      };

      const result = mapBillValidateResponseToModel(mockResponse);

      expect(typeof result).toBe('object');
      expect(result).toEqual(mockResponse);
    });

    it('should handle response with mixed data types', () => {
      const mockResponse = {
        string: 'test',
        number: 123,
        boolean: true,
        array: [1, 2, 3],
        object: {nested: 'value'},
        nullValue: null,
        undefinedValue: undefined,
      } as any;

      const result = mapBillValidateResponseToModel(mockResponse);

      expect(result).toEqual(mockResponse);
    });
  });

  describe('Edge cases', () => {
    it('should handle circular reference in response (edge case)', () => {
      const mockResponse: any = {
        billCode: 'BILL001',
        isValid: true,
      };
      mockResponse.circular = mockResponse; // Create circular reference

      const result = mapBillValidateResponseToModel(mockResponse);

      expect(result).toEqual(mockResponse);
      expect(result.circular).toBe(mockResponse);
    });

    it('should handle very large response objects', () => {
      const largeResponse: any = {
        isValid: true,
        billCode: 'BILL001',
      };
      for (let i = 0; i < 1000; i++) {
        largeResponse[`property${i}`] = `value${i}`;
      }

      const result = mapBillValidateResponseToModel(largeResponse);

      expect(result).toEqual(largeResponse);
      expect(result.isValid).toBe(true);
      expect(result.billCode).toBe('BILL001');
    });

    it('should handle response with special characters', () => {
      const mockResponse = {
        billCode: 'BILL001',
        customerName: 'Nguyễn Văn A',
        specialChars: '!@#$%^&*()_+-=[]{}|;:,.<>?',
        unicode: '🎉🚀💯',
        vietnamese: 'Tiếng Việt có dấu',
      } as any;

      const result = mapBillValidateResponseToModel(mockResponse);

      expect(result).toEqual(mockResponse);
      expect(result.customerName).toBe('Nguyễn Văn A');
      expect(result.vietnamese).toBe('Tiếng Việt có dấu');
    });
  });

  describe('Integration scenarios', () => {
    it('should work correctly in a typical bill validation flow', () => {
      const mockResponse: BillValidateResponse = {
        isValid: true,
        billCode: 'EVN001234567',
        amount: 150000,
        customerName: 'Nguyễn Văn A',
        dueDate: '2023-12-31',
        providerName: 'EVN HCMC',
      };

      const result = mapBillValidateResponseToModel(mockResponse);

      expect(result).toEqual(mockResponse);
      expect(result.isValid).toBe(true);
      expect(result.billCode).toBe('EVN001234567');
      expect(result.amount).toBe(150000);
    });

    it('should maintain consistency across multiple mappings', () => {
      const mockResponse: BillValidateResponse = {
        isValid: false,
        billCode: 'INVALID001',
        errorMessage: 'Bill not found',
      };

      const result1 = mapBillValidateResponseToModel(mockResponse);
      const result2 = mapBillValidateResponseToModel(mockResponse);

      expect(result1).toEqual(mockResponse);
      expect(result2).toEqual(mockResponse);
      expect(result1).toEqual(result2);
    });
  });

  describe('Real-world scenarios', () => {
    it('should handle successful bill validation response', () => {
      const mockResponse: BillValidateResponse = {
        isValid: true,
        billCode: 'EVN123456789',
        amount: 250000,
        customerName: 'Trần Thị B',
        customerAddress: '123 Nguyễn Huệ, Q1, TPHCM',
        dueDate: '2023-12-31',
        providerName: 'Công ty Điện lực TPHCM',
        billPeriod: '11/2023',
        canPayPartial: false,
      };

      const result = mapBillValidateResponseToModel(mockResponse);

      expect(result).toEqual(mockResponse);
      expect(result.isValid).toBe(true);
      expect(result.customerName).toBe('Trần Thị B');
      expect(result.providerName).toBe('Công ty Điện lực TPHCM');
    });

    it('should handle failed bill validation response', () => {
      const mockResponse: BillValidateResponse = {
        isValid: false,
        billCode: 'INVALID123',
        errorMessage: 'Mã hóa đơn không tồn tại',
        errorCode: 'BILL_NOT_FOUND',
      };

      const result = mapBillValidateResponseToModel(mockResponse);

      expect(result).toEqual(mockResponse);
      expect(result.isValid).toBe(false);
      expect(result.errorMessage).toBe('Mã hóa đơn không tồn tại');
      expect(result.errorCode).toBe('BILL_NOT_FOUND');
    });

    it('should handle bill validation with payment options', () => {
      const mockResponse: BillValidateResponse = {
        isValid: true,
        billCode: 'WATER001234',
        amount: 180000,
        customerName: 'Lê Văn C',
        minPaymentAmount: 50000,
        maxPaymentAmount: 180000,
        canPayPartial: true,
        supportedPaymentMethods: ['BANK_TRANSFER', 'CREDIT_CARD', 'E_WALLET'],
        fee: 5000,
        totalAmount: 185000,
      };

      const result = mapBillValidateResponseToModel(mockResponse);

      expect(result).toEqual(mockResponse);
      expect(result.canPayPartial).toBe(true);
      expect(result.supportedPaymentMethods).toEqual(['BANK_TRANSFER', 'CREDIT_CARD', 'E_WALLET']);
      expect(result.totalAmount).toBe(185000);
    });
  });
});
