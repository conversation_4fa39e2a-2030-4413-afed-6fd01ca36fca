import {describe, it, expect} from '@jest/globals';
import {mapProviderListResponseToModel} from '../ProviderListMapper';
import {ProviderListResponse, ProviderResponse} from '../../../models/provider-list/ProviderListResponse';
import {ProviderListModel, ProviderModel} from '../../../../domain/entities/provider-list/ProviderListModel';

describe('ProviderListMapper', () => {
  describe('mapProviderListResponseToModel', () => {
    it('should create ProviderListModel from response array', () => {
      const mockResponse: ProviderListResponse = [
        {
          subgroupId: 'ELECTRIC_001',
          serviceCode: 'EVN_HCMC',
          categoryCode: 'ELECTRIC',
          subgroupNameVn: 'Điện lực TP.HCM',
          subgroupNameEn: 'EVN Ho Chi Minh City',
          partnerCode: 'EVN_HCMC',
          partnerName: 'Công ty Điện lực TP.HCM',
          autoBillSupport: true,
          voucherSupport: false,
          phoneRequired: true,
          isRecommend: true,
          partnerType: 'UTILITY',
          payFee: 5000,
          type: 'ELECTRIC_BILL',
          paymentSupport: ['BANK_TRANSFER', 'CARD'],
          description: 'Thanh toán tiền điện TP.HCM',
        },
        {
          subgroupId: 'WATER_001',
          serviceCode: 'SAWACO',
          categoryCode: 'WATER',
          subgroupNameVn: 'Công ty cấp nước Sài Gòn',
          subgroupNameEn: 'Saigon Water Corporation',
          partnerCode: 'SAWACO',
          partnerName: 'SAWACO',
          autoBillSupport: false,
          voucherSupport: true,
          phoneRequired: false,
          isRecommend: false,
          partnerType: 'UTILITY',
          payFee: 3000,
          type: 'WATER_BILL',
          paymentSupport: ['BANK_TRANSFER'],
          description: 'Thanh toán tiền nước SAWACO',
        },
      ];

      const result = mapProviderListResponseToModel(mockResponse);

      expect(result).toHaveLength(2);
      expect(result[0]).toBeInstanceOf(ProviderModel);
      expect(result[1]).toBeInstanceOf(ProviderModel);

      // Check first provider - based on ProviderModel constructor order
      // constructor(id, serviceCode, categoryCode, subGroupId, subgroupNameVn, subgroupNameEn, partnerCode, partnerName, autoBillSupport, voucherSupport, phoneRequired, isRecommend, partnerType, payFee, type, paymentSupport, description)
      expect(result[0].id).toBe('ELECTRIC_001'); // First param: provider.subgroupId
      expect(result[0].serviceCode).toBe('EVN_HCMC');
      expect(result[0].categoryCode).toBe('ELECTRIC');
      expect(result[0].subGroupId).toBe('ELECTRIC_001'); // Fourth param: provider.subgroupId again
      expect(result[0].subgroupNameVn).toBe('Điện lực TP.HCM');
      expect(result[0].subgroupNameEn).toBe('EVN Ho Chi Minh City');
      expect(result[0].partnerCode).toBe('EVN_HCMC');
      expect(result[0].partnerName).toBe('Công ty Điện lực TP.HCM');
      expect(result[0].autoBillSupport).toBe(true);
      expect(result[0].voucherSupport).toBe(false);
      expect(result[0].phoneRequired).toBe(true);
      expect(result[0].isRecommend).toBe(true);
      expect(result[0].partnerType).toBe('UTILITY');
      expect(result[0].payFee).toBe(5000);
      expect(result[0].type).toBe('ELECTRIC_BILL');
      expect(result[0].paymentSupport).toEqual(['BANK_TRANSFER', 'CARD']);
      expect(result[0].description).toBe('Thanh toán tiền điện TP.HCM');

      // Check second provider
      expect(result[1].id).toBe('WATER_001');
      expect(result[1].serviceCode).toBe('SAWACO');
      expect(result[1].categoryCode).toBe('WATER');
      expect(result[1].subGroupId).toBe('WATER_001');
      expect(result[1].subgroupNameVn).toBe('Công ty cấp nước Sài Gòn');
      expect(result[1].subgroupNameEn).toBe('Saigon Water Corporation');
      expect(result[1].partnerCode).toBe('SAWACO');
      expect(result[1].partnerName).toBe('SAWACO');
      expect(result[1].autoBillSupport).toBe(false);
      expect(result[1].voucherSupport).toBe(true);
      expect(result[1].phoneRequired).toBe(false);
      expect(result[1].isRecommend).toBe(false);
      expect(result[1].partnerType).toBe('UTILITY');
      expect(result[1].payFee).toBe(3000);
      expect(result[1].type).toBe('WATER_BILL');
      expect(result[1].paymentSupport).toEqual(['BANK_TRANSFER']);
      expect(result[1].description).toBe('Thanh toán tiền nước SAWACO');
    });

    it('should handle empty response array', () => {
      const mockResponse: ProviderListResponse = [];

      const result = mapProviderListResponseToModel(mockResponse);

      expect(result).toEqual([]);
      expect(result).toHaveLength(0);
    });

    it('should handle single provider response', () => {
      const mockResponse: ProviderListResponse = [
        {
          subgroupId: 'INTERNET_001',
          serviceCode: 'VIETTEL_FIBER',
          categoryCode: 'INTERNET',
          subgroupNameVn: 'Viettel Internet',
          subgroupNameEn: 'Viettel Internet',
          partnerCode: 'VIETTEL',
          partnerName: 'Viettel Telecom',
          autoBillSupport: true,
          voucherSupport: true,
          phoneRequired: true,
          isRecommend: true,
          partnerType: 'TELECOM',
          payFee: 2000,
          type: 'INTERNET_BILL',
          paymentSupport: ['BANK_TRANSFER', 'CARD', 'E_WALLET'],
          description: 'Thanh toán cước internet Viettel',
        },
      ];

      const result = mapProviderListResponseToModel(mockResponse);

      expect(result).toHaveLength(1);
      expect(result[0]).toBeInstanceOf(ProviderModel);
      expect(result[0].id).toBe('INTERNET_001');
      expect(result[0].serviceCode).toBe('VIETTEL_FIBER');
      expect(result[0].categoryCode).toBe('INTERNET');
      expect(result[0].subGroupId).toBe('INTERNET_001');
      expect(result[0].partnerType).toBe('TELECOM');
      expect(result[0].paymentSupport).toEqual(['BANK_TRANSFER', 'CARD', 'E_WALLET']);
    });

    it('should handle Vietnamese characters correctly', () => {
      const mockResponse: ProviderListResponse = [
        {
          subgroupId: 'MOBILE_001',
          serviceCode: 'MOBIFONE_PREPAID',
          categoryCode: 'MOBILE',
          subgroupNameVn: 'MobiFone Trả trước',
          subgroupNameEn: 'MobiFone Prepaid',
          partnerCode: 'MOBIFONE',
          partnerName: 'Công ty Viễn thông MobiFone',
          autoBillSupport: false,
          voucherSupport: true,
          phoneRequired: true,
          isRecommend: false,
          partnerType: 'VIỄN_THÔNG',
          payFee: 1000,
          type: 'NẠP_TIỀN_ĐIỆN_THOẠI',
          paymentSupport: ['CHUYỂN_KHOẢN', 'THẺ_TÍN_DỤNG'],
          description: 'Nạp tiền điện thoại MobiFone trả trước',
        },
      ];

      const result = mapProviderListResponseToModel(mockResponse);

      expect(result[0].subgroupNameVn).toBe('MobiFone Trả trước');
      expect(result[0].partnerName).toBe('Công ty Viễn thông MobiFone');
      expect(result[0].partnerType).toBe('VIỄN_THÔNG');
      expect(result[0].type).toBe('NẠP_TIỀN_ĐIỆN_THOẠI');
      expect(result[0].paymentSupport).toEqual(['CHUYỂN_KHOẢN', 'THẺ_TÍN_DỤNG']);
      expect(result[0].description).toBe('Nạp tiền điện thoại MobiFone trả trước');
    });

    it('should handle null and undefined values', () => {
      const mockResponse: ProviderListResponse = [
        {
          subgroupId: null as any,
          serviceCode: undefined as any,
          categoryCode: null as any,
          subgroupNameVn: undefined as any,
          subgroupNameEn: null as any,
          partnerCode: undefined as any,
          partnerName: null as any,
          autoBillSupport: undefined as any,
          voucherSupport: null as any,
          phoneRequired: undefined as any,
          isRecommend: null as any,
          partnerType: undefined as any,
          payFee: null as any,
          type: undefined as any,
          paymentSupport: null as any,
          description: undefined as any,
        },
      ];

      const result = mapProviderListResponseToModel(mockResponse);

      expect(result).toHaveLength(1);
      expect(result[0].id).toBeNull(); // First param: provider.subgroupId
      expect(result[0].serviceCode).toBeUndefined();
      expect(result[0].categoryCode).toBeNull();
      expect(result[0].subGroupId).toBeNull(); // Fourth param: provider.subgroupId again
      expect(result[0].subgroupNameVn).toBeUndefined();
      expect(result[0].subgroupNameEn).toBeNull();
      expect(result[0].partnerCode).toBeUndefined();
      expect(result[0].partnerName).toBeNull();
      expect(result[0].autoBillSupport).toBeUndefined();
      expect(result[0].voucherSupport).toBeNull();
      expect(result[0].phoneRequired).toBeUndefined();
      expect(result[0].isRecommend).toBeNull();
      expect(result[0].partnerType).toBeUndefined();
      expect(result[0].payFee).toBeNull();
      expect(result[0].type).toBeUndefined();
      expect(result[0].paymentSupport).toBeNull();
      expect(result[0].description).toBeUndefined();
    });

    it('should handle different data types correctly', () => {
      const mockResponse: ProviderListResponse = [
        {
          subgroupId: 123 as any, // Number instead of string
          serviceCode: true as any, // Boolean instead of string
          categoryCode: ['CATEGORY'] as any, // Array instead of string
          subgroupNameVn: {name: 'Test'} as any, // Object instead of string
          subgroupNameEn: 456 as any, // Number instead of string
          partnerCode: false as any, // Boolean instead of string
          partnerName: ['Partner'] as any, // Array instead of string
          autoBillSupport: 'true' as any, // String instead of boolean
          voucherSupport: 1 as any, // Number instead of boolean
          phoneRequired: 'false' as any, // String instead of boolean
          isRecommend: 0 as any, // Number instead of boolean
          partnerType: {type: 'UTILITY'} as any, // Object instead of string
          payFee: '5000' as any, // String instead of number
          type: 789 as any, // Number instead of string
          paymentSupport: 'BANK_TRANSFER' as any, // String instead of array
          description: {desc: 'Test description'} as any, // Object instead of string
        },
      ];

      const result = mapProviderListResponseToModel(mockResponse);

      expect(result[0].id).toBe(123); // First param: provider.subgroupId
      expect(result[0].serviceCode).toBe(true);
      expect(result[0].categoryCode).toEqual(['CATEGORY']);
      expect(result[0].subGroupId).toBe(123); // Fourth param: provider.subgroupId again
      expect(result[0].subgroupNameVn).toEqual({name: 'Test'});
      expect(result[0].subgroupNameEn).toBe(456);
      expect(result[0].partnerCode).toBe(false);
      expect(result[0].partnerName).toEqual(['Partner']);
      expect(result[0].autoBillSupport).toBe('true');
      expect(result[0].voucherSupport).toBe(1);
      expect(result[0].phoneRequired).toBe('false');
      expect(result[0].isRecommend).toBe(0);
      expect(result[0].partnerType).toEqual({type: 'UTILITY'});
      expect(result[0].payFee).toBe('5000');
      expect(result[0].type).toBe(789);
      expect(result[0].paymentSupport).toBe('BANK_TRANSFER');
      expect(result[0].description).toEqual({desc: 'Test description'});
    });

    it('should handle boolean values correctly', () => {
      const mockResponse: ProviderListResponse = [
        {
          subgroupId: 'BOOL_TEST_001',
          serviceCode: 'BOOL_TEST',
          categoryCode: 'TEST',
          subgroupNameVn: 'Boolean Test',
          subgroupNameEn: 'Boolean Test',
          partnerCode: 'BOOL_TEST',
          partnerName: 'Boolean Test Partner',
          autoBillSupport: true,
          voucherSupport: false,
          phoneRequired: true,
          isRecommend: false,
          partnerType: 'TEST',
          payFee: 0,
          type: 'BOOL_TEST',
          paymentSupport: [],
          description: 'Boolean test description',
        },
      ];

      const result = mapProviderListResponseToModel(mockResponse);

      expect(result[0].autoBillSupport).toBe(true);
      expect(result[0].voucherSupport).toBe(false);
      expect(result[0].phoneRequired).toBe(true);
      expect(result[0].isRecommend).toBe(false);
      expect(typeof result[0].autoBillSupport).toBe('boolean');
      expect(typeof result[0].voucherSupport).toBe('boolean');
      expect(typeof result[0].phoneRequired).toBe('boolean');
      expect(typeof result[0].isRecommend).toBe('boolean');
    });

    it('should handle numeric values correctly', () => {
      const mockResponse: ProviderListResponse = [
        {
          subgroupId: 'NUMERIC_TEST_001',
          serviceCode: 'NUMERIC_TEST',
          categoryCode: 'TEST',
          subgroupNameVn: 'Numeric Test',
          subgroupNameEn: 'Numeric Test',
          partnerCode: 'NUMERIC_TEST',
          partnerName: 'Numeric Test Partner',
          autoBillSupport: true,
          voucherSupport: false,
          phoneRequired: false,
          isRecommend: true,
          partnerType: 'TEST',
          payFee: 15000,
          type: 'NUMERIC_TEST',
          paymentSupport: ['TEST'],
          description: 'Numeric test description',
        },
      ];

      const result = mapProviderListResponseToModel(mockResponse);

      expect(result[0].payFee).toBe(15000);
      expect(typeof result[0].payFee).toBe('number');
    });

    it('should handle array values correctly', () => {
      const mockResponse: ProviderListResponse = [
        {
          subgroupId: 'ARRAY_TEST_001',
          serviceCode: 'ARRAY_TEST',
          categoryCode: 'TEST',
          subgroupNameVn: 'Array Test',
          subgroupNameEn: 'Array Test',
          partnerCode: 'ARRAY_TEST',
          partnerName: 'Array Test Partner',
          autoBillSupport: true,
          voucherSupport: false,
          phoneRequired: false,
          isRecommend: true,
          partnerType: 'TEST',
          payFee: 0,
          type: 'ARRAY_TEST',
          paymentSupport: ['BANK_TRANSFER', 'CARD', 'E_WALLET', 'CASH'],
          description: 'Array test description',
        },
      ];

      const result = mapProviderListResponseToModel(mockResponse);

      expect(result[0].paymentSupport).toEqual(['BANK_TRANSFER', 'CARD', 'E_WALLET', 'CASH']);
      expect(Array.isArray(result[0].paymentSupport)).toBe(true);
      expect(result[0].paymentSupport).toHaveLength(4);
    });

    it('should handle large arrays efficiently', () => {
      const largeProviderList = Array.from({length: 100}, (_, index) => ({
        subgroupId: `PROVIDER_${index}`,
        serviceCode: `SERVICE_${index}`,
        categoryCode: `CATEGORY_${index % 5}`,
        subgroupNameVn: `Nhà cung cấp ${index}`,
        subgroupNameEn: `Provider ${index}`,
        partnerCode: `PARTNER_${index}`,
        partnerName: `Partner Name ${index}`,
        autoBillSupport: index % 2 === 0,
        voucherSupport: index % 3 === 0,
        phoneRequired: index % 4 === 0,
        isRecommend: index % 5 === 0,
        partnerType: index % 2 === 0 ? 'UTILITY' : 'TELECOM',
        payFee: index * 1000,
        type: `TYPE_${index % 3}`,
        paymentSupport: [`PAYMENT_${index % 2}`, `PAYMENT_${(index + 1) % 2}`],
        description: `Description for provider ${index}`,
      })) as ProviderListResponse;

      const startTime = performance.now();
      const result = mapProviderListResponseToModel(largeProviderList);
      const endTime = performance.now();

      expect(result).toHaveLength(100);
      expect(result[0]).toBeInstanceOf(ProviderModel);
      expect(result[99]).toBeInstanceOf(ProviderModel);
      expect(result[0].id).toBe('PROVIDER_0');
      expect(result[99].id).toBe('PROVIDER_99');
      expect(endTime - startTime).toBeLessThan(50); // Should complete in less than 50ms
    });

    it('should handle special characters and symbols', () => {
      const mockResponse: ProviderListResponse = [
        {
          subgroupId: 'SPECIAL_@#$%_001',
          serviceCode: 'SERVICE_!@#$%^&*()',
          categoryCode: 'CATEGORY_<>?:"{}|',
          subgroupNameVn: 'Tên nhóm với ký tự đặc biệt !@#$%',
          subgroupNameEn: 'Group name with special chars !@#$%',
          partnerCode: 'PARTNER_~`!@#$%^&*()_-+={}[]|\\:";\'<>?,./',
          partnerName: 'Partner Name !@#$%^&*()_+',
          autoBillSupport: true,
          voucherSupport: false,
          phoneRequired: true,
          isRecommend: false,
          partnerType: 'TYPE_!@#$%',
          payFee: 12345,
          type: 'TYPE_!@#$%^&*()',
          paymentSupport: ['PAYMENT_!@#', 'PAYMENT_$%^'],
          description: 'Description with special characters !@#$%^&*()_+-={}[]|\\:";\'<>?,./',
        },
      ];

      const result = mapProviderListResponseToModel(mockResponse);

      expect(result[0].id).toBe('SPECIAL_@#$%_001');
      expect(result[0].serviceCode).toBe('SERVICE_!@#$%^&*()');
      expect(result[0].categoryCode).toBe('CATEGORY_<>?:"{}|');
      expect(result[0].subGroupId).toBe('SPECIAL_@#$%_001');
      expect(result[0].subgroupNameVn).toBe('Tên nhóm với ký tự đặc biệt !@#$%');
      expect(result[0].subgroupNameEn).toBe('Group name with special chars !@#$%');
      expect(result[0].partnerCode).toBe('PARTNER_~`!@#$%^&*()_-+={}[]|\\:";\'<>?,./');
      expect(result[0].partnerName).toBe('Partner Name !@#$%^&*()_+');
      expect(result[0].partnerType).toBe('TYPE_!@#$%');
      expect(result[0].type).toBe('TYPE_!@#$%^&*()');
      expect(result[0].paymentSupport).toEqual(['PAYMENT_!@#', 'PAYMENT_$%^']);
      expect(result[0].description).toBe('Description with special characters !@#$%^&*()_+-={}[]|\\:";\'<>?,./');
    });
  });

  describe('Performance and Memory Management', () => {
    it('should handle repeated mappings efficiently', () => {
      const mockResponse: ProviderListResponse = [
        {
          subgroupId: 'PERF_TEST_001',
          serviceCode: 'PERF_TEST',
          categoryCode: 'PERFORMANCE',
          subgroupNameVn: 'Performance Test',
          subgroupNameEn: 'Performance Test',
          partnerCode: 'PERF_TEST',
          partnerName: 'Performance Test Partner',
          autoBillSupport: true,
          voucherSupport: false,
          phoneRequired: false,
          isRecommend: true,
          partnerType: 'TEST',
          payFee: 1000,
          type: 'PERF_TEST',
          paymentSupport: ['TEST'],
          description: 'Performance test description',
        },
      ];

      const iterations = 1000;
      const startTime = performance.now();

      for (let i = 0; i < iterations; i++) {
        mapProviderListResponseToModel(mockResponse);
      }

      const endTime = performance.now();
      expect(endTime - startTime).toBeLessThan(100); // Should complete 1000 mappings in less than 100ms
    });

    it('should not cause memory leaks with repeated mappings', () => {
      const mockResponse: ProviderListResponse = [
        {
          subgroupId: 'MEMORY_TEST_001',
          serviceCode: 'MEMORY_TEST',
          categoryCode: 'MEMORY',
          subgroupNameVn: 'Memory Test',
          subgroupNameEn: 'Memory Test',
          partnerCode: 'MEMORY_TEST',
          partnerName: 'Memory Test Partner',
          autoBillSupport: false,
          voucherSupport: true,
          phoneRequired: true,
          isRecommend: false,
          partnerType: 'TEST',
          payFee: 2000,
          type: 'MEMORY_TEST',
          paymentSupport: ['MEMORY_TEST'],
          description: 'Memory test description',
        },
      ];

      // Perform many mappings to test for memory leaks
      for (let i = 0; i < 100; i++) {
        const result = mapProviderListResponseToModel(mockResponse);
        expect(result).toHaveLength(1);
        expect(result[0]).toBeInstanceOf(ProviderModel);
      }

      // If we reach here without running out of memory, the test passes
      expect(true).toBe(true);
    });
  });

  describe('Type Safety and Edge Cases', () => {
    it('should maintain type safety with complex structures', () => {
      const mockResponse: ProviderListResponse = [
        {
          subgroupId: 'TYPE_SAFETY_001',
          serviceCode: 'TYPE_SAFETY_TEST',
          categoryCode: 'TYPE_TEST',
          subgroupNameVn: 'Type Safety Test',
          subgroupNameEn: 'Type Safety Test',
          partnerCode: 'TYPE_SAFETY',
          partnerName: 'Type Safety Partner',
          autoBillSupport: true,
          voucherSupport: false,
          phoneRequired: true,
          isRecommend: false,
          partnerType: 'TEST',
          payFee: 5000,
          type: 'TYPE_SAFETY_TEST',
          paymentSupport: ['BANK_TRANSFER', 'CARD'],
          description: 'Type safety test description',
        },
      ];

      const result = mapProviderListResponseToModel(mockResponse);

      // Verify all properties exist and have correct structure
      expect(result[0]).toBeInstanceOf(ProviderModel);
      expect(typeof result[0].id).toBe('string'); // First param: provider.subgroupId
      expect(typeof result[0].serviceCode).toBe('string');
      expect(typeof result[0].categoryCode).toBe('string');
      expect(typeof result[0].subGroupId).toBe('string'); // Fourth param: provider.subgroupId again
      expect(typeof result[0].subgroupNameVn).toBe('string');
      expect(typeof result[0].subgroupNameEn).toBe('string');
      expect(typeof result[0].partnerCode).toBe('string');
      expect(typeof result[0].partnerName).toBe('string');
      expect(typeof result[0].autoBillSupport).toBe('boolean');
      expect(typeof result[0].voucherSupport).toBe('boolean');
      expect(typeof result[0].phoneRequired).toBe('boolean');
      expect(typeof result[0].isRecommend).toBe('boolean');
      expect(typeof result[0].partnerType).toBe('string');
      expect(typeof result[0].payFee).toBe('number');
      expect(typeof result[0].type).toBe('string');
      expect(Array.isArray(result[0].paymentSupport)).toBe(true);
      expect(typeof result[0].description).toBe('string');
    });

    it('should handle circular references gracefully', () => {
      const circularObject: any = {
        subgroupId: 'CIRCULAR_001',
        serviceCode: 'CIRCULAR_TEST',
        categoryCode: 'CIRCULAR',
        subgroupNameVn: 'Circular Test',
        subgroupNameEn: 'Circular Test',
        partnerCode: 'CIRCULAR',
        partnerName: 'Circular Partner',
        autoBillSupport: true,
        voucherSupport: false,
        phoneRequired: false,
        isRecommend: true,
        partnerType: 'TEST',
        payFee: 1000,
        type: 'CIRCULAR_TEST',
        paymentSupport: ['CIRCULAR'],
        description: 'Circular test description',
      };

      // Create circular reference
      circularObject.self = circularObject;
      circularObject.paymentSupport.push(circularObject);

      const result = mapProviderListResponseToModel([circularObject]);

      expect(result).toHaveLength(1);
      expect(result[0]).toBeInstanceOf(ProviderModel);
      expect(result[0].id).toBe('CIRCULAR_001');
    });
  });
});
