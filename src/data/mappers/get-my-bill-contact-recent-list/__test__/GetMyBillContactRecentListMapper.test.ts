import {describe, it, expect, jest} from '@jest/globals';
import {
  mapGetMyBillContactRecentListResponseToModel,
  mapRecentBillContactResponseToModel,
} from '../GetMyBillContactRecentListMapper';
import {
  GetMyBillContactRecentListResponse,
  GetMyBillContactRecentResponse,
} from '../../../models/get-my-bill-contact-recent-list/GetMyBillContactRecentListResponse';
import {
  GetMyBillContactRecentListModel,
  GetMyBillContactRecentModel,
} from '../../../../domain/entities/get-my-bill-contact-recent-list/GetMyBillContactRecentListModel';

// Mock console.log to avoid test output pollution
const consoleSpy = jest.spyOn(console, 'log').mockImplementation(() => {});

describe('GetMyBillContactRecentListMapper', () => {
  afterEach(() => {
    consoleSpy.mockClear();
  });

  afterAll(() => {
    consoleSpy.mockRestore();
  });

  describe('mapGetMyBillContactRecentListResponseToModel', () => {
    it('should create GetMyBillContactRecentListModel from response array', () => {
      const mockResponse: GetMyBillContactRecentListResponse = [
        {
          id: 'CONTACT_001',
          billCode: 'EVN_001',
          category: 'ELECTRIC',
          subGroupId: 'SUB_001',
          customerName: 'Nguyen Van A',
          totalAmount: 500000,
          period: '2023-12',
          paymentDate: '2023-12-15T10:30:00Z',
          accountNumber: 'ACC_001',
          coreRef: 'CORE_REF_001',
          serviceCode: 'ELECTRIC_SERVICE',
          arrangementId: 'ARR_001',
          paymentOrderId: 'ORDER_001',
          cifNo: 'CIF_001',
        },
        {
          id: 'CONTACT_002',
          billCode: 'SAWACO_001',
          category: 'WATER',
          subGroupId: 'SUB_002',
          customerName: 'Tran Thi B',
          totalAmount: 200000,
          period: '2023-12',
          paymentDate: '2023-12-16T14:45:00Z',
          accountNumber: 'ACC_002',
          coreRef: 'CORE_REF_002',
          serviceCode: 'WATER_SERVICE',
          arrangementId: 'ARR_002',
          paymentOrderId: 'ORDER_002',
          cifNo: 'CIF_002',
        },
      ];

      const result = mapGetMyBillContactRecentListResponseToModel(mockResponse);

      expect(result).toHaveLength(2);
      expect(result[0]).toBeInstanceOf(GetMyBillContactRecentModel);
      expect(result[1]).toBeInstanceOf(GetMyBillContactRecentModel);
      
      expect(result[0].id).toBe('CONTACT_001');
      expect(result[0].billCode).toBe('EVN_001');
      expect(result[0].category).toBe('ELECTRIC');
      expect(result[0].customerName).toBe('Nguyen Van A');
      expect(result[0].totalAmount).toBe(500000);
      
      expect(result[1].id).toBe('CONTACT_002');
      expect(result[1].billCode).toBe('SAWACO_001');
      expect(result[1].category).toBe('WATER');
      expect(result[1].customerName).toBe('Tran Thi B');
      expect(result[1].totalAmount).toBe(200000);
    });

    it('should handle empty response array', () => {
      const mockResponse: GetMyBillContactRecentListResponse = [];

      const result = mapGetMyBillContactRecentListResponseToModel(mockResponse);

      expect(result).toEqual([]);
      expect(result).toHaveLength(0);
    });

    it('should handle single item response array', () => {
      const mockResponse: GetMyBillContactRecentListResponse = [
        {
          id: 'CONTACT_SINGLE',
          billCode: 'SINGLE_001',
          category: 'INTERNET',
          subGroupId: 'SUB_SINGLE',
          customerName: 'Le Van C',
          totalAmount: 300000,
          period: '2023-12',
          paymentDate: '2023-12-17T09:15:00Z',
          accountNumber: 'ACC_SINGLE',
          coreRef: 'CORE_REF_SINGLE',
          serviceCode: 'INTERNET_SERVICE',
          arrangementId: 'ARR_SINGLE',
          paymentOrderId: 'ORDER_SINGLE',
          cifNo: 'CIF_SINGLE',
        },
      ];

      const result = mapGetMyBillContactRecentListResponseToModel(mockResponse);

      expect(result).toHaveLength(1);
      expect(result[0]).toBeInstanceOf(GetMyBillContactRecentModel);
      expect(result[0].id).toBe('CONTACT_SINGLE');
      expect(result[0].billCode).toBe('SINGLE_001');
      expect(result[0].category).toBe('INTERNET');
    });

    it('should handle large response arrays efficiently', () => {
      const largeResponse = Array.from({length: 1000}, (_, index) => ({
        id: `CONTACT_${index}`,
        billCode: `BILL_${index}`,
        category: `CATEGORY_${index % 5}`,
        subGroupId: `SUB_${index}`,
        customerName: `Customer ${index}`,
        totalAmount: (index + 1) * 1000,
        period: '2023-12',
        paymentDate: `2023-12-${String((index % 28) + 1).padStart(2, '0')}T10:00:00Z`,
        accountNumber: `ACC_${index}`,
        coreRef: `CORE_REF_${index}`,
        serviceCode: `SERVICE_${index}`,
        arrangementId: `ARR_${index}`,
        paymentOrderId: `ORDER_${index}`,
        cifNo: `CIF_${index}`,
      })) as GetMyBillContactRecentListResponse;

      const startTime = performance.now();
      const result = mapGetMyBillContactRecentListResponseToModel(largeResponse);
      const endTime = performance.now();

      expect(result).toHaveLength(1000);
      expect(result[0]).toBeInstanceOf(GetMyBillContactRecentModel);
      expect(result[999]).toBeInstanceOf(GetMyBillContactRecentModel);
      expect(endTime - startTime).toBeLessThan(50); // Should complete in less than 50ms
    });
  });

  describe('mapRecentBillContactResponseToModel', () => {
    it('should create GetMyBillContactRecentModel with all properties', () => {
      const mockResponse: GetMyBillContactRecentResponse = {
        id: 'CONTACT_001',
        billCode: 'EVN_HCM_001',
        category: 'ELECTRIC',
        subGroupId: 'ELECTRIC_SUB_001',
        customerName: 'Nguyen Van A',
        totalAmount: 750000,
        period: '2023-12',
        paymentDate: '2023-12-15T10:30:00Z',
        accountNumber: 'MSB_ACC_001',
        coreRef: 'CORE_REF_001',
        serviceCode: 'ELECTRIC_BILL_SERVICE',
        arrangementId: 'ARR_001',
        paymentOrderId: 'PAY_ORDER_001',
        cifNo: 'CIF_001',
      };

      const result = mapRecentBillContactResponseToModel(mockResponse);

      expect(result).toBeInstanceOf(GetMyBillContactRecentModel);
      expect(result.id).toBe('CONTACT_001');
      expect(result.billCode).toBe('EVN_HCM_001');
      expect(result.category).toBe('ELECTRIC');
      expect(result.subGroupId).toBe('ELECTRIC_SUB_001');
      expect(result.customerName).toBe('Nguyen Van A');
      expect(result.totalAmount).toBe(750000);
      expect(result.period).toBe('2023-12');
      expect(result.paymentDate).toBe('2023-12-15T10:30:00Z');
      expect(result.accountNumber).toBe('MSB_ACC_001');
      expect(result.coreRef).toBe('CORE_REF_001');
      expect(result.serviceCode).toBe('ELECTRIC_BILL_SERVICE');
      expect(result.arrangementId).toBe('ARR_001');
      expect(result.paymentOrderId).toBe('PAY_ORDER_001');
      expect(result.cifNo).toBe('CIF_001');
    });

    it('should log contact data to console', () => {
      const mockResponse: GetMyBillContactRecentResponse = {
        id: 'CONTACT_LOG_TEST',
        billCode: 'LOG_TEST_001',
        category: 'TEST',
        subGroupId: 'TEST_SUB',
        customerName: 'Test Customer',
        totalAmount: 100000,
        period: '2023-12',
        paymentDate: '2023-12-01T00:00:00Z',
        accountNumber: 'TEST_ACC',
        coreRef: 'TEST_CORE_REF',
        serviceCode: 'TEST_SERVICE',
        arrangementId: 'TEST_ARR',
        paymentOrderId: 'TEST_ORDER',
        cifNo: 'TEST_CIF',
      };

      const result = mapRecentBillContactResponseToModel(mockResponse);

      expect(consoleSpy).toHaveBeenCalledWith('LOG CONTACT', result);
      expect(consoleSpy).toHaveBeenCalledTimes(1);
    });

    it('should handle null and undefined values', () => {
      const mockResponse: GetMyBillContactRecentResponse = {
        id: null as any,
        billCode: undefined as any,
        category: null as any,
        subGroupId: undefined as any,
        customerName: null as any,
        totalAmount: undefined as any,
        period: null as any,
        paymentDate: undefined as any,
        accountNumber: null as any,
        coreRef: undefined as any,
        serviceCode: null as any,
        arrangementId: undefined as any,
        paymentOrderId: null as any,
        cifNo: undefined as any,
      };

      const result = mapRecentBillContactResponseToModel(mockResponse);

      expect(result).toBeInstanceOf(GetMyBillContactRecentModel);
      expect(result.id).toBeNull();
      expect(result.billCode).toBeUndefined();
      expect(result.category).toBeNull();
      expect(result.subGroupId).toBeUndefined();
      expect(result.customerName).toBeNull();
      expect(result.totalAmount).toBeUndefined();
      expect(result.period).toBeNull();
      expect(result.paymentDate).toBeUndefined();
      expect(result.accountNumber).toBeNull();
      expect(result.coreRef).toBeUndefined();
      expect(result.serviceCode).toBeNull();
      expect(result.arrangementId).toBeUndefined();
      expect(result.paymentOrderId).toBeNull();
      expect(result.cifNo).toBeUndefined();
    });

    it('should handle Vietnamese characters correctly', () => {
      const mockResponse: GetMyBillContactRecentResponse = {
        id: 'CONTACT_VN_001',
        billCode: 'EVN_TPHCM_001',
        category: 'ĐIỆN_LỰC',
        subGroupId: 'ĐIỆN_SINH_HOẠT',
        customerName: 'Nguyễn Văn Anh',
        totalAmount: 850000,
        period: 'Tháng 12/2023',
        paymentDate: '2023-12-15T10:30:00Z',
        accountNumber: 'TK_MSB_001',
        coreRef: 'CORE_REF_VN_001',
        serviceCode: 'DỊCH_VỤ_ĐIỆN',
        arrangementId: 'ARR_VN_001',
        paymentOrderId: 'LỆNH_THANH_TOÁN_001',
        cifNo: 'CIF_VN_001',
      };

      const result = mapRecentBillContactResponseToModel(mockResponse);

      expect(result).toBeInstanceOf(GetMyBillContactRecentModel);
      expect(result.customerName).toBe('Nguyễn Văn Anh');
      expect(result.category).toBe('ĐIỆN_LỰC');
      expect(result.subGroupId).toBe('ĐIỆN_SINH_HOẠT');
      expect(result.period).toBe('Tháng 12/2023');
      expect(result.serviceCode).toBe('DỊCH_VỤ_ĐIỆN');
      expect(result.paymentOrderId).toBe('LỆNH_THANH_TOÁN_001');
    });

    it('should handle different data types correctly', () => {
      const mockResponse: GetMyBillContactRecentResponse = {
        id: 123 as any, // Number instead of string
        billCode: true as any, // Boolean instead of string
        category: ['ELECTRIC'] as any, // Array instead of string
        subGroupId: {id: 'SUB_001'} as any, // Object instead of string
        customerName: 'Valid Customer Name',
        totalAmount: '500000' as any, // String instead of number
        period: 2023 as any, // Number instead of string
        paymentDate: new Date('2023-12-15') as any, // Date instead of string
        accountNumber: 'Valid Account',
        coreRef: 'Valid Core Ref',
        serviceCode: 'Valid Service',
        arrangementId: 'Valid Arrangement',
        paymentOrderId: 'Valid Order',
        cifNo: 'Valid CIF',
      };

      const result = mapRecentBillContactResponseToModel(mockResponse);

      expect(result).toBeInstanceOf(GetMyBillContactRecentModel);
      expect(result.id).toBe(123);
      expect(result.billCode).toBe(true);
      expect(result.category).toEqual(['ELECTRIC']);
      expect(result.subGroupId).toEqual({id: 'SUB_001'});
      expect(result.totalAmount).toBe('500000');
      expect(result.period).toBe(2023);
    });

    it('should handle empty string values', () => {
      const mockResponse: GetMyBillContactRecentResponse = {
        id: '',
        billCode: '',
        category: '',
        subGroupId: '',
        customerName: '',
        totalAmount: 0,
        period: '',
        paymentDate: '',
        accountNumber: '',
        coreRef: '',
        serviceCode: '',
        arrangementId: '',
        paymentOrderId: '',
        cifNo: '',
      };

      const result = mapRecentBillContactResponseToModel(mockResponse);

      expect(result).toBeInstanceOf(GetMyBillContactRecentModel);
      expect(result.id).toBe('');
      expect(result.billCode).toBe('');
      expect(result.category).toBe('');
      expect(result.customerName).toBe('');
      expect(result.totalAmount).toBe(0);
      expect(result.period).toBe('');
    });

    it('should handle special characters and symbols', () => {
      const mockResponse: GetMyBillContactRecentResponse = {
        id: 'CONTACT_@#$%_001',
        billCode: 'BILL_!@#$%^&*()_001',
        category: 'CATEGORY_<>?:"{}|_+',
        subGroupId: 'SUB_~`!@#$%^&*()_-+={}[]|\\:";\'<>?,./',
        customerName: 'Customer Name with Symbols !@#$%',
        totalAmount: 999999,
        period: 'Period 2023-12 !@#',
        paymentDate: '2023-12-15T10:30:00Z',
        accountNumber: 'ACC_!@#$%^&*()_001',
        coreRef: 'CORE_REF_!@#$%^&*()_001',
        serviceCode: 'SERVICE_!@#$%^&*()_001',
        arrangementId: 'ARR_!@#$%^&*()_001',
        paymentOrderId: 'ORDER_!@#$%^&*()_001',
        cifNo: 'CIF_!@#$%^&*()_001',
      };

      const result = mapRecentBillContactResponseToModel(mockResponse);

      expect(result).toBeInstanceOf(GetMyBillContactRecentModel);
      expect(result.id).toBe('CONTACT_@#$%_001');
      expect(result.billCode).toBe('BILL_!@#$%^&*()_001');
      expect(result.category).toBe('CATEGORY_<>?:"{}|_+');
      expect(result.customerName).toBe('Customer Name with Symbols !@#$%');
    });
  });

  describe('Performance and Memory Management', () => {
    it('should handle repeated mappings efficiently', () => {
      const mockResponse: GetMyBillContactRecentResponse = {
        id: 'PERF_TEST_001',
        billCode: 'PERF_BILL_001',
        category: 'PERFORMANCE',
        subGroupId: 'PERF_SUB',
        customerName: 'Performance Test Customer',
        totalAmount: 100000,
        period: '2023-12',
        paymentDate: '2023-12-01T00:00:00Z',
        accountNumber: 'PERF_ACC',
        coreRef: 'PERF_CORE_REF',
        serviceCode: 'PERF_SERVICE',
        arrangementId: 'PERF_ARR',
        paymentOrderId: 'PERF_ORDER',
        cifNo: 'PERF_CIF',
      };

      const iterations = 1000;
      const startTime = performance.now();
      
      for (let i = 0; i < iterations; i++) {
        mapRecentBillContactResponseToModel(mockResponse);
      }
      
      const endTime = performance.now();
      expect(endTime - startTime).toBeLessThan(50); // Should complete 1000 mappings in less than 50ms
    });

    it('should not cause memory leaks with repeated mappings', () => {
      const mockResponse: GetMyBillContactRecentResponse = {
        id: 'MEMORY_TEST_001',
        billCode: 'MEMORY_BILL_001',
        category: 'MEMORY',
        subGroupId: 'MEMORY_SUB',
        customerName: 'Memory Test Customer',
        totalAmount: 100000,
        period: '2023-12',
        paymentDate: '2023-12-01T00:00:00Z',
        accountNumber: 'MEMORY_ACC',
        coreRef: 'MEMORY_CORE_REF',
        serviceCode: 'MEMORY_SERVICE',
        arrangementId: 'MEMORY_ARR',
        paymentOrderId: 'MEMORY_ORDER',
        cifNo: 'MEMORY_CIF',
      };

      // Perform many mappings to test for memory leaks
      for (let i = 0; i < 100; i++) {
        const result = mapRecentBillContactResponseToModel(mockResponse);
        expect(result).toBeInstanceOf(GetMyBillContactRecentModel);
      }

      // If we reach here without running out of memory, the test passes
      expect(true).toBe(true);
    });
  });

  describe('Type Safety and Edge Cases', () => {
    it('should maintain type safety with complex nested objects', () => {
      const mockResponse: GetMyBillContactRecentResponse = {
        id: 'TYPE_SAFETY_001',
        billCode: 'TYPE_BILL_001',
        category: 'TYPE_TEST',
        subGroupId: 'TYPE_SUB',
        customerName: 'Type Safety Customer',
        totalAmount: 100000,
        period: '2023-12',
        paymentDate: '2023-12-01T00:00:00Z',
        accountNumber: 'TYPE_ACC',
        coreRef: 'TYPE_CORE_REF',
        serviceCode: 'TYPE_SERVICE',
        arrangementId: 'TYPE_ARR',
        paymentOrderId: 'TYPE_ORDER',
        cifNo: 'TYPE_CIF',
      };

      const result = mapRecentBillContactResponseToModel(mockResponse);

      // Verify all properties exist and have correct types
      expect(typeof result.id).toBe('string');
      expect(typeof result.billCode).toBe('string');
      expect(typeof result.category).toBe('string');
      expect(typeof result.subGroupId).toBe('string');
      expect(typeof result.customerName).toBe('string');
      expect(typeof result.totalAmount).toBe('number');
      expect(typeof result.period).toBe('string');
      expect(typeof result.paymentDate).toBe('string');
      expect(typeof result.accountNumber).toBe('string');
      expect(typeof result.coreRef).toBe('string');
      expect(typeof result.serviceCode).toBe('string');
      expect(typeof result.arrangementId).toBe('string');
      expect(typeof result.paymentOrderId).toBe('string');
      expect(typeof result.cifNo).toBe('string');
    });

    it('should handle circular references gracefully', () => {
      const circularObject: any = {
        id: 'CIRCULAR_001',
        billCode: 'CIRCULAR_BILL_001',
        category: 'CIRCULAR',
        subGroupId: 'CIRCULAR_SUB',
        customerName: 'Circular Test Customer',
        totalAmount: 100000,
        period: '2023-12',
        paymentDate: '2023-12-01T00:00:00Z',
        accountNumber: 'CIRCULAR_ACC',
        coreRef: 'CIRCULAR_CORE_REF',
        serviceCode: 'CIRCULAR_SERVICE',
        arrangementId: 'CIRCULAR_ARR',
        paymentOrderId: 'CIRCULAR_ORDER',
        cifNo: 'CIRCULAR_CIF',
      };
      
      // Create circular reference
      circularObject.self = circularObject;

      const result = mapRecentBillContactResponseToModel(circularObject);

      expect(result).toBeInstanceOf(GetMyBillContactRecentModel);
      expect(result.id).toBe('CIRCULAR_001');
    });
  });
});
