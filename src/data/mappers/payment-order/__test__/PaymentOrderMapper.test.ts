import {describe, it, expect} from '@jest/globals';
import {mapPaymentOrderResponseToModel} from '../PaymentOrderMapper';
import {PaymentOrderResponse} from '../../../models/payment-order/PaymentOrderResponse';
import {PaymentOrderModel} from '../../../../domain/entities/payment-order/PaymentOrderModel';

describe('PaymentOrderMapper', () => {
  describe('mapPaymentOrderResponseToModel', () => {
    it('should create PaymentOrderModel instance with status and data from response', () => {
      const mockResponse: PaymentOrderResponse = {
        status: 'SUCCESS',
        data: {
          orderId: '12345',
          amount: 100000,
          transactionId: 'TXN001',
        },
      };

      const result = mapPaymentOrderResponseToModel(mockResponse);

      expect(result).toBeInstanceOf(PaymentOrderModel);
      expect(result.status).toBe('SUCCESS');
      expect(result.data).toEqual(mockResponse.data);
    });

    it('should handle response with null status', () => {
      const mockResponse: PaymentOrderResponse = {
        status: null as any,
        data: {
          orderId: '12345',
        },
      };

      const result = mapPaymentOrderResponseToModel(mockResponse);

      expect(result).toBeInstanceOf(PaymentOrderModel);
      expect(result.status).toBeNull();
      expect(result.data).toEqual(mockResponse.data);
    });

    it('should handle response with undefined status', () => {
      const mockResponse: PaymentOrderResponse = {
        status: undefined as any,
        data: {
          orderId: '12345',
        },
      };

      const result = mapPaymentOrderResponseToModel(mockResponse);

      expect(result).toBeInstanceOf(PaymentOrderModel);
      expect(result.status).toBeUndefined();
      expect(result.data).toEqual(mockResponse.data);
    });

    it('should handle response with null data', () => {
      const mockResponse: PaymentOrderResponse = {
        status: 'FAILED',
        data: null as any,
      };

      const result = mapPaymentOrderResponseToModel(mockResponse);

      expect(result).toBeInstanceOf(PaymentOrderModel);
      expect(result.status).toBe('FAILED');
      expect(result.data).toBeNull();
    });

    it('should handle response with undefined data', () => {
      const mockResponse: PaymentOrderResponse = {
        status: 'PENDING',
        data: undefined as any,
      };

      const result = mapPaymentOrderResponseToModel(mockResponse);

      expect(result).toBeInstanceOf(PaymentOrderModel);
      expect(result.status).toBe('PENDING');
      expect(result.data).toBeUndefined();
    });

    it('should handle response with empty data object', () => {
      const mockResponse: PaymentOrderResponse = {
        status: 'SUCCESS',
        data: {},
      };

      const result = mapPaymentOrderResponseToModel(mockResponse);

      expect(result).toBeInstanceOf(PaymentOrderModel);
      expect(result.status).toBe('SUCCESS');
      expect(result.data).toEqual({});
    });

    it('should handle response with complex nested data', () => {
      const mockResponse: PaymentOrderResponse = {
        status: 'SUCCESS',
        data: {
          orderId: '12345',
          amount: 100000,
          customer: {
            name: 'John Doe',
            email: '<EMAIL>',
            address: {
              street: '123 Main St',
              city: 'Ho Chi Minh City',
              country: 'Vietnam',
            },
          },
          paymentDetails: {
            method: 'BANK_TRANSFER',
            bankCode: 'VCB',
            accountNumber: '**********',
          },
          items: [
            {
              id: 'item1',
              name: 'Electricity Bill',
              amount: 100000,
            },
          ],
        },
      };

      const result = mapPaymentOrderResponseToModel(mockResponse);

      expect(result).toBeInstanceOf(PaymentOrderModel);
      expect(result.status).toBe('SUCCESS');
      expect(result.data).toEqual(mockResponse.data);
      expect(result.data.customer.name).toBe('John Doe');
      expect(result.data.paymentDetails.method).toBe('BANK_TRANSFER');
      expect(result.data.items).toHaveLength(1);
    });

    it('should handle response with array data', () => {
      const mockResponse: PaymentOrderResponse = {
        status: 'SUCCESS',
        data: [
          {id: '1', amount: 50000},
          {id: '2', amount: 75000},
        ] as any,
      };

      const result = mapPaymentOrderResponseToModel(mockResponse);

      expect(result).toBeInstanceOf(PaymentOrderModel);
      expect(result.status).toBe('SUCCESS');
      expect(Array.isArray(result.data)).toBe(true);
      expect(result.data).toHaveLength(2);
    });

    it('should handle response with string data', () => {
      const mockResponse: PaymentOrderResponse = {
        status: 'ERROR',
        data: 'Payment failed due to insufficient funds' as any,
      };

      const result = mapPaymentOrderResponseToModel(mockResponse);

      expect(result).toBeInstanceOf(PaymentOrderModel);
      expect(result.status).toBe('ERROR');
      expect(result.data).toBe('Payment failed due to insufficient funds');
    });

    it('should handle response with numeric data', () => {
      const mockResponse: PaymentOrderResponse = {
        status: 'SUCCESS',
        data: 123456 as any,
      };

      const result = mapPaymentOrderResponseToModel(mockResponse);

      expect(result).toBeInstanceOf(PaymentOrderModel);
      expect(result.status).toBe('SUCCESS');
      expect(result.data).toBe(123456);
    });

    it('should handle response with boolean data', () => {
      const mockResponse: PaymentOrderResponse = {
        status: 'SUCCESS',
        data: true as any,
      };

      const result = mapPaymentOrderResponseToModel(mockResponse);

      expect(result).toBeInstanceOf(PaymentOrderModel);
      expect(result.status).toBe('SUCCESS');
      expect(result.data).toBe(true);
    });

    it('should handle response with date objects in data', () => {
      const testDate = new Date('2023-01-01');
      const mockResponse: PaymentOrderResponse = {
        status: 'SUCCESS',
        data: {
          orderId: '12345',
          createdAt: testDate,
          processedAt: testDate.toISOString(),
        },
      };

      const result = mapPaymentOrderResponseToModel(mockResponse);

      expect(result).toBeInstanceOf(PaymentOrderModel);
      expect(result.data.createdAt).toBe(testDate);
      expect(result.data.processedAt).toBe(testDate.toISOString());
    });

    it('should handle response with function properties in data (edge case)', () => {
      const mockFunction = () => 'test';
      const mockResponse: PaymentOrderResponse = {
        status: 'SUCCESS',
        data: {
          orderId: '12345',
          functionProperty: mockFunction,
        } as any,
      };

      const result = mapPaymentOrderResponseToModel(mockResponse);

      expect(result).toBeInstanceOf(PaymentOrderModel);
      expect(result.data.functionProperty).toBe(mockFunction);
    });

    it('should handle response with Symbol properties in data (edge case)', () => {
      const symbolKey = Symbol('test');
      const mockResponse: PaymentOrderResponse = {
        status: 'SUCCESS',
        data: {
          orderId: '12345',
          [symbolKey]: 'symbol value',
        } as any,
      };

      const result = mapPaymentOrderResponseToModel(mockResponse);

      expect(result).toBeInstanceOf(PaymentOrderModel);
      expect(result.data[symbolKey]).toBe('symbol value');
    });
  });

  describe('Performance considerations', () => {
    it('should handle mapping quickly for normal responses', () => {
      const mockResponse: PaymentOrderResponse = {
        status: 'SUCCESS',
        data: {
          orderId: '12345',
          amount: 100000,
        },
      };
      
      const startTime = performance.now();
      const result = mapPaymentOrderResponseToModel(mockResponse);
      const endTime = performance.now();
      
      expect(result).toBeInstanceOf(PaymentOrderModel);
      expect(endTime - startTime).toBeLessThan(10); // Should complete in less than 10ms
    });

    it('should handle multiple rapid mappings efficiently', () => {
      const mockResponse: PaymentOrderResponse = {
        status: 'SUCCESS',
        data: {
          orderId: '12345',
          amount: 100000,
        },
      };
      const iterations = 1000;
      
      const startTime = performance.now();
      for (let i = 0; i < iterations; i++) {
        mapPaymentOrderResponseToModel(mockResponse);
      }
      const endTime = performance.now();
      
      expect(endTime - startTime).toBeLessThan(100); // Should complete 1000 mappings in less than 100ms
    });
  });

  describe('Memory management', () => {
    it('should not cause memory leaks with repeated mappings', () => {
      const mockResponse: PaymentOrderResponse = {
        status: 'SUCCESS',
        data: {
          orderId: '12345',
          amount: 100000,
        },
      };
      
      // Perform many mappings to test for memory leaks
      for (let i = 0; i < 100; i++) {
        const result = mapPaymentOrderResponseToModel(mockResponse);
        expect(result).toBeInstanceOf(PaymentOrderModel);
      }
      
      // If we reach here without running out of memory, the test passes
      expect(true).toBe(true);
    });
  });

  describe('Type safety', () => {
    it('should maintain type safety with proper response structure', () => {
      const mockResponse: PaymentOrderResponse = {
        status: 'SUCCESS',
        data: {
          orderId: '12345',
          amount: 100000,
        },
      };

      const result = mapPaymentOrderResponseToModel(mockResponse);

      expect(typeof result).toBe('object');
      expect(result).toBeInstanceOf(PaymentOrderModel);
    });

    it('should handle response with mixed data types', () => {
      const mockResponse: PaymentOrderResponse = {
        status: 'SUCCESS',
        data: {
          string: 'test',
          number: 123,
          boolean: true,
          array: [1, 2, 3],
          object: {nested: 'value'},
          nullValue: null,
          undefinedValue: undefined,
        },
      };

      const result = mapPaymentOrderResponseToModel(mockResponse);

      expect(result).toBeInstanceOf(PaymentOrderModel);
      expect(result.data).toEqual(mockResponse.data);
    });
  });

  describe('Edge cases', () => {
    it('should handle circular reference in data (edge case)', () => {
      const mockData: any = {
        orderId: '12345',
        amount: 100000,
      };
      mockData.circular = mockData; // Create circular reference

      const mockResponse: PaymentOrderResponse = {
        status: 'SUCCESS',
        data: mockData,
      };

      const result = mapPaymentOrderResponseToModel(mockResponse);

      expect(result).toBeInstanceOf(PaymentOrderModel);
      expect(result.data.circular).toBe(mockData);
    });

    it('should handle very large data objects', () => {
      const largeData: any = {
        orderId: '12345',
        amount: 100000,
      };
      for (let i = 0; i < 1000; i++) {
        largeData[`property${i}`] = `value${i}`;
      }

      const mockResponse: PaymentOrderResponse = {
        status: 'SUCCESS',
        data: largeData,
      };

      const result = mapPaymentOrderResponseToModel(mockResponse);

      expect(result).toBeInstanceOf(PaymentOrderModel);
      expect(result.data.orderId).toBe('12345');
      expect(result.data.amount).toBe(100000);
    });

    it('should handle response with special characters', () => {
      const mockResponse: PaymentOrderResponse = {
        status: 'SUCCESS',
        data: {
          orderId: '12345',
          customerName: 'Nguyễn Văn A',
          specialChars: '!@#$%^&*()_+-=[]{}|;:,.<>?',
          unicode: '🎉🚀💯',
          vietnamese: 'Tiếng Việt có dấu',
        },
      };

      const result = mapPaymentOrderResponseToModel(mockResponse);

      expect(result).toBeInstanceOf(PaymentOrderModel);
      expect(result.data.customerName).toBe('Nguyễn Văn A');
      expect(result.data.vietnamese).toBe('Tiếng Việt có dấu');
    });
  });

  describe('Integration scenarios', () => {
    it('should work correctly in a typical payment order flow', () => {
      const mockResponse: PaymentOrderResponse = {
        status: 'SUCCESS',
        data: {
          orderId: 'ORD123456789',
          transactionId: 'TXN987654321',
          amount: 250000,
          currency: 'VND',
          customerName: 'Nguyễn Văn A',
          billCode: 'EVN001234567',
          providerName: 'EVN HCMC',
          paymentMethod: 'BANK_TRANSFER',
          processedAt: new Date().toISOString(),
        },
      };

      const result = mapPaymentOrderResponseToModel(mockResponse);

      expect(result).toBeInstanceOf(PaymentOrderModel);
      expect(result.status).toBe('SUCCESS');
      expect(result.data.orderId).toBe('ORD123456789');
      expect(result.data.amount).toBe(250000);
    });

    it('should maintain consistency across multiple mappings', () => {
      const mockResponse: PaymentOrderResponse = {
        status: 'FAILED',
        data: {
          orderId: 'ORD123456789',
          errorCode: 'INSUFFICIENT_FUNDS',
          errorMessage: 'Not enough balance',
        },
      };

      const result1 = mapPaymentOrderResponseToModel(mockResponse);
      const result2 = mapPaymentOrderResponseToModel(mockResponse);

      expect(result1).toBeInstanceOf(PaymentOrderModel);
      expect(result2).toBeInstanceOf(PaymentOrderModel);
      expect(result1.status).toBe(result2.status);
      expect(result1.data).toEqual(result2.data);
    });
  });

  describe('Real-world scenarios', () => {
    it('should handle successful payment order response', () => {
      const mockResponse: PaymentOrderResponse = {
        status: 'SUCCESS',
        data: {
          orderId: 'ORD20231201001',
          transactionId: 'TXN20231201001',
          amount: 150000,
          fee: 5000,
          totalAmount: 155000,
          currency: 'VND',
          customerName: 'Trần Thị B',
          billCode: 'WATER123456789',
          providerName: 'Công ty Cấp nước TPHCM',
          paymentMethod: 'BANK_TRANSFER',
          sourceAccount: '**********',
          bankCode: 'VCB',
          processedAt: '2023-12-01T10:30:00Z',
          status: 'COMPLETED',
        },
      };

      const result = mapPaymentOrderResponseToModel(mockResponse);

      expect(result).toBeInstanceOf(PaymentOrderModel);
      expect(result.status).toBe('SUCCESS');
      expect(result.data.customerName).toBe('Trần Thị B');
      expect(result.data.totalAmount).toBe(155000);
    });

    it('should handle failed payment order response', () => {
      const mockResponse: PaymentOrderResponse = {
        status: 'FAILED',
        data: {
          orderId: 'ORD20231201002',
          errorCode: 'BILL_ALREADY_PAID',
          errorMessage: 'Hóa đơn đã được thanh toán',
          billCode: 'ELEC987654321',
          attemptedAt: '2023-12-01T11:00:00Z',
        },
      };

      const result = mapPaymentOrderResponseToModel(mockResponse);

      expect(result).toBeInstanceOf(PaymentOrderModel);
      expect(result.status).toBe('FAILED');
      expect(result.data.errorCode).toBe('BILL_ALREADY_PAID');
      expect(result.data.errorMessage).toBe('Hóa đơn đã được thanh toán');
    });

    it('should handle pending payment order response', () => {
      const mockResponse: PaymentOrderResponse = {
        status: 'PENDING',
        data: {
          orderId: 'ORD20231201003',
          amount: 200000,
          customerName: 'Lê Văn C',
          billCode: 'GAS555666777',
          providerName: 'Công ty Gas Petrolimex',
          submittedAt: '2023-12-01T12:00:00Z',
          estimatedProcessingTime: '5-10 minutes',
        },
      };

      const result = mapPaymentOrderResponseToModel(mockResponse);

      expect(result).toBeInstanceOf(PaymentOrderModel);
      expect(result.status).toBe('PENDING');
      expect(result.data.estimatedProcessingTime).toBe('5-10 minutes');
    });
  });
});
