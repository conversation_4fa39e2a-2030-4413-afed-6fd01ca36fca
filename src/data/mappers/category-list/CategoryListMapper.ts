import {CategoryListModel, CategoryModel} from '../../../domain/entities/category-list/CategoryListModel';
import {CategoryListResponse} from '../../models/category-list/CategoryListResponse';

export function mapCategoryListResponseToModel(response: Partial<CategoryListResponse>): CategoryListModel {
  const items = response.items;
  return (
    items?.map(item => {
      const categoryCode = item.productParams.find(param => param.code === 'CODE')?.dataValue;
      const categoryNameVn = item.productParams.find(param => param.code === 'VI')?.dataValue;
      const subCategories = items?.filter(subItem => {
        const subCategoryCode = subItem.productParams.find(param => param.code === 'CODE')?.dataValue;
        return subItem.code?.includes('SUB') && categoryCode?.includes(subCategoryCode || '');
      });

      if (item.code?.includes('SUB')) {
        return new CategoryModel(
          categoryCode || '',
          item.id.toString() || '',
          item.code || '',
          item.name || '',
          categoryNameVn || '',
          item.status === 'ACTIVE' || false,
          mapCategoryListResponseToModel({items: []}),
        );
      }
      return new CategoryModel(
        categoryCode || '',
        item.id.toString() || '',
        item.code || '',
        item.name || '',
        categoryNameVn || '',
        item.status === 'ACTIVE' || false,
        mapCategoryListResponseToModel({items: subCategories}),
      );
    }) ?? []
  );
}
