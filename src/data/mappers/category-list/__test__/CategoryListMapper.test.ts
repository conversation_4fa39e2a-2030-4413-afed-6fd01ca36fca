import {describe, it, expect} from '@jest/globals';
import {mapCategoryListResponseToModel} from '../CategoryListMapper';
import {CategoryListResponse, CategoryItemResponse, ProductParamResponse} from '../../../models/category-list/CategoryListResponse';
import {CategoryListModel, CategoryModel} from '../../../../domain/entities/category-list/CategoryListModel';

describe('CategoryListMapper', () => {
  describe('mapCategoryListResponseToModel', () => {
    it('should create CategoryListModel from response with main categories and subcategories', () => {
      const mockResponse: Partial<CategoryListResponse> = {
        items: [
          {
            id: 1,
            code: 'ELECTRIC_MAIN',
            name: 'Electric Bills',
            status: 'ACTIVE',
            productParams: [
              { code: 'CODE', dataValue: 'ELECTRIC' },
              { code: 'VI', dataValue: 'H<PERSON>a đơn điện' },
            ],
          },
          {
            id: 2,
            code: 'ELECTRIC_SUB_EVN',
            name: 'E<PERSON> <PERSON>',
            status: 'ACTIVE',
            productParams: [
              { code: 'CODE', dataValue: 'EVN_HCMC' },
              { code: 'VI', dataValue: 'Điện lực TP.HCM' },
            ],
          },
          {
            id: 3,
            code: 'WATER_MAIN',
            name: 'Water Bills',
            status: 'ACTIVE',
            productParams: [
              { code: 'CODE', dataValue: 'WATER' },
              { code: 'VI', dataValue: 'Hóa đơn nước' },
            ],
          },
          {
            id: 4,
            code: 'WATER_SUB_SAWACO',
            name: 'SAWACO',
            status: 'INACTIVE',
            productParams: [
              { code: 'CODE', dataValue: 'SAWACO' },
              { code: 'VI', dataValue: 'Công ty cấp nước Sài Gòn' },
            ],
          },
        ],
      };

      const result = mapCategoryListResponseToModel(mockResponse);

      expect(result).toHaveLength(4);
      expect(result[0]).toBeInstanceOf(CategoryModel);

      // Check main electric category
      expect(result[0].categoryCode).toBe('ELECTRIC');
      expect(result[0].id).toBe('1');
      expect(result[0].code).toBe('ELECTRIC_MAIN');
      expect(result[0].name).toBe('Electric Bills');
      expect(result[0].categoryNameVn).toBe('Hóa đơn điện');
      expect(result[0].isActive).toBe(true);
      expect(result[0].subCategories).toEqual([]);

      // Check electric subcategory
      expect(result[1].categoryCode).toBe('EVN_HCMC');
      expect(result[1].id).toBe('2');
      expect(result[1].code).toBe('ELECTRIC_SUB_EVN');
      expect(result[1].name).toBe('EVN Ho Chi Minh');
      expect(result[1].categoryNameVn).toBe('Điện lực TP.HCM');
      expect(result[1].isActive).toBe(true);
      expect(result[1].subCategories).toEqual([]);

      // Check water subcategory with inactive status
      expect(result[3].categoryCode).toBe('SAWACO');
      expect(result[3].isActive).toBe(false);
    });

    it('should handle empty response', () => {
      const mockResponse: Partial<CategoryListResponse> = {
        items: [],
      };

      const result = mapCategoryListResponseToModel(mockResponse);

      expect(result).toEqual([]);
      expect(result).toHaveLength(0);
    });

    it('should handle response without items', () => {
      const mockResponse: Partial<CategoryListResponse> = {};

      const result = mapCategoryListResponseToModel(mockResponse);

      expect(result).toEqual([]);
      expect(result).toHaveLength(0);
    });

    it('should handle response with undefined items', () => {
      const mockResponse: Partial<CategoryListResponse> = {
        items: undefined,
      };

      const result = mapCategoryListResponseToModel(mockResponse);

      expect(result).toEqual([]);
      expect(result).toHaveLength(0);
    });

    it('should handle categories without productParams', () => {
      const mockResponse: Partial<CategoryListResponse> = {
        items: [
          {
            id: 1,
            code: 'NO_PARAMS',
            name: 'No Params Category',
            status: 'ACTIVE',
            productParams: [],
          },
        ],
      };

      const result = mapCategoryListResponseToModel(mockResponse);

      expect(result).toHaveLength(1);
      expect(result[0].categoryCode).toBe('');
      expect(result[0].categoryNameVn).toBe('');
      expect(result[0].id).toBe('1');
      expect(result[0].code).toBe('NO_PARAMS');
      expect(result[0].name).toBe('No Params Category');
      expect(result[0].isActive).toBe(true);
    });

    it('should handle categories with missing CODE or VI params', () => {
      const mockResponse: Partial<CategoryListResponse> = {
        items: [
          {
            id: 1,
            code: 'MISSING_CODE',
            name: 'Missing Code',
            status: 'ACTIVE',
            productParams: [
              { code: 'VI', dataValue: 'Thiếu mã' },
            ],
          },
          {
            id: 2,
            code: 'MISSING_VI',
            name: 'Missing VI',
            status: 'ACTIVE',
            productParams: [
              { code: 'CODE', dataValue: 'MISSING_VI_CODE' },
            ],
          },
        ],
      };

      const result = mapCategoryListResponseToModel(mockResponse);

      expect(result).toHaveLength(2);
      expect(result[0].categoryCode).toBe('');
      expect(result[0].categoryNameVn).toBe('Thiếu mã');
      expect(result[1].categoryCode).toBe('MISSING_VI_CODE');
      expect(result[1].categoryNameVn).toBe('');
    });

    it('should handle Vietnamese characters correctly', () => {
      const mockResponse: Partial<CategoryListResponse> = {
        items: [
          {
            id: 1,
            code: 'VIETNAMESE_TEST',
            name: 'Vietnamese Test Category',
            status: 'ACTIVE',
            productParams: [
              { code: 'CODE', dataValue: 'TIẾNG_VIỆT' },
              { code: 'VI', dataValue: 'Danh mục tiếng Việt với ký tự đặc biệt: áàảãạăắằẳẵặâấầẩẫậéèẻẽẹêếềểễệíìỉĩịóòỏõọôốồổỗộơớờởỡợúùủũụưứừửữựýỳỷỹỵđ' },
            ],
          },
        ],
      };

      const result = mapCategoryListResponseToModel(mockResponse);

      expect(result[0].categoryCode).toBe('TIẾNG_VIỆT');
      expect(result[0].categoryNameVn).toBe('Danh mục tiếng Việt với ký tự đặc biệt: áàảãạăắằẳẵặâấầẩẫậéèẻẽẹêếềểễệíìỉĩịóòỏõọôốồổỗộơớờởỡợúùủũụưứừửữựýỳỷỹỵđ');
    });

    it('should handle null and undefined values', () => {
      const mockResponse: Partial<CategoryListResponse> = {
        items: [
          {
            id: null as any,
            code: undefined as any,
            name: null as any,
            status: undefined as any,
            productParams: [
              { code: 'CODE', dataValue: null as any },
              { code: 'VI', dataValue: undefined as any },
            ],
          },
        ],
      };

      const result = mapCategoryListResponseToModel(mockResponse);

      expect(result).toHaveLength(1);
      expect(result[0].categoryCode).toBe('');
      expect(result[0].id).toBe('null');
      expect(result[0].code).toBe('');
      expect(result[0].name).toBe('');
      expect(result[0].categoryNameVn).toBe('');
      expect(result[0].isActive).toBe(false);
    });

    it('should handle different data types correctly', () => {
      const mockResponse: Partial<CategoryListResponse> = {
        items: [
          {
            id: 123,
            code: 'DATA_TYPE_TEST',
            name: 'Data Type Test',
            status: 'ACTIVE',
            productParams: [
              { code: 'CODE', dataValue: 123 as any },
              { code: 'VI', dataValue: true as any },
            ],
          },
        ],
      };

      const result = mapCategoryListResponseToModel(mockResponse);

      expect(result[0].categoryCode).toBe('123');
      expect(result[0].categoryNameVn).toBe('true');
      expect(result[0].id).toBe('123');
    });

    it('should handle status values correctly', () => {
      const mockResponse: Partial<CategoryListResponse> = {
        items: [
          {
            id: 1,
            code: 'ACTIVE_STATUS',
            name: 'Active Status',
            status: 'ACTIVE',
            productParams: [
              { code: 'CODE', dataValue: 'ACTIVE_CODE' },
              { code: 'VI', dataValue: 'Trạng thái hoạt động' },
            ],
          },
          {
            id: 2,
            code: 'INACTIVE_STATUS',
            name: 'Inactive Status',
            status: 'INACTIVE',
            productParams: [
              { code: 'CODE', dataValue: 'INACTIVE_CODE' },
              { code: 'VI', dataValue: 'Trạng thái không hoạt động' },
            ],
          },
          {
            id: 3,
            code: 'OTHER_STATUS',
            name: 'Other Status',
            status: 'PENDING',
            productParams: [
              { code: 'CODE', dataValue: 'OTHER_CODE' },
              { code: 'VI', dataValue: 'Trạng thái khác' },
            ],
          },
        ],
      };

      const result = mapCategoryListResponseToModel(mockResponse);

      expect(result[0].isActive).toBe(true);
      expect(result[1].isActive).toBe(false);
      expect(result[2].isActive).toBe(false);
    });

    it('should handle subcategory logic correctly', () => {
      const mockResponse: Partial<CategoryListResponse> = {
        items: [
          {
            id: 1,
            code: 'MAIN_CATEGORY',
            name: 'Main Category',
            status: 'ACTIVE',
            productParams: [
              { code: 'CODE', dataValue: 'MAIN' },
              { code: 'VI', dataValue: 'Danh mục chính' },
            ],
          },
          {
            id: 2,
            code: 'SUB_CATEGORY_1',
            name: 'Sub Category 1',
            status: 'ACTIVE',
            productParams: [
              { code: 'CODE', dataValue: 'SUB1' },
              { code: 'VI', dataValue: 'Danh mục phụ 1' },
            ],
          },
          {
            id: 3,
            code: 'SUB_CATEGORY_2',
            name: 'Sub Category 2',
            status: 'INACTIVE',
            productParams: [
              { code: 'CODE', dataValue: 'SUB2' },
              { code: 'VI', dataValue: 'Danh mục phụ 2' },
            ],
          },
        ],
      };

      const result = mapCategoryListResponseToModel(mockResponse);

      expect(result).toHaveLength(3);
      
      // Main category should have empty subCategories since no SUB items match
      expect(result[0].code).toBe('MAIN_CATEGORY');
      expect(result[0].subCategories).toEqual([]);
      
      // Sub categories should have empty subCategories
      expect(result[1].code).toBe('SUB_CATEGORY_1');
      expect(result[1].subCategories).toEqual([]);
      expect(result[2].code).toBe('SUB_CATEGORY_2');
      expect(result[2].subCategories).toEqual([]);
    });

    it('should handle large arrays efficiently', () => {
      const largeItemList = Array.from({length: 100}, (_, index) => ({
        id: index + 1,
        code: `CATEGORY_${index}`,
        name: `Category ${index}`,
        status: index % 2 === 0 ? 'ACTIVE' : 'INACTIVE',
        productParams: [
          { code: 'CODE', dataValue: `CODE_${index}` },
          { code: 'VI', dataValue: `Danh mục ${index}` },
        ],
      })) as CategoryItemResponse[];

      const mockResponse: Partial<CategoryListResponse> = {
        items: largeItemList,
      };

      const startTime = performance.now();
      const result = mapCategoryListResponseToModel(mockResponse);
      const endTime = performance.now();

      expect(result).toHaveLength(100);
      expect(result[0]).toBeInstanceOf(CategoryModel);
      expect(result[99]).toBeInstanceOf(CategoryModel);
      expect(result[0].categoryCode).toBe('CODE_0');
      expect(result[99].categoryCode).toBe('CODE_99');
      expect(endTime - startTime).toBeLessThan(100); // Should complete in less than 100ms
    });

    it('should handle special characters and symbols', () => {
      const mockResponse: Partial<CategoryListResponse> = {
        items: [
          {
            id: 1,
            code: 'SPECIAL_!@#$%^&*()_CATEGORY',
            name: 'Category with special chars !@#$%',
            status: 'ACTIVE',
            productParams: [
              { code: 'CODE', dataValue: 'CODE_!@#$%^&*()_+-={}[]|\\:";\'<>?,./' },
              { code: 'VI', dataValue: 'Danh mục với ký tự đặc biệt !@#$%^&*()_+-={}[]|\\:";\'<>?,./' },
            ],
          },
        ],
      };

      const result = mapCategoryListResponseToModel(mockResponse);

      expect(result[0].code).toBe('SPECIAL_!@#$%^&*()_CATEGORY');
      expect(result[0].name).toBe('Category with special chars !@#$%');
      expect(result[0].categoryCode).toBe('CODE_!@#$%^&*()_+-={}[]|\\:";\'<>?,./');
      expect(result[0].categoryNameVn).toBe('Danh mục với ký tự đặc biệt !@#$%^&*()_+-={}[]|\\:";\'<>?,./');
    });

    it('should handle productParams with different codes', () => {
      const mockResponse: Partial<CategoryListResponse> = {
        items: [
          {
            id: 1,
            code: 'MULTI_PARAMS',
            name: 'Multi Params Category',
            status: 'ACTIVE',
            productParams: [
              { code: 'OTHER_PARAM', dataValue: 'Other Value' },
              { code: 'CODE', dataValue: 'MULTI_CODE' },
              { code: 'ANOTHER_PARAM', dataValue: 'Another Value' },
              { code: 'VI', dataValue: 'Danh mục nhiều tham số' },
              { code: 'EN', dataValue: 'Multi Params Category' },
            ],
          },
        ],
      };

      const result = mapCategoryListResponseToModel(mockResponse);

      expect(result[0].categoryCode).toBe('MULTI_CODE');
      expect(result[0].categoryNameVn).toBe('Danh mục nhiều tham số');
    });
  });

  describe('Performance and Memory Management', () => {
    it('should handle repeated mappings efficiently', () => {
      const mockResponse: Partial<CategoryListResponse> = {
        items: [
          {
            id: 1,
            code: 'PERF_TEST',
            name: 'Performance Test',
            status: 'ACTIVE',
            productParams: [
              { code: 'CODE', dataValue: 'PERF_CODE' },
              { code: 'VI', dataValue: 'Test hiệu suất' },
            ],
          },
        ],
      };

      const iterations = 1000;
      const startTime = performance.now();
      
      for (let i = 0; i < iterations; i++) {
        mapCategoryListResponseToModel(mockResponse);
      }
      
      const endTime = performance.now();
      expect(endTime - startTime).toBeLessThan(200); // Should complete 1000 mappings in less than 200ms
    });

    it('should not cause memory leaks with repeated mappings', () => {
      const mockResponse: Partial<CategoryListResponse> = {
        items: [
          {
            id: 1,
            code: 'MEMORY_TEST',
            name: 'Memory Test',
            status: 'ACTIVE',
            productParams: [
              { code: 'CODE', dataValue: 'MEMORY_CODE' },
              { code: 'VI', dataValue: 'Test bộ nhớ' },
            ],
          },
        ],
      };

      // Perform many mappings to test for memory leaks
      for (let i = 0; i < 100; i++) {
        const result = mapCategoryListResponseToModel(mockResponse);
        expect(result).toHaveLength(1);
        expect(result[0]).toBeInstanceOf(CategoryModel);
      }

      // If we reach here without running out of memory, the test passes
      expect(true).toBe(true);
    });
  });

  describe('Type Safety and Edge Cases', () => {
    it('should maintain type safety with complex structures', () => {
      const mockResponse: Partial<CategoryListResponse> = {
        items: [
          {
            id: 1,
            code: 'TYPE_SAFETY_TEST',
            name: 'Type Safety Test',
            status: 'ACTIVE',
            productParams: [
              { code: 'CODE', dataValue: 'TYPE_SAFETY_CODE' },
              { code: 'VI', dataValue: 'Test an toàn kiểu' },
            ],
          },
        ],
      };

      const result = mapCategoryListResponseToModel(mockResponse);

      // Verify all properties exist and have correct structure
      expect(result[0]).toBeInstanceOf(CategoryModel);
      expect(typeof result[0].categoryCode).toBe('string');
      expect(typeof result[0].id).toBe('string');
      expect(typeof result[0].code).toBe('string');
      expect(typeof result[0].name).toBe('string');
      expect(typeof result[0].categoryNameVn).toBe('string');
      expect(typeof result[0].isActive).toBe('boolean');
      expect(Array.isArray(result[0].subCategories)).toBe(true);
    });

    it('should handle circular references gracefully', () => {
      const circularObject: any = {
        id: 1,
        code: 'CIRCULAR_TEST',
        name: 'Circular Test',
        status: 'ACTIVE',
        productParams: [
          { code: 'CODE', dataValue: 'CIRCULAR_CODE' },
          { code: 'VI', dataValue: 'Test vòng lặp' },
        ],
      };
      
      // Create circular reference
      circularObject.self = circularObject;
      circularObject.productParams.push(circularObject);

      const result = mapCategoryListResponseToModel({ items: [circularObject] });

      expect(result).toHaveLength(1);
      expect(result[0]).toBeInstanceOf(CategoryModel);
      expect(result[0].categoryCode).toBe('CIRCULAR_CODE');
    });
  });
});
