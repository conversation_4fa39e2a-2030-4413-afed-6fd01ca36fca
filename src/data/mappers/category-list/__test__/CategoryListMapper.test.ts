import {describe, it, expect} from '@jest/globals';
import {mapCategoryListResponseToModel} from '../CategoryListMapper';
import {CategoryListModel, CategoryModel} from '../../../../domain/entities/category-list/CategoryListModel';

describe('CategoryListMapper', () => {
  describe('mapCategoryListResponseToModel', () => {
    it('should create CategoryListModel from response with main categories and subcategories', () => {
      const mockResponse = {
        items: [
          {
            id: 1,
            code: 'ELECTRIC_MAIN',
            name: 'Electric Bills',
            status: 'ACTIVE',
            productParams: [
              {code: 'CODE', dataValue: 'ELECTRIC'},
              {code: 'VI', dataValue: 'Hóa đơn điện'},
            ],
          },
          {
            id: 2,
            code: 'ELECTRIC_SUB_EVN',
            name: '<PERSON><PERSON>',
            status: 'ACTIVE',
            productParams: [
              {code: 'CODE', dataValue: 'EVN_HCMC'},
              {code: 'VI', dataValue: 'Điện lực TP.HCM'},
            ],
          },
        ],
      };

      const result = mapCategoryListResponseToModel(mockResponse);

      expect(result).toHaveLength(2);
      expect(result[0]).toBeInstanceOf(CategoryModel);

      // Check main electric category - CategoryModel constructor params:
      // (id, order, categoryCode, categoryName, description, active, children)
      expect(result[0].id).toBe('ELECTRIC'); // categoryCode from productParams
      expect(result[0].order).toBe('1'); // item.id.toString()
      expect(result[0].categoryCode).toBe('ELECTRIC_MAIN'); // item.code
      expect(result[0].categoryName).toBe('Electric Bills'); // item.name
      expect(result[0].description).toBe('Hóa đơn điện'); // categoryNameVn from productParams
      expect(result[0].active).toBe(true); // item.status === 'ACTIVE'
      expect(Array.isArray(result[0].children)).toBe(true); // subcategories array

      // Check electric subcategory
      expect(result[1].id).toBe('EVN_HCMC'); // categoryCode from productParams
      expect(result[1].order).toBe('2'); // item.id.toString()
      expect(result[1].categoryCode).toBe('ELECTRIC_SUB_EVN'); // item.code
      expect(result[1].categoryName).toBe('EVN Ho Chi Minh'); // item.name
      expect(result[1].description).toBe('Điện lực TP.HCM'); // categoryNameVn from productParams
      expect(result[1].active).toBe(true); // item.status === 'ACTIVE'
      expect(Array.isArray(result[1].children)).toBe(true); // subcategories array
    });

    it('should handle empty response', () => {
      const mockResponse = {
        items: [],
      };

      const result = mapCategoryListResponseToModel(mockResponse);

      expect(result).toEqual([]);
      expect(result).toHaveLength(0);
    });

    it('should handle response without items', () => {
      const mockResponse = {};

      const result = mapCategoryListResponseToModel(mockResponse);

      expect(result).toEqual([]);
      expect(result).toHaveLength(0);
    });

    it('should handle response with undefined items', () => {
      const mockResponse = {
        items: undefined,
      };

      const result = mapCategoryListResponseToModel(mockResponse);

      expect(result).toEqual([]);
      expect(result).toHaveLength(0);
    });

    it('should handle categories without productParams', () => {
      const mockResponse = {
        items: [
          {
            id: 1,
            code: 'NO_PARAMS',
            name: 'No Params Category',
            status: 'ACTIVE',
            productParams: [],
          },
        ],
      };

      const result = mapCategoryListResponseToModel(mockResponse);

      expect(result).toHaveLength(1);
      expect(result[0].id).toBe(''); // Empty categoryCode
      expect(result[0].order).toBe('1');
      expect(result[0].categoryCode).toBe('NO_PARAMS');
      expect(result[0].categoryName).toBe('No Params Category');
      expect(result[0].description).toBe(''); // Empty categoryNameVn
      expect(result[0].active).toBe(true);
    });

    it('should handle categories with missing CODE or VI params', () => {
      const mockResponse = {
        items: [
          {
            id: 1,
            code: 'MISSING_CODE',
            name: 'Missing Code',
            status: 'ACTIVE',
            productParams: [{code: 'VI', dataValue: 'Thiếu mã'}],
          },
          {
            id: 2,
            code: 'MISSING_VI_CODE',
            name: 'Missing VI',
            status: 'ACTIVE',
            productParams: [{code: 'CODE', dataValue: 'MISSING_VI_CODE'}],
          },
        ],
      };

      const result = mapCategoryListResponseToModel(mockResponse);

      expect(result).toHaveLength(2);
      expect(result[0].id).toBe(''); // Missing CODE param
      expect(result[0].description).toBe('Thiếu mã');
      expect(result[1].id).toBe('MISSING_VI_CODE');
      expect(result[1].description).toBe(''); // Missing VI param
    });

    it('should handle Vietnamese characters correctly', () => {
      const mockResponse = {
        items: [
          {
            id: 1,
            code: 'VIETNAMESE_TEST',
            name: 'Vietnamese Category',
            status: 'ACTIVE',
            productParams: [
              {code: 'CODE', dataValue: 'TIẾNG_VIỆT'},
              {
                code: 'VI',
                dataValue:
                  'Danh mục tiếng Việt với ký tự đặc biệt: áàảãạăắằẳẵặâấầẩẫậéèẻẽẹêếềểễệíìỉĩịóòỏõọôốồổỗộơớờởỡợúùủũụưứừửữựýỳỷỹỵđ',
              },
            ],
          },
        ],
      };

      const result = mapCategoryListResponseToModel(mockResponse);

      expect(result[0].id).toBe('TIẾNG_VIỆT');
      expect(result[0].description).toBe(
        'Danh mục tiếng Việt với ký tự đặc biệt: áàảãạăắằẳẵặâấầẩẫậéèẻẽẹêếềểễệíìỉĩịóòỏõọôốồổỗộơớờởỡợúùủũụưứừửữựýỳỷỹỵđ',
      );
    });

    it('should handle null and undefined values', () => {
      const mockResponse = {
        items: [
          {
            id: 1,
            code: null,
            name: undefined,
            status: 'ACTIVE',
            productParams: [
              {code: 'CODE', dataValue: 'NULL_TEST'},
              {code: 'VI', dataValue: null},
            ],
          },
        ],
      };

      const result = mapCategoryListResponseToModel(mockResponse);

      expect(result).toHaveLength(1);
      expect(result[0].id).toBe('NULL_TEST');
      expect(result[0].categoryCode).toBe(''); // null code becomes empty string
      expect(result[0].categoryName).toBe(''); // undefined name becomes empty string
      expect(result[0].description).toBe(''); // null dataValue becomes empty string
    });

    it('should handle different data types correctly', () => {
      const mockResponse = {
        items: [
          {
            id: 123,
            code: 'DATA_TYPE_TEST',
            name: 'Data Type Test',
            status: 'ACTIVE',
            productParams: [
              {code: 'CODE', dataValue: 123},
              {code: 'VI', dataValue: true},
            ],
          },
        ],
      };

      const result = mapCategoryListResponseToModel(mockResponse);

      expect(result[0].id).toBe(123); // Number dataValue stays as number
      expect(result[0].order).toBe('123'); // Number id converted to string
      expect(result[0].categoryCode).toBe('DATA_TYPE_TEST');
      expect(result[0].description).toBe(true); // Boolean dataValue stays as boolean
    });

    it('should handle status values correctly', () => {
      const mockResponse = {
        items: [
          {
            id: 1,
            code: 'ACTIVE_STATUS',
            name: 'Active',
            status: 'ACTIVE',
            productParams: [{code: 'CODE', dataValue: 'ACTIVE_CODE'}],
          },
          {
            id: 2,
            code: 'INACTIVE_STATUS',
            name: 'Inactive',
            status: 'INACTIVE',
            productParams: [{code: 'CODE', dataValue: 'INACTIVE_CODE'}],
          },
          {
            id: 3,
            code: 'NULL_STATUS',
            name: 'Null Status',
            status: null,
            productParams: [{code: 'CODE', dataValue: 'NULL_CODE'}],
          },
        ],
      };

      const result = mapCategoryListResponseToModel(mockResponse);

      expect(result[0].active).toBe(true); // 'ACTIVE' status
      expect(result[1].active).toBe(false); // 'INACTIVE' status
      expect(result[2].active).toBe(false); // null status
    });

    it('should handle performance with large arrays efficiently', () => {
      const items = Array.from({length: 100}, (_, index) => ({
        id: index,
        code: `CATEGORY_${index}`,
        name: `Category ${index}`,
        status: 'ACTIVE',
        productParams: [
          {code: 'CODE', dataValue: `CODE_${index}`},
          {code: 'VI', dataValue: `Danh mục ${index}`},
        ],
      }));

      const mockResponse = {items};

      const startTime = Date.now();
      const result = mapCategoryListResponseToModel(mockResponse);
      const endTime = Date.now();

      expect(result).toHaveLength(100);
      expect(result[0]).toBeInstanceOf(CategoryModel);
      expect(result[99]).toBeInstanceOf(CategoryModel);
      expect(result[0].id).toBe('CODE_0');
      expect(result[99].id).toBe('CODE_99');
      expect(endTime - startTime).toBeLessThan(100); // Should complete in less than 100ms
    });

    it('should maintain type safety with complex structures', () => {
      const mockResponse = {
        items: [
          {
            id: 1,
            code: 'TYPE_SAFETY_TEST',
            name: 'Type Safety Test',
            status: 'ACTIVE',
            productParams: [
              {code: 'CODE', dataValue: 'COMPLEX_TYPE'},
              {code: 'VI', dataValue: 'Kiểm tra kiểu dữ liệu'},
            ],
          },
        ],
      };

      const result = mapCategoryListResponseToModel(mockResponse);

      // Verify all properties exist and have correct structure
      expect(result[0]).toBeInstanceOf(CategoryModel);
      expect(typeof result[0].id).toBe('string');
      expect(typeof result[0].order).toBe('string');
      expect(typeof result[0].categoryCode).toBe('string');
      expect(typeof result[0].categoryName).toBe('string');
      expect(typeof result[0].description).toBe('string');
      expect(typeof result[0].active).toBe('boolean');
      expect(Array.isArray(result[0].children)).toBe(true);
    });
  });
});
