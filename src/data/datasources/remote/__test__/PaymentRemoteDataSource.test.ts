import {describe, it, expect, jest, beforeEach} from '@jest/globals';
import {PaymentRemoteDataSource} from '../PaymentRemoteDataSource';
import {ValidateRequest} from '../../../models/validate/ValidateRequest';
import {ValidateResponse} from '../../../models/validate/ValidateResponse';
import {BaseResponse} from '../../../../core/BaseResponse';
import {CustomError, ErrorCategory} from '../../../../core/MSBCustomError';
import {IHttpClient} from 'msb-host-shared-module';

// Mock dependencies
jest.mock('../../../../utils/PathResolver', () => ({
  PathResolver: {
    payment: {
      validate: jest.fn(),
    },
  },
}));

jest.mock('../../../../utils/ResponseHandler', () => ({
  handleResponse: jest.fn(),
}));

jest.mock('../../../../core/MSBCustomError', () => ({
  ...jest.requireActual('../../../../core/MSBCustomError'),
  createError: jest.fn(
    () =>
      new (jest.requireActual('../../../../core/MSBCustomError').CustomError)(
        'UNKNOWN_ERROR',
        jest.requireActual('../../../../core/MSBCustomError').ErrorCategory.UNKNOWN,
        'Unknown Error',
        'An unknown error occurred',
        false,
      ),
  ),
}));

import {PathResolver} from '../../../../utils/PathResolver';
import {handleResponse} from '../../../../utils/ResponseHandler';
import {createError} from '../../../../core/MSBCustomError';

const mockHandleResponse = handleResponse as jest.MockedFunction<typeof handleResponse>;
const mockCreateError = createError as jest.MockedFunction<typeof createError>;

describe('PaymentRemoteDataSource', () => {
  let paymentDataSource: PaymentRemoteDataSource;
  let mockHttpClient: jest.Mocked<IHttpClient>;

  beforeEach(() => {
    jest.clearAllMocks();

    // Setup PathResolver mock
    (PathResolver.payment.validate as jest.MockedFunction<any>).mockReturnValue('/api/payment/validate');

    // Create mock HTTP client
    mockHttpClient = {
      get: jest.fn(),
      post: jest.fn(),
      put: jest.fn(),
      delete: jest.fn(),
      patch: jest.fn(),
    };

    paymentDataSource = new PaymentRemoteDataSource(mockHttpClient);
  });

  describe('constructor', () => {
    it('should create PaymentRemoteDataSource with HTTP client', () => {
      expect(paymentDataSource).toBeInstanceOf(PaymentRemoteDataSource);
      expect(paymentDataSource['httpClient']).toBe(mockHttpClient);
    });
  });

  describe('validate', () => {
    const mockRequest: ValidateRequest = {
      accountNumber: '**********',
      amount: 100000,
      currency: 'VND',
    };

    const mockHttpResponse = {
      status: 200,
      data: {
        transactionId: 'TXN123',
        status: 'SUCCESS',
        message: 'Validation successful',
      },
    };

    const mockValidateResponse: BaseResponse<ValidateResponse> = {
      transactionId: 'TXN123',
      status: 'SUCCESS',
      message: 'Validation successful',
    };

    it('should call PathResolver to get validate URL', async () => {
      mockHttpClient.post.mockResolvedValue(mockHttpResponse);
      mockHandleResponse.mockReturnValue(mockValidateResponse);

      await paymentDataSource.validate(mockRequest);

      expect(PathResolver.payment.validate).toHaveBeenCalled();
    });

    it('should call HTTP client post method with correct URL and request', async () => {
      mockHttpClient.post.mockResolvedValue(mockHttpResponse);
      mockHandleResponse.mockReturnValue(mockValidateResponse);

      await paymentDataSource.validate(mockRequest);

      expect(mockHttpClient.post).toHaveBeenCalledWith('/api/payment/validate', mockRequest);
      expect(mockHttpClient.post).toHaveBeenCalledTimes(1);
    });

    it('should call handleResponse with HTTP response', async () => {
      mockHttpClient.post.mockResolvedValue(mockHttpResponse);
      mockHandleResponse.mockReturnValue(mockValidateResponse);

      await paymentDataSource.validate(mockRequest);

      expect(mockHandleResponse).toHaveBeenCalledWith(mockHttpResponse);
      expect(mockHandleResponse).toHaveBeenCalledTimes(1);
    });

    it('should return response from handleResponse', async () => {
      mockHttpClient.post.mockResolvedValue(mockHttpResponse);
      mockHandleResponse.mockReturnValue(mockValidateResponse);

      const result = await paymentDataSource.validate(mockRequest);

      expect(result).toBe(mockValidateResponse);
    });

    it('should handle successful validation response', async () => {
      const successResponse = {
        status: 200,
        data: {
          transactionId: 'TXN456',
          status: 'SUCCESS',
          message: 'Account validation successful',
        },
      };

      const expectedResponse: BaseResponse<ValidateResponse> = {
        transactionId: 'TXN456',
        status: 'SUCCESS',
        message: 'Account validation successful',
      };

      mockHttpClient.post.mockResolvedValue(successResponse);
      mockHandleResponse.mockReturnValue(expectedResponse);

      const result = await paymentDataSource.validate(mockRequest);

      expect(result).toEqual(expectedResponse);
    });

    it('should handle different request types', async () => {
      const requests = [
        {
          accountNumber: '**********',
          amount: 50000,
          currency: 'VND',
        },
        {
          accountNumber: '**********',
          amount: 200000,
          currency: 'USD',
        },
        {
          accountNumber: '**********',
          amount: 1000000,
          currency: 'VND',
        },
      ];

      for (const request of requests) {
        mockHttpClient.post.mockResolvedValue(mockHttpResponse);
        mockHandleResponse.mockReturnValue(mockValidateResponse);

        await paymentDataSource.validate(request);

        expect(mockHttpClient.post).toHaveBeenCalledWith('/api/payment/validate', request);
      }

      expect(mockHttpClient.post).toHaveBeenCalledTimes(3);
    });
  });

  describe('Error handling', () => {
    const mockRequest: ValidateRequest = {
      accountNumber: '**********',
      amount: 100000,
      currency: 'VND',
    };

    it('should re-throw CustomError when HTTP client throws CustomError', async () => {
      const customError = new CustomError(
        'VALIDATION_ERROR',
        ErrorCategory.VALIDATION,
        'Validation Error',
        'Invalid account number',
        false,
      );

      mockHttpClient.post.mockRejectedValue(customError);

      await expect(paymentDataSource.validate(mockRequest)).rejects.toThrow(customError);
      expect(mockCreateError).not.toHaveBeenCalled();
    });

    it('should create generic error when HTTP client throws non-CustomError', async () => {
      const networkError = new Error('Network connection failed');
      const genericError = new CustomError(
        'UNKNOWN_ERROR',
        ErrorCategory.UNKNOWN,
        'Unknown Error',
        'An unknown error occurred',
        false,
      );

      mockHttpClient.post.mockRejectedValue(networkError);
      mockCreateError.mockReturnValue(genericError);

      await expect(paymentDataSource.validate(mockRequest)).rejects.toThrow(genericError);
      expect(mockCreateError).toHaveBeenCalled();
    });

    it('should handle HTTP 400 errors', async () => {
      const badRequestError = new CustomError(
        'BAD_REQUEST',
        ErrorCategory.VALIDATION,
        'Bad Request',
        'Invalid request parameters',
        false,
      );

      mockHttpClient.post.mockRejectedValue(badRequestError);

      await expect(paymentDataSource.validate(mockRequest)).rejects.toThrow(badRequestError);
    });

    it('should handle HTTP 401 errors', async () => {
      const unauthorizedError = new CustomError(
        'UNAUTHORIZED',
        ErrorCategory.AUTHENTICATION,
        'Unauthorized',
        'Authentication required',
        false,
      );

      mockHttpClient.post.mockRejectedValue(unauthorizedError);

      await expect(paymentDataSource.validate(mockRequest)).rejects.toThrow(unauthorizedError);
    });

    it('should handle HTTP 403 errors', async () => {
      const forbiddenError = new CustomError(
        'FORBIDDEN',
        ErrorCategory.AUTHORIZATION,
        'Forbidden',
        'Access denied',
        false,
      );

      mockHttpClient.post.mockRejectedValue(forbiddenError);

      await expect(paymentDataSource.validate(mockRequest)).rejects.toThrow(forbiddenError);
    });

    it('should handle HTTP 404 errors', async () => {
      const notFoundError = new CustomError('NOT_FOUND', ErrorCategory.API, 'Not Found', 'Endpoint not found', false);

      mockHttpClient.post.mockRejectedValue(notFoundError);

      await expect(paymentDataSource.validate(mockRequest)).rejects.toThrow(notFoundError);
    });

    it('should handle HTTP 500 errors', async () => {
      const serverError = new CustomError(
        'INTERNAL_SERVER_ERROR',
        ErrorCategory.API,
        'Internal Server Error',
        'Server encountered an error',
        true,
      );

      mockHttpClient.post.mockRejectedValue(serverError);

      await expect(paymentDataSource.validate(mockRequest)).rejects.toThrow(serverError);
    });

    it('should handle timeout errors', async () => {
      const timeoutError = new Error('Request timeout');
      const genericError = new CustomError(
        'UNKNOWN_ERROR',
        ErrorCategory.UNKNOWN,
        'Unknown Error',
        'An unknown error occurred',
        false,
      );

      mockHttpClient.post.mockRejectedValue(timeoutError);
      mockCreateError.mockReturnValue(genericError);

      await expect(paymentDataSource.validate(mockRequest)).rejects.toThrow(genericError);
    });

    it('should handle network errors', async () => {
      const networkError = new Error('Network Error');
      const genericError = new CustomError(
        'UNKNOWN_ERROR',
        ErrorCategory.UNKNOWN,
        'Unknown Error',
        'An unknown error occurred',
        false,
      );

      mockHttpClient.post.mockRejectedValue(networkError);
      mockCreateError.mockReturnValue(genericError);

      await expect(paymentDataSource.validate(mockRequest)).rejects.toThrow(genericError);
    });
  });

  describe('Response handling edge cases', () => {
    const mockRequest: ValidateRequest = {
      accountNumber: '**********',
      amount: 100000,
      currency: 'VND',
    };

    it('should handle null response from HTTP client', async () => {
      mockHttpClient.post.mockResolvedValue(null);
      mockHandleResponse.mockReturnValue({} as any);

      await paymentDataSource.validate(mockRequest);

      expect(mockHandleResponse).toHaveBeenCalledWith(null);
    });

    it('should handle undefined response from HTTP client', async () => {
      mockHttpClient.post.mockResolvedValue(undefined);
      mockHandleResponse.mockReturnValue({} as any);

      await paymentDataSource.validate(mockRequest);

      expect(mockHandleResponse).toHaveBeenCalledWith(undefined);
    });

    it('should handle response with error status', async () => {
      const errorResponse = {
        status: 400,
        data: {
          errors: [
            {
              key: 'INVALID_ACCOUNT',
              message: 'Account number is invalid',
              context: ['accountNumber'],
            },
          ],
        },
      };

      const expectedResponse: BaseResponse<ValidateResponse> = {
        errors: [
          {
            key: 'INVALID_ACCOUNT',
            message: 'Account number is invalid',
            context: ['accountNumber'],
          },
        ],
      };

      mockHttpClient.post.mockResolvedValue(errorResponse);
      mockHandleResponse.mockReturnValue(expectedResponse);

      const result = await paymentDataSource.validate(mockRequest);

      expect(result).toEqual(expectedResponse);
    });
  });

  describe('Integration scenarios', () => {
    it('should handle complete validation flow', async () => {
      const request: ValidateRequest = {
        accountNumber: '**********',
        amount: 500000,
        currency: 'VND',
      };

      const httpResponse = {
        status: 200,
        data: {
          transactionId: 'TXN789',
          status: 'SUCCESS',
          message: 'Validation completed successfully',
        },
      };

      const expectedResponse: BaseResponse<ValidateResponse> = {
        transactionId: 'TXN789',
        status: 'SUCCESS',
        message: 'Validation completed successfully',
      };

      mockHttpClient.post.mockResolvedValue(httpResponse);
      mockHandleResponse.mockReturnValue(expectedResponse);

      const result = await paymentDataSource.validate(request);

      expect(PathResolver.payment.validate).toHaveBeenCalled();
      expect(mockHttpClient.post).toHaveBeenCalledWith('/api/payment/validate', request);
      expect(mockHandleResponse).toHaveBeenCalledWith(httpResponse);
      expect(result).toEqual(expectedResponse);
    });

    it('should maintain interface contract', async () => {
      const request: ValidateRequest = {
        accountNumber: '**********',
        amount: 100000,
        currency: 'VND',
      };

      mockHttpClient.post.mockResolvedValue({});
      mockHandleResponse.mockReturnValue({} as any);

      await paymentDataSource.validate(request);

      // Verify the interface contract is maintained
      expect(mockHttpClient.post).toHaveBeenCalledWith(
        expect.any(String),
        expect.objectContaining({
          accountNumber: expect.any(String),
          amount: expect.any(Number),
          currency: expect.any(String),
        }),
      );
    });
  });
});
