import {describe, it, expect, beforeEach, afterEach, jest} from '@jest/globals';
import {
  mockResponseForGetBillProviders,
  mockResponseForGetBillProvidersByCategory,
  mockServerFailureForGetBillProviders,
  mockBillProvidersResponse,
} from '../../../__mocks__/service-apis/get-bill-providers';
import {
  mockResponseForValidateBill,
  mockResponseForValidateBillInvalid,
  mockServerFailureForValidateBill,
  mockBillValidationResponse,
} from '../../../__mocks__/service-apis/validate-bill';

// Mock service class for demonstration
class BillPayService {
  async getBillProviders(categoryId?: string) {
    const url = categoryId
      ? `https://gateway.msb.com.vn/api/payment/client-api/v1/bill-pay/providers?category=${categoryId}`
      : 'https://gateway.msb.com.vn/api/payment/client-api/v1/bill-pay/providers';

    const response = await fetch(url);
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    return response.json();
  }

  async validateBill(providerId: string, customerCode: string) {
    const response = await fetch('https://gateway.msb.com.vn/api/payment/client-api/v1/bill-pay/validate', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        providerId,
        customerCode,
      }),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    return response.json();
  }
}

describe('BillPayService', () => {
  let billPayService: BillPayService;

  beforeEach(() => {
    billPayService = new BillPayService();
    jest.clearAllMocks();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('getBillProviders', () => {
    it('should fetch all bill providers successfully', async () => {
      mockResponseForGetBillProviders();

      const result = await billPayService.getBillProviders();

      expect(result).toEqual(mockBillProvidersResponse);
      expect(Array.isArray(result)).toBe(true);
      expect(result.length).toBeGreaterThan(0);
    });

    it('should fetch bill providers by category', async () => {
      const categoryId = 'electricity';
      mockResponseForGetBillProvidersByCategory(categoryId);

      const result = await billPayService.getBillProviders(categoryId);

      expect(Array.isArray(result)).toBe(true);
      // Should only return providers for the specified category
      result.forEach((provider: any) => {
        expect(provider.categoryId).toBe(categoryId);
      });
    });

    it('should handle server failure when fetching bill providers', async () => {
      mockServerFailureForGetBillProviders();

      await expect(billPayService.getBillProviders()).rejects.toThrow('HTTP error! status: 500');
    });

    it('should return providers with correct structure', async () => {
      mockResponseForGetBillProviders();

      const result = await billPayService.getBillProviders();

      expect(result[0]).toHaveProperty('id');
      expect(result[0]).toHaveProperty('name');
      expect(result[0]).toHaveProperty('code');
      expect(result[0]).toHaveProperty('categoryId');
      expect(result[0]).toHaveProperty('enabled');
      expect(result[0]).toHaveProperty('fields');
      expect(result[0]).toHaveProperty('fees');
    });
  });

  describe('validateBill', () => {
    it('should validate bill successfully with valid customer code', async () => {
      mockResponseForValidateBill();

      const result = await billPayService.validateBill('evn', '123456789');

      expect(result).toEqual(mockBillValidationResponse.valid);
      expect(result.success).toBe(true);
      expect(result.data.isValid).toBe(true);
      expect(result.data.customerInfo).toBeDefined();
      expect(result.data.customerInfo.customerCode).toBe('123456789');
    });

    it('should handle invalid customer code', async () => {
      mockResponseForValidateBillInvalid();

      await expect(billPayService.validateBill('evn', 'INVALID')).rejects.toThrow('HTTP error! status: 400');
    });

    it('should handle server failure during validation', async () => {
      mockServerFailureForValidateBill();

      await expect(billPayService.validateBill('evn', '123456789')).rejects.toThrow('HTTP error! status: 500');
    });

    it('should return customer info with bill details', async () => {
      mockResponseForValidateBill();

      const result = await billPayService.validateBill('evn', '123456789');

      expect(result.data.customerInfo).toHaveProperty('customerName');
      expect(result.data.customerInfo).toHaveProperty('address');
      expect(result.data.customerInfo).toHaveProperty('billAmount');
      expect(result.data.customerInfo).toHaveProperty('dueDate');
      expect(result.data.customerInfo).toHaveProperty('billDetails');
      expect(result.data.fees).toHaveProperty('serviceFee');
      expect(result.data.fees).toHaveProperty('totalAmount');
    });

    it('should validate different provider types', async () => {
      mockResponseForValidateBill();

      // Test electricity provider
      const electricityResult = await billPayService.validateBill('evn', '123456789');
      expect(electricityResult.success).toBe(true);

      // Test mobile provider
      const mobileResult = await billPayService.validateBill('viettel', '**********');
      expect(mobileResult.success).toBe(true);
    });
  });
});
