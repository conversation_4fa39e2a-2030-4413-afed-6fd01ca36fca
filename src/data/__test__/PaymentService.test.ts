import {describe, it, expect, beforeEach, jest} from '@jest/globals';
import {
  mockResponseForGetPaymentMethods,
  mockServerFailureForGetPaymentMethods,
  mockPaymentMethodsResponse,
} from '../../../__mocks__/service-apis/get-payment-methods';
import {
  mockResponseForProcessPayment,
  mockServerFailureForProcessPayment,
  mockErrorResponseForProcessPayment,
  mockPaymentResponse,
} from '../../../__mocks__/service-apis/process-payment';

// Example service interface
interface PaymentMethod {
  id: string;
  name: string;
  type: string;
  isActive: boolean;
}

interface PaymentRequest {
  amount: number;
  currency: string;
  methodId: string;
}

interface PaymentResponse {
  transactionId: string;
  status: 'SUCCESS' | 'FAILED';
  message: string;
}

// Example service implementation
class PaymentService {
  async getPaymentMethods(): Promise<PaymentMethod[]> {
    const response = await fetch('https://gateway.msb.com.vn/api/payment/client-api/v1/payment-methods');
    const data = await response.json();

    if (!data.success) {
      throw new Error(data.message);
    }

    return data.data.methods;
  }

  async processPayment(request: PaymentRequest): Promise<PaymentResponse> {
    const response = await fetch('https://gateway.msb.com.vn/api/payment/client-api/v1/process', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(request),
    });

    const data = await response.json();

    if (!data.success) {
      throw new Error(data.message);
    }

    return data.data;
  }
}

describe('PaymentService', () => {
  let paymentService: PaymentService;

  beforeEach(() => {
    paymentService = new PaymentService();
  });

  describe('getPaymentMethods', () => {
    it('should fetch payment methods successfully', async () => {
      // Setup MSW handler for successful response
      mockResponseForGetPaymentMethods();

      const methods = await paymentService.getPaymentMethods();

      expect(Array.isArray(methods)).toBe(true);
      expect(methods.length).toBeGreaterThan(0);
      expect(methods[0]).toHaveProperty('id');
      expect(methods[0]).toHaveProperty('name');
      expect(methods[0]).toHaveProperty('type');
    });

    it('should handle API errors', async () => {
      // Setup MSW handler for server failure
      mockServerFailureForGetPaymentMethods();

      await expect(paymentService.getPaymentMethods()).rejects.toThrow();
    });
  });

  describe('processPayment', () => {
    const paymentRequest: PaymentRequest = {
      amount: 100000,
      currency: 'VND',
      methodId: 'CREDIT_CARD',
    };

    it('should process payment successfully', async () => {
      // Setup MSW handler for successful response
      mockResponseForProcessPayment();

      const result = await paymentService.processPayment(paymentRequest);

      expect(result).toHaveProperty('transactionId');
      expect(result).toHaveProperty('status');
      expect(result).toHaveProperty('message');
    });

    it('should handle payment processing errors', async () => {
      // Setup MSW handler for error response
      mockErrorResponseForProcessPayment();

      await expect(paymentService.processPayment(paymentRequest)).rejects.toThrow();
    });
  });
});
