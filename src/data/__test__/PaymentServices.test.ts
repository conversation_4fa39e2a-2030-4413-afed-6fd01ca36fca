import {describe, it, expect, beforeEach, afterEach, jest} from '@jest/globals';
import {
  mockResponseForGetPaymentMethods,
  mockResponseForGetBillProviders,
  mockResponseForValidateBill,
  mockResponseForProcessPayment,
  mockResponseForGetTransactionHistory,
  mockResponseForGetSourceAccounts,
  mockResponseForGetContacts,
  mockResponseForSaveContact,
  mockResponseForValidateTransfer,
  mockResponseForProcessTransfer,
} from '../../../__mocks__/service-apis';

// Mock service classes for demonstration
class PaymentService {
  async getPaymentMethods() {
    const response = await fetch('https://gateway.msb.com.vn/api/payment/client-api/v1/payment-methods');
    return response.json();
  }

  async getBillProviders() {
    const response = await fetch('https://gateway.msb.com.vn/api/payment/client-api/v1/bill-pay/providers');
    return response.json();
  }

  async validateBill(providerId: string, customerCode: string) {
    const response = await fetch('https://gateway.msb.com.vn/api/payment/client-api/v1/bill-pay/validate', {
      method: 'POST',
      headers: {'Content-Type': 'application/json'},
      body: JSON.stringify({providerId, customerCode}),
    });
    return response.json();
  }

  async processPayment(paymentData: any) {
    const response = await fetch('https://gateway.msb.com.vn/api/payment/client-api/v1/process', {
      method: 'POST',
      headers: {'Content-Type': 'application/json'},
      body: JSON.stringify(paymentData),
    });
    return response.json();
  }

  async getTransactionHistory() {
    const response = await fetch('https://gateway.msb.com.vn/api/payment/client-api/v1/payment/transactions');
    return response.json();
  }

  async getSourceAccounts() {
    const response = await fetch('https://gateway.msb.com.vn/api/payment/client-api/v1/arrangement/accounts');
    return response.json();
  }

  async getContacts() {
    const response = await fetch('https://gateway.msb.com.vn/api/payment/client-api/v1/address-book/contacts');
    return response.json();
  }

  async saveContact(contactData: any) {
    const response = await fetch('https://gateway.msb.com.vn/api/payment/client-api/v1/address-book/contacts', {
      method: 'POST',
      headers: {'Content-Type': 'application/json'},
      body: JSON.stringify(contactData),
    });
    return response.json();
  }

  async validateTransfer(accountNumber: string, bankCode: string) {
    const response = await fetch('https://gateway.msb.com.vn/api/payment/client-api/v1/transfer/validate', {
      method: 'POST',
      headers: {'Content-Type': 'application/json'},
      body: JSON.stringify({accountNumber, bankCode}),
    });
    return response.json();
  }

  async processTransfer(transferData: any) {
    const response = await fetch('https://gateway.msb.com.vn/api/payment/client-api/v1/transfer/process', {
      method: 'POST',
      headers: {'Content-Type': 'application/json'},
      body: JSON.stringify(transferData),
    });
    return response.json();
  }
}

describe('PaymentServices Integration Tests', () => {
  let paymentService: PaymentService;

  beforeEach(() => {
    paymentService = new PaymentService();
    jest.clearAllMocks();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('Payment Methods', () => {
    it('should fetch payment methods successfully', async () => {
      mockResponseForGetPaymentMethods();

      const result = await paymentService.getPaymentMethods();

      expect(result.success).toBe(true);
      expect(Array.isArray(result.data.methods)).toBe(true);
      expect(result.data.methods.length).toBeGreaterThan(0);
      expect(result.data.methods[0]).toHaveProperty('id');
      expect(result.data.methods[0]).toHaveProperty('name');
      expect(result.data.methods[0]).toHaveProperty('type');
    });
  });

  describe('Bill Payment Flow', () => {
    it('should complete full bill payment flow', async () => {
      // 1. Get bill providers
      mockResponseForGetBillProviders();
      const providers = await paymentService.getBillProviders();
      expect(providers).toBeDefined();
      expect(Array.isArray(providers)).toBe(true);

      // 2. Validate bill
      mockResponseForValidateBill();
      const validation = await paymentService.validateBill('evn', '*********');
      expect(validation.success).toBe(true);
      expect(validation.data.isValid).toBe(true);
      expect(validation.data.customerInfo).toBeDefined();

      // 3. Process payment
      mockResponseForProcessPayment();
      const payment = await paymentService.processPayment({
        amount: validation.data.customerInfo.billAmount,
        providerId: 'evn',
        customerCode: '*********',
        paymentMethod: 'bank_transfer',
      });
      expect(payment.success).toBe(true);
      expect(payment.data.status).toBe('SUCCESS');
      expect(payment.data.transactionId).toBeDefined();
    });
  });

  describe('Transfer Flow', () => {
    it('should complete full transfer flow', async () => {
      // 1. Get source accounts
      mockResponseForGetSourceAccounts();
      const accounts = await paymentService.getSourceAccounts();
      expect(accounts.success).toBe(true);
      expect(Array.isArray(accounts.data.accounts)).toBe(true);
      expect(accounts.data.accounts.length).toBeGreaterThan(0);

      // 2. Validate transfer account
      mockResponseForValidateTransfer();
      const validation = await paymentService.validateTransfer('************', 'VCB');
      expect(validation.success).toBe(true);
      expect(validation.data.isValid).toBe(true);
      expect(validation.data.accountName).toBeDefined();

      // 3. Process transfer
      mockResponseForProcessTransfer();
      const transfer = await paymentService.processTransfer({
        amount: 500000,
        sourceAccountNumber: accounts.data.accounts[0].accountNumber,
        destinationAccountNumber: '************',
        destinationBankCode: 'VCB',
        description: 'Transfer to friend',
      });
      expect(transfer.success).toBe(true);
      expect(transfer.data.status).toBe('SUCCESS');
      expect(transfer.data.transactionId).toBeDefined();
    });
  });

  describe('Contact Management', () => {
    it('should manage contacts successfully', async () => {
      // 1. Get existing contacts
      mockResponseForGetContacts();
      const contacts = await paymentService.getContacts();
      expect(contacts.success).toBe(true);
      expect(Array.isArray(contacts.data.contacts)).toBe(true);

      // 2. Save new contact
      mockResponseForSaveContact();
      const newContact = await paymentService.saveContact({
        name: 'NEW CONTACT',
        type: 'TRANSFER',
        accountNumber: '************',
        bankCode: 'MSB',
      });
      expect(newContact.success).toBe(true);
      expect(newContact.data.contact).toBeDefined();
      expect(newContact.data.contact.name).toBe('NEW CONTACT');
    });
  });

  describe('Transaction History', () => {
    it('should fetch transaction history successfully', async () => {
      mockResponseForGetTransactionHistory();

      const result = await paymentService.getTransactionHistory();

      expect(result.success).toBe(true);
      expect(Array.isArray(result.data.transactions)).toBe(true);
      expect(result.data.pagination).toBeDefined();
      expect(result.data.pagination).toHaveProperty('page');
      expect(result.data.pagination).toHaveProperty('limit');
      expect(result.data.pagination).toHaveProperty('total');
    });
  });

  describe('Data Structure Validation', () => {
    it('should have consistent data structures across all APIs', async () => {
      // Test payment methods structure
      mockResponseForGetPaymentMethods();
      const paymentMethods = await paymentService.getPaymentMethods();
      expect(paymentMethods).toHaveProperty('success');
      expect(paymentMethods).toHaveProperty('data');
      expect(paymentMethods).toHaveProperty('message');

      // Test bill providers structure
      mockResponseForGetBillProviders();
      const billProviders = await paymentService.getBillProviders();
      expect(Array.isArray(billProviders)).toBe(true);
      if (billProviders.length > 0) {
        expect(billProviders[0]).toHaveProperty('id');
        expect(billProviders[0]).toHaveProperty('name');
        expect(billProviders[0]).toHaveProperty('categoryId');
      }

      // Test source accounts structure
      mockResponseForGetSourceAccounts();
      const sourceAccounts = await paymentService.getSourceAccounts();
      expect(sourceAccounts.success).toBe(true);
      expect(sourceAccounts.data.accounts[0]).toHaveProperty('accountNumber');
      expect(sourceAccounts.data.accounts[0]).toHaveProperty('accountName');
      expect(sourceAccounts.data.accounts[0]).toHaveProperty('balance');
      expect(sourceAccounts.data.accounts[0]).toHaveProperty('limits');
    });
  });
});
