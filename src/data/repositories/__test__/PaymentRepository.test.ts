import {describe, it, expect, jest, beforeEach} from '@jest/globals';
import {PaymentRepository} from '../PaymentRepository';
import {IPaymentDataSource} from '../../datasources/IPaymentDataSource';
import {ValidateRequest} from '../../models/validate/ValidateRequest';
import {ValidateResponse} from '../../models/validate/ValidateResponse';
import {ValidateModel} from '../../../domain/entities/validate/ValidateModel';
import {BaseResponse} from '../../../core/BaseResponse';
import {CustomError, ErrorCategory} from '../../../core/MSBCustomError';

// Mock dependencies
jest.mock('../../../utils/HandleData', () => ({
  handleData: jest.fn(),
}));

jest.mock('../../mappers/validate/ValidateMapper', () => ({
  mapValidateResponseToModel: jest.fn(),
}));

import {handleData} from '../../../utils/HandleData';
import {mapValidateResponseToModel} from '../../mappers/validate/ValidateMapper';

const mockHandleData = handleData as jest.MockedFunction<typeof handleData>;
const mockMapValidateResponseToModel = mapValidateResponseToModel as jest.MockedFunction<
  typeof mapValidateResponseToModel
>;

describe('PaymentRepository', () => {
  let paymentRepository: PaymentRepository;
  let mockDataSource: jest.Mocked<IPaymentDataSource>;

  beforeEach(() => {
    jest.clearAllMocks();

    // Create mock data source
    mockDataSource = {
      validate: jest.fn(),
    };

    paymentRepository = new PaymentRepository(mockDataSource);
  });

  describe('constructor', () => {
    it('should create PaymentRepository with data source', () => {
      expect(paymentRepository).toBeInstanceOf(PaymentRepository);
      expect(paymentRepository['remoteDataSource']).toBe(mockDataSource);
    });
  });

  describe('validate', () => {
    const mockRequest: ValidateRequest = {
      accountNumber: '**********',
      amount: 100000,
      currency: 'VND',
    };

    const mockResponse: BaseResponse<ValidateResponse> = {
      transactionId: 'TXN123',
      status: 'SUCCESS',
      message: 'Validation successful',
    };

    const mockModel: ValidateModel = {
      transactionId: 'TXN123',
      isValid: true,
      validationMessage: 'Validation successful',
    };

    it('should call data source validate method with correct request', async () => {
      mockDataSource.validate.mockResolvedValue(mockResponse);
      mockHandleData.mockResolvedValue(mockModel);

      await paymentRepository.validate(mockRequest);

      expect(mockDataSource.validate).toHaveBeenCalledWith(mockRequest);
      expect(mockDataSource.validate).toHaveBeenCalledTimes(1);
    });

    it('should call handleData with data source promise and mapper', async () => {
      const dataSourcePromise = Promise.resolve(mockResponse);
      mockDataSource.validate.mockReturnValue(dataSourcePromise);
      mockHandleData.mockResolvedValue(mockModel);

      await paymentRepository.validate(mockRequest);

      expect(mockHandleData).toHaveBeenCalledWith(dataSourcePromise, mapValidateResponseToModel);
      expect(mockHandleData).toHaveBeenCalledTimes(1);
    });

    it('should return mapped model from handleData', async () => {
      mockDataSource.validate.mockResolvedValue(mockResponse);
      mockHandleData.mockResolvedValue(mockModel);

      const result = await paymentRepository.validate(mockRequest);

      expect(result).toBe(mockModel);
    });

    it('should handle successful validation', async () => {
      const successResponse: BaseResponse<ValidateResponse> = {
        transactionId: 'TXN456',
        status: 'SUCCESS',
        message: 'Account validation successful',
      };

      const successModel: ValidateModel = {
        transactionId: 'TXN456',
        isValid: true,
        validationMessage: 'Account validation successful',
      };

      mockDataSource.validate.mockResolvedValue(successResponse);
      mockHandleData.mockResolvedValue(successModel);

      const result = await paymentRepository.validate(mockRequest);

      expect(result).toEqual(successModel);
      expect(result.isValid).toBe(true);
      expect(result.transactionId).toBe('TXN456');
    });

    it('should handle validation errors from data source', async () => {
      const errorMessage = 'Invalid account number';

      mockDataSource.validate.mockImplementation(() => {
        throw new Error(errorMessage);
      });
      mockHandleData.mockImplementation(() => {
        throw new Error(errorMessage);
      });

      await expect(paymentRepository.validate(mockRequest)).rejects.toThrow(errorMessage);
      expect(mockDataSource.validate).toHaveBeenCalledWith(mockRequest);
    });

    it('should handle errors from handleData', async () => {
      const error = new CustomError(
        'MAPPING_ERROR',
        ErrorCategory.SYSTEM,
        'Mapping Error',
        'Failed to map response to model',
        false,
      );

      mockDataSource.validate.mockResolvedValue(mockResponse);
      mockHandleData.mockRejectedValue(error);

      await expect(paymentRepository.validate(mockRequest)).rejects.toThrow(error);
    });

    it('should handle different request types', async () => {
      const requests = [
        {
          accountNumber: '**********',
          amount: 50000,
          currency: 'VND',
        },
        {
          accountNumber: '**********',
          amount: 200000,
          currency: 'USD',
        },
        {
          accountNumber: '**********',
          amount: 1000000,
          currency: 'VND',
        },
      ];

      for (const request of requests) {
        mockDataSource.validate.mockResolvedValue(mockResponse);
        mockHandleData.mockResolvedValue(mockModel);

        await paymentRepository.validate(request);

        expect(mockDataSource.validate).toHaveBeenCalledWith(request);
      }

      expect(mockDataSource.validate).toHaveBeenCalledTimes(3);
    });

    it('should handle response with errors', async () => {
      const errorResponse: BaseResponse<ValidateResponse> = {
        errors: [
          {
            key: 'INVALID_ACCOUNT',
            message: 'Account number is invalid',
            context: ['accountNumber'],
          },
        ],
      };

      const error = new CustomError(
        'INVALID_ACCOUNT',
        ErrorCategory.VALIDATION,
        'Invalid Account',
        'Account number is invalid',
        false,
      );

      mockDataSource.validate.mockResolvedValue(errorResponse);
      mockHandleData.mockRejectedValue(error);

      await expect(paymentRepository.validate(mockRequest)).rejects.toThrow(error);
    });

    it('should handle null response from data source', async () => {
      const error = new CustomError(
        'NULL_RESPONSE',
        ErrorCategory.SYSTEM,
        'Null Response',
        'Data source returned null response',
        true,
      );

      mockDataSource.validate.mockResolvedValue(null as any);
      mockHandleData.mockRejectedValue(error);

      await expect(paymentRepository.validate(mockRequest)).rejects.toThrow(error);
    });

    it('should handle undefined response from data source', async () => {
      const error = new CustomError(
        'UNDEFINED_RESPONSE',
        ErrorCategory.SYSTEM,
        'Undefined Response',
        'Data source returned undefined response',
        true,
      );

      mockDataSource.validate.mockResolvedValue(undefined as any);
      mockHandleData.mockRejectedValue(error);

      await expect(paymentRepository.validate(mockRequest)).rejects.toThrow(error);
    });
  });

  describe('Integration scenarios', () => {
    it('should handle complete validation flow', async () => {
      const request: ValidateRequest = {
        accountNumber: '**********',
        amount: 500000,
        currency: 'VND',
      };

      const response: BaseResponse<ValidateResponse> = {
        transactionId: 'TXN789',
        status: 'SUCCESS',
        message: 'Validation completed successfully',
      };

      const expectedModel: ValidateModel = {
        transactionId: 'TXN789',
        isValid: true,
        validationMessage: 'Validation completed successfully',
      };

      mockDataSource.validate.mockResolvedValue(response);
      mockHandleData.mockResolvedValue(expectedModel);

      const result = await paymentRepository.validate(request);

      expect(mockDataSource.validate).toHaveBeenCalledWith(request);
      expect(mockHandleData).toHaveBeenCalledWith(expect.any(Promise), mapValidateResponseToModel);
      expect(result).toEqual(expectedModel);
    });

    it('should maintain data source interface contract', async () => {
      const request: ValidateRequest = {
        accountNumber: '**********',
        amount: 100000,
        currency: 'VND',
      };

      // Verify that the repository calls the data source with the exact interface
      mockDataSource.validate.mockResolvedValue({} as any);
      mockHandleData.mockResolvedValue({} as any);

      await paymentRepository.validate(request);

      // Verify the data source method signature
      expect(mockDataSource.validate).toHaveBeenCalledWith(
        expect.objectContaining({
          accountNumber: expect.any(String),
          amount: expect.any(Number),
          currency: expect.any(String),
        }),
      );
    });
  });

  describe('Error handling edge cases', () => {
    it('should propagate network errors from data source', async () => {
      const errorMessage = 'Unable to connect to server';

      mockDataSource.validate.mockImplementation(() => {
        throw new Error(errorMessage);
      });
      mockHandleData.mockImplementation(() => {
        throw new Error(errorMessage);
      });

      await expect(
        paymentRepository.validate({
          accountNumber: '**********',
          amount: 100000,
          currency: 'VND',
        }),
      ).rejects.toThrow(errorMessage);
    });

    it('should propagate API errors from data source', async () => {
      const errorMessage = 'Internal server error';

      mockDataSource.validate.mockImplementation(() => {
        throw new Error(errorMessage);
      });
      mockHandleData.mockImplementation(() => {
        throw new Error(errorMessage);
      });

      await expect(
        paymentRepository.validate({
          accountNumber: '**********',
          amount: 100000,
          currency: 'VND',
        }),
      ).rejects.toThrow(errorMessage);
    });

    it('should handle business logic errors', async () => {
      const businessError = new CustomError(
        'INSUFFICIENT_FUNDS',
        ErrorCategory.BUSINESS,
        'Insufficient Funds',
        'Account does not have sufficient funds',
        false,
      );

      mockDataSource.validate.mockResolvedValue({} as any);
      mockHandleData.mockRejectedValue(businessError);

      await expect(
        paymentRepository.validate({
          accountNumber: '**********',
          amount: 100000,
          currency: 'VND',
        }),
      ).rejects.toThrow(businessError);
    });
  });
});
