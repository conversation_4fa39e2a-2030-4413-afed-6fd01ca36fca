import {describe, it, expect, jest, beforeEach} from '@jest/globals';
import {ArrangementRepository} from '../ArrangementRepository';
import {IArrangementDataSource} from '../../datasources/IArrangementDataSource';
import {SourceAccountListRequest} from '../../models/source-account-list/SourceAccountListRequest';
import {SourceAccountListResponse} from '../../models/source-account-list/SourceAccountListResponse';
import {SourceAccountListModel} from '../../../domain/entities/source-account-list/SourceAccountListModel';
import {BaseResponse} from '../../../core/BaseResponse';
import {CustomError, ErrorCategory} from '../../../core/MSBCustomError';

// Mock dependencies
jest.mock('../../../utils/HandleData', () => ({
  handleData: jest.fn(),
}));

jest.mock('../../mappers/source-account-list/SourceAccountListMapper', () => ({
  mapSourceAccountListResponseToModel: jest.fn(),
}));

import {handleData} from '../../../utils/HandleData';
import {mapSourceAccountListResponseToModel} from '../../mappers/source-account-list/SourceAccountListMapper';

const mockHandleData = handleData as jest.MockedFunction<typeof handleData>;
const mockMapSourceAccountListResponseToModel = mapSourceAccountListResponseToModel as jest.MockedFunction<typeof mapSourceAccountListResponseToModel>;

describe('ArrangementRepository', () => {
  let arrangementRepository: ArrangementRepository;
  let mockDataSource: jest.Mocked<IArrangementDataSource>;

  beforeEach(() => {
    jest.clearAllMocks();

    // Create mock data source
    mockDataSource = {
      sourceAccountList: jest.fn(),
    };

    arrangementRepository = new ArrangementRepository(mockDataSource);
  });

  describe('constructor', () => {
    it('should create ArrangementRepository with data source', () => {
      expect(arrangementRepository).toBeInstanceOf(ArrangementRepository);
      expect(arrangementRepository['remoteDataSource']).toBe(mockDataSource);
    });
  });

  describe('sourceAccountList', () => {
    const mockRequest: SourceAccountListRequest = {
      customerId: 'CUST123',
      accountType: 'SAVINGS',
    };

    const mockResponse: BaseResponse<SourceAccountListResponse> = {
      accounts: [
        {
          accountId: 'ACC001',
          accountNumber: '**********',
          accountName: 'Primary Savings',
          balance: 1000000,
          currency: 'VND',
        },
        {
          accountId: 'ACC002',
          accountNumber: '**********',
          accountName: 'Secondary Savings',
          balance: 500000,
          currency: 'VND',
        },
      ],
      totalCount: 2,
    };

    const mockModel: SourceAccountListModel = {
      accounts: [
        {
          id: 'ACC001',
          number: '**********',
          name: 'Primary Savings',
          availableBalance: 1000000,
          currencyCode: 'VND',
        },
        {
          id: 'ACC002',
          number: '**********',
          name: 'Secondary Savings',
          availableBalance: 500000,
          currencyCode: 'VND',
        },
      ],
      total: 2,
    };

    it('should call data source sourceAccountList method with correct request', async () => {
      mockDataSource.sourceAccountList.mockResolvedValue(mockResponse);
      mockHandleData.mockResolvedValue(mockModel);

      await arrangementRepository.sourceAccountList(mockRequest);

      expect(mockDataSource.sourceAccountList).toHaveBeenCalledWith(mockRequest);
      expect(mockDataSource.sourceAccountList).toHaveBeenCalledTimes(1);
    });

    it('should call handleData with data source promise and mapper', async () => {
      const dataSourcePromise = Promise.resolve(mockResponse);
      mockDataSource.sourceAccountList.mockReturnValue(dataSourcePromise);
      mockHandleData.mockResolvedValue(mockModel);

      await arrangementRepository.sourceAccountList(mockRequest);

      expect(mockHandleData).toHaveBeenCalledWith(
        dataSourcePromise,
        mapSourceAccountListResponseToModel
      );
      expect(mockHandleData).toHaveBeenCalledTimes(1);
    });

    it('should return mapped model from handleData', async () => {
      mockDataSource.sourceAccountList.mockResolvedValue(mockResponse);
      mockHandleData.mockResolvedValue(mockModel);

      const result = await arrangementRepository.sourceAccountList(mockRequest);

      expect(result).toBe(mockModel);
    });

    it('should handle successful account list retrieval', async () => {
      const successResponse: BaseResponse<SourceAccountListResponse> = {
        accounts: [
          {
            accountId: 'ACC003',
            accountNumber: '**********',
            accountName: 'Business Account',
            balance: 2000000,
            currency: 'VND',
          },
        ],
        totalCount: 1,
      };

      const successModel: SourceAccountListModel = {
        accounts: [
          {
            id: 'ACC003',
            number: '**********',
            name: 'Business Account',
            availableBalance: 2000000,
            currencyCode: 'VND',
          },
        ],
        total: 1,
      };

      mockDataSource.sourceAccountList.mockResolvedValue(successResponse);
      mockHandleData.mockResolvedValue(successModel);

      const result = await arrangementRepository.sourceAccountList(mockRequest);

      expect(result).toEqual(successModel);
      expect(result.accounts).toHaveLength(1);
      expect(result.total).toBe(1);
    });

    it('should handle empty account list', async () => {
      const emptyResponse: BaseResponse<SourceAccountListResponse> = {
        accounts: [],
        totalCount: 0,
      };

      const emptyModel: SourceAccountListModel = {
        accounts: [],
        total: 0,
      };

      mockDataSource.sourceAccountList.mockResolvedValue(emptyResponse);
      mockHandleData.mockResolvedValue(emptyModel);

      const result = await arrangementRepository.sourceAccountList(mockRequest);

      expect(result).toEqual(emptyModel);
      expect(result.accounts).toHaveLength(0);
      expect(result.total).toBe(0);
    });

    it('should handle errors from data source', async () => {
      const error = new CustomError(
        'ACCOUNT_LIST_ERROR',
        ErrorCategory.API,
        'Account List Error',
        'Failed to retrieve account list',
        true
      );

      mockDataSource.sourceAccountList.mockRejectedValue(error);

      await expect(arrangementRepository.sourceAccountList(mockRequest)).rejects.toThrow(error);
      expect(mockDataSource.sourceAccountList).toHaveBeenCalledWith(mockRequest);
    });

    it('should handle errors from handleData', async () => {
      const error = new CustomError(
        'MAPPING_ERROR',
        ErrorCategory.SYSTEM,
        'Mapping Error',
        'Failed to map response to model',
        false
      );

      mockDataSource.sourceAccountList.mockResolvedValue(mockResponse);
      mockHandleData.mockRejectedValue(error);

      await expect(arrangementRepository.sourceAccountList(mockRequest)).rejects.toThrow(error);
    });

    it('should handle different request types', async () => {
      const requests = [
        {
          customerId: 'CUST001',
          accountType: 'CHECKING',
        },
        {
          customerId: 'CUST002',
          accountType: 'SAVINGS',
        },
        {
          customerId: 'CUST003',
          accountType: 'BUSINESS',
        },
      ];

      for (const request of requests) {
        mockDataSource.sourceAccountList.mockResolvedValue(mockResponse);
        mockHandleData.mockResolvedValue(mockModel);

        await arrangementRepository.sourceAccountList(request);

        expect(mockDataSource.sourceAccountList).toHaveBeenCalledWith(request);
      }

      expect(mockDataSource.sourceAccountList).toHaveBeenCalledTimes(3);
    });

    it('should handle response with errors', async () => {
      const errorResponse: BaseResponse<SourceAccountListResponse> = {
        errors: [
          {
            key: 'INVALID_CUSTOMER',
            message: 'Customer ID is invalid',
            context: ['customerId'],
          },
        ],
      };

      const error = new CustomError(
        'INVALID_CUSTOMER',
        ErrorCategory.VALIDATION,
        'Invalid Customer',
        'Customer ID is invalid',
        false
      );

      mockDataSource.sourceAccountList.mockResolvedValue(errorResponse);
      mockHandleData.mockRejectedValue(error);

      await expect(arrangementRepository.sourceAccountList(mockRequest)).rejects.toThrow(error);
    });

    it('should handle null response from data source', async () => {
      const error = new CustomError(
        'NULL_RESPONSE',
        ErrorCategory.SYSTEM,
        'Null Response',
        'Data source returned null response',
        true
      );

      mockDataSource.sourceAccountList.mockResolvedValue(null as any);
      mockHandleData.mockRejectedValue(error);

      await expect(arrangementRepository.sourceAccountList(mockRequest)).rejects.toThrow(error);
    });

    it('should handle undefined response from data source', async () => {
      const error = new CustomError(
        'UNDEFINED_RESPONSE',
        ErrorCategory.SYSTEM,
        'Undefined Response',
        'Data source returned undefined response',
        true
      );

      mockDataSource.sourceAccountList.mockResolvedValue(undefined as any);
      mockHandleData.mockRejectedValue(error);

      await expect(arrangementRepository.sourceAccountList(mockRequest)).rejects.toThrow(error);
    });
  });

  describe('Integration scenarios', () => {
    it('should handle complete source account list flow', async () => {
      const request: SourceAccountListRequest = {
        customerId: 'CUST999',
        accountType: 'ALL',
      };

      const response: BaseResponse<SourceAccountListResponse> = {
        accounts: [
          {
            accountId: 'ACC999',
            accountNumber: '**********',
            accountName: 'Premium Account',
            balance: 5000000,
            currency: 'VND',
          },
        ],
        totalCount: 1,
      };

      const expectedModel: SourceAccountListModel = {
        accounts: [
          {
            id: 'ACC999',
            number: '**********',
            name: 'Premium Account',
            availableBalance: 5000000,
            currencyCode: 'VND',
          },
        ],
        total: 1,
      };

      mockDataSource.sourceAccountList.mockResolvedValue(response);
      mockHandleData.mockResolvedValue(expectedModel);

      const result = await arrangementRepository.sourceAccountList(request);

      expect(mockDataSource.sourceAccountList).toHaveBeenCalledWith(request);
      expect(mockHandleData).toHaveBeenCalledWith(
        expect.any(Promise),
        mapSourceAccountListResponseToModel
      );
      expect(result).toEqual(expectedModel);
    });

    it('should maintain data source interface contract', async () => {
      const request: SourceAccountListRequest = {
        customerId: 'CUST123',
        accountType: 'SAVINGS',
      };

      // Verify that the repository calls the data source with the exact interface
      mockDataSource.sourceAccountList.mockResolvedValue({} as any);
      mockHandleData.mockResolvedValue({} as any);

      await arrangementRepository.sourceAccountList(request);

      // Verify the data source method signature
      expect(mockDataSource.sourceAccountList).toHaveBeenCalledWith(
        expect.objectContaining({
          customerId: expect.any(String),
          accountType: expect.any(String),
        })
      );
    });
  });

  describe('Error handling edge cases', () => {
    it('should propagate network errors from data source', async () => {
      const networkError = new CustomError(
        'NETWORK_ERROR',
        ErrorCategory.NETWORK,
        'Network Error',
        'Unable to connect to server',
        true
      );

      mockDataSource.sourceAccountList.mockRejectedValue(networkError);

      await expect(arrangementRepository.sourceAccountList({
        customerId: 'CUST123',
        accountType: 'SAVINGS',
      })).rejects.toThrow(networkError);
    });

    it('should propagate API errors from data source', async () => {
      const apiError = new CustomError(
        'API_ERROR',
        ErrorCategory.API,
        'API Error',
        'Internal server error',
        false
      );

      mockDataSource.sourceAccountList.mockRejectedValue(apiError);

      await expect(arrangementRepository.sourceAccountList({
        customerId: 'CUST123',
        accountType: 'SAVINGS',
      })).rejects.toThrow(apiError);
    });

    it('should handle business logic errors', async () => {
      const businessError = new CustomError(
        'CUSTOMER_NOT_FOUND',
        ErrorCategory.BUSINESS,
        'Customer Not Found',
        'Customer does not exist in the system',
        false
      );

      mockDataSource.sourceAccountList.mockResolvedValue({} as any);
      mockHandleData.mockRejectedValue(businessError);

      await expect(arrangementRepository.sourceAccountList({
        customerId: 'INVALID_CUSTOMER',
        accountType: 'SAVINGS',
      })).rejects.toThrow(businessError);
    });
  });
});
