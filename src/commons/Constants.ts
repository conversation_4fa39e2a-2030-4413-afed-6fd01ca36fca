export const PAYMENT_TYPE = {
  QR_PAYMENT: 'QR_PAYMENT',
  BILLING_ACCOUNT: 'BILLING_ACCOUNT',
  BILLING_CREDIT: 'BILLING_CREDIT',
  TOPUP_ACCOUNT: 'TOPUP_ACCOUNT',
  TOPUP_CREDIT: 'TOPUP_CREDIT',
};
export const A05_CODE = {
  RED: 'RED',
  YELLOW: 'YELLOW',
  WHITE: 'WHITE',
  UNKNOWN: 'UNKNOWN',
};
export const PAYMENT_ORDER_STATUS = {
  PROCESSED: 'PROCESSED',
  REJECTED: 'REJECTED',
  ACCEPTED: 'ACCEPTED',
};
// export const CONTACT_TAB = {
//   MONEY: 'MONEY',
//   BOTTOMSHEET: 'BOTTOMSHEET',
// };

export const DIALOG_CODE = {
  USER_NOT_FOUND: 'USER_NOT_FOUND',
  USER_NOT_INVALID: 'USER_NOT_INVALID',
  USER_NOT_FOUND_NAPAS: 'USER_NOT_FOUND_NAPAS',
  ONLY_CITAD: 'ONLY_CITAD',
  INTERRUPT_ALL: 'INTERRUPT_ALL',
  INTERRUPT_247: 'INTERRUPT_247',
  INTERRUPT_247_IN_WORKING_TIME: 'INTERRUPT_247_IN_WORKING_TIME',
  INTERRUPT_247_OUT_WORKING_TIME: 'INTERRUPT_247_OUT_WORKING_TIME',
  INTERRUPT_COMMON: 'INTERRUPT_COMMON',
  ERROR_401: 'ERROR_401',
  DUPLICATE_ACCOUNT_SOURCE: 'DUPLICATE_ACCOUNT_SOURCE',
  DUPLICATE_CONTACT: 'DUPLICATE_CONTACT',
  ONE_ACCOUNT_SOURCE: 'ONE_ACCOUNT_SOURCE',
  ACCOUNT_IS_CARD: 'ACCOUNT_IS_CARD',
  ERROR_ACCOUNT_SOURCE: 'ERROR_ACCOUNT_SOURCE',
};

export const ERROR_KEY = {
  BPE0006: 'BPE-0006', // Giấy tờ tuỳ thân
  BPE0005: 'BPE-0005', // Sinh trắc học
  BPE0004: 'BPE-0004', // Gói truy vấn
  BMS009: 'BMS-0009', // Napas gián đoạn
  BMS010: 'BMS-0010', // Khong tim thay nguoi nhan (Nội bộ)
  BMS014: 'BMS-0010', // Khong tim thay nguoi nhan (Nội bộ)
  BMS011: 'BMS-0011', // Khong tim thay nguoi nhan (Napas)
  BMS0017: 'BMS-0017',
  FTES0006: 'FTES-0006', // Citad gián đoạn
};

export const TRANSFER_RESULT_ACTION = {
  TRANSFER_SHARE: 'TRANSFER_SHARE',
  TRANSER_SAVE_TEMPLATE: 'TRANSER_SAVE_TEMPLATE',
  TRANSER_SAVE_BENEFICIARY: 'TRANSER_SAVE_BENEFICIARY',
  TRANSFER_MANAGER: 'TRANSFER_MANAGER',
  TRANSFER_SUPPORT: 'TRANSFER_SUPPORT',
};

export const QR_CODE_ACTION = {
  SHARE: 'SHARE',
  SAVE_IMAGE: 'SAVE_IMAGE',
  THEME_CHANGE: 'THEME_CHANGE',
};

export const MSB_BANK_CODE_NAPAS = '970426';

export const CONTACT_TYPE = {
  SOURCE_ACCOUNT: '1',
  FAVOURITE: '2',
  OTHER: '3',
};

export enum CommonState {
  SUCCESS = 'SUCCESS',
  ERROR = 'ERROR',
  RETRY = 'RETRY',
}

export enum ContactType {
  BILLPAY = 'billpay',
  ACCOUNT = 'account',
}

export enum CONTACT_GROUP_TYPE {
  PAYMENT = 'PAYMENT',
  TRANSFER = 'TRANSFER',
}
export enum ACCOUNT_TYPE {
  ACCT = 'ACCT',
  CREDIT = 'CREDIT',
}

export const VIETTEL_SERVICE_CODE = '810001'; //TODO: stupid solution check with back-end later
// eslint-disable-next-line @typescript-eslint/no-explicit-any
export type SafeAny = any;
