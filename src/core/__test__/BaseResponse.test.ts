import {describe, it, expect} from '@jest/globals';
import {BaseResponse, MSBError} from '../BaseResponse';

describe('BaseResponse', () => {
  describe('Type Definitions', () => {
    it('should create BaseResponse with data only', () => {
      const response: BaseResponse<{id: string; name: string}> = {
        id: '123',
        name: 'Test User',
      };

      expect(response.id).toBe('123');
      expect(response.name).toBe('Test User');
      expect(response.errors).toBeUndefined();
      expect(response.error).toBeUndefined();
    });

    it('should create BaseResponse with errors array', () => {
      const errors: MSBError[] = [
        {
          key: 'VALIDATION_ERROR',
          message: 'Invalid input data',
          context: ['field1', 'field2'],
        },
        {
          key: 'BUSINESS_ERROR',
          message: 'Business rule violation',
          context: ['rule1'],
        },
      ];

      const response: BaseResponse<any> = {
        errors,
      };

      expect(response.errors).toEqual(errors);
      expect(response.errors?.length).toBe(2);
      expect(response.errors?.[0].key).toBe('VALIDATION_ERROR');
      expect(response.errors?.[1].key).toBe('BUSINESS_ERROR');
    });

    it('should create BaseResponse with single error', () => {
      const error: MSBError = {
        key: 'NETWORK_ERROR',
        message: 'Connection timeout',
        context: ['timeout', '30s'],
      };

      const response: BaseResponse<any> = {
        error,
      };

      expect(response.error).toEqual(error);
      expect(response.error?.key).toBe('NETWORK_ERROR');
      expect(response.error?.message).toBe('Connection timeout');
      expect(response.error?.context).toEqual(['timeout', '30s']);
    });

    it('should create BaseResponse with null errors', () => {
      const response: BaseResponse<{success: boolean}> = {
        success: true,
        errors: null,
      };

      expect(response.success).toBe(true);
      expect(response.errors).toBeNull();
    });

    it('should create BaseResponse with undefined errors', () => {
      const response: BaseResponse<{success: boolean}> = {
        success: true,
        errors: undefined,
      };

      expect(response.success).toBe(true);
      expect(response.errors).toBeUndefined();
    });

    it('should create BaseResponse with both data and errors', () => {
      const response: BaseResponse<{partialData: string}> = {
        partialData: 'Some data was processed',
        errors: [
          {
            key: 'PARTIAL_ERROR',
            message: 'Some operations failed',
            context: ['operation1'],
          },
        ],
      };

      expect(response.partialData).toBe('Some data was processed');
      expect(response.errors).toBeDefined();
      expect(response.errors?.length).toBe(1);
    });
  });

  describe('MSBError', () => {
    it('should create MSBError with all required fields', () => {
      const error: MSBError = {
        key: 'TEST_ERROR',
        message: 'This is a test error',
        context: ['test', 'unit'],
      };

      expect(error.key).toBe('TEST_ERROR');
      expect(error.message).toBe('This is a test error');
      expect(error.context).toEqual(['test', 'unit']);
    });

    it('should create MSBError with empty context', () => {
      const error: MSBError = {
        key: 'SIMPLE_ERROR',
        message: 'Simple error message',
        context: [],
      };

      expect(error.key).toBe('SIMPLE_ERROR');
      expect(error.message).toBe('Simple error message');
      expect(error.context).toEqual([]);
    });

    it('should create MSBError with single context item', () => {
      const error: MSBError = {
        key: 'FIELD_ERROR',
        message: 'Field validation failed',
        context: ['username'],
      };

      expect(error.key).toBe('FIELD_ERROR');
      expect(error.message).toBe('Field validation failed');
      expect(error.context).toEqual(['username']);
    });

    it('should create MSBError with multiple context items', () => {
      const error: MSBError = {
        key: 'COMPLEX_ERROR',
        message: 'Multiple validation errors',
        context: ['field1', 'field2', 'field3', 'validation_rule'],
      };

      expect(error.key).toBe('COMPLEX_ERROR');
      expect(error.message).toBe('Multiple validation errors');
      expect(error.context).toEqual(['field1', 'field2', 'field3', 'validation_rule']);
    });
  });

  describe('Generic Type Support', () => {
    interface UserData {
      id: number;
      username: string;
      email: string;
    }

    it('should support custom data types', () => {
      const response: BaseResponse<UserData> = {
        id: 1,
        username: 'testuser',
        email: '<EMAIL>',
      };

      expect(response.id).toBe(1);
      expect(response.username).toBe('testuser');
      expect(response.email).toBe('<EMAIL>');
    });

    interface ApiResponse {
      success: boolean;
      data: UserData[];
      total: number;
    }

    it('should support nested data structures', () => {
      const response: BaseResponse<ApiResponse> = {
        success: true,
        data: [
          {id: 1, username: 'user1', email: '<EMAIL>'},
          {id: 2, username: 'user2', email: '<EMAIL>'},
        ],
        total: 2,
      };

      expect(response.success).toBe(true);
      expect(response.data).toHaveLength(2);
      expect(response.total).toBe(2);
      expect(response.data[0].username).toBe('user1');
    });

    it('should support array data types', () => {
      const response: BaseResponse<string[]> = ['item1', 'item2', 'item3'];

      expect(Array.isArray(response)).toBe(true);
      expect(response).toHaveLength(3);
      expect(response[0]).toBe('item1');
    });

    it('should support primitive data types', () => {
      const stringResponse: BaseResponse<string> = 'Simple string response';
      const numberResponse: BaseResponse<number> = 42;
      const booleanResponse: BaseResponse<boolean> = true;

      expect(typeof stringResponse).toBe('string');
      expect(stringResponse).toBe('Simple string response');

      expect(typeof numberResponse).toBe('number');
      expect(numberResponse).toBe(42);

      expect(typeof booleanResponse).toBe('boolean');
      expect(booleanResponse).toBe(true);
    });
  });

  describe('Error Handling Scenarios', () => {
    it('should handle API error response', () => {
      const response: BaseResponse<any> = {
        errors: [
          {
            key: 'API_ERROR',
            message: 'Internal server error',
            context: ['500', 'server'],
          },
        ],
      };

      expect(response.errors).toBeDefined();
      expect(response.errors?.[0].key).toBe('API_ERROR');
    });

    it('should handle validation error response', () => {
      const response: BaseResponse<any> = {
        error: {
          key: 'VALIDATION_FAILED',
          message: 'Required fields are missing',
          context: ['name', 'email'],
        },
      };

      expect(response.error).toBeDefined();
      expect(response.error?.key).toBe('VALIDATION_FAILED');
      expect(response.error?.context).toContain('name');
      expect(response.error?.context).toContain('email');
    });

    it('should handle mixed success and error response', () => {
      const response: BaseResponse<{processed: number; failed: number}> = {
        processed: 8,
        failed: 2,
        errors: [
          {
            key: 'PARTIAL_FAILURE',
            message: 'Some items could not be processed',
            context: ['item9', 'item10'],
          },
        ],
      };

      expect(response.processed).toBe(8);
      expect(response.failed).toBe(2);
      expect(response.errors).toBeDefined();
      expect(response.errors?.[0].key).toBe('PARTIAL_FAILURE');
    });
  });
});
