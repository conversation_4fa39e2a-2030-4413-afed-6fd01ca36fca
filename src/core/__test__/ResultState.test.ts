import {describe, it, expect} from '@jest/globals';
import {ResultState} from '../ResultState';
import {CustomError, ErrorCategory} from '../MSBCustomError';

describe('ResultState', () => {
  describe('SUCCESS state', () => {
    it('should create SUCCESS state with data', () => {
      const data = {id: 1, name: 'Test User'};
      const result: ResultState<{id: number; name: string}> = {
        status: 'SUCCESS',
        data,
      };

      expect(result.status).toBe('SUCCESS');
      if (result.status === 'SUCCESS') {
        expect(result.data).toEqual(data);
        expect(result.data?.id).toBe(1);
        expect(result.data?.name).toBe('Test User');
      }
    });

    it('should create SUCCESS state with undefined data', () => {
      const result: ResultState<string> = {
        status: 'SUCCESS',
        data: undefined,
      };

      expect(result.status).toBe('SUCCESS');
      if (result.status === 'SUCCESS') {
        expect(result.data).toBeUndefined();
      }
    });

    it('should create SUCCESS state with null data', () => {
      const result: ResultState<any> = {
        status: 'SUCCESS',
        data: null,
      };

      expect(result.status).toBe('SUCCESS');
      if (result.status === 'SUCCESS') {
        expect(result.data).toBeNull();
      }
    });

    it('should create SUCCESS state with array data', () => {
      const data = ['item1', 'item2', 'item3'];
      const result: ResultState<string[]> = {
        status: 'SUCCESS',
        data,
      };

      expect(result.status).toBe('SUCCESS');
      if (result.status === 'SUCCESS') {
        expect(Array.isArray(result.data)).toBe(true);
        expect(result.data).toHaveLength(3);
        expect(result.data?.[0]).toBe('item1');
      }
    });

    it('should create SUCCESS state with primitive data', () => {
      const stringResult: ResultState<string> = {
        status: 'SUCCESS',
        data: 'test string',
      };

      const numberResult: ResultState<number> = {
        status: 'SUCCESS',
        data: 42,
      };

      const booleanResult: ResultState<boolean> = {
        status: 'SUCCESS',
        data: true,
      };

      expect(stringResult.status).toBe('SUCCESS');
      expect(numberResult.status).toBe('SUCCESS');
      expect(booleanResult.status).toBe('SUCCESS');

      if (stringResult.status === 'SUCCESS') {
        expect(stringResult.data).toBe('test string');
      }
      if (numberResult.status === 'SUCCESS') {
        expect(numberResult.data).toBe(42);
      }
      if (booleanResult.status === 'SUCCESS') {
        expect(booleanResult.data).toBe(true);
      }
    });
  });

  describe('ERROR state', () => {
    it('should create ERROR state with CustomError', () => {
      const error = new CustomError(
        'TEST_ERROR',
        ErrorCategory.NETWORK,
        'Network Error',
        'Connection failed',
        true,
      );

      const result: ResultState<any> = {
        status: 'ERROR',
        error,
      };

      expect(result.status).toBe('ERROR');
      if (result.status === 'ERROR') {
        expect(result.error).toBeInstanceOf(CustomError);
        expect(result.error.code).toBe('TEST_ERROR');
        expect(result.error.category).toBe(ErrorCategory.NETWORK);
        expect(result.error.title).toBe('Network Error');
        expect(result.error.userMessage).toBe('Connection failed');
        expect(result.error.retryable).toBe(true);
      }
    });

    it('should create ERROR state with different error categories', () => {
      const networkError = new CustomError(
        'NETWORK_001',
        ErrorCategory.NETWORK,
        'Network Error',
        'Network connection failed',
        true,
      );

      const apiError = new CustomError(
        'API_001',
        ErrorCategory.API,
        'API Error',
        'API request failed',
        false,
      );

      const businessError = new CustomError(
        'BUSINESS_001',
        ErrorCategory.BUSINESS,
        'Business Error',
        'Business rule violation',
        false,
      );

      const networkResult: ResultState<any> = {status: 'ERROR', error: networkError};
      const apiResult: ResultState<any> = {status: 'ERROR', error: apiError};
      const businessResult: ResultState<any> = {status: 'ERROR', error: businessError};

      expect(networkResult.status).toBe('ERROR');
      expect(apiResult.status).toBe('ERROR');
      expect(businessResult.status).toBe('ERROR');

      if (networkResult.status === 'ERROR') {
        expect(networkResult.error.category).toBe(ErrorCategory.NETWORK);
      }
      if (apiResult.status === 'ERROR') {
        expect(apiResult.error.category).toBe(ErrorCategory.API);
      }
      if (businessResult.status === 'ERROR') {
        expect(businessResult.error.category).toBe(ErrorCategory.BUSINESS);
      }
    });
  });

  describe('Type Guards and Discrimination', () => {
    it('should discriminate between SUCCESS and ERROR states', () => {
      const successResult: ResultState<string> = {
        status: 'SUCCESS',
        data: 'success data',
      };

      const errorResult: ResultState<string> = {
        status: 'ERROR',
        error: new CustomError('ERR_001', ErrorCategory.UNKNOWN, 'Error', 'Error message', false),
      };

      // Type guard for SUCCESS
      if (successResult.status === 'SUCCESS') {
        expect(successResult.data).toBe('success data');
        // TypeScript should know that 'error' property doesn't exist
        expect('error' in successResult).toBe(false);
      }

      // Type guard for ERROR
      if (errorResult.status === 'ERROR') {
        expect(errorResult.error).toBeInstanceOf(CustomError);
        // TypeScript should know that 'data' property doesn't exist
        expect('data' in errorResult).toBe(false);
      }
    });

    it('should work with switch statements', () => {
      const processResult = <T>(result: ResultState<T>): string => {
        switch (result.status) {
          case 'SUCCESS':
            return `Success with data: ${JSON.stringify(result.data)}`;
          case 'ERROR':
            return `Error: ${result.error.userMessage}`;
          default:
            // This should never be reached due to exhaustive checking
            return 'Unknown status';
        }
      };

      const successResult: ResultState<{value: number}> = {
        status: 'SUCCESS',
        data: {value: 100},
      };

      const errorResult: ResultState<{value: number}> = {
        status: 'ERROR',
        error: new CustomError('ERR_001', ErrorCategory.VALIDATION, 'Validation Error', 'Invalid input', false),
      };

      expect(processResult(successResult)).toBe('Success with data: {"value":100}');
      expect(processResult(errorResult)).toBe('Error: Invalid input');
    });
  });

  describe('Generic Type Support', () => {
    interface User {
      id: number;
      name: string;
      email: string;
    }

    it('should support custom interface types', () => {
      const user: User = {
        id: 1,
        name: 'John Doe',
        email: '<EMAIL>',
      };

      const result: ResultState<User> = {
        status: 'SUCCESS',
        data: user,
      };

      expect(result.status).toBe('SUCCESS');
      if (result.status === 'SUCCESS') {
        expect(result.data?.id).toBe(1);
        expect(result.data?.name).toBe('John Doe');
        expect(result.data?.email).toBe('<EMAIL>');
      }
    });

    it('should support array types', () => {
      const users: User[] = [
        {id: 1, name: 'User 1', email: '<EMAIL>'},
        {id: 2, name: 'User 2', email: '<EMAIL>'},
      ];

      const result: ResultState<User[]> = {
        status: 'SUCCESS',
        data: users,
      };

      expect(result.status).toBe('SUCCESS');
      if (result.status === 'SUCCESS') {
        expect(Array.isArray(result.data)).toBe(true);
        expect(result.data).toHaveLength(2);
        expect(result.data?.[0].name).toBe('User 1');
      }
    });

    it('should support nested object types', () => {
      interface ApiResponse {
        users: User[];
        total: number;
        page: number;
      }

      const apiData: ApiResponse = {
        users: [{id: 1, name: 'Test User', email: '<EMAIL>'}],
        total: 1,
        page: 1,
      };

      const result: ResultState<ApiResponse> = {
        status: 'SUCCESS',
        data: apiData,
      };

      expect(result.status).toBe('SUCCESS');
      if (result.status === 'SUCCESS') {
        expect(result.data?.users).toHaveLength(1);
        expect(result.data?.total).toBe(1);
        expect(result.data?.page).toBe(1);
      }
    });
  });

  describe('Real-world Usage Patterns', () => {
    it('should handle async operation results', async () => {
      // Simulate async operation that returns ResultState
      const fetchUser = async (id: number): Promise<ResultState<User>> => {
        if (id > 0) {
          return {
            status: 'SUCCESS',
            data: {id, name: `User ${id}`, email: `user${id}@test.com`},
          };
        } else {
          return {
            status: 'ERROR',
            error: new CustomError('INVALID_ID', ErrorCategory.VALIDATION, 'Invalid ID', 'User ID must be positive', false),
          };
        }
      };

      const successResult = await fetchUser(1);
      const errorResult = await fetchUser(-1);

      expect(successResult.status).toBe('SUCCESS');
      expect(errorResult.status).toBe('ERROR');

      if (successResult.status === 'SUCCESS') {
        expect(successResult.data?.id).toBe(1);
      }

      if (errorResult.status === 'ERROR') {
        expect(errorResult.error.code).toBe('INVALID_ID');
      }
    });

    it('should handle repository pattern results', () => {
      // Simulate repository method return types
      const createUser = (userData: Partial<User>): ResultState<User> => {
        if (!userData.name || !userData.email) {
          return {
            status: 'ERROR',
            error: new CustomError(
              'MISSING_FIELDS',
              ErrorCategory.VALIDATION,
              'Missing Required Fields',
              'Name and email are required',
              false,
            ),
          };
        }

        return {
          status: 'SUCCESS',
          data: {
            id: Math.floor(Math.random() * 1000),
            name: userData.name,
            email: userData.email,
          },
        };
      };

      const validResult = createUser({name: 'John Doe', email: '<EMAIL>'});
      const invalidResult = createUser({name: 'John Doe'});

      expect(validResult.status).toBe('SUCCESS');
      expect(invalidResult.status).toBe('ERROR');
    });
  });
});
