import {describe, it, expect} from '@jest/globals';
import {MSBErrorCode} from '../MSBErrorCode';

describe('MSBErrorCode', () => {
  describe('Enum Values', () => {
    it('should have UNKNOWN_ERROR code', () => {
      expect(MSBErrorCode.UNKNOWN_ERROR).toBe('MSB-9999');
    });

    it('should have NOT_VALID_DATA_FORMAT code', () => {
      expect(MSBErrorCode.NOT_VALID_DATA_FORMAT).toBe('MSB-9997');
    });

    it('should have EMPTY_DATA code', () => {
      expect(MSBErrorCode.EMPTY_DATA).toBe('MSB-9998');
    });

    it('should have A05 code', () => {
      expect(MSBErrorCode.A05).toBe('A05');
    });

    it('should have FTES error codes', () => {
      expect(MSBErrorCode.FTES0009).toBe('FTES-0009');
      expect(MSBErrorCode.FTES0008).toBe('FTES-0008');
      expect(MSBErrorCode.FTES0001).toBe('FTES-0001');
      expect(MSBErrorCode.FTES0006).toBe('FTES-0006');
    });

    it('should have BMS error codes', () => {
      expect(MSBErrorCode.BMS009).toBe('BMS-0009');
      expect(MSBErrorCode.BMS010).toBe('BMS-0010');
      expect(MSBErrorCode.BMS014).toBe('BMS-0010'); // Note: Same value as BMS010
      expect(MSBErrorCode.BMS011).toBe('BMS-0011');
      expect(MSBErrorCode.BMS0017).toBe('BMS-0017');
    });

    it('should have BPE error codes', () => {
      expect(MSBErrorCode.BPE0001).toBe('BPE-0001');
      expect(MSBErrorCode.BPE0015).toBe('BPE-0015');
      expect(MSBErrorCode.BPE0010).toBe('BPE-0010');
      expect(MSBErrorCode.BPE0019).toBe('BPE-0019');
    });
  });

  describe('Error Code Categories', () => {
    it('should identify MSB system errors', () => {
      const msbErrors = [MSBErrorCode.UNKNOWN_ERROR, MSBErrorCode.NOT_VALID_DATA_FORMAT, MSBErrorCode.EMPTY_DATA];

      msbErrors.forEach(code => {
        expect(code).toMatch(/^MSB-/);
      });
    });

    it('should identify FTES errors', () => {
      const ftesErrors = [MSBErrorCode.FTES0009, MSBErrorCode.FTES0008, MSBErrorCode.FTES0001, MSBErrorCode.FTES0006];

      ftesErrors.forEach(code => {
        expect(code).toMatch(/^FTES-/);
      });
    });

    it('should identify BMS errors', () => {
      const bmsErrors = [
        MSBErrorCode.BMS009,
        MSBErrorCode.BMS010,
        MSBErrorCode.BMS014,
        MSBErrorCode.BMS011,
        MSBErrorCode.BMS0017,
      ];

      bmsErrors.forEach(code => {
        expect(code).toMatch(/^BMS-/);
      });
    });

    it('should identify BPE errors', () => {
      const bpeErrors = [MSBErrorCode.BPE0001, MSBErrorCode.BPE0015, MSBErrorCode.BPE0010, MSBErrorCode.BPE0019];

      bpeErrors.forEach(code => {
        expect(code).toMatch(/^BPE-/);
      });
    });

    it('should identify standalone error codes', () => {
      expect(MSBErrorCode.A05).toBe('A05');
      expect(MSBErrorCode.A05).not.toMatch(/^[A-Z]+-/);
    });
  });

  describe('Error Code Uniqueness', () => {
    it('should have unique error code values', () => {
      const allValues = Object.values(MSBErrorCode);
      const uniqueValues = [...new Set(allValues)];

      // Note: BMS010 and BMS014 have the same value, so we expect one less unique value
      expect(uniqueValues.length).toBe(allValues.length - 1);
    });

    it('should have unique error code keys', () => {
      const allKeys = Object.keys(MSBErrorCode);
      const uniqueKeys = [...new Set(allKeys)];

      expect(uniqueKeys.length).toBe(allKeys.length);
    });
  });

  describe('Error Code Format Validation', () => {
    it('should follow MSB error code format', () => {
      const msbPattern = /^MSB-\d{4}$/;

      expect(MSBErrorCode.UNKNOWN_ERROR).toMatch(msbPattern);
      expect(MSBErrorCode.NOT_VALID_DATA_FORMAT).toMatch(msbPattern);
      expect(MSBErrorCode.EMPTY_DATA).toMatch(msbPattern);
    });

    it('should follow FTES error code format', () => {
      const ftesPattern = /^FTES-\d{4}$/;

      expect(MSBErrorCode.FTES0009).toMatch(ftesPattern);
      expect(MSBErrorCode.FTES0008).toMatch(ftesPattern);
      expect(MSBErrorCode.FTES0001).toMatch(ftesPattern);
      expect(MSBErrorCode.FTES0006).toMatch(ftesPattern);
    });

    it('should follow BMS error code format', () => {
      const bmsPattern = /^BMS-\d{4}$/;

      expect(MSBErrorCode.BMS009).toMatch(bmsPattern);
      expect(MSBErrorCode.BMS010).toMatch(bmsPattern);
      expect(MSBErrorCode.BMS011).toMatch(bmsPattern);
      expect(MSBErrorCode.BMS0017).toMatch(bmsPattern);
    });

    it('should follow BPE error code format', () => {
      const bpePattern = /^BPE-\d{4}$/;

      expect(MSBErrorCode.BPE0001).toMatch(bpePattern);
      expect(MSBErrorCode.BPE0015).toMatch(bpePattern);
      expect(MSBErrorCode.BPE0010).toMatch(bpePattern);
      expect(MSBErrorCode.BPE0019).toMatch(bpePattern);
    });
  });

  describe('Error Code Usage', () => {
    it('should be usable as object keys', () => {
      const errorMessages = {
        [MSBErrorCode.UNKNOWN_ERROR]: 'Unknown error occurred',
        [MSBErrorCode.NOT_VALID_DATA_FORMAT]: 'Invalid data format',
        [MSBErrorCode.EMPTY_DATA]: 'No data provided',
      };

      expect(errorMessages[MSBErrorCode.UNKNOWN_ERROR]).toBe('Unknown error occurred');
      expect(errorMessages[MSBErrorCode.NOT_VALID_DATA_FORMAT]).toBe('Invalid data format');
      expect(errorMessages[MSBErrorCode.EMPTY_DATA]).toBe('No data provided');
    });

    it('should be usable in switch statements', () => {
      const getErrorCategory = (code: MSBErrorCode): string => {
        switch (code) {
          case MSBErrorCode.UNKNOWN_ERROR:
          case MSBErrorCode.NOT_VALID_DATA_FORMAT:
          case MSBErrorCode.EMPTY_DATA:
            return 'System Error';
          case MSBErrorCode.FTES0009:
          case MSBErrorCode.FTES0008:
          case MSBErrorCode.FTES0001:
          case MSBErrorCode.FTES0006:
            return 'FTES Error';
          case MSBErrorCode.BMS009:
          case MSBErrorCode.BMS010:
          case MSBErrorCode.BMS011:
          case MSBErrorCode.BMS0017:
            return 'BMS Error';
          case MSBErrorCode.BPE0001:
            return 'BPE Error';
          case MSBErrorCode.A05:
            return 'A05 Error';
          default:
            return 'Unknown Category';
        }
      };

      expect(getErrorCategory(MSBErrorCode.UNKNOWN_ERROR)).toBe('System Error');
      expect(getErrorCategory(MSBErrorCode.FTES0009)).toBe('FTES Error');
      expect(getErrorCategory(MSBErrorCode.BMS009)).toBe('BMS Error');
      expect(getErrorCategory(MSBErrorCode.BPE0001)).toBe('BPE Error');
      expect(getErrorCategory(MSBErrorCode.A05)).toBe('A05 Error');
    });

    it('should be comparable', () => {
      expect(MSBErrorCode.UNKNOWN_ERROR === 'MSB-9999').toBe(true);
      expect(MSBErrorCode.NOT_VALID_DATA_FORMAT === 'MSB-9997').toBe(true);
      expect(MSBErrorCode.EMPTY_DATA === 'MSB-9998').toBe(true);
    });
  });

  describe('Error Code Documentation', () => {
    it('should have meaningful error code names', () => {
      // Test that error code names are descriptive
      expect(MSBErrorCode.UNKNOWN_ERROR).toBeDefined();
      expect(MSBErrorCode.NOT_VALID_DATA_FORMAT).toBeDefined();
      expect(MSBErrorCode.EMPTY_DATA).toBeDefined();
    });

    it('should cover common error scenarios', () => {
      // Verify we have error codes for common scenarios
      const commonScenarios = ['UNKNOWN_ERROR', 'NOT_VALID_DATA_FORMAT', 'EMPTY_DATA'];

      commonScenarios.forEach(scenario => {
        expect(MSBErrorCode[scenario as keyof typeof MSBErrorCode]).toBeDefined();
      });
    });
  });
});
