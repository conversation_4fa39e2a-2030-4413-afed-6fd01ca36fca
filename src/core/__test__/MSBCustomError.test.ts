import {describe, it, expect, beforeEach, jest} from '@jest/globals';
import {
  CustomError,
  ErrorCategory,
  ErrorAction,
  ErrorMapper,
  createError,
  extractErrorFromResponse,
  isRetryable,
  hasError,
} from '../MSBCustomError';
import {BaseResponse} from '../BaseResponse';
import {MSBErrorCode} from '../MSBErrorCode';

describe('CustomError', () => {
  describe('constructor', () => {
    it('should create CustomError with all parameters', () => {
      const actions: ErrorAction[] = [
        {type: 'RETRY', label: 'Thử lại', primary: true},
        {type: 'CANCEL', label: 'Hủy', primary: false},
      ];

      const error = new CustomError(
        'TEST_001',
        ErrorCategory.NETWORK,
        'Network Error',
        'Không thể kết nối mạng',
        true,
        actions,
      );

      expect(error.code).toBe('TEST_001');
      expect(error.category).toBe(ErrorCategory.NETWORK);
      expect(error.title).toBe('Network Error');
      expect(error.userMessage).toBe('Không thể kết nối mạng');
      expect(error.retryable).toBe(true);
      expect(error.actions).toEqual(actions);
      expect(error.name).toBe('CustomError');
    });

    it('should create CustomError with null actions', () => {
      const error = new CustomError('TEST_002', ErrorCategory.API, 'API Error', 'Lỗi API', false, null);

      expect(error.actions).toEqual([]);
    });

    it('should create CustomError with undefined actions', () => {
      const error = new CustomError('TEST_003', ErrorCategory.BUSINESS, 'Business Error', 'Lỗi nghiệp vụ', false);

      expect(error.actions).toEqual([]);
    });
  });

  describe('ErrorAction', () => {
    it('should create valid ErrorAction', () => {
      const action: ErrorAction = {
        type: 'CONTACT_SUPPORT',
        label: 'Liên hệ hỗ trợ',
        primary: false,
      };

      expect(action.type).toBe('CONTACT_SUPPORT');
      expect(action.label).toBe('Liên hệ hỗ trợ');
      expect(action.primary).toBe(false);
    });

    it('should create ErrorAction without primary flag', () => {
      const action: ErrorAction = {
        type: 'BACK',
        label: 'Quay lại',
      };

      expect(action.type).toBe('BACK');
      expect(action.label).toBe('Quay lại');
      expect(action.primary).toBeUndefined();
    });
  });
});

describe('ErrorMapper', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('createError', () => {
    it('should create error from predefined error code', () => {
      const error = ErrorMapper.createError(MSBErrorCode.UNKNOWN_ERROR);

      expect(error).toBeInstanceOf(CustomError);
      expect(error.code).toBe(MSBErrorCode.UNKNOWN_ERROR);
      expect(error.category).toBe(ErrorCategory.UNKNOWN);
    });

    it('should create error with predefined message', () => {
      const error = ErrorMapper.createError(MSBErrorCode.UNKNOWN_ERROR);

      expect(error.userMessage).toBe('error.errorOccurred');
    });

    it('should create unknown error for unrecognized code', () => {
      const error = ErrorMapper.createError('INVALID_CODE');

      expect(error.code).toBe('INVALID_CODE');
      expect(error.category).toBe(ErrorCategory.UNKNOWN);
      expect(error.title).toBe('error.oops');
    });
  });

  describe('extractErrorFromResponse', () => {
    it('should extract error from response with errors array', () => {
      const response: BaseResponse<any> = {
        errors: [
          {
            key: MSBErrorCode.UNKNOWN_ERROR,
            message: 'Test error message',
            context: ['test'],
          },
        ],
      };

      const error = ErrorMapper.extractErrorFromResponse(response);

      expect(error).toBeInstanceOf(CustomError);
      expect(error).not.toBeNull();
      expect(error!.code).toBe(MSBErrorCode.UNKNOWN_ERROR);
      expect(error!.userMessage).toBe('error.errorOccurred');
    });

    it('should extract error from response with context localization', () => {
      const response: BaseResponse<any> = {
        errors: [
          {
            key: MSBErrorCode.NOT_VALID_DATA_FORMAT,
            message: 'Invalid data format',
            context: {
              vi: 'Định dạng dữ liệu không hợp lệ',
              en: 'Invalid data format',
            },
          },
        ],
      };

      const error = ErrorMapper.extractErrorFromResponse(response);

      expect(error).not.toBeNull();
      expect(error!.code).toBe(MSBErrorCode.NOT_VALID_DATA_FORMAT);
      expect(error!.userMessage).toBe('Định dạng dữ liệu không hợp lệ');
    });

    it('should return null for response without errors', () => {
      const response: BaseResponse<any> = {
        data: {success: true},
      };

      const error = ErrorMapper.extractErrorFromResponse(response);

      expect(error).toBeNull();
    });

    it('should return null for response with empty errors array', () => {
      const response: BaseResponse<any> = {
        errors: [],
      };

      const error = ErrorMapper.extractErrorFromResponse(response);

      expect(error).toBeNull();
    });
  });

  describe('isRetryable', () => {
    it('should return true for retryable error codes', () => {
      expect(ErrorMapper.isRetryable(MSBErrorCode.UNKNOWN_ERROR)).toBe(true);
    });

    it('should return true for validation error codes', () => {
      expect(ErrorMapper.isRetryable(MSBErrorCode.NOT_VALID_DATA_FORMAT)).toBe(true);
    });

    it('should return true for unknown error codes', () => {
      expect(ErrorMapper.isRetryable('UNKNOWN_CODE')).toBe(true);
    });
  });

  describe('hasError', () => {
    it('should return true for response with errors array', () => {
      const response: BaseResponse<any> = {
        errors: [
          {
            key: 'TEST_ERROR',
            message: 'Test error',
            context: [],
          },
        ],
      };

      expect(ErrorMapper.hasError(response)).toBe(true);
    });

    it('should return false for response with null errors', () => {
      const response: BaseResponse<any> = {
        errors: null,
      };

      expect(ErrorMapper.hasError(response)).toBe(false);
    });

    it('should return false for response without errors', () => {
      const response: BaseResponse<any> = {
        data: {success: true},
      };

      expect(ErrorMapper.hasError(response)).toBe(false);
    });

    it('should return false for response with empty errors array', () => {
      const response: BaseResponse<any> = {
        errors: [],
      };

      expect(ErrorMapper.hasError(response)).toBe(false);
    });
  });

  describe('addPredefinedError', () => {
    it('should add new predefined error', () => {
      const customError = new CustomError(
        'CUSTOM_001',
        ErrorCategory.BUSINESS,
        'Custom Error',
        'Custom error message',
        false,
      );

      ErrorMapper.addPredefinedError('CUSTOM_001', customError);
      const availableCodes = ErrorMapper.getAvailableErrorCodes();

      expect(availableCodes).toContain('CUSTOM_001');
    });
  });

  describe('getAvailableErrorCodes', () => {
    it('should return array of available error codes', () => {
      const codes = ErrorMapper.getAvailableErrorCodes();

      expect(Array.isArray(codes)).toBe(true);
      expect(codes.length).toBeGreaterThan(0);
    });
  });
});

// Test convenience functions
describe('Convenience Functions', () => {
  it('should export createError function', () => {
    const error = createError(MSBErrorCode.UNKNOWN_ERROR);
    expect(error).toBeInstanceOf(CustomError);
  });

  it('should export extractErrorFromResponse function', () => {
    const response: BaseResponse<any> = {
      errors: [{key: 'TEST', message: 'Test', context: []}],
    };
    const error = extractErrorFromResponse(response);
    expect(error).toBeInstanceOf(CustomError);
  });

  it('should export isRetryable function', () => {
    const result = isRetryable(MSBErrorCode.UNKNOWN_ERROR);
    expect(typeof result).toBe('boolean');
  });

  it('should export hasError function', () => {
    const response: BaseResponse<any> = {errors: []};
    const result = hasError(response);
    expect(typeof result).toBe('boolean');
  });
});
