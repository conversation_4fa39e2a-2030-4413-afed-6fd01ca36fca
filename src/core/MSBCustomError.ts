/**
 * Error System - Single source of truth cho error handling
 * CustomError + ErrorMapper + ActionHandlers
 */

import {MSBErrorCode} from './MSBErrorCode';
import {BaseResponse, MSBError} from './BaseResponse';

/**
 * Error categories
 */
export enum ErrorCategory {
  NETWORK = 'NETWORK',
  API = 'API',
  BUSINESS = 'BUSINESS',
  VALIDATION = 'VALIDATION',
  SYSTEM = 'SYSTEM',
  UNKNOWN = 'UNKNOWN',
}

/**
 * Action definition - đơn giản, max 2 actions
 */
export interface ErrorAction {
  type: string; // Action type, e.g. 'RETRY', 'CONTACT_SUPPORT', 'BACK', 'GO_HOME', etc.
  label: string;
  primary?: boolean; // true = confirm button, false = cancel button
}

/**
 * CustomError - Simple error class với support null/undefined actions
 */
export class CustomError extends Error {
  public readonly code: string;
  public readonly category: ErrorCategory;
  public readonly title: string;
  public readonly userMessage: string;
  public readonly retryable: boolean;
  public readonly actions: ErrorAction[];

  constructor(
    code: string,
    category: ErrorCategory,
    title: string,
    userMessage: string,
    retryable: boolean,
    actions?: ErrorAction[] | null,
  ) {
    super(userMessage);
    this.name = 'CustomError';
    this.code = code;
    this.category = category;
    this.title = title;
    this.userMessage = userMessage;
    this.retryable = retryable;
    this.actions = actions || []; // ✅ Default to empty array
  }

  /**
   * Get primary action (confirm button)
   */
  getPrimaryAction(): ErrorAction | null {
    return this.actions.find(action => action.primary) || this.actions[0] || null;
  }

  /**
   * Get secondary action (cancel button)
   */
  getSecondaryAction(): ErrorAction | null {
    return this.actions.find(action => !action.primary) || this.actions[1] || null;
  }
}

/**
 * Predefined CustomError instances - no intermediate definitions
 */
const PredefinedErrors: Record<string, CustomError> = {
  // Common errors
  [MSBErrorCode.UNKNOWN_ERROR]: new CustomError(
    MSBErrorCode.UNKNOWN_ERROR,
    ErrorCategory.UNKNOWN,
    'error.oops',
    'error.errorOccurred',
    true,
    [
      {type: 'RETRY', label: 'error.action.rety', primary: true},
      {type: 'CONTACT_SUPPORT', label: 'error.action.contact'},
    ],
  ),

  [MSBErrorCode.PIS0101]: new CustomError(
    MSBErrorCode.PIS0101,
    ErrorCategory.BUSINESS,
    'error.oops',
    'error.errorOccurred',
    false,
    [{type: 'NAVIGATE_BACK', label: 'error.action.back'}],
  ),

  [MSBErrorCode.PIS0106]: new CustomError(
    MSBErrorCode.PIS0106,
    ErrorCategory.NETWORK,
    'error.oops',
    'error.errorOccurred',
    true,
    [
      {type: 'RETRY', label: 'error.action.retry', primary: true},
      {type: 'NAVIGATE_BACK', label: 'error.action.back'},
    ],
  ),

  [MSBErrorCode.PIS0103]: new CustomError(
    MSBErrorCode.PIS0103,
    ErrorCategory.VALIDATION,
    'error.oops',
    'error.errorOccurred',
    false,
    [{type: 'CLOSE', label: 'error.action.close'}],
  ),

  [MSBErrorCode.A05]: new CustomError(
    MSBErrorCode.A05,
    ErrorCategory.SYSTEM,
    'error.oops',
    'error.errorOccurred',
    true,
    [
      {type: 'RETRY', label: 'error.action.retry', primary: true},
      {type: 'CLOSE', label: 'error.action.close'},
    ],
  ),

  [MSBErrorCode.FTES0008]: new CustomError(
    MSBErrorCode.FTES0008,
    ErrorCategory.BUSINESS,
    'error.oops',
    'error.errorOccurred',
    true,
    [{type: 'CLOSE', label: 'error.action.close'}],
  ),

  // ✅ Example với null actions - default close only
  [MSBErrorCode.EMPTY_DATA]: new CustomError(
    MSBErrorCode.EMPTY_DATA,
    ErrorCategory.API,
    'error.oops',
    'error.errorOccurred',
    false,
    null, //
  ),

  [MSBErrorCode.BPE0015]: new CustomError(
    MSBErrorCode.EMPTY_DATA,
    ErrorCategory.API,
    'error.oops',
    'error.validation.BPE0015',
    false,
    null, //
  ),

  [MSBErrorCode.BPE0019]: new CustomError(
    MSBErrorCode.EMPTY_DATA,
    ErrorCategory.API,
    'error.oops',
    'error.validation.BPE0019',
    false,
    null, //
  ),
};

/**
 * Error Mapper - Main utility
 */
export class ErrorMapper {
  /**
   * Create CustomError từ error code
   */
  static createError(code?: string): CustomError {
    console.log('🛠 LOG: 🚀 --> --------------------------------------------------------🛠 LOG: 🚀 -->');
    console.log('🛠 LOG: 🚀 --> ~ ErrorMapper ~ createError ~ code:', code);
    console.log('🛠 LOG: 🚀 --> --------------------------------------------------------🛠 LOG: 🚀 -->');
    const errorCode = code || MSBErrorCode.UNKNOWN_ERROR;
    const predefinedError = PredefinedErrors[errorCode] || PredefinedErrors[MSBErrorCode.UNKNOWN_ERROR];

    // Return a copy với correct code
    return new CustomError(
      errorCode,
      predefinedError.category,
      predefinedError.title,
      predefinedError.userMessage,
      predefinedError.retryable,
      predefinedError.actions,
    );
  }

  /**
   * Extract error từ BaseResponse
   */
  static extractErrorFromResponse<T>(response: BaseResponse<T>): CustomError | null {
    if (!response?.errors || response.errors.length <= 0) {
      return null;
    }

    const firstError = response.errors[0];
    let errorCode = firstError.key || MSBErrorCode.UNKNOWN_ERROR;

    // Create base error
    const baseError = ErrorMapper.createError(errorCode);

    // Override message if available in response
    let userMessage = baseError.userMessage;
    if (firstError.context) {
      userMessage = firstError.context.vi || firstError.context.en || userMessage;
    }

    // Return new error với customized message
    return new CustomError(
      baseError.code,
      baseError.category,
      baseError.title,
      userMessage,
      baseError.retryable,
      baseError.actions,
    );
  }

  /**
   * Check if error is retryable
   */
  static isRetryable(code?: string): boolean {
    const error = ErrorMapper.createError(code);
    return error.retryable;
  }

  /**
   * Get error category
   */
  static getCategory(code?: string): ErrorCategory {
    const error = ErrorMapper.createError(code);
    return error.category;
  }

  /**
   * Check if response has error
   */
  static hasError<T>(response: BaseResponse<T>): boolean {
    return !!(response?.errors && response.errors.length > 0);
  }

  /**
   * Get first error from response
   */
  static getFirstError<T>(response: BaseResponse<T>): MSBError | null {
    if (!ErrorMapper.hasError(response)) {
      return null;
    }
    return response.errors![0];
  }

  /**
   * Add new predefined error
   */
  static addPredefinedError(code: string, error: CustomError): void {
    PredefinedErrors[code] = error;
  }

  /**
   * Get all available error codes
   */
  static getAvailableErrorCodes(): string[] {
    return Object.keys(PredefinedErrors);
  }
}

// Export convenience functions
export const createError = ErrorMapper.createError;
export const extractErrorFromResponse = ErrorMapper.extractErrorFromResponse;
export const isRetryable = ErrorMapper.isRetryable;
export const hasError = ErrorMapper.hasError;
