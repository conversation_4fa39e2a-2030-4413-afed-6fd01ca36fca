export enum MSBErrorCode {
  UNKNOWN_ERROR = 'MSB-9999', // Unknown error
  NOT_VALID_DATA_FORMAT = 'MSB-9997', // jsonparse error, data wrong format....
  EMPTY_DATA = 'MSB-9998', // empty data

  A05 = 'A05',
  FTES0009 = 'FTES-0009', // <PERSON><PERSON><PERSON><PERSON> tờ tuỳ thân
  FTES0008 = 'FTES-0008', // Sinh trắc học
  FTES0001 = 'FTES-0001', // G<PERSON>i truy vấn
  BMS009 = 'BMS-0009', // Napas gián đoạn
  BMS010 = 'BMS-0010', //
  BMS014 = 'BMS-0010', //
  BMS011 = 'BMS-0011', //
  BMS0017 = 'BMS-0017',
  FTES0006 = 'FTES-0006', // Citad gián đoạn

  PIS0100 = 'PIS-0100', // General error from ePayment
  PIS0101 = 'PIS-0101', // Bill has no debt
  PIS0102 = 'PIS-0102', // Bill not found or no debt
  PIS0103 = 'PIS-0103', // Request format is invalid
  PIS0104 = 'PIS-0104', // Request field is invalid
  PIS0105 = 'PIS-0105', // Error calling partner
  PIS0106 = 'PIS-0106', // Error connecting to partner
  PIS0107 = 'PIS-0107', // ePayment timeout
  CME0001 = 'CME-0001', // Internal Server Error
  CME0002 = 'CME-0002', // Cif in token not found
  CME0003 = 'CME-0003', // Username in token not found
  CME0016 = 'CME-0016', // Category code not found
  CME0017 = 'CME-0017', // Internal User not found
  CME0018 = 'CME-0018', // Legal entity not found
  CME0019 = 'CME-0019', // Service agreement not found
  BPE0001 = 'BPE-0001', // Không tìm thấy hợp đồng
  BPE0015 = 'BPE-0015', // Danh sách bill không khớp với hệ thống
  BPE0010 = 'BPE-0010', // The entity is not valid
  BPE0019 = 'BPE-0019', // Bill not found
}
