30092a1c7828485560741eaa99f3c16f
'use strict';

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.AnimatedImage = void 0;
var _reactNative = require("react-native");
var _index = require("../createAnimatedComponent/index.js");
var AnimatedImage = exports.AnimatedImage = (0, _index.createAnimatedComponent)(_reactNative.Image);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJPYmplY3QiLCJkZWZpbmVQcm9wZXJ0eSIsImV4cG9ydHMiLCJ2YWx1ZSIsIkFuaW1hdGVkSW1hZ2UiLCJfcmVhY3ROYXRpdmUiLCJyZXF1aXJlIiwiX2luZGV4IiwiY3JlYXRlQW5pbWF0ZWRDb21wb25lbnQiLCJJbWFnZSJdLCJzb3VyY2VzIjpbIi4uLy4uLy4uL3NyYy9jb21wb25lbnQvSW1hZ2UudHMiXSwic291cmNlc0NvbnRlbnQiOltudWxsXSwibWFwcGluZ3MiOiJBQUFBLFlBQVk7O0FBQUFBLE1BQUEsQ0FBQUMsY0FBQSxDQUFBQyxPQUFBO0VBQUFDLEtBQUE7QUFBQTtBQUFBRCxPQUFBLENBQUFFLGFBQUE7QUFDWixJQUFBQyxZQUFBLEdBQUFDLE9BQUE7QUFDQSxJQUFBQyxNQUFBLEdBQUFELE9BQUE7QUFRTyxJQUFNRixhQUFhLEdBQUFGLE9BQUEsQ0FBQUUsYUFBQSxHQUFHLElBQUFJLDhCQUF1QixFQUFDQyxrQkFBSyxDQUFDIiwiaWdub3JlTGlzdCI6W119