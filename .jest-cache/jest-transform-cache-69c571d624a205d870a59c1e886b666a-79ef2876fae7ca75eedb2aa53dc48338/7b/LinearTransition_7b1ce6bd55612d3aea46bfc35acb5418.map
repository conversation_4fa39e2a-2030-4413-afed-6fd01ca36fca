{"version": 3, "names": ["_interopRequireDefault", "require", "Object", "defineProperty", "exports", "value", "LinearTransition", "Layout", "_slicedToArray2", "_classCallCheck2", "_createClass2", "_possibleConstructorReturn2", "_getPrototypeOf2", "_inherits2", "_index", "_callSuper", "t", "o", "e", "default", "_isNativeReflectConstruct", "Reflect", "construct", "constructor", "apply", "Boolean", "prototype", "valueOf", "call", "_ComplexAnimationBuil", "_this", "_len", "arguments", "length", "args", "Array", "_key", "concat", "build", "delayFunction", "getDelayFunction", "_this$getAnimationAnd", "getAnimationAndConfig", "_this$getAnimationAnd2", "animation", "config", "callback", "callbackV", "delay", "get<PERSON>elay", "values", "initialValues", "originX", "currentOriginX", "originY", "currentOriginY", "width", "currentWidth", "height", "currentHeight", "animations", "targetOriginX", "targetOriginY", "targetWidth", "targetHeight", "key", "createInstance", "ComplexAnimationBuilder", "presetName"], "sources": ["../../../../src/layoutReanimation/defaultTransitions/LinearTransition.ts"], "sourcesContent": [null], "mappings": "AAAA,YAAY;;AAAA,IAAAA,sBAAA,GAAAC,OAAA;AAAAC,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,KAAA;AAAA;AAAAD,OAAA,CAAAE,gBAAA,GAAAF,OAAA,CAAAG,MAAA;AAAA,IAAAC,eAAA,GAAAR,sBAAA,CAAAC,OAAA;AAAA,IAAAQ,gBAAA,GAAAT,sBAAA,CAAAC,OAAA;AAAA,IAAAS,aAAA,GAAAV,sBAAA,CAAAC,OAAA;AAAA,IAAAU,2BAAA,GAAAX,sBAAA,CAAAC,OAAA;AAAA,IAAAW,gBAAA,GAAAZ,sBAAA,CAAAC,OAAA;AAAA,IAAAY,UAAA,GAAAb,sBAAA,CAAAC,OAAA;AAEZ,IAAAa,MAAA,GAAAb,OAAA;AAA6D,SAAAc,WAAAC,CAAA,EAAAC,CAAA,EAAAC,CAAA,WAAAD,CAAA,OAAAL,gBAAA,CAAAO,OAAA,EAAAF,CAAA,OAAAN,2BAAA,CAAAQ,OAAA,EAAAH,CAAA,EAAAI,yBAAA,KAAAC,OAAA,CAAAC,SAAA,CAAAL,CAAA,EAAAC,CAAA,YAAAN,gBAAA,CAAAO,OAAA,EAAAH,CAAA,EAAAO,WAAA,IAAAN,CAAA,CAAAO,KAAA,CAAAR,CAAA,EAAAE,CAAA;AAAA,SAAAE,0BAAA,cAAAJ,CAAA,IAAAS,OAAA,CAAAC,SAAA,CAAAC,OAAA,CAAAC,IAAA,CAAAP,OAAA,CAAAC,SAAA,CAAAG,OAAA,iCAAAT,CAAA,aAAAI,yBAAA,YAAAA,0BAAA,aAAAJ,CAAA;AAAA,IAehDV,gBAAgB,GAAAF,OAAA,CAAAE,gBAAA,aAAAuB,qBAAA;EAAA,SAAAvB,iBAAA;IAAA,IAAAwB,KAAA;IAAA,IAAArB,gBAAA,CAAAU,OAAA,QAAAb,gBAAA;IAAA,SAAAyB,IAAA,GAAAC,SAAA,CAAAC,MAAA,EAAAC,IAAA,OAAAC,KAAA,CAAAJ,IAAA,GAAAK,IAAA,MAAAA,IAAA,GAAAL,IAAA,EAAAK,IAAA;MAAAF,IAAA,CAAAE,IAAA,IAAAJ,SAAA,CAAAI,IAAA;IAAA;IAAAN,KAAA,GAAAf,UAAA,OAAAT,gBAAA,KAAA+B,MAAA,CAAAH,IAAA;IAAAJ,KAAA,CAY3BQ,KAAK,GAAG,YAA+B;MACrC,IAAMC,aAAa,GAAGT,KAAA,CAAKU,gBAAgB,CAAC,CAAC;MAC7C,IAAAC,qBAAA,GAA4BX,KAAA,CAAKY,qBAAqB,CAAC,CAAC;QAAAC,sBAAA,OAAAnC,eAAA,CAAAW,OAAA,EAAAsB,qBAAA;QAAjDG,SAAS,GAAAD,sBAAA;QAAEE,MAAM,GAAAF,sBAAA;MACxB,IAAMG,QAAQ,GAAGhB,KAAA,CAAKiB,SAAS;MAC/B,IAAMC,KAAK,GAAGlB,KAAA,CAAKmB,QAAQ,CAAC,CAAC;MAE7B,OAAQ,UAAAC,MAAM,EAAK;QACjB,SAAS;;QACT,OAAO;UACLC,aAAa,EAAE;YACbC,OAAO,EAAEF,MAAM,CAACG,cAAc;YAC9BC,OAAO,EAAEJ,MAAM,CAACK,cAAc;YAC9BC,KAAK,EAAEN,MAAM,CAACO,YAAY;YAC1BC,MAAM,EAAER,MAAM,CAACS;UACjB,CAAC;UACDC,UAAU,EAAE;YACVR,OAAO,EAAEb,aAAa,CACpBS,KAAK,EACLJ,SAAS,CAACM,MAAM,CAACW,aAAa,EAAEhB,MAAM,CACxC,CAAC;YACDS,OAAO,EAAEf,aAAa,CACpBS,KAAK,EACLJ,SAAS,CAACM,MAAM,CAACY,aAAa,EAAEjB,MAAM,CACxC,CAAC;YACDW,KAAK,EAAEjB,aAAa,CAACS,KAAK,EAAEJ,SAAS,CAACM,MAAM,CAACa,WAAW,EAAElB,MAAM,CAAC,CAAC;YAClEa,MAAM,EAAEnB,aAAa,CAACS,KAAK,EAAEJ,SAAS,CAACM,MAAM,CAACc,YAAY,EAAEnB,MAAM,CAAC;UACrE,CAAC;UACDC,QAAA,EAAAA;QACF,CAAC;MACH,CAAC;IACH,CAAC;IAAA,OAAAhB,KAAA;EAAA;EAAA,IAAAjB,UAAA,CAAAM,OAAA,EAAAb,gBAAA,EAAAuB,qBAAA;EAAA,WAAAnB,aAAA,CAAAS,OAAA,EAAAb,gBAAA;IAAA2D,GAAA;IAAA5D,KAAA,EApCD,SAAO6D,cAAcA,CAAA,EAEF;MACjB,OAAO,IAAI5D,gBAAgB,CAAC,CAAC;IAC/B;EAAA;AAAA,EATQ6D,8BAAuB;AADpB7D,gBAAgB,CAIpB8D,UAAU,GAAG,kBAAkB;AA0CjC,IAAM7D,MAAM,GAAAH,OAAA,CAAAG,MAAA,GAAGD,gBAAgB", "ignoreList": []}