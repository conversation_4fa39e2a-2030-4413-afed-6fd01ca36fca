abad4b8bc4f7422ffdba5df55c32846b
"use strict";

/* istanbul ignore next */
function cov_16gtl7bslq() {
  var path = "/Users/<USER>/projects/react-native/payment-module/src/components/transfer-content-input/types.ts";
  var hash = "ab270029844272c5b961232edff7344ca2c4cae3";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/projects/react-native/payment-module/src/components/transfer-content-input/types.ts",
    statementMap: {
      "0": {
        start: {
          line: 3,
          column: 0
        },
        end: {
          line: 5,
          column: 3
        }
      }
    },
    fnMap: {},
    branchMap: {},
    s: {
      "0": 0
    },
    f: {},
    b: {},
    inputSourceMap: {
      version: 3,
      file: undefined,
      names: [],
      sourceRoot: undefined,
      sources: ["/Users/<USER>/projects/react-native/payment-module/src/components/transfer-content-input/types.ts"],
      sourcesContent: ["import {MSBInputBaseProps} from 'msb-shared-component';\n\nexport type TransferContentInputProps = MSBInputBaseProps & {};\n"],
      mappings: "",
      ignoreList: []
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "ab270029844272c5b961232edff7344ca2c4cae3"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_16gtl7bslq = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_16gtl7bslq();
cov_16gtl7bslq().s[0]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6W10sInNvdXJjZXMiOlsiL1VzZXJzL3NvbnNhbXNldC9wcm9qZWN0cy9yZWFjdC1uYXRpdmUvcGF5bWVudC1tb2R1bGUvc3JjL2NvbXBvbmVudHMvdHJhbnNmZXItY29udGVudC1pbnB1dC90eXBlcy50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQge01TQklucHV0QmFzZVByb3BzfSBmcm9tICdtc2Itc2hhcmVkLWNvbXBvbmVudCc7XG5cbmV4cG9ydCB0eXBlIFRyYW5zZmVyQ29udGVudElucHV0UHJvcHMgPSBNU0JJbnB1dEJhc2VQcm9wcyAmIHt9O1xuIl0sIm1hcHBpbmdzIjoiIiwiaWdub3JlTGlzdCI6W119