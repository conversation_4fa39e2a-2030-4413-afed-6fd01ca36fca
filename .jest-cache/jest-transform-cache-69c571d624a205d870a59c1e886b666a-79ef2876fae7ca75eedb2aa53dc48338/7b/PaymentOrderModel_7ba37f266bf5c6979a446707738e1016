f34d86dc2be13614789967921c48a0fa
"use strict";

/* istanbul ignore next */
function cov_1pg0slapbr() {
  var path = "/Users/<USER>/projects/react-native/payment-module/src/domain/entities/payment-order/PaymentOrderModel.ts";
  var hash = "1cb098ef12612f3db61e44eda097013b56ea42fc";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/projects/react-native/payment-module/src/domain/entities/payment-order/PaymentOrderModel.ts",
    statementMap: {
      "0": {
        start: {
          line: 3,
          column: 29
        },
        end: {
          line: 3,
          column: 84
        }
      },
      "1": {
        start: {
          line: 4,
          column: 20
        },
        end: {
          line: 4,
          column: 89
        }
      },
      "2": {
        start: {
          line: 5,
          column: 23
        },
        end: {
          line: 5,
          column: 95
        }
      },
      "3": {
        start: {
          line: 6,
          column: 0
        },
        end: {
          line: 8,
          column: 3
        }
      },
      "4": {
        start: {
          line: 9,
          column: 0
        },
        end: {
          line: 9,
          column: 35
        }
      },
      "5": {
        start: {
          line: 10,
          column: 24
        },
        end: {
          line: 14,
          column: 2
        }
      },
      "6": {
        start: {
          line: 11,
          column: 2
        },
        end: {
          line: 11,
          column: 57
        }
      },
      "7": {
        start: {
          line: 12,
          column: 2
        },
        end: {
          line: 12,
          column: 23
        }
      },
      "8": {
        start: {
          line: 13,
          column: 2
        },
        end: {
          line: 13,
          column: 19
        }
      },
      "9": {
        start: {
          line: 15,
          column: 0
        },
        end: {
          line: 15,
          column: 46
        }
      }
    },
    fnMap: {
      "0": {
        name: "PaymentOrderModel",
        decl: {
          start: {
            line: 10,
            column: 60
          },
          end: {
            line: 10,
            column: 77
          }
        },
        loc: {
          start: {
            line: 10,
            column: 92
          },
          end: {
            line: 14,
            column: 1
          }
        },
        line: 10
      }
    },
    branchMap: {},
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0
    },
    f: {
      "0": 0
    },
    b: {},
    inputSourceMap: {
      version: 3,
      file: undefined,
      names: ["PaymentOrderModel", "_createClass2", "default", "status", "data", "_classCallCheck2", "exports"],
      sourceRoot: undefined,
      sources: ["/Users/<USER>/projects/react-native/payment-module/src/domain/entities/payment-order/PaymentOrderModel.ts"],
      sourcesContent: ["export class PaymentOrderModel {\n  constructor(public status?: string, public data?: string) {}\n}\n"],
      mappings: ";;;;;;;;;IAAaA,iBAAiB,OAAAC,aAAA,CAAAC,OAAA,EAC5B,SAAAF,kBAAmBG,MAAe,EAASC,IAAa;EAAA,IAAAC,gBAAA,CAAAH,OAAA,QAAAF,iBAAA;EAArC,KAAAG,MAAM,GAANA,MAAM;EAAkB,KAAAC,IAAI,GAAJA,IAAI;AAAY,CAAC;AAD9DE,OAAA,CAAAN,iBAAA,GAAAA,iBAAA",
      ignoreList: []
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "1cb098ef12612f3db61e44eda097013b56ea42fc"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_1pg0slapbr = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_1pg0slapbr();
var _interopRequireDefault =
/* istanbul ignore next */
(cov_1pg0slapbr().s[0]++, require("@babel/runtime/helpers/interopRequireDefault"));
var _createClass2 =
/* istanbul ignore next */
(cov_1pg0slapbr().s[1]++, _interopRequireDefault(require("@babel/runtime/helpers/createClass")));
var _classCallCheck2 =
/* istanbul ignore next */
(cov_1pg0slapbr().s[2]++, _interopRequireDefault(require("@babel/runtime/helpers/classCallCheck")));
/* istanbul ignore next */
cov_1pg0slapbr().s[3]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_1pg0slapbr().s[4]++;
exports.PaymentOrderModel = void 0;
var PaymentOrderModel =
/* istanbul ignore next */
(cov_1pg0slapbr().s[5]++, (0, _createClass2.default)(function PaymentOrderModel(status, data) {
  /* istanbul ignore next */
  cov_1pg0slapbr().f[0]++;
  cov_1pg0slapbr().s[6]++;
  (0, _classCallCheck2.default)(this, PaymentOrderModel);
  /* istanbul ignore next */
  cov_1pg0slapbr().s[7]++;
  this.status = status;
  /* istanbul ignore next */
  cov_1pg0slapbr().s[8]++;
  this.data = data;
}));
/* istanbul ignore next */
cov_1pg0slapbr().s[9]++;
exports.PaymentOrderModel = PaymentOrderModel;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJQYXltZW50T3JkZXJNb2RlbCIsImNvdl8xcGcwc2xhcGJyIiwicyIsIl9jcmVhdGVDbGFzczIiLCJkZWZhdWx0Iiwic3RhdHVzIiwiZGF0YSIsImYiLCJfY2xhc3NDYWxsQ2hlY2syIiwiZXhwb3J0cyJdLCJzb3VyY2VzIjpbIi9Vc2Vycy9zb25zYW1zZXQvcHJvamVjdHMvcmVhY3QtbmF0aXZlL3BheW1lbnQtbW9kdWxlL3NyYy9kb21haW4vZW50aXRpZXMvcGF5bWVudC1vcmRlci9QYXltZW50T3JkZXJNb2RlbC50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgY2xhc3MgUGF5bWVudE9yZGVyTW9kZWwge1xuICBjb25zdHJ1Y3RvcihwdWJsaWMgc3RhdHVzPzogc3RyaW5nLCBwdWJsaWMgZGF0YT86IHN0cmluZykge31cbn1cbiJdLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztJQUFhQSxpQkFBaUI7QUFBQTtBQUFBLENBQUFDLGNBQUEsR0FBQUMsQ0FBQSxXQUFBQyxhQUFBLENBQUFDLE9BQUEsRUFDNUIsU0FBQUosa0JBQW1CSyxNQUFlLEVBQVNDLElBQWE7RUFBQTtFQUFBTCxjQUFBLEdBQUFNLENBQUE7RUFBQU4sY0FBQSxHQUFBQyxDQUFBO0VBQUEsSUFBQU0sZ0JBQUEsQ0FBQUosT0FBQSxRQUFBSixpQkFBQTtFQUFBO0VBQUFDLGNBQUEsR0FBQUMsQ0FBQTtFQUFyQyxLQUFBRyxNQUFNLEdBQU5BLE1BQU07RUFBQTtFQUFBSixjQUFBLEdBQUFDLENBQUE7RUFBa0IsS0FBQUksSUFBSSxHQUFKQSxJQUFJO0FBQVksQ0FBQztBQUFBO0FBQUFMLGNBQUEsR0FBQUMsQ0FBQTtBQUQ5RE8sT0FBQSxDQUFBVCxpQkFBQSxHQUFBQSxpQkFBQSIsImlnbm9yZUxpc3QiOltdfQ==