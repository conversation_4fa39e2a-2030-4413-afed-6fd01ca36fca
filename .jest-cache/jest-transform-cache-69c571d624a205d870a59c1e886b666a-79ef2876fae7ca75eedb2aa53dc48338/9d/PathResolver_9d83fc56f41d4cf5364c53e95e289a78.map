{"version": 3, "names": ["cov_1rhagj6quu", "actualCoverage", "s", "baseUrl", "b", "process", "env", "API_URL", "exports", "PathResolver", "billContact", "saveBillContact", "f", "deleteBillContact", "id", "editBillContact", "myBillContactList", "category", "getMyBillContactRecentList", "getMyBillHistoryList", "paymentOrder", "paymentOrderStatus", "payment", "validate", "arrangement", "sourceAccountList", "customer", "getProfile", "billPay", "categoryList", "providerList", "code", "myBillList", "getBillDetail", "billValidate"], "sources": ["/Users/<USER>/projects/react-native/payment-module/src/utils/PathResolver.ts"], "sourcesContent": ["const baseUrl = process.env.API_URL || '';\n\nexport const PathResolver = {\n  billContact: {\n    saveBillContact: () => `${baseUrl}/contact-manager-extension/client-api/v1/contacts`,\n    deleteBillContact: (id: string) => `${baseUrl}/contact-manager/client-api/v2/contacts/${id}`,\n    editBillContact: (id: string) => `${baseUrl}/contact-manager/client-api/v2/contacts/${id}`,\n    myBillContactList: (category: string) => `${baseUrl}/contact-manager/client-api/v2/contacts?category=${category}`,\n    getMyBillContactRecentList: () => `${baseUrl}/transaction-manager/client-api/v2/transactions`,\n    getMyBillHistoryList: () => `${baseUrl}/transaction-manager/client-api/v2/transactions`,\n  },\n\n  getMyBillHistoryList: {\n    getMyBillHistoryList: () => `${baseUrl}/transaction-manager/client-api/v2/transactions`,\n  },\n\n  paymentOrder: {\n    paymentOrder: () => `${baseUrl}/payment-order-service/client-api/v3/payment-orders`,\n    paymentOrderStatus: (id: string) => `${baseUrl}/payment-order-service/client-api/v3/payment-orders/${id}`,\n  },\n\n  payment: {\n    validate: () => `${baseUrl}/payment/validate`,\n  },\n\n  arrangement: {\n    sourceAccountList: () => `${baseUrl}/arrangement-manager-extension/client-api/v1/arrangements/search`,\n  },\n\n  customer: {\n    getProfile: () => `${baseUrl}/digital-customer/client-api/v1/customers/me`,\n  },\n  billPay: {\n    categoryList: () => `${baseUrl}/product-setup/client-api/v1/product/search?code=BLP.CT&status=ACTIVE`,\n    providerList: (code: string) => `${baseUrl}/contact-manager-extension/client-api/v1/bill-pay/${code}/providers`,\n    myBillList: () => `${baseUrl}/bill-pay/my-bill-list`,\n    getBillDetail: () => `${baseUrl}/billpay-extension/client-api/v1/billpays/bills/query`,\n    billValidate: () => `${baseUrl}/payment-order-service/client-api/v3/payment-orders/validate`,\n  },\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAMI;IAAAA,cAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,cAAA;AAAAA,cAAA,GAAAE,CAAA;;;;;;;AANJ,IAAMC,OAAO;AAAA;AAAA,CAAAH,cAAA,GAAAE,CAAA;AAAG;AAAA,CAAAF,cAAA,GAAAI,CAAA,UAAAC,OAAO,CAACC,GAAG,CAACC,OAAO;AAAA;AAAA,CAAAP,cAAA,GAAAI,CAAA,UAAI,EAAE;AAAA;AAAAJ,cAAA,GAAAE,CAAA;AAE5BM,OAAA,CAAAC,YAAY,GAAG;EAC1BC,WAAW,EAAE;IACXC,eAAe,EAAE,SAAjBA,eAAeA,CAAA;MAAA;MAAAX,cAAA,GAAAY,CAAA;MAAAZ,cAAA,GAAAE,CAAA;MAAA,OAAQ,GAAGC,OAAO,mDAAmD;IAAA;IACpFU,iBAAiB,EAAE,SAAnBA,iBAAiBA,CAAGC,EAAU;MAAA;MAAAd,cAAA,GAAAY,CAAA;MAAAZ,cAAA,GAAAE,CAAA;MAAA,OAAK,GAAGC,OAAO,2CAA2CW,EAAE,EAAE;IAAA;IAC5FC,eAAe,EAAE,SAAjBA,eAAeA,CAAGD,EAAU;MAAA;MAAAd,cAAA,GAAAY,CAAA;MAAAZ,cAAA,GAAAE,CAAA;MAAA,OAAK,GAAGC,OAAO,2CAA2CW,EAAE,EAAE;IAAA;IAC1FE,iBAAiB,EAAE,SAAnBA,iBAAiBA,CAAGC,QAAgB;MAAA;MAAAjB,cAAA,GAAAY,CAAA;MAAAZ,cAAA,GAAAE,CAAA;MAAA,OAAK,GAAGC,OAAO,oDAAoDc,QAAQ,EAAE;IAAA;IACjHC,0BAA0B,EAAE,SAA5BA,0BAA0BA,CAAA;MAAA;MAAAlB,cAAA,GAAAY,CAAA;MAAAZ,cAAA,GAAAE,CAAA;MAAA,OAAQ,GAAGC,OAAO,iDAAiD;IAAA;IAC7FgB,oBAAoB,EAAE,SAAtBA,oBAAoBA,CAAA;MAAA;MAAAnB,cAAA,GAAAY,CAAA;MAAAZ,cAAA,GAAAE,CAAA;MAAA,OAAQ,GAAGC,OAAO,iDAAiD;IAAA;GACxF;EAEDgB,oBAAoB,EAAE;IACpBA,oBAAoB,EAAE,SAAtBA,oBAAoBA,CAAA;MAAA;MAAAnB,cAAA,GAAAY,CAAA;MAAAZ,cAAA,GAAAE,CAAA;MAAA,OAAQ,GAAGC,OAAO,iDAAiD;IAAA;GACxF;EAEDiB,YAAY,EAAE;IACZA,YAAY,EAAE,SAAdA,YAAYA,CAAA;MAAA;MAAApB,cAAA,GAAAY,CAAA;MAAAZ,cAAA,GAAAE,CAAA;MAAA,OAAQ,GAAGC,OAAO,qDAAqD;IAAA;IACnFkB,kBAAkB,EAAE,SAApBA,kBAAkBA,CAAGP,EAAU;MAAA;MAAAd,cAAA,GAAAY,CAAA;MAAAZ,cAAA,GAAAE,CAAA;MAAA,OAAK,GAAGC,OAAO,uDAAuDW,EAAE,EAAE;IAAA;GAC1G;EAEDQ,OAAO,EAAE;IACPC,QAAQ,EAAE,SAAVA,QAAQA,CAAA;MAAA;MAAAvB,cAAA,GAAAY,CAAA;MAAAZ,cAAA,GAAAE,CAAA;MAAA,OAAQ,GAAGC,OAAO,mBAAmB;IAAA;GAC9C;EAEDqB,WAAW,EAAE;IACXC,iBAAiB,EAAE,SAAnBA,iBAAiBA,CAAA;MAAA;MAAAzB,cAAA,GAAAY,CAAA;MAAAZ,cAAA,GAAAE,CAAA;MAAA,OAAQ,GAAGC,OAAO,kEAAkE;IAAA;GACtG;EAEDuB,QAAQ,EAAE;IACRC,UAAU,EAAE,SAAZA,UAAUA,CAAA;MAAA;MAAA3B,cAAA,GAAAY,CAAA;MAAAZ,cAAA,GAAAE,CAAA;MAAA,OAAQ,GAAGC,OAAO,8CAA8C;IAAA;GAC3E;EACDyB,OAAO,EAAE;IACPC,YAAY,EAAE,SAAdA,YAAYA,CAAA;MAAA;MAAA7B,cAAA,GAAAY,CAAA;MAAAZ,cAAA,GAAAE,CAAA;MAAA,OAAQ,GAAGC,OAAO,uEAAuE;IAAA;IACrG2B,YAAY,EAAE,SAAdA,YAAYA,CAAGC,IAAY;MAAA;MAAA/B,cAAA,GAAAY,CAAA;MAAAZ,cAAA,GAAAE,CAAA;MAAA,OAAK,GAAGC,OAAO,qDAAqD4B,IAAI,YAAY;IAAA;IAC/GC,UAAU,EAAE,SAAZA,UAAUA,CAAA;MAAA;MAAAhC,cAAA,GAAAY,CAAA;MAAAZ,cAAA,GAAAE,CAAA;MAAA,OAAQ,GAAGC,OAAO,wBAAwB;IAAA;IACpD8B,aAAa,EAAE,SAAfA,aAAaA,CAAA;MAAA;MAAAjC,cAAA,GAAAY,CAAA;MAAAZ,cAAA,GAAAE,CAAA;MAAA,OAAQ,GAAGC,OAAO,uDAAuD;IAAA;IACtF+B,YAAY,EAAE,SAAdA,YAAYA,CAAA;MAAA;MAAAlC,cAAA,GAAAY,CAAA;MAAAZ,cAAA,GAAAE,CAAA;MAAA,OAAQ,GAAGC,OAAO,8DAA8D;IAAA;;CAE/F", "ignoreList": []}