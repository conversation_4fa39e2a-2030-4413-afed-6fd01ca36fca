{"version": 3, "names": ["_interopRequireDefault", "require", "_classCallCheck2", "_createClass2", "_possibleConstructorReturn2", "_getPrototypeOf2", "_inherits2", "_jsxRuntime", "_callSuper", "t", "o", "e", "default", "_isNativeReflectConstruct", "Reflect", "construct", "constructor", "apply", "Boolean", "prototype", "valueOf", "call", "View", "requireNativeComponent", "React", "RCTScrollView", "mockScrollView", "BaseComponent", "ScrollViewMock", "_BaseComponent", "arguments", "key", "value", "render", "jsxs", "Object", "assign", "props", "children", "refreshControl", "jsx", "module", "exports"], "sources": ["mockScrollView.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @format\n * @flow strict-local\n */\n\n/* eslint-env jest */\n\n'use strict';\n\nconst View = require('../Libraries/Components/View/View');\nconst requireNativeComponent =\n  require('../Libraries/ReactNative/requireNativeComponent').default;\nconst React = require('react');\nconst RCTScrollView: $FlowFixMe = requireNativeComponent('RCTScrollView');\n\nfunction mockScrollView(BaseComponent: $FlowFixMe) {\n  class ScrollViewMock extends BaseComponent {\n    render(): React.MixedElement {\n      return (\n        <RCTScrollView {...this.props}>\n          {this.props.refreshControl}\n          <View>{this.props.children}</View>\n        </RCTScrollView>\n      );\n    }\n  }\n  return ScrollViewMock;\n}\n\nmodule.exports = (mockScrollView: $FlowFixMe);\n"], "mappings": "AAYA,YAAY;;AAAC,IAAAA,sBAAA,GAAAC,OAAA;AAAA,IAAAC,gBAAA,GAAAF,sBAAA,CAAAC,OAAA;AAAA,IAAAE,aAAA,GAAAH,sBAAA,CAAAC,OAAA;AAAA,IAAAG,2BAAA,GAAAJ,sBAAA,CAAAC,OAAA;AAAA,IAAAI,gBAAA,GAAAL,sBAAA,CAAAC,OAAA;AAAA,IAAAK,UAAA,GAAAN,sBAAA,CAAAC,OAAA;AAAA,IAAAM,WAAA,GAAAN,OAAA;AAAA,SAAAO,WAAAC,CAAA,EAAAC,CAAA,EAAAC,CAAA,WAAAD,CAAA,OAAAL,gBAAA,CAAAO,OAAA,EAAAF,CAAA,OAAAN,2BAAA,CAAAQ,OAAA,EAAAH,CAAA,EAAAI,yBAAA,KAAAC,OAAA,CAAAC,SAAA,CAAAL,CAAA,EAAAC,CAAA,YAAAN,gBAAA,CAAAO,OAAA,EAAAH,CAAA,EAAAO,WAAA,IAAAN,CAAA,CAAAO,KAAA,CAAAR,CAAA,EAAAE,CAAA;AAAA,SAAAE,0BAAA,cAAAJ,CAAA,IAAAS,OAAA,CAAAC,SAAA,CAAAC,OAAA,CAAAC,IAAA,CAAAP,OAAA,CAAAC,SAAA,CAAAG,OAAA,iCAAAT,CAAA,aAAAI,yBAAA,YAAAA,0BAAA,aAAAJ,CAAA;AAEb,IAAMa,IAAI,GAAGrB,OAAO,CAAC,mCAAmC,CAAC;AACzD,IAAMsB,sBAAsB,GAC1BtB,OAAO,CAAC,iDAAiD,CAAC,CAACW,OAAO;AACpE,IAAMY,KAAK,GAAGvB,OAAO,CAAC,OAAO,CAAC;AAC9B,IAAMwB,aAAyB,GAAGF,sBAAsB,CAAC,eAAe,CAAC;AAEzE,SAASG,cAAcA,CAACC,aAAyB,EAAE;EAAA,IAC3CC,cAAc,aAAAC,cAAA;IAAA,SAAAD,eAAA;MAAA,IAAA1B,gBAAA,CAAAU,OAAA,QAAAgB,cAAA;MAAA,OAAApB,UAAA,OAAAoB,cAAA,EAAAE,SAAA;IAAA;IAAA,IAAAxB,UAAA,CAAAM,OAAA,EAAAgB,cAAA,EAAAC,cAAA;IAAA,WAAA1B,aAAA,CAAAS,OAAA,EAAAgB,cAAA;MAAAG,GAAA;MAAAC,KAAA,EAClB,SAAAC,MAAMA,CAAA,EAAuB;QAC3B,OACE,IAAA1B,WAAA,CAAA2B,IAAA,EAACT,aAAa,EAAAU,MAAA,CAAAC,MAAA,KAAK,IAAI,CAACC,KAAK;UAAAC,QAAA,GAC1B,IAAI,CAACD,KAAK,CAACE,cAAc,EAC1B,IAAAhC,WAAA,CAAAiC,GAAA,EAAClB,IAAI;YAAAgB,QAAA,EAAE,IAAI,CAACD,KAAK,CAACC;UAAQ,CAAO,CAAC;QAAA,EACrB,CAAC;MAEpB;IAAC;EAAA,EAR0BX,aAAa;EAU1C,OAAOC,cAAc;AACvB;AAEAa,MAAM,CAACC,OAAO,GAAIhB,cAA2B", "ignoreList": []}