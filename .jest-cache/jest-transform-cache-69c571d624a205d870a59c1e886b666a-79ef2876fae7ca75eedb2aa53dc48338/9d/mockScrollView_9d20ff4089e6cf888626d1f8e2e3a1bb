2d0da1716b4d1c1afc7d4eb79dd56963
'use strict';

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
var _classCallCheck2 = _interopRequireDefault(require("@babel/runtime/helpers/classCallCheck"));
var _createClass2 = _interopRequireDefault(require("@babel/runtime/helpers/createClass"));
var _possibleConstructorReturn2 = _interopRequireDefault(require("@babel/runtime/helpers/possibleConstructorReturn"));
var _getPrototypeOf2 = _interopRequireDefault(require("@babel/runtime/helpers/getPrototypeOf"));
var _inherits2 = _interopRequireDefault(require("@babel/runtime/helpers/inherits"));
var _jsxRuntime = require("react/jsx-runtime");
function _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }
function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }
var View = require('../Libraries/Components/View/View');
var requireNativeComponent = require('../Libraries/ReactNative/requireNativeComponent').default;
var React = require('react');
var RCTScrollView = requireNativeComponent('RCTScrollView');
function mockScrollView(BaseComponent) {
  var ScrollViewMock = function (_BaseComponent) {
    function ScrollViewMock() {
      (0, _classCallCheck2.default)(this, ScrollViewMock);
      return _callSuper(this, ScrollViewMock, arguments);
    }
    (0, _inherits2.default)(ScrollViewMock, _BaseComponent);
    return (0, _createClass2.default)(ScrollViewMock, [{
      key: "render",
      value: function render() {
        return (0, _jsxRuntime.jsxs)(RCTScrollView, Object.assign({}, this.props, {
          children: [this.props.refreshControl, (0, _jsxRuntime.jsx)(View, {
            children: this.props.children
          })]
        }));
      }
    }]);
  }(BaseComponent);
  return ScrollViewMock;
}
module.exports = mockScrollView;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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