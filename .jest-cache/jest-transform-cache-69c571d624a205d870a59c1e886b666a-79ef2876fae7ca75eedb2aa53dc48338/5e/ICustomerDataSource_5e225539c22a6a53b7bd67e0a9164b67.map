{"version": 3, "names": [], "sources": ["/Users/<USER>/projects/react-native/payment-module/src/data/datasources/ICustomerDataSource.ts"], "sourcesContent": ["import {GetProfileResponse} from '../models/get-profile/GetProfileResponse';\nimport {BaseResponse} from '../../core/BaseResponse';\n\nexport interface ICustomerDataSource {\n  getProfile(): Promise<BaseResponse<GetProfileResponse>>;\n}\n"], "mappings": "", "ignoreList": []}