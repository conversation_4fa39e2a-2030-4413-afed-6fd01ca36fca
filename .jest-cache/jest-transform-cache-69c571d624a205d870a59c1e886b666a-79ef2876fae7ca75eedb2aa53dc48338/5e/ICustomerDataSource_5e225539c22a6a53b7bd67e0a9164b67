b3b6e9427f71e884513dc850d76269d6
"use strict";

/* istanbul ignore next */
function cov_bu6uh3115() {
  var path = "/Users/<USER>/projects/react-native/payment-module/src/data/datasources/ICustomerDataSource.ts";
  var hash = "69d78b6de042d1ab3584b0a83d650d33d55ae2b6";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/projects/react-native/payment-module/src/data/datasources/ICustomerDataSource.ts",
    statementMap: {
      "0": {
        start: {
          line: 3,
          column: 0
        },
        end: {
          line: 5,
          column: 3
        }
      }
    },
    fnMap: {},
    branchMap: {},
    s: {
      "0": 0
    },
    f: {},
    b: {},
    inputSourceMap: {
      version: 3,
      file: undefined,
      names: [],
      sourceRoot: undefined,
      sources: ["/Users/<USER>/projects/react-native/payment-module/src/data/datasources/ICustomerDataSource.ts"],
      sourcesContent: ["import {GetProfileResponse} from '../models/get-profile/GetProfileResponse';\nimport {BaseResponse} from '../../core/BaseResponse';\n\nexport interface ICustomerDataSource {\n  getProfile(): Promise<BaseResponse<GetProfileResponse>>;\n}\n"],
      mappings: "",
      ignoreList: []
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "69d78b6de042d1ab3584b0a83d650d33d55ae2b6"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_bu6uh3115 = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_bu6uh3115();
cov_bu6uh3115().s[0]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6W10sInNvdXJjZXMiOlsiL1VzZXJzL3NvbnNhbXNldC9wcm9qZWN0cy9yZWFjdC1uYXRpdmUvcGF5bWVudC1tb2R1bGUvc3JjL2RhdGEvZGF0YXNvdXJjZXMvSUN1c3RvbWVyRGF0YVNvdXJjZS50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQge0dldFByb2ZpbGVSZXNwb25zZX0gZnJvbSAnLi4vbW9kZWxzL2dldC1wcm9maWxlL0dldFByb2ZpbGVSZXNwb25zZSc7XG5pbXBvcnQge0Jhc2VSZXNwb25zZX0gZnJvbSAnLi4vLi4vY29yZS9CYXNlUmVzcG9uc2UnO1xuXG5leHBvcnQgaW50ZXJmYWNlIElDdXN0b21lckRhdGFTb3VyY2Uge1xuICBnZXRQcm9maWxlKCk6IFByb21pc2U8QmFzZVJlc3BvbnNlPEdldFByb2ZpbGVSZXNwb25zZT4+O1xufVxuIl0sIm1hcHBpbmdzIjoiIiwiaWdub3JlTGlzdCI6W119