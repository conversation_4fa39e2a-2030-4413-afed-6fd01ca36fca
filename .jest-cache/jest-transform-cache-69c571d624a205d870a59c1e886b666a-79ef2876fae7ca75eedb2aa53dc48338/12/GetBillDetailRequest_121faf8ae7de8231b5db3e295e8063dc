286afc4d08c87<PERSON>abee40f5b8dabfbe5
"use strict";

/* istanbul ignore next */
function cov_x2mkzpb5b() {
  var path = "/Users/<USER>/projects/react-native/payment-module/src/data/models/get-bill-detail/GetBillDetailRequest.ts";
  var hash = "b598852bc9d4cac45e735a0aae31a1cb8bb73144";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/projects/react-native/payment-module/src/data/models/get-bill-detail/GetBillDetailRequest.ts",
    statementMap: {
      "0": {
        start: {
          line: 3,
          column: 0
        },
        end: {
          line: 5,
          column: 3
        }
      }
    },
    fnMap: {},
    branchMap: {},
    s: {
      "0": 0
    },
    f: {},
    b: {},
    inputSourceMap: {
      version: 3,
      file: undefined,
      names: [],
      sourceRoot: undefined,
      sources: ["/Users/<USER>/projects/react-native/payment-module/src/data/models/get-bill-detail/GetBillDetailRequest.ts"],
      sourcesContent: ["export interface GetBillDetailRequest {\n  billCode?: string;\n  serviceCode?: string;\n  accountingType?: 'ACCT' | 'CREDIT';\n  phoneNumber?: string;\n  qrContent?: string;\n  amount?: number;\n}\n"],
      mappings: "",
      ignoreList: []
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "b598852bc9d4cac45e735a0aae31a1cb8bb73144"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_x2mkzpb5b = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_x2mkzpb5b();
cov_x2mkzpb5b().s[0]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6W10sInNvdXJjZXMiOlsiL1VzZXJzL3NvbnNhbXNldC9wcm9qZWN0cy9yZWFjdC1uYXRpdmUvcGF5bWVudC1tb2R1bGUvc3JjL2RhdGEvbW9kZWxzL2dldC1iaWxsLWRldGFpbC9HZXRCaWxsRGV0YWlsUmVxdWVzdC50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgaW50ZXJmYWNlIEdldEJpbGxEZXRhaWxSZXF1ZXN0IHtcbiAgYmlsbENvZGU/OiBzdHJpbmc7XG4gIHNlcnZpY2VDb2RlPzogc3RyaW5nO1xuICBhY2NvdW50aW5nVHlwZT86ICdBQ0NUJyB8ICdDUkVESVQnO1xuICBwaG9uZU51bWJlcj86IHN0cmluZztcbiAgcXJDb250ZW50Pzogc3RyaW5nO1xuICBhbW91bnQ/OiBudW1iZXI7XG59XG4iXSwibWFwcGluZ3MiOiIiLCJpZ25vcmVMaXN0IjpbXX0=