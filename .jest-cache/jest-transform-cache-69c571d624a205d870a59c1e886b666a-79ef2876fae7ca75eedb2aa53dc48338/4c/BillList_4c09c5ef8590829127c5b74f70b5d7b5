7fc49aaa8213a223f9570b82fa8316c6
"use strict";

/* istanbul ignore next */
function cov_1l35xl7ib9() {
  var path = "/Users/<USER>/projects/react-native/payment-module/src/presentation/payment-home/components/bill-list/BillList.tsx";
  var hash = "a520ae5f8a8209227bf740bbb55d9408000781e8";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/projects/react-native/payment-module/src/presentation/payment-home/components/bill-list/BillList.tsx",
    statementMap: {
      "0": {
        start: {
          line: 3,
          column: 22
        },
        end: {
          line: 7,
          column: 1
        }
      },
      "1": {
        start: {
          line: 4,
          column: 2
        },
        end: {
          line: 6,
          column: 4
        }
      },
      "2": {
        start: {
          line: 8,
          column: 0
        },
        end: {
          line: 10,
          column: 3
        }
      },
      "3": {
        start: {
          line: 11,
          column: 0
        },
        end: {
          line: 11,
          column: 26
        }
      },
      "4": {
        start: {
          line: 12,
          column: 21
        },
        end: {
          line: 12,
          column: 44
        }
      },
      "5": {
        start: {
          line: 13,
          column: 14
        },
        end: {
          line: 13,
          column: 47
        }
      },
      "6": {
        start: {
          line: 14,
          column: 37
        },
        end: {
          line: 14,
          column: 76
        }
      },
      "7": {
        start: {
          line: 15,
          column: 22
        },
        end: {
          line: 15,
          column: 48
        }
      },
      "8": {
        start: {
          line: 16,
          column: 25
        },
        end: {
          line: 16,
          column: 54
        }
      },
      "9": {
        start: {
          line: 17,
          column: 15
        },
        end: {
          line: 75,
          column: 1
        }
      },
      "10": {
        start: {
          line: 18,
          column: 18
        },
        end: {
          line: 18,
          column: 32
        }
      },
      "11": {
        start: {
          line: 19,
          column: 17
        },
        end: {
          line: 19,
          column: 32
        }
      },
      "12": {
        start: {
          line: 20,
          column: 17
        },
        end: {
          line: 20,
          column: 32
        }
      },
      "13": {
        start: {
          line: 21,
          column: 12
        },
        end: {
          line: 21,
          column: 22
        }
      },
      "14": {
        start: {
          line: 22,
          column: 13
        },
        end: {
          line: 22,
          column: 24
        }
      },
      "15": {
        start: {
          line: 23,
          column: 14
        },
        end: {
          line: 23,
          column: 26
        }
      },
      "16": {
        start: {
          line: 24,
          column: 15
        },
        end: {
          line: 24,
          column: 28
        }
      },
      "17": {
        start: {
          line: 25,
          column: 16
        },
        end: {
          line: 25,
          column: 30
        }
      },
      "18": {
        start: {
          line: 26,
          column: 25
        },
        end: {
          line: 26,
          column: 56
        }
      },
      "19": {
        start: {
          line: 28,
          column: 2
        },
        end: {
          line: 71,
          column: 3
        }
      },
      "20": {
        start: {
          line: 29,
          column: 4
        },
        end: {
          line: 29,
          column: 53
        }
      },
      "21": {
        start: {
          line: 30,
          column: 4
        },
        end: {
          line: 30,
          column: 94
        }
      },
      "22": {
        start: {
          line: 32,
          column: 19
        },
        end: {
          line: 32,
          column: 21
        }
      },
      "23": {
        start: {
          line: 33,
          column: 4
        },
        end: {
          line: 70,
          column: 5
        }
      },
      "24": {
        start: {
          line: 34,
          column: 6
        },
        end: {
          line: 34,
          column: 58
        }
      },
      "25": {
        start: {
          line: 35,
          column: 6
        },
        end: {
          line: 37,
          column: 9
        }
      },
      "26": {
        start: {
          line: 39,
          column: 6
        },
        end: {
          line: 41,
          column: 9
        }
      },
      "27": {
        start: {
          line: 40,
          column: 8
        },
        end: {
          line: 40,
          column: 66
        }
      },
      "28": {
        start: {
          line: 42,
          column: 6
        },
        end: {
          line: 69,
          column: 7
        }
      },
      "29": {
        start: {
          line: 43,
          column: 8
        },
        end: {
          line: 43,
          column: 62
        }
      },
      "30": {
        start: {
          line: 44,
          column: 8
        },
        end: {
          line: 44,
          column: 95
        }
      },
      "31": {
        start: {
          line: 46,
          column: 8
        },
        end: {
          line: 46,
          column: 51
        }
      },
      "32": {
        start: {
          line: 47,
          column: 8
        },
        end: {
          line: 68,
          column: 12
        }
      },
      "33": {
        start: {
          line: 52,
          column: 12
        },
        end: {
          line: 52,
          column: 36
        }
      },
      "34": {
        start: {
          line: 55,
          column: 23
        },
        end: {
          line: 55,
          column: 33
        }
      },
      "35": {
        start: {
          line: 56,
          column: 12
        },
        end: {
          line: 66,
          column: 15
        }
      },
      "36": {
        start: {
          line: 72,
          column: 2
        },
        end: {
          line: 74,
          column: 14
        }
      },
      "37": {
        start: {
          line: 76,
          column: 0
        },
        end: {
          line: 76,
          column: 28
        }
      },
      "38": {
        start: {
          line: 77,
          column: 13
        },
        end: {
          line: 84,
          column: 2
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 3,
            column: 54
          },
          end: {
            line: 3,
            column: 55
          }
        },
        loc: {
          start: {
            line: 3,
            column: 69
          },
          end: {
            line: 7,
            column: 1
          }
        },
        line: 3
      },
      "1": {
        name: "BillList",
        decl: {
          start: {
            line: 17,
            column: 24
          },
          end: {
            line: 17,
            column: 32
          }
        },
        loc: {
          start: {
            line: 17,
            column: 39
          },
          end: {
            line: 75,
            column: 1
          }
        },
        line: 17
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 39,
            column: 30
          },
          end: {
            line: 39,
            column: 31
          }
        },
        loc: {
          start: {
            line: 39,
            column: 46
          },
          end: {
            line: 41,
            column: 7
          }
        },
        line: 39
      },
      "3": {
        name: "keyExtractor",
        decl: {
          start: {
            line: 51,
            column: 33
          },
          end: {
            line: 51,
            column: 45
          }
        },
        loc: {
          start: {
            line: 51,
            column: 59
          },
          end: {
            line: 53,
            column: 11
          }
        },
        line: 51
      },
      "4": {
        name: "renderItem",
        decl: {
          start: {
            line: 54,
            column: 31
          },
          end: {
            line: 54,
            column: 41
          }
        },
        loc: {
          start: {
            line: 54,
            column: 49
          },
          end: {
            line: 67,
            column: 11
          }
        },
        line: 54
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 3,
            column: 22
          },
          end: {
            line: 7,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 3,
            column: 22
          },
          end: {
            line: 3,
            column: 26
          }
        }, {
          start: {
            line: 3,
            column: 30
          },
          end: {
            line: 3,
            column: 50
          }
        }, {
          start: {
            line: 3,
            column: 54
          },
          end: {
            line: 7,
            column: 1
          }
        }],
        line: 3
      },
      "1": {
        loc: {
          start: {
            line: 4,
            column: 9
          },
          end: {
            line: 6,
            column: 3
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 4,
            column: 33
          },
          end: {
            line: 4,
            column: 36
          }
        }, {
          start: {
            line: 4,
            column: 39
          },
          end: {
            line: 6,
            column: 3
          }
        }],
        line: 4
      },
      "2": {
        loc: {
          start: {
            line: 4,
            column: 9
          },
          end: {
            line: 4,
            column: 30
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 4,
            column: 9
          },
          end: {
            line: 4,
            column: 12
          }
        }, {
          start: {
            line: 4,
            column: 16
          },
          end: {
            line: 4,
            column: 30
          }
        }],
        line: 4
      },
      "3": {
        loc: {
          start: {
            line: 28,
            column: 2
          },
          end: {
            line: 71,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 28,
            column: 2
          },
          end: {
            line: 71,
            column: 3
          }
        }, {
          start: {
            line: 31,
            column: 9
          },
          end: {
            line: 71,
            column: 3
          }
        }],
        line: 28
      },
      "4": {
        loc: {
          start: {
            line: 33,
            column: 4
          },
          end: {
            line: 70,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 33,
            column: 4
          },
          end: {
            line: 70,
            column: 5
          }
        }, {
          start: {
            line: 38,
            column: 11
          },
          end: {
            line: 70,
            column: 5
          }
        }],
        line: 33
      },
      "5": {
        loc: {
          start: {
            line: 42,
            column: 6
          },
          end: {
            line: 69,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 42,
            column: 6
          },
          end: {
            line: 69,
            column: 7
          }
        }, {
          start: {
            line: 45,
            column: 13
          },
          end: {
            line: 69,
            column: 7
          }
        }],
        line: 42
      },
      "6": {
        loc: {
          start: {
            line: 65,
            column: 26
          },
          end: {
            line: 65,
            column: 62
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 65,
            column: 47
          },
          end: {
            line: 65,
            column: 57
          }
        }, {
          start: {
            line: 65,
            column: 60
          },
          end: {
            line: 65,
            column: 62
          }
        }],
        line: 65
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0
    },
    b: {
      "0": [0, 0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0]
    },
    inputSourceMap: {
      version: 3,
      file: undefined,
      names: ["react_native_1", "require", "react_1", "__importDefault", "react_native_gesture_handler_1", "EmptyBill_tsx_1", "BillListItem_tsx_1", "BillList", "_ref", "isBlocked", "isEditable", "searchText", "bills", "onEdit", "onClick", "onDelete", "onPayment", "normalizedSearch", "trim", "toLowerCase", "content", "undefined", "console", "log", "default", "createElement", "EmptyBillSystemErrorScreen", "filtered", "length", "EmptyScreen", "isRecent", "filter", "bill", "getSearchContent", "includes", "EmptyBillFilteredScreen", "View", "style", "styles", "container", "FlatList", "data", "keyExtractor", "item", "index", "toString", "renderItem", "_ref2", "BillListItem", "key", "getId", "highlight", "exports", "StyleSheet", "create", "flexDirection", "flex", "justifyContent", "width"],
      sourceRoot: undefined,
      sources: ["/Users/<USER>/projects/react-native/payment-module/src/presentation/payment-home/components/bill-list/BillList.tsx"],
      sourcesContent: ["import {StyleSheet, View} from 'react-native';\nimport React from 'react';\nimport {FlatList} from 'react-native-gesture-handler';\n\nimport {EmptyBillFilteredScreen, EmptyBillSystemErrorScreen, EmptyScreen} from './EmptyBill.tsx';\nimport {BillListItem} from './BillListItem.tsx';\nimport {IBillContact} from '../../../../domain/entities/IBillContact.ts';\n\ntype Props = {\n  isBlocked: boolean;\n  isEditable: boolean;\n  searchText: string;\n  bills?: IBillContact[];\n  onClick: (item: IBillContact) => void;\n  onEdit?: (item: IBillContact) => void;\n  onDelete?: (item: IBillContact) => void;\n  onPayment?: (item: IBillContact) => void;\n};\n\nexport const BillList = ({isBlocked, isEditable, searchText, bills, onEdit, onClick, onDelete, onPayment}: Props) => {\n  const normalizedSearch = searchText.trim().toLowerCase();\n  let content: React.JSX.Element;\n  if (bills === undefined) {\n    console.log('BillList: ==>>>>> undefined bills');\n    content = <EmptyBillSystemErrorScreen />;\n  } else {\n    let filtered: IBillContact[] | undefined = [];\n    if (bills.length === 0) {\n      console.log('BillList: ==>>>>> bills.length === 0');\n      content = <EmptyScreen isRecent={!isEditable} />;\n    } else {\n      filtered = bills.filter(bill => {\n        return bill.getSearchContent().includes(normalizedSearch);\n      });\n\n      // console.log('filteredAccountContact', filteredAccountContact);\n\n      if (filtered.length === 0) {\n        console.log('BillList: ==>>>>> empty filtered bills');\n        content = <EmptyBillFilteredScreen />;\n      } else {\n        console.log('BillList: ==>>>>> NGON DATA');\n        content = (\n          <View style={styles.container}>\n            <FlatList\n              data={filtered}\n              keyExtractor={(item, index) => index.toString()}\n              renderItem={({item}) => (\n                <BillListItem\n                  key={item.getId()}\n                  item={item}\n                  isEditable={isEditable}\n                  onEdit={onEdit}\n                  onDelete={onDelete}\n                  onClick={onClick}\n                  onPayment={onPayment}\n                  isBlocked={isBlocked}\n                  highlight={(searchText ?? '').trim()}\n                />\n              )}\n            />\n          </View>\n        );\n      }\n    }\n  }\n  return <View style={styles.container}>{content}</View>;\n};\n\nconst styles = StyleSheet.create({\n  container: {\n    flexDirection: 'column',\n    flex: 1,\n    justifyContent: 'center',\n    width: '100%',\n  },\n});\n"],
      mappings: ";;;;;;;;;;;AAAA,IAAAA,cAAA,GAAAC,OAAA;AACA,IAAAC,OAAA,GAAAC,eAAA,CAAAF,OAAA;AACA,IAAAG,8BAAA,GAAAH,OAAA;AAEA,IAAAI,eAAA,GAAAJ,OAAA;AACA,IAAAK,kBAAA,GAAAL,OAAA;AAcO,IAAMM,QAAQ,GAAG,SAAXA,QAAQA,CAAAC,IAAA,EAA+F;EAAA,IAA1FC,SAAS,GAAAD,IAAA,CAATC,SAAS;IAAEC,UAAU,GAAAF,IAAA,CAAVE,UAAU;IAAEC,UAAU,GAAAH,IAAA,CAAVG,UAAU;IAAEC,KAAK,GAAAJ,IAAA,CAALI,KAAK;IAAEC,MAAM,GAAAL,IAAA,CAANK,MAAM;IAAEC,OAAO,GAAAN,IAAA,CAAPM,OAAO;IAAEC,QAAQ,GAAAP,IAAA,CAARO,QAAQ;IAAEC,SAAS,GAAAR,IAAA,CAATQ,SAAS;EACtG,IAAMC,gBAAgB,GAAGN,UAAU,CAACO,IAAI,EAAE,CAACC,WAAW,EAAE;EACxD,IAAIC,OAA0B;EAC9B,IAAIR,KAAK,KAAKS,SAAS,EAAE;IACvBC,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;IAChDH,OAAO,GAAGlB,OAAA,CAAAsB,OAAA,CAAAC,aAAA,CAACpB,eAAA,CAAAqB,0BAA0B,OAAG;EAC1C,CAAC,MAAM;IACL,IAAIC,QAAQ,GAA+B,EAAE;IAC7C,IAAIf,KAAK,CAACgB,MAAM,KAAK,CAAC,EAAE;MACtBN,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC;MACnDH,OAAO,GAAGlB,OAAA,CAAAsB,OAAA,CAAAC,aAAA,CAACpB,eAAA,CAAAwB,WAAW;QAACC,QAAQ,EAAE,CAACpB;MAAU,EAAI;IAClD,CAAC,MAAM;MACLiB,QAAQ,GAAGf,KAAK,CAACmB,MAAM,CAAC,UAAAC,IAAI,EAAG;QAC7B,OAAOA,IAAI,CAACC,gBAAgB,EAAE,CAACC,QAAQ,CAACjB,gBAAgB,CAAC;MAC3D,CAAC,CAAC;MAIF,IAAIU,QAAQ,CAACC,MAAM,KAAK,CAAC,EAAE;QACzBN,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;QACrDH,OAAO,GAAGlB,OAAA,CAAAsB,OAAA,CAAAC,aAAA,CAACpB,eAAA,CAAA8B,uBAAuB,OAAG;MACvC,CAAC,MAAM;QACLb,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC;QAC1CH,OAAO,GACLlB,OAAA,CAAAsB,OAAA,CAAAC,aAAA,CAACzB,cAAA,CAAAoC,IAAI;UAACC,KAAK,EAAEC,MAAM,CAACC;QAAS,GAC3BrC,OAAA,CAAAsB,OAAA,CAAAC,aAAA,CAACrB,8BAAA,CAAAoC,QAAQ;UACPC,IAAI,EAAEd,QAAQ;UACde,YAAY,EAAE,SAAdA,YAAYA,CAAGC,IAAI,EAAEC,KAAK;YAAA,OAAKA,KAAK,CAACC,QAAQ,EAAE;UAAA;UAC/CC,UAAU,EAAE,SAAZA,UAAUA,CAAAC,KAAA;YAAA,IAAIJ,IAAI,GAAAI,KAAA,CAAJJ,IAAI;YAAA,OAChBzC,OAAA,CAAAsB,OAAA,CAAAC,aAAA,CAACnB,kBAAA,CAAA0C,YAAY;cACXC,GAAG,EAAEN,IAAI,CAACO,KAAK,EAAE;cACjBP,IAAI,EAAEA,IAAI;cACVjC,UAAU,EAAEA,UAAU;cACtBG,MAAM,EAAEA,MAAM;cACdE,QAAQ,EAAEA,QAAQ;cAClBD,OAAO,EAAEA,OAAO;cAChBE,SAAS,EAAEA,SAAS;cACpBP,SAAS,EAAEA,SAAS;cACpB0C,SAAS,EAAE,CAACxC,UAAU,WAAVA,UAAU,GAAI,EAAE,EAAEO,IAAI;YAAE,EACpC;UAAA;QACH,EACD,CAEL;MACH;IACF;EACF;EACA,OAAOhB,OAAA,CAAAsB,OAAA,CAAAC,aAAA,CAACzB,cAAA,CAAAoC,IAAI;IAACC,KAAK,EAAEC,MAAM,CAACC;EAAS,GAAGnB,OAAO,CAAQ;AACxD,CAAC;AAhDYgC,OAAA,CAAA7C,QAAQ,GAAAA,QAAA;AAkDrB,IAAM+B,MAAM,GAAGtC,cAAA,CAAAqD,UAAU,CAACC,MAAM,CAAC;EAC/Bf,SAAS,EAAE;IACTgB,aAAa,EAAE,QAAQ;IACvBC,IAAI,EAAE,CAAC;IACPC,cAAc,EAAE,QAAQ;IACxBC,KAAK,EAAE;;CAEV,CAAC",
      ignoreList: []
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "a520ae5f8a8209227bf740bbb55d9408000781e8"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_1l35xl7ib9 = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_1l35xl7ib9();
var __importDefault =
/* istanbul ignore next */
(cov_1l35xl7ib9().s[0]++,
/* istanbul ignore next */
(cov_1l35xl7ib9().b[0][0]++, this) &&
/* istanbul ignore next */
(cov_1l35xl7ib9().b[0][1]++, this.__importDefault) ||
/* istanbul ignore next */
(cov_1l35xl7ib9().b[0][2]++, function (mod) {
  /* istanbul ignore next */
  cov_1l35xl7ib9().f[0]++;
  cov_1l35xl7ib9().s[1]++;
  return /* istanbul ignore next */(cov_1l35xl7ib9().b[2][0]++, mod) &&
  /* istanbul ignore next */
  (cov_1l35xl7ib9().b[2][1]++, mod.__esModule) ?
  /* istanbul ignore next */
  (cov_1l35xl7ib9().b[1][0]++, mod) :
  /* istanbul ignore next */
  (cov_1l35xl7ib9().b[1][1]++, {
    "default": mod
  });
}));
/* istanbul ignore next */
cov_1l35xl7ib9().s[2]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_1l35xl7ib9().s[3]++;
exports.BillList = void 0;
var react_native_1 =
/* istanbul ignore next */
(cov_1l35xl7ib9().s[4]++, require("react-native"));
var react_1 =
/* istanbul ignore next */
(cov_1l35xl7ib9().s[5]++, __importDefault(require("react")));
var react_native_gesture_handler_1 =
/* istanbul ignore next */
(cov_1l35xl7ib9().s[6]++, require("react-native-gesture-handler"));
var EmptyBill_tsx_1 =
/* istanbul ignore next */
(cov_1l35xl7ib9().s[7]++, require("./EmptyBill.tsx"));
var BillListItem_tsx_1 =
/* istanbul ignore next */
(cov_1l35xl7ib9().s[8]++, require("./BillListItem.tsx"));
/* istanbul ignore next */
cov_1l35xl7ib9().s[9]++;
var BillList = function BillList(_ref) {
  /* istanbul ignore next */
  cov_1l35xl7ib9().f[1]++;
  var isBlocked =
    /* istanbul ignore next */
    (cov_1l35xl7ib9().s[10]++, _ref.isBlocked),
    isEditable =
    /* istanbul ignore next */
    (cov_1l35xl7ib9().s[11]++, _ref.isEditable),
    searchText =
    /* istanbul ignore next */
    (cov_1l35xl7ib9().s[12]++, _ref.searchText),
    bills =
    /* istanbul ignore next */
    (cov_1l35xl7ib9().s[13]++, _ref.bills),
    onEdit =
    /* istanbul ignore next */
    (cov_1l35xl7ib9().s[14]++, _ref.onEdit),
    onClick =
    /* istanbul ignore next */
    (cov_1l35xl7ib9().s[15]++, _ref.onClick),
    onDelete =
    /* istanbul ignore next */
    (cov_1l35xl7ib9().s[16]++, _ref.onDelete),
    onPayment =
    /* istanbul ignore next */
    (cov_1l35xl7ib9().s[17]++, _ref.onPayment);
  var normalizedSearch =
  /* istanbul ignore next */
  (cov_1l35xl7ib9().s[18]++, searchText.trim().toLowerCase());
  var content;
  /* istanbul ignore next */
  cov_1l35xl7ib9().s[19]++;
  if (bills === undefined) {
    /* istanbul ignore next */
    cov_1l35xl7ib9().b[3][0]++;
    cov_1l35xl7ib9().s[20]++;
    console.log('BillList: ==>>>>> undefined bills');
    /* istanbul ignore next */
    cov_1l35xl7ib9().s[21]++;
    content = react_1.default.createElement(EmptyBill_tsx_1.EmptyBillSystemErrorScreen, null);
  } else {
    /* istanbul ignore next */
    cov_1l35xl7ib9().b[3][1]++;
    var filtered =
    /* istanbul ignore next */
    (cov_1l35xl7ib9().s[22]++, []);
    /* istanbul ignore next */
    cov_1l35xl7ib9().s[23]++;
    if (bills.length === 0) {
      /* istanbul ignore next */
      cov_1l35xl7ib9().b[4][0]++;
      cov_1l35xl7ib9().s[24]++;
      console.log('BillList: ==>>>>> bills.length === 0');
      /* istanbul ignore next */
      cov_1l35xl7ib9().s[25]++;
      content = react_1.default.createElement(EmptyBill_tsx_1.EmptyScreen, {
        isRecent: !isEditable
      });
    } else {
      /* istanbul ignore next */
      cov_1l35xl7ib9().b[4][1]++;
      cov_1l35xl7ib9().s[26]++;
      filtered = bills.filter(function (bill) {
        /* istanbul ignore next */
        cov_1l35xl7ib9().f[2]++;
        cov_1l35xl7ib9().s[27]++;
        return bill.getSearchContent().includes(normalizedSearch);
      });
      /* istanbul ignore next */
      cov_1l35xl7ib9().s[28]++;
      if (filtered.length === 0) {
        /* istanbul ignore next */
        cov_1l35xl7ib9().b[5][0]++;
        cov_1l35xl7ib9().s[29]++;
        console.log('BillList: ==>>>>> empty filtered bills');
        /* istanbul ignore next */
        cov_1l35xl7ib9().s[30]++;
        content = react_1.default.createElement(EmptyBill_tsx_1.EmptyBillFilteredScreen, null);
      } else {
        /* istanbul ignore next */
        cov_1l35xl7ib9().b[5][1]++;
        cov_1l35xl7ib9().s[31]++;
        console.log('BillList: ==>>>>> NGON DATA');
        /* istanbul ignore next */
        cov_1l35xl7ib9().s[32]++;
        content = react_1.default.createElement(react_native_1.View, {
          style: styles.container
        }, react_1.default.createElement(react_native_gesture_handler_1.FlatList, {
          data: filtered,
          keyExtractor: function keyExtractor(item, index) {
            /* istanbul ignore next */
            cov_1l35xl7ib9().f[3]++;
            cov_1l35xl7ib9().s[33]++;
            return index.toString();
          },
          renderItem: function renderItem(_ref2) {
            /* istanbul ignore next */
            cov_1l35xl7ib9().f[4]++;
            var item =
            /* istanbul ignore next */
            (cov_1l35xl7ib9().s[34]++, _ref2.item);
            /* istanbul ignore next */
            cov_1l35xl7ib9().s[35]++;
            return react_1.default.createElement(BillListItem_tsx_1.BillListItem, {
              key: item.getId(),
              item: item,
              isEditable: isEditable,
              onEdit: onEdit,
              onDelete: onDelete,
              onClick: onClick,
              onPayment: onPayment,
              isBlocked: isBlocked,
              highlight: (searchText != null ?
              /* istanbul ignore next */
              (cov_1l35xl7ib9().b[6][0]++, searchText) :
              /* istanbul ignore next */
              (cov_1l35xl7ib9().b[6][1]++, '')).trim()
            });
          }
        }));
      }
    }
  }
  /* istanbul ignore next */
  cov_1l35xl7ib9().s[36]++;
  return react_1.default.createElement(react_native_1.View, {
    style: styles.container
  }, content);
};
/* istanbul ignore next */
cov_1l35xl7ib9().s[37]++;
exports.BillList = BillList;
var styles =
/* istanbul ignore next */
(cov_1l35xl7ib9().s[38]++, react_native_1.StyleSheet.create({
  container: {
    flexDirection: 'column',
    flex: 1,
    justifyContent: 'center',
    width: '100%'
  }
}));
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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