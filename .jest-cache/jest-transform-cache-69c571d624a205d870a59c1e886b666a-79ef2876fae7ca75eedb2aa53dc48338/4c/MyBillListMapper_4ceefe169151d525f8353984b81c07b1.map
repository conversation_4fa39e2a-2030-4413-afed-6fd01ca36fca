{"version": 3, "names": ["cov_29vbnljbv1", "actualCoverage", "s", "exports", "mapMyBillListResponseToModel", "MyBillContactListModel_1", "require", "response", "f", "console", "log", "map", "bill", "mapBillContactResponseToModel", "_response$accounts", "data", "MyBillContactModel", "id", "name", "alias", "category", "activeStatus", "accessContextScope", "accounts", "b", "account", "mapAccountResponseToModel", "AccountModel", "bankName", "accountNumber", "bankCode", "accountType", "externalId", "bankPostCode"], "sources": ["/Users/<USER>/projects/react-native/payment-module/src/data/mappers/my-bill-list/MyBillListMapper.ts"], "sourcesContent": ["import {\n  AccountModel,\n  MyBillContactListModel,\n  MyBillContactModel,\n} from '../../../domain/entities/my-bill-contact-list/MyBillContactListModel';\nimport {AccountResponse, MyBillContactResponse, MyBillListResponse} from '../../models/my-bill-list/MyBillListResponse';\n\nexport function mapMyBillListResponseToModel(response: MyBillListResponse): MyBillContactListModel {\n  console.log('-------------LOG RESPONSE', response);\n  return response.map(bill => mapBillContactResponseToModel(bill));\n}\n\nfunction mapBillContactResponseToModel(response: MyBillContactResponse): MyBillContactModel {\n  const data = new MyBillContactModel(\n    response.id,\n    response.name,\n    response.alias,\n    response.category,\n    response.activeStatus,\n    response.accessContextScope,\n    response.accounts?.map(account => mapAccountResponseToModel(account)),\n  );\n\n  console.log('LOG CONTACT', data, response);\n  console.log('LOG RESPONSE', response);\n  return data;\n}\n\nfunction mapAccountResponseToModel(account: AccountResponse): AccountModel {\n  return new AccountModel(\n    account.bankName,\n    account.accountNumber,\n    account.bankCode,\n    account.accountType,\n    account.externalId,\n    account.bankPostCode,\n  );\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAaY;IAAAA,cAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,cAAA;AAAAA,cAAA,GAAAE,CAAA;;;;;;AANZC,OAAA,CAAAC,4BAAA,GAAAA,4BAAA;AAPA,IAAAC,wBAAA;AAAA;AAAA,CAAAL,cAAA,GAAAE,CAAA,OAAAI,OAAA;AAOA,SAAgBF,4BAA4BA,CAACG,QAA4B;EAAA;EAAAP,cAAA,GAAAQ,CAAA;EAAAR,cAAA,GAAAE,CAAA;EACvEO,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEH,QAAQ,CAAC;EAAA;EAAAP,cAAA,GAAAE,CAAA;EAClD,OAAOK,QAAQ,CAACI,GAAG,CAAC,UAAAC,IAAI;IAAA;IAAAZ,cAAA,GAAAQ,CAAA;IAAAR,cAAA,GAAAE,CAAA;IAAA,OAAIW,6BAA6B,CAACD,IAAI,CAAC;EAAA,EAAC;AAClE;AAEA,SAASC,6BAA6BA,CAACN,QAA+B;EAAA;EAAAP,cAAA,GAAAQ,CAAA;EAAA,IAAAM,kBAAA;EACpE,IAAMC,IAAI;EAAA;EAAA,CAAAf,cAAA,GAAAE,CAAA,OAAG,IAAIG,wBAAA,CAAAW,kBAAkB,CACjCT,QAAQ,CAACU,EAAE,EACXV,QAAQ,CAACW,IAAI,EACbX,QAAQ,CAACY,KAAK,EACdZ,QAAQ,CAACa,QAAQ,EACjBb,QAAQ,CAACc,YAAY,EACrBd,QAAQ,CAACe,kBAAkB,GAAAR,kBAAA,GAC3BP,QAAQ,CAACgB,QAAQ;EAAA;EAAA,CAAAvB,cAAA,GAAAwB,CAAA;EAAA;EAAA,CAAAxB,cAAA,GAAAwB,CAAA,UAAjBV,kBAAA,CAAmBH,GAAG,CAAC,UAAAc,OAAO;IAAA;IAAAzB,cAAA,GAAAQ,CAAA;IAAAR,cAAA,GAAAE,CAAA;IAAA,OAAIwB,yBAAyB,CAACD,OAAO,CAAC;EAAA,EAAC,EACtE;EAAA;EAAAzB,cAAA,GAAAE,CAAA;EAEDO,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEK,IAAI,EAAER,QAAQ,CAAC;EAAA;EAAAP,cAAA,GAAAE,CAAA;EAC1CO,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEH,QAAQ,CAAC;EAAA;EAAAP,cAAA,GAAAE,CAAA;EACrC,OAAOa,IAAI;AACb;AAEA,SAASW,yBAAyBA,CAACD,OAAwB;EAAA;EAAAzB,cAAA,GAAAQ,CAAA;EAAAR,cAAA,GAAAE,CAAA;EACzD,OAAO,IAAIG,wBAAA,CAAAsB,YAAY,CACrBF,OAAO,CAACG,QAAQ,EAChBH,OAAO,CAACI,aAAa,EACrBJ,OAAO,CAACK,QAAQ,EAChBL,OAAO,CAACM,WAAW,EACnBN,OAAO,CAACO,UAAU,EAClBP,OAAO,CAACQ,YAAY,CACrB;AACH", "ignoreList": []}