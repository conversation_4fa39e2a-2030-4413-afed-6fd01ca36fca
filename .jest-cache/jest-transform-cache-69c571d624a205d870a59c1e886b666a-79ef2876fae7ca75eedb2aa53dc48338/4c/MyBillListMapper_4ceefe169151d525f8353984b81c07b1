4851c51fe980af45d176a2f457c7e30f
"use strict";

/* istanbul ignore next */
function cov_29vbnljbv1() {
  var path = "/Users/<USER>/projects/react-native/payment-module/src/data/mappers/my-bill-list/MyBillListMapper.ts";
  var hash = "ffef8080e3a824e9d051e82677a78eb9fcb06049";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/projects/react-native/payment-module/src/data/mappers/my-bill-list/MyBillListMapper.ts",
    statementMap: {
      "0": {
        start: {
          line: 3,
          column: 0
        },
        end: {
          line: 5,
          column: 3
        }
      },
      "1": {
        start: {
          line: 6,
          column: 0
        },
        end: {
          line: 6,
          column: 68
        }
      },
      "2": {
        start: {
          line: 7,
          column: 31
        },
        end: {
          line: 7,
          column: 110
        }
      },
      "3": {
        start: {
          line: 9,
          column: 2
        },
        end: {
          line: 9,
          column: 53
        }
      },
      "4": {
        start: {
          line: 10,
          column: 2
        },
        end: {
          line: 12,
          column: 5
        }
      },
      "5": {
        start: {
          line: 11,
          column: 4
        },
        end: {
          line: 11,
          column: 47
        }
      },
      "6": {
        start: {
          line: 16,
          column: 13
        },
        end: {
          line: 18,
          column: 5
        }
      },
      "7": {
        start: {
          line: 17,
          column: 4
        },
        end: {
          line: 17,
          column: 46
        }
      },
      "8": {
        start: {
          line: 19,
          column: 2
        },
        end: {
          line: 19,
          column: 45
        }
      },
      "9": {
        start: {
          line: 20,
          column: 2
        },
        end: {
          line: 20,
          column: 40
        }
      },
      "10": {
        start: {
          line: 21,
          column: 2
        },
        end: {
          line: 21,
          column: 14
        }
      },
      "11": {
        start: {
          line: 24,
          column: 2
        },
        end: {
          line: 24,
          column: 173
        }
      }
    },
    fnMap: {
      "0": {
        name: "mapMyBillListResponseToModel",
        decl: {
          start: {
            line: 8,
            column: 9
          },
          end: {
            line: 8,
            column: 37
          }
        },
        loc: {
          start: {
            line: 8,
            column: 48
          },
          end: {
            line: 13,
            column: 1
          }
        },
        line: 8
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 10,
            column: 22
          },
          end: {
            line: 10,
            column: 23
          }
        },
        loc: {
          start: {
            line: 10,
            column: 38
          },
          end: {
            line: 12,
            column: 3
          }
        },
        line: 10
      },
      "2": {
        name: "mapBillContactResponseToModel",
        decl: {
          start: {
            line: 14,
            column: 9
          },
          end: {
            line: 14,
            column: 38
          }
        },
        loc: {
          start: {
            line: 14,
            column: 49
          },
          end: {
            line: 22,
            column: 1
          }
        },
        line: 14
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 16,
            column: 259
          },
          end: {
            line: 16,
            column: 260
          }
        },
        loc: {
          start: {
            line: 16,
            column: 278
          },
          end: {
            line: 18,
            column: 3
          }
        },
        line: 16
      },
      "4": {
        name: "mapAccountResponseToModel",
        decl: {
          start: {
            line: 23,
            column: 9
          },
          end: {
            line: 23,
            column: 34
          }
        },
        loc: {
          start: {
            line: 23,
            column: 44
          },
          end: {
            line: 25,
            column: 1
          }
        },
        line: 23
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 16,
            column: 176
          },
          end: {
            line: 18,
            column: 4
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 16,
            column: 227
          },
          end: {
            line: 16,
            column: 233
          }
        }, {
          start: {
            line: 16,
            column: 236
          },
          end: {
            line: 18,
            column: 4
          }
        }],
        line: 16
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0
    },
    b: {
      "0": [0, 0]
    },
    inputSourceMap: {
      version: 3,
      file: undefined,
      names: ["exports", "mapMyBillListResponseToModel", "MyBillContactListModel_1", "require", "response", "console", "log", "map", "bill", "mapBillContactResponseToModel", "_response$accounts", "data", "MyBillContactModel", "id", "name", "alias", "category", "activeStatus", "accessContextScope", "accounts", "account", "mapAccountResponseToModel", "AccountModel", "bankName", "accountNumber", "bankCode", "accountType", "externalId", "bankPostCode"],
      sourceRoot: undefined,
      sources: ["/Users/<USER>/projects/react-native/payment-module/src/data/mappers/my-bill-list/MyBillListMapper.ts"],
      sourcesContent: ["import {\n  AccountModel,\n  MyBillContactListModel,\n  MyBillContactModel,\n} from '../../../domain/entities/my-bill-contact-list/MyBillContactListModel';\nimport {AccountResponse, MyBillContactResponse, MyBillListResponse} from '../../models/my-bill-list/MyBillListResponse';\n\nexport function mapMyBillListResponseToModel(response: MyBillListResponse): MyBillContactListModel {\n  console.log('-------------LOG RESPONSE', response);\n  return response.map(bill => mapBillContactResponseToModel(bill));\n}\n\nfunction mapBillContactResponseToModel(response: MyBillContactResponse): MyBillContactModel {\n  const data = new MyBillContactModel(\n    response.id,\n    response.name,\n    response.alias,\n    response.category,\n    response.activeStatus,\n    response.accessContextScope,\n    response.accounts?.map(account => mapAccountResponseToModel(account)),\n  );\n\n  console.log('LOG CONTACT', data, response);\n  console.log('LOG RESPONSE', response);\n  return data;\n}\n\nfunction mapAccountResponseToModel(account: AccountResponse): AccountModel {\n  return new AccountModel(\n    account.bankName,\n    account.accountNumber,\n    account.bankCode,\n    account.accountType,\n    account.externalId,\n    account.bankPostCode,\n  );\n}\n"],
      mappings: ";;;;;AAOAA,OAAA,CAAAC,4BAAA,GAAAA,4BAAA;AAPA,IAAAC,wBAAA,GAAAC,OAAA;AAOA,SAAgBF,4BAA4BA,CAACG,QAA4B;EACvEC,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEF,QAAQ,CAAC;EAClD,OAAOA,QAAQ,CAACG,GAAG,CAAC,UAAAC,IAAI;IAAA,OAAIC,6BAA6B,CAACD,IAAI,CAAC;EAAA,EAAC;AAClE;AAEA,SAASC,6BAA6BA,CAACL,QAA+B;EAAA,IAAAM,kBAAA;EACpE,IAAMC,IAAI,GAAG,IAAIT,wBAAA,CAAAU,kBAAkB,CACjCR,QAAQ,CAACS,EAAE,EACXT,QAAQ,CAACU,IAAI,EACbV,QAAQ,CAACW,KAAK,EACdX,QAAQ,CAACY,QAAQ,EACjBZ,QAAQ,CAACa,YAAY,EACrBb,QAAQ,CAACc,kBAAkB,GAAAR,kBAAA,GAC3BN,QAAQ,CAACe,QAAQ,qBAAjBT,kBAAA,CAAmBH,GAAG,CAAC,UAAAa,OAAO;IAAA,OAAIC,yBAAyB,CAACD,OAAO,CAAC;EAAA,EAAC,CACtE;EAEDf,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEK,IAAI,EAAEP,QAAQ,CAAC;EAC1CC,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEF,QAAQ,CAAC;EACrC,OAAOO,IAAI;AACb;AAEA,SAASU,yBAAyBA,CAACD,OAAwB;EACzD,OAAO,IAAIlB,wBAAA,CAAAoB,YAAY,CACrBF,OAAO,CAACG,QAAQ,EAChBH,OAAO,CAACI,aAAa,EACrBJ,OAAO,CAACK,QAAQ,EAChBL,OAAO,CAACM,WAAW,EACnBN,OAAO,CAACO,UAAU,EAClBP,OAAO,CAACQ,YAAY,CACrB;AACH",
      ignoreList: []
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "ffef8080e3a824e9d051e82677a78eb9fcb06049"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_29vbnljbv1 = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_29vbnljbv1();
cov_29vbnljbv1().s[0]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_29vbnljbv1().s[1]++;
exports.mapMyBillListResponseToModel = mapMyBillListResponseToModel;
var MyBillContactListModel_1 =
/* istanbul ignore next */
(cov_29vbnljbv1().s[2]++, require("../../../domain/entities/my-bill-contact-list/MyBillContactListModel"));
function mapMyBillListResponseToModel(response) {
  /* istanbul ignore next */
  cov_29vbnljbv1().f[0]++;
  cov_29vbnljbv1().s[3]++;
  console.log('-------------LOG RESPONSE', response);
  /* istanbul ignore next */
  cov_29vbnljbv1().s[4]++;
  return response.map(function (bill) {
    /* istanbul ignore next */
    cov_29vbnljbv1().f[1]++;
    cov_29vbnljbv1().s[5]++;
    return mapBillContactResponseToModel(bill);
  });
}
function mapBillContactResponseToModel(response) {
  /* istanbul ignore next */
  cov_29vbnljbv1().f[2]++;
  var _response$accounts;
  var data =
  /* istanbul ignore next */
  (cov_29vbnljbv1().s[6]++, new MyBillContactListModel_1.MyBillContactModel(response.id, response.name, response.alias, response.category, response.activeStatus, response.accessContextScope, (_response$accounts = response.accounts) == null ?
  /* istanbul ignore next */
  (cov_29vbnljbv1().b[0][0]++, void 0) :
  /* istanbul ignore next */
  (cov_29vbnljbv1().b[0][1]++, _response$accounts.map(function (account) {
    /* istanbul ignore next */
    cov_29vbnljbv1().f[3]++;
    cov_29vbnljbv1().s[7]++;
    return mapAccountResponseToModel(account);
  }))));
  /* istanbul ignore next */
  cov_29vbnljbv1().s[8]++;
  console.log('LOG CONTACT', data, response);
  /* istanbul ignore next */
  cov_29vbnljbv1().s[9]++;
  console.log('LOG RESPONSE', response);
  /* istanbul ignore next */
  cov_29vbnljbv1().s[10]++;
  return data;
}
function mapAccountResponseToModel(account) {
  /* istanbul ignore next */
  cov_29vbnljbv1().f[4]++;
  cov_29vbnljbv1().s[11]++;
  return new MyBillContactListModel_1.AccountModel(account.bankName, account.accountNumber, account.bankCode, account.accountType, account.externalId, account.bankPostCode);
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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