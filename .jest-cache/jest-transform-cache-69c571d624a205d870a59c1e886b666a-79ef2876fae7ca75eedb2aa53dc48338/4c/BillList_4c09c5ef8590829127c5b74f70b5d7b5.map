{"version": 3, "names": ["cov_1l35xl7ib9", "actualCoverage", "react_native_1", "s", "require", "react_1", "__importDefault", "react_native_gesture_handler_1", "EmptyBill_tsx_1", "BillListItem_tsx_1", "BillList", "_ref", "f", "isBlocked", "isEditable", "searchText", "bills", "onEdit", "onClick", "onDelete", "onPayment", "normalizedSearch", "trim", "toLowerCase", "content", "undefined", "b", "console", "log", "default", "createElement", "EmptyBillSystemErrorScreen", "filtered", "length", "EmptyScreen", "isRecent", "filter", "bill", "getSearchContent", "includes", "EmptyBillFilteredScreen", "View", "style", "styles", "container", "FlatList", "data", "keyExtractor", "item", "index", "toString", "renderItem", "_ref2", "BillListItem", "key", "getId", "highlight", "exports", "StyleSheet", "create", "flexDirection", "flex", "justifyContent", "width"], "sources": ["/Users/<USER>/projects/react-native/payment-module/src/presentation/payment-home/components/bill-list/BillList.tsx"], "sourcesContent": ["import {StyleSheet, View} from 'react-native';\nimport React from 'react';\nimport {FlatList} from 'react-native-gesture-handler';\n\nimport {EmptyBillFilteredScreen, EmptyBillSystemErrorScreen, EmptyScreen} from './EmptyBill.tsx';\nimport {BillListItem} from './BillListItem.tsx';\nimport {IBillContact} from '../../../../domain/entities/IBillContact.ts';\n\ntype Props = {\n  isBlocked: boolean;\n  isEditable: boolean;\n  searchText: string;\n  bills?: IBillContact[];\n  onClick: (item: IBillContact) => void;\n  onEdit?: (item: IBillContact) => void;\n  onDelete?: (item: IBillContact) => void;\n  onPayment?: (item: IBillContact) => void;\n};\n\nexport const BillList = ({isBlocked, isEditable, searchText, bills, onEdit, onClick, onDelete, onPayment}: Props) => {\n  const normalizedSearch = searchText.trim().toLowerCase();\n  let content: React.JSX.Element;\n  if (bills === undefined) {\n    console.log('BillList: ==>>>>> undefined bills');\n    content = <EmptyBillSystemErrorScreen />;\n  } else {\n    let filtered: IBillContact[] | undefined = [];\n    if (bills.length === 0) {\n      console.log('BillList: ==>>>>> bills.length === 0');\n      content = <EmptyScreen isRecent={!isEditable} />;\n    } else {\n      filtered = bills.filter(bill => {\n        return bill.getSearchContent().includes(normalizedSearch);\n      });\n\n      // console.log('filteredAccountContact', filteredAccountContact);\n\n      if (filtered.length === 0) {\n        console.log('BillList: ==>>>>> empty filtered bills');\n        content = <EmptyBillFilteredScreen />;\n      } else {\n        console.log('BillList: ==>>>>> NGON DATA');\n        content = (\n          <View style={styles.container}>\n            <FlatList\n              data={filtered}\n              keyExtractor={(item, index) => index.toString()}\n              renderItem={({item}) => (\n                <BillListItem\n                  key={item.getId()}\n                  item={item}\n                  isEditable={isEditable}\n                  onEdit={onEdit}\n                  onDelete={onDelete}\n                  onClick={onClick}\n                  onPayment={onPayment}\n                  isBlocked={isBlocked}\n                  highlight={(searchText ?? '').trim()}\n                />\n              )}\n            />\n          </View>\n        );\n      }\n    }\n  }\n  return <View style={styles.container}>{content}</View>;\n};\n\nconst styles = StyleSheet.create({\n  container: {\n    flexDirection: 'column',\n    flex: 1,\n    justifyContent: 'center',\n    width: '100%',\n  },\n});\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAKA;IAAAA,cAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,cAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AALA,IAAAE,cAAA;AAAA;AAAA,CAAAF,cAAA,GAAAG,CAAA,OAAAC,OAAA;AACA,IAAAC,OAAA;AAAA;AAAA,CAAAL,cAAA,GAAAG,CAAA,OAAAG,eAAA,CAAAF,OAAA;AACA,IAAAG,8BAAA;AAAA;AAAA,CAAAP,cAAA,GAAAG,CAAA,OAAAC,OAAA;AAEA,IAAAI,eAAA;AAAA;AAAA,CAAAR,cAAA,GAAAG,CAAA,OAAAC,OAAA;AACA,IAAAK,kBAAA;AAAA;AAAA,CAAAT,cAAA,GAAAG,CAAA,OAAAC,OAAA;AAAA;AAAAJ,cAAA,GAAAG,CAAA;AAcO,IAAMO,QAAQ,GAAG,SAAXA,QAAQA,CAAAC,IAAA,EAA+F;EAAA;EAAAX,cAAA,GAAAY,CAAA;EAAA,IAA1FC,SAAS;IAAA;IAAA,CAAAb,cAAA,GAAAG,CAAA,QAAAQ,IAAA,CAATE,SAAS;IAAEC,UAAU;IAAA;IAAA,CAAAd,cAAA,GAAAG,CAAA,QAAAQ,IAAA,CAAVG,UAAU;IAAEC,UAAU;IAAA;IAAA,CAAAf,cAAA,GAAAG,CAAA,QAAAQ,IAAA,CAAVI,UAAU;IAAEC,KAAK;IAAA;IAAA,CAAAhB,cAAA,GAAAG,CAAA,QAAAQ,IAAA,CAALK,KAAK;IAAEC,MAAM;IAAA;IAAA,CAAAjB,cAAA,GAAAG,CAAA,QAAAQ,IAAA,CAANM,MAAM;IAAEC,OAAO;IAAA;IAAA,CAAAlB,cAAA,GAAAG,CAAA,QAAAQ,IAAA,CAAPO,OAAO;IAAEC,QAAQ;IAAA;IAAA,CAAAnB,cAAA,GAAAG,CAAA,QAAAQ,IAAA,CAARQ,QAAQ;IAAEC,SAAS;IAAA;IAAA,CAAApB,cAAA,GAAAG,CAAA,QAAAQ,IAAA,CAATS,SAAS;EACtG,IAAMC,gBAAgB;EAAA;EAAA,CAAArB,cAAA,GAAAG,CAAA,QAAGY,UAAU,CAACO,IAAI,EAAE,CAACC,WAAW,EAAE;EACxD,IAAIC,OAA0B;EAAA;EAAAxB,cAAA,GAAAG,CAAA;EAC9B,IAAIa,KAAK,KAAKS,SAAS,EAAE;IAAA;IAAAzB,cAAA,GAAA0B,CAAA;IAAA1B,cAAA,GAAAG,CAAA;IACvBwB,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;IAAA;IAAA5B,cAAA,GAAAG,CAAA;IAChDqB,OAAO,GAAGnB,OAAA,CAAAwB,OAAA,CAAAC,aAAA,CAACtB,eAAA,CAAAuB,0BAA0B,OAAG;EAC1C,CAAC,MAAM;IAAA;IAAA/B,cAAA,GAAA0B,CAAA;IACL,IAAIM,QAAQ;IAAA;IAAA,CAAAhC,cAAA,GAAAG,CAAA,QAA+B,EAAE;IAAA;IAAAH,cAAA,GAAAG,CAAA;IAC7C,IAAIa,KAAK,CAACiB,MAAM,KAAK,CAAC,EAAE;MAAA;MAAAjC,cAAA,GAAA0B,CAAA;MAAA1B,cAAA,GAAAG,CAAA;MACtBwB,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC;MAAA;MAAA5B,cAAA,GAAAG,CAAA;MACnDqB,OAAO,GAAGnB,OAAA,CAAAwB,OAAA,CAAAC,aAAA,CAACtB,eAAA,CAAA0B,WAAW;QAACC,QAAQ,EAAE,CAACrB;MAAU,EAAI;IAClD,CAAC,MAAM;MAAA;MAAAd,cAAA,GAAA0B,CAAA;MAAA1B,cAAA,GAAAG,CAAA;MACL6B,QAAQ,GAAGhB,KAAK,CAACoB,MAAM,CAAC,UAAAC,IAAI,EAAG;QAAA;QAAArC,cAAA,GAAAY,CAAA;QAAAZ,cAAA,GAAAG,CAAA;QAC7B,OAAOkC,IAAI,CAACC,gBAAgB,EAAE,CAACC,QAAQ,CAAClB,gBAAgB,CAAC;MAC3D,CAAC,CAAC;MAAA;MAAArB,cAAA,GAAAG,CAAA;MAIF,IAAI6B,QAAQ,CAACC,MAAM,KAAK,CAAC,EAAE;QAAA;QAAAjC,cAAA,GAAA0B,CAAA;QAAA1B,cAAA,GAAAG,CAAA;QACzBwB,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;QAAA;QAAA5B,cAAA,GAAAG,CAAA;QACrDqB,OAAO,GAAGnB,OAAA,CAAAwB,OAAA,CAAAC,aAAA,CAACtB,eAAA,CAAAgC,uBAAuB,OAAG;MACvC,CAAC,MAAM;QAAA;QAAAxC,cAAA,GAAA0B,CAAA;QAAA1B,cAAA,GAAAG,CAAA;QACLwB,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC;QAAA;QAAA5B,cAAA,GAAAG,CAAA;QAC1CqB,OAAO,GACLnB,OAAA,CAAAwB,OAAA,CAAAC,aAAA,CAAC5B,cAAA,CAAAuC,IAAI;UAACC,KAAK,EAAEC,MAAM,CAACC;QAAS,GAC3BvC,OAAA,CAAAwB,OAAA,CAAAC,aAAA,CAACvB,8BAAA,CAAAsC,QAAQ;UACPC,IAAI,EAAEd,QAAQ;UACde,YAAY,EAAE,SAAdA,YAAYA,CAAGC,IAAI,EAAEC,KAAK;YAAA;YAAAjD,cAAA,GAAAY,CAAA;YAAAZ,cAAA,GAAAG,CAAA;YAAA,OAAK8C,KAAK,CAACC,QAAQ,EAAE;UAAA;UAC/CC,UAAU,EAAE,SAAZA,UAAUA,CAAAC,KAAA;YAAA;YAAApD,cAAA,GAAAY,CAAA;YAAA,IAAIoC,IAAI;YAAA;YAAA,CAAAhD,cAAA,GAAAG,CAAA,QAAAiD,KAAA,CAAJJ,IAAI;YAAA;YAAAhD,cAAA,GAAAG,CAAA;YAAA,OAChBE,OAAA,CAAAwB,OAAA,CAAAC,aAAA,CAACrB,kBAAA,CAAA4C,YAAY;cACXC,GAAG,EAAEN,IAAI,CAACO,KAAK,EAAE;cACjBP,IAAI,EAAEA,IAAI;cACVlC,UAAU,EAAEA,UAAU;cACtBG,MAAM,EAAEA,MAAM;cACdE,QAAQ,EAAEA,QAAQ;cAClBD,OAAO,EAAEA,OAAO;cAChBE,SAAS,EAAEA,SAAS;cACpBP,SAAS,EAAEA,SAAS;cACpB2C,SAAS,EAAE,CAACzC,UAAU;cAAA;cAAA,CAAAf,cAAA,GAAA0B,CAAA,UAAVX,UAAU;cAAA;cAAA,CAAAf,cAAA,GAAA0B,CAAA,UAAI,EAAE,GAAEJ,IAAI;YAAE,EACpC;UAAA;QACH,EACD,CAEL;MACH;IACF;EACF;EAAA;EAAAtB,cAAA,GAAAG,CAAA;EACA,OAAOE,OAAA,CAAAwB,OAAA,CAAAC,aAAA,CAAC5B,cAAA,CAAAuC,IAAI;IAACC,KAAK,EAAEC,MAAM,CAACC;EAAS,GAAGpB,OAAO,CAAQ;AACxD,CAAC;AAAA;AAAAxB,cAAA,GAAAG,CAAA;AAhDYsD,OAAA,CAAA/C,QAAQ,GAAAA,QAAA;AAkDrB,IAAMiC,MAAM;AAAA;AAAA,CAAA3C,cAAA,GAAAG,CAAA,QAAGD,cAAA,CAAAwD,UAAU,CAACC,MAAM,CAAC;EAC/Bf,SAAS,EAAE;IACTgB,aAAa,EAAE,QAAQ;IACvBC,IAAI,EAAE,CAAC;IACPC,cAAc,EAAE,QAAQ;IACxBC,KAAK,EAAE;;CAEV,CAAC", "ignoreList": []}