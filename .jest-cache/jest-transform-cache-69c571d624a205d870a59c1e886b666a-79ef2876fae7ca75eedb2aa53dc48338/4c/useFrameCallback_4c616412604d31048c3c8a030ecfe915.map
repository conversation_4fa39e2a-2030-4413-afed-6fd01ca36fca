{"version": 3, "names": ["_interopRequireDefault", "require", "Object", "defineProperty", "exports", "value", "useFrameCallback", "_react", "_FrameCallbackRegistryJS", "frameCallbackRegistry", "FrameCallbackRegistryJS", "callback", "autostart", "arguments", "length", "undefined", "ref", "useRef", "setActive", "isActive", "manageStateFrameCallback", "current", "callbackId", "useEffect", "registerFrameCallback", "memoizedFrameCallback", "unregisterFrameCallback"], "sources": ["../../../src/hook/useFrameCallback.ts"], "sourcesContent": [null], "mappings": "AAAA,YAAY;;AAAA,IAAAA,sBAAA,GAAAC,OAAA;AAAAC,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,KAAA;AAAA;AAAAD,OAAA,CAAAE,gBAAA,GAAAA,gBAAA;AACZ,IAAAC,MAAA,GAAAN,OAAA;AACA,IAAAO,wBAAA,GAAAR,sBAAA,CAAAC,OAAA;AAgBA,IAAMQ,qBAAqB,GAAG,IAAIC,gCAAuB,CAAC,CAAC;AAWpD,SAASJ,gBAAgBA,CAC9BK,QAAwC,EAEzB;EAAA,IADfC,SAAS,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,IAAI;EAEhB,IAAMG,GAAG,GAAG,IAAAC,aAAM,EAAgB;IAChCC,SAAS,EAAG,SAAZA,SAASA,CAAGC,QAAiB,EAAK;MAChCV,qBAAqB,CAACW,wBAAwB,CAC5CJ,GAAG,CAACK,OAAO,CAACC,UAAU,EACtBH,QACF,CAAC;MACDH,GAAG,CAACK,OAAO,CAACF,QAAQ,GAAGA,QAAQ;IACjC,CAAC;IACDA,QAAQ,EAAEP,SAAS;IACnBU,UAAU,EAAE,CAAC;EACf,CAAC,CAAC;EAEF,IAAAC,gBAAS,EAAC,YAAM;IACdP,GAAG,CAACK,OAAO,CAACC,UAAU,GACpBb,qBAAqB,CAACe,qBAAqB,CAACb,QAAQ,CAAC;IACvD,IAAMc,qBAAqB,GAAGT,GAAG,CAACK,OAAO;IACzCL,GAAG,CAACK,OAAO,CAACH,SAAS,CAACF,GAAG,CAACK,OAAO,CAACF,QAAQ,CAAC;IAE3C,OAAO,YAAM;MACXV,qBAAqB,CAACiB,uBAAuB,CAC3CD,qBAAqB,CAACH,UACxB,CAAC;MACDG,qBAAqB,CAACH,UAAU,GAAG,CAAC,CAAC;IACvC,CAAC;EACH,CAAC,EAAE,CAACX,QAAQ,EAAEC,SAAS,CAAC,CAAC;EAEzB,OAAOI,GAAG,CAACK,OAAO;AACpB", "ignoreList": []}