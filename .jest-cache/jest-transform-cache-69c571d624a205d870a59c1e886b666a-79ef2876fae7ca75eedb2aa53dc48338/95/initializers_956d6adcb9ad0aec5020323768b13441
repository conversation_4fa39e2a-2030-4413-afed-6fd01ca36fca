c040307dea02b762e55b503d13bc1e63
'use strict';

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.callGuardDEV = callGuardDEV;
exports.initializeUIRuntime = initializeUIRuntime;
exports.setupCallGuard = setupCallGuard;
exports.setupConsole = setupConsole;
var _slicedToArray2 = _interopRequireDefault(require("@babel/runtime/helpers/slicedToArray"));
var _errors = require("./errors.js");
var _PlatformChecker = require("./PlatformChecker.js");
var _threads = require("./threads.js");
var _mockedRequestAnimationFrame = require("./mockedRequestAnimationFrame.js");
var _index = require("./logger/index.js");
var IS_JEST = (0, _PlatformChecker.isJest)();
var SHOULD_BE_USE_WEB = (0, _PlatformChecker.shouldBeUseWeb)();
var IS_CHROME_DEBUGGER = (0, _PlatformChecker.isChromeDebugger)();
function overrideLogFunctionImplementation() {
  'worklet';

  (0, _index.replaceLoggerImplementation)(function (data) {
    'worklet';

    (0, _threads.runOnJS)(_index.logToLogBoxAndConsole)(data);
  });
}
(0, _index.registerLoggerConfig)(_index.DEFAULT_LOGGER_CONFIG);
overrideLogFunctionImplementation();
if (SHOULD_BE_USE_WEB) {
  global._WORKLET = false;
  global._log = console.log;
  global._getAnimationTimestamp = function () {
    return performance.now();
  };
} else {
  (0, _threads.executeOnUIRuntimeSync)(_errors.registerReanimatedError)();
  (0, _threads.executeOnUIRuntimeSync)(_index.registerLoggerConfig)(_index.DEFAULT_LOGGER_CONFIG);
  (0, _threads.executeOnUIRuntimeSync)(overrideLogFunctionImplementation)();
}
function callGuardDEV(fn) {
  'worklet';

  try {
    for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {
      args[_key - 1] = arguments[_key];
    }
    return fn.apply(void 0, args);
  } catch (e) {
    if (global.__ErrorUtils) {
      global.__ErrorUtils.reportFatalError(e);
    } else {
      throw e;
    }
  }
}
function setupCallGuard() {
  'worklet';

  global.__callGuardDEV = callGuardDEV;
  global.__ErrorUtils = {
    reportFatalError: function reportFatalError(error) {
      (0, _threads.runOnJS)(_errors.reportFatalErrorOnJS)({
        message: error.message,
        stack: error.stack
      });
    }
  };
}
function createMemorySafeCapturableConsole() {
  var consoleCopy = Object.fromEntries(Object.entries(console).map(function (_ref) {
    var _ref2 = (0, _slicedToArray2.default)(_ref, 2),
      methodName = _ref2[0],
      method = _ref2[1];
    var methodWrapper = function methodWrapper() {
      return method.apply(void 0, arguments);
    };
    if (method.name) {
      Object.defineProperty(methodWrapper, 'name', {
        value: method.name,
        writable: false
      });
    }
    return [methodName, methodWrapper];
  }));
  return consoleCopy;
}
var capturableConsole = createMemorySafeCapturableConsole();
function setupConsole() {
  'worklet';

  if (!IS_CHROME_DEBUGGER) {
    global.console = {
      assert: (0, _threads.runOnJS)(capturableConsole.assert),
      debug: (0, _threads.runOnJS)(capturableConsole.debug),
      log: (0, _threads.runOnJS)(capturableConsole.log),
      warn: (0, _threads.runOnJS)(capturableConsole.warn),
      error: (0, _threads.runOnJS)(capturableConsole.error),
      info: (0, _threads.runOnJS)(capturableConsole.info)
    };
  }
}
function setupRequestAnimationFrame() {
  'worklet';
  var nativeRequestAnimationFrame = global.requestAnimationFrame;
  var animationFrameCallbacks = [];
  var flushRequested = false;
  global.__flushAnimationFrame = function (frameTimestamp) {
    var currentCallbacks = animationFrameCallbacks;
    animationFrameCallbacks = [];
    currentCallbacks.forEach(function (f) {
      return f(frameTimestamp);
    });
    (0, _threads.callMicrotasks)();
  };
  global.requestAnimationFrame = function (callback) {
    animationFrameCallbacks.push(callback);
    if (!flushRequested) {
      flushRequested = true;
      nativeRequestAnimationFrame(function (timestamp) {
        flushRequested = false;
        global.__frameTimestamp = timestamp;
        global.__flushAnimationFrame(timestamp);
        global.__frameTimestamp = undefined;
      });
    }
    return -1;
  };
}
function initializeUIRuntime() {
  if (IS_JEST) {
    globalThis.requestAnimationFrame = _mockedRequestAnimationFrame.mockedRequestAnimationFrame;
  }
  (0, _threads.runOnUIImmediately)(function () {
    'worklet';

    setupCallGuard();
    setupConsole();
    if (!SHOULD_BE_USE_WEB) {
      (0, _threads.setupMicrotasks)();
      setupRequestAnimationFrame();
    }
  })();
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJfaW50ZXJvcFJlcXVpcmVEZWZhdWx0IiwicmVxdWlyZSIsIk9iamVjdCIsImRlZmluZVByb3BlcnR5IiwiZXhwb3J0cyIsInZhbHVlIiwiY2FsbEd1YXJkREVWIiwiaW5pdGlhbGl6ZVVJUnVudGltZSIsInNldHVwQ2FsbEd1YXJkIiwic2V0dXBDb25zb2xlIiwiX3NsaWNlZFRvQXJyYXkyIiwiX2Vycm9ycyIsIl9QbGF0Zm9ybUNoZWNrZXIiLCJfdGhyZWFkcyIsIl9tb2NrZWRSZXF1ZXN0QW5pbWF0aW9uRnJhbWUiLCJfaW5kZXgiLCJJU19KRVNUIiwiaXNKZXN0IiwiU0hPVUxEX0JFX1VTRV9XRUIiLCJzaG91bGRCZVVzZVdlYiIsIklTX0NIUk9NRV9ERUJVR0dFUiIsImlzQ2hyb21lRGVidWdnZXIiLCJvdmVycmlkZUxvZ0Z1bmN0aW9uSW1wbGVtZW50YXRpb24iLCJyZXBsYWNlTG9nZ2VySW1wbGVtZW50YXRpb24iLCJkYXRhIiwicnVuT25KUyIsImxvZ1RvTG9nQm94QW5kQ29uc29sZSIsInJlZ2lzdGVyTG9nZ2VyQ29uZmlnIiwiREVGQVVMVF9MT0dHRVJfQ09ORklHIiwiZ2xvYmFsIiwiX1dPUktMRVQiLCJfbG9nIiwiY29uc29sZSIsImxvZyIsIl9nZXRBbmltYXRpb25UaW1lc3RhbXAiLCJwZXJmb3JtYW5jZSIsIm5vdyIsImV4ZWN1dGVPblVJUnVudGltZVN5bmMiLCJyZWdpc3RlclJlYW5pbWF0ZWRFcnJvciIsImZuIiwiX2xlbiIsImFyZ3VtZW50cyIsImxlbmd0aCIsImFyZ3MiLCJBcnJheSIsIl9rZXkiLCJhcHBseSIsImUiLCJfX0Vycm9yVXRpbHMiLCJyZXBvcnRGYXRhbEVycm9yIiwiX19jYWxsR3VhcmRERVYiLCJlcnJvciIsInJlcG9ydEZhdGFsRXJyb3JPbkpTIiwibWVzc2FnZSIsInN0YWNrIiwiY3JlYXRlTWVtb3J5U2FmZUNhcHR1cmFibGVDb25zb2xlIiwiY29uc29sZUNvcHkiLCJmcm9tRW50cmllcyIsImVudHJpZXMiLCJtYXAiLCJfcmVmIiwiX3JlZjIiLCJkZWZhdWx0IiwibWV0aG9kTmFtZSIsIm1ldGhvZCIsIm1ldGhvZFdyYXBwZXIiLCJuYW1lIiwid3JpdGFibGUiLCJjYXB0dXJhYmxlQ29uc29sZSIsImFzc2VydCIsImRlYnVnIiwid2FybiIsImluZm8iLCJzZXR1cFJlcXVlc3RBbmltYXRpb25GcmFtZSIsIm5hdGl2ZVJlcXVlc3RBbmltYXRpb25GcmFtZSIsInJlcXVlc3RBbmltYXRpb25GcmFtZSIsImFuaW1hdGlvbkZyYW1lQ2FsbGJhY2tzIiwiZmx1c2hSZXF1ZXN0ZWQiLCJfX2ZsdXNoQW5pbWF0aW9uRnJhbWUiLCJmcmFtZVRpbWVzdGFtcCIsImN1cnJlbnRDYWxsYmFja3MiLCJmb3JFYWNoIiwiZiIsImNhbGxNaWNyb3Rhc2tzIiwiY2FsbGJhY2siLCJwdXNoIiwidGltZXN0YW1wIiwiX19mcmFtZVRpbWVzdGFtcCIsInVuZGVmaW5lZCIsImdsb2JhbFRoaXMiLCJtb2NrZWRSZXF1ZXN0QW5pbWF0aW9uRnJhbWUiLCJydW5PblVJSW1tZWRpYXRlbHkiLCJzZXR1cE1pY3JvdGFza3MiXSwic291cmNlcyI6WyIuLi8uLi9zcmMvaW5pdGlhbGl6ZXJzLnRzIl0sInNvdXJjZXNDb250ZW50IjpbbnVsbF0sIm1hcHBpbmdzIjoiQUFBQSxZQUFZOztBQUFBLElBQUFBLHNCQUFBLEdBQUFDLE9BQUE7QUFBQUMsTUFBQSxDQUFBQyxjQUFBLENBQUFDLE9BQUE7RUFBQUMsS0FBQTtBQUFBO0FBQUFELE9BQUEsQ0FBQUUsWUFBQSxHQUFBQSxZQUFBO0FBQUFGLE9BQUEsQ0FBQUcsbUJBQUEsR0FBQUEsbUJBQUE7QUFBQUgsT0FBQSxDQUFBSSxjQUFBLEdBQUFBLGNBQUE7QUFBQUosT0FBQSxDQUFBSyxZQUFBLEdBQUFBLFlBQUE7QUFBQSxJQUFBQyxlQUFBLEdBQUFWLHNCQUFBLENBQUFDLE9BQUE7QUFDWixJQUFBVSxPQUFBLEdBQUFWLE9BQUE7QUFDQSxJQUFBVyxnQkFBQSxHQUFBWCxPQUFBO0FBQ0EsSUFBQVksUUFBQSxHQUFBWixPQUFBO0FBT0EsSUFBQWEsNEJBQUEsR0FBQWIsT0FBQTtBQUNBLElBQUFjLE1BQUEsR0FBQWQsT0FBQTtBQU9BLElBQU1lLE9BQU8sR0FBRyxJQUFBQyx1QkFBTSxFQUFDLENBQUM7QUFDeEIsSUFBTUMsaUJBQWlCLEdBQUcsSUFBQUMsK0JBQWMsRUFBQyxDQUFDO0FBQzFDLElBQU1DLGtCQUFrQixHQUFHLElBQUFDLGlDQUFnQixFQUFDLENBQUM7QUFLN0MsU0FBU0MsaUNBQWlDQSxDQUFBLEVBQUc7RUFDM0MsU0FBUzs7RUFDVCxJQUFBQyxrQ0FBMkIsRUFBRSxVQUFBQyxJQUFJLEVBQUs7SUFDcEMsU0FBUzs7SUFDVCxJQUFBQyxnQkFBTyxFQUFDQyw0QkFBcUIsQ0FBQyxDQUFDRixJQUFJLENBQUM7RUFDdEMsQ0FBQyxDQUFDO0FBQ0o7QUFJQSxJQUFBRywyQkFBb0IsRUFBQ0MsNEJBQXFCLENBQUM7QUFDM0NOLGlDQUFpQyxDQUFDLENBQUM7QUFHbkMsSUFBSUosaUJBQWlCLEVBQUU7RUFDckJXLE1BQU0sQ0FBQ0MsUUFBUSxHQUFHLEtBQUs7RUFDdkJELE1BQU0sQ0FBQ0UsSUFBSSxHQUFHQyxPQUFPLENBQUNDLEdBQUc7RUFDekJKLE1BQU0sQ0FBQ0ssc0JBQXNCLEdBQUc7SUFBQSxPQUFNQyxXQUFXLENBQUNDLEdBQUcsQ0FBQyxDQUFDO0VBQUE7QUFDekQsQ0FBQyxNQUFNO0VBSUwsSUFBQUMsK0JBQXNCLEVBQUNDLCtCQUF1QixDQUFDLENBQUMsQ0FBQztFQUNqRCxJQUFBRCwrQkFBc0IsRUFBQ1YsMkJBQW9CLENBQUMsQ0FBQ0MsNEJBQXFCLENBQUM7RUFDbkUsSUFBQVMsK0JBQXNCLEVBQUNmLGlDQUFpQyxDQUFDLENBQUMsQ0FBQztBQUM3RDtBQUdPLFNBQVNoQixZQUFZQSxDQUMxQmlDLEVBQWtDLEVBRWQ7RUFDcEIsU0FBUzs7RUFDVCxJQUFJO0lBQUEsU0FBQUMsSUFBQSxHQUFBQyxTQUFBLENBQUFDLE1BQUEsRUFIREMsSUFBVSxPQUFBQyxLQUFBLENBQUFKLElBQUEsT0FBQUEsSUFBQSxXQUFBSyxJQUFBLE1BQUFBLElBQUEsR0FBQUwsSUFBQSxFQUFBSyxJQUFBO01BQVZGLElBQVUsQ0FBQUUsSUFBQSxRQUFBSixTQUFBLENBQUFJLElBQUE7SUFBQTtJQUlYLE9BQU9OLEVBQUUsQ0FBQU8sS0FBQSxTQUFJSCxJQUFJLENBQUM7RUFDcEIsQ0FBQyxDQUFDLE9BQU9JLENBQUMsRUFBRTtJQUNWLElBQUlsQixNQUFNLENBQUNtQixZQUFZLEVBQUU7TUFDdkJuQixNQUFNLENBQUNtQixZQUFZLENBQUNDLGdCQUFnQixDQUFDRixDQUFVLENBQUM7SUFDbEQsQ0FBQyxNQUFNO01BQ0wsTUFBTUEsQ0FBQztJQUNUO0VBQ0Y7QUFDRjtBQUVPLFNBQVN2QyxjQUFjQSxDQUFBLEVBQUc7RUFDL0IsU0FBUzs7RUFDVHFCLE1BQU0sQ0FBQ3FCLGNBQWMsR0FBRzVDLFlBQVk7RUFDcEN1QixNQUFNLENBQUNtQixZQUFZLEdBQUc7SUFDcEJDLGdCQUFnQixFQUFHLFNBQW5CQSxnQkFBZ0JBLENBQUdFLEtBQVksRUFBSztNQUNsQyxJQUFBMUIsZ0JBQU8sRUFBQzJCLDRCQUFvQixDQUFDLENBQUM7UUFDNUJDLE9BQU8sRUFBRUYsS0FBSyxDQUFDRSxPQUFPO1FBQ3RCQyxLQUFLLEVBQUVILEtBQUssQ0FBQ0c7TUFDZixDQUFDLENBQUM7SUFDSjtFQUNGLENBQUM7QUFDSDtBQWVBLFNBQVNDLGlDQUFpQ0EsQ0FBQSxFQUFtQjtFQUMzRCxJQUFNQyxXQUFXLEdBQUd0RCxNQUFNLENBQUN1RCxXQUFXLENBQ3BDdkQsTUFBTSxDQUFDd0QsT0FBTyxDQUFDMUIsT0FBTyxDQUFDLENBQUMyQixHQUFHLENBQUMsVUFBQUMsSUFBQSxFQUEwQjtJQUFBLElBQUFDLEtBQUEsT0FBQW5ELGVBQUEsQ0FBQW9ELE9BQUEsRUFBQUYsSUFBQTtNQUF4QkcsVUFBVSxHQUFBRixLQUFBO01BQUVHLE1BQU0sR0FBQUgsS0FBQTtJQUM5QyxJQUFNSSxhQUFhLEdBQUcsU0FBU0EsYUFBYUEsQ0FBQSxFQUFxQjtNQUMvRCxPQUFPRCxNQUFNLENBQUFsQixLQUFBLFNBQUFMLFNBQVEsQ0FBQztJQUN4QixDQUFDO0lBQ0QsSUFBSXVCLE1BQU0sQ0FBQ0UsSUFBSSxFQUFFO01BUWZoRSxNQUFNLENBQUNDLGNBQWMsQ0FBQzhELGFBQWEsRUFBRSxNQUFNLEVBQUU7UUFDM0M1RCxLQUFLLEVBQUUyRCxNQUFNLENBQUNFLElBQUk7UUFDbEJDLFFBQVEsRUFBRTtNQUNaLENBQUMsQ0FBQztJQUNKO0lBQ0EsT0FBTyxDQUFDSixVQUFVLEVBQUVFLGFBQWEsQ0FBQztFQUNwQyxDQUFDLENBQ0gsQ0FBQztFQUVELE9BQU9ULFdBQVc7QUFDcEI7QUFJQSxJQUFNWSxpQkFBaUIsR0FBR2IsaUNBQWlDLENBQUMsQ0FBQztBQUV0RCxTQUFTOUMsWUFBWUEsQ0FBQSxFQUFHO0VBQzdCLFNBQVM7O0VBQ1QsSUFBSSxDQUFDVyxrQkFBa0IsRUFBRTtJQUV2QlMsTUFBTSxDQUFDRyxPQUFPLEdBQUc7TUFFZnFDLE1BQU0sRUFBRSxJQUFBNUMsZ0JBQU8sRUFBQzJDLGlCQUFpQixDQUFDQyxNQUFNLENBQUM7TUFDekNDLEtBQUssRUFBRSxJQUFBN0MsZ0JBQU8sRUFBQzJDLGlCQUFpQixDQUFDRSxLQUFLLENBQUM7TUFDdkNyQyxHQUFHLEVBQUUsSUFBQVIsZ0JBQU8sRUFBQzJDLGlCQUFpQixDQUFDbkMsR0FBRyxDQUFDO01BQ25Dc0MsSUFBSSxFQUFFLElBQUE5QyxnQkFBTyxFQUFDMkMsaUJBQWlCLENBQUNHLElBQUksQ0FBQztNQUNyQ3BCLEtBQUssRUFBRSxJQUFBMUIsZ0JBQU8sRUFBQzJDLGlCQUFpQixDQUFDakIsS0FBSyxDQUFDO01BQ3ZDcUIsSUFBSSxFQUFFLElBQUEvQyxnQkFBTyxFQUFDMkMsaUJBQWlCLENBQUNJLElBQUk7SUFFdEMsQ0FBQztFQUNIO0FBQ0Y7QUFFQSxTQUFTQywwQkFBMEJBLENBQUEsRUFBRztFQUNwQyxTQUFTO0VBSVQsSUFBTUMsMkJBQTJCLEdBQUc3QyxNQUFNLENBQUM4QyxxQkFBcUI7RUFFaEUsSUFBSUMsdUJBQTJELEdBQUcsRUFBRTtFQUNwRSxJQUFJQyxjQUFjLEdBQUcsS0FBSztFQUUxQmhELE1BQU0sQ0FBQ2lELHFCQUFxQixHQUFJLFVBQUFDLGNBQXNCLEVBQUs7SUFDekQsSUFBTUMsZ0JBQWdCLEdBQUdKLHVCQUF1QjtJQUNoREEsdUJBQXVCLEdBQUcsRUFBRTtJQUM1QkksZ0JBQWdCLENBQUNDLE9BQU8sQ0FBRSxVQUFBQyxDQUFDO01BQUEsT0FBS0EsQ0FBQyxDQUFDSCxjQUFjLENBQUM7SUFBQSxFQUFDO0lBQ2xELElBQUFJLHVCQUFjLEVBQUMsQ0FBQztFQUNsQixDQUFDO0VBRUR0RCxNQUFNLENBQUM4QyxxQkFBcUIsR0FDMUIsVUFBQVMsUUFBcUMsRUFDMUI7SUFDWFIsdUJBQXVCLENBQUNTLElBQUksQ0FBQ0QsUUFBUSxDQUFDO0lBQ3RDLElBQUksQ0FBQ1AsY0FBYyxFQUFFO01BQ25CQSxjQUFjLEdBQUcsSUFBSTtNQUNyQkgsMkJBQTJCLENBQUUsVUFBQVksU0FBUyxFQUFLO1FBQ3pDVCxjQUFjLEdBQUcsS0FBSztRQUN0QmhELE1BQU0sQ0FBQzBELGdCQUFnQixHQUFHRCxTQUFTO1FBQ25DekQsTUFBTSxDQUFDaUQscUJBQXFCLENBQUNRLFNBQVMsQ0FBQztRQUN2Q3pELE1BQU0sQ0FBQzBELGdCQUFnQixHQUFHQyxTQUFTO01BQ3JDLENBQUMsQ0FBQztJQUNKO0lBS0EsT0FBTyxDQUFDLENBQUM7RUFDWCxDQUFDO0FBQ0g7QUFFTyxTQUFTakYsbUJBQW1CQSxDQUFBLEVBQUc7RUFDcEMsSUFBSVMsT0FBTyxFQUFFO0lBT1h5RSxVQUFVLENBQUNkLHFCQUFxQixHQUFHZSx3REFBMkI7RUFDaEU7RUFFQSxJQUFBQywyQkFBa0IsRUFBQyxZQUFNO0lBQ3ZCLFNBQVM7O0lBQ1RuRixjQUFjLENBQUMsQ0FBQztJQUNoQkMsWUFBWSxDQUFDLENBQUM7SUFDZCxJQUFJLENBQUNTLGlCQUFpQixFQUFFO01BQ3RCLElBQUEwRSx3QkFBZSxFQUFDLENBQUM7TUFDakJuQiwwQkFBMEIsQ0FBQyxDQUFDO0lBQzlCO0VBQ0YsQ0FBQyxDQUFDLENBQUMsQ0FBQztBQUNOIiwiaWdub3JlTGlzdCI6W119