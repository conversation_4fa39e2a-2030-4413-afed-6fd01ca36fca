{"version": 3, "names": ["PlatformColor", "exports", "_len", "arguments", "length", "names", "Array", "_key", "semantic", "DynamicColorIOSPrivate", "tuple", "dynamic", "light", "dark", "highContrastLight", "highContrastDark", "_normalizeColorObject", "color", "undefined", "normalizeColor", "require", "dynamicColor", "normalizeColorObject", "_processColorObject", "processColor", "default", "processColorObject"], "sources": ["PlatformColorValueTypes.ios.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @format\n * @flow strict-local\n */\n\nimport type {ProcessedColorValue} from './processColor';\nimport type {ColorValue, NativeColorValue} from './StyleSheet';\n\n/** The actual type of the opaque NativeColorValue on iOS platform */\ntype LocalNativeColorValue = {\n  semantic?: Array<string>,\n  dynamic?: {\n    light: ?(ColorValue | ProcessedColorValue),\n    dark: ?(ColorValue | ProcessedColorValue),\n    highContrastLight?: ?(ColorValue | ProcessedColorValue),\n    highContrastDark?: ?(ColorValue | ProcessedColorValue),\n  },\n};\n\nexport const PlatformColor = (...names: Array<string>): ColorValue => {\n  // $FlowExpectedError[incompatible-return] LocalNativeColorValue is the iOS LocalNativeColorValue type\n  return ({semantic: names}: LocalNativeColorValue);\n};\n\nexport type DynamicColorIOSTuplePrivate = {\n  light: ColorValue,\n  dark: ColorValue,\n  highContrastLight?: ColorValue,\n  highContrastDark?: ColorValue,\n};\n\nexport const DynamicColorIOSPrivate = (\n  tuple: DynamicColorIOSTuplePrivate,\n): ColorValue => {\n  return ({\n    dynamic: {\n      light: tuple.light,\n      dark: tuple.dark,\n      highContrastLight: tuple.highContrastLight,\n      highContrastDark: tuple.highContrastDark,\n    },\n    /* $FlowExpectedError[incompatible-return]\n     * LocalNativeColorValue is the actual type of the opaque NativeColorValue on iOS platform */\n  }: LocalNativeColorValue);\n};\n\nconst _normalizeColorObject = (\n  color: LocalNativeColorValue,\n): ?LocalNativeColorValue => {\n  if ('semantic' in color) {\n    // an ios semantic color\n    return color;\n  } else if ('dynamic' in color && color.dynamic !== undefined) {\n    const normalizeColor = require('./normalizeColor');\n\n    // a dynamic, appearance aware color\n    const dynamic = color.dynamic;\n    const dynamicColor: LocalNativeColorValue = {\n      dynamic: {\n        // $FlowFixMe[incompatible-use]\n        light: normalizeColor(dynamic.light),\n        // $FlowFixMe[incompatible-use]\n        dark: normalizeColor(dynamic.dark),\n        // $FlowFixMe[incompatible-use]\n        highContrastLight: normalizeColor(dynamic.highContrastLight),\n        // $FlowFixMe[incompatible-use]\n        highContrastDark: normalizeColor(dynamic.highContrastDark),\n      },\n    };\n    return dynamicColor;\n  }\n  return null;\n};\n\nexport const normalizeColorObject: (\n  color: NativeColorValue,\n  /* $FlowExpectedError[incompatible-type]\n   * LocalNativeColorValue is the actual type of the opaque NativeColorValue on iOS platform */\n) => ?ProcessedColorValue = _normalizeColorObject;\n\nconst _processColorObject = (\n  color: LocalNativeColorValue,\n): ?LocalNativeColorValue => {\n  if ('dynamic' in color && color.dynamic != null) {\n    const processColor = require('./processColor').default;\n    const dynamic = color.dynamic;\n    const dynamicColor: LocalNativeColorValue = {\n      dynamic: {\n        // $FlowFixMe[incompatible-use]\n        light: processColor(dynamic.light),\n        // $FlowFixMe[incompatible-use]\n        dark: processColor(dynamic.dark),\n        // $FlowFixMe[incompatible-use]\n        highContrastLight: processColor(dynamic.highContrastLight),\n        // $FlowFixMe[incompatible-use]\n        highContrastDark: processColor(dynamic.highContrastDark),\n      },\n    };\n    return dynamicColor;\n  }\n  return color;\n};\n\nexport const processColorObject: (\n  color: NativeColorValue,\n  /* $FlowExpectedError[incompatible-type]\n   * LocalNativeColorValue is the actual type of the opaque NativeColorValue on iOS platform */\n) => ?NativeColorValue = _processColorObject;\n"], "mappings": ";;;;AAwBO,IAAMA,aAAa,GAAAC,OAAA,CAAAD,aAAA,GAAG,SAAhBA,aAAaA,CAAA,EAA4C;EAAA,SAAAE,IAAA,GAAAC,SAAA,CAAAC,MAAA,EAArCC,KAAK,OAAAC,KAAA,CAAAJ,IAAA,GAAAK,IAAA,MAAAA,IAAA,GAAAL,IAAA,EAAAK,IAAA;IAALF,KAAK,CAAAE,IAAA,IAAAJ,SAAA,CAAAI,IAAA;EAAA;EAEpC,OAAQ;IAACC,QAAQ,EAAEH;EAAK,CAAC;AAC3B,CAAC;AASM,IAAMI,sBAAsB,GAAAR,OAAA,CAAAQ,sBAAA,GAAG,SAAzBA,sBAAsBA,CACjCC,KAAkC,EACnB;EACf,OAAQ;IACNC,OAAO,EAAE;MACPC,KAAK,EAAEF,KAAK,CAACE,KAAK;MAClBC,IAAI,EAAEH,KAAK,CAACG,IAAI;MAChBC,iBAAiB,EAAEJ,KAAK,CAACI,iBAAiB;MAC1CC,gBAAgB,EAAEL,KAAK,CAACK;IAC1B;EAGF,CAAC;AACH,CAAC;AAED,IAAMC,qBAAqB,GAAG,SAAxBA,qBAAqBA,CACzBC,KAA4B,EACD;EAC3B,IAAI,UAAU,IAAIA,KAAK,EAAE;IAEvB,OAAOA,KAAK;EACd,CAAC,MAAM,IAAI,SAAS,IAAIA,KAAK,IAAIA,KAAK,CAACN,OAAO,KAAKO,SAAS,EAAE;IAC5D,IAAMC,cAAc,GAAGC,OAAO,CAAC,kBAAkB,CAAC;IAGlD,IAAMT,OAAO,GAAGM,KAAK,CAACN,OAAO;IAC7B,IAAMU,YAAmC,GAAG;MAC1CV,OAAO,EAAE;QAEPC,KAAK,EAAEO,cAAc,CAACR,OAAO,CAACC,KAAK,CAAC;QAEpCC,IAAI,EAAEM,cAAc,CAACR,OAAO,CAACE,IAAI,CAAC;QAElCC,iBAAiB,EAAEK,cAAc,CAACR,OAAO,CAACG,iBAAiB,CAAC;QAE5DC,gBAAgB,EAAEI,cAAc,CAACR,OAAO,CAACI,gBAAgB;MAC3D;IACF,CAAC;IACD,OAAOM,YAAY;EACrB;EACA,OAAO,IAAI;AACb,CAAC;AAEM,IAAMC,oBAIY,GAAArB,OAAA,CAAAqB,oBAAA,GAAGN,qBAAqB;AAEjD,IAAMO,mBAAmB,GAAG,SAAtBA,mBAAmBA,CACvBN,KAA4B,EACD;EAC3B,IAAI,SAAS,IAAIA,KAAK,IAAIA,KAAK,CAACN,OAAO,IAAI,IAAI,EAAE;IAC/C,IAAMa,YAAY,GAAGJ,OAAO,CAAC,gBAAgB,CAAC,CAACK,OAAO;IACtD,IAAMd,OAAO,GAAGM,KAAK,CAACN,OAAO;IAC7B,IAAMU,YAAmC,GAAG;MAC1CV,OAAO,EAAE;QAEPC,KAAK,EAAEY,YAAY,CAACb,OAAO,CAACC,KAAK,CAAC;QAElCC,IAAI,EAAEW,YAAY,CAACb,OAAO,CAACE,IAAI,CAAC;QAEhCC,iBAAiB,EAAEU,YAAY,CAACb,OAAO,CAACG,iBAAiB,CAAC;QAE1DC,gBAAgB,EAAES,YAAY,CAACb,OAAO,CAACI,gBAAgB;MACzD;IACF,CAAC;IACD,OAAOM,YAAY;EACrB;EACA,OAAOJ,KAAK;AACd,CAAC;AAEM,IAAMS,kBAIS,GAAAzB,OAAA,CAAAyB,kBAAA,GAAGH,mBAAmB", "ignoreList": []}