6ff13f62d6fa09a6dcf4e886c603040f
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.processColorObject = exports.normalizeColorObject = exports.PlatformColor = exports.DynamicColorIOSPrivate = void 0;
var PlatformColor = exports.PlatformColor = function PlatformColor() {
  for (var _len = arguments.length, names = new Array(_len), _key = 0; _key < _len; _key++) {
    names[_key] = arguments[_key];
  }
  return {
    semantic: names
  };
};
var DynamicColorIOSPrivate = exports.DynamicColorIOSPrivate = function DynamicColorIOSPrivate(tuple) {
  return {
    dynamic: {
      light: tuple.light,
      dark: tuple.dark,
      highContrastLight: tuple.highContrastLight,
      highContrastDark: tuple.highContrastDark
    }
  };
};
var _normalizeColorObject = function _normalizeColorObject(color) {
  if ('semantic' in color) {
    return color;
  } else if ('dynamic' in color && color.dynamic !== undefined) {
    var normalizeColor = require('./normalizeColor');
    var dynamic = color.dynamic;
    var dynamicColor = {
      dynamic: {
        light: normalizeColor(dynamic.light),
        dark: normalizeColor(dynamic.dark),
        highContrastLight: normalizeColor(dynamic.highContrastLight),
        highContrastDark: normalizeColor(dynamic.highContrastDark)
      }
    };
    return dynamicColor;
  }
  return null;
};
var normalizeColorObject = exports.normalizeColorObject = _normalizeColorObject;
var _processColorObject = function _processColorObject(color) {
  if ('dynamic' in color && color.dynamic != null) {
    var processColor = require('./processColor').default;
    var dynamic = color.dynamic;
    var dynamicColor = {
      dynamic: {
        light: processColor(dynamic.light),
        dark: processColor(dynamic.dark),
        highContrastLight: processColor(dynamic.highContrastLight),
        highContrastDark: processColor(dynamic.highContrastDark)
      }
    };
    return dynamicColor;
  }
  return color;
};
var processColorObject = exports.processColorObject = _processColorObject;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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