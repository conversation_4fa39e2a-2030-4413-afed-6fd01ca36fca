{"version": 3, "names": ["_interopRequireDefault", "require", "Object", "defineProperty", "exports", "value", "callGuardDEV", "initializeUIRuntime", "setupCallGuard", "setupConsole", "_slicedToArray2", "_errors", "_PlatformChecker", "_threads", "_mockedRequestAnimationFrame", "_index", "IS_JEST", "isJest", "SHOULD_BE_USE_WEB", "shouldBeUseWeb", "IS_CHROME_DEBUGGER", "isChromeDebugger", "overrideLogFunctionImplementation", "replaceLoggerImplementation", "data", "runOnJS", "logToLogBoxAndConsole", "registerLoggerConfig", "DEFAULT_LOGGER_CONFIG", "global", "_WORKLET", "_log", "console", "log", "_getAnimationTimestamp", "performance", "now", "executeOnUIRuntimeSync", "registerReanimatedError", "fn", "_len", "arguments", "length", "args", "Array", "_key", "apply", "e", "__E<PERSON><PERSON><PERSON><PERSON>s", "reportFatalError", "__callGuardDEV", "error", "reportFatalErrorOnJS", "message", "stack", "createMemorySafeCapturableConsole", "consoleCopy", "fromEntries", "entries", "map", "_ref", "_ref2", "default", "methodName", "method", "methodWrapper", "name", "writable", "capturableConsole", "assert", "debug", "warn", "info", "setupRequestAnimationFrame", "nativeRequestAnimationFrame", "requestAnimationFrame", "animationFrameCallbacks", "flushRequested", "__flushAnimationFrame", "frameTimestamp", "currentCallbacks", "for<PERSON>ach", "f", "callMicrotasks", "callback", "push", "timestamp", "__frameTimestamp", "undefined", "globalThis", "mockedRequestAnimationFrame", "runOnUIImmediately", "setupMicrotasks"], "sources": ["../../src/initializers.ts"], "sourcesContent": [null], "mappings": "AAAA,YAAY;;AAAA,IAAAA,sBAAA,GAAAC,OAAA;AAAAC,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,KAAA;AAAA;AAAAD,OAAA,CAAAE,YAAA,GAAAA,YAAA;AAAAF,OAAA,CAAAG,mBAAA,GAAAA,mBAAA;AAAAH,OAAA,CAAAI,cAAA,GAAAA,cAAA;AAAAJ,OAAA,CAAAK,YAAA,GAAAA,YAAA;AAAA,IAAAC,eAAA,GAAAV,sBAAA,CAAAC,OAAA;AACZ,IAAAU,OAAA,GAAAV,OAAA;AACA,IAAAW,gBAAA,GAAAX,OAAA;AACA,IAAAY,QAAA,GAAAZ,OAAA;AAOA,IAAAa,4BAAA,GAAAb,OAAA;AACA,IAAAc,MAAA,GAAAd,OAAA;AAOA,IAAMe,OAAO,GAAG,IAAAC,uBAAM,EAAC,CAAC;AACxB,IAAMC,iBAAiB,GAAG,IAAAC,+BAAc,EAAC,CAAC;AAC1C,IAAMC,kBAAkB,GAAG,IAAAC,iCAAgB,EAAC,CAAC;AAK7C,SAASC,iCAAiCA,CAAA,EAAG;EAC3C,SAAS;;EACT,IAAAC,kCAA2B,EAAE,UAAAC,IAAI,EAAK;IACpC,SAAS;;IACT,IAAAC,gBAAO,EAACC,4BAAqB,CAAC,CAACF,IAAI,CAAC;EACtC,CAAC,CAAC;AACJ;AAIA,IAAAG,2BAAoB,EAACC,4BAAqB,CAAC;AAC3CN,iCAAiC,CAAC,CAAC;AAGnC,IAAIJ,iBAAiB,EAAE;EACrBW,MAAM,CAACC,QAAQ,GAAG,KAAK;EACvBD,MAAM,CAACE,IAAI,GAAGC,OAAO,CAACC,GAAG;EACzBJ,MAAM,CAACK,sBAAsB,GAAG;IAAA,OAAMC,WAAW,CAACC,GAAG,CAAC,CAAC;EAAA;AACzD,CAAC,MAAM;EAIL,IAAAC,+BAAsB,EAACC,+BAAuB,CAAC,CAAC,CAAC;EACjD,IAAAD,+BAAsB,EAACV,2BAAoB,CAAC,CAACC,4BAAqB,CAAC;EACnE,IAAAS,+BAAsB,EAACf,iCAAiC,CAAC,CAAC,CAAC;AAC7D;AAGO,SAAShB,YAAYA,CAC1BiC,EAAkC,EAEd;EACpB,SAAS;;EACT,IAAI;IAAA,SAAAC,IAAA,GAAAC,SAAA,CAAAC,MAAA,EAHDC,IAAU,OAAAC,KAAA,CAAAJ,IAAA,OAAAA,IAAA,WAAAK,IAAA,MAAAA,IAAA,GAAAL,IAAA,EAAAK,IAAA;MAAVF,IAAU,CAAAE,IAAA,QAAAJ,SAAA,CAAAI,IAAA;IAAA;IAIX,OAAON,EAAE,CAAAO,KAAA,SAAIH,IAAI,CAAC;EACpB,CAAC,CAAC,OAAOI,CAAC,EAAE;IACV,IAAIlB,MAAM,CAACmB,YAAY,EAAE;MACvBnB,MAAM,CAACmB,YAAY,CAACC,gBAAgB,CAACF,CAAU,CAAC;IAClD,CAAC,MAAM;MACL,MAAMA,CAAC;IACT;EACF;AACF;AAEO,SAASvC,cAAcA,CAAA,EAAG;EAC/B,SAAS;;EACTqB,MAAM,CAACqB,cAAc,GAAG5C,YAAY;EACpCuB,MAAM,CAACmB,YAAY,GAAG;IACpBC,gBAAgB,EAAG,SAAnBA,gBAAgBA,CAAGE,KAAY,EAAK;MAClC,IAAA1B,gBAAO,EAAC2B,4BAAoB,CAAC,CAAC;QAC5BC,OAAO,EAAEF,KAAK,CAACE,OAAO;QACtBC,KAAK,EAAEH,KAAK,CAACG;MACf,CAAC,CAAC;IACJ;EACF,CAAC;AACH;AAeA,SAASC,iCAAiCA,CAAA,EAAmB;EAC3D,IAAMC,WAAW,GAAGtD,MAAM,CAACuD,WAAW,CACpCvD,MAAM,CAACwD,OAAO,CAAC1B,OAAO,CAAC,CAAC2B,GAAG,CAAC,UAAAC,IAAA,EAA0B;IAAA,IAAAC,KAAA,OAAAnD,eAAA,CAAAoD,OAAA,EAAAF,IAAA;MAAxBG,UAAU,GAAAF,KAAA;MAAEG,MAAM,GAAAH,KAAA;IAC9C,IAAMI,aAAa,GAAG,SAASA,aAAaA,CAAA,EAAqB;MAC/D,OAAOD,MAAM,CAAAlB,KAAA,SAAAL,SAAQ,CAAC;IACxB,CAAC;IACD,IAAIuB,MAAM,CAACE,IAAI,EAAE;MAQfhE,MAAM,CAACC,cAAc,CAAC8D,aAAa,EAAE,MAAM,EAAE;QAC3C5D,KAAK,EAAE2D,MAAM,CAACE,IAAI;QAClBC,QAAQ,EAAE;MACZ,CAAC,CAAC;IACJ;IACA,OAAO,CAACJ,UAAU,EAAEE,aAAa,CAAC;EACpC,CAAC,CACH,CAAC;EAED,OAAOT,WAAW;AACpB;AAIA,IAAMY,iBAAiB,GAAGb,iCAAiC,CAAC,CAAC;AAEtD,SAAS9C,YAAYA,CAAA,EAAG;EAC7B,SAAS;;EACT,IAAI,CAACW,kBAAkB,EAAE;IAEvBS,MAAM,CAACG,OAAO,GAAG;MAEfqC,MAAM,EAAE,IAAA5C,gBAAO,EAAC2C,iBAAiB,CAACC,MAAM,CAAC;MACzCC,KAAK,EAAE,IAAA7C,gBAAO,EAAC2C,iBAAiB,CAACE,KAAK,CAAC;MACvCrC,GAAG,EAAE,IAAAR,gBAAO,EAAC2C,iBAAiB,CAACnC,GAAG,CAAC;MACnCsC,IAAI,EAAE,IAAA9C,gBAAO,EAAC2C,iBAAiB,CAACG,IAAI,CAAC;MACrCpB,KAAK,EAAE,IAAA1B,gBAAO,EAAC2C,iBAAiB,CAACjB,KAAK,CAAC;MACvCqB,IAAI,EAAE,IAAA/C,gBAAO,EAAC2C,iBAAiB,CAACI,IAAI;IAEtC,CAAC;EACH;AACF;AAEA,SAASC,0BAA0BA,CAAA,EAAG;EACpC,SAAS;EAIT,IAAMC,2BAA2B,GAAG7C,MAAM,CAAC8C,qBAAqB;EAEhE,IAAIC,uBAA2D,GAAG,EAAE;EACpE,IAAIC,cAAc,GAAG,KAAK;EAE1BhD,MAAM,CAACiD,qBAAqB,GAAI,UAAAC,cAAsB,EAAK;IACzD,IAAMC,gBAAgB,GAAGJ,uBAAuB;IAChDA,uBAAuB,GAAG,EAAE;IAC5BI,gBAAgB,CAACC,OAAO,CAAE,UAAAC,CAAC;MAAA,OAAKA,CAAC,CAACH,cAAc,CAAC;IAAA,EAAC;IAClD,IAAAI,uBAAc,EAAC,CAAC;EAClB,CAAC;EAEDtD,MAAM,CAAC8C,qBAAqB,GAC1B,UAAAS,QAAqC,EAC1B;IACXR,uBAAuB,CAACS,IAAI,CAACD,QAAQ,CAAC;IACtC,IAAI,CAACP,cAAc,EAAE;MACnBA,cAAc,GAAG,IAAI;MACrBH,2BAA2B,CAAE,UAAAY,SAAS,EAAK;QACzCT,cAAc,GAAG,KAAK;QACtBhD,MAAM,CAAC0D,gBAAgB,GAAGD,SAAS;QACnCzD,MAAM,CAACiD,qBAAqB,CAACQ,SAAS,CAAC;QACvCzD,MAAM,CAAC0D,gBAAgB,GAAGC,SAAS;MACrC,CAAC,CAAC;IACJ;IAKA,OAAO,CAAC,CAAC;EACX,CAAC;AACH;AAEO,SAASjF,mBAAmBA,CAAA,EAAG;EACpC,IAAIS,OAAO,EAAE;IAOXyE,UAAU,CAACd,qBAAqB,GAAGe,wDAA2B;EAChE;EAEA,IAAAC,2BAAkB,EAAC,YAAM;IACvB,SAAS;;IACTnF,cAAc,CAAC,CAAC;IAChBC,YAAY,CAAC,CAAC;IACd,IAAI,CAACS,iBAAiB,EAAE;MACtB,IAAA0E,wBAAe,EAAC,CAAC;MACjBnB,0BAA0B,CAAC,CAAC;IAC9B;EACF,CAAC,CAAC,CAAC,CAAC;AACN", "ignoreList": []}