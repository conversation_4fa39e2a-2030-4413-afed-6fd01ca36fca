57987b362c3638488fbcf6f7c96cb51c
'use strict';

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.useAnimatedRef = useAnimatedRef;
var _react = require("react");
var _useSharedValue = require("./useSharedValue.js");
var _fabricUtils = require("../fabricUtils");
var _shareables = require("../shareables.js");
var _shareableMappingCache = require("../shareableMappingCache.js");
var _reactNative = require("react-native");
var _findNodeHandle = require("../platformFunctions/findNodeHandle");
var _PlatformChecker = require("../PlatformChecker.js");
var IS_WEB = (0, _PlatformChecker.isWeb)();
function getComponentOrScrollable(component) {
  if ((0, _PlatformChecker.isFabric)() && component.getNativeScrollRef) {
    return component.getNativeScrollRef();
  } else if (!(0, _PlatformChecker.isFabric)() && component.getScrollableNode) {
    return component.getScrollableNode();
  }
  return component;
}
function useAnimatedRef() {
  var tag = (0, _useSharedValue.useSharedValue)(-1);
  var viewName = (0, _useSharedValue.useSharedValue)(null);
  var ref = (0, _react.useRef)();
  if (!ref.current) {
    var _fun = function fun(component) {
      if (component) {
        var getTagValueFunction = (0, _PlatformChecker.isFabric)() ? _fabricUtils.getShadowNodeWrapperFromRef : _findNodeHandle.findNodeHandle;
        var getTagOrShadowNodeWrapper = function getTagOrShadowNodeWrapper() {
          return IS_WEB ? getComponentOrScrollable(component) : getTagValueFunction(getComponentOrScrollable(component));
        };
        tag.value = getTagOrShadowNodeWrapper();
        _fun.getTag = (0, _PlatformChecker.isFabric)() ? function () {
          return (0, _findNodeHandle.findNodeHandle)(getComponentOrScrollable(component));
        } : getTagOrShadowNodeWrapper;
        _fun.current = component;
        if (_reactNative.Platform.OS === 'ios' && !(0, _PlatformChecker.isFabric)()) {
          var _component$viewConfig;
          viewName.value = (component == null || (_component$viewConfig = component.viewConfig) == null ? void 0 : _component$viewConfig.uiViewClassName) || 'RCTView';
        }
      }
      return tag.value;
    };
    _fun.current = null;
    var animatedRefShareableHandle = (0, _shareables.makeShareableCloneRecursive)({
      __init: function __init() {
        'worklet';

        var f = function f() {
          return tag.value;
        };
        f.viewName = viewName;
        return f;
      }
    });
    _shareableMappingCache.shareableMappingCache.set(_fun, animatedRefShareableHandle);
    ref.current = _fun;
  }
  return ref.current;
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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