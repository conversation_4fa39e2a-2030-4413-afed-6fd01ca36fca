{"version": 3, "names": ["Object", "defineProperty", "exports", "value", "useAnimatedRef", "_react", "require", "_useSharedValue", "_fabricUtils", "_shareables", "_shareableMappingCache", "_reactNative", "_findNodeHandle", "_PlatformChecker", "IS_WEB", "isWeb", "getComponentOrScrollable", "component", "isF<PERSON><PERSON>", "getNativeScrollRef", "getScrollableNode", "tag", "useSharedValue", "viewName", "ref", "useRef", "current", "fun", "getTagValueFunction", "getShadowNodeWrapperFromRef", "findNodeHandle", "getTagOrShadowNodeWrapper", "getTag", "Platform", "OS", "_component$viewConfig", "viewConfig", "uiViewClassName", "animatedRefShareableHandle", "makeShareableCloneRecursive", "__init", "f", "shareableMappingCache", "set"], "sources": ["../../../src/hook/useAnimatedRef.ts"], "sourcesContent": [null], "mappings": "AAAA,YAAY;;AAAAA,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,KAAA;AAAA;AAAAD,OAAA,CAAAE,cAAA,GAAAA,cAAA;AAEZ,IAAAC,MAAA,GAAAC,OAAA;AACA,IAAAC,eAAA,GAAAD,OAAA;AAGA,IAAAE,YAAA,GAAAF,OAAA;AACA,IAAAG,WAAA,GAAAH,OAAA;AACA,IAAAI,sBAAA,GAAAJ,OAAA;AACA,IAAAK,YAAA,GAAAL,OAAA;AACA,IAAAM,eAAA,GAAAN,OAAA;AAEA,IAAAO,gBAAA,GAAAP,OAAA;AAEA,IAAMQ,MAAM,GAAG,IAAAC,sBAAK,EAAC,CAAC;AAYtB,SAASC,wBAAwBA,CAACC,SAAmC,EAAE;EACrE,IAAI,IAAAC,yBAAQ,EAAC,CAAC,IAAID,SAAS,CAACE,kBAAkB,EAAE;IAC9C,OAAOF,SAAS,CAACE,kBAAkB,CAAC,CAAC;EACvC,CAAC,MAAM,IAAI,CAAC,IAAAD,yBAAQ,EAAC,CAAC,IAAID,SAAS,CAACG,iBAAiB,EAAE;IACrD,OAAOH,SAAS,CAACG,iBAAiB,CAAC,CAAC;EACtC;EACA,OAAOH,SAAS;AAClB;AASO,SAASb,cAAcA,CAAA,EAED;EAC3B,IAAMiB,GAAG,GAAG,IAAAC,8BAAc,EAAoC,CAAC,CAAC,CAAC;EACjE,IAAMC,QAAQ,GAAG,IAAAD,8BAAc,EAAgB,IAAI,CAAC;EAEpD,IAAME,GAAG,GAAG,IAAAC,aAAM,EAA0B,CAAC;EAE7C,IAAI,CAACD,GAAG,CAACE,OAAO,EAAE;IAChB,IAAMC,IAA4B,GAChC,SADIA,GAA4BA,CAChCV,SAAS,EACN;MAEH,IAAIA,SAAS,EAAE;QACb,IAAMW,mBAAmB,GAAG,IAAAV,yBAAQ,EAAC,CAAC,GAClCW,wCAA2B,GAC3BC,8BAAc;QAElB,IAAMC,yBAAyB,GAAG,SAA5BA,yBAAyBA,CAAA,EAAS;UACtC,OAAOjB,MAAM,GACTE,wBAAwB,CAACC,SAAS,CAAC,GACnCW,mBAAmB,CAACZ,wBAAwB,CAACC,SAAS,CAAC,CAAC;QAC9D,CAAC;QAEDI,GAAG,CAAClB,KAAK,GAAG4B,yBAAyB,CAAC,CAAC;QAGvCJ,IAAG,CAACK,MAAM,GAAG,IAAAd,yBAAQ,EAAC,CAAC,GACnB;UAAA,OAAM,IAAAY,8BAAc,EAACd,wBAAwB,CAACC,SAAS,CAAC,CAAC;QAAA,IACzDc,yBAAyB;QAE7BJ,IAAG,CAACD,OAAO,GAAGT,SAAS;QAEvB,IAAIgB,qBAAQ,CAACC,EAAE,KAAK,KAAK,IAAI,CAAC,IAAAhB,yBAAQ,EAAC,CAAC,EAAE;UAAA,IAAAiB,qBAAA;UACxCZ,QAAQ,CAACpB,KAAK,GACX,CAAAc,SAAS,aAAAkB,qBAAA,GAATlB,SAAS,CAA+BmB,UAAU,qBAAlDD,qBAAA,CACGE,eAAe,KAAI,SAAS;QACpC;MACF;MACA,OAAOhB,GAAG,CAAClB,KAAK;IAClB,CAAE;IAEFwB,IAAG,CAACD,OAAO,GAAG,IAAI;IAElB,IAAMY,0BAA0B,GAAG,IAAAC,uCAA2B,EAAC;MAC7DC,MAAM,EAAE,SAARA,MAAMA,CAAA,EAAQ;QACZ,SAAS;;QACT,IAAMC,CAAkB,GAAG,SAArBA,CAAkBA,CAAA;UAAA,OAASpB,GAAG,CAAClB,KAAK;QAAA;QAC1CsC,CAAC,CAAClB,QAAQ,GAAGA,QAAQ;QACrB,OAAOkB,CAAC;MACV;IACF,CAAC,CAAC;IACFC,4CAAqB,CAACC,GAAG,CAAChB,IAAG,EAAEW,0BAA0B,CAAC;IAC1Dd,GAAG,CAACE,OAAO,GAAGC,IAAG;EACnB;EAEA,OAAOH,GAAG,CAACE,OAAO;AACpB", "ignoreList": []}