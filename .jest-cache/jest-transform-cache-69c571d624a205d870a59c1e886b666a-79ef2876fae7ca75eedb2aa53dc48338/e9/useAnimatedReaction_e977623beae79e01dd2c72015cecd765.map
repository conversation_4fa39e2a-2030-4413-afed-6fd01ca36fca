{"version": 3, "names": ["_interopRequireDefault", "require", "Object", "defineProperty", "exports", "value", "useAnimatedReaction", "_toConsumableArray2", "_react", "_core", "_useSharedValue", "_PlatformChecker", "prepare", "react", "dependencies", "_prepare$__closure", "previous", "useSharedValue", "inputs", "values", "__closure", "shouldBeUseWeb", "_dependencies", "length", "undefined", "_prepare$__closure2", "_react$__closure", "concat", "default", "__workletHash", "push", "useEffect", "fun", "input", "mapperId", "startMapper", "stopMapper"], "sources": ["../../../src/hook/useAnimatedReaction.ts"], "sourcesContent": [null], "mappings": "AAAA,YAAY;;AAAA,IAAAA,sBAAA,GAAAC,OAAA;AAAAC,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,KAAA;AAAA;AAAAD,OAAA,CAAAE,mBAAA,GAAAA,mBAAA;AAAA,IAAAC,mBAAA,GAAAP,sBAAA,CAAAC,OAAA;AACZ,IAAAO,MAAA,GAAAP,OAAA;AAEA,IAAAQ,KAAA,GAAAR,OAAA;AAEA,IAAAS,eAAA,GAAAT,OAAA;AACA,IAAAU,gBAAA,GAAAV,OAAA;AAuBO,SAASK,mBAAmBA,CACjCM,OAA4C,EAC5CC,KAGC,EACDC,YAA6B,EAC7B;EAAA,IAAAC,kBAAA;EACA,IAAMC,QAAQ,GAAG,IAAAC,8BAAc,EAAwB,IAAI,CAAC;EAE5D,IAAIC,MAAM,GAAGhB,MAAM,CAACiB,MAAM,EAAAJ,kBAAA,GAACH,OAAO,CAACQ,SAAS,YAAAL,kBAAA,GAAI,CAAC,CAAC,CAAC;EAEnD,IAAI,IAAAM,+BAAc,EAAC,CAAC,EAAE;IAAA,IAAAC,aAAA;IACpB,IAAI,CAACJ,MAAM,CAACK,MAAM,KAAAD,aAAA,GAAIR,YAAY,aAAZQ,aAAA,CAAcC,MAAM,EAAE;MAE1CL,MAAM,GAAGJ,YAAY;IACvB;EACF;EAEA,IAAIA,YAAY,KAAKU,SAAS,EAAE;IAAA,IAAAC,mBAAA,EAAAC,gBAAA;IAC9BZ,YAAY,MAAAa,MAAA,KAAApB,mBAAA,CAAAqB,OAAA,EACP1B,MAAM,CAACiB,MAAM,EAAAM,mBAAA,GAACb,OAAO,CAACQ,SAAS,YAAAK,mBAAA,GAAI,CAAC,CAAC,CAAC,OAAAlB,mBAAA,CAAAqB,OAAA,EACtC1B,MAAM,CAACiB,MAAM,EAAAO,gBAAA,GAACb,KAAK,CAACO,SAAS,YAAAM,gBAAA,GAAI,CAAC,CAAC,CAAC,IACvCd,OAAO,CAACiB,aAAa,EACrBhB,KAAK,CAACgB,aAAa,EACpB;EACH,CAAC,MAAM;IACLf,YAAY,CAACgB,IAAI,CAAClB,OAAO,CAACiB,aAAa,EAAEhB,KAAK,CAACgB,aAAa,CAAC;EAC/D;EAEA,IAAAE,gBAAS,EAAC,YAAM;IACd,IAAMC,GAAG,GAAG,SAANA,GAAGA,CAAA,EAAS;MAChB,SAAS;;MACT,IAAMC,KAAK,GAAGrB,OAAO,CAAC,CAAC;MACvBC,KAAK,CAACoB,KAAK,EAAEjB,QAAQ,CAACX,KAAK,CAAC;MAC5BW,QAAQ,CAACX,KAAK,GAAG4B,KAAK;IACxB,CAAC;IACD,IAAMC,QAAQ,GAAG,IAAAC,iBAAW,EAACH,GAAG,EAAEd,MAAM,CAAC;IACzC,OAAO,YAAM;MACX,IAAAkB,gBAAU,EAACF,QAAQ,CAAC;IACtB,CAAC;EACH,CAAC,EAAEpB,YAAY,CAAC;AAClB", "ignoreList": []}