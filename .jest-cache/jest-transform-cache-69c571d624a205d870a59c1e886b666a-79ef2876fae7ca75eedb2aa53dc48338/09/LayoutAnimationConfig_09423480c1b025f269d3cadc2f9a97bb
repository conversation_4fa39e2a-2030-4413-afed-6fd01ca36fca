24507ecfccdabab0ddf73944d47a415b
'use strict';

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.SkipEnteringContext = exports.LayoutAnimationConfig = void 0;
var _classCallCheck2 = _interopRequireDefault(require("@babel/runtime/helpers/classCallCheck"));
var _createClass2 = _interopRequireDefault(require("@babel/runtime/helpers/createClass"));
var _possibleConstructorReturn2 = _interopRequireDefault(require("@babel/runtime/helpers/possibleConstructorReturn"));
var _getPrototypeOf2 = _interopRequireDefault(require("@babel/runtime/helpers/getPrototypeOf"));
var _inherits2 = _interopRequireDefault(require("@babel/runtime/helpers/inherits"));
var _react = _interopRequireWildcard(require("react"));
var _core = require("../core.js");
var _findNodeHandle = require("../platformFunctions/findNodeHandle");
function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
function _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }
function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }
var SkipEnteringContext = exports.SkipEnteringContext = (0, _react.createContext)(null);
function SkipEntering(props) {
  var skipValueRef = (0, _react.useRef)(props.shouldSkip);
  (0, _react.useEffect)(function () {
    skipValueRef.current = false;
  }, [skipValueRef]);
  return _react.default.createElement(SkipEnteringContext.Provider, {
    value: skipValueRef
  }, props.children);
}
var LayoutAnimationConfig = exports.LayoutAnimationConfig = function (_Component) {
  function LayoutAnimationConfig() {
    (0, _classCallCheck2.default)(this, LayoutAnimationConfig);
    return _callSuper(this, LayoutAnimationConfig, arguments);
  }
  (0, _inherits2.default)(LayoutAnimationConfig, _Component);
  return (0, _createClass2.default)(LayoutAnimationConfig, [{
    key: "getMaybeWrappedChildren",
    value: function getMaybeWrappedChildren() {
      return _react.Children.count(this.props.children) > 1 && this.props.skipExiting ? _react.Children.map(this.props.children, function (child) {
        return _react.default.createElement(LayoutAnimationConfig, {
          skipExiting: true
        }, child);
      }) : this.props.children;
    }
  }, {
    key: "setShouldAnimateExiting",
    value: function setShouldAnimateExiting() {
      if (_react.Children.count(this.props.children) === 1) {
        var tag = (0, _findNodeHandle.findNodeHandle)(this);
        if (tag) {
          (0, _core.setShouldAnimateExitingForTag)(tag, !this.props.skipExiting);
        }
      }
    }
  }, {
    key: "componentWillUnmount",
    value: function componentWillUnmount() {
      if (this.props.skipExiting !== undefined) {
        this.setShouldAnimateExiting();
      }
    }
  }, {
    key: "render",
    value: function render() {
      var children = this.getMaybeWrappedChildren();
      if (this.props.skipEntering === undefined) {
        return children;
      }
      return _react.default.createElement(SkipEntering, {
        shouldSkip: this.props.skipEntering
      }, children);
    }
  }]);
}(_react.Component);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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