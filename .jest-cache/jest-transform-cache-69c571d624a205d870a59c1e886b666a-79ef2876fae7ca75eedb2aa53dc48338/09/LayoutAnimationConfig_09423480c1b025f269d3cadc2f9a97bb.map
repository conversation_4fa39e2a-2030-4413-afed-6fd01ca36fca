{"version": 3, "names": ["_interopRequireDefault", "require", "Object", "defineProperty", "exports", "value", "SkipEnteringContext", "LayoutAnimationConfig", "_classCallCheck2", "_createClass2", "_possibleConstructorReturn2", "_getPrototypeOf2", "_inherits2", "_react", "_interopRequireWildcard", "_core", "_findNodeHandle", "_getRequireWildcardCache", "e", "WeakMap", "r", "t", "__esModule", "default", "has", "get", "n", "__proto__", "a", "getOwnPropertyDescriptor", "u", "hasOwnProperty", "call", "i", "set", "_callSuper", "o", "_isNativeReflectConstruct", "Reflect", "construct", "constructor", "apply", "Boolean", "prototype", "valueOf", "createContext", "SkipEntering", "props", "skipV<PERSON>ue<PERSON>ef", "useRef", "shouldSkip", "useEffect", "current", "React", "createElement", "Provider", "children", "_Component", "arguments", "key", "getMaybeWrappedChildren", "Children", "count", "skipExiting", "map", "child", "setShouldAnimateExiting", "tag", "findNodeHandle", "setShouldAnimateExitingForTag", "componentWillUnmount", "undefined", "render", "skipEntering", "Component"], "sources": ["../../../src/component/LayoutAnimationConfig.tsx"], "sourcesContent": [null], "mappings": "AAAA,YAAY;;AAAA,IAAAA,sBAAA,GAAAC,OAAA;AAAAC,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,KAAA;AAAA;AAAAD,OAAA,CAAAE,mBAAA,GAAAF,OAAA,CAAAG,qBAAA;AAAA,IAAAC,gBAAA,GAAAR,sBAAA,CAAAC,OAAA;AAAA,IAAAQ,aAAA,GAAAT,sBAAA,CAAAC,OAAA;AAAA,IAAAS,2BAAA,GAAAV,sBAAA,CAAAC,OAAA;AAAA,IAAAU,gBAAA,GAAAX,sBAAA,CAAAC,OAAA;AAAA,IAAAW,UAAA,GAAAZ,sBAAA,CAAAC,OAAA;AACZ,IAAAY,MAAA,GAAAC,uBAAA,CAAAb,OAAA;AAQA,IAAAc,KAAA,GAAAd,OAAA;AACA,IAAAe,eAAA,GAAAf,OAAA;AAAoE,SAAAgB,yBAAAC,CAAA,6BAAAC,OAAA,mBAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,yBAAAC,CAAA,WAAAA,CAAA,GAAAG,CAAA,GAAAD,CAAA,KAAAF,CAAA;AAAA,SAAAJ,wBAAAI,CAAA,EAAAE,CAAA,SAAAA,CAAA,IAAAF,CAAA,IAAAA,CAAA,CAAAI,UAAA,SAAAJ,CAAA,eAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,WAAAK,OAAA,EAAAL,CAAA,QAAAG,CAAA,GAAAJ,wBAAA,CAAAG,CAAA,OAAAC,CAAA,IAAAA,CAAA,CAAAG,GAAA,CAAAN,CAAA,UAAAG,CAAA,CAAAI,GAAA,CAAAP,CAAA,OAAAQ,CAAA,KAAAC,SAAA,UAAAC,CAAA,GAAA1B,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAA2B,wBAAA,WAAAC,CAAA,IAAAZ,CAAA,oBAAAY,CAAA,OAAAC,cAAA,CAAAC,IAAA,CAAAd,CAAA,EAAAY,CAAA,SAAAG,CAAA,GAAAL,CAAA,GAAA1B,MAAA,CAAA2B,wBAAA,CAAAX,CAAA,EAAAY,CAAA,UAAAG,CAAA,KAAAA,CAAA,CAAAR,GAAA,IAAAQ,CAAA,CAAAC,GAAA,IAAAhC,MAAA,CAAAC,cAAA,CAAAuB,CAAA,EAAAI,CAAA,EAAAG,CAAA,IAAAP,CAAA,CAAAI,CAAA,IAAAZ,CAAA,CAAAY,CAAA,YAAAJ,CAAA,CAAAH,OAAA,GAAAL,CAAA,EAAAG,CAAA,IAAAA,CAAA,CAAAa,GAAA,CAAAhB,CAAA,EAAAQ,CAAA,GAAAA,CAAA;AAAA,SAAAS,WAAAd,CAAA,EAAAe,CAAA,EAAAlB,CAAA,WAAAkB,CAAA,OAAAzB,gBAAA,CAAAY,OAAA,EAAAa,CAAA,OAAA1B,2BAAA,CAAAa,OAAA,EAAAF,CAAA,EAAAgB,yBAAA,KAAAC,OAAA,CAAAC,SAAA,CAAAH,CAAA,EAAAlB,CAAA,YAAAP,gBAAA,CAAAY,OAAA,EAAAF,CAAA,EAAAmB,WAAA,IAAAJ,CAAA,CAAAK,KAAA,CAAApB,CAAA,EAAAH,CAAA;AAAA,SAAAmB,0BAAA,cAAAhB,CAAA,IAAAqB,OAAA,CAAAC,SAAA,CAAAC,OAAA,CAAAZ,IAAA,CAAAM,OAAA,CAAAC,SAAA,CAAAG,OAAA,iCAAArB,CAAA,aAAAgB,yBAAA,YAAAA,0BAAA,aAAAhB,CAAA;AAE7D,IAAMf,mBAAmB,GAAAF,OAAA,CAAAE,mBAAA,GAC9B,IAAAuC,oBAAa,EAAyC,IAAI,CAAC;AAU7D,SAASC,YAAYA,CAACC,KAAmD,EAAE;EACzE,IAAMC,YAAY,GAAG,IAAAC,aAAM,EAACF,KAAK,CAACG,UAAU,CAAC;EAE7C,IAAAC,gBAAS,EAAC,YAAM;IACdH,YAAY,CAACI,OAAO,GAAG,KAAK;EAC9B,CAAC,EAAE,CAACJ,YAAY,CAAC,CAAC;EAElB,OACEK,cAAA,CAAAC,aAAA,CAAChD,mBAAmB,CAACiD,QAAQ;IAAClD,KAAK,EAAE2C;EAAa,GAC/CD,KAAK,CAACS,QACqB,CAAC;AAEnC;AAAA,IAkBajD,qBAAqB,GAAAH,OAAA,CAAAG,qBAAA,aAAAkD,UAAA;EAAA,SAAAlD,sBAAA;IAAA,IAAAC,gBAAA,CAAAe,OAAA,QAAAhB,qBAAA;IAAA,OAAA4B,UAAA,OAAA5B,qBAAA,EAAAmD,SAAA;EAAA;EAAA,IAAA9C,UAAA,CAAAW,OAAA,EAAAhB,qBAAA,EAAAkD,UAAA;EAAA,WAAAhD,aAAA,CAAAc,OAAA,EAAAhB,qBAAA;IAAAoD,GAAA;IAAAtD,KAAA,EAChC,SAAAuD,uBAAuBA,CAAA,EAAG;MACxB,OAAOC,eAAQ,CAACC,KAAK,CAAC,IAAI,CAACf,KAAK,CAACS,QAAQ,CAAC,GAAG,CAAC,IAAI,IAAI,CAACT,KAAK,CAACgB,WAAW,GACpEF,eAAQ,CAACG,GAAG,CAAC,IAAI,CAACjB,KAAK,CAACS,QAAQ,EAAG,UAAAS,KAAK;QAAA,OACtCZ,cAAA,CAAAC,aAAA,CAAC/C,qBAAqB;UAACwD,WAAW;QAAA,GAAEE,KAA6B,CAClE;MAAA,EAAC,GACF,IAAI,CAAClB,KAAK,CAACS,QAAQ;IACzB;EAAA;IAAAG,GAAA;IAAAtD,KAAA,EAEA,SAAA6D,uBAAuBA,CAAA,EAAG;MACxB,IAAIL,eAAQ,CAACC,KAAK,CAAC,IAAI,CAACf,KAAK,CAACS,QAAQ,CAAC,KAAK,CAAC,EAAE;QAC7C,IAAMW,GAAG,GAAG,IAAAC,8BAAc,EAAC,IAAI,CAAC;QAChC,IAAID,GAAG,EAAE;UACP,IAAAE,mCAA6B,EAACF,GAAG,EAAE,CAAC,IAAI,CAACpB,KAAK,CAACgB,WAAW,CAAC;QAC7D;MACF;IACF;EAAA;IAAAJ,GAAA;IAAAtD,KAAA,EAEA,SAAAiE,oBAAoBA,CAAA,EAAS;MAC3B,IAAI,IAAI,CAACvB,KAAK,CAACgB,WAAW,KAAKQ,SAAS,EAAE;QACxC,IAAI,CAACL,uBAAuB,CAAC,CAAC;MAChC;IACF;EAAA;IAAAP,GAAA;IAAAtD,KAAA,EAEA,SAAAmE,MAAMA,CAAA,EAAc;MAClB,IAAMhB,QAAQ,GAAG,IAAI,CAACI,uBAAuB,CAAC,CAAC;MAE/C,IAAI,IAAI,CAACb,KAAK,CAAC0B,YAAY,KAAKF,SAAS,EAAE;QACzC,OAAOf,QAAQ;MACjB;MAEA,OACEH,cAAA,CAAAC,aAAA,CAACR,YAAY;QAACI,UAAU,EAAE,IAAI,CAACH,KAAK,CAAC0B;MAAa,GAC/CjB,QACW,CAAC;IAEnB;EAAA;AAAA,EApCyCkB,gBAAS", "ignoreList": []}