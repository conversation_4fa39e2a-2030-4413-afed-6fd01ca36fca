07ffce53bb862b93d343e9ae7f7eee53
var _ViewConfigIgnore = require("../../NativeComponent/ViewConfigIgnore");
var RCTTextInputViewConfig = {
  bubblingEventTypes: {
    topBlur: {
      phasedRegistrationNames: {
        bubbled: 'onBlur',
        captured: 'onBlurCapture'
      }
    },
    topChange: {
      phasedRegistrationNames: {
        bubbled: 'onChange',
        captured: 'onChangeCapture'
      }
    },
    topEndEditing: {
      phasedRegistrationNames: {
        bubbled: 'onEndEditing',
        captured: 'onEndEditingCapture'
      }
    },
    topFocus: {
      phasedRegistrationNames: {
        bubbled: 'onFocus',
        captured: 'onFocusCapture'
      }
    },
    topKeyPress: {
      phasedRegistrationNames: {
        bubbled: 'onKeyPress',
        captured: 'onKeyPressCapture'
      }
    },
    topSubmitEditing: {
      phasedRegistrationNames: {
        bubbled: 'onSubmitEditing',
        captured: 'onSubmitEditingCapture'
      }
    },
    topTouchCancel: {
      phasedRegistrationNames: {
        bubbled: 'onTouchCancel',
        captured: 'onTouchCancelCapture'
      }
    },
    topTouchEnd: {
      phasedRegistrationNames: {
        bubbled: 'onTouchEnd',
        captured: 'onTouchEndCapture'
      }
    },
    topTouchMove: {
      phasedRegistrationNames: {
        bubbled: 'onTouchMove',
        captured: 'onTouchMoveCapture'
      }
    }
  },
  directEventTypes: {
    topScroll: {
      registrationName: 'onScroll'
    },
    topSelectionChange: {
      registrationName: 'onSelectionChange'
    },
    topContentSizeChange: {
      registrationName: 'onContentSizeChange'
    },
    topChangeSync: {
      registrationName: 'onChangeSync'
    },
    topKeyPressSync: {
      registrationName: 'onKeyPressSync'
    }
  },
  validAttributes: Object.assign({
    dynamicTypeRamp: true,
    fontSize: true,
    fontWeight: true,
    fontVariant: true,
    textShadowOffset: {
      diff: require('../../Utilities/differ/sizesDiffer')
    },
    allowFontScaling: true,
    fontStyle: true,
    textTransform: true,
    textAlign: true,
    fontFamily: true,
    lineHeight: true,
    isHighlighted: true,
    writingDirection: true,
    textDecorationLine: true,
    textShadowRadius: true,
    letterSpacing: true,
    textDecorationStyle: true,
    textDecorationColor: {
      process: require('../../StyleSheet/processColor').default
    },
    color: {
      process: require('../../StyleSheet/processColor').default
    },
    maxFontSizeMultiplier: true,
    textShadowColor: {
      process: require('../../StyleSheet/processColor').default
    },
    editable: true,
    inputAccessoryViewID: true,
    inputAccessoryViewButtonLabel: true,
    caretHidden: true,
    enablesReturnKeyAutomatically: true,
    placeholderTextColor: {
      process: require('../../StyleSheet/processColor').default
    },
    clearButtonMode: true,
    keyboardType: true,
    selection: true,
    returnKeyType: true,
    submitBehavior: true,
    mostRecentEventCount: true,
    scrollEnabled: true,
    selectionColor: {
      process: require('../../StyleSheet/processColor').default
    },
    contextMenuHidden: true,
    secureTextEntry: true,
    placeholder: true,
    autoCorrect: true,
    multiline: true,
    textContentType: true,
    maxLength: true,
    autoCapitalize: true,
    keyboardAppearance: true,
    passwordRules: true,
    spellCheck: true,
    selectTextOnFocus: true,
    text: true,
    clearTextOnFocus: true,
    showSoftInputOnFocus: true,
    autoFocus: true,
    lineBreakStrategyIOS: true,
    lineBreakModeIOS: true,
    smartInsertDelete: true
  }, (0, _ViewConfigIgnore.ConditionallyIgnoredEventHandlers)({
    onChange: true,
    onSelectionChange: true,
    onContentSizeChange: true,
    onScroll: true,
    onChangeSync: true,
    onKeyPressSync: true
  }), {
    disableKeyboardShortcuts: true
  })
};
module.exports = RCTTextInputViewConfig;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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