{"version": 3, "names": ["_ViewConfigIgnore", "require", "RCTTextInputViewConfig", "bubblingEventTypes", "topBlur", "phasedRegistrationNames", "bubbled", "captured", "topChange", "topEndEditing", "topFocus", "topKeyPress", "topSubmitEditing", "topTouchCancel", "topTouchEnd", "topTouchMove", "directEventTypes", "topScroll", "registrationName", "topSelectionChange", "topContentSizeChange", "topChangeSync", "topKeyPressSync", "validAttributes", "Object", "assign", "dynamicTypeRamp", "fontSize", "fontWeight", "fontVariant", "textShadowOffset", "diff", "allowFontScaling", "fontStyle", "textTransform", "textAlign", "fontFamily", "lineHeight", "isHighlighted", "writingDirection", "textDecorationLine", "textShadowRadius", "letterSpacing", "textDecorationStyle", "textDecorationColor", "process", "default", "color", "maxFontSizeMultiplier", "textShadowColor", "editable", "inputAccessoryViewID", "inputAccessoryViewButtonLabel", "caretHidden", "enablesReturnKeyAutomatically", "placeholderTextColor", "clearButtonMode", "keyboardType", "selection", "returnKeyType", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mostRecentEventCount", "scrollEnabled", "selectionColor", "contextMenuHidden", "secureTextEntry", "placeholder", "autoCorrect", "multiline", "textContentType", "max<PERSON><PERSON><PERSON>", "autoCapitalize", "keyboardAppearance", "passwordRules", "spell<PERSON>heck", "selectTextOnFocus", "text", "clearTextOnFocus", "showSoftInputOnFocus", "autoFocus", "lineBreakStrategyIOS", "lineBreakModeIOS", "smartInsertDelete", "ConditionallyIgnoredEventHandlers", "onChange", "onSelectionChange", "onContentSizeChange", "onScroll", "onChangeSync", "onKeyPressSync", "disableKeyboardShortcuts", "module", "exports"], "sources": ["RCTTextInputViewConfig.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @flow strict-local\n * @format\n */\n\nimport type {PartialViewConfig} from '../../Renderer/shims/ReactNativeTypes';\n\nimport {ConditionallyIgnoredEventHandlers} from '../../NativeComponent/ViewConfigIgnore';\n\ntype PartialViewConfigWithoutName = $Rest<\n  PartialViewConfig,\n  {uiViewClassName: string},\n>;\n\nconst RCTTextInputViewConfig = {\n  bubblingEventTypes: {\n    topBlur: {\n      phasedRegistrationNames: {\n        bubbled: 'onBlur',\n        captured: 'onBlurCapture',\n      },\n    },\n    topChange: {\n      phasedRegistrationNames: {\n        bubbled: 'onChange',\n        captured: 'onChangeCapture',\n      },\n    },\n    topEndEditing: {\n      phasedRegistrationNames: {\n        bubbled: 'onEndEditing',\n        captured: 'onEndEditingCapture',\n      },\n    },\n    topFocus: {\n      phasedRegistrationNames: {\n        bubbled: 'onFocus',\n        captured: 'onFocusCapture',\n      },\n    },\n    topKeyPress: {\n      phasedRegistrationNames: {\n        bubbled: 'onKeyPress',\n        captured: 'onKeyPressCapture',\n      },\n    },\n    topSubmitEditing: {\n      phasedRegistrationNames: {\n        bubbled: 'onSubmitEditing',\n        captured: 'onSubmitEditingCapture',\n      },\n    },\n    topTouchCancel: {\n      phasedRegistrationNames: {\n        bubbled: 'onTouchCancel',\n        captured: 'onTouchCancelCapture',\n      },\n    },\n    topTouchEnd: {\n      phasedRegistrationNames: {\n        bubbled: 'onTouchEnd',\n        captured: 'onTouchEndCapture',\n      },\n    },\n\n    topTouchMove: {\n      phasedRegistrationNames: {\n        bubbled: 'onTouchMove',\n        captured: 'onTouchMoveCapture',\n      },\n    },\n  },\n  directEventTypes: {\n    topScroll: {\n      registrationName: 'onScroll',\n    },\n    topSelectionChange: {\n      registrationName: 'onSelectionChange',\n    },\n    topContentSizeChange: {\n      registrationName: 'onContentSizeChange',\n    },\n    topChangeSync: {\n      registrationName: 'onChangeSync',\n    },\n    topKeyPressSync: {\n      registrationName: 'onKeyPressSync',\n    },\n  },\n  validAttributes: {\n    dynamicTypeRamp: true,\n    fontSize: true,\n    fontWeight: true,\n    fontVariant: true,\n    // flowlint-next-line untyped-import:off\n    textShadowOffset: {diff: require('../../Utilities/differ/sizesDiffer')},\n    allowFontScaling: true,\n    fontStyle: true,\n    textTransform: true,\n    textAlign: true,\n    fontFamily: true,\n    lineHeight: true,\n    isHighlighted: true,\n    writingDirection: true,\n    textDecorationLine: true,\n    textShadowRadius: true,\n    letterSpacing: true,\n    textDecorationStyle: true,\n    textDecorationColor: {\n      process: require('../../StyleSheet/processColor').default,\n    },\n    color: {process: require('../../StyleSheet/processColor').default},\n    maxFontSizeMultiplier: true,\n    textShadowColor: {\n      process: require('../../StyleSheet/processColor').default,\n    },\n    editable: true,\n    inputAccessoryViewID: true,\n    inputAccessoryViewButtonLabel: true,\n    caretHidden: true,\n    enablesReturnKeyAutomatically: true,\n    placeholderTextColor: {\n      process: require('../../StyleSheet/processColor').default,\n    },\n    clearButtonMode: true,\n    keyboardType: true,\n    selection: true,\n    returnKeyType: true,\n    submitBehavior: true,\n    mostRecentEventCount: true,\n    scrollEnabled: true,\n    selectionColor: {process: require('../../StyleSheet/processColor').default},\n    contextMenuHidden: true,\n    secureTextEntry: true,\n    placeholder: true,\n    autoCorrect: true,\n    multiline: true,\n    textContentType: true,\n    maxLength: true,\n    autoCapitalize: true,\n    keyboardAppearance: true,\n    passwordRules: true,\n    spellCheck: true,\n    selectTextOnFocus: true,\n    text: true,\n    clearTextOnFocus: true,\n    showSoftInputOnFocus: true,\n    autoFocus: true,\n    lineBreakStrategyIOS: true,\n    lineBreakModeIOS: true,\n    smartInsertDelete: true,\n    ...ConditionallyIgnoredEventHandlers({\n      onChange: true,\n      onSelectionChange: true,\n      onContentSizeChange: true,\n      onScroll: true,\n      onChangeSync: true,\n      onKeyPressSync: true,\n    }),\n    disableKeyboardShortcuts: true,\n  },\n};\n\nmodule.exports = (RCTTextInputViewConfig: PartialViewConfigWithoutName);\n"], "mappings": "AAYA,IAAAA,iBAAA,GAAAC,OAAA;AAOA,IAAMC,sBAAsB,GAAG;EAC7BC,kBAAkB,EAAE;IAClBC,OAAO,EAAE;MACPC,uBAAuB,EAAE;QACvBC,OAAO,EAAE,QAAQ;QACjBC,QAAQ,EAAE;MACZ;IACF,CAAC;IACDC,SAAS,EAAE;MACTH,uBAAuB,EAAE;QACvBC,OAAO,EAAE,UAAU;QACnBC,QAAQ,EAAE;MACZ;IACF,CAAC;IACDE,aAAa,EAAE;MACbJ,uBAAuB,EAAE;QACvBC,OAAO,EAAE,cAAc;QACvBC,QAAQ,EAAE;MACZ;IACF,CAAC;IACDG,QAAQ,EAAE;MACRL,uBAAuB,EAAE;QACvBC,OAAO,EAAE,SAAS;QAClBC,QAAQ,EAAE;MACZ;IACF,CAAC;IACDI,WAAW,EAAE;MACXN,uBAAuB,EAAE;QACvBC,OAAO,EAAE,YAAY;QACrBC,QAAQ,EAAE;MACZ;IACF,CAAC;IACDK,gBAAgB,EAAE;MAChBP,uBAAuB,EAAE;QACvBC,OAAO,EAAE,iBAAiB;QAC1BC,QAAQ,EAAE;MACZ;IACF,CAAC;IACDM,cAAc,EAAE;MACdR,uBAAuB,EAAE;QACvBC,OAAO,EAAE,eAAe;QACxBC,QAAQ,EAAE;MACZ;IACF,CAAC;IACDO,WAAW,EAAE;MACXT,uBAAuB,EAAE;QACvBC,OAAO,EAAE,YAAY;QACrBC,QAAQ,EAAE;MACZ;IACF,CAAC;IAEDQ,YAAY,EAAE;MACZV,uBAAuB,EAAE;QACvBC,OAAO,EAAE,aAAa;QACtBC,QAAQ,EAAE;MACZ;IACF;EACF,CAAC;EACDS,gBAAgB,EAAE;IAChBC,SAAS,EAAE;MACTC,gBAAgB,EAAE;IACpB,CAAC;IACDC,kBAAkB,EAAE;MAClBD,gBAAgB,EAAE;IACpB,CAAC;IACDE,oBAAoB,EAAE;MACpBF,gBAAgB,EAAE;IACpB,CAAC;IACDG,aAAa,EAAE;MACbH,gBAAgB,EAAE;IACpB,CAAC;IACDI,eAAe,EAAE;MACfJ,gBAAgB,EAAE;IACpB;EACF,CAAC;EACDK,eAAe,EAAAC,MAAA,CAAAC,MAAA;IACbC,eAAe,EAAE,IAAI;IACrBC,QAAQ,EAAE,IAAI;IACdC,UAAU,EAAE,IAAI;IAChBC,WAAW,EAAE,IAAI;IAEjBC,gBAAgB,EAAE;MAACC,IAAI,EAAE9B,OAAO,CAAC,oCAAoC;IAAC,CAAC;IACvE+B,gBAAgB,EAAE,IAAI;IACtBC,SAAS,EAAE,IAAI;IACfC,aAAa,EAAE,IAAI;IACnBC,SAAS,EAAE,IAAI;IACfC,UAAU,EAAE,IAAI;IAChBC,UAAU,EAAE,IAAI;IAChBC,aAAa,EAAE,IAAI;IACnBC,gBAAgB,EAAE,IAAI;IACtBC,kBAAkB,EAAE,IAAI;IACxBC,gBAAgB,EAAE,IAAI;IACtBC,aAAa,EAAE,IAAI;IACnBC,mBAAmB,EAAE,IAAI;IACzBC,mBAAmB,EAAE;MACnBC,OAAO,EAAE5C,OAAO,CAAC,+BAA+B,CAAC,CAAC6C;IACpD,CAAC;IACDC,KAAK,EAAE;MAACF,OAAO,EAAE5C,OAAO,CAAC,+BAA+B,CAAC,CAAC6C;IAAO,CAAC;IAClEE,qBAAqB,EAAE,IAAI;IAC3BC,eAAe,EAAE;MACfJ,OAAO,EAAE5C,OAAO,CAAC,+BAA+B,CAAC,CAAC6C;IACpD,CAAC;IACDI,QAAQ,EAAE,IAAI;IACdC,oBAAoB,EAAE,IAAI;IAC1BC,6BAA6B,EAAE,IAAI;IACnCC,WAAW,EAAE,IAAI;IACjBC,6BAA6B,EAAE,IAAI;IACnCC,oBAAoB,EAAE;MACpBV,OAAO,EAAE5C,OAAO,CAAC,+BAA+B,CAAC,CAAC6C;IACpD,CAAC;IACDU,eAAe,EAAE,IAAI;IACrBC,YAAY,EAAE,IAAI;IAClBC,SAAS,EAAE,IAAI;IACfC,aAAa,EAAE,IAAI;IACnBC,cAAc,EAAE,IAAI;IACpBC,oBAAoB,EAAE,IAAI;IAC1BC,aAAa,EAAE,IAAI;IACnBC,cAAc,EAAE;MAAClB,OAAO,EAAE5C,OAAO,CAAC,+BAA+B,CAAC,CAAC6C;IAAO,CAAC;IAC3EkB,iBAAiB,EAAE,IAAI;IACvBC,eAAe,EAAE,IAAI;IACrBC,WAAW,EAAE,IAAI;IACjBC,WAAW,EAAE,IAAI;IACjBC,SAAS,EAAE,IAAI;IACfC,eAAe,EAAE,IAAI;IACrBC,SAAS,EAAE,IAAI;IACfC,cAAc,EAAE,IAAI;IACpBC,kBAAkB,EAAE,IAAI;IACxBC,aAAa,EAAE,IAAI;IACnBC,UAAU,EAAE,IAAI;IAChBC,iBAAiB,EAAE,IAAI;IACvBC,IAAI,EAAE,IAAI;IACVC,gBAAgB,EAAE,IAAI;IACtBC,oBAAoB,EAAE,IAAI;IAC1BC,SAAS,EAAE,IAAI;IACfC,oBAAoB,EAAE,IAAI;IAC1BC,gBAAgB,EAAE,IAAI;IACtBC,iBAAiB,EAAE;EAAI,GACpB,IAAAC,mDAAiC,EAAC;IACnCC,QAAQ,EAAE,IAAI;IACdC,iBAAiB,EAAE,IAAI;IACvBC,mBAAmB,EAAE,IAAI;IACzBC,QAAQ,EAAE,IAAI;IACdC,YAAY,EAAE,IAAI;IAClBC,cAAc,EAAE;EAClB,CAAC,CAAC;IACFC,wBAAwB,EAAE;EAAI;AAElC,CAAC;AAEDC,MAAM,CAACC,OAAO,GAAI1F,sBAAqD", "ignoreList": []}