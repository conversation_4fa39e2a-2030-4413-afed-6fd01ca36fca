2e7f8b1bbdbb98d1791df09a44fd4c74
'use strict';

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.AnimatedText = void 0;
var _reactNative = require("react-native");
var _index = require("../createAnimatedComponent/index.js");
var AnimatedText = exports.AnimatedText = (0, _index.createAnimatedComponent)(_reactNative.Text);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJPYmplY3QiLCJkZWZpbmVQcm9wZXJ0eSIsImV4cG9ydHMiLCJ2YWx1ZSIsIkFuaW1hdGVkVGV4dCIsIl9yZWFjdE5hdGl2ZSIsInJlcXVpcmUiLCJfaW5kZXgiLCJjcmVhdGVBbmltYXRlZENvbXBvbmVudCIsIlRleHQiXSwic291cmNlcyI6WyIuLi8uLi8uLi9zcmMvY29tcG9uZW50L1RleHQudHMiXSwic291cmNlc0NvbnRlbnQiOltudWxsXSwibWFwcGluZ3MiOiJBQUFBLFlBQVk7O0FBQUFBLE1BQUEsQ0FBQUMsY0FBQSxDQUFBQyxPQUFBO0VBQUFDLEtBQUE7QUFBQTtBQUFBRCxPQUFBLENBQUFFLFlBQUE7QUFDWixJQUFBQyxZQUFBLEdBQUFDLE9BQUE7QUFDQSxJQUFBQyxNQUFBLEdBQUFELE9BQUE7QUFRTyxJQUFNRixZQUFZLEdBQUFGLE9BQUEsQ0FBQUUsWUFBQSxHQUFHLElBQUFJLDhCQUF1QixFQUFDQyxpQkFBSSxDQUFDIiwiaWdub3JlTGlzdCI6W119