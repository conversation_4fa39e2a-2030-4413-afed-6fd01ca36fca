b19f31019afe7d50c9ce8cdf0e6b2229
'use strict';

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.RotateOutData = exports.RotateOut = exports.RotateInData = exports.RotateIn = void 0;
var _animationParser = require("../animationParser.js");
var DEFAULT_ROTATE_TIME = 0.3;
var RotateInData = exports.RotateInData = {
  RotateInDownLeft: {
    name: 'RotateInDownLeft',
    style: {
      0: {
        transform: [{
          translateX: '-50%',
          translateY: '-250%',
          rotate: '-90deg'
        }],
        opacity: 0
      },
      100: {
        transform: [{
          translateX: '0%',
          translateY: '0%',
          rotate: '0deg'
        }],
        opacity: 1
      }
    },
    duration: DEFAULT_ROTATE_TIME
  },
  RotateInDownRight: {
    name: 'RotateInDownRight',
    style: {
      0: {
        transform: [{
          translateX: '40%',
          translateY: '-250%',
          rotate: '90deg'
        }],
        opacity: 0
      },
      100: {
        transform: [{
          translateX: '0%',
          translateY: '0%',
          rotate: '0deg'
        }],
        opacity: 1
      }
    },
    duration: DEFAULT_ROTATE_TIME
  },
  RotateInUpLeft: {
    name: 'RotateInUpLeft',
    style: {
      0: {
        transform: [{
          translateX: '-40%',
          translateY: '250%',
          rotate: '90deg'
        }],
        opacity: 0
      },
      100: {
        transform: [{
          translateX: '0%',
          translateY: '0%',
          rotate: '0deg'
        }],
        opacity: 1
      }
    },
    duration: DEFAULT_ROTATE_TIME
  },
  RotateInUpRight: {
    name: 'RotateInUpRight',
    style: {
      0: {
        transform: [{
          translateX: '40%',
          translateY: '250%',
          rotate: '-90deg'
        }],
        opacity: 0
      },
      100: {
        transform: [{
          translateX: '0%',
          translateY: '0%',
          rotate: '0deg'
        }],
        opacity: 1
      }
    },
    duration: DEFAULT_ROTATE_TIME
  }
};
var RotateOutData = exports.RotateOutData = {
  RotateOutDownLeft: {
    name: 'RotateOutDownLeft',
    style: {
      0: {
        transform: [{
          translateX: '0%',
          translateY: '0%',
          rotate: '0deg'
        }],
        opacity: 1
      },
      100: {
        transform: [{
          translateX: '-40%',
          translateY: '250%',
          rotate: '90deg'
        }],
        opacity: 0
      }
    },
    duration: DEFAULT_ROTATE_TIME
  },
  RotateOutDownRight: {
    name: 'RotateOutDownRight',
    style: {
      0: {
        transform: [{
          translateX: '0%',
          translateY: '0%',
          rotate: '0deg'
        }],
        opacity: 1
      },
      100: {
        transform: [{
          translateX: '40%',
          translateY: '250%',
          rotate: '-90deg'
        }],
        opacity: 0
      }
    },
    duration: DEFAULT_ROTATE_TIME
  },
  RotateOutUpLeft: {
    name: 'RotateOutUpLeft',
    style: {
      0: {
        transform: [{
          translateX: '0%',
          translateY: '0%',
          rotate: '0deg'
        }],
        opacity: 1
      },
      100: {
        transform: [{
          translateX: '-40%',
          translateY: '-250%',
          rotate: '-90deg'
        }],
        opacity: 0
      }
    },
    duration: DEFAULT_ROTATE_TIME
  },
  RotateOutUpRight: {
    name: 'RotateOutUpRight',
    style: {
      0: {
        transform: [{
          translateX: '0%',
          translateY: '0%',
          rotate: '0deg'
        }],
        opacity: 1
      },
      100: {
        transform: [{
          translateX: '40%',
          translateY: '-250%',
          rotate: '90deg'
        }],
        opacity: 0
      }
    },
    duration: DEFAULT_ROTATE_TIME
  }
};
var RotateIn = exports.RotateIn = {
  RotateInDownLeft: {
    style: (0, _animationParser.convertAnimationObjectToKeyframes)(RotateInData.RotateInDownLeft),
    duration: RotateInData.RotateInDownLeft.duration
  },
  RotateInDownRight: {
    style: (0, _animationParser.convertAnimationObjectToKeyframes)(RotateInData.RotateInDownRight),
    duration: RotateInData.RotateInDownRight.duration
  },
  RotateInUpLeft: {
    style: (0, _animationParser.convertAnimationObjectToKeyframes)(RotateInData.RotateInUpLeft),
    duration: RotateInData.RotateInUpLeft.duration
  },
  RotateInUpRight: {
    style: (0, _animationParser.convertAnimationObjectToKeyframes)(RotateInData.RotateInUpRight),
    duration: RotateInData.RotateInUpRight.duration
  }
};
var RotateOut = exports.RotateOut = {
  RotateOutDownLeft: {
    style: (0, _animationParser.convertAnimationObjectToKeyframes)(RotateOutData.RotateOutDownLeft),
    duration: RotateOutData.RotateOutDownLeft.duration
  },
  RotateOutDownRight: {
    style: (0, _animationParser.convertAnimationObjectToKeyframes)(RotateOutData.RotateOutDownRight),
    duration: RotateOutData.RotateOutDownRight.duration
  },
  RotateOutUpLeft: {
    style: (0, _animationParser.convertAnimationObjectToKeyframes)(RotateOutData.RotateOutUpLeft),
    duration: RotateOutData.RotateOutUpLeft.duration
  },
  RotateOutUpRight: {
    style: (0, _animationParser.convertAnimationObjectToKeyframes)(RotateOutData.RotateOutUpRight),
    duration: RotateOutData.RotateOutUpRight.duration
  }
};
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJPYmplY3QiLCJkZWZpbmVQcm9wZXJ0eSIsImV4cG9ydHMiLCJ2YWx1ZSIsIlJvdGF0ZU91dERhdGEiLCJSb3RhdGVPdXQiLCJSb3RhdGVJbkRhdGEiLCJSb3RhdGVJbiIsIl9hbmltYXRpb25QYXJzZXIiLCJyZXF1aXJlIiwiREVGQVVMVF9ST1RBVEVfVElNRSIsIlJvdGF0ZUluRG93bkxlZnQiLCJuYW1lIiwic3R5bGUiLCJ0cmFuc2Zvcm0iLCJ0cmFuc2xhdGVYIiwidHJhbnNsYXRlWSIsInJvdGF0ZSIsIm9wYWNpdHkiLCJkdXJhdGlvbiIsIlJvdGF0ZUluRG93blJpZ2h0IiwiUm90YXRlSW5VcExlZnQiLCJSb3RhdGVJblVwUmlnaHQiLCJSb3RhdGVPdXREb3duTGVmdCIsIlJvdGF0ZU91dERvd25SaWdodCIsIlJvdGF0ZU91dFVwTGVmdCIsIlJvdGF0ZU91dFVwUmlnaHQiLCJjb252ZXJ0QW5pbWF0aW9uT2JqZWN0VG9LZXlmcmFtZXMiXSwic291cmNlcyI6WyIuLi8uLi8uLi8uLi8uLi9zcmMvbGF5b3V0UmVhbmltYXRpb24vd2ViL2FuaW1hdGlvbi9Sb3RhdGUud2ViLnRzIl0sInNvdXJjZXNDb250ZW50IjpbbnVsbF0sIm1hcHBpbmdzIjoiQUFBQSxZQUFZOztBQUFBQSxNQUFBLENBQUFDLGNBQUEsQ0FBQUMsT0FBQTtFQUFBQyxLQUFBO0FBQUE7QUFBQUQsT0FBQSxDQUFBRSxhQUFBLEdBQUFGLE9BQUEsQ0FBQUcsU0FBQSxHQUFBSCxPQUFBLENBQUFJLFlBQUEsR0FBQUosT0FBQSxDQUFBSyxRQUFBO0FBQ1osSUFBQUMsZ0JBQUEsR0FBQUMsT0FBQTtBQUVBLElBQU1DLG1CQUFtQixHQUFHLEdBQUc7QUFFeEIsSUFBTUosWUFBWSxHQUFBSixPQUFBLENBQUFJLFlBQUEsR0FBRztFQUMxQkssZ0JBQWdCLEVBQUU7SUFDaEJDLElBQUksRUFBRSxrQkFBa0I7SUFDeEJDLEtBQUssRUFBRTtNQUNMLENBQUMsRUFBRTtRQUNEQyxTQUFTLEVBQUUsQ0FDVDtVQUNFQyxVQUFVLEVBQUUsTUFBTTtVQUNsQkMsVUFBVSxFQUFFLE9BQU87VUFDbkJDLE1BQU0sRUFBRTtRQUNWLENBQUMsQ0FDRjtRQUNEQyxPQUFPLEVBQUU7TUFDWCxDQUFDO01BQ0QsR0FBRyxFQUFFO1FBQ0hKLFNBQVMsRUFBRSxDQUNUO1VBQ0VDLFVBQVUsRUFBRSxJQUFJO1VBQ2hCQyxVQUFVLEVBQUUsSUFBSTtVQUNoQkMsTUFBTSxFQUFFO1FBQ1YsQ0FBQyxDQUNGO1FBQ0RDLE9BQU8sRUFBRTtNQUNYO0lBQ0YsQ0FBQztJQUNEQyxRQUFRLEVBQUVUO0VBQ1osQ0FBQztFQUVEVSxpQkFBaUIsRUFBRTtJQUNqQlIsSUFBSSxFQUFFLG1CQUFtQjtJQUN6QkMsS0FBSyxFQUFFO01BQ0wsQ0FBQyxFQUFFO1FBQ0RDLFNBQVMsRUFBRSxDQUNUO1VBQ0VDLFVBQVUsRUFBRSxLQUFLO1VBQ2pCQyxVQUFVLEVBQUUsT0FBTztVQUNuQkMsTUFBTSxFQUFFO1FBQ1YsQ0FBQyxDQUNGO1FBQ0RDLE9BQU8sRUFBRTtNQUNYLENBQUM7TUFDRCxHQUFHLEVBQUU7UUFDSEosU0FBUyxFQUFFLENBQ1Q7VUFDRUMsVUFBVSxFQUFFLElBQUk7VUFDaEJDLFVBQVUsRUFBRSxJQUFJO1VBQ2hCQyxNQUFNLEVBQUU7UUFDVixDQUFDLENBQ0Y7UUFDREMsT0FBTyxFQUFFO01BQ1g7SUFDRixDQUFDO0lBQ0RDLFFBQVEsRUFBRVQ7RUFDWixDQUFDO0VBRURXLGNBQWMsRUFBRTtJQUNkVCxJQUFJLEVBQUUsZ0JBQWdCO0lBQ3RCQyxLQUFLLEVBQUU7TUFDTCxDQUFDLEVBQUU7UUFDREMsU0FBUyxFQUFFLENBQ1Q7VUFDRUMsVUFBVSxFQUFFLE1BQU07VUFDbEJDLFVBQVUsRUFBRSxNQUFNO1VBQ2xCQyxNQUFNLEVBQUU7UUFDVixDQUFDLENBQ0Y7UUFDREMsT0FBTyxFQUFFO01BQ1gsQ0FBQztNQUNELEdBQUcsRUFBRTtRQUNISixTQUFTLEVBQUUsQ0FDVDtVQUNFQyxVQUFVLEVBQUUsSUFBSTtVQUNoQkMsVUFBVSxFQUFFLElBQUk7VUFDaEJDLE1BQU0sRUFBRTtRQUNWLENBQUMsQ0FDRjtRQUNEQyxPQUFPLEVBQUU7TUFDWDtJQUNGLENBQUM7SUFDREMsUUFBUSxFQUFFVDtFQUNaLENBQUM7RUFFRFksZUFBZSxFQUFFO0lBQ2ZWLElBQUksRUFBRSxpQkFBaUI7SUFDdkJDLEtBQUssRUFBRTtNQUNMLENBQUMsRUFBRTtRQUNEQyxTQUFTLEVBQUUsQ0FDVDtVQUNFQyxVQUFVLEVBQUUsS0FBSztVQUNqQkMsVUFBVSxFQUFFLE1BQU07VUFDbEJDLE1BQU0sRUFBRTtRQUNWLENBQUMsQ0FDRjtRQUNEQyxPQUFPLEVBQUU7TUFDWCxDQUFDO01BQ0QsR0FBRyxFQUFFO1FBQ0hKLFNBQVMsRUFBRSxDQUNUO1VBQ0VDLFVBQVUsRUFBRSxJQUFJO1VBQ2hCQyxVQUFVLEVBQUUsSUFBSTtVQUNoQkMsTUFBTSxFQUFFO1FBQ1YsQ0FBQyxDQUNGO1FBQ0RDLE9BQU8sRUFBRTtNQUNYO0lBQ0YsQ0FBQztJQUNEQyxRQUFRLEVBQUVUO0VBQ1o7QUFDRixDQUFDO0FBRU0sSUFBTU4sYUFBYSxHQUFBRixPQUFBLENBQUFFLGFBQUEsR0FBRztFQUMzQm1CLGlCQUFpQixFQUFFO0lBQ2pCWCxJQUFJLEVBQUUsbUJBQW1CO0lBQ3pCQyxLQUFLLEVBQUU7TUFDTCxDQUFDLEVBQUU7UUFDREMsU0FBUyxFQUFFLENBQ1Q7VUFDRUMsVUFBVSxFQUFFLElBQUk7VUFDaEJDLFVBQVUsRUFBRSxJQUFJO1VBQ2hCQyxNQUFNLEVBQUU7UUFDVixDQUFDLENBQ0Y7UUFDREMsT0FBTyxFQUFFO01BQ1gsQ0FBQztNQUNELEdBQUcsRUFBRTtRQUNISixTQUFTLEVBQUUsQ0FDVDtVQUNFQyxVQUFVLEVBQUUsTUFBTTtVQUNsQkMsVUFBVSxFQUFFLE1BQU07VUFDbEJDLE1BQU0sRUFBRTtRQUNWLENBQUMsQ0FDRjtRQUNEQyxPQUFPLEVBQUU7TUFDWDtJQUNGLENBQUM7SUFDREMsUUFBUSxFQUFFVDtFQUNaLENBQUM7RUFFRGMsa0JBQWtCLEVBQUU7SUFDbEJaLElBQUksRUFBRSxvQkFBb0I7SUFDMUJDLEtBQUssRUFBRTtNQUNMLENBQUMsRUFBRTtRQUNEQyxTQUFTLEVBQUUsQ0FDVDtVQUNFQyxVQUFVLEVBQUUsSUFBSTtVQUNoQkMsVUFBVSxFQUFFLElBQUk7VUFDaEJDLE1BQU0sRUFBRTtRQUNWLENBQUMsQ0FDRjtRQUNEQyxPQUFPLEVBQUU7TUFDWCxDQUFDO01BQ0QsR0FBRyxFQUFFO1FBQ0hKLFNBQVMsRUFBRSxDQUNUO1VBQ0VDLFVBQVUsRUFBRSxLQUFLO1VBQ2pCQyxVQUFVLEVBQUUsTUFBTTtVQUNsQkMsTUFBTSxFQUFFO1FBQ1YsQ0FBQyxDQUNGO1FBQ0RDLE9BQU8sRUFBRTtNQUNYO0lBQ0YsQ0FBQztJQUNEQyxRQUFRLEVBQUVUO0VBQ1osQ0FBQztFQUVEZSxlQUFlLEVBQUU7SUFDZmIsSUFBSSxFQUFFLGlCQUFpQjtJQUN2QkMsS0FBSyxFQUFFO01BQ0wsQ0FBQyxFQUFFO1FBQ0RDLFNBQVMsRUFBRSxDQUNUO1VBQ0VDLFVBQVUsRUFBRSxJQUFJO1VBQ2hCQyxVQUFVLEVBQUUsSUFBSTtVQUNoQkMsTUFBTSxFQUFFO1FBQ1YsQ0FBQyxDQUNGO1FBQ0RDLE9BQU8sRUFBRTtNQUNYLENBQUM7TUFDRCxHQUFHLEVBQUU7UUFDSEosU0FBUyxFQUFFLENBQ1Q7VUFDRUMsVUFBVSxFQUFFLE1BQU07VUFDbEJDLFVBQVUsRUFBRSxPQUFPO1VBQ25CQyxNQUFNLEVBQUU7UUFDVixDQUFDLENBQ0Y7UUFDREMsT0FBTyxFQUFFO01BQ1g7SUFDRixDQUFDO0lBQ0RDLFFBQVEsRUFBRVQ7RUFDWixDQUFDO0VBRURnQixnQkFBZ0IsRUFBRTtJQUNoQmQsSUFBSSxFQUFFLGtCQUFrQjtJQUN4QkMsS0FBSyxFQUFFO01BQ0wsQ0FBQyxFQUFFO1FBQ0RDLFNBQVMsRUFBRSxDQUNUO1VBQ0VDLFVBQVUsRUFBRSxJQUFJO1VBQ2hCQyxVQUFVLEVBQUUsSUFBSTtVQUNoQkMsTUFBTSxFQUFFO1FBQ1YsQ0FBQyxDQUNGO1FBQ0RDLE9BQU8sRUFBRTtNQUNYLENBQUM7TUFDRCxHQUFHLEVBQUU7UUFDSEosU0FBUyxFQUFFLENBQ1Q7VUFDRUMsVUFBVSxFQUFFLEtBQUs7VUFDakJDLFVBQVUsRUFBRSxPQUFPO1VBQ25CQyxNQUFNLEVBQUU7UUFDVixDQUFDLENBQ0Y7UUFDREMsT0FBTyxFQUFFO01BQ1g7SUFDRixDQUFDO0lBQ0RDLFFBQVEsRUFBRVQ7RUFDWjtBQUNGLENBQUM7QUFFTSxJQUFNSCxRQUFRLEdBQUFMLE9BQUEsQ0FBQUssUUFBQSxHQUFHO0VBQ3RCSSxnQkFBZ0IsRUFBRTtJQUNoQkUsS0FBSyxFQUFFLElBQUFjLGtEQUFpQyxFQUFDckIsWUFBWSxDQUFDSyxnQkFBZ0IsQ0FBQztJQUN2RVEsUUFBUSxFQUFFYixZQUFZLENBQUNLLGdCQUFnQixDQUFDUTtFQUMxQyxDQUFDO0VBQ0RDLGlCQUFpQixFQUFFO0lBQ2pCUCxLQUFLLEVBQUUsSUFBQWMsa0RBQWlDLEVBQUNyQixZQUFZLENBQUNjLGlCQUFpQixDQUFDO0lBQ3hFRCxRQUFRLEVBQUViLFlBQVksQ0FBQ2MsaUJBQWlCLENBQUNEO0VBQzNDLENBQUM7RUFDREUsY0FBYyxFQUFFO0lBQ2RSLEtBQUssRUFBRSxJQUFBYyxrREFBaUMsRUFBQ3JCLFlBQVksQ0FBQ2UsY0FBYyxDQUFDO0lBQ3JFRixRQUFRLEVBQUViLFlBQVksQ0FBQ2UsY0FBYyxDQUFDRjtFQUN4QyxDQUFDO0VBQ0RHLGVBQWUsRUFBRTtJQUNmVCxLQUFLLEVBQUUsSUFBQWMsa0RBQWlDLEVBQUNyQixZQUFZLENBQUNnQixlQUFlLENBQUM7SUFDdEVILFFBQVEsRUFBRWIsWUFBWSxDQUFDZ0IsZUFBZSxDQUFDSDtFQUN6QztBQUNGLENBQUM7QUFFTSxJQUFNZCxTQUFTLEdBQUFILE9BQUEsQ0FBQUcsU0FBQSxHQUFHO0VBQ3ZCa0IsaUJBQWlCLEVBQUU7SUFDakJWLEtBQUssRUFBRSxJQUFBYyxrREFBaUMsRUFBQ3ZCLGFBQWEsQ0FBQ21CLGlCQUFpQixDQUFDO0lBQ3pFSixRQUFRLEVBQUVmLGFBQWEsQ0FBQ21CLGlCQUFpQixDQUFDSjtFQUM1QyxDQUFDO0VBQ0RLLGtCQUFrQixFQUFFO0lBQ2xCWCxLQUFLLEVBQUUsSUFBQWMsa0RBQWlDLEVBQUN2QixhQUFhLENBQUNvQixrQkFBa0IsQ0FBQztJQUMxRUwsUUFBUSxFQUFFZixhQUFhLENBQUNvQixrQkFBa0IsQ0FBQ0w7RUFDN0MsQ0FBQztFQUNETSxlQUFlLEVBQUU7SUFDZlosS0FBSyxFQUFFLElBQUFjLGtEQUFpQyxFQUFDdkIsYUFBYSxDQUFDcUIsZUFBZSxDQUFDO0lBQ3ZFTixRQUFRLEVBQUVmLGFBQWEsQ0FBQ3FCLGVBQWUsQ0FBQ047RUFDMUMsQ0FBQztFQUNETyxnQkFBZ0IsRUFBRTtJQUNoQmIsS0FBSyxFQUFFLElBQUFjLGtEQUFpQyxFQUFDdkIsYUFBYSxDQUFDc0IsZ0JBQWdCLENBQUM7SUFDeEVQLFFBQVEsRUFBRWYsYUFBYSxDQUFDc0IsZ0JBQWdCLENBQUNQO0VBQzNDO0FBQ0YsQ0FBQyIsImlnbm9yZUxpc3QiOltdfQ==