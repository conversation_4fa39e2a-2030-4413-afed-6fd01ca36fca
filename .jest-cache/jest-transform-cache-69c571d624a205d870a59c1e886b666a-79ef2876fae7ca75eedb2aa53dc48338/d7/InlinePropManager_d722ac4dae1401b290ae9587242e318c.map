{"version": 3, "names": ["_interopRequireDefault", "require", "Object", "defineProperty", "exports", "value", "InlinePropManager", "getInlineStyle", "hasInlineStyles", "_classCallCheck2", "_createClass2", "_slicedToArray2", "_utils", "_ViewDescriptorsSet", "_ConfigHelper", "_UpdateProps", "_mappers", "_isSharedValue", "isInlineStyleTransform", "transform", "Array", "isArray", "some", "t", "inlinePropsHasChanged", "styles1", "styles2", "keys", "length", "key", "getInlinePropsUpdate", "inlineProps", "update", "_ref", "entries", "_ref2", "default", "styleValue", "isSharedValue", "map", "item", "extractSharedValuesMapFromProps", "props", "_props$style", "styles", "flattenArray", "style", "for<PERSON>ach", "_ref3", "_ref4", "styleKey", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "newStyle", "_ref5", "_ref6", "_inlinePropsViewDescriptors", "_inlinePropsMapperId", "_inlineProps", "attachInlineProps", "animatedComponent", "viewInfo", "newInlineProps", "has<PERSON><PERSON>ed", "makeViewDescriptorsSet", "viewTag", "viewName", "shadowNodeWrapper", "viewConfig", "adaptViewConfig", "add", "tag", "name", "shareableViewDescriptors", "updaterFunction", "updateProps", "stopMapper", "startMapper", "values", "detachInlineProps"], "sources": ["../../../src/createAnimatedComponent/InlinePropManager.ts"], "sourcesContent": [null], "mappings": "AAAA,YAAY;;AAAA,IAAAA,sBAAA,GAAAC,OAAA;AAAAC,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,KAAA;AAAA;AAAAD,OAAA,CAAAE,iBAAA;AAAAF,OAAA,CAAAG,cAAA,GAAAA,cAAA;AAAAH,OAAA,CAAAI,eAAA,GAAAA,eAAA;AAAA,IAAAC,gBAAA,GAAAT,sBAAA,CAAAC,OAAA;AAAA,IAAAS,aAAA,GAAAV,sBAAA,CAAAC,OAAA;AAAA,IAAAU,eAAA,GAAAX,sBAAA,CAAAC,OAAA;AAQZ,IAAAW,MAAA,GAAAX,OAAA;AACA,IAAAY,mBAAA,GAAAZ,OAAA;AAEA,IAAAa,aAAA,GAAAb,OAAA;AACA,IAAAc,YAAA,GAAAf,sBAAA,CAAAC,OAAA;AACA,IAAAe,QAAA,GAAAf,OAAA;AACA,IAAAgB,cAAA,GAAAhB,OAAA;AAEA,SAASiB,sBAAsBA,CAACC,SAAkB,EAAW;EAC3D,IAAI,CAACC,KAAK,CAACC,OAAO,CAACF,SAAS,CAAC,EAAE;IAC7B,OAAO,KAAK;EACd;EAEA,OAAOA,SAAS,CAACG,IAAI,CAAE,UAAAC,CAA0B;IAAA,OAAKf,eAAe,CAACe,CAAC,CAAC;EAAA,EAAC;AAC3E;AAEA,SAASC,qBAAqBA,CAC5BC,OAAmB,EACnBC,OAAmB,EACV;EACT,IAAIxB,MAAM,CAACyB,IAAI,CAACF,OAAO,CAAC,CAACG,MAAM,KAAK1B,MAAM,CAACyB,IAAI,CAACD,OAAO,CAAC,CAACE,MAAM,EAAE;IAC/D,OAAO,IAAI;EACb;EAEA,KAAK,IAAMC,GAAG,IAAI3B,MAAM,CAACyB,IAAI,CAACF,OAAO,CAAC,EAAE;IACtC,IAAIA,OAAO,CAACI,GAAG,CAAC,KAAKH,OAAO,CAACG,GAAG,CAAC,EAAE;MACjC,OAAO,IAAI;IACb;EACF;EAEA,OAAO,KAAK;AACd;AAEA,SAASC,oBAAoBA,CAACC,WAAoC,EAAE;EAClE,SAAS;;EACT,IAAMC,MAA+B,GAAG,CAAC,CAAC;EAC1C,SAAAC,IAAA,IAAgC/B,MAAM,CAACgC,OAAO,CAACH,WAAW,CAAC,EAAE;IAAA,IAAAI,KAAA,OAAAxB,eAAA,CAAAyB,OAAA,EAAAH,IAAA;IAAA,IAAjDJ,GAAG,GAAAM,KAAA;IAAA,IAAEE,UAAU,GAAAF,KAAA;IACzB,IAAI,IAAAG,4BAAa,EAACD,UAAU,CAAC,EAAE;MAC7BL,MAAM,CAACH,GAAG,CAAC,GAAGQ,UAAU,CAAChC,KAAK;IAChC,CAAC,MAAM,IAAIe,KAAK,CAACC,OAAO,CAACgB,UAAU,CAAC,EAAE;MACpCL,MAAM,CAACH,GAAG,CAAC,GAAGQ,UAAU,CAACE,GAAG,CAAE,UAAAC,IAAI,EAAK;QACrC,OAAOV,oBAAoB,CAACU,IAAI,CAAC;MACnC,CAAC,CAAC;IACJ,CAAC,MAAM,IAAI,OAAOH,UAAU,KAAK,QAAQ,EAAE;MACzCL,MAAM,CAACH,GAAG,CAAC,GAAGC,oBAAoB,CAACO,UAAqC,CAAC;IAC3E,CAAC,MAAM;MACLL,MAAM,CAACH,GAAG,CAAC,GAAGQ,UAAU;IAC1B;EACF;EACA,OAAOL,MAAM;AACf;AAEA,SAASS,+BAA+BA,CACtCC,KAEC,EACwB;EACzB,IAAMX,WAAoC,GAAG,CAAC,CAAC;EAE/C,KAAK,IAAMF,GAAG,IAAIa,KAAK,EAAE;IACvB,IAAMrC,KAAK,GAAGqC,KAAK,CAACb,GAAG,CAAC;IACxB,IAAIA,GAAG,KAAK,OAAO,EAAE;MAAA,IAAAc,YAAA;MACnB,IAAMC,MAAM,GAAG,IAAAC,mBAAY,GAAAF,YAAA,GAAaD,KAAK,CAACI,KAAK,YAAAH,YAAA,GAAI,EAAE,CAAC;MAC1DC,MAAM,CAACG,OAAO,CAAE,UAAAD,KAAK,EAAK;QACxB,IAAI,CAACA,KAAK,EAAE;UACV;QACF;QACA,SAAAE,KAAA,IAAqC9C,MAAM,CAACgC,OAAO,CAACY,KAAK,CAAC,EAAE;UAAA,IAAAG,KAAA,OAAAtC,eAAA,CAAAyB,OAAA,EAAAY,KAAA;UAAA,IAAhDE,QAAQ,GAAAD,KAAA;UAAA,IAAEZ,UAAU,GAAAY,KAAA;UAC9B,IAAI,IAAAX,4BAAa,EAACD,UAAU,CAAC,EAAE;YAC7BN,WAAW,CAACmB,QAAQ,CAAC,GAAGb,UAAU;UACpC,CAAC,MAAM,IACLa,QAAQ,KAAK,WAAW,IACxBhC,sBAAsB,CAACmB,UAAU,CAAC,EAClC;YACAN,WAAW,CAACmB,QAAQ,CAAC,GAAGb,UAAU;UACpC;QACF;MACF,CAAC,CAAC;IACJ,CAAC,MAAM,IAAI,IAAAC,4BAAa,EAACjC,KAAK,CAAC,EAAE;MAC/B0B,WAAW,CAACF,GAAG,CAAC,GAAGxB,KAAK;IAC1B;EACF;EAEA,OAAO0B,WAAW;AACpB;AAEO,SAASvB,eAAeA,CAACsC,KAAiB,EAAW;EAC1D,IAAI,CAACA,KAAK,EAAE;IACV,OAAO,KAAK;EACd;EACA,OAAO5C,MAAM,CAACyB,IAAI,CAACmB,KAAK,CAAC,CAACxB,IAAI,CAAE,UAAAO,GAAG,EAAK;IACtC,IAAMQ,UAAU,GAAGS,KAAK,CAACjB,GAAG,CAAC;IAC7B,OACE,IAAAS,4BAAa,EAACD,UAAU,CAAC,IACxBR,GAAG,KAAK,WAAW,IAAIX,sBAAsB,CAACmB,UAAU,CAAE;EAE/D,CAAC,CAAC;AACJ;AAEO,SAAS9B,cAAcA,CAC5BuC,KAA8B,EAC9BK,aAAsB,EACtB;EACA,IAAIA,aAAa,EAAE;IACjB,OAAOrB,oBAAoB,CAACgB,KAAK,CAAC;EACpC;EACA,IAAMM,QAAoB,GAAG,CAAC,CAAC;EAC/B,SAAAC,KAAA,IAAgCnD,MAAM,CAACgC,OAAO,CAACY,KAAK,CAAC,EAAE;IAAA,IAAAQ,KAAA,OAAA3C,eAAA,CAAAyB,OAAA,EAAAiB,KAAA;IAAA,IAA3CxB,GAAG,GAAAyB,KAAA;IAAA,IAAEjB,UAAU,GAAAiB,KAAA;IACzB,IACE,CAAC,IAAAhB,4BAAa,EAACD,UAAU,CAAC,IAC1B,EAAER,GAAG,KAAK,WAAW,IAAIX,sBAAsB,CAACmB,UAAU,CAAC,CAAC,EAC5D;MACAe,QAAQ,CAACvB,GAAG,CAAC,GAAGQ,UAAU;IAC5B;EACF;EACA,OAAOe,QAAQ;AACjB;AAAA,IAEa9C,iBAAiB,GAAAF,OAAA,CAAAE,iBAAA;EAAA,SAAAA,kBAAA;IAAA,IAAAG,gBAAA,CAAA2B,OAAA,QAAA9B,iBAAA;IAAA,KAC5BiD,2BAA2B,GAA8B,IAAI;IAAA,KAC7DC,oBAAoB,GAAkB,IAAI;IAAA,KAC1CC,YAAY,GAAe,CAAC,CAAC;EAAA;EAAA,WAAA/C,aAAA,CAAA0B,OAAA,EAAA9B,iBAAA;IAAAuB,GAAA;IAAAxB,KAAA,EAEtB,SAAAqD,iBAAiBA,CACtBC,iBAC4B,EAC5BC,QAAkB,EAClB;MACA,IAAMC,cAAuC,GAC3CpB,+BAA+B,CAACkB,iBAAiB,CAACjB,KAAK,CAAC;MAC1D,IAAMoB,UAAU,GAAGtC,qBAAqB,CAACqC,cAAc,EAAE,IAAI,CAACJ,YAAY,CAAC;MAE3E,IAAIK,UAAU,EAAE;QACd,IAAI,CAAC,IAAI,CAACP,2BAA2B,EAAE;UACrC,IAAI,CAACA,2BAA2B,GAAG,IAAAQ,0CAAsB,EAAC,CAAC;UAE3D,IAAQC,OAAO,GAA8CJ,QAAQ,CAA7DI,OAAO;YAAEC,QAAQ,GAAoCL,QAAQ,CAApDK,QAAQ;YAAEC,iBAAiB,GAAiBN,QAAQ,CAA1CM,iBAAiB;YAAEC,UAAA,GAAeP,QAAQ,CAAvBO,UAAA;UAE9C,IAAIjE,MAAM,CAACyB,IAAI,CAACkC,cAAc,CAAC,CAACjC,MAAM,IAAIuC,UAAU,EAAE;YACpD,IAAAC,6BAAe,EAACD,UAAU,CAAC;UAC7B;UAEA,IAAI,CAACZ,2BAA2B,CAACc,GAAG,CAAC;YACnCC,GAAG,EAAEN,OAAiB;YACtBO,IAAI,EAAEN,QAAS;YACfC,iBAAiB,EAAEA;UACrB,CAAC,CAAC;QACJ;QACA,IAAMM,wBAAwB,GAC5B,IAAI,CAACjB,2BAA2B,CAACiB,wBAAwB;QAE3D,IAAMC,eAAe,GAAG,SAAlBA,eAAeA,CAAA,EAAS;UAC5B,SAAS;;UACT,IAAMzC,MAAM,GAAGF,oBAAoB,CAAC+B,cAAc,CAAC;UACnD,IAAAa,oBAAW,EAACF,wBAAwB,EAAExC,MAAM,CAAC;QAC/C,CAAC;QACD,IAAI,CAACyB,YAAY,GAAGI,cAAc;QAClC,IAAI,IAAI,CAACL,oBAAoB,EAAE;UAC7B,IAAAmB,mBAAU,EAAC,IAAI,CAACnB,oBAAoB,CAAC;QACvC;QACA,IAAI,CAACA,oBAAoB,GAAG,IAAI;QAChC,IAAItD,MAAM,CAACyB,IAAI,CAACkC,cAAc,CAAC,CAACjC,MAAM,EAAE;UACtC,IAAI,CAAC4B,oBAAoB,GAAG,IAAAoB,oBAAW,EACrCH,eAAe,EACfvE,MAAM,CAAC2E,MAAM,CAAChB,cAAc,CAC9B,CAAC;QACH;MACF;IACF;EAAA;IAAAhC,GAAA;IAAAxB,KAAA,EAEO,SAAAyE,iBAAiBA,CAAA,EAAG;MACzB,IAAI,IAAI,CAACtB,oBAAoB,EAAE;QAC7B,IAAAmB,mBAAU,EAAC,IAAI,CAACnB,oBAAoB,CAAC;MACvC;IACF;EAAA;AAAA", "ignoreList": []}