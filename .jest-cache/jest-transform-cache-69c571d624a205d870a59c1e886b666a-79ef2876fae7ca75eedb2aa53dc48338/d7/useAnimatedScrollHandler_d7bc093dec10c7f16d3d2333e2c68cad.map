{"version": 3, "names": ["Object", "defineProperty", "exports", "value", "useAnimatedScrollHandler", "_useHandler2", "require", "_useEvent", "handlers", "dependencies", "scrollHandlers", "onScroll", "_use<PERSON><PERSON>ler", "useHandler", "context", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subscribeForEvents", "onBeginDrag", "undefined", "push", "onEndDrag", "onMomentumBegin", "onMomentumEnd", "useEvent", "event", "eventName", "endsWith"], "sources": ["../../../src/hook/useAnimatedScrollHandler.ts"], "sourcesContent": [null], "mappings": "AAAA,YAAY;;AAAAA,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,KAAA;AAAA;AAAAD,OAAA,CAAAE,wBAAA,GAAAA,wBAAA;AAMZ,IAAAC,YAAA,GAAAC,OAAA;AAEA,IAAAC,SAAA,GAAAD,OAAA;AAwCO,SAASF,wBAAwBA,CAGtCI,QAA0D,EAC1DC,YAA6B,EAC7B;EAEA,IAAMC,cAAuC,GAC3C,OAAOF,QAAQ,KAAK,UAAU,GAAG;IAAEG,QAAQ,EAAEH;EAAS,CAAC,GAAGA,QAAQ;EACpE,IAAAI,WAAA,GAA0C,IAAAC,uBAAU,EAGlDH,cAAc,EAA4CD,YAAY,CAAC;IAHjEK,OAAO,GAAAF,WAAA,CAAPE,OAAO;IAAEC,oBAAA,GAAAH,WAAA,CAAAG,oBAAA;EAMjB,IAAMC,kBAAkB,GAAG,CAAC,UAAU,CAAC;EACvC,IAAIN,cAAc,CAACO,WAAW,KAAKC,SAAS,EAAE;IAC5CF,kBAAkB,CAACG,IAAI,CAAC,mBAAmB,CAAC;EAC9C;EACA,IAAIT,cAAc,CAACU,SAAS,KAAKF,SAAS,EAAE;IAC1CF,kBAAkB,CAACG,IAAI,CAAC,iBAAiB,CAAC;EAC5C;EACA,IAAIT,cAAc,CAACW,eAAe,KAAKH,SAAS,EAAE;IAChDF,kBAAkB,CAACG,IAAI,CAAC,uBAAuB,CAAC;EAClD;EACA,IAAIT,cAAc,CAACY,aAAa,KAAKJ,SAAS,EAAE;IAC9CF,kBAAkB,CAACG,IAAI,CAAC,qBAAqB,CAAC;EAChD;EAEA,OAAO,IAAAI,kBAAQ,EACZ,UAAAC,KAA4B,EAAK;IAChC,SAAS;;IACT,IACEb,QAAQ,GAKND,cAAc,CALhBC,QAAQ;MACRM,WAAW,GAITP,cAAc,CAJhBO,WAAW;MACXG,SAAS,GAGPV,cAAc,CAHhBU,SAAS;MACTC,eAAe,GAEbX,cAAc,CAFhBW,eAAe;MACfC,aAAA,GACEZ,cAAc,CADhBY,aAAA;IAEF,IAAIX,QAAQ,IAAIa,KAAK,CAACC,SAAS,CAACC,QAAQ,CAAC,UAAU,CAAC,EAAE;MACpDf,QAAQ,CAACa,KAAK,EAAEV,OAAO,CAAC;IAC1B,CAAC,MAAM,IAAIG,WAAW,IAAIO,KAAK,CAACC,SAAS,CAACC,QAAQ,CAAC,mBAAmB,CAAC,EAAE;MACvET,WAAW,CAACO,KAAK,EAAEV,OAAO,CAAC;IAC7B,CAAC,MAAM,IAAIM,SAAS,IAAII,KAAK,CAACC,SAAS,CAACC,QAAQ,CAAC,iBAAiB,CAAC,EAAE;MACnEN,SAAS,CAACI,KAAK,EAAEV,OAAO,CAAC;IAC3B,CAAC,MAAM,IACLO,eAAe,IACfG,KAAK,CAACC,SAAS,CAACC,QAAQ,CAAC,uBAAuB,CAAC,EACjD;MACAL,eAAe,CAACG,KAAK,EAAEV,OAAO,CAAC;IACjC,CAAC,MAAM,IACLQ,aAAa,IACbE,KAAK,CAACC,SAAS,CAACC,QAAQ,CAAC,qBAAqB,CAAC,EAC/C;MACAJ,aAAa,CAACE,KAAK,EAAEV,OAAO,CAAC;IAC/B;EACF,CAAC,EACDE,kBAAkB,EAClBD,oBAGF,CAAC;AACH", "ignoreList": []}