{"version": 3, "names": ["Object", "defineProperty", "exports", "value", "scrollTo", "_PlatformChecker", "require", "_dispatchCommand", "_index", "scrollToFabric", "animatedRef", "x", "y", "animated", "dispatchCommand", "scrollToPaper", "_WORKLET", "viewTag", "global", "_scrollToPaper", "scrollToJest", "logger", "warn", "scrollToChromeDebugger", "scrollToDefault", "shouldBeUseWeb", "isF<PERSON><PERSON>", "isJest", "isChromeDebugger"], "sources": ["../../../src/platformFunctions/scrollTo.ts"], "sourcesContent": [null], "mappings": "AAAA,YAAY;;AAAAA,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,KAAA;AAAA;AAAAD,OAAA,CAAAE,QAAA;AACZ,IAAAC,gBAAA,GAAAC,OAAA;AAMA,IAAAC,gBAAA,GAAAD,OAAA;AAOA,IAAAE,MAAA,GAAAF,OAAA;AAoBO,IAAIF,QAAkB;AAE7B,SAASK,cAAcA,CACrBC,WAA8C,EAC9CC,CAAS,EACTC,CAAS,EACTC,QAAiB,EACjB;EACA,SAAS;;EACT,IAAAC,gCAAe,EAEbJ,WAAW,EACX,UAAU,EACV,CAACC,CAAC,EAAEC,CAAC,EAAEC,QAAQ,CACjB,CAAC;AACH;AAEA,SAASE,aAAaA,CACpBL,WAA8C,EAC9CC,CAAS,EACTC,CAAS,EACTC,QAAiB,EACjB;EACA,SAAS;;EACT,IAAI,CAACG,QAAQ,EAAE;IACb;EACF;EAEA,IAAMC,OAAO,GAAGP,WAAW,CAAC,CAAW;EACvCQ,MAAM,CAACC,cAAc,CAAEF,OAAO,EAAEN,CAAC,EAAEC,CAAC,EAAEC,QAAQ,CAAC;AACjD;AAEA,SAASO,YAAYA,CAAA,EAAG;EACtBC,aAAM,CAACC,IAAI,CAAC,wCAAwC,CAAC;AACvD;AAEA,SAASC,sBAAsBA,CAAA,EAAG;EAChCF,aAAM,CAACC,IAAI,CAAC,mDAAmD,CAAC;AAClE;AAEA,SAASE,eAAeA,CAAA,EAAG;EACzBH,aAAM,CAACC,IAAI,CAAC,oDAAoD,CAAC;AACnE;AAEA,IAAI,CAAC,IAAAG,+BAAc,EAAC,CAAC,EAAE;EAIrB,IAAI,IAAAC,yBAAQ,EAAC,CAAC,EAAE;IACdxB,OAAA,CAAAE,QAAA,GAAAA,QAAQ,GAAGK,cAAqC;EAClD,CAAC,MAAM;IACLP,OAAA,CAAAE,QAAA,GAAAA,QAAQ,GAAGW,aAAoC;EACjD;AACF,CAAC,MAAM,IAAI,IAAAY,uBAAM,EAAC,CAAC,EAAE;EACnBzB,OAAA,CAAAE,QAAA,GAAAA,QAAQ,GAAGgB,YAAY;AACzB,CAAC,MAAM,IAAI,IAAAQ,iCAAgB,EAAC,CAAC,EAAE;EAC7B1B,OAAA,CAAAE,QAAA,GAAAA,QAAQ,GAAGmB,sBAAsB;AACnC,CAAC,MAAM;EACLrB,OAAA,CAAAE,QAAA,GAAAA,QAAQ,GAAGoB,eAAe;AAC5B", "ignoreList": []}