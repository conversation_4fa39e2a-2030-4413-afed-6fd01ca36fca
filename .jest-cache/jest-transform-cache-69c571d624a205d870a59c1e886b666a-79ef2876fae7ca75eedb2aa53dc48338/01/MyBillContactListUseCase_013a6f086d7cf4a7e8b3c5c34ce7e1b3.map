{"version": 3, "names": ["cov_rbjge26du", "actualCoverage", "ExcecutionHandler_1", "s", "require", "MyBillContactListUseCase", "f", "repository", "_classCallCheck2", "default", "_createClass2", "key", "value", "_execute", "_asyncToGenerator2", "_this", "ExecutionHandler", "execute", "myBillContactList", "apply", "arguments", "exports"], "sources": ["/Users/<USER>/projects/react-native/payment-module/src/domain/usecases/bill-contact/MyBillContactListUseCase.ts"], "sourcesContent": ["import {IBillContactRepository} from '../../repositories/IBillContactRepository';\nimport {MyBillContactListModel} from '../../entities/my-bill-contact-list/MyBillContactListModel';\nimport {ResultState} from '../../../core/ResultState';\nimport {ExecutionHandler} from '../../../utils/ExcecutionHandler';\nexport class MyBillContactListUseCase {\n  private repository: IBillContactRepository;\n\n  constructor(repository: IBillContactRepository) {\n    this.repository = repository;\n  }\n\n  public async execute(): Promise<ResultState<MyBillContactListModel>> {\n    // call this.repository.myBillContactList(...)\n    return ExecutionHandler.execute(() => this.repository.myBillContactList());\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IASE;IAAAA,aAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,aAAA;;;;;;;;;;;;;;;;;;;;;AANF,IAAAE,mBAAA;AAAA;AAAA,CAAAF,aAAA,GAAAG,CAAA,OAAAC,OAAA;AAAkE,IACrDC,wBAAwB;AAAA;AAAA,CAAAL,aAAA,GAAAG,CAAA;EAAA;EAAAH,aAAA,GAAAM,CAAA;EAGnC,SAAAD,yBAAYE,UAAkC;IAAA;IAAAP,aAAA,GAAAM,CAAA;IAAAN,aAAA,GAAAG,CAAA;IAAA,IAAAK,gBAAA,CAAAC,OAAA,QAAAJ,wBAAA;IAAA;IAAAL,aAAA,GAAAG,CAAA;IAC5C,IAAI,CAACI,UAAU,GAAGA,UAAU;EAC9B;EAAA;EAAAP,aAAA,GAAAG,CAAA;EAAC,WAAAO,aAAA,CAAAD,OAAA,EAAAJ,wBAAA;IAAAM,GAAA;IAAAC,KAAA;MAAA;MAAAZ,aAAA,GAAAM,CAAA;MAAA,IAAAO,QAAA;MAAA;MAAA,CAAAb,aAAA,GAAAG,CAAA,YAAAW,kBAAA,CAAAL,OAAA,EAEM,aAAa;QAAA;QAAAT,aAAA,GAAAM,CAAA;QAAA,IAAAS,KAAA;QAAA;QAAA,CAAAf,aAAA,GAAAG,CAAA;QAAA;QAAAH,aAAA,GAAAG,CAAA;QAElB,OAAOD,mBAAA,CAAAc,gBAAgB,CAACC,OAAO,CAAC;UAAA;UAAAjB,aAAA,GAAAM,CAAA;UAAAN,aAAA,GAAAG,CAAA;UAAA,OAAMY,KAAI,CAACR,UAAU,CAACW,iBAAiB,EAAE;QAAA,EAAC;MAC5E,CAAC;MAAA,SAHYD,OAAOA,CAAA;QAAA;QAAAjB,aAAA,GAAAM,CAAA;QAAAN,aAAA,GAAAG,CAAA;QAAA,OAAAU,QAAA,CAAAM,KAAA,OAAAC,SAAA;MAAA;MAAA;MAAApB,aAAA,GAAAG,CAAA;MAAA,OAAPc,OAAO;IAAA;EAAA;AAAA;AAAA;AAAAjB,aAAA,GAAAG,CAAA;AAPtBkB,OAAA,CAAAhB,wBAAA,GAAAA,wBAAA", "ignoreList": []}