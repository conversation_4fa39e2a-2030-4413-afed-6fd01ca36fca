1242c4cb9924e64f30ee3ecd027f0c43
"use strict";

/* istanbul ignore next */
function cov_rbjge26du() {
  var path = "/Users/<USER>/projects/react-native/payment-module/src/domain/usecases/bill-contact/MyBillContactListUseCase.ts";
  var hash = "5e4cda2f156a488a83947f704317b154304d9d4a";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/projects/react-native/payment-module/src/domain/usecases/bill-contact/MyBillContactListUseCase.ts",
    statementMap: {
      "0": {
        start: {
          line: 3,
          column: 29
        },
        end: {
          line: 3,
          column: 84
        }
      },
      "1": {
        start: {
          line: 4,
          column: 25
        },
        end: {
          line: 4,
          column: 99
        }
      },
      "2": {
        start: {
          line: 5,
          column: 23
        },
        end: {
          line: 5,
          column: 95
        }
      },
      "3": {
        start: {
          line: 6,
          column: 20
        },
        end: {
          line: 6,
          column: 89
        }
      },
      "4": {
        start: {
          line: 7,
          column: 0
        },
        end: {
          line: 9,
          column: 3
        }
      },
      "5": {
        start: {
          line: 10,
          column: 0
        },
        end: {
          line: 10,
          column: 42
        }
      },
      "6": {
        start: {
          line: 11,
          column: 26
        },
        end: {
          line: 11,
          column: 69
        }
      },
      "7": {
        start: {
          line: 12,
          column: 31
        },
        end: {
          line: 32,
          column: 3
        }
      },
      "8": {
        start: {
          line: 14,
          column: 4
        },
        end: {
          line: 14,
          column: 66
        }
      },
      "9": {
        start: {
          line: 15,
          column: 4
        },
        end: {
          line: 15,
          column: 33
        }
      },
      "10": {
        start: {
          line: 17,
          column: 2
        },
        end: {
          line: 31,
          column: 6
        }
      },
      "11": {
        start: {
          line: 20,
          column: 21
        },
        end: {
          line: 25,
          column: 8
        }
      },
      "12": {
        start: {
          line: 21,
          column: 20
        },
        end: {
          line: 21,
          column: 24
        }
      },
      "13": {
        start: {
          line: 22,
          column: 8
        },
        end: {
          line: 24,
          column: 11
        }
      },
      "14": {
        start: {
          line: 23,
          column: 10
        },
        end: {
          line: 23,
          column: 54
        }
      },
      "15": {
        start: {
          line: 27,
          column: 8
        },
        end: {
          line: 27,
          column: 47
        }
      },
      "16": {
        start: {
          line: 29,
          column: 6
        },
        end: {
          line: 29,
          column: 21
        }
      },
      "17": {
        start: {
          line: 33,
          column: 0
        },
        end: {
          line: 33,
          column: 60
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 12,
            column: 31
          },
          end: {
            line: 12,
            column: 32
          }
        },
        loc: {
          start: {
            line: 12,
            column: 43
          },
          end: {
            line: 32,
            column: 1
          }
        },
        line: 12
      },
      "1": {
        name: "MyBillContactListUseCase",
        decl: {
          start: {
            line: 13,
            column: 11
          },
          end: {
            line: 13,
            column: 35
          }
        },
        loc: {
          start: {
            line: 13,
            column: 48
          },
          end: {
            line: 16,
            column: 3
          }
        },
        line: 13
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 19,
            column: 11
          },
          end: {
            line: 19,
            column: 12
          }
        },
        loc: {
          start: {
            line: 19,
            column: 23
          },
          end: {
            line: 30,
            column: 5
          }
        },
        line: 19
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 20,
            column: 53
          },
          end: {
            line: 20,
            column: 54
          }
        },
        loc: {
          start: {
            line: 20,
            column: 66
          },
          end: {
            line: 25,
            column: 7
          }
        },
        line: 20
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 22,
            column: 60
          },
          end: {
            line: 22,
            column: 61
          }
        },
        loc: {
          start: {
            line: 22,
            column: 72
          },
          end: {
            line: 24,
            column: 9
          }
        },
        line: 22
      },
      "5": {
        name: "execute",
        decl: {
          start: {
            line: 26,
            column: 15
          },
          end: {
            line: 26,
            column: 22
          }
        },
        loc: {
          start: {
            line: 26,
            column: 25
          },
          end: {
            line: 28,
            column: 7
          }
        },
        line: 26
      }
    },
    branchMap: {},
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0
    },
    b: {},
    inputSourceMap: {
      version: 3,
      file: undefined,
      names: ["ExcecutionHandler_1", "require", "MyBillContactListUseCase", "repository", "_classCallCheck2", "default", "_createClass2", "key", "value", "_execute", "_asyncToGenerator2", "_this", "ExecutionHandler", "execute", "myBillContactList", "apply", "arguments", "exports"],
      sourceRoot: undefined,
      sources: ["/Users/<USER>/projects/react-native/payment-module/src/domain/usecases/bill-contact/MyBillContactListUseCase.ts"],
      sourcesContent: ["import {IBillContactRepository} from '../../repositories/IBillContactRepository';\nimport {MyBillContactListModel} from '../../entities/my-bill-contact-list/MyBillContactListModel';\nimport {ResultState} from '../../../core/ResultState';\nimport {ExecutionHandler} from '../../../utils/ExcecutionHandler';\nexport class MyBillContactListUseCase {\n  private repository: IBillContactRepository;\n\n  constructor(repository: IBillContactRepository) {\n    this.repository = repository;\n  }\n\n  public async execute(): Promise<ResultState<MyBillContactListModel>> {\n    // call this.repository.myBillContactList(...)\n    return ExecutionHandler.execute(() => this.repository.myBillContactList());\n  }\n}\n"],
      mappings: ";;;;;;;;;;AAGA,IAAAA,mBAAA,GAAAC,OAAA;AAAkE,IACrDC,wBAAwB;EAGnC,SAAAA,yBAAYC,UAAkC;IAAA,IAAAC,gBAAA,CAAAC,OAAA,QAAAH,wBAAA;IAC5C,IAAI,CAACC,UAAU,GAAGA,UAAU;EAC9B;EAAC,WAAAG,aAAA,CAAAD,OAAA,EAAAH,wBAAA;IAAAK,GAAA;IAAAC,KAAA;MAAA,IAAAC,QAAA,OAAAC,kBAAA,CAAAL,OAAA,EAEM,aAAa;QAAA,IAAAM,KAAA;QAElB,OAAOX,mBAAA,CAAAY,gBAAgB,CAACC,OAAO,CAAC;UAAA,OAAMF,KAAI,CAACR,UAAU,CAACW,iBAAiB,EAAE;QAAA,EAAC;MAC5E,CAAC;MAAA,SAHYD,OAAOA,CAAA;QAAA,OAAAJ,QAAA,CAAAM,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAPH,OAAO;IAAA;EAAA;AAAA;AAPtBI,OAAA,CAAAf,wBAAA,GAAAA,wBAAA",
      ignoreList: []
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "5e4cda2f156a488a83947f704317b154304d9d4a"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_rbjge26du = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_rbjge26du();
var _interopRequireDefault =
/* istanbul ignore next */
(cov_rbjge26du().s[0]++, require("@babel/runtime/helpers/interopRequireDefault"));
var _asyncToGenerator2 =
/* istanbul ignore next */
(cov_rbjge26du().s[1]++, _interopRequireDefault(require("@babel/runtime/helpers/asyncToGenerator")));
var _classCallCheck2 =
/* istanbul ignore next */
(cov_rbjge26du().s[2]++, _interopRequireDefault(require("@babel/runtime/helpers/classCallCheck")));
var _createClass2 =
/* istanbul ignore next */
(cov_rbjge26du().s[3]++, _interopRequireDefault(require("@babel/runtime/helpers/createClass")));
/* istanbul ignore next */
cov_rbjge26du().s[4]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_rbjge26du().s[5]++;
exports.MyBillContactListUseCase = void 0;
var ExcecutionHandler_1 =
/* istanbul ignore next */
(cov_rbjge26du().s[6]++, require("../../../utils/ExcecutionHandler"));
var MyBillContactListUseCase =
/* istanbul ignore next */
(cov_rbjge26du().s[7]++, function () {
  /* istanbul ignore next */
  cov_rbjge26du().f[0]++;
  function MyBillContactListUseCase(repository) {
    /* istanbul ignore next */
    cov_rbjge26du().f[1]++;
    cov_rbjge26du().s[8]++;
    (0, _classCallCheck2.default)(this, MyBillContactListUseCase);
    /* istanbul ignore next */
    cov_rbjge26du().s[9]++;
    this.repository = repository;
  }
  /* istanbul ignore next */
  cov_rbjge26du().s[10]++;
  return (0, _createClass2.default)(MyBillContactListUseCase, [{
    key: "execute",
    value: function () {
      /* istanbul ignore next */
      cov_rbjge26du().f[2]++;
      var _execute =
      /* istanbul ignore next */
      (cov_rbjge26du().s[11]++, (0, _asyncToGenerator2.default)(function* () {
        /* istanbul ignore next */
        cov_rbjge26du().f[3]++;
        var _this =
        /* istanbul ignore next */
        (cov_rbjge26du().s[12]++, this);
        /* istanbul ignore next */
        cov_rbjge26du().s[13]++;
        return ExcecutionHandler_1.ExecutionHandler.execute(function () {
          /* istanbul ignore next */
          cov_rbjge26du().f[4]++;
          cov_rbjge26du().s[14]++;
          return _this.repository.myBillContactList();
        });
      }));
      function execute() {
        /* istanbul ignore next */
        cov_rbjge26du().f[5]++;
        cov_rbjge26du().s[15]++;
        return _execute.apply(this, arguments);
      }
      /* istanbul ignore next */
      cov_rbjge26du().s[16]++;
      return execute;
    }()
  }]);
}());
/* istanbul ignore next */
cov_rbjge26du().s[17]++;
exports.MyBillContactListUseCase = MyBillContactListUseCase;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJjb3ZfcmJqZ2UyNmR1IiwiYWN0dWFsQ292ZXJhZ2UiLCJFeGNlY3V0aW9uSGFuZGxlcl8xIiwicyIsInJlcXVpcmUiLCJNeUJpbGxDb250YWN0TGlzdFVzZUNhc2UiLCJmIiwicmVwb3NpdG9yeSIsIl9jbGFzc0NhbGxDaGVjazIiLCJkZWZhdWx0IiwiX2NyZWF0ZUNsYXNzMiIsImtleSIsInZhbHVlIiwiX2V4ZWN1dGUiLCJfYXN5bmNUb0dlbmVyYXRvcjIiLCJfdGhpcyIsIkV4ZWN1dGlvbkhhbmRsZXIiLCJleGVjdXRlIiwibXlCaWxsQ29udGFjdExpc3QiLCJhcHBseSIsImFyZ3VtZW50cyIsImV4cG9ydHMiXSwic291cmNlcyI6WyIvVXNlcnMvc29uc2Ftc2V0L3Byb2plY3RzL3JlYWN0LW5hdGl2ZS9wYXltZW50LW1vZHVsZS9zcmMvZG9tYWluL3VzZWNhc2VzL2JpbGwtY29udGFjdC9NeUJpbGxDb250YWN0TGlzdFVzZUNhc2UudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHtJQmlsbENvbnRhY3RSZXBvc2l0b3J5fSBmcm9tICcuLi8uLi9yZXBvc2l0b3JpZXMvSUJpbGxDb250YWN0UmVwb3NpdG9yeSc7XG5pbXBvcnQge015QmlsbENvbnRhY3RMaXN0TW9kZWx9IGZyb20gJy4uLy4uL2VudGl0aWVzL215LWJpbGwtY29udGFjdC1saXN0L015QmlsbENvbnRhY3RMaXN0TW9kZWwnO1xuaW1wb3J0IHtSZXN1bHRTdGF0ZX0gZnJvbSAnLi4vLi4vLi4vY29yZS9SZXN1bHRTdGF0ZSc7XG5pbXBvcnQge0V4ZWN1dGlvbkhhbmRsZXJ9IGZyb20gJy4uLy4uLy4uL3V0aWxzL0V4Y2VjdXRpb25IYW5kbGVyJztcbmV4cG9ydCBjbGFzcyBNeUJpbGxDb250YWN0TGlzdFVzZUNhc2Uge1xuICBwcml2YXRlIHJlcG9zaXRvcnk6IElCaWxsQ29udGFjdFJlcG9zaXRvcnk7XG5cbiAgY29uc3RydWN0b3IocmVwb3NpdG9yeTogSUJpbGxDb250YWN0UmVwb3NpdG9yeSkge1xuICAgIHRoaXMucmVwb3NpdG9yeSA9IHJlcG9zaXRvcnk7XG4gIH1cblxuICBwdWJsaWMgYXN5bmMgZXhlY3V0ZSgpOiBQcm9taXNlPFJlc3VsdFN0YXRlPE15QmlsbENvbnRhY3RMaXN0TW9kZWw+PiB7XG4gICAgLy8gY2FsbCB0aGlzLnJlcG9zaXRvcnkubXlCaWxsQ29udGFjdExpc3QoLi4uKVxuICAgIHJldHVybiBFeGVjdXRpb25IYW5kbGVyLmV4ZWN1dGUoKCkgPT4gdGhpcy5yZXBvc2l0b3J5Lm15QmlsbENvbnRhY3RMaXN0KCkpO1xuICB9XG59XG4iXSwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztJQVNFO0lBQUFBLGFBQUEsWUFBQUEsQ0FBQTtNQUFBLE9BQUFDLGNBQUE7SUFBQTtFQUFBO0VBQUEsT0FBQUEsY0FBQTtBQUFBO0FBQUFELGFBQUE7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQU5GLElBQUFFLG1CQUFBO0FBQUE7QUFBQSxDQUFBRixhQUFBLEdBQUFHLENBQUEsT0FBQUMsT0FBQTtBQUFrRSxJQUNyREMsd0JBQXdCO0FBQUE7QUFBQSxDQUFBTCxhQUFBLEdBQUFHLENBQUE7RUFBQTtFQUFBSCxhQUFBLEdBQUFNLENBQUE7RUFHbkMsU0FBQUQseUJBQVlFLFVBQWtDO0lBQUE7SUFBQVAsYUFBQSxHQUFBTSxDQUFBO0lBQUFOLGFBQUEsR0FBQUcsQ0FBQTtJQUFBLElBQUFLLGdCQUFBLENBQUFDLE9BQUEsUUFBQUosd0JBQUE7SUFBQTtJQUFBTCxhQUFBLEdBQUFHLENBQUE7SUFDNUMsSUFBSSxDQUFDSSxVQUFVLEdBQUdBLFVBQVU7RUFDOUI7RUFBQTtFQUFBUCxhQUFBLEdBQUFHLENBQUE7RUFBQyxXQUFBTyxhQUFBLENBQUFELE9BQUEsRUFBQUosd0JBQUE7SUFBQU0sR0FBQTtJQUFBQyxLQUFBO01BQUE7TUFBQVosYUFBQSxHQUFBTSxDQUFBO01BQUEsSUFBQU8sUUFBQTtNQUFBO01BQUEsQ0FBQWIsYUFBQSxHQUFBRyxDQUFBLFlBQUFXLGtCQUFBLENBQUFMLE9BQUEsRUFFTSxhQUFhO1FBQUE7UUFBQVQsYUFBQSxHQUFBTSxDQUFBO1FBQUEsSUFBQVMsS0FBQTtRQUFBO1FBQUEsQ0FBQWYsYUFBQSxHQUFBRyxDQUFBO1FBQUE7UUFBQUgsYUFBQSxHQUFBRyxDQUFBO1FBRWxCLE9BQU9ELG1CQUFBLENBQUFjLGdCQUFnQixDQUFDQyxPQUFPLENBQUM7VUFBQTtVQUFBakIsYUFBQSxHQUFBTSxDQUFBO1VBQUFOLGFBQUEsR0FBQUcsQ0FBQTtVQUFBLE9BQU1ZLEtBQUksQ0FBQ1IsVUFBVSxDQUFDVyxpQkFBaUIsRUFBRTtRQUFBLEVBQUM7TUFDNUUsQ0FBQztNQUFBLFNBSFlELE9BQU9BLENBQUE7UUFBQTtRQUFBakIsYUFBQSxHQUFBTSxDQUFBO1FBQUFOLGFBQUEsR0FBQUcsQ0FBQTtRQUFBLE9BQUFVLFFBQUEsQ0FBQU0sS0FBQSxPQUFBQyxTQUFBO01BQUE7TUFBQTtNQUFBcEIsYUFBQSxHQUFBRyxDQUFBO01BQUEsT0FBUGMsT0FBTztJQUFBO0VBQUE7QUFBQTtBQUFBO0FBQUFqQixhQUFBLEdBQUFHLENBQUE7QUFQdEJrQixPQUFBLENBQUFoQix3QkFBQSxHQUFBQSx3QkFBQSIsImlnbm9yZUxpc3QiOltdfQ==