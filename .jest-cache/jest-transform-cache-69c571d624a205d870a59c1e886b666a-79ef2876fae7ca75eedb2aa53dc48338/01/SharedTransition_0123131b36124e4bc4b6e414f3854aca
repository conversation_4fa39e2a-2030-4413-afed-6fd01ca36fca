2a37593785b2217470f6059010ac01ee
'use strict';

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.SharedTransition = void 0;
var _classCallCheck2 = _interopRequireDefault(require("@babel/runtime/helpers/classCallCheck"));
var _createClass2 = _interopRequireDefault(require("@babel/runtime/helpers/createClass"));
var _index = require("../../animation/index.js");
var _commonTypes = require("../animationBuilder/commonTypes.js");
var _commonTypes2 = require("../../commonTypes.js");
var _ProgressTransitionManager = require("./ProgressTransitionManager.js");
var _UpdateLayoutAnimations = require("../../UpdateLayoutAnimations.js");
var _util = require("../../animation/util.js");
var _errors = require("../../errors.js");
var SUPPORTED_PROPS = ['width', 'height', 'originX', 'originY', 'transform', 'borderRadius', 'borderTopLeftRadius', 'borderTopRightRadius', 'borderBottomLeftRadius', 'borderBottomRightRadius'];
var SharedTransition = exports.SharedTransition = function () {
  function SharedTransition() {
    (0, _classCallCheck2.default)(this, SharedTransition);
    this._customAnimationFactory = null;
    this._animation = null;
    this._transitionDuration = 500;
    this._reduceMotion = _commonTypes2.ReduceMotion.System;
    this._customProgressAnimation = undefined;
    this._progressAnimation = undefined;
    this._defaultTransitionType = undefined;
  }
  return (0, _createClass2.default)(SharedTransition, [{
    key: "custom",
    value: function custom(customAnimationFactory) {
      this._customAnimationFactory = customAnimationFactory;
      return this;
    }
  }, {
    key: "progressAnimation",
    value: function progressAnimation(progressAnimationCallback) {
      this._customProgressAnimation = function (viewTag, values, progress) {
        'worklet';

        var newStyles = progressAnimationCallback(values, progress);
        global._notifyAboutProgress(viewTag, newStyles, true);
      };
      return this;
    }
  }, {
    key: "duration",
    value: function duration(_duration) {
      this._transitionDuration = _duration;
      return this;
    }
  }, {
    key: "reduceMotion",
    value: function reduceMotion(_reduceMotion) {
      this._reduceMotion = _reduceMotion;
      return this;
    }
  }, {
    key: "defaultTransitionType",
    value: function defaultTransitionType(transitionType) {
      this._defaultTransitionType = transitionType;
      return this;
    }
  }, {
    key: "registerTransition",
    value: function registerTransition(viewTag, sharedTransitionTag) {
      var isUnmounting = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;
      if ((0, _util.getReduceMotionFromConfig)(this.getReduceMotion())) {
        return;
      }
      var transitionAnimation = this.getTransitionAnimation();
      var progressAnimation = this.getProgressAnimation();
      if (!this._defaultTransitionType) {
        if (this._customAnimationFactory && !this._customProgressAnimation) {
          this._defaultTransitionType = _commonTypes.SharedTransitionType.ANIMATION;
        } else {
          this._defaultTransitionType = _commonTypes.SharedTransitionType.PROGRESS_ANIMATION;
        }
      }
      var layoutAnimationType = this._defaultTransitionType === _commonTypes.SharedTransitionType.ANIMATION ? _commonTypes.LayoutAnimationType.SHARED_ELEMENT_TRANSITION : _commonTypes.LayoutAnimationType.SHARED_ELEMENT_TRANSITION_PROGRESS;
      (0, _UpdateLayoutAnimations.updateLayoutAnimations)(viewTag, layoutAnimationType, transitionAnimation, sharedTransitionTag, isUnmounting);
      SharedTransition._progressTransitionManager.addProgressAnimation(viewTag, progressAnimation);
    }
  }, {
    key: "unregisterTransition",
    value: function unregisterTransition(viewTag) {
      var isUnmounting = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;
      var layoutAnimationType = this._defaultTransitionType === _commonTypes.SharedTransitionType.ANIMATION ? _commonTypes.LayoutAnimationType.SHARED_ELEMENT_TRANSITION : _commonTypes.LayoutAnimationType.SHARED_ELEMENT_TRANSITION_PROGRESS;
      (0, _UpdateLayoutAnimations.updateLayoutAnimations)(viewTag, layoutAnimationType, undefined, undefined, isUnmounting);
      SharedTransition._progressTransitionManager.removeProgressAnimation(viewTag, isUnmounting);
    }
  }, {
    key: "getReduceMotion",
    value: function getReduceMotion() {
      return this._reduceMotion;
    }
  }, {
    key: "getTransitionAnimation",
    value: function getTransitionAnimation() {
      if (!this._animation) {
        this.buildAnimation();
      }
      return this._animation;
    }
  }, {
    key: "getProgressAnimation",
    value: function getProgressAnimation() {
      if (!this._progressAnimation) {
        this.buildProgressAnimation();
      }
      return this._progressAnimation;
    }
  }, {
    key: "buildAnimation",
    value: function buildAnimation() {
      var animationFactory = this._customAnimationFactory;
      var transitionDuration = this._transitionDuration;
      var reduceMotion = this._reduceMotion;
      this._animation = function (values) {
        'worklet';

        var animations = {};
        var initialValues = {};
        if (animationFactory) {
          animations = animationFactory(values);
          for (var key in animations) {
            if (!SUPPORTED_PROPS.includes(key)) {
              throw new _errors.ReanimatedError(`The prop '${key}' is not supported yet.`);
            }
          }
        } else {
          for (var propName of SUPPORTED_PROPS) {
            if (propName === 'transform') {
              var matrix = values.targetTransformMatrix;
              animations.transformMatrix = (0, _index.withTiming)(matrix, {
                reduceMotion: reduceMotion,
                duration: transitionDuration
              });
            } else {
              var capitalizedPropName = `${propName.charAt(0).toUpperCase()}${propName.slice(1)}`;
              var keyToTargetValue = `target${capitalizedPropName}`;
              animations[propName] = (0, _index.withTiming)(values[keyToTargetValue], {
                reduceMotion: reduceMotion,
                duration: transitionDuration
              });
            }
          }
        }
        for (var _propName in animations) {
          if (_propName === 'transform') {
            initialValues.transformMatrix = values.currentTransformMatrix;
          } else {
            var _capitalizedPropName = _propName.charAt(0).toUpperCase() + _propName.slice(1);
            var keyToCurrentValue = `current${_capitalizedPropName}`;
            initialValues[_propName] = values[keyToCurrentValue];
          }
        }
        return {
          initialValues: initialValues,
          animations: animations
        };
      };
    }
  }, {
    key: "buildProgressAnimation",
    value: function buildProgressAnimation() {
      if (this._customProgressAnimation) {
        this._progressAnimation = this._customProgressAnimation;
        return;
      }
      this._progressAnimation = function (viewTag, values, progress) {
        'worklet';

        var newStyles = {};
        for (var propertyName of SUPPORTED_PROPS) {
          if (propertyName === 'transform') {
            var currentMatrix = values.currentTransformMatrix;
            var targetMatrix = values.targetTransformMatrix;
            var newMatrix = new Array(9);
            for (var i = 0; i < 9; i++) {
              newMatrix[i] = progress * (targetMatrix[i] - currentMatrix[i]) + currentMatrix[i];
            }
            newStyles.transformMatrix = newMatrix;
          } else {
            var PropertyName = propertyName.charAt(0).toUpperCase() + propertyName.slice(1);
            var currentPropertyName = `current${PropertyName}`;
            var targetPropertyName = `target${PropertyName}`;
            var currentValue = values[currentPropertyName];
            var targetValue = values[targetPropertyName];
            newStyles[propertyName] = progress * (targetValue - currentValue) + currentValue;
          }
        }
        global._notifyAboutProgress(viewTag, newStyles, true);
      };
    }
  }], [{
    key: "custom",
    value: function custom(customAnimationFactory) {
      return new SharedTransition().custom(customAnimationFactory);
    }
  }, {
    key: "duration",
    value: function duration(_duration2) {
      return new SharedTransition().duration(_duration2);
    }
  }, {
    key: "progressAnimation",
    value: function progressAnimation(progressAnimationCallback) {
      return new SharedTransition().progressAnimation(progressAnimationCallback);
    }
  }, {
    key: "defaultTransitionType",
    value: function defaultTransitionType(transitionType) {
      return new SharedTransition().defaultTransitionType(transitionType);
    }
  }, {
    key: "reduceMotion",
    value: function reduceMotion(_reduceMotion2) {
      return new SharedTransition().reduceMotion(_reduceMotion2);
    }
  }]);
}();
SharedTransition._progressTransitionManager = new _ProgressTransitionManager.ProgressTransitionManager();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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