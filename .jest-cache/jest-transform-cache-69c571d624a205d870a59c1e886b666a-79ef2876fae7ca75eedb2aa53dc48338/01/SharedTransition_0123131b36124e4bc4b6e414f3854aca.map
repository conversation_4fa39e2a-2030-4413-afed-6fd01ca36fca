{"version": 3, "names": ["_interopRequireDefault", "require", "Object", "defineProperty", "exports", "value", "SharedTransition", "_classCallCheck2", "_createClass2", "_index", "_commonTypes", "_commonTypes2", "_ProgressTransitionManager", "_UpdateLayoutAnimations", "_util", "_errors", "SUPPORTED_PROPS", "default", "_customAnimationFactory", "_animation", "_transitionDuration", "_reduceMotion", "ReduceMotion", "System", "_customProgressAnimation", "undefined", "_progressAnimation", "_defaultTransitionType", "key", "custom", "customAnimationFactory", "progressAnimation", "progressAnimationCallback", "viewTag", "values", "progress", "newStyles", "global", "_notifyAboutProgress", "duration", "reduceMotion", "defaultTransitionType", "transitionType", "registerTransition", "sharedTransitionTag", "isUnmounting", "arguments", "length", "getReduceMotionFromConfig", "getReduceMotion", "transitionAnimation", "getTransitionAnimation", "getProgressAnimation", "SharedTransitionType", "ANIMATION", "PROGRESS_ANIMATION", "layoutAnimationType", "LayoutAnimationType", "SHARED_ELEMENT_TRANSITION", "SHARED_ELEMENT_TRANSITION_PROGRESS", "updateLayoutAnimations", "_progressTransitionManager", "addProgressAnimation", "unregisterTransition", "removeProgressAnimation", "buildAnimation", "buildProgressAnimation", "animationFactory", "transitionDuration", "animations", "initialValues", "includes", "ReanimatedError", "propName", "matrix", "targetTransformMatrix", "transformMatrix", "withTiming", "capitalizedPropName", "char<PERSON>t", "toUpperCase", "slice", "keyToTargetValue", "currentTransformMatrix", "keyToCurrentValue", "propertyName", "currentMatrix", "targetMatrix", "newMatrix", "Array", "i", "PropertyName", "currentPropertyName", "targetPropertyName", "currentValue", "targetValue", "ProgressTransitionManager"], "sources": ["../../../../src/layoutReanimation/sharedTransitions/SharedTransition.ts"], "sourcesContent": [null], "mappings": "AAAA,YAAY;;AAAA,IAAAA,sBAAA,GAAAC,OAAA;AAAAC,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,KAAA;AAAA;AAAAD,OAAA,CAAAE,gBAAA;AAAA,IAAAC,gBAAA,GAAAP,sBAAA,CAAAC,OAAA;AAAA,IAAAO,aAAA,GAAAR,sBAAA,CAAAC,OAAA;AACZ,IAAAQ,MAAA,GAAAR,OAAA;AAQA,IAAAS,YAAA,GAAAT,OAAA;AAKA,IAAAU,aAAA,GAAAV,OAAA;AACA,IAAAW,0BAAA,GAAAX,OAAA;AACA,IAAAY,uBAAA,GAAAZ,OAAA;AACA,IAAAa,KAAA,GAAAb,OAAA;AACA,IAAAc,OAAA,GAAAd,OAAA;AAEA,IAAMe,eAAe,GAAG,CACtB,OAAO,EACP,QAAQ,EACR,SAAS,EACT,SAAS,EACT,WAAW,EACX,cAAc,EACd,qBAAqB,EACrB,sBAAsB,EACtB,wBAAwB,EACxB,yBAAyB,CACjB;AAAA,IAYGV,gBAAgB,GAAAF,OAAA,CAAAE,gBAAA;EAAA,SAAAA,iBAAA;IAAA,IAAAC,gBAAA,CAAAU,OAAA,QAAAX,gBAAA;IAAA,KACnBY,uBAAuB,GAA4B,IAAI;IAAA,KACvDC,UAAU,GAA8C,IAAI;IAAA,KAC5DC,mBAAmB,GAAG,GAAG;IAAA,KACzBC,aAAa,GAAiBC,0BAAY,CAACC,MAAM;IAAA,KACjDC,wBAAwB,GAAuBC,SAAS;IAAA,KACxDC,kBAAkB,GAAuBD,SAAS;IAAA,KAClDE,sBAAsB,GAA0BF,SAAS;EAAA;EAAA,WAAAjB,aAAA,CAAAS,OAAA,EAAAX,gBAAA;IAAAsB,GAAA;IAAAvB,KAAA,EAG1D,SAAAwB,MAAMA,CAACC,sBAAwC,EAAoB;MACxE,IAAI,CAACZ,uBAAuB,GAAGY,sBAAsB;MACrD,OAAO,IAAI;IACb;EAAA;IAAAF,GAAA;IAAAvB,KAAA,EAEO,SAAA0B,iBAAiBA,CACtBC,yBAAkD,EAChC;MAClB,IAAI,CAACR,wBAAwB,GAAG,UAACS,OAAO,EAAEC,MAAM,EAAEC,QAAQ,EAAK;QAC7D,SAAS;;QACT,IAAMC,SAAS,GAAGJ,yBAAyB,CAACE,MAAM,EAAEC,QAAQ,CAAC;QAC7DE,MAAM,CAACC,oBAAoB,CAACL,OAAO,EAAEG,SAAS,EAAE,IAAI,CAAC;MACvD,CAAC;MACD,OAAO,IAAI;IACb;EAAA;IAAAR,GAAA;IAAAvB,KAAA,EAEO,SAAAkC,QAAQA,CAACA,SAAgB,EAAoB;MAClD,IAAI,CAACnB,mBAAmB,GAAGmB,SAAQ;MACnC,OAAO,IAAI;IACb;EAAA;IAAAX,GAAA;IAAAvB,KAAA,EAEO,SAAAmC,YAAYA,CAACnB,aAA2B,EAAQ;MACrD,IAAI,CAACA,aAAa,GAAGA,aAAa;MAClC,OAAO,IAAI;IACb;EAAA;IAAAO,GAAA;IAAAvB,KAAA,EAEO,SAAAoC,qBAAqBA,CAC1BC,cAAoC,EAClB;MAClB,IAAI,CAACf,sBAAsB,GAAGe,cAAc;MAC5C,OAAO,IAAI;IACb;EAAA;IAAAd,GAAA;IAAAvB,KAAA,EAEO,SAAAsC,kBAAkBA,CACvBV,OAAe,EACfW,mBAA2B,EAE3B;MAAA,IADAC,YAAY,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAArB,SAAA,GAAAqB,SAAA,MAAG,KAAK;MAEpB,IAAI,IAAAE,+BAAyB,EAAC,IAAI,CAACC,eAAe,CAAC,CAAC,CAAC,EAAE;QACrD;MACF;MAEA,IAAMC,mBAAmB,GAAG,IAAI,CAACC,sBAAsB,CAAC,CAAC;MACzD,IAAMpB,iBAAiB,GAAG,IAAI,CAACqB,oBAAoB,CAAC,CAAC;MACrD,IAAI,CAAC,IAAI,CAACzB,sBAAsB,EAAE;QAChC,IAAI,IAAI,CAACT,uBAAuB,IAAI,CAAC,IAAI,CAACM,wBAAwB,EAAE;UAClE,IAAI,CAACG,sBAAsB,GAAG0B,iCAAoB,CAACC,SAAS;QAC9D,CAAC,MAAM;UACL,IAAI,CAAC3B,sBAAsB,GAAG0B,iCAAoB,CAACE,kBAAkB;QACvE;MACF;MACA,IAAMC,mBAAmB,GACvB,IAAI,CAAC7B,sBAAsB,KAAK0B,iCAAoB,CAACC,SAAS,GAC1DG,gCAAmB,CAACC,yBAAyB,GAC7CD,gCAAmB,CAACE,kCAAkC;MAC5D,IAAAC,8CAAsB,EACpB3B,OAAO,EACPuB,mBAAmB,EACnBN,mBAAmB,EACnBN,mBAAmB,EACnBC,YACF,CAAC;MACDvC,gBAAgB,CAACuD,0BAA0B,CAACC,oBAAoB,CAC9D7B,OAAO,EACPF,iBACF,CAAC;IACH;EAAA;IAAAH,GAAA;IAAAvB,KAAA,EAEO,SAAA0D,oBAAoBA,CAAC9B,OAAe,EAA8B;MAAA,IAA5BY,YAAY,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAArB,SAAA,GAAAqB,SAAA,MAAG,KAAK;MAC/D,IAAMU,mBAAmB,GACvB,IAAI,CAAC7B,sBAAsB,KAAK0B,iCAAoB,CAACC,SAAS,GAC1DG,gCAAmB,CAACC,yBAAyB,GAC7CD,gCAAmB,CAACE,kCAAkC;MAC5D,IAAAC,8CAAsB,EACpB3B,OAAO,EACPuB,mBAAmB,EACnB/B,SAAS,EACTA,SAAS,EACToB,YACF,CAAC;MACDvC,gBAAgB,CAACuD,0BAA0B,CAACG,uBAAuB,CACjE/B,OAAO,EACPY,YACF,CAAC;IACH;EAAA;IAAAjB,GAAA;IAAAvB,KAAA,EAEO,SAAA4C,eAAeA,CAAA,EAAiB;MACrC,OAAO,IAAI,CAAC5B,aAAa;IAC3B;EAAA;IAAAO,GAAA;IAAAvB,KAAA,EAEQ,SAAA8C,sBAAsBA,CAAA,EAAuC;MACnE,IAAI,CAAC,IAAI,CAAChC,UAAU,EAAE;QACpB,IAAI,CAAC8C,cAAc,CAAC,CAAC;MACvB;MACA,OAAO,IAAI,CAAC9C,UAAU;IACxB;EAAA;IAAAS,GAAA;IAAAvB,KAAA,EAEQ,SAAA+C,oBAAoBA,CAAA,EAAsB;MAChD,IAAI,CAAC,IAAI,CAAC1B,kBAAkB,EAAE;QAC5B,IAAI,CAACwC,sBAAsB,CAAC,CAAC;MAC/B;MACA,OAAO,IAAI,CAACxC,kBAAkB;IAChC;EAAA;IAAAE,GAAA;IAAAvB,KAAA,EAEQ,SAAA4D,cAAcA,CAAA,EAAG;MACvB,IAAME,gBAAgB,GAAG,IAAI,CAACjD,uBAAuB;MACrD,IAAMkD,kBAAkB,GAAG,IAAI,CAAChD,mBAAmB;MACnD,IAAMoB,YAAY,GAAG,IAAI,CAACnB,aAAa;MACvC,IAAI,CAACF,UAAU,GAAI,UAAAe,MAAwC,EAAK;QAC9D,SAAS;;QACT,IAAImC,UAEH,GAAG,CAAC,CAAC;QACN,IAAMC,aAEL,GAAG,CAAC,CAAC;QAEN,IAAIH,gBAAgB,EAAE;UACpBE,UAAU,GAAGF,gBAAgB,CAACjC,MAAM,CAAC;UACrC,KAAK,IAAMN,GAAG,IAAIyC,UAAU,EAAE;YAC5B,IAAI,CAAErD,eAAe,CAAuBuD,QAAQ,CAAC3C,GAAG,CAAC,EAAE;cACzD,MAAM,IAAI4C,uBAAe,CACvB,aAAa5C,GAAG,yBAClB,CAAC;YACH;UACF;QACF,CAAC,MAAM;UACL,KAAK,IAAM6C,QAAQ,IAAIzD,eAAe,EAAE;YACtC,IAAIyD,QAAQ,KAAK,WAAW,EAAE;cAC5B,IAAMC,MAAM,GAAGxC,MAAM,CAACyC,qBAAqB;cAC3CN,UAAU,CAACO,eAAe,GAAG,IAAAC,iBAAU,EAACH,MAAM,EAAE;gBAC9ClC,YAAY,EAAZA,YAAY;gBACZD,QAAQ,EAAE6B;cACZ,CAAC,CAAC;YACJ,CAAC,MAAM;cACL,IAAMU,mBAAmB,GAAG,GAAGL,QAAQ,CACpCM,MAAM,CAAC,CAAC,CAAC,CACTC,WAAW,CAAC,CAAC,GAAGP,QAAQ,CAACQ,KAAK,CAC/B,CACF,CAAC,EAAyC;cAC1C,IAAMC,gBAAgB,GAAG,SAASJ,mBAAmB,EAAW;cAChET,UAAU,CAACI,QAAQ,CAAC,GAAG,IAAAI,iBAAU,EAAC3C,MAAM,CAACgD,gBAAgB,CAAC,EAAE;gBAC1D1C,YAAY,EAAZA,YAAY;gBACZD,QAAQ,EAAE6B;cACZ,CAAC,CAAC;YACJ;UACF;QACF;QAEA,KAAK,IAAMK,SAAQ,IAAIJ,UAAU,EAAE;UACjC,IAAII,SAAQ,KAAK,WAAW,EAAE;YAC5BH,aAAa,CAACM,eAAe,GAAG1C,MAAM,CAACiD,sBAAsB;UAC/D,CAAC,MAAM;YACL,IAAML,oBAAmB,GAAIL,SAAQ,CAACM,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAC3DP,SAAQ,CAACQ,KAAK,CAAC,CAAC,CAAyC;YAC3D,IAAMG,iBAAiB,GAAG,UAAUN,oBAAmB,EAAW;YAClER,aAAa,CAACG,SAAQ,CAAC,GAAGvC,MAAM,CAACkD,iBAAiB,CAAC;UACrD;QACF;QAEA,OAAO;UAAEd,aAAa,EAAbA,aAAa;UAAED,UAAA,EAAAA;QAAW,CAAC;MACtC,CAAC;IACH;EAAA;IAAAzC,GAAA;IAAAvB,KAAA,EAEQ,SAAA6D,sBAAsBA,CAAA,EAAG;MAC/B,IAAI,IAAI,CAAC1C,wBAAwB,EAAE;QACjC,IAAI,CAACE,kBAAkB,GAAG,IAAI,CAACF,wBAAwB;QACvD;MACF;MACA,IAAI,CAACE,kBAAkB,GAAG,UAACO,OAAO,EAAEC,MAAM,EAAEC,QAAQ,EAAK;QACvD,SAAS;;QACT,IAAMC,SAA+C,GAAG,CAAC,CAAC;QAC1D,KAAK,IAAMiD,YAAY,IAAIrE,eAAe,EAAE;UAC1C,IAAIqE,YAAY,KAAK,WAAW,EAAE;YAGhC,IAAMC,aAAa,GAAGpD,MAAM,CAACiD,sBAAsB;YACnD,IAAMI,YAAY,GAAGrD,MAAM,CAACyC,qBAAqB;YACjD,IAAMa,SAAS,GAAG,IAAIC,KAAK,CAAC,CAAC,CAAC;YAC9B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;cAC1BF,SAAS,CAACE,CAAC,CAAC,GACVvD,QAAQ,IAAIoD,YAAY,CAACG,CAAC,CAAC,GAAGJ,aAAa,CAACI,CAAC,CAAC,CAAC,GAC/CJ,aAAa,CAACI,CAAC,CAAC;YACpB;YACAtD,SAAS,CAACwC,eAAe,GAAGY,SAAS;UACvC,CAAC,MAAM;YAEL,IAAMG,YAAY,GAAIN,YAAY,CAACN,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GACxDK,YAAY,CAACJ,KAAK,CAAC,CAAC,CAAyC;YAC/D,IAAMW,mBAAmB,GAAG,UAAUD,YAAY,EAAW;YAC7D,IAAME,kBAAkB,GAAG,SAASF,YAAY,EAAW;YAE3D,IAAMG,YAAY,GAAG5D,MAAM,CAAC0D,mBAAmB,CAAC;YAChD,IAAMG,WAAW,GAAG7D,MAAM,CAAC2D,kBAAkB,CAAC;YAE9CzD,SAAS,CAACiD,YAAY,CAAC,GACrBlD,QAAQ,IAAI4D,WAAW,GAAGD,YAAY,CAAC,GAAGA,YAAY;UAC1D;QACF;QACAzD,MAAM,CAACC,oBAAoB,CAACL,OAAO,EAAEG,SAAS,EAAE,IAAI,CAAC;MACvD,CAAC;IACH;EAAA;IAAAR,GAAA;IAAAvB,KAAA,EAeA,SAAcwB,MAAMA,CAClBC,sBAAwC,EACtB;MAClB,OAAO,IAAIxB,gBAAgB,CAAC,CAAC,CAACuB,MAAM,CAACC,sBAAsB,CAAC;IAC9D;EAAA;IAAAF,GAAA;IAAAvB,KAAA,EAWA,SAAckC,QAAQA,CAACA,UAAgB,EAAoB;MACzD,OAAO,IAAIjC,gBAAgB,CAAC,CAAC,CAACiC,QAAQ,CAACA,UAAQ,CAAC;IAClD;EAAA;IAAAX,GAAA;IAAAvB,KAAA,EAaA,SAAc0B,iBAAiBA,CAC7BC,yBAAkD,EAChC;MAClB,OAAO,IAAI1B,gBAAgB,CAAC,CAAC,CAACyB,iBAAiB,CAACC,yBAAyB,CAAC;IAC5E;EAAA;IAAAJ,GAAA;IAAAvB,KAAA,EAWA,SAAcoC,qBAAqBA,CACjCC,cAAoC,EAClB;MAClB,OAAO,IAAIpC,gBAAgB,CAAC,CAAC,CAACmC,qBAAqB,CAACC,cAAc,CAAC;IACrE;EAAA;IAAAd,GAAA;IAAAvB,KAAA,EAaA,SAAcmC,YAAYA,CAACA,cAA0B,EAAoB;MACvE,OAAO,IAAIlC,gBAAgB,CAAC,CAAC,CAACkC,YAAY,CAACA,cAAY,CAAC;IAC1D;EAAA;AAAA;AAlSWlC,gBAAgB,CAQZuD,0BAA0B,GAAG,IAAImC,oDAAyB,CAAC,CAAC", "ignoreList": []}