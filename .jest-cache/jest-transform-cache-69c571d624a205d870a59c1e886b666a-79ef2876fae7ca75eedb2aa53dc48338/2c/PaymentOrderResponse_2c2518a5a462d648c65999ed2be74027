c4be85ed471b0500e57ba2093a138fc2
"use strict";

/* istanbul ignore next */
function cov_3votbmdfd() {
  var path = "/Users/<USER>/projects/react-native/payment-module/src/data/models/payment-order/PaymentOrderResponse.ts";
  var hash = "f5016275ee2c3f006b8ac7ba30e7aa2c63c90bc0";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/projects/react-native/payment-module/src/data/models/payment-order/PaymentOrderResponse.ts",
    statementMap: {
      "0": {
        start: {
          line: 3,
          column: 0
        },
        end: {
          line: 5,
          column: 3
        }
      }
    },
    fnMap: {},
    branchMap: {},
    s: {
      "0": 0
    },
    f: {},
    b: {},
    inputSourceMap: {
      version: 3,
      file: undefined,
      names: [],
      sourceRoot: undefined,
      sources: ["/Users/<USER>/projects/react-native/payment-module/src/data/models/payment-order/PaymentOrderResponse.ts"],
      sourcesContent: ["export interface PaymentOrderResponse {\n  status?: string;\n  data?: string;\n}\n"],
      mappings: "",
      ignoreList: []
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "f5016275ee2c3f006b8ac7ba30e7aa2c63c90bc0"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_3votbmdfd = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_3votbmdfd();
cov_3votbmdfd().s[0]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6W10sInNvdXJjZXMiOlsiL1VzZXJzL3NvbnNhbXNldC9wcm9qZWN0cy9yZWFjdC1uYXRpdmUvcGF5bWVudC1tb2R1bGUvc3JjL2RhdGEvbW9kZWxzL3BheW1lbnQtb3JkZXIvUGF5bWVudE9yZGVyUmVzcG9uc2UudHMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGludGVyZmFjZSBQYXltZW50T3JkZXJSZXNwb25zZSB7XG4gIHN0YXR1cz86IHN0cmluZztcbiAgZGF0YT86IHN0cmluZztcbn1cbiJdLCJtYXBwaW5ncyI6IiIsImlnbm9yZUxpc3QiOltdfQ==