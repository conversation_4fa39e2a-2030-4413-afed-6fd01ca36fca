1a1ba5b89475d6bde5de0c6d610fe70e
"use strict";

/* istanbul ignore next */
function cov_4wzuvr7ui() {
  var path = "/Users/<USER>/projects/react-native/payment-module/src/domain/entities/validate/ValidateModel.ts";
  var hash = "9689cffe3252c08fa2be02e416e1fd914a87b38d";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/projects/react-native/payment-module/src/domain/entities/validate/ValidateModel.ts",
    statementMap: {
      "0": {
        start: {
          line: 3,
          column: 29
        },
        end: {
          line: 3,
          column: 84
        }
      },
      "1": {
        start: {
          line: 4,
          column: 20
        },
        end: {
          line: 4,
          column: 89
        }
      },
      "2": {
        start: {
          line: 5,
          column: 23
        },
        end: {
          line: 5,
          column: 95
        }
      },
      "3": {
        start: {
          line: 6,
          column: 0
        },
        end: {
          line: 8,
          column: 3
        }
      },
      "4": {
        start: {
          line: 9,
          column: 0
        },
        end: {
          line: 9,
          column: 31
        }
      },
      "5": {
        start: {
          line: 10,
          column: 20
        },
        end: {
          line: 12,
          column: 2
        }
      },
      "6": {
        start: {
          line: 11,
          column: 2
        },
        end: {
          line: 11,
          column: 53
        }
      },
      "7": {
        start: {
          line: 13,
          column: 0
        },
        end: {
          line: 13,
          column: 38
        }
      }
    },
    fnMap: {
      "0": {
        name: "ValidateModel",
        decl: {
          start: {
            line: 10,
            column: 56
          },
          end: {
            line: 10,
            column: 69
          }
        },
        loc: {
          start: {
            line: 10,
            column: 72
          },
          end: {
            line: 12,
            column: 1
          }
        },
        line: 10
      }
    },
    branchMap: {},
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0
    },
    f: {
      "0": 0
    },
    b: {},
    inputSourceMap: {
      version: 3,
      file: undefined,
      names: ["ValidateModel", "_createClass2", "default", "_classCallCheck2", "exports"],
      sourceRoot: undefined,
      sources: ["/Users/<USER>/projects/react-native/payment-module/src/domain/entities/validate/ValidateModel.ts"],
      sourcesContent: ["export class ValidateModel {\n  // TODO: define fields\n}\n"],
      mappings: ";;;;;;;;;IAAaA,aAAa,OAAAC,aAAA,CAAAC,OAAA,WAAAF,cAAA;EAAA,IAAAG,gBAAA,CAAAD,OAAA,QAAAF,aAAA;AAAA;AAA1BI,OAAA,CAAAJ,aAAA,GAAAA,aAAA",
      ignoreList: []
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "9689cffe3252c08fa2be02e416e1fd914a87b38d"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_4wzuvr7ui = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_4wzuvr7ui();
var _interopRequireDefault =
/* istanbul ignore next */
(cov_4wzuvr7ui().s[0]++, require("@babel/runtime/helpers/interopRequireDefault"));
var _createClass2 =
/* istanbul ignore next */
(cov_4wzuvr7ui().s[1]++, _interopRequireDefault(require("@babel/runtime/helpers/createClass")));
var _classCallCheck2 =
/* istanbul ignore next */
(cov_4wzuvr7ui().s[2]++, _interopRequireDefault(require("@babel/runtime/helpers/classCallCheck")));
/* istanbul ignore next */
cov_4wzuvr7ui().s[3]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_4wzuvr7ui().s[4]++;
exports.ValidateModel = void 0;
var ValidateModel =
/* istanbul ignore next */
(cov_4wzuvr7ui().s[5]++, (0, _createClass2.default)(function ValidateModel() {
  /* istanbul ignore next */
  cov_4wzuvr7ui().f[0]++;
  cov_4wzuvr7ui().s[6]++;
  (0, _classCallCheck2.default)(this, ValidateModel);
}));
/* istanbul ignore next */
cov_4wzuvr7ui().s[7]++;
exports.ValidateModel = ValidateModel;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJWYWxpZGF0ZU1vZGVsIiwiY292XzR3enV2cjd1aSIsInMiLCJfY3JlYXRlQ2xhc3MyIiwiZGVmYXVsdCIsImYiLCJfY2xhc3NDYWxsQ2hlY2syIiwiZXhwb3J0cyJdLCJzb3VyY2VzIjpbIi9Vc2Vycy9zb25zYW1zZXQvcHJvamVjdHMvcmVhY3QtbmF0aXZlL3BheW1lbnQtbW9kdWxlL3NyYy9kb21haW4vZW50aXRpZXMvdmFsaWRhdGUvVmFsaWRhdGVNb2RlbC50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgY2xhc3MgVmFsaWRhdGVNb2RlbCB7XG4gIC8vIFRPRE86IGRlZmluZSBmaWVsZHNcbn1cbiJdLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0lBQWFBLGFBQWE7QUFBQTtBQUFBLENBQUFDLGFBQUEsR0FBQUMsQ0FBQSxXQUFBQyxhQUFBLENBQUFDLE9BQUEsV0FBQUosY0FBQTtFQUFBO0VBQUFDLGFBQUEsR0FBQUksQ0FBQTtFQUFBSixhQUFBLEdBQUFDLENBQUE7RUFBQSxJQUFBSSxnQkFBQSxDQUFBRixPQUFBLFFBQUFKLGFBQUE7QUFBQTtBQUFBO0FBQUFDLGFBQUEsR0FBQUMsQ0FBQTtBQUExQkssT0FBQSxDQUFBUCxhQUFBLEdBQUFBLGFBQUEiLCJpZ25vcmVMaXN0IjpbXX0=