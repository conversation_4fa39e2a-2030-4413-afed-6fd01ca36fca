{"version": 3, "names": ["_interopRequireDefault", "require", "Object", "defineProperty", "exports", "value", "with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "setUpTests", "getAnimatedStyle", "advanceAnimationByTime", "advanceAnimationByFrame", "_slicedToArray2", "_PlatformChecker", "_errors", "defaultFramerateConfig", "fps", "isEmpty", "obj", "keys", "length", "getStylesFromObject", "undefined", "fromEntries", "entries", "map", "_ref", "_ref2", "default", "property", "_isReanimatedSharedValue", "getCurrentStyle", "component", "_component$props$jest", "styleObject", "props", "style", "currentStyle", "Array", "isArray", "for<PERSON>ach", "assign", "jestInlineStyles", "jestInlineStyle", "jestAnimatedStyleValue", "jestAnimatedStyle", "inlineStyles", "checkEqual", "current", "expected", "i", "findStyleDiff", "shouldMatchAllProps", "diffs", "isEqual", "push", "expect", "compareStyle", "expectedStyle", "config", "message", "pass", "_findStyleDiff", "currentStyleStr", "JSON", "stringify", "expectedStyleStr", "differences", "diff", "join", "frameTime", "Math", "round", "beforeTest", "jest", "useFakeTimers", "afterTest", "runOnlyPendingTimers", "useRealTimers", "animationTest", "console", "warn", "time", "arguments", "advanceTimersByTime", "count", "requireFunction", "isJest", "ReanimatedError", "userFramerateConfig", "global", "expectModule", "jestGlobals", "extend", "framerateConfig", "toHaveAnimatedStyle"], "sources": ["../../src/jestUtils.ts"], "sourcesContent": [null], "mappings": "AACA,YAAY;;AAAA,IAAAA,sBAAA,GAAAC,OAAA;AAAAC,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,KAAA;AAAA;AAAAD,OAAA,CAAAE,mBAAA,GAAAF,OAAA,CAAAG,UAAA,GAAAH,OAAA,CAAAI,gBAAA,GAAAJ,OAAA,CAAAK,sBAAA,GAAAL,OAAA,CAAAM,uBAAA;AAAA,IAAAC,eAAA,GAAAX,sBAAA,CAAAC,OAAA;AAQZ,IAAAW,gBAAA,GAAAX,OAAA;AAEA,IAAAY,OAAA,GAAAZ,OAAA;AAeA,IAAMa,sBAAsB,GAAG;EAC7BC,GAAG,EAAE;AACP,CAAC;AAED,IAAMC,OAAO,GAAI,SAAXA,OAAOA,CAAIC,GAAW;EAAA,OAAKf,MAAM,CAACgB,IAAI,CAACD,GAAG,CAAC,CAACE,MAAM,KAAK,CAAC;AAAA;AAC9D,IAAMC,mBAAmB,GAAI,SAAvBA,mBAAmBA,CAAIH,GAAW,EAAK;EAC3C,OAAOA,GAAG,KAAKI,SAAS,GACpB,CAAC,CAAC,GACFnB,MAAM,CAACoB,WAAW,CAChBpB,MAAM,CAACqB,OAAO,CAACN,GAAG,CAAC,CAACO,GAAG,CAAC,UAAAC,IAAA;IAAA,IAAAC,KAAA,OAAAf,eAAA,CAAAgB,OAAA,EAAAF,IAAA;MAAEG,QAAQ,GAAAF,KAAA;MAAErB,KAAK,GAAAqB,KAAA;IAAA,OAAM,CAC7CE,QAAQ,EACRvB,KAAK,CAACwB,wBAAwB,GAAGxB,KAAK,CAACA,KAAK,GAAGA,KAAK,CACrD;EAAA,EACH,CAAC;AACP,CAAC;AASD,IAAMyB,eAAe,GAAI,SAAnBA,eAAeA,CAAIC,SAAwB,EAAmB;EAAA,IAAAC,qBAAA;EAClE,IAAMC,WAAW,GAAGF,SAAS,CAACG,KAAK,CAACC,KAAK;EAEzC,IAAIC,YAAY,GAAG,CAAC,CAAC;EAErB,IAAIC,KAAK,CAACC,OAAO,CAACL,WAAW,CAAC,EAAE;IAG9BA,WAAW,CAACM,OAAO,CAAE,UAAAJ,KAAK,EAAK;MAC7BC,YAAY,GAAAlC,MAAA,CAAAsC,MAAA,KACPJ,YAAY,EACZD,KAAA,CACJ;IACH,CAAC,CAAC;IAEF,OAAOC,YAAY;EACrB;EAEA,IAAMK,gBAAgB,GAAGV,SAAS,CAACG,KAAK,CAACQ,eAAkC;EAC3E,IAAMC,sBAAsB,IAAAX,qBAAA,GAAGD,SAAS,CAACG,KAAK,CAACU,iBAAiB,qBAAjCZ,qBAAA,CAAmC3B,KAAK;EAEvE,IAAIgC,KAAK,CAACC,OAAO,CAACG,gBAAgB,CAAC,EAAE;IACnC,KAAK,IAAMxB,GAAG,IAAIwB,gBAAgB,EAAE;MAClC,IAAI,mBAAmB,IAAIxB,GAAG,EAAE;QAC9B;MACF;MAEA,IAAM4B,aAAY,GAAGzB,mBAAmB,CAACH,GAAG,CAAC;MAE7CmB,YAAY,GAAAlC,MAAA,CAAAsC,MAAA,KACPJ,YAAY,EACZS,aAAA,CACJ;IACH;IAEAT,YAAY,GAAAlC,MAAA,CAAAsC,MAAA,KACPP,WAAW,EACXG,YAAY,EACZO,sBAAA,CACJ;IAED,OAAOP,YAAY;EACrB;EAEA,IAAMS,YAAY,GAAGzB,mBAAmB,CAACqB,gBAAgB,CAAC;EAE1DL,YAAY,GAAGpB,OAAO,CAAC2B,sBAAgC,CAAC,GAAAzC,MAAA,CAAAsC,MAAA,KAC/CP,WAAW,EAAKY,YAAA,IAAA3C,MAAA,CAAAsC,MAAA,KAChBP,WAAW,EAAKU,sBAAA,CAAwB;EAEjD,OAAOP,YAAY;AACrB,CAAC;AAED,IAAMU,WAAU,GAAG,SAAbA,UAAUA,CAAWC,OAAc,EAAEC,QAAe,EAAK;EAC7D,IAAIX,KAAK,CAACC,OAAO,CAACU,QAAQ,CAAC,EAAE;IAC3B,IAAI,CAACX,KAAK,CAACC,OAAO,CAACS,OAAO,CAAC,IAAIC,QAAQ,CAAC7B,MAAM,KAAK4B,OAAO,CAAC5B,MAAM,EAAE;MACjE,OAAO,KAAK;IACd;IACA,KAAK,IAAI8B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,OAAO,CAAC5B,MAAM,EAAE8B,CAAC,EAAE,EAAE;MACvC,IAAI,CAACH,WAAU,CAACC,OAAO,CAACE,CAAC,CAAC,EAAED,QAAQ,CAACC,CAAC,CAAC,CAAC,EAAE;QACxC,OAAO,KAAK;MACd;IACF;EACF,CAAC,MAAM,IAAI,OAAOF,OAAO,KAAK,QAAQ,IAAIA,OAAO,EAAE;IACjD,IAAI,OAAOC,QAAQ,KAAK,QAAQ,IAAI,CAACA,QAAQ,EAAE;MAC7C,OAAO,KAAK;IACd;IACA,KAAK,IAAMpB,QAAQ,IAAIoB,QAAQ,EAAE;MAC/B,IAAI,CAACF,WAAU,CAACC,OAAO,CAACnB,QAAQ,CAAC,EAAEoB,QAAQ,CAACpB,QAAQ,CAAC,CAAC,EAAE;QACtD,OAAO,KAAK;MACd;IACF;EACF,CAAC,MAAM;IACL,OAAOmB,OAAO,KAAKC,QAAQ;EAC7B;EACA,OAAO,IAAI;AACb,CAAC;AAED,IAAME,aAAa,GAAG,SAAhBA,aAAaA,CACjBH,OAAqB,EACrBC,QAAsB,EACtBG,mBAA6B,EAC1B;EACH,IAAMC,KAAK,GAAG,EAAE;EAChB,IAAIC,OAAO,GAAG,IAAI;EAClB,IAAIzB,QAA4B;EAChC,KAAKA,QAAQ,IAAIoB,QAAQ,EAAE;IACzB,IAAI,CAACF,WAAU,CAACC,OAAO,CAACnB,QAAQ,CAAC,EAAEoB,QAAQ,CAACpB,QAAQ,CAAC,CAAC,EAAE;MACtDyB,OAAO,GAAG,KAAK;MACfD,KAAK,CAACE,IAAI,CAAC;QACT1B,QAAQ,EAARA,QAAQ;QACRmB,OAAO,EAAEA,OAAO,CAACnB,QAAQ,CAAC;QAC1B2B,MAAM,EAAEP,QAAQ,CAACpB,QAAQ;MAC3B,CAAC,CAAC;IACJ;EACF;EAEA,IACEuB,mBAAmB,IACnBjD,MAAM,CAACgB,IAAI,CAAC6B,OAAO,CAAC,CAAC5B,MAAM,KAAKjB,MAAM,CAACgB,IAAI,CAAC8B,QAAQ,CAAC,CAAC7B,MAAM,EAC5D;IACAkC,OAAO,GAAG,KAAK;IAEf,IAAIzB,SAA4B;IAChC,KAAKA,SAAQ,IAAImB,OAAO,EAAE;MACxB,IAAIC,QAAQ,CAACpB,SAAQ,CAAC,KAAKP,SAAS,EAAE;QACpC+B,KAAK,CAACE,IAAI,CAAC;UACT1B,QAAQ,EAARA,SAAQ;UACRmB,OAAO,EAAEA,OAAO,CAACnB,SAAQ,CAAC;UAC1B2B,MAAM,EAAEP,QAAQ,CAACpB,SAAQ;QAC3B,CAAC,CAAC;MACJ;IACF;EACF;EAEA,OAAO;IAAEyB,OAAO,EAAPA,OAAO;IAAED,KAAA,EAAAA;EAAM,CAAC;AAC3B,CAAC;AAED,IAAMI,YAAY,GAAG,SAAfA,YAAYA,CAChBzB,SAAwB,EACxB0B,aAA2B,EAC3BC,MAAiC,EAC9B;EACH,IAAI,CAAC3B,SAAS,CAACG,KAAK,CAACC,KAAK,EAAE;IAC1B,OAAO;MAAEwB,OAAO,EAAE,SAATA,OAAOA,CAAA;QAAA,OAAQ,iCAAiC;MAAA;MAAEC,IAAI,EAAE;IAAM,CAAC;EAC1E;EACA,IAAQT,mBAAA,GAAwBO,MAAM,CAA9BP,mBAAA;EACR,IAAMf,YAAY,GAAGN,eAAe,CAACC,SAAS,CAAC;EAC/C,IAAA8B,cAAA,GAA2BX,aAAa,CACtCd,YAAY,EACZqB,aAAa,EACbN,mBACF,CAAC;IAJOE,OAAO,GAAAQ,cAAA,CAAPR,OAAO;IAAED,KAAA,GAAAS,cAAA,CAAAT,KAAA;EAMjB,IAAIC,OAAO,EAAE;IACX,OAAO;MAAEM,OAAO,EAAE,SAATA,OAAOA,CAAA;QAAA,OAAQ,IAAI;MAAA;MAAEC,IAAI,EAAE;IAAK,CAAC;EAC5C;EAEA,IAAME,eAAe,GAAGC,IAAI,CAACC,SAAS,CAAC5B,YAAY,CAAC;EACpD,IAAM6B,gBAAgB,GAAGF,IAAI,CAACC,SAAS,CAACP,aAAa,CAAC;EACtD,IAAMS,WAAW,GAAGd,KAAK,CACtB5B,GAAG,CACD,UAAA2C,IAAI;IAAA,OACH,MAAMA,IAAI,CAACvC,QAAQ,eAAemC,IAAI,CAACC,SAAS,CAC9CG,IAAI,CAACZ,MACP,CAAC,YAAYQ,IAAI,CAACC,SAAS,CAACG,IAAI,CAACpB,OAAO,CAAC,EAC7C;EAAA,EAAC,CACAqB,IAAI,CAAC,IAAI,CAAC;EAEb,OAAO;IACLT,OAAO,EAAE,SAATA,OAAOA,CAAA;MAAA,OACL,aAAaM,gBAAgB,eAAeH,eAAe,qBAAqBI,WAAW,EAAE;IAAA;IAC/FN,IAAI,EAAE;EACR,CAAC;AACH,CAAC;AAED,IAAIS,SAAS,GAAGC,IAAI,CAACC,KAAK,CAAC,IAAI,GAAGzD,sBAAsB,CAACC,GAAG,CAAC;AAE7D,IAAMyD,UAAU,GAAG,SAAbA,UAAUA,CAAA,EAAS;EACvBC,IAAI,CAACC,aAAa,CAAC,CAAC;AACtB,CAAC;AAED,IAAMC,SAAS,GAAG,SAAZA,SAASA,CAAA,EAAS;EACtBF,IAAI,CAACG,oBAAoB,CAAC,CAAC;EAC3BH,IAAI,CAACI,aAAa,CAAC,CAAC;AACtB,CAAC;AAEM,IAAMvE,mBAAmB,GAAAF,OAAA,CAAAE,mBAAA,GAAI,SAAvBA,mBAAmBA,CAAIwE,aAAyB,EAAK;EAChEC,OAAO,CAACC,IAAI,CACV,sKACF,CAAC;EACDR,UAAU,CAAC,CAAC;EACZM,aAAa,CAAC,CAAC;EACfH,SAAS,CAAC,CAAC;AACb,CAAC;AAEM,IAAMlE,sBAAsB,GAAAL,OAAA,CAAAK,sBAAA,GAAG,SAAzBA,sBAAsBA,CAAA,EAAyB;EAAA,IAArBwE,IAAI,GAAAC,SAAA,CAAA/D,MAAA,QAAA+D,SAAA,QAAA7D,SAAA,GAAA6D,SAAA,MAAGb,SAAS;EACrDU,OAAO,CAACC,IAAI,CACV,kEACF,CAAC;EACDP,IAAI,CAACU,mBAAmB,CAACF,IAAI,CAAC;EAC9BR,IAAI,CAACG,oBAAoB,CAAC,CAAC;AAC7B,CAAC;AAEM,IAAMlE,uBAAuB,GAAAN,OAAA,CAAAM,uBAAA,GAAI,SAA3BA,uBAAuBA,CAAI0E,KAAa,EAAK;EACxDL,OAAO,CAACC,IAAI,CACV,kEACF,CAAC;EACDP,IAAI,CAACU,mBAAmB,CAACC,KAAK,GAAGf,SAAS,CAAC;EAC3CI,IAAI,CAACG,oBAAoB,CAAC,CAAC;AAC7B,CAAC;AAED,IAAMS,eAAe,GAAG,IAAAC,uBAAM,EAAC,CAAC,GAC5BrF,OAAO,GACP,YAAM;EACJ,MAAM,IAAIsF,uBAAe,CACvB,qDACF,CAAC;AACH,CAAC;AAME,IAAMhF,UAAU,GAAAH,OAAA,CAAAG,UAAA,GAAG,SAAbA,UAAUA,CAAA,EAAiC;EAAA,IAA7BiF,mBAAmB,GAAAN,SAAA,CAAA/D,MAAA,QAAA+D,SAAA,QAAA7D,SAAA,GAAA6D,SAAA,MAAG,CAAC,CAAC;EACjD,IAAI3B,MAAmB,GAAIkC,MAAM,CAC9BlC,MAAM;EACT,IAAIA,MAAM,KAAKlC,SAAS,EAAE;IACxB,IAAMqE,YAAY,GAAGL,eAAe,CAAC,QAAQ,CAAC;IAC9C9B,MAAM,GAAGmC,YAAY;IAKrB,IAAI,OAAOnC,MAAM,KAAK,QAAQ,EAAE;MAC9B,IAAMoC,WAAW,GAAGN,eAAe,CAAC,eAAe,CAAC;MACpD9B,MAAM,GAAGoC,WAAW,CAACpC,MAAM;IAC7B;IACA,IAAIA,MAAM,KAAKlC,SAAS,IAAIkC,MAAM,CAACqC,MAAM,KAAKvE,SAAS,EAAE;MACvDkC,MAAM,GAAGmC,YAAY,CAAC/D,OAAO;IAC/B;EACF;EAEA,IAAMkE,eAAe,GAAA3F,MAAA,CAAAsC,MAAA,KAChB1B,sBAAsB,EACtB0E,mBAAA,CACJ;EACDnB,SAAS,GAAGC,IAAI,CAACC,KAAK,CAAC,IAAI,GAAGsB,eAAe,CAAC9E,GAAG,CAAC;EAElDwC,MAAM,CAACqC,MAAM,CAAC;IACZE,mBAAmB,WAAnBA,mBAAmBA,CACjB/D,SAG4B,EAC5B0B,aAA2B,EAE3B;MAAA,IADAC,MAAiC,GAAAwB,SAAA,CAAA/D,MAAA,QAAA+D,SAAA,QAAA7D,SAAA,GAAA6D,SAAA,MAAG,CAAC,CAAC;MAEtC,OAAO1B,YAAY,CAACzB,SAAS,EAAE0B,aAAa,EAAEC,MAAM,CAAC;IACvD;EACF,CAAC,CAAC;AACJ,CAAC;AAQM,IAAMlD,gBAAgB,GAAAJ,OAAA,CAAAI,gBAAA,GAAI,SAApBA,gBAAgBA,CAAIuB,SAA4B,EAAK;EAChE,OAAOD,eAAe,CAGpBC,SACF,CAAC;AACH,CAAC", "ignoreList": []}