0e4e71305ba880ccb1f91a7d81a7c835
"use strict";

/* istanbul ignore next */
function cov_5meac05gs() {
  var path = "/Users/<USER>/projects/react-native/payment-module/src/presentation/qr-payment-info/styles.ts";
  var hash = "3dc16b0e170a2ff102950be2069949e7fd38aa13";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/projects/react-native/payment-module/src/presentation/qr-payment-info/styles.ts",
    statementMap: {
      "0": {
        start: {
          line: 3,
          column: 0
        },
        end: {
          line: 5,
          column: 3
        }
      },
      "1": {
        start: {
          line: 6,
          column: 0
        },
        end: {
          line: 6,
          column: 27
        }
      },
      "2": {
        start: {
          line: 7,
          column: 29
        },
        end: {
          line: 7,
          column: 60
        }
      },
      "3": {
        start: {
          line: 8,
          column: 0
        },
        end: {
          line: 53,
          column: 3
        }
      },
      "4": {
        start: {
          line: 9,
          column: 18
        },
        end: {
          line: 9,
          column: 32
        }
      },
      "5": {
        start: {
          line: 10,
          column: 17
        },
        end: {
          line: 10,
          column: 32
        }
      },
      "6": {
        start: {
          line: 11,
          column: 13
        },
        end: {
          line: 11,
          column: 24
        }
      },
      "7": {
        start: {
          line: 12,
          column: 2
        },
        end: {
          line: 52,
          column: 4
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 8,
            column: 68
          },
          end: {
            line: 8,
            column: 69
          }
        },
        loc: {
          start: {
            line: 8,
            column: 84
          },
          end: {
            line: 53,
            column: 1
          }
        },
        line: 8
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 19,
            column: 35
          },
          end: {
            line: 19,
            column: 87
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 19,
            column: 56
          },
          end: {
            line: 19,
            column: 62
          }
        }, {
          start: {
            line: 19,
            column: 65
          },
          end: {
            line: 19,
            column: 87
          }
        }],
        line: 19
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0
    },
    f: {
      "0": 0
    },
    b: {
      "0": [0, 0]
    },
    inputSourceMap: {
      version: 3,
      file: undefined,
      names: ["msb_shared_component_1", "require", "exports", "makeStyle", "createMSBStyleSheet", "_ref", "SizeAlias", "Typography", "Shadow", "accountInfo", "Object", "assign", "backgroundColor", "ColorAlias", "BackgroundWhite", "borderRadius", "SpacingXSmall", "paddingHorizontal", "SpacingSmall", "paddingVertical", "center", "amountInput", "base_medium", "marginTop", "btnContinue", "marginHorizontal", "container", "flex", "contentContainerScrollView", "padding", "contentContainer", "Radius3", "SpacingXLarge", "contentLabel", "color", "ColorLabelCaption", "TextMain", "marginRight", "Spacing4xSmall", "paddingBottom", "flexDirectionRow", "flexDirection", "imageBackground", "resizeMode", "scrollViewContentContainer", "Spacing3xLarge"],
      sourceRoot: undefined,
      sources: ["/Users/<USER>/projects/react-native/payment-module/src/presentation/qr-payment-info/styles.ts"],
      sourcesContent: ["import {ColorAlias, ColorLabelCaption, createMSBStyleSheet} from 'msb-shared-component';\n\nexport const makeStyle = createMSBStyleSheet(({SizeAlias, Typography, Shadow}) => {\n  return {\n    accountInfo: {\n      backgroundColor: ColorAlias.BackgroundWhite,\n      borderRadius: SizeAlias.SpacingXSmall,\n      paddingHorizontal: SizeAlias.SpacingSmall,\n      paddingVertical: SizeAlias.SpacingXSmall,\n      ...Shadow.center,\n    },\n    amountInput: {\n      ...Typography?.base_medium,\n      marginTop: SizeAlias.SpacingSmall,\n    },\n    btnContinue: {\n      marginHorizontal: SizeAlias.SpacingSmall,\n    },\n    container: {\n      flex: 1,\n    },\n\n    contentContainerScrollView: {\n      padding: SizeAlias.SpacingSmall,\n    },\n\n    contentContainer: {\n      backgroundColor: ColorAlias.BackgroundWhite,\n      borderRadius: SizeAlias.Radius3,\n      marginTop: SizeAlias.SpacingXLarge,\n      padding: SizeAlias.SpacingSmall,\n      ...Shadow.center,\n    },\n    contentLabel: {\n      color: ColorLabelCaption.TextMain,\n      marginRight: SizeAlias.Spacing4xSmall,\n      paddingBottom: SizeAlias.Spacing4xSmall,\n    },\n    flexDirectionRow: {\n      flexDirection: 'row',\n    },\n    imageBackground: {\n      flex: 1,\n      resizeMode: 'stretch',\n    },\n    scrollViewContentContainer: {\n      paddingBottom: SizeAlias.Spacing3xLarge,\n    },\n  };\n});\n"],
      mappings: ";;;;;;AAAA,IAAAA,sBAAA,GAAAC,OAAA;AAEaC,OAAA,CAAAC,SAAS,GAAG,IAAAH,sBAAA,CAAAI,mBAAmB,EAAC,UAAAC,IAAA,EAAoC;EAAA,IAAlCC,SAAS,GAAAD,IAAA,CAATC,SAAS;IAAEC,UAAU,GAAAF,IAAA,CAAVE,UAAU;IAAEC,MAAM,GAAAH,IAAA,CAANG,MAAM;EAC1E,OAAO;IACLC,WAAW,EAAAC,MAAA,CAAAC,MAAA;MACTC,eAAe,EAAEZ,sBAAA,CAAAa,UAAU,CAACC,eAAe;MAC3CC,YAAY,EAAET,SAAS,CAACU,aAAa;MACrCC,iBAAiB,EAAEX,SAAS,CAACY,YAAY;MACzCC,eAAe,EAAEb,SAAS,CAACU;IAAa,GACrCR,MAAM,CAACY,MAAM,CACjB;IACDC,WAAW,EAAAX,MAAA,CAAAC,MAAA,KACNJ,UAAU,oBAAVA,UAAU,CAAEe,WAAW;MAC1BC,SAAS,EAAEjB,SAAS,CAACY;IAAY,EAClC;IACDM,WAAW,EAAE;MACXC,gBAAgB,EAAEnB,SAAS,CAACY;KAC7B;IACDQ,SAAS,EAAE;MACTC,IAAI,EAAE;KACP;IAEDC,0BAA0B,EAAE;MAC1BC,OAAO,EAAEvB,SAAS,CAACY;KACpB;IAEDY,gBAAgB,EAAApB,MAAA,CAAAC,MAAA;MACdC,eAAe,EAAEZ,sBAAA,CAAAa,UAAU,CAACC,eAAe;MAC3CC,YAAY,EAAET,SAAS,CAACyB,OAAO;MAC/BR,SAAS,EAAEjB,SAAS,CAAC0B,aAAa;MAClCH,OAAO,EAAEvB,SAAS,CAACY;IAAY,GAC5BV,MAAM,CAACY,MAAM,CACjB;IACDa,YAAY,EAAE;MACZC,KAAK,EAAElC,sBAAA,CAAAmC,iBAAiB,CAACC,QAAQ;MACjCC,WAAW,EAAE/B,SAAS,CAACgC,cAAc;MACrCC,aAAa,EAAEjC,SAAS,CAACgC;KAC1B;IACDE,gBAAgB,EAAE;MAChBC,aAAa,EAAE;KAChB;IACDC,eAAe,EAAE;MACff,IAAI,EAAE,CAAC;MACPgB,UAAU,EAAE;KACb;IACDC,0BAA0B,EAAE;MAC1BL,aAAa,EAAEjC,SAAS,CAACuC;;GAE5B;AACH,CAAC,CAAC",
      ignoreList: []
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "3dc16b0e170a2ff102950be2069949e7fd38aa13"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_5meac05gs = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_5meac05gs();
cov_5meac05gs().s[0]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_5meac05gs().s[1]++;
exports.makeStyle = void 0;
var msb_shared_component_1 =
/* istanbul ignore next */
(cov_5meac05gs().s[2]++, require("msb-shared-component"));
/* istanbul ignore next */
cov_5meac05gs().s[3]++;
exports.makeStyle = (0, msb_shared_component_1.createMSBStyleSheet)(function (_ref) {
  /* istanbul ignore next */
  cov_5meac05gs().f[0]++;
  var SizeAlias =
    /* istanbul ignore next */
    (cov_5meac05gs().s[4]++, _ref.SizeAlias),
    Typography =
    /* istanbul ignore next */
    (cov_5meac05gs().s[5]++, _ref.Typography),
    Shadow =
    /* istanbul ignore next */
    (cov_5meac05gs().s[6]++, _ref.Shadow);
  /* istanbul ignore next */
  cov_5meac05gs().s[7]++;
  return {
    accountInfo: Object.assign({
      backgroundColor: msb_shared_component_1.ColorAlias.BackgroundWhite,
      borderRadius: SizeAlias.SpacingXSmall,
      paddingHorizontal: SizeAlias.SpacingSmall,
      paddingVertical: SizeAlias.SpacingXSmall
    }, Shadow.center),
    amountInput: Object.assign({}, Typography == null ?
    /* istanbul ignore next */
    (cov_5meac05gs().b[0][0]++, void 0) :
    /* istanbul ignore next */
    (cov_5meac05gs().b[0][1]++, Typography.base_medium), {
      marginTop: SizeAlias.SpacingSmall
    }),
    btnContinue: {
      marginHorizontal: SizeAlias.SpacingSmall
    },
    container: {
      flex: 1
    },
    contentContainerScrollView: {
      padding: SizeAlias.SpacingSmall
    },
    contentContainer: Object.assign({
      backgroundColor: msb_shared_component_1.ColorAlias.BackgroundWhite,
      borderRadius: SizeAlias.Radius3,
      marginTop: SizeAlias.SpacingXLarge,
      padding: SizeAlias.SpacingSmall
    }, Shadow.center),
    contentLabel: {
      color: msb_shared_component_1.ColorLabelCaption.TextMain,
      marginRight: SizeAlias.Spacing4xSmall,
      paddingBottom: SizeAlias.Spacing4xSmall
    },
    flexDirectionRow: {
      flexDirection: 'row'
    },
    imageBackground: {
      flex: 1,
      resizeMode: 'stretch'
    },
    scrollViewContentContainer: {
      paddingBottom: SizeAlias.Spacing3xLarge
    }
  };
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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