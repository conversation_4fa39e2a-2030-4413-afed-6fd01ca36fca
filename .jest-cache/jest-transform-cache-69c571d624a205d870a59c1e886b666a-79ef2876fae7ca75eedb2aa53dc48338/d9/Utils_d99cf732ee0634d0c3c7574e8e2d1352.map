{"version": 3, "names": ["cov_8ogkki442", "actualCoverage", "msb_host_shared_module_1", "s", "require", "i18n_1", "Utils_1", "__importDefault", "checkIBMB", "onConfirm", "f", "default", "showPopup", "iconType", "PopupType", "WARNING", "title", "translate", "content", "confirmBtnText", "checkBiometricAuthentication", "BiometricAuthentication", "onCancel", "_msb_host_shared_modu", "b", "hostSharedModule", "d", "domainService", "showBottomSheet", "header", "children", "cancelBtnText", "enableDynamicSizing", "checkIdentification", "Identification", "_msb_host_shared_modu2", "checkErrorSystem", "errorMessage", "errorCode", "checkDuplicateSourceAccount", "checkDailyAvailableLimit", "dailyAvailableLimit", "value", "checkVisibleCounterPartyMSB", "userNotValid", "exports"], "sources": ["/Users/<USER>/projects/react-native/payment-module/src/presentation/payment-info/utils/Utils.ts"], "sourcesContent": ["import {hostSharedModule, PopupType} from 'msb-host-shared-module';\nimport {ReactNode} from 'react';\nimport {translate} from '../../../locales/i18n';\nimport Utils from '../../../utils/Utils';\n\n// chec gói truy vấn\nconst checkIBMB = (onConfirm?: () => void) => {\n  Utils.showPopup({\n    iconType: PopupType.WARNING,\n    title: translate('paymentInfor.paymentInfoValidate.checkIBMB'),\n    content: translate('paymentInfor.paymentInfoValidate.checkIBMBDescription'),\n    confirmBtnText: translate('close'),\n    onConfirm,\n  });\n};\n\n// chưa thu nhập sinh trắc học\nconst checkBiometricAuthentication = (\n  BiometricAuthentication: ReactNode,\n  onConfirm?: () => void,\n  onCancel?: () => void,\n) => {\n  hostSharedModule.d.domainService?.showBottomSheet({\n    header: translate('paymentConfirm.transferNotYetExecuted'),\n    children: BiometricAuthentication,\n    cancelBtnText: translate('paymentConfirm.doItLater'),\n    confirmBtnText: translate('paymentConfirm.updateNow'),\n    onCancel,\n    onConfirm,\n    enableDynamicSizing: false,\n  });\n};\n// giấy tờ tuỳ thân hết hạn\nconst checkIdentification = (Identification: ReactNode, onConfirm?: () => void, onCancel?: () => void) => {\n  hostSharedModule.d.domainService?.showBottomSheet({\n    header: translate('paymentConfirm.transferNotYetExecuted'),\n    children: Identification,\n    cancelBtnText: translate('paymentConfirm.doItLater'),\n    confirmBtnText: translate('paymentConfirm.updateNow'),\n    onCancel,\n    onConfirm,\n    enableDynamicSizing: false,\n  });\n};\n\n// Có gián đoạn tạm thời\nconst checkErrorSystem = (errorMessage?: string, onConfirm?: () => void, errorCode?: string) => {\n  Utils.showPopup({\n    iconType: PopupType.WARNING,\n    title: translate('paymentConfirm.haveTemporaryInterruption'),\n    content: errorMessage || translate('paymentConfirm.explainHaveTemporaryInterruption'),\n    confirmBtnText: translate('close'),\n    onConfirm,\n    errorCode: errorCode ? `Mã: ${errorCode}` : '',\n  });\n};\n\n// check tài khoản nguồn trùng tài khoản thụ hưởng\nconst checkDuplicateSourceAccount = (onConfirm?: () => void) => {\n  Utils.showPopup({\n    iconType: PopupType.WARNING,\n    title: translate('paymentInfor.paymentInfoValidate.duplicateSourceAccount'),\n    content: translate('paymentInfor.paymentInfoValidate.duplicateSourceAccountDescription'),\n    confirmBtnText: translate('close'),\n    onConfirm,\n  });\n};\n\n// số tiền giao dịch vượt quá hạn mức còn lại trong ngayf\nconst checkDailyAvailableLimit = (dailyAvailableLimit: string, onConfirm: () => void) => {\n  Utils.showPopup({\n    iconType: PopupType.WARNING,\n    title: translate('paymentInfor.paymentInfoValidateTitle'),\n    content: translate('paymentInfor.paymentInfoValidateDescription', {\n      value: dailyAvailableLimit,\n    }),\n    cancelBtnText: translate('close'),\n    confirmBtnText: 'Cài đặt hạn mức',\n    onConfirm,\n  });\n};\n\n// không tìm thấy thông tin người nhận Nội bộ\nconst checkVisibleCounterPartyMSB = (onConfirm?: () => void) => {\n  Utils.showPopup({\n    iconType: PopupType.WARNING,\n    title: translate('paymentInfor.userNotFound'),\n    content: translate('paymentInfor.userNotFoundDescription'),\n    confirmBtnText: translate('close'),\n    onConfirm,\n  });\n};\n\nconst userNotValid = (onConfirm?: () => void) => {\n  Utils.showPopup({\n    iconType: PopupType.WARNING,\n    title: translate('paymentInfor.userNotValid'),\n    content: translate('paymentInfor.userNotValidDescription'),\n    confirmBtnText: translate('close'),\n    onConfirm,\n  });\n};\n\nexport default {\n  checkIBMB,\n  checkBiometricAuthentication,\n  checkIdentification,\n  checkErrorSystem,\n  checkDuplicateSourceAccount,\n  checkDailyAvailableLimit,\n  checkVisibleCounterPartyMSB,\n  userNotValid,\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAQY;IAAAA,aAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,aAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;AARZ,IAAAE,wBAAA;AAAA;AAAA,CAAAF,aAAA,GAAAG,CAAA,OAAAC,OAAA;AAEA,IAAAC,MAAA;AAAA;AAAA,CAAAL,aAAA,GAAAG,CAAA,OAAAC,OAAA;AACA,IAAAE,OAAA;AAAA;AAAA,CAAAN,aAAA,GAAAG,CAAA,OAAAI,eAAA,CAAAH,OAAA;AAAA;AAAAJ,aAAA,GAAAG,CAAA;AAGA,IAAMK,SAAS,GAAG,SAAZA,SAASA,CAAIC,SAAsB,EAAI;EAAA;EAAAT,aAAA,GAAAU,CAAA;EAAAV,aAAA,GAAAG,CAAA;EAC3CG,OAAA,CAAAK,OAAK,CAACC,SAAS,CAAC;IACdC,QAAQ,EAAEX,wBAAA,CAAAY,SAAS,CAACC,OAAO;IAC3BC,KAAK,EAAE,IAAAX,MAAA,CAAAY,SAAS,EAAC,4CAA4C,CAAC;IAC9DC,OAAO,EAAE,IAAAb,MAAA,CAAAY,SAAS,EAAC,uDAAuD,CAAC;IAC3EE,cAAc,EAAE,IAAAd,MAAA,CAAAY,SAAS,EAAC,OAAO,CAAC;IAClCR,SAAS,EAATA;GACD,CAAC;AACJ,CAAC;AAAA;AAAAT,aAAA,GAAAG,CAAA;AAGD,IAAMiB,4BAA4B,GAAG,SAA/BA,4BAA4BA,CAChCC,uBAAkC,EAClCZ,SAAsB,EACtBa,QAAqB,EACnB;EAAA;EAAAtB,aAAA,GAAAU,CAAA;EAAA,IAAAa,qBAAA;EAAA;EAAAvB,aAAA,GAAAG,CAAA;EACF;EAAA,CAAAH,aAAA,GAAAwB,CAAA,WAAAD,qBAAA,GAAArB,wBAAA,CAAAuB,gBAAgB,CAACC,CAAC,CAACC,aAAa;EAAA;EAAA,CAAA3B,aAAA,GAAAwB,CAAA,UAAhCD,qBAAA,CAAkCK,eAAe,CAAC;IAChDC,MAAM,EAAE,IAAAxB,MAAA,CAAAY,SAAS,EAAC,uCAAuC,CAAC;IAC1Da,QAAQ,EAAET,uBAAuB;IACjCU,aAAa,EAAE,IAAA1B,MAAA,CAAAY,SAAS,EAAC,0BAA0B,CAAC;IACpDE,cAAc,EAAE,IAAAd,MAAA,CAAAY,SAAS,EAAC,0BAA0B,CAAC;IACrDK,QAAQ,EAARA,QAAQ;IACRb,SAAS,EAATA,SAAS;IACTuB,mBAAmB,EAAE;GACtB,CAAC;AACJ,CAAC;AAAA;AAAAhC,aAAA,GAAAG,CAAA;AAED,IAAM8B,mBAAmB,GAAG,SAAtBA,mBAAmBA,CAAIC,cAAyB,EAAEzB,SAAsB,EAAEa,QAAqB,EAAI;EAAA;EAAAtB,aAAA,GAAAU,CAAA;EAAA,IAAAyB,sBAAA;EAAA;EAAAnC,aAAA,GAAAG,CAAA;EACvG;EAAA,CAAAH,aAAA,GAAAwB,CAAA,WAAAW,sBAAA,GAAAjC,wBAAA,CAAAuB,gBAAgB,CAACC,CAAC,CAACC,aAAa;EAAA;EAAA,CAAA3B,aAAA,GAAAwB,CAAA,UAAhCW,sBAAA,CAAkCP,eAAe,CAAC;IAChDC,MAAM,EAAE,IAAAxB,MAAA,CAAAY,SAAS,EAAC,uCAAuC,CAAC;IAC1Da,QAAQ,EAAEI,cAAc;IACxBH,aAAa,EAAE,IAAA1B,MAAA,CAAAY,SAAS,EAAC,0BAA0B,CAAC;IACpDE,cAAc,EAAE,IAAAd,MAAA,CAAAY,SAAS,EAAC,0BAA0B,CAAC;IACrDK,QAAQ,EAARA,QAAQ;IACRb,SAAS,EAATA,SAAS;IACTuB,mBAAmB,EAAE;GACtB,CAAC;AACJ,CAAC;AAAA;AAAAhC,aAAA,GAAAG,CAAA;AAGD,IAAMiC,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAAIC,YAAqB,EAAE5B,SAAsB,EAAE6B,SAAkB,EAAI;EAAA;EAAAtC,aAAA,GAAAU,CAAA;EAAAV,aAAA,GAAAG,CAAA;EAC7FG,OAAA,CAAAK,OAAK,CAACC,SAAS,CAAC;IACdC,QAAQ,EAAEX,wBAAA,CAAAY,SAAS,CAACC,OAAO;IAC3BC,KAAK,EAAE,IAAAX,MAAA,CAAAY,SAAS,EAAC,0CAA0C,CAAC;IAC5DC,OAAO;IAAE;IAAA,CAAAlB,aAAA,GAAAwB,CAAA,UAAAa,YAAY;IAAA;IAAA,CAAArC,aAAA,GAAAwB,CAAA,UAAI,IAAAnB,MAAA,CAAAY,SAAS,EAAC,iDAAiD,CAAC;IACrFE,cAAc,EAAE,IAAAd,MAAA,CAAAY,SAAS,EAAC,OAAO,CAAC;IAClCR,SAAS,EAATA,SAAS;IACT6B,SAAS,EAAEA,SAAS;IAAA;IAAA,CAAAtC,aAAA,GAAAwB,CAAA,UAAG,OAAOc,SAAS,EAAE;IAAA;IAAA,CAAAtC,aAAA,GAAAwB,CAAA,UAAG;GAC7C,CAAC;AACJ,CAAC;AAAA;AAAAxB,aAAA,GAAAG,CAAA;AAGD,IAAMoC,2BAA2B,GAAG,SAA9BA,2BAA2BA,CAAI9B,SAAsB,EAAI;EAAA;EAAAT,aAAA,GAAAU,CAAA;EAAAV,aAAA,GAAAG,CAAA;EAC7DG,OAAA,CAAAK,OAAK,CAACC,SAAS,CAAC;IACdC,QAAQ,EAAEX,wBAAA,CAAAY,SAAS,CAACC,OAAO;IAC3BC,KAAK,EAAE,IAAAX,MAAA,CAAAY,SAAS,EAAC,yDAAyD,CAAC;IAC3EC,OAAO,EAAE,IAAAb,MAAA,CAAAY,SAAS,EAAC,oEAAoE,CAAC;IACxFE,cAAc,EAAE,IAAAd,MAAA,CAAAY,SAAS,EAAC,OAAO,CAAC;IAClCR,SAAS,EAATA;GACD,CAAC;AACJ,CAAC;AAAA;AAAAT,aAAA,GAAAG,CAAA;AAGD,IAAMqC,wBAAwB,GAAG,SAA3BA,wBAAwBA,CAAIC,mBAA2B,EAAEhC,SAAqB,EAAI;EAAA;EAAAT,aAAA,GAAAU,CAAA;EAAAV,aAAA,GAAAG,CAAA;EACtFG,OAAA,CAAAK,OAAK,CAACC,SAAS,CAAC;IACdC,QAAQ,EAAEX,wBAAA,CAAAY,SAAS,CAACC,OAAO;IAC3BC,KAAK,EAAE,IAAAX,MAAA,CAAAY,SAAS,EAAC,uCAAuC,CAAC;IACzDC,OAAO,EAAE,IAAAb,MAAA,CAAAY,SAAS,EAAC,6CAA6C,EAAE;MAChEyB,KAAK,EAAED;KACR,CAAC;IACFV,aAAa,EAAE,IAAA1B,MAAA,CAAAY,SAAS,EAAC,OAAO,CAAC;IACjCE,cAAc,EAAE,iBAAiB;IACjCV,SAAS,EAATA;GACD,CAAC;AACJ,CAAC;AAAA;AAAAT,aAAA,GAAAG,CAAA;AAGD,IAAMwC,2BAA2B,GAAG,SAA9BA,2BAA2BA,CAAIlC,SAAsB,EAAI;EAAA;EAAAT,aAAA,GAAAU,CAAA;EAAAV,aAAA,GAAAG,CAAA;EAC7DG,OAAA,CAAAK,OAAK,CAACC,SAAS,CAAC;IACdC,QAAQ,EAAEX,wBAAA,CAAAY,SAAS,CAACC,OAAO;IAC3BC,KAAK,EAAE,IAAAX,MAAA,CAAAY,SAAS,EAAC,2BAA2B,CAAC;IAC7CC,OAAO,EAAE,IAAAb,MAAA,CAAAY,SAAS,EAAC,sCAAsC,CAAC;IAC1DE,cAAc,EAAE,IAAAd,MAAA,CAAAY,SAAS,EAAC,OAAO,CAAC;IAClCR,SAAS,EAATA;GACD,CAAC;AACJ,CAAC;AAAA;AAAAT,aAAA,GAAAG,CAAA;AAED,IAAMyC,YAAY,GAAG,SAAfA,YAAYA,CAAInC,SAAsB,EAAI;EAAA;EAAAT,aAAA,GAAAU,CAAA;EAAAV,aAAA,GAAAG,CAAA;EAC9CG,OAAA,CAAAK,OAAK,CAACC,SAAS,CAAC;IACdC,QAAQ,EAAEX,wBAAA,CAAAY,SAAS,CAACC,OAAO;IAC3BC,KAAK,EAAE,IAAAX,MAAA,CAAAY,SAAS,EAAC,2BAA2B,CAAC;IAC7CC,OAAO,EAAE,IAAAb,MAAA,CAAAY,SAAS,EAAC,sCAAsC,CAAC;IAC1DE,cAAc,EAAE,IAAAd,MAAA,CAAAY,SAAS,EAAC,OAAO,CAAC;IAClCR,SAAS,EAATA;GACD,CAAC;AACJ,CAAC;AAAA;AAAAT,aAAA,GAAAG,CAAA;AAED0C,OAAA,CAAAlC,OAAA,GAAe;EACbH,SAAS,EAATA,SAAS;EACTY,4BAA4B,EAA5BA,4BAA4B;EAC5Ba,mBAAmB,EAAnBA,mBAAmB;EACnBG,gBAAgB,EAAhBA,gBAAgB;EAChBG,2BAA2B,EAA3BA,2BAA2B;EAC3BC,wBAAwB,EAAxBA,wBAAwB;EACxBG,2BAA2B,EAA3BA,2BAA2B;EAC3BC,YAAY,EAAZA;CACD", "ignoreList": []}