9baf038fb82e11faae5de9c209bf7a86
"use strict";

/* istanbul ignore next */
function cov_2efpwvh9ai() {
  var path = "/Users/<USER>/projects/react-native/payment-module/src/domain/repositories/IArrangementRepository.ts";
  var hash = "91e3479c7863ed5ce08d837e684fea7f3249e2b4";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/projects/react-native/payment-module/src/domain/repositories/IArrangementRepository.ts",
    statementMap: {
      "0": {
        start: {
          line: 3,
          column: 0
        },
        end: {
          line: 5,
          column: 3
        }
      }
    },
    fnMap: {},
    branchMap: {},
    s: {
      "0": 0
    },
    f: {},
    b: {},
    inputSourceMap: {
      version: 3,
      file: undefined,
      names: [],
      sourceRoot: undefined,
      sources: ["/Users/<USER>/projects/react-native/payment-module/src/domain/repositories/IArrangementRepository.ts"],
      sourcesContent: ["import {SourceAccountListModel} from '../entities/source-account-list/SourceAccountListModel';\nimport {SourceAccountListRequest} from '../../data/models/source-account-list/SourceAccountListRequest';\nimport {BaseResponse} from '../../core/BaseResponse';\n\nexport interface IArrangementRepository {\n  sourceAccountList(request: SourceAccountListRequest): Promise<BaseResponse<SourceAccountListModel>>;\n}\n"],
      mappings: "",
      ignoreList: []
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "91e3479c7863ed5ce08d837e684fea7f3249e2b4"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_2efpwvh9ai = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_2efpwvh9ai();
cov_2efpwvh9ai().s[0]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6W10sInNvdXJjZXMiOlsiL1VzZXJzL3NvbnNhbXNldC9wcm9qZWN0cy9yZWFjdC1uYXRpdmUvcGF5bWVudC1tb2R1bGUvc3JjL2RvbWFpbi9yZXBvc2l0b3JpZXMvSUFycmFuZ2VtZW50UmVwb3NpdG9yeS50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQge1NvdXJjZUFjY291bnRMaXN0TW9kZWx9IGZyb20gJy4uL2VudGl0aWVzL3NvdXJjZS1hY2NvdW50LWxpc3QvU291cmNlQWNjb3VudExpc3RNb2RlbCc7XG5pbXBvcnQge1NvdXJjZUFjY291bnRMaXN0UmVxdWVzdH0gZnJvbSAnLi4vLi4vZGF0YS9tb2RlbHMvc291cmNlLWFjY291bnQtbGlzdC9Tb3VyY2VBY2NvdW50TGlzdFJlcXVlc3QnO1xuaW1wb3J0IHtCYXNlUmVzcG9uc2V9IGZyb20gJy4uLy4uL2NvcmUvQmFzZVJlc3BvbnNlJztcblxuZXhwb3J0IGludGVyZmFjZSBJQXJyYW5nZW1lbnRSZXBvc2l0b3J5IHtcbiAgc291cmNlQWNjb3VudExpc3QocmVxdWVzdDogU291cmNlQWNjb3VudExpc3RSZXF1ZXN0KTogUHJvbWlzZTxCYXNlUmVzcG9uc2U8U291cmNlQWNjb3VudExpc3RNb2RlbD4+O1xufVxuIl0sIm1hcHBpbmdzIjoiIiwiaWdub3JlTGlzdCI6W119