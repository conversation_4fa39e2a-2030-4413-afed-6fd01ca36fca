{"version": 3, "names": [], "sources": ["/Users/<USER>/projects/react-native/payment-module/src/domain/repositories/IArrangementRepository.ts"], "sourcesContent": ["import {SourceAccountListModel} from '../entities/source-account-list/SourceAccountListModel';\nimport {SourceAccountListRequest} from '../../data/models/source-account-list/SourceAccountListRequest';\nimport {BaseResponse} from '../../core/BaseResponse';\n\nexport interface IArrangementRepository {\n  sourceAccountList(request: SourceAccountListRequest): Promise<BaseResponse<SourceAccountListModel>>;\n}\n"], "mappings": "", "ignoreList": []}