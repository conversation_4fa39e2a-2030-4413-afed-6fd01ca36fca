61cb1c4fc8f1edebd6116bb84edf29f3
"use strict";

/* istanbul ignore next */
function cov_11a0sjx3zk() {
  var path = "/Users/<USER>/projects/react-native/payment-module/src/domain/entities/get-my-bill-history-list/GetMyBillHistoryListModel.ts";
  var hash = "3e3201b1922cc6f3623a0996e33bfc7ae44888ee";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/projects/react-native/payment-module/src/domain/entities/get-my-bill-history-list/GetMyBillHistoryListModel.ts",
    statementMap: {
      "0": {
        start: {
          line: 3,
          column: 29
        },
        end: {
          line: 3,
          column: 84
        }
      },
      "1": {
        start: {
          line: 4,
          column: 20
        },
        end: {
          line: 4,
          column: 89
        }
      },
      "2": {
        start: {
          line: 5,
          column: 23
        },
        end: {
          line: 5,
          column: 95
        }
      },
      "3": {
        start: {
          line: 6,
          column: 0
        },
        end: {
          line: 8,
          column: 3
        }
      },
      "4": {
        start: {
          line: 9,
          column: 0
        },
        end: {
          line: 9,
          column: 43
        }
      },
      "5": {
        start: {
          line: 10,
          column: 32
        },
        end: {
          line: 26,
          column: 2
        }
      },
      "6": {
        start: {
          line: 11,
          column: 2
        },
        end: {
          line: 11,
          column: 65
        }
      },
      "7": {
        start: {
          line: 12,
          column: 2
        },
        end: {
          line: 12,
          column: 15
        }
      },
      "8": {
        start: {
          line: 13,
          column: 2
        },
        end: {
          line: 13,
          column: 27
        }
      },
      "9": {
        start: {
          line: 14,
          column: 2
        },
        end: {
          line: 14,
          column: 27
        }
      },
      "10": {
        start: {
          line: 15,
          column: 2
        },
        end: {
          line: 15,
          column: 31
        }
      },
      "11": {
        start: {
          line: 16,
          column: 2
        },
        end: {
          line: 16,
          column: 35
        }
      },
      "12": {
        start: {
          line: 17,
          column: 2
        },
        end: {
          line: 17,
          column: 33
        }
      },
      "13": {
        start: {
          line: 18,
          column: 2
        },
        end: {
          line: 18,
          column: 23
        }
      },
      "14": {
        start: {
          line: 19,
          column: 2
        },
        end: {
          line: 19,
          column: 33
        }
      },
      "15": {
        start: {
          line: 20,
          column: 2
        },
        end: {
          line: 20,
          column: 37
        }
      },
      "16": {
        start: {
          line: 21,
          column: 2
        },
        end: {
          line: 21,
          column: 25
        }
      },
      "17": {
        start: {
          line: 22,
          column: 2
        },
        end: {
          line: 22,
          column: 33
        }
      },
      "18": {
        start: {
          line: 23,
          column: 2
        },
        end: {
          line: 23,
          column: 37
        }
      },
      "19": {
        start: {
          line: 24,
          column: 2
        },
        end: {
          line: 24,
          column: 39
        }
      },
      "20": {
        start: {
          line: 25,
          column: 2
        },
        end: {
          line: 25,
          column: 21
        }
      },
      "21": {
        start: {
          line: 27,
          column: 0
        },
        end: {
          line: 27,
          column: 62
        }
      }
    },
    fnMap: {
      "0": {
        name: "GetMyBillHistoryListModel",
        decl: {
          start: {
            line: 10,
            column: 68
          },
          end: {
            line: 10,
            column: 93
          }
        },
        loc: {
          start: {
            line: 10,
            column: 253
          },
          end: {
            line: 26,
            column: 1
          }
        },
        line: 10
      }
    },
    branchMap: {},
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0
    },
    f: {
      "0": 0
    },
    b: {},
    inputSourceMap: {
      version: 3,
      file: undefined,
      names: ["GetMyBillHistoryListModel", "_createClass2", "default", "id", "billCode", "category", "subGroupId", "customerName", "totalAmount", "period", "paymentDate", "accountNumber", "coreRef", "serviceCode", "arrangementId", "paymentOrderId", "cifNo", "_classCallCheck2", "exports"],
      sourceRoot: undefined,
      sources: ["/Users/<USER>/projects/react-native/payment-module/src/domain/entities/get-my-bill-history-list/GetMyBillHistoryListModel.ts"],
      sourcesContent: ["export interface BillHistoryData {\n  id: string;\n  billCode: string;\n  category: string;\n  subGroupId?: string;\n  customerName?: string;\n  totalAmount: number;\n  period?: string;\n  paymentDate: string;\n  accountNumber: string;\n  coreRef?: string;\n  content?: string;\n  serviceCode: string;\n  arrangementId: string;\n  paymentOrderId: string;\n  cifNo: string;\n  creditDebitIndicator?: 'CRDT' | 'DBIT';\n  transactionAmountCurrency?: {\n    amount: string;\n    currencyCode: string;\n  };\n  creationTime: string;\n  counterPartyName?: string;\n  description?: string;\n}\n\nexport interface BillData {\n  id: string;\n  transName?: string;\n  content?: string;\n  amount: string;\n  transDate: string;\n  creationDate: string;\n}\n\nexport interface BillHistoryDTO {\n  title: string;\n  data: BillData[];\n}\n\nexport interface BillHistoryModel {\n  billHistoryDTO: BillHistoryDTO[];\n  billHistory: BillHistoryData[];\n}\n\nexport class GetMyBillHistoryListModel {\n  constructor(\n    public id: string,\n    public billCode: string,\n    public category: string,\n    public subGroupId?: string,\n    public customerName?: string,\n    public totalAmount?: number,\n    public period?: string,\n    public paymentDate?: string,\n    public accountNumber?: string,\n    public coreRef?: string,\n    public serviceCode?: string,\n    public arrangementId?: string,\n    public paymentOrderId?: string,\n    public cifNo?: string,\n  ) {}\n}\n"],
      mappings: ";;;;;;;;;IA6CaA,yBAAyB,OAAAC,aAAA,CAAAC,OAAA,EACpC,SAAAF,0BACSG,EAAU,EACVC,QAAgB,EAChBC,QAAgB,EAChBC,UAAmB,EACnBC,YAAqB,EACrBC,WAAoB,EACpBC,MAAe,EACfC,WAAoB,EACpBC,aAAsB,EACtBC,OAAgB,EAChBC,WAAoB,EACpBC,aAAsB,EACtBC,cAAuB,EACvBC,KAAc;EAAA,IAAAC,gBAAA,CAAAf,OAAA,QAAAF,yBAAA;EAbd,KAAAG,EAAE,GAAFA,EAAE;EACF,KAAAC,QAAQ,GAARA,QAAQ;EACR,KAAAC,QAAQ,GAARA,QAAQ;EACR,KAAAC,UAAU,GAAVA,UAAU;EACV,KAAAC,YAAY,GAAZA,YAAY;EACZ,KAAAC,WAAW,GAAXA,WAAW;EACX,KAAAC,MAAM,GAANA,MAAM;EACN,KAAAC,WAAW,GAAXA,WAAW;EACX,KAAAC,aAAa,GAAbA,aAAa;EACb,KAAAC,OAAO,GAAPA,OAAO;EACP,KAAAC,WAAW,GAAXA,WAAW;EACX,KAAAC,aAAa,GAAbA,aAAa;EACb,KAAAC,cAAc,GAAdA,cAAc;EACd,KAAAC,KAAK,GAALA,KAAK;AACX,CAAC;AAhBNE,OAAA,CAAAlB,yBAAA,GAAAA,yBAAA",
      ignoreList: []
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "3e3201b1922cc6f3623a0996e33bfc7ae44888ee"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_11a0sjx3zk = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_11a0sjx3zk();
var _interopRequireDefault =
/* istanbul ignore next */
(cov_11a0sjx3zk().s[0]++, require("@babel/runtime/helpers/interopRequireDefault"));
var _createClass2 =
/* istanbul ignore next */
(cov_11a0sjx3zk().s[1]++, _interopRequireDefault(require("@babel/runtime/helpers/createClass")));
var _classCallCheck2 =
/* istanbul ignore next */
(cov_11a0sjx3zk().s[2]++, _interopRequireDefault(require("@babel/runtime/helpers/classCallCheck")));
/* istanbul ignore next */
cov_11a0sjx3zk().s[3]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_11a0sjx3zk().s[4]++;
exports.GetMyBillHistoryListModel = void 0;
var GetMyBillHistoryListModel =
/* istanbul ignore next */
(cov_11a0sjx3zk().s[5]++, (0, _createClass2.default)(function GetMyBillHistoryListModel(id, billCode, category, subGroupId, customerName, totalAmount, period, paymentDate, accountNumber, coreRef, serviceCode, arrangementId, paymentOrderId, cifNo) {
  /* istanbul ignore next */
  cov_11a0sjx3zk().f[0]++;
  cov_11a0sjx3zk().s[6]++;
  (0, _classCallCheck2.default)(this, GetMyBillHistoryListModel);
  /* istanbul ignore next */
  cov_11a0sjx3zk().s[7]++;
  this.id = id;
  /* istanbul ignore next */
  cov_11a0sjx3zk().s[8]++;
  this.billCode = billCode;
  /* istanbul ignore next */
  cov_11a0sjx3zk().s[9]++;
  this.category = category;
  /* istanbul ignore next */
  cov_11a0sjx3zk().s[10]++;
  this.subGroupId = subGroupId;
  /* istanbul ignore next */
  cov_11a0sjx3zk().s[11]++;
  this.customerName = customerName;
  /* istanbul ignore next */
  cov_11a0sjx3zk().s[12]++;
  this.totalAmount = totalAmount;
  /* istanbul ignore next */
  cov_11a0sjx3zk().s[13]++;
  this.period = period;
  /* istanbul ignore next */
  cov_11a0sjx3zk().s[14]++;
  this.paymentDate = paymentDate;
  /* istanbul ignore next */
  cov_11a0sjx3zk().s[15]++;
  this.accountNumber = accountNumber;
  /* istanbul ignore next */
  cov_11a0sjx3zk().s[16]++;
  this.coreRef = coreRef;
  /* istanbul ignore next */
  cov_11a0sjx3zk().s[17]++;
  this.serviceCode = serviceCode;
  /* istanbul ignore next */
  cov_11a0sjx3zk().s[18]++;
  this.arrangementId = arrangementId;
  /* istanbul ignore next */
  cov_11a0sjx3zk().s[19]++;
  this.paymentOrderId = paymentOrderId;
  /* istanbul ignore next */
  cov_11a0sjx3zk().s[20]++;
  this.cifNo = cifNo;
}));
/* istanbul ignore next */
cov_11a0sjx3zk().s[21]++;
exports.GetMyBillHistoryListModel = GetMyBillHistoryListModel;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJjb3ZfMTFhMHNqeDN6ayIsImFjdHVhbENvdmVyYWdlIiwiR2V0TXlCaWxsSGlzdG9yeUxpc3RNb2RlbCIsInMiLCJfY3JlYXRlQ2xhc3MyIiwiZGVmYXVsdCIsImlkIiwiYmlsbENvZGUiLCJjYXRlZ29yeSIsInN1Ykdyb3VwSWQiLCJjdXN0b21lck5hbWUiLCJ0b3RhbEFtb3VudCIsInBlcmlvZCIsInBheW1lbnREYXRlIiwiYWNjb3VudE51bWJlciIsImNvcmVSZWYiLCJzZXJ2aWNlQ29kZSIsImFycmFuZ2VtZW50SWQiLCJwYXltZW50T3JkZXJJZCIsImNpZk5vIiwiZiIsIl9jbGFzc0NhbGxDaGVjazIiLCJleHBvcnRzIl0sInNvdXJjZXMiOlsiL1VzZXJzL3NvbnNhbXNldC9wcm9qZWN0cy9yZWFjdC1uYXRpdmUvcGF5bWVudC1tb2R1bGUvc3JjL2RvbWFpbi9lbnRpdGllcy9nZXQtbXktYmlsbC1oaXN0b3J5LWxpc3QvR2V0TXlCaWxsSGlzdG9yeUxpc3RNb2RlbC50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgaW50ZXJmYWNlIEJpbGxIaXN0b3J5RGF0YSB7XG4gIGlkOiBzdHJpbmc7XG4gIGJpbGxDb2RlOiBzdHJpbmc7XG4gIGNhdGVnb3J5OiBzdHJpbmc7XG4gIHN1Ykdyb3VwSWQ/OiBzdHJpbmc7XG4gIGN1c3RvbWVyTmFtZT86IHN0cmluZztcbiAgdG90YWxBbW91bnQ6IG51bWJlcjtcbiAgcGVyaW9kPzogc3RyaW5nO1xuICBwYXltZW50RGF0ZTogc3RyaW5nO1xuICBhY2NvdW50TnVtYmVyOiBzdHJpbmc7XG4gIGNvcmVSZWY/OiBzdHJpbmc7XG4gIGNvbnRlbnQ/OiBzdHJpbmc7XG4gIHNlcnZpY2VDb2RlOiBzdHJpbmc7XG4gIGFycmFuZ2VtZW50SWQ6IHN0cmluZztcbiAgcGF5bWVudE9yZGVySWQ6IHN0cmluZztcbiAgY2lmTm86IHN0cmluZztcbiAgY3JlZGl0RGViaXRJbmRpY2F0b3I/OiAnQ1JEVCcgfCAnREJJVCc7XG4gIHRyYW5zYWN0aW9uQW1vdW50Q3VycmVuY3k/OiB7XG4gICAgYW1vdW50OiBzdHJpbmc7XG4gICAgY3VycmVuY3lDb2RlOiBzdHJpbmc7XG4gIH07XG4gIGNyZWF0aW9uVGltZTogc3RyaW5nO1xuICBjb3VudGVyUGFydHlOYW1lPzogc3RyaW5nO1xuICBkZXNjcmlwdGlvbj86IHN0cmluZztcbn1cblxuZXhwb3J0IGludGVyZmFjZSBCaWxsRGF0YSB7XG4gIGlkOiBzdHJpbmc7XG4gIHRyYW5zTmFtZT86IHN0cmluZztcbiAgY29udGVudD86IHN0cmluZztcbiAgYW1vdW50OiBzdHJpbmc7XG4gIHRyYW5zRGF0ZTogc3RyaW5nO1xuICBjcmVhdGlvbkRhdGU6IHN0cmluZztcbn1cblxuZXhwb3J0IGludGVyZmFjZSBCaWxsSGlzdG9yeURUTyB7XG4gIHRpdGxlOiBzdHJpbmc7XG4gIGRhdGE6IEJpbGxEYXRhW107XG59XG5cbmV4cG9ydCBpbnRlcmZhY2UgQmlsbEhpc3RvcnlNb2RlbCB7XG4gIGJpbGxIaXN0b3J5RFRPOiBCaWxsSGlzdG9yeURUT1tdO1xuICBiaWxsSGlzdG9yeTogQmlsbEhpc3RvcnlEYXRhW107XG59XG5cbmV4cG9ydCBjbGFzcyBHZXRNeUJpbGxIaXN0b3J5TGlzdE1vZGVsIHtcbiAgY29uc3RydWN0b3IoXG4gICAgcHVibGljIGlkOiBzdHJpbmcsXG4gICAgcHVibGljIGJpbGxDb2RlOiBzdHJpbmcsXG4gICAgcHVibGljIGNhdGVnb3J5OiBzdHJpbmcsXG4gICAgcHVibGljIHN1Ykdyb3VwSWQ/OiBzdHJpbmcsXG4gICAgcHVibGljIGN1c3RvbWVyTmFtZT86IHN0cmluZyxcbiAgICBwdWJsaWMgdG90YWxBbW91bnQ/OiBudW1iZXIsXG4gICAgcHVibGljIHBlcmlvZD86IHN0cmluZyxcbiAgICBwdWJsaWMgcGF5bWVudERhdGU/OiBzdHJpbmcsXG4gICAgcHVibGljIGFjY291bnROdW1iZXI/OiBzdHJpbmcsXG4gICAgcHVibGljIGNvcmVSZWY/OiBzdHJpbmcsXG4gICAgcHVibGljIHNlcnZpY2VDb2RlPzogc3RyaW5nLFxuICAgIHB1YmxpYyBhcnJhbmdlbWVudElkPzogc3RyaW5nLFxuICAgIHB1YmxpYyBwYXltZW50T3JkZXJJZD86IHN0cmluZyxcbiAgICBwdWJsaWMgY2lmTm8/OiBzdHJpbmcsXG4gICkge31cbn1cbiJdLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0lBbURXO0lBQUFBLGNBQUEsWUFBQUEsQ0FBQTtNQUFBLE9BQUFDLGNBQUE7SUFBQTtFQUFBO0VBQUEsT0FBQUEsY0FBQTtBQUFBO0FBQUFELGNBQUE7Ozs7Ozs7Ozs7Ozs7Ozs7OztJQU5FRSx5QkFBeUI7QUFBQTtBQUFBLENBQUFGLGNBQUEsR0FBQUcsQ0FBQSxXQUFBQyxhQUFBLENBQUFDLE9BQUEsRUFDcEMsU0FBQUgsMEJBQ1NJLEVBQVUsRUFDVkMsUUFBZ0IsRUFDaEJDLFFBQWdCLEVBQ2hCQyxVQUFtQixFQUNuQkMsWUFBcUIsRUFDckJDLFdBQW9CLEVBQ3BCQyxNQUFlLEVBQ2ZDLFdBQW9CLEVBQ3BCQyxhQUFzQixFQUN0QkMsT0FBZ0IsRUFDaEJDLFdBQW9CLEVBQ3BCQyxhQUFzQixFQUN0QkMsY0FBdUIsRUFDdkJDLEtBQWM7RUFBQTtFQUFBbkIsY0FBQSxHQUFBb0IsQ0FBQTtFQUFBcEIsY0FBQSxHQUFBRyxDQUFBO0VBQUEsSUFBQWtCLGdCQUFBLENBQUFoQixPQUFBLFFBQUFILHlCQUFBO0VBQUE7RUFBQUYsY0FBQSxHQUFBRyxDQUFBO0VBYmQsS0FBQUcsRUFBRSxHQUFGQSxFQUFFO0VBQUE7RUFBQU4sY0FBQSxHQUFBRyxDQUFBO0VBQ0YsS0FBQUksUUFBUSxHQUFSQSxRQUFRO0VBQUE7RUFBQVAsY0FBQSxHQUFBRyxDQUFBO0VBQ1IsS0FBQUssUUFBUSxHQUFSQSxRQUFRO0VBQUE7RUFBQVIsY0FBQSxHQUFBRyxDQUFBO0VBQ1IsS0FBQU0sVUFBVSxHQUFWQSxVQUFVO0VBQUE7RUFBQVQsY0FBQSxHQUFBRyxDQUFBO0VBQ1YsS0FBQU8sWUFBWSxHQUFaQSxZQUFZO0VBQUE7RUFBQVYsY0FBQSxHQUFBRyxDQUFBO0VBQ1osS0FBQVEsV0FBVyxHQUFYQSxXQUFXO0VBQUE7RUFBQVgsY0FBQSxHQUFBRyxDQUFBO0VBQ1gsS0FBQVMsTUFBTSxHQUFOQSxNQUFNO0VBQUE7RUFBQVosY0FBQSxHQUFBRyxDQUFBO0VBQ04sS0FBQVUsV0FBVyxHQUFYQSxXQUFXO0VBQUE7RUFBQWIsY0FBQSxHQUFBRyxDQUFBO0VBQ1gsS0FBQVcsYUFBYSxHQUFiQSxhQUFhO0VBQUE7RUFBQWQsY0FBQSxHQUFBRyxDQUFBO0VBQ2IsS0FBQVksT0FBTyxHQUFQQSxPQUFPO0VBQUE7RUFBQWYsY0FBQSxHQUFBRyxDQUFBO0VBQ1AsS0FBQWEsV0FBVyxHQUFYQSxXQUFXO0VBQUE7RUFBQWhCLGNBQUEsR0FBQUcsQ0FBQTtFQUNYLEtBQUFjLGFBQWEsR0FBYkEsYUFBYTtFQUFBO0VBQUFqQixjQUFBLEdBQUFHLENBQUE7RUFDYixLQUFBZSxjQUFjLEdBQWRBLGNBQWM7RUFBQTtFQUFBbEIsY0FBQSxHQUFBRyxDQUFBO0VBQ2QsS0FBQWdCLEtBQUssR0FBTEEsS0FBSztBQUNYLENBQUM7QUFBQTtBQUFBbkIsY0FBQSxHQUFBRyxDQUFBO0FBaEJObUIsT0FBQSxDQUFBcEIseUJBQUEsR0FBQUEseUJBQUEiLCJpZ25vcmVMaXN0IjpbXX0=