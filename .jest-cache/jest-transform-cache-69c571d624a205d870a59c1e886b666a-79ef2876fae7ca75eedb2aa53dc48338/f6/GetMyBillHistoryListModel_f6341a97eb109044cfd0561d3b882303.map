{"version": 3, "names": ["cov_11a0sjx3zk", "actualCoverage", "GetMyBillHistoryListModel", "s", "_createClass2", "default", "id", "billCode", "category", "subGroupId", "customerName", "totalAmount", "period", "paymentDate", "accountNumber", "coreRef", "serviceCode", "arrangementId", "paymentOrderId", "cifNo", "f", "_classCallCheck2", "exports"], "sources": ["/Users/<USER>/projects/react-native/payment-module/src/domain/entities/get-my-bill-history-list/GetMyBillHistoryListModel.ts"], "sourcesContent": ["export interface BillHistoryData {\n  id: string;\n  billCode: string;\n  category: string;\n  subGroupId?: string;\n  customerName?: string;\n  totalAmount: number;\n  period?: string;\n  paymentDate: string;\n  accountNumber: string;\n  coreRef?: string;\n  content?: string;\n  serviceCode: string;\n  arrangementId: string;\n  paymentOrderId: string;\n  cifNo: string;\n  creditDebitIndicator?: 'CRDT' | 'DBIT';\n  transactionAmountCurrency?: {\n    amount: string;\n    currencyCode: string;\n  };\n  creationTime: string;\n  counterPartyName?: string;\n  description?: string;\n}\n\nexport interface BillData {\n  id: string;\n  transName?: string;\n  content?: string;\n  amount: string;\n  transDate: string;\n  creationDate: string;\n}\n\nexport interface BillHistoryDTO {\n  title: string;\n  data: BillData[];\n}\n\nexport interface BillHistoryModel {\n  billHistoryDTO: BillHistoryDTO[];\n  billHistory: BillHistoryData[];\n}\n\nexport class GetMyBillHistoryListModel {\n  constructor(\n    public id: string,\n    public billCode: string,\n    public category: string,\n    public subGroupId?: string,\n    public customerName?: string,\n    public totalAmount?: number,\n    public period?: string,\n    public paymentDate?: string,\n    public accountNumber?: string,\n    public coreRef?: string,\n    public serviceCode?: string,\n    public arrangementId?: string,\n    public paymentOrderId?: string,\n    public cifNo?: string,\n  ) {}\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAmDW;IAAAA,cAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,cAAA;;;;;;;;;;;;;;;;;;IANEE,yBAAyB;AAAA;AAAA,CAAAF,cAAA,GAAAG,CAAA,WAAAC,aAAA,CAAAC,OAAA,EACpC,SAAAH,0BACSI,EAAU,EACVC,QAAgB,EAChBC,QAAgB,EAChBC,UAAmB,EACnBC,YAAqB,EACrBC,WAAoB,EACpBC,MAAe,EACfC,WAAoB,EACpBC,aAAsB,EACtBC,OAAgB,EAChBC,WAAoB,EACpBC,aAAsB,EACtBC,cAAuB,EACvBC,KAAc;EAAA;EAAAnB,cAAA,GAAAoB,CAAA;EAAApB,cAAA,GAAAG,CAAA;EAAA,IAAAkB,gBAAA,CAAAhB,OAAA,QAAAH,yBAAA;EAAA;EAAAF,cAAA,GAAAG,CAAA;EAbd,KAAAG,EAAE,GAAFA,EAAE;EAAA;EAAAN,cAAA,GAAAG,CAAA;EACF,KAAAI,QAAQ,GAARA,QAAQ;EAAA;EAAAP,cAAA,GAAAG,CAAA;EACR,KAAAK,QAAQ,GAARA,QAAQ;EAAA;EAAAR,cAAA,GAAAG,CAAA;EACR,KAAAM,UAAU,GAAVA,UAAU;EAAA;EAAAT,cAAA,GAAAG,CAAA;EACV,KAAAO,YAAY,GAAZA,YAAY;EAAA;EAAAV,cAAA,GAAAG,CAAA;EACZ,KAAAQ,WAAW,GAAXA,WAAW;EAAA;EAAAX,cAAA,GAAAG,CAAA;EACX,KAAAS,MAAM,GAANA,MAAM;EAAA;EAAAZ,cAAA,GAAAG,CAAA;EACN,KAAAU,WAAW,GAAXA,WAAW;EAAA;EAAAb,cAAA,GAAAG,CAAA;EACX,KAAAW,aAAa,GAAbA,aAAa;EAAA;EAAAd,cAAA,GAAAG,CAAA;EACb,KAAAY,OAAO,GAAPA,OAAO;EAAA;EAAAf,cAAA,GAAAG,CAAA;EACP,KAAAa,WAAW,GAAXA,WAAW;EAAA;EAAAhB,cAAA,GAAAG,CAAA;EACX,KAAAc,aAAa,GAAbA,aAAa;EAAA;EAAAjB,cAAA,GAAAG,CAAA;EACb,KAAAe,cAAc,GAAdA,cAAc;EAAA;EAAAlB,cAAA,GAAAG,CAAA;EACd,KAAAgB,KAAK,GAALA,KAAK;AACX,CAAC;AAAA;AAAAnB,cAAA,GAAAG,CAAA;AAhBNmB,OAAA,CAAApB,yBAAA,GAAAA,yBAAA", "ignoreList": []}