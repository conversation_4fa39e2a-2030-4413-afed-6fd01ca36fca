008caa349808c1e3ef2c8c025d8cdf1c
"use strict";

/* istanbul ignore next */
function cov_240buoq3h5() {
  var path = "/Users/<USER>/projects/react-native/payment-module/src/data/models/source-account-list/SourceAccountListRequest.ts";
  var hash = "b8c6ff3b78693019aa04d8e113007e469094b849";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/projects/react-native/payment-module/src/data/models/source-account-list/SourceAccountListRequest.ts",
    statementMap: {
      "0": {
        start: {
          line: 3,
          column: 0
        },
        end: {
          line: 5,
          column: 3
        }
      }
    },
    fnMap: {},
    branchMap: {},
    s: {
      "0": 0
    },
    f: {},
    b: {},
    inputSourceMap: {
      version: 3,
      file: undefined,
      names: [],
      sourceRoot: undefined,
      sources: ["/Users/<USER>/projects/react-native/payment-module/src/data/models/source-account-list/SourceAccountListRequest.ts"],
      sourcesContent: ["export interface SourceAccountListRequest {\n  externalStateIds: string[];\n  externalProductKindIds: string[];\n  currency: string;\n}\n"],
      mappings: "",
      ignoreList: []
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "b8c6ff3b78693019aa04d8e113007e469094b849"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_240buoq3h5 = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_240buoq3h5();
cov_240buoq3h5().s[0]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6W10sInNvdXJjZXMiOlsiL1VzZXJzL3NvbnNhbXNldC9wcm9qZWN0cy9yZWFjdC1uYXRpdmUvcGF5bWVudC1tb2R1bGUvc3JjL2RhdGEvbW9kZWxzL3NvdXJjZS1hY2NvdW50LWxpc3QvU291cmNlQWNjb3VudExpc3RSZXF1ZXN0LnRzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBpbnRlcmZhY2UgU291cmNlQWNjb3VudExpc3RSZXF1ZXN0IHtcbiAgZXh0ZXJuYWxTdGF0ZUlkczogc3RyaW5nW107XG4gIGV4dGVybmFsUHJvZHVjdEtpbmRJZHM6IHN0cmluZ1tdO1xuICBjdXJyZW5jeTogc3RyaW5nO1xufVxuIl0sIm1hcHBpbmdzIjoiIiwiaWdub3JlTGlzdCI6W119