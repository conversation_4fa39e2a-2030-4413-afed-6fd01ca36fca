457960ab1c813774ebdcc99f253f9c74
"use strict";

/* istanbul ignore next */
function cov_obi5uywjh() {
  var path = "/Users/<USER>/projects/react-native/payment-module/src/data/repositories/ArrangementRepository.ts";
  var hash = "b6595b5a64e20fbcacc8e337f0d44fbc30c626e4";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/projects/react-native/payment-module/src/data/repositories/ArrangementRepository.ts",
    statementMap: {
      "0": {
        start: {
          line: 3,
          column: 29
        },
        end: {
          line: 3,
          column: 84
        }
      },
      "1": {
        start: {
          line: 4,
          column: 25
        },
        end: {
          line: 4,
          column: 99
        }
      },
      "2": {
        start: {
          line: 5,
          column: 23
        },
        end: {
          line: 5,
          column: 95
        }
      },
      "3": {
        start: {
          line: 6,
          column: 20
        },
        end: {
          line: 6,
          column: 89
        }
      },
      "4": {
        start: {
          line: 7,
          column: 0
        },
        end: {
          line: 9,
          column: 3
        }
      },
      "5": {
        start: {
          line: 10,
          column: 0
        },
        end: {
          line: 10,
          column: 39
        }
      },
      "6": {
        start: {
          line: 11,
          column: 32
        },
        end: {
          line: 11,
          column: 97
        }
      },
      "7": {
        start: {
          line: 12,
          column: 19
        },
        end: {
          line: 12,
          column: 52
        }
      },
      "8": {
        start: {
          line: 13,
          column: 28
        },
        end: {
          line: 30,
          column: 3
        }
      },
      "9": {
        start: {
          line: 15,
          column: 4
        },
        end: {
          line: 15,
          column: 63
        }
      },
      "10": {
        start: {
          line: 16,
          column: 4
        },
        end: {
          line: 16,
          column: 45
        }
      },
      "11": {
        start: {
          line: 18,
          column: 2
        },
        end: {
          line: 29,
          column: 6
        }
      },
      "12": {
        start: {
          line: 21,
          column: 31
        },
        end: {
          line: 23,
          column: 8
        }
      },
      "13": {
        start: {
          line: 22,
          column: 8
        },
        end: {
          line: 22,
          column: 157
        }
      },
      "14": {
        start: {
          line: 25,
          column: 8
        },
        end: {
          line: 25,
          column: 57
        }
      },
      "15": {
        start: {
          line: 27,
          column: 6
        },
        end: {
          line: 27,
          column: 31
        }
      },
      "16": {
        start: {
          line: 31,
          column: 0
        },
        end: {
          line: 31,
          column: 54
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 13,
            column: 28
          },
          end: {
            line: 13,
            column: 29
          }
        },
        loc: {
          start: {
            line: 13,
            column: 40
          },
          end: {
            line: 30,
            column: 1
          }
        },
        line: 13
      },
      "1": {
        name: "ArrangementRepository",
        decl: {
          start: {
            line: 14,
            column: 11
          },
          end: {
            line: 14,
            column: 32
          }
        },
        loc: {
          start: {
            line: 14,
            column: 51
          },
          end: {
            line: 17,
            column: 3
          }
        },
        line: 14
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 20,
            column: 11
          },
          end: {
            line: 20,
            column: 12
          }
        },
        loc: {
          start: {
            line: 20,
            column: 23
          },
          end: {
            line: 28,
            column: 5
          }
        },
        line: 20
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 21,
            column: 63
          },
          end: {
            line: 21,
            column: 64
          }
        },
        loc: {
          start: {
            line: 21,
            column: 83
          },
          end: {
            line: 23,
            column: 7
          }
        },
        line: 21
      },
      "4": {
        name: "sourceAccountList",
        decl: {
          start: {
            line: 24,
            column: 15
          },
          end: {
            line: 24,
            column: 32
          }
        },
        loc: {
          start: {
            line: 24,
            column: 37
          },
          end: {
            line: 26,
            column: 7
          }
        },
        line: 24
      }
    },
    branchMap: {},
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0
    },
    b: {},
    inputSourceMap: {
      version: 3,
      file: undefined,
      names: ["SourceAccountListMapper_1", "require", "HandleData_1", "ArrangementRepository", "remoteDataSource", "_classCallCheck2", "default", "_createClass2", "key", "value", "_sourceAccountList", "_asyncToGenerator2", "request", "handleData", "sourceAccountList", "mapSourceAccountListResponseToModel", "_x", "apply", "arguments", "exports"],
      sourceRoot: undefined,
      sources: ["/Users/<USER>/projects/react-native/payment-module/src/data/repositories/ArrangementRepository.ts"],
      sourcesContent: ["import {mapSourceAccountListResponseToModel} from '../mappers/source-account-list/SourceAccountListMapper';\nimport {handleData} from '../../utils/HandleData';\nimport {SourceAccountListModel} from '../../domain/entities/source-account-list/SourceAccountListModel';\nimport {SourceAccountListRequest} from '../models/source-account-list/SourceAccountListRequest';\nimport {IArrangementDataSource} from '../datasources/IArrangementDataSource';\nimport {BaseResponse} from '../../core/BaseResponse';\nimport {IArrangementRepository} from '../../domain/repositories/IArrangementRepository';\n\nexport class ArrangementRepository implements IArrangementRepository {\n  private remoteDataSource: IArrangementDataSource;\n\n  constructor(remoteDataSource: IArrangementDataSource) {\n    this.remoteDataSource = remoteDataSource;\n  }\n\n  async sourceAccountList(request: SourceAccountListRequest): Promise<BaseResponse<SourceAccountListModel>> {\n    return handleData<SourceAccountListModel>(\n      this.remoteDataSource.sourceAccountList(request),\n      mapSourceAccountListResponseToModel,\n    );\n  }\n}\n"],
      mappings: ";;;;;;;;;;AAAA,IAAAA,yBAAA,GAAAC,OAAA;AACA,IAAAC,YAAA,GAAAD,OAAA;AAAkD,IAOrCE,qBAAqB;EAGhC,SAAAA,sBAAYC,gBAAwC;IAAA,IAAAC,gBAAA,CAAAC,OAAA,QAAAH,qBAAA;IAClD,IAAI,CAACC,gBAAgB,GAAGA,gBAAgB;EAC1C;EAAC,WAAAG,aAAA,CAAAD,OAAA,EAAAH,qBAAA;IAAAK,GAAA;IAAAC,KAAA;MAAA,IAAAC,kBAAA,OAAAC,kBAAA,CAAAL,OAAA,EAED,WAAwBM,OAAiC;QACvD,OAAO,IAAAV,YAAA,CAAAW,UAAU,EACf,IAAI,CAACT,gBAAgB,CAACU,iBAAiB,CAACF,OAAO,CAAC,EAChDZ,yBAAA,CAAAe,mCAAmC,CACpC;MACH,CAAC;MAAA,SALKD,iBAAiBA,CAAAE,EAAA;QAAA,OAAAN,kBAAA,CAAAO,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAjBJ,iBAAiB;IAAA;EAAA;AAAA;AAPzBK,OAAA,CAAAhB,qBAAA,GAAAA,qBAAA",
      ignoreList: []
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "b6595b5a64e20fbcacc8e337f0d44fbc30c626e4"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_obi5uywjh = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_obi5uywjh();
var _interopRequireDefault =
/* istanbul ignore next */
(cov_obi5uywjh().s[0]++, require("@babel/runtime/helpers/interopRequireDefault"));
var _asyncToGenerator2 =
/* istanbul ignore next */
(cov_obi5uywjh().s[1]++, _interopRequireDefault(require("@babel/runtime/helpers/asyncToGenerator")));
var _classCallCheck2 =
/* istanbul ignore next */
(cov_obi5uywjh().s[2]++, _interopRequireDefault(require("@babel/runtime/helpers/classCallCheck")));
var _createClass2 =
/* istanbul ignore next */
(cov_obi5uywjh().s[3]++, _interopRequireDefault(require("@babel/runtime/helpers/createClass")));
/* istanbul ignore next */
cov_obi5uywjh().s[4]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_obi5uywjh().s[5]++;
exports.ArrangementRepository = void 0;
var SourceAccountListMapper_1 =
/* istanbul ignore next */
(cov_obi5uywjh().s[6]++, require("../mappers/source-account-list/SourceAccountListMapper"));
var HandleData_1 =
/* istanbul ignore next */
(cov_obi5uywjh().s[7]++, require("../../utils/HandleData"));
var ArrangementRepository =
/* istanbul ignore next */
(cov_obi5uywjh().s[8]++, function () {
  /* istanbul ignore next */
  cov_obi5uywjh().f[0]++;
  function ArrangementRepository(remoteDataSource) {
    /* istanbul ignore next */
    cov_obi5uywjh().f[1]++;
    cov_obi5uywjh().s[9]++;
    (0, _classCallCheck2.default)(this, ArrangementRepository);
    /* istanbul ignore next */
    cov_obi5uywjh().s[10]++;
    this.remoteDataSource = remoteDataSource;
  }
  /* istanbul ignore next */
  cov_obi5uywjh().s[11]++;
  return (0, _createClass2.default)(ArrangementRepository, [{
    key: "sourceAccountList",
    value: function () {
      /* istanbul ignore next */
      cov_obi5uywjh().f[2]++;
      var _sourceAccountList =
      /* istanbul ignore next */
      (cov_obi5uywjh().s[12]++, (0, _asyncToGenerator2.default)(function* (request) {
        /* istanbul ignore next */
        cov_obi5uywjh().f[3]++;
        cov_obi5uywjh().s[13]++;
        return (0, HandleData_1.handleData)(this.remoteDataSource.sourceAccountList(request), SourceAccountListMapper_1.mapSourceAccountListResponseToModel);
      }));
      function sourceAccountList(_x) {
        /* istanbul ignore next */
        cov_obi5uywjh().f[4]++;
        cov_obi5uywjh().s[14]++;
        return _sourceAccountList.apply(this, arguments);
      }
      /* istanbul ignore next */
      cov_obi5uywjh().s[15]++;
      return sourceAccountList;
    }()
  }]);
}());
/* istanbul ignore next */
cov_obi5uywjh().s[16]++;
exports.ArrangementRepository = ArrangementRepository;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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