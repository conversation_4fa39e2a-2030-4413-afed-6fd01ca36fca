{"version": 3, "names": ["cov_obi5uywjh", "actualCoverage", "SourceAccountListMapper_1", "s", "require", "HandleData_1", "ArrangementRepository", "f", "remoteDataSource", "_classCallCheck2", "default", "_createClass2", "key", "value", "_sourceAccountList", "_asyncToGenerator2", "request", "handleData", "sourceAccountList", "mapSourceAccountListResponseToModel", "_x", "apply", "arguments", "exports"], "sources": ["/Users/<USER>/projects/react-native/payment-module/src/data/repositories/ArrangementRepository.ts"], "sourcesContent": ["import {mapSourceAccountListResponseToModel} from '../mappers/source-account-list/SourceAccountListMapper';\nimport {handleData} from '../../utils/HandleData';\nimport {SourceAccountListModel} from '../../domain/entities/source-account-list/SourceAccountListModel';\nimport {SourceAccountListRequest} from '../models/source-account-list/SourceAccountListRequest';\nimport {IArrangementDataSource} from '../datasources/IArrangementDataSource';\nimport {BaseResponse} from '../../core/BaseResponse';\nimport {IArrangementRepository} from '../../domain/repositories/IArrangementRepository';\n\nexport class ArrangementRepository implements IArrangementRepository {\n  private remoteDataSource: IArrangementDataSource;\n\n  constructor(remoteDataSource: IArrangementDataSource) {\n    this.remoteDataSource = remoteDataSource;\n  }\n\n  async sourceAccountList(request: SourceAccountListRequest): Promise<BaseResponse<SourceAccountListModel>> {\n    return handleData<SourceAccountListModel>(\n      this.remoteDataSource.sourceAccountList(request),\n      mapSourceAccountListResponseToModel,\n    );\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAYS;IAAAA,aAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,aAAA;;;;;;;;;;;;;;;;;;;;;AAZT,IAAAE,yBAAA;AAAA;AAAA,CAAAF,aAAA,GAAAG,CAAA,OAAAC,OAAA;AACA,IAAAC,YAAA;AAAA;AAAA,CAAAL,aAAA,GAAAG,CAAA,OAAAC,OAAA;AAAkD,IAOrCE,qBAAqB;AAAA;AAAA,CAAAN,aAAA,GAAAG,CAAA;EAAA;EAAAH,aAAA,GAAAO,CAAA;EAGhC,SAAAD,sBAAYE,gBAAwC;IAAA;IAAAR,aAAA,GAAAO,CAAA;IAAAP,aAAA,GAAAG,CAAA;IAAA,IAAAM,gBAAA,CAAAC,OAAA,QAAAJ,qBAAA;IAAA;IAAAN,aAAA,GAAAG,CAAA;IAClD,IAAI,CAACK,gBAAgB,GAAGA,gBAAgB;EAC1C;EAAA;EAAAR,aAAA,GAAAG,CAAA;EAAC,WAAAQ,aAAA,CAAAD,OAAA,EAAAJ,qBAAA;IAAAM,GAAA;IAAAC,KAAA;MAAA;MAAAb,aAAA,GAAAO,CAAA;MAAA,IAAAO,kBAAA;MAAA;MAAA,CAAAd,aAAA,GAAAG,CAAA,YAAAY,kBAAA,CAAAL,OAAA,EAED,WAAwBM,OAAiC;QAAA;QAAAhB,aAAA,GAAAO,CAAA;QAAAP,aAAA,GAAAG,CAAA;QACvD,OAAO,IAAAE,YAAA,CAAAY,UAAU,EACf,IAAI,CAACT,gBAAgB,CAACU,iBAAiB,CAACF,OAAO,CAAC,EAChDd,yBAAA,CAAAiB,mCAAmC,CACpC;MACH,CAAC;MAAA,SALKD,iBAAiBA,CAAAE,EAAA;QAAA;QAAApB,aAAA,GAAAO,CAAA;QAAAP,aAAA,GAAAG,CAAA;QAAA,OAAAW,kBAAA,CAAAO,KAAA,OAAAC,SAAA;MAAA;MAAA;MAAAtB,aAAA,GAAAG,CAAA;MAAA,OAAjBe,iBAAiB;IAAA;EAAA;AAAA;AAAA;AAAAlB,aAAA,GAAAG,CAAA;AAPzBoB,OAAA,CAAAjB,qBAAA,GAAAA,qBAAA", "ignoreList": []}