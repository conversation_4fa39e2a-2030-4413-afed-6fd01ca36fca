{"version": 3, "names": [], "sources": ["/Users/<USER>/projects/react-native/payment-module/src/components/source-account-list/types.ts"], "sourcesContent": ["import {ViewStyle} from 'react-native';\nimport {SourceAccountModel} from '../../domain/entities/source-account-list/SourceAccountListModel';\n\nexport type SourceAccountListProps = {\n  style?: ViewStyle;\n\n  onSelectAcount: (acc?: SourceAccountModel) => void;\n  accountList: SourceAccountModel[] | undefined;\n  accSelected?: SourceAccountModel;\n  onClose?: () => void;\n};\n"], "mappings": "", "ignoreList": []}