1dcc69206dcd10c4201d7d1e4cfaa5c4
"use strict";

/* istanbul ignore next */
function cov_2hglb0akom() {
  var path = "/Users/<USER>/projects/react-native/payment-module/src/components/source-account-list/types.ts";
  var hash = "82df54405c0ee0a746d393e80e3906b01e7ad3e5";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/projects/react-native/payment-module/src/components/source-account-list/types.ts",
    statementMap: {
      "0": {
        start: {
          line: 3,
          column: 0
        },
        end: {
          line: 5,
          column: 3
        }
      }
    },
    fnMap: {},
    branchMap: {},
    s: {
      "0": 0
    },
    f: {},
    b: {},
    inputSourceMap: {
      version: 3,
      file: undefined,
      names: [],
      sourceRoot: undefined,
      sources: ["/Users/<USER>/projects/react-native/payment-module/src/components/source-account-list/types.ts"],
      sourcesContent: ["import {ViewStyle} from 'react-native';\nimport {SourceAccountModel} from '../../domain/entities/source-account-list/SourceAccountListModel';\n\nexport type SourceAccountListProps = {\n  style?: ViewStyle;\n\n  onSelectAcount: (acc?: SourceAccountModel) => void;\n  accountList: SourceAccountModel[] | undefined;\n  accSelected?: SourceAccountModel;\n  onClose?: () => void;\n};\n"],
      mappings: "",
      ignoreList: []
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "82df54405c0ee0a746d393e80e3906b01e7ad3e5"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_2hglb0akom = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_2hglb0akom();
cov_2hglb0akom().s[0]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6W10sInNvdXJjZXMiOlsiL1VzZXJzL3NvbnNhbXNldC9wcm9qZWN0cy9yZWFjdC1uYXRpdmUvcGF5bWVudC1tb2R1bGUvc3JjL2NvbXBvbmVudHMvc291cmNlLWFjY291bnQtbGlzdC90eXBlcy50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQge1ZpZXdTdHlsZX0gZnJvbSAncmVhY3QtbmF0aXZlJztcbmltcG9ydCB7U291cmNlQWNjb3VudE1vZGVsfSBmcm9tICcuLi8uLi9kb21haW4vZW50aXRpZXMvc291cmNlLWFjY291bnQtbGlzdC9Tb3VyY2VBY2NvdW50TGlzdE1vZGVsJztcblxuZXhwb3J0IHR5cGUgU291cmNlQWNjb3VudExpc3RQcm9wcyA9IHtcbiAgc3R5bGU/OiBWaWV3U3R5bGU7XG5cbiAgb25TZWxlY3RBY291bnQ6IChhY2M/OiBTb3VyY2VBY2NvdW50TW9kZWwpID0+IHZvaWQ7XG4gIGFjY291bnRMaXN0OiBTb3VyY2VBY2NvdW50TW9kZWxbXSB8IHVuZGVmaW5lZDtcbiAgYWNjU2VsZWN0ZWQ/OiBTb3VyY2VBY2NvdW50TW9kZWw7XG4gIG9uQ2xvc2U/OiAoKSA9PiB2b2lkO1xufTtcbiJdLCJtYXBwaW5ncyI6IiIsImlnbm9yZUxpc3QiOltdfQ==