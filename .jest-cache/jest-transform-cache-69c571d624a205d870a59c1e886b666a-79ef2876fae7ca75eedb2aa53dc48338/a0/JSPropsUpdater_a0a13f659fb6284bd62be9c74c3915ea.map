{"version": 3, "names": ["_interopRequireDefault", "require", "Object", "defineProperty", "exports", "value", "default", "_classCallCheck2", "_createClass2", "_reactNative", "_PlatformChecker", "_threads", "_NativeReanimatedModule", "SHOULD_BE_USE_WEB", "shouldBeUseWeb", "JSPropsUpdaterPaper", "_reanimatedEventEmitter", "NativeEventEmitter", "Platform", "OS", "NativeReanimatedModule", "undefined", "key", "addOnJSPropsChangeListener", "animatedComponent", "viewTag", "getComponentViewTag", "_tagToComponentMapping", "set", "size", "listener", "data", "component", "get", "_updateFromNative", "props", "addListener", "removeOnJSPropsChangeListener", "delete", "removeAllListeners", "Map", "JSPropsUpdaterFabric", "isInitialized", "updater", "runOnUIImmediately", "global", "updateJSProps", "runOnJS", "JSPropsUpdaterWeb", "_animatedComponent", "JSPropsUpdater", "_IS_FABRIC", "_default"], "sources": ["../../../src/createAnimatedComponent/JSPropsUpdater.ts"], "sourcesContent": [null], "mappings": "AAAA,YAAY;;AAAA,IAAAA,sBAAA,GAAAC,OAAA;AAAAC,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,KAAA;AAAA;AAAAD,OAAA,CAAAE,OAAA;AAAA,IAAAC,gBAAA,GAAAP,sBAAA,CAAAC,OAAA;AAAA,IAAAO,aAAA,GAAAR,sBAAA,CAAAC,OAAA;AACZ,IAAAQ,YAAA,GAAAR,OAAA;AAEA,IAAAS,gBAAA,GAAAT,OAAA;AAEA,IAAAU,QAAA,GAAAV,OAAA;AAOA,IAAAW,uBAAA,GAAAZ,sBAAA,CAAAC,OAAA;AAOA,IAAMY,iBAAiB,GAAG,IAAAC,+BAAc,EAAC,CAAC;AAAA,IAEpCC,mBAAmB;EAIvB,SAAAA,oBAAA,EAAc;IAAA,IAAAR,gBAAA,CAAAD,OAAA,QAAAS,mBAAA;IACZ,IAAI,CAACC,uBAAuB,GAAG,IAAIC,+BAAkB,CAEnDC,qBAAQ,CAACC,EAAE,KAAK,KAAK,IAAID,qBAAQ,CAACC,EAAE,KAAK,OAAO,GAC3CC,+BAAsB,GACvBC,SACN,CAAC;EACH;EAAA,WAAAb,aAAA,CAAAF,OAAA,EAAAS,mBAAA;IAAAO,GAAA;IAAAjB,KAAA,EAEO,SAAAkB,0BAA0BA,CAC/BC,iBAG4B,EAC5B;MACA,IAAMC,OAAO,GAAGD,iBAAiB,CAACE,mBAAmB,CAAC,CAAC;MACvDX,mBAAmB,CAACY,sBAAsB,CAACC,GAAG,CAACH,OAAO,EAAED,iBAAiB,CAAC;MAC1E,IAAIT,mBAAmB,CAACY,sBAAsB,CAACE,IAAI,KAAK,CAAC,EAAE;QACzD,IAAMC,QAAQ,GAAI,SAAZA,QAAQA,CAAIC,IAAkB,EAAK;UACvC,IAAMC,SAAS,GAAGjB,mBAAmB,CAACY,sBAAsB,CAACM,GAAG,CAC9DF,IAAI,CAACN,OACP,CAAC;UACDO,SAAS,YAATA,SAAS,CAAEE,iBAAiB,CAACH,IAAI,CAACI,KAAK,CAAC;QAC1C,CAAC;QACD,IAAI,CAACnB,uBAAuB,CAACoB,WAAW,CACtC,yBAAyB,EACzBN,QACF,CAAC;MACH;IACF;EAAA;IAAAR,GAAA;IAAAjB,KAAA,EAEO,SAAAgC,6BAA6BA,CAClCb,iBAG4B,EAC5B;MACA,IAAMC,OAAO,GAAGD,iBAAiB,CAACE,mBAAmB,CAAC,CAAC;MACvDX,mBAAmB,CAACY,sBAAsB,CAACW,MAAM,CAACb,OAAO,CAAC;MAC1D,IAAIV,mBAAmB,CAACY,sBAAsB,CAACE,IAAI,KAAK,CAAC,EAAE;QACzD,IAAI,CAACb,uBAAuB,CAACuB,kBAAkB,CAC7C,yBACF,CAAC;MACH;IACF;EAAA;AAAA;AAhDIxB,mBAAmB,CACRY,sBAAsB,GAAG,IAAIa,GAAG,CAAC,CAAC;AAAA,IAkD7CC,oBAAoB;EAIxB,SAAAA,qBAAA,EAAc;IAAA,IAAAlC,gBAAA,CAAAD,OAAA,QAAAmC,oBAAA;IACZ,IAAI,CAACA,oBAAoB,CAACC,aAAa,EAAE;MACvC,IAAMC,OAAO,GAAG,SAAVA,OAAOA,CAAIlB,OAAe,EAAEU,KAAc,EAAK;QACnD,IAAMH,SAAS,GACbS,oBAAoB,CAACd,sBAAsB,CAACM,GAAG,CAACR,OAAO,CAAC;QAC1DO,SAAS,YAATA,SAAS,CAAEE,iBAAiB,CAACC,KAAK,CAAC;MACrC,CAAC;MACD,IAAAS,2BAAkB,EAAC,YAAM;QACvB,SAAS;;QACTC,MAAM,CAACC,aAAa,GAAG,UAACrB,OAAe,EAAEU,KAAc,EAAK;UAC1D,IAAAY,gBAAO,EAACJ,OAAO,CAAC,CAAClB,OAAO,EAAEU,KAAK,CAAC;QAClC,CAAC;MACH,CAAC,CAAC,CAAC,CAAC;MACJM,oBAAoB,CAACC,aAAa,GAAG,IAAI;IAC3C;EACF;EAAA,WAAAlC,aAAA,CAAAF,OAAA,EAAAmC,oBAAA;IAAAnB,GAAA;IAAAjB,KAAA,EAEO,SAAAkB,0BAA0BA,CAC/BC,iBAG4B,EAC5B;MACA,IAAI,CAACiB,oBAAoB,CAACC,aAAa,EAAE;QACvC;MACF;MACA,IAAMjB,OAAO,GAAGD,iBAAiB,CAACE,mBAAmB,CAAC,CAAC;MACvDe,oBAAoB,CAACd,sBAAsB,CAACC,GAAG,CAACH,OAAO,EAAED,iBAAiB,CAAC;IAC7E;EAAA;IAAAF,GAAA;IAAAjB,KAAA,EAEO,SAAAgC,6BAA6BA,CAClCb,iBAG4B,EAC5B;MACA,IAAI,CAACiB,oBAAoB,CAACC,aAAa,EAAE;QACvC;MACF;MACA,IAAMjB,OAAO,GAAGD,iBAAiB,CAACE,mBAAmB,CAAC,CAAC;MACvDe,oBAAoB,CAACd,sBAAsB,CAACW,MAAM,CAACb,OAAO,CAAC;IAC7D;EAAA;AAAA;AA7CIgB,oBAAoB,CACTd,sBAAsB,GAAG,IAAIa,GAAG,CAAC,CAAC;AAD7CC,oBAAoB,CAETC,aAAa,GAAG,KAAK;AAAA,IA8ChCM,iBAAiB;EAAA,SAAAA,kBAAA;IAAA,IAAAzC,gBAAA,CAAAD,OAAA,QAAA0C,iBAAA;EAAA;EAAA,WAAAxC,aAAA,CAAAF,OAAA,EAAA0C,iBAAA;IAAA1B,GAAA;IAAAjB,KAAA,EACd,SAAAkB,0BAA0BA,CAC/B0B,kBAG4B,EAC5B,CACA;EAAA;IAAA3B,GAAA;IAAAjB,KAAA,EAGK,SAAAgC,6BAA6BA,CAClCY,kBAG4B,EAC5B,CACA;EAAA;AAAA;AASJ,IAAIC,cAAqC;AACzC,IAAIrC,iBAAiB,EAAE;EACrBqC,cAAc,GAAGF,iBAAiB;AACpC,CAAC,MAAM,IAAIH,MAAM,CAACM,UAAU,EAAE;EAC5BD,cAAc,GAAGT,oBAAoB;AACvC,CAAC,MAAM;EACLS,cAAc,GAAGnC,mBAAmB;AACtC;AAAA,IAAAqC,QAAA,GAAAhD,OAAA,CAAAE,OAAA,GAEe4C,cAAc", "ignoreList": []}