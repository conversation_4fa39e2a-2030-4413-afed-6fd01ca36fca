{"version": 3, "names": ["_interopRequireDefault", "require", "Object", "defineProperty", "exports", "value", "EntryExitTransition", "combineTransition", "_defineProperty2", "_classCallCheck2", "_createClass2", "_possibleConstructorReturn2", "_getPrototypeOf2", "_inherits2", "_index", "_index2", "_Fade", "_index3", "_callSuper", "t", "o", "e", "default", "_isNativeReflectConstruct", "Reflect", "construct", "constructor", "apply", "Boolean", "prototype", "valueOf", "call", "_BaseAnimationBuilder", "_this", "_len", "arguments", "length", "args", "Array", "_key", "concat", "enteringV", "FadeIn", "exitingV", "FadeOut", "build", "delayFunction", "getDelayFunction", "callback", "callbackV", "delay", "get<PERSON>elay", "enteringAnimation", "exitingAnimation", "exitingDuration", "getDuration", "values", "enteringValues", "exitingValues", "animations", "transform", "prop", "keys", "isArray", "for<PERSON>ach", "index", "transformProp", "push", "withSequence", "withTiming", "initialValues", "duration", "sequence", "undefined", "includes", "mergedTransform", "map", "objectKeys", "logger", "error", "current", "assign", "originX", "currentOriginX", "originY", "currentOriginY", "width", "currentWidth", "height", "currentHeight", "targetOriginX", "targetOriginY", "targetWidth", "targetHeight", "key", "entering", "animation", "exiting", "createInstance", "instance", "BaseAnimationBuilder", "presetName"], "sources": ["../../../../src/layoutReanimation/defaultTransitions/EntryExitTransition.ts"], "sourcesContent": [null], "mappings": "AAAA,YAAY;;AAAA,IAAAA,sBAAA,GAAAC,OAAA;AAAAC,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,KAAA;AAAA;AAAAD,OAAA,CAAAE,mBAAA;AAAAF,OAAA,CAAAG,iBAAA,GAAAA,iBAAA;AAAA,IAAAC,gBAAA,GAAAR,sBAAA,CAAAC,OAAA;AAAA,IAAAQ,gBAAA,GAAAT,sBAAA,CAAAC,OAAA;AAAA,IAAAS,aAAA,GAAAV,sBAAA,CAAAC,OAAA;AAAA,IAAAU,2BAAA,GAAAX,sBAAA,CAAAC,OAAA;AAAA,IAAAW,gBAAA,GAAAZ,sBAAA,CAAAC,OAAA;AAAA,IAAAY,UAAA,GAAAb,sBAAA,CAAAC,OAAA;AAOZ,IAAAa,MAAA,GAAAb,OAAA;AACA,IAAAc,OAAA,GAAAd,OAAA;AACA,IAAAe,KAAA,GAAAf,OAAA;AAMA,IAAAgB,OAAA,GAAAhB,OAAA;AAAqC,SAAAiB,WAAAC,CAAA,EAAAC,CAAA,EAAAC,CAAA,WAAAD,CAAA,OAAAR,gBAAA,CAAAU,OAAA,EAAAF,CAAA,OAAAT,2BAAA,CAAAW,OAAA,EAAAH,CAAA,EAAAI,yBAAA,KAAAC,OAAA,CAAAC,SAAA,CAAAL,CAAA,EAAAC,CAAA,YAAAT,gBAAA,CAAAU,OAAA,EAAAH,CAAA,EAAAO,WAAA,IAAAN,CAAA,CAAAO,KAAA,CAAAR,CAAA,EAAAE,CAAA;AAAA,SAAAE,0BAAA,cAAAJ,CAAA,IAAAS,OAAA,CAAAC,SAAA,CAAAC,OAAA,CAAAC,IAAA,CAAAP,OAAA,CAAAC,SAAA,CAAAG,OAAA,iCAAAT,CAAA,aAAAI,yBAAA,YAAAA,0BAAA,aAAAJ,CAAA;AAAA,IAExBb,mBAAmB,GAAAF,OAAA,CAAAE,mBAAA,aAAA0B,qBAAA;EAAA,SAAA1B,oBAAA;IAAA,IAAA2B,KAAA;IAAA,IAAAxB,gBAAA,CAAAa,OAAA,QAAAhB,mBAAA;IAAA,SAAA4B,IAAA,GAAAC,SAAA,CAAAC,MAAA,EAAAC,IAAA,OAAAC,KAAA,CAAAJ,IAAA,GAAAK,IAAA,MAAAA,IAAA,GAAAL,IAAA,EAAAK,IAAA;MAAAF,IAAA,CAAAE,IAAA,IAAAJ,SAAA,CAAAI,IAAA;IAAA;IAAAN,KAAA,GAAAf,UAAA,OAAAZ,mBAAA,KAAAkC,MAAA,CAAAH,IAAA;IAAAJ,KAAA,CAM9BQ,SAAS,GAAuDC,YAAM;IAAAT,KAAA,CAEtEU,QAAQ,GAAuDC,aAAO;IAAAX,KAAA,CAoCtEY,KAAK,GAAG,YAA+B;MACrC,IAAMC,aAAa,GAAGb,KAAA,CAAKc,gBAAgB,CAAC,CAAC;MAC7C,IAAMC,QAAQ,GAAGf,KAAA,CAAKgB,SAAS;MAC/B,IAAMC,KAAK,GAAGjB,KAAA,CAAKkB,QAAQ,CAAC,CAAC;MAE7B,IAAMC,iBAAiB,GAAGnB,KAAA,CAAKQ,SAAS,CAACI,KAAK,CAAC,CAAC;MAEhD,IAAMQ,gBAAgB,GAAGpB,KAAA,CAAKU,QAAQ,CAACE,KAAK,CAAC,CAAC;MAC9C,IAAMS,eAAe,GAAGrB,KAAA,CAAKU,QAAQ,CAACY,WAAW,CAAC,CAAC;MAEnD,OAAQ,UAAAC,MAAM,EAAK;QACjB,SAAS;;QACT,IAAMC,cAAc,GAAGL,iBAAiB,CAACI,MAAM,CAAC;QAChD,IAAME,aAAa,GAAGL,gBAAgB,CAACG,MAAM,CAAC;QAC9C,IAAMG,UAAwC,GAAG;UAC/CC,SAAS,EAAE;QACb,CAAC;QAED,KAAK,IAAMC,IAAI,IAAI3D,MAAM,CAAC4D,IAAI,CAACJ,aAAa,CAACC,UAAU,CAAC,EAAE;UACxD,IAAIE,IAAI,KAAK,WAAW,EAAE;YACxB,IAAI,CAACvB,KAAK,CAACyB,OAAO,CAACL,aAAa,CAACC,UAAU,CAACC,SAAS,CAAC,EAAE;cACtD;YACF;YACAF,aAAa,CAACC,UAAU,CAACC,SAAS,CAACI,OAAO,CAAC,UAAC3D,KAAK,EAAE4D,KAAK,EAAK;cAC3D,KAAK,IAAMC,aAAa,IAAIhE,MAAM,CAAC4D,IAAI,CAACzD,KAAK,CAAC,EAAE;gBAC9CsD,UAAU,CAACC,SAAS,CAAEO,IAAI,KAAA3D,gBAAA,CAAAc,OAAA,MACvB4C,aAAa,EAAGpB,aAAa,CAC5BI,KAAK,EACL,IAAAkB,oBAAY,EACV/D,KAAK,CAAC6D,aAAa,CAA6B,EAChD,IAAAG,kBAAU,EACRX,aAAa,CAACY,aAAa,CAACV,SAAS,GASjCF,aAAa,CAACY,aAAa,CAACV,SAAS,CAACK,KAAK,CAAC,CAC1CC,aAAa,CACd,GACD,CAAC,EACL;kBAAEK,QAAQ,EAAE;gBAAE,CAChB,CACF,CACF,EACqB,CAAC;cAC1B;YACF,CAAC,CAAC;UACJ,CAAC,MAAM;YACL,IAAMC,QAAQ,GACZf,cAAc,CAACE,UAAU,CAACE,IAAI,CAAC,KAAKY,SAAS,GACzC,CACEf,aAAa,CAACC,UAAU,CAACE,IAAI,CAAC,EAC9B,IAAAQ,kBAAU,EAACZ,cAAc,CAACa,aAAa,CAACT,IAAI,CAAC,EAAE;cAC7CU,QAAQ,EAAE;YACZ,CAAC,CAAC,EACFd,cAAc,CAACE,UAAU,CAACE,IAAI,CAAC,CAChC,GACD,CACEH,aAAa,CAACC,UAAU,CAACE,IAAI,CAAC,EAC9B,IAAAQ,kBAAU,EACRnE,MAAM,CAAC4D,IAAI,CAACN,MAAM,CAAC,CAACkB,QAAQ,CAACb,IAAI,CAAC,GAC9BL,MAAM,CAACK,IAAI,CAAiC,GAC5CH,aAAa,CAACY,aAAa,CAACT,IAAI,CAAC,EACrC;cAAEU,QAAQ,EAAE;YAAE,CAChB,CAAC,CACF;YAEPZ,UAAU,CAACE,IAAI,CAAC,GAAGf,aAAa,CAACI,KAAK,EAAEkB,oBAAY,CAAAzC,KAAA,SAAI6C,QAAQ,CAAC,CAAC;UACpE;QACF;QACA,KAAK,IAAMX,KAAI,IAAI3D,MAAM,CAAC4D,IAAI,CAACL,cAAc,CAACE,UAAU,CAAC,EAAE;UACzD,IAAIE,KAAI,KAAK,WAAW,EAAE;YACxB,IAAI,CAACvB,KAAK,CAACyB,OAAO,CAACN,cAAc,CAACE,UAAU,CAACC,SAAS,CAAC,EAAE;cACvD;YACF;YACAH,cAAc,CAACE,UAAU,CAACC,SAAS,CAACI,OAAO,CAAC,UAAC3D,KAAK,EAAE4D,KAAK,EAAK;cAC5D,KAAK,IAAMC,aAAa,IAAIhE,MAAM,CAAC4D,IAAI,CAACzD,KAAK,CAAC,EAAE;gBAC9CsD,UAAU,CAACC,SAAS,CAAEO,IAAI,KAAA3D,gBAAA,CAAAc,OAAA,MACvB4C,aAAa,EAAGpB,aAAa,CAC5BI,KAAK,GAAGI,eAAe,EACvB,IAAAc,oBAAY,EACV,IAAAC,kBAAU,EACRZ,cAAc,CAACa,aAAa,CAACV,SAAS,GAEhCH,cAAc,CAACa,aAAa,CACzBV,SAAS,CACZK,KAAK,CAAC,CACNC,aAAa,CACd,GACD,CAAC,EACL;kBAAEK,QAAQ,EAAEjB;gBAAgB,CAC9B,CAAC,EACDjD,KAAK,CACH6D,aAAa,CAEjB,CACF,EACqB,CAAC;cAC1B;YACF,CAAC,CAAC;UACJ,CAAC,MAAM,IAAIP,UAAU,CAACE,KAAI,CAAC,KAAKY,SAAS,EAAE;YAEzC;UACF,CAAC,MAAM;YACLd,UAAU,CAACE,KAAI,CAAC,GAAGf,aAAa,CAC9BI,KAAK,EACL,IAAAkB,oBAAY,EACV,IAAAC,kBAAU,EAACZ,cAAc,CAACa,aAAa,CAACT,KAAI,CAAC,EAAE;cAAEU,QAAQ,EAAE;YAAE,CAAC,CAAC,EAC/Dd,cAAc,CAACE,UAAU,CAACE,KAAI,CAChC,CACF,CAAC;UACH;QACF;QAEA,IAAMc,eAAe,GAAG,CACtBrC,KAAK,CAACyB,OAAO,CAACL,aAAa,CAACY,aAAa,CAACV,SAAS,CAAC,GAChDF,aAAa,CAACY,aAAa,CAACV,SAAS,GACrC,EAAE,EACNpB,MAAM,CACN,CAACF,KAAK,CAACyB,OAAO,CAACN,cAAc,CAACE,UAAU,CAACC,SAAS,CAAC,GAC/CH,cAAc,CAACE,UAAU,CAACC,SAAS,GACnC,EAAE,EACJgB,GAAG,CAAE,UAAAvE,KAAK,EAAK;UACf,IAAMwE,UAAU,GAAG3E,MAAM,CAAC4D,IAAI,CAACzD,KAAK,CAAC;UACrC,IAAI,CAAAwE,UAAU,oBAAVA,UAAU,CAAEzC,MAAM,IAAG,CAAC,EAAE;YAC1B0C,cAAM,CAACC,KAAK,CAAC,2CAA2C,CAAC;YACzD,OAAO1E,KAAK;UACd;UAEA,IAAM6D,aAAa,GAAGW,UAAU,CAAC,CAAC,CAAC;UACnC,IAAMG,OAAO,GAGV3E,KAAK,CAAC6D,aAAa,CAAC,CAAqBc,OAAO;UACnD,IAAI,OAAOA,OAAO,KAAK,QAAQ,EAAE;YAC/B,IAAIA,OAAO,CAACN,QAAQ,CAAC,KAAK,CAAC,EAAE;cAC3B,WAAAlE,gBAAA,CAAAc,OAAA,MACG4C,aAAa,EAAG;YAErB,CAAC,MAAM;cACL,WAAA1D,gBAAA,CAAAc,OAAA,MACG4C,aAAa,EAAG;YAErB;UACF,CAAC,MAAM,IAAIA,aAAa,CAACQ,QAAQ,CAAC,WAAW,CAAC,EAAE;YAC9C,WAAAlE,gBAAA,CAAAc,OAAA,MAAU4C,aAAa,EAAG;UAC5B,CAAC,MAAM;YACL,WAAA1D,gBAAA,CAAAc,OAAA,MAAU4C,aAAa,EAAG;UAC5B;QACF,CAAC,CACH,CAAC;QAED,OAAO;UACLI,aAAa,EAAApE,MAAA,CAAA+E,MAAA,KACRvB,aAAa,CAACY,aAAa;YAC9BY,OAAO,EAAE1B,MAAM,CAAC2B,cAAc;YAC9BC,OAAO,EAAE5B,MAAM,CAAC6B,cAAc;YAC9BC,KAAK,EAAE9B,MAAM,CAAC+B,YAAY;YAC1BC,MAAM,EAAEhC,MAAM,CAACiC,aAAa;YAC5B7B,SAAS,EAAEe;UAAA,EACZ;UACDhB,UAAU,EAAAzD,MAAA,CAAA+E,MAAA;YACRC,OAAO,EAAEpC,aAAa,CACpBI,KAAK,GAAGI,eAAe,EACvB,IAAAe,kBAAU,EAACb,MAAM,CAACkC,aAAa,EAAE;cAAEnB,QAAQ,EAAEjB;YAAgB,CAAC,CAChE,CAAC;YACD8B,OAAO,EAAEtC,aAAa,CACpBI,KAAK,GAAGI,eAAe,EACvB,IAAAe,kBAAU,EAACb,MAAM,CAACmC,aAAa,EAAE;cAAEpB,QAAQ,EAAEjB;YAAgB,CAAC,CAChE,CAAC;YACDgC,KAAK,EAAExC,aAAa,CAClBI,KAAK,GAAGI,eAAe,EACvB,IAAAe,kBAAU,EAACb,MAAM,CAACoC,WAAW,EAAE;cAAErB,QAAQ,EAAEjB;YAAgB,CAAC,CAC9D,CAAC;YACDkC,MAAM,EAAE1C,aAAa,CACnBI,KAAK,GAAGI,eAAe,EACvB,IAAAe,kBAAU,EAACb,MAAM,CAACqC,YAAY,EAAE;cAAEtB,QAAQ,EAAEjB;YAAgB,CAAC,CAC/D;UAAC,GACEK,UAAA,CACJ;UACDX,QAAA,EAAAA;QACF,CAAC;MACH,CAAC;IACH,CAAC;IAAA,OAAAf,KAAA;EAAA;EAAA,IAAApB,UAAA,CAAAS,OAAA,EAAAhB,mBAAA,EAAA0B,qBAAA;EAAA,WAAAtB,aAAA,CAAAY,OAAA,EAAAhB,mBAAA;IAAAwF,GAAA;IAAAzF,KAAA,EAhND,SAAA0F,QAAQA,CACNC,SAA6D,EACxC;MACrB,IAAI,CAACvD,SAAS,GAAGuD,SAAS;MAC1B,OAAO,IAAI;IACb;EAAA;IAAAF,GAAA;IAAAzF,KAAA,EASA,SAAA4F,OAAOA,CACLD,SAA6D,EACxC;MACrB,IAAI,CAACrD,QAAQ,GAAGqD,SAAS;MACzB,OAAO,IAAI;IACb;EAAA;IAAAF,GAAA;IAAAzF,KAAA,EAhCA,SAAO6F,cAAcA,CAAA,EAEF;MACjB,OAAO,IAAI5F,mBAAmB,CAAC,CAAC;IAClC;EAAA;IAAAwF,GAAA;IAAAzF,KAAA,EAEA,SAAO0F,QAAQA,CACbC,SAA6D,EACxC;MACrB,IAAMG,QAAQ,GAAG,IAAI,CAACD,cAAc,CAAC,CAAC;MACtC,OAAOC,QAAQ,CAACJ,QAAQ,CAACC,SAAS,CAAC;IACrC;EAAA;IAAAF,GAAA;IAAAzF,KAAA,EASA,SAAO4F,OAAOA,CACZD,SAA6D,EACxC;MACrB,IAAMG,QAAQ,GAAG,IAAI,CAACD,cAAc,CAAC,CAAC;MACtC,OAAOC,QAAQ,CAACF,OAAO,CAACD,SAAS,CAAC;IACpC;EAAA;AAAA,EAlCQI,2BAAoB;AADjB9F,mBAAmB,CAIvB+F,UAAU,GAAG,qBAAqB;AA2OpC,SAAS9F,iBAAiBA,CAC/B0F,OAA2D,EAC3DF,QAA4D,EACvC;EACrB,OAAOzF,mBAAmB,CAACyF,QAAQ,CAACA,QAAQ,CAAC,CAACE,OAAO,CAACA,OAAO,CAAC;AAChE", "ignoreList": []}