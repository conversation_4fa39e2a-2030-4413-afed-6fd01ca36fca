{"version": 3, "names": ["_NativeFrameRateLogger", "_interopRequireDefault", "require", "invariant", "FrameRateLogger", "setGlobalOptions", "options", "debug", "undefined", "NativeFrameRateLogger", "setContext", "context", "beginScroll", "endScroll", "module", "exports"], "sources": ["FrameRateLogger.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @format\n * @flow strict-local\n */\n\nimport NativeFrameRateLogger from './NativeFrameRateLogger';\n\nconst invariant = require('invariant');\n\n/**\n * Flow API for native FrameRateLogger module. If the native module is not installed, function calls\n * are just no-ops.\n *\n * Typical behavior is that `setContext` is called when a new screen is loaded (e.g. via a\n * navigation integration), and then `beginScroll` is called by `ScrollResponder` at which point the\n * native module then begins tracking frame drops. When `ScrollResponder` calls `endScroll`, the\n * native module gathers up all it's frame drop data and reports it via an analytics pipeline for\n * analysis.\n *\n * Note that `beginScroll` may be called multiple times by `ScrollResponder` - unclear if that's a\n * bug, but the native module should be robust to that.\n *\n * In the future we may add support for tracking frame drops in other types of interactions beyond\n * scrolling.\n */\nconst FrameRateLogger = {\n  /**\n   * Enable `debug` to see local logs of what's going on.\n   */\n  setGlobalOptions: function (options: {debug?: boolean, ...}) {\n    if (options.debug !== undefined) {\n      invariant(\n        NativeFrameRateLogger,\n        'Trying to debug FrameRateLogger without the native module!',\n      );\n    }\n    NativeFrameRateLogger?.setGlobalOptions({\n      debug: !!options.debug,\n    });\n  },\n\n  /**\n   * Must call `setContext` before any events can be properly tracked, which is done automatically\n   * in `AppRegistry`, but navigation is also a common place to hook in.\n   */\n  setContext: function (context: string) {\n    NativeFrameRateLogger?.setContext(context);\n  },\n\n  /**\n   * Called in `ScrollResponder` so any component that uses that module will handle this\n   * automatically.\n   */\n  beginScroll() {\n    NativeFrameRateLogger?.beginScroll();\n  },\n\n  /**\n   * Called in `ScrollResponder` so any component that uses that module will handle this\n   * automatically.\n   */\n  endScroll() {\n    NativeFrameRateLogger?.endScroll();\n  },\n};\n\nmodule.exports = FrameRateLogger;\n"], "mappings": ";AAUA,IAAAA,sBAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA,IAAMC,SAAS,GAAGD,OAAO,CAAC,WAAW,CAAC;AAkBtC,IAAME,eAAe,GAAG;EAItBC,gBAAgB,EAAE,SAAlBA,gBAAgBA,CAAYC,OAA+B,EAAE;IAC3D,IAAIA,OAAO,CAACC,KAAK,KAAKC,SAAS,EAAE;MAC/BL,SAAS,CACPM,8BAAqB,EACrB,4DACF,CAAC;IACH;IACAA,8BAAqB,YAArBA,8BAAqB,CAAEJ,gBAAgB,CAAC;MACtCE,KAAK,EAAE,CAAC,CAACD,OAAO,CAACC;IACnB,CAAC,CAAC;EACJ,CAAC;EAMDG,UAAU,EAAE,SAAZA,UAAUA,CAAYC,OAAe,EAAE;IACrCF,8BAAqB,YAArBA,8BAAqB,CAAEC,UAAU,CAACC,OAAO,CAAC;EAC5C,CAAC;EAMDC,WAAW,WAAXA,WAAWA,CAAA,EAAG;IACZH,8BAAqB,YAArBA,8BAAqB,CAAEG,WAAW,CAAC,CAAC;EACtC,CAAC;EAMDC,SAAS,WAATA,SAASA,CAAA,EAAG;IACVJ,8BAAqB,YAArBA,8BAAqB,CAAEI,SAAS,CAAC,CAAC;EACpC;AACF,CAAC;AAEDC,MAAM,CAACC,OAAO,GAAGX,eAAe", "ignoreList": []}