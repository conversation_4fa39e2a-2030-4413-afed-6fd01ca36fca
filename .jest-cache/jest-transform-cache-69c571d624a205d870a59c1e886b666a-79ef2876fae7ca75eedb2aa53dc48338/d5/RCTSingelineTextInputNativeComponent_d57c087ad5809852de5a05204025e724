d947083f046118ff4c504b5aaa71eb9b
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = exports.__INTERNAL_VIEW_CONFIG = exports.Commands = void 0;
var NativeComponentRegistry = _interopRequireWildcard(require("../../NativeComponent/NativeComponentRegistry"));
var _codegenNativeCommands = _interopRequireDefault(require("../../Utilities/codegenNativeCommands"));
var _RCTTextInputViewConfig = _interopRequireDefault(require("./RCTTextInputViewConfig"));
function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
var Commands = exports.Commands = (0, _codegenNativeCommands.default)({
  supportedCommands: ['focus', 'blur', 'setTextAndSelection']
});
var __INTERNAL_VIEW_CONFIG = exports.__INTERNAL_VIEW_CONFIG = Object.assign({
  uiViewClassName: 'RCTSinglelineTextInputView'
}, _RCTTextInputViewConfig.default);
var SinglelineTextInputNativeComponent = NativeComponentRegistry.get('RCTSinglelineTextInputView', function () {
  return __INTERNAL_VIEW_CONFIG;
});
var _default = exports.default = SinglelineTextInputNativeComponent;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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