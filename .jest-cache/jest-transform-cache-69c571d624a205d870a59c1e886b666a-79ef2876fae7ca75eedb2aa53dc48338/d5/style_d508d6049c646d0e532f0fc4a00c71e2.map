{"version": 3, "names": ["cov_zcc6o448n", "actualCoverage", "s", "msb_shared_component_1", "require", "exports", "makeStyle", "createMSBStyleSheet", "_ref", "f", "Typography", "ColorGlobal", "ColorDataView", "ColorAlias", "SizeGlobal", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "container", "flexDirection", "alignItems", "padding", "SpacingSmall", "logo", "height", "Size800", "marginRight", "resizeMode", "width", "subContainer", "flex", "justifyContent", "subTitleContainer", "icon", "title", "Object", "assign", "b", "base_semiBold", "color", "TextMain", "marginBottom", "Spacing2xSmall", "subTitle", "base_regular", "TextSub", "subTitleFlex", "dot", "Size200", "paddingHorizontal", "SpacingXMSmall", "deleteAnimated", "backgroundColor", "Red500", "editAnimated", "Blue500", "childViewAnimated"], "sources": ["/Users/<USER>/projects/react-native/payment-module/src/components/bill-item/style.ts"], "sourcesContent": ["import {createMSBStyleSheet} from 'msb-shared-component';\n\nexport const makeStyle = createMSBStyleSheet(\n  ({Typography, ColorGlobal, ColorDataView, ColorAlias, SizeGlobal, SizeAlias}) => {\n    return {\n      container: {flexDirection: 'row', alignItems: 'center', padding: SizeAlias.SpacingSmall},\n      logo: {\n        height: SizeGlobal.Size800,\n        marginRight: SizeAlias.SpacingSmall,\n        resizeMode: 'contain',\n        width: SizeGlobal.Size800,\n      },\n      subContainer: {flexDirection: 'column', flex: 1, justifyContent: 'center'},\n      subTitleContainer: {flexDirection: 'row', alignItems: 'center'},\n      icon: {\n        height: SizeGlobal.Size800,\n        marginRight: SizeAlias.SpacingSmall,\n        resizeMode: 'contain',\n        width: SizeGlobal.Size800,\n      },\n      title: {\n        ...Typography?.base_semiBold,\n        color: ColorDataView.TextMain,\n        marginBottom: SizeAlias.Spacing2xSmall,\n      },\n      subTitle: {...Typography?.base_regular, color: ColorDataView.TextSub},\n      subTitleFlex: {...Typography?.base_regular, color: ColorDataView.TextSub, flex: 1},\n      dot: {\n        width: SizeGlobal.Size200,\n        height: SizeGlobal.Size200,\n        resizeMode: 'contain',\n        paddingHorizontal: SizeAlias.SpacingXMSmall,\n      },\n      deleteAnimated: {\n        alignItems: 'center',\n        justifyContent: 'center',\n        flex: 1,\n        backgroundColor: ColorGlobal.Red500,\n      },\n      editAnimated: {\n        flexDirection: 'row',\n        alignItems: 'center',\n        justifyContent: 'center',\n        backgroundColor: ColorGlobal.Blue500,\n        flex: 1,\n      },\n      childViewAnimated: {alignItems: 'center', justifyContent: 'center'},\n    };\n  },\n);\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAKM;IAAAA,aAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,aAAA;AAAAA,aAAA,GAAAE,CAAA;;;;;;;AALN,IAAAC,sBAAA;AAAA;AAAA,CAAAH,aAAA,GAAAE,CAAA,OAAAE,OAAA;AAAA;AAAAJ,aAAA,GAAAE,CAAA;AAEaG,OAAA,CAAAC,SAAS,GAAG,IAAAH,sBAAA,CAAAI,mBAAmB,EAC1C,UAAAC,IAAA,EAAgF;EAAA;EAAAR,aAAA,GAAAS,CAAA;EAAA,IAA9EC,UAAU;IAAA;IAAA,CAAAV,aAAA,GAAAE,CAAA,OAAAM,IAAA,CAAVE,UAAU;IAAEC,WAAW;IAAA;IAAA,CAAAX,aAAA,GAAAE,CAAA,OAAAM,IAAA,CAAXG,WAAW;IAAEC,aAAa;IAAA;IAAA,CAAAZ,aAAA,GAAAE,CAAA,OAAAM,IAAA,CAAbI,aAAa;IAAEC,UAAU;IAAA;IAAA,CAAAb,aAAA,GAAAE,CAAA,OAAAM,IAAA,CAAVK,UAAU;IAAEC,UAAU;IAAA;IAAA,CAAAd,aAAA,GAAAE,CAAA,OAAAM,IAAA,CAAVM,UAAU;IAAEC,SAAS;IAAA;IAAA,CAAAf,aAAA,GAAAE,CAAA,OAAAM,IAAA,CAATO,SAAS;EAAA;EAAAf,aAAA,GAAAE,CAAA;EACzE,OAAO;IACLc,SAAS,EAAE;MAACC,aAAa,EAAE,KAAK;MAAEC,UAAU,EAAE,QAAQ;MAAEC,OAAO,EAAEJ,SAAS,CAACK;IAAY,CAAC;IACxFC,IAAI,EAAE;MACJC,MAAM,EAAER,UAAU,CAACS,OAAO;MAC1BC,WAAW,EAAET,SAAS,CAACK,YAAY;MACnCK,UAAU,EAAE,SAAS;MACrBC,KAAK,EAAEZ,UAAU,CAACS;KACnB;IACDI,YAAY,EAAE;MAACV,aAAa,EAAE,QAAQ;MAAEW,IAAI,EAAE,CAAC;MAAEC,cAAc,EAAE;IAAQ,CAAC;IAC1EC,iBAAiB,EAAE;MAACb,aAAa,EAAE,KAAK;MAAEC,UAAU,EAAE;IAAQ,CAAC;IAC/Da,IAAI,EAAE;MACJT,MAAM,EAAER,UAAU,CAACS,OAAO;MAC1BC,WAAW,EAAET,SAAS,CAACK,YAAY;MACnCK,UAAU,EAAE,SAAS;MACrBC,KAAK,EAAEZ,UAAU,CAACS;KACnB;IACDS,KAAK,EAAAC,MAAA,CAAAC,MAAA,KACAxB,UAAU;IAAA;IAAA,CAAAV,aAAA,GAAAmC,CAAA;IAAA;IAAA,CAAAnC,aAAA,GAAAmC,CAAA,UAAVzB,UAAU,CAAE0B,aAAa;MAC5BC,KAAK,EAAEzB,aAAa,CAAC0B,QAAQ;MAC7BC,YAAY,EAAExB,SAAS,CAACyB;IAAc,EACvC;IACDC,QAAQ,EAAAR,MAAA,CAAAC,MAAA,KAAMxB,UAAU;IAAA;IAAA,CAAAV,aAAA,GAAAmC,CAAA;IAAA;IAAA,CAAAnC,aAAA,GAAAmC,CAAA,UAAVzB,UAAU,CAAEgC,YAAY;MAAEL,KAAK,EAAEzB,aAAa,CAAC+B;IAAO,EAAC;IACrEC,YAAY,EAAAX,MAAA,CAAAC,MAAA,KAAMxB,UAAU;IAAA;IAAA,CAAAV,aAAA,GAAAmC,CAAA;IAAA;IAAA,CAAAnC,aAAA,GAAAmC,CAAA,UAAVzB,UAAU,CAAEgC,YAAY;MAAEL,KAAK,EAAEzB,aAAa,CAAC+B,OAAO;MAAEf,IAAI,EAAE;IAAC,EAAC;IAClFiB,GAAG,EAAE;MACHnB,KAAK,EAAEZ,UAAU,CAACgC,OAAO;MACzBxB,MAAM,EAAER,UAAU,CAACgC,OAAO;MAC1BrB,UAAU,EAAE,SAAS;MACrBsB,iBAAiB,EAAEhC,SAAS,CAACiC;KAC9B;IACDC,cAAc,EAAE;MACd/B,UAAU,EAAE,QAAQ;MACpBW,cAAc,EAAE,QAAQ;MACxBD,IAAI,EAAE,CAAC;MACPsB,eAAe,EAAEvC,WAAW,CAACwC;KAC9B;IACDC,YAAY,EAAE;MACZnC,aAAa,EAAE,KAAK;MACpBC,UAAU,EAAE,QAAQ;MACpBW,cAAc,EAAE,QAAQ;MACxBqB,eAAe,EAAEvC,WAAW,CAAC0C,OAAO;MACpCzB,IAAI,EAAE;KACP;IACD0B,iBAAiB,EAAE;MAACpC,UAAU,EAAE,QAAQ;MAAEW,cAAc,EAAE;IAAQ;GACnE;AACH,CAAC,CACF", "ignoreList": []}