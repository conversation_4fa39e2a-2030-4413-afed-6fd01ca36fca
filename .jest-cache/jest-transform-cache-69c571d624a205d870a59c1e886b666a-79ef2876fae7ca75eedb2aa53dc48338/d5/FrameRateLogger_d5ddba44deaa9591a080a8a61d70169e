334cc5cdad8f2148fdc2462654e4635d
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
var _NativeFrameRateLogger = _interopRequireDefault(require("./NativeFrameRateLogger"));
var invariant = require('invariant');
var FrameRateLogger = {
  setGlobalOptions: function setGlobalOptions(options) {
    if (options.debug !== undefined) {
      invariant(_NativeFrameRateLogger.default, 'Trying to debug FrameRateLogger without the native module!');
    }
    _NativeFrameRateLogger.default == null || _NativeFrameRateLogger.default.setGlobalOptions({
      debug: !!options.debug
    });
  },
  setContext: function setContext(context) {
    _NativeFrameRateLogger.default == null || _NativeFrameRateLogger.default.setContext(context);
  },
  beginScroll: function beginScroll() {
    _NativeFrameRateLogger.default == null || _NativeFrameRateLogger.default.beginScroll();
  },
  endScroll: function endScroll() {
    _NativeFrameRateLogger.default == null || _NativeFrameRateLogger.default.endScroll();
  }
};
module.exports = FrameRateLogger;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJfTmF0aXZlRnJhbWVSYXRlTG9nZ2VyIiwiX2ludGVyb3BSZXF1aXJlRGVmYXVsdCIsInJlcXVpcmUiLCJpbnZhcmlhbnQiLCJGcmFtZVJhdGVMb2dnZXIiLCJzZXRHbG9iYWxPcHRpb25zIiwib3B0aW9ucyIsImRlYnVnIiwidW5kZWZpbmVkIiwiTmF0aXZlRnJhbWVSYXRlTG9nZ2VyIiwic2V0Q29udGV4dCIsImNvbnRleHQiLCJiZWdpblNjcm9sbCIsImVuZFNjcm9sbCIsIm1vZHVsZSIsImV4cG9ydHMiXSwic291cmNlcyI6WyJGcmFtZVJhdGVMb2dnZXIuanMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBDb3B5cmlnaHQgKGMpIE1ldGEgUGxhdGZvcm1zLCBJbmMuIGFuZCBhZmZpbGlhdGVzLlxuICpcbiAqIFRoaXMgc291cmNlIGNvZGUgaXMgbGljZW5zZWQgdW5kZXIgdGhlIE1JVCBsaWNlbnNlIGZvdW5kIGluIHRoZVxuICogTElDRU5TRSBmaWxlIGluIHRoZSByb290IGRpcmVjdG9yeSBvZiB0aGlzIHNvdXJjZSB0cmVlLlxuICpcbiAqIEBmb3JtYXRcbiAqIEBmbG93IHN0cmljdC1sb2NhbFxuICovXG5cbmltcG9ydCBOYXRpdmVGcmFtZVJhdGVMb2dnZXIgZnJvbSAnLi9OYXRpdmVGcmFtZVJhdGVMb2dnZXInO1xuXG5jb25zdCBpbnZhcmlhbnQgPSByZXF1aXJlKCdpbnZhcmlhbnQnKTtcblxuLyoqXG4gKiBGbG93IEFQSSBmb3IgbmF0aXZlIEZyYW1lUmF0ZUxvZ2dlciBtb2R1bGUuIElmIHRoZSBuYXRpdmUgbW9kdWxlIGlzIG5vdCBpbnN0YWxsZWQsIGZ1bmN0aW9uIGNhbGxzXG4gKiBhcmUganVzdCBuby1vcHMuXG4gKlxuICogVHlwaWNhbCBiZWhhdmlvciBpcyB0aGF0IGBzZXRDb250ZXh0YCBpcyBjYWxsZWQgd2hlbiBhIG5ldyBzY3JlZW4gaXMgbG9hZGVkIChlLmcuIHZpYSBhXG4gKiBuYXZpZ2F0aW9uIGludGVncmF0aW9uKSwgYW5kIHRoZW4gYGJlZ2luU2Nyb2xsYCBpcyBjYWxsZWQgYnkgYFNjcm9sbFJlc3BvbmRlcmAgYXQgd2hpY2ggcG9pbnQgdGhlXG4gKiBuYXRpdmUgbW9kdWxlIHRoZW4gYmVnaW5zIHRyYWNraW5nIGZyYW1lIGRyb3BzLiBXaGVuIGBTY3JvbGxSZXNwb25kZXJgIGNhbGxzIGBlbmRTY3JvbGxgLCB0aGVcbiAqIG5hdGl2ZSBtb2R1bGUgZ2F0aGVycyB1cCBhbGwgaXQncyBmcmFtZSBkcm9wIGRhdGEgYW5kIHJlcG9ydHMgaXQgdmlhIGFuIGFuYWx5dGljcyBwaXBlbGluZSBmb3JcbiAqIGFuYWx5c2lzLlxuICpcbiAqIE5vdGUgdGhhdCBgYmVnaW5TY3JvbGxgIG1heSBiZSBjYWxsZWQgbXVsdGlwbGUgdGltZXMgYnkgYFNjcm9sbFJlc3BvbmRlcmAgLSB1bmNsZWFyIGlmIHRoYXQncyBhXG4gKiBidWcsIGJ1dCB0aGUgbmF0aXZlIG1vZHVsZSBzaG91bGQgYmUgcm9idXN0IHRvIHRoYXQuXG4gKlxuICogSW4gdGhlIGZ1dHVyZSB3ZSBtYXkgYWRkIHN1cHBvcnQgZm9yIHRyYWNraW5nIGZyYW1lIGRyb3BzIGluIG90aGVyIHR5cGVzIG9mIGludGVyYWN0aW9ucyBiZXlvbmRcbiAqIHNjcm9sbGluZy5cbiAqL1xuY29uc3QgRnJhbWVSYXRlTG9nZ2VyID0ge1xuICAvKipcbiAgICogRW5hYmxlIGBkZWJ1Z2AgdG8gc2VlIGxvY2FsIGxvZ3Mgb2Ygd2hhdCdzIGdvaW5nIG9uLlxuICAgKi9cbiAgc2V0R2xvYmFsT3B0aW9uczogZnVuY3Rpb24gKG9wdGlvbnM6IHtkZWJ1Zz86IGJvb2xlYW4sIC4uLn0pIHtcbiAgICBpZiAob3B0aW9ucy5kZWJ1ZyAhPT0gdW5kZWZpbmVkKSB7XG4gICAgICBpbnZhcmlhbnQoXG4gICAgICAgIE5hdGl2ZUZyYW1lUmF0ZUxvZ2dlcixcbiAgICAgICAgJ1RyeWluZyB0byBkZWJ1ZyBGcmFtZVJhdGVMb2dnZXIgd2l0aG91dCB0aGUgbmF0aXZlIG1vZHVsZSEnLFxuICAgICAgKTtcbiAgICB9XG4gICAgTmF0aXZlRnJhbWVSYXRlTG9nZ2VyPy5zZXRHbG9iYWxPcHRpb25zKHtcbiAgICAgIGRlYnVnOiAhIW9wdGlvbnMuZGVidWcsXG4gICAgfSk7XG4gIH0sXG5cbiAgLyoqXG4gICAqIE11c3QgY2FsbCBgc2V0Q29udGV4dGAgYmVmb3JlIGFueSBldmVudHMgY2FuIGJlIHByb3Blcmx5IHRyYWNrZWQsIHdoaWNoIGlzIGRvbmUgYXV0b21hdGljYWxseVxuICAgKiBpbiBgQXBwUmVnaXN0cnlgLCBidXQgbmF2aWdhdGlvbiBpcyBhbHNvIGEgY29tbW9uIHBsYWNlIHRvIGhvb2sgaW4uXG4gICAqL1xuICBzZXRDb250ZXh0OiBmdW5jdGlvbiAoY29udGV4dDogc3RyaW5nKSB7XG4gICAgTmF0aXZlRnJhbWVSYXRlTG9nZ2VyPy5zZXRDb250ZXh0KGNvbnRleHQpO1xuICB9LFxuXG4gIC8qKlxuICAgKiBDYWxsZWQgaW4gYFNjcm9sbFJlc3BvbmRlcmAgc28gYW55IGNvbXBvbmVudCB0aGF0IHVzZXMgdGhhdCBtb2R1bGUgd2lsbCBoYW5kbGUgdGhpc1xuICAgKiBhdXRvbWF0aWNhbGx5LlxuICAgKi9cbiAgYmVnaW5TY3JvbGwoKSB7XG4gICAgTmF0aXZlRnJhbWVSYXRlTG9nZ2VyPy5iZWdpblNjcm9sbCgpO1xuICB9LFxuXG4gIC8qKlxuICAgKiBDYWxsZWQgaW4gYFNjcm9sbFJlc3BvbmRlcmAgc28gYW55IGNvbXBvbmVudCB0aGF0IHVzZXMgdGhhdCBtb2R1bGUgd2lsbCBoYW5kbGUgdGhpc1xuICAgKiBhdXRvbWF0aWNhbGx5LlxuICAgKi9cbiAgZW5kU2Nyb2xsKCkge1xuICAgIE5hdGl2ZUZyYW1lUmF0ZUxvZ2dlcj8uZW5kU2Nyb2xsKCk7XG4gIH0sXG59O1xuXG5tb2R1bGUuZXhwb3J0cyA9IEZyYW1lUmF0ZUxvZ2dlcjtcbiJdLCJtYXBwaW5ncyI6IjtBQVVBLElBQUFBLHNCQUFBLEdBQUFDLHNCQUFBLENBQUFDLE9BQUE7QUFFQSxJQUFNQyxTQUFTLEdBQUdELE9BQU8sQ0FBQyxXQUFXLENBQUM7QUFrQnRDLElBQU1FLGVBQWUsR0FBRztFQUl0QkMsZ0JBQWdCLEVBQUUsU0FBbEJBLGdCQUFnQkEsQ0FBWUMsT0FBK0IsRUFBRTtJQUMzRCxJQUFJQSxPQUFPLENBQUNDLEtBQUssS0FBS0MsU0FBUyxFQUFFO01BQy9CTCxTQUFTLENBQ1BNLDhCQUFxQixFQUNyQiw0REFDRixDQUFDO0lBQ0g7SUFDQUEsOEJBQXFCLFlBQXJCQSw4QkFBcUIsQ0FBRUosZ0JBQWdCLENBQUM7TUFDdENFLEtBQUssRUFBRSxDQUFDLENBQUNELE9BQU8sQ0FBQ0M7SUFDbkIsQ0FBQyxDQUFDO0VBQ0osQ0FBQztFQU1ERyxVQUFVLEVBQUUsU0FBWkEsVUFBVUEsQ0FBWUMsT0FBZSxFQUFFO0lBQ3JDRiw4QkFBcUIsWUFBckJBLDhCQUFxQixDQUFFQyxVQUFVLENBQUNDLE9BQU8sQ0FBQztFQUM1QyxDQUFDO0VBTURDLFdBQVcsV0FBWEEsV0FBV0EsQ0FBQSxFQUFHO0lBQ1pILDhCQUFxQixZQUFyQkEsOEJBQXFCLENBQUVHLFdBQVcsQ0FBQyxDQUFDO0VBQ3RDLENBQUM7RUFNREMsU0FBUyxXQUFUQSxTQUFTQSxDQUFBLEVBQUc7SUFDVkosOEJBQXFCLFlBQXJCQSw4QkFBcUIsQ0FBRUksU0FBUyxDQUFDLENBQUM7RUFDcEM7QUFDRixDQUFDO0FBRURDLE1BQU0sQ0FBQ0MsT0FBTyxHQUFHWCxlQUFlIiwiaWdub3JlTGlzdCI6W119