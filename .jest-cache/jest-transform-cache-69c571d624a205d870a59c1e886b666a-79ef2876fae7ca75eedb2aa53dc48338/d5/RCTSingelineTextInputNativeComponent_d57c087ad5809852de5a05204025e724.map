{"version": 3, "names": ["NativeComponentRegistry", "_interopRequireWildcard", "require", "_codegenNativeCommands", "_interopRequireDefault", "_RCTTextInputViewConfig", "_getRequireWildcardCache", "e", "WeakMap", "r", "t", "__esModule", "default", "has", "get", "n", "__proto__", "a", "Object", "defineProperty", "getOwnPropertyDescriptor", "u", "hasOwnProperty", "call", "i", "set", "Commands", "exports", "codegenNativeCommands", "supportedCommands", "__INTERNAL_VIEW_CONFIG", "assign", "uiViewClassName", "RCTTextInputViewConfig", "SinglelineTextInputNativeComponent", "_default"], "sources": ["RCTSingelineTextInputNativeComponent.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @flow strict-local\n * @format\n */\n\nimport type {\n  HostComponent,\n  PartialViewConfig,\n} from '../../Renderer/shims/ReactNativeTypes';\nimport type {TextInputNativeCommands} from './TextInputNativeCommands';\n\nimport * as NativeComponentRegistry from '../../NativeComponent/NativeComponentRegistry';\nimport codegenNativeCommands from '../../Utilities/codegenNativeCommands';\nimport RCTTextInputViewConfig from './RCTTextInputViewConfig';\n\ntype NativeType = HostComponent<{...}>;\n\ntype NativeCommands = TextInputNativeCommands<NativeType>;\n\nexport const Commands: NativeCommands = codegenNativeCommands<NativeCommands>({\n  supportedCommands: ['focus', 'blur', 'setTextAndSelection'],\n});\n\nexport const __INTERNAL_VIEW_CONFIG: PartialViewConfig = {\n  uiViewClassName: 'RCTSinglelineTextInputView',\n  ...RCTTextInputViewConfig,\n};\n\nconst SinglelineTextInputNativeComponent: HostComponent<{...}> =\n  NativeComponentRegistry.get<{...}>(\n    'RCTSinglelineTextInputView',\n    () => __INTERNAL_VIEW_CONFIG,\n  );\n\n// flowlint-next-line unclear-type:off\nexport default ((SinglelineTextInputNativeComponent: any): HostComponent<{\n  ...\n}>);\n"], "mappings": ";;;;;AAgBA,IAAAA,uBAAA,GAAAC,uBAAA,CAAAC,OAAA;AACA,IAAAC,sBAAA,GAAAC,sBAAA,CAAAF,OAAA;AACA,IAAAG,uBAAA,GAAAD,sBAAA,CAAAF,OAAA;AAA8D,SAAAI,yBAAAC,CAAA,6BAAAC,OAAA,mBAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,yBAAAC,CAAA,WAAAA,CAAA,GAAAG,CAAA,GAAAD,CAAA,KAAAF,CAAA;AAAA,SAAAN,wBAAAM,CAAA,EAAAE,CAAA,SAAAA,CAAA,IAAAF,CAAA,IAAAA,CAAA,CAAAI,UAAA,SAAAJ,CAAA,eAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,WAAAK,OAAA,EAAAL,CAAA,QAAAG,CAAA,GAAAJ,wBAAA,CAAAG,CAAA,OAAAC,CAAA,IAAAA,CAAA,CAAAG,GAAA,CAAAN,CAAA,UAAAG,CAAA,CAAAI,GAAA,CAAAP,CAAA,OAAAQ,CAAA,KAAAC,SAAA,UAAAC,CAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,CAAA,IAAAd,CAAA,oBAAAc,CAAA,OAAAC,cAAA,CAAAC,IAAA,CAAAhB,CAAA,EAAAc,CAAA,SAAAG,CAAA,GAAAP,CAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAb,CAAA,EAAAc,CAAA,UAAAG,CAAA,KAAAA,CAAA,CAAAV,GAAA,IAAAU,CAAA,CAAAC,GAAA,IAAAP,MAAA,CAAAC,cAAA,CAAAJ,CAAA,EAAAM,CAAA,EAAAG,CAAA,IAAAT,CAAA,CAAAM,CAAA,IAAAd,CAAA,CAAAc,CAAA,YAAAN,CAAA,CAAAH,OAAA,GAAAL,CAAA,EAAAG,CAAA,IAAAA,CAAA,CAAAe,GAAA,CAAAlB,CAAA,EAAAQ,CAAA,GAAAA,CAAA;AAMvD,IAAMW,QAAwB,GAAAC,OAAA,CAAAD,QAAA,GAAG,IAAAE,8BAAqB,EAAiB;EAC5EC,iBAAiB,EAAE,CAAC,OAAO,EAAE,MAAM,EAAE,qBAAqB;AAC5D,CAAC,CAAC;AAEK,IAAMC,sBAAyC,GAAAH,OAAA,CAAAG,sBAAA,GAAAZ,MAAA,CAAAa,MAAA;EACpDC,eAAe,EAAE;AAA4B,GAC1CC,+BAAsB,CAC1B;AAED,IAAMC,kCAAwD,GAC5DlC,uBAAuB,CAACc,GAAG,CACzB,4BAA4B,EAC5B;EAAA,OAAMgB,sBAAsB;AAAA,CAC9B,CAAC;AAAC,IAAAK,QAAA,GAAAR,OAAA,CAAAf,OAAA,GAGasB,kCAAkC", "ignoreList": []}