c21ed47d4f594ec22cc9731388309905
"use strict";

/* istanbul ignore next */
function cov_2kucvge68b() {
  var path = "/Users/<USER>/projects/react-native/payment-module/src/presentation/payment-confirm/hook.ts";
  var hash = "cd215afe54b82ac9ce192a61b080b531e11438d6";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/projects/react-native/payment-module/src/presentation/payment-confirm/hook.ts",
    statementMap: {
      "0": {
        start: {
          line: 3,
          column: 29
        },
        end: {
          line: 3,
          column: 84
        }
      },
      "1": {
        start: {
          line: 4,
          column: 25
        },
        end: {
          line: 4,
          column: 99
        }
      },
      "2": {
        start: {
          line: 5,
          column: 22
        },
        end: {
          line: 5,
          column: 93
        }
      },
      "3": {
        start: {
          line: 6,
          column: 22
        },
        end: {
          line: 10,
          column: 1
        }
      },
      "4": {
        start: {
          line: 7,
          column: 2
        },
        end: {
          line: 9,
          column: 4
        }
      },
      "5": {
        start: {
          line: 11,
          column: 0
        },
        end: {
          line: 13,
          column: 3
        }
      },
      "6": {
        start: {
          line: 14,
          column: 15
        },
        end: {
          line: 14,
          column: 50
        }
      },
      "7": {
        start: {
          line: 15,
          column: 20
        },
        end: {
          line: 15,
          column: 73
        }
      },
      "8": {
        start: {
          line: 16,
          column: 31
        },
        end: {
          line: 16,
          column: 64
        }
      },
      "9": {
        start: {
          line: 17,
          column: 13
        },
        end: {
          line: 17,
          column: 42
        }
      },
      "10": {
        start: {
          line: 18,
          column: 14
        },
        end: {
          line: 18,
          column: 30
        }
      },
      "11": {
        start: {
          line: 19,
          column: 20
        },
        end: {
          line: 19,
          column: 51
        }
      },
      "12": {
        start: {
          line: 20,
          column: 18
        },
        end: {
          line: 20,
          column: 52
        }
      },
      "13": {
        start: {
          line: 21,
          column: 19
        },
        end: {
          line: 21,
          column: 52
        }
      },
      "14": {
        start: {
          line: 22,
          column: 24
        },
        end: {
          line: 129,
          column: 1
        }
      },
      "15": {
        start: {
          line: 24,
          column: 14
        },
        end: {
          line: 24,
          column: 38
        }
      },
      "16": {
        start: {
          line: 25,
          column: 19
        },
        end: {
          line: 25,
          column: 48
        }
      },
      "17": {
        start: {
          line: 26,
          column: 22
        },
        end: {
          line: 26,
          column: 34
        }
      },
      "18": {
        start: {
          line: 27,
          column: 18
        },
        end: {
          line: 27,
          column: 43
        }
      },
      "19": {
        start: {
          line: 28,
          column: 28
        },
        end: {
          line: 28,
          column: 51
        }
      },
      "20": {
        start: {
          line: 29,
          column: 16
        },
        end: {
          line: 29,
          column: 79
        }
      },
      "21": {
        start: {
          line: 30,
          column: 16
        },
        end: {
          line: 30,
          column: 113
        }
      },
      "22": {
        start: {
          line: 31,
          column: 13
        },
        end: {
          line: 31,
          column: 41
        }
      },
      "23": {
        start: {
          line: 32,
          column: 12
        },
        end: {
          line: 32,
          column: 49
        }
      },
      "24": {
        start: {
          line: 33,
          column: 16
        },
        end: {
          line: 33,
          column: 24
        }
      },
      "25": {
        start: {
          line: 34,
          column: 17
        },
        end: {
          line: 34,
          column: 25
        }
      },
      "26": {
        start: {
          line: 35,
          column: 20
        },
        end: {
          line: 45,
          column: 19
        }
      },
      "27": {
        start: {
          line: 37,
          column: 4
        },
        end: {
          line: 40,
          column: 5
        }
      },
      "28": {
        start: {
          line: 39,
          column: 6
        },
        end: {
          line: 39,
          column: 287
        }
      },
      "29": {
        start: {
          line: 41,
          column: 23
        },
        end: {
          line: 43,
          column: 9
        }
      },
      "30": {
        start: {
          line: 42,
          column: 6
        },
        end: {
          line: 42,
          column: 38
        }
      },
      "31": {
        start: {
          line: 44,
          column: 4
        },
        end: {
          line: 44,
          column: 24
        }
      },
      "32": {
        start: {
          line: 46,
          column: 18
        },
        end: {
          line: 48,
          column: 3
        }
      },
      "33": {
        start: {
          line: 47,
          column: 4
        },
        end: {
          line: 47,
          column: 19
        }
      },
      "34": {
        start: {
          line: 49,
          column: 21
        },
        end: {
          line: 70,
          column: 5
        }
      },
      "35": {
        start: {
          line: 50,
          column: 16
        },
        end: {
          line: 66,
          column: 6
        }
      },
      "36": {
        start: {
          line: 51,
          column: 6
        },
        end: {
          line: 65,
          column: 7
        }
      },
      "37": {
        start: {
          line: 52,
          column: 8
        },
        end: {
          line: 52,
          column: 25
        }
      },
      "38": {
        start: {
          line: 53,
          column: 21
        },
        end: {
          line: 53,
          column: 67
        }
      },
      "39": {
        start: {
          line: 54,
          column: 8
        },
        end: {
          line: 54,
          column: 46
        }
      },
      "40": {
        start: {
          line: 55,
          column: 21
        },
        end: {
          line: 55,
          column: 107
        }
      },
      "41": {
        start: {
          line: 56,
          column: 8
        },
        end: {
          line: 56,
          column: 38
        }
      },
      "42": {
        start: {
          line: 57,
          column: 8
        },
        end: {
          line: 57,
          column: 26
        }
      },
      "43": {
        start: {
          line: 58,
          column: 8
        },
        end: {
          line: 64,
          column: 9
        }
      },
      "44": {
        start: {
          line: 60,
          column: 10
        },
        end: {
          line: 60,
          column: 153
        }
      },
      "45": {
        start: {
          line: 62,
          column: 10
        },
        end: {
          line: 62,
          column: 57
        }
      },
      "46": {
        start: {
          line: 63,
          column: 10
        },
        end: {
          line: 63,
          column: 30
        }
      },
      "47": {
        start: {
          line: 67,
          column: 4
        },
        end: {
          line: 69,
          column: 6
        }
      },
      "48": {
        start: {
          line: 68,
          column: 6
        },
        end: {
          line: 68,
          column: 42
        }
      },
      "49": {
        start: {
          line: 71,
          column: 23
        },
        end: {
          line: 95,
          column: 5
        }
      },
      "50": {
        start: {
          line: 72,
          column: 16
        },
        end: {
          line: 91,
          column: 6
        }
      },
      "51": {
        start: {
          line: 73,
          column: 19
        },
        end: {
          line: 76,
          column: 8
        }
      },
      "52": {
        start: {
          line: 77,
          column: 6
        },
        end: {
          line: 77,
          column: 36
        }
      },
      "53": {
        start: {
          line: 78,
          column: 6
        },
        end: {
          line: 90,
          column: 7
        }
      },
      "54": {
        start: {
          line: 80,
          column: 8
        },
        end: {
          line: 80,
          column: 26
        }
      },
      "55": {
        start: {
          line: 81,
          column: 8
        },
        end: {
          line: 86,
          column: 11
        }
      },
      "56": {
        start: {
          line: 88,
          column: 8
        },
        end: {
          line: 88,
          column: 26
        }
      },
      "57": {
        start: {
          line: 89,
          column: 8
        },
        end: {
          line: 89,
          column: 55
        }
      },
      "58": {
        start: {
          line: 92,
          column: 4
        },
        end: {
          line: 94,
          column: 6
        }
      },
      "59": {
        start: {
          line: 93,
          column: 6
        },
        end: {
          line: 93,
          column: 42
        }
      },
      "60": {
        start: {
          line: 96,
          column: 30
        },
        end: {
          line: 108,
          column: 3
        }
      },
      "61": {
        start: {
          line: 98,
          column: 4
        },
        end: {
          line: 107,
          column: 7
        }
      },
      "62": {
        start: {
          line: 105,
          column: 8
        },
        end: {
          line: 105,
          column: 34
        }
      },
      "63": {
        start: {
          line: 109,
          column: 25
        },
        end: {
          line: 111,
          column: 3
        }
      },
      "64": {
        start: {
          line: 110,
          column: 4
        },
        end: {
          line: 110,
          column: 13
        }
      },
      "65": {
        start: {
          line: 112,
          column: 15
        },
        end: {
          line: 119,
          column: 3
        }
      },
      "66": {
        start: {
          line: 113,
          column: 4
        },
        end: {
          line: 118,
          column: 7
        }
      },
      "67": {
        start: {
          line: 120,
          column: 2
        },
        end: {
          line: 128,
          column: 4
        }
      },
      "68": {
        start: {
          line: 130,
          column: 0
        },
        end: {
          line: 130,
          column: 36
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 6,
            column: 54
          },
          end: {
            line: 6,
            column: 55
          }
        },
        loc: {
          start: {
            line: 6,
            column: 69
          },
          end: {
            line: 10,
            column: 1
          }
        },
        line: 6
      },
      "1": {
        name: "usePaymentConfirm",
        decl: {
          start: {
            line: 22,
            column: 33
          },
          end: {
            line: 22,
            column: 50
          }
        },
        loc: {
          start: {
            line: 22,
            column: 53
          },
          end: {
            line: 129,
            column: 1
          }
        },
        line: 22
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 35,
            column: 41
          },
          end: {
            line: 35,
            column: 42
          }
        },
        loc: {
          start: {
            line: 35,
            column: 53
          },
          end: {
            line: 45,
            column: 3
          }
        },
        line: 35
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 41,
            column: 209
          },
          end: {
            line: 41,
            column: 210
          }
        },
        loc: {
          start: {
            line: 41,
            column: 230
          },
          end: {
            line: 43,
            column: 5
          }
        },
        line: 41
      },
      "4": {
        name: "onConfirm",
        decl: {
          start: {
            line: 46,
            column: 27
          },
          end: {
            line: 46,
            column: 36
          }
        },
        loc: {
          start: {
            line: 46,
            column: 39
          },
          end: {
            line: 48,
            column: 3
          }
        },
        line: 46
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 49,
            column: 21
          },
          end: {
            line: 49,
            column: 22
          }
        },
        loc: {
          start: {
            line: 49,
            column: 33
          },
          end: {
            line: 70,
            column: 3
          }
        },
        line: 49
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 50,
            column: 48
          },
          end: {
            line: 50,
            column: 49
          }
        },
        loc: {
          start: {
            line: 50,
            column: 61
          },
          end: {
            line: 66,
            column: 5
          }
        },
        line: 50
      },
      "7": {
        name: "paymentOrder",
        decl: {
          start: {
            line: 67,
            column: 20
          },
          end: {
            line: 67,
            column: 32
          }
        },
        loc: {
          start: {
            line: 67,
            column: 35
          },
          end: {
            line: 69,
            column: 5
          }
        },
        line: 67
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 71,
            column: 23
          },
          end: {
            line: 71,
            column: 24
          }
        },
        loc: {
          start: {
            line: 71,
            column: 35
          },
          end: {
            line: 95,
            column: 3
          }
        },
        line: 71
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 72,
            column: 48
          },
          end: {
            line: 72,
            column: 49
          }
        },
        loc: {
          start: {
            line: 72,
            column: 63
          },
          end: {
            line: 91,
            column: 5
          }
        },
        line: 72
      },
      "10": {
        name: "getOrderStatus",
        decl: {
          start: {
            line: 92,
            column: 20
          },
          end: {
            line: 92,
            column: 34
          }
        },
        loc: {
          start: {
            line: 92,
            column: 39
          },
          end: {
            line: 94,
            column: 5
          }
        },
        line: 92
      },
      "11": {
        name: "endTransactionConfirm",
        decl: {
          start: {
            line: 96,
            column: 39
          },
          end: {
            line: 96,
            column: 60
          }
        },
        loc: {
          start: {
            line: 96,
            column: 63
          },
          end: {
            line: 108,
            column: 3
          }
        },
        line: 96
      },
      "12": {
        name: "onCancel",
        decl: {
          start: {
            line: 104,
            column: 25
          },
          end: {
            line: 104,
            column: 33
          }
        },
        loc: {
          start: {
            line: 104,
            column: 36
          },
          end: {
            line: 106,
            column: 7
          }
        },
        line: 104
      },
      "13": {
        name: "endOfTransaction",
        decl: {
          start: {
            line: 109,
            column: 34
          },
          end: {
            line: 109,
            column: 50
          }
        },
        loc: {
          start: {
            line: 109,
            column: 53
          },
          end: {
            line: 111,
            column: 3
          }
        },
        line: 109
      },
      "14": {
        name: "goHome",
        decl: {
          start: {
            line: 112,
            column: 24
          },
          end: {
            line: 112,
            column: 30
          }
        },
        loc: {
          start: {
            line: 112,
            column: 33
          },
          end: {
            line: 119,
            column: 3
          }
        },
        line: 112
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 6,
            column: 22
          },
          end: {
            line: 10,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 6,
            column: 22
          },
          end: {
            line: 6,
            column: 26
          }
        }, {
          start: {
            line: 6,
            column: 30
          },
          end: {
            line: 6,
            column: 50
          }
        }, {
          start: {
            line: 6,
            column: 54
          },
          end: {
            line: 10,
            column: 1
          }
        }],
        line: 6
      },
      "1": {
        loc: {
          start: {
            line: 7,
            column: 9
          },
          end: {
            line: 9,
            column: 3
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 7,
            column: 33
          },
          end: {
            line: 7,
            column: 36
          }
        }, {
          start: {
            line: 7,
            column: 39
          },
          end: {
            line: 9,
            column: 3
          }
        }],
        line: 7
      },
      "2": {
        loc: {
          start: {
            line: 7,
            column: 9
          },
          end: {
            line: 7,
            column: 30
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 7,
            column: 9
          },
          end: {
            line: 7,
            column: 12
          }
        }, {
          start: {
            line: 7,
            column: 16
          },
          end: {
            line: 7,
            column: 30
          }
        }],
        line: 7
      },
      "3": {
        loc: {
          start: {
            line: 29,
            column: 16
          },
          end: {
            line: 29,
            column: 79
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 29,
            column: 51
          },
          end: {
            line: 29,
            column: 55
          }
        }, {
          start: {
            line: 29,
            column: 58
          },
          end: {
            line: 29,
            column: 79
          }
        }],
        line: 29
      },
      "4": {
        loc: {
          start: {
            line: 30,
            column: 16
          },
          end: {
            line: 30,
            column: 113
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 30,
            column: 73
          },
          end: {
            line: 30,
            column: 79
          }
        }, {
          start: {
            line: 30,
            column: 82
          },
          end: {
            line: 30,
            column: 113
          }
        }],
        line: 30
      },
      "5": {
        loc: {
          start: {
            line: 37,
            column: 4
          },
          end: {
            line: 40,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 37,
            column: 4
          },
          end: {
            line: 40,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 37
      },
      "6": {
        loc: {
          start: {
            line: 37,
            column: 8
          },
          end: {
            line: 37,
            column: 469
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 37,
            column: 8
          },
          end: {
            line: 37,
            column: 159
          }
        }, {
          start: {
            line: 37,
            column: 163
          },
          end: {
            line: 37,
            column: 315
          }
        }, {
          start: {
            line: 37,
            column: 319
          },
          end: {
            line: 37,
            column: 469
          }
        }],
        line: 37
      },
      "7": {
        loc: {
          start: {
            line: 37,
            column: 9
          },
          end: {
            line: 37,
            column: 115
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 37,
            column: 73
          },
          end: {
            line: 37,
            column: 79
          }
        }, {
          start: {
            line: 37,
            column: 82
          },
          end: {
            line: 37,
            column: 115
          }
        }],
        line: 37
      },
      "8": {
        loc: {
          start: {
            line: 37,
            column: 164
          },
          end: {
            line: 37,
            column: 272
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 37,
            column: 229
          },
          end: {
            line: 37,
            column: 235
          }
        }, {
          start: {
            line: 37,
            column: 238
          },
          end: {
            line: 37,
            column: 272
          }
        }],
        line: 37
      },
      "9": {
        loc: {
          start: {
            line: 37,
            column: 320
          },
          end: {
            line: 37,
            column: 428
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 37,
            column: 385
          },
          end: {
            line: 37,
            column: 391
          }
        }, {
          start: {
            line: 37,
            column: 394
          },
          end: {
            line: 37,
            column: 428
          }
        }],
        line: 37
      },
      "10": {
        loc: {
          start: {
            line: 39,
            column: 13
          },
          end: {
            line: 39,
            column: 286
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 39,
            column: 248
          },
          end: {
            line: 39,
            column: 254
          }
        }, {
          start: {
            line: 39,
            column: 257
          },
          end: {
            line: 39,
            column: 286
          }
        }],
        line: 39
      },
      "11": {
        loc: {
          start: {
            line: 39,
            column: 13
          },
          end: {
            line: 39,
            column: 245
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 39,
            column: 13
          },
          end: {
            line: 39,
            column: 75
          }
        }, {
          start: {
            line: 39,
            column: 79
          },
          end: {
            line: 39,
            column: 167
          }
        }, {
          start: {
            line: 39,
            column: 171
          },
          end: {
            line: 39,
            column: 245
          }
        }],
        line: 39
      },
      "12": {
        loc: {
          start: {
            line: 41,
            column: 23
          },
          end: {
            line: 43,
            column: 9
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 41,
            column: 171
          },
          end: {
            line: 41,
            column: 177
          }
        }, {
          start: {
            line: 41,
            column: 180
          },
          end: {
            line: 43,
            column: 9
          }
        }],
        line: 41
      },
      "13": {
        loc: {
          start: {
            line: 41,
            column: 23
          },
          end: {
            line: 41,
            column: 168
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 41,
            column: 23
          },
          end: {
            line: 41,
            column: 42
          }
        }, {
          start: {
            line: 41,
            column: 46
          },
          end: {
            line: 41,
            column: 100
          }
        }, {
          start: {
            line: 41,
            column: 104
          },
          end: {
            line: 41,
            column: 168
          }
        }],
        line: 41
      },
      "14": {
        loc: {
          start: {
            line: 42,
            column: 20
          },
          end: {
            line: 42,
            column: 36
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 42,
            column: 20
          },
          end: {
            line: 42,
            column: 31
          }
        }, {
          start: {
            line: 42,
            column: 35
          },
          end: {
            line: 42,
            column: 36
          }
        }],
        line: 42
      },
      "15": {
        loc: {
          start: {
            line: 51,
            column: 6
          },
          end: {
            line: 65,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 51,
            column: 6
          },
          end: {
            line: 65,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 51
      },
      "16": {
        loc: {
          start: {
            line: 58,
            column: 8
          },
          end: {
            line: 64,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 58,
            column: 8
          },
          end: {
            line: 64,
            column: 9
          }
        }, {
          start: {
            line: 61,
            column: 15
          },
          end: {
            line: 64,
            column: 9
          }
        }],
        line: 58
      },
      "17": {
        loc: {
          start: {
            line: 60,
            column: 25
          },
          end: {
            line: 60,
            column: 151
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 60,
            column: 124
          },
          end: {
            line: 60,
            column: 146
          }
        }, {
          start: {
            line: 60,
            column: 149
          },
          end: {
            line: 60,
            column: 151
          }
        }],
        line: 60
      },
      "18": {
        loc: {
          start: {
            line: 60,
            column: 51
          },
          end: {
            line: 60,
            column: 112
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 60,
            column: 73
          },
          end: {
            line: 60,
            column: 79
          }
        }, {
          start: {
            line: 60,
            column: 82
          },
          end: {
            line: 60,
            column: 112
          }
        }],
        line: 60
      },
      "19": {
        loc: {
          start: {
            line: 78,
            column: 6
          },
          end: {
            line: 90,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 78,
            column: 6
          },
          end: {
            line: 90,
            column: 7
          }
        }, {
          start: {
            line: 87,
            column: 13
          },
          end: {
            line: 90,
            column: 7
          }
        }],
        line: 78
      },
      "20": {
        loc: {
          start: {
            line: 83,
            column: 31
          },
          end: {
            line: 83,
            column: 204
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 83,
            column: 139
          },
          end: {
            line: 83,
            column: 160
          }
        }, {
          start: {
            line: 83,
            column: 163
          },
          end: {
            line: 83,
            column: 204
          }
        }],
        line: 83
      },
      "21": {
        loc: {
          start: {
            line: 83,
            column: 56
          },
          end: {
            line: 83,
            column: 127
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 83,
            column: 95
          },
          end: {
            line: 83,
            column: 101
          }
        }, {
          start: {
            line: 83,
            column: 104
          },
          end: {
            line: 83,
            column: 127
          }
        }],
        line: 83
      },
      "22": {
        loc: {
          start: {
            line: 84,
            column: 28
          },
          end: {
            line: 84,
            column: 156
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 84,
            column: 121
          },
          end: {
            line: 84,
            column: 127
          }
        }, {
          start: {
            line: 84,
            column: 130
          },
          end: {
            line: 84,
            column: 156
          }
        }],
        line: 84
      },
      "23": {
        loc: {
          start: {
            line: 84,
            column: 28
          },
          end: {
            line: 84,
            column: 118
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 84,
            column: 28
          },
          end: {
            line: 84,
            column: 65
          }
        }, {
          start: {
            line: 84,
            column: 69
          },
          end: {
            line: 84,
            column: 118
          }
        }],
        line: 84
      },
      "24": {
        loc: {
          start: {
            line: 98,
            column: 4
          },
          end: {
            line: 107,
            column: 6
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 98,
            column: 4
          },
          end: {
            line: 98,
            column: 95
          }
        }, {
          start: {
            line: 98,
            column: 99
          },
          end: {
            line: 107,
            column: 6
          }
        }],
        line: 98
      },
      "25": {
        loc: {
          start: {
            line: 113,
            column: 4
          },
          end: {
            line: 118,
            column: 6
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 113,
            column: 4
          },
          end: {
            line: 113,
            column: 22
          }
        }, {
          start: {
            line: 113,
            column: 26
          },
          end: {
            line: 118,
            column: 6
          }
        }],
        line: 113
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0
    },
    b: {
      "0": [0, 0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0, 0],
      "12": [0, 0],
      "13": [0, 0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0],
      "20": [0, 0],
      "21": [0, 0],
      "22": [0, 0],
      "23": [0, 0],
      "24": [0, 0],
      "25": [0, 0]
    },
    inputSourceMap: {
      version: 3,
      file: undefined,
      names: ["native_1", "require", "ScreenNames_1", "__importDefault", "msb_host_shared_module_1", "i18n_1", "react_1", "DIContainer_1", "Constants_1", "PopupUtils_1", "usePaymentConfirm", "_paymentInfo$provider", "route", "useRoute", "navigation", "useNavigation", "_route$params", "params", "paymentInfo", "_route$params$hasPeri", "hasPeriod", "isTopup", "provider", "_ref", "useState", "_ref2", "_slicedToArray2", "default", "isLoading", "setLoading", "totalAmount", "useMemo", "_paymentInfo$paymentV", "_paymentInfo$paymentV2", "_paymentInfo$paymentV3", "_paymentInfo$billInfo", "paymentValidate", "paymentType", "PAYMENT_TYPE", "TOPUP_ACCOUNT", "TOPUP_CREDIT", "QR_PAYMENT", "_paymentInfo$paymentV4", "transferTransactionInformation", "instructedAmount", "amount", "_totalAmount", "billInfo", "billList", "reduce", "sum", "bill", "onConfirm", "paymentOrder", "_ref3", "_asyncToGenerator2", "Object", "assign", "console", "log", "result", "DIContainer", "getInstance", "getPaymentOrderUseCase", "execute", "status", "_paymentInfo$paymentV5", "getOrderStatus", "id", "showErrorPopup", "error", "goBack", "apply", "arguments", "_ref4", "getPaymentOrderStatusUseCase", "paymentMode", "_result$data$bankStat", "_result$data", "_result$data2", "navigate", "PaymentResultScreen", "paymentResultType", "data", "bankStatus", "PAYMENT_ORDER_STATUS", "REJECTED", "additionalInfo", "additions", "t24TraceCode", "_x", "endTransactionConfirm", "_msb_host_shared_modu", "hostSharedModule", "d", "domainService", "showPopup", "iconType", "title", "translate", "content", "cancelBtnText", "confirmBtnText", "onCancel", "endOfTransaction", "goHome", "reset", "index", "routes", "name", "exports"],
      sourceRoot: undefined,
      sources: ["/Users/<USER>/projects/react-native/payment-module/src/presentation/payment-confirm/hook.ts"],
      sourcesContent: ["import {RouteProp, useNavigation, useRoute, NavigationProp} from '@react-navigation/native';\n\nimport ScreenNames from '../../commons/ScreenNames';\nimport {PaymentStackParamList} from '../../navigation/PaymentStack';\nimport {hostSharedModule} from 'msb-host-shared-module';\nimport {translate} from '../../locales/i18n';\nimport {useMemo, useState} from 'react';\nimport {DIContainer} from '../../di/DIContainer';\nimport {PaymentOrderRequest} from '../../data/models/payment-order/PaymentOrderRequest';\nimport {PAYMENT_ORDER_STATUS, PAYMENT_TYPE} from '../../commons/Constants';\nimport {showErrorPopup} from '../../utils/PopupUtils';\n// import Utils from '../../utils/Utils';\n\nconst usePaymentConfirm = () => {\n  const route = useRoute<RouteProp<PaymentStackParamList, 'PaymentConfirmScreen'>>();\n  const navigation = useNavigation<NavigationProp<PaymentStackParamList, 'PaymentConfirmScreen'>>();\n\n  const {paymentInfo, hasPeriod = true} = route.params;\n\n  const isTopup = paymentInfo.provider?.isTopup();\n\n  const [isLoading, setLoading] = useState<boolean>(false);\n\n  const totalAmount = useMemo(() => {\n    if (\n      paymentInfo.paymentValidate?.paymentType === PAYMENT_TYPE.TOPUP_ACCOUNT ||\n      paymentInfo.paymentValidate?.paymentType === PAYMENT_TYPE.TOPUP_CREDIT ||\n      paymentInfo.paymentValidate?.paymentType === PAYMENT_TYPE.QR_PAYMENT\n    ) {\n      return paymentInfo.paymentValidate?.transferTransactionInformation?.instructedAmount?.amount;\n    }\n    const _totalAmount = paymentInfo?.billInfo?.billList?.reduce((sum, bill) => sum + (bill.amount || 0), 0);\n    return _totalAmount;\n  }, [paymentInfo]);\n\n  const onConfirm = () => {\n    // navigation.navigate(ScreenNames.PaymentResultScreen);\n    paymentOrder();\n  };\n\n  const paymentOrder = async () => {\n    if (paymentInfo.paymentValidate) {\n      setLoading(true);\n      const params: PaymentOrderRequest = {\n        ...paymentInfo.paymentValidate,\n      };\n      console.log('request params', params);\n      const result = await DIContainer.getInstance().getPaymentOrderUseCase().execute(params);\n      console.log('result', result);\n\n      setLoading(false);\n      if (result.status === 'SUCCESS') {\n        getOrderStatus(paymentInfo?.paymentValidate.id ?? '');\n      } else {\n        showErrorPopup(result.error);\n        navigation.goBack();\n      }\n    }\n  };\n\n  const getOrderStatus = async (id: string) => {\n    const result = await DIContainer.getInstance().getPaymentOrderStatusUseCase().execute({paymentMode: '', id: id});\n    console.log('result', result);\n    if (result.status === 'SUCCESS') {\n      setLoading(false);\n      navigation.navigate(ScreenNames.PaymentResultScreen, {\n        paymentInfo: {\n          ...paymentInfo,\n          paymentResultType: result.data?.bankStatus ?? PAYMENT_ORDER_STATUS.REJECTED,\n          additionalInfo: result.data?.additions?.t24TraceCode,\n        },\n      });\n    } else {\n      setLoading(false);\n      showErrorPopup(result.error);\n      // Utils.checkErrorSystem();\n    }\n  };\n\n  const endTransactionConfirm = () => {\n    hostSharedModule.d.domainService?.showPopup({\n      iconType: 'WARNING',\n      title: translate('paymentConfirm.endOfTransaction'),\n      content: translate('paymentConfirm.endOfTransactionDescription'),\n      cancelBtnText: translate('paymentConfirm.endOfTransaction'),\n      confirmBtnText: translate('paymentConfirm.close'),\n      onCancel: () => endOfTransaction(),\n    });\n  };\n\n  const endOfTransaction = () => {\n    goHome();\n  };\n\n  const goHome = () => {\n    navigation?.reset({\n      index: 0,\n      routes: [\n        {\n          name: 'SegmentStack',\n        },\n      ],\n    });\n  };\n\n  return {\n    onConfirm,\n    paymentInfo,\n    totalAmount,\n    endTransactionConfirm,\n    isLoading,\n    hasPeriod,\n    isTopup,\n  };\n};\n\nexport type Props = ReturnType<typeof usePaymentConfirm>;\n\nexport default usePaymentConfirm;\n"],
      mappings: ";;;;;;;;;;;;;AAAA,IAAAA,QAAA,GAAAC,OAAA;AAEA,IAAAC,aAAA,GAAAC,eAAA,CAAAF,OAAA;AAEA,IAAAG,wBAAA,GAAAH,OAAA;AACA,IAAAI,MAAA,GAAAJ,OAAA;AACA,IAAAK,OAAA,GAAAL,OAAA;AACA,IAAAM,aAAA,GAAAN,OAAA;AAEA,IAAAO,WAAA,GAAAP,OAAA;AACA,IAAAQ,YAAA,GAAAR,OAAA;AAGA,IAAMS,iBAAiB,GAAG,SAApBA,iBAAiBA,CAAA,EAAQ;EAAA,IAAAC,qBAAA;EAC7B,IAAMC,KAAK,GAAG,IAAAZ,QAAA,CAAAa,QAAQ,GAA4D;EAClF,IAAMC,UAAU,GAAG,IAAAd,QAAA,CAAAe,aAAa,GAAiE;EAEjG,IAAAC,aAAA,GAAwCJ,KAAK,CAACK,MAAM;IAA7CC,WAAW,GAAAF,aAAA,CAAXE,WAAW;IAAAC,qBAAA,GAAAH,aAAA,CAAEI,SAAS;IAATA,SAAS,GAAAD,qBAAA,cAAG,IAAI,GAAAA,qBAAA;EAEpC,IAAME,OAAO,IAAAV,qBAAA,GAAGO,WAAW,CAACI,QAAQ,qBAApBX,qBAAA,CAAsBU,OAAO,EAAE;EAE/C,IAAAE,IAAA,GAAgC,IAAAjB,OAAA,CAAAkB,QAAQ,EAAU,KAAK,CAAC;IAAAC,KAAA,OAAAC,eAAA,CAAAC,OAAA,EAAAJ,IAAA;IAAjDK,SAAS,GAAAH,KAAA;IAAEI,UAAU,GAAAJ,KAAA;EAE5B,IAAMK,WAAW,GAAG,IAAAxB,OAAA,CAAAyB,OAAO,EAAC,YAAK;IAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,qBAAA;IAC/B,IACE,EAAAH,qBAAA,GAAAd,WAAW,CAACkB,eAAe,qBAA3BJ,qBAAA,CAA6BK,WAAW,MAAK7B,WAAA,CAAA8B,YAAY,CAACC,aAAa,IACvE,EAAAN,sBAAA,GAAAf,WAAW,CAACkB,eAAe,qBAA3BH,sBAAA,CAA6BI,WAAW,MAAK7B,WAAA,CAAA8B,YAAY,CAACE,YAAY,IACtE,EAAAN,sBAAA,GAAAhB,WAAW,CAACkB,eAAe,qBAA3BF,sBAAA,CAA6BG,WAAW,MAAK7B,WAAA,CAAA8B,YAAY,CAACG,UAAU,EACpE;MAAA,IAAAC,sBAAA;MACA,QAAAA,sBAAA,GAAOxB,WAAW,CAACkB,eAAe,cAAAM,sBAAA,GAA3BA,sBAAA,CAA6BC,8BAA8B,cAAAD,sBAAA,GAA3DA,sBAAA,CAA6DE,gBAAgB,qBAA7EF,sBAAA,CAA+EG,MAAM;IAC9F;IACA,IAAMC,YAAY,GAAG5B,WAAW,aAAAiB,qBAAA,GAAXjB,WAAW,CAAE6B,QAAQ,cAAAZ,qBAAA,GAArBA,qBAAA,CAAuBa,QAAQ,qBAA/Bb,qBAAA,CAAiCc,MAAM,CAAC,UAACC,GAAG,EAAEC,IAAI;MAAA,OAAKD,GAAG,IAAIC,IAAI,CAACN,MAAM,IAAI,CAAC,CAAC;IAAA,GAAE,CAAC,CAAC;IACxG,OAAOC,YAAY;EACrB,CAAC,EAAE,CAAC5B,WAAW,CAAC,CAAC;EAEjB,IAAMkC,SAAS,GAAG,SAAZA,SAASA,CAAA,EAAQ;IAErBC,YAAY,EAAE;EAChB,CAAC;EAED,IAAMA,YAAY;IAAA,IAAAC,KAAA,OAAAC,kBAAA,CAAA5B,OAAA,EAAG,aAAW;MAC9B,IAAIT,WAAW,CAACkB,eAAe,EAAE;QAC/BP,UAAU,CAAC,IAAI,CAAC;QAChB,IAAMZ,MAAM,GAAAuC,MAAA,CAAAC,MAAA,KACPvC,WAAW,CAACkB,eAAe,CAC/B;QACDsB,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAE1C,MAAM,CAAC;QACrC,IAAM2C,MAAM,SAASrD,aAAA,CAAAsD,WAAW,CAACC,WAAW,EAAE,CAACC,sBAAsB,EAAE,CAACC,OAAO,CAAC/C,MAAM,CAAC;QACvFyC,OAAO,CAACC,GAAG,CAAC,QAAQ,EAAEC,MAAM,CAAC;QAE7B/B,UAAU,CAAC,KAAK,CAAC;QACjB,IAAI+B,MAAM,CAACK,MAAM,KAAK,SAAS,EAAE;UAAA,IAAAC,sBAAA;UAC/BC,cAAc,EAAAD,sBAAA,GAAChD,WAAW,oBAAXA,WAAW,CAAEkB,eAAe,CAACgC,EAAE,YAAAF,sBAAA,GAAI,EAAE,CAAC;QACvD,CAAC,MAAM;UACL,IAAAzD,YAAA,CAAA4D,cAAc,EAACT,MAAM,CAACU,KAAK,CAAC;UAC5BxD,UAAU,CAACyD,MAAM,EAAE;QACrB;MACF;IACF,CAAC;IAAA,gBAlBKlB,YAAYA,CAAA;MAAA,OAAAC,KAAA,CAAAkB,KAAA,OAAAC,SAAA;IAAA;EAAA,GAkBjB;EAED,IAAMN,cAAc;IAAA,IAAAO,KAAA,OAAAnB,kBAAA,CAAA5B,OAAA,EAAG,WAAOyC,EAAU,EAAI;MAC1C,IAAMR,MAAM,SAASrD,aAAA,CAAAsD,WAAW,CAACC,WAAW,EAAE,CAACa,4BAA4B,EAAE,CAACX,OAAO,CAAC;QAACY,WAAW,EAAE,EAAE;QAAER,EAAE,EAAEA;MAAE,CAAC,CAAC;MAChHV,OAAO,CAACC,GAAG,CAAC,QAAQ,EAAEC,MAAM,CAAC;MAC7B,IAAIA,MAAM,CAACK,MAAM,KAAK,SAAS,EAAE;QAAA,IAAAY,qBAAA,EAAAC,YAAA,EAAAC,aAAA;QAC/BlD,UAAU,CAAC,KAAK,CAAC;QACjBf,UAAU,CAACkE,QAAQ,CAAC9E,aAAA,CAAAyB,OAAW,CAACsD,mBAAmB,EAAE;UACnD/D,WAAW,EAAAsC,MAAA,CAAAC,MAAA,KACNvC,WAAW;YACdgE,iBAAiB,GAAAL,qBAAA,IAAAC,YAAA,GAAElB,MAAM,CAACuB,IAAI,qBAAXL,YAAA,CAAaM,UAAU,YAAAP,qBAAA,GAAIrE,WAAA,CAAA6E,oBAAoB,CAACC,QAAQ;YAC3EC,cAAc,GAAAR,aAAA,GAAEnB,MAAM,CAACuB,IAAI,cAAAJ,aAAA,GAAXA,aAAA,CAAaS,SAAS,qBAAtBT,aAAA,CAAwBU;UAAY;SAEvD,CAAC;MACJ,CAAC,MAAM;QACL5D,UAAU,CAAC,KAAK,CAAC;QACjB,IAAApB,YAAA,CAAA4D,cAAc,EAACT,MAAM,CAACU,KAAK,CAAC;MAE9B;IACF,CAAC;IAAA,gBAjBKH,cAAcA,CAAAuB,EAAA;MAAA,OAAAhB,KAAA,CAAAF,KAAA,OAAAC,SAAA;IAAA;EAAA,GAiBnB;EAED,IAAMkB,qBAAqB,GAAG,SAAxBA,qBAAqBA,CAAA,EAAQ;IAAA,IAAAC,qBAAA;IACjC,CAAAA,qBAAA,GAAAxF,wBAAA,CAAAyF,gBAAgB,CAACC,CAAC,CAACC,aAAa,aAAhCH,qBAAA,CAAkCI,SAAS,CAAC;MAC1CC,QAAQ,EAAE,SAAS;MACnBC,KAAK,EAAE,IAAA7F,MAAA,CAAA8F,SAAS,EAAC,iCAAiC,CAAC;MACnDC,OAAO,EAAE,IAAA/F,MAAA,CAAA8F,SAAS,EAAC,4CAA4C,CAAC;MAChEE,aAAa,EAAE,IAAAhG,MAAA,CAAA8F,SAAS,EAAC,iCAAiC,CAAC;MAC3DG,cAAc,EAAE,IAAAjG,MAAA,CAAA8F,SAAS,EAAC,sBAAsB,CAAC;MACjDI,QAAQ,EAAE,SAAVA,QAAQA,CAAA;QAAA,OAAQC,gBAAgB,EAAE;MAAA;KACnC,CAAC;EACJ,CAAC;EAED,IAAMA,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAAA,EAAQ;IAC5BC,MAAM,EAAE;EACV,CAAC;EAED,IAAMA,MAAM,GAAG,SAATA,MAAMA,CAAA,EAAQ;IAClB3F,UAAU,YAAVA,UAAU,CAAE4F,KAAK,CAAC;MAChBC,KAAK,EAAE,CAAC;MACRC,MAAM,EAAE,CACN;QACEC,IAAI,EAAE;OACP;KAEJ,CAAC;EACJ,CAAC;EAED,OAAO;IACLzD,SAAS,EAATA,SAAS;IACTlC,WAAW,EAAXA,WAAW;IACXY,WAAW,EAAXA,WAAW;IACX6D,qBAAqB,EAArBA,qBAAqB;IACrB/D,SAAS,EAATA,SAAS;IACTR,SAAS,EAATA,SAAS;IACTC,OAAO,EAAPA;GACD;AACH,CAAC;AAIDyF,OAAA,CAAAnF,OAAA,GAAejB,iBAAiB",
      ignoreList: []
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "cd215afe54b82ac9ce192a61b080b531e11438d6"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_2kucvge68b = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_2kucvge68b();
var _interopRequireDefault =
/* istanbul ignore next */
(cov_2kucvge68b().s[0]++, require("@babel/runtime/helpers/interopRequireDefault"));
var _asyncToGenerator2 =
/* istanbul ignore next */
(cov_2kucvge68b().s[1]++, _interopRequireDefault(require("@babel/runtime/helpers/asyncToGenerator")));
var _slicedToArray2 =
/* istanbul ignore next */
(cov_2kucvge68b().s[2]++, _interopRequireDefault(require("@babel/runtime/helpers/slicedToArray")));
var __importDefault =
/* istanbul ignore next */
(cov_2kucvge68b().s[3]++,
/* istanbul ignore next */
(cov_2kucvge68b().b[0][0]++, this) &&
/* istanbul ignore next */
(cov_2kucvge68b().b[0][1]++, this.__importDefault) ||
/* istanbul ignore next */
(cov_2kucvge68b().b[0][2]++, function (mod) {
  /* istanbul ignore next */
  cov_2kucvge68b().f[0]++;
  cov_2kucvge68b().s[4]++;
  return /* istanbul ignore next */(cov_2kucvge68b().b[2][0]++, mod) &&
  /* istanbul ignore next */
  (cov_2kucvge68b().b[2][1]++, mod.__esModule) ?
  /* istanbul ignore next */
  (cov_2kucvge68b().b[1][0]++, mod) :
  /* istanbul ignore next */
  (cov_2kucvge68b().b[1][1]++, {
    "default": mod
  });
}));
/* istanbul ignore next */
cov_2kucvge68b().s[5]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
var native_1 =
/* istanbul ignore next */
(cov_2kucvge68b().s[6]++, require("@react-navigation/native"));
var ScreenNames_1 =
/* istanbul ignore next */
(cov_2kucvge68b().s[7]++, __importDefault(require("../../commons/ScreenNames")));
var msb_host_shared_module_1 =
/* istanbul ignore next */
(cov_2kucvge68b().s[8]++, require("msb-host-shared-module"));
var i18n_1 =
/* istanbul ignore next */
(cov_2kucvge68b().s[9]++, require("../../locales/i18n"));
var react_1 =
/* istanbul ignore next */
(cov_2kucvge68b().s[10]++, require("react"));
var DIContainer_1 =
/* istanbul ignore next */
(cov_2kucvge68b().s[11]++, require("../../di/DIContainer"));
var Constants_1 =
/* istanbul ignore next */
(cov_2kucvge68b().s[12]++, require("../../commons/Constants"));
var PopupUtils_1 =
/* istanbul ignore next */
(cov_2kucvge68b().s[13]++, require("../../utils/PopupUtils"));
/* istanbul ignore next */
cov_2kucvge68b().s[14]++;
var usePaymentConfirm = function usePaymentConfirm() {
  /* istanbul ignore next */
  cov_2kucvge68b().f[1]++;
  var _paymentInfo$provider;
  var route =
  /* istanbul ignore next */
  (cov_2kucvge68b().s[15]++, (0, native_1.useRoute)());
  var navigation =
  /* istanbul ignore next */
  (cov_2kucvge68b().s[16]++, (0, native_1.useNavigation)());
  var _route$params =
    /* istanbul ignore next */
    (cov_2kucvge68b().s[17]++, route.params),
    paymentInfo =
    /* istanbul ignore next */
    (cov_2kucvge68b().s[18]++, _route$params.paymentInfo),
    _route$params$hasPeri =
    /* istanbul ignore next */
    (cov_2kucvge68b().s[19]++, _route$params.hasPeriod),
    hasPeriod =
    /* istanbul ignore next */
    (cov_2kucvge68b().s[20]++, _route$params$hasPeri === void 0 ?
    /* istanbul ignore next */
    (cov_2kucvge68b().b[3][0]++, true) :
    /* istanbul ignore next */
    (cov_2kucvge68b().b[3][1]++, _route$params$hasPeri));
  var isTopup =
  /* istanbul ignore next */
  (cov_2kucvge68b().s[21]++, (_paymentInfo$provider = paymentInfo.provider) == null ?
  /* istanbul ignore next */
  (cov_2kucvge68b().b[4][0]++, void 0) :
  /* istanbul ignore next */
  (cov_2kucvge68b().b[4][1]++, _paymentInfo$provider.isTopup()));
  var _ref =
    /* istanbul ignore next */
    (cov_2kucvge68b().s[22]++, (0, react_1.useState)(false)),
    _ref2 =
    /* istanbul ignore next */
    (cov_2kucvge68b().s[23]++, (0, _slicedToArray2.default)(_ref, 2)),
    isLoading =
    /* istanbul ignore next */
    (cov_2kucvge68b().s[24]++, _ref2[0]),
    setLoading =
    /* istanbul ignore next */
    (cov_2kucvge68b().s[25]++, _ref2[1]);
  var totalAmount =
  /* istanbul ignore next */
  (cov_2kucvge68b().s[26]++, (0, react_1.useMemo)(function () {
    /* istanbul ignore next */
    cov_2kucvge68b().f[2]++;
    var _paymentInfo$paymentV, _paymentInfo$paymentV2, _paymentInfo$paymentV3, _paymentInfo$billInfo;
    /* istanbul ignore next */
    cov_2kucvge68b().s[27]++;
    if (
    /* istanbul ignore next */
    (cov_2kucvge68b().b[6][0]++, ((_paymentInfo$paymentV = paymentInfo.paymentValidate) == null ?
    /* istanbul ignore next */
    (cov_2kucvge68b().b[7][0]++, void 0) :
    /* istanbul ignore next */
    (cov_2kucvge68b().b[7][1]++, _paymentInfo$paymentV.paymentType)) === Constants_1.PAYMENT_TYPE.TOPUP_ACCOUNT) ||
    /* istanbul ignore next */
    (cov_2kucvge68b().b[6][1]++, ((_paymentInfo$paymentV2 = paymentInfo.paymentValidate) == null ?
    /* istanbul ignore next */
    (cov_2kucvge68b().b[8][0]++, void 0) :
    /* istanbul ignore next */
    (cov_2kucvge68b().b[8][1]++, _paymentInfo$paymentV2.paymentType)) === Constants_1.PAYMENT_TYPE.TOPUP_CREDIT) ||
    /* istanbul ignore next */
    (cov_2kucvge68b().b[6][2]++, ((_paymentInfo$paymentV3 = paymentInfo.paymentValidate) == null ?
    /* istanbul ignore next */
    (cov_2kucvge68b().b[9][0]++, void 0) :
    /* istanbul ignore next */
    (cov_2kucvge68b().b[9][1]++, _paymentInfo$paymentV3.paymentType)) === Constants_1.PAYMENT_TYPE.QR_PAYMENT)) {
      /* istanbul ignore next */
      cov_2kucvge68b().b[5][0]++;
      var _paymentInfo$paymentV4;
      /* istanbul ignore next */
      cov_2kucvge68b().s[28]++;
      return /* istanbul ignore next */(cov_2kucvge68b().b[11][0]++, (_paymentInfo$paymentV4 = paymentInfo.paymentValidate) == null) ||
      /* istanbul ignore next */
      (cov_2kucvge68b().b[11][1]++, (_paymentInfo$paymentV4 = _paymentInfo$paymentV4.transferTransactionInformation) == null) ||
      /* istanbul ignore next */
      (cov_2kucvge68b().b[11][2]++, (_paymentInfo$paymentV4 = _paymentInfo$paymentV4.instructedAmount) == null) ?
      /* istanbul ignore next */
      (cov_2kucvge68b().b[10][0]++, void 0) :
      /* istanbul ignore next */
      (cov_2kucvge68b().b[10][1]++, _paymentInfo$paymentV4.amount);
    } else
    /* istanbul ignore next */
    {
      cov_2kucvge68b().b[5][1]++;
    }
    var _totalAmount =
    /* istanbul ignore next */
    (cov_2kucvge68b().s[29]++,
    /* istanbul ignore next */
    (cov_2kucvge68b().b[13][0]++, paymentInfo == null) ||
    /* istanbul ignore next */
    (cov_2kucvge68b().b[13][1]++, (_paymentInfo$billInfo = paymentInfo.billInfo) == null) ||
    /* istanbul ignore next */
    (cov_2kucvge68b().b[13][2]++, (_paymentInfo$billInfo = _paymentInfo$billInfo.billList) == null) ?
    /* istanbul ignore next */
    (cov_2kucvge68b().b[12][0]++, void 0) :
    /* istanbul ignore next */
    (cov_2kucvge68b().b[12][1]++, _paymentInfo$billInfo.reduce(function (sum, bill) {
      /* istanbul ignore next */
      cov_2kucvge68b().f[3]++;
      cov_2kucvge68b().s[30]++;
      return sum + (
      /* istanbul ignore next */
      (cov_2kucvge68b().b[14][0]++, bill.amount) ||
      /* istanbul ignore next */
      (cov_2kucvge68b().b[14][1]++, 0));
    }, 0)));
    /* istanbul ignore next */
    cov_2kucvge68b().s[31]++;
    return _totalAmount;
  }, [paymentInfo]));
  /* istanbul ignore next */
  cov_2kucvge68b().s[32]++;
  var onConfirm = function onConfirm() {
    /* istanbul ignore next */
    cov_2kucvge68b().f[4]++;
    cov_2kucvge68b().s[33]++;
    paymentOrder();
  };
  var paymentOrder =
  /* istanbul ignore next */
  (cov_2kucvge68b().s[34]++, function () {
    /* istanbul ignore next */
    cov_2kucvge68b().f[5]++;
    var _ref3 =
    /* istanbul ignore next */
    (cov_2kucvge68b().s[35]++, (0, _asyncToGenerator2.default)(function* () {
      /* istanbul ignore next */
      cov_2kucvge68b().f[6]++;
      cov_2kucvge68b().s[36]++;
      if (paymentInfo.paymentValidate) {
        /* istanbul ignore next */
        cov_2kucvge68b().b[15][0]++;
        cov_2kucvge68b().s[37]++;
        setLoading(true);
        var params =
        /* istanbul ignore next */
        (cov_2kucvge68b().s[38]++, Object.assign({}, paymentInfo.paymentValidate));
        /* istanbul ignore next */
        cov_2kucvge68b().s[39]++;
        console.log('request params', params);
        var result =
        /* istanbul ignore next */
        (cov_2kucvge68b().s[40]++, yield DIContainer_1.DIContainer.getInstance().getPaymentOrderUseCase().execute(params));
        /* istanbul ignore next */
        cov_2kucvge68b().s[41]++;
        console.log('result', result);
        /* istanbul ignore next */
        cov_2kucvge68b().s[42]++;
        setLoading(false);
        /* istanbul ignore next */
        cov_2kucvge68b().s[43]++;
        if (result.status === 'SUCCESS') {
          /* istanbul ignore next */
          cov_2kucvge68b().b[16][0]++;
          var _paymentInfo$paymentV5;
          /* istanbul ignore next */
          cov_2kucvge68b().s[44]++;
          getOrderStatus((_paymentInfo$paymentV5 = paymentInfo == null ?
          /* istanbul ignore next */
          (cov_2kucvge68b().b[18][0]++, void 0) :
          /* istanbul ignore next */
          (cov_2kucvge68b().b[18][1]++, paymentInfo.paymentValidate.id)) != null ?
          /* istanbul ignore next */
          (cov_2kucvge68b().b[17][0]++, _paymentInfo$paymentV5) :
          /* istanbul ignore next */
          (cov_2kucvge68b().b[17][1]++, ''));
        } else {
          /* istanbul ignore next */
          cov_2kucvge68b().b[16][1]++;
          cov_2kucvge68b().s[45]++;
          (0, PopupUtils_1.showErrorPopup)(result.error);
          /* istanbul ignore next */
          cov_2kucvge68b().s[46]++;
          navigation.goBack();
        }
      } else
      /* istanbul ignore next */
      {
        cov_2kucvge68b().b[15][1]++;
      }
    }));
    /* istanbul ignore next */
    cov_2kucvge68b().s[47]++;
    return function paymentOrder() {
      /* istanbul ignore next */
      cov_2kucvge68b().f[7]++;
      cov_2kucvge68b().s[48]++;
      return _ref3.apply(this, arguments);
    };
  }());
  var getOrderStatus =
  /* istanbul ignore next */
  (cov_2kucvge68b().s[49]++, function () {
    /* istanbul ignore next */
    cov_2kucvge68b().f[8]++;
    var _ref4 =
    /* istanbul ignore next */
    (cov_2kucvge68b().s[50]++, (0, _asyncToGenerator2.default)(function* (id) {
      /* istanbul ignore next */
      cov_2kucvge68b().f[9]++;
      var result =
      /* istanbul ignore next */
      (cov_2kucvge68b().s[51]++, yield DIContainer_1.DIContainer.getInstance().getPaymentOrderStatusUseCase().execute({
        paymentMode: '',
        id: id
      }));
      /* istanbul ignore next */
      cov_2kucvge68b().s[52]++;
      console.log('result', result);
      /* istanbul ignore next */
      cov_2kucvge68b().s[53]++;
      if (result.status === 'SUCCESS') {
        /* istanbul ignore next */
        cov_2kucvge68b().b[19][0]++;
        var _result$data$bankStat, _result$data, _result$data2;
        /* istanbul ignore next */
        cov_2kucvge68b().s[54]++;
        setLoading(false);
        /* istanbul ignore next */
        cov_2kucvge68b().s[55]++;
        navigation.navigate(ScreenNames_1.default.PaymentResultScreen, {
          paymentInfo: Object.assign({}, paymentInfo, {
            paymentResultType: (_result$data$bankStat = (_result$data = result.data) == null ?
            /* istanbul ignore next */
            (cov_2kucvge68b().b[21][0]++, void 0) :
            /* istanbul ignore next */
            (cov_2kucvge68b().b[21][1]++, _result$data.bankStatus)) != null ?
            /* istanbul ignore next */
            (cov_2kucvge68b().b[20][0]++, _result$data$bankStat) :
            /* istanbul ignore next */
            (cov_2kucvge68b().b[20][1]++, Constants_1.PAYMENT_ORDER_STATUS.REJECTED),
            additionalInfo:
            /* istanbul ignore next */
            (cov_2kucvge68b().b[23][0]++, (_result$data2 = result.data) == null) ||
            /* istanbul ignore next */
            (cov_2kucvge68b().b[23][1]++, (_result$data2 = _result$data2.additions) == null) ?
            /* istanbul ignore next */
            (cov_2kucvge68b().b[22][0]++, void 0) :
            /* istanbul ignore next */
            (cov_2kucvge68b().b[22][1]++, _result$data2.t24TraceCode)
          })
        });
      } else {
        /* istanbul ignore next */
        cov_2kucvge68b().b[19][1]++;
        cov_2kucvge68b().s[56]++;
        setLoading(false);
        /* istanbul ignore next */
        cov_2kucvge68b().s[57]++;
        (0, PopupUtils_1.showErrorPopup)(result.error);
      }
    }));
    /* istanbul ignore next */
    cov_2kucvge68b().s[58]++;
    return function getOrderStatus(_x) {
      /* istanbul ignore next */
      cov_2kucvge68b().f[10]++;
      cov_2kucvge68b().s[59]++;
      return _ref4.apply(this, arguments);
    };
  }());
  /* istanbul ignore next */
  cov_2kucvge68b().s[60]++;
  var endTransactionConfirm = function endTransactionConfirm() {
    /* istanbul ignore next */
    cov_2kucvge68b().f[11]++;
    var _msb_host_shared_modu;
    /* istanbul ignore next */
    cov_2kucvge68b().s[61]++;
    /* istanbul ignore next */
    (cov_2kucvge68b().b[24][0]++, (_msb_host_shared_modu = msb_host_shared_module_1.hostSharedModule.d.domainService) == null) ||
    /* istanbul ignore next */
    (cov_2kucvge68b().b[24][1]++, _msb_host_shared_modu.showPopup({
      iconType: 'WARNING',
      title: (0, i18n_1.translate)('paymentConfirm.endOfTransaction'),
      content: (0, i18n_1.translate)('paymentConfirm.endOfTransactionDescription'),
      cancelBtnText: (0, i18n_1.translate)('paymentConfirm.endOfTransaction'),
      confirmBtnText: (0, i18n_1.translate)('paymentConfirm.close'),
      onCancel: function onCancel() {
        /* istanbul ignore next */
        cov_2kucvge68b().f[12]++;
        cov_2kucvge68b().s[62]++;
        return endOfTransaction();
      }
    }));
  };
  /* istanbul ignore next */
  cov_2kucvge68b().s[63]++;
  var endOfTransaction = function endOfTransaction() {
    /* istanbul ignore next */
    cov_2kucvge68b().f[13]++;
    cov_2kucvge68b().s[64]++;
    goHome();
  };
  /* istanbul ignore next */
  cov_2kucvge68b().s[65]++;
  var goHome = function goHome() {
    /* istanbul ignore next */
    cov_2kucvge68b().f[14]++;
    cov_2kucvge68b().s[66]++;
    /* istanbul ignore next */
    (cov_2kucvge68b().b[25][0]++, navigation == null) ||
    /* istanbul ignore next */
    (cov_2kucvge68b().b[25][1]++, navigation.reset({
      index: 0,
      routes: [{
        name: 'SegmentStack'
      }]
    }));
  };
  /* istanbul ignore next */
  cov_2kucvge68b().s[67]++;
  return {
    onConfirm: onConfirm,
    paymentInfo: paymentInfo,
    totalAmount: totalAmount,
    endTransactionConfirm: endTransactionConfirm,
    isLoading: isLoading,
    hasPeriod: hasPeriod,
    isTopup: isTopup
  };
};
/* istanbul ignore next */
cov_2kucvge68b().s[68]++;
exports.default = usePaymentConfirm;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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