{"version": 3, "names": ["cov_mxky14b7x", "actualCoverage", "msb_communication_lib_1", "s", "require", "en_json_1", "__importDefault", "vi_json_1", "exports", "translations", "en", "default", "vi", "translate", "i18n", "t"], "sources": ["/Users/<USER>/projects/react-native/payment-module/src/locales/i18n.ts"], "sourcesContent": ["import {i18n, I18nPath} from 'msb-communication-lib';\nimport en from './en.json';\nimport vi from './vi.json';\n\nexport const translations = {\n  en,\n  vi,\n};\n\ntype ResourceType = typeof translations;\nexport type I18nKeys = I18nPath<ResourceType[keyof ResourceType]>;\nexport const translate = i18n.t<I18nKeys>;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAKE;IAAAA,aAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,aAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AALF,IAAAE,uBAAA;AAAA;AAAA,CAAAF,aAAA,GAAAG,CAAA,OAAAC,OAAA;AACA,IAAAC,SAAA;AAAA;AAAA,CAAAL,aAAA,GAAAG,CAAA,OAAAG,eAAA,CAAAF,OAAA;AACA,IAAAG,SAAA;AAAA;AAAA,CAAAP,aAAA,GAAAG,CAAA,OAAAG,eAAA,CAAAF,OAAA;AAAA;AAAAJ,aAAA,GAAAG,CAAA;AAEaK,OAAA,CAAAC,YAAY,GAAG;EAC1BC,EAAE,EAAFL,SAAA,CAAAM,OAAE;EACFC,EAAE,EAAFL,SAAA,CAAAI;CACD;AAAA;AAAAX,aAAA,GAAAG,CAAA;AAIYK,OAAA,CAAAK,SAAS,GAAGX,uBAAA,CAAAY,IAAI,CAACC,CAAW", "ignoreList": []}