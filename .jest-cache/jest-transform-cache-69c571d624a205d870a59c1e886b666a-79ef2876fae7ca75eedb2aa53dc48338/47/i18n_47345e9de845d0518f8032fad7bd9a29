48a5e63c4175256846f75558080cd1c2
"use strict";

/* istanbul ignore next */
function cov_mxky14b7x() {
  var path = "/Users/<USER>/projects/react-native/payment-module/src/locales/i18n.ts";
  var hash = "fa1ff9b399008e6731f3784db512ed3f48debba2";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/projects/react-native/payment-module/src/locales/i18n.ts",
    statementMap: {
      "0": {
        start: {
          line: 3,
          column: 22
        },
        end: {
          line: 7,
          column: 1
        }
      },
      "1": {
        start: {
          line: 4,
          column: 2
        },
        end: {
          line: 6,
          column: 4
        }
      },
      "2": {
        start: {
          line: 8,
          column: 0
        },
        end: {
          line: 10,
          column: 3
        }
      },
      "3": {
        start: {
          line: 11,
          column: 0
        },
        end: {
          line: 11,
          column: 50
        }
      },
      "4": {
        start: {
          line: 12,
          column: 30
        },
        end: {
          line: 12,
          column: 62
        }
      },
      "5": {
        start: {
          line: 13,
          column: 16
        },
        end: {
          line: 13,
          column: 53
        }
      },
      "6": {
        start: {
          line: 14,
          column: 16
        },
        end: {
          line: 14,
          column: 53
        }
      },
      "7": {
        start: {
          line: 15,
          column: 0
        },
        end: {
          line: 18,
          column: 2
        }
      },
      "8": {
        start: {
          line: 19,
          column: 0
        },
        end: {
          line: 19,
          column: 51
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 3,
            column: 54
          },
          end: {
            line: 3,
            column: 55
          }
        },
        loc: {
          start: {
            line: 3,
            column: 69
          },
          end: {
            line: 7,
            column: 1
          }
        },
        line: 3
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 3,
            column: 22
          },
          end: {
            line: 7,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 3,
            column: 22
          },
          end: {
            line: 3,
            column: 26
          }
        }, {
          start: {
            line: 3,
            column: 30
          },
          end: {
            line: 3,
            column: 50
          }
        }, {
          start: {
            line: 3,
            column: 54
          },
          end: {
            line: 7,
            column: 1
          }
        }],
        line: 3
      },
      "1": {
        loc: {
          start: {
            line: 4,
            column: 9
          },
          end: {
            line: 6,
            column: 3
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 4,
            column: 33
          },
          end: {
            line: 4,
            column: 36
          }
        }, {
          start: {
            line: 4,
            column: 39
          },
          end: {
            line: 6,
            column: 3
          }
        }],
        line: 4
      },
      "2": {
        loc: {
          start: {
            line: 4,
            column: 9
          },
          end: {
            line: 4,
            column: 30
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 4,
            column: 9
          },
          end: {
            line: 4,
            column: 12
          }
        }, {
          start: {
            line: 4,
            column: 16
          },
          end: {
            line: 4,
            column: 30
          }
        }],
        line: 4
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0
    },
    f: {
      "0": 0
    },
    b: {
      "0": [0, 0, 0],
      "1": [0, 0],
      "2": [0, 0]
    },
    inputSourceMap: {
      version: 3,
      file: undefined,
      names: ["msb_communication_lib_1", "require", "en_json_1", "__importDefault", "vi_json_1", "exports", "translations", "en", "default", "vi", "translate", "i18n", "t"],
      sourceRoot: undefined,
      sources: ["/Users/<USER>/projects/react-native/payment-module/src/locales/i18n.ts"],
      sourcesContent: ["import {i18n, I18nPath} from 'msb-communication-lib';\nimport en from './en.json';\nimport vi from './vi.json';\n\nexport const translations = {\n  en,\n  vi,\n};\n\ntype ResourceType = typeof translations;\nexport type I18nKeys = I18nPath<ResourceType[keyof ResourceType]>;\nexport const translate = i18n.t<I18nKeys>;\n"],
      mappings: ";;;;;;;;;;;AAAA,IAAAA,uBAAA,GAAAC,OAAA;AACA,IAAAC,SAAA,GAAAC,eAAA,CAAAF,OAAA;AACA,IAAAG,SAAA,GAAAD,eAAA,CAAAF,OAAA;AAEaI,OAAA,CAAAC,YAAY,GAAG;EAC1BC,EAAE,EAAFL,SAAA,CAAAM,OAAE;EACFC,EAAE,EAAFL,SAAA,CAAAI;CACD;AAIYH,OAAA,CAAAK,SAAS,GAAGV,uBAAA,CAAAW,IAAI,CAACC,CAAW",
      ignoreList: []
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "fa1ff9b399008e6731f3784db512ed3f48debba2"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_mxky14b7x = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_mxky14b7x();
var __importDefault =
/* istanbul ignore next */
(cov_mxky14b7x().s[0]++,
/* istanbul ignore next */
(cov_mxky14b7x().b[0][0]++, this) &&
/* istanbul ignore next */
(cov_mxky14b7x().b[0][1]++, this.__importDefault) ||
/* istanbul ignore next */
(cov_mxky14b7x().b[0][2]++, function (mod) {
  /* istanbul ignore next */
  cov_mxky14b7x().f[0]++;
  cov_mxky14b7x().s[1]++;
  return /* istanbul ignore next */(cov_mxky14b7x().b[2][0]++, mod) &&
  /* istanbul ignore next */
  (cov_mxky14b7x().b[2][1]++, mod.__esModule) ?
  /* istanbul ignore next */
  (cov_mxky14b7x().b[1][0]++, mod) :
  /* istanbul ignore next */
  (cov_mxky14b7x().b[1][1]++, {
    "default": mod
  });
}));
/* istanbul ignore next */
cov_mxky14b7x().s[2]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_mxky14b7x().s[3]++;
exports.translate = exports.translations = void 0;
var msb_communication_lib_1 =
/* istanbul ignore next */
(cov_mxky14b7x().s[4]++, require("msb-communication-lib"));
var en_json_1 =
/* istanbul ignore next */
(cov_mxky14b7x().s[5]++, __importDefault(require("./en.json")));
var vi_json_1 =
/* istanbul ignore next */
(cov_mxky14b7x().s[6]++, __importDefault(require("./vi.json")));
/* istanbul ignore next */
cov_mxky14b7x().s[7]++;
exports.translations = {
  en: en_json_1.default,
  vi: vi_json_1.default
};
/* istanbul ignore next */
cov_mxky14b7x().s[8]++;
exports.translate = msb_communication_lib_1.i18n.t;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJjb3ZfbXhreTE0Yjd4IiwiYWN0dWFsQ292ZXJhZ2UiLCJtc2JfY29tbXVuaWNhdGlvbl9saWJfMSIsInMiLCJyZXF1aXJlIiwiZW5fanNvbl8xIiwiX19pbXBvcnREZWZhdWx0IiwidmlfanNvbl8xIiwiZXhwb3J0cyIsInRyYW5zbGF0aW9ucyIsImVuIiwiZGVmYXVsdCIsInZpIiwidHJhbnNsYXRlIiwiaTE4biIsInQiXSwic291cmNlcyI6WyIvVXNlcnMvc29uc2Ftc2V0L3Byb2plY3RzL3JlYWN0LW5hdGl2ZS9wYXltZW50LW1vZHVsZS9zcmMvbG9jYWxlcy9pMThuLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7aTE4biwgSTE4blBhdGh9IGZyb20gJ21zYi1jb21tdW5pY2F0aW9uLWxpYic7XG5pbXBvcnQgZW4gZnJvbSAnLi9lbi5qc29uJztcbmltcG9ydCB2aSBmcm9tICcuL3ZpLmpzb24nO1xuXG5leHBvcnQgY29uc3QgdHJhbnNsYXRpb25zID0ge1xuICBlbixcbiAgdmksXG59O1xuXG50eXBlIFJlc291cmNlVHlwZSA9IHR5cGVvZiB0cmFuc2xhdGlvbnM7XG5leHBvcnQgdHlwZSBJMThuS2V5cyA9IEkxOG5QYXRoPFJlc291cmNlVHlwZVtrZXlvZiBSZXNvdXJjZVR5cGVdPjtcbmV4cG9ydCBjb25zdCB0cmFuc2xhdGUgPSBpMThuLnQ8STE4bktleXM+O1xuIl0sIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7SUFLRTtJQUFBQSxhQUFBLFlBQUFBLENBQUE7TUFBQSxPQUFBQyxjQUFBO0lBQUE7RUFBQTtFQUFBLE9BQUFBLGNBQUE7QUFBQTtBQUFBRCxhQUFBOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBTEYsSUFBQUUsdUJBQUE7QUFBQTtBQUFBLENBQUFGLGFBQUEsR0FBQUcsQ0FBQSxPQUFBQyxPQUFBO0FBQ0EsSUFBQUMsU0FBQTtBQUFBO0FBQUEsQ0FBQUwsYUFBQSxHQUFBRyxDQUFBLE9BQUFHLGVBQUEsQ0FBQUYsT0FBQTtBQUNBLElBQUFHLFNBQUE7QUFBQTtBQUFBLENBQUFQLGFBQUEsR0FBQUcsQ0FBQSxPQUFBRyxlQUFBLENBQUFGLE9BQUE7QUFBQTtBQUFBSixhQUFBLEdBQUFHLENBQUE7QUFFYUssT0FBQSxDQUFBQyxZQUFZLEdBQUc7RUFDMUJDLEVBQUUsRUFBRkwsU0FBQSxDQUFBTSxPQUFFO0VBQ0ZDLEVBQUUsRUFBRkwsU0FBQSxDQUFBSTtDQUNEO0FBQUE7QUFBQVgsYUFBQSxHQUFBRyxDQUFBO0FBSVlLLE9BQUEsQ0FBQUssU0FBUyxHQUFHWCx1QkFBQSxDQUFBWSxJQUFJLENBQUNDLENBQVciLCJpZ25vcmVMaXN0IjpbXX0=