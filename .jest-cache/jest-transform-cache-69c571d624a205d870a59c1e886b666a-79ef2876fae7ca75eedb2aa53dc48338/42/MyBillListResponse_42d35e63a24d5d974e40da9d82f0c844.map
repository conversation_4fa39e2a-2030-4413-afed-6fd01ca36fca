{"version": 3, "names": [], "sources": ["/Users/<USER>/projects/react-native/payment-module/src/data/models/my-bill-list/MyBillListResponse.ts"], "sourcesContent": ["export type MyBillListResponse = MyBillContactResponse[];\n\nexport interface MyBillContactResponse {\n  id: string | undefined;\n  accessContextScope: string | undefined;\n  name: string | undefined;\n  alias: string | undefined;\n  category: string | undefined;\n  activeStatus: string | undefined;\n  accounts: AccountResponse[] | undefined;\n}\n\nexport interface AccountResponse {\n  accountNumber: string | undefined;\n  bankCode: string | undefined;\n  bankName?: string | undefined;\n  externalId?: string | undefined;\n  accountType?: string | undefined;\n  bankPostCode?: string | undefined;\n}\n"], "mappings": "", "ignoreList": []}