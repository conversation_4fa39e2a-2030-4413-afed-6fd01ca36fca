bc5e7cef34e6f84c307816c1aec41fe7
"use strict";

/* istanbul ignore next */
function cov_2d0s2ndw5z() {
  var path = "/Users/<USER>/projects/react-native/payment-module/src/data/models/my-bill-list/MyBillListResponse.ts";
  var hash = "86478c780639ab2d2747f531585d2fe8e2b0bb6a";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/projects/react-native/payment-module/src/data/models/my-bill-list/MyBillListResponse.ts",
    statementMap: {
      "0": {
        start: {
          line: 3,
          column: 0
        },
        end: {
          line: 5,
          column: 3
        }
      }
    },
    fnMap: {},
    branchMap: {},
    s: {
      "0": 0
    },
    f: {},
    b: {},
    inputSourceMap: {
      version: 3,
      file: undefined,
      names: [],
      sourceRoot: undefined,
      sources: ["/Users/<USER>/projects/react-native/payment-module/src/data/models/my-bill-list/MyBillListResponse.ts"],
      sourcesContent: ["export type MyBillListResponse = MyBillContactResponse[];\n\nexport interface MyBillContactResponse {\n  id: string | undefined;\n  accessContextScope: string | undefined;\n  name: string | undefined;\n  alias: string | undefined;\n  category: string | undefined;\n  activeStatus: string | undefined;\n  accounts: AccountResponse[] | undefined;\n}\n\nexport interface AccountResponse {\n  accountNumber: string | undefined;\n  bankCode: string | undefined;\n  bankName?: string | undefined;\n  externalId?: string | undefined;\n  accountType?: string | undefined;\n  bankPostCode?: string | undefined;\n}\n"],
      mappings: "",
      ignoreList: []
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "86478c780639ab2d2747f531585d2fe8e2b0bb6a"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_2d0s2ndw5z = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_2d0s2ndw5z();
cov_2d0s2ndw5z().s[0]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6W10sInNvdXJjZXMiOlsiL1VzZXJzL3NvbnNhbXNldC9wcm9qZWN0cy9yZWFjdC1uYXRpdmUvcGF5bWVudC1tb2R1bGUvc3JjL2RhdGEvbW9kZWxzL215LWJpbGwtbGlzdC9NeUJpbGxMaXN0UmVzcG9uc2UudHMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IHR5cGUgTXlCaWxsTGlzdFJlc3BvbnNlID0gTXlCaWxsQ29udGFjdFJlc3BvbnNlW107XG5cbmV4cG9ydCBpbnRlcmZhY2UgTXlCaWxsQ29udGFjdFJlc3BvbnNlIHtcbiAgaWQ6IHN0cmluZyB8IHVuZGVmaW5lZDtcbiAgYWNjZXNzQ29udGV4dFNjb3BlOiBzdHJpbmcgfCB1bmRlZmluZWQ7XG4gIG5hbWU6IHN0cmluZyB8IHVuZGVmaW5lZDtcbiAgYWxpYXM6IHN0cmluZyB8IHVuZGVmaW5lZDtcbiAgY2F0ZWdvcnk6IHN0cmluZyB8IHVuZGVmaW5lZDtcbiAgYWN0aXZlU3RhdHVzOiBzdHJpbmcgfCB1bmRlZmluZWQ7XG4gIGFjY291bnRzOiBBY2NvdW50UmVzcG9uc2VbXSB8IHVuZGVmaW5lZDtcbn1cblxuZXhwb3J0IGludGVyZmFjZSBBY2NvdW50UmVzcG9uc2Uge1xuICBhY2NvdW50TnVtYmVyOiBzdHJpbmcgfCB1bmRlZmluZWQ7XG4gIGJhbmtDb2RlOiBzdHJpbmcgfCB1bmRlZmluZWQ7XG4gIGJhbmtOYW1lPzogc3RyaW5nIHwgdW5kZWZpbmVkO1xuICBleHRlcm5hbElkPzogc3RyaW5nIHwgdW5kZWZpbmVkO1xuICBhY2NvdW50VHlwZT86IHN0cmluZyB8IHVuZGVmaW5lZDtcbiAgYmFua1Bvc3RDb2RlPzogc3RyaW5nIHwgdW5kZWZpbmVkO1xufVxuIl0sIm1hcHBpbmdzIjoiIiwiaWdub3JlTGlzdCI6W119