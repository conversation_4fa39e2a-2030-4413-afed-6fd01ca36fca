1e50f9ba57bfbf09c1d8fe99f30882f1
'use strict';

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.applyStyle = applyStyle;
exports.applyStyleForBelowTopScreen = applyStyleForBelowTopScreen;
var _PlatformChecker = require("../PlatformChecker.js");
var _UpdateProps = _interopRequireDefault(require("../UpdateProps.js"));
var IS_FABRIC = (0, _PlatformChecker.isFabric)();
function createViewDescriptorPaper(screenId) {
  'worklet';

  return {
    tag: screenId,
    name: 'RCTView'
  };
}
function createViewDescriptorFabric(screenId) {
  'worklet';

  return {
    shadowNodeWrapper: screenId
  };
}
var createViewDescriptor = IS_FABRIC ? createViewDescriptorFabric : createViewDescriptorPaper;
function applyStyleForTopScreen(screenTransitionConfig, event) {
  'worklet';

  var screenDimensions = screenTransitionConfig.screenDimensions,
    topScreenId = screenTransitionConfig.topScreenId,
    screenTransition = screenTransitionConfig.screenTransition;
  var computeTopScreenStyle = screenTransition.topScreenStyle;
  var topScreenStyle = computeTopScreenStyle(event, screenDimensions);
  var topScreenDescriptor = {
    value: [createViewDescriptor(topScreenId)]
  };
  (0, _UpdateProps.default)(topScreenDescriptor, topScreenStyle, undefined);
}
function applyStyleForBelowTopScreen(screenTransitionConfig, event) {
  'worklet';

  var screenDimensions = screenTransitionConfig.screenDimensions,
    belowTopScreenId = screenTransitionConfig.belowTopScreenId,
    screenTransition = screenTransitionConfig.screenTransition;
  var computeBelowTopScreenStyle = screenTransition.belowTopScreenStyle;
  var belowTopScreenStyle = computeBelowTopScreenStyle(event, screenDimensions);
  var belowTopScreenDescriptor = {
    value: [createViewDescriptor(belowTopScreenId)]
  };
  (0, _UpdateProps.default)(belowTopScreenDescriptor, belowTopScreenStyle, undefined);
}
function applyStyle(screenTransitionConfig, event) {
  'worklet';

  applyStyleForTopScreen(screenTransitionConfig, event);
  applyStyleForBelowTopScreen(screenTransitionConfig, event);
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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