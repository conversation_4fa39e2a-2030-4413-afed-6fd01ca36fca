{"version": 3, "names": ["_interopRequireDefault", "require", "Object", "defineProperty", "exports", "value", "ComplexAnimationBuilder", "_classCallCheck2", "_createClass2", "_possibleConstructorReturn2", "_getPrototypeOf2", "_inherits2", "_index", "_BaseAnimationBuilder2", "_util", "_callSuper", "t", "o", "e", "default", "_isNativeReflectConstruct", "Reflect", "construct", "constructor", "apply", "Boolean", "prototype", "valueOf", "call", "_BaseAnimationBuilder", "arguments", "key", "easing", "easingFunction", "__DEV__", "assertEasingIsWorklet", "easingV", "rotate", "degree", "rotateV", "springify", "duration", "durationV", "type", "with<PERSON><PERSON><PERSON>", "dampingRatio", "dampingRatioV", "damping", "dampingV", "mass", "massV", "stiffness", "stiffnessV", "overshootClamping", "overshootClampingV", "restDisplacementThreshold", "restDisplacementThresholdV", "restSpeedThreshold", "restSpeedThresholdV", "withInitialValues", "values", "initialValues", "getAnimationAndConfig", "withTiming", "animation", "config", "maybeSetConfigValue", "variableName", "for<PERSON>ach", "_ref", "instance", "createInstance", "BaseAnimationBuilder"], "sources": ["../../../../src/layoutReanimation/animationBuilder/ComplexAnimationBuilder.ts"], "sourcesContent": [null], "mappings": "AAAA,YAAY;;AAAA,IAAAA,sBAAA,GAAAC,OAAA;AAAAC,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,KAAA;AAAA;AAAAD,OAAA,CAAAE,uBAAA;AAAA,IAAAC,gBAAA,GAAAP,sBAAA,CAAAC,OAAA;AAAA,IAAAO,aAAA,GAAAR,sBAAA,CAAAC,OAAA;AAAA,IAAAQ,2BAAA,GAAAT,sBAAA,CAAAC,OAAA;AAAA,IAAAS,gBAAA,GAAAV,sBAAA,CAAAC,OAAA;AAAA,IAAAU,UAAA,GAAAX,sBAAA,CAAAC,OAAA;AACZ,IAAAW,MAAA,GAAAX,OAAA;AAMA,IAAAY,sBAAA,GAAAZ,OAAA;AAEA,IAAAa,KAAA,GAAAb,OAAA;AAA4D,SAAAc,WAAAC,CAAA,EAAAC,CAAA,EAAAC,CAAA,WAAAD,CAAA,OAAAP,gBAAA,CAAAS,OAAA,EAAAF,CAAA,OAAAR,2BAAA,CAAAU,OAAA,EAAAH,CAAA,EAAAI,yBAAA,KAAAC,OAAA,CAAAC,SAAA,CAAAL,CAAA,EAAAC,CAAA,YAAAR,gBAAA,CAAAS,OAAA,EAAAH,CAAA,EAAAO,WAAA,IAAAN,CAAA,CAAAO,KAAA,CAAAR,CAAA,EAAAE,CAAA;AAAA,SAAAE,0BAAA,cAAAJ,CAAA,IAAAS,OAAA,CAAAC,SAAA,CAAAC,OAAA,CAAAC,IAAA,CAAAP,OAAA,CAAAC,SAAA,CAAAG,OAAA,iCAAAT,CAAA,aAAAI,yBAAA,YAAAA,0BAAA,aAAAJ,CAAA;AAAA,IAE/CV,uBAAuB,GAAAF,OAAA,CAAAE,uBAAA,aAAAuB,qBAAA;EAAA,SAAAvB,wBAAA;IAAA,IAAAC,gBAAA,CAAAY,OAAA,QAAAb,uBAAA;IAAA,OAAAS,UAAA,OAAAT,uBAAA,EAAAwB,SAAA;EAAA;EAAA,IAAAnB,UAAA,CAAAQ,OAAA,EAAAb,uBAAA,EAAAuB,qBAAA;EAAA,WAAArB,aAAA,CAAAW,OAAA,EAAAb,uBAAA;IAAAyB,GAAA;IAAA1B,KAAA,EAiClC,SAAA2B,MAAMA,CAACC,cAA8B,EAAQ;MAC3C,IAAIC,OAAO,EAAE;QACX,IAAAC,2BAAqB,EAACF,cAAc,CAAC;MACvC;MACA,IAAI,CAACG,OAAO,GAAGH,cAAc;MAC7B,OAAO,IAAI;IACb;EAAA;IAAAF,GAAA;IAAA1B,KAAA,EAiBA,SAAAgC,MAAMA,CAACC,MAAc,EAAQ;MAC3B,IAAI,CAACC,OAAO,GAAGD,MAAM;MACrB,OAAO,IAAI;IACb;EAAA;IAAAP,GAAA;IAAA1B,KAAA,EAkBA,SAAAmC,SAASA,CAACC,QAAiB,EAAQ;MACjC,IAAI,CAACC,SAAS,GAAGD,QAAQ;MACzB,IAAI,CAACE,IAAI,GAAGC,iBAA+B;MAC3C,OAAO,IAAI;IACb;EAAA;IAAAb,GAAA;IAAA1B,KAAA,EAiBA,SAAAwC,YAAYA,CAACxC,KAAa,EAAQ;MAChC,IAAI,CAACyC,aAAa,GAAGzC,KAAK;MAC1B,OAAO,IAAI;IACb;EAAA;IAAA0B,GAAA;IAAA1B,KAAA,EAkBA,SAAA0C,OAAOA,CAACA,SAAe,EAAQ;MAC7B,IAAI,CAACC,QAAQ,GAAGD,SAAO;MACvB,OAAO,IAAI;IACb;EAAA;IAAAhB,GAAA;IAAA1B,KAAA,EAeA,SAAA4C,IAAIA,CAACA,MAAY,EAAQ;MACvB,IAAI,CAACC,KAAK,GAAGD,MAAI;MACjB,OAAO,IAAI;IACb;EAAA;IAAAlB,GAAA;IAAA1B,KAAA,EAiBA,SAAA8C,SAASA,CAACA,WAAiB,EAAQ;MACjC,IAAI,CAACC,UAAU,GAAGD,WAAS;MAC3B,OAAO,IAAI;IACb;EAAA;IAAApB,GAAA;IAAA1B,KAAA,EAkBA,SAAAgD,iBAAiBA,CAACA,mBAAyB,EAAQ;MACjD,IAAI,CAACC,kBAAkB,GAAGD,mBAAiB;MAC3C,OAAO,IAAI;IACb;EAAA;IAAAtB,GAAA;IAAA1B,KAAA,EAkBA,SAAAkD,yBAAyBA,CAACA,2BAAiC,EAAE;MAC3D,IAAI,CAACC,0BAA0B,GAAGD,2BAAyB;MAC3D,OAAO,IAAI;IACb;EAAA;IAAAxB,GAAA;IAAA1B,KAAA,EAmBA,SAAAoD,kBAAkBA,CAACA,oBAA0B,EAAQ;MACnD,IAAI,CAACC,mBAAmB,GAAGD,oBAAkB;MAC7C,OAAO,IAAI;IACb;EAAA;IAAA1B,GAAA;IAAA1B,KAAA,EAeA,SAAAsD,iBAAiBA,CAACC,MAAkB,EAAQ;MAC1C,IAAI,CAACC,aAAa,GAAGD,MAAM;MAC3B,OAAO,IAAI;IACb;EAAA;IAAA7B,GAAA;IAAA1B,KAAA,EAEA,SAAAyD,qBAAqBA,CAAA,EAA6B;MAChD,IAAMrB,QAAQ,GAAG,IAAI,CAACC,SAAS;MAC/B,IAAMV,MAAM,GAAG,IAAI,CAACI,OAAO;MAC3B,IAAMC,MAAM,GAAG,IAAI,CAACE,OAAO;MAC3B,IAAMI,IAAI,GAAG,IAAI,CAACA,IAAI,GAAG,IAAI,CAACA,IAAI,GAAIoB,iBAAgC;MACtE,IAAMhB,OAAO,GAAG,IAAI,CAACC,QAAQ;MAC7B,IAAMH,YAAY,GAAG,IAAI,CAACC,aAAa;MACvC,IAAMG,IAAI,GAAG,IAAI,CAACC,KAAK;MACvB,IAAMC,SAAS,GAAG,IAAI,CAACC,UAAU;MACjC,IAAMC,iBAAiB,GAAG,IAAI,CAACC,kBAAkB;MACjD,IAAMC,yBAAyB,GAAG,IAAI,CAACC,0BAA0B;MACjE,IAAMC,kBAAkB,GAAG,IAAI,CAACC,mBAAmB;MAEnD,IAAMM,SAAS,GAAGrB,IAAI;MAEtB,IAAMsB,MAAkC,GAAG,CAAC,CAAC;MAE7C,SAASC,mBAAmBA,CAC1B7D,KAAsC,EACtC8D,YAAiB,EACjB;QACA,IAAI9D,KAAK,EAAE;UACT4D,MAAM,CAACE,YAAY,CAAC,GAAG9D,KAAK;QAC9B;MACF;MAEA,IAAIsC,IAAI,KAAKoB,iBAAU,EAAE;QACvBG,mBAAmB,CAAClC,MAAM,EAAE,QAAQ,CAAC;MACvC;MAGE,CACE;QAAEmC,YAAY,EAAE,SAAS;QAAE9D,KAAK,EAAE0C;MAAQ,CAAC,EAC3C;QAAEoB,YAAY,EAAE,cAAc;QAAE9D,KAAK,EAAEwC;MAAa,CAAC,EACrD;QAAEsB,YAAY,EAAE,MAAM;QAAE9D,KAAK,EAAE4C;MAAK,CAAC,EACrC;QAAEkB,YAAY,EAAE,WAAW;QAAE9D,KAAK,EAAE8C;MAAU,CAAC,EAC/C;QAAEgB,YAAY,EAAE,mBAAmB;QAAE9D,KAAK,EAAEgD;MAAkB,CAAC,EAC/D;QACEc,YAAY,EAAE,2BAA2B;QACzC9D,KAAK,EAAEkD;MACT,CAAC,EACD;QAAEY,YAAY,EAAE,oBAAoB;QAAE9D,KAAK,EAAEoD;MAAmB,CAAC,EACjE;QAAEU,YAAY,EAAE,UAAU;QAAE9D,KAAK,EAAEoC;MAAS,CAAC,EAC7C;QAAE0B,YAAY,EAAE,QAAQ;QAAE9D,KAAK,EAAEgC;MAAO,CAAC,CAC1C,CACD+B,OAAO,CAAC,UAAAC,IAAA;QAAA,IAAGhE,KAAK,GAAAgE,IAAA,CAALhE,KAAK;UAAE8D,YAAA,GAAAE,IAAA,CAAAF,YAAA;QAAA,OAClBD,mBAAmB,CAAC7D,KAAK,EAAE8D,YAAY,CACzC;MAAA,EAAC;MAED,OAAO,CAACH,SAAS,EAAEC,MAAM,CAAC;IAC5B;EAAA;IAAAlC,GAAA;IAAA1B,KAAA,EA7QA,SAAO2B,MAAMA,CAEXC,cAA8B,EAC9B;MACA,IAAMqC,QAAQ,GAAG,IAAI,CAACC,cAAc,CAAC,CAAC;MACtC,OAAOD,QAAQ,CAACtC,MAAM,CAACC,cAAc,CAAC;IACxC;EAAA;IAAAF,GAAA;IAAA1B,KAAA,EAiBA,SAAOgC,MAAMA,CAEXC,MAAc,EACd;MACA,IAAMgC,QAAQ,GAAG,IAAI,CAACC,cAAc,CAAC,CAAC;MACtC,OAAOD,QAAQ,CAACjC,MAAM,CAACC,MAAM,CAAC;IAChC;EAAA;IAAAP,GAAA;IAAA1B,KAAA,EAeA,SAAOmC,SAASA,CAEdC,QAAiB,EACQ;MACzB,IAAM6B,QAAQ,GAAG,IAAI,CAACC,cAAc,CAAC,CAAC;MACtC,OAAOD,QAAQ,CAAC9B,SAAS,CAACC,QAAQ,CAAC;IACrC;EAAA;IAAAV,GAAA;IAAA1B,KAAA,EAeA,SAAOwC,YAAYA,CAEjBA,aAAoB,EACpB;MACA,IAAMyB,QAAQ,GAAG,IAAI,CAACC,cAAc,CAAC,CAAC;MACtC,OAAOD,QAAQ,CAACzB,YAAY,CAACA,aAAY,CAAC;IAC5C;EAAA;IAAAd,GAAA;IAAA1B,KAAA,EAeA,SAAO0C,OAAOA,CAEZA,QAAe,EACf;MACA,IAAMuB,QAAQ,GAAG,IAAI,CAACC,cAAc,CAAC,CAAC;MACtC,OAAOD,QAAQ,CAACvB,OAAO,CAACA,QAAO,CAAC;IAClC;EAAA;IAAAhB,GAAA;IAAA1B,KAAA,EAeA,SAAO4C,IAAIA,CAAoDA,KAAY,EAAE;MAC3E,IAAMqB,QAAQ,GAAG,IAAI,CAACC,cAAc,CAAC,CAAC;MACtC,OAAOD,QAAQ,CAACrB,IAAI,CAACA,KAAI,CAAC;IAC5B;EAAA;IAAAlB,GAAA;IAAA1B,KAAA,EAcA,SAAO8C,SAASA,CAEdA,UAAiB,EACjB;MACA,IAAMmB,QAAQ,GAAG,IAAI,CAACC,cAAc,CAAC,CAAC;MACtC,OAAOD,QAAQ,CAACnB,SAAS,CAACA,UAAS,CAAC;IACtC;EAAA;IAAApB,GAAA;IAAA1B,KAAA,EAeA,SAAOgD,iBAAiBA,CAEtBA,kBAAyB,EACzB;MACA,IAAMiB,QAAQ,GAAG,IAAI,CAACC,cAAc,CAAC,CAAC;MACtC,OAAOD,QAAQ,CAACjB,iBAAiB,CAACA,kBAAiB,CAAC;IACtD;EAAA;IAAAtB,GAAA;IAAA1B,KAAA,EAeA,SAAOkD,yBAAyBA,CAE9BA,0BAAiC,EACjC;MACA,IAAMe,QAAQ,GAAG,IAAI,CAACC,cAAc,CAAC,CAAC;MACtC,OAAOD,QAAQ,CAACf,yBAAyB,CAACA,0BAAyB,CAAC;IACtE;EAAA;IAAAxB,GAAA;IAAA1B,KAAA,EAgBA,SAAOoD,kBAAkBA,CAEvBA,mBAA0B,EAC1B;MACA,IAAMa,QAAQ,GAAG,IAAI,CAACC,cAAc,CAAC,CAAC;MACtC,OAAOD,QAAQ,CAACb,kBAAkB,CAACA,mBAAkB,CAAC;IACxD;EAAA;IAAA1B,GAAA;IAAA1B,KAAA,EAYA,SAAOsD,iBAAiBA,CAEtBC,MAAkB,EAClB;MACA,IAAMU,QAAQ,GAAG,IAAI,CAACC,cAAc,CAAC,CAAC;MACtC,OAAOD,QAAQ,CAACX,iBAAiB,CAACC,MAAM,CAAC;IAC3C;EAAA;AAAA,EA7O2CY,2CAAoB", "ignoreList": []}