e15dc2c64bbb97a3212d4cdc089793e1
'use strict';

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.ComplexAnimationBuilder = void 0;
var _classCallCheck2 = _interopRequireDefault(require("@babel/runtime/helpers/classCallCheck"));
var _createClass2 = _interopRequireDefault(require("@babel/runtime/helpers/createClass"));
var _possibleConstructorReturn2 = _interopRequireDefault(require("@babel/runtime/helpers/possibleConstructorReturn"));
var _getPrototypeOf2 = _interopRequireDefault(require("@babel/runtime/helpers/getPrototypeOf"));
var _inherits2 = _interopRequireDefault(require("@babel/runtime/helpers/inherits"));
var _index = require("../../animation/index.js");
var _BaseAnimationBuilder2 = require("./BaseAnimationBuilder.js");
var _util = require("../../animation/util.js");
function _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }
function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }
var ComplexAnimationBuilder = exports.ComplexAnimationBuilder = function (_BaseAnimationBuilder) {
  function ComplexAnimationBuilder() {
    (0, _classCallCheck2.default)(this, ComplexAnimationBuilder);
    return _callSuper(this, ComplexAnimationBuilder, arguments);
  }
  (0, _inherits2.default)(ComplexAnimationBuilder, _BaseAnimationBuilder);
  return (0, _createClass2.default)(ComplexAnimationBuilder, [{
    key: "easing",
    value: function easing(easingFunction) {
      if (__DEV__) {
        (0, _util.assertEasingIsWorklet)(easingFunction);
      }
      this.easingV = easingFunction;
      return this;
    }
  }, {
    key: "rotate",
    value: function rotate(degree) {
      this.rotateV = degree;
      return this;
    }
  }, {
    key: "springify",
    value: function springify(duration) {
      this.durationV = duration;
      this.type = _index.withSpring;
      return this;
    }
  }, {
    key: "dampingRatio",
    value: function dampingRatio(value) {
      this.dampingRatioV = value;
      return this;
    }
  }, {
    key: "damping",
    value: function damping(_damping2) {
      this.dampingV = _damping2;
      return this;
    }
  }, {
    key: "mass",
    value: function mass(_mass2) {
      this.massV = _mass2;
      return this;
    }
  }, {
    key: "stiffness",
    value: function stiffness(_stiffness2) {
      this.stiffnessV = _stiffness2;
      return this;
    }
  }, {
    key: "overshootClamping",
    value: function overshootClamping(_overshootClamping2) {
      this.overshootClampingV = _overshootClamping2;
      return this;
    }
  }, {
    key: "restDisplacementThreshold",
    value: function restDisplacementThreshold(_restDisplacementThreshold2) {
      this.restDisplacementThresholdV = _restDisplacementThreshold2;
      return this;
    }
  }, {
    key: "restSpeedThreshold",
    value: function restSpeedThreshold(_restSpeedThreshold2) {
      this.restSpeedThresholdV = _restSpeedThreshold2;
      return this;
    }
  }, {
    key: "withInitialValues",
    value: function withInitialValues(values) {
      this.initialValues = values;
      return this;
    }
  }, {
    key: "getAnimationAndConfig",
    value: function getAnimationAndConfig() {
      var duration = this.durationV;
      var easing = this.easingV;
      var rotate = this.rotateV;
      var type = this.type ? this.type : _index.withTiming;
      var damping = this.dampingV;
      var dampingRatio = this.dampingRatioV;
      var mass = this.massV;
      var stiffness = this.stiffnessV;
      var overshootClamping = this.overshootClampingV;
      var restDisplacementThreshold = this.restDisplacementThresholdV;
      var restSpeedThreshold = this.restSpeedThresholdV;
      var animation = type;
      var config = {};
      function maybeSetConfigValue(value, variableName) {
        if (value) {
          config[variableName] = value;
        }
      }
      if (type === _index.withTiming) {
        maybeSetConfigValue(easing, 'easing');
      }
      [{
        variableName: 'damping',
        value: damping
      }, {
        variableName: 'dampingRatio',
        value: dampingRatio
      }, {
        variableName: 'mass',
        value: mass
      }, {
        variableName: 'stiffness',
        value: stiffness
      }, {
        variableName: 'overshootClamping',
        value: overshootClamping
      }, {
        variableName: 'restDisplacementThreshold',
        value: restDisplacementThreshold
      }, {
        variableName: 'restSpeedThreshold',
        value: restSpeedThreshold
      }, {
        variableName: 'duration',
        value: duration
      }, {
        variableName: 'rotate',
        value: rotate
      }].forEach(function (_ref) {
        var value = _ref.value,
          variableName = _ref.variableName;
        return maybeSetConfigValue(value, variableName);
      });
      return [animation, config];
    }
  }], [{
    key: "easing",
    value: function easing(easingFunction) {
      var instance = this.createInstance();
      return instance.easing(easingFunction);
    }
  }, {
    key: "rotate",
    value: function rotate(degree) {
      var instance = this.createInstance();
      return instance.rotate(degree);
    }
  }, {
    key: "springify",
    value: function springify(duration) {
      var instance = this.createInstance();
      return instance.springify(duration);
    }
  }, {
    key: "dampingRatio",
    value: function dampingRatio(_dampingRatio) {
      var instance = this.createInstance();
      return instance.dampingRatio(_dampingRatio);
    }
  }, {
    key: "damping",
    value: function damping(_damping) {
      var instance = this.createInstance();
      return instance.damping(_damping);
    }
  }, {
    key: "mass",
    value: function mass(_mass) {
      var instance = this.createInstance();
      return instance.mass(_mass);
    }
  }, {
    key: "stiffness",
    value: function stiffness(_stiffness) {
      var instance = this.createInstance();
      return instance.stiffness(_stiffness);
    }
  }, {
    key: "overshootClamping",
    value: function overshootClamping(_overshootClamping) {
      var instance = this.createInstance();
      return instance.overshootClamping(_overshootClamping);
    }
  }, {
    key: "restDisplacementThreshold",
    value: function restDisplacementThreshold(_restDisplacementThreshold) {
      var instance = this.createInstance();
      return instance.restDisplacementThreshold(_restDisplacementThreshold);
    }
  }, {
    key: "restSpeedThreshold",
    value: function restSpeedThreshold(_restSpeedThreshold) {
      var instance = this.createInstance();
      return instance.restSpeedThreshold(_restSpeedThreshold);
    }
  }, {
    key: "withInitialValues",
    value: function withInitialValues(values) {
      var instance = this.createInstance();
      return instance.withInitialValues(values);
    }
  }]);
}(_BaseAnimationBuilder2.BaseAnimationBuilder);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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