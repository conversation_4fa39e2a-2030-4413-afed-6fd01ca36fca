{"version": 3, "names": ["_VirtualizeUtils", "require", "module", "exports", "keyExtractor", "VirtualizedList", "VirtualizedSectionList", "VirtualizedListContextResetter", "VirtualizedListContext", "ViewabilityHelper", "FillRateHelper"], "sources": ["index.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @format\n * @flow\n */\n\n'use strict';\n\nimport typeof FillRateHelper from './Lists/FillRateHelper';\nimport typeof ViewabilityHelper from './Lists/ViewabilityHelper';\nimport typeof VirtualizedList from './Lists/VirtualizedList';\nimport typeof VirtualizedSectionList from './Lists/VirtualizedSectionList';\n\nimport {typeof VirtualizedListContextResetter} from './Lists/VirtualizedListContext';\nimport {keyExtractor} from './Lists/VirtualizeUtils';\n\nexport type {\n  ViewToken,\n  ViewabilityConfig,\n  ViewabilityConfigCallbackPair,\n} from './Lists/ViewabilityHelper';\nexport type {\n  CellRendererProps,\n  RenderItemProps,\n  RenderItemType,\n  Separators,\n} from './Lists/VirtualizedListProps';\nexport type {\n  Props as VirtualizedSectionListProps,\n  ScrollToLocationParamsType,\n  SectionBase,\n} from './Lists/VirtualizedSectionList';\nexport type {FillRateInfo} from './Lists/FillRateHelper';\n\nmodule.exports = {\n  keyExtractor,\n\n  get VirtualizedList(): VirtualizedList {\n    return require('./Lists/VirtualizedList');\n  },\n  get VirtualizedSectionList(): VirtualizedSectionList {\n    return require('./Lists/VirtualizedSectionList');\n  },\n  get VirtualizedListContextResetter(): VirtualizedListContextResetter {\n    const VirtualizedListContext = require('./Lists/VirtualizedListContext');\n    return VirtualizedListContext.VirtualizedListContextResetter;\n  },\n  get ViewabilityHelper(): ViewabilityHelper {\n    return require('./Lists/ViewabilityHelper');\n  },\n  get FillRateHelper(): FillRateHelper {\n    return require('./Lists/FillRateHelper');\n  },\n};\n"], "mappings": "AAUA,YAAY;;AAQZ,IAAAA,gBAAA,GAAAC,OAAA;AAoBAC,MAAM,CAACC,OAAO,GAAG;EACfC,YAAY,EAAZA,6BAAY;EAEZ,IAAIC,eAAeA,CAAA,EAAoB;IACrC,OAAOJ,OAAO,CAAC,yBAAyB,CAAC;EAC3C,CAAC;EACD,IAAIK,sBAAsBA,CAAA,EAA2B;IACnD,OAAOL,OAAO,CAAC,gCAAgC,CAAC;EAClD,CAAC;EACD,IAAIM,8BAA8BA,CAAA,EAAmC;IACnE,IAAMC,sBAAsB,GAAGP,OAAO,CAAC,gCAAgC,CAAC;IACxE,OAAOO,sBAAsB,CAACD,8BAA8B;EAC9D,CAAC;EACD,IAAIE,iBAAiBA,CAAA,EAAsB;IACzC,OAAOR,OAAO,CAAC,2BAA2B,CAAC;EAC7C,CAAC;EACD,IAAIS,cAAcA,CAAA,EAAmB;IACnC,OAAOT,OAAO,CAAC,wBAAwB,CAAC;EAC1C;AACF,CAAC", "ignoreList": []}