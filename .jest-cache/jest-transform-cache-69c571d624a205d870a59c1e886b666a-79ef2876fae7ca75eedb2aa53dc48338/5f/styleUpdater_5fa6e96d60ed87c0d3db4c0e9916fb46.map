{"version": 3, "names": ["_interopRequireDefault", "require", "Object", "defineProperty", "exports", "value", "applyStyle", "applyStyleForBelowTopScreen", "_PlatformChecker", "_UpdateProps", "IS_FABRIC", "isF<PERSON><PERSON>", "createViewDescriptorPaper", "screenId", "tag", "name", "createViewDescriptorFabric", "shadowNodeWrapper", "createViewDescriptor", "applyStyleForTopScreen", "screenTransitionConfig", "event", "screenDimensions", "topScreenId", "screenTransition", "computeTopScreenStyle", "topScreenStyle", "topScreenDescriptor", "updateProps", "undefined", "belowTopScreenId", "computeBelowTopScreenStyle", "belowTopScreenStyle", "belowTopScreenDescriptor"], "sources": ["../../../src/screenTransition/styleUpdater.ts"], "sourcesContent": [null], "mappings": "AAAA,YAAY;;AAAA,IAAAA,sBAAA,GAAAC,OAAA;AAAAC,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,KAAA;AAAA;AAAAD,OAAA,CAAAE,UAAA,GAAAA,UAAA;AAAAF,OAAA,CAAAG,2BAAA,GAAAA,2BAAA;AACZ,IAAAC,gBAAA,GAAAP,OAAA;AACA,IAAAQ,YAAA,GAAAT,sBAAA,CAAAC,OAAA;AAQA,IAAMS,SAAS,GAAG,IAAAC,yBAAQ,EAAC,CAAC;AAE5B,SAASC,yBAAyBA,CAACC,QAAoC,EAAE;EACvE,SAAS;;EACT,OAAO;IAAEC,GAAG,EAAED,QAAQ;IAAEE,IAAI,EAAE;EAAU,CAAC;AAC3C;AACA,SAASC,0BAA0BA,CAACH,QAAoC,EAAE;EACxE,SAAS;;EACT,OAAO;IAAEI,iBAAiB,EAAEJ;EAAS,CAAC;AACxC;AACA,IAAMK,oBAAoB,GAAGR,SAAS,GAClCM,0BAA0B,GAC1BJ,yBAAyB;AAE7B,SAASO,sBAAsBA,CAC7BC,sBAA8C,EAC9CC,KAAoC,EACpC;EACA,SAAS;;EACT,IAAQC,gBAAgB,GACtBF,sBAAsB,CADhBE,gBAAgB;IAAEC,WAAW,GACnCH,sBAAsB,CADEG,WAAW;IAAEC,gBAAA,GACrCJ,sBAAsB,CADeI,gBAAA;EAEvC,IAAwBC,qBAAA,GAA0BD,gBAAgB,CAA1DE,cAAc;EACtB,IAAMA,cAAc,GAAGD,qBAAqB,CAACJ,KAAK,EAAEC,gBAAgB,CAAC;EACrE,IAAMK,mBAAmB,GAAG;IAC1BtB,KAAK,EAAE,CAACa,oBAAoB,CAACK,WAAW,CAAC;EAC3C,CAAC;EACD,IAAAK,oBAAW,EAACD,mBAAmB,EAAED,cAAc,EAAEG,SAAS,CAAC;AAC7D;AAEO,SAAStB,2BAA2BA,CACzCa,sBAA8C,EAC9CC,KAAoC,EACpC;EACA,SAAS;;EACT,IAAQC,gBAAgB,GACtBF,sBAAsB,CADhBE,gBAAgB;IAAEQ,gBAAgB,GACxCV,sBAAsB,CADEU,gBAAgB;IAAEN,gBAAA,GAC1CJ,sBAAsB,CADoBI,gBAAA;EAE5C,IAA6BO,0BAAA,GAA+BP,gBAAgB,CAApEQ,mBAAmB;EAC3B,IAAMA,mBAAmB,GAAGD,0BAA0B,CACpDV,KAAK,EACLC,gBACF,CAAC;EACD,IAAMW,wBAAwB,GAAG;IAC/B5B,KAAK,EAAE,CAACa,oBAAoB,CAACY,gBAAgB,CAAC;EAChD,CAAC;EACD,IAAAF,oBAAW,EAACK,wBAAwB,EAAED,mBAAmB,EAAEH,SAAS,CAAC;AACvE;AAEO,SAASvB,UAAUA,CACxBc,sBAA8C,EAC9CC,KAAoC,EACpC;EACA,SAAS;;EACTF,sBAAsB,CAACC,sBAAsB,EAAEC,KAAK,CAAC;EACrDd,2BAA2B,CAACa,sBAAsB,EAAEC,KAAK,CAAC;AAC5D", "ignoreList": []}