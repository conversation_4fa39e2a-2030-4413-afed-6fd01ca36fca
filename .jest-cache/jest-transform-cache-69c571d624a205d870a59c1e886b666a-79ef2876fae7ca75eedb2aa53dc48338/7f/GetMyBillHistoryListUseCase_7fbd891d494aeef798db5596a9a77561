aaccea5df63fe5f303c64fe633862d7c
"use strict";

/* istanbul ignore next */
function cov_vviy9qibm() {
  var path = "/Users/<USER>/projects/react-native/payment-module/src/domain/usecases/bill-contact/GetMyBillHistoryListUseCase.ts";
  var hash = "7d5c71288f657c756074dc694219949208dcd05e";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/projects/react-native/payment-module/src/domain/usecases/bill-contact/GetMyBillHistoryListUseCase.ts",
    statementMap: {
      "0": {
        start: {
          line: 3,
          column: 29
        },
        end: {
          line: 3,
          column: 84
        }
      },
      "1": {
        start: {
          line: 4,
          column: 25
        },
        end: {
          line: 4,
          column: 99
        }
      },
      "2": {
        start: {
          line: 5,
          column: 23
        },
        end: {
          line: 5,
          column: 95
        }
      },
      "3": {
        start: {
          line: 6,
          column: 20
        },
        end: {
          line: 6,
          column: 89
        }
      },
      "4": {
        start: {
          line: 7,
          column: 0
        },
        end: {
          line: 9,
          column: 3
        }
      },
      "5": {
        start: {
          line: 10,
          column: 0
        },
        end: {
          line: 10,
          column: 45
        }
      },
      "6": {
        start: {
          line: 11,
          column: 26
        },
        end: {
          line: 11,
          column: 69
        }
      },
      "7": {
        start: {
          line: 12,
          column: 34
        },
        end: {
          line: 32,
          column: 3
        }
      },
      "8": {
        start: {
          line: 14,
          column: 4
        },
        end: {
          line: 14,
          column: 69
        }
      },
      "9": {
        start: {
          line: 15,
          column: 4
        },
        end: {
          line: 15,
          column: 33
        }
      },
      "10": {
        start: {
          line: 17,
          column: 2
        },
        end: {
          line: 31,
          column: 6
        }
      },
      "11": {
        start: {
          line: 20,
          column: 21
        },
        end: {
          line: 25,
          column: 8
        }
      },
      "12": {
        start: {
          line: 21,
          column: 20
        },
        end: {
          line: 21,
          column: 24
        }
      },
      "13": {
        start: {
          line: 22,
          column: 8
        },
        end: {
          line: 24,
          column: 11
        }
      },
      "14": {
        start: {
          line: 23,
          column: 10
        },
        end: {
          line: 23,
          column: 64
        }
      },
      "15": {
        start: {
          line: 27,
          column: 8
        },
        end: {
          line: 27,
          column: 47
        }
      },
      "16": {
        start: {
          line: 29,
          column: 6
        },
        end: {
          line: 29,
          column: 21
        }
      },
      "17": {
        start: {
          line: 33,
          column: 0
        },
        end: {
          line: 33,
          column: 66
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 12,
            column: 34
          },
          end: {
            line: 12,
            column: 35
          }
        },
        loc: {
          start: {
            line: 12,
            column: 46
          },
          end: {
            line: 32,
            column: 1
          }
        },
        line: 12
      },
      "1": {
        name: "GetMyBillHistoryListUseCase",
        decl: {
          start: {
            line: 13,
            column: 11
          },
          end: {
            line: 13,
            column: 38
          }
        },
        loc: {
          start: {
            line: 13,
            column: 51
          },
          end: {
            line: 16,
            column: 3
          }
        },
        line: 13
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 19,
            column: 11
          },
          end: {
            line: 19,
            column: 12
          }
        },
        loc: {
          start: {
            line: 19,
            column: 23
          },
          end: {
            line: 30,
            column: 5
          }
        },
        line: 19
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 20,
            column: 53
          },
          end: {
            line: 20,
            column: 54
          }
        },
        loc: {
          start: {
            line: 20,
            column: 73
          },
          end: {
            line: 25,
            column: 7
          }
        },
        line: 20
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 22,
            column: 60
          },
          end: {
            line: 22,
            column: 61
          }
        },
        loc: {
          start: {
            line: 22,
            column: 72
          },
          end: {
            line: 24,
            column: 9
          }
        },
        line: 22
      },
      "5": {
        name: "execute",
        decl: {
          start: {
            line: 26,
            column: 15
          },
          end: {
            line: 26,
            column: 22
          }
        },
        loc: {
          start: {
            line: 26,
            column: 27
          },
          end: {
            line: 28,
            column: 7
          }
        },
        line: 26
      }
    },
    branchMap: {},
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0
    },
    b: {},
    inputSourceMap: {
      version: 3,
      file: undefined,
      names: ["ExcecutionHandler_1", "require", "GetMyBillHistoryListUseCase", "repository", "_classCallCheck2", "default", "_createClass2", "key", "value", "_execute", "_asyncToGenerator2", "request", "_this", "ExecutionHandler", "execute", "getMyBillHistoryList", "_x", "apply", "arguments", "exports"],
      sourceRoot: undefined,
      sources: ["/Users/<USER>/projects/react-native/payment-module/src/domain/usecases/bill-contact/GetMyBillHistoryListUseCase.ts"],
      sourcesContent: ["import {IBillContactRepository} from '../../repositories/IBillContactRepository';\nimport {ExecutionHandler} from '../../../utils/ExcecutionHandler';\nimport {GetMyBillHistoryListRequest} from '../../../data/models/get-my-bill-history-list/GetMyBillHistoryListRequest';\nimport {ResultState} from '../../../core/ResultState';\nimport {BillHistoryModel} from '../../entities/get-my-bill-history-list/GetMyBillHistoryListModel';\nexport class GetMyBillHistoryListUseCase {\n  private repository: IBillContactRepository;\n\n  constructor(repository: IBillContactRepository) {\n    this.repository = repository;\n  }\n\n  public async execute(request: GetMyBillHistoryListRequest): Promise<ResultState<BillHistoryModel>> {\n    // call this.repository.getMyBillHistoryList(...)\n    //TODO: implement state\n    return ExecutionHandler.execute(() => this.repository.getMyBillHistoryList(request));\n  }\n}\n"],
      mappings: ";;;;;;;;;;AACA,IAAAA,mBAAA,GAAAC,OAAA;AAAkE,IAIrDC,2BAA2B;EAGtC,SAAAA,4BAAYC,UAAkC;IAAA,IAAAC,gBAAA,CAAAC,OAAA,QAAAH,2BAAA;IAC5C,IAAI,CAACC,UAAU,GAAGA,UAAU;EAC9B;EAAC,WAAAG,aAAA,CAAAD,OAAA,EAAAH,2BAAA;IAAAK,GAAA;IAAAC,KAAA;MAAA,IAAAC,QAAA,OAAAC,kBAAA,CAAAL,OAAA,EAEM,WAAcM,OAAoC;QAAA,IAAAC,KAAA;QAGvD,OAAOZ,mBAAA,CAAAa,gBAAgB,CAACC,OAAO,CAAC;UAAA,OAAMF,KAAI,CAACT,UAAU,CAACY,oBAAoB,CAACJ,OAAO,CAAC;QAAA,EAAC;MACtF,CAAC;MAAA,SAJYG,OAAOA,CAAAE,EAAA;QAAA,OAAAP,QAAA,CAAAQ,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAPJ,OAAO;IAAA;EAAA;AAAA;AAPtBK,OAAA,CAAAjB,2BAAA,GAAAA,2BAAA",
      ignoreList: []
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "7d5c71288f657c756074dc694219949208dcd05e"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_vviy9qibm = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_vviy9qibm();
var _interopRequireDefault =
/* istanbul ignore next */
(cov_vviy9qibm().s[0]++, require("@babel/runtime/helpers/interopRequireDefault"));
var _asyncToGenerator2 =
/* istanbul ignore next */
(cov_vviy9qibm().s[1]++, _interopRequireDefault(require("@babel/runtime/helpers/asyncToGenerator")));
var _classCallCheck2 =
/* istanbul ignore next */
(cov_vviy9qibm().s[2]++, _interopRequireDefault(require("@babel/runtime/helpers/classCallCheck")));
var _createClass2 =
/* istanbul ignore next */
(cov_vviy9qibm().s[3]++, _interopRequireDefault(require("@babel/runtime/helpers/createClass")));
/* istanbul ignore next */
cov_vviy9qibm().s[4]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_vviy9qibm().s[5]++;
exports.GetMyBillHistoryListUseCase = void 0;
var ExcecutionHandler_1 =
/* istanbul ignore next */
(cov_vviy9qibm().s[6]++, require("../../../utils/ExcecutionHandler"));
var GetMyBillHistoryListUseCase =
/* istanbul ignore next */
(cov_vviy9qibm().s[7]++, function () {
  /* istanbul ignore next */
  cov_vviy9qibm().f[0]++;
  function GetMyBillHistoryListUseCase(repository) {
    /* istanbul ignore next */
    cov_vviy9qibm().f[1]++;
    cov_vviy9qibm().s[8]++;
    (0, _classCallCheck2.default)(this, GetMyBillHistoryListUseCase);
    /* istanbul ignore next */
    cov_vviy9qibm().s[9]++;
    this.repository = repository;
  }
  /* istanbul ignore next */
  cov_vviy9qibm().s[10]++;
  return (0, _createClass2.default)(GetMyBillHistoryListUseCase, [{
    key: "execute",
    value: function () {
      /* istanbul ignore next */
      cov_vviy9qibm().f[2]++;
      var _execute =
      /* istanbul ignore next */
      (cov_vviy9qibm().s[11]++, (0, _asyncToGenerator2.default)(function* (request) {
        /* istanbul ignore next */
        cov_vviy9qibm().f[3]++;
        var _this =
        /* istanbul ignore next */
        (cov_vviy9qibm().s[12]++, this);
        /* istanbul ignore next */
        cov_vviy9qibm().s[13]++;
        return ExcecutionHandler_1.ExecutionHandler.execute(function () {
          /* istanbul ignore next */
          cov_vviy9qibm().f[4]++;
          cov_vviy9qibm().s[14]++;
          return _this.repository.getMyBillHistoryList(request);
        });
      }));
      function execute(_x) {
        /* istanbul ignore next */
        cov_vviy9qibm().f[5]++;
        cov_vviy9qibm().s[15]++;
        return _execute.apply(this, arguments);
      }
      /* istanbul ignore next */
      cov_vviy9qibm().s[16]++;
      return execute;
    }()
  }]);
}());
/* istanbul ignore next */
cov_vviy9qibm().s[17]++;
exports.GetMyBillHistoryListUseCase = GetMyBillHistoryListUseCase;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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