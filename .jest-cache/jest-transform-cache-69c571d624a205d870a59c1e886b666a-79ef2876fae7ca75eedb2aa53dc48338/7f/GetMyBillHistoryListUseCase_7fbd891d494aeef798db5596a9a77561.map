{"version": 3, "names": ["cov_vviy9qibm", "actualCoverage", "ExcecutionHandler_1", "s", "require", "GetMyBillHistoryListUseCase", "f", "repository", "_classCallCheck2", "default", "_createClass2", "key", "value", "_execute", "_asyncToGenerator2", "request", "_this", "ExecutionHandler", "execute", "getMyBillHistoryList", "_x", "apply", "arguments", "exports"], "sources": ["/Users/<USER>/projects/react-native/payment-module/src/domain/usecases/bill-contact/GetMyBillHistoryListUseCase.ts"], "sourcesContent": ["import {IBillContactRepository} from '../../repositories/IBillContactRepository';\nimport {ExecutionHandler} from '../../../utils/ExcecutionHandler';\nimport {GetMyBillHistoryListRequest} from '../../../data/models/get-my-bill-history-list/GetMyBillHistoryListRequest';\nimport {ResultState} from '../../../core/ResultState';\nimport {BillHistoryModel} from '../../entities/get-my-bill-history-list/GetMyBillHistoryListModel';\nexport class GetMyBillHistoryListUseCase {\n  private repository: IBillContactRepository;\n\n  constructor(repository: IBillContactRepository) {\n    this.repository = repository;\n  }\n\n  public async execute(request: GetMyBillHistoryListRequest): Promise<ResultState<BillHistoryModel>> {\n    // call this.repository.getMyBillHistoryList(...)\n    //TODO: implement state\n    return ExecutionHandler.execute(() => this.repository.getMyBillHistoryList(request));\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAUE;IAAAA,aAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,aAAA;;;;;;;;;;;;;;;;;;;;;AATF,IAAAE,mBAAA;AAAA;AAAA,CAAAF,aAAA,GAAAG,CAAA,OAAAC,OAAA;AAAkE,IAIrDC,2BAA2B;AAAA;AAAA,CAAAL,aAAA,GAAAG,CAAA;EAAA;EAAAH,aAAA,GAAAM,CAAA;EAGtC,SAAAD,4BAAYE,UAAkC;IAAA;IAAAP,aAAA,GAAAM,CAAA;IAAAN,aAAA,GAAAG,CAAA;IAAA,IAAAK,gBAAA,CAAAC,OAAA,QAAAJ,2BAAA;IAAA;IAAAL,aAAA,GAAAG,CAAA;IAC5C,IAAI,CAACI,UAAU,GAAGA,UAAU;EAC9B;EAAA;EAAAP,aAAA,GAAAG,CAAA;EAAC,WAAAO,aAAA,CAAAD,OAAA,EAAAJ,2BAAA;IAAAM,GAAA;IAAAC,KAAA;MAAA;MAAAZ,aAAA,GAAAM,CAAA;MAAA,IAAAO,QAAA;MAAA;MAAA,CAAAb,aAAA,GAAAG,CAAA,YAAAW,kBAAA,CAAAL,OAAA,EAEM,WAAcM,OAAoC;QAAA;QAAAf,aAAA,GAAAM,CAAA;QAAA,IAAAU,KAAA;QAAA;QAAA,CAAAhB,aAAA,GAAAG,CAAA;QAAA;QAAAH,aAAA,GAAAG,CAAA;QAGvD,OAAOD,mBAAA,CAAAe,gBAAgB,CAACC,OAAO,CAAC;UAAA;UAAAlB,aAAA,GAAAM,CAAA;UAAAN,aAAA,GAAAG,CAAA;UAAA,OAAMa,KAAI,CAACT,UAAU,CAACY,oBAAoB,CAACJ,OAAO,CAAC;QAAA,EAAC;MACtF,CAAC;MAAA,SAJYG,OAAOA,CAAAE,EAAA;QAAA;QAAApB,aAAA,GAAAM,CAAA;QAAAN,aAAA,GAAAG,CAAA;QAAA,OAAAU,QAAA,CAAAQ,KAAA,OAAAC,SAAA;MAAA;MAAA;MAAAtB,aAAA,GAAAG,CAAA;MAAA,OAAPe,OAAO;IAAA;EAAA;AAAA;AAAA;AAAAlB,aAAA,GAAAG,CAAA;AAPtBoB,OAAA,CAAAlB,2BAAA,GAAAA,2BAAA", "ignoreList": []}