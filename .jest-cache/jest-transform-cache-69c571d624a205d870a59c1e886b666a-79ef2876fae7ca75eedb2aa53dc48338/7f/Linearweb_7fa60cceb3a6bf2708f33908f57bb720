4c0f4d9a05b5a36f71c6e76357fff782
'use strict';

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.LinearTransition = LinearTransition;
function LinearTransition(name, transitionData) {
  var translateX = transitionData.translateX,
    translateY = transitionData.translateY,
    scaleX = transitionData.scaleX,
    scaleY = transitionData.scaleY;
  var linearTransition = {
    name: name,
    style: {
      0: {
        transform: [{
          translateX: `${translateX}px`,
          translateY: `${translateY}px`,
          scale: `${scaleX},${scaleY}`
        }]
      }
    },
    duration: 300
  };
  return linearTransition;
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJPYmplY3QiLCJkZWZpbmVQcm9wZXJ0eSIsImV4cG9ydHMiLCJ2YWx1ZSIsIkxpbmVhclRyYW5zaXRpb24iLCJuYW1lIiwidHJhbnNpdGlvbkRhdGEiLCJ0cmFuc2xhdGVYIiwidHJhbnNsYXRlWSIsInNjYWxlWCIsInNjYWxlWSIsImxpbmVhclRyYW5zaXRpb24iLCJzdHlsZSIsInRyYW5zZm9ybSIsInNjYWxlIiwiZHVyYXRpb24iXSwic291cmNlcyI6WyIuLi8uLi8uLi8uLi8uLi9zcmMvbGF5b3V0UmVhbmltYXRpb24vd2ViL3RyYW5zaXRpb24vTGluZWFyLndlYi50cyJdLCJzb3VyY2VzQ29udGVudCI6W251bGxdLCJtYXBwaW5ncyI6IkFBQUEsWUFBWTs7QUFBQUEsTUFBQSxDQUFBQyxjQUFBLENBQUFDLE9BQUE7RUFBQUMsS0FBQTtBQUFBO0FBQUFELE9BQUEsQ0FBQUUsZ0JBQUEsR0FBQUEsZ0JBQUE7QUFHTCxTQUFTQSxnQkFBZ0JBLENBQUNDLElBQVksRUFBRUMsY0FBOEIsRUFBRTtFQUM3RSxJQUFRQyxVQUFVLEdBQWlDRCxjQUFjLENBQXpEQyxVQUFVO0lBQUVDLFVBQVUsR0FBcUJGLGNBQWMsQ0FBN0NFLFVBQVU7SUFBRUMsTUFBTSxHQUFhSCxjQUFjLENBQWpDRyxNQUFNO0lBQUVDLE1BQUEsR0FBV0osY0FBYyxDQUF6QkksTUFBQTtFQUV4QyxJQUFNQyxnQkFBZ0IsR0FBRztJQUN2Qk4sSUFBSSxFQUFKQSxJQUFJO0lBQ0pPLEtBQUssRUFBRTtNQUNMLENBQUMsRUFBRTtRQUNEQyxTQUFTLEVBQUUsQ0FDVDtVQUNFTixVQUFVLEVBQUUsR0FBR0EsVUFBVSxJQUFJO1VBQzdCQyxVQUFVLEVBQUUsR0FBR0EsVUFBVSxJQUFJO1VBQzdCTSxLQUFLLEVBQUUsR0FBR0wsTUFBTSxJQUFJQyxNQUFNO1FBQzVCLENBQUM7TUFFTDtJQUNGLENBQUM7SUFDREssUUFBUSxFQUFFO0VBQ1osQ0FBQztFQUVELE9BQU9KLGdCQUFnQjtBQUN6QiIsImlnbm9yZUxpc3QiOltdfQ==