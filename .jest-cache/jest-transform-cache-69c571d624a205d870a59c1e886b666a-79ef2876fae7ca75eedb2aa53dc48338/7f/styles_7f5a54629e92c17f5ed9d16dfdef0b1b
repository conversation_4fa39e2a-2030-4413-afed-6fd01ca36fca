1d8b4d496264df3e2db448397e419f06
"use strict";

/* istanbul ignore next */
function cov_1wypb7n4jl() {
  var path = "/Users/<USER>/projects/react-native/payment-module/src/presentation/payment-home/styles.ts";
  var hash = "5bebe72016fe4c13b665964e3733dc84436a6b7e";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/projects/react-native/payment-module/src/presentation/payment-home/styles.ts",
    statementMap: {
      "0": {
        start: {
          line: 3,
          column: 0
        },
        end: {
          line: 5,
          column: 3
        }
      },
      "1": {
        start: {
          line: 6,
          column: 0
        },
        end: {
          line: 6,
          column: 27
        }
      },
      "2": {
        start: {
          line: 7,
          column: 29
        },
        end: {
          line: 7,
          column: 60
        }
      },
      "3": {
        start: {
          line: 8,
          column: 0
        },
        end: {
          line: 60,
          column: 3
        }
      },
      "4": {
        start: {
          line: 9,
          column: 19
        },
        end: {
          line: 9,
          column: 34
        }
      },
      "5": {
        start: {
          line: 10,
          column: 18
        },
        end: {
          line: 10,
          column: 34
        }
      },
      "6": {
        start: {
          line: 11,
          column: 17
        },
        end: {
          line: 11,
          column: 32
        }
      },
      "7": {
        start: {
          line: 12,
          column: 16
        },
        end: {
          line: 12,
          column: 30
        }
      },
      "8": {
        start: {
          line: 13,
          column: 13
        },
        end: {
          line: 13,
          column: 24
        }
      },
      "9": {
        start: {
          line: 14,
          column: 2
        },
        end: {
          line: 59,
          column: 4
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 8,
            column: 68
          },
          end: {
            line: 8,
            column: 69
          }
        },
        loc: {
          start: {
            line: 8,
            column: 84
          },
          end: {
            line: 60,
            column: 1
          }
        },
        line: 8
      }
    },
    branchMap: {},
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0
    },
    f: {
      "0": 0
    },
    b: {},
    inputSourceMap: {
      version: 3,
      file: undefined,
      names: ["msb_shared_component_1", "require", "exports", "makeStyle", "createMSBStyleSheet", "_ref", "SizeGlobal", "ColorGlobal", "ColorAlias", "SizeAlias", "Shadow", "contactCard", "Object", "assign", "backgroundColor", "NeutralWhite", "borderRadius", "Radius3", "borderWidth", "Size25", "borderColor", "BorderDefault", "flexDirection", "flex", "marginHorizontal", "SpacingSmall", "center", "container", "paddingBottom", "bottomSheetContentContainer", "marginBottom", "SpacingMedium", "iconSize", "height", "IconMedium", "width", "moneyHubContainer", "justifyContent", "paddingHorizontal", "moneyHubItem", "alignItems", "scheduleTransfer", "scheduleTransferItem", "spacing", "Size150", "spacingHorizontal"],
      sourceRoot: undefined,
      sources: ["/Users/<USER>/projects/react-native/payment-module/src/presentation/payment-home/styles.ts"],
      sourcesContent: ["// import StyleCommon from '../../commons/Styles';\nimport {createMSBStyleSheet} from 'msb-shared-component';\n\nexport const makeStyle = createMSBStyleSheet(({SizeGlobal, ColorGlobal, ColorAlias, SizeAlias, Shadow}) => {\n  return {\n    contactCard: {\n      backgroundColor: ColorGlobal.NeutralWhite,\n      borderRadius: SizeAlias.Radius3,\n      borderWidth: SizeGlobal.Size25,\n      borderColor: ColorAlias.BorderDefault,\n      flexDirection: 'column',\n      flex: 1,\n      marginHorizontal: SizeAlias.SpacingSmall,\n      // marginBottom: SizeAlias.SpacingMedium,\n      // ...StyleCommon.boxShadow,\n      ...Shadow.center,\n    },\n    container: {\n      flex: 1,\n      paddingBottom: 0,\n    },\n    bottomSheetContentContainer: {\n      flex: 1,\n      marginBottom: SizeAlias.SpacingMedium,\n    },\n    iconSize: {\n      height: SizeAlias.IconMedium,\n      width: SizeAlias.IconMedium,\n    },\n    moneyHubContainer: {\n      flexDirection: 'row',\n      justifyContent: 'space-around',\n      paddingHorizontal: SizeAlias.SpacingSmall,\n    },\n    moneyHubItem: {\n      alignItems: 'center',\n      flexDirection: 'column',\n    },\n    scheduleTransfer: {\n      flexDirection: 'row',\n      justifyContent: 'center',\n    },\n    scheduleTransferItem: {\n      alignItems: 'center',\n      flexDirection: 'row',\n    },\n    spacing: {\n      height: SizeGlobal.Size150,\n    },\n    spacingHorizontal: {\n      width: SizeGlobal.Size150,\n    },\n  };\n});\n"],
      mappings: ";;;;;;AACA,IAAAA,sBAAA,GAAAC,OAAA;AAEaC,OAAA,CAAAC,SAAS,GAAG,IAAAH,sBAAA,CAAAI,mBAAmB,EAAC,UAAAC,IAAA,EAA6D;EAAA,IAA3DC,UAAU,GAAAD,IAAA,CAAVC,UAAU;IAAEC,WAAW,GAAAF,IAAA,CAAXE,WAAW;IAAEC,UAAU,GAAAH,IAAA,CAAVG,UAAU;IAAEC,SAAS,GAAAJ,IAAA,CAATI,SAAS;IAAEC,MAAM,GAAAL,IAAA,CAANK,MAAM;EACnG,OAAO;IACLC,WAAW,EAAAC,MAAA,CAAAC,MAAA;MACTC,eAAe,EAAEP,WAAW,CAACQ,YAAY;MACzCC,YAAY,EAAEP,SAAS,CAACQ,OAAO;MAC/BC,WAAW,EAAEZ,UAAU,CAACa,MAAM;MAC9BC,WAAW,EAAEZ,UAAU,CAACa,aAAa;MACrCC,aAAa,EAAE,QAAQ;MACvBC,IAAI,EAAE,CAAC;MACPC,gBAAgB,EAAEf,SAAS,CAACgB;IAAY,GAGrCf,MAAM,CAACgB,MAAM,CACjB;IACDC,SAAS,EAAE;MACTJ,IAAI,EAAE,CAAC;MACPK,aAAa,EAAE;KAChB;IACDC,2BAA2B,EAAE;MAC3BN,IAAI,EAAE,CAAC;MACPO,YAAY,EAAErB,SAAS,CAACsB;KACzB;IACDC,QAAQ,EAAE;MACRC,MAAM,EAAExB,SAAS,CAACyB,UAAU;MAC5BC,KAAK,EAAE1B,SAAS,CAACyB;KAClB;IACDE,iBAAiB,EAAE;MACjBd,aAAa,EAAE,KAAK;MACpBe,cAAc,EAAE,cAAc;MAC9BC,iBAAiB,EAAE7B,SAAS,CAACgB;KAC9B;IACDc,YAAY,EAAE;MACZC,UAAU,EAAE,QAAQ;MACpBlB,aAAa,EAAE;KAChB;IACDmB,gBAAgB,EAAE;MAChBnB,aAAa,EAAE,KAAK;MACpBe,cAAc,EAAE;KACjB;IACDK,oBAAoB,EAAE;MACpBF,UAAU,EAAE,QAAQ;MACpBlB,aAAa,EAAE;KAChB;IACDqB,OAAO,EAAE;MACPV,MAAM,EAAE3B,UAAU,CAACsC;KACpB;IACDC,iBAAiB,EAAE;MACjBV,KAAK,EAAE7B,UAAU,CAACsC;;GAErB;AACH,CAAC,CAAC",
      ignoreList: []
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "5bebe72016fe4c13b665964e3733dc84436a6b7e"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_1wypb7n4jl = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_1wypb7n4jl();
cov_1wypb7n4jl().s[0]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_1wypb7n4jl().s[1]++;
exports.makeStyle = void 0;
var msb_shared_component_1 =
/* istanbul ignore next */
(cov_1wypb7n4jl().s[2]++, require("msb-shared-component"));
/* istanbul ignore next */
cov_1wypb7n4jl().s[3]++;
exports.makeStyle = (0, msb_shared_component_1.createMSBStyleSheet)(function (_ref) {
  /* istanbul ignore next */
  cov_1wypb7n4jl().f[0]++;
  var SizeGlobal =
    /* istanbul ignore next */
    (cov_1wypb7n4jl().s[4]++, _ref.SizeGlobal),
    ColorGlobal =
    /* istanbul ignore next */
    (cov_1wypb7n4jl().s[5]++, _ref.ColorGlobal),
    ColorAlias =
    /* istanbul ignore next */
    (cov_1wypb7n4jl().s[6]++, _ref.ColorAlias),
    SizeAlias =
    /* istanbul ignore next */
    (cov_1wypb7n4jl().s[7]++, _ref.SizeAlias),
    Shadow =
    /* istanbul ignore next */
    (cov_1wypb7n4jl().s[8]++, _ref.Shadow);
  /* istanbul ignore next */
  cov_1wypb7n4jl().s[9]++;
  return {
    contactCard: Object.assign({
      backgroundColor: ColorGlobal.NeutralWhite,
      borderRadius: SizeAlias.Radius3,
      borderWidth: SizeGlobal.Size25,
      borderColor: ColorAlias.BorderDefault,
      flexDirection: 'column',
      flex: 1,
      marginHorizontal: SizeAlias.SpacingSmall
    }, Shadow.center),
    container: {
      flex: 1,
      paddingBottom: 0
    },
    bottomSheetContentContainer: {
      flex: 1,
      marginBottom: SizeAlias.SpacingMedium
    },
    iconSize: {
      height: SizeAlias.IconMedium,
      width: SizeAlias.IconMedium
    },
    moneyHubContainer: {
      flexDirection: 'row',
      justifyContent: 'space-around',
      paddingHorizontal: SizeAlias.SpacingSmall
    },
    moneyHubItem: {
      alignItems: 'center',
      flexDirection: 'column'
    },
    scheduleTransfer: {
      flexDirection: 'row',
      justifyContent: 'center'
    },
    scheduleTransferItem: {
      alignItems: 'center',
      flexDirection: 'row'
    },
    spacing: {
      height: SizeGlobal.Size150
    },
    spacingHorizontal: {
      width: SizeGlobal.Size150
    }
  };
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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