e1dbff2fbd0b184d6bf243ebb8adcd4d
"use strict";

/* istanbul ignore next */
function cov_2ejddx8s9j() {
  var path = "/Users/<USER>/projects/react-native/payment-module/src/data/mappers/bill-validate/BillValidateMapper.ts";
  var hash = "540f6f3a4d110d8417ae7fc3748f48c6af27ce4d";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/projects/react-native/payment-module/src/data/mappers/bill-validate/BillValidateMapper.ts",
    statementMap: {
      "0": {
        start: {
          line: 3,
          column: 0
        },
        end: {
          line: 5,
          column: 3
        }
      },
      "1": {
        start: {
          line: 6,
          column: 0
        },
        end: {
          line: 6,
          column: 48
        }
      },
      "2": {
        start: {
          line: 7,
          column: 37
        },
        end: {
          line: 9,
          column: 1
        }
      },
      "3": {
        start: {
          line: 8,
          column: 2
        },
        end: {
          line: 8,
          column: 37
        }
      },
      "4": {
        start: {
          line: 10,
          column: 0
        },
        end: {
          line: 10,
          column: 72
        }
      }
    },
    fnMap: {
      "0": {
        name: "mapBillValidateResponseToModel",
        decl: {
          start: {
            line: 7,
            column: 46
          },
          end: {
            line: 7,
            column: 76
          }
        },
        loc: {
          start: {
            line: 7,
            column: 87
          },
          end: {
            line: 9,
            column: 1
          }
        },
        line: 7
      }
    },
    branchMap: {},
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0
    },
    f: {
      "0": 0
    },
    b: {},
    inputSourceMap: {
      version: 3,
      file: undefined,
      names: ["mapBillValidateResponseToModel", "response", "Object", "assign", "exports"],
      sourceRoot: undefined,
      sources: ["/Users/<USER>/projects/react-native/payment-module/src/data/mappers/bill-validate/BillValidateMapper.ts"],
      sourcesContent: ["import {BillValidateResponse} from '../../models/bill-validate/BillValidateResponse';\nimport {BillValidateModel} from '../../../domain/entities/bill-validate/BillValidateModel';\n\nexport const mapBillValidateResponseToModel = (response: BillValidateResponse): BillValidateModel => {\n  return {\n    ...response,\n  };\n};\n"],
      mappings: ";;;;;;AAGO,IAAMA,8BAA8B,GAAG,SAAjCA,8BAA8BA,CAAIC,QAA8B,EAAuB;EAClG,OAAAC,MAAA,CAAAC,MAAA,KACKF,QAAQ;AAEf,CAAC;AAJYG,OAAA,CAAAJ,8BAA8B,GAAAA,8BAAA",
      ignoreList: []
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "540f6f3a4d110d8417ae7fc3748f48c6af27ce4d"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_2ejddx8s9j = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_2ejddx8s9j();
cov_2ejddx8s9j().s[0]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_2ejddx8s9j().s[1]++;
exports.mapBillValidateResponseToModel = void 0;
/* istanbul ignore next */
cov_2ejddx8s9j().s[2]++;
var mapBillValidateResponseToModel = function mapBillValidateResponseToModel(response) {
  /* istanbul ignore next */
  cov_2ejddx8s9j().f[0]++;
  cov_2ejddx8s9j().s[3]++;
  return Object.assign({}, response);
};
/* istanbul ignore next */
cov_2ejddx8s9j().s[4]++;
exports.mapBillValidateResponseToModel = mapBillValidateResponseToModel;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJtYXBCaWxsVmFsaWRhdGVSZXNwb25zZVRvTW9kZWwiLCJyZXNwb25zZSIsImNvdl8yZWpkZHg4czlqIiwiZiIsInMiLCJPYmplY3QiLCJhc3NpZ24iLCJleHBvcnRzIl0sInNvdXJjZXMiOlsiL1VzZXJzL3NvbnNhbXNldC9wcm9qZWN0cy9yZWFjdC1uYXRpdmUvcGF5bWVudC1tb2R1bGUvc3JjL2RhdGEvbWFwcGVycy9iaWxsLXZhbGlkYXRlL0JpbGxWYWxpZGF0ZU1hcHBlci50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQge0JpbGxWYWxpZGF0ZVJlc3BvbnNlfSBmcm9tICcuLi8uLi9tb2RlbHMvYmlsbC12YWxpZGF0ZS9CaWxsVmFsaWRhdGVSZXNwb25zZSc7XG5pbXBvcnQge0JpbGxWYWxpZGF0ZU1vZGVsfSBmcm9tICcuLi8uLi8uLi9kb21haW4vZW50aXRpZXMvYmlsbC12YWxpZGF0ZS9CaWxsVmFsaWRhdGVNb2RlbCc7XG5cbmV4cG9ydCBjb25zdCBtYXBCaWxsVmFsaWRhdGVSZXNwb25zZVRvTW9kZWwgPSAocmVzcG9uc2U6IEJpbGxWYWxpZGF0ZVJlc3BvbnNlKTogQmlsbFZhbGlkYXRlTW9kZWwgPT4ge1xuICByZXR1cm4ge1xuICAgIC4uLnJlc3BvbnNlLFxuICB9O1xufTtcbiJdLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUdPLElBQU1BLDhCQUE4QixHQUFHLFNBQWpDQSw4QkFBOEJBLENBQUlDLFFBQThCLEVBQXVCO0VBQUE7RUFBQUMsY0FBQSxHQUFBQyxDQUFBO0VBQUFELGNBQUEsR0FBQUUsQ0FBQTtFQUNsRyxPQUFBQyxNQUFBLENBQUFDLE1BQUEsS0FDS0wsUUFBUTtBQUVmLENBQUM7QUFBQTtBQUFBQyxjQUFBLEdBQUFFLENBQUE7QUFKWUcsT0FBQSxDQUFBUCw4QkFBOEIsR0FBQUEsOEJBQUEiLCJpZ25vcmVMaXN0IjpbXX0=