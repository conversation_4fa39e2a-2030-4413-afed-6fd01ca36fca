{"version": 3, "names": ["cov_1wypb7n4jl", "actualCoverage", "s", "msb_shared_component_1", "require", "exports", "makeStyle", "createMSBStyleSheet", "_ref", "f", "SizeGlobal", "ColorGlobal", "ColorAlias", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Shadow", "contactCard", "Object", "assign", "backgroundColor", "NeutralWhite", "borderRadius", "Radius3", "borderWidth", "Size25", "borderColor", "BorderDefault", "flexDirection", "flex", "marginHorizontal", "SpacingSmall", "center", "container", "paddingBottom", "bottomSheetContentContainer", "marginBottom", "SpacingMedium", "iconSize", "height", "IconMedium", "width", "moneyHubContainer", "justifyContent", "paddingHorizontal", "moneyHubItem", "alignItems", "scheduleTransfer", "scheduleTransferItem", "spacing", "Size150", "spacingHorizontal"], "sources": ["/Users/<USER>/projects/react-native/payment-module/src/presentation/payment-home/styles.ts"], "sourcesContent": ["// import StyleCommon from '../../commons/Styles';\nimport {createMSBStyleSheet} from 'msb-shared-component';\n\nexport const makeStyle = createMSBStyleSheet(({SizeGlobal, ColorGlobal, ColorAlias, SizeAlias, Shadow}) => {\n  return {\n    contactCard: {\n      backgroundColor: ColorGlobal.NeutralWhite,\n      borderRadius: SizeAlias.Radius3,\n      borderWidth: SizeGlobal.Size25,\n      borderColor: ColorAlias.BorderDefault,\n      flexDirection: 'column',\n      flex: 1,\n      marginHorizontal: SizeAlias.SpacingSmall,\n      // marginBottom: SizeAlias.SpacingMedium,\n      // ...StyleCommon.boxShadow,\n      ...Shadow.center,\n    },\n    container: {\n      flex: 1,\n      paddingBottom: 0,\n    },\n    bottomSheetContentContainer: {\n      flex: 1,\n      marginBottom: SizeAlias.SpacingMedium,\n    },\n    iconSize: {\n      height: SizeAlias.IconMedium,\n      width: SizeAlias.IconMedium,\n    },\n    moneyHubContainer: {\n      flexDirection: 'row',\n      justifyContent: 'space-around',\n      paddingHorizontal: SizeAlias.SpacingSmall,\n    },\n    moneyHubItem: {\n      alignItems: 'center',\n      flexDirection: 'column',\n    },\n    scheduleTransfer: {\n      flexDirection: 'row',\n      justifyContent: 'center',\n    },\n    scheduleTransferItem: {\n      alignItems: 'center',\n      flexDirection: 'row',\n    },\n    spacing: {\n      height: SizeGlobal.Size150,\n    },\n    spacingHorizontal: {\n      width: SizeGlobal.Size150,\n    },\n  };\n});\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAMM;IAAAA,cAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,cAAA;AAAAA,cAAA,GAAAE,CAAA;;;;;;;AALN,IAAAC,sBAAA;AAAA;AAAA,CAAAH,cAAA,GAAAE,CAAA,OAAAE,OAAA;AAAA;AAAAJ,cAAA,GAAAE,CAAA;AAEaG,OAAA,CAAAC,SAAS,GAAG,IAAAH,sBAAA,CAAAI,mBAAmB,EAAC,UAAAC,IAAA,EAA6D;EAAA;EAAAR,cAAA,GAAAS,CAAA;EAAA,IAA3DC,UAAU;IAAA;IAAA,CAAAV,cAAA,GAAAE,CAAA,OAAAM,IAAA,CAAVE,UAAU;IAAEC,WAAW;IAAA;IAAA,CAAAX,cAAA,GAAAE,CAAA,OAAAM,IAAA,CAAXG,WAAW;IAAEC,UAAU;IAAA;IAAA,CAAAZ,cAAA,GAAAE,CAAA,OAAAM,IAAA,CAAVI,UAAU;IAAEC,SAAS;IAAA;IAAA,CAAAb,cAAA,GAAAE,CAAA,OAAAM,IAAA,CAATK,SAAS;IAAEC,MAAM;IAAA;IAAA,CAAAd,cAAA,GAAAE,CAAA,OAAAM,IAAA,CAANM,MAAM;EAAA;EAAAd,cAAA,GAAAE,CAAA;EACnG,OAAO;IACLa,WAAW,EAAAC,MAAA,CAAAC,MAAA;MACTC,eAAe,EAAEP,WAAW,CAACQ,YAAY;MACzCC,YAAY,EAAEP,SAAS,CAACQ,OAAO;MAC/BC,WAAW,EAAEZ,UAAU,CAACa,MAAM;MAC9BC,WAAW,EAAEZ,UAAU,CAACa,aAAa;MACrCC,aAAa,EAAE,QAAQ;MACvBC,IAAI,EAAE,CAAC;MACPC,gBAAgB,EAAEf,SAAS,CAACgB;IAAY,GAGrCf,MAAM,CAACgB,MAAM,CACjB;IACDC,SAAS,EAAE;MACTJ,IAAI,EAAE,CAAC;MACPK,aAAa,EAAE;KAChB;IACDC,2BAA2B,EAAE;MAC3BN,IAAI,EAAE,CAAC;MACPO,YAAY,EAAErB,SAAS,CAACsB;KACzB;IACDC,QAAQ,EAAE;MACRC,MAAM,EAAExB,SAAS,CAACyB,UAAU;MAC5BC,KAAK,EAAE1B,SAAS,CAACyB;KAClB;IACDE,iBAAiB,EAAE;MACjBd,aAAa,EAAE,KAAK;MACpBe,cAAc,EAAE,cAAc;MAC9BC,iBAAiB,EAAE7B,SAAS,CAACgB;KAC9B;IACDc,YAAY,EAAE;MACZC,UAAU,EAAE,QAAQ;MACpBlB,aAAa,EAAE;KAChB;IACDmB,gBAAgB,EAAE;MAChBnB,aAAa,EAAE,KAAK;MACpBe,cAAc,EAAE;KACjB;IACDK,oBAAoB,EAAE;MACpBF,UAAU,EAAE,QAAQ;MACpBlB,aAAa,EAAE;KAChB;IACDqB,OAAO,EAAE;MACPV,MAAM,EAAE3B,UAAU,CAACsC;KACpB;IACDC,iBAAiB,EAAE;MACjBV,KAAK,EAAE7B,UAAU,CAACsC;;GAErB;AACH,CAAC,CAAC", "ignoreList": []}