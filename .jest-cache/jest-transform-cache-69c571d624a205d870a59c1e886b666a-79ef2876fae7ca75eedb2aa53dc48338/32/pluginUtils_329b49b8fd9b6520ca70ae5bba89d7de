a718ac7d4765ae48c2f012f4149fac66
'use strict';

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.getUseOfValueInStyleWarning = getUseOfValueInStyleWarning;
function getUseOfValueInStyleWarning() {
  return "It looks like you might be using shared value's .value inside reanimated inline style. " + 'If you want a component to update when shared value changes you should use the shared value' + ' directly instead of its current state represented by `.value`. See documentation here: ' + 'https://docs.swmansion.com/react-native-reanimated/docs/fundamentals/glossary/#animations-in-inline-styling';
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJPYmplY3QiLCJkZWZpbmVQcm9wZXJ0eSIsImV4cG9ydHMiLCJ2YWx1ZSIsImdldFVzZU9mVmFsdWVJblN0eWxlV2FybmluZyJdLCJzb3VyY2VzIjpbIi4uLy4uL3NyYy9wbHVnaW5VdGlscy50cyJdLCJzb3VyY2VzQ29udGVudCI6W251bGxdLCJtYXBwaW5ncyI6IkFBQUEsWUFBWTs7QUFBQUEsTUFBQSxDQUFBQyxjQUFBLENBQUFDLE9BQUE7RUFBQUMsS0FBQTtBQUFBO0FBQUFELE9BQUEsQ0FBQUUsMkJBQUEsR0FBQUEsMkJBQUE7QUFDTCxTQUFTQSwyQkFBMkJBLENBQUEsRUFBRztFQUM1QyxPQUNFLHlGQUF5RixHQUN6Riw2RkFBNkYsR0FDN0YsMEZBQTBGLEdBQzFGLDZHQUE2RztBQUVqSCIsImlnbm9yZUxpc3QiOltdfQ==