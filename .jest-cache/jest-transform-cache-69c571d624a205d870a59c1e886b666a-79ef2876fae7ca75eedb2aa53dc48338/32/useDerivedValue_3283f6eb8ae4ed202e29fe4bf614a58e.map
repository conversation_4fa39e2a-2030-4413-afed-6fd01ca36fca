{"version": 3, "names": ["_interopRequireDefault", "require", "Object", "defineProperty", "exports", "value", "useDerivedValue", "_toConsumableArray2", "_react", "_index", "_core", "_PlatformChecker", "updater", "dependencies", "_updater$__closure", "initRef", "useRef", "inputs", "values", "__closure", "shouldBeUseWeb", "_dependencies", "length", "undefined", "concat", "default", "__workletHash", "push", "current", "makeMutable", "initialUpdaterRun", "sharedValue", "useEffect", "fun", "mapperId", "startMapper", "stopMapper"], "sources": ["../../../src/hook/useDerivedValue.ts"], "sourcesContent": [null], "mappings": "AAAA,YAAY;;AAAA,IAAAA,sBAAA,GAAAC,OAAA;AAAAC,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,KAAA;AAAA;AAAAD,OAAA,CAAAE,eAAA,GAAAA,eAAA;AAAA,IAAAC,mBAAA,GAAAP,sBAAA,CAAAC,OAAA;AACZ,IAAAO,MAAA,GAAAP,OAAA;AACA,IAAAQ,MAAA,GAAAR,OAAA;AAEA,IAAAS,KAAA,GAAAT,OAAA;AAEA,IAAAU,gBAAA,GAAAV,OAAA;AA8BO,SAASK,eAAeA,CAC7BM,OAAmC,EACnCC,YAA6B,EACR;EAAA,IAAAC,kBAAA;EACrB,IAAMC,OAAO,GAAG,IAAAC,aAAM,EAA4B,IAAI,CAAC;EACvD,IAAIC,MAAM,GAAGf,MAAM,CAACgB,MAAM,EAAAJ,kBAAA,GAACF,OAAO,CAACO,SAAS,YAAAL,kBAAA,GAAI,CAAC,CAAC,CAAC;EACnD,IAAI,IAAAM,+BAAc,EAAC,CAAC,EAAE;IAAA,IAAAC,aAAA;IACpB,IAAI,CAACJ,MAAM,CAACK,MAAM,KAAAD,aAAA,GAAIR,YAAY,aAAZQ,aAAA,CAAcC,MAAM,EAAE;MAE1CL,MAAM,GAAGJ,YAAY;IACvB;EACF;EAGA,IAAIA,YAAY,KAAKU,SAAS,EAAE;IAC9BV,YAAY,MAAAW,MAAA,KAAAjB,mBAAA,CAAAkB,OAAA,EAAOR,MAAM,IAAEL,OAAO,CAACc,aAAa,EAAC;EACnD,CAAC,MAAM;IACLb,YAAY,CAACc,IAAI,CAACf,OAAO,CAACc,aAAa,CAAC;EAC1C;EAEA,IAAIX,OAAO,CAACa,OAAO,KAAK,IAAI,EAAE;IAC5Bb,OAAO,CAACa,OAAO,GAAG,IAAAC,iBAAW,EAAC,IAAAC,wBAAiB,EAAClB,OAAO,CAAC,CAAC;EAC3D;EAEA,IAAMmB,WAA+B,GAAGhB,OAAO,CAACa,OAAO;EAEvD,IAAAI,gBAAS,EAAC,YAAM;IACd,IAAMC,GAAG,GAAG,SAANA,GAAGA,CAAA,EAAS;MAChB,SAAS;;MACTF,WAAW,CAAC1B,KAAK,GAAGO,OAAO,CAAC,CAAC;IAC/B,CAAC;IACD,IAAMsB,QAAQ,GAAG,IAAAC,iBAAW,EAACF,GAAG,EAAEhB,MAAM,EAAE,CACxCc,WAAW,CACZ,CAAC;IACF,OAAO,YAAM;MACX,IAAAK,gBAAU,EAACF,QAAQ,CAAC;IACtB,CAAC;EACH,CAAC,EAAErB,YAAY,CAAC;EAEhB,OAAOkB,WAAW;AACpB", "ignoreList": []}