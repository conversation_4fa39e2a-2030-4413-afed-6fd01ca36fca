{"version": 3, "names": [], "sources": ["/Users/<USER>/projects/react-native/payment-module/src/data/models/get-profile/GetProfileResponse.ts"], "sourcesContent": ["export interface GetProfileResponse {\n  id?: string;\n  username?: string;\n  customer?: Customer;\n  internalUserId?: string;\n  cif?: string;\n  fullName?: string;\n  firstName?: string;\n  lastName?: string;\n  icNo?: string;\n  oldIcNo?: string;\n  icExpiredDate?: string;\n  icIssuedDate?: string;\n  email?: string;\n  dob?: string;\n  serviceGroup?: string;\n  segment?: string;\n  branchCode?: string;\n  currentAddress?: string;\n  incomeCode?: string;\n  incomeValue?: string;\n  marriageStatus?: string;\n  permanentAddress?: string;\n  personalReferralCode?: string;\n  referralCodeBy?: string;\n  additions?: Additions;\n  occupationCode?: string;\n  occupationPosition?: string;\n  programCode?: string;\n  hubCode?: string;\n  bioEnrollmentState?: string;\n  icType?: string;\n  gender?: string;\n  icPlace?: string;\n  createdBy?: string;\n  updatedBy?: string;\n  createdAt?: any;\n  updatedAt?: any;\n}\n\nexport interface Customer {\n  id?: string;\n  phoneNo?: string;\n  type?: string;\n  status?: string;\n  additions?: Additions;\n  createdBy?: any;\n  updatedBy?: any;\n  createdAt?: string;\n  updatedAt?: string;\n}\n\nexport interface Additions {}\n"], "mappings": "", "ignoreList": []}