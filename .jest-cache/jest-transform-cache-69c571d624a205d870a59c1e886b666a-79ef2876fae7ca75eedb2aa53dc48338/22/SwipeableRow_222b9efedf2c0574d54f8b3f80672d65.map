{"version": 3, "names": ["react_1", "cov_189a83zfws", "s", "__importDefault", "require", "react_native_1", "react_native_gesture_handler_1", "react_native_reanimated_1", "__importStar", "msb_shared_component_1", "nativeGesture", "Gesture", "Native", "SwipeableRow", "props", "f", "translateX", "useSharedValue", "prevTranslationX", "panGesture", "Pan", "minPointers", "maxPointers", "activeOffsetX", "hitSlop", "horizontal", "onStart", "value", "onUpdate", "event", "b", "translationX", "onEnd", "SizeGlobal", "Size1200", "with<PERSON><PERSON><PERSON>", "simultaneousWithExternalGesture", "requireExternalGestureToFail", "animatedStyle", "useAnimatedStyle", "transform", "animatedStyleAndroid", "opacity", "interpolate", "disabledSwipe", "default", "createElement", "View", "pointerEvents", "style", "styles", "container", "children", "row", "renderRightActions", "GestureDetector", "gesture", "exports", "StyleSheet", "create", "justifyContent", "position", "right", "height"], "sources": ["/Users/<USER>/projects/react-native/payment-module/src/components/swiable-row/SwipeableRow.tsx"], "sourcesContent": ["import React from 'react';\nimport {StyleSheet, View} from 'react-native';\nimport {Gesture, GestureDetector} from 'react-native-gesture-handler';\nimport Animated, {\n  interpolate,\n  SharedValue,\n  useAnimatedStyle,\n  useSharedValue,\n  withSpring,\n} from 'react-native-reanimated';\nimport {SizeGlobal} from 'msb-shared-component';\n\ntype Props = {\n  children: React.ReactNode;\n  renderRightActions: (translateX: SharedValue<number>) => React.ReactNode;\n  disabledSwipe?: boolean;\n};\nconst nativeGesture = Gesture.Native(); // Detect native scroll\nexport const SwipeableRow: React.FC<Props> = props => {\n  const translateX = useSharedValue(0);\n  const prevTranslationX = useSharedValue(0);\n\n  const panGesture = Gesture.Pan()\n    .minPointers(1)\n    .maxPointers(1)\n    .activeOffsetX([-10, 10])\n    .hitSlop({horizontal: -20})\n    .onStart(() => {\n      prevTranslationX.value = translateX.value;\n    })\n    .onUpdate(event => {\n      if (translateX.value > 0) {\n        // Change this condition to prevent swiping left\n        translateX.value = 1;\n        return;\n      }\n      if (translateX.value <= -120) {\n        translateX.value = -120;\n        return;\n      }\n      translateX.value = event.translationX + prevTranslationX.value;\n    })\n    .onEnd(() => {\n      if (translateX.value < -SizeGlobal.Size1200 * 2) {\n        translateX.value = withSpring(-SizeGlobal.Size1200 * 2);\n      } else {\n        translateX.value = withSpring(0);\n      }\n    })\n    .simultaneousWithExternalGesture(nativeGesture)\n    .requireExternalGestureToFail(nativeGesture);\n\n  const animatedStyle = useAnimatedStyle(() => ({\n    transform: [{translateX: translateX.value}],\n  }));\n\n  const animatedStyleAndroid = useAnimatedStyle(() => {\n    const opacity = interpolate(translateX.value, [0, -SizeGlobal.Size1200 * 2], [0, 1]);\n    return {\n      opacity: opacity,\n    };\n  });\n\n  return props.disabledSwipe ? (\n    <View pointerEvents=\"box-none\" style={styles.container}>\n      <Animated.View style={animatedStyle}>{props.children}</Animated.View>\n      <Animated.View style={[styles.row, animatedStyleAndroid]}>{props.renderRightActions?.(translateX)}</Animated.View>\n    </View>\n  ) : (\n    <GestureDetector gesture={panGesture}>\n      {/*<View style={{justifyContent: 'center'}}>*/}\n      <View pointerEvents=\"box-none\" style={styles.container}>\n        <Animated.View style={animatedStyle}>{props.children}</Animated.View>\n        <Animated.View style={[styles.row, animatedStyleAndroid]}>\n          {props.renderRightActions?.(translateX)}\n        </Animated.View>\n      </View>\n    </GestureDetector>\n  );\n  // return <Swipeable renderRightActions={renderRightActions}>{props.children}</Swipeable>;\n};\n\nconst styles = StyleSheet.create({\n  container: {justifyContent: 'center'},\n  row: {position: 'absolute', right: 0, height: '100%'},\n});\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAAA,OAAA;AAAA;AAAA,CAAAC,cAAA,GAAAC,CAAA,QAAAC,eAAA,CAAAC,OAAA;AACA,IAAAC,cAAA;AAAA;AAAA,CAAAJ,cAAA,GAAAC,CAAA,QAAAE,OAAA;AACA,IAAAE,8BAAA;AAAA;AAAA,CAAAL,cAAA,GAAAC,CAAA,QAAAE,OAAA;AACA,IAAAG,yBAAA;AAAA;AAAA,CAAAN,cAAA,GAAAC,CAAA,QAAAM,YAAA,CAAAJ,OAAA;AAOA,IAAAK,sBAAA;AAAA;AAAA,CAAAR,cAAA,GAAAC,CAAA,QAAAE,OAAA;AAOA,IAAMM,aAAa;AAAA;AAAA,CAAAT,cAAA,GAAAC,CAAA,QAAGI,8BAAA,CAAAK,OAAO,CAACC,MAAM,EAAE;AAAA;AAAAX,cAAA,GAAAC,CAAA;AAC/B,IAAMW,YAAY,GAAoB,SAAhCA,YAAYA,CAAoBC,KAAK,EAAG;EAAA;EAAAb,cAAA,GAAAc,CAAA;EACnD,IAAMC,UAAU;EAAA;EAAA,CAAAf,cAAA,GAAAC,CAAA,QAAG,IAAAK,yBAAA,CAAAU,cAAc,EAAC,CAAC,CAAC;EACpC,IAAMC,gBAAgB;EAAA;EAAA,CAAAjB,cAAA,GAAAC,CAAA,QAAG,IAAAK,yBAAA,CAAAU,cAAc,EAAC,CAAC,CAAC;EAE1C,IAAME,UAAU;EAAA;EAAA,CAAAlB,cAAA,GAAAC,CAAA,QAAGI,8BAAA,CAAAK,OAAO,CAACS,GAAG,EAAE,CAC7BC,WAAW,CAAC,CAAC,CAAC,CACdC,WAAW,CAAC,CAAC,CAAC,CACdC,aAAa,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CACxBC,OAAO,CAAC;IAACC,UAAU,EAAE,CAAC;EAAE,CAAC,CAAC,CAC1BC,OAAO,CAAC,YAAK;IAAA;IAAAzB,cAAA,GAAAc,CAAA;IAAAd,cAAA,GAAAC,CAAA;IACZgB,gBAAgB,CAACS,KAAK,GAAGX,UAAU,CAACW,KAAK;EAC3C,CAAC,CAAC,CACDC,QAAQ,CAAC,UAAAC,KAAK,EAAG;IAAA;IAAA5B,cAAA,GAAAc,CAAA;IAAAd,cAAA,GAAAC,CAAA;IAChB,IAAIc,UAAU,CAACW,KAAK,GAAG,CAAC,EAAE;MAAA;MAAA1B,cAAA,GAAA6B,CAAA;MAAA7B,cAAA,GAAAC,CAAA;MAExBc,UAAU,CAACW,KAAK,GAAG,CAAC;MAAA;MAAA1B,cAAA,GAAAC,CAAA;MACpB;IACF;IAAA;IAAA;MAAAD,cAAA,GAAA6B,CAAA;IAAA;IAAA7B,cAAA,GAAAC,CAAA;IACA,IAAIc,UAAU,CAACW,KAAK,IAAI,CAAC,GAAG,EAAE;MAAA;MAAA1B,cAAA,GAAA6B,CAAA;MAAA7B,cAAA,GAAAC,CAAA;MAC5Bc,UAAU,CAACW,KAAK,GAAG,CAAC,GAAG;MAAA;MAAA1B,cAAA,GAAAC,CAAA;MACvB;IACF;IAAA;IAAA;MAAAD,cAAA,GAAA6B,CAAA;IAAA;IAAA7B,cAAA,GAAAC,CAAA;IACAc,UAAU,CAACW,KAAK,GAAGE,KAAK,CAACE,YAAY,GAAGb,gBAAgB,CAACS,KAAK;EAChE,CAAC,CAAC,CACDK,KAAK,CAAC,YAAK;IAAA;IAAA/B,cAAA,GAAAc,CAAA;IAAAd,cAAA,GAAAC,CAAA;IACV,IAAIc,UAAU,CAACW,KAAK,GAAG,CAAClB,sBAAA,CAAAwB,UAAU,CAACC,QAAQ,GAAG,CAAC,EAAE;MAAA;MAAAjC,cAAA,GAAA6B,CAAA;MAAA7B,cAAA,GAAAC,CAAA;MAC/Cc,UAAU,CAACW,KAAK,GAAG,IAAApB,yBAAA,CAAA4B,UAAU,EAAC,CAAC1B,sBAAA,CAAAwB,UAAU,CAACC,QAAQ,GAAG,CAAC,CAAC;IACzD,CAAC,MAAM;MAAA;MAAAjC,cAAA,GAAA6B,CAAA;MAAA7B,cAAA,GAAAC,CAAA;MACLc,UAAU,CAACW,KAAK,GAAG,IAAApB,yBAAA,CAAA4B,UAAU,EAAC,CAAC,CAAC;IAClC;EACF,CAAC,CAAC,CACDC,+BAA+B,CAAC1B,aAAa,CAAC,CAC9C2B,4BAA4B,CAAC3B,aAAa,CAAC;EAE9C,IAAM4B,aAAa;EAAA;EAAA,CAAArC,cAAA,GAAAC,CAAA,QAAG,IAAAK,yBAAA,CAAAgC,gBAAgB,EAAC;IAAA;IAAAtC,cAAA,GAAAc,CAAA;IAAAd,cAAA,GAAAC,CAAA;IAAA,OAAO;MAC5CsC,SAAS,EAAE,CAAC;QAACxB,UAAU,EAAEA,UAAU,CAACW;MAAK,CAAC;KAC3C;EAAA,CAAC,CAAC;EAEH,IAAMc,oBAAoB;EAAA;EAAA,CAAAxC,cAAA,GAAAC,CAAA,QAAG,IAAAK,yBAAA,CAAAgC,gBAAgB,EAAC,YAAK;IAAA;IAAAtC,cAAA,GAAAc,CAAA;IACjD,IAAM2B,OAAO;IAAA;IAAA,CAAAzC,cAAA,GAAAC,CAAA,QAAG,IAAAK,yBAAA,CAAAoC,WAAW,EAAC3B,UAAU,CAACW,KAAK,EAAE,CAAC,CAAC,EAAE,CAAClB,sBAAA,CAAAwB,UAAU,CAACC,QAAQ,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAAA;IAAAjC,cAAA,GAAAC,CAAA;IACpF,OAAO;MACLwC,OAAO,EAAEA;KACV;EACH,CAAC,CAAC;EAAA;EAAAzC,cAAA,GAAAC,CAAA;EAEF,OAAOY,KAAK,CAAC8B,aAAa;EAAA;EAAA,CAAA3C,cAAA,GAAA6B,CAAA,WACxB9B,OAAA,CAAA6C,OAAA,CAAAC,aAAA,CAACzC,cAAA,CAAA0C,IAAI;IAACC,aAAa,EAAC,UAAU;IAACC,KAAK,EAAEC,MAAM,CAACC;EAAS,GACpDnD,OAAA,CAAA6C,OAAA,CAAAC,aAAA,CAACvC,yBAAA,CAAAsC,OAAQ,CAACE,IAAI;IAACE,KAAK,EAAEX;EAAa,GAAGxB,KAAK,CAACsC,QAAQ,CAAiB,EACrEpD,OAAA,CAAA6C,OAAA,CAAAC,aAAA,CAACvC,yBAAA,CAAAsC,OAAQ,CAACE,IAAI;IAACE,KAAK,EAAE,CAACC,MAAM,CAACG,GAAG,EAAEZ,oBAAoB;EAAC,GAAG3B,KAAK,CAACwC,kBAAkB;EAAA;EAAA,CAAArD,cAAA,GAAA6B,CAAA;EAAA;EAAA,CAAA7B,cAAA,GAAA6B,CAAA,WAAxBhB,KAAK,CAACwC,kBAAkB,CAAGtC,UAAU,CAAC,EAAiB,CAC7G;EAAA;EAAA,CAAAf,cAAA,GAAA6B,CAAA,WAEP9B,OAAA,CAAA6C,OAAA,CAAAC,aAAA,CAACxC,8BAAA,CAAAiD,eAAe;IAACC,OAAO,EAAErC;EAAU,GAElCnB,OAAA,CAAA6C,OAAA,CAAAC,aAAA,CAACzC,cAAA,CAAA0C,IAAI;IAACC,aAAa,EAAC,UAAU;IAACC,KAAK,EAAEC,MAAM,CAACC;EAAS,GACpDnD,OAAA,CAAA6C,OAAA,CAAAC,aAAA,CAACvC,yBAAA,CAAAsC,OAAQ,CAACE,IAAI;IAACE,KAAK,EAAEX;EAAa,GAAGxB,KAAK,CAACsC,QAAQ,CAAiB,EACrEpD,OAAA,CAAA6C,OAAA,CAAAC,aAAA,CAACvC,yBAAA,CAAAsC,OAAQ,CAACE,IAAI;IAACE,KAAK,EAAE,CAACC,MAAM,CAACG,GAAG,EAAEZ,oBAAoB;EAAC,GACrD3B,KAAK,CAACwC,kBAAkB;EAAA;EAAA,CAAArD,cAAA,GAAA6B,CAAA;EAAA;EAAA,CAAA7B,cAAA,GAAA6B,CAAA,WAAxBhB,KAAK,CAACwC,kBAAkB,CAAGtC,UAAU,CAAC,EACzB,CACX,CAEV;AAEH,CAAC;AAAA;AAAAf,cAAA,GAAAC,CAAA;AA9DYuD,OAAA,CAAA5C,YAAY,GAAAA,YAAA;AAgEzB,IAAMqC,MAAM;AAAA;AAAA,CAAAjD,cAAA,GAAAC,CAAA,QAAGG,cAAA,CAAAqD,UAAU,CAACC,MAAM,CAAC;EAC/BR,SAAS,EAAE;IAACS,cAAc,EAAE;EAAQ,CAAC;EACrCP,GAAG,EAAE;IAACQ,QAAQ,EAAE,UAAU;IAAEC,KAAK,EAAE,CAAC;IAAEC,MAAM,EAAE;EAAM;CACrD,CAAC", "ignoreList": []}