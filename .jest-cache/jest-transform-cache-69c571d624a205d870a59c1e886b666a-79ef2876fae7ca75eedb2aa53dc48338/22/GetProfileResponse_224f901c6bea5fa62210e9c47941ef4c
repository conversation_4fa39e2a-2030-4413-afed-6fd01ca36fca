52c42b35a6106a7eccd9ba740d1a652b
"use strict";

/* istanbul ignore next */
function cov_1b6y7h00gf() {
  var path = "/Users/<USER>/projects/react-native/payment-module/src/data/models/get-profile/GetProfileResponse.ts";
  var hash = "3316e681fd8a7b5dfd3d1afe398e55e156ab9865";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/projects/react-native/payment-module/src/data/models/get-profile/GetProfileResponse.ts",
    statementMap: {
      "0": {
        start: {
          line: 3,
          column: 0
        },
        end: {
          line: 5,
          column: 3
        }
      }
    },
    fnMap: {},
    branchMap: {},
    s: {
      "0": 0
    },
    f: {},
    b: {},
    inputSourceMap: {
      version: 3,
      file: undefined,
      names: [],
      sourceRoot: undefined,
      sources: ["/Users/<USER>/projects/react-native/payment-module/src/data/models/get-profile/GetProfileResponse.ts"],
      sourcesContent: ["export interface GetProfileResponse {\n  id?: string;\n  username?: string;\n  customer?: Customer;\n  internalUserId?: string;\n  cif?: string;\n  fullName?: string;\n  firstName?: string;\n  lastName?: string;\n  icNo?: string;\n  oldIcNo?: string;\n  icExpiredDate?: string;\n  icIssuedDate?: string;\n  email?: string;\n  dob?: string;\n  serviceGroup?: string;\n  segment?: string;\n  branchCode?: string;\n  currentAddress?: string;\n  incomeCode?: string;\n  incomeValue?: string;\n  marriageStatus?: string;\n  permanentAddress?: string;\n  personalReferralCode?: string;\n  referralCodeBy?: string;\n  additions?: Additions;\n  occupationCode?: string;\n  occupationPosition?: string;\n  programCode?: string;\n  hubCode?: string;\n  bioEnrollmentState?: string;\n  icType?: string;\n  gender?: string;\n  icPlace?: string;\n  createdBy?: string;\n  updatedBy?: string;\n  createdAt?: any;\n  updatedAt?: any;\n}\n\nexport interface Customer {\n  id?: string;\n  phoneNo?: string;\n  type?: string;\n  status?: string;\n  additions?: Additions;\n  createdBy?: any;\n  updatedBy?: any;\n  createdAt?: string;\n  updatedAt?: string;\n}\n\nexport interface Additions {}\n"],
      mappings: "",
      ignoreList: []
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "3316e681fd8a7b5dfd3d1afe398e55e156ab9865"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_1b6y7h00gf = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_1b6y7h00gf();
cov_1b6y7h00gf().s[0]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6W10sInNvdXJjZXMiOlsiL1VzZXJzL3NvbnNhbXNldC9wcm9qZWN0cy9yZWFjdC1uYXRpdmUvcGF5bWVudC1tb2R1bGUvc3JjL2RhdGEvbW9kZWxzL2dldC1wcm9maWxlL0dldFByb2ZpbGVSZXNwb25zZS50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgaW50ZXJmYWNlIEdldFByb2ZpbGVSZXNwb25zZSB7XG4gIGlkPzogc3RyaW5nO1xuICB1c2VybmFtZT86IHN0cmluZztcbiAgY3VzdG9tZXI/OiBDdXN0b21lcjtcbiAgaW50ZXJuYWxVc2VySWQ/OiBzdHJpbmc7XG4gIGNpZj86IHN0cmluZztcbiAgZnVsbE5hbWU/OiBzdHJpbmc7XG4gIGZpcnN0TmFtZT86IHN0cmluZztcbiAgbGFzdE5hbWU/OiBzdHJpbmc7XG4gIGljTm8/OiBzdHJpbmc7XG4gIG9sZEljTm8/OiBzdHJpbmc7XG4gIGljRXhwaXJlZERhdGU/OiBzdHJpbmc7XG4gIGljSXNzdWVkRGF0ZT86IHN0cmluZztcbiAgZW1haWw/OiBzdHJpbmc7XG4gIGRvYj86IHN0cmluZztcbiAgc2VydmljZUdyb3VwPzogc3RyaW5nO1xuICBzZWdtZW50Pzogc3RyaW5nO1xuICBicmFuY2hDb2RlPzogc3RyaW5nO1xuICBjdXJyZW50QWRkcmVzcz86IHN0cmluZztcbiAgaW5jb21lQ29kZT86IHN0cmluZztcbiAgaW5jb21lVmFsdWU/OiBzdHJpbmc7XG4gIG1hcnJpYWdlU3RhdHVzPzogc3RyaW5nO1xuICBwZXJtYW5lbnRBZGRyZXNzPzogc3RyaW5nO1xuICBwZXJzb25hbFJlZmVycmFsQ29kZT86IHN0cmluZztcbiAgcmVmZXJyYWxDb2RlQnk/OiBzdHJpbmc7XG4gIGFkZGl0aW9ucz86IEFkZGl0aW9ucztcbiAgb2NjdXBhdGlvbkNvZGU/OiBzdHJpbmc7XG4gIG9jY3VwYXRpb25Qb3NpdGlvbj86IHN0cmluZztcbiAgcHJvZ3JhbUNvZGU/OiBzdHJpbmc7XG4gIGh1YkNvZGU/OiBzdHJpbmc7XG4gIGJpb0Vucm9sbG1lbnRTdGF0ZT86IHN0cmluZztcbiAgaWNUeXBlPzogc3RyaW5nO1xuICBnZW5kZXI/OiBzdHJpbmc7XG4gIGljUGxhY2U/OiBzdHJpbmc7XG4gIGNyZWF0ZWRCeT86IHN0cmluZztcbiAgdXBkYXRlZEJ5Pzogc3RyaW5nO1xuICBjcmVhdGVkQXQ/OiBhbnk7XG4gIHVwZGF0ZWRBdD86IGFueTtcbn1cblxuZXhwb3J0IGludGVyZmFjZSBDdXN0b21lciB7XG4gIGlkPzogc3RyaW5nO1xuICBwaG9uZU5vPzogc3RyaW5nO1xuICB0eXBlPzogc3RyaW5nO1xuICBzdGF0dXM/OiBzdHJpbmc7XG4gIGFkZGl0aW9ucz86IEFkZGl0aW9ucztcbiAgY3JlYXRlZEJ5PzogYW55O1xuICB1cGRhdGVkQnk/OiBhbnk7XG4gIGNyZWF0ZWRBdD86IHN0cmluZztcbiAgdXBkYXRlZEF0Pzogc3RyaW5nO1xufVxuXG5leHBvcnQgaW50ZXJmYWNlIEFkZGl0aW9ucyB7fVxuIl0sIm1hcHBpbmdzIjoiIiwiaWdub3JlTGlzdCI6W119