{"version": 3, "names": ["cov_1gn21uqobv", "actualCoverage", "react_1", "s", "require", "DIContainer_1", "PopupUtils_1", "native_1", "useCategory", "f", "navigation", "useNavigation", "_ref", "useState", "_ref2", "_slicedToArray2", "default", "categories", "setCategories", "_ref3", "_ref4", "state", "setState", "useEffect", "getCategories", "_ref5", "_asyncToGenerator2", "console", "log", "result", "DIContainer", "getInstance", "getCategoryListUseCase", "execute", "status", "b", "showErrorPopup", "error", "data", "apply", "arguments", "gotoPaymentBill", "_ref6", "category", "getValidateCustomerUseCase", "id", "navigate", "_x", "exports"], "sources": ["/Users/<USER>/projects/react-native/payment-module/src/presentation/payment-home/components/feature-section/hook.ts"], "sourcesContent": ["import {useEffect, useState} from 'react';\nimport {CategoryModel} from '../../../../domain/entities/category-list/CategoryListModel';\nimport {DIContainer} from '../../../../di/DIContainer';\nimport {showErrorPopup} from '../../../../utils/PopupUtils';\nimport {NavigationProp, useNavigation} from '@react-navigation/native';\nimport {PaymentStackParamList} from '../../../../navigation/PaymentStack';\n\nexport const useCategory = () => {\n  const navigation = useNavigation<NavigationProp<PaymentStackParamList>>();\n  const [categories, setCategories] = useState<CategoryModel[] | undefined>();\n  const [state, setState] = useState<'INIT' | 'LOADING' | 'SUCCESS' | 'ERROR'>('INIT');\n  useEffect(() => {\n    getCategories();\n  }, []);\n\n  const getCategories = async () => {\n    console.log('start call useCategory');\n    const result = await DIContainer.getInstance().getCategoryListUseCase().execute();\n    setState(result.status);\n    if (result.status === 'ERROR') {\n      showErrorPopup(result.error);\n    } else if (result.status === 'SUCCESS') {\n      setCategories(result?.data);\n    }\n  };\n\n  const gotoPaymentBill = async (category: CategoryModel) => {\n    console.log('checkCustomerDetail');\n    const result = await DIContainer.getInstance().getValidateCustomerUseCase().execute();\n    if (result.status === 'ERROR') {\n      showErrorPopup(result.error);\n    } //TODO : navigate to next screen\n    if (category.id === 'MB-MR') {\n      navigation.navigate('PaymentPhoneScreen', {category});\n    } else {\n      navigation.navigate('PaymentBillScreen', {category});\n    }\n  };\n\n  return {\n    categories,\n    state,\n    gotoPaymentBill,\n  };\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IASE;IAAAA,cAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,cAAA;;;;;;;;;;;;;;;;;;AATF,IAAAE,OAAA;AAAA;AAAA,CAAAF,cAAA,GAAAG,CAAA,OAAAC,OAAA;AAEA,IAAAC,aAAA;AAAA;AAAA,CAAAL,cAAA,GAAAG,CAAA,OAAAC,OAAA;AACA,IAAAE,YAAA;AAAA;AAAA,CAAAN,cAAA,GAAAG,CAAA,OAAAC,OAAA;AACA,IAAAG,QAAA;AAAA;AAAA,CAAAP,cAAA,GAAAG,CAAA,OAAAC,OAAA;AAAA;AAAAJ,cAAA,GAAAG,CAAA;AAGO,IAAMK,WAAW,GAAG,SAAdA,WAAWA,CAAA,EAAQ;EAAA;EAAAR,cAAA,GAAAS,CAAA;EAC9B,IAAMC,UAAU;EAAA;EAAA,CAAAV,cAAA,GAAAG,CAAA,QAAG,IAAAI,QAAA,CAAAI,aAAa,GAAyC;EACzE,IAAAC,IAAA;IAAA;IAAA,CAAAZ,cAAA,GAAAG,CAAA,QAAoC,IAAAD,OAAA,CAAAW,QAAQ,GAA+B;IAAAC,KAAA;IAAA;IAAA,CAAAd,cAAA,GAAAG,CAAA,YAAAY,eAAA,CAAAC,OAAA,EAAAJ,IAAA;IAApEK,UAAU;IAAA;IAAA,CAAAjB,cAAA,GAAAG,CAAA,QAAAW,KAAA;IAAEI,aAAa;IAAA;IAAA,CAAAlB,cAAA,GAAAG,CAAA,QAAAW,KAAA;EAChC,IAAAK,KAAA;IAAA;IAAA,CAAAnB,cAAA,GAAAG,CAAA,QAA0B,IAAAD,OAAA,CAAAW,QAAQ,EAA2C,MAAM,CAAC;IAAAO,KAAA;IAAA;IAAA,CAAApB,cAAA,GAAAG,CAAA,YAAAY,eAAA,CAAAC,OAAA,EAAAG,KAAA;IAA7EE,KAAK;IAAA;IAAA,CAAArB,cAAA,GAAAG,CAAA,QAAAiB,KAAA;IAAEE,QAAQ;IAAA;IAAA,CAAAtB,cAAA,GAAAG,CAAA,QAAAiB,KAAA;EAAA;EAAApB,cAAA,GAAAG,CAAA;EACtB,IAAAD,OAAA,CAAAqB,SAAS,EAAC,YAAK;IAAA;IAAAvB,cAAA,GAAAS,CAAA;IAAAT,cAAA,GAAAG,CAAA;IACbqB,aAAa,EAAE;EACjB,CAAC,EAAE,EAAE,CAAC;EAEN,IAAMA,aAAa;EAAA;EAAA,CAAAxB,cAAA,GAAAG,CAAA;IAAA;IAAAH,cAAA,GAAAS,CAAA;IAAA,IAAAgB,KAAA;IAAA;IAAA,CAAAzB,cAAA,GAAAG,CAAA,YAAAuB,kBAAA,CAAAV,OAAA,EAAG,aAAW;MAAA;MAAAhB,cAAA,GAAAS,CAAA;MAAAT,cAAA,GAAAG,CAAA;MAC/BwB,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAC;MACrC,IAAMC,MAAM;MAAA;MAAA,CAAA7B,cAAA,GAAAG,CAAA,cAASE,aAAA,CAAAyB,WAAW,CAACC,WAAW,EAAE,CAACC,sBAAsB,EAAE,CAACC,OAAO,EAAE;MAAA;MAAAjC,cAAA,GAAAG,CAAA;MACjFmB,QAAQ,CAACO,MAAM,CAACK,MAAM,CAAC;MAAA;MAAAlC,cAAA,GAAAG,CAAA;MACvB,IAAI0B,MAAM,CAACK,MAAM,KAAK,OAAO,EAAE;QAAA;QAAAlC,cAAA,GAAAmC,CAAA;QAAAnC,cAAA,GAAAG,CAAA;QAC7B,IAAAG,YAAA,CAAA8B,cAAc,EAACP,MAAM,CAACQ,KAAK,CAAC;MAC9B,CAAC,MAAM;QAAA;QAAArC,cAAA,GAAAmC,CAAA;QAAAnC,cAAA,GAAAG,CAAA;QAAA,IAAI0B,MAAM,CAACK,MAAM,KAAK,SAAS,EAAE;UAAA;UAAAlC,cAAA,GAAAmC,CAAA;UAAAnC,cAAA,GAAAG,CAAA;UACtCe,aAAa,CAACW,MAAM;UAAA;UAAA,CAAA7B,cAAA,GAAAmC,CAAA;UAAA;UAAA,CAAAnC,cAAA,GAAAmC,CAAA,UAANN,MAAM,CAAES,IAAI,EAAC;QAC7B;QAAA;QAAA;UAAAtC,cAAA,GAAAmC,CAAA;QAAA;MAAA;IACF,CAAC;IAAA;IAAAnC,cAAA,GAAAG,CAAA;IAAA,gBATKqB,aAAaA,CAAA;MAAA;MAAAxB,cAAA,GAAAS,CAAA;MAAAT,cAAA,GAAAG,CAAA;MAAA,OAAAsB,KAAA,CAAAc,KAAA,OAAAC,SAAA;IAAA;EAAA,GASlB;EAED,IAAMC,eAAe;EAAA;EAAA,CAAAzC,cAAA,GAAAG,CAAA;IAAA;IAAAH,cAAA,GAAAS,CAAA;IAAA,IAAAiC,KAAA;IAAA;IAAA,CAAA1C,cAAA,GAAAG,CAAA,YAAAuB,kBAAA,CAAAV,OAAA,EAAG,WAAO2B,QAAuB,EAAI;MAAA;MAAA3C,cAAA,GAAAS,CAAA;MAAAT,cAAA,GAAAG,CAAA;MACxDwB,OAAO,CAACC,GAAG,CAAC,qBAAqB,CAAC;MAClC,IAAMC,MAAM;MAAA;MAAA,CAAA7B,cAAA,GAAAG,CAAA,cAASE,aAAA,CAAAyB,WAAW,CAACC,WAAW,EAAE,CAACa,0BAA0B,EAAE,CAACX,OAAO,EAAE;MAAA;MAAAjC,cAAA,GAAAG,CAAA;MACrF,IAAI0B,MAAM,CAACK,MAAM,KAAK,OAAO,EAAE;QAAA;QAAAlC,cAAA,GAAAmC,CAAA;QAAAnC,cAAA,GAAAG,CAAA;QAC7B,IAAAG,YAAA,CAAA8B,cAAc,EAACP,MAAM,CAACQ,KAAK,CAAC;MAC9B;MAAA;MAAA;QAAArC,cAAA,GAAAmC,CAAA;MAAA;MAAAnC,cAAA,GAAAG,CAAA;MACA,IAAIwC,QAAQ,CAACE,EAAE,KAAK,OAAO,EAAE;QAAA;QAAA7C,cAAA,GAAAmC,CAAA;QAAAnC,cAAA,GAAAG,CAAA;QAC3BO,UAAU,CAACoC,QAAQ,CAAC,oBAAoB,EAAE;UAACH,QAAQ,EAARA;QAAQ,CAAC,CAAC;MACvD,CAAC,MAAM;QAAA;QAAA3C,cAAA,GAAAmC,CAAA;QAAAnC,cAAA,GAAAG,CAAA;QACLO,UAAU,CAACoC,QAAQ,CAAC,mBAAmB,EAAE;UAACH,QAAQ,EAARA;QAAQ,CAAC,CAAC;MACtD;IACF,CAAC;IAAA;IAAA3C,cAAA,GAAAG,CAAA;IAAA,gBAXKsC,eAAeA,CAAAM,EAAA;MAAA;MAAA/C,cAAA,GAAAS,CAAA;MAAAT,cAAA,GAAAG,CAAA;MAAA,OAAAuC,KAAA,CAAAH,KAAA,OAAAC,SAAA;IAAA;EAAA,GAWpB;EAAA;EAAAxC,cAAA,GAAAG,CAAA;EAED,OAAO;IACLc,UAAU,EAAVA,UAAU;IACVI,KAAK,EAALA,KAAK;IACLoB,eAAe,EAAfA;GACD;AACH,CAAC;AAAA;AAAAzC,cAAA,GAAAG,CAAA;AArCY6C,OAAA,CAAAxC,WAAW,GAAAA,WAAA", "ignoreList": []}