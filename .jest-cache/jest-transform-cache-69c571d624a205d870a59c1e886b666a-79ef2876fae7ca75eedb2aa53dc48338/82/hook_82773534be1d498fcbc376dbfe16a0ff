b92655fdfed703e4f7fb406584425b16
"use strict";

/* istanbul ignore next */
function cov_1gn21uqobv() {
  var path = "/Users/<USER>/projects/react-native/payment-module/src/presentation/payment-home/components/feature-section/hook.ts";
  var hash = "a74ffc573a4d2a5e01e9468cd94693f586a90892";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/projects/react-native/payment-module/src/presentation/payment-home/components/feature-section/hook.ts",
    statementMap: {
      "0": {
        start: {
          line: 3,
          column: 29
        },
        end: {
          line: 3,
          column: 84
        }
      },
      "1": {
        start: {
          line: 4,
          column: 25
        },
        end: {
          line: 4,
          column: 99
        }
      },
      "2": {
        start: {
          line: 5,
          column: 22
        },
        end: {
          line: 5,
          column: 93
        }
      },
      "3": {
        start: {
          line: 6,
          column: 0
        },
        end: {
          line: 8,
          column: 3
        }
      },
      "4": {
        start: {
          line: 9,
          column: 0
        },
        end: {
          line: 9,
          column: 29
        }
      },
      "5": {
        start: {
          line: 10,
          column: 14
        },
        end: {
          line: 10,
          column: 30
        }
      },
      "6": {
        start: {
          line: 11,
          column: 20
        },
        end: {
          line: 11,
          column: 57
        }
      },
      "7": {
        start: {
          line: 12,
          column: 19
        },
        end: {
          line: 12,
          column: 58
        }
      },
      "8": {
        start: {
          line: 13,
          column: 15
        },
        end: {
          line: 13,
          column: 50
        }
      },
      "9": {
        start: {
          line: 14,
          column: 18
        },
        end: {
          line: 68,
          column: 1
        }
      },
      "10": {
        start: {
          line: 15,
          column: 19
        },
        end: {
          line: 15,
          column: 48
        }
      },
      "11": {
        start: {
          line: 16,
          column: 13
        },
        end: {
          line: 16,
          column: 36
        }
      },
      "12": {
        start: {
          line: 17,
          column: 12
        },
        end: {
          line: 17,
          column: 49
        }
      },
      "13": {
        start: {
          line: 18,
          column: 17
        },
        end: {
          line: 18,
          column: 25
        }
      },
      "14": {
        start: {
          line: 19,
          column: 20
        },
        end: {
          line: 19,
          column: 28
        }
      },
      "15": {
        start: {
          line: 20,
          column: 14
        },
        end: {
          line: 20,
          column: 43
        }
      },
      "16": {
        start: {
          line: 21,
          column: 12
        },
        end: {
          line: 21,
          column: 50
        }
      },
      "17": {
        start: {
          line: 22,
          column: 12
        },
        end: {
          line: 22,
          column: 20
        }
      },
      "18": {
        start: {
          line: 23,
          column: 15
        },
        end: {
          line: 23,
          column: 23
        }
      },
      "19": {
        start: {
          line: 24,
          column: 2
        },
        end: {
          line: 26,
          column: 9
        }
      },
      "20": {
        start: {
          line: 25,
          column: 4
        },
        end: {
          line: 25,
          column: 20
        }
      },
      "21": {
        start: {
          line: 27,
          column: 22
        },
        end: {
          line: 41,
          column: 5
        }
      },
      "22": {
        start: {
          line: 28,
          column: 16
        },
        end: {
          line: 37,
          column: 6
        }
      },
      "23": {
        start: {
          line: 29,
          column: 6
        },
        end: {
          line: 29,
          column: 44
        }
      },
      "24": {
        start: {
          line: 30,
          column: 19
        },
        end: {
          line: 30,
          column: 99
        }
      },
      "25": {
        start: {
          line: 31,
          column: 6
        },
        end: {
          line: 31,
          column: 30
        }
      },
      "26": {
        start: {
          line: 32,
          column: 6
        },
        end: {
          line: 36,
          column: 7
        }
      },
      "27": {
        start: {
          line: 33,
          column: 8
        },
        end: {
          line: 33,
          column: 55
        }
      },
      "28": {
        start: {
          line: 34,
          column: 13
        },
        end: {
          line: 36,
          column: 7
        }
      },
      "29": {
        start: {
          line: 35,
          column: 8
        },
        end: {
          line: 35,
          column: 61
        }
      },
      "30": {
        start: {
          line: 38,
          column: 4
        },
        end: {
          line: 40,
          column: 6
        }
      },
      "31": {
        start: {
          line: 39,
          column: 6
        },
        end: {
          line: 39,
          column: 42
        }
      },
      "32": {
        start: {
          line: 42,
          column: 24
        },
        end: {
          line: 62,
          column: 5
        }
      },
      "33": {
        start: {
          line: 43,
          column: 16
        },
        end: {
          line: 58,
          column: 6
        }
      },
      "34": {
        start: {
          line: 44,
          column: 6
        },
        end: {
          line: 44,
          column: 41
        }
      },
      "35": {
        start: {
          line: 45,
          column: 19
        },
        end: {
          line: 45,
          column: 103
        }
      },
      "36": {
        start: {
          line: 46,
          column: 6
        },
        end: {
          line: 48,
          column: 7
        }
      },
      "37": {
        start: {
          line: 47,
          column: 8
        },
        end: {
          line: 47,
          column: 55
        }
      },
      "38": {
        start: {
          line: 49,
          column: 6
        },
        end: {
          line: 57,
          column: 7
        }
      },
      "39": {
        start: {
          line: 50,
          column: 8
        },
        end: {
          line: 52,
          column: 11
        }
      },
      "40": {
        start: {
          line: 54,
          column: 8
        },
        end: {
          line: 56,
          column: 11
        }
      },
      "41": {
        start: {
          line: 59,
          column: 4
        },
        end: {
          line: 61,
          column: 6
        }
      },
      "42": {
        start: {
          line: 60,
          column: 6
        },
        end: {
          line: 60,
          column: 42
        }
      },
      "43": {
        start: {
          line: 63,
          column: 2
        },
        end: {
          line: 67,
          column: 4
        }
      },
      "44": {
        start: {
          line: 69,
          column: 0
        },
        end: {
          line: 69,
          column: 34
        }
      }
    },
    fnMap: {
      "0": {
        name: "useCategory",
        decl: {
          start: {
            line: 14,
            column: 27
          },
          end: {
            line: 14,
            column: 38
          }
        },
        loc: {
          start: {
            line: 14,
            column: 41
          },
          end: {
            line: 68,
            column: 1
          }
        },
        line: 14
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 24,
            column: 25
          },
          end: {
            line: 24,
            column: 26
          }
        },
        loc: {
          start: {
            line: 24,
            column: 37
          },
          end: {
            line: 26,
            column: 3
          }
        },
        line: 24
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 27,
            column: 22
          },
          end: {
            line: 27,
            column: 23
          }
        },
        loc: {
          start: {
            line: 27,
            column: 34
          },
          end: {
            line: 41,
            column: 3
          }
        },
        line: 27
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 28,
            column: 48
          },
          end: {
            line: 28,
            column: 49
          }
        },
        loc: {
          start: {
            line: 28,
            column: 61
          },
          end: {
            line: 37,
            column: 5
          }
        },
        line: 28
      },
      "4": {
        name: "getCategories",
        decl: {
          start: {
            line: 38,
            column: 20
          },
          end: {
            line: 38,
            column: 33
          }
        },
        loc: {
          start: {
            line: 38,
            column: 36
          },
          end: {
            line: 40,
            column: 5
          }
        },
        line: 38
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 42,
            column: 24
          },
          end: {
            line: 42,
            column: 25
          }
        },
        loc: {
          start: {
            line: 42,
            column: 36
          },
          end: {
            line: 62,
            column: 3
          }
        },
        line: 42
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 43,
            column: 48
          },
          end: {
            line: 43,
            column: 49
          }
        },
        loc: {
          start: {
            line: 43,
            column: 69
          },
          end: {
            line: 58,
            column: 5
          }
        },
        line: 43
      },
      "7": {
        name: "gotoPaymentBill",
        decl: {
          start: {
            line: 59,
            column: 20
          },
          end: {
            line: 59,
            column: 35
          }
        },
        loc: {
          start: {
            line: 59,
            column: 40
          },
          end: {
            line: 61,
            column: 5
          }
        },
        line: 59
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 32,
            column: 6
          },
          end: {
            line: 36,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 32,
            column: 6
          },
          end: {
            line: 36,
            column: 7
          }
        }, {
          start: {
            line: 34,
            column: 13
          },
          end: {
            line: 36,
            column: 7
          }
        }],
        line: 32
      },
      "1": {
        loc: {
          start: {
            line: 34,
            column: 13
          },
          end: {
            line: 36,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 34,
            column: 13
          },
          end: {
            line: 36,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 34
      },
      "2": {
        loc: {
          start: {
            line: 35,
            column: 22
          },
          end: {
            line: 35,
            column: 59
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 35,
            column: 39
          },
          end: {
            line: 35,
            column: 45
          }
        }, {
          start: {
            line: 35,
            column: 48
          },
          end: {
            line: 35,
            column: 59
          }
        }],
        line: 35
      },
      "3": {
        loc: {
          start: {
            line: 46,
            column: 6
          },
          end: {
            line: 48,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 46,
            column: 6
          },
          end: {
            line: 48,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 46
      },
      "4": {
        loc: {
          start: {
            line: 49,
            column: 6
          },
          end: {
            line: 57,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 49,
            column: 6
          },
          end: {
            line: 57,
            column: 7
          }
        }, {
          start: {
            line: 53,
            column: 13
          },
          end: {
            line: 57,
            column: 7
          }
        }],
        line: 49
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0
    },
    b: {
      "0": [0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0]
    },
    inputSourceMap: {
      version: 3,
      file: undefined,
      names: ["react_1", "require", "DIContainer_1", "PopupUtils_1", "native_1", "useCategory", "navigation", "useNavigation", "_ref", "useState", "_ref2", "_slicedToArray2", "default", "categories", "setCategories", "_ref3", "_ref4", "state", "setState", "useEffect", "getCategories", "_ref5", "_asyncToGenerator2", "console", "log", "result", "DIContainer", "getInstance", "getCategoryListUseCase", "execute", "status", "showErrorPopup", "error", "data", "apply", "arguments", "gotoPaymentBill", "_ref6", "category", "getValidateCustomerUseCase", "id", "navigate", "_x", "exports"],
      sourceRoot: undefined,
      sources: ["/Users/<USER>/projects/react-native/payment-module/src/presentation/payment-home/components/feature-section/hook.ts"],
      sourcesContent: ["import {useEffect, useState} from 'react';\nimport {CategoryModel} from '../../../../domain/entities/category-list/CategoryListModel';\nimport {DIContainer} from '../../../../di/DIContainer';\nimport {showErrorPopup} from '../../../../utils/PopupUtils';\nimport {NavigationProp, useNavigation} from '@react-navigation/native';\nimport {PaymentStackParamList} from '../../../../navigation/PaymentStack';\n\nexport const useCategory = () => {\n  const navigation = useNavigation<NavigationProp<PaymentStackParamList>>();\n  const [categories, setCategories] = useState<CategoryModel[] | undefined>();\n  const [state, setState] = useState<'INIT' | 'LOADING' | 'SUCCESS' | 'ERROR'>('INIT');\n  useEffect(() => {\n    getCategories();\n  }, []);\n\n  const getCategories = async () => {\n    console.log('start call useCategory');\n    const result = await DIContainer.getInstance().getCategoryListUseCase().execute();\n    setState(result.status);\n    if (result.status === 'ERROR') {\n      showErrorPopup(result.error);\n    } else if (result.status === 'SUCCESS') {\n      setCategories(result?.data);\n    }\n  };\n\n  const gotoPaymentBill = async (category: CategoryModel) => {\n    console.log('checkCustomerDetail');\n    const result = await DIContainer.getInstance().getValidateCustomerUseCase().execute();\n    if (result.status === 'ERROR') {\n      showErrorPopup(result.error);\n    } //TODO : navigate to next screen\n    if (category.id === 'MB-MR') {\n      navigation.navigate('PaymentPhoneScreen', {category});\n    } else {\n      navigation.navigate('PaymentBillScreen', {category});\n    }\n  };\n\n  return {\n    categories,\n    state,\n    gotoPaymentBill,\n  };\n};\n"],
      mappings: ";;;;;;;;;AAAA,IAAAA,OAAA,GAAAC,OAAA;AAEA,IAAAC,aAAA,GAAAD,OAAA;AACA,IAAAE,YAAA,GAAAF,OAAA;AACA,IAAAG,QAAA,GAAAH,OAAA;AAGO,IAAMI,WAAW,GAAG,SAAdA,WAAWA,CAAA,EAAQ;EAC9B,IAAMC,UAAU,GAAG,IAAAF,QAAA,CAAAG,aAAa,GAAyC;EACzE,IAAAC,IAAA,GAAoC,IAAAR,OAAA,CAAAS,QAAQ,GAA+B;IAAAC,KAAA,OAAAC,eAAA,CAAAC,OAAA,EAAAJ,IAAA;IAApEK,UAAU,GAAAH,KAAA;IAAEI,aAAa,GAAAJ,KAAA;EAChC,IAAAK,KAAA,GAA0B,IAAAf,OAAA,CAAAS,QAAQ,EAA2C,MAAM,CAAC;IAAAO,KAAA,OAAAL,eAAA,CAAAC,OAAA,EAAAG,KAAA;IAA7EE,KAAK,GAAAD,KAAA;IAAEE,QAAQ,GAAAF,KAAA;EACtB,IAAAhB,OAAA,CAAAmB,SAAS,EAAC,YAAK;IACbC,aAAa,EAAE;EACjB,CAAC,EAAE,EAAE,CAAC;EAEN,IAAMA,aAAa;IAAA,IAAAC,KAAA,OAAAC,kBAAA,CAAAV,OAAA,EAAG,aAAW;MAC/BW,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAC;MACrC,IAAMC,MAAM,SAASvB,aAAA,CAAAwB,WAAW,CAACC,WAAW,EAAE,CAACC,sBAAsB,EAAE,CAACC,OAAO,EAAE;MACjFX,QAAQ,CAACO,MAAM,CAACK,MAAM,CAAC;MACvB,IAAIL,MAAM,CAACK,MAAM,KAAK,OAAO,EAAE;QAC7B,IAAA3B,YAAA,CAAA4B,cAAc,EAACN,MAAM,CAACO,KAAK,CAAC;MAC9B,CAAC,MAAM,IAAIP,MAAM,CAACK,MAAM,KAAK,SAAS,EAAE;QACtChB,aAAa,CAACW,MAAM,oBAANA,MAAM,CAAEQ,IAAI,CAAC;MAC7B;IACF,CAAC;IAAA,gBATKb,aAAaA,CAAA;MAAA,OAAAC,KAAA,CAAAa,KAAA,OAAAC,SAAA;IAAA;EAAA,GASlB;EAED,IAAMC,eAAe;IAAA,IAAAC,KAAA,OAAAf,kBAAA,CAAAV,OAAA,EAAG,WAAO0B,QAAuB,EAAI;MACxDf,OAAO,CAACC,GAAG,CAAC,qBAAqB,CAAC;MAClC,IAAMC,MAAM,SAASvB,aAAA,CAAAwB,WAAW,CAACC,WAAW,EAAE,CAACY,0BAA0B,EAAE,CAACV,OAAO,EAAE;MACrF,IAAIJ,MAAM,CAACK,MAAM,KAAK,OAAO,EAAE;QAC7B,IAAA3B,YAAA,CAAA4B,cAAc,EAACN,MAAM,CAACO,KAAK,CAAC;MAC9B;MACA,IAAIM,QAAQ,CAACE,EAAE,KAAK,OAAO,EAAE;QAC3BlC,UAAU,CAACmC,QAAQ,CAAC,oBAAoB,EAAE;UAACH,QAAQ,EAARA;QAAQ,CAAC,CAAC;MACvD,CAAC,MAAM;QACLhC,UAAU,CAACmC,QAAQ,CAAC,mBAAmB,EAAE;UAACH,QAAQ,EAARA;QAAQ,CAAC,CAAC;MACtD;IACF,CAAC;IAAA,gBAXKF,eAAeA,CAAAM,EAAA;MAAA,OAAAL,KAAA,CAAAH,KAAA,OAAAC,SAAA;IAAA;EAAA,GAWpB;EAED,OAAO;IACLtB,UAAU,EAAVA,UAAU;IACVI,KAAK,EAALA,KAAK;IACLmB,eAAe,EAAfA;GACD;AACH,CAAC;AArCYO,OAAA,CAAAtC,WAAW,GAAAA,WAAA",
      ignoreList: []
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "a74ffc573a4d2a5e01e9468cd94693f586a90892"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_1gn21uqobv = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_1gn21uqobv();
var _interopRequireDefault =
/* istanbul ignore next */
(cov_1gn21uqobv().s[0]++, require("@babel/runtime/helpers/interopRequireDefault"));
var _asyncToGenerator2 =
/* istanbul ignore next */
(cov_1gn21uqobv().s[1]++, _interopRequireDefault(require("@babel/runtime/helpers/asyncToGenerator")));
var _slicedToArray2 =
/* istanbul ignore next */
(cov_1gn21uqobv().s[2]++, _interopRequireDefault(require("@babel/runtime/helpers/slicedToArray")));
/* istanbul ignore next */
cov_1gn21uqobv().s[3]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_1gn21uqobv().s[4]++;
exports.useCategory = void 0;
var react_1 =
/* istanbul ignore next */
(cov_1gn21uqobv().s[5]++, require("react"));
var DIContainer_1 =
/* istanbul ignore next */
(cov_1gn21uqobv().s[6]++, require("../../../../di/DIContainer"));
var PopupUtils_1 =
/* istanbul ignore next */
(cov_1gn21uqobv().s[7]++, require("../../../../utils/PopupUtils"));
var native_1 =
/* istanbul ignore next */
(cov_1gn21uqobv().s[8]++, require("@react-navigation/native"));
/* istanbul ignore next */
cov_1gn21uqobv().s[9]++;
var useCategory = function useCategory() {
  /* istanbul ignore next */
  cov_1gn21uqobv().f[0]++;
  var navigation =
  /* istanbul ignore next */
  (cov_1gn21uqobv().s[10]++, (0, native_1.useNavigation)());
  var _ref =
    /* istanbul ignore next */
    (cov_1gn21uqobv().s[11]++, (0, react_1.useState)()),
    _ref2 =
    /* istanbul ignore next */
    (cov_1gn21uqobv().s[12]++, (0, _slicedToArray2.default)(_ref, 2)),
    categories =
    /* istanbul ignore next */
    (cov_1gn21uqobv().s[13]++, _ref2[0]),
    setCategories =
    /* istanbul ignore next */
    (cov_1gn21uqobv().s[14]++, _ref2[1]);
  var _ref3 =
    /* istanbul ignore next */
    (cov_1gn21uqobv().s[15]++, (0, react_1.useState)('INIT')),
    _ref4 =
    /* istanbul ignore next */
    (cov_1gn21uqobv().s[16]++, (0, _slicedToArray2.default)(_ref3, 2)),
    state =
    /* istanbul ignore next */
    (cov_1gn21uqobv().s[17]++, _ref4[0]),
    setState =
    /* istanbul ignore next */
    (cov_1gn21uqobv().s[18]++, _ref4[1]);
  /* istanbul ignore next */
  cov_1gn21uqobv().s[19]++;
  (0, react_1.useEffect)(function () {
    /* istanbul ignore next */
    cov_1gn21uqobv().f[1]++;
    cov_1gn21uqobv().s[20]++;
    getCategories();
  }, []);
  var getCategories =
  /* istanbul ignore next */
  (cov_1gn21uqobv().s[21]++, function () {
    /* istanbul ignore next */
    cov_1gn21uqobv().f[2]++;
    var _ref5 =
    /* istanbul ignore next */
    (cov_1gn21uqobv().s[22]++, (0, _asyncToGenerator2.default)(function* () {
      /* istanbul ignore next */
      cov_1gn21uqobv().f[3]++;
      cov_1gn21uqobv().s[23]++;
      console.log('start call useCategory');
      var result =
      /* istanbul ignore next */
      (cov_1gn21uqobv().s[24]++, yield DIContainer_1.DIContainer.getInstance().getCategoryListUseCase().execute());
      /* istanbul ignore next */
      cov_1gn21uqobv().s[25]++;
      setState(result.status);
      /* istanbul ignore next */
      cov_1gn21uqobv().s[26]++;
      if (result.status === 'ERROR') {
        /* istanbul ignore next */
        cov_1gn21uqobv().b[0][0]++;
        cov_1gn21uqobv().s[27]++;
        (0, PopupUtils_1.showErrorPopup)(result.error);
      } else {
        /* istanbul ignore next */
        cov_1gn21uqobv().b[0][1]++;
        cov_1gn21uqobv().s[28]++;
        if (result.status === 'SUCCESS') {
          /* istanbul ignore next */
          cov_1gn21uqobv().b[1][0]++;
          cov_1gn21uqobv().s[29]++;
          setCategories(result == null ?
          /* istanbul ignore next */
          (cov_1gn21uqobv().b[2][0]++, void 0) :
          /* istanbul ignore next */
          (cov_1gn21uqobv().b[2][1]++, result.data));
        } else
        /* istanbul ignore next */
        {
          cov_1gn21uqobv().b[1][1]++;
        }
      }
    }));
    /* istanbul ignore next */
    cov_1gn21uqobv().s[30]++;
    return function getCategories() {
      /* istanbul ignore next */
      cov_1gn21uqobv().f[4]++;
      cov_1gn21uqobv().s[31]++;
      return _ref5.apply(this, arguments);
    };
  }());
  var gotoPaymentBill =
  /* istanbul ignore next */
  (cov_1gn21uqobv().s[32]++, function () {
    /* istanbul ignore next */
    cov_1gn21uqobv().f[5]++;
    var _ref6 =
    /* istanbul ignore next */
    (cov_1gn21uqobv().s[33]++, (0, _asyncToGenerator2.default)(function* (category) {
      /* istanbul ignore next */
      cov_1gn21uqobv().f[6]++;
      cov_1gn21uqobv().s[34]++;
      console.log('checkCustomerDetail');
      var result =
      /* istanbul ignore next */
      (cov_1gn21uqobv().s[35]++, yield DIContainer_1.DIContainer.getInstance().getValidateCustomerUseCase().execute());
      /* istanbul ignore next */
      cov_1gn21uqobv().s[36]++;
      if (result.status === 'ERROR') {
        /* istanbul ignore next */
        cov_1gn21uqobv().b[3][0]++;
        cov_1gn21uqobv().s[37]++;
        (0, PopupUtils_1.showErrorPopup)(result.error);
      } else
      /* istanbul ignore next */
      {
        cov_1gn21uqobv().b[3][1]++;
      }
      cov_1gn21uqobv().s[38]++;
      if (category.id === 'MB-MR') {
        /* istanbul ignore next */
        cov_1gn21uqobv().b[4][0]++;
        cov_1gn21uqobv().s[39]++;
        navigation.navigate('PaymentPhoneScreen', {
          category: category
        });
      } else {
        /* istanbul ignore next */
        cov_1gn21uqobv().b[4][1]++;
        cov_1gn21uqobv().s[40]++;
        navigation.navigate('PaymentBillScreen', {
          category: category
        });
      }
    }));
    /* istanbul ignore next */
    cov_1gn21uqobv().s[41]++;
    return function gotoPaymentBill(_x) {
      /* istanbul ignore next */
      cov_1gn21uqobv().f[7]++;
      cov_1gn21uqobv().s[42]++;
      return _ref6.apply(this, arguments);
    };
  }());
  /* istanbul ignore next */
  cov_1gn21uqobv().s[43]++;
  return {
    categories: categories,
    state: state,
    gotoPaymentBill: gotoPaymentBill
  };
};
/* istanbul ignore next */
cov_1gn21uqobv().s[44]++;
exports.useCategory = useCategory;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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