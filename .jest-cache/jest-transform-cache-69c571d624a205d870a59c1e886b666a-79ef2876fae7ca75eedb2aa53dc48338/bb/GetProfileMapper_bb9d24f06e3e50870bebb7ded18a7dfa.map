{"version": 3, "names": ["exports", "mapGetProfileResponseToModel", "GetProfileModel_1", "cov_7pr7mzvr3", "s", "require", "response", "f", "GetProfileModel", "serviceGroup"], "sources": ["/Users/<USER>/projects/react-native/payment-module/src/data/mappers/get-profile/GetProfileMapper.ts"], "sourcesContent": ["import {GetProfileResponse} from '../../models/get-profile/GetProfileResponse';\nimport {GetProfileModel} from '../../../domain/entities/get-profile/GetProfileModel';\nimport {BaseResponse} from '../../../core/BaseResponse';\n\nexport function mapGetProfileResponseToModel(response: BaseResponse<GetProfileResponse>): GetProfileModel {\n  return new GetProfileModel(response.serviceGroup);\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIAA,OAAA,CAAAC,4BAAA,GAAAA,4BAAA;AAHA,IAAAC,iBAAA;AAAA;AAAA,CAAAC,aAAA,GAAAC,CAAA,OAAAC,OAAA;AAGA,SAAgBJ,4BAA4BA,CAACK,QAA0C;EAAA;EAAAH,aAAA,GAAAI,CAAA;EAAAJ,aAAA,GAAAC,CAAA;EACrF,OAAO,IAAIF,iBAAA,CAAAM,eAAe,CAACF,QAAQ,CAACG,YAAY,CAAC;AACnD", "ignoreList": []}