016f4aa12da8c9b04406ae30e2d7400e
"use strict";

/* istanbul ignore next */
function cov_7pr7mzvr3() {
  var path = "/Users/<USER>/projects/react-native/payment-module/src/data/mappers/get-profile/GetProfileMapper.ts";
  var hash = "382e2d212d6762e5803ad2a08143f884fa47d531";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/projects/react-native/payment-module/src/data/mappers/get-profile/GetProfileMapper.ts",
    statementMap: {
      "0": {
        start: {
          line: 3,
          column: 0
        },
        end: {
          line: 5,
          column: 3
        }
      },
      "1": {
        start: {
          line: 6,
          column: 0
        },
        end: {
          line: 6,
          column: 68
        }
      },
      "2": {
        start: {
          line: 7,
          column: 24
        },
        end: {
          line: 7,
          column: 87
        }
      },
      "3": {
        start: {
          line: 9,
          column: 2
        },
        end: {
          line: 9,
          column: 70
        }
      }
    },
    fnMap: {
      "0": {
        name: "mapGetProfileResponseToModel",
        decl: {
          start: {
            line: 8,
            column: 9
          },
          end: {
            line: 8,
            column: 37
          }
        },
        loc: {
          start: {
            line: 8,
            column: 48
          },
          end: {
            line: 10,
            column: 1
          }
        },
        line: 8
      }
    },
    branchMap: {},
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0
    },
    f: {
      "0": 0
    },
    b: {},
    inputSourceMap: {
      version: 3,
      file: undefined,
      names: ["exports", "mapGetProfileResponseToModel", "GetProfileModel_1", "require", "response", "GetProfileModel", "serviceGroup"],
      sourceRoot: undefined,
      sources: ["/Users/<USER>/projects/react-native/payment-module/src/data/mappers/get-profile/GetProfileMapper.ts"],
      sourcesContent: ["import {GetProfileResponse} from '../../models/get-profile/GetProfileResponse';\nimport {GetProfileModel} from '../../../domain/entities/get-profile/GetProfileModel';\nimport {BaseResponse} from '../../../core/BaseResponse';\n\nexport function mapGetProfileResponseToModel(response: BaseResponse<GetProfileResponse>): GetProfileModel {\n  return new GetProfileModel(response.serviceGroup);\n}\n"],
      mappings: ";;;;;AAIAA,OAAA,CAAAC,4BAAA,GAAAA,4BAAA;AAHA,IAAAC,iBAAA,GAAAC,OAAA;AAGA,SAAgBF,4BAA4BA,CAACG,QAA0C;EACrF,OAAO,IAAIF,iBAAA,CAAAG,eAAe,CAACD,QAAQ,CAACE,YAAY,CAAC;AACnD",
      ignoreList: []
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "382e2d212d6762e5803ad2a08143f884fa47d531"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_7pr7mzvr3 = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_7pr7mzvr3();
cov_7pr7mzvr3().s[0]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_7pr7mzvr3().s[1]++;
exports.mapGetProfileResponseToModel = mapGetProfileResponseToModel;
var GetProfileModel_1 =
/* istanbul ignore next */
(cov_7pr7mzvr3().s[2]++, require("../../../domain/entities/get-profile/GetProfileModel"));
function mapGetProfileResponseToModel(response) {
  /* istanbul ignore next */
  cov_7pr7mzvr3().f[0]++;
  cov_7pr7mzvr3().s[3]++;
  return new GetProfileModel_1.GetProfileModel(response.serviceGroup);
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJleHBvcnRzIiwibWFwR2V0UHJvZmlsZVJlc3BvbnNlVG9Nb2RlbCIsIkdldFByb2ZpbGVNb2RlbF8xIiwiY292XzdwcjdtenZyMyIsInMiLCJyZXF1aXJlIiwicmVzcG9uc2UiLCJmIiwiR2V0UHJvZmlsZU1vZGVsIiwic2VydmljZUdyb3VwIl0sInNvdXJjZXMiOlsiL1VzZXJzL3NvbnNhbXNldC9wcm9qZWN0cy9yZWFjdC1uYXRpdmUvcGF5bWVudC1tb2R1bGUvc3JjL2RhdGEvbWFwcGVycy9nZXQtcHJvZmlsZS9HZXRQcm9maWxlTWFwcGVyLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7R2V0UHJvZmlsZVJlc3BvbnNlfSBmcm9tICcuLi8uLi9tb2RlbHMvZ2V0LXByb2ZpbGUvR2V0UHJvZmlsZVJlc3BvbnNlJztcbmltcG9ydCB7R2V0UHJvZmlsZU1vZGVsfSBmcm9tICcuLi8uLi8uLi9kb21haW4vZW50aXRpZXMvZ2V0LXByb2ZpbGUvR2V0UHJvZmlsZU1vZGVsJztcbmltcG9ydCB7QmFzZVJlc3BvbnNlfSBmcm9tICcuLi8uLi8uLi9jb3JlL0Jhc2VSZXNwb25zZSc7XG5cbmV4cG9ydCBmdW5jdGlvbiBtYXBHZXRQcm9maWxlUmVzcG9uc2VUb01vZGVsKHJlc3BvbnNlOiBCYXNlUmVzcG9uc2U8R2V0UHJvZmlsZVJlc3BvbnNlPik6IEdldFByb2ZpbGVNb2RlbCB7XG4gIHJldHVybiBuZXcgR2V0UHJvZmlsZU1vZGVsKHJlc3BvbnNlLnNlcnZpY2VHcm91cCk7XG59XG4iXSwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBSUFBLE9BQUEsQ0FBQUMsNEJBQUEsR0FBQUEsNEJBQUE7QUFIQSxJQUFBQyxpQkFBQTtBQUFBO0FBQUEsQ0FBQUMsYUFBQSxHQUFBQyxDQUFBLE9BQUFDLE9BQUE7QUFHQSxTQUFnQkosNEJBQTRCQSxDQUFDSyxRQUEwQztFQUFBO0VBQUFILGFBQUEsR0FBQUksQ0FBQTtFQUFBSixhQUFBLEdBQUFDLENBQUE7RUFDckYsT0FBTyxJQUFJRixpQkFBQSxDQUFBTSxlQUFlLENBQUNGLFFBQVEsQ0FBQ0csWUFBWSxDQUFDO0FBQ25EIiwiaWdub3JlTGlzdCI6W119