aa4323f4d58c10ae4d9f23fcdbd2239d
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = exports.__INTERNAL_VIEW_CONFIG = exports.Commands = void 0;
var NativeComponentRegistry = _interopRequireWildcard(require("../../NativeComponent/NativeComponentRegistry"));
var _codegenNativeCommands = _interopRequireDefault(require("../../Utilities/codegenNativeCommands"));
function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
var Commands = exports.Commands = (0, _codegenNativeCommands.default)({
  supportedCommands: ['focus', 'blur', 'setTextAndSelection']
});
var __INTERNAL_VIEW_CONFIG = exports.__INTERNAL_VIEW_CONFIG = {
  uiViewClassName: 'AndroidTextInput',
  bubblingEventTypes: {
    topBlur: {
      phasedRegistrationNames: {
        bubbled: 'onBlur',
        captured: 'onBlurCapture'
      }
    },
    topEndEditing: {
      phasedRegistrationNames: {
        bubbled: 'onEndEditing',
        captured: 'onEndEditingCapture'
      }
    },
    topFocus: {
      phasedRegistrationNames: {
        bubbled: 'onFocus',
        captured: 'onFocusCapture'
      }
    },
    topKeyPress: {
      phasedRegistrationNames: {
        bubbled: 'onKeyPress',
        captured: 'onKeyPressCapture'
      }
    },
    topSubmitEditing: {
      phasedRegistrationNames: {
        bubbled: 'onSubmitEditing',
        captured: 'onSubmitEditingCapture'
      }
    }
  },
  directEventTypes: {
    topScroll: {
      registrationName: 'onScroll'
    }
  },
  validAttributes: {
    maxFontSizeMultiplier: true,
    adjustsFontSizeToFit: true,
    minimumFontScale: true,
    autoFocus: true,
    placeholder: true,
    inlineImagePadding: true,
    contextMenuHidden: true,
    textShadowColor: {
      process: require('../../StyleSheet/processColor').default
    },
    maxLength: true,
    selectTextOnFocus: true,
    textShadowRadius: true,
    underlineColorAndroid: {
      process: require('../../StyleSheet/processColor').default
    },
    textDecorationLine: true,
    submitBehavior: true,
    textAlignVertical: true,
    fontStyle: true,
    textShadowOffset: true,
    selectionColor: {
      process: require('../../StyleSheet/processColor').default
    },
    selectionHandleColor: {
      process: require('../../StyleSheet/processColor').default
    },
    placeholderTextColor: {
      process: require('../../StyleSheet/processColor').default
    },
    importantForAutofill: true,
    lineHeight: true,
    textTransform: true,
    returnKeyType: true,
    keyboardType: true,
    multiline: true,
    color: {
      process: require('../../StyleSheet/processColor').default
    },
    autoComplete: true,
    numberOfLines: true,
    letterSpacing: true,
    returnKeyLabel: true,
    fontSize: true,
    onKeyPress: true,
    cursorColor: {
      process: require('../../StyleSheet/processColor').default
    },
    text: true,
    showSoftInputOnFocus: true,
    textAlign: true,
    autoCapitalize: true,
    autoCorrect: true,
    caretHidden: true,
    secureTextEntry: true,
    textBreakStrategy: true,
    onScroll: true,
    onContentSizeChange: true,
    disableFullscreenUI: true,
    includeFontPadding: true,
    fontWeight: true,
    fontFamily: true,
    allowFontScaling: true,
    onSelectionChange: true,
    mostRecentEventCount: true,
    inlineImageLeft: true,
    editable: true,
    fontVariant: true,
    borderBottomRightRadius: true,
    borderBottomColor: {
      process: require('../../StyleSheet/processColor').default
    },
    borderRadius: true,
    borderRightColor: {
      process: require('../../StyleSheet/processColor').default
    },
    borderColor: {
      process: require('../../StyleSheet/processColor').default
    },
    borderTopRightRadius: true,
    borderStyle: true,
    borderBottomLeftRadius: true,
    borderLeftColor: {
      process: require('../../StyleSheet/processColor').default
    },
    borderTopLeftRadius: true,
    borderTopColor: {
      process: require('../../StyleSheet/processColor').default
    }
  }
};
var AndroidTextInputNativeComponent = NativeComponentRegistry.get('AndroidTextInput', function () {
  return __INTERNAL_VIEW_CONFIG;
});
var _default = exports.default = AndroidTextInputNativeComponent;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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