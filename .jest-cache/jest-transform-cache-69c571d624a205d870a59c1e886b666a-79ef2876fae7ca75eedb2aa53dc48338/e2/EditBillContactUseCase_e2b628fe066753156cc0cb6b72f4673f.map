{"version": 3, "names": ["cov_2ajle7s26n", "actualCoverage", "ExcecutionHandler_1", "s", "require", "EditBillContactUseCase", "f", "repository", "_classCallCheck2", "default", "_createClass2", "key", "value", "_execute", "_asyncToGenerator2", "request", "_this", "ExecutionHandler", "execute", "editBillContact", "_x", "apply", "arguments", "exports"], "sources": ["/Users/<USER>/projects/react-native/payment-module/src/domain/usecases/bill-contact/EditBillContactUseCase.ts"], "sourcesContent": ["import {IBillContactRepository} from '../../repositories/IBillContactRepository';\nimport {EditBillContactModel} from '../../entities/edit-bill-contact/EditBillContactModel';\nimport {ResultState} from '../../../core/ResultState';\nimport {ExecutionHandler} from '../../../utils/ExcecutionHandler';\nimport {EditBillContactRequest} from '../../../data/models/edit-bill-contact/EditBillContactRequest';\nexport class EditBillContactUseCase {\n  private repository: IBillContactRepository;\n\n  constructor(repository: IBillContactRepository) {\n    this.repository = repository;\n  }\n\n  public async execute(request: EditBillContactRequest): Promise<ResultState<EditBillContactModel>> {\n    // call this.repository.editBillContact(...)\n    return ExecutionHandler.execute(() => this.repository.editBillContact(request));\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAUE;IAAAA,cAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,cAAA;;;;;;;;;;;;;;;;;;;;;AAPF,IAAAE,mBAAA;AAAA;AAAA,CAAAF,cAAA,GAAAG,CAAA,OAAAC,OAAA;AAAkE,IAErDC,sBAAsB;AAAA;AAAA,CAAAL,cAAA,GAAAG,CAAA;EAAA;EAAAH,cAAA,GAAAM,CAAA;EAGjC,SAAAD,uBAAYE,UAAkC;IAAA;IAAAP,cAAA,GAAAM,CAAA;IAAAN,cAAA,GAAAG,CAAA;IAAA,IAAAK,gBAAA,CAAAC,OAAA,QAAAJ,sBAAA;IAAA;IAAAL,cAAA,GAAAG,CAAA;IAC5C,IAAI,CAACI,UAAU,GAAGA,UAAU;EAC9B;EAAA;EAAAP,cAAA,GAAAG,CAAA;EAAC,WAAAO,aAAA,CAAAD,OAAA,EAAAJ,sBAAA;IAAAM,GAAA;IAAAC,KAAA;MAAA;MAAAZ,cAAA,GAAAM,CAAA;MAAA,IAAAO,QAAA;MAAA;MAAA,CAAAb,cAAA,GAAAG,CAAA,YAAAW,kBAAA,CAAAL,OAAA,EAEM,WAAcM,OAA+B;QAAA;QAAAf,cAAA,GAAAM,CAAA;QAAA,IAAAU,KAAA;QAAA;QAAA,CAAAhB,cAAA,GAAAG,CAAA;QAAA;QAAAH,cAAA,GAAAG,CAAA;QAElD,OAAOD,mBAAA,CAAAe,gBAAgB,CAACC,OAAO,CAAC;UAAA;UAAAlB,cAAA,GAAAM,CAAA;UAAAN,cAAA,GAAAG,CAAA;UAAA,OAAMa,KAAI,CAACT,UAAU,CAACY,eAAe,CAACJ,OAAO,CAAC;QAAA,EAAC;MACjF,CAAC;MAAA,SAHYG,OAAOA,CAAAE,EAAA;QAAA;QAAApB,cAAA,GAAAM,CAAA;QAAAN,cAAA,GAAAG,CAAA;QAAA,OAAAU,QAAA,CAAAQ,KAAA,OAAAC,SAAA;MAAA;MAAA;MAAAtB,cAAA,GAAAG,CAAA;MAAA,OAAPe,OAAO;IAAA;EAAA;AAAA;AAAA;AAAAlB,cAAA,GAAAG,CAAA;AAPtBoB,OAAA,CAAAlB,sBAAA,GAAAA,sBAAA", "ignoreList": []}