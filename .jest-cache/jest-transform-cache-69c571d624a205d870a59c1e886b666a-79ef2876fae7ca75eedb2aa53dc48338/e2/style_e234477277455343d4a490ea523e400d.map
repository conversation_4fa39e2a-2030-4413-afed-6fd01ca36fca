{"version": 3, "names": ["cov_3bfk213p8", "actualCoverage", "s", "msb_shared_component_1", "require", "exports", "makeStyle", "createMSBStyleSheet", "_ref", "f", "SizeGlobal", "ColorAlias", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "button", "width", "margin", "paddingVertical", "SpacingXSmall", "justifyContent", "alignItems", "gap", "SizeButton", "SmallSpacingVertical", "containerFeatureButton", "flexDirection", "flexWrap", "containerGroupApp", "height", "getSize", "borderTopWidth", "Size25", "borderColor", "BorderDefault", "gridIconSize", "IconLarge", "functionIconSize", "IconMedium"], "sources": ["/Users/<USER>/projects/react-native/payment-module/src/presentation/payment-home/components/category-selection/style.ts"], "sourcesContent": ["import {createMSBStyleSheet, getSize, SizeButton} from 'msb-shared-component';\n\nexport const makeStyle = createMSBStyleSheet(({SizeGlobal, ColorAlias, SizeAlias}) => {\n  return {\n    button: {\n      // backgroundColor: ColorGlobal.Red500,\n      width: '30%',\n      margin: '1.5%',\n      // height: 68, // TODO: Designer\n      paddingVertical: SizeAlias.SpacingXSmall,\n      justifyContent: 'center',\n      alignItems: 'center',\n      gap: SizeButton.SmallSpacingVertical,\n    },\n    containerFeatureButton: {\n      flexDirection: 'row',\n      flexWrap: 'wrap', // Tự động xuống dòng\n      justifyContent: 'flex-start', // Căn trái\n    },\n    containerGroupApp: {\n      // backgroundColor: ColorGlobal.Red500,\n      height: getSize(52), // TODO: Designer\n      borderTopWidth: SizeGlobal.Size25,\n      borderColor: ColorAlias.BorderDefault,\n      flexDirection: 'row',\n    },\n    gridIconSize: {\n      height: SizeAlias.IconLarge,\n      width: SizeAlias.IconLarge,\n    },\n\n    functionIconSize: {\n      height: SizeAlias.IconMedium,\n      width: SizeAlias.IconMedium,\n    },\n  };\n});\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IASM;IAAAA,aAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,aAAA;AAAAA,aAAA,GAAAE,CAAA;;;;;;;AATN,IAAAC,sBAAA;AAAA;AAAA,CAAAH,aAAA,GAAAE,CAAA,OAAAE,OAAA;AAAA;AAAAJ,aAAA,GAAAE,CAAA;AAEaG,OAAA,CAAAC,SAAS,GAAG,IAAAH,sBAAA,CAAAI,mBAAmB,EAAC,UAAAC,IAAA,EAAwC;EAAA;EAAAR,aAAA,GAAAS,CAAA;EAAA,IAAtCC,UAAU;IAAA;IAAA,CAAAV,aAAA,GAAAE,CAAA,OAAAM,IAAA,CAAVE,UAAU;IAAEC,UAAU;IAAA;IAAA,CAAAX,aAAA,GAAAE,CAAA,OAAAM,IAAA,CAAVG,UAAU;IAAEC,SAAS;IAAA;IAAA,CAAAZ,aAAA,GAAAE,CAAA,OAAAM,IAAA,CAATI,SAAS;EAAA;EAAAZ,aAAA,GAAAE,CAAA;EAC9E,OAAO;IACLW,MAAM,EAAE;MAENC,KAAK,EAAE,KAAK;MACZC,MAAM,EAAE,MAAM;MAEdC,eAAe,EAAEJ,SAAS,CAACK,aAAa;MACxCC,cAAc,EAAE,QAAQ;MACxBC,UAAU,EAAE,QAAQ;MACpBC,GAAG,EAAEjB,sBAAA,CAAAkB,UAAU,CAACC;KACjB;IACDC,sBAAsB,EAAE;MACtBC,aAAa,EAAE,KAAK;MACpBC,QAAQ,EAAE,MAAM;MAChBP,cAAc,EAAE;KACjB;IACDQ,iBAAiB,EAAE;MAEjBC,MAAM,EAAE,IAAAxB,sBAAA,CAAAyB,OAAO,EAAC,EAAE,CAAC;MACnBC,cAAc,EAAEnB,UAAU,CAACoB,MAAM;MACjCC,WAAW,EAAEpB,UAAU,CAACqB,aAAa;MACrCR,aAAa,EAAE;KAChB;IACDS,YAAY,EAAE;MACZN,MAAM,EAAEf,SAAS,CAACsB,SAAS;MAC3BpB,KAAK,EAAEF,SAAS,CAACsB;KAClB;IAEDC,gBAAgB,EAAE;MAChBR,MAAM,EAAEf,SAAS,CAACwB,UAAU;MAC5BtB,KAAK,EAAEF,SAAS,CAACwB;;GAEpB;AACH,CAAC,CAAC", "ignoreList": []}