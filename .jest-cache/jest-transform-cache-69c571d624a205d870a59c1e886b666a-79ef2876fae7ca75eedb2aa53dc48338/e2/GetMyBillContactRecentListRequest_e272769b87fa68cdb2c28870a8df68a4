d0ef3f5b115b96365562ba4a7ac2bdad
"use strict";

/* istanbul ignore next */
function cov_1wzhyiq6sd() {
  var path = "/Users/<USER>/projects/react-native/payment-module/src/data/models/get-my-bill-contact-recent-list/GetMyBillContactRecentListRequest.ts";
  var hash = "5a5c01dcd741df633362ba60013b990e4eea71bf";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/projects/react-native/payment-module/src/data/models/get-my-bill-contact-recent-list/GetMyBillContactRecentListRequest.ts",
    statementMap: {
      "0": {
        start: {
          line: 3,
          column: 0
        },
        end: {
          line: 5,
          column: 3
        }
      }
    },
    fnMap: {},
    branchMap: {},
    s: {
      "0": 0
    },
    f: {},
    b: {},
    inputSourceMap: {
      version: 3,
      file: undefined,
      names: [],
      sourceRoot: undefined,
      sources: ["/Users/<USER>/projects/react-native/payment-module/src/data/models/get-my-bill-contact-recent-list/GetMyBillContactRecentListRequest.ts"],
      sourcesContent: ["export interface GetMyBillContactRecentListRequest {\n  // TODO: define fields\n}\n"],
      mappings: "",
      ignoreList: []
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "5a5c01dcd741df633362ba60013b990e4eea71bf"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_1wzhyiq6sd = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_1wzhyiq6sd();
cov_1wzhyiq6sd().s[0]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6W10sInNvdXJjZXMiOlsiL1VzZXJzL3NvbnNhbXNldC9wcm9qZWN0cy9yZWFjdC1uYXRpdmUvcGF5bWVudC1tb2R1bGUvc3JjL2RhdGEvbW9kZWxzL2dldC1teS1iaWxsLWNvbnRhY3QtcmVjZW50LWxpc3QvR2V0TXlCaWxsQ29udGFjdFJlY2VudExpc3RSZXF1ZXN0LnRzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBpbnRlcmZhY2UgR2V0TXlCaWxsQ29udGFjdFJlY2VudExpc3RSZXF1ZXN0IHtcbiAgLy8gVE9ETzogZGVmaW5lIGZpZWxkc1xufVxuIl0sIm1hcHBpbmdzIjoiIiwiaWdub3JlTGlzdCI6W119