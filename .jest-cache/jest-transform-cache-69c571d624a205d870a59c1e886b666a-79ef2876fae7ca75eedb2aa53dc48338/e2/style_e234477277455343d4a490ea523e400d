0a538f967851ee847fdc5abc0094de03
"use strict";

/* istanbul ignore next */
function cov_3bfk213p8() {
  var path = "/Users/<USER>/projects/react-native/payment-module/src/presentation/payment-home/components/category-selection/style.ts";
  var hash = "e9b203bb29bfa2d98e8838fce3ebcf251ec6b363";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/projects/react-native/payment-module/src/presentation/payment-home/components/category-selection/style.ts",
    statementMap: {
      "0": {
        start: {
          line: 3,
          column: 0
        },
        end: {
          line: 5,
          column: 3
        }
      },
      "1": {
        start: {
          line: 6,
          column: 0
        },
        end: {
          line: 6,
          column: 27
        }
      },
      "2": {
        start: {
          line: 7,
          column: 29
        },
        end: {
          line: 7,
          column: 60
        }
      },
      "3": {
        start: {
          line: 8,
          column: 0
        },
        end: {
          line: 41,
          column: 3
        }
      },
      "4": {
        start: {
          line: 9,
          column: 19
        },
        end: {
          line: 9,
          column: 34
        }
      },
      "5": {
        start: {
          line: 10,
          column: 17
        },
        end: {
          line: 10,
          column: 32
        }
      },
      "6": {
        start: {
          line: 11,
          column: 16
        },
        end: {
          line: 11,
          column: 30
        }
      },
      "7": {
        start: {
          line: 12,
          column: 2
        },
        end: {
          line: 40,
          column: 4
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 8,
            column: 68
          },
          end: {
            line: 8,
            column: 69
          }
        },
        loc: {
          start: {
            line: 8,
            column: 84
          },
          end: {
            line: 41,
            column: 1
          }
        },
        line: 8
      }
    },
    branchMap: {},
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0
    },
    f: {
      "0": 0
    },
    b: {},
    inputSourceMap: {
      version: 3,
      file: undefined,
      names: ["msb_shared_component_1", "require", "exports", "makeStyle", "createMSBStyleSheet", "_ref", "SizeGlobal", "ColorAlias", "SizeAlias", "button", "width", "margin", "paddingVertical", "SpacingXSmall", "justifyContent", "alignItems", "gap", "SizeButton", "SmallSpacingVertical", "containerFeatureButton", "flexDirection", "flexWrap", "containerGroupApp", "height", "getSize", "borderTopWidth", "Size25", "borderColor", "BorderDefault", "gridIconSize", "IconLarge", "functionIconSize", "IconMedium"],
      sourceRoot: undefined,
      sources: ["/Users/<USER>/projects/react-native/payment-module/src/presentation/payment-home/components/category-selection/style.ts"],
      sourcesContent: ["import {createMSBStyleSheet, getSize, SizeButton} from 'msb-shared-component';\n\nexport const makeStyle = createMSBStyleSheet(({SizeGlobal, ColorAlias, SizeAlias}) => {\n  return {\n    button: {\n      // backgroundColor: ColorGlobal.Red500,\n      width: '30%',\n      margin: '1.5%',\n      // height: 68, // TODO: Designer\n      paddingVertical: SizeAlias.SpacingXSmall,\n      justifyContent: 'center',\n      alignItems: 'center',\n      gap: SizeButton.SmallSpacingVertical,\n    },\n    containerFeatureButton: {\n      flexDirection: 'row',\n      flexWrap: 'wrap', // T\u1EF1 \u0111\u1ED9ng xu\u1ED1ng d\xF2ng\n      justifyContent: 'flex-start', // C\u0103n tr\xE1i\n    },\n    containerGroupApp: {\n      // backgroundColor: ColorGlobal.Red500,\n      height: getSize(52), // TODO: Designer\n      borderTopWidth: SizeGlobal.Size25,\n      borderColor: ColorAlias.BorderDefault,\n      flexDirection: 'row',\n    },\n    gridIconSize: {\n      height: SizeAlias.IconLarge,\n      width: SizeAlias.IconLarge,\n    },\n\n    functionIconSize: {\n      height: SizeAlias.IconMedium,\n      width: SizeAlias.IconMedium,\n    },\n  };\n});\n"],
      mappings: ";;;;;;AAAA,IAAAA,sBAAA,GAAAC,OAAA;AAEaC,OAAA,CAAAC,SAAS,GAAG,IAAAH,sBAAA,CAAAI,mBAAmB,EAAC,UAAAC,IAAA,EAAwC;EAAA,IAAtCC,UAAU,GAAAD,IAAA,CAAVC,UAAU;IAAEC,UAAU,GAAAF,IAAA,CAAVE,UAAU;IAAEC,SAAS,GAAAH,IAAA,CAATG,SAAS;EAC9E,OAAO;IACLC,MAAM,EAAE;MAENC,KAAK,EAAE,KAAK;MACZC,MAAM,EAAE,MAAM;MAEdC,eAAe,EAAEJ,SAAS,CAACK,aAAa;MACxCC,cAAc,EAAE,QAAQ;MACxBC,UAAU,EAAE,QAAQ;MACpBC,GAAG,EAAEhB,sBAAA,CAAAiB,UAAU,CAACC;KACjB;IACDC,sBAAsB,EAAE;MACtBC,aAAa,EAAE,KAAK;MACpBC,QAAQ,EAAE,MAAM;MAChBP,cAAc,EAAE;KACjB;IACDQ,iBAAiB,EAAE;MAEjBC,MAAM,EAAE,IAAAvB,sBAAA,CAAAwB,OAAO,EAAC,EAAE,CAAC;MACnBC,cAAc,EAAEnB,UAAU,CAACoB,MAAM;MACjCC,WAAW,EAAEpB,UAAU,CAACqB,aAAa;MACrCR,aAAa,EAAE;KAChB;IACDS,YAAY,EAAE;MACZN,MAAM,EAAEf,SAAS,CAACsB,SAAS;MAC3BpB,KAAK,EAAEF,SAAS,CAACsB;KAClB;IAEDC,gBAAgB,EAAE;MAChBR,MAAM,EAAEf,SAAS,CAACwB,UAAU;MAC5BtB,KAAK,EAAEF,SAAS,CAACwB;;GAEpB;AACH,CAAC,CAAC",
      ignoreList: []
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "e9b203bb29bfa2d98e8838fce3ebcf251ec6b363"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_3bfk213p8 = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_3bfk213p8();
cov_3bfk213p8().s[0]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_3bfk213p8().s[1]++;
exports.makeStyle = void 0;
var msb_shared_component_1 =
/* istanbul ignore next */
(cov_3bfk213p8().s[2]++, require("msb-shared-component"));
/* istanbul ignore next */
cov_3bfk213p8().s[3]++;
exports.makeStyle = (0, msb_shared_component_1.createMSBStyleSheet)(function (_ref) {
  /* istanbul ignore next */
  cov_3bfk213p8().f[0]++;
  var SizeGlobal =
    /* istanbul ignore next */
    (cov_3bfk213p8().s[4]++, _ref.SizeGlobal),
    ColorAlias =
    /* istanbul ignore next */
    (cov_3bfk213p8().s[5]++, _ref.ColorAlias),
    SizeAlias =
    /* istanbul ignore next */
    (cov_3bfk213p8().s[6]++, _ref.SizeAlias);
  /* istanbul ignore next */
  cov_3bfk213p8().s[7]++;
  return {
    button: {
      width: '30%',
      margin: '1.5%',
      paddingVertical: SizeAlias.SpacingXSmall,
      justifyContent: 'center',
      alignItems: 'center',
      gap: msb_shared_component_1.SizeButton.SmallSpacingVertical
    },
    containerFeatureButton: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      justifyContent: 'flex-start'
    },
    containerGroupApp: {
      height: (0, msb_shared_component_1.getSize)(52),
      borderTopWidth: SizeGlobal.Size25,
      borderColor: ColorAlias.BorderDefault,
      flexDirection: 'row'
    },
    gridIconSize: {
      height: SizeAlias.IconLarge,
      width: SizeAlias.IconLarge
    },
    functionIconSize: {
      height: SizeAlias.IconMedium,
      width: SizeAlias.IconMedium
    }
  };
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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