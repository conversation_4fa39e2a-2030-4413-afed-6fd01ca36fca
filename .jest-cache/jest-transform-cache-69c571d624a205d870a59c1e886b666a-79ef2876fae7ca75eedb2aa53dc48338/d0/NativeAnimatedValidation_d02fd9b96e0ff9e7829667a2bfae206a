bfb4bfa7ffb6951e6c7efee2127ecfe2
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.validateInterpolation = validateInterpolation;
exports.validateStyles = validateStyles;
exports.validateTransform = validateTransform;
var _NativeAnimatedAllowlist = require("../../../Libraries/Animated/NativeAnimatedAllowlist");
function validateInterpolation(config) {
  for (var _key in config) {
    if (_key !== 'debugID' && !(0, _NativeAnimatedAllowlist.isSupportedInterpolationParam)(_key)) {
      console.error(`Interpolation property '${_key}' is not supported by native animated module`);
    }
  }
}
function validateStyles(styles) {
  for (var _key2 in styles) {
    if (!(0, _NativeAnimatedAllowlist.isSupportedStyleProp)(_key2)) {
      console.error(`Style property '${_key2}' is not supported by native animated module`);
    }
  }
}
function validateTransform(configs) {
  configs.forEach(function (config) {
    if (!(0, _NativeAnimatedAllowlist.isSupportedTransformProp)(config.property)) {
      console.error(`Property '${config.property}' is not supported by native animated module`);
    }
  });
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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