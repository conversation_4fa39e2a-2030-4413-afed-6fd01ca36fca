{"version": 3, "names": ["_NativeAnimatedAllowlist", "require", "validateInterpolation", "config", "key", "isSupportedInterpolationParam", "console", "error", "validateStyles", "styles", "isSupportedStyleProp", "validateTransform", "configs", "for<PERSON>ach", "isSupportedTransformProp", "property"], "sources": ["NativeAnimatedValidation.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @flow strict-local\n * @format\n */\n\nimport type {InterpolationConfigType} from '../../../Libraries/Animated/nodes/AnimatedInterpolation';\n\nimport {\n  isSupportedInterpolationParam,\n  isSupportedStyleProp,\n  isSupportedTransformProp,\n} from '../../../Libraries/Animated/NativeAnimatedAllowlist';\n\nexport function validateInterpolation<OutputT: number | string>(\n  config: InterpolationConfigType<OutputT>,\n): void {\n  for (const key in config) {\n    if (key !== 'debugID' && !isSupportedInterpolationParam(key)) {\n      console.error(\n        `Interpolation property '${key}' is not supported by native animated module`,\n      );\n    }\n  }\n}\n\nexport function validateStyles(styles: {[key: string]: ?number, ...}): void {\n  for (const key in styles) {\n    if (!isSupportedStyleProp(key)) {\n      console.error(\n        `Style property '${key}' is not supported by native animated module`,\n      );\n    }\n  }\n}\n\nexport function validateTransform(\n  configs: Array<\n    | {\n        type: 'animated',\n        property: string,\n        nodeTag: ?number,\n        ...\n      }\n    | {\n        type: 'static',\n        property: string,\n        value: number | string,\n        ...\n      },\n  >,\n): void {\n  configs.forEach(config => {\n    if (!isSupportedTransformProp(config.property)) {\n      console.error(\n        `Property '${config.property}' is not supported by native animated module`,\n      );\n    }\n  });\n}\n"], "mappings": ";;;;;;AAYA,IAAAA,wBAAA,GAAAC,OAAA;AAMO,SAASC,qBAAqBA,CACnCC,MAAwC,EAClC;EACN,KAAK,IAAMC,IAAG,IAAID,MAAM,EAAE;IACxB,IAAIC,IAAG,KAAK,SAAS,IAAI,CAAC,IAAAC,sDAA6B,EAACD,IAAG,CAAC,EAAE;MAC5DE,OAAO,CAACC,KAAK,CACX,2BAA2BH,IAAG,8CAChC,CAAC;IACH;EACF;AACF;AAEO,SAASI,cAAcA,CAACC,MAAqC,EAAQ;EAC1E,KAAK,IAAML,KAAG,IAAIK,MAAM,EAAE;IACxB,IAAI,CAAC,IAAAC,6CAAoB,EAACN,KAAG,CAAC,EAAE;MAC9BE,OAAO,CAACC,KAAK,CACX,mBAAmBH,KAAG,8CACxB,CAAC;IACH;EACF;AACF;AAEO,SAASO,iBAAiBA,CAC/BC,OAaC,EACK;EACNA,OAAO,CAACC,OAAO,CAAC,UAAAV,MAAM,EAAI;IACxB,IAAI,CAAC,IAAAW,iDAAwB,EAACX,MAAM,CAACY,QAAQ,CAAC,EAAE;MAC9CT,OAAO,CAACC,KAAK,CACX,aAAaJ,MAAM,CAACY,QAAQ,8CAC9B,CAAC;IACH;EACF,CAAC,CAAC;AACJ", "ignoreList": []}