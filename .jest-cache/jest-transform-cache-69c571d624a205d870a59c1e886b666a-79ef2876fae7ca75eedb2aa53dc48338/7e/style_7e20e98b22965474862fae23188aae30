b4c9a077507ff9b0d2a6ef6ddf9c9f5c
"use strict";

/* istanbul ignore next */
function cov_o0795co3v() {
  var path = "/Users/<USER>/projects/react-native/payment-module/src/presentation/savecontact/style.ts";
  var hash = "5d18cdd3e86bcd76334ffe6e5d6e02605121c130";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/projects/react-native/payment-module/src/presentation/savecontact/style.ts",
    statementMap: {
      "0": {
        start: {
          line: 3,
          column: 0
        },
        end: {
          line: 5,
          column: 3
        }
      },
      "1": {
        start: {
          line: 6,
          column: 0
        },
        end: {
          line: 6,
          column: 27
        }
      },
      "2": {
        start: {
          line: 7,
          column: 29
        },
        end: {
          line: 7,
          column: 60
        }
      },
      "3": {
        start: {
          line: 8,
          column: 0
        },
        end: {
          line: 57,
          column: 3
        }
      },
      "4": {
        start: {
          line: 9,
          column: 18
        },
        end: {
          line: 9,
          column: 32
        }
      },
      "5": {
        start: {
          line: 10,
          column: 13
        },
        end: {
          line: 10,
          column: 24
        }
      },
      "6": {
        start: {
          line: 11,
          column: 17
        },
        end: {
          line: 11,
          column: 32
        }
      },
      "7": {
        start: {
          line: 12,
          column: 16
        },
        end: {
          line: 12,
          column: 30
        }
      },
      "8": {
        start: {
          line: 13,
          column: 17
        },
        end: {
          line: 13,
          column: 32
        }
      },
      "9": {
        start: {
          line: 14,
          column: 2
        },
        end: {
          line: 56,
          column: 4
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 8,
            column: 68
          },
          end: {
            line: 8,
            column: 69
          }
        },
        loc: {
          start: {
            line: 8,
            column: 84
          },
          end: {
            line: 57,
            column: 1
          }
        },
        line: 8
      }
    },
    branchMap: {},
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0
    },
    f: {
      "0": 0
    },
    b: {},
    inputSourceMap: {
      version: 3,
      file: undefined,
      names: ["msb_shared_component_1", "require", "exports", "makeStyle", "createMSBStyleSheet", "_ref", "SizeAlias", "Shadow", "ColorField", "SizeField", "SizeGlobal", "body", "flex", "paddingHorizontal", "SpacingSmall", "bottomSpace", "marginBottom", "SpacingLarge", "card", "Object", "assign", "backgroundColor", "borderRadius", "Radius3", "flexDirection", "justifyContent", "paddingVertical", "padding", "center", "container", "favorite", "borderColor", "BorderDefault", "BorderRadius", "borderWidth", "BorderStroke", "minHeight", "getSize", "Size400", "leftFavorite", "flexWrap", "width", "logo", "height", "Size800", "marginRight", "resizeMode", "space", "SpacingXSmall"],
      sourceRoot: undefined,
      sources: ["/Users/<USER>/projects/react-native/payment-module/src/presentation/savecontact/style.ts"],
      sourcesContent: ["import {createMSBStyleSheet, getSize} from 'msb-shared-component';\n\nexport const makeStyle = createMSBStyleSheet(({SizeAlias, Shadow, ColorField, SizeField, SizeGlobal}) => {\n  return {\n    body: {\n      flex: 1,\n      paddingHorizontal: SizeAlias.SpacingSmall,\n    },\n    bottomSpace: {\n      marginBottom: SizeAlias.SpacingLarge,\n    },\n    card: {\n      backgroundColor: 'white',\n      borderRadius: SizeAlias.Radius3,\n      flexDirection: 'column',\n      justifyContent: 'space-between',\n      paddingVertical: SizeAlias.SpacingSmall,\n      padding: SizeAlias.SpacingSmall,\n      ...Shadow.center,\n    },\n    container: {\n      flex: 1,\n    },\n    favorite: {\n      borderColor: ColorField.BorderDefault,\n      borderRadius: SizeField.BorderRadius,\n      borderWidth: SizeField.BorderStroke,\n      flexDirection: 'row',\n      justifyContent: 'space-between',\n      minHeight: getSize(40),\n      paddingHorizontal: SizeGlobal.Size400,\n    },\n    leftFavorite: {\n      flexWrap: 'wrap',\n      justifyContent: 'center',\n      width: '85%',\n    },\n    logo: {\n      height: SizeGlobal.Size800,\n      marginRight: SizeAlias.SpacingSmall,\n      resizeMode: 'contain',\n      width: SizeGlobal.Size800,\n    },\n    space: {\n      height: SizeAlias.SpacingXSmall,\n    },\n  };\n});\n"],
      mappings: ";;;;;;AAAA,IAAAA,sBAAA,GAAAC,OAAA;AAEaC,OAAA,CAAAC,SAAS,GAAG,IAAAH,sBAAA,CAAAI,mBAAmB,EAAC,UAAAC,IAAA,EAA2D;EAAA,IAAzDC,SAAS,GAAAD,IAAA,CAATC,SAAS;IAAEC,MAAM,GAAAF,IAAA,CAANE,MAAM;IAAEC,UAAU,GAAAH,IAAA,CAAVG,UAAU;IAAEC,SAAS,GAAAJ,IAAA,CAATI,SAAS;IAAEC,UAAU,GAAAL,IAAA,CAAVK,UAAU;EACjG,OAAO;IACLC,IAAI,EAAE;MACJC,IAAI,EAAE,CAAC;MACPC,iBAAiB,EAAEP,SAAS,CAACQ;KAC9B;IACDC,WAAW,EAAE;MACXC,YAAY,EAAEV,SAAS,CAACW;KACzB;IACDC,IAAI,EAAAC,MAAA,CAAAC,MAAA;MACFC,eAAe,EAAE,OAAO;MACxBC,YAAY,EAAEhB,SAAS,CAACiB,OAAO;MAC/BC,aAAa,EAAE,QAAQ;MACvBC,cAAc,EAAE,eAAe;MAC/BC,eAAe,EAAEpB,SAAS,CAACQ,YAAY;MACvCa,OAAO,EAAErB,SAAS,CAACQ;IAAY,GAC5BP,MAAM,CAACqB,MAAM,CACjB;IACDC,SAAS,EAAE;MACTjB,IAAI,EAAE;KACP;IACDkB,QAAQ,EAAE;MACRC,WAAW,EAAEvB,UAAU,CAACwB,aAAa;MACrCV,YAAY,EAAEb,SAAS,CAACwB,YAAY;MACpCC,WAAW,EAAEzB,SAAS,CAAC0B,YAAY;MACnCX,aAAa,EAAE,KAAK;MACpBC,cAAc,EAAE,eAAe;MAC/BW,SAAS,EAAE,IAAApC,sBAAA,CAAAqC,OAAO,EAAC,EAAE,CAAC;MACtBxB,iBAAiB,EAAEH,UAAU,CAAC4B;KAC/B;IACDC,YAAY,EAAE;MACZC,QAAQ,EAAE,MAAM;MAChBf,cAAc,EAAE,QAAQ;MACxBgB,KAAK,EAAE;KACR;IACDC,IAAI,EAAE;MACJC,MAAM,EAAEjC,UAAU,CAACkC,OAAO;MAC1BC,WAAW,EAAEvC,SAAS,CAACQ,YAAY;MACnCgC,UAAU,EAAE,SAAS;MACrBL,KAAK,EAAE/B,UAAU,CAACkC;KACnB;IACDG,KAAK,EAAE;MACLJ,MAAM,EAAErC,SAAS,CAAC0C;;GAErB;AACH,CAAC,CAAC",
      ignoreList: []
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "5d18cdd3e86bcd76334ffe6e5d6e02605121c130"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_o0795co3v = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_o0795co3v();
cov_o0795co3v().s[0]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_o0795co3v().s[1]++;
exports.makeStyle = void 0;
var msb_shared_component_1 =
/* istanbul ignore next */
(cov_o0795co3v().s[2]++, require("msb-shared-component"));
/* istanbul ignore next */
cov_o0795co3v().s[3]++;
exports.makeStyle = (0, msb_shared_component_1.createMSBStyleSheet)(function (_ref) {
  /* istanbul ignore next */
  cov_o0795co3v().f[0]++;
  var SizeAlias =
    /* istanbul ignore next */
    (cov_o0795co3v().s[4]++, _ref.SizeAlias),
    Shadow =
    /* istanbul ignore next */
    (cov_o0795co3v().s[5]++, _ref.Shadow),
    ColorField =
    /* istanbul ignore next */
    (cov_o0795co3v().s[6]++, _ref.ColorField),
    SizeField =
    /* istanbul ignore next */
    (cov_o0795co3v().s[7]++, _ref.SizeField),
    SizeGlobal =
    /* istanbul ignore next */
    (cov_o0795co3v().s[8]++, _ref.SizeGlobal);
  /* istanbul ignore next */
  cov_o0795co3v().s[9]++;
  return {
    body: {
      flex: 1,
      paddingHorizontal: SizeAlias.SpacingSmall
    },
    bottomSpace: {
      marginBottom: SizeAlias.SpacingLarge
    },
    card: Object.assign({
      backgroundColor: 'white',
      borderRadius: SizeAlias.Radius3,
      flexDirection: 'column',
      justifyContent: 'space-between',
      paddingVertical: SizeAlias.SpacingSmall,
      padding: SizeAlias.SpacingSmall
    }, Shadow.center),
    container: {
      flex: 1
    },
    favorite: {
      borderColor: ColorField.BorderDefault,
      borderRadius: SizeField.BorderRadius,
      borderWidth: SizeField.BorderStroke,
      flexDirection: 'row',
      justifyContent: 'space-between',
      minHeight: (0, msb_shared_component_1.getSize)(40),
      paddingHorizontal: SizeGlobal.Size400
    },
    leftFavorite: {
      flexWrap: 'wrap',
      justifyContent: 'center',
      width: '85%'
    },
    logo: {
      height: SizeGlobal.Size800,
      marginRight: SizeAlias.SpacingSmall,
      resizeMode: 'contain',
      width: SizeGlobal.Size800
    },
    space: {
      height: SizeAlias.SpacingXSmall
    }
  };
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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