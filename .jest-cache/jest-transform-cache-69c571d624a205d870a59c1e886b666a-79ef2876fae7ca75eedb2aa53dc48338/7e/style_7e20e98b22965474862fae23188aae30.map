{"version": 3, "names": ["cov_o0795co3v", "actualCoverage", "s", "msb_shared_component_1", "require", "exports", "makeStyle", "createMSBStyleSheet", "_ref", "f", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Shadow", "ColorField", "SizeField", "SizeGlobal", "body", "flex", "paddingHorizontal", "SpacingSmall", "bottomSpace", "marginBottom", "SpacingLarge", "card", "Object", "assign", "backgroundColor", "borderRadius", "Radius3", "flexDirection", "justifyContent", "paddingVertical", "padding", "center", "container", "favorite", "borderColor", "BorderDefault", "BorderRadius", "borderWidth", "BorderStroke", "minHeight", "getSize", "Size400", "leftFavorite", "flexWrap", "width", "logo", "height", "Size800", "marginRight", "resizeMode", "space", "SpacingXSmall"], "sources": ["/Users/<USER>/projects/react-native/payment-module/src/presentation/savecontact/style.ts"], "sourcesContent": ["import {createMSBStyleSheet, getSize} from 'msb-shared-component';\n\nexport const makeStyle = createMSBStyleSheet(({SizeAlias, Shadow, ColorField, SizeField, SizeGlobal}) => {\n  return {\n    body: {\n      flex: 1,\n      paddingHorizontal: SizeAlias.SpacingSmall,\n    },\n    bottomSpace: {\n      marginBottom: SizeAlias.SpacingLarge,\n    },\n    card: {\n      backgroundColor: 'white',\n      borderRadius: SizeAlias.Radius3,\n      flexDirection: 'column',\n      justifyContent: 'space-between',\n      paddingVertical: SizeAlias.SpacingSmall,\n      padding: SizeAlias.SpacingSmall,\n      ...Shadow.center,\n    },\n    container: {\n      flex: 1,\n    },\n    favorite: {\n      borderColor: ColorField.BorderDefault,\n      borderRadius: SizeField.BorderRadius,\n      borderWidth: SizeField.BorderStroke,\n      flexDirection: 'row',\n      justifyContent: 'space-between',\n      minHeight: getSize(40),\n      paddingHorizontal: SizeGlobal.Size400,\n    },\n    leftFavorite: {\n      flexWrap: 'wrap',\n      justifyContent: 'center',\n      width: '85%',\n    },\n    logo: {\n      height: SizeGlobal.Size800,\n      marginRight: SizeAlias.SpacingSmall,\n      resizeMode: 'contain',\n      width: SizeGlobal.Size800,\n    },\n    space: {\n      height: SizeAlias.SpacingXSmall,\n    },\n  };\n});\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAKY;IAAAA,aAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,aAAA;AAAAA,aAAA,GAAAE,CAAA;;;;;;;AALZ,IAAAC,sBAAA;AAAA;AAAA,CAAAH,aAAA,GAAAE,CAAA,OAAAE,OAAA;AAAA;AAAAJ,aAAA,GAAAE,CAAA;AAEaG,OAAA,CAAAC,SAAS,GAAG,IAAAH,sBAAA,CAAAI,mBAAmB,EAAC,UAAAC,IAAA,EAA2D;EAAA;EAAAR,aAAA,GAAAS,CAAA;EAAA,IAAzDC,SAAS;IAAA;IAAA,CAAAV,aAAA,GAAAE,CAAA,OAAAM,IAAA,CAATE,SAAS;IAAEC,MAAM;IAAA;IAAA,CAAAX,aAAA,GAAAE,CAAA,OAAAM,IAAA,CAANG,MAAM;IAAEC,UAAU;IAAA;IAAA,CAAAZ,aAAA,GAAAE,CAAA,OAAAM,IAAA,CAAVI,UAAU;IAAEC,SAAS;IAAA;IAAA,CAAAb,aAAA,GAAAE,CAAA,OAAAM,IAAA,CAATK,SAAS;IAAEC,UAAU;IAAA;IAAA,CAAAd,aAAA,GAAAE,CAAA,OAAAM,IAAA,CAAVM,UAAU;EAAA;EAAAd,aAAA,GAAAE,CAAA;EACjG,OAAO;IACLa,IAAI,EAAE;MACJC,IAAI,EAAE,CAAC;MACPC,iBAAiB,EAAEP,SAAS,CAACQ;KAC9B;IACDC,WAAW,EAAE;MACXC,YAAY,EAAEV,SAAS,CAACW;KACzB;IACDC,IAAI,EAAAC,MAAA,CAAAC,MAAA;MACFC,eAAe,EAAE,OAAO;MACxBC,YAAY,EAAEhB,SAAS,CAACiB,OAAO;MAC/BC,aAAa,EAAE,QAAQ;MACvBC,cAAc,EAAE,eAAe;MAC/BC,eAAe,EAAEpB,SAAS,CAACQ,YAAY;MACvCa,OAAO,EAAErB,SAAS,CAACQ;IAAY,GAC5BP,MAAM,CAACqB,MAAM,CACjB;IACDC,SAAS,EAAE;MACTjB,IAAI,EAAE;KACP;IACDkB,QAAQ,EAAE;MACRC,WAAW,EAAEvB,UAAU,CAACwB,aAAa;MACrCV,YAAY,EAAEb,SAAS,CAACwB,YAAY;MACpCC,WAAW,EAAEzB,SAAS,CAAC0B,YAAY;MACnCX,aAAa,EAAE,KAAK;MACpBC,cAAc,EAAE,eAAe;MAC/BW,SAAS,EAAE,IAAArC,sBAAA,CAAAsC,OAAO,EAAC,EAAE,CAAC;MACtBxB,iBAAiB,EAAEH,UAAU,CAAC4B;KAC/B;IACDC,YAAY,EAAE;MACZC,QAAQ,EAAE,MAAM;MAChBf,cAAc,EAAE,QAAQ;MACxBgB,KAAK,EAAE;KACR;IACDC,IAAI,EAAE;MACJC,MAAM,EAAEjC,UAAU,CAACkC,OAAO;MAC1BC,WAAW,EAAEvC,SAAS,CAACQ,YAAY;MACnCgC,UAAU,EAAE,SAAS;MACrBL,KAAK,EAAE/B,UAAU,CAACkC;KACnB;IACDG,KAAK,EAAE;MACLJ,MAAM,EAAErC,SAAS,CAAC0C;;GAErB;AACH,CAAC,CAAC", "ignoreList": []}