{"version": 3, "names": ["_interopRequireDefault", "require", "Object", "defineProperty", "exports", "value", "getFabricUIManager", "_defineLazyObjectProperty", "nativeFabricUIManagerProxy", "CACHED_PROPERTIES", "global", "nativeFabricUIManager", "createProxyWithCachedProperties", "implementation", "propertiesToCache", "proxy", "create", "_loop", "propertyName", "defineLazyObjectProperty", "get"], "sources": ["FabricUIManager.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @flow strict\n * @format\n */\n\n'use strict';\n\nimport type {\n  InternalInstanceHandle,\n  LayoutAnimationConfig,\n  MeasureInWindowOnSuccessCallback,\n  MeasureLayoutOnSuccessCallback,\n  MeasureOnSuccessCallback,\n  Node,\n} from '../Renderer/shims/ReactNativeTypes';\nimport type {RootTag} from '../Types/RootTagTypes';\n\nimport defineLazyObjectProperty from '../Utilities/defineLazyObjectProperty';\n\nexport type NodeSet = Array<Node>;\nexport type NodeProps = {...};\nexport interface Spec {\n  +createNode: (\n    reactTag: number,\n    viewName: string,\n    rootTag: RootTag,\n    props: NodeProps,\n    instanceHandle: InternalInstanceHandle,\n  ) => Node;\n  +cloneNode: (node: Node) => Node;\n  +cloneNodeWithNewChildren: (node: Node) => Node;\n  +cloneNodeWithNewProps: (node: Node, newProps: NodeProps) => Node;\n  +cloneNodeWithNewChildrenAndProps: (node: Node, newProps: NodeProps) => Node;\n  +createChildSet: (rootTag: RootTag) => NodeSet;\n  +appendChild: (parentNode: Node, child: Node) => Node;\n  +appendChildToSet: (childSet: NodeSet, child: Node) => void;\n  +completeRoot: (rootTag: RootTag, childSet: NodeSet) => void;\n  +measure: (node: Node, callback: MeasureOnSuccessCallback) => void;\n  +measureInWindow: (\n    node: Node,\n    callback: MeasureInWindowOnSuccessCallback,\n  ) => void;\n  +measureLayout: (\n    node: Node,\n    relativeNode: Node,\n    onFail: () => void,\n    onSuccess: MeasureLayoutOnSuccessCallback,\n  ) => void;\n  +configureNextLayoutAnimation: (\n    config: LayoutAnimationConfig,\n    callback: () => void, // check what is returned here\n    errorCallback: () => void,\n  ) => void;\n  +sendAccessibilityEvent: (node: Node, eventType: string) => void;\n  +findShadowNodeByTag_DEPRECATED: (reactTag: number) => ?Node;\n  +setNativeProps: (node: Node, newProps: NodeProps) => void;\n  +dispatchCommand: (\n    node: Node,\n    commandName: string,\n    args: Array<mixed>,\n  ) => void;\n  +findNodeAtPoint: (\n    node: Node,\n    locationX: number,\n    locationY: number,\n    callback: (instanceHandle: ?InternalInstanceHandle) => void,\n  ) => void;\n  +compareDocumentPosition: (node: Node, otherNode: Node) => number;\n  +getBoundingClientRect: (\n    node: Node,\n    includeTransform: boolean,\n  ) => ?[\n    /* x: */ number,\n    /* y: */ number,\n    /* width: */ number,\n    /* height: */ number,\n  ];\n}\n\nlet nativeFabricUIManagerProxy: ?Spec;\n\n// This is a list of all the methods in global.nativeFabricUIManager that we'll\n// cache in JavaScript, as the current implementation of the binding\n// creates a new host function every time methods are accessed.\nconst CACHED_PROPERTIES = [\n  'createNode',\n  'cloneNode',\n  'cloneNodeWithNewChildren',\n  'cloneNodeWithNewProps',\n  'cloneNodeWithNewChildrenAndProps',\n  'createChildSet',\n  'appendChild',\n  'appendChildToSet',\n  'completeRoot',\n  'measure',\n  'measureInWindow',\n  'measureLayout',\n  'configureNextLayoutAnimation',\n  'sendAccessibilityEvent',\n  'findShadowNodeByTag_DEPRECATED',\n  'setNativeProps',\n  'dispatchCommand',\n  'compareDocumentPosition',\n  'getBoundingClientRect',\n];\n\n// This is exposed as a getter because apps using the legacy renderer AND\n// Fabric can define the binding lazily. If we evaluated the global and cached\n// it in the module we might be caching an `undefined` value before it is set.\nexport function getFabricUIManager(): ?Spec {\n  if (\n    nativeFabricUIManagerProxy == null &&\n    global.nativeFabricUIManager != null\n  ) {\n    nativeFabricUIManagerProxy = createProxyWithCachedProperties(\n      global.nativeFabricUIManager,\n      CACHED_PROPERTIES,\n    );\n  }\n  return nativeFabricUIManagerProxy;\n}\n\n/**\n *\n * Returns an object that caches the specified properties the first time they\n * are accessed, and falls back to the original object for other properties.\n */\nfunction createProxyWithCachedProperties(\n  implementation: Spec,\n  propertiesToCache: $ReadOnlyArray<string>,\n): Spec {\n  const proxy = Object.create(implementation);\n  for (const propertyName of propertiesToCache) {\n    defineLazyObjectProperty(proxy, propertyName, {\n      // $FlowExpectedError[prop-missing]\n      get: () => implementation[propertyName],\n    });\n  }\n  return proxy;\n}\n"], "mappings": "AAUA,YAAY;;AAAC,IAAAA,sBAAA,GAAAC,OAAA;AAAAC,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,KAAA;AAAA;AAAAD,OAAA,CAAAE,kBAAA,GAAAA,kBAAA;AAYb,IAAAC,yBAAA,GAAAP,sBAAA,CAAAC,OAAA;AA8DA,IAAIO,0BAAiC;AAKrC,IAAMC,iBAAiB,GAAG,CACxB,YAAY,EACZ,WAAW,EACX,0BAA0B,EAC1B,uBAAuB,EACvB,kCAAkC,EAClC,gBAAgB,EAChB,aAAa,EACb,kBAAkB,EAClB,cAAc,EACd,SAAS,EACT,iBAAiB,EACjB,eAAe,EACf,8BAA8B,EAC9B,wBAAwB,EACxB,gCAAgC,EAChC,gBAAgB,EAChB,iBAAiB,EACjB,yBAAyB,EACzB,uBAAuB,CACxB;AAKM,SAASH,kBAAkBA,CAAA,EAAU;EAC1C,IACEE,0BAA0B,IAAI,IAAI,IAClCE,MAAM,CAACC,qBAAqB,IAAI,IAAI,EACpC;IACAH,0BAA0B,GAAGI,+BAA+B,CAC1DF,MAAM,CAACC,qBAAqB,EAC5BF,iBACF,CAAC;EACH;EACA,OAAOD,0BAA0B;AACnC;AAOA,SAASI,+BAA+BA,CACtCC,cAAoB,EACpBC,iBAAyC,EACnC;EACN,IAAMC,KAAK,GAAGb,MAAM,CAACc,MAAM,CAACH,cAAc,CAAC;EAAC,IAAAI,KAAA,YAAAA,MAAAC,YAAA,EACE;IAC5C,IAAAC,iCAAwB,EAACJ,KAAK,EAAEG,YAAY,EAAE;MAE5CE,GAAG,EAAE,SAALA,GAAGA,CAAA;QAAA,OAAQP,cAAc,CAACK,YAAY,CAAC;MAAA;IACzC,CAAC,CAAC;EACJ,CAAC;EALD,KAAK,IAAMA,YAAY,IAAIJ,iBAAiB;IAAAG,KAAA,CAAAC,YAAA;EAAA;EAM5C,OAAOH,KAAK;AACd", "ignoreList": []}