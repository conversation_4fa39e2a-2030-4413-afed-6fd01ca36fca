1847f2abe788cc5ad3bf0f543b5d3e1b
'use strict';

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.getFabricUIManager = getFabricUIManager;
var _defineLazyObjectProperty = _interopRequireDefault(require("../Utilities/defineLazyObjectProperty"));
var nativeFabricUIManagerProxy;
var CACHED_PROPERTIES = ['createNode', 'cloneNode', 'cloneNodeWithNewChildren', 'cloneNodeWithNewProps', 'cloneNodeWithNewChildrenAndProps', 'createChildSet', 'appendChild', 'appendChildToSet', 'completeRoot', 'measure', 'measureInWindow', 'measureLayout', 'configureNextLayoutAnimation', 'sendAccessibilityEvent', 'findShadowNodeByTag_DEPRECATED', 'setNativeProps', 'dispatchCommand', 'compareDocumentPosition', 'getBoundingClientRect'];
function getFabricUIManager() {
  if (nativeFabricUIManagerProxy == null && global.nativeFabricUIManager != null) {
    nativeFabricUIManagerProxy = createProxyWithCachedProperties(global.nativeFabricUIManager, CACHED_PROPERTIES);
  }
  return nativeFabricUIManagerProxy;
}
function createProxyWithCachedProperties(implementation, propertiesToCache) {
  var proxy = Object.create(implementation);
  var _loop = function _loop(propertyName) {
    (0, _defineLazyObjectProperty.default)(proxy, propertyName, {
      get: function get() {
        return implementation[propertyName];
      }
    });
  };
  for (var propertyName of propertiesToCache) {
    _loop(propertyName);
  }
  return proxy;
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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