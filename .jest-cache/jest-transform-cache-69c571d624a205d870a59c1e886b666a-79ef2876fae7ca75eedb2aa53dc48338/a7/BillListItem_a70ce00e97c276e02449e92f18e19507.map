{"version": 3, "names": ["cov_18ovk3ncpr", "actualCoverage", "react_native_1", "s", "require", "react_1", "__importDefault", "__1", "index_1", "msb_shared_component_1", "FormatUtils_1", "BillListItem", "_ref", "f", "isBlocked", "isEditable", "item", "highlight", "onEdit", "_onClick", "onClick", "onDelete", "onPayment", "_ref2", "useMSBStyles", "exports", "makeStyle", "styles", "reminderStatus", "getReminderStatus", "default", "createElement", "View", "b", "SwipeableBillItem", "editOnClick", "deleteOnClick", "accountItem", "showDialogIMBM", "onPressPayment", "BillItem", "style", "line", "amountPaymentContainer", "MSBTextBase", "amountPaymentText", "formatMoney", "getPayableAmount", "currencyText", "content", "createMSBStyleSheet", "_ref3", "ColorAlias", "ColorDataView", "ColorGlobal", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Typography", "container", "backgroundColor", "Neutral50", "height", "getSize", "justifyContent", "paddingHorizontal", "SpacingSmall", "BorderDefault", "marginHorizontal", "flexDirection", "borderBottomWidth", "borderBottomColor", "Neutral100", "paddingTop", "paddingBottom", "Object", "assign", "small_semiBold", "color", "Neutral800", "small_regular", "Neutral600"], "sources": ["/Users/<USER>/projects/react-native/payment-module/src/presentation/payment-home/components/bill-list/BillListItem.tsx"], "sourcesContent": ["import {View} from 'react-native';\nimport React from 'react';\nimport {showDialogIMBM} from '../..';\nimport {BillItem, SwipeableBillItem} from '../../../../components/bill-item/index';\nimport {IBillContact} from '../../../../domain/entities/IBillContact';\nimport {createMSBStyleSheet, getSize, MSBTextBase, useMSBStyles} from 'msb-shared-component';\nimport FormatUtils from '../../../../utils/FormatUtils';\ntype ItemProps = {\n  isBlocked: boolean;\n  isEditable: boolean;\n  item: IBillContact;\n  highlight: string;\n  onClick: (item: IBillContact) => void;\n  onEdit?: (item: IBillContact) => void;\n  onDelete?: (item: IBillContact) => void;\n  onPayment?: (item: IBillContact) => void;\n};\n\nexport const BillListItem = ({\n  isBlocked,\n  isEditable,\n  item,\n  highlight,\n  onEdit,\n  onClick,\n  onDelete,\n  onPayment,\n}: ItemProps) => {\n  const {styles} = useMSBStyles(makeStyle);\n  const reminderStatus = item.getReminderStatus() === 'ACTIVE';\n\n  // const alteredItem: any = beneficiaryStore?.isBeneficiary ? {...item, localType: 'MYACCOUNT'} : {...item};\n  // return <MSBTextBase content=\"ITEM\" />;\n  return (\n    <View>\n      {isEditable ? (\n        <SwipeableBillItem\n          item={item}\n          editOnClick={onEdit}\n          deleteOnClick={onDelete}\n          onClick={accountItem => {\n            if (isBlocked) {\n              showDialogIMBM();\n              return;\n            }\n            onClick(accountItem);\n          }}\n          highlight={highlight}\n          onPressPayment={onPayment}\n        />\n      ) : (\n        <BillItem\n          item={item}\n          onClick={accountItem => {\n            if (isBlocked) {\n              showDialogIMBM();\n              return;\n            }\n            onClick(accountItem);\n          }}\n          highlight={highlight}\n          onPressPayment={onPayment}\n        />\n      )}\n      <View style={styles.line} />\n      {reminderStatus && (\n        <View style={styles.amountPaymentContainer}>\n          {/* <MSBTextBase>{item.getCustomerName()}</MSBTextBase> */}\n          <MSBTextBase>Hóa đơn T3/2025</MSBTextBase>\n          <MSBTextBase style={styles.amountPaymentText}>\n            {FormatUtils.formatMoney(item.getPayableAmount())} <MSBTextBase style={styles.currencyText} content=\"VND\" />\n          </MSBTextBase>\n        </View>\n      )}\n    </View>\n  );\n};\n\nexport const makeStyle = createMSBStyleSheet(({ColorAlias, ColorDataView, ColorGlobal, SizeAlias, Typography}) => {\n  return {\n    container: {\n      backgroundColor: ColorGlobal.Neutral50,\n      height: getSize(36),\n      justifyContent: 'center',\n      paddingHorizontal: SizeAlias.SpacingSmall,\n    },\n    line: {\n      backgroundColor: ColorAlias.BorderDefault,\n      height: 1,\n      marginHorizontal: SizeAlias.SpacingSmall,\n    },\n    amountPaymentContainer: {\n      flexDirection: 'row',\n      justifyContent: 'space-between',\n      paddingHorizontal: SizeAlias.SpacingSmall,\n      borderBottomWidth: 1,\n      borderBottomColor: ColorGlobal.Neutral100,\n      paddingTop: SizeAlias.SpacingSmall,\n      paddingBottom: SizeAlias.SpacingSmall,\n    },\n    amountPaymentText: {\n      ...Typography?.small_semiBold,\n      color: ColorGlobal.Neutral800,\n    },\n    currencyText: {\n      ...Typography?.small_regular,\n      color: ColorGlobal.Neutral600,\n    },\n  };\n});\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAKA;IAAAA,cAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,cAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AALA,IAAAE,cAAA;AAAA;AAAA,CAAAF,cAAA,GAAAG,CAAA,OAAAC,OAAA;AACA,IAAAC,OAAA;AAAA;AAAA,CAAAL,cAAA,GAAAG,CAAA,OAAAG,eAAA,CAAAF,OAAA;AACA,IAAAG,GAAA;AAAA;AAAA,CAAAP,cAAA,GAAAG,CAAA,OAAAC,OAAA;AACA,IAAAI,OAAA;AAAA;AAAA,CAAAR,cAAA,GAAAG,CAAA,OAAAC,OAAA;AAEA,IAAAK,sBAAA;AAAA;AAAA,CAAAT,cAAA,GAAAG,CAAA,OAAAC,OAAA;AACA,IAAAM,aAAA;AAAA;AAAA,CAAAV,cAAA,GAAAG,CAAA,OAAAG,eAAA,CAAAF,OAAA;AAAA;AAAAJ,cAAA,GAAAG,CAAA;AAYO,IAAMQ,YAAY,GAAG,SAAfA,YAAYA,CAAAC,IAAA,EAST;EAAA;EAAAZ,cAAA,GAAAa,CAAA;EAAA,IARdC,SAAS;IAAA;IAAA,CAAAd,cAAA,GAAAG,CAAA,QAAAS,IAAA,CAATE,SAAS;IACTC,UAAU;IAAA;IAAA,CAAAf,cAAA,GAAAG,CAAA,QAAAS,IAAA,CAAVG,UAAU;IACVC,IAAI;IAAA;IAAA,CAAAhB,cAAA,GAAAG,CAAA,QAAAS,IAAA,CAAJI,IAAI;IACJC,SAAS;IAAA;IAAA,CAAAjB,cAAA,GAAAG,CAAA,QAAAS,IAAA,CAATK,SAAS;IACTC,MAAM;IAAA;IAAA,CAAAlB,cAAA,GAAAG,CAAA,QAAAS,IAAA,CAANM,MAAM;IACNC,QAAO;IAAA;IAAA,CAAAnB,cAAA,GAAAG,CAAA,QAAAS,IAAA,CAAPQ,OAAO;IACPC,QAAQ;IAAA;IAAA,CAAArB,cAAA,GAAAG,CAAA,QAAAS,IAAA,CAARS,QAAQ;IACRC,SAAS;IAAA;IAAA,CAAAtB,cAAA,GAAAG,CAAA,QAAAS,IAAA,CAATU,SAAS;EAET,IAAAC,KAAA;IAAA;IAAA,CAAAvB,cAAA,GAAAG,CAAA,QAAiB,IAAAM,sBAAA,CAAAe,YAAY,EAACC,OAAA,CAAAC,SAAS,CAAC;IAAjCC,MAAM;IAAA;IAAA,CAAA3B,cAAA,GAAAG,CAAA,QAAAoB,KAAA,CAANI,MAAM;EACb,IAAMC,cAAc;EAAA;EAAA,CAAA5B,cAAA,GAAAG,CAAA,QAAGa,IAAI,CAACa,iBAAiB,EAAE,KAAK,QAAQ;EAAA;EAAA7B,cAAA,GAAAG,CAAA;EAI5D,OACEE,OAAA,CAAAyB,OAAA,CAAAC,aAAA,CAAC7B,cAAA,CAAA8B,IAAI,QACFjB,UAAU;EAAA;EAAA,CAAAf,cAAA,GAAAiC,CAAA,UACT5B,OAAA,CAAAyB,OAAA,CAAAC,aAAA,CAACvB,OAAA,CAAA0B,iBAAiB;IAChBlB,IAAI,EAAEA,IAAI;IACVmB,WAAW,EAAEjB,MAAM;IACnBkB,aAAa,EAAEf,QAAQ;IACvBD,OAAO,EAAE,SAATA,OAAOA,CAAEiB,WAAW,EAAG;MAAA;MAAArC,cAAA,GAAAa,CAAA;MAAAb,cAAA,GAAAG,CAAA;MACrB,IAAIW,SAAS,EAAE;QAAA;QAAAd,cAAA,GAAAiC,CAAA;QAAAjC,cAAA,GAAAG,CAAA;QACb,IAAAI,GAAA,CAAA+B,cAAc,GAAE;QAAA;QAAAtC,cAAA,GAAAG,CAAA;QAChB;MACF;MAAA;MAAA;QAAAH,cAAA,GAAAiC,CAAA;MAAA;MAAAjC,cAAA,GAAAG,CAAA;MACAgB,QAAO,CAACkB,WAAW,CAAC;IACtB,CAAC;IACDpB,SAAS,EAAEA,SAAS;IACpBsB,cAAc,EAAEjB;EAAS,EACzB;EAAA;EAAA,CAAAtB,cAAA,GAAAiC,CAAA,UAEF5B,OAAA,CAAAyB,OAAA,CAAAC,aAAA,CAACvB,OAAA,CAAAgC,QAAQ;IACPxB,IAAI,EAAEA,IAAI;IACVI,OAAO,EAAE,SAATA,OAAOA,CAAEiB,WAAW,EAAG;MAAA;MAAArC,cAAA,GAAAa,CAAA;MAAAb,cAAA,GAAAG,CAAA;MACrB,IAAIW,SAAS,EAAE;QAAA;QAAAd,cAAA,GAAAiC,CAAA;QAAAjC,cAAA,GAAAG,CAAA;QACb,IAAAI,GAAA,CAAA+B,cAAc,GAAE;QAAA;QAAAtC,cAAA,GAAAG,CAAA;QAChB;MACF;MAAA;MAAA;QAAAH,cAAA,GAAAiC,CAAA;MAAA;MAAAjC,cAAA,GAAAG,CAAA;MACAgB,QAAO,CAACkB,WAAW,CAAC;IACtB,CAAC;IACDpB,SAAS,EAAEA,SAAS;IACpBsB,cAAc,EAAEjB;EAAS,EAE5B,GACDjB,OAAA,CAAAyB,OAAA,CAAAC,aAAA,CAAC7B,cAAA,CAAA8B,IAAI;IAACS,KAAK,EAAEd,MAAM,CAACe;EAAI,EAAI;EAC3B;EAAA,CAAA1C,cAAA,GAAAiC,CAAA,UAAAL,cAAc;EAAA;EAAA,CAAA5B,cAAA,GAAAiC,CAAA,UACb5B,OAAA,CAAAyB,OAAA,CAAAC,aAAA,CAAC7B,cAAA,CAAA8B,IAAI;IAACS,KAAK,EAAEd,MAAM,CAACgB;EAAsB,GAExCtC,OAAA,CAAAyB,OAAA,CAAAC,aAAA,CAACtB,sBAAA,CAAAmC,WAAW,uCAA8B,EAC1CvC,OAAA,CAAAyB,OAAA,CAAAC,aAAA,CAACtB,sBAAA,CAAAmC,WAAW;IAACH,KAAK,EAAEd,MAAM,CAACkB;EAAiB,GACzCnC,aAAA,CAAAoB,OAAW,CAACgB,WAAW,CAAC9B,IAAI,CAAC+B,gBAAgB,EAAE,CAAC,E,KAAE1C,OAAA,CAAAyB,OAAA,CAAAC,aAAA,CAACtB,sBAAA,CAAAmC,WAAW;IAACH,KAAK,EAAEd,MAAM,CAACqB,YAAY;IAAEC,OAAO,EAAC;EAAK,EAAG,CAChG,CAEjB,EACI;AAEX,CAAC;AAAA;AAAAjD,cAAA,GAAAG,CAAA;AA1DYsB,OAAA,CAAAd,YAAY,GAAAA,YAAA;AAAA;AAAAX,cAAA,GAAAG,CAAA;AA4DZsB,OAAA,CAAAC,SAAS,GAAG,IAAAjB,sBAAA,CAAAyC,mBAAmB,EAAC,UAAAC,KAAA,EAAoE;EAAA;EAAAnD,cAAA,GAAAa,CAAA;EAAA,IAAlEuC,UAAU;IAAA;IAAA,CAAApD,cAAA,GAAAG,CAAA,QAAAgD,KAAA,CAAVC,UAAU;IAAEC,aAAa;IAAA;IAAA,CAAArD,cAAA,GAAAG,CAAA,QAAAgD,KAAA,CAAbE,aAAa;IAAEC,WAAW;IAAA;IAAA,CAAAtD,cAAA,GAAAG,CAAA,QAAAgD,KAAA,CAAXG,WAAW;IAAEC,SAAS;IAAA;IAAA,CAAAvD,cAAA,GAAAG,CAAA,QAAAgD,KAAA,CAATI,SAAS;IAAEC,UAAU;IAAA;IAAA,CAAAxD,cAAA,GAAAG,CAAA,QAAAgD,KAAA,CAAVK,UAAU;EAAA;EAAAxD,cAAA,GAAAG,CAAA;EAC1G,OAAO;IACLsD,SAAS,EAAE;MACTC,eAAe,EAAEJ,WAAW,CAACK,SAAS;MACtCC,MAAM,EAAE,IAAAnD,sBAAA,CAAAoD,OAAO,EAAC,EAAE,CAAC;MACnBC,cAAc,EAAE,QAAQ;MACxBC,iBAAiB,EAAER,SAAS,CAACS;KAC9B;IACDtB,IAAI,EAAE;MACJgB,eAAe,EAAEN,UAAU,CAACa,aAAa;MACzCL,MAAM,EAAE,CAAC;MACTM,gBAAgB,EAAEX,SAAS,CAACS;KAC7B;IACDrB,sBAAsB,EAAE;MACtBwB,aAAa,EAAE,KAAK;MACpBL,cAAc,EAAE,eAAe;MAC/BC,iBAAiB,EAAER,SAAS,CAACS,YAAY;MACzCI,iBAAiB,EAAE,CAAC;MACpBC,iBAAiB,EAAEf,WAAW,CAACgB,UAAU;MACzCC,UAAU,EAAEhB,SAAS,CAACS,YAAY;MAClCQ,aAAa,EAAEjB,SAAS,CAACS;KAC1B;IACDnB,iBAAiB,EAAA4B,MAAA,CAAAC,MAAA,KACZlB,UAAU;IAAA;IAAA,CAAAxD,cAAA,GAAAiC,CAAA;IAAA;IAAA,CAAAjC,cAAA,GAAAiC,CAAA,UAAVuB,UAAU,CAAEmB,cAAc;MAC7BC,KAAK,EAAEtB,WAAW,CAACuB;IAAU,EAC9B;IACD7B,YAAY,EAAAyB,MAAA,CAAAC,MAAA,KACPlB,UAAU;IAAA;IAAA,CAAAxD,cAAA,GAAAiC,CAAA;IAAA;IAAA,CAAAjC,cAAA,GAAAiC,CAAA,UAAVuB,UAAU,CAAEsB,aAAa;MAC5BF,KAAK,EAAEtB,WAAW,CAACyB;IAAU;GAEhC;AACH,CAAC,CAAC", "ignoreList": []}