{"version": 3, "names": ["Object", "defineProperty", "exports", "value", "adaptViewConfig", "addWhitelistedNativeProps", "addWhitelistedUIProps", "configureProps", "configure<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_propsAllowlists", "require", "_core", "_errors", "_index", "_PlatformChecker", "SHOULD_BE_USE_WEB", "shouldBeUseWeb", "assertNoOverlapInLists", "key", "PropsAllowlists", "NATIVE_THREAD_PROPS_WHITELIST", "UI_THREAD_PROPS_WHITELIST", "ReanimatedError", "jsiConfigureProps", "keys", "props", "oldSize", "length", "assign", "config", "updateLoggerConfig", "executeOnUIRuntimeSync", "PROCESSED_VIEW_NAMES", "Set", "viewConfig", "viewName", "uiViewClassName", "validAttributes", "has", "propsToAdd", "for<PERSON>ach", "add"], "sources": ["../../src/ConfigHelper.ts"], "sourcesContent": [null], "mappings": "AAAA,YAAY;;AAAAA,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,KAAA;AAAA;AAAAD,OAAA,CAAAE,eAAA,GAAAA,eAAA;AAAAF,OAAA,CAAAG,yBAAA,GAAAA,yBAAA;AAAAH,OAAA,CAAAI,qBAAA,GAAAA,qBAAA;AAAAJ,OAAA,CAAAK,cAAA,GAAAA,cAAA;AAAAL,OAAA,CAAAM,yBAAA,GAAAA,yBAAA;AACZ,IAAAC,gBAAA,GAAAC,OAAA;AACA,IAAAC,KAAA,GAAAD,OAAA;AACA,IAAAE,OAAA,GAAAF,OAAA;AACA,IAAAG,MAAA,GAAAH,OAAA;AAEA,IAAAI,gBAAA,GAAAJ,OAAA;AAEA,IAAMK,iBAAiB,GAAG,IAAAC,+BAAc,EAAC,CAAC;AAE1C,SAASC,sBAAsBA,CAAA,EAAG;EAChC,KAAK,IAAMC,GAAG,IAAIC,gCAAe,CAACC,6BAA6B,EAAE;IAC/D,IAAIF,GAAG,IAAIC,gCAAe,CAACE,yBAAyB,EAAE;MACpD,MAAM,IAAIC,uBAAe,CACvB,cAAcJ,GAAG,wFACnB,CAAC;IACH;EACF;AACF;AAEO,SAASX,cAAcA,CAAA,EAAS;EACrCU,sBAAsB,CAAC,CAAC;EACxB,IAAAM,uBAAiB,EACfvB,MAAM,CAACwB,IAAI,CAACL,gCAAe,CAACE,yBAAyB,CAAC,EACtDrB,MAAM,CAACwB,IAAI,CAACL,gCAAe,CAACC,6BAA6B,CAC3D,CAAC;AACH;AAEO,SAASf,yBAAyBA,CACvCoB,KAA8B,EACxB;EACN,IAAMC,OAAO,GAAG1B,MAAM,CAACwB,IAAI,CACzBL,gCAAe,CAACC,6BAClB,CAAC,CAACO,MAAM;EACRR,gCAAe,CAACC,6BAA6B,GAAApB,MAAA,CAAA4B,MAAA,KACxCT,gCAAe,CAACC,6BAA6B,EAC7CK,KAAA,CACJ;EACD,IACEC,OAAO,KACP1B,MAAM,CAACwB,IAAI,CAACL,gCAAe,CAACC,6BAA6B,CAAC,CAACO,MAAM,EACjE;IACApB,cAAc,CAAC,CAAC;EAClB;AACF;AAEO,SAASD,qBAAqBA,CAACmB,KAA8B,EAAQ;EAC1E,IAAMC,OAAO,GAAG1B,MAAM,CAACwB,IAAI,CAACL,gCAAe,CAACE,yBAAyB,CAAC,CAACM,MAAM;EAC7ER,gCAAe,CAACE,yBAAyB,GAAArB,MAAA,CAAA4B,MAAA,KACpCT,gCAAe,CAACE,yBAAyB,EACzCI,KAAA,CACJ;EACD,IACEC,OAAO,KAAK1B,MAAM,CAACwB,IAAI,CAACL,gCAAe,CAACE,yBAAyB,CAAC,CAACM,MAAM,EACzE;IACApB,cAAc,CAAC,CAAC;EAClB;AACF;AAWO,SAASC,yBAAyBA,CAACqB,MAAoB,EAAE;EAE9D,IAAAC,yBAAkB,EAACD,MAAM,CAAC;EAE1B,IAAI,CAACd,iBAAiB,EAAE;IACtB,IAAAgB,4BAAsB,EAACD,yBAAkB,CAAC,CAACD,MAAM,CAAC;EACpD;AACF;AAEA,IAAMG,oBAAoB,GAAG,IAAIC,GAAG,CAAC,CAAC;AAW/B,SAAS7B,eAAeA,CAAC8B,UAAsB,EAAQ;EAC5D,IAAMC,QAAQ,GAAGD,UAAU,CAACE,eAAe;EAC3C,IAAMX,KAAK,GAAGS,UAAU,CAACG,eAAe;EAGxC,IAAI,CAACL,oBAAoB,CAACM,GAAG,CAACH,QAAQ,CAAC,EAAE;IACvC,IAAMI,UAAmC,GAAG,CAAC,CAAC;IAC9CvC,MAAM,CAACwB,IAAI,CAACC,KAAK,CAAC,CAACe,OAAO,CAAE,UAAAtB,GAAG,EAAK;MAGlC,IACE,EAAEA,GAAG,IAAIC,gCAAe,CAACC,6BAA6B,CAAC,IACvD,EAAEF,GAAG,IAAIC,gCAAe,CAACE,yBAAyB,CAAC,EACnD;QACAkB,UAAU,CAACrB,GAAG,CAAC,GAAG,IAAI;MACxB;IACF,CAAC,CAAC;IACFZ,qBAAqB,CAACiC,UAAU,CAAC;IAEjCP,oBAAoB,CAACS,GAAG,CAACN,QAAQ,CAAC;EACpC;AACF;AAEA5B,cAAc,CAAC,CAAC", "ignoreList": []}