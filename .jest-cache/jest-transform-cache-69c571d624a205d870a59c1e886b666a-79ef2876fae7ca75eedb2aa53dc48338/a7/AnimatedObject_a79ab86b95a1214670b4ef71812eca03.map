{"version": 3, "names": ["_interopRequireDefault", "require", "Object", "defineProperty", "exports", "value", "default", "isPlainObject", "_classCallCheck2", "_createClass2", "_possibleConstructorReturn2", "_getPrototypeOf2", "_get2", "_inherits2", "_classPrivateFieldLooseBase2", "_classPrivateFieldLooseKey2", "_AnimatedNode", "_AnimatedWithChildren2", "React", "_interopRequireWildcard", "_getRequireWildcardCache", "e", "WeakMap", "r", "t", "__esModule", "has", "get", "n", "__proto__", "a", "getOwnPropertyDescriptor", "u", "hasOwnProperty", "call", "i", "set", "_callSuper", "o", "_isNativeReflectConstruct", "Reflect", "construct", "constructor", "apply", "Boolean", "prototype", "valueOf", "_superPropGet", "p", "MAX_DEPTH", "getPrototypeOf", "isPrototypeOf", "isValidElement", "flatAnimatedNodes", "nodes", "arguments", "length", "undefined", "depth", "AnimatedNode", "push", "Array", "isArray", "ii", "element", "keys", "key", "mapAnimatedNodes", "fn", "map", "result", "_nodes", "AnimatedObject", "_Animated<PERSON>ith<PERSON><PERSON><PERSON><PERSON>", "config", "_this", "writable", "_value", "__getValue", "node", "__getValueWithStaticObject", "staticObject", "index", "__getAnimatedValue", "__attach", "__add<PERSON><PERSON>d", "__detach", "__remove<PERSON><PERSON>d", "__makeNative", "platformConfig", "__getNativeConfig", "type", "nodeTag", "__getNativeTag", "debugID", "__getDebugID", "from", "AnimatedWithChildren"], "sources": ["AnimatedObject.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @flow\n * @format\n * @oncall react_native\n */\n\n'use strict';\n\nimport type {PlatformConfig} from '../AnimatedPlatformConfig';\nimport type {AnimatedNodeConfig} from './AnimatedNode';\n\nimport AnimatedNode from './AnimatedNode';\nimport AnimatedWithChildren from './AnimatedWithChildren';\nimport * as React from 'react';\n\nconst MAX_DEPTH = 5;\n\nexport function isPlainObject(\n  value: mixed,\n  /* $FlowIssue[incompatible-type-guard] - Flow does not know that the prototype\n     and ReactElement checks preserve the type refinement of `value`. */\n): value is $ReadOnly<{[string]: mixed}> {\n  return (\n    value !== null &&\n    typeof value === 'object' &&\n    Object.getPrototypeOf(value).isPrototypeOf(Object) &&\n    !React.isValidElement(value)\n  );\n}\n\nfunction flatAnimatedNodes(\n  value: mixed,\n  nodes: Array<AnimatedNode> = [],\n  depth: number = 0,\n): Array<AnimatedNode> {\n  if (depth >= MAX_DEPTH) {\n    return nodes;\n  }\n  if (value instanceof AnimatedNode) {\n    nodes.push(value);\n  } else if (Array.isArray(value)) {\n    for (let ii = 0, length = value.length; ii < length; ii++) {\n      const element = value[ii];\n      flatAnimatedNodes(element, nodes, depth + 1);\n    }\n  } else if (isPlainObject(value)) {\n    const keys = Object.keys(value);\n    for (let ii = 0, length = keys.length; ii < length; ii++) {\n      const key = keys[ii];\n      flatAnimatedNodes(value[key], nodes, depth + 1);\n    }\n  }\n  return nodes;\n}\n\n// Returns a copy of value with a transformation fn applied to any AnimatedNodes\nfunction mapAnimatedNodes(value: any, fn: any => any, depth: number = 0): any {\n  if (depth >= MAX_DEPTH) {\n    return value;\n  }\n\n  if (value instanceof AnimatedNode) {\n    return fn(value);\n  } else if (Array.isArray(value)) {\n    return value.map(element => mapAnimatedNodes(element, fn, depth + 1));\n  } else if (isPlainObject(value)) {\n    const result: {[string]: any} = {};\n    const keys = Object.keys(value);\n    for (let ii = 0, length = keys.length; ii < length; ii++) {\n      const key = keys[ii];\n      result[key] = mapAnimatedNodes(value[key], fn, depth + 1);\n    }\n    return result;\n  } else {\n    return value;\n  }\n}\n\nexport default class AnimatedObject extends AnimatedWithChildren {\n  #nodes: $ReadOnlyArray<AnimatedNode>;\n  _value: mixed;\n\n  /**\n   * Creates an `AnimatedObject` if `value` contains `AnimatedNode` instances.\n   * Otherwise, returns `null`.\n   */\n  static from(value: mixed): ?AnimatedObject {\n    const nodes = flatAnimatedNodes(value);\n    if (nodes.length === 0) {\n      return null;\n    }\n    return new AnimatedObject(nodes, value);\n  }\n\n  /**\n   * Should only be called by `AnimatedObject.from`.\n   */\n  constructor(\n    nodes: $ReadOnlyArray<AnimatedNode>,\n    value: mixed,\n    config?: ?AnimatedNodeConfig,\n  ) {\n    super(config);\n    this.#nodes = nodes;\n    this._value = value;\n  }\n\n  __getValue(): any {\n    return mapAnimatedNodes(this._value, node => {\n      return node.__getValue();\n    });\n  }\n\n  __getValueWithStaticObject(staticObject: mixed): any {\n    const nodes = this.#nodes;\n    let index = 0;\n    // NOTE: We can depend on `this._value` and `staticObject` sharing a\n    // structure because of `useAnimatedPropsMemo`.\n    return mapAnimatedNodes(staticObject, () => nodes[index++].__getValue());\n  }\n\n  __getAnimatedValue(): any {\n    return mapAnimatedNodes(this._value, node => {\n      return node.__getAnimatedValue();\n    });\n  }\n\n  __attach(): void {\n    const nodes = this.#nodes;\n    for (let ii = 0, length = nodes.length; ii < length; ii++) {\n      const node = nodes[ii];\n      node.__addChild(this);\n    }\n    super.__attach();\n  }\n\n  __detach(): void {\n    const nodes = this.#nodes;\n    for (let ii = 0, length = nodes.length; ii < length; ii++) {\n      const node = nodes[ii];\n      node.__removeChild(this);\n    }\n    super.__detach();\n  }\n\n  __makeNative(platformConfig: ?PlatformConfig): void {\n    const nodes = this.#nodes;\n    for (let ii = 0, length = nodes.length; ii < length; ii++) {\n      const node = nodes[ii];\n      node.__makeNative(platformConfig);\n    }\n    super.__makeNative(platformConfig);\n  }\n\n  __getNativeConfig(): any {\n    return {\n      type: 'object',\n      value: mapAnimatedNodes(this._value, node => {\n        return {nodeTag: node.__getNativeTag()};\n      }),\n      debugID: this.__getDebugID(),\n    };\n  }\n}\n"], "mappings": "AAWA,YAAY;;AAAC,IAAAA,sBAAA,GAAAC,OAAA;AAAAC,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,KAAA;AAAA;AAAAD,OAAA,CAAAE,OAAA;AAAAF,OAAA,CAAAG,aAAA,GAAAA,aAAA;AAAA,IAAAC,gBAAA,GAAAR,sBAAA,CAAAC,OAAA;AAAA,IAAAQ,aAAA,GAAAT,sBAAA,CAAAC,OAAA;AAAA,IAAAS,2BAAA,GAAAV,sBAAA,CAAAC,OAAA;AAAA,IAAAU,gBAAA,GAAAX,sBAAA,CAAAC,OAAA;AAAA,IAAAW,KAAA,GAAAZ,sBAAA,CAAAC,OAAA;AAAA,IAAAY,UAAA,GAAAb,sBAAA,CAAAC,OAAA;AAAA,IAAAa,4BAAA,GAAAd,sBAAA,CAAAC,OAAA;AAAA,IAAAc,2BAAA,GAAAf,sBAAA,CAAAC,OAAA;AAKb,IAAAe,aAAA,GAAAhB,sBAAA,CAAAC,OAAA;AACA,IAAAgB,sBAAA,GAAAjB,sBAAA,CAAAC,OAAA;AACA,IAAAiB,KAAA,GAAAC,uBAAA,CAAAlB,OAAA;AAA+B,SAAAmB,yBAAAC,CAAA,6BAAAC,OAAA,mBAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,yBAAAC,CAAA,WAAAA,CAAA,GAAAG,CAAA,GAAAD,CAAA,KAAAF,CAAA;AAAA,SAAAF,wBAAAE,CAAA,EAAAE,CAAA,SAAAA,CAAA,IAAAF,CAAA,IAAAA,CAAA,CAAAI,UAAA,SAAAJ,CAAA,eAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,WAAAf,OAAA,EAAAe,CAAA,QAAAG,CAAA,GAAAJ,wBAAA,CAAAG,CAAA,OAAAC,CAAA,IAAAA,CAAA,CAAAE,GAAA,CAAAL,CAAA,UAAAG,CAAA,CAAAG,GAAA,CAAAN,CAAA,OAAAO,CAAA,KAAAC,SAAA,UAAAC,CAAA,GAAA5B,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAA6B,wBAAA,WAAAC,CAAA,IAAAX,CAAA,oBAAAW,CAAA,OAAAC,cAAA,CAAAC,IAAA,CAAAb,CAAA,EAAAW,CAAA,SAAAG,CAAA,GAAAL,CAAA,GAAA5B,MAAA,CAAA6B,wBAAA,CAAAV,CAAA,EAAAW,CAAA,UAAAG,CAAA,KAAAA,CAAA,CAAAR,GAAA,IAAAQ,CAAA,CAAAC,GAAA,IAAAlC,MAAA,CAAAC,cAAA,CAAAyB,CAAA,EAAAI,CAAA,EAAAG,CAAA,IAAAP,CAAA,CAAAI,CAAA,IAAAX,CAAA,CAAAW,CAAA,YAAAJ,CAAA,CAAAtB,OAAA,GAAAe,CAAA,EAAAG,CAAA,IAAAA,CAAA,CAAAY,GAAA,CAAAf,CAAA,EAAAO,CAAA,GAAAA,CAAA;AAAA,SAAAS,WAAAb,CAAA,EAAAc,CAAA,EAAAjB,CAAA,WAAAiB,CAAA,OAAA3B,gBAAA,CAAAL,OAAA,EAAAgC,CAAA,OAAA5B,2BAAA,CAAAJ,OAAA,EAAAkB,CAAA,EAAAe,yBAAA,KAAAC,OAAA,CAAAC,SAAA,CAAAH,CAAA,EAAAjB,CAAA,YAAAV,gBAAA,CAAAL,OAAA,EAAAkB,CAAA,EAAAkB,WAAA,IAAAJ,CAAA,CAAAK,KAAA,CAAAnB,CAAA,EAAAH,CAAA;AAAA,SAAAkB,0BAAA,cAAAf,CAAA,IAAAoB,OAAA,CAAAC,SAAA,CAAAC,OAAA,CAAAZ,IAAA,CAAAM,OAAA,CAAAC,SAAA,CAAAG,OAAA,iCAAApB,CAAA,aAAAe,yBAAA,YAAAA,0BAAA,aAAAf,CAAA;AAAA,SAAAuB,cAAAvB,CAAA,EAAAc,CAAA,EAAAjB,CAAA,EAAAE,CAAA,QAAAyB,CAAA,OAAApC,KAAA,CAAAN,OAAA,MAAAK,gBAAA,CAAAL,OAAA,MAAAiB,CAAA,GAAAC,CAAA,CAAAqB,SAAA,GAAArB,CAAA,GAAAc,CAAA,EAAAjB,CAAA,cAAAE,CAAA,yBAAAyB,CAAA,aAAAxB,CAAA,WAAAwB,CAAA,CAAAL,KAAA,CAAAtB,CAAA,EAAAG,CAAA,OAAAwB,CAAA;AAE/B,IAAMC,SAAS,GAAG,CAAC;AAEZ,SAAS1C,aAAaA,CAC3BF,KAAY,EAG2B;EACvC,OACEA,KAAK,KAAK,IAAI,IACd,OAAOA,KAAK,KAAK,QAAQ,IACzBH,MAAM,CAACgD,cAAc,CAAC7C,KAAK,CAAC,CAAC8C,aAAa,CAACjD,MAAM,CAAC,IAClD,CAACgB,KAAK,CAACkC,cAAc,CAAC/C,KAAK,CAAC;AAEhC;AAEA,SAASgD,iBAAiBA,CACxBhD,KAAY,EAGS;EAAA,IAFrBiD,KAA0B,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,EAAE;EAAA,IAC/BG,KAAa,GAAAH,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC;EAEjB,IAAIG,KAAK,IAAIT,SAAS,EAAE;IACtB,OAAOK,KAAK;EACd;EACA,IAAIjD,KAAK,YAAYsD,qBAAY,EAAE;IACjCL,KAAK,CAACM,IAAI,CAACvD,KAAK,CAAC;EACnB,CAAC,MAAM,IAAIwD,KAAK,CAACC,OAAO,CAACzD,KAAK,CAAC,EAAE;IAC/B,KAAK,IAAI0D,EAAE,GAAG,CAAC,EAAEP,MAAM,GAAGnD,KAAK,CAACmD,MAAM,EAAEO,EAAE,GAAGP,MAAM,EAAEO,EAAE,EAAE,EAAE;MACzD,IAAMC,OAAO,GAAG3D,KAAK,CAAC0D,EAAE,CAAC;MACzBV,iBAAiB,CAACW,OAAO,EAAEV,KAAK,EAAEI,KAAK,GAAG,CAAC,CAAC;IAC9C;EACF,CAAC,MAAM,IAAInD,aAAa,CAACF,KAAK,CAAC,EAAE;IAC/B,IAAM4D,IAAI,GAAG/D,MAAM,CAAC+D,IAAI,CAAC5D,KAAK,CAAC;IAC/B,KAAK,IAAI0D,GAAE,GAAG,CAAC,EAAEP,OAAM,GAAGS,IAAI,CAACT,MAAM,EAAEO,GAAE,GAAGP,OAAM,EAAEO,GAAE,EAAE,EAAE;MACxD,IAAMG,GAAG,GAAGD,IAAI,CAACF,GAAE,CAAC;MACpBV,iBAAiB,CAAChD,KAAK,CAAC6D,GAAG,CAAC,EAAEZ,KAAK,EAAEI,KAAK,GAAG,CAAC,CAAC;IACjD;EACF;EACA,OAAOJ,KAAK;AACd;AAGA,SAASa,gBAAgBA,CAAC9D,KAAU,EAAE+D,EAAc,EAA0B;EAAA,IAAxBV,KAAa,GAAAH,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC;EACrE,IAAIG,KAAK,IAAIT,SAAS,EAAE;IACtB,OAAO5C,KAAK;EACd;EAEA,IAAIA,KAAK,YAAYsD,qBAAY,EAAE;IACjC,OAAOS,EAAE,CAAC/D,KAAK,CAAC;EAClB,CAAC,MAAM,IAAIwD,KAAK,CAACC,OAAO,CAACzD,KAAK,CAAC,EAAE;IAC/B,OAAOA,KAAK,CAACgE,GAAG,CAAC,UAAAL,OAAO;MAAA,OAAIG,gBAAgB,CAACH,OAAO,EAAEI,EAAE,EAAEV,KAAK,GAAG,CAAC,CAAC;IAAA,EAAC;EACvE,CAAC,MAAM,IAAInD,aAAa,CAACF,KAAK,CAAC,EAAE;IAC/B,IAAMiE,MAAuB,GAAG,CAAC,CAAC;IAClC,IAAML,IAAI,GAAG/D,MAAM,CAAC+D,IAAI,CAAC5D,KAAK,CAAC;IAC/B,KAAK,IAAI0D,EAAE,GAAG,CAAC,EAAEP,MAAM,GAAGS,IAAI,CAACT,MAAM,EAAEO,EAAE,GAAGP,MAAM,EAAEO,EAAE,EAAE,EAAE;MACxD,IAAMG,GAAG,GAAGD,IAAI,CAACF,EAAE,CAAC;MACpBO,MAAM,CAACJ,GAAG,CAAC,GAAGC,gBAAgB,CAAC9D,KAAK,CAAC6D,GAAG,CAAC,EAAEE,EAAE,EAAEV,KAAK,GAAG,CAAC,CAAC;IAC3D;IACA,OAAOY,MAAM;EACf,CAAC,MAAM;IACL,OAAOjE,KAAK;EACd;AACF;AAAC,IAAAkE,MAAA,OAAAxD,2BAAA,CAAAT,OAAA;AAAA,IAEoBkE,cAAc,GAAApE,OAAA,CAAAE,OAAA,aAAAmE,qBAAA;EAmBjC,SAAAD,eACElB,KAAmC,EACnCjD,KAAY,EACZqE,MAA4B,EAC5B;IAAA,IAAAC,KAAA;IAAA,IAAAnE,gBAAA,CAAAF,OAAA,QAAAkE,cAAA;IACAG,KAAA,GAAAtC,UAAA,OAAAmC,cAAA,GAAME,MAAM;IAAExE,MAAA,CAAAC,cAAA,CAAAwE,KAAA,EAAAJ,MAAA;MAAAK,QAAA;MAAAvE,KAAA;IAAA;IACd,IAAAS,4BAAA,CAAAR,OAAA,EAAAqE,KAAA,EAAAJ,MAAA,EAAAA,MAAA,IAAcjB,KAAK;IACnBqB,KAAA,CAAKE,MAAM,GAAGxE,KAAK;IAAC,OAAAsE,KAAA;EACtB;EAAC,IAAA9D,UAAA,CAAAP,OAAA,EAAAkE,cAAA,EAAAC,qBAAA;EAAA,WAAAhE,aAAA,CAAAH,OAAA,EAAAkE,cAAA;IAAAN,GAAA;IAAA7D,KAAA,EAED,SAAAyE,UAAUA,CAAA,EAAQ;MAChB,OAAOX,gBAAgB,CAAC,IAAI,CAACU,MAAM,EAAE,UAAAE,IAAI,EAAI;QAC3C,OAAOA,IAAI,CAACD,UAAU,CAAC,CAAC;MAC1B,CAAC,CAAC;IACJ;EAAC;IAAAZ,GAAA;IAAA7D,KAAA,EAED,SAAA2E,0BAA0BA,CAACC,YAAmB,EAAO;MACnD,IAAM3B,KAAK,OAAAxC,4BAAA,CAAAR,OAAA,EAAG,IAAI,EAAAiE,MAAA,EAAAA,MAAA,CAAO;MACzB,IAAIW,KAAK,GAAG,CAAC;MAGb,OAAOf,gBAAgB,CAACc,YAAY,EAAE;QAAA,OAAM3B,KAAK,CAAC4B,KAAK,EAAE,CAAC,CAACJ,UAAU,CAAC,CAAC;MAAA,EAAC;IAC1E;EAAC;IAAAZ,GAAA;IAAA7D,KAAA,EAED,SAAA8E,kBAAkBA,CAAA,EAAQ;MACxB,OAAOhB,gBAAgB,CAAC,IAAI,CAACU,MAAM,EAAE,UAAAE,IAAI,EAAI;QAC3C,OAAOA,IAAI,CAACI,kBAAkB,CAAC,CAAC;MAClC,CAAC,CAAC;IACJ;EAAC;IAAAjB,GAAA;IAAA7D,KAAA,EAED,SAAA+E,QAAQA,CAAA,EAAS;MACf,IAAM9B,KAAK,OAAAxC,4BAAA,CAAAR,OAAA,EAAG,IAAI,EAAAiE,MAAA,EAAAA,MAAA,CAAO;MACzB,KAAK,IAAIR,EAAE,GAAG,CAAC,EAAEP,MAAM,GAAGF,KAAK,CAACE,MAAM,EAAEO,EAAE,GAAGP,MAAM,EAAEO,EAAE,EAAE,EAAE;QACzD,IAAMgB,IAAI,GAAGzB,KAAK,CAACS,EAAE,CAAC;QACtBgB,IAAI,CAACM,UAAU,CAAC,IAAI,CAAC;MACvB;MACAtC,aAAA,CAAAyB,cAAA;IACF;EAAC;IAAAN,GAAA;IAAA7D,KAAA,EAED,SAAAiF,QAAQA,CAAA,EAAS;MACf,IAAMhC,KAAK,OAAAxC,4BAAA,CAAAR,OAAA,EAAG,IAAI,EAAAiE,MAAA,EAAAA,MAAA,CAAO;MACzB,KAAK,IAAIR,EAAE,GAAG,CAAC,EAAEP,MAAM,GAAGF,KAAK,CAACE,MAAM,EAAEO,EAAE,GAAGP,MAAM,EAAEO,EAAE,EAAE,EAAE;QACzD,IAAMgB,IAAI,GAAGzB,KAAK,CAACS,EAAE,CAAC;QACtBgB,IAAI,CAACQ,aAAa,CAAC,IAAI,CAAC;MAC1B;MACAxC,aAAA,CAAAyB,cAAA;IACF;EAAC;IAAAN,GAAA;IAAA7D,KAAA,EAED,SAAAmF,YAAYA,CAACC,cAA+B,EAAQ;MAClD,IAAMnC,KAAK,OAAAxC,4BAAA,CAAAR,OAAA,EAAG,IAAI,EAAAiE,MAAA,EAAAA,MAAA,CAAO;MACzB,KAAK,IAAIR,EAAE,GAAG,CAAC,EAAEP,MAAM,GAAGF,KAAK,CAACE,MAAM,EAAEO,EAAE,GAAGP,MAAM,EAAEO,EAAE,EAAE,EAAE;QACzD,IAAMgB,IAAI,GAAGzB,KAAK,CAACS,EAAE,CAAC;QACtBgB,IAAI,CAACS,YAAY,CAACC,cAAc,CAAC;MACnC;MACA1C,aAAA,CAAAyB,cAAA,4BAAmBiB,cAAc;IACnC;EAAC;IAAAvB,GAAA;IAAA7D,KAAA,EAED,SAAAqF,iBAAiBA,CAAA,EAAQ;MACvB,OAAO;QACLC,IAAI,EAAE,QAAQ;QACdtF,KAAK,EAAE8D,gBAAgB,CAAC,IAAI,CAACU,MAAM,EAAE,UAAAE,IAAI,EAAI;UAC3C,OAAO;YAACa,OAAO,EAAEb,IAAI,CAACc,cAAc,CAAC;UAAC,CAAC;QACzC,CAAC,CAAC;QACFC,OAAO,EAAE,IAAI,CAACC,YAAY,CAAC;MAC7B,CAAC;IACH;EAAC;IAAA7B,GAAA;IAAA7D,KAAA,EA5ED,SAAO2F,IAAIA,CAAC3F,KAAY,EAAmB;MACzC,IAAMiD,KAAK,GAAGD,iBAAiB,CAAChD,KAAK,CAAC;MACtC,IAAIiD,KAAK,CAACE,MAAM,KAAK,CAAC,EAAE;QACtB,OAAO,IAAI;MACb;MACA,OAAO,IAAIgB,cAAc,CAAClB,KAAK,EAAEjD,KAAK,CAAC;IACzC;EAAC;AAAA,EAdyC4F,8BAAoB", "ignoreList": []}