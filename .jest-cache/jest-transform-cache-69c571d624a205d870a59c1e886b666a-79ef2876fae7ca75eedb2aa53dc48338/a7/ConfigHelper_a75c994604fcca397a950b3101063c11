d8c54a95b5a8f97a27c69f1ec2e39715
'use strict';

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.adaptViewConfig = adaptViewConfig;
exports.addWhitelistedNativeProps = addWhitelistedNativeProps;
exports.addWhitelistedUIProps = addWhitelistedUIProps;
exports.configureProps = configureProps;
exports.configureReanimatedLogger = configureReanimatedLogger;
var _propsAllowlists = require("./propsAllowlists.js");
var _core = require("./core.js");
var _errors = require("./errors.js");
var _index = require("./logger/index.js");
var _PlatformChecker = require("./PlatformChecker.js");
var SHOULD_BE_USE_WEB = (0, _PlatformChecker.shouldBeUseWeb)();
function assertNoOverlapInLists() {
  for (var key in _propsAllowlists.PropsAllowlists.NATIVE_THREAD_PROPS_WHITELIST) {
    if (key in _propsAllowlists.PropsAllowlists.UI_THREAD_PROPS_WHITELIST) {
      throw new _errors.ReanimatedError(`Property \`${key}\` was whitelisted both as UI and native prop. Please remove it from one of the lists.`);
    }
  }
}
function configureProps() {
  assertNoOverlapInLists();
  (0, _core.jsiConfigureProps)(Object.keys(_propsAllowlists.PropsAllowlists.UI_THREAD_PROPS_WHITELIST), Object.keys(_propsAllowlists.PropsAllowlists.NATIVE_THREAD_PROPS_WHITELIST));
}
function addWhitelistedNativeProps(props) {
  var oldSize = Object.keys(_propsAllowlists.PropsAllowlists.NATIVE_THREAD_PROPS_WHITELIST).length;
  _propsAllowlists.PropsAllowlists.NATIVE_THREAD_PROPS_WHITELIST = Object.assign({}, _propsAllowlists.PropsAllowlists.NATIVE_THREAD_PROPS_WHITELIST, props);
  if (oldSize !== Object.keys(_propsAllowlists.PropsAllowlists.NATIVE_THREAD_PROPS_WHITELIST).length) {
    configureProps();
  }
}
function addWhitelistedUIProps(props) {
  var oldSize = Object.keys(_propsAllowlists.PropsAllowlists.UI_THREAD_PROPS_WHITELIST).length;
  _propsAllowlists.PropsAllowlists.UI_THREAD_PROPS_WHITELIST = Object.assign({}, _propsAllowlists.PropsAllowlists.UI_THREAD_PROPS_WHITELIST, props);
  if (oldSize !== Object.keys(_propsAllowlists.PropsAllowlists.UI_THREAD_PROPS_WHITELIST).length) {
    configureProps();
  }
}
function configureReanimatedLogger(config) {
  (0, _index.updateLoggerConfig)(config);
  if (!SHOULD_BE_USE_WEB) {
    (0, _core.executeOnUIRuntimeSync)(_index.updateLoggerConfig)(config);
  }
}
var PROCESSED_VIEW_NAMES = new Set();
function adaptViewConfig(viewConfig) {
  var viewName = viewConfig.uiViewClassName;
  var props = viewConfig.validAttributes;
  if (!PROCESSED_VIEW_NAMES.has(viewName)) {
    var propsToAdd = {};
    Object.keys(props).forEach(function (key) {
      if (!(key in _propsAllowlists.PropsAllowlists.NATIVE_THREAD_PROPS_WHITELIST) && !(key in _propsAllowlists.PropsAllowlists.UI_THREAD_PROPS_WHITELIST)) {
        propsToAdd[key] = true;
      }
    });
    addWhitelistedUIProps(propsToAdd);
    PROCESSED_VIEW_NAMES.add(viewName);
  }
}
configureProps();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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