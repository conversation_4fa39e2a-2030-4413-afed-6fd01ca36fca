0b14f55a83ee51818fdf00e0df6b4d4a
"use strict";

/* istanbul ignore next */
function cov_hqubabn7h() {
  var path = "/Users/<USER>/projects/react-native/payment-module/src/data/datasources/remote/PaymentRemoteDataSource.ts";
  var hash = "329a843740cff50684c9e56d34078626ff4ef386";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/projects/react-native/payment-module/src/data/datasources/remote/PaymentRemoteDataSource.ts",
    statementMap: {
      "0": {
        start: {
          line: 3,
          column: 29
        },
        end: {
          line: 3,
          column: 84
        }
      },
      "1": {
        start: {
          line: 4,
          column: 25
        },
        end: {
          line: 4,
          column: 99
        }
      },
      "2": {
        start: {
          line: 5,
          column: 23
        },
        end: {
          line: 5,
          column: 95
        }
      },
      "3": {
        start: {
          line: 6,
          column: 20
        },
        end: {
          line: 6,
          column: 89
        }
      },
      "4": {
        start: {
          line: 7,
          column: 0
        },
        end: {
          line: 9,
          column: 3
        }
      },
      "5": {
        start: {
          line: 10,
          column: 0
        },
        end: {
          line: 10,
          column: 41
        }
      },
      "6": {
        start: {
          line: 11,
          column: 21
        },
        end: {
          line: 11,
          column: 59
        }
      },
      "7": {
        start: {
          line: 12,
          column: 24
        },
        end: {
          line: 12,
          column: 65
        }
      },
      "8": {
        start: {
          line: 13,
          column: 23
        },
        end: {
          line: 13,
          column: 62
        }
      },
      "9": {
        start: {
          line: 14,
          column: 30
        },
        end: {
          line: 40,
          column: 3
        }
      },
      "10": {
        start: {
          line: 16,
          column: 4
        },
        end: {
          line: 16,
          column: 65
        }
      },
      "11": {
        start: {
          line: 17,
          column: 4
        },
        end: {
          line: 17,
          column: 33
        }
      },
      "12": {
        start: {
          line: 19,
          column: 2
        },
        end: {
          line: 39,
          column: 6
        }
      },
      "13": {
        start: {
          line: 22,
          column: 22
        },
        end: {
          line: 33,
          column: 8
        }
      },
      "14": {
        start: {
          line: 23,
          column: 8
        },
        end: {
          line: 32,
          column: 9
        }
      },
      "15": {
        start: {
          line: 24,
          column: 20
        },
        end: {
          line: 24,
          column: 66
        }
      },
      "16": {
        start: {
          line: 25,
          column: 25
        },
        end: {
          line: 25,
          column: 65
        }
      },
      "17": {
        start: {
          line: 26,
          column: 10
        },
        end: {
          line: 26,
          column: 65
        }
      },
      "18": {
        start: {
          line: 28,
          column: 10
        },
        end: {
          line: 30,
          column: 11
        }
      },
      "19": {
        start: {
          line: 29,
          column: 12
        },
        end: {
          line: 29,
          column: 24
        }
      },
      "20": {
        start: {
          line: 31,
          column: 10
        },
        end: {
          line: 31,
          column: 52
        }
      },
      "21": {
        start: {
          line: 35,
          column: 8
        },
        end: {
          line: 35,
          column: 48
        }
      },
      "22": {
        start: {
          line: 37,
          column: 6
        },
        end: {
          line: 37,
          column: 22
        }
      },
      "23": {
        start: {
          line: 41,
          column: 0
        },
        end: {
          line: 41,
          column: 58
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 14,
            column: 30
          },
          end: {
            line: 14,
            column: 31
          }
        },
        loc: {
          start: {
            line: 14,
            column: 42
          },
          end: {
            line: 40,
            column: 1
          }
        },
        line: 14
      },
      "1": {
        name: "PaymentRemoteDataSource",
        decl: {
          start: {
            line: 15,
            column: 11
          },
          end: {
            line: 15,
            column: 34
          }
        },
        loc: {
          start: {
            line: 15,
            column: 47
          },
          end: {
            line: 18,
            column: 3
          }
        },
        line: 15
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 21,
            column: 11
          },
          end: {
            line: 21,
            column: 12
          }
        },
        loc: {
          start: {
            line: 21,
            column: 23
          },
          end: {
            line: 38,
            column: 5
          }
        },
        line: 21
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 22,
            column: 54
          },
          end: {
            line: 22,
            column: 55
          }
        },
        loc: {
          start: {
            line: 22,
            column: 74
          },
          end: {
            line: 33,
            column: 7
          }
        },
        line: 22
      },
      "4": {
        name: "validate",
        decl: {
          start: {
            line: 34,
            column: 15
          },
          end: {
            line: 34,
            column: 23
          }
        },
        loc: {
          start: {
            line: 34,
            column: 28
          },
          end: {
            line: 36,
            column: 7
          }
        },
        line: 34
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 28,
            column: 10
          },
          end: {
            line: 30,
            column: 11
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 28,
            column: 10
          },
          end: {
            line: 30,
            column: 11
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 28
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0
    },
    b: {
      "0": [0, 0]
    },
    inputSourceMap: {
      version: 3,
      file: undefined,
      names: ["PathResolver_1", "require", "ResponseHandler_1", "MSBCustomError_1", "PaymentRemoteDataSource", "httpClient", "_classCallCheck2", "default", "_createClass2", "key", "value", "_validate", "_asyncToGenerator2", "request", "url", "PathResolver", "payment", "validate", "response", "post", "handleResponse", "error", "CustomError", "createError", "_x", "apply", "arguments", "exports"],
      sourceRoot: undefined,
      sources: ["/Users/<USER>/projects/react-native/payment-module/src/data/datasources/remote/PaymentRemoteDataSource.ts"],
      sourcesContent: ["import {BaseResponse} from '../../../core/BaseResponse';\nimport {PathResolver} from '../../../utils/PathResolver';\nimport {IHttpClient} from 'msb-host-shared-module';\nimport {handleResponse} from '../../../utils/ResponseHandler';\nimport {ValidateResponse} from '../../models/validate/ValidateResponse';\nimport {ValidateRequest} from '../../models/validate/ValidateRequest';\nimport {IPaymentDataSource} from '../IPaymentDataSource';\nimport {createError, CustomError} from '../../../core/MSBCustomError';\n\nexport class PaymentRemoteDataSource implements IPaymentDataSource {\n  constructor(private httpClient: IHttpClient) {}\n\n  async validate(request: ValidateRequest): Promise<BaseResponse<ValidateResponse>> {\n    try {\n      const url = PathResolver.payment.validate();\n      const response = await this.httpClient.post(url, request);\n      return handleResponse(response);\n    } catch (error: any) {\n      if (error instanceof CustomError) {\n        throw error;\n      }\n      throw createError();\n    }\n  }\n}\n"],
      mappings: ";;;;;;;;;;AACA,IAAAA,cAAA,GAAAC,OAAA;AAEA,IAAAC,iBAAA,GAAAD,OAAA;AAIA,IAAAE,gBAAA,GAAAF,OAAA;AAAsE,IAEzDG,uBAAuB;EAClC,SAAAA,wBAAoBC,UAAuB;IAAA,IAAAC,gBAAA,CAAAC,OAAA,QAAAH,uBAAA;IAAvB,KAAAC,UAAU,GAAVA,UAAU;EAAgB;EAAC,WAAAG,aAAA,CAAAD,OAAA,EAAAH,uBAAA;IAAAK,GAAA;IAAAC,KAAA;MAAA,IAAAC,SAAA,OAAAC,kBAAA,CAAAL,OAAA,EAE/C,WAAeM,OAAwB;QACrC,IAAI;UACF,IAAMC,GAAG,GAAGd,cAAA,CAAAe,YAAY,CAACC,OAAO,CAACC,QAAQ,EAAE;UAC3C,IAAMC,QAAQ,SAAS,IAAI,CAACb,UAAU,CAACc,IAAI,CAACL,GAAG,EAAED,OAAO,CAAC;UACzD,OAAO,IAAAX,iBAAA,CAAAkB,cAAc,EAACF,QAAQ,CAAC;QACjC,CAAC,CAAC,OAAOG,KAAU,EAAE;UACnB,IAAIA,KAAK,YAAYlB,gBAAA,CAAAmB,WAAW,EAAE;YAChC,MAAMD,KAAK;UACb;UACA,MAAM,IAAAlB,gBAAA,CAAAoB,WAAW,GAAE;QACrB;MACF,CAAC;MAAA,SAXKN,QAAQA,CAAAO,EAAA;QAAA,OAAAb,SAAA,CAAAc,KAAA,OAAAC,SAAA;MAAA;MAAA,OAART,QAAQ;IAAA;EAAA;AAAA;AAHhBU,OAAA,CAAAvB,uBAAA,GAAAA,uBAAA",
      ignoreList: []
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "329a843740cff50684c9e56d34078626ff4ef386"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_hqubabn7h = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_hqubabn7h();
var _interopRequireDefault =
/* istanbul ignore next */
(cov_hqubabn7h().s[0]++, require("@babel/runtime/helpers/interopRequireDefault"));
var _asyncToGenerator2 =
/* istanbul ignore next */
(cov_hqubabn7h().s[1]++, _interopRequireDefault(require("@babel/runtime/helpers/asyncToGenerator")));
var _classCallCheck2 =
/* istanbul ignore next */
(cov_hqubabn7h().s[2]++, _interopRequireDefault(require("@babel/runtime/helpers/classCallCheck")));
var _createClass2 =
/* istanbul ignore next */
(cov_hqubabn7h().s[3]++, _interopRequireDefault(require("@babel/runtime/helpers/createClass")));
/* istanbul ignore next */
cov_hqubabn7h().s[4]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_hqubabn7h().s[5]++;
exports.PaymentRemoteDataSource = void 0;
var PathResolver_1 =
/* istanbul ignore next */
(cov_hqubabn7h().s[6]++, require("../../../utils/PathResolver"));
var ResponseHandler_1 =
/* istanbul ignore next */
(cov_hqubabn7h().s[7]++, require("../../../utils/ResponseHandler"));
var MSBCustomError_1 =
/* istanbul ignore next */
(cov_hqubabn7h().s[8]++, require("../../../core/MSBCustomError"));
var PaymentRemoteDataSource =
/* istanbul ignore next */
(cov_hqubabn7h().s[9]++, function () {
  /* istanbul ignore next */
  cov_hqubabn7h().f[0]++;
  function PaymentRemoteDataSource(httpClient) {
    /* istanbul ignore next */
    cov_hqubabn7h().f[1]++;
    cov_hqubabn7h().s[10]++;
    (0, _classCallCheck2.default)(this, PaymentRemoteDataSource);
    /* istanbul ignore next */
    cov_hqubabn7h().s[11]++;
    this.httpClient = httpClient;
  }
  /* istanbul ignore next */
  cov_hqubabn7h().s[12]++;
  return (0, _createClass2.default)(PaymentRemoteDataSource, [{
    key: "validate",
    value: function () {
      /* istanbul ignore next */
      cov_hqubabn7h().f[2]++;
      var _validate =
      /* istanbul ignore next */
      (cov_hqubabn7h().s[13]++, (0, _asyncToGenerator2.default)(function* (request) {
        /* istanbul ignore next */
        cov_hqubabn7h().f[3]++;
        cov_hqubabn7h().s[14]++;
        try {
          var url =
          /* istanbul ignore next */
          (cov_hqubabn7h().s[15]++, PathResolver_1.PathResolver.payment.validate());
          var response =
          /* istanbul ignore next */
          (cov_hqubabn7h().s[16]++, yield this.httpClient.post(url, request));
          /* istanbul ignore next */
          cov_hqubabn7h().s[17]++;
          return (0, ResponseHandler_1.handleResponse)(response);
        } catch (error) {
          /* istanbul ignore next */
          cov_hqubabn7h().s[18]++;
          if (error instanceof MSBCustomError_1.CustomError) {
            /* istanbul ignore next */
            cov_hqubabn7h().b[0][0]++;
            cov_hqubabn7h().s[19]++;
            throw error;
          } else
          /* istanbul ignore next */
          {
            cov_hqubabn7h().b[0][1]++;
          }
          cov_hqubabn7h().s[20]++;
          throw (0, MSBCustomError_1.createError)();
        }
      }));
      function validate(_x) {
        /* istanbul ignore next */
        cov_hqubabn7h().f[4]++;
        cov_hqubabn7h().s[21]++;
        return _validate.apply(this, arguments);
      }
      /* istanbul ignore next */
      cov_hqubabn7h().s[22]++;
      return validate;
    }()
  }]);
}());
/* istanbul ignore next */
cov_hqubabn7h().s[23]++;
exports.PaymentRemoteDataSource = PaymentRemoteDataSource;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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