5492e135b94271a35387c556c0ebf568
"use strict";

/* istanbul ignore next */
function cov_1bu50qtciu() {
  var path = "/Users/<USER>/projects/react-native/payment-module/src/presentation/payment-result/styles.ts";
  var hash = "5bc5e387f7cb9ff59ce0619cf4c6dc7564e1cdf8";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/projects/react-native/payment-module/src/presentation/payment-result/styles.ts",
    statementMap: {
      "0": {
        start: {
          line: 3,
          column: 22
        },
        end: {
          line: 7,
          column: 1
        }
      },
      "1": {
        start: {
          line: 4,
          column: 2
        },
        end: {
          line: 6,
          column: 4
        }
      },
      "2": {
        start: {
          line: 8,
          column: 0
        },
        end: {
          line: 10,
          column: 3
        }
      },
      "3": {
        start: {
          line: 11,
          column: 0
        },
        end: {
          line: 11,
          column: 27
        }
      },
      "4": {
        start: {
          line: 12,
          column: 29
        },
        end: {
          line: 12,
          column: 60
        }
      },
      "5": {
        start: {
          line: 13,
          column: 23
        },
        end: {
          line: 13,
          column: 77
        }
      },
      "6": {
        start: {
          line: 14,
          column: 0
        },
        end: {
          line: 126,
          column: 3
        }
      },
      "7": {
        start: {
          line: 15,
          column: 18
        },
        end: {
          line: 15,
          column: 32
        }
      },
      "8": {
        start: {
          line: 16,
          column: 17
        },
        end: {
          line: 16,
          column: 32
        }
      },
      "9": {
        start: {
          line: 17,
          column: 20
        },
        end: {
          line: 17,
          column: 38
        }
      },
      "10": {
        start: {
          line: 18,
          column: 13
        },
        end: {
          line: 18,
          column: 24
        }
      },
      "11": {
        start: {
          line: 19,
          column: 17
        },
        end: {
          line: 19,
          column: 32
        }
      },
      "12": {
        start: {
          line: 20,
          column: 2
        },
        end: {
          line: 125,
          column: 4
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 3,
            column: 54
          },
          end: {
            line: 3,
            column: 55
          }
        },
        loc: {
          start: {
            line: 3,
            column: 69
          },
          end: {
            line: 7,
            column: 1
          }
        },
        line: 3
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 14,
            column: 68
          },
          end: {
            line: 14,
            column: 69
          }
        },
        loc: {
          start: {
            line: 14,
            column: 84
          },
          end: {
            line: 126,
            column: 1
          }
        },
        line: 14
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 3,
            column: 22
          },
          end: {
            line: 7,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 3,
            column: 22
          },
          end: {
            line: 3,
            column: 26
          }
        }, {
          start: {
            line: 3,
            column: 30
          },
          end: {
            line: 3,
            column: 50
          }
        }, {
          start: {
            line: 3,
            column: 54
          },
          end: {
            line: 7,
            column: 1
          }
        }],
        line: 3
      },
      "1": {
        loc: {
          start: {
            line: 4,
            column: 9
          },
          end: {
            line: 6,
            column: 3
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 4,
            column: 33
          },
          end: {
            line: 4,
            column: 36
          }
        }, {
          start: {
            line: 4,
            column: 39
          },
          end: {
            line: 6,
            column: 3
          }
        }],
        line: 4
      },
      "2": {
        loc: {
          start: {
            line: 4,
            column: 9
          },
          end: {
            line: 4,
            column: 30
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 4,
            column: 9
          },
          end: {
            line: 4,
            column: 12
          }
        }, {
          start: {
            line: 4,
            column: 16
          },
          end: {
            line: 4,
            column: 30
          }
        }],
        line: 4
      },
      "3": {
        loc: {
          start: {
            line: 91,
            column: 34
          },
          end: {
            line: 91,
            column: 88
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 91,
            column: 55
          },
          end: {
            line: 91,
            column: 61
          }
        }, {
          start: {
            line: 91,
            column: 64
          },
          end: {
            line: 91,
            column: 88
          }
        }],
        line: 91
      },
      "4": {
        loc: {
          start: {
            line: 109,
            column: 31
          },
          end: {
            line: 109,
            column: 85
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 109,
            column: 52
          },
          end: {
            line: 109,
            column: 58
          }
        }, {
          start: {
            line: 109,
            column: 61
          },
          end: {
            line: 109,
            column: 85
          }
        }],
        line: 109
      },
      "5": {
        loc: {
          start: {
            line: 112,
            column: 32
          },
          end: {
            line: 112,
            column: 86
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 112,
            column: 53
          },
          end: {
            line: 112,
            column: 59
          }
        }, {
          start: {
            line: 112,
            column: 62
          },
          end: {
            line: 112,
            column: 86
          }
        }],
        line: 112
      },
      "6": {
        loc: {
          start: {
            line: 116,
            column: 32
          },
          end: {
            line: 116,
            column: 84
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 116,
            column: 53
          },
          end: {
            line: 116,
            column: 59
          }
        }, {
          start: {
            line: 116,
            column: 62
          },
          end: {
            line: 116,
            column: 84
          }
        }],
        line: 116
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0
    },
    f: {
      "0": 0,
      "1": 0
    },
    b: {
      "0": [0, 0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0]
    },
    inputSourceMap: {
      version: 3,
      file: undefined,
      names: ["msb_shared_component_1", "require", "DimensionUtils_1", "__importDefault", "exports", "makeStyle", "createMSBStyleSheet", "_ref", "SizeAlias", "ColorField", "ColorDataView", "Shadow", "Typography", "alignItemsCenter", "alignItems", "btnConfirm", "marginHorizontal", "SpacingXMSmall", "container", "flex", "scrollViewContainer", "paddingHorizontal", "SpacingSmall", "paddingVertical", "Spacing2xSmall", "containerViewshot", "height", "default", "getWindowHeight", "paddingBottom", "width", "getWindowWidth", "justifyContent", "contentContainer", "Object", "assign", "backgroundColor", "ColorGlobal", "NeutralWhite", "borderRadius", "padding", "overflow", "center", "contentContainerView", "contentContainerViewShot", "marginBottom", "opacity", "contentViewShot", "flex1", "icLogo", "marginTop", "SpacingLarge", "resizeMode", "getSize", "imageBackground", "logoShot", "marginTop16", "sender", "flexDirection", "senderName", "base_semiBold", "color", "TextMain", "marginLeft", "SpacingXSmall", "shotView", "left", "position", "zIndex", "transferResultAction", "borderTopColor", "BorderDefault", "borderTopWidth", "txtName", "txtTitle", "small_regular", "TextSub", "Spacing4xSmall", "txtValue", "base_medium", "top16", "accountInfo", "SizeGlobal", "Size500"],
      sourceRoot: undefined,
      sources: ["/Users/<USER>/projects/react-native/payment-module/src/presentation/payment-result/styles.ts"],
      sourcesContent: ["import {ColorGlobal, createMSBStyleSheet, getSize, SizeGlobal} from 'msb-shared-component';\n\nimport DimensionUtils from '../../utils/DimensionUtils';\n\nexport const makeStyle = createMSBStyleSheet(({SizeAlias, ColorField, ColorDataView, Shadow, Typography}) => {\n  return {\n    alignItemsCenter: {\n      alignItems: 'center',\n    },\n    btnConfirm: {\n      marginHorizontal: SizeAlias.SpacingXMSmall,\n    },\n\n    container: {\n      flex: 1,\n      // paddingHorizontal: SizeAlias.SpacingSmall,\n      // paddingVertical: SizeAlias.Spacing2xSmall,\n    },\n    scrollViewContainer: {\n      flex: 1,\n      paddingHorizontal: SizeAlias.SpacingSmall,\n      paddingVertical: SizeAlias.Spacing2xSmall,\n    },\n    containerViewshot: {\n      flex: 1,\n      height: DimensionUtils.getWindowHeight(),\n      paddingBottom: SizeAlias.SpacingSmall,\n      paddingHorizontal: SizeAlias.SpacingSmall,\n      width: DimensionUtils.getWindowWidth(),\n      justifyContent: 'center',\n    },\n    contentContainer: {\n      backgroundColor: ColorGlobal.NeutralWhite,\n      borderRadius: SizeAlias.SpacingXMSmall,\n      padding: SizeAlias.SpacingSmall,\n      overflow: 'hidden',\n      ...Shadow.center,\n    },\n    contentContainerView: {\n      backgroundColor: ColorGlobal.NeutralWhite,\n      borderRadius: SizeAlias.SpacingXMSmall,\n      ...Shadow.center,\n    },\n    // render image shot\n    contentContainerViewShot: {\n      backgroundColor: ColorGlobal.NeutralWhite,\n      borderRadius: SizeAlias.SpacingXMSmall,\n      marginBottom: SizeAlias.SpacingSmall,\n      padding: SizeAlias.SpacingSmall,\n      opacity: 0.9,\n    },\n    contentViewShot: {\n      borderRadius: SizeAlias.SpacingXMSmall,\n    },\n    flex1: {\n      flex: 1,\n    },\n    icLogo: {\n      marginTop: SizeAlias.SpacingLarge,\n      marginBottom: SizeAlias.SpacingSmall,\n      resizeMode: 'stretch',\n      height: getSize(56),\n      width: getSize(156),\n    },\n    imageBackground: {\n      flex: 1,\n      resizeMode: 'stretch',\n    },\n    logoShot: {height: getSize(35), marginBottom: SizeAlias.SpacingLarge, resizeMode: 'contain', width: getSize(150)},\n    marginTop16: {\n      marginTop: SizeAlias.SpacingSmall,\n      backgroundColor: 'transparent',\n    },\n\n    sender: {\n      flexDirection: 'row',\n      alignItems: 'center',\n    },\n    senderName: {\n      ...Typography?.base_semiBold,\n      color: ColorDataView.TextMain,\n      marginLeft: SizeAlias.SpacingXSmall,\n    },\n    shotView: {\n      left: -10000,\n      position: 'absolute',\n      height: DimensionUtils.getWindowHeight(),\n      width: DimensionUtils.getWindowWidth(),\n      zIndex: 1000,\n      justifyContent: 'center',\n      alignItems: 'center',\n    },\n    transferResultAction: {\n      borderTopColor: ColorField.BorderDefault,\n      borderTopWidth: 1,\n      marginTop: SizeAlias.SpacingXSmall,\n    },\n    txtName: {\n      ...Typography?.base_semiBold,\n      color: ColorDataView.TextMain,\n    },\n    txtTitle: {\n      ...Typography?.small_regular,\n      // ...Tpg.base_semiBold,\n      color: ColorDataView.TextSub,\n      marginBottom: SizeAlias.Spacing4xSmall,\n    },\n    txtValue: {\n      ...Typography?.base_medium,\n      color: ColorDataView.TextMain,\n    },\n    top16: {\n      marginTop: SizeAlias.SpacingSmall,\n    },\n    accountInfo: {\n      marginTop: SizeGlobal.Size500,\n    },\n  };\n});\n"],
      mappings: ";;;;;;;;;;;AAAA,IAAAA,sBAAA,GAAAC,OAAA;AAEA,IAAAC,gBAAA,GAAAC,eAAA,CAAAF,OAAA;AAEaG,OAAA,CAAAC,SAAS,GAAG,IAAAL,sBAAA,CAAAM,mBAAmB,EAAC,UAAAC,IAAA,EAA+D;EAAA,IAA7DC,SAAS,GAAAD,IAAA,CAATC,SAAS;IAAEC,UAAU,GAAAF,IAAA,CAAVE,UAAU;IAAEC,aAAa,GAAAH,IAAA,CAAbG,aAAa;IAAEC,MAAM,GAAAJ,IAAA,CAANI,MAAM;IAAEC,UAAU,GAAAL,IAAA,CAAVK,UAAU;EACrG,OAAO;IACLC,gBAAgB,EAAE;MAChBC,UAAU,EAAE;KACb;IACDC,UAAU,EAAE;MACVC,gBAAgB,EAAER,SAAS,CAACS;KAC7B;IAEDC,SAAS,EAAE;MACTC,IAAI,EAAE;KAGP;IACDC,mBAAmB,EAAE;MACnBD,IAAI,EAAE,CAAC;MACPE,iBAAiB,EAAEb,SAAS,CAACc,YAAY;MACzCC,eAAe,EAAEf,SAAS,CAACgB;KAC5B;IACDC,iBAAiB,EAAE;MACjBN,IAAI,EAAE,CAAC;MACPO,MAAM,EAAExB,gBAAA,CAAAyB,OAAc,CAACC,eAAe,EAAE;MACxCC,aAAa,EAAErB,SAAS,CAACc,YAAY;MACrCD,iBAAiB,EAAEb,SAAS,CAACc,YAAY;MACzCQ,KAAK,EAAE5B,gBAAA,CAAAyB,OAAc,CAACI,cAAc,EAAE;MACtCC,cAAc,EAAE;KACjB;IACDC,gBAAgB,EAAAC,MAAA,CAAAC,MAAA;MACdC,eAAe,EAAEpC,sBAAA,CAAAqC,WAAW,CAACC,YAAY;MACzCC,YAAY,EAAE/B,SAAS,CAACS,cAAc;MACtCuB,OAAO,EAAEhC,SAAS,CAACc,YAAY;MAC/BmB,QAAQ,EAAE;IAAQ,GACf9B,MAAM,CAAC+B,MAAM,CACjB;IACDC,oBAAoB,EAAAT,MAAA,CAAAC,MAAA;MAClBC,eAAe,EAAEpC,sBAAA,CAAAqC,WAAW,CAACC,YAAY;MACzCC,YAAY,EAAE/B,SAAS,CAACS;IAAc,GACnCN,MAAM,CAAC+B,MAAM,CACjB;IAEDE,wBAAwB,EAAE;MACxBR,eAAe,EAAEpC,sBAAA,CAAAqC,WAAW,CAACC,YAAY;MACzCC,YAAY,EAAE/B,SAAS,CAACS,cAAc;MACtC4B,YAAY,EAAErC,SAAS,CAACc,YAAY;MACpCkB,OAAO,EAAEhC,SAAS,CAACc,YAAY;MAC/BwB,OAAO,EAAE;KACV;IACDC,eAAe,EAAE;MACfR,YAAY,EAAE/B,SAAS,CAACS;KACzB;IACD+B,KAAK,EAAE;MACL7B,IAAI,EAAE;KACP;IACD8B,MAAM,EAAE;MACNC,SAAS,EAAE1C,SAAS,CAAC2C,YAAY;MACjCN,YAAY,EAAErC,SAAS,CAACc,YAAY;MACpC8B,UAAU,EAAE,SAAS;MACrB1B,MAAM,EAAE,IAAA1B,sBAAA,CAAAqD,OAAO,EAAC,EAAE,CAAC;MACnBvB,KAAK,EAAE,IAAA9B,sBAAA,CAAAqD,OAAO,EAAC,GAAG;KACnB;IACDC,eAAe,EAAE;MACfnC,IAAI,EAAE,CAAC;MACPiC,UAAU,EAAE;KACb;IACDG,QAAQ,EAAE;MAAC7B,MAAM,EAAE,IAAA1B,sBAAA,CAAAqD,OAAO,EAAC,EAAE,CAAC;MAAER,YAAY,EAAErC,SAAS,CAAC2C,YAAY;MAAEC,UAAU,EAAE,SAAS;MAAEtB,KAAK,EAAE,IAAA9B,sBAAA,CAAAqD,OAAO,EAAC,GAAG;IAAC,CAAC;IACjHG,WAAW,EAAE;MACXN,SAAS,EAAE1C,SAAS,CAACc,YAAY;MACjCc,eAAe,EAAE;KAClB;IAEDqB,MAAM,EAAE;MACNC,aAAa,EAAE,KAAK;MACpB5C,UAAU,EAAE;KACb;IACD6C,UAAU,EAAAzB,MAAA,CAAAC,MAAA,KACLvB,UAAU,oBAAVA,UAAU,CAAEgD,aAAa;MAC5BC,KAAK,EAAEnD,aAAa,CAACoD,QAAQ;MAC7BC,UAAU,EAAEvD,SAAS,CAACwD;IAAa,EACpC;IACDC,QAAQ,EAAE;MACRC,IAAI,EAAE,CAAC,KAAK;MACZC,QAAQ,EAAE,UAAU;MACpBzC,MAAM,EAAExB,gBAAA,CAAAyB,OAAc,CAACC,eAAe,EAAE;MACxCE,KAAK,EAAE5B,gBAAA,CAAAyB,OAAc,CAACI,cAAc,EAAE;MACtCqC,MAAM,EAAE,IAAI;MACZpC,cAAc,EAAE,QAAQ;MACxBlB,UAAU,EAAE;KACb;IACDuD,oBAAoB,EAAE;MACpBC,cAAc,EAAE7D,UAAU,CAAC8D,aAAa;MACxCC,cAAc,EAAE,CAAC;MACjBtB,SAAS,EAAE1C,SAAS,CAACwD;KACtB;IACDS,OAAO,EAAAvC,MAAA,CAAAC,MAAA,KACFvB,UAAU,oBAAVA,UAAU,CAAEgD,aAAa;MAC5BC,KAAK,EAAEnD,aAAa,CAACoD;IAAQ,EAC9B;IACDY,QAAQ,EAAAxC,MAAA,CAAAC,MAAA,KACHvB,UAAU,oBAAVA,UAAU,CAAE+D,aAAa;MAE5Bd,KAAK,EAAEnD,aAAa,CAACkE,OAAO;MAC5B/B,YAAY,EAAErC,SAAS,CAACqE;IAAc,EACvC;IACDC,QAAQ,EAAA5C,MAAA,CAAAC,MAAA,KACHvB,UAAU,oBAAVA,UAAU,CAAEmE,WAAW;MAC1BlB,KAAK,EAAEnD,aAAa,CAACoD;IAAQ,EAC9B;IACDkB,KAAK,EAAE;MACL9B,SAAS,EAAE1C,SAAS,CAACc;KACtB;IACD2D,WAAW,EAAE;MACX/B,SAAS,EAAElD,sBAAA,CAAAkF,UAAU,CAACC;;GAEzB;AACH,CAAC,CAAC",
      ignoreList: []
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "5bc5e387f7cb9ff59ce0619cf4c6dc7564e1cdf8"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_1bu50qtciu = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_1bu50qtciu();
var __importDefault =
/* istanbul ignore next */
(cov_1bu50qtciu().s[0]++,
/* istanbul ignore next */
(cov_1bu50qtciu().b[0][0]++, this) &&
/* istanbul ignore next */
(cov_1bu50qtciu().b[0][1]++, this.__importDefault) ||
/* istanbul ignore next */
(cov_1bu50qtciu().b[0][2]++, function (mod) {
  /* istanbul ignore next */
  cov_1bu50qtciu().f[0]++;
  cov_1bu50qtciu().s[1]++;
  return /* istanbul ignore next */(cov_1bu50qtciu().b[2][0]++, mod) &&
  /* istanbul ignore next */
  (cov_1bu50qtciu().b[2][1]++, mod.__esModule) ?
  /* istanbul ignore next */
  (cov_1bu50qtciu().b[1][0]++, mod) :
  /* istanbul ignore next */
  (cov_1bu50qtciu().b[1][1]++, {
    "default": mod
  });
}));
/* istanbul ignore next */
cov_1bu50qtciu().s[2]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_1bu50qtciu().s[3]++;
exports.makeStyle = void 0;
var msb_shared_component_1 =
/* istanbul ignore next */
(cov_1bu50qtciu().s[4]++, require("msb-shared-component"));
var DimensionUtils_1 =
/* istanbul ignore next */
(cov_1bu50qtciu().s[5]++, __importDefault(require("../../utils/DimensionUtils")));
/* istanbul ignore next */
cov_1bu50qtciu().s[6]++;
exports.makeStyle = (0, msb_shared_component_1.createMSBStyleSheet)(function (_ref) {
  /* istanbul ignore next */
  cov_1bu50qtciu().f[1]++;
  var SizeAlias =
    /* istanbul ignore next */
    (cov_1bu50qtciu().s[7]++, _ref.SizeAlias),
    ColorField =
    /* istanbul ignore next */
    (cov_1bu50qtciu().s[8]++, _ref.ColorField),
    ColorDataView =
    /* istanbul ignore next */
    (cov_1bu50qtciu().s[9]++, _ref.ColorDataView),
    Shadow =
    /* istanbul ignore next */
    (cov_1bu50qtciu().s[10]++, _ref.Shadow),
    Typography =
    /* istanbul ignore next */
    (cov_1bu50qtciu().s[11]++, _ref.Typography);
  /* istanbul ignore next */
  cov_1bu50qtciu().s[12]++;
  return {
    alignItemsCenter: {
      alignItems: 'center'
    },
    btnConfirm: {
      marginHorizontal: SizeAlias.SpacingXMSmall
    },
    container: {
      flex: 1
    },
    scrollViewContainer: {
      flex: 1,
      paddingHorizontal: SizeAlias.SpacingSmall,
      paddingVertical: SizeAlias.Spacing2xSmall
    },
    containerViewshot: {
      flex: 1,
      height: DimensionUtils_1.default.getWindowHeight(),
      paddingBottom: SizeAlias.SpacingSmall,
      paddingHorizontal: SizeAlias.SpacingSmall,
      width: DimensionUtils_1.default.getWindowWidth(),
      justifyContent: 'center'
    },
    contentContainer: Object.assign({
      backgroundColor: msb_shared_component_1.ColorGlobal.NeutralWhite,
      borderRadius: SizeAlias.SpacingXMSmall,
      padding: SizeAlias.SpacingSmall,
      overflow: 'hidden'
    }, Shadow.center),
    contentContainerView: Object.assign({
      backgroundColor: msb_shared_component_1.ColorGlobal.NeutralWhite,
      borderRadius: SizeAlias.SpacingXMSmall
    }, Shadow.center),
    contentContainerViewShot: {
      backgroundColor: msb_shared_component_1.ColorGlobal.NeutralWhite,
      borderRadius: SizeAlias.SpacingXMSmall,
      marginBottom: SizeAlias.SpacingSmall,
      padding: SizeAlias.SpacingSmall,
      opacity: 0.9
    },
    contentViewShot: {
      borderRadius: SizeAlias.SpacingXMSmall
    },
    flex1: {
      flex: 1
    },
    icLogo: {
      marginTop: SizeAlias.SpacingLarge,
      marginBottom: SizeAlias.SpacingSmall,
      resizeMode: 'stretch',
      height: (0, msb_shared_component_1.getSize)(56),
      width: (0, msb_shared_component_1.getSize)(156)
    },
    imageBackground: {
      flex: 1,
      resizeMode: 'stretch'
    },
    logoShot: {
      height: (0, msb_shared_component_1.getSize)(35),
      marginBottom: SizeAlias.SpacingLarge,
      resizeMode: 'contain',
      width: (0, msb_shared_component_1.getSize)(150)
    },
    marginTop16: {
      marginTop: SizeAlias.SpacingSmall,
      backgroundColor: 'transparent'
    },
    sender: {
      flexDirection: 'row',
      alignItems: 'center'
    },
    senderName: Object.assign({}, Typography == null ?
    /* istanbul ignore next */
    (cov_1bu50qtciu().b[3][0]++, void 0) :
    /* istanbul ignore next */
    (cov_1bu50qtciu().b[3][1]++, Typography.base_semiBold), {
      color: ColorDataView.TextMain,
      marginLeft: SizeAlias.SpacingXSmall
    }),
    shotView: {
      left: -10000,
      position: 'absolute',
      height: DimensionUtils_1.default.getWindowHeight(),
      width: DimensionUtils_1.default.getWindowWidth(),
      zIndex: 1000,
      justifyContent: 'center',
      alignItems: 'center'
    },
    transferResultAction: {
      borderTopColor: ColorField.BorderDefault,
      borderTopWidth: 1,
      marginTop: SizeAlias.SpacingXSmall
    },
    txtName: Object.assign({}, Typography == null ?
    /* istanbul ignore next */
    (cov_1bu50qtciu().b[4][0]++, void 0) :
    /* istanbul ignore next */
    (cov_1bu50qtciu().b[4][1]++, Typography.base_semiBold), {
      color: ColorDataView.TextMain
    }),
    txtTitle: Object.assign({}, Typography == null ?
    /* istanbul ignore next */
    (cov_1bu50qtciu().b[5][0]++, void 0) :
    /* istanbul ignore next */
    (cov_1bu50qtciu().b[5][1]++, Typography.small_regular), {
      color: ColorDataView.TextSub,
      marginBottom: SizeAlias.Spacing4xSmall
    }),
    txtValue: Object.assign({}, Typography == null ?
    /* istanbul ignore next */
    (cov_1bu50qtciu().b[6][0]++, void 0) :
    /* istanbul ignore next */
    (cov_1bu50qtciu().b[6][1]++, Typography.base_medium), {
      color: ColorDataView.TextMain
    }),
    top16: {
      marginTop: SizeAlias.SpacingSmall
    },
    accountInfo: {
      marginTop: msb_shared_component_1.SizeGlobal.Size500
    }
  };
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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