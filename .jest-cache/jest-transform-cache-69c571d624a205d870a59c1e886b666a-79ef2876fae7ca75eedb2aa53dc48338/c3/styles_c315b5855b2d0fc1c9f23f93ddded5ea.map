{"version": 3, "names": ["cov_1bu50qtciu", "actualCoverage", "msb_shared_component_1", "s", "require", "DimensionUtils_1", "__importDefault", "exports", "makeStyle", "createMSBStyleSheet", "_ref", "f", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ColorField", "ColorDataView", "Shadow", "Typography", "alignItemsCenter", "alignItems", "btnConfirm", "marginHorizontal", "SpacingXMSmall", "container", "flex", "scrollViewContainer", "paddingHorizontal", "SpacingSmall", "paddingVertical", "Spacing2xSmall", "containerViewshot", "height", "default", "getWindowHeight", "paddingBottom", "width", "getWindowWidth", "justifyContent", "contentContainer", "Object", "assign", "backgroundColor", "ColorGlobal", "NeutralWhite", "borderRadius", "padding", "overflow", "center", "contentContainerView", "contentContainerViewShot", "marginBottom", "opacity", "contentViewShot", "flex1", "icLogo", "marginTop", "SpacingLarge", "resizeMode", "getSize", "imageBackground", "logoShot", "marginTop16", "sender", "flexDirection", "sender<PERSON>ame", "b", "base_semiBold", "color", "TextMain", "marginLeft", "SpacingXSmall", "<PERSON><PERSON>ie<PERSON>", "left", "position", "zIndex", "transferResultAction", "borderTopColor", "BorderDefault", "borderTopWidth", "txtName", "txtTitle", "small_regular", "TextSub", "Spacing4xSmall", "txtValue", "base_medium", "top16", "accountInfo", "SizeGlobal", "Size500"], "sources": ["/Users/<USER>/projects/react-native/payment-module/src/presentation/payment-result/styles.ts"], "sourcesContent": ["import {ColorGlobal, createMSBStyleSheet, getSize, SizeGlobal} from 'msb-shared-component';\n\nimport DimensionUtils from '../../utils/DimensionUtils';\n\nexport const makeStyle = createMSBStyleSheet(({<PERSON><PERSON><PERSON><PERSON><PERSON>, ColorField, ColorDataView, Shadow, Typography}) => {\n  return {\n    alignItemsCenter: {\n      alignItems: 'center',\n    },\n    btnConfirm: {\n      marginHorizontal: SizeAlias.SpacingXMSmall,\n    },\n\n    container: {\n      flex: 1,\n      // paddingHorizontal: SizeAlias.SpacingSmall,\n      // paddingVertical: SizeAlias.Spacing2xSmall,\n    },\n    scrollViewContainer: {\n      flex: 1,\n      paddingHorizontal: SizeAlias.SpacingSmall,\n      paddingVertical: SizeAlias.Spacing2xSmall,\n    },\n    containerViewshot: {\n      flex: 1,\n      height: DimensionUtils.getWindowHeight(),\n      paddingBottom: SizeAlias.SpacingSmall,\n      paddingHorizontal: SizeAlias.SpacingSmall,\n      width: DimensionUtils.getWindowWidth(),\n      justifyContent: 'center',\n    },\n    contentContainer: {\n      backgroundColor: ColorGlobal.NeutralWhite,\n      borderRadius: SizeAlias.SpacingXMSmall,\n      padding: SizeAlias.SpacingSmall,\n      overflow: 'hidden',\n      ...Shadow.center,\n    },\n    contentContainerView: {\n      backgroundColor: ColorGlobal.NeutralWhite,\n      borderRadius: SizeAlias.SpacingXMSmall,\n      ...Shadow.center,\n    },\n    // render image shot\n    contentContainerViewShot: {\n      backgroundColor: ColorGlobal.NeutralWhite,\n      borderRadius: SizeAlias.SpacingXMSmall,\n      marginBottom: SizeAlias.SpacingSmall,\n      padding: SizeAlias.SpacingSmall,\n      opacity: 0.9,\n    },\n    contentViewShot: {\n      borderRadius: SizeAlias.SpacingXMSmall,\n    },\n    flex1: {\n      flex: 1,\n    },\n    icLogo: {\n      marginTop: SizeAlias.SpacingLarge,\n      marginBottom: SizeAlias.SpacingSmall,\n      resizeMode: 'stretch',\n      height: getSize(56),\n      width: getSize(156),\n    },\n    imageBackground: {\n      flex: 1,\n      resizeMode: 'stretch',\n    },\n    logoShot: {height: getSize(35), marginBottom: SizeAlias.SpacingLarge, resizeMode: 'contain', width: getSize(150)},\n    marginTop16: {\n      marginTop: SizeAlias.SpacingSmall,\n      backgroundColor: 'transparent',\n    },\n\n    sender: {\n      flexDirection: 'row',\n      alignItems: 'center',\n    },\n    senderName: {\n      ...Typography?.base_semiBold,\n      color: ColorDataView.TextMain,\n      marginLeft: SizeAlias.SpacingXSmall,\n    },\n    shotView: {\n      left: -10000,\n      position: 'absolute',\n      height: DimensionUtils.getWindowHeight(),\n      width: DimensionUtils.getWindowWidth(),\n      zIndex: 1000,\n      justifyContent: 'center',\n      alignItems: 'center',\n    },\n    transferResultAction: {\n      borderTopColor: ColorField.BorderDefault,\n      borderTopWidth: 1,\n      marginTop: SizeAlias.SpacingXSmall,\n    },\n    txtName: {\n      ...Typography?.base_semiBold,\n      color: ColorDataView.TextMain,\n    },\n    txtTitle: {\n      ...Typography?.small_regular,\n      // ...Tpg.base_semiBold,\n      color: ColorDataView.TextSub,\n      marginBottom: SizeAlias.Spacing4xSmall,\n    },\n    txtValue: {\n      ...Typography?.base_medium,\n      color: ColorDataView.TextMain,\n    },\n    top16: {\n      marginTop: SizeAlias.SpacingSmall,\n    },\n    accountInfo: {\n      marginTop: SizeGlobal.Size500,\n    },\n  };\n});\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAI0D;IAAAA,cAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,cAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAJ1D,IAAAE,sBAAA;AAAA;AAAA,CAAAF,cAAA,GAAAG,CAAA,OAAAC,OAAA;AAEA,IAAAC,gBAAA;AAAA;AAAA,CAAAL,cAAA,GAAAG,CAAA,OAAAG,eAAA,CAAAF,OAAA;AAAA;AAAAJ,cAAA,GAAAG,CAAA;AAEaI,OAAA,CAAAC,SAAS,GAAG,IAAAN,sBAAA,CAAAO,mBAAmB,EAAC,UAAAC,IAAA,EAA+D;EAAA;EAAAV,cAAA,GAAAW,CAAA;EAAA,IAA7DC,SAAS;IAAA;IAAA,CAAAZ,cAAA,GAAAG,CAAA,OAAAO,IAAA,CAATE,SAAS;IAAEC,UAAU;IAAA;IAAA,CAAAb,cAAA,GAAAG,CAAA,OAAAO,IAAA,CAAVG,UAAU;IAAEC,aAAa;IAAA;IAAA,CAAAd,cAAA,GAAAG,CAAA,OAAAO,IAAA,CAAbI,aAAa;IAAEC,MAAM;IAAA;IAAA,CAAAf,cAAA,GAAAG,CAAA,QAAAO,IAAA,CAANK,MAAM;IAAEC,UAAU;IAAA;IAAA,CAAAhB,cAAA,GAAAG,CAAA,QAAAO,IAAA,CAAVM,UAAU;EAAA;EAAAhB,cAAA,GAAAG,CAAA;EACrG,OAAO;IACLc,gBAAgB,EAAE;MAChBC,UAAU,EAAE;KACb;IACDC,UAAU,EAAE;MACVC,gBAAgB,EAAER,SAAS,CAACS;KAC7B;IAEDC,SAAS,EAAE;MACTC,IAAI,EAAE;KAGP;IACDC,mBAAmB,EAAE;MACnBD,IAAI,EAAE,CAAC;MACPE,iBAAiB,EAAEb,SAAS,CAACc,YAAY;MACzCC,eAAe,EAAEf,SAAS,CAACgB;KAC5B;IACDC,iBAAiB,EAAE;MACjBN,IAAI,EAAE,CAAC;MACPO,MAAM,EAAEzB,gBAAA,CAAA0B,OAAc,CAACC,eAAe,EAAE;MACxCC,aAAa,EAAErB,SAAS,CAACc,YAAY;MACrCD,iBAAiB,EAAEb,SAAS,CAACc,YAAY;MACzCQ,KAAK,EAAE7B,gBAAA,CAAA0B,OAAc,CAACI,cAAc,EAAE;MACtCC,cAAc,EAAE;KACjB;IACDC,gBAAgB,EAAAC,MAAA,CAAAC,MAAA;MACdC,eAAe,EAAEtC,sBAAA,CAAAuC,WAAW,CAACC,YAAY;MACzCC,YAAY,EAAE/B,SAAS,CAACS,cAAc;MACtCuB,OAAO,EAAEhC,SAAS,CAACc,YAAY;MAC/BmB,QAAQ,EAAE;IAAQ,GACf9B,MAAM,CAAC+B,MAAM,CACjB;IACDC,oBAAoB,EAAAT,MAAA,CAAAC,MAAA;MAClBC,eAAe,EAAEtC,sBAAA,CAAAuC,WAAW,CAACC,YAAY;MACzCC,YAAY,EAAE/B,SAAS,CAACS;IAAc,GACnCN,MAAM,CAAC+B,MAAM,CACjB;IAEDE,wBAAwB,EAAE;MACxBR,eAAe,EAAEtC,sBAAA,CAAAuC,WAAW,CAACC,YAAY;MACzCC,YAAY,EAAE/B,SAAS,CAACS,cAAc;MACtC4B,YAAY,EAAErC,SAAS,CAACc,YAAY;MACpCkB,OAAO,EAAEhC,SAAS,CAACc,YAAY;MAC/BwB,OAAO,EAAE;KACV;IACDC,eAAe,EAAE;MACfR,YAAY,EAAE/B,SAAS,CAACS;KACzB;IACD+B,KAAK,EAAE;MACL7B,IAAI,EAAE;KACP;IACD8B,MAAM,EAAE;MACNC,SAAS,EAAE1C,SAAS,CAAC2C,YAAY;MACjCN,YAAY,EAAErC,SAAS,CAACc,YAAY;MACpC8B,UAAU,EAAE,SAAS;MACrB1B,MAAM,EAAE,IAAA5B,sBAAA,CAAAuD,OAAO,EAAC,EAAE,CAAC;MACnBvB,KAAK,EAAE,IAAAhC,sBAAA,CAAAuD,OAAO,EAAC,GAAG;KACnB;IACDC,eAAe,EAAE;MACfnC,IAAI,EAAE,CAAC;MACPiC,UAAU,EAAE;KACb;IACDG,QAAQ,EAAE;MAAC7B,MAAM,EAAE,IAAA5B,sBAAA,CAAAuD,OAAO,EAAC,EAAE,CAAC;MAAER,YAAY,EAAErC,SAAS,CAAC2C,YAAY;MAAEC,UAAU,EAAE,SAAS;MAAEtB,KAAK,EAAE,IAAAhC,sBAAA,CAAAuD,OAAO,EAAC,GAAG;IAAC,CAAC;IACjHG,WAAW,EAAE;MACXN,SAAS,EAAE1C,SAAS,CAACc,YAAY;MACjCc,eAAe,EAAE;KAClB;IAEDqB,MAAM,EAAE;MACNC,aAAa,EAAE,KAAK;MACpB5C,UAAU,EAAE;KACb;IACD6C,UAAU,EAAAzB,MAAA,CAAAC,MAAA,KACLvB,UAAU;IAAA;IAAA,CAAAhB,cAAA,GAAAgE,CAAA;IAAA;IAAA,CAAAhE,cAAA,GAAAgE,CAAA,UAAVhD,UAAU,CAAEiD,aAAa;MAC5BC,KAAK,EAAEpD,aAAa,CAACqD,QAAQ;MAC7BC,UAAU,EAAExD,SAAS,CAACyD;IAAa,EACpC;IACDC,QAAQ,EAAE;MACRC,IAAI,EAAE,CAAC,KAAK;MACZC,QAAQ,EAAE,UAAU;MACpB1C,MAAM,EAAEzB,gBAAA,CAAA0B,OAAc,CAACC,eAAe,EAAE;MACxCE,KAAK,EAAE7B,gBAAA,CAAA0B,OAAc,CAACI,cAAc,EAAE;MACtCsC,MAAM,EAAE,IAAI;MACZrC,cAAc,EAAE,QAAQ;MACxBlB,UAAU,EAAE;KACb;IACDwD,oBAAoB,EAAE;MACpBC,cAAc,EAAE9D,UAAU,CAAC+D,aAAa;MACxCC,cAAc,EAAE,CAAC;MACjBvB,SAAS,EAAE1C,SAAS,CAACyD;KACtB;IACDS,OAAO,EAAAxC,MAAA,CAAAC,MAAA,KACFvB,UAAU;IAAA;IAAA,CAAAhB,cAAA,GAAAgE,CAAA;IAAA;IAAA,CAAAhE,cAAA,GAAAgE,CAAA,UAAVhD,UAAU,CAAEiD,aAAa;MAC5BC,KAAK,EAAEpD,aAAa,CAACqD;IAAQ,EAC9B;IACDY,QAAQ,EAAAzC,MAAA,CAAAC,MAAA,KACHvB,UAAU;IAAA;IAAA,CAAAhB,cAAA,GAAAgE,CAAA;IAAA;IAAA,CAAAhE,cAAA,GAAAgE,CAAA,UAAVhD,UAAU,CAAEgE,aAAa;MAE5Bd,KAAK,EAAEpD,aAAa,CAACmE,OAAO;MAC5BhC,YAAY,EAAErC,SAAS,CAACsE;IAAc,EACvC;IACDC,QAAQ,EAAA7C,MAAA,CAAAC,MAAA,KACHvB,UAAU;IAAA;IAAA,CAAAhB,cAAA,GAAAgE,CAAA;IAAA;IAAA,CAAAhE,cAAA,GAAAgE,CAAA,UAAVhD,UAAU,CAAEoE,WAAW;MAC1BlB,KAAK,EAAEpD,aAAa,CAACqD;IAAQ,EAC9B;IACDkB,KAAK,EAAE;MACL/B,SAAS,EAAE1C,SAAS,CAACc;KACtB;IACD4D,WAAW,EAAE;MACXhC,SAAS,EAAEpD,sBAAA,CAAAqF,UAAU,CAACC;;GAEzB;AACH,CAAC,CAAC", "ignoreList": []}