{"version": 3, "names": ["cov_hqubabn7h", "actualCoverage", "PathResolver_1", "s", "require", "ResponseHandler_1", "MSBCustomError_1", "PaymentRemoteDataSource", "f", "httpClient", "_classCallCheck2", "default", "_createClass2", "key", "value", "_validate", "_asyncToGenerator2", "request", "url", "PathResolver", "payment", "validate", "response", "post", "handleResponse", "error", "CustomError", "b", "createError", "_x", "apply", "arguments", "exports"], "sources": ["/Users/<USER>/projects/react-native/payment-module/src/data/datasources/remote/PaymentRemoteDataSource.ts"], "sourcesContent": ["import {BaseResponse} from '../../../core/BaseResponse';\nimport {PathResolver} from '../../../utils/PathResolver';\nimport {IHttpClient} from 'msb-host-shared-module';\nimport {handleResponse} from '../../../utils/ResponseHandler';\nimport {ValidateResponse} from '../../models/validate/ValidateResponse';\nimport {ValidateRequest} from '../../models/validate/ValidateRequest';\nimport {IPaymentDataSource} from '../IPaymentDataSource';\nimport {createError, CustomError} from '../../../core/MSBCustomError';\n\nexport class PaymentRemoteDataSource implements IPaymentDataSource {\n  constructor(private httpClient: IHttpClient) {}\n\n  async validate(request: ValidateRequest): Promise<BaseResponse<ValidateResponse>> {\n    try {\n      const url = PathResolver.payment.validate();\n      const response = await this.httpClient.post(url, request);\n      return handleResponse(response);\n    } catch (error: any) {\n      if (error instanceof CustomError) {\n        throw error;\n      }\n      throw createError();\n    }\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAU6C;IAAAA,aAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,aAAA;;;;;;;;;;;;;;;;;;;;;AAT7C,IAAAE,cAAA;AAAA;AAAA,CAAAF,aAAA,GAAAG,CAAA,OAAAC,OAAA;AAEA,IAAAC,iBAAA;AAAA;AAAA,CAAAL,aAAA,GAAAG,CAAA,OAAAC,OAAA;AAIA,IAAAE,gBAAA;AAAA;AAAA,CAAAN,aAAA,GAAAG,CAAA,OAAAC,OAAA;AAAsE,IAEzDG,uBAAuB;AAAA;AAAA,CAAAP,aAAA,GAAAG,CAAA;EAAA;EAAAH,aAAA,GAAAQ,CAAA;EAClC,SAAAD,wBAAoBE,UAAuB;IAAA;IAAAT,aAAA,GAAAQ,CAAA;IAAAR,aAAA,GAAAG,CAAA;IAAA,IAAAO,gBAAA,CAAAC,OAAA,QAAAJ,uBAAA;IAAA;IAAAP,aAAA,GAAAG,CAAA;IAAvB,KAAAM,UAAU,GAAVA,UAAU;EAAgB;EAAA;EAAAT,aAAA,GAAAG,CAAA;EAAC,WAAAS,aAAA,CAAAD,OAAA,EAAAJ,uBAAA;IAAAM,GAAA;IAAAC,KAAA;MAAA;MAAAd,aAAA,GAAAQ,CAAA;MAAA,IAAAO,SAAA;MAAA;MAAA,CAAAf,aAAA,GAAAG,CAAA,YAAAa,kBAAA,CAAAL,OAAA,EAE/C,WAAeM,OAAwB;QAAA;QAAAjB,aAAA,GAAAQ,CAAA;QAAAR,aAAA,GAAAG,CAAA;QACrC,IAAI;UACF,IAAMe,GAAG;UAAA;UAAA,CAAAlB,aAAA,GAAAG,CAAA,QAAGD,cAAA,CAAAiB,YAAY,CAACC,OAAO,CAACC,QAAQ,EAAE;UAC3C,IAAMC,QAAQ;UAAA;UAAA,CAAAtB,aAAA,GAAAG,CAAA,cAAS,IAAI,CAACM,UAAU,CAACc,IAAI,CAACL,GAAG,EAAED,OAAO,CAAC;UAAA;UAAAjB,aAAA,GAAAG,CAAA;UACzD,OAAO,IAAAE,iBAAA,CAAAmB,cAAc,EAACF,QAAQ,CAAC;QACjC,CAAC,CAAC,OAAOG,KAAU,EAAE;UAAA;UAAAzB,aAAA,GAAAG,CAAA;UACnB,IAAIsB,KAAK,YAAYnB,gBAAA,CAAAoB,WAAW,EAAE;YAAA;YAAA1B,aAAA,GAAA2B,CAAA;YAAA3B,aAAA,GAAAG,CAAA;YAChC,MAAMsB,KAAK;UACb;UAAA;UAAA;YAAAzB,aAAA,GAAA2B,CAAA;UAAA;UAAA3B,aAAA,GAAAG,CAAA;UACA,MAAM,IAAAG,gBAAA,CAAAsB,WAAW,GAAE;QACrB;MACF,CAAC;MAAA,SAXKP,QAAQA,CAAAQ,EAAA;QAAA;QAAA7B,aAAA,GAAAQ,CAAA;QAAAR,aAAA,GAAAG,CAAA;QAAA,OAAAY,SAAA,CAAAe,KAAA,OAAAC,SAAA;MAAA;MAAA;MAAA/B,aAAA,GAAAG,CAAA;MAAA,OAARkB,QAAQ;IAAA;EAAA;AAAA;AAAA;AAAArB,aAAA,GAAAG,CAAA;AAHhB6B,OAAA,CAAAzB,uBAAA,GAAAA,uBAAA", "ignoreList": []}