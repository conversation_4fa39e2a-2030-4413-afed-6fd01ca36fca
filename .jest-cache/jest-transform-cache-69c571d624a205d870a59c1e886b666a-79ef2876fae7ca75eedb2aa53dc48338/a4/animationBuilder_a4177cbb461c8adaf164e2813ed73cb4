4eacc56601b032b4df446c06afb7fe5a
'use strict';

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.maybeBuild = maybeBuild;
var _index = require("./logger/index.js");
var mockTargetValues = {
  targetOriginX: 0,
  targetOriginY: 0,
  targetWidth: 0,
  targetHeight: 0,
  targetGlobalOriginX: 0,
  targetGlobalOriginY: 0,
  targetBorderRadius: 0,
  windowWidth: 0,
  windowHeight: 0,
  currentOriginX: 0,
  currentOriginY: 0,
  currentWidth: 0,
  currentHeight: 0,
  currentGlobalOriginX: 0,
  currentGlobalOriginY: 0,
  currentBorderRadius: 0
};
function getCommonProperties(layoutStyle, componentStyle) {
  var componentStyleFlat = Array.isArray(componentStyle) ? componentStyle.flat() : [componentStyle];
  componentStyleFlat = componentStyleFlat.filter(Boolean);
  componentStyleFlat = componentStyleFlat.map(function (style) {
    return 'initial' in style ? style.initial.value : style;
  });
  var componentStylesKeys = componentStyleFlat.flatMap(function (style) {
    return Object.keys(style);
  });
  var commonKeys = Object.keys(layoutStyle).filter(function (key) {
    return componentStylesKeys.includes(key);
  });
  return commonKeys;
}
function maybeReportOverwrittenProperties(layoutAnimationStyle, style, displayName) {
  var commonProperties = getCommonProperties(layoutAnimationStyle, style);
  if (commonProperties.length > 0) {
    _index.logger.warn(`${commonProperties.length === 1 ? 'Property' : 'Properties'} "${commonProperties.join(', ')}" of ${displayName} may be overwritten by a layout animation. Please wrap your component with an animated view and apply the layout animation on the wrapper.`);
  }
}
function maybeBuild(layoutAnimationOrBuilder, style, displayName) {
  var isAnimationBuilder = function isAnimationBuilder(value) {
    return 'build' in layoutAnimationOrBuilder && typeof layoutAnimationOrBuilder.build === 'function';
  };
  if (isAnimationBuilder(layoutAnimationOrBuilder)) {
    var animationFactory = layoutAnimationOrBuilder.build();
    if (__DEV__ && style) {
      var layoutAnimation = animationFactory(mockTargetValues);
      maybeReportOverwrittenProperties(layoutAnimation.animations, style, displayName);
    }
    return animationFactory;
  } else {
    return layoutAnimationOrBuilder;
  }
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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