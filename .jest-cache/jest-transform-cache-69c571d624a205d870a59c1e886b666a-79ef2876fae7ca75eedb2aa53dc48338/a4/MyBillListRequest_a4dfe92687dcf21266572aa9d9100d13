e5d0d8da1acbff4542239eb5a51441ed
"use strict";

/* istanbul ignore next */
function cov_awbmdn3hq() {
  var path = "/Users/<USER>/projects/react-native/payment-module/src/data/models/my-bill-list/MyBillListRequest.ts";
  var hash = "e7cfb3e260cfb347c4f28d2deab64217daa44781";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/projects/react-native/payment-module/src/data/models/my-bill-list/MyBillListRequest.ts",
    statementMap: {
      "0": {
        start: {
          line: 3,
          column: 0
        },
        end: {
          line: 5,
          column: 3
        }
      }
    },
    fnMap: {},
    branchMap: {},
    s: {
      "0": 0
    },
    f: {},
    b: {},
    inputSourceMap: {
      version: 3,
      file: undefined,
      names: [],
      sourceRoot: undefined,
      sources: ["/Users/<USER>/projects/react-native/payment-module/src/data/models/my-bill-list/MyBillListRequest.ts"],
      sourcesContent: ["export interface MyBillListRequest {\n  // TODO: define fields\n}\n"],
      mappings: "",
      ignoreList: []
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "e7cfb3e260cfb347c4f28d2deab64217daa44781"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_awbmdn3hq = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_awbmdn3hq();
cov_awbmdn3hq().s[0]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6W10sInNvdXJjZXMiOlsiL1VzZXJzL3NvbnNhbXNldC9wcm9qZWN0cy9yZWFjdC1uYXRpdmUvcGF5bWVudC1tb2R1bGUvc3JjL2RhdGEvbW9kZWxzL215LWJpbGwtbGlzdC9NeUJpbGxMaXN0UmVxdWVzdC50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgaW50ZXJmYWNlIE15QmlsbExpc3RSZXF1ZXN0IHtcbiAgLy8gVE9ETzogZGVmaW5lIGZpZWxkc1xufVxuIl0sIm1hcHBpbmdzIjoiIiwiaWdub3JlTGlzdCI6W119