{"version": 3, "names": ["Object", "defineProperty", "exports", "value", "maybeBuild", "_index", "require", "mockTargetValues", "targetOriginX", "targetOriginY", "targetWidth", "targetHeight", "targetGlobalOriginX", "targetGlobalOriginY", "targetBorderRadius", "windowWidth", "windowHeight", "currentOriginX", "currentOriginY", "currentWidth", "currentHeight", "currentGlobalOriginX", "currentGlobalOriginY", "currentBorderRadius", "getCommonProperties", "layoutStyle", "componentStyle", "componentStyleFlat", "Array", "isArray", "flat", "filter", "Boolean", "map", "style", "initial", "componentStylesKeys", "flatMap", "keys", "commonKeys", "key", "includes", "maybeReportOverwrittenProperties", "layoutAnimationStyle", "displayName", "commonProperties", "length", "logger", "warn", "join", "layoutAnimationOrBuilder", "isAnimationBuilder", "build", "animationFactory", "__DEV__", "layoutAnimation", "animations"], "sources": ["../../src/animationBuilder.tsx"], "sourcesContent": [null], "mappings": "AAAA,YAAY;;AAAAA,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,KAAA;AAAA;AAAAD,OAAA,CAAAE,UAAA,GAAAA,UAAA;AAQZ,IAAAC,MAAA,GAAAC,OAAA;AAEA,IAAMC,gBAAwC,GAAG;EAC/CC,aAAa,EAAE,CAAC;EAChBC,aAAa,EAAE,CAAC;EAChBC,WAAW,EAAE,CAAC;EACdC,YAAY,EAAE,CAAC;EACfC,mBAAmB,EAAE,CAAC;EACtBC,mBAAmB,EAAE,CAAC;EACtBC,kBAAkB,EAAE,CAAC;EACrBC,WAAW,EAAE,CAAC;EACdC,YAAY,EAAE,CAAC;EACfC,cAAc,EAAE,CAAC;EACjBC,cAAc,EAAE,CAAC;EACjBC,YAAY,EAAE,CAAC;EACfC,aAAa,EAAE,CAAC;EAChBC,oBAAoB,EAAE,CAAC;EACvBC,oBAAoB,EAAE,CAAC;EACvBC,mBAAmB,EAAE;AACvB,CAAC;AAED,SAASC,mBAAmBA,CAC1BC,WAAuB,EACvBC,cAAuC,EACvC;EACA,IAAIC,kBAAkB,GAAGC,KAAK,CAACC,OAAO,CAACH,cAAc,CAAC,GAClDA,cAAc,CAACI,IAAI,CAAC,CAAC,GACrB,CAACJ,cAAc,CAAC;EAEpBC,kBAAkB,GAAGA,kBAAkB,CAACI,MAAM,CAACC,OAAO,CAAC;EAEvDL,kBAAkB,GAAGA,kBAAkB,CAACM,GAAG,CAAE,UAAAC,KAAK;IAAA,OAChD,SAAS,IAAIA,KAAK,GACdA,KAAK,CAACC,OAAO,CAAChC,KAAK,GACnB+B,KACN;EAAA,EAAC;EAED,IAAME,mBAAmB,GAAGT,kBAAkB,CAACU,OAAO,CAAE,UAAAH,KAAK;IAAA,OAC3DlC,MAAM,CAACsC,IAAI,CAACJ,KAAK,CACnB;EAAA,EAAC;EAED,IAAMK,UAAU,GAAGvC,MAAM,CAACsC,IAAI,CAACb,WAAW,CAAC,CAACM,MAAM,CAAE,UAAAS,GAAG;IAAA,OACrDJ,mBAAmB,CAACK,QAAQ,CAACD,GAAG,CAClC;EAAA,EAAC;EAED,OAAOD,UAAU;AACnB;AAEA,SAASG,gCAAgCA,CACvCC,oBAAgC,EAChCT,KAA8B,EAC9BU,WAAmB,EACnB;EACA,IAAMC,gBAAgB,GAAGrB,mBAAmB,CAACmB,oBAAoB,EAAET,KAAK,CAAC;EAEzE,IAAIW,gBAAgB,CAACC,MAAM,GAAG,CAAC,EAAE;IAC/BC,aAAM,CAACC,IAAI,CACT,GACEH,gBAAgB,CAACC,MAAM,KAAK,CAAC,GAAG,UAAU,GAAG,YAAY,KACtDD,gBAAgB,CAACI,IAAI,CACxB,IACF,CAAC,QAAQL,WAAW,4IACtB,CAAC;EACH;AACF;AAEO,SAASxC,UAAUA,CACxB8C,wBAGY,EACZhB,KAA0C,EAC1CU,WAAmB,EACiB;EACpC,IAAMO,kBAAkB,GACtB,SADIA,kBAAkBA,CACtBhD,KAAmE;IAAA,OAEnE,OAAO,IAAI+C,wBAAwB,IACnC,OAAOA,wBAAwB,CAACE,KAAK,KAAK,UAAU;EAAA;EAEtD,IAAID,kBAAkB,CAACD,wBAAwB,CAAC,EAAE;IAChD,IAAMG,gBAAgB,GAAGH,wBAAwB,CAACE,KAAK,CAAC,CAAC;IAEzD,IAAIE,OAAO,IAAIpB,KAAK,EAAE;MACpB,IAAMqB,eAAe,GAAGF,gBAAgB,CAAC9C,gBAAgB,CAAC;MAC1DmC,gCAAgC,CAC9Ba,eAAe,CAACC,UAAU,EAC1BtB,KAAK,EACLU,WACF,CAAC;IACH;IAEA,OAAOS,gBAAgB;EACzB,CAAC,MAAM;IACL,OAAOH,wBAAwB;EACjC;AACF", "ignoreList": []}