3125e9e06d3a8e8d08bff72ea879ecb2
"use strict";

/* istanbul ignore next */
function cov_23vhvvsj9s() {
  var path = "/Users/<USER>/projects/react-native/payment-module/src/domain/repositories/IBillContactRepository.ts";
  var hash = "a2164bfef23d63dbd0e0cda569b1baa6a35ccc08";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/projects/react-native/payment-module/src/domain/repositories/IBillContactRepository.ts",
    statementMap: {
      "0": {
        start: {
          line: 3,
          column: 0
        },
        end: {
          line: 5,
          column: 3
        }
      }
    },
    fnMap: {},
    branchMap: {},
    s: {
      "0": 0
    },
    f: {},
    b: {},
    inputSourceMap: {
      version: 3,
      file: undefined,
      names: [],
      sourceRoot: undefined,
      sources: ["/Users/<USER>/projects/react-native/payment-module/src/domain/repositories/IBillContactRepository.ts"],
      sourcesContent: ["import {BillHistoryModel} from '../entities/get-my-bill-history-list/GetMyBillHistoryListModel';\nimport {GetMyBillHistoryListRequest} from '../../data/models/get-my-bill-history-list/GetMyBillHistoryListRequest';\nimport {GetMyBillContactRecentListModel} from '../entities/get-my-bill-contact-recent-list/GetMyBillContactRecentListModel';\nimport {GetMyBillContactRecentListRequest} from '../../data/models/get-my-bill-contact-recent-list/GetMyBillContactRecentListRequest';\nimport {MyBillContactListModel} from '../entities/my-bill-contact-list/MyBillContactListModel';\nimport {EditBillContactModel} from '../entities/edit-bill-contact/EditBillContactModel';\nimport {EditBillContactRequest} from '../../data/models/edit-bill-contact/EditBillContactRequest';\nimport {DeleteBillContactModel} from '../entities/delete-bill-contact/DeleteBillContactModel';\nimport {DeleteBillContactRequest} from '../../data/models/delete-bill-contact/DeleteBillContactRequest';\nimport {SaveBillContactModel} from '../entities/save-bill-contact/SaveBillContactModel';\nimport {SaveBillContactRequest} from '../../data/models/save-bill-contact/SaveBillContactRequest';\nimport {BaseResponse} from '../../core/BaseResponse';\n\nexport interface IBillContactRepository {\n  saveBillContact(request: SaveBillContactRequest): Promise<BaseResponse<SaveBillContactModel>>;\n  deleteBillContact(request: DeleteBillContactRequest): Promise<BaseResponse<DeleteBillContactModel>>;\n  editBillContact(request: EditBillContactRequest): Promise<BaseResponse<EditBillContactModel>>;\n  myBillContactList(): Promise<BaseResponse<MyBillContactListModel>>;\n  getMyBillContactRecentList(\n    request: GetMyBillContactRecentListRequest,\n  ): Promise<BaseResponse<GetMyBillContactRecentListModel>>;\n  getMyBillHistoryList(request: GetMyBillHistoryListRequest): Promise<BaseResponse<BillHistoryModel>>;\n}\n"],
      mappings: "",
      ignoreList: []
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "a2164bfef23d63dbd0e0cda569b1baa6a35ccc08"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_23vhvvsj9s = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_23vhvvsj9s();
cov_23vhvvsj9s().s[0]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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