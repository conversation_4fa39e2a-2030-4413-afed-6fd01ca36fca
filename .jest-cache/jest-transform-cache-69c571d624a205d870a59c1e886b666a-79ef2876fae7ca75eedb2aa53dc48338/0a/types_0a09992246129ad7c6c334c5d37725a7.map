{"version": 3, "names": [], "sources": ["/Users/<USER>/projects/react-native/payment-module/src/components/bill-info/types.ts"], "sourcesContent": ["import {TextStyle, ViewStyle} from 'react-native';\nimport {MSBIcons} from 'msb-shared-component';\n\nimport {SafeAny} from '../../commons/Constants';\n\nexport type BillInfoProps = {\n  style?: ViewStyle;\n  styleName?: TextStyle;\n  styleBankName?: TextStyle;\n  styleAccountNo?: TextStyle;\n  onPress?: (tag?: string) => SafeAny;\n  title?: string;\n  icon?: MSBIcons;\n  name?: string;\n  bankName?: string;\n  bankAlias?: string;\n  isNotShowBankName?: boolean;\n  bankLogo?: string;\n  isUppercase?: boolean;\n};\n"], "mappings": "", "ignoreList": []}