{"version": 3, "names": ["cov_1kvid3b9qb", "actualCoverage", "react_1", "s", "__importDefault", "require", "react_native_1", "i18n_1", "msb_shared_component_1", "EmptyScreen", "_ref", "f", "_theme$Typography", "_theme$Typography2", "isRecent", "_ref2", "useMSBStyles", "makeStyle", "styles", "theme", "default", "createElement", "ScrollView", "View", "style", "container", "MSBFastImage", "nameImage", "image", "folder", "MSBFolderImage", "IMAGES", "MSBTextBase", "Typography", "b", "base_semiBold", "content", "translate", "small_regular", "textAlign", "exports", "EmptyBillSystemErrorScreen", "_theme$Typography3", "_theme$Typography4", "_ref3", "containerEmptySavedSystemError", "EmptyBillFilteredScreen", "_theme$Typography5", "_theme$Typography6", "_ref4", "EmptyTransactionHistoryScreen", "_ref5", "title", "type", "MSBEmptyState", "emptyTitle", "emptySubTitle", "createMSBStyleSheet", "_ref6", "SizeGlobal", "alignItems", "flexDirection", "height", "justifyContent", "width", "flex", "getSize", "marginBottom", "Size500", "marginTop"], "sources": ["/Users/<USER>/projects/react-native/payment-module/src/presentation/payment-home/components/bill-list/EmptyBill.tsx"], "sourcesContent": ["import React from 'react';\nimport {Image, ScrollView, View} from 'react-native';\n\n// import Images from '../../../../assets/images/Images';\nimport {translate} from '../../../../locales/i18n';\nimport {\n  createMSBStyleSheet,\n  EmptyType,\n  getSize,\n  MSBEmptyState,\n  MSBFastImage,\n  MSBFolderImage,\n  MSBTextBase,\n  useMSBStyles,\n} from 'msb-shared-component';\n\nexport const EmptyScreen = ({isRecent}: any) => {\n  const {styles, theme} = useMSBStyles(makeStyle);\n\n  return (\n    <ScrollView>\n      <View style={styles.container}>\n        {/* <Image style={styles.image} source={Images.icEmptyBill} /> */}\n        <MSBFastImage nameImage={'empty-data'} style={styles.image} folder={MSBFolderImage.IMAGES} />\n        <MSBTextBase style={theme.Typography?.base_semiBold} content={translate('billingTab.titleEmpty')} />\n        <MSBTextBase\n          style={[theme.Typography?.small_regular, styles.textAlign]}\n          content={translate(isRecent ? 'billingTab.contentEmptyRecent' : 'billingTab.contentEmpty')}\n        />\n      </View>\n    </ScrollView>\n  );\n};\n\nexport const EmptyBillSystemErrorScreen = () => {\n  const {styles, theme} = useMSBStyles(makeStyle);\n\n  return (\n    <ScrollView>\n      <View style={styles.containerEmptySavedSystemError}>\n        {/* <Image style={styles.image} source={Images.icEmptyBillSystemError} /> */}\n        <MSBFastImage nameImage={'coffee'} style={styles.image} folder={MSBFolderImage.IMAGES} />\n        <MSBTextBase\n          style={theme.Typography?.base_semiBold}\n          content={translate('billingTab.titleEmptyContactSystemError')}\n        />\n        <MSBTextBase\n          style={[theme.Typography?.small_regular, styles.textAlign]}\n          content={translate('billingTab.contentEmptyContactSystemError')}\n        />\n      </View>\n    </ScrollView>\n  );\n};\n\nexport const EmptyBillFilteredScreen = () => {\n  const {styles, theme} = useMSBStyles(makeStyle);\n\n  return (\n    <ScrollView>\n      <View style={styles.containerEmptySavedSystemError}>\n        <MSBFastImage nameImage={'empty-data'} style={styles.image} folder={MSBFolderImage.IMAGES} />\n        <MSBTextBase\n          style={theme.Typography?.base_semiBold}\n          content={translate('billingTab.titleEmptyContactFiltered')}\n        />\n        <MSBTextBase\n          style={theme.Typography?.small_regular}\n          content={translate('billingTab.contentEmptyContactFiltered')}\n        />\n      </View>\n    </ScrollView>\n  );\n};\n\nexport const EmptyTransactionHistoryScreen = ({\n  title,\n  content,\n  type,\n}: {\n  title: string;\n  content: string;\n  type: EmptyType;\n}) => {\n  return (\n    <View>\n      <MSBEmptyState emptyTitle={title} emptySubTitle={content} type={type} />\n    </View>\n  );\n};\n\nconst makeStyle = createMSBStyleSheet(({SizeGlobal}) => ({\n  container: {\n    alignItems: 'center',\n    flexDirection: 'column',\n    height: 320,\n    justifyContent: 'center',\n    width: '100%',\n  },\n  containerEmptySavedSystemError: {\n    alignItems: 'center',\n    flexDirection: 'column',\n    flex: 1,\n    justifyContent: 'center',\n    width: '100%',\n  },\n  image: {\n    height: getSize(144),\n    marginBottom: SizeGlobal.Size500,\n    marginTop: SizeGlobal.Size500,\n    width: getSize(144),\n  },\n  textAlign: {\n    textAlign: 'center',\n  },\n}));\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAgBa;IAAAA,cAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,cAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAhBb,IAAAE,OAAA;AAAA;AAAA,CAAAF,cAAA,GAAAG,CAAA,OAAAC,eAAA,CAAAC,OAAA;AACA,IAAAC,cAAA;AAAA;AAAA,CAAAN,cAAA,GAAAG,CAAA,OAAAE,OAAA;AAGA,IAAAE,MAAA;AAAA;AAAA,CAAAP,cAAA,GAAAG,CAAA,OAAAE,OAAA;AACA,IAAAG,sBAAA;AAAA;AAAA,CAAAR,cAAA,GAAAG,CAAA,OAAAE,OAAA;AAAA;AAAAL,cAAA,GAAAG,CAAA;AAWO,IAAMM,WAAW,GAAG,SAAdA,WAAWA,CAAAC,IAAA,EAAuB;EAAA;EAAAV,cAAA,GAAAW,CAAA;EAAA,IAAAC,iBAAA,EAAAC,kBAAA;EAAA,IAAlBC,QAAQ;EAAA;EAAA,CAAAd,cAAA,GAAAG,CAAA,OAAAO,IAAA,CAARI,QAAQ;EACnC,IAAAC,KAAA;IAAA;IAAA,CAAAf,cAAA,GAAAG,CAAA,QAAwB,IAAAK,sBAAA,CAAAQ,YAAY,EAACC,SAAS,CAAC;IAAxCC,MAAM;IAAA;IAAA,CAAAlB,cAAA,GAAAG,CAAA,QAAAY,KAAA,CAANG,MAAM;IAAEC,KAAK;IAAA;IAAA,CAAAnB,cAAA,GAAAG,CAAA,QAAAY,KAAA,CAALI,KAAK;EAAA;EAAAnB,cAAA,GAAAG,CAAA;EAEpB,OACED,OAAA,CAAAkB,OAAA,CAAAC,aAAA,CAACf,cAAA,CAAAgB,UAAU,QACTpB,OAAA,CAAAkB,OAAA,CAAAC,aAAA,CAACf,cAAA,CAAAiB,IAAI;IAACC,KAAK,EAAEN,MAAM,CAACO;EAAS,GAE3BvB,OAAA,CAAAkB,OAAA,CAAAC,aAAA,CAACb,sBAAA,CAAAkB,YAAY;IAACC,SAAS,EAAE,YAAY;IAAEH,KAAK,EAAEN,MAAM,CAACU,KAAK;IAAEC,MAAM,EAAErB,sBAAA,CAAAsB,cAAc,CAACC;EAAM,EAAI,EAC7F7B,OAAA,CAAAkB,OAAA,CAAAC,aAAA,CAACb,sBAAA,CAAAwB,WAAW;IAACR,KAAK,GAAAZ,iBAAA,GAAEO,KAAK,CAACc,UAAU;IAAA;IAAA,CAAAjC,cAAA,GAAAkC,CAAA;IAAA;IAAA,CAAAlC,cAAA,GAAAkC,CAAA,UAAhBtB,iBAAA,CAAkBuB,aAAa;IAAEC,OAAO,EAAE,IAAA7B,MAAA,CAAA8B,SAAS,EAAC,uBAAuB;EAAC,EAAI,EACpGnC,OAAA,CAAAkB,OAAA,CAAAC,aAAA,CAACb,sBAAA,CAAAwB,WAAW;IACVR,KAAK,EAAE,EAAAX,kBAAA,GAACM,KAAK,CAACc,UAAU;IAAA;IAAA,CAAAjC,cAAA,GAAAkC,CAAA;IAAA;IAAA,CAAAlC,cAAA,GAAAkC,CAAA,UAAhBrB,kBAAA,CAAkByB,aAAa,GAAEpB,MAAM,CAACqB,SAAS,CAAC;IAC1DH,OAAO,EAAE,IAAA7B,MAAA,CAAA8B,SAAS,EAACvB,QAAQ;IAAA;IAAA,CAAAd,cAAA,GAAAkC,CAAA,UAAG,+BAA+B;IAAA;IAAA,CAAAlC,cAAA,GAAAkC,CAAA,UAAG,yBAAyB;EAAC,EAC1F,CACG,CACI;AAEjB,CAAC;AAAA;AAAAlC,cAAA,GAAAG,CAAA;AAhBYqC,OAAA,CAAA/B,WAAW,GAAAA,WAAA;AAAA;AAAAT,cAAA,GAAAG,CAAA;AAkBjB,IAAMsC,0BAA0B,GAAG,SAA7BA,0BAA0BA,CAAA,EAAQ;EAAA;EAAAzC,cAAA,GAAAW,CAAA;EAAA,IAAA+B,kBAAA,EAAAC,kBAAA;EAC7C,IAAAC,KAAA;IAAA;IAAA,CAAA5C,cAAA,GAAAG,CAAA,QAAwB,IAAAK,sBAAA,CAAAQ,YAAY,EAACC,SAAS,CAAC;IAAxCC,MAAM;IAAA;IAAA,CAAAlB,cAAA,GAAAG,CAAA,QAAAyC,KAAA,CAAN1B,MAAM;IAAEC,KAAK;IAAA;IAAA,CAAAnB,cAAA,GAAAG,CAAA,QAAAyC,KAAA,CAALzB,KAAK;EAAA;EAAAnB,cAAA,GAAAG,CAAA;EAEpB,OACED,OAAA,CAAAkB,OAAA,CAAAC,aAAA,CAACf,cAAA,CAAAgB,UAAU,QACTpB,OAAA,CAAAkB,OAAA,CAAAC,aAAA,CAACf,cAAA,CAAAiB,IAAI;IAACC,KAAK,EAAEN,MAAM,CAAC2B;EAA8B,GAEhD3C,OAAA,CAAAkB,OAAA,CAAAC,aAAA,CAACb,sBAAA,CAAAkB,YAAY;IAACC,SAAS,EAAE,QAAQ;IAAEH,KAAK,EAAEN,MAAM,CAACU,KAAK;IAAEC,MAAM,EAAErB,sBAAA,CAAAsB,cAAc,CAACC;EAAM,EAAI,EACzF7B,OAAA,CAAAkB,OAAA,CAAAC,aAAA,CAACb,sBAAA,CAAAwB,WAAW;IACVR,KAAK,GAAAkB,kBAAA,GAAEvB,KAAK,CAACc,UAAU;IAAA;IAAA,CAAAjC,cAAA,GAAAkC,CAAA;IAAA;IAAA,CAAAlC,cAAA,GAAAkC,CAAA,UAAhBQ,kBAAA,CAAkBP,aAAa;IACtCC,OAAO,EAAE,IAAA7B,MAAA,CAAA8B,SAAS,EAAC,yCAAyC;EAAC,EAC7D,EACFnC,OAAA,CAAAkB,OAAA,CAAAC,aAAA,CAACb,sBAAA,CAAAwB,WAAW;IACVR,KAAK,EAAE,EAAAmB,kBAAA,GAACxB,KAAK,CAACc,UAAU;IAAA;IAAA,CAAAjC,cAAA,GAAAkC,CAAA;IAAA;IAAA,CAAAlC,cAAA,GAAAkC,CAAA,UAAhBS,kBAAA,CAAkBL,aAAa,GAAEpB,MAAM,CAACqB,SAAS,CAAC;IAC1DH,OAAO,EAAE,IAAA7B,MAAA,CAAA8B,SAAS,EAAC,2CAA2C;EAAC,EAC/D,CACG,CACI;AAEjB,CAAC;AAAA;AAAArC,cAAA,GAAAG,CAAA;AAnBYqC,OAAA,CAAAC,0BAA0B,GAAAA,0BAAA;AAAA;AAAAzC,cAAA,GAAAG,CAAA;AAqBhC,IAAM2C,uBAAuB,GAAG,SAA1BA,uBAAuBA,CAAA,EAAQ;EAAA;EAAA9C,cAAA,GAAAW,CAAA;EAAA,IAAAoC,kBAAA,EAAAC,kBAAA;EAC1C,IAAAC,KAAA;IAAA;IAAA,CAAAjD,cAAA,GAAAG,CAAA,QAAwB,IAAAK,sBAAA,CAAAQ,YAAY,EAACC,SAAS,CAAC;IAAxCC,MAAM;IAAA;IAAA,CAAAlB,cAAA,GAAAG,CAAA,QAAA8C,KAAA,CAAN/B,MAAM;IAAEC,KAAK;IAAA;IAAA,CAAAnB,cAAA,GAAAG,CAAA,QAAA8C,KAAA,CAAL9B,KAAK;EAAA;EAAAnB,cAAA,GAAAG,CAAA;EAEpB,OACED,OAAA,CAAAkB,OAAA,CAAAC,aAAA,CAACf,cAAA,CAAAgB,UAAU,QACTpB,OAAA,CAAAkB,OAAA,CAAAC,aAAA,CAACf,cAAA,CAAAiB,IAAI;IAACC,KAAK,EAAEN,MAAM,CAAC2B;EAA8B,GAChD3C,OAAA,CAAAkB,OAAA,CAAAC,aAAA,CAACb,sBAAA,CAAAkB,YAAY;IAACC,SAAS,EAAE,YAAY;IAAEH,KAAK,EAAEN,MAAM,CAACU,KAAK;IAAEC,MAAM,EAAErB,sBAAA,CAAAsB,cAAc,CAACC;EAAM,EAAI,EAC7F7B,OAAA,CAAAkB,OAAA,CAAAC,aAAA,CAACb,sBAAA,CAAAwB,WAAW;IACVR,KAAK,GAAAuB,kBAAA,GAAE5B,KAAK,CAACc,UAAU;IAAA;IAAA,CAAAjC,cAAA,GAAAkC,CAAA;IAAA;IAAA,CAAAlC,cAAA,GAAAkC,CAAA,UAAhBa,kBAAA,CAAkBZ,aAAa;IACtCC,OAAO,EAAE,IAAA7B,MAAA,CAAA8B,SAAS,EAAC,sCAAsC;EAAC,EAC1D,EACFnC,OAAA,CAAAkB,OAAA,CAAAC,aAAA,CAACb,sBAAA,CAAAwB,WAAW;IACVR,KAAK,GAAAwB,kBAAA,GAAE7B,KAAK,CAACc,UAAU;IAAA;IAAA,CAAAjC,cAAA,GAAAkC,CAAA;IAAA;IAAA,CAAAlC,cAAA,GAAAkC,CAAA,UAAhBc,kBAAA,CAAkBV,aAAa;IACtCF,OAAO,EAAE,IAAA7B,MAAA,CAAA8B,SAAS,EAAC,wCAAwC;EAAC,EAC5D,CACG,CACI;AAEjB,CAAC;AAAA;AAAArC,cAAA,GAAAG,CAAA;AAlBYqC,OAAA,CAAAM,uBAAuB,GAAAA,uBAAA;AAAA;AAAA9C,cAAA,GAAAG,CAAA;AAoB7B,IAAM+C,6BAA6B,GAAG,SAAhCA,6BAA6BA,CAAAC,KAAA,EAQrC;EAAA;EAAAnD,cAAA,GAAAW,CAAA;EAAA,IAPHyC,KAAK;IAAA;IAAA,CAAApD,cAAA,GAAAG,CAAA,QAAAgD,KAAA,CAALC,KAAK;IACLhB,OAAO;IAAA;IAAA,CAAApC,cAAA,GAAAG,CAAA,QAAAgD,KAAA,CAAPf,OAAO;IACPiB,IAAI;IAAA;IAAA,CAAArD,cAAA,GAAAG,CAAA,QAAAgD,KAAA,CAAJE,IAAI;EAAA;EAAArD,cAAA,GAAAG,CAAA;EAMJ,OACED,OAAA,CAAAkB,OAAA,CAAAC,aAAA,CAACf,cAAA,CAAAiB,IAAI,QACHrB,OAAA,CAAAkB,OAAA,CAAAC,aAAA,CAACb,sBAAA,CAAA8C,aAAa;IAACC,UAAU,EAAEH,KAAK;IAAEI,aAAa,EAAEpB,OAAO;IAAEiB,IAAI,EAAEA;EAAI,EAAI,CACnE;AAEX,CAAC;AAAA;AAAArD,cAAA,GAAAG,CAAA;AAdYqC,OAAA,CAAAU,6BAA6B,GAAAA,6BAAA;AAgB1C,IAAMjC,SAAS;AAAA;AAAA,CAAAjB,cAAA,GAAAG,CAAA,QAAG,IAAAK,sBAAA,CAAAiD,mBAAmB,EAAC,UAAAC,KAAA;EAAA;EAAA1D,cAAA,GAAAW,CAAA;EAAA,IAAEgD,UAAU;EAAA;EAAA,CAAA3D,cAAA,GAAAG,CAAA,QAAAuD,KAAA,CAAVC,UAAU;EAAA;EAAA3D,cAAA,GAAAG,CAAA;EAAA,OAAO;IACvDsB,SAAS,EAAE;MACTmC,UAAU,EAAE,QAAQ;MACpBC,aAAa,EAAE,QAAQ;MACvBC,MAAM,EAAE,GAAG;MACXC,cAAc,EAAE,QAAQ;MACxBC,KAAK,EAAE;KACR;IACDnB,8BAA8B,EAAE;MAC9Be,UAAU,EAAE,QAAQ;MACpBC,aAAa,EAAE,QAAQ;MACvBI,IAAI,EAAE,CAAC;MACPF,cAAc,EAAE,QAAQ;MACxBC,KAAK,EAAE;KACR;IACDpC,KAAK,EAAE;MACLkC,MAAM,EAAE,IAAAtD,sBAAA,CAAA0D,OAAO,EAAC,GAAG,CAAC;MACpBC,YAAY,EAAER,UAAU,CAACS,OAAO;MAChCC,SAAS,EAAEV,UAAU,CAACS,OAAO;MAC7BJ,KAAK,EAAE,IAAAxD,sBAAA,CAAA0D,OAAO,EAAC,GAAG;KACnB;IACD3B,SAAS,EAAE;MACTA,SAAS,EAAE;;GAEd;AAAA,CAAC,CAAC", "ignoreList": []}