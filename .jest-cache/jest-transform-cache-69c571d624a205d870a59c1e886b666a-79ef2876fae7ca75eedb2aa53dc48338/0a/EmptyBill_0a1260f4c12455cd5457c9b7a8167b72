5bdff782a2bdee4deb9362dc5cb7a379
"use strict";

/* istanbul ignore next */
function cov_1kvid3b9qb() {
  var path = "/Users/<USER>/projects/react-native/payment-module/src/presentation/payment-home/components/bill-list/EmptyBill.tsx";
  var hash = "c95c2804c2e799fb2c30a797bb2ed8244fb58ccc";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/projects/react-native/payment-module/src/presentation/payment-home/components/bill-list/EmptyBill.tsx",
    statementMap: {
      "0": {
        start: {
          line: 3,
          column: 22
        },
        end: {
          line: 7,
          column: 1
        }
      },
      "1": {
        start: {
          line: 4,
          column: 2
        },
        end: {
          line: 6,
          column: 4
        }
      },
      "2": {
        start: {
          line: 8,
          column: 0
        },
        end: {
          line: 10,
          column: 3
        }
      },
      "3": {
        start: {
          line: 11,
          column: 0
        },
        end: {
          line: 11,
          column: 140
        }
      },
      "4": {
        start: {
          line: 12,
          column: 14
        },
        end: {
          line: 12,
          column: 47
        }
      },
      "5": {
        start: {
          line: 13,
          column: 21
        },
        end: {
          line: 13,
          column: 44
        }
      },
      "6": {
        start: {
          line: 14,
          column: 13
        },
        end: {
          line: 14,
          column: 48
        }
      },
      "7": {
        start: {
          line: 15,
          column: 29
        },
        end: {
          line: 15,
          column: 60
        }
      },
      "8": {
        start: {
          line: 16,
          column: 18
        },
        end: {
          line: 35,
          column: 1
        }
      },
      "9": {
        start: {
          line: 18,
          column: 17
        },
        end: {
          line: 18,
          column: 30
        }
      },
      "10": {
        start: {
          line: 19,
          column: 14
        },
        end: {
          line: 19,
          column: 65
        }
      },
      "11": {
        start: {
          line: 20,
          column: 13
        },
        end: {
          line: 20,
          column: 25
        }
      },
      "12": {
        start: {
          line: 21,
          column: 12
        },
        end: {
          line: 21,
          column: 23
        }
      },
      "13": {
        start: {
          line: 22,
          column: 2
        },
        end: {
          line: 34,
          column: 7
        }
      },
      "14": {
        start: {
          line: 36,
          column: 0
        },
        end: {
          line: 36,
          column: 34
        }
      },
      "15": {
        start: {
          line: 37,
          column: 33
        },
        end: {
          line: 55,
          column: 1
        }
      },
      "16": {
        start: {
          line: 39,
          column: 14
        },
        end: {
          line: 39,
          column: 65
        }
      },
      "17": {
        start: {
          line: 40,
          column: 13
        },
        end: {
          line: 40,
          column: 25
        }
      },
      "18": {
        start: {
          line: 41,
          column: 12
        },
        end: {
          line: 41,
          column: 23
        }
      },
      "19": {
        start: {
          line: 42,
          column: 2
        },
        end: {
          line: 54,
          column: 7
        }
      },
      "20": {
        start: {
          line: 56,
          column: 0
        },
        end: {
          line: 56,
          column: 64
        }
      },
      "21": {
        start: {
          line: 57,
          column: 30
        },
        end: {
          line: 75,
          column: 1
        }
      },
      "22": {
        start: {
          line: 59,
          column: 14
        },
        end: {
          line: 59,
          column: 65
        }
      },
      "23": {
        start: {
          line: 60,
          column: 13
        },
        end: {
          line: 60,
          column: 25
        }
      },
      "24": {
        start: {
          line: 61,
          column: 12
        },
        end: {
          line: 61,
          column: 23
        }
      },
      "25": {
        start: {
          line: 62,
          column: 2
        },
        end: {
          line: 74,
          column: 7
        }
      },
      "26": {
        start: {
          line: 76,
          column: 0
        },
        end: {
          line: 76,
          column: 58
        }
      },
      "27": {
        start: {
          line: 77,
          column: 36
        },
        end: {
          line: 86,
          column: 1
        }
      },
      "28": {
        start: {
          line: 78,
          column: 14
        },
        end: {
          line: 78,
          column: 25
        }
      },
      "29": {
        start: {
          line: 79,
          column: 14
        },
        end: {
          line: 79,
          column: 27
        }
      },
      "30": {
        start: {
          line: 80,
          column: 11
        },
        end: {
          line: 80,
          column: 21
        }
      },
      "31": {
        start: {
          line: 81,
          column: 2
        },
        end: {
          line: 85,
          column: 6
        }
      },
      "32": {
        start: {
          line: 87,
          column: 0
        },
        end: {
          line: 87,
          column: 70
        }
      },
      "33": {
        start: {
          line: 88,
          column: 16
        },
        end: {
          line: 115,
          column: 2
        }
      },
      "34": {
        start: {
          line: 89,
          column: 19
        },
        end: {
          line: 89,
          column: 35
        }
      },
      "35": {
        start: {
          line: 90,
          column: 2
        },
        end: {
          line: 114,
          column: 4
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 3,
            column: 54
          },
          end: {
            line: 3,
            column: 55
          }
        },
        loc: {
          start: {
            line: 3,
            column: 69
          },
          end: {
            line: 7,
            column: 1
          }
        },
        line: 3
      },
      "1": {
        name: "EmptyScreen",
        decl: {
          start: {
            line: 16,
            column: 27
          },
          end: {
            line: 16,
            column: 38
          }
        },
        loc: {
          start: {
            line: 16,
            column: 45
          },
          end: {
            line: 35,
            column: 1
          }
        },
        line: 16
      },
      "2": {
        name: "EmptyBillSystemErrorScreen",
        decl: {
          start: {
            line: 37,
            column: 42
          },
          end: {
            line: 37,
            column: 68
          }
        },
        loc: {
          start: {
            line: 37,
            column: 71
          },
          end: {
            line: 55,
            column: 1
          }
        },
        line: 37
      },
      "3": {
        name: "EmptyBillFilteredScreen",
        decl: {
          start: {
            line: 57,
            column: 39
          },
          end: {
            line: 57,
            column: 62
          }
        },
        loc: {
          start: {
            line: 57,
            column: 65
          },
          end: {
            line: 75,
            column: 1
          }
        },
        line: 57
      },
      "4": {
        name: "EmptyTransactionHistoryScreen",
        decl: {
          start: {
            line: 77,
            column: 45
          },
          end: {
            line: 77,
            column: 74
          }
        },
        loc: {
          start: {
            line: 77,
            column: 82
          },
          end: {
            line: 86,
            column: 1
          }
        },
        line: 77
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 88,
            column: 64
          },
          end: {
            line: 88,
            column: 65
          }
        },
        loc: {
          start: {
            line: 88,
            column: 81
          },
          end: {
            line: 115,
            column: 1
          }
        },
        line: 88
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 3,
            column: 22
          },
          end: {
            line: 7,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 3,
            column: 22
          },
          end: {
            line: 3,
            column: 26
          }
        }, {
          start: {
            line: 3,
            column: 30
          },
          end: {
            line: 3,
            column: 50
          }
        }, {
          start: {
            line: 3,
            column: 54
          },
          end: {
            line: 7,
            column: 1
          }
        }],
        line: 3
      },
      "1": {
        loc: {
          start: {
            line: 4,
            column: 9
          },
          end: {
            line: 6,
            column: 3
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 4,
            column: 33
          },
          end: {
            line: 4,
            column: 36
          }
        }, {
          start: {
            line: 4,
            column: 39
          },
          end: {
            line: 6,
            column: 3
          }
        }],
        line: 4
      },
      "2": {
        loc: {
          start: {
            line: 4,
            column: 9
          },
          end: {
            line: 4,
            column: 30
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 4,
            column: 9
          },
          end: {
            line: 4,
            column: 12
          }
        }, {
          start: {
            line: 4,
            column: 16
          },
          end: {
            line: 4,
            column: 30
          }
        }],
        line: 4
      },
      "3": {
        loc: {
          start: {
            line: 29,
            column: 11
          },
          end: {
            line: 29,
            column: 100
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 29,
            column: 60
          },
          end: {
            line: 29,
            column: 66
          }
        }, {
          start: {
            line: 29,
            column: 69
          },
          end: {
            line: 29,
            column: 100
          }
        }],
        line: 29
      },
      "4": {
        loc: {
          start: {
            line: 32,
            column: 12
          },
          end: {
            line: 32,
            column: 103
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 32,
            column: 62
          },
          end: {
            line: 32,
            column: 68
          }
        }, {
          start: {
            line: 32,
            column: 71
          },
          end: {
            line: 32,
            column: 103
          }
        }],
        line: 32
      },
      "5": {
        loc: {
          start: {
            line: 33,
            column: 35
          },
          end: {
            line: 33,
            column: 105
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 33,
            column: 46
          },
          end: {
            line: 33,
            column: 77
          }
        }, {
          start: {
            line: 33,
            column: 80
          },
          end: {
            line: 33,
            column: 105
          }
        }],
        line: 33
      },
      "6": {
        loc: {
          start: {
            line: 49,
            column: 11
          },
          end: {
            line: 49,
            column: 102
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 49,
            column: 61
          },
          end: {
            line: 49,
            column: 67
          }
        }, {
          start: {
            line: 49,
            column: 70
          },
          end: {
            line: 49,
            column: 102
          }
        }],
        line: 49
      },
      "7": {
        loc: {
          start: {
            line: 52,
            column: 12
          },
          end: {
            line: 52,
            column: 103
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 52,
            column: 62
          },
          end: {
            line: 52,
            column: 68
          }
        }, {
          start: {
            line: 52,
            column: 71
          },
          end: {
            line: 52,
            column: 103
          }
        }],
        line: 52
      },
      "8": {
        loc: {
          start: {
            line: 69,
            column: 11
          },
          end: {
            line: 69,
            column: 102
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 69,
            column: 61
          },
          end: {
            line: 69,
            column: 67
          }
        }, {
          start: {
            line: 69,
            column: 70
          },
          end: {
            line: 69,
            column: 102
          }
        }],
        line: 69
      },
      "9": {
        loc: {
          start: {
            line: 72,
            column: 11
          },
          end: {
            line: 72,
            column: 102
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 72,
            column: 61
          },
          end: {
            line: 72,
            column: 67
          }
        }, {
          start: {
            line: 72,
            column: 70
          },
          end: {
            line: 72,
            column: 102
          }
        }],
        line: 72
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0
    },
    b: {
      "0": [0, 0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0]
    },
    inputSourceMap: {
      version: 3,
      file: undefined,
      names: ["react_1", "__importDefault", "require", "react_native_1", "i18n_1", "msb_shared_component_1", "EmptyScreen", "_ref", "_theme$Typography", "_theme$Typography2", "isRecent", "_ref2", "useMSBStyles", "makeStyle", "styles", "theme", "default", "createElement", "ScrollView", "View", "style", "container", "MSBFastImage", "nameImage", "image", "folder", "MSBFolderImage", "IMAGES", "MSBTextBase", "Typography", "base_semiBold", "content", "translate", "small_regular", "textAlign", "exports", "EmptyBillSystemErrorScreen", "_theme$Typography3", "_theme$Typography4", "_ref3", "containerEmptySavedSystemError", "EmptyBillFilteredScreen", "_theme$Typography5", "_theme$Typography6", "_ref4", "EmptyTransactionHistoryScreen", "_ref5", "title", "type", "MSBEmptyState", "emptyTitle", "emptySubTitle", "createMSBStyleSheet", "_ref6", "SizeGlobal", "alignItems", "flexDirection", "height", "justifyContent", "width", "flex", "getSize", "marginBottom", "Size500", "marginTop"],
      sourceRoot: undefined,
      sources: ["/Users/<USER>/projects/react-native/payment-module/src/presentation/payment-home/components/bill-list/EmptyBill.tsx"],
      sourcesContent: ["import React from 'react';\nimport {Image, ScrollView, View} from 'react-native';\n\n// import Images from '../../../../assets/images/Images';\nimport {translate} from '../../../../locales/i18n';\nimport {\n  createMSBStyleSheet,\n  EmptyType,\n  getSize,\n  MSBEmptyState,\n  MSBFastImage,\n  MSBFolderImage,\n  MSBTextBase,\n  useMSBStyles,\n} from 'msb-shared-component';\n\nexport const EmptyScreen = ({isRecent}: any) => {\n  const {styles, theme} = useMSBStyles(makeStyle);\n\n  return (\n    <ScrollView>\n      <View style={styles.container}>\n        {/* <Image style={styles.image} source={Images.icEmptyBill} /> */}\n        <MSBFastImage nameImage={'empty-data'} style={styles.image} folder={MSBFolderImage.IMAGES} />\n        <MSBTextBase style={theme.Typography?.base_semiBold} content={translate('billingTab.titleEmpty')} />\n        <MSBTextBase\n          style={[theme.Typography?.small_regular, styles.textAlign]}\n          content={translate(isRecent ? 'billingTab.contentEmptyRecent' : 'billingTab.contentEmpty')}\n        />\n      </View>\n    </ScrollView>\n  );\n};\n\nexport const EmptyBillSystemErrorScreen = () => {\n  const {styles, theme} = useMSBStyles(makeStyle);\n\n  return (\n    <ScrollView>\n      <View style={styles.containerEmptySavedSystemError}>\n        {/* <Image style={styles.image} source={Images.icEmptyBillSystemError} /> */}\n        <MSBFastImage nameImage={'coffee'} style={styles.image} folder={MSBFolderImage.IMAGES} />\n        <MSBTextBase\n          style={theme.Typography?.base_semiBold}\n          content={translate('billingTab.titleEmptyContactSystemError')}\n        />\n        <MSBTextBase\n          style={[theme.Typography?.small_regular, styles.textAlign]}\n          content={translate('billingTab.contentEmptyContactSystemError')}\n        />\n      </View>\n    </ScrollView>\n  );\n};\n\nexport const EmptyBillFilteredScreen = () => {\n  const {styles, theme} = useMSBStyles(makeStyle);\n\n  return (\n    <ScrollView>\n      <View style={styles.containerEmptySavedSystemError}>\n        <MSBFastImage nameImage={'empty-data'} style={styles.image} folder={MSBFolderImage.IMAGES} />\n        <MSBTextBase\n          style={theme.Typography?.base_semiBold}\n          content={translate('billingTab.titleEmptyContactFiltered')}\n        />\n        <MSBTextBase\n          style={theme.Typography?.small_regular}\n          content={translate('billingTab.contentEmptyContactFiltered')}\n        />\n      </View>\n    </ScrollView>\n  );\n};\n\nexport const EmptyTransactionHistoryScreen = ({\n  title,\n  content,\n  type,\n}: {\n  title: string;\n  content: string;\n  type: EmptyType;\n}) => {\n  return (\n    <View>\n      <MSBEmptyState emptyTitle={title} emptySubTitle={content} type={type} />\n    </View>\n  );\n};\n\nconst makeStyle = createMSBStyleSheet(({SizeGlobal}) => ({\n  container: {\n    alignItems: 'center',\n    flexDirection: 'column',\n    height: 320,\n    justifyContent: 'center',\n    width: '100%',\n  },\n  containerEmptySavedSystemError: {\n    alignItems: 'center',\n    flexDirection: 'column',\n    flex: 1,\n    justifyContent: 'center',\n    width: '100%',\n  },\n  image: {\n    height: getSize(144),\n    marginBottom: SizeGlobal.Size500,\n    marginTop: SizeGlobal.Size500,\n    width: getSize(144),\n  },\n  textAlign: {\n    textAlign: 'center',\n  },\n}));\n"],
      mappings: ";;;;;;;;;;;AAAA,IAAAA,OAAA,GAAAC,eAAA,CAAAC,OAAA;AACA,IAAAC,cAAA,GAAAD,OAAA;AAGA,IAAAE,MAAA,GAAAF,OAAA;AACA,IAAAG,sBAAA,GAAAH,OAAA;AAWO,IAAMI,WAAW,GAAG,SAAdA,WAAWA,CAAAC,IAAA,EAAuB;EAAA,IAAAC,iBAAA,EAAAC,kBAAA;EAAA,IAAlBC,QAAQ,GAAAH,IAAA,CAARG,QAAQ;EACnC,IAAAC,KAAA,GAAwB,IAAAN,sBAAA,CAAAO,YAAY,EAACC,SAAS,CAAC;IAAxCC,MAAM,GAAAH,KAAA,CAANG,MAAM;IAAEC,KAAK,GAAAJ,KAAA,CAALI,KAAK;EAEpB,OACEf,OAAA,CAAAgB,OAAA,CAAAC,aAAA,CAACd,cAAA,CAAAe,UAAU,QACTlB,OAAA,CAAAgB,OAAA,CAAAC,aAAA,CAACd,cAAA,CAAAgB,IAAI;IAACC,KAAK,EAAEN,MAAM,CAACO;EAAS,GAE3BrB,OAAA,CAAAgB,OAAA,CAAAC,aAAA,CAACZ,sBAAA,CAAAiB,YAAY;IAACC,SAAS,EAAE,YAAY;IAAEH,KAAK,EAAEN,MAAM,CAACU,KAAK;IAAEC,MAAM,EAAEpB,sBAAA,CAAAqB,cAAc,CAACC;EAAM,EAAI,EAC7F3B,OAAA,CAAAgB,OAAA,CAAAC,aAAA,CAACZ,sBAAA,CAAAuB,WAAW;IAACR,KAAK,GAAAZ,iBAAA,GAAEO,KAAK,CAACc,UAAU,qBAAhBrB,iBAAA,CAAkBsB,aAAa;IAAEC,OAAO,EAAE,IAAA3B,MAAA,CAAA4B,SAAS,EAAC,uBAAuB;EAAC,EAAI,EACpGhC,OAAA,CAAAgB,OAAA,CAAAC,aAAA,CAACZ,sBAAA,CAAAuB,WAAW;IACVR,KAAK,EAAE,EAAAX,kBAAA,GAACM,KAAK,CAACc,UAAU,qBAAhBpB,kBAAA,CAAkBwB,aAAa,EAAEnB,MAAM,CAACoB,SAAS,CAAC;IAC1DH,OAAO,EAAE,IAAA3B,MAAA,CAAA4B,SAAS,EAACtB,QAAQ,GAAG,+BAA+B,GAAG,yBAAyB;EAAC,EAC1F,CACG,CACI;AAEjB,CAAC;AAhBYyB,OAAA,CAAA7B,WAAW,GAAAA,WAAA;AAkBjB,IAAM8B,0BAA0B,GAAG,SAA7BA,0BAA0BA,CAAA,EAAQ;EAAA,IAAAC,kBAAA,EAAAC,kBAAA;EAC7C,IAAAC,KAAA,GAAwB,IAAAlC,sBAAA,CAAAO,YAAY,EAACC,SAAS,CAAC;IAAxCC,MAAM,GAAAyB,KAAA,CAANzB,MAAM;IAAEC,KAAK,GAAAwB,KAAA,CAALxB,KAAK;EAEpB,OACEf,OAAA,CAAAgB,OAAA,CAAAC,aAAA,CAACd,cAAA,CAAAe,UAAU,QACTlB,OAAA,CAAAgB,OAAA,CAAAC,aAAA,CAACd,cAAA,CAAAgB,IAAI;IAACC,KAAK,EAAEN,MAAM,CAAC0B;EAA8B,GAEhDxC,OAAA,CAAAgB,OAAA,CAAAC,aAAA,CAACZ,sBAAA,CAAAiB,YAAY;IAACC,SAAS,EAAE,QAAQ;IAAEH,KAAK,EAAEN,MAAM,CAACU,KAAK;IAAEC,MAAM,EAAEpB,sBAAA,CAAAqB,cAAc,CAACC;EAAM,EAAI,EACzF3B,OAAA,CAAAgB,OAAA,CAAAC,aAAA,CAACZ,sBAAA,CAAAuB,WAAW;IACVR,KAAK,GAAAiB,kBAAA,GAAEtB,KAAK,CAACc,UAAU,qBAAhBQ,kBAAA,CAAkBP,aAAa;IACtCC,OAAO,EAAE,IAAA3B,MAAA,CAAA4B,SAAS,EAAC,yCAAyC;EAAC,EAC7D,EACFhC,OAAA,CAAAgB,OAAA,CAAAC,aAAA,CAACZ,sBAAA,CAAAuB,WAAW;IACVR,KAAK,EAAE,EAAAkB,kBAAA,GAACvB,KAAK,CAACc,UAAU,qBAAhBS,kBAAA,CAAkBL,aAAa,EAAEnB,MAAM,CAACoB,SAAS,CAAC;IAC1DH,OAAO,EAAE,IAAA3B,MAAA,CAAA4B,SAAS,EAAC,2CAA2C;EAAC,EAC/D,CACG,CACI;AAEjB,CAAC;AAnBYG,OAAA,CAAAC,0BAA0B,GAAAA,0BAAA;AAqBhC,IAAMK,uBAAuB,GAAG,SAA1BA,uBAAuBA,CAAA,EAAQ;EAAA,IAAAC,kBAAA,EAAAC,kBAAA;EAC1C,IAAAC,KAAA,GAAwB,IAAAvC,sBAAA,CAAAO,YAAY,EAACC,SAAS,CAAC;IAAxCC,MAAM,GAAA8B,KAAA,CAAN9B,MAAM;IAAEC,KAAK,GAAA6B,KAAA,CAAL7B,KAAK;EAEpB,OACEf,OAAA,CAAAgB,OAAA,CAAAC,aAAA,CAACd,cAAA,CAAAe,UAAU,QACTlB,OAAA,CAAAgB,OAAA,CAAAC,aAAA,CAACd,cAAA,CAAAgB,IAAI;IAACC,KAAK,EAAEN,MAAM,CAAC0B;EAA8B,GAChDxC,OAAA,CAAAgB,OAAA,CAAAC,aAAA,CAACZ,sBAAA,CAAAiB,YAAY;IAACC,SAAS,EAAE,YAAY;IAAEH,KAAK,EAAEN,MAAM,CAACU,KAAK;IAAEC,MAAM,EAAEpB,sBAAA,CAAAqB,cAAc,CAACC;EAAM,EAAI,EAC7F3B,OAAA,CAAAgB,OAAA,CAAAC,aAAA,CAACZ,sBAAA,CAAAuB,WAAW;IACVR,KAAK,GAAAsB,kBAAA,GAAE3B,KAAK,CAACc,UAAU,qBAAhBa,kBAAA,CAAkBZ,aAAa;IACtCC,OAAO,EAAE,IAAA3B,MAAA,CAAA4B,SAAS,EAAC,sCAAsC;EAAC,EAC1D,EACFhC,OAAA,CAAAgB,OAAA,CAAAC,aAAA,CAACZ,sBAAA,CAAAuB,WAAW;IACVR,KAAK,GAAAuB,kBAAA,GAAE5B,KAAK,CAACc,UAAU,qBAAhBc,kBAAA,CAAkBV,aAAa;IACtCF,OAAO,EAAE,IAAA3B,MAAA,CAAA4B,SAAS,EAAC,wCAAwC;EAAC,EAC5D,CACG,CACI;AAEjB,CAAC;AAlBYG,OAAA,CAAAM,uBAAuB,GAAAA,uBAAA;AAoB7B,IAAMI,6BAA6B,GAAG,SAAhCA,6BAA6BA,CAAAC,KAAA,EAQrC;EAAA,IAPHC,KAAK,GAAAD,KAAA,CAALC,KAAK;IACLhB,OAAO,GAAAe,KAAA,CAAPf,OAAO;IACPiB,IAAI,GAAAF,KAAA,CAAJE,IAAI;EAMJ,OACEhD,OAAA,CAAAgB,OAAA,CAAAC,aAAA,CAACd,cAAA,CAAAgB,IAAI,QACHnB,OAAA,CAAAgB,OAAA,CAAAC,aAAA,CAACZ,sBAAA,CAAA4C,aAAa;IAACC,UAAU,EAAEH,KAAK;IAAEI,aAAa,EAAEpB,OAAO;IAAEiB,IAAI,EAAEA;EAAI,EAAI,CACnE;AAEX,CAAC;AAdYb,OAAA,CAAAU,6BAA6B,GAAAA,6BAAA;AAgB1C,IAAMhC,SAAS,GAAG,IAAAR,sBAAA,CAAA+C,mBAAmB,EAAC,UAAAC,KAAA;EAAA,IAAEC,UAAU,GAAAD,KAAA,CAAVC,UAAU;EAAA,OAAO;IACvDjC,SAAS,EAAE;MACTkC,UAAU,EAAE,QAAQ;MACpBC,aAAa,EAAE,QAAQ;MACvBC,MAAM,EAAE,GAAG;MACXC,cAAc,EAAE,QAAQ;MACxBC,KAAK,EAAE;KACR;IACDnB,8BAA8B,EAAE;MAC9Be,UAAU,EAAE,QAAQ;MACpBC,aAAa,EAAE,QAAQ;MACvBI,IAAI,EAAE,CAAC;MACPF,cAAc,EAAE,QAAQ;MACxBC,KAAK,EAAE;KACR;IACDnC,KAAK,EAAE;MACLiC,MAAM,EAAE,IAAApD,sBAAA,CAAAwD,OAAO,EAAC,GAAG,CAAC;MACpBC,YAAY,EAAER,UAAU,CAACS,OAAO;MAChCC,SAAS,EAAEV,UAAU,CAACS,OAAO;MAC7BJ,KAAK,EAAE,IAAAtD,sBAAA,CAAAwD,OAAO,EAAC,GAAG;KACnB;IACD3B,SAAS,EAAE;MACTA,SAAS,EAAE;;GAEd;AAAA,CAAC,CAAC",
      ignoreList: []
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "c95c2804c2e799fb2c30a797bb2ed8244fb58ccc"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_1kvid3b9qb = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_1kvid3b9qb();
var __importDefault =
/* istanbul ignore next */
(cov_1kvid3b9qb().s[0]++,
/* istanbul ignore next */
(cov_1kvid3b9qb().b[0][0]++, this) &&
/* istanbul ignore next */
(cov_1kvid3b9qb().b[0][1]++, this.__importDefault) ||
/* istanbul ignore next */
(cov_1kvid3b9qb().b[0][2]++, function (mod) {
  /* istanbul ignore next */
  cov_1kvid3b9qb().f[0]++;
  cov_1kvid3b9qb().s[1]++;
  return /* istanbul ignore next */(cov_1kvid3b9qb().b[2][0]++, mod) &&
  /* istanbul ignore next */
  (cov_1kvid3b9qb().b[2][1]++, mod.__esModule) ?
  /* istanbul ignore next */
  (cov_1kvid3b9qb().b[1][0]++, mod) :
  /* istanbul ignore next */
  (cov_1kvid3b9qb().b[1][1]++, {
    "default": mod
  });
}));
/* istanbul ignore next */
cov_1kvid3b9qb().s[2]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_1kvid3b9qb().s[3]++;
exports.EmptyTransactionHistoryScreen = exports.EmptyBillFilteredScreen = exports.EmptyBillSystemErrorScreen = exports.EmptyScreen = void 0;
var react_1 =
/* istanbul ignore next */
(cov_1kvid3b9qb().s[4]++, __importDefault(require("react")));
var react_native_1 =
/* istanbul ignore next */
(cov_1kvid3b9qb().s[5]++, require("react-native"));
var i18n_1 =
/* istanbul ignore next */
(cov_1kvid3b9qb().s[6]++, require("../../../../locales/i18n"));
var msb_shared_component_1 =
/* istanbul ignore next */
(cov_1kvid3b9qb().s[7]++, require("msb-shared-component"));
/* istanbul ignore next */
cov_1kvid3b9qb().s[8]++;
var EmptyScreen = function EmptyScreen(_ref) {
  /* istanbul ignore next */
  cov_1kvid3b9qb().f[1]++;
  var _theme$Typography, _theme$Typography2;
  var isRecent =
  /* istanbul ignore next */
  (cov_1kvid3b9qb().s[9]++, _ref.isRecent);
  var _ref2 =
    /* istanbul ignore next */
    (cov_1kvid3b9qb().s[10]++, (0, msb_shared_component_1.useMSBStyles)(makeStyle)),
    styles =
    /* istanbul ignore next */
    (cov_1kvid3b9qb().s[11]++, _ref2.styles),
    theme =
    /* istanbul ignore next */
    (cov_1kvid3b9qb().s[12]++, _ref2.theme);
  /* istanbul ignore next */
  cov_1kvid3b9qb().s[13]++;
  return react_1.default.createElement(react_native_1.ScrollView, null, react_1.default.createElement(react_native_1.View, {
    style: styles.container
  }, react_1.default.createElement(msb_shared_component_1.MSBFastImage, {
    nameImage: 'empty-data',
    style: styles.image,
    folder: msb_shared_component_1.MSBFolderImage.IMAGES
  }), react_1.default.createElement(msb_shared_component_1.MSBTextBase, {
    style: (_theme$Typography = theme.Typography) == null ?
    /* istanbul ignore next */
    (cov_1kvid3b9qb().b[3][0]++, void 0) :
    /* istanbul ignore next */
    (cov_1kvid3b9qb().b[3][1]++, _theme$Typography.base_semiBold),
    content: (0, i18n_1.translate)('billingTab.titleEmpty')
  }), react_1.default.createElement(msb_shared_component_1.MSBTextBase, {
    style: [(_theme$Typography2 = theme.Typography) == null ?
    /* istanbul ignore next */
    (cov_1kvid3b9qb().b[4][0]++, void 0) :
    /* istanbul ignore next */
    (cov_1kvid3b9qb().b[4][1]++, _theme$Typography2.small_regular), styles.textAlign],
    content: (0, i18n_1.translate)(isRecent ?
    /* istanbul ignore next */
    (cov_1kvid3b9qb().b[5][0]++, 'billingTab.contentEmptyRecent') :
    /* istanbul ignore next */
    (cov_1kvid3b9qb().b[5][1]++, 'billingTab.contentEmpty'))
  })));
};
/* istanbul ignore next */
cov_1kvid3b9qb().s[14]++;
exports.EmptyScreen = EmptyScreen;
/* istanbul ignore next */
cov_1kvid3b9qb().s[15]++;
var EmptyBillSystemErrorScreen = function EmptyBillSystemErrorScreen() {
  /* istanbul ignore next */
  cov_1kvid3b9qb().f[2]++;
  var _theme$Typography3, _theme$Typography4;
  var _ref3 =
    /* istanbul ignore next */
    (cov_1kvid3b9qb().s[16]++, (0, msb_shared_component_1.useMSBStyles)(makeStyle)),
    styles =
    /* istanbul ignore next */
    (cov_1kvid3b9qb().s[17]++, _ref3.styles),
    theme =
    /* istanbul ignore next */
    (cov_1kvid3b9qb().s[18]++, _ref3.theme);
  /* istanbul ignore next */
  cov_1kvid3b9qb().s[19]++;
  return react_1.default.createElement(react_native_1.ScrollView, null, react_1.default.createElement(react_native_1.View, {
    style: styles.containerEmptySavedSystemError
  }, react_1.default.createElement(msb_shared_component_1.MSBFastImage, {
    nameImage: 'coffee',
    style: styles.image,
    folder: msb_shared_component_1.MSBFolderImage.IMAGES
  }), react_1.default.createElement(msb_shared_component_1.MSBTextBase, {
    style: (_theme$Typography3 = theme.Typography) == null ?
    /* istanbul ignore next */
    (cov_1kvid3b9qb().b[6][0]++, void 0) :
    /* istanbul ignore next */
    (cov_1kvid3b9qb().b[6][1]++, _theme$Typography3.base_semiBold),
    content: (0, i18n_1.translate)('billingTab.titleEmptyContactSystemError')
  }), react_1.default.createElement(msb_shared_component_1.MSBTextBase, {
    style: [(_theme$Typography4 = theme.Typography) == null ?
    /* istanbul ignore next */
    (cov_1kvid3b9qb().b[7][0]++, void 0) :
    /* istanbul ignore next */
    (cov_1kvid3b9qb().b[7][1]++, _theme$Typography4.small_regular), styles.textAlign],
    content: (0, i18n_1.translate)('billingTab.contentEmptyContactSystemError')
  })));
};
/* istanbul ignore next */
cov_1kvid3b9qb().s[20]++;
exports.EmptyBillSystemErrorScreen = EmptyBillSystemErrorScreen;
/* istanbul ignore next */
cov_1kvid3b9qb().s[21]++;
var EmptyBillFilteredScreen = function EmptyBillFilteredScreen() {
  /* istanbul ignore next */
  cov_1kvid3b9qb().f[3]++;
  var _theme$Typography5, _theme$Typography6;
  var _ref4 =
    /* istanbul ignore next */
    (cov_1kvid3b9qb().s[22]++, (0, msb_shared_component_1.useMSBStyles)(makeStyle)),
    styles =
    /* istanbul ignore next */
    (cov_1kvid3b9qb().s[23]++, _ref4.styles),
    theme =
    /* istanbul ignore next */
    (cov_1kvid3b9qb().s[24]++, _ref4.theme);
  /* istanbul ignore next */
  cov_1kvid3b9qb().s[25]++;
  return react_1.default.createElement(react_native_1.ScrollView, null, react_1.default.createElement(react_native_1.View, {
    style: styles.containerEmptySavedSystemError
  }, react_1.default.createElement(msb_shared_component_1.MSBFastImage, {
    nameImage: 'empty-data',
    style: styles.image,
    folder: msb_shared_component_1.MSBFolderImage.IMAGES
  }), react_1.default.createElement(msb_shared_component_1.MSBTextBase, {
    style: (_theme$Typography5 = theme.Typography) == null ?
    /* istanbul ignore next */
    (cov_1kvid3b9qb().b[8][0]++, void 0) :
    /* istanbul ignore next */
    (cov_1kvid3b9qb().b[8][1]++, _theme$Typography5.base_semiBold),
    content: (0, i18n_1.translate)('billingTab.titleEmptyContactFiltered')
  }), react_1.default.createElement(msb_shared_component_1.MSBTextBase, {
    style: (_theme$Typography6 = theme.Typography) == null ?
    /* istanbul ignore next */
    (cov_1kvid3b9qb().b[9][0]++, void 0) :
    /* istanbul ignore next */
    (cov_1kvid3b9qb().b[9][1]++, _theme$Typography6.small_regular),
    content: (0, i18n_1.translate)('billingTab.contentEmptyContactFiltered')
  })));
};
/* istanbul ignore next */
cov_1kvid3b9qb().s[26]++;
exports.EmptyBillFilteredScreen = EmptyBillFilteredScreen;
/* istanbul ignore next */
cov_1kvid3b9qb().s[27]++;
var EmptyTransactionHistoryScreen = function EmptyTransactionHistoryScreen(_ref5) {
  /* istanbul ignore next */
  cov_1kvid3b9qb().f[4]++;
  var title =
    /* istanbul ignore next */
    (cov_1kvid3b9qb().s[28]++, _ref5.title),
    content =
    /* istanbul ignore next */
    (cov_1kvid3b9qb().s[29]++, _ref5.content),
    type =
    /* istanbul ignore next */
    (cov_1kvid3b9qb().s[30]++, _ref5.type);
  /* istanbul ignore next */
  cov_1kvid3b9qb().s[31]++;
  return react_1.default.createElement(react_native_1.View, null, react_1.default.createElement(msb_shared_component_1.MSBEmptyState, {
    emptyTitle: title,
    emptySubTitle: content,
    type: type
  }));
};
/* istanbul ignore next */
cov_1kvid3b9qb().s[32]++;
exports.EmptyTransactionHistoryScreen = EmptyTransactionHistoryScreen;
var makeStyle =
/* istanbul ignore next */
(cov_1kvid3b9qb().s[33]++, (0, msb_shared_component_1.createMSBStyleSheet)(function (_ref6) {
  /* istanbul ignore next */
  cov_1kvid3b9qb().f[5]++;
  var SizeGlobal =
  /* istanbul ignore next */
  (cov_1kvid3b9qb().s[34]++, _ref6.SizeGlobal);
  /* istanbul ignore next */
  cov_1kvid3b9qb().s[35]++;
  return {
    container: {
      alignItems: 'center',
      flexDirection: 'column',
      height: 320,
      justifyContent: 'center',
      width: '100%'
    },
    containerEmptySavedSystemError: {
      alignItems: 'center',
      flexDirection: 'column',
      flex: 1,
      justifyContent: 'center',
      width: '100%'
    },
    image: {
      height: (0, msb_shared_component_1.getSize)(144),
      marginBottom: SizeGlobal.Size500,
      marginTop: SizeGlobal.Size500,
      width: (0, msb_shared_component_1.getSize)(144)
    },
    textAlign: {
      textAlign: 'center'
    }
  };
}));
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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