570939b6046c391728e3bcd552c28f69
'use strict';

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.LightSpeedOutData = exports.LightSpeedOut = exports.LightSpeedInData = exports.LightSpeedIn = void 0;
var _animationParser = require("../animationParser.js");
var DEFAULT_LIGHTSPEED_TIME = 0.3;
var LightSpeedInData = exports.LightSpeedInData = {
  LightSpeedInRight: {
    name: 'LightSpeedInRight',
    style: {
      0: {
        transform: [{
          translateX: '100vw',
          skewX: '-45deg'
        }],
        opacity: 0
      },
      70: {
        transform: [{
          skewX: '10deg'
        }]
      },
      85: {
        transform: [{
          skewX: '-5deg'
        }]
      },
      100: {
        transform: [{
          skewX: '0deg'
        }]
      }
    },
    duration: DEFAULT_LIGHTSPEED_TIME
  },
  LightSpeedInLeft: {
    name: 'LightSpeedInLeft',
    style: {
      0: {
        transform: [{
          translateX: '-100vw',
          skewX: '45deg'
        }],
        opacity: 0
      },
      70: {
        transform: [{
          skewX: '-10deg'
        }]
      },
      85: {
        transform: [{
          skewX: '5deg'
        }]
      },
      100: {
        transform: [{
          skewX: '0deg'
        }]
      }
    },
    duration: DEFAULT_LIGHTSPEED_TIME
  }
};
var LightSpeedOutData = exports.LightSpeedOutData = {
  LightSpeedOutRight: {
    name: 'LightSpeedOutRight',
    style: {
      0: {
        transform: [{
          translateX: '0vw',
          skewX: '0deg'
        }],
        opacity: 1
      },
      100: {
        transform: [{
          translateX: '100vw',
          skewX: '-45deg'
        }],
        opacity: 0
      }
    },
    duration: DEFAULT_LIGHTSPEED_TIME
  },
  LightSpeedOutLeft: {
    name: 'LightSpeedOutLeft',
    style: {
      0: {
        transform: [{
          translateX: '0vw',
          skew: '0deg'
        }],
        opacity: 1
      },
      100: {
        transform: [{
          translateX: '-100vw',
          skew: '45deg'
        }],
        opacity: 0
      }
    },
    duration: DEFAULT_LIGHTSPEED_TIME
  }
};
var LightSpeedIn = exports.LightSpeedIn = {
  LightSpeedInRight: {
    style: (0, _animationParser.convertAnimationObjectToKeyframes)(LightSpeedInData.LightSpeedInRight),
    duration: LightSpeedInData.LightSpeedInRight.duration
  },
  LightSpeedInLeft: {
    style: (0, _animationParser.convertAnimationObjectToKeyframes)(LightSpeedInData.LightSpeedInLeft),
    duration: LightSpeedInData.LightSpeedInLeft.duration
  }
};
var LightSpeedOut = exports.LightSpeedOut = {
  LightSpeedOutRight: {
    style: (0, _animationParser.convertAnimationObjectToKeyframes)(LightSpeedOutData.LightSpeedOutRight),
    duration: LightSpeedOutData.LightSpeedOutRight.duration
  },
  LightSpeedOutLeft: {
    style: (0, _animationParser.convertAnimationObjectToKeyframes)(LightSpeedOutData.LightSpeedOutLeft),
    duration: LightSpeedOutData.LightSpeedOutLeft.duration
  }
};
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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