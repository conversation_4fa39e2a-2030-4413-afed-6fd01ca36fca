{"version": 3, "names": [], "sources": ["/Users/<USER>/projects/react-native/payment-module/src/core/BaseResponse.ts"], "sourcesContent": ["export interface MSBStatus {\n  code?: string;\n  message?: string;\n  title?: string;\n}\n\nexport interface MSBError {\n  message?: string;\n  key?: string;\n  context?: MSBLocaliztion;\n}\n\nexport interface MSBLocaliztion {\n  vi?: string;\n  en?: string;\n}\n\n// export interface BaseResponse<T> {\n//   status?: MSBStatus;\n//   data?: T;\n//   errors?: [MSBError];\n// }\n\nexport type BaseResponse<T> = {\n  errors?: [MSBError] | null | undefined;\n} & T;\n\n// export const isValidResponse = <T>(response: BaseResponse<T> | null | undefined): boolean => {\n//   console.log('isValidResponse', !(!response || response.errors));\n//   if (!response) {\n//     return false;\n//   } // Kiểm tra response có tồn tại không\n//   // if (!response.status || response.status?.message != 'Success') return false; // Kiểm tra status có tồn tại không\n//   if (response.errors) {\n//     return false;\n//   } // Nế<PERSON> có lỗi thì response không hợp lệ\n//   // if (response.data === undefined || response.data === null) return false; // Kiểm tra data có tồn tại không\n//   return true;\n// };\n"], "mappings": "", "ignoreList": []}