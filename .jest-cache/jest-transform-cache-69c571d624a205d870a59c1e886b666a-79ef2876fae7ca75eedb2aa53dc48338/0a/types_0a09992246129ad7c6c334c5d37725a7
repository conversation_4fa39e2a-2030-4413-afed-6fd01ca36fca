9a87ccecdfe70987cade1652ec44837f
"use strict";

/* istanbul ignore next */
function cov_1qgwdtfd7u() {
  var path = "/Users/<USER>/projects/react-native/payment-module/src/components/bill-info/types.ts";
  var hash = "50183a26d832583a49f71c98a9048d3cf4f14d78";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/projects/react-native/payment-module/src/components/bill-info/types.ts",
    statementMap: {
      "0": {
        start: {
          line: 3,
          column: 0
        },
        end: {
          line: 5,
          column: 3
        }
      }
    },
    fnMap: {},
    branchMap: {},
    s: {
      "0": 0
    },
    f: {},
    b: {},
    inputSourceMap: {
      version: 3,
      file: undefined,
      names: [],
      sourceRoot: undefined,
      sources: ["/Users/<USER>/projects/react-native/payment-module/src/components/bill-info/types.ts"],
      sourcesContent: ["import {TextStyle, ViewStyle} from 'react-native';\nimport {MSBIcons} from 'msb-shared-component';\n\nimport {SafeAny} from '../../commons/Constants';\n\nexport type BillInfoProps = {\n  style?: ViewStyle;\n  styleName?: TextStyle;\n  styleBankName?: TextStyle;\n  styleAccountNo?: TextStyle;\n  onPress?: (tag?: string) => SafeAny;\n  title?: string;\n  icon?: MSBIcons;\n  name?: string;\n  bankName?: string;\n  bankAlias?: string;\n  isNotShowBankName?: boolean;\n  bankLogo?: string;\n  isUppercase?: boolean;\n};\n"],
      mappings: "",
      ignoreList: []
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "50183a26d832583a49f71c98a9048d3cf4f14d78"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_1qgwdtfd7u = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_1qgwdtfd7u();
cov_1qgwdtfd7u().s[0]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6W10sInNvdXJjZXMiOlsiL1VzZXJzL3NvbnNhbXNldC9wcm9qZWN0cy9yZWFjdC1uYXRpdmUvcGF5bWVudC1tb2R1bGUvc3JjL2NvbXBvbmVudHMvYmlsbC1pbmZvL3R5cGVzLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7VGV4dFN0eWxlLCBWaWV3U3R5bGV9IGZyb20gJ3JlYWN0LW5hdGl2ZSc7XG5pbXBvcnQge01TQkljb25zfSBmcm9tICdtc2Itc2hhcmVkLWNvbXBvbmVudCc7XG5cbmltcG9ydCB7U2FmZUFueX0gZnJvbSAnLi4vLi4vY29tbW9ucy9Db25zdGFudHMnO1xuXG5leHBvcnQgdHlwZSBCaWxsSW5mb1Byb3BzID0ge1xuICBzdHlsZT86IFZpZXdTdHlsZTtcbiAgc3R5bGVOYW1lPzogVGV4dFN0eWxlO1xuICBzdHlsZUJhbmtOYW1lPzogVGV4dFN0eWxlO1xuICBzdHlsZUFjY291bnRObz86IFRleHRTdHlsZTtcbiAgb25QcmVzcz86ICh0YWc/OiBzdHJpbmcpID0+IFNhZmVBbnk7XG4gIHRpdGxlPzogc3RyaW5nO1xuICBpY29uPzogTVNCSWNvbnM7XG4gIG5hbWU/OiBzdHJpbmc7XG4gIGJhbmtOYW1lPzogc3RyaW5nO1xuICBiYW5rQWxpYXM/OiBzdHJpbmc7XG4gIGlzTm90U2hvd0JhbmtOYW1lPzogYm9vbGVhbjtcbiAgYmFua0xvZ28/OiBzdHJpbmc7XG4gIGlzVXBwZXJjYXNlPzogYm9vbGVhbjtcbn07XG4iXSwibWFwcGluZ3MiOiIiLCJpZ25vcmVMaXN0IjpbXX0=