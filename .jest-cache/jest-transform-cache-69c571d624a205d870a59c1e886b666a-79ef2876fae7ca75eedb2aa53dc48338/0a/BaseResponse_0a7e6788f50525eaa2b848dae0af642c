490d2d8f4c5b10534e8b5a0e29c4d018
"use strict";

/* istanbul ignore next */
function cov_2dkj3cmskh() {
  var path = "/Users/<USER>/projects/react-native/payment-module/src/core/BaseResponse.ts";
  var hash = "8c0a4ea4a80636e9fea629cb73c930854df68c12";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/projects/react-native/payment-module/src/core/BaseResponse.ts",
    statementMap: {
      "0": {
        start: {
          line: 3,
          column: 0
        },
        end: {
          line: 5,
          column: 3
        }
      }
    },
    fnMap: {},
    branchMap: {},
    s: {
      "0": 0
    },
    f: {},
    b: {},
    inputSourceMap: {
      version: 3,
      file: undefined,
      names: [],
      sourceRoot: undefined,
      sources: ["/Users/<USER>/projects/react-native/payment-module/src/core/BaseResponse.ts"],
      sourcesContent: ["export interface MSBStatus {\n  code?: string;\n  message?: string;\n  title?: string;\n}\n\nexport interface MSBError {\n  message?: string;\n  key?: string;\n  context?: MSBLocaliztion;\n}\n\nexport interface MSBLocaliztion {\n  vi?: string;\n  en?: string;\n}\n\n// export interface BaseResponse<T> {\n//   status?: MSBStatus;\n//   data?: T;\n//   errors?: [MSBError];\n// }\n\nexport type BaseResponse<T> = {\n  errors?: [MSBError] | null | undefined;\n} & T;\n\n// export const isValidResponse = <T>(response: BaseResponse<T> | null | undefined): boolean => {\n//   console.log('isValidResponse', !(!response || response.errors));\n//   if (!response) {\n//     return false;\n//   } // Ki\u1EC3m tra response c\xF3 t\u1ED3n t\u1EA1i kh\xF4ng\n//   // if (!response.status || response.status?.message != 'Success') return false; // Ki\u1EC3m tra status c\xF3 t\u1ED3n t\u1EA1i kh\xF4ng\n//   if (response.errors) {\n//     return false;\n//   } // N\u1EBFu c\xF3 l\u1ED7i th\xEC response kh\xF4ng h\u1EE3p l\u1EC7\n//   // if (response.data === undefined || response.data === null) return false; // Ki\u1EC3m tra data c\xF3 t\u1ED3n t\u1EA1i kh\xF4ng\n//   return true;\n// };\n"],
      mappings: "",
      ignoreList: []
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "8c0a4ea4a80636e9fea629cb73c930854df68c12"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_2dkj3cmskh = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_2dkj3cmskh();
cov_2dkj3cmskh().s[0]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6W10sInNvdXJjZXMiOlsiL1VzZXJzL3NvbnNhbXNldC9wcm9qZWN0cy9yZWFjdC1uYXRpdmUvcGF5bWVudC1tb2R1bGUvc3JjL2NvcmUvQmFzZVJlc3BvbnNlLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBpbnRlcmZhY2UgTVNCU3RhdHVzIHtcbiAgY29kZT86IHN0cmluZztcbiAgbWVzc2FnZT86IHN0cmluZztcbiAgdGl0bGU/OiBzdHJpbmc7XG59XG5cbmV4cG9ydCBpbnRlcmZhY2UgTVNCRXJyb3Ige1xuICBtZXNzYWdlPzogc3RyaW5nO1xuICBrZXk/OiBzdHJpbmc7XG4gIGNvbnRleHQ/OiBNU0JMb2NhbGl6dGlvbjtcbn1cblxuZXhwb3J0IGludGVyZmFjZSBNU0JMb2NhbGl6dGlvbiB7XG4gIHZpPzogc3RyaW5nO1xuICBlbj86IHN0cmluZztcbn1cblxuLy8gZXhwb3J0IGludGVyZmFjZSBCYXNlUmVzcG9uc2U8VD4ge1xuLy8gICBzdGF0dXM/OiBNU0JTdGF0dXM7XG4vLyAgIGRhdGE/OiBUO1xuLy8gICBlcnJvcnM/OiBbTVNCRXJyb3JdO1xuLy8gfVxuXG5leHBvcnQgdHlwZSBCYXNlUmVzcG9uc2U8VD4gPSB7XG4gIGVycm9ycz86IFtNU0JFcnJvcl0gfCBudWxsIHwgdW5kZWZpbmVkO1xufSAmIFQ7XG5cbi8vIGV4cG9ydCBjb25zdCBpc1ZhbGlkUmVzcG9uc2UgPSA8VD4ocmVzcG9uc2U6IEJhc2VSZXNwb25zZTxUPiB8IG51bGwgfCB1bmRlZmluZWQpOiBib29sZWFuID0+IHtcbi8vICAgY29uc29sZS5sb2coJ2lzVmFsaWRSZXNwb25zZScsICEoIXJlc3BvbnNlIHx8IHJlc3BvbnNlLmVycm9ycykpO1xuLy8gICBpZiAoIXJlc3BvbnNlKSB7XG4vLyAgICAgcmV0dXJuIGZhbHNlO1xuLy8gICB9IC8vIEtp4buDbSB0cmEgcmVzcG9uc2UgY8OzIHThu5NuIHThuqFpIGtow7RuZ1xuLy8gICAvLyBpZiAoIXJlc3BvbnNlLnN0YXR1cyB8fCByZXNwb25zZS5zdGF0dXM/Lm1lc3NhZ2UgIT0gJ1N1Y2Nlc3MnKSByZXR1cm4gZmFsc2U7IC8vIEtp4buDbSB0cmEgc3RhdHVzIGPDsyB04buTbiB04bqhaSBraMO0bmdcbi8vICAgaWYgKHJlc3BvbnNlLmVycm9ycykge1xuLy8gICAgIHJldHVybiBmYWxzZTtcbi8vICAgfSAvLyBO4bq/dSBjw7MgbOG7l2kgdGjDrCByZXNwb25zZSBraMO0bmcgaOG7o3AgbOG7h1xuLy8gICAvLyBpZiAocmVzcG9uc2UuZGF0YSA9PT0gdW5kZWZpbmVkIHx8IHJlc3BvbnNlLmRhdGEgPT09IG51bGwpIHJldHVybiBmYWxzZTsgLy8gS2nhu4NtIHRyYSBkYXRhIGPDsyB04buTbiB04bqhaSBraMO0bmdcbi8vICAgcmV0dXJuIHRydWU7XG4vLyB9O1xuIl0sIm1hcHBpbmdzIjoiIiwiaWdub3JlTGlzdCI6W119