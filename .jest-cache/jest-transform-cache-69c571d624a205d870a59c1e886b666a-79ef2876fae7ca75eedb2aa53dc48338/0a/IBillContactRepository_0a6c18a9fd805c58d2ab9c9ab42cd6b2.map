{"version": 3, "names": [], "sources": ["/Users/<USER>/projects/react-native/payment-module/src/domain/repositories/IBillContactRepository.ts"], "sourcesContent": ["import {BillHistoryModel} from '../entities/get-my-bill-history-list/GetMyBillHistoryListModel';\nimport {GetMyBillHistoryListRequest} from '../../data/models/get-my-bill-history-list/GetMyBillHistoryListRequest';\nimport {GetMyBillContactRecentListModel} from '../entities/get-my-bill-contact-recent-list/GetMyBillContactRecentListModel';\nimport {GetMyBillContactRecentListRequest} from '../../data/models/get-my-bill-contact-recent-list/GetMyBillContactRecentListRequest';\nimport {MyBillContactListModel} from '../entities/my-bill-contact-list/MyBillContactListModel';\nimport {EditBillContactModel} from '../entities/edit-bill-contact/EditBillContactModel';\nimport {EditBillContactRequest} from '../../data/models/edit-bill-contact/EditBillContactRequest';\nimport {DeleteBillContactModel} from '../entities/delete-bill-contact/DeleteBillContactModel';\nimport {DeleteBillContactRequest} from '../../data/models/delete-bill-contact/DeleteBillContactRequest';\nimport {SaveBillContactModel} from '../entities/save-bill-contact/SaveBillContactModel';\nimport {SaveBillContactRequest} from '../../data/models/save-bill-contact/SaveBillContactRequest';\nimport {BaseResponse} from '../../core/BaseResponse';\n\nexport interface IBillContactRepository {\n  saveBillContact(request: SaveBillContactRequest): Promise<BaseResponse<SaveBillContactModel>>;\n  deleteBillContact(request: DeleteBillContactRequest): Promise<BaseResponse<DeleteBillContactModel>>;\n  editBillContact(request: EditBillContactRequest): Promise<BaseResponse<EditBillContactModel>>;\n  myBillContactList(): Promise<BaseResponse<MyBillContactListModel>>;\n  getMyBillContactRecentList(\n    request: GetMyBillContactRecentListRequest,\n  ): Promise<BaseResponse<GetMyBillContactRecentListModel>>;\n  getMyBillHistoryList(request: GetMyBillHistoryListRequest): Promise<BaseResponse<BillHistoryModel>>;\n}\n"], "mappings": "", "ignoreList": []}