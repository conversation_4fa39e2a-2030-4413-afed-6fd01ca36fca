{"version": 3, "names": ["Object", "defineProperty", "exports", "value", "LightSpeedOutData", "LightSpeedOut", "LightSpeedInData", "LightSpeedIn", "_animation<PERSON><PERSON>er", "require", "DEFAULT_LIGHTSPEED_TIME", "LightSpeedInRight", "name", "style", "transform", "translateX", "skewX", "opacity", "duration", "LightSpeedInLeft", "LightSpeedOutRight", "LightSpeedOutLeft", "skew", "convertAnimationObjectToKeyframes"], "sources": ["../../../../../src/layoutReanimation/web/animation/Lightspeed.web.ts"], "sourcesContent": [null], "mappings": "AAAA,YAAY;;AAAAA,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,KAAA;AAAA;AAAAD,OAAA,CAAAE,iBAAA,GAAAF,OAAA,CAAAG,aAAA,GAAAH,OAAA,CAAAI,gBAAA,GAAAJ,OAAA,CAAAK,YAAA;AACZ,IAAAC,gBAAA,GAAAC,OAAA;AAEA,IAAMC,uBAAuB,GAAG,GAAG;AAE5B,IAAMJ,gBAAgB,GAAAJ,OAAA,CAAAI,gBAAA,GAAG;EAC9BK,iBAAiB,EAAE;IACjBC,IAAI,EAAE,mBAAmB;IACzBC,KAAK,EAAE;MACL,CAAC,EAAE;QACDC,SAAS,EAAE,CAAC;UAAEC,UAAU,EAAE,OAAO;UAAEC,KAAK,EAAE;QAAS,CAAC,CAAC;QACrDC,OAAO,EAAE;MACX,CAAC;MACD,EAAE,EAAE;QAAEH,SAAS,EAAE,CAAC;UAAEE,KAAK,EAAE;QAAQ,CAAC;MAAE,CAAC;MACvC,EAAE,EAAE;QAAEF,SAAS,EAAE,CAAC;UAAEE,KAAK,EAAE;QAAQ,CAAC;MAAE,CAAC;MACvC,GAAG,EAAE;QAAEF,SAAS,EAAE,CAAC;UAAEE,KAAK,EAAE;QAAO,CAAC;MAAE;IACxC,CAAC;IACDE,QAAQ,EAAER;EACZ,CAAC;EAEDS,gBAAgB,EAAE;IAChBP,IAAI,EAAE,kBAAkB;IACxBC,KAAK,EAAE;MACL,CAAC,EAAE;QACDC,SAAS,EAAE,CAAC;UAAEC,UAAU,EAAE,QAAQ;UAAEC,KAAK,EAAE;QAAQ,CAAC,CAAC;QACrDC,OAAO,EAAE;MACX,CAAC;MACD,EAAE,EAAE;QAAEH,SAAS,EAAE,CAAC;UAAEE,KAAK,EAAE;QAAS,CAAC;MAAE,CAAC;MACxC,EAAE,EAAE;QAAEF,SAAS,EAAE,CAAC;UAAEE,KAAK,EAAE;QAAO,CAAC;MAAE,CAAC;MACtC,GAAG,EAAE;QAAEF,SAAS,EAAE,CAAC;UAAEE,KAAK,EAAE;QAAO,CAAC;MAAE;IACxC,CAAC;IACDE,QAAQ,EAAER;EACZ;AACF,CAAC;AAEM,IAAMN,iBAAiB,GAAAF,OAAA,CAAAE,iBAAA,GAAG;EAC/BgB,kBAAkB,EAAE;IAClBR,IAAI,EAAE,oBAAoB;IAC1BC,KAAK,EAAE;MACL,CAAC,EAAE;QACDC,SAAS,EAAE,CAAC;UAAEC,UAAU,EAAE,KAAK;UAAEC,KAAK,EAAE;QAAO,CAAC,CAAC;QACjDC,OAAO,EAAE;MACX,CAAC;MACD,GAAG,EAAE;QACHH,SAAS,EAAE,CAAC;UAAEC,UAAU,EAAE,OAAO;UAAEC,KAAK,EAAE;QAAS,CAAC,CAAC;QACrDC,OAAO,EAAE;MACX;IACF,CAAC;IACDC,QAAQ,EAAER;EACZ,CAAC;EAEDW,iBAAiB,EAAE;IACjBT,IAAI,EAAE,mBAAmB;IACzBC,KAAK,EAAE;MACL,CAAC,EAAE;QACDC,SAAS,EAAE,CAAC;UAAEC,UAAU,EAAE,KAAK;UAAEO,IAAI,EAAE;QAAO,CAAC,CAAC;QAChDL,OAAO,EAAE;MACX,CAAC;MACD,GAAG,EAAE;QACHH,SAAS,EAAE,CAAC;UAAEC,UAAU,EAAE,QAAQ;UAAEO,IAAI,EAAE;QAAQ,CAAC,CAAC;QACpDL,OAAO,EAAE;MACX;IACF,CAAC;IACDC,QAAQ,EAAER;EACZ;AACF,CAAC;AAEM,IAAMH,YAAY,GAAAL,OAAA,CAAAK,YAAA,GAAG;EAC1BI,iBAAiB,EAAE;IACjBE,KAAK,EAAE,IAAAU,kDAAiC,EACtCjB,gBAAgB,CAACK,iBACnB,CAAC;IACDO,QAAQ,EAAEZ,gBAAgB,CAACK,iBAAiB,CAACO;EAC/C,CAAC;EACDC,gBAAgB,EAAE;IAChBN,KAAK,EAAE,IAAAU,kDAAiC,EAACjB,gBAAgB,CAACa,gBAAgB,CAAC;IAC3ED,QAAQ,EAAEZ,gBAAgB,CAACa,gBAAgB,CAACD;EAC9C;AACF,CAAC;AAEM,IAAMb,aAAa,GAAAH,OAAA,CAAAG,aAAA,GAAG;EAC3Be,kBAAkB,EAAE;IAClBP,KAAK,EAAE,IAAAU,kDAAiC,EACtCnB,iBAAiB,CAACgB,kBACpB,CAAC;IACDF,QAAQ,EAAEd,iBAAiB,CAACgB,kBAAkB,CAACF;EACjD,CAAC;EACDG,iBAAiB,EAAE;IACjBR,KAAK,EAAE,IAAAU,kDAAiC,EACtCnB,iBAAiB,CAACiB,iBACpB,CAAC;IACDH,QAAQ,EAAEd,iBAAiB,CAACiB,iBAAiB,CAACH;EAChD;AACF,CAAC", "ignoreList": []}