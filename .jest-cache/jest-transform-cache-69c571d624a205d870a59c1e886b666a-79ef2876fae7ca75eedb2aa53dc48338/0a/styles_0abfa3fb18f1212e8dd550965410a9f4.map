{"version": 3, "names": ["cov_29v4s5houy", "actualCoverage", "s", "msb_shared_component_1", "require", "exports", "makeStyle", "createMSBStyleSheet", "_ref", "f", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ColorDataView", "Typography", "SizeGlobal", "container", "flex", "paddingVertical", "Spacing2xSmall", "contentContainer", "backgroundColor", "ColorAlias", "<PERSON><PERSON><PERSON><PERSON>", "paddingHorizontal", "SpacingSmall", "borderRadius", "Radius3", "padding", "scrollViewContainer", "sender", "marginTop", "SpacingXMSmall", "flexDirection", "alignItems", "sender<PERSON>ame", "Object", "assign", "b", "base_semiBold", "color", "TextMain", "marginLeft", "SpacingXSmall", "imageBackground", "resizeMode", "marginTop16", "txtName", "txtTitle", "small_regular", "TextSub", "marginBottom", "Spacing4xSmall", "txtValue", "base_medium", "accountInfo", "Size500"], "sources": ["/Users/<USER>/projects/react-native/payment-module/src/presentation/payment-result-detail/styles.ts"], "sourcesContent": ["import {ColorAlias, createMSBStyleSheet} from 'msb-shared-component';\n\nexport const makeStyle = createMSBStyleSheet(({SizeAlias, ColorDataView, Typography, SizeGlobal}) => {\n  return {\n    container: {\n      flex: 1,\n      paddingVertical: SizeAlias.Spacing2xSmall,\n    },\n    contentContainer: {\n      backgroundColor: ColorAlias.BackgroundWhite,\n      paddingHorizontal: SizeAlias.SpacingSmall,\n      borderRadius: SizeAlias.Radius3,\n      padding: SizeAlias.SpacingSmall,\n    },\n    scrollViewContainer: {\n      flex: 1,\n      paddingHorizontal: SizeAlias.SpacingSmall,\n      paddingVertical: SizeAlias.Spacing2xSmall,\n    },\n    sender: {\n      marginTop: SizeAlias.SpacingXMSmall,\n      flexDirection: 'row',\n      alignItems: 'center',\n    },\n    senderName: {\n      ...Typography?.base_semiBold,\n      color: ColorDataView.TextMain,\n      marginLeft: SizeAlias.SpacingXSmall,\n    },\n    imageBackground: {\n      flex: 1,\n      resizeMode: 'stretch',\n    },\n    marginTop16: {\n      marginTop: SizeAlias.SpacingSmall,\n      backgroundColor: 'transparent',\n    },\n    txtName: {\n      ...Typography?.base_semiBold,\n      // ...Tpg.base_semiBold,\n      color: ColorDataView.TextMain,\n    },\n    txtTitle: {\n      ...Typography?.small_regular,\n      color: ColorDataView.TextSub,\n      marginBottom: SizeAlias.Spacing4xSmall,\n    },\n    txtValue: {\n      ...Typography?.base_medium,\n      color: ColorDataView.TextMain,\n    },\n    flex: {\n      flex: 1,\n    },\n    accountInfo: {\n      marginTop: SizeGlobal.Size500,\n    },\n  };\n});\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAMM;IAAAA,cAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,cAAA;AAAAA,cAAA,GAAAE,CAAA;;;;;;;AANN,IAAAC,sBAAA;AAAA;AAAA,CAAAH,cAAA,GAAAE,CAAA,OAAAE,OAAA;AAAA;AAAAJ,cAAA,GAAAE,CAAA;AAEaG,OAAA,CAAAC,SAAS,GAAG,IAAAH,sBAAA,CAAAI,mBAAmB,EAAC,UAAAC,IAAA,EAAuD;EAAA;EAAAR,cAAA,GAAAS,CAAA;EAAA,IAArDC,SAAS;IAAA;IAAA,CAAAV,cAAA,GAAAE,CAAA,OAAAM,IAAA,CAATE,SAAS;IAAEC,aAAa;IAAA;IAAA,CAAAX,cAAA,GAAAE,CAAA,OAAAM,IAAA,CAAbG,aAAa;IAAEC,UAAU;IAAA;IAAA,CAAAZ,cAAA,GAAAE,CAAA,OAAAM,IAAA,CAAVI,UAAU;IAAEC,UAAU;IAAA;IAAA,CAAAb,cAAA,GAAAE,CAAA,OAAAM,IAAA,CAAVK,UAAU;EAAA;EAAAb,cAAA,GAAAE,CAAA;EAC7F,OAAO;IACLY,SAAS,EAAE;MACTC,IAAI,EAAE,CAAC;MACPC,eAAe,EAAEN,SAAS,CAACO;KAC5B;IACDC,gBAAgB,EAAE;MAChBC,eAAe,EAAEhB,sBAAA,CAAAiB,UAAU,CAACC,eAAe;MAC3CC,iBAAiB,EAAEZ,SAAS,CAACa,YAAY;MACzCC,YAAY,EAAEd,SAAS,CAACe,OAAO;MAC/BC,OAAO,EAAEhB,SAAS,CAACa;KACpB;IACDI,mBAAmB,EAAE;MACnBZ,IAAI,EAAE,CAAC;MACPO,iBAAiB,EAAEZ,SAAS,CAACa,YAAY;MACzCP,eAAe,EAAEN,SAAS,CAACO;KAC5B;IACDW,MAAM,EAAE;MACNC,SAAS,EAAEnB,SAAS,CAACoB,cAAc;MACnCC,aAAa,EAAE,KAAK;MACpBC,UAAU,EAAE;KACb;IACDC,UAAU,EAAAC,MAAA,CAAAC,MAAA,KACLvB,UAAU;IAAA;IAAA,CAAAZ,cAAA,GAAAoC,CAAA;IAAA;IAAA,CAAApC,cAAA,GAAAoC,CAAA,UAAVxB,UAAU,CAAEyB,aAAa;MAC5BC,KAAK,EAAE3B,aAAa,CAAC4B,QAAQ;MAC7BC,UAAU,EAAE9B,SAAS,CAAC+B;IAAa,EACpC;IACDC,eAAe,EAAE;MACf3B,IAAI,EAAE,CAAC;MACP4B,UAAU,EAAE;KACb;IACDC,WAAW,EAAE;MACXf,SAAS,EAAEnB,SAAS,CAACa,YAAY;MACjCJ,eAAe,EAAE;KAClB;IACD0B,OAAO,EAAAX,MAAA,CAAAC,MAAA,KACFvB,UAAU;IAAA;IAAA,CAAAZ,cAAA,GAAAoC,CAAA;IAAA;IAAA,CAAApC,cAAA,GAAAoC,CAAA,UAAVxB,UAAU,CAAEyB,aAAa;MAE5BC,KAAK,EAAE3B,aAAa,CAAC4B;IAAQ,EAC9B;IACDO,QAAQ,EAAAZ,MAAA,CAAAC,MAAA,KACHvB,UAAU;IAAA;IAAA,CAAAZ,cAAA,GAAAoC,CAAA;IAAA;IAAA,CAAApC,cAAA,GAAAoC,CAAA,UAAVxB,UAAU,CAAEmC,aAAa;MAC5BT,KAAK,EAAE3B,aAAa,CAACqC,OAAO;MAC5BC,YAAY,EAAEvC,SAAS,CAACwC;IAAc,EACvC;IACDC,QAAQ,EAAAjB,MAAA,CAAAC,MAAA,KACHvB,UAAU;IAAA;IAAA,CAAAZ,cAAA,GAAAoC,CAAA;IAAA;IAAA,CAAApC,cAAA,GAAAoC,CAAA,UAAVxB,UAAU,CAAEwC,WAAW;MAC1Bd,KAAK,EAAE3B,aAAa,CAAC4B;IAAQ,EAC9B;IACDxB,IAAI,EAAE;MACJA,IAAI,EAAE;KACP;IACDsC,WAAW,EAAE;MACXxB,SAAS,EAAEhB,UAAU,CAACyC;;GAEzB;AACH,CAAC,CAAC", "ignoreList": []}