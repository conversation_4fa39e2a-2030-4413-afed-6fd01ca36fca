b4a3f961cc4f4452d4cf60f8dfa071d2
"use strict";

/* istanbul ignore next */
function cov_khr123bkv() {
  var path = "/Users/<USER>/projects/react-native/payment-module/src/data/models/edit-bill-contact/EditBillContactResponse.ts";
  var hash = "5242829059cc34229cf4ed04393796a316b68242";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/projects/react-native/payment-module/src/data/models/edit-bill-contact/EditBillContactResponse.ts",
    statementMap: {
      "0": {
        start: {
          line: 3,
          column: 0
        },
        end: {
          line: 5,
          column: 3
        }
      }
    },
    fnMap: {},
    branchMap: {},
    s: {
      "0": 0
    },
    f: {},
    b: {},
    inputSourceMap: {
      version: 3,
      file: undefined,
      names: [],
      sourceRoot: undefined,
      sources: ["/Users/<USER>/projects/react-native/payment-module/src/data/models/edit-bill-contact/EditBillContactResponse.ts"],
      sourcesContent: ["export interface EditBillContactResponse {\n  // TODO: define fields\n}\n"],
      mappings: "",
      ignoreList: []
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "5242829059cc34229cf4ed04393796a316b68242"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_khr123bkv = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_khr123bkv();
cov_khr123bkv().s[0]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6W10sInNvdXJjZXMiOlsiL1VzZXJzL3NvbnNhbXNldC9wcm9qZWN0cy9yZWFjdC1uYXRpdmUvcGF5bWVudC1tb2R1bGUvc3JjL2RhdGEvbW9kZWxzL2VkaXQtYmlsbC1jb250YWN0L0VkaXRCaWxsQ29udGFjdFJlc3BvbnNlLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBpbnRlcmZhY2UgRWRpdEJpbGxDb250YWN0UmVzcG9uc2Uge1xuICAvLyBUT0RPOiBkZWZpbmUgZmllbGRzXG59XG4iXSwibWFwcGluZ3MiOiIiLCJpZ25vcmVMaXN0IjpbXX0=