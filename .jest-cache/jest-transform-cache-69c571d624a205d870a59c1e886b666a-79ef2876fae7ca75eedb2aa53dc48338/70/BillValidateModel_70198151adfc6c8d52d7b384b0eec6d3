6c3bc1bc9e85747e8176156c7f801d94
"use strict";

/* istanbul ignore next */
function cov_34nhgwxd7() {
  var path = "/Users/<USER>/projects/react-native/payment-module/src/domain/entities/bill-validate/BillValidateModel.ts";
  var hash = "e5198945a02aab59740f25840825050ef8cb65fd";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/projects/react-native/payment-module/src/domain/entities/bill-validate/BillValidateModel.ts",
    statementMap: {
      "0": {
        start: {
          line: 3,
          column: 0
        },
        end: {
          line: 5,
          column: 3
        }
      }
    },
    fnMap: {},
    branchMap: {},
    s: {
      "0": 0
    },
    f: {},
    b: {},
    inputSourceMap: {
      version: 3,
      file: undefined,
      names: [],
      sourceRoot: undefined,
      sources: ["/Users/<USER>/projects/react-native/payment-module/src/domain/entities/bill-validate/BillValidateModel.ts"],
      sourcesContent: ["export interface BillValidateModel {\n  id: string;\n  originatorAccount: OriginatorAccountModel;\n  instructionPriority: string;\n  requestedExecutionDate: string;\n  paymentMode: string;\n  paymentType: string;\n  transferTransactionInformation: TransferTransactionInformationModel;\n  originator: OriginatorModel;\n  totalAmount: CurrencyAmountModel;\n  isIntraLegalEntityPaymentOrder: boolean;\n  canApprove: boolean;\n  finalApprover: boolean;\n}\n\nexport interface OriginatorAccountModel {\n  arrangementId: string;\n  externalArrangementId: string;\n  identification: AccountIdentificationModel;\n}\n\nexport interface AccountIdentificationModel {\n  identification: string;\n  schemeName: string;\n}\n\nexport interface TransferTransactionInformationModel {\n  counterparty: CounterpartyModel;\n  counterpartyAccount: AccountIdentificationModel;\n  instructedAmount: CurrencyAmountModel;\n  additions: TransferAdditionsModel;\n}\n\nexport interface CounterpartyModel {\n  name: string;\n  role: string;\n}\n\nexport interface CurrencyAmountModel {\n  amount: string;\n  currencyCode: string;\n}\n\nexport interface TransferAdditionsModel {\n  bpQueryRef: string;\n  bpBillList: string; // Ho\u1EB7c: BillItemModel[] n\u1EBFu parse JSON\n  bpSummary: string; // Ho\u1EB7c: BpSummaryModel n\u1EBFu parse JSON\n  bpServiceCode: string;\n  cifNo: string;\n  bpCategory: string;\n  bpAccountingNumber: string;\n}\n\nexport interface OriginatorModel {\n  name: string;\n  role: string;\n  postalAddress: Record<string, any>;\n}\n"],
      mappings: "",
      ignoreList: []
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "e5198945a02aab59740f25840825050ef8cb65fd"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_34nhgwxd7 = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_34nhgwxd7();
cov_34nhgwxd7().s[0]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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