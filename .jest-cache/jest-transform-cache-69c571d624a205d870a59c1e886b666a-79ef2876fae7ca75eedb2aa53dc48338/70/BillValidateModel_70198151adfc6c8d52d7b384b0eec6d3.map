{"version": 3, "names": [], "sources": ["/Users/<USER>/projects/react-native/payment-module/src/domain/entities/bill-validate/BillValidateModel.ts"], "sourcesContent": ["export interface BillValidateModel {\n  id: string;\n  originatorAccount: OriginatorAccountModel;\n  instructionPriority: string;\n  requestedExecutionDate: string;\n  paymentMode: string;\n  paymentType: string;\n  transferTransactionInformation: TransferTransactionInformationModel;\n  originator: OriginatorModel;\n  totalAmount: CurrencyAmountModel;\n  isIntraLegalEntityPaymentOrder: boolean;\n  canApprove: boolean;\n  finalApprover: boolean;\n}\n\nexport interface OriginatorAccountModel {\n  arrangementId: string;\n  externalArrangementId: string;\n  identification: AccountIdentificationModel;\n}\n\nexport interface AccountIdentificationModel {\n  identification: string;\n  schemeName: string;\n}\n\nexport interface TransferTransactionInformationModel {\n  counterparty: CounterpartyModel;\n  counterpartyAccount: AccountIdentificationModel;\n  instructedAmount: CurrencyAmountModel;\n  additions: TransferAdditionsModel;\n}\n\nexport interface CounterpartyModel {\n  name: string;\n  role: string;\n}\n\nexport interface CurrencyAmountModel {\n  amount: string;\n  currencyCode: string;\n}\n\nexport interface TransferAdditionsModel {\n  bpQueryRef: string;\n  bpBillList: string; // Hoặc: BillItemModel[] nếu parse JSON\n  bpSummary: string; // Hoặc: BpSummaryModel nếu parse JSON\n  bpServiceCode: string;\n  cifNo: string;\n  bpCategory: string;\n  bpAccountingNumber: string;\n}\n\nexport interface OriginatorModel {\n  name: string;\n  role: string;\n  postalAddress: Record<string, any>;\n}\n"], "mappings": "", "ignoreList": []}