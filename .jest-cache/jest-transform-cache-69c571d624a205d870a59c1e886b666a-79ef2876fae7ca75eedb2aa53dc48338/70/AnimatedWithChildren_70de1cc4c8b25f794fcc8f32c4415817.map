{"version": 3, "names": ["_interopRequireDefault", "require", "Object", "defineProperty", "exports", "value", "default", "_classCallCheck2", "_createClass2", "_possibleConstructorReturn2", "_getPrototypeOf2", "_get2", "_inherits2", "_NativeAnimatedHelper2", "_AnimatedNode2", "_callSuper", "t", "o", "e", "_isNativeReflectConstruct", "Reflect", "construct", "constructor", "apply", "Boolean", "prototype", "valueOf", "call", "_superPropGet", "r", "p", "_NativeAnimatedHelper", "NativeAnimatedHelper", "API", "connectAnimatedNodes", "disconnectAnimatedNodes", "AnimatedWithChildren", "_AnimatedNode", "_this", "_len", "arguments", "length", "args", "Array", "_key", "concat", "_children", "key", "__makeNative", "platformConfig", "__isNative", "children", "ii", "child", "__getNativeTag", "__add<PERSON><PERSON>d", "__attach", "push", "__getPlatformConfig", "__remove<PERSON><PERSON>d", "index", "indexOf", "console", "warn", "splice", "__detach", "__get<PERSON><PERSON><PERSON><PERSON>", "__callListeners", "__getValue", "AnimatedNode"], "sources": ["AnimatedWithChildren.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @flow strict-local\n * @format\n */\n\n'use strict';\n\nimport type {PlatformConfig} from '../AnimatedPlatformConfig';\n\nimport NativeAnimatedHelper from '../../../src/private/animated/NativeAnimatedHelper';\nimport AnimatedNode from './AnimatedNode';\n\nconst {connectAnimatedNodes, disconnectAnimatedNodes} =\n  NativeAnimatedHelper.API;\n\nexport default class AnimatedWithChildren extends AnimatedNode {\n  _children: Array<AnimatedNode> = [];\n\n  __makeNative(platformConfig: ?PlatformConfig) {\n    if (!this.__isNative) {\n      this.__isNative = true;\n\n      const children = this._children;\n      let length = children.length;\n      if (length > 0) {\n        for (let ii = 0; ii < length; ii++) {\n          const child = children[ii];\n          child.__makeNative(platformConfig);\n          connectAnimatedNodes(this.__getNativeTag(), child.__getNativeTag());\n        }\n      }\n    }\n    super.__makeNative(platformConfig);\n  }\n\n  __addChild(child: AnimatedNode): void {\n    if (this._children.length === 0) {\n      this.__attach();\n    }\n    this._children.push(child);\n    if (this.__isNative) {\n      // Only accept \"native\" animated nodes as children\n      child.__makeNative(this.__getPlatformConfig());\n      connectAnimatedNodes(this.__getNativeTag(), child.__getNativeTag());\n    }\n  }\n\n  __removeChild(child: AnimatedNode): void {\n    const index = this._children.indexOf(child);\n    if (index === -1) {\n      console.warn(\"Trying to remove a child that doesn't exist\");\n      return;\n    }\n    if (this.__isNative && child.__isNative) {\n      disconnectAnimatedNodes(this.__getNativeTag(), child.__getNativeTag());\n    }\n    this._children.splice(index, 1);\n    if (this._children.length === 0) {\n      this.__detach();\n    }\n  }\n\n  __getChildren(): $ReadOnlyArray<AnimatedNode> {\n    return this._children;\n  }\n\n  __callListeners(value: number): void {\n    super.__callListeners(value);\n    if (!this.__isNative) {\n      const children = this._children;\n      for (let ii = 0, length = children.length; ii < length; ii++) {\n        const child = children[ii];\n        // $FlowFixMe[method-unbinding] added when improving typing for this parameters\n        if (child.__getValue) {\n          child.__callListeners(child.__getValue());\n        }\n      }\n    }\n  }\n}\n"], "mappings": "AAUA,YAAY;;AAAC,IAAAA,sBAAA,GAAAC,OAAA;AAAAC,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,KAAA;AAAA;AAAAD,OAAA,CAAAE,OAAA;AAAA,IAAAC,gBAAA,GAAAP,sBAAA,CAAAC,OAAA;AAAA,IAAAO,aAAA,GAAAR,sBAAA,CAAAC,OAAA;AAAA,IAAAQ,2BAAA,GAAAT,sBAAA,CAAAC,OAAA;AAAA,IAAAS,gBAAA,GAAAV,sBAAA,CAAAC,OAAA;AAAA,IAAAU,KAAA,GAAAX,sBAAA,CAAAC,OAAA;AAAA,IAAAW,UAAA,GAAAZ,sBAAA,CAAAC,OAAA;AAIb,IAAAY,sBAAA,GAAAb,sBAAA,CAAAC,OAAA;AACA,IAAAa,cAAA,GAAAd,sBAAA,CAAAC,OAAA;AAA0C,SAAAc,WAAAC,CAAA,EAAAC,CAAA,EAAAC,CAAA,WAAAD,CAAA,OAAAP,gBAAA,CAAAJ,OAAA,EAAAW,CAAA,OAAAR,2BAAA,CAAAH,OAAA,EAAAU,CAAA,EAAAG,yBAAA,KAAAC,OAAA,CAAAC,SAAA,CAAAJ,CAAA,EAAAC,CAAA,YAAAR,gBAAA,CAAAJ,OAAA,EAAAU,CAAA,EAAAM,WAAA,IAAAL,CAAA,CAAAM,KAAA,CAAAP,CAAA,EAAAE,CAAA;AAAA,SAAAC,0BAAA,cAAAH,CAAA,IAAAQ,OAAA,CAAAC,SAAA,CAAAC,OAAA,CAAAC,IAAA,CAAAP,OAAA,CAAAC,SAAA,CAAAG,OAAA,iCAAAR,CAAA,aAAAG,yBAAA,YAAAA,0BAAA,aAAAH,CAAA;AAAA,SAAAY,cAAAZ,CAAA,EAAAC,CAAA,EAAAC,CAAA,EAAAW,CAAA,QAAAC,CAAA,OAAAnB,KAAA,CAAAL,OAAA,MAAAI,gBAAA,CAAAJ,OAAA,MAAAuB,CAAA,GAAAb,CAAA,CAAAS,SAAA,GAAAT,CAAA,GAAAC,CAAA,EAAAC,CAAA,cAAAW,CAAA,yBAAAC,CAAA,aAAAd,CAAA,WAAAc,CAAA,CAAAP,KAAA,CAAAL,CAAA,EAAAF,CAAA,OAAAc,CAAA;AAE1C,IAAAC,qBAAA,GACEC,8BAAoB,CAACC,GAAG;EADnBC,oBAAoB,GAAAH,qBAAA,CAApBG,oBAAoB;EAAEC,uBAAuB,GAAAJ,qBAAA,CAAvBI,uBAAuB;AACzB,IAENC,oBAAoB,GAAAhC,OAAA,CAAAE,OAAA,aAAA+B,aAAA;EAAA,SAAAD,qBAAA;IAAA,IAAAE,KAAA;IAAA,IAAA/B,gBAAA,CAAAD,OAAA,QAAA8B,oBAAA;IAAA,SAAAG,IAAA,GAAAC,SAAA,CAAAC,MAAA,EAAAC,IAAA,OAAAC,KAAA,CAAAJ,IAAA,GAAAK,IAAA,MAAAA,IAAA,GAAAL,IAAA,EAAAK,IAAA;MAAAF,IAAA,CAAAE,IAAA,IAAAJ,SAAA,CAAAI,IAAA;IAAA;IAAAN,KAAA,GAAAvB,UAAA,OAAAqB,oBAAA,KAAAS,MAAA,CAAAH,IAAA;IAAAJ,KAAA,CACvCQ,SAAS,GAAwB,EAAE;IAAA,OAAAR,KAAA;EAAA;EAAA,IAAA1B,UAAA,CAAAN,OAAA,EAAA8B,oBAAA,EAAAC,aAAA;EAAA,WAAA7B,aAAA,CAAAF,OAAA,EAAA8B,oBAAA;IAAAW,GAAA;IAAA1C,KAAA,EAEnC,SAAA2C,YAAYA,CAACC,cAA+B,EAAE;MAC5C,IAAI,CAAC,IAAI,CAACC,UAAU,EAAE;QACpB,IAAI,CAACA,UAAU,GAAG,IAAI;QAEtB,IAAMC,QAAQ,GAAG,IAAI,CAACL,SAAS;QAC/B,IAAIL,MAAM,GAAGU,QAAQ,CAACV,MAAM;QAC5B,IAAIA,MAAM,GAAG,CAAC,EAAE;UACd,KAAK,IAAIW,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGX,MAAM,EAAEW,EAAE,EAAE,EAAE;YAClC,IAAMC,KAAK,GAAGF,QAAQ,CAACC,EAAE,CAAC;YAC1BC,KAAK,CAACL,YAAY,CAACC,cAAc,CAAC;YAClCf,oBAAoB,CAAC,IAAI,CAACoB,cAAc,CAAC,CAAC,EAAED,KAAK,CAACC,cAAc,CAAC,CAAC,CAAC;UACrE;QACF;MACF;MACA1B,aAAA,CAAAQ,oBAAA,4BAAmBa,cAAc;IACnC;EAAC;IAAAF,GAAA;IAAA1C,KAAA,EAED,SAAAkD,UAAUA,CAACF,KAAmB,EAAQ;MACpC,IAAI,IAAI,CAACP,SAAS,CAACL,MAAM,KAAK,CAAC,EAAE;QAC/B,IAAI,CAACe,QAAQ,CAAC,CAAC;MACjB;MACA,IAAI,CAACV,SAAS,CAACW,IAAI,CAACJ,KAAK,CAAC;MAC1B,IAAI,IAAI,CAACH,UAAU,EAAE;QAEnBG,KAAK,CAACL,YAAY,CAAC,IAAI,CAACU,mBAAmB,CAAC,CAAC,CAAC;QAC9CxB,oBAAoB,CAAC,IAAI,CAACoB,cAAc,CAAC,CAAC,EAAED,KAAK,CAACC,cAAc,CAAC,CAAC,CAAC;MACrE;IACF;EAAC;IAAAP,GAAA;IAAA1C,KAAA,EAED,SAAAsD,aAAaA,CAACN,KAAmB,EAAQ;MACvC,IAAMO,KAAK,GAAG,IAAI,CAACd,SAAS,CAACe,OAAO,CAACR,KAAK,CAAC;MAC3C,IAAIO,KAAK,KAAK,CAAC,CAAC,EAAE;QAChBE,OAAO,CAACC,IAAI,CAAC,6CAA6C,CAAC;QAC3D;MACF;MACA,IAAI,IAAI,CAACb,UAAU,IAAIG,KAAK,CAACH,UAAU,EAAE;QACvCf,uBAAuB,CAAC,IAAI,CAACmB,cAAc,CAAC,CAAC,EAAED,KAAK,CAACC,cAAc,CAAC,CAAC,CAAC;MACxE;MACA,IAAI,CAACR,SAAS,CAACkB,MAAM,CAACJ,KAAK,EAAE,CAAC,CAAC;MAC/B,IAAI,IAAI,CAACd,SAAS,CAACL,MAAM,KAAK,CAAC,EAAE;QAC/B,IAAI,CAACwB,QAAQ,CAAC,CAAC;MACjB;IACF;EAAC;IAAAlB,GAAA;IAAA1C,KAAA,EAED,SAAA6D,aAAaA,CAAA,EAAiC;MAC5C,OAAO,IAAI,CAACpB,SAAS;IACvB;EAAC;IAAAC,GAAA;IAAA1C,KAAA,EAED,SAAA8D,eAAeA,CAAC9D,KAAa,EAAQ;MACnCuB,aAAA,CAAAQ,oBAAA,+BAAsB/B,KAAK;MAC3B,IAAI,CAAC,IAAI,CAAC6C,UAAU,EAAE;QACpB,IAAMC,QAAQ,GAAG,IAAI,CAACL,SAAS;QAC/B,KAAK,IAAIM,EAAE,GAAG,CAAC,EAAEX,MAAM,GAAGU,QAAQ,CAACV,MAAM,EAAEW,EAAE,GAAGX,MAAM,EAAEW,EAAE,EAAE,EAAE;UAC5D,IAAMC,KAAK,GAAGF,QAAQ,CAACC,EAAE,CAAC;UAE1B,IAAIC,KAAK,CAACe,UAAU,EAAE;YACpBf,KAAK,CAACc,eAAe,CAACd,KAAK,CAACe,UAAU,CAAC,CAAC,CAAC;UAC3C;QACF;MACF;IACF;EAAC;AAAA,EA/D+CC,sBAAY", "ignoreList": []}