c6a155894c5df273c9202a2fc91cf5b5
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = useMergeRefs;
var _useRefEffect = _interopRequireDefault(require("./useRefEffect"));
var _react = _interopRequireWildcard(require("react"));
var React = _react;
function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
function useMergeRefs() {
  for (var _len = arguments.length, refs = new Array(_len), _key = 0; _key < _len; _key++) {
    refs[_key] = arguments[_key];
  }
  var refEffect = (0, _react.useCallback)(function (current) {
    var cleanups = refs.map(function (ref) {
      if (ref == null) {
        return undefined;
      } else {
        if (typeof ref === 'function') {
          var cleanup = ref(current);
          return typeof cleanup === 'function' ? cleanup : function () {
            ref(null);
          };
        } else {
          ref.current = current;
          return function () {
            ref.current = null;
          };
        }
      }
    });
    return function () {
      for (var cleanup of cleanups) {
        cleanup == null || cleanup();
      }
    };
  }, [].concat(refs));
  return (0, _useRefEffect.default)(refEffect);
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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