2ed920cd13b3d442daa1f349d74cc3a4
"use strict";

/* istanbul ignore next */
function cov_phz0q228c() {
  var path = "/Users/<USER>/projects/react-native/payment-module/src/presentation/payment-info/components/payment-bill-info/types.ts";
  var hash = "3b5ec82a8a92f233a20b4bb2279d4a8f80ed69d4";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/projects/react-native/payment-module/src/presentation/payment-info/components/payment-bill-info/types.ts",
    statementMap: {
      "0": {
        start: {
          line: 3,
          column: 0
        },
        end: {
          line: 5,
          column: 3
        }
      }
    },
    fnMap: {},
    branchMap: {},
    s: {
      "0": 0
    },
    f: {},
    b: {},
    inputSourceMap: {
      version: 3,
      file: undefined,
      names: [],
      sourceRoot: undefined,
      sources: ["/Users/<USER>/projects/react-native/payment-module/src/presentation/payment-info/components/payment-bill-info/types.ts"],
      sourcesContent: ["import {ViewStyle} from 'react-native';\n\nexport type PaymentBillInfoProps = {\n  style?: ViewStyle;\n  isTopup?: boolean;\n  customerInfo: CustomerInfoProps;\n  billList: BillListProps[];\n};\n\nexport type CustomerInfoProps = {\n  logo: string;\n  fullName: string;\n  categoryName: string;\n  billCode: string;\n};\n\nexport type BillListProps = {\n  amount: number;\n  dateTime: string;\n};\n"],
      mappings: "",
      ignoreList: []
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "3b5ec82a8a92f233a20b4bb2279d4a8f80ed69d4"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_phz0q228c = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_phz0q228c();
cov_phz0q228c().s[0]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6W10sInNvdXJjZXMiOlsiL1VzZXJzL3NvbnNhbXNldC9wcm9qZWN0cy9yZWFjdC1uYXRpdmUvcGF5bWVudC1tb2R1bGUvc3JjL3ByZXNlbnRhdGlvbi9wYXltZW50LWluZm8vY29tcG9uZW50cy9wYXltZW50LWJpbGwtaW5mby90eXBlcy50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQge1ZpZXdTdHlsZX0gZnJvbSAncmVhY3QtbmF0aXZlJztcblxuZXhwb3J0IHR5cGUgUGF5bWVudEJpbGxJbmZvUHJvcHMgPSB7XG4gIHN0eWxlPzogVmlld1N0eWxlO1xuICBpc1RvcHVwPzogYm9vbGVhbjtcbiAgY3VzdG9tZXJJbmZvOiBDdXN0b21lckluZm9Qcm9wcztcbiAgYmlsbExpc3Q6IEJpbGxMaXN0UHJvcHNbXTtcbn07XG5cbmV4cG9ydCB0eXBlIEN1c3RvbWVySW5mb1Byb3BzID0ge1xuICBsb2dvOiBzdHJpbmc7XG4gIGZ1bGxOYW1lOiBzdHJpbmc7XG4gIGNhdGVnb3J5TmFtZTogc3RyaW5nO1xuICBiaWxsQ29kZTogc3RyaW5nO1xufTtcblxuZXhwb3J0IHR5cGUgQmlsbExpc3RQcm9wcyA9IHtcbiAgYW1vdW50OiBudW1iZXI7XG4gIGRhdGVUaW1lOiBzdHJpbmc7XG59O1xuIl0sIm1hcHBpbmdzIjoiIiwiaWdub3JlTGlzdCI6W119