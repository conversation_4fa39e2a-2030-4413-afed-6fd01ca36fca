{"version": 3, "names": [], "sources": ["/Users/<USER>/projects/react-native/payment-module/src/presentation/payment-info/components/payment-bill-info/types.ts"], "sourcesContent": ["import {ViewStyle} from 'react-native';\n\nexport type PaymentBillInfoProps = {\n  style?: ViewStyle;\n  isTopup?: boolean;\n  customerInfo: CustomerInfoProps;\n  billList: BillListProps[];\n};\n\nexport type CustomerInfoProps = {\n  logo: string;\n  fullName: string;\n  categoryName: string;\n  billCode: string;\n};\n\nexport type BillListProps = {\n  amount: number;\n  dateTime: string;\n};\n"], "mappings": "", "ignoreList": []}