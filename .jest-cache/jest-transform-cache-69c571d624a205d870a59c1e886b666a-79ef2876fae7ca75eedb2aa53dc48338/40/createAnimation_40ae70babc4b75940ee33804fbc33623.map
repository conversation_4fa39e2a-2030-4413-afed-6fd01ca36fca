{"version": 3, "names": ["_interopRequireDefault", "require", "Object", "defineProperty", "exports", "value", "TransitionGenerator", "createAnimationWithInitialValues", "createCustomKeyFrameAnimation", "_defineProperty2", "_objectWithoutProperties2", "_slicedToArray2", "_config", "_animation<PERSON><PERSON>er", "_LinearWeb", "_SequencedWeb", "_FadingWeb", "_JumpingWeb", "_domUtils", "_<PERSON>ur<PERSON><PERSON><PERSON>", "_EntryExitWeb", "_excluded", "addPxToTransform", "transform", "newTransform", "map", "transformProp", "newTransformProp", "_ref", "entries", "_ref2", "default", "key", "includes", "keyframeDefinitions", "values", "animationData", "name", "style", "duration", "generateNextCustomKeyframeName", "parsedKeyframe", "convertAnimationObjectToKeyframes", "insertWebAnimation", "animationName", "initialValues", "animationStyle", "structuredClone", "AnimationsData", "firstAnimationStep", "rest", "transformWithPx", "transformStyle", "Map", "rule", "_ref3", "_ref4", "property", "set", "_ref5", "_ref6", "Array", "from", "_ref7", "_ref8", "assign", "keyframeName", "animationObject", "keyframe", "customKeyframeCounter", "transitionType", "transitionData", "transitionKeyframeName", "dummyTransitionKeyframeName", "transitionObject", "TransitionType", "LINEAR", "LinearTransition", "SEQUENCED", "SequencedTransition", "FADING", "FadingTransition", "JUMPING", "JumpingTransition", "CURVED", "_CurvedTransition", "CurvedTransition", "firstKeyframeObj", "secondKeyframeObj", "dummy<PERSON><PERSON><PERSON>", "ENTRY_EXIT", "EntryExitTransition", "transitionKeyframe"], "sources": ["../../../../src/layoutReanimation/web/createAnimation.ts"], "sourcesContent": [null], "mappings": "AAAA,YAAY;;AAAA,IAAAA,sBAAA,GAAAC,OAAA;AAAAC,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,KAAA;AAAA;AAAAD,OAAA,CAAAE,mBAAA,GAAAA,mBAAA;AAAAF,OAAA,CAAAG,gCAAA,GAAAA,gCAAA;AAAAH,OAAA,CAAAI,6BAAA,GAAAA,6BAAA;AAAA,IAAAC,gBAAA,GAAAT,sBAAA,CAAAC,OAAA;AAAA,IAAAS,yBAAA,GAAAV,sBAAA,CAAAC,OAAA;AAAA,IAAAU,eAAA,GAAAX,sBAAA,CAAAC,OAAA;AAEZ,IAAAW,OAAA,GAAAX,OAAA;AAEA,IAAAY,gBAAA,GAAAZ,OAAA;AAOA,IAAAa,UAAA,GAAAb,OAAA;AACA,IAAAc,aAAA,GAAAd,OAAA;AACA,IAAAe,UAAA,GAAAf,OAAA;AACA,IAAAgB,WAAA,GAAAhB,OAAA;AACA,IAAAiB,SAAA,GAAAjB,OAAA;AACA,IAAAkB,UAAA,GAAAlB,OAAA;AACA,IAAAmB,aAAA,GAAAnB,OAAA;AAAgE,IAAAoB,SAAA;AAShE,SAASC,gBAAgBA,CAACC,SAAwB,EAAE;EAKlD,IAAMC,YAAY,GAAGD,SAAS,CAACE,GAAG,CAAE,UAAAC,aAA8B,EAAK;IACrE,IAAMC,gBAAkD,GAAG,CAAC,CAAC;IAC7D,SAAAC,IAAA,IAA2B1B,MAAM,CAAC2B,OAAO,CAACH,aAAa,CAAC,EAAE;MAAA,IAAAI,KAAA,OAAAnB,eAAA,CAAAoB,OAAA,EAAAH,IAAA;MAAA,IAA9CI,GAAG,GAAAF,KAAA;MAAA,IAAEzB,KAAK,GAAAyB,KAAA;MACpB,IACE,CAACE,GAAG,CAACC,QAAQ,CAAC,WAAW,CAAC,IAAID,GAAG,CAACC,QAAQ,CAAC,aAAa,CAAC,KACzD,OAAO5B,KAAK,KAAK,QAAQ,EACzB;QAGAsB,gBAAgB,CAACK,GAAG,CAAC,GAAG,GAAG3B,KAAK,IAAI;MACtC,CAAC,MAAM;QAELsB,gBAAgB,CAACK,GAAG,CAAC,GAAG3B,KAAK;MAC/B;IACF;IACA,OAAOsB,gBAAgB;EACzB,CAAC,CAAC;EAEF,OAAOH,YAAY;AACrB;AAEO,SAAShB,6BAA6BA,CAC3C0B,mBAAwC,EACxC;EACA,KAAK,IAAM7B,KAAK,IAAIH,MAAM,CAACiC,MAAM,CAACD,mBAAmB,CAAC,EAAE;IACtD,IAAI7B,KAAK,CAACkB,SAAS,EAAE;MACnBlB,KAAK,CAACkB,SAAS,GAAGD,gBAAgB,CAACjB,KAAK,CAACkB,SAA0B,CAAC;IACtE;EACF;EAEA,IAAMa,aAA4B,GAAG;IACnCC,IAAI,EAAE,EAAE;IACRC,KAAK,EAAEJ,mBAAmB;IAC1BK,QAAQ,EAAE,CAAC;EACb,CAAC;EAEDH,aAAa,CAACC,IAAI,GAAGG,8BAA8B,CAAC,CAAC;EAErD,IAAMC,cAAc,GAAG,IAAAC,kDAAiC,EAACN,aAAa,CAAC;EAEvE,IAAAO,4BAAkB,EAACP,aAAa,CAACC,IAAI,EAAEI,cAAc,CAAC;EAEtD,OAAOL,aAAa,CAACC,IAAI;AAC3B;AAEO,SAAS9B,gCAAgCA,CAC9CqC,aAAqB,EACrBC,aAAsC,EACtC;EACA,IAAMC,cAAc,GAAGC,eAAe,CAACC,sBAAc,CAACJ,aAAa,CAAC,CAACN,KAAK,CAAC;EAC3E,IAAMW,kBAAkB,GAAGH,cAAc,CAAC,GAAG,CAAC;EAE9C,IAAQvB,SAAS,GAAcsB,aAAa,CAApCtB,SAAS;IAAK2B,IAAA,OAAAxC,yBAAA,CAAAqB,OAAA,EAASc,aAAa,EAAAxB,SAAA;EAC5C,IAAM8B,eAAe,GAAG7B,gBAAgB,CAACC,SAA0B,CAAC;EAEpE,IAAIA,SAAS,EAAE;IAEb,IAAI,CAAC0B,kBAAkB,CAAC1B,SAAS,EAAE;MACjC0B,kBAAkB,CAAC1B,SAAS,GAAG4B,eAAe;IAChD,CAAC,MAAM;MAGL,IAAMC,cAAc,GAAG,IAAIC,GAAG,CAAc,CAAC;MAG7C,KAAK,IAAMC,IAAI,IAAIL,kBAAkB,CAAC1B,SAAS,EAAE;QAE/C,SAAAgC,KAAA,IAAgCrD,MAAM,CAAC2B,OAAO,CAACyB,IAAI,CAAC,EAAE;UAAA,IAAAE,KAAA,OAAA7C,eAAA,CAAAoB,OAAA,EAAAwB,KAAA;UAAA,IAA1CE,QAAQ,GAAAD,KAAA;UAAA,IAAEnD,KAAK,GAAAmD,KAAA;UACzBJ,cAAc,CAACM,GAAG,CAACD,QAAQ,EAAEpD,KAAK,CAAC;QACrC;MACF;MAGA,KAAK,IAAMiD,KAAI,IAAIH,eAAe,EAAE;QAClC,SAAAQ,KAAA,IAAgCzD,MAAM,CAAC2B,OAAO,CAACyB,KAAI,CAAC,EAAE;UAAA,IAAAM,KAAA,OAAAjD,eAAA,CAAAoB,OAAA,EAAA4B,KAAA;UAAA,IAA1CF,SAAQ,GAAAG,KAAA;UAAA,IAAEvD,MAAK,GAAAuD,KAAA;UACzBR,cAAc,CAACM,GAAG,CAACD,SAAQ,EAAEpD,MAAK,CAAC;QACrC;MACF;MAGA4C,kBAAkB,CAAC1B,SAAS,GAAGsC,KAAK,CAACC,IAAI,CACvCV,cAAc,EACd,UAAAW,KAAA;QAAA,IAAAC,KAAA,OAAArD,eAAA,CAAAoB,OAAA,EAAAgC,KAAA;UAAEN,QAAQ,GAAAO,KAAA;UAAE3D,KAAK,GAAA2D,KAAA;QAAA,WAAAvD,gBAAA,CAAAsB,OAAA,MACd0B,QAAQ,EAAGpD,KAAA;MAAA,CAEhB,CAAC;IACH;EACF;EAEAyC,cAAc,CAAC,GAAG,CAAC,GAAA5C,MAAA,CAAA+D,MAAA,KACdnB,cAAc,CAAC,GAAG,CAAC,EACnBI,IAAA,CACJ;EAGD,IAAMgB,YAAY,GAAG1B,8BAA8B,CAAC,CAAC;EAErD,IAAM2B,eAA8B,GAAG;IACrC9B,IAAI,EAAE6B,YAAY;IAClB5B,KAAK,EAAEQ,cAAc;IACrBP,QAAQ,EAAES,sBAAc,CAACJ,aAAa,CAAC,CAACL;EAC1C,CAAC;EAED,IAAM6B,QAAQ,GAAG,IAAA1B,kDAAiC,EAACyB,eAAe,CAAC;EAEnE,IAAAxB,4BAAkB,EAACuB,YAAY,EAAEE,QAAQ,CAAC;EAE1C,OAAOF,YAAY;AACrB;AAEA,IAAIG,qBAAqB,GAAG,CAAC;AAE7B,SAAS7B,8BAA8BA,CAAA,EAAG;EACxC,OAAO,MAAM6B,qBAAqB,EAAE,EAAE;AACxC;AAWO,SAAS/D,mBAAmBA,CACjCgE,cAA8B,EAC9BC,cAA8B,EAC9B;EACA,IAAMC,sBAAsB,GAAGhC,8BAA8B,CAAC,CAAC;EAC/D,IAAIiC,2BAA2B;EAE/B,IAAIC,gBAAgB;EAEpB,QAAQJ,cAAc;IACpB,KAAKK,sBAAc,CAACC,MAAM;MACxBF,gBAAgB,GAAG,IAAAG,2BAAgB,EACjCL,sBAAsB,EACtBD,cACF,CAAC;MACD;IACF,KAAKI,sBAAc,CAACG,SAAS;MAC3BJ,gBAAgB,GAAG,IAAAK,iCAAmB,EACpCP,sBAAsB,EACtBD,cACF,CAAC;MACD;IACF,KAAKI,sBAAc,CAACK,MAAM;MACxBN,gBAAgB,GAAG,IAAAO,2BAAgB,EACjCT,sBAAsB,EACtBD,cACF,CAAC;MACD;IACF,KAAKI,sBAAc,CAACO,OAAO;MACzBR,gBAAgB,GAAG,IAAAS,6BAAiB,EAClCX,sBAAsB,EACtBD,cACF,CAAC;MACD;IAGF,KAAKI,sBAAc,CAACS,MAAM;MAAE;QAC1BX,2BAA2B,GAAGjC,8BAA8B,CAAC,CAAC;QAE9D,IAAA6C,iBAAA,GAAgD,IAAAC,2BAAgB,EAC9Dd,sBAAsB,EACtBC,2BAA2B,EAC3BF,cACF,CAAC;UAJOgB,gBAAgB,GAAAF,iBAAA,CAAhBE,gBAAgB;UAAEC,iBAAA,GAAAH,iBAAA,CAAAG,iBAAA;QAM1Bd,gBAAgB,GAAGa,gBAAgB;QAEnC,IAAME,aAAa,GACjB,IAAA/C,kDAAiC,EAAC8C,iBAAiB,CAAC;QAEtD,IAAA7C,4BAAkB,EAAC8B,2BAA2B,EAAEgB,aAAa,CAAC;QAE9D;MACF;IACA,KAAKd,sBAAc,CAACe,UAAU;MAC5BhB,gBAAgB,GAAG,IAAAiB,iCAAmB,EACpCnB,sBAAsB,EACtBD,cACF,CAAC;MACD;EACJ;EAEA,IAAMqB,kBAAkB,GACtB,IAAAlD,kDAAiC,EAACgC,gBAAgB,CAAC;EAErD,IAAA/B,4BAAkB,EAAC6B,sBAAsB,EAAEoB,kBAAkB,CAAC;EAE9D,OAAO;IAAEpB,sBAAsB,EAAtBA,sBAAsB;IAAEC,2BAAA,EAAAA;EAA4B,CAAC;AAChE", "ignoreList": []}