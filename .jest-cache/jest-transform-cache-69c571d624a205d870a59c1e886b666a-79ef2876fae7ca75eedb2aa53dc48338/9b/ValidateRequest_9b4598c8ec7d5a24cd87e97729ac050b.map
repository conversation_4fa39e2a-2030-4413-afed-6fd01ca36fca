{"version": 3, "names": [], "sources": ["/Users/<USER>/projects/react-native/payment-module/src/data/models/validate/ValidateRequest.ts"], "sourcesContent": ["export interface ValidateRequest {\n  originatorAccount: OriginatorAccount;\n  requestedExecutionDate: string;\n  paymentType: string;\n  transferTransactionInformation: TransferTransactionInformation;\n}\n\nexport interface OriginatorAccount {\n  identification: Identification;\n}\n\nexport interface Identification {\n  identification: string;\n  schemeName: string;\n}\n\nexport interface TransferTransactionInformation {\n  instructedAmount: InstructedAmount;\n  counterparty: Counterparty;\n  counterpartyAccount: CounterpartyAccount;\n  additions: Additions;\n}\n\nexport interface InstructedAmount {\n  amount: string;\n  currencyCode: string;\n}\n\nexport interface Counterparty {\n  name: string;\n}\n\nexport interface CounterpartyAccount {\n  identification: Identification2;\n}\n\nexport interface Identification2 {\n  identification: string;\n  schemeName: string;\n}\n\nexport interface Additions {\n  bpQueryRef: string;\n  bpBillList: string;\n  bpSummary: string;\n  bpServiceCode: string;\n  cifNo: string;\n  bpCategory: string;\n}\n"], "mappings": "", "ignoreList": []}