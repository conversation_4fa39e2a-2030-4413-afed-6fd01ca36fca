e2eab11d7be34720ec1c7c779caefa13
"use strict";

/* istanbul ignore next */
function cov_14tmyx1quo() {
  var path = "/Users/<USER>/projects/react-native/payment-module/src/data/models/validate/ValidateRequest.ts";
  var hash = "fecd23e40ce91c9e471fa36ad2e31d0f6797f30f";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/projects/react-native/payment-module/src/data/models/validate/ValidateRequest.ts",
    statementMap: {
      "0": {
        start: {
          line: 3,
          column: 0
        },
        end: {
          line: 5,
          column: 3
        }
      }
    },
    fnMap: {},
    branchMap: {},
    s: {
      "0": 0
    },
    f: {},
    b: {},
    inputSourceMap: {
      version: 3,
      file: undefined,
      names: [],
      sourceRoot: undefined,
      sources: ["/Users/<USER>/projects/react-native/payment-module/src/data/models/validate/ValidateRequest.ts"],
      sourcesContent: ["export interface ValidateRequest {\n  originatorAccount: OriginatorAccount;\n  requestedExecutionDate: string;\n  paymentType: string;\n  transferTransactionInformation: TransferTransactionInformation;\n}\n\nexport interface OriginatorAccount {\n  identification: Identification;\n}\n\nexport interface Identification {\n  identification: string;\n  schemeName: string;\n}\n\nexport interface TransferTransactionInformation {\n  instructedAmount: InstructedAmount;\n  counterparty: Counterparty;\n  counterpartyAccount: CounterpartyAccount;\n  additions: Additions;\n}\n\nexport interface InstructedAmount {\n  amount: string;\n  currencyCode: string;\n}\n\nexport interface Counterparty {\n  name: string;\n}\n\nexport interface CounterpartyAccount {\n  identification: Identification2;\n}\n\nexport interface Identification2 {\n  identification: string;\n  schemeName: string;\n}\n\nexport interface Additions {\n  bpQueryRef: string;\n  bpBillList: string;\n  bpSummary: string;\n  bpServiceCode: string;\n  cifNo: string;\n  bpCategory: string;\n}\n"],
      mappings: "",
      ignoreList: []
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "fecd23e40ce91c9e471fa36ad2e31d0f6797f30f"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_14tmyx1quo = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_14tmyx1quo();
cov_14tmyx1quo().s[0]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6W10sInNvdXJjZXMiOlsiL1VzZXJzL3NvbnNhbXNldC9wcm9qZWN0cy9yZWFjdC1uYXRpdmUvcGF5bWVudC1tb2R1bGUvc3JjL2RhdGEvbW9kZWxzL3ZhbGlkYXRlL1ZhbGlkYXRlUmVxdWVzdC50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgaW50ZXJmYWNlIFZhbGlkYXRlUmVxdWVzdCB7XG4gIG9yaWdpbmF0b3JBY2NvdW50OiBPcmlnaW5hdG9yQWNjb3VudDtcbiAgcmVxdWVzdGVkRXhlY3V0aW9uRGF0ZTogc3RyaW5nO1xuICBwYXltZW50VHlwZTogc3RyaW5nO1xuICB0cmFuc2ZlclRyYW5zYWN0aW9uSW5mb3JtYXRpb246IFRyYW5zZmVyVHJhbnNhY3Rpb25JbmZvcm1hdGlvbjtcbn1cblxuZXhwb3J0IGludGVyZmFjZSBPcmlnaW5hdG9yQWNjb3VudCB7XG4gIGlkZW50aWZpY2F0aW9uOiBJZGVudGlmaWNhdGlvbjtcbn1cblxuZXhwb3J0IGludGVyZmFjZSBJZGVudGlmaWNhdGlvbiB7XG4gIGlkZW50aWZpY2F0aW9uOiBzdHJpbmc7XG4gIHNjaGVtZU5hbWU6IHN0cmluZztcbn1cblxuZXhwb3J0IGludGVyZmFjZSBUcmFuc2ZlclRyYW5zYWN0aW9uSW5mb3JtYXRpb24ge1xuICBpbnN0cnVjdGVkQW1vdW50OiBJbnN0cnVjdGVkQW1vdW50O1xuICBjb3VudGVycGFydHk6IENvdW50ZXJwYXJ0eTtcbiAgY291bnRlcnBhcnR5QWNjb3VudDogQ291bnRlcnBhcnR5QWNjb3VudDtcbiAgYWRkaXRpb25zOiBBZGRpdGlvbnM7XG59XG5cbmV4cG9ydCBpbnRlcmZhY2UgSW5zdHJ1Y3RlZEFtb3VudCB7XG4gIGFtb3VudDogc3RyaW5nO1xuICBjdXJyZW5jeUNvZGU6IHN0cmluZztcbn1cblxuZXhwb3J0IGludGVyZmFjZSBDb3VudGVycGFydHkge1xuICBuYW1lOiBzdHJpbmc7XG59XG5cbmV4cG9ydCBpbnRlcmZhY2UgQ291bnRlcnBhcnR5QWNjb3VudCB7XG4gIGlkZW50aWZpY2F0aW9uOiBJZGVudGlmaWNhdGlvbjI7XG59XG5cbmV4cG9ydCBpbnRlcmZhY2UgSWRlbnRpZmljYXRpb24yIHtcbiAgaWRlbnRpZmljYXRpb246IHN0cmluZztcbiAgc2NoZW1lTmFtZTogc3RyaW5nO1xufVxuXG5leHBvcnQgaW50ZXJmYWNlIEFkZGl0aW9ucyB7XG4gIGJwUXVlcnlSZWY6IHN0cmluZztcbiAgYnBCaWxsTGlzdDogc3RyaW5nO1xuICBicFN1bW1hcnk6IHN0cmluZztcbiAgYnBTZXJ2aWNlQ29kZTogc3RyaW5nO1xuICBjaWZObzogc3RyaW5nO1xuICBicENhdGVnb3J5OiBzdHJpbmc7XG59XG4iXSwibWFwcGluZ3MiOiIiLCJpZ25vcmVMaXN0IjpbXX0=