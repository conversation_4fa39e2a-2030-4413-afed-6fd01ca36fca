79b2c52d203084b4209b68dbcbad844f
'use strict';

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.ReanimatedFlatList = void 0;
var _objectWithoutProperties2 = _interopRequireDefault(require("@babel/runtime/helpers/objectWithoutProperties"));
var _react = _interopRequireWildcard(require("react"));
var _reactNative = require("react-native");
var _View = require("./View.js");
var _index = require("../createAnimatedComponent/index.js");
var _LayoutAnimationConfig = require("./LayoutAnimationConfig.js");
var _excluded = ["itemLayoutAnimation", "skipEnteringExitingAnimations"];
function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
function _extends() {
  return _extends = Object.assign ? Object.assign.bind() : function (n) {
    for (var e = 1; e < arguments.length; e++) {
      var t = arguments[e];
      for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]);
    }
    return n;
  }, _extends.apply(null, arguments);
}
var AnimatedFlatList = (0, _index.createAnimatedComponent)(_reactNative.FlatList);
var createCellRendererComponent = function createCellRendererComponent(itemLayoutAnimationRef) {
  var CellRendererComponent = function CellRendererComponent(props) {
    return _react.default.createElement(_View.AnimatedView, {
      layout: itemLayoutAnimationRef == null ? void 0 : itemLayoutAnimationRef.current,
      onLayout: props.onLayout,
      style: props.style
    }, props.children);
  };
  return CellRendererComponent;
};
var FlatListForwardRefRender = function FlatListForwardRefRender(props, ref) {
  var itemLayoutAnimation = props.itemLayoutAnimation,
    skipEnteringExitingAnimations = props.skipEnteringExitingAnimations,
    restProps = (0, _objectWithoutProperties2.default)(props, _excluded);
  if (!('scrollEventThrottle' in restProps)) {
    restProps.scrollEventThrottle = 1;
  }
  var itemLayoutAnimationRef = (0, _react.useRef)(itemLayoutAnimation);
  itemLayoutAnimationRef.current = itemLayoutAnimation;
  var CellRendererComponent = _react.default.useMemo(function () {
    return createCellRendererComponent(itemLayoutAnimationRef);
  }, [itemLayoutAnimationRef]);
  var animatedFlatList = _react.default.createElement(AnimatedFlatList, _extends({
    ref: ref
  }, restProps, {
    CellRendererComponent: CellRendererComponent
  }));
  if (skipEnteringExitingAnimations === undefined) {
    return animatedFlatList;
  }
  return _react.default.createElement(_LayoutAnimationConfig.LayoutAnimationConfig, {
    skipEntering: true,
    skipExiting: true
  }, animatedFlatList);
};
var ReanimatedFlatList = exports.ReanimatedFlatList = (0, _react.forwardRef)(FlatListForwardRefRender);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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