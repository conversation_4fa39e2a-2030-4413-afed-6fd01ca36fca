c3efe4d2f282e7df01d7e1cef27fad3e
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = exports.VERTICAL = exports.HORIZONTAL = void 0;
var React = _interopRequireWildcard(require("react"));
function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
var ScrollViewContext = React.createContext(null);
if (__DEV__) {
  ScrollViewContext.displayName = 'ScrollViewContext';
}
var _default = exports.default = ScrollViewContext;
var HORIZONTAL = exports.HORIZONTAL = Object.freeze({
  horizontal: true
});
var VERTICAL = exports.VERTICAL = Object.freeze({
  horizontal: false
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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