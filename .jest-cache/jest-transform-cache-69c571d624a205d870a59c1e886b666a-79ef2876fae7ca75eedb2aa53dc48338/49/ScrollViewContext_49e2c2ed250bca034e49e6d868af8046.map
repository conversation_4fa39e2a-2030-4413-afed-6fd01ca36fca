{"version": 3, "names": ["React", "_interopRequireWildcard", "require", "_getRequireWildcardCache", "e", "WeakMap", "r", "t", "__esModule", "default", "has", "get", "n", "__proto__", "a", "Object", "defineProperty", "getOwnPropertyDescriptor", "u", "hasOwnProperty", "call", "i", "set", "ScrollViewContext", "createContext", "__DEV__", "displayName", "_default", "exports", "HORIZONTAL", "freeze", "horizontal", "VERTICAL"], "sources": ["ScrollViewContext.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @flow strict\n * @format\n */\n\nimport * as React from 'react';\n\ntype Value = {horizontal: boolean} | null;\n\nconst ScrollViewContext: React.Context<Value> = React.createContext(null);\nif (__DEV__) {\n  ScrollViewContext.displayName = 'ScrollViewContext';\n}\nexport default ScrollViewContext;\n\n// $FlowFixMe[incompatible-type] frozen objects are readonly\nexport const HORIZONTAL: Value = Object.freeze({horizontal: true});\n// $FlowFixMe[incompatible-type] frozen objects are readonly\nexport const VERTICAL: Value = Object.freeze({horizontal: false});\n"], "mappings": ";;;;AAUA,IAAAA,KAAA,GAAAC,uBAAA,CAAAC,OAAA;AAA+B,SAAAC,yBAAAC,CAAA,6BAAAC,OAAA,mBAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,yBAAAC,CAAA,WAAAA,CAAA,GAAAG,CAAA,GAAAD,CAAA,KAAAF,CAAA;AAAA,SAAAH,wBAAAG,CAAA,EAAAE,CAAA,SAAAA,CAAA,IAAAF,CAAA,IAAAA,CAAA,CAAAI,UAAA,SAAAJ,CAAA,eAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,WAAAK,OAAA,EAAAL,CAAA,QAAAG,CAAA,GAAAJ,wBAAA,CAAAG,CAAA,OAAAC,CAAA,IAAAA,CAAA,CAAAG,GAAA,CAAAN,CAAA,UAAAG,CAAA,CAAAI,GAAA,CAAAP,CAAA,OAAAQ,CAAA,KAAAC,SAAA,UAAAC,CAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,CAAA,IAAAd,CAAA,oBAAAc,CAAA,OAAAC,cAAA,CAAAC,IAAA,CAAAhB,CAAA,EAAAc,CAAA,SAAAG,CAAA,GAAAP,CAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAb,CAAA,EAAAc,CAAA,UAAAG,CAAA,KAAAA,CAAA,CAAAV,GAAA,IAAAU,CAAA,CAAAC,GAAA,IAAAP,MAAA,CAAAC,cAAA,CAAAJ,CAAA,EAAAM,CAAA,EAAAG,CAAA,IAAAT,CAAA,CAAAM,CAAA,IAAAd,CAAA,CAAAc,CAAA,YAAAN,CAAA,CAAAH,OAAA,GAAAL,CAAA,EAAAG,CAAA,IAAAA,CAAA,CAAAe,GAAA,CAAAlB,CAAA,EAAAQ,CAAA,GAAAA,CAAA;AAI/B,IAAMW,iBAAuC,GAAGvB,KAAK,CAACwB,aAAa,CAAC,IAAI,CAAC;AACzE,IAAIC,OAAO,EAAE;EACXF,iBAAiB,CAACG,WAAW,GAAG,mBAAmB;AACrD;AAAC,IAAAC,QAAA,GAAAC,OAAA,CAAAnB,OAAA,GACcc,iBAAiB;AAGzB,IAAMM,UAAiB,GAAAD,OAAA,CAAAC,UAAA,GAAGd,MAAM,CAACe,MAAM,CAAC;EAACC,UAAU,EAAE;AAAI,CAAC,CAAC;AAE3D,IAAMC,QAAe,GAAAJ,OAAA,CAAAI,QAAA,GAAGjB,MAAM,CAACe,MAAM,CAAC;EAACC,UAAU,EAAE;AAAK,CAAC,CAAC", "ignoreList": []}