20f7d342b175f4563ed46b81b25bf55b
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var TurboModuleRegistry = _interopRequireWildcard(require("../../../../Libraries/TurboModule/TurboModuleRegistry"));
function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
var _default = exports.default = TurboModuleRegistry.getEnforcing('ImageLoader');
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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