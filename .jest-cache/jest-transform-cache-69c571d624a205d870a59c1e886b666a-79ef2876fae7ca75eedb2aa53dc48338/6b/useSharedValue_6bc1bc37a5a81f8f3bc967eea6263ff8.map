{"version": 3, "names": ["_interopRequireDefault", "require", "Object", "defineProperty", "exports", "value", "useSharedValue", "_slicedToArray2", "_react", "_index", "_core", "initialValue", "_useState", "useState", "makeMutable", "_useState2", "default", "mutable", "useEffect", "cancelAnimation"], "sources": ["../../../src/hook/useSharedValue.ts"], "sourcesContent": [null], "mappings": "AAAA,YAAY;;AAAA,IAAAA,sBAAA,GAAAC,OAAA;AAAAC,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,KAAA;AAAA;AAAAD,OAAA,CAAAE,cAAA,GAAAA,cAAA;AAAA,IAAAC,eAAA,GAAAP,sBAAA,CAAAC,OAAA;AACZ,IAAAO,MAAA,GAAAP,OAAA;AACA,IAAAQ,MAAA,GAAAR,OAAA;AAEA,IAAAS,KAAA,GAAAT,OAAA;AAaO,SAASK,cAAcA,CAAQK,YAAmB,EAAsB;EAC7E,IAAAC,SAAA,GAAkB,IAAAC,eAAQ,EAAC;MAAA,OAAM,IAAAC,iBAAW,EAACH,YAAY,CAAC;IAAA,EAAC;IAAAI,UAAA,OAAAR,eAAA,CAAAS,OAAA,EAAAJ,SAAA;IAApDK,OAAO,GAAAF,UAAA;EACd,IAAAG,gBAAS,EAAC,YAAM;IACd,OAAO,YAAM;MACX,IAAAC,sBAAe,EAACF,OAAO,CAAC;IAC1B,CAAC;EACH,CAAC,EAAE,CAACA,OAAO,CAAC,CAAC;EACb,OAAOA,OAAO;AAChB", "ignoreList": []}