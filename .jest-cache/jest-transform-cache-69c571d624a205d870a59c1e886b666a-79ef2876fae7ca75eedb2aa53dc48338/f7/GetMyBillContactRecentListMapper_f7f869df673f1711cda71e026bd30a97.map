{"version": 3, "names": ["cov_i0qyg63zy", "actualCoverage", "s", "exports", "mapGetMyBillContactRecentListResponseToModel", "mapRecentBillContactResponseToModel", "GetMyBillContactRecentListModel_1", "require", "response", "f", "map", "item", "data", "GetMyBillContactRecentModel", "id", "billCode", "category", "subGroupId", "customerName", "totalAmount", "period", "paymentDate", "accountNumber", "coreRef", "serviceCode", "arrangementId", "paymentOrderId", "cifNo", "console", "log"], "sources": ["/Users/<USER>/projects/react-native/payment-module/src/data/mappers/get-my-bill-contact-recent-list/GetMyBillContactRecentListMapper.ts"], "sourcesContent": ["import {\n  GetMyBillContactRecentListResponse,\n  GetMyBillContactRecentResponse,\n} from '../../models/get-my-bill-contact-recent-list/GetMyBillContactRecentListResponse';\nimport {\n  GetMyBillContactRecentListModel,\n  GetMyBillContactRecentModel,\n} from '../../../domain/entities/get-my-bill-contact-recent-list/GetMyBillContactRecentListModel';\n\nexport function mapGetMyBillContactRecentListResponseToModel(\n  response: GetMyBillContactRecentListResponse,\n): GetMyBillContactRecentListModel {\n  return response.map(item => mapRecentBillContactResponseToModel(item));\n}\n\nexport function mapRecentBillContactResponseToModel(\n  response: GetMyBillContactRecentResponse,\n): GetMyBillContactRecentModel {\n  const data = new GetMyBillContactRecentModel(\n    response.id,\n    response.billCode,\n    response.category,\n    response.subGroupId,\n    response.customerName,\n    response.totalAmount,\n    response.period,\n    response.paymentDate,\n    response.accountNumber,\n    response.coreRef,\n    response.serviceCode,\n    response.arrangementId,\n    response.paymentOrderId,\n    response.cifNo,\n  );\n\n  console.log('LOG CONTACT', data);\n  return data;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAmCU;IAAAA,aAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,aAAA;AAAAA,aAAA,GAAAE,CAAA;;;;;;AA1BVC,OAAA,CAAAC,4CAAA,GAAAA,4CAAA;AAAA;AAAAJ,aAAA,GAAAE,CAAA;AAMAC,OAAA,CAAAE,mCAAA,GAAAA,mCAAA;AAXA,IAAAC,iCAAA;AAAA;AAAA,CAAAN,aAAA,GAAAE,CAAA,OAAAK,OAAA;AAKA,SAAgBH,4CAA4CA,CAC1DI,QAA4C;EAAA;EAAAR,aAAA,GAAAS,CAAA;EAAAT,aAAA,GAAAE,CAAA;EAE5C,OAAOM,QAAQ,CAACE,GAAG,CAAC,UAAAC,IAAI;IAAA;IAAAX,aAAA,GAAAS,CAAA;IAAAT,aAAA,GAAAE,CAAA;IAAA,OAAIG,mCAAmC,CAACM,IAAI,CAAC;EAAA,EAAC;AACxE;AAEA,SAAgBN,mCAAmCA,CACjDG,QAAwC;EAAA;EAAAR,aAAA,GAAAS,CAAA;EAExC,IAAMG,IAAI;EAAA;EAAA,CAAAZ,aAAA,GAAAE,CAAA,OAAG,IAAII,iCAAA,CAAAO,2BAA2B,CAC1CL,QAAQ,CAACM,EAAE,EACXN,QAAQ,CAACO,QAAQ,EACjBP,QAAQ,CAACQ,QAAQ,EACjBR,QAAQ,CAACS,UAAU,EACnBT,QAAQ,CAACU,YAAY,EACrBV,QAAQ,CAACW,WAAW,EACpBX,QAAQ,CAACY,MAAM,EACfZ,QAAQ,CAACa,WAAW,EACpBb,QAAQ,CAACc,aAAa,EACtBd,QAAQ,CAACe,OAAO,EAChBf,QAAQ,CAACgB,WAAW,EACpBhB,QAAQ,CAACiB,aAAa,EACtBjB,QAAQ,CAACkB,cAAc,EACvBlB,QAAQ,CAACmB,KAAK,CACf;EAAA;EAAA3B,aAAA,GAAAE,CAAA;EAED0B,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEjB,IAAI,CAAC;EAAA;EAAAZ,aAAA,GAAAE,CAAA;EAChC,OAAOU,IAAI;AACb", "ignoreList": []}