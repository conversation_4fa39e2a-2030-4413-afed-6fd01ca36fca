6d50039dbc4ca0d0e942ccfdcc641446
"use strict";

/* istanbul ignore next */
function cov_i0qyg63zy() {
  var path = "/Users/<USER>/projects/react-native/payment-module/src/data/mappers/get-my-bill-contact-recent-list/GetMyBillContactRecentListMapper.ts";
  var hash = "8edb7e49c9965627392d33d28901c95242437a95";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/projects/react-native/payment-module/src/data/mappers/get-my-bill-contact-recent-list/GetMyBillContactRecentListMapper.ts",
    statementMap: {
      "0": {
        start: {
          line: 3,
          column: 0
        },
        end: {
          line: 5,
          column: 3
        }
      },
      "1": {
        start: {
          line: 6,
          column: 0
        },
        end: {
          line: 6,
          column: 100
        }
      },
      "2": {
        start: {
          line: 7,
          column: 0
        },
        end: {
          line: 7,
          column: 82
        }
      },
      "3": {
        start: {
          line: 8,
          column: 40
        },
        end: {
          line: 8,
          column: 139
        }
      },
      "4": {
        start: {
          line: 10,
          column: 2
        },
        end: {
          line: 12,
          column: 5
        }
      },
      "5": {
        start: {
          line: 11,
          column: 4
        },
        end: {
          line: 11,
          column: 53
        }
      },
      "6": {
        start: {
          line: 15,
          column: 13
        },
        end: {
          line: 15,
          column: 363
        }
      },
      "7": {
        start: {
          line: 16,
          column: 2
        },
        end: {
          line: 16,
          column: 35
        }
      },
      "8": {
        start: {
          line: 17,
          column: 2
        },
        end: {
          line: 17,
          column: 14
        }
      }
    },
    fnMap: {
      "0": {
        name: "mapGetMyBillContactRecentListResponseToModel",
        decl: {
          start: {
            line: 9,
            column: 9
          },
          end: {
            line: 9,
            column: 53
          }
        },
        loc: {
          start: {
            line: 9,
            column: 64
          },
          end: {
            line: 13,
            column: 1
          }
        },
        line: 9
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 10,
            column: 22
          },
          end: {
            line: 10,
            column: 23
          }
        },
        loc: {
          start: {
            line: 10,
            column: 38
          },
          end: {
            line: 12,
            column: 3
          }
        },
        line: 10
      },
      "2": {
        name: "mapRecentBillContactResponseToModel",
        decl: {
          start: {
            line: 14,
            column: 9
          },
          end: {
            line: 14,
            column: 44
          }
        },
        loc: {
          start: {
            line: 14,
            column: 55
          },
          end: {
            line: 18,
            column: 1
          }
        },
        line: 14
      }
    },
    branchMap: {},
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0
    },
    b: {},
    inputSourceMap: {
      version: 3,
      file: undefined,
      names: ["exports", "mapGetMyBillContactRecentListResponseToModel", "mapRecentBillContactResponseToModel", "GetMyBillContactRecentListModel_1", "require", "response", "map", "item", "data", "GetMyBillContactRecentModel", "id", "billCode", "category", "subGroupId", "customerName", "totalAmount", "period", "paymentDate", "accountNumber", "coreRef", "serviceCode", "arrangementId", "paymentOrderId", "cifNo", "console", "log"],
      sourceRoot: undefined,
      sources: ["/Users/<USER>/projects/react-native/payment-module/src/data/mappers/get-my-bill-contact-recent-list/GetMyBillContactRecentListMapper.ts"],
      sourcesContent: ["import {\n  GetMyBillContactRecentListResponse,\n  GetMyBillContactRecentResponse,\n} from '../../models/get-my-bill-contact-recent-list/GetMyBillContactRecentListResponse';\nimport {\n  GetMyBillContactRecentListModel,\n  GetMyBillContactRecentModel,\n} from '../../../domain/entities/get-my-bill-contact-recent-list/GetMyBillContactRecentListModel';\n\nexport function mapGetMyBillContactRecentListResponseToModel(\n  response: GetMyBillContactRecentListResponse,\n): GetMyBillContactRecentListModel {\n  return response.map(item => mapRecentBillContactResponseToModel(item));\n}\n\nexport function mapRecentBillContactResponseToModel(\n  response: GetMyBillContactRecentResponse,\n): GetMyBillContactRecentModel {\n  const data = new GetMyBillContactRecentModel(\n    response.id,\n    response.billCode,\n    response.category,\n    response.subGroupId,\n    response.customerName,\n    response.totalAmount,\n    response.period,\n    response.paymentDate,\n    response.accountNumber,\n    response.coreRef,\n    response.serviceCode,\n    response.arrangementId,\n    response.paymentOrderId,\n    response.cifNo,\n  );\n\n  console.log('LOG CONTACT', data);\n  return data;\n}\n"],
      mappings: ";;;;;AASAA,OAAA,CAAAC,4CAAA,GAAAA,4CAAA;AAMAD,OAAA,CAAAE,mCAAA,GAAAA,mCAAA;AAXA,IAAAC,iCAAA,GAAAC,OAAA;AAKA,SAAgBH,4CAA4CA,CAC1DI,QAA4C;EAE5C,OAAOA,QAAQ,CAACC,GAAG,CAAC,UAAAC,IAAI;IAAA,OAAIL,mCAAmC,CAACK,IAAI,CAAC;EAAA,EAAC;AACxE;AAEA,SAAgBL,mCAAmCA,CACjDG,QAAwC;EAExC,IAAMG,IAAI,GAAG,IAAIL,iCAAA,CAAAM,2BAA2B,CAC1CJ,QAAQ,CAACK,EAAE,EACXL,QAAQ,CAACM,QAAQ,EACjBN,QAAQ,CAACO,QAAQ,EACjBP,QAAQ,CAACQ,UAAU,EACnBR,QAAQ,CAACS,YAAY,EACrBT,QAAQ,CAACU,WAAW,EACpBV,QAAQ,CAACW,MAAM,EACfX,QAAQ,CAACY,WAAW,EACpBZ,QAAQ,CAACa,aAAa,EACtBb,QAAQ,CAACc,OAAO,EAChBd,QAAQ,CAACe,WAAW,EACpBf,QAAQ,CAACgB,aAAa,EACtBhB,QAAQ,CAACiB,cAAc,EACvBjB,QAAQ,CAACkB,KAAK,CACf;EAEDC,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEjB,IAAI,CAAC;EAChC,OAAOA,IAAI;AACb",
      ignoreList: []
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "8edb7e49c9965627392d33d28901c95242437a95"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_i0qyg63zy = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_i0qyg63zy();
cov_i0qyg63zy().s[0]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_i0qyg63zy().s[1]++;
exports.mapGetMyBillContactRecentListResponseToModel = mapGetMyBillContactRecentListResponseToModel;
/* istanbul ignore next */
cov_i0qyg63zy().s[2]++;
exports.mapRecentBillContactResponseToModel = mapRecentBillContactResponseToModel;
var GetMyBillContactRecentListModel_1 =
/* istanbul ignore next */
(cov_i0qyg63zy().s[3]++, require("../../../domain/entities/get-my-bill-contact-recent-list/GetMyBillContactRecentListModel"));
function mapGetMyBillContactRecentListResponseToModel(response) {
  /* istanbul ignore next */
  cov_i0qyg63zy().f[0]++;
  cov_i0qyg63zy().s[4]++;
  return response.map(function (item) {
    /* istanbul ignore next */
    cov_i0qyg63zy().f[1]++;
    cov_i0qyg63zy().s[5]++;
    return mapRecentBillContactResponseToModel(item);
  });
}
function mapRecentBillContactResponseToModel(response) {
  /* istanbul ignore next */
  cov_i0qyg63zy().f[2]++;
  var data =
  /* istanbul ignore next */
  (cov_i0qyg63zy().s[6]++, new GetMyBillContactRecentListModel_1.GetMyBillContactRecentModel(response.id, response.billCode, response.category, response.subGroupId, response.customerName, response.totalAmount, response.period, response.paymentDate, response.accountNumber, response.coreRef, response.serviceCode, response.arrangementId, response.paymentOrderId, response.cifNo));
  /* istanbul ignore next */
  cov_i0qyg63zy().s[7]++;
  console.log('LOG CONTACT', data);
  /* istanbul ignore next */
  cov_i0qyg63zy().s[8]++;
  return data;
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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