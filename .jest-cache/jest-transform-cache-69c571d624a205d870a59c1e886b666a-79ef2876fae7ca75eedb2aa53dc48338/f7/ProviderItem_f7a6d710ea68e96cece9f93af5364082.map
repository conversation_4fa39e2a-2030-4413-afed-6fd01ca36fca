{"version": 3, "names": ["cov_290m8kml25", "actualCoverage", "msb_shared_component_1", "s", "require", "react_native_1", "highlight_text_1", "__importDefault", "react_1", "ProviderItem", "_ref", "f", "_item$subGroupId$toSt", "_item$subGroupId", "item", "highlight", "onClick", "defaultValue", "index", "isTopup", "_ref2", "useMSBStyles", "makeStyle", "styles", "theme", "default", "createElement", "MSBTouchable", "testID", "style", "itemContainerV1", "onPress", "b", "View", "itemContainerV2", "MSBIcon", "folderIcon", "MSBFolderImage", "LOGO_TOPUP", "LOGO_BILLING", "icon", "subGroupId", "toString", "iconSize", "MSBIconSize", "SIZE_40", "styleContainer", "logo", "MSBFastImage", "nameImage", "folder", "ICON_SVG", "textContainer", "shortNameCitad", "shortName", "text", "getName", "search", "width", "serviceCode", "iconColor", "ColorItem", "IconBrand", "MSBIcons", "IconCheck", "SizeGlobal", "Size800", "line", "exports", "createMSBStyleSheet", "_ref3", "Typography", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ColorDataView", "ColorAlias", "ColorGlobal", "fullName", "Object", "assign", "base_regular", "color", "TextSub", "alignItems", "flexDirection", "paddingHorizontal", "SpacingXSmall", "height", "getSize", "padding", "Spacing4xSmall", "backgroundColor", "Neutral100", "listContainer", "NeutralWhite", "marginRight", "SpacingSmall", "resizeMode", "TextInformation", "base_semiBold", "status", "TextSuccess", "caption_regular", "marginTop", "flex"], "sources": ["/Users/<USER>/projects/react-native/payment-module/src/components/provider-list/ProviderItem.tsx"], "sourcesContent": ["import {\n  MSBTouchable,\n  MSBIcon,\n  ColorItem,\n  MSBIcons,\n  SizeGlobal,\n  MSBFolderImage,\n  MSBIconSize,\n  MSBFastImage,\n  createMSBStyleSheet,\n  useMSBStyles,\n  getSize,\n} from 'msb-shared-component';\nimport {View} from 'react-native';\nimport {ProviderModel} from '../../domain/entities/provider-list/ProviderListModel';\n// import Utils from '../../utils/Utils';\nimport HighlightText from '../highlight-text';\nimport React from 'react';\n\nexport interface ProviderItemProps {\n  item: ProviderModel;\n  highlight: string;\n  onClick?: (bankItem: ProviderModel) => void;\n  defaultValue?: ProviderModel | null | undefined;\n  index?: number;\n}\n\n// Hàm renderItem độc lập\nexport const ProviderItem: React.FC<ProviderItemProps> = ({item, highlight, onClick, defaultValue, index}) => {\n  const isTopup = item.isTopup();\n  const {styles, theme} = useMSBStyles(makeStyle);\n  return (\n    <MSBTouchable\n      testID={`transfer.beneficiaryScreen.pressBank.${index}}`}\n      style={styles.itemContainerV1}\n      onPress={() => onClick && onClick(item)}>\n      <View style={styles.itemContainerV2}>\n        {true ? (\n          // <Image source={Utils.getProviderIcon(item.getIconName())} style={styles.logo} />\n          // <MSBIcon icon=\"74\" folderIcon={MSBFolderImage.LOGO_TOPUP} iconSize={MSBIconSize.SIZE_24} />\n          // <MSBFastImage folder={MSBFolderImage.LOGO_TOPUP} source={74} style={styles.logo} />\n          <MSBIcon\n            folderIcon={isTopup ? MSBFolderImage.LOGO_TOPUP : MSBFolderImage.LOGO_BILLING}\n            icon={item.subGroupId?.toString() ?? ''}\n            iconSize={MSBIconSize.SIZE_40}\n            styleContainer={styles.logo}\n          />\n        ) : (\n          // <Image source={Utils.getDefaultIcon(item.categoryCode ?? '')} style={styles.logo} />\n          <MSBFastImage nameImage={'tone-bill'} style={styles.logo} folder={MSBFolderImage.ICON_SVG} />\n        )}\n        <View style={styles.textContainer}>\n          <View style={styles.shortNameCitad}>\n            <HighlightText style={styles.shortName} text={item.getName()} search={highlight} />\n            <View style={{width: 10}} />\n          </View>\n          {/* <HighlightText style={styles.fullName} text={item.description || ''} search={highlight} /> */}\n          {/*<Text style={styles.shortName}>{item.shortName}</Text>*/}\n          {/*<Text style={styles.fullName}>{item.fullName}</Text>*/}\n        </View>\n        {item?.serviceCode === defaultValue?.serviceCode && (\n          <MSBIcon iconColor={ColorItem.IconBrand} icon={MSBIcons.IconCheck} iconSize={SizeGlobal.Size800} />\n        )}\n      </View>\n      <View style={styles.line} />\n    </MSBTouchable>\n  );\n};\n\nconst makeStyle = createMSBStyleSheet(({Typography, SizeAlias, SizeGlobal, ColorDataView, ColorAlias, ColorGlobal}) => {\n  return {\n    fullName: {\n      ...Typography?.base_regular,\n      color: ColorDataView.TextSub,\n    },\n    itemContainerV1: {\n      alignItems: 'center',\n      flexDirection: 'column',\n      paddingHorizontal: SizeAlias.SpacingXSmall,\n    },\n    itemContainerV2: {\n      alignItems: 'center',\n      flexDirection: 'row',\n      height: getSize(72),\n      padding: SizeAlias.Spacing4xSmall,\n    },\n    line: {\n      backgroundColor: ColorGlobal.Neutral100,\n      height: 1,\n      paddingHorizontal: SizeAlias.SpacingXSmall,\n      width: '100%',\n    },\n    listContainer: {\n      backgroundColor: ColorGlobal.NeutralWhite,\n      padding: 0,\n    },\n    logo: {\n      height: SizeGlobal.Size800,\n      marginRight: SizeAlias.SpacingSmall,\n      resizeMode: 'contain',\n      width: SizeGlobal.Size800,\n    },\n    shortName: {\n      color: ColorAlias.TextInformation,\n      ...Typography?.base_semiBold,\n    },\n    shortNameCitad: {\n      alignItems: 'center',\n      flexDirection: 'row',\n    },\n    status: {\n      // color: ColorDataView '#007b00',\n      color: ColorAlias.TextSuccess,\n      ...Typography?.caption_regular,\n      marginTop: SizeAlias.Spacing4xSmall,\n    },\n    textContainer: {\n      flex: 1,\n    },\n  };\n});\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IA4Ba;IAAAA,cAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,cAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA5Bb,IAAAE,sBAAA;AAAA;AAAA,CAAAF,cAAA,GAAAG,CAAA,OAAAC,OAAA;AAaA,IAAAC,cAAA;AAAA;AAAA,CAAAL,cAAA,GAAAG,CAAA,OAAAC,OAAA;AAGA,IAAAE,gBAAA;AAAA;AAAA,CAAAN,cAAA,GAAAG,CAAA,OAAAI,eAAA,CAAAH,OAAA;AACA,IAAAI,OAAA;AAAA;AAAA,CAAAR,cAAA,GAAAG,CAAA,OAAAI,eAAA,CAAAH,OAAA;AAAA;AAAAJ,cAAA,GAAAG,CAAA;AAWO,IAAMM,YAAY,GAAgC,SAA5CA,YAAYA,CAAAC,IAAA,EAAoF;EAAA;EAAAV,cAAA,GAAAW,CAAA;EAAA,IAAAC,qBAAA,EAAAC,gBAAA;EAAA,IAAlDC,IAAI;IAAA;IAAA,CAAAd,cAAA,GAAAG,CAAA,OAAAO,IAAA,CAAJI,IAAI;IAAEC,SAAS;IAAA;IAAA,CAAAf,cAAA,GAAAG,CAAA,QAAAO,IAAA,CAATK,SAAS;IAAEC,OAAO;IAAA;IAAA,CAAAhB,cAAA,GAAAG,CAAA,QAAAO,IAAA,CAAPM,OAAO;IAAEC,YAAY;IAAA;IAAA,CAAAjB,cAAA,GAAAG,CAAA,QAAAO,IAAA,CAAZO,YAAY;IAAEC,KAAK;IAAA;IAAA,CAAAlB,cAAA,GAAAG,CAAA,QAAAO,IAAA,CAALQ,KAAK;EACtG,IAAMC,OAAO;EAAA;EAAA,CAAAnB,cAAA,GAAAG,CAAA,QAAGW,IAAI,CAACK,OAAO,EAAE;EAC9B,IAAAC,KAAA;IAAA;IAAA,CAAApB,cAAA,GAAAG,CAAA,QAAwB,IAAAD,sBAAA,CAAAmB,YAAY,EAACC,SAAS,CAAC;IAAxCC,MAAM;IAAA;IAAA,CAAAvB,cAAA,GAAAG,CAAA,QAAAiB,KAAA,CAANG,MAAM;IAAEC,KAAK;IAAA;IAAA,CAAAxB,cAAA,GAAAG,CAAA,QAAAiB,KAAA,CAALI,KAAK;EAAA;EAAAxB,cAAA,GAAAG,CAAA;EACpB,OACEK,OAAA,CAAAiB,OAAA,CAAAC,aAAA,CAACxB,sBAAA,CAAAyB,YAAY;IACXC,MAAM,EAAE,wCAAwCV,KAAK,GAAG;IACxDW,KAAK,EAAEN,MAAM,CAACO,eAAe;IAC7BC,OAAO,EAAE,SAATA,OAAOA,CAAA;MAAA;MAAA/B,cAAA,GAAAW,CAAA;MAAAX,cAAA,GAAAG,CAAA;MAAA,OAAQ,2BAAAH,cAAA,GAAAgC,CAAA,UAAAhB,OAAO;MAAA;MAAA,CAAAhB,cAAA,GAAAgC,CAAA,UAAIhB,OAAO,CAACF,IAAI,CAAC;IAAA;EAAA,GACvCN,OAAA,CAAAiB,OAAA,CAAAC,aAAA,CAACrB,cAAA,CAAA4B,IAAI;IAACJ,KAAK,EAAEN,MAAM,CAACW;EAAe,GAChC,IAAI;EAAA;EAAA,CAAAlC,cAAA,GAAAgC,CAAA,UAIHxB,OAAA,CAAAiB,OAAA,CAAAC,aAAA,CAACxB,sBAAA,CAAAiC,OAAO;IACNC,UAAU,EAAEjB,OAAO;IAAA;IAAA,CAAAnB,cAAA,GAAAgC,CAAA,UAAG9B,sBAAA,CAAAmC,cAAc,CAACC,UAAU;IAAA;IAAA,CAAAtC,cAAA,GAAAgC,CAAA,UAAG9B,sBAAA,CAAAmC,cAAc,CAACE,YAAY;IAC7EC,IAAI,GAAA5B,qBAAA,IAAAC,gBAAA,GAAEC,IAAI,CAAC2B,UAAU;IAAA;IAAA,CAAAzC,cAAA,GAAAgC,CAAA;IAAA;IAAA,CAAAhC,cAAA,GAAAgC,CAAA,UAAfnB,gBAAA,CAAiB6B,QAAQ,EAAE;IAAA;IAAA,CAAA1C,cAAA,GAAAgC,CAAA,UAAApB,qBAAA;IAAA;IAAA,CAAAZ,cAAA,GAAAgC,CAAA,UAAI,EAAE;IACvCW,QAAQ,EAAEzC,sBAAA,CAAA0C,WAAW,CAACC,OAAO;IAC7BC,cAAc,EAAEvB,MAAM,CAACwB;EAAI,EAC3B;EAAA;EAAA,CAAA/C,cAAA,GAAAgC,CAAA,UAGFxB,OAAA,CAAAiB,OAAA,CAAAC,aAAA,CAACxB,sBAAA,CAAA8C,YAAY;IAACC,SAAS,EAAE,WAAW;IAAEpB,KAAK,EAAEN,MAAM,CAACwB,IAAI;IAAEG,MAAM,EAAEhD,sBAAA,CAAAmC,cAAc,CAACc;EAAQ,EAC1F,GACD3C,OAAA,CAAAiB,OAAA,CAAAC,aAAA,CAACrB,cAAA,CAAA4B,IAAI;IAACJ,KAAK,EAAEN,MAAM,CAAC6B;EAAa,GAC/B5C,OAAA,CAAAiB,OAAA,CAAAC,aAAA,CAACrB,cAAA,CAAA4B,IAAI;IAACJ,KAAK,EAAEN,MAAM,CAAC8B;EAAc,GAChC7C,OAAA,CAAAiB,OAAA,CAAAC,aAAA,CAACpB,gBAAA,CAAAmB,OAAa;IAACI,KAAK,EAAEN,MAAM,CAAC+B,SAAS;IAAEC,IAAI,EAAEzC,IAAI,CAAC0C,OAAO,EAAE;IAAEC,MAAM,EAAE1C;EAAS,EAAI,EACnFP,OAAA,CAAAiB,OAAA,CAAAC,aAAA,CAACrB,cAAA,CAAA4B,IAAI;IAACJ,KAAK,EAAE;MAAC6B,KAAK,EAAE;IAAE;EAAC,EAAI,CACvB,CAIF;EACN;EAAA,CAAA1D,cAAA,GAAAgC,CAAA,WAAAlB,IAAI;EAAA;EAAA,CAAAd,cAAA,GAAAgC,CAAA;EAAA;EAAA,CAAAhC,cAAA,GAAAgC,CAAA,UAAJlB,IAAI,CAAE6C,WAAW,QAAK1C,YAAY;EAAA;EAAA,CAAAjB,cAAA,GAAAgC,CAAA;EAAA;EAAA,CAAAhC,cAAA,GAAAgC,CAAA,WAAZf,YAAY,CAAE0C,WAAW;EAAA;EAAA,CAAA3D,cAAA,GAAAgC,CAAA,UAC9CxB,OAAA,CAAAiB,OAAA,CAAAC,aAAA,CAACxB,sBAAA,CAAAiC,OAAO;IAACyB,SAAS,EAAE1D,sBAAA,CAAA2D,SAAS,CAACC,SAAS;IAAEtB,IAAI,EAAEtC,sBAAA,CAAA6D,QAAQ,CAACC,SAAS;IAAErB,QAAQ,EAAEzC,sBAAA,CAAA+D,UAAU,CAACC;EAAO,EAChG,EACI,EACP1D,OAAA,CAAAiB,OAAA,CAAAC,aAAA,CAACrB,cAAA,CAAA4B,IAAI;IAACJ,KAAK,EAAEN,MAAM,CAAC4C;EAAI,EAAI,CACf;AAEnB,CAAC;AAAA;AAAAnE,cAAA,GAAAG,CAAA;AAvCYiE,OAAA,CAAA3D,YAAY,GAAAA,YAAA;AAyCzB,IAAMa,SAAS;AAAA;AAAA,CAAAtB,cAAA,GAAAG,CAAA,QAAG,IAAAD,sBAAA,CAAAmE,mBAAmB,EAAC,UAAAC,KAAA,EAAgF;EAAA;EAAAtE,cAAA,GAAAW,CAAA;EAAA,IAA9E4D,UAAU;IAAA;IAAA,CAAAvE,cAAA,GAAAG,CAAA,QAAAmE,KAAA,CAAVC,UAAU;IAAEC,SAAS;IAAA;IAAA,CAAAxE,cAAA,GAAAG,CAAA,QAAAmE,KAAA,CAATE,SAAS;IAAEP,UAAU;IAAA;IAAA,CAAAjE,cAAA,GAAAG,CAAA,QAAAmE,KAAA,CAAVL,UAAU;IAAEQ,aAAa;IAAA;IAAA,CAAAzE,cAAA,GAAAG,CAAA,QAAAmE,KAAA,CAAbG,aAAa;IAAEC,UAAU;IAAA;IAAA,CAAA1E,cAAA,GAAAG,CAAA,QAAAmE,KAAA,CAAVI,UAAU;IAAEC,WAAW;IAAA;IAAA,CAAA3E,cAAA,GAAAG,CAAA,QAAAmE,KAAA,CAAXK,WAAW;EAAA;EAAA3E,cAAA,GAAAG,CAAA;EAC/G,OAAO;IACLyE,QAAQ,EAAAC,MAAA,CAAAC,MAAA,KACHP,UAAU;IAAA;IAAA,CAAAvE,cAAA,GAAAgC,CAAA;IAAA;IAAA,CAAAhC,cAAA,GAAAgC,CAAA,WAAVuC,UAAU,CAAEQ,YAAY;MAC3BC,KAAK,EAAEP,aAAa,CAACQ;IAAO,EAC7B;IACDnD,eAAe,EAAE;MACfoD,UAAU,EAAE,QAAQ;MACpBC,aAAa,EAAE,QAAQ;MACvBC,iBAAiB,EAAEZ,SAAS,CAACa;KAC9B;IACDnD,eAAe,EAAE;MACfgD,UAAU,EAAE,QAAQ;MACpBC,aAAa,EAAE,KAAK;MACpBG,MAAM,EAAE,IAAApF,sBAAA,CAAAqF,OAAO,EAAC,EAAE,CAAC;MACnBC,OAAO,EAAEhB,SAAS,CAACiB;KACpB;IACDtB,IAAI,EAAE;MACJuB,eAAe,EAAEf,WAAW,CAACgB,UAAU;MACvCL,MAAM,EAAE,CAAC;MACTF,iBAAiB,EAAEZ,SAAS,CAACa,aAAa;MAC1C3B,KAAK,EAAE;KACR;IACDkC,aAAa,EAAE;MACbF,eAAe,EAAEf,WAAW,CAACkB,YAAY;MACzCL,OAAO,EAAE;KACV;IACDzC,IAAI,EAAE;MACJuC,MAAM,EAAErB,UAAU,CAACC,OAAO;MAC1B4B,WAAW,EAAEtB,SAAS,CAACuB,YAAY;MACnCC,UAAU,EAAE,SAAS;MACrBtC,KAAK,EAAEO,UAAU,CAACC;KACnB;IACDZ,SAAS,EAAAuB,MAAA,CAAAC,MAAA;MACPE,KAAK,EAAEN,UAAU,CAACuB;IAAe,GAC9B1B,UAAU;IAAA;IAAA,CAAAvE,cAAA,GAAAgC,CAAA;IAAA;IAAA,CAAAhC,cAAA,GAAAgC,CAAA,WAAVuC,UAAU,CAAE2B,aAAa,EAC7B;IACD7C,cAAc,EAAE;MACd6B,UAAU,EAAE,QAAQ;MACpBC,aAAa,EAAE;KAChB;IACDgB,MAAM,EAAAtB,MAAA,CAAAC,MAAA;MAEJE,KAAK,EAAEN,UAAU,CAAC0B;IAAW,GAC1B7B,UAAU;IAAA;IAAA,CAAAvE,cAAA,GAAAgC,CAAA;IAAA;IAAA,CAAAhC,cAAA,GAAAgC,CAAA,WAAVuC,UAAU,CAAE8B,eAAe;MAC9BC,SAAS,EAAE9B,SAAS,CAACiB;IAAc,EACpC;IACDrC,aAAa,EAAE;MACbmD,IAAI,EAAE;;GAET;AACH,CAAC,CAAC", "ignoreList": []}