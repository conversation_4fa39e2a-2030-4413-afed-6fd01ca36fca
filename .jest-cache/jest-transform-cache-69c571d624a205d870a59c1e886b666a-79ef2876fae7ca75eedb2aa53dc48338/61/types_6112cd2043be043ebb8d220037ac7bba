ef55f30c0591fc63da6bed5f84c72a1d
"use strict";

/* istanbul ignore next */
function cov_26gftqi1gy() {
  var path = "/Users/<USER>/projects/react-native/payment-module/src/presentation/payment-result/components/transfer-result/types.ts";
  var hash = "eec589d0de8e078e3f2dc5ffdd17b5afdc94b210";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/projects/react-native/payment-module/src/presentation/payment-result/components/transfer-result/types.ts",
    statementMap: {
      "0": {
        start: {
          line: 3,
          column: 0
        },
        end: {
          line: 5,
          column: 3
        }
      }
    },
    fnMap: {},
    branchMap: {},
    s: {
      "0": 0
    },
    f: {},
    b: {},
    inputSourceMap: {
      version: 3,
      file: undefined,
      names: [],
      sourceRoot: undefined,
      sources: ["/Users/<USER>/projects/react-native/payment-module/src/presentation/payment-result/components/transfer-result/types.ts"],
      sourcesContent: ["import {ViewStyle} from 'react-native';\n\nexport type TransferResultProps = {\n  style?: ViewStyle;\n  title?: string;\n  amount?: number | string;\n  resultType?: string;\n  isDetail?: boolean;\n  date?: string;\n  paymentType?: string;\n};\n"],
      mappings: "",
      ignoreList: []
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "eec589d0de8e078e3f2dc5ffdd17b5afdc94b210"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_26gftqi1gy = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_26gftqi1gy();
cov_26gftqi1gy().s[0]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6W10sInNvdXJjZXMiOlsiL1VzZXJzL3NvbnNhbXNldC9wcm9qZWN0cy9yZWFjdC1uYXRpdmUvcGF5bWVudC1tb2R1bGUvc3JjL3ByZXNlbnRhdGlvbi9wYXltZW50LXJlc3VsdC9jb21wb25lbnRzL3RyYW5zZmVyLXJlc3VsdC90eXBlcy50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQge1ZpZXdTdHlsZX0gZnJvbSAncmVhY3QtbmF0aXZlJztcblxuZXhwb3J0IHR5cGUgVHJhbnNmZXJSZXN1bHRQcm9wcyA9IHtcbiAgc3R5bGU/OiBWaWV3U3R5bGU7XG4gIHRpdGxlPzogc3RyaW5nO1xuICBhbW91bnQ/OiBudW1iZXIgfCBzdHJpbmc7XG4gIHJlc3VsdFR5cGU/OiBzdHJpbmc7XG4gIGlzRGV0YWlsPzogYm9vbGVhbjtcbiAgZGF0ZT86IHN0cmluZztcbiAgcGF5bWVudFR5cGU/OiBzdHJpbmc7XG59O1xuIl0sIm1hcHBpbmdzIjoiIiwiaWdub3JlTGlzdCI6W119