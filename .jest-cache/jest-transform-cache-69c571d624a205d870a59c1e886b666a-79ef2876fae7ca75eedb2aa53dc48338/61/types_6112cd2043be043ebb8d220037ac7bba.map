{"version": 3, "names": [], "sources": ["/Users/<USER>/projects/react-native/payment-module/src/presentation/payment-result/components/transfer-result/types.ts"], "sourcesContent": ["import {ViewStyle} from 'react-native';\n\nexport type TransferResultProps = {\n  style?: ViewStyle;\n  title?: string;\n  amount?: number | string;\n  resultType?: string;\n  isDetail?: boolean;\n  date?: string;\n  paymentType?: string;\n};\n"], "mappings": "", "ignoreList": []}