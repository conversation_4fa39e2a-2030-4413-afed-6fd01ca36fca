7cbde5464663b03701fce9b80c07328a
"use strict";

/* istanbul ignore next */
function cov_1j8qxna1tn() {
  var path = "/Users/<USER>/projects/react-native/payment-module/src/data/models/save-bill-contact/SaveBillContactResponse.ts";
  var hash = "5dd2d439635427939e71bb96c9487191a04e9c86";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/projects/react-native/payment-module/src/data/models/save-bill-contact/SaveBillContactResponse.ts",
    statementMap: {
      "0": {
        start: {
          line: 3,
          column: 0
        },
        end: {
          line: 5,
          column: 3
        }
      }
    },
    fnMap: {},
    branchMap: {},
    s: {
      "0": 0
    },
    f: {},
    b: {},
    inputSourceMap: {
      version: 3,
      file: undefined,
      names: [],
      sourceRoot: undefined,
      sources: ["/Users/<USER>/projects/react-native/payment-module/src/data/models/save-bill-contact/SaveBillContactResponse.ts"],
      sourcesContent: ["export interface SaveBillContactResponse {\n  // TODO: define fields\n}\n"],
      mappings: "",
      ignoreList: []
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "5dd2d439635427939e71bb96c9487191a04e9c86"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_1j8qxna1tn = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_1j8qxna1tn();
cov_1j8qxna1tn().s[0]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6W10sInNvdXJjZXMiOlsiL1VzZXJzL3NvbnNhbXNldC9wcm9qZWN0cy9yZWFjdC1uYXRpdmUvcGF5bWVudC1tb2R1bGUvc3JjL2RhdGEvbW9kZWxzL3NhdmUtYmlsbC1jb250YWN0L1NhdmVCaWxsQ29udGFjdFJlc3BvbnNlLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBpbnRlcmZhY2UgU2F2ZUJpbGxDb250YWN0UmVzcG9uc2Uge1xuICAvLyBUT0RPOiBkZWZpbmUgZmllbGRzXG59XG4iXSwibWFwcGluZ3MiOiIiLCJpZ25vcmVMaXN0IjpbXX0=