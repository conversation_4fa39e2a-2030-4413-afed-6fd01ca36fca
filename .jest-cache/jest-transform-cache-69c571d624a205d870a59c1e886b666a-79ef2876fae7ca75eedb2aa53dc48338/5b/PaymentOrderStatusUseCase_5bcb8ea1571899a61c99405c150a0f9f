5cd0cd454198c4498314e459f86a6980
"use strict";

/* istanbul ignore next */
function cov_1byase1e00() {
  var path = "/Users/<USER>/projects/react-native/payment-module/src/domain/usecases/payment-order/PaymentOrderStatusUseCase.ts";
  var hash = "5b136d47cccdb46e68a5d81246ea19d5241a9b18";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/projects/react-native/payment-module/src/domain/usecases/payment-order/PaymentOrderStatusUseCase.ts",
    statementMap: {
      "0": {
        start: {
          line: 3,
          column: 29
        },
        end: {
          line: 3,
          column: 84
        }
      },
      "1": {
        start: {
          line: 4,
          column: 25
        },
        end: {
          line: 4,
          column: 99
        }
      },
      "2": {
        start: {
          line: 5,
          column: 23
        },
        end: {
          line: 5,
          column: 95
        }
      },
      "3": {
        start: {
          line: 6,
          column: 20
        },
        end: {
          line: 6,
          column: 89
        }
      },
      "4": {
        start: {
          line: 7,
          column: 0
        },
        end: {
          line: 9,
          column: 3
        }
      },
      "5": {
        start: {
          line: 10,
          column: 0
        },
        end: {
          line: 10,
          column: 43
        }
      },
      "6": {
        start: {
          line: 11,
          column: 26
        },
        end: {
          line: 11,
          column: 69
        }
      },
      "7": {
        start: {
          line: 12,
          column: 32
        },
        end: {
          line: 32,
          column: 3
        }
      },
      "8": {
        start: {
          line: 14,
          column: 4
        },
        end: {
          line: 14,
          column: 67
        }
      },
      "9": {
        start: {
          line: 15,
          column: 4
        },
        end: {
          line: 15,
          column: 33
        }
      },
      "10": {
        start: {
          line: 17,
          column: 2
        },
        end: {
          line: 31,
          column: 6
        }
      },
      "11": {
        start: {
          line: 20,
          column: 21
        },
        end: {
          line: 25,
          column: 8
        }
      },
      "12": {
        start: {
          line: 21,
          column: 20
        },
        end: {
          line: 21,
          column: 24
        }
      },
      "13": {
        start: {
          line: 22,
          column: 8
        },
        end: {
          line: 24,
          column: 11
        }
      },
      "14": {
        start: {
          line: 23,
          column: 10
        },
        end: {
          line: 23,
          column: 62
        }
      },
      "15": {
        start: {
          line: 27,
          column: 8
        },
        end: {
          line: 27,
          column: 47
        }
      },
      "16": {
        start: {
          line: 29,
          column: 6
        },
        end: {
          line: 29,
          column: 21
        }
      },
      "17": {
        start: {
          line: 33,
          column: 0
        },
        end: {
          line: 33,
          column: 62
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 12,
            column: 32
          },
          end: {
            line: 12,
            column: 33
          }
        },
        loc: {
          start: {
            line: 12,
            column: 44
          },
          end: {
            line: 32,
            column: 1
          }
        },
        line: 12
      },
      "1": {
        name: "PaymentOrderStatusUseCase",
        decl: {
          start: {
            line: 13,
            column: 11
          },
          end: {
            line: 13,
            column: 36
          }
        },
        loc: {
          start: {
            line: 13,
            column: 49
          },
          end: {
            line: 16,
            column: 3
          }
        },
        line: 13
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 19,
            column: 11
          },
          end: {
            line: 19,
            column: 12
          }
        },
        loc: {
          start: {
            line: 19,
            column: 23
          },
          end: {
            line: 30,
            column: 5
          }
        },
        line: 19
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 20,
            column: 53
          },
          end: {
            line: 20,
            column: 54
          }
        },
        loc: {
          start: {
            line: 20,
            column: 73
          },
          end: {
            line: 25,
            column: 7
          }
        },
        line: 20
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 22,
            column: 60
          },
          end: {
            line: 22,
            column: 61
          }
        },
        loc: {
          start: {
            line: 22,
            column: 72
          },
          end: {
            line: 24,
            column: 9
          }
        },
        line: 22
      },
      "5": {
        name: "execute",
        decl: {
          start: {
            line: 26,
            column: 15
          },
          end: {
            line: 26,
            column: 22
          }
        },
        loc: {
          start: {
            line: 26,
            column: 27
          },
          end: {
            line: 28,
            column: 7
          }
        },
        line: 26
      }
    },
    branchMap: {},
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0
    },
    b: {},
    inputSourceMap: {
      version: 3,
      file: undefined,
      names: ["ExcecutionHandler_1", "require", "PaymentOrderStatusUseCase", "repository", "_classCallCheck2", "default", "_createClass2", "key", "value", "_execute", "_asyncToGenerator2", "request", "_this", "ExecutionHandler", "execute", "paymentOrderStatus", "_x", "apply", "arguments", "exports"],
      sourceRoot: undefined,
      sources: ["/Users/<USER>/projects/react-native/payment-module/src/domain/usecases/payment-order/PaymentOrderStatusUseCase.ts"],
      sourcesContent: ["import {IPaymentOrderRepository} from '../../repositories/IPaymentOrderRepository';\nimport {PaymentOrderStatusModel} from '../../entities/payment-order-status/PaymentOrderStatusModel';\nimport {ResultState} from '../../../core/ResultState';\nimport {ExecutionHandler} from '../../../utils/ExcecutionHandler';\nimport {PaymentOrderStatusRequest} from '../../../data/models/payment-order-status/PaymentOrderStatusRequest';\nexport class PaymentOrderStatusUseCase {\n  private repository: IPaymentOrderRepository;\n\n  constructor(repository: IPaymentOrderRepository) {\n    this.repository = repository;\n  }\n\n  public async execute(request: PaymentOrderStatusRequest): Promise<ResultState<PaymentOrderStatusModel>> {\n    // call this.repository.paymentOrderStatus(...)\n    return ExecutionHandler.execute(() => this.repository.paymentOrderStatus(request));\n  }\n}\n"],
      mappings: ";;;;;;;;;;AAGA,IAAAA,mBAAA,GAAAC,OAAA;AAAkE,IAErDC,yBAAyB;EAGpC,SAAAA,0BAAYC,UAAmC;IAAA,IAAAC,gBAAA,CAAAC,OAAA,QAAAH,yBAAA;IAC7C,IAAI,CAACC,UAAU,GAAGA,UAAU;EAC9B;EAAC,WAAAG,aAAA,CAAAD,OAAA,EAAAH,yBAAA;IAAAK,GAAA;IAAAC,KAAA;MAAA,IAAAC,QAAA,OAAAC,kBAAA,CAAAL,OAAA,EAEM,WAAcM,OAAkC;QAAA,IAAAC,KAAA;QAErD,OAAOZ,mBAAA,CAAAa,gBAAgB,CAACC,OAAO,CAAC;UAAA,OAAMF,KAAI,CAACT,UAAU,CAACY,kBAAkB,CAACJ,OAAO,CAAC;QAAA,EAAC;MACpF,CAAC;MAAA,SAHYG,OAAOA,CAAAE,EAAA;QAAA,OAAAP,QAAA,CAAAQ,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAPJ,OAAO;IAAA;EAAA;AAAA;AAPtBK,OAAA,CAAAjB,yBAAA,GAAAA,yBAAA",
      ignoreList: []
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "5b136d47cccdb46e68a5d81246ea19d5241a9b18"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_1byase1e00 = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_1byase1e00();
var _interopRequireDefault =
/* istanbul ignore next */
(cov_1byase1e00().s[0]++, require("@babel/runtime/helpers/interopRequireDefault"));
var _asyncToGenerator2 =
/* istanbul ignore next */
(cov_1byase1e00().s[1]++, _interopRequireDefault(require("@babel/runtime/helpers/asyncToGenerator")));
var _classCallCheck2 =
/* istanbul ignore next */
(cov_1byase1e00().s[2]++, _interopRequireDefault(require("@babel/runtime/helpers/classCallCheck")));
var _createClass2 =
/* istanbul ignore next */
(cov_1byase1e00().s[3]++, _interopRequireDefault(require("@babel/runtime/helpers/createClass")));
/* istanbul ignore next */
cov_1byase1e00().s[4]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_1byase1e00().s[5]++;
exports.PaymentOrderStatusUseCase = void 0;
var ExcecutionHandler_1 =
/* istanbul ignore next */
(cov_1byase1e00().s[6]++, require("../../../utils/ExcecutionHandler"));
var PaymentOrderStatusUseCase =
/* istanbul ignore next */
(cov_1byase1e00().s[7]++, function () {
  /* istanbul ignore next */
  cov_1byase1e00().f[0]++;
  function PaymentOrderStatusUseCase(repository) {
    /* istanbul ignore next */
    cov_1byase1e00().f[1]++;
    cov_1byase1e00().s[8]++;
    (0, _classCallCheck2.default)(this, PaymentOrderStatusUseCase);
    /* istanbul ignore next */
    cov_1byase1e00().s[9]++;
    this.repository = repository;
  }
  /* istanbul ignore next */
  cov_1byase1e00().s[10]++;
  return (0, _createClass2.default)(PaymentOrderStatusUseCase, [{
    key: "execute",
    value: function () {
      /* istanbul ignore next */
      cov_1byase1e00().f[2]++;
      var _execute =
      /* istanbul ignore next */
      (cov_1byase1e00().s[11]++, (0, _asyncToGenerator2.default)(function* (request) {
        /* istanbul ignore next */
        cov_1byase1e00().f[3]++;
        var _this =
        /* istanbul ignore next */
        (cov_1byase1e00().s[12]++, this);
        /* istanbul ignore next */
        cov_1byase1e00().s[13]++;
        return ExcecutionHandler_1.ExecutionHandler.execute(function () {
          /* istanbul ignore next */
          cov_1byase1e00().f[4]++;
          cov_1byase1e00().s[14]++;
          return _this.repository.paymentOrderStatus(request);
        });
      }));
      function execute(_x) {
        /* istanbul ignore next */
        cov_1byase1e00().f[5]++;
        cov_1byase1e00().s[15]++;
        return _execute.apply(this, arguments);
      }
      /* istanbul ignore next */
      cov_1byase1e00().s[16]++;
      return execute;
    }()
  }]);
}());
/* istanbul ignore next */
cov_1byase1e00().s[17]++;
exports.PaymentOrderStatusUseCase = PaymentOrderStatusUseCase;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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