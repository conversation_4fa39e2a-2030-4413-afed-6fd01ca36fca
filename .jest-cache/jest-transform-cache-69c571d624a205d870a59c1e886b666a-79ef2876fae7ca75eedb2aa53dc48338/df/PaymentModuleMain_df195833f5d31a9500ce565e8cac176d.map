{"version": 3, "names": ["cov_1ev0ychahr", "actualCoverage", "LocaleContext_1", "s", "require", "msb_host_shared_module_1", "react_1", "__importDefault", "i18n_ts_1", "PaymentStack_tsx_1", "PaymentModuleMain", "f", "_ref", "useHostInjection", "locale", "default", "createElement", "LocaleProvider", "translations", "defaultLocale", "exports"], "sources": ["/Users/<USER>/projects/react-native/payment-module/src/PaymentModuleMain.tsx"], "sourcesContent": ["import {LocaleProvider} from 'msb-communication-lib/dist/locales/LocaleContext';\nimport {useHostInjection} from 'msb-host-shared-module';\nimport React from 'react';\n\nimport {translations} from './locales/i18n.ts';\nimport PaymentStack from './navigation/PaymentStack.tsx';\n\nconst PaymentModuleMain = () => {\n  const {locale} = useHostInjection();\n\n  return (\n    <LocaleProvider translations={translations} defaultLocale={locale}>\n      <PaymentStack />\n    </LocaleProvider>\n  );\n};\n\nexport default PaymentModuleMain;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAOM;IAAAA,cAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,cAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAPN,IAAAE,eAAA;AAAA;AAAA,CAAAF,cAAA,GAAAG,CAAA,OAAAC,OAAA;AACA,IAAAC,wBAAA;AAAA;AAAA,CAAAL,cAAA,GAAAG,CAAA,OAAAC,OAAA;AACA,IAAAE,OAAA;AAAA;AAAA,CAAAN,cAAA,GAAAG,CAAA,OAAAI,eAAA,CAAAH,OAAA;AAEA,IAAAI,SAAA;AAAA;AAAA,CAAAR,cAAA,GAAAG,CAAA,OAAAC,OAAA;AACA,IAAAK,kBAAA;AAAA;AAAA,CAAAT,cAAA,GAAAG,CAAA,OAAAI,eAAA,CAAAH,OAAA;AAAA;AAAAJ,cAAA,GAAAG,CAAA;AAEA,IAAMO,iBAAiB,GAAG,SAApBA,iBAAiBA,CAAA,EAAQ;EAAA;EAAAV,cAAA,GAAAW,CAAA;EAC7B,IAAAC,IAAA;IAAA;IAAA,CAAAZ,cAAA,GAAAG,CAAA,OAAiB,IAAAE,wBAAA,CAAAQ,gBAAgB,GAAE;IAA5BC,MAAM;IAAA;IAAA,CAAAd,cAAA,GAAAG,CAAA,QAAAS,IAAA,CAANE,MAAM;EAAA;EAAAd,cAAA,GAAAG,CAAA;EAEb,OACEG,OAAA,CAAAS,OAAA,CAAAC,aAAA,CAACd,eAAA,CAAAe,cAAc;IAACC,YAAY,EAAEV,SAAA,CAAAU,YAAY;IAAEC,aAAa,EAAEL;EAAM,GAC/DR,OAAA,CAAAS,OAAA,CAAAC,aAAA,CAACP,kBAAA,CAAAM,OAAY,OAAG,CACD;AAErB,CAAC;AAAA;AAAAf,cAAA,GAAAG,CAAA;AAEDiB,OAAA,CAAAL,OAAA,GAAeL,iBAAiB", "ignoreList": []}