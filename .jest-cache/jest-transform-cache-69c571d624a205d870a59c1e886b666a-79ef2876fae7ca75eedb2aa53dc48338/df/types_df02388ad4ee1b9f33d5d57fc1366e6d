cb34286c96a36818dcb46488c1ec3b7f
"use strict";

/* istanbul ignore next */
function cov_16xqdz8gra() {
  var path = "/Users/<USER>/projects/react-native/payment-module/src/navigation/types.ts";
  var hash = "5ac70df80a6e153753438ea8df0fdd75babaf95c";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/projects/react-native/payment-module/src/navigation/types.ts",
    statementMap: {
      "0": {
        start: {
          line: 3,
          column: 0
        },
        end: {
          line: 5,
          column: 3
        }
      }
    },
    fnMap: {},
    branchMap: {},
    s: {
      "0": 0
    },
    f: {},
    b: {},
    inputSourceMap: {
      version: 3,
      file: undefined,
      names: [],
      sourceRoot: undefined,
      sources: ["/Users/<USER>/projects/react-native/payment-module/src/navigation/types.ts"],
      sourcesContent: ["import {PaymentOrderRequest} from '../data/models/payment-order/PaymentOrderRequest';\nimport {GetBillDetailModel} from '../domain/entities/get-bill-detail/GetBillDetailModel';\nimport {ProviderModel} from '../domain/entities/provider-list/ProviderListModel';\n\nexport interface PaymentInfoModel {\n  title?: string;\n  categoryName?: string;\n  billInfo?: GetBillDetailModel;\n  paymentValidate?: PaymentOrderRequest;\n  originatorAccount?: {\n    identification?: string;\n    name?: string;\n    accountNo?: string;\n    bankName?: string;\n    bankCode?: string;\n  };\n  paymentResultType?: string;\n  contractName?: string;\n  provider?: ProviderModel | null;\n  additionalInfo?: string;\n  qrPaymentInfo?: QRPaymentInfoModel;\n}\n\nexport interface QRPaymentInfoModel {\n  accountNo?: string;\n  bankCode?: string;\n  remark?: string;\n  service?: string;\n  qrType?: string;\n  amount?: number;\n  serviceCode?: string;\n  storeId?: string;\n  merchantName?: string;\n  payType?: string;\n  qrFormat?: string;\n  quantity?: number;\n  expiredTime?: string;\n  qrContent?: string;\n}\n"],
      mappings: "",
      ignoreList: []
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "5ac70df80a6e153753438ea8df0fdd75babaf95c"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_16xqdz8gra = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_16xqdz8gra();
cov_16xqdz8gra().s[0]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6W10sInNvdXJjZXMiOlsiL1VzZXJzL3NvbnNhbXNldC9wcm9qZWN0cy9yZWFjdC1uYXRpdmUvcGF5bWVudC1tb2R1bGUvc3JjL25hdmlnYXRpb24vdHlwZXMudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHtQYXltZW50T3JkZXJSZXF1ZXN0fSBmcm9tICcuLi9kYXRhL21vZGVscy9wYXltZW50LW9yZGVyL1BheW1lbnRPcmRlclJlcXVlc3QnO1xuaW1wb3J0IHtHZXRCaWxsRGV0YWlsTW9kZWx9IGZyb20gJy4uL2RvbWFpbi9lbnRpdGllcy9nZXQtYmlsbC1kZXRhaWwvR2V0QmlsbERldGFpbE1vZGVsJztcbmltcG9ydCB7UHJvdmlkZXJNb2RlbH0gZnJvbSAnLi4vZG9tYWluL2VudGl0aWVzL3Byb3ZpZGVyLWxpc3QvUHJvdmlkZXJMaXN0TW9kZWwnO1xuXG5leHBvcnQgaW50ZXJmYWNlIFBheW1lbnRJbmZvTW9kZWwge1xuICB0aXRsZT86IHN0cmluZztcbiAgY2F0ZWdvcnlOYW1lPzogc3RyaW5nO1xuICBiaWxsSW5mbz86IEdldEJpbGxEZXRhaWxNb2RlbDtcbiAgcGF5bWVudFZhbGlkYXRlPzogUGF5bWVudE9yZGVyUmVxdWVzdDtcbiAgb3JpZ2luYXRvckFjY291bnQ/OiB7XG4gICAgaWRlbnRpZmljYXRpb24/OiBzdHJpbmc7XG4gICAgbmFtZT86IHN0cmluZztcbiAgICBhY2NvdW50Tm8/OiBzdHJpbmc7XG4gICAgYmFua05hbWU/OiBzdHJpbmc7XG4gICAgYmFua0NvZGU/OiBzdHJpbmc7XG4gIH07XG4gIHBheW1lbnRSZXN1bHRUeXBlPzogc3RyaW5nO1xuICBjb250cmFjdE5hbWU/OiBzdHJpbmc7XG4gIHByb3ZpZGVyPzogUHJvdmlkZXJNb2RlbCB8IG51bGw7XG4gIGFkZGl0aW9uYWxJbmZvPzogc3RyaW5nO1xuICBxclBheW1lbnRJbmZvPzogUVJQYXltZW50SW5mb01vZGVsO1xufVxuXG5leHBvcnQgaW50ZXJmYWNlIFFSUGF5bWVudEluZm9Nb2RlbCB7XG4gIGFjY291bnRObz86IHN0cmluZztcbiAgYmFua0NvZGU/OiBzdHJpbmc7XG4gIHJlbWFyaz86IHN0cmluZztcbiAgc2VydmljZT86IHN0cmluZztcbiAgcXJUeXBlPzogc3RyaW5nO1xuICBhbW91bnQ/OiBudW1iZXI7XG4gIHNlcnZpY2VDb2RlPzogc3RyaW5nO1xuICBzdG9yZUlkPzogc3RyaW5nO1xuICBtZXJjaGFudE5hbWU/OiBzdHJpbmc7XG4gIHBheVR5cGU/OiBzdHJpbmc7XG4gIHFyRm9ybWF0Pzogc3RyaW5nO1xuICBxdWFudGl0eT86IG51bWJlcjtcbiAgZXhwaXJlZFRpbWU/OiBzdHJpbmc7XG4gIHFyQ29udGVudD86IHN0cmluZztcbn1cbiJdLCJtYXBwaW5ncyI6IiIsImlnbm9yZUxpc3QiOltdfQ==