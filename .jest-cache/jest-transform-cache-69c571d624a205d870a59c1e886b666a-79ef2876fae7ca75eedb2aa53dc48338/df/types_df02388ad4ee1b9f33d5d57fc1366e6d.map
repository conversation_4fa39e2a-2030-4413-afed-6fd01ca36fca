{"version": 3, "names": [], "sources": ["/Users/<USER>/projects/react-native/payment-module/src/navigation/types.ts"], "sourcesContent": ["import {PaymentOrderRequest} from '../data/models/payment-order/PaymentOrderRequest';\nimport {GetBillDetailModel} from '../domain/entities/get-bill-detail/GetBillDetailModel';\nimport {ProviderModel} from '../domain/entities/provider-list/ProviderListModel';\n\nexport interface PaymentInfoModel {\n  title?: string;\n  categoryName?: string;\n  billInfo?: GetBillDetailModel;\n  paymentValidate?: PaymentOrderRequest;\n  originatorAccount?: {\n    identification?: string;\n    name?: string;\n    accountNo?: string;\n    bankName?: string;\n    bankCode?: string;\n  };\n  paymentResultType?: string;\n  contractName?: string;\n  provider?: ProviderModel | null;\n  additionalInfo?: string;\n  qrPaymentInfo?: QRPaymentInfoModel;\n}\n\nexport interface QRPaymentInfoModel {\n  accountNo?: string;\n  bankCode?: string;\n  remark?: string;\n  service?: string;\n  qrType?: string;\n  amount?: number;\n  serviceCode?: string;\n  storeId?: string;\n  merchantName?: string;\n  payType?: string;\n  qrFormat?: string;\n  quantity?: number;\n  expiredTime?: string;\n  qrContent?: string;\n}\n"], "mappings": "", "ignoreList": []}