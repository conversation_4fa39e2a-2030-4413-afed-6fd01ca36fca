a9717ed08be679bc3e198e26c6a45e06
"use strict";

/* istanbul ignore next */
function cov_1ev0ychahr() {
  var path = "/Users/<USER>/projects/react-native/payment-module/src/PaymentModuleMain.tsx";
  var hash = "66b7adfb1bf613e0d5d812ffdde8420b2961b15f";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/projects/react-native/payment-module/src/PaymentModuleMain.tsx",
    statementMap: {
      "0": {
        start: {
          line: 3,
          column: 22
        },
        end: {
          line: 7,
          column: 1
        }
      },
      "1": {
        start: {
          line: 4,
          column: 2
        },
        end: {
          line: 6,
          column: 4
        }
      },
      "2": {
        start: {
          line: 8,
          column: 0
        },
        end: {
          line: 10,
          column: 3
        }
      },
      "3": {
        start: {
          line: 11,
          column: 22
        },
        end: {
          line: 11,
          column: 81
        }
      },
      "4": {
        start: {
          line: 12,
          column: 31
        },
        end: {
          line: 12,
          column: 64
        }
      },
      "5": {
        start: {
          line: 13,
          column: 14
        },
        end: {
          line: 13,
          column: 47
        }
      },
      "6": {
        start: {
          line: 14,
          column: 16
        },
        end: {
          line: 14,
          column: 44
        }
      },
      "7": {
        start: {
          line: 15,
          column: 25
        },
        end: {
          line: 15,
          column: 82
        }
      },
      "8": {
        start: {
          line: 16,
          column: 24
        },
        end: {
          line: 23,
          column: 1
        }
      },
      "9": {
        start: {
          line: 17,
          column: 13
        },
        end: {
          line: 17,
          column: 61
        }
      },
      "10": {
        start: {
          line: 18,
          column: 13
        },
        end: {
          line: 18,
          column: 24
        }
      },
      "11": {
        start: {
          line: 19,
          column: 2
        },
        end: {
          line: 22,
          column: 70
        }
      },
      "12": {
        start: {
          line: 24,
          column: 0
        },
        end: {
          line: 24,
          column: 36
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 3,
            column: 54
          },
          end: {
            line: 3,
            column: 55
          }
        },
        loc: {
          start: {
            line: 3,
            column: 69
          },
          end: {
            line: 7,
            column: 1
          }
        },
        line: 3
      },
      "1": {
        name: "PaymentModuleMain",
        decl: {
          start: {
            line: 16,
            column: 33
          },
          end: {
            line: 16,
            column: 50
          }
        },
        loc: {
          start: {
            line: 16,
            column: 53
          },
          end: {
            line: 23,
            column: 1
          }
        },
        line: 16
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 3,
            column: 22
          },
          end: {
            line: 7,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 3,
            column: 22
          },
          end: {
            line: 3,
            column: 26
          }
        }, {
          start: {
            line: 3,
            column: 30
          },
          end: {
            line: 3,
            column: 50
          }
        }, {
          start: {
            line: 3,
            column: 54
          },
          end: {
            line: 7,
            column: 1
          }
        }],
        line: 3
      },
      "1": {
        loc: {
          start: {
            line: 4,
            column: 9
          },
          end: {
            line: 6,
            column: 3
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 4,
            column: 33
          },
          end: {
            line: 4,
            column: 36
          }
        }, {
          start: {
            line: 4,
            column: 39
          },
          end: {
            line: 6,
            column: 3
          }
        }],
        line: 4
      },
      "2": {
        loc: {
          start: {
            line: 4,
            column: 9
          },
          end: {
            line: 4,
            column: 30
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 4,
            column: 9
          },
          end: {
            line: 4,
            column: 12
          }
        }, {
          start: {
            line: 4,
            column: 16
          },
          end: {
            line: 4,
            column: 30
          }
        }],
        line: 4
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0
    },
    f: {
      "0": 0,
      "1": 0
    },
    b: {
      "0": [0, 0, 0],
      "1": [0, 0],
      "2": [0, 0]
    },
    inputSourceMap: {
      version: 3,
      file: undefined,
      names: ["LocaleContext_1", "require", "msb_host_shared_module_1", "react_1", "__importDefault", "i18n_ts_1", "PaymentStack_tsx_1", "PaymentModuleMain", "_ref", "useHostInjection", "locale", "default", "createElement", "LocaleProvider", "translations", "defaultLocale", "exports"],
      sourceRoot: undefined,
      sources: ["/Users/<USER>/projects/react-native/payment-module/src/PaymentModuleMain.tsx"],
      sourcesContent: ["import {LocaleProvider} from 'msb-communication-lib/dist/locales/LocaleContext';\nimport {useHostInjection} from 'msb-host-shared-module';\nimport React from 'react';\n\nimport {translations} from './locales/i18n.ts';\nimport PaymentStack from './navigation/PaymentStack.tsx';\n\nconst PaymentModuleMain = () => {\n  const {locale} = useHostInjection();\n\n  return (\n    <LocaleProvider translations={translations} defaultLocale={locale}>\n      <PaymentStack />\n    </LocaleProvider>\n  );\n};\n\nexport default PaymentModuleMain;\n"],
      mappings: ";;;;;;;;;;AAAA,IAAAA,eAAA,GAAAC,OAAA;AACA,IAAAC,wBAAA,GAAAD,OAAA;AACA,IAAAE,OAAA,GAAAC,eAAA,CAAAH,OAAA;AAEA,IAAAI,SAAA,GAAAJ,OAAA;AACA,IAAAK,kBAAA,GAAAF,eAAA,CAAAH,OAAA;AAEA,IAAMM,iBAAiB,GAAG,SAApBA,iBAAiBA,CAAA,EAAQ;EAC7B,IAAAC,IAAA,GAAiB,IAAAN,wBAAA,CAAAO,gBAAgB,GAAE;IAA5BC,MAAM,GAAAF,IAAA,CAANE,MAAM;EAEb,OACEP,OAAA,CAAAQ,OAAA,CAAAC,aAAA,CAACZ,eAAA,CAAAa,cAAc;IAACC,YAAY,EAAET,SAAA,CAAAS,YAAY;IAAEC,aAAa,EAAEL;EAAM,GAC/DP,OAAA,CAAAQ,OAAA,CAAAC,aAAA,CAACN,kBAAA,CAAAK,OAAY,OAAG,CACD;AAErB,CAAC;AAEDK,OAAA,CAAAL,OAAA,GAAeJ,iBAAiB",
      ignoreList: []
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "66b7adfb1bf613e0d5d812ffdde8420b2961b15f"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_1ev0ychahr = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_1ev0ychahr();
var __importDefault =
/* istanbul ignore next */
(cov_1ev0ychahr().s[0]++,
/* istanbul ignore next */
(cov_1ev0ychahr().b[0][0]++, this) &&
/* istanbul ignore next */
(cov_1ev0ychahr().b[0][1]++, this.__importDefault) ||
/* istanbul ignore next */
(cov_1ev0ychahr().b[0][2]++, function (mod) {
  /* istanbul ignore next */
  cov_1ev0ychahr().f[0]++;
  cov_1ev0ychahr().s[1]++;
  return /* istanbul ignore next */(cov_1ev0ychahr().b[2][0]++, mod) &&
  /* istanbul ignore next */
  (cov_1ev0ychahr().b[2][1]++, mod.__esModule) ?
  /* istanbul ignore next */
  (cov_1ev0ychahr().b[1][0]++, mod) :
  /* istanbul ignore next */
  (cov_1ev0ychahr().b[1][1]++, {
    "default": mod
  });
}));
/* istanbul ignore next */
cov_1ev0ychahr().s[2]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
var LocaleContext_1 =
/* istanbul ignore next */
(cov_1ev0ychahr().s[3]++, require("msb-communication-lib/dist/locales/LocaleContext"));
var msb_host_shared_module_1 =
/* istanbul ignore next */
(cov_1ev0ychahr().s[4]++, require("msb-host-shared-module"));
var react_1 =
/* istanbul ignore next */
(cov_1ev0ychahr().s[5]++, __importDefault(require("react")));
var i18n_ts_1 =
/* istanbul ignore next */
(cov_1ev0ychahr().s[6]++, require("./locales/i18n.ts"));
var PaymentStack_tsx_1 =
/* istanbul ignore next */
(cov_1ev0ychahr().s[7]++, __importDefault(require("./navigation/PaymentStack.tsx")));
/* istanbul ignore next */
cov_1ev0ychahr().s[8]++;
var PaymentModuleMain = function PaymentModuleMain() {
  /* istanbul ignore next */
  cov_1ev0ychahr().f[1]++;
  var _ref =
    /* istanbul ignore next */
    (cov_1ev0ychahr().s[9]++, (0, msb_host_shared_module_1.useHostInjection)()),
    locale =
    /* istanbul ignore next */
    (cov_1ev0ychahr().s[10]++, _ref.locale);
  /* istanbul ignore next */
  cov_1ev0ychahr().s[11]++;
  return react_1.default.createElement(LocaleContext_1.LocaleProvider, {
    translations: i18n_ts_1.translations,
    defaultLocale: locale
  }, react_1.default.createElement(PaymentStack_tsx_1.default, null));
};
/* istanbul ignore next */
cov_1ev0ychahr().s[12]++;
exports.default = PaymentModuleMain;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJjb3ZfMWV2MHljaGFociIsImFjdHVhbENvdmVyYWdlIiwiTG9jYWxlQ29udGV4dF8xIiwicyIsInJlcXVpcmUiLCJtc2JfaG9zdF9zaGFyZWRfbW9kdWxlXzEiLCJyZWFjdF8xIiwiX19pbXBvcnREZWZhdWx0IiwiaTE4bl90c18xIiwiUGF5bWVudFN0YWNrX3RzeF8xIiwiUGF5bWVudE1vZHVsZU1haW4iLCJmIiwiX3JlZiIsInVzZUhvc3RJbmplY3Rpb24iLCJsb2NhbGUiLCJkZWZhdWx0IiwiY3JlYXRlRWxlbWVudCIsIkxvY2FsZVByb3ZpZGVyIiwidHJhbnNsYXRpb25zIiwiZGVmYXVsdExvY2FsZSIsImV4cG9ydHMiXSwic291cmNlcyI6WyIvVXNlcnMvc29uc2Ftc2V0L3Byb2plY3RzL3JlYWN0LW5hdGl2ZS9wYXltZW50LW1vZHVsZS9zcmMvUGF5bWVudE1vZHVsZU1haW4udHN4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7TG9jYWxlUHJvdmlkZXJ9IGZyb20gJ21zYi1jb21tdW5pY2F0aW9uLWxpYi9kaXN0L2xvY2FsZXMvTG9jYWxlQ29udGV4dCc7XG5pbXBvcnQge3VzZUhvc3RJbmplY3Rpb259IGZyb20gJ21zYi1ob3N0LXNoYXJlZC1tb2R1bGUnO1xuaW1wb3J0IFJlYWN0IGZyb20gJ3JlYWN0JztcblxuaW1wb3J0IHt0cmFuc2xhdGlvbnN9IGZyb20gJy4vbG9jYWxlcy9pMThuLnRzJztcbmltcG9ydCBQYXltZW50U3RhY2sgZnJvbSAnLi9uYXZpZ2F0aW9uL1BheW1lbnRTdGFjay50c3gnO1xuXG5jb25zdCBQYXltZW50TW9kdWxlTWFpbiA9ICgpID0+IHtcbiAgY29uc3Qge2xvY2FsZX0gPSB1c2VIb3N0SW5qZWN0aW9uKCk7XG5cbiAgcmV0dXJuIChcbiAgICA8TG9jYWxlUHJvdmlkZXIgdHJhbnNsYXRpb25zPXt0cmFuc2xhdGlvbnN9IGRlZmF1bHRMb2NhbGU9e2xvY2FsZX0+XG4gICAgICA8UGF5bWVudFN0YWNrIC8+XG4gICAgPC9Mb2NhbGVQcm92aWRlcj5cbiAgKTtcbn07XG5cbmV4cG9ydCBkZWZhdWx0IFBheW1lbnRNb2R1bGVNYWluO1xuIl0sIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7SUFPTTtJQUFBQSxjQUFBLFlBQUFBLENBQUE7TUFBQSxPQUFBQyxjQUFBO0lBQUE7RUFBQTtFQUFBLE9BQUFBLGNBQUE7QUFBQTtBQUFBRCxjQUFBOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBUE4sSUFBQUUsZUFBQTtBQUFBO0FBQUEsQ0FBQUYsY0FBQSxHQUFBRyxDQUFBLE9BQUFDLE9BQUE7QUFDQSxJQUFBQyx3QkFBQTtBQUFBO0FBQUEsQ0FBQUwsY0FBQSxHQUFBRyxDQUFBLE9BQUFDLE9BQUE7QUFDQSxJQUFBRSxPQUFBO0FBQUE7QUFBQSxDQUFBTixjQUFBLEdBQUFHLENBQUEsT0FBQUksZUFBQSxDQUFBSCxPQUFBO0FBRUEsSUFBQUksU0FBQTtBQUFBO0FBQUEsQ0FBQVIsY0FBQSxHQUFBRyxDQUFBLE9BQUFDLE9BQUE7QUFDQSxJQUFBSyxrQkFBQTtBQUFBO0FBQUEsQ0FBQVQsY0FBQSxHQUFBRyxDQUFBLE9BQUFJLGVBQUEsQ0FBQUgsT0FBQTtBQUFBO0FBQUFKLGNBQUEsR0FBQUcsQ0FBQTtBQUVBLElBQU1PLGlCQUFpQixHQUFHLFNBQXBCQSxpQkFBaUJBLENBQUEsRUFBUTtFQUFBO0VBQUFWLGNBQUEsR0FBQVcsQ0FBQTtFQUM3QixJQUFBQyxJQUFBO0lBQUE7SUFBQSxDQUFBWixjQUFBLEdBQUFHLENBQUEsT0FBaUIsSUFBQUUsd0JBQUEsQ0FBQVEsZ0JBQWdCLEdBQUU7SUFBNUJDLE1BQU07SUFBQTtJQUFBLENBQUFkLGNBQUEsR0FBQUcsQ0FBQSxRQUFBUyxJQUFBLENBQU5FLE1BQU07RUFBQTtFQUFBZCxjQUFBLEdBQUFHLENBQUE7RUFFYixPQUNFRyxPQUFBLENBQUFTLE9BQUEsQ0FBQUMsYUFBQSxDQUFDZCxlQUFBLENBQUFlLGNBQWM7SUFBQ0MsWUFBWSxFQUFFVixTQUFBLENBQUFVLFlBQVk7SUFBRUMsYUFBYSxFQUFFTDtFQUFNLEdBQy9EUixPQUFBLENBQUFTLE9BQUEsQ0FBQUMsYUFBQSxDQUFDUCxrQkFBQSxDQUFBTSxPQUFZLE9BQUcsQ0FDRDtBQUVyQixDQUFDO0FBQUE7QUFBQWYsY0FBQSxHQUFBRyxDQUFBO0FBRURpQixPQUFBLENBQUFMLE9BQUEsR0FBZUwsaUJBQWlCIiwiaWdub3JlTGlzdCI6W119