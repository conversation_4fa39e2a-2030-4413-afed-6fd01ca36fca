{"version": 3, "names": ["TextInputState", "require", "dismissKeyboard", "blurTextInput", "currentlyFocusedInput", "module", "exports"], "sources": ["dismissKeyboard.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @flow strict-local\n * @format\n */\n\n// This function dismisses the currently-open keyboard, if any.\n\n'use strict';\n\nconst TextInputState = require('../Components/TextInput/TextInputState');\n\nfunction dismissKeyboard() {\n  TextInputState.blurTextInput(TextInputState.currentlyFocusedInput());\n}\n\nmodule.exports = dismissKeyboard;\n"], "mappings": "AAYA,YAAY;;AAEZ,IAAMA,cAAc,GAAGC,OAAO,CAAC,wCAAwC,CAAC;AAExE,SAASC,eAAeA,CAAA,EAAG;EACzBF,cAAc,CAACG,aAAa,CAACH,cAAc,CAACI,qBAAqB,CAAC,CAAC,CAAC;AACtE;AAEAC,MAAM,CAACC,OAAO,GAAGJ,eAAe", "ignoreList": []}