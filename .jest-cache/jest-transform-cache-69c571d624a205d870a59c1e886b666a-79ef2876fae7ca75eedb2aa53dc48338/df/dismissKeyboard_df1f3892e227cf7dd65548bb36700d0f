2c2ebcdf292021183d5931a1cb332125
'use strict';

var TextInputState = require('../Components/TextInput/TextInputState');
function dismissKeyboard() {
  TextInputState.blurTextInput(TextInputState.currentlyFocusedInput());
}
module.exports = dismissKeyboard;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJUZXh0SW5wdXRTdGF0ZSIsInJlcXVpcmUiLCJkaXNtaXNzS2V5Ym9hcmQiLCJibHVyVGV4dElucHV0IiwiY3VycmVudGx5Rm9jdXNlZElucHV0IiwibW9kdWxlIiwiZXhwb3J0cyJdLCJzb3VyY2VzIjpbImRpc21pc3NLZXlib2FyZC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIENvcHlyaWdodCAoYykgTWV0YSBQbGF0Zm9ybXMsIEluYy4gYW5kIGFmZmlsaWF0ZXMuXG4gKlxuICogVGhpcyBzb3VyY2UgY29kZSBpcyBsaWNlbnNlZCB1bmRlciB0aGUgTUlUIGxpY2Vuc2UgZm91bmQgaW4gdGhlXG4gKiBMSUNFTlNFIGZpbGUgaW4gdGhlIHJvb3QgZGlyZWN0b3J5IG9mIHRoaXMgc291cmNlIHRyZWUuXG4gKlxuICogQGZsb3cgc3RyaWN0LWxvY2FsXG4gKiBAZm9ybWF0XG4gKi9cblxuLy8gVGhpcyBmdW5jdGlvbiBkaXNtaXNzZXMgdGhlIGN1cnJlbnRseS1vcGVuIGtleWJvYXJkLCBpZiBhbnkuXG5cbid1c2Ugc3RyaWN0JztcblxuY29uc3QgVGV4dElucHV0U3RhdGUgPSByZXF1aXJlKCcuLi9Db21wb25lbnRzL1RleHRJbnB1dC9UZXh0SW5wdXRTdGF0ZScpO1xuXG5mdW5jdGlvbiBkaXNtaXNzS2V5Ym9hcmQoKSB7XG4gIFRleHRJbnB1dFN0YXRlLmJsdXJUZXh0SW5wdXQoVGV4dElucHV0U3RhdGUuY3VycmVudGx5Rm9jdXNlZElucHV0KCkpO1xufVxuXG5tb2R1bGUuZXhwb3J0cyA9IGRpc21pc3NLZXlib2FyZDtcbiJdLCJtYXBwaW5ncyI6IkFBWUEsWUFBWTs7QUFFWixJQUFNQSxjQUFjLEdBQUdDLE9BQU8sQ0FBQyx3Q0FBd0MsQ0FBQztBQUV4RSxTQUFTQyxlQUFlQSxDQUFBLEVBQUc7RUFDekJGLGNBQWMsQ0FBQ0csYUFBYSxDQUFDSCxjQUFjLENBQUNJLHFCQUFxQixDQUFDLENBQUMsQ0FBQztBQUN0RTtBQUVBQyxNQUFNLENBQUNDLE9BQU8sR0FBR0osZUFBZSIsImlnbm9yZUxpc3QiOltdfQ==