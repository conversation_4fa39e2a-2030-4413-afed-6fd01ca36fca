{"version": 3, "names": ["Object", "defineProperty", "exports", "value", "RNScreensTurboModule", "_index", "require", "noopFactory", "defaultReturnValue", "logger", "warn", "global", "startTransition", "topScreenId", "belowTopScreenId", "canStartTransition", "updateTransition", "finishTransition"], "sources": ["../../../src/screenTransition/RNScreensTurboModule.ts"], "sourcesContent": [null], "mappings": "AAAA,YAAY;;AAAAA,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,KAAA;AAAA;AAAAD,OAAA,CAAAE,oBAAA;AAGZ,IAAAC,MAAA,GAAAC,OAAA;AAEA,SAASC,WAAWA,CAAIC,kBAAsB,EAAW;EACvD,OAAO,YAAM;IACX,SAAS;;IACTC,aAAM,CAACC,IAAI,CACT,qJACF,CAAC;IACD,OAAOF,kBAAkB;EAC3B,CAAC;AACH;AAQO,IAAMJ,oBAA8C,GAAAF,OAAA,CAAAE,oBAAA,GACzDO,MAAM,CAACP,oBAAoB,IAAI;EAC7BQ,eAAe,EAAEL,WAAW,CAAoB;IAC9CM,WAAW,EAAE,CAAC,CAAC;IACfC,gBAAgB,EAAE,CAAC,CAAC;IACpBC,kBAAkB,EAAE;EACtB,CAAC,CAAC;EACFC,gBAAgB,EAAET,WAAW,CAAC,CAAC;EAC/BU,gBAAgB,EAAEV,WAAW,CAAC;AAChC,CAAC", "ignoreList": []}