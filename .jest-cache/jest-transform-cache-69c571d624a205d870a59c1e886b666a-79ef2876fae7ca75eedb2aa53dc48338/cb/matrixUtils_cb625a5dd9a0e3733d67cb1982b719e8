9daef73f31b6821298bd61b76595294b
'use strict';

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.addMatrices = addMatrices;
exports.decomposeMatrix = decomposeMatrix;
exports.decomposeMatrixIntoMatricesAndAngles = decomposeMatrixIntoMatricesAndAngles;
exports.flatten = flatten;
exports.getRotationMatrix = getRotationMatrix;
exports.isAffineMatrix = isAffineMatrix;
exports.isAffineMatrixFlat = isAffineMatrixFlat;
exports.multiplyMatrices = multiplyMatrices;
exports.scaleMatrix = scaleMatrix;
exports.subtractMatrices = subtractMatrices;
exports.unflatten = unflatten;
var _slicedToArray2 = _interopRequireDefault(require("@babel/runtime/helpers/slicedToArray"));
var _errors = require("../../errors.js");
function isAffineMatrixFlat(x) {
  'worklet';

  return Array.isArray(x) && x.length === 16 && x.every(function (element) {
    return typeof element === 'number' && !isNaN(element);
  });
}
function isAffineMatrix(x) {
  'worklet';

  return Array.isArray(x) && x.length === 4 && x.every(function (row) {
    return Array.isArray(row) && row.length === 4 && row.every(function (element) {
      return typeof element === 'number' && !isNaN(element);
    });
  });
}
function flatten(matrix) {
  'worklet';

  return matrix.flat();
}
function unflatten(m) {
  'worklet';

  return [[m[0], m[1], m[2], m[3]], [m[4], m[5], m[6], m[7]], [m[8], m[9], m[10], m[11]], [m[12], m[13], m[14], m[15]]];
}
function maybeFlattenMatrix(matrix) {
  'worklet';

  return isAffineMatrix(matrix) ? flatten(matrix) : matrix;
}
function multiplyMatrices(a, b) {
  'worklet';

  return [[a[0][0] * b[0][0] + a[0][1] * b[1][0] + a[0][2] * b[2][0] + a[0][3] * b[3][0], a[0][0] * b[0][1] + a[0][1] * b[1][1] + a[0][2] * b[2][1] + a[0][3] * b[3][1], a[0][0] * b[0][2] + a[0][1] * b[1][2] + a[0][2] * b[2][2] + a[0][3] * b[3][2], a[0][0] * b[0][3] + a[0][1] * b[1][3] + a[0][2] * b[2][3] + a[0][3] * b[3][3]], [a[1][0] * b[0][0] + a[1][1] * b[1][0] + a[1][2] * b[2][0] + a[1][3] * b[3][0], a[1][0] * b[0][1] + a[1][1] * b[1][1] + a[1][2] * b[2][1] + a[1][3] * b[3][1], a[1][0] * b[0][2] + a[1][1] * b[1][2] + a[1][2] * b[2][2] + a[1][3] * b[3][2], a[1][0] * b[0][3] + a[1][1] * b[1][3] + a[1][2] * b[2][3] + a[1][3] * b[3][3]], [a[2][0] * b[0][0] + a[2][1] * b[1][0] + a[2][2] * b[2][0] + a[2][3] * b[3][0], a[2][0] * b[0][1] + a[2][1] * b[1][1] + a[2][2] * b[2][1] + a[2][3] * b[3][1], a[2][0] * b[0][2] + a[2][1] * b[1][2] + a[2][2] * b[2][2] + a[2][3] * b[3][2], a[2][0] * b[0][3] + a[2][1] * b[1][3] + a[2][2] * b[2][3] + a[2][3] * b[3][3]], [a[3][0] * b[0][0] + a[3][1] * b[1][0] + a[3][2] * b[2][0] + a[3][3] * b[3][0], a[3][0] * b[0][1] + a[3][1] * b[1][1] + a[3][2] * b[2][1] + a[3][3] * b[3][1], a[3][0] * b[0][2] + a[3][1] * b[1][2] + a[3][2] * b[2][2] + a[3][3] * b[3][2], a[3][0] * b[0][3] + a[3][1] * b[1][3] + a[3][2] * b[2][3] + a[3][3] * b[3][3]]];
}
function subtractMatrices(maybeFlatA, maybeFlatB) {
  'worklet';

  var isFlatOnStart = isAffineMatrixFlat(maybeFlatA);
  var a = maybeFlattenMatrix(maybeFlatA);
  var b = maybeFlattenMatrix(maybeFlatB);
  var c = a.map(function (_, i) {
    return a[i] - b[i];
  });
  return isFlatOnStart ? c : unflatten(c);
}
function addMatrices(maybeFlatA, maybeFlatB) {
  'worklet';

  var isFlatOnStart = isAffineMatrixFlat(maybeFlatA);
  var a = maybeFlattenMatrix(maybeFlatA);
  var b = maybeFlattenMatrix(maybeFlatB);
  var c = a.map(function (_, i) {
    return a[i] + b[i];
  });
  return isFlatOnStart ? c : unflatten(c);
}
function scaleMatrix(maybeFlatA, scalar) {
  'worklet';

  var isFlatOnStart = isAffineMatrixFlat(maybeFlatA);
  var a = maybeFlattenMatrix(maybeFlatA);
  var b = a.map(function (x) {
    return x * scalar;
  });
  return isFlatOnStart ? b : unflatten(b);
}
function getRotationMatrix(angle) {
  'worklet';

  var axis = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'z';
  var cos = Math.cos(angle);
  var sin = Math.sin(angle);
  switch (axis) {
    case 'z':
      return [[cos, sin, 0, 0], [-sin, cos, 0, 0], [0, 0, 1, 0], [0, 0, 0, 1]];
    case 'y':
      return [[cos, 0, -sin, 0], [0, 1, 0, 0], [sin, 0, cos, 0], [0, 0, 0, 1]];
    case 'x':
      return [[1, 0, 0, 0], [0, cos, sin, 0], [0, -sin, cos, 0], [0, 0, 0, 1]];
  }
}
function norm3d(x, y, z) {
  'worklet';

  return Math.sqrt(x * x + y * y + z * z);
}
function transposeMatrix(matrix) {
  'worklet';

  var m = flatten(matrix);
  return [[m[0], m[4], m[8], m[12]], [m[1], m[5], m[9], m[13]], [m[2], m[6], m[10], m[14]], [m[3], m[7], m[11], m[15]]];
}
function assertVectorsHaveEqualLengths(a, b) {
  'worklet';

  if (__DEV__ && a.length !== b.length) {
    throw new _errors.ReanimatedError(`Cannot calculate inner product of two vectors of different lengths. Length of ${a.toString()} is ${a.length} and length of ${b.toString()} is ${b.length}.`);
  }
}
function innerProduct(a, b) {
  'worklet';

  assertVectorsHaveEqualLengths(a, b);
  return a.reduce(function (acc, _, i) {
    return acc + a[i] * b[i];
  }, 0);
}
function projection(u, a) {
  'worklet';

  assertVectorsHaveEqualLengths(u, a);
  var s = innerProduct(u, a) / innerProduct(u, u);
  return u.map(function (e) {
    return e * s;
  });
}
function subtractVectors(a, b) {
  'worklet';

  assertVectorsHaveEqualLengths(a, b);
  return a.map(function (_, i) {
    return a[i] - b[i];
  });
}
function scaleVector(u, a) {
  'worklet';

  return u.map(function (e) {
    return e * a;
  });
}
function gramSchmidtAlgorithm(matrix) {
  'worklet';

  var _matrix = (0, _slicedToArray2.default)(matrix, 4),
    a0 = _matrix[0],
    a1 = _matrix[1],
    a2 = _matrix[2],
    a3 = _matrix[3];
  var u0 = a0;
  var u1 = subtractVectors(a1, projection(u0, a1));
  var u2 = subtractVectors(subtractVectors(a2, projection(u0, a2)), projection(u1, a2));
  var u3 = subtractVectors(subtractVectors(subtractVectors(a3, projection(u0, a3)), projection(u1, a3)), projection(u2, a3));
  var _map = [u0, u1, u2, u3].map(function (u) {
      return scaleVector(u, 1 / Math.sqrt(innerProduct(u, u)));
    }),
    _map2 = (0, _slicedToArray2.default)(_map, 4),
    e0 = _map2[0],
    e1 = _map2[1],
    e2 = _map2[2],
    e3 = _map2[3];
  var rotationMatrix = [[e0[0], e1[0], e2[0], e3[0]], [e0[1], e1[1], e2[1], e3[1]], [e0[2], e1[2], e2[2], e3[2]], [e0[3], e1[3], e2[3], e3[3]]];
  var skewMatrix = [[innerProduct(e0, a0), innerProduct(e0, a1), innerProduct(e0, a2), innerProduct(e0, a3)], [0, innerProduct(e1, a1), innerProduct(e1, a2), innerProduct(e1, a3)], [0, 0, innerProduct(e2, a2), innerProduct(e2, a3)], [0, 0, 0, innerProduct(e3, a3)]];
  return {
    rotationMatrix: transposeMatrix(rotationMatrix),
    skewMatrix: transposeMatrix(skewMatrix)
  };
}
function decomposeMatrix(unknownTypeMatrix) {
  'worklet';

  var matrix = maybeFlattenMatrix(unknownTypeMatrix);
  if (matrix[15] === 0) {
    throw new _errors.ReanimatedError('Invalid transform matrix.');
  }
  matrix.forEach(function (_, i) {
    return matrix[i] /= matrix[15];
  });
  var translationMatrix = [[1, 0, 0, 0], [0, 1, 0, 0], [0, 0, 1, 0], [matrix[12], matrix[13], matrix[14], 1]];
  var sx = matrix[15] * norm3d(matrix[0], matrix[4], matrix[8]);
  var sy = matrix[15] * norm3d(matrix[1], matrix[5], matrix[9]);
  var sz = matrix[15] * norm3d(matrix[2], matrix[6], matrix[10]);
  var scaleMatrix = [[sx, 0, 0, 0], [0, sy, 0, 0], [0, 0, sz, 0], [0, 0, 0, 1]];
  var rotationAndSkewMatrix = [[matrix[0] / sx, matrix[1] / sx, matrix[2] / sx, 0], [matrix[4] / sy, matrix[5] / sy, matrix[6] / sy, 0], [matrix[8] / sz, matrix[9] / sz, matrix[10] / sz, 0], [0, 0, 0, 1]];
  var _gramSchmidtAlgorithm = gramSchmidtAlgorithm(rotationAndSkewMatrix),
    rotationMatrix = _gramSchmidtAlgorithm.rotationMatrix,
    skewMatrix = _gramSchmidtAlgorithm.skewMatrix;
  return {
    translationMatrix: translationMatrix,
    scaleMatrix: scaleMatrix,
    rotationMatrix: rotationMatrix,
    skewMatrix: skewMatrix
  };
}
function decomposeMatrixIntoMatricesAndAngles(matrix) {
  'worklet';
  var _decomposeMatrix = decomposeMatrix(matrix),
    scaleMatrix = _decomposeMatrix.scaleMatrix,
    rotationMatrix = _decomposeMatrix.rotationMatrix,
    translationMatrix = _decomposeMatrix.translationMatrix,
    skewMatrix = _decomposeMatrix.skewMatrix;
  var sinRy = -rotationMatrix[0][2];
  var ry = Math.asin(sinRy);
  var rx;
  var rz;
  if (sinRy === 1 || sinRy === -1) {
    rz = 0;
    rx = Math.atan2(sinRy * rotationMatrix[0][1], sinRy * rotationMatrix[0][2]);
  } else {
    rz = Math.atan2(rotationMatrix[0][1], rotationMatrix[0][0]);
    rx = Math.atan2(rotationMatrix[1][2], rotationMatrix[2][2]);
  }
  return {
    scaleMatrix: scaleMatrix,
    rotationMatrix: rotationMatrix,
    translationMatrix: translationMatrix,
    skewMatrix: skewMatrix,
    rx: rx || 0,
    ry: ry || 0,
    rz: rz || 0
  };
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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