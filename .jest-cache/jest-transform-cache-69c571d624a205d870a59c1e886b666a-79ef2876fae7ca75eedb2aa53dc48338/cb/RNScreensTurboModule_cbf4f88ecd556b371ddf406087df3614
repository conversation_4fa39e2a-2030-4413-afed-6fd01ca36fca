3cc9d178c1e76322bdf64ab2488989a5
'use strict';

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.RNScreensTurboModule = void 0;
var _index = require("../logger/index.js");
function noopFactory(defaultReturnValue) {
  return function () {
    'worklet';

    _index.logger.warn('RNScreensTurboModule has not been found. Check that you have installed `react-native-screens@3.30.0` or newer in your project and rebuilt your app.');
    return defaultReturnValue;
  };
}
var RNScreensTurboModule = exports.RNScreensTurboModule = global.RNScreensTurboModule || {
  startTransition: noopFactory({
    topScreenId: -1,
    belowTopScreenId: -1,
    canStartTransition: false
  }),
  updateTransition: noopFactory(),
  finishTransition: noopFactory()
};
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJPYmplY3QiLCJkZWZpbmVQcm9wZXJ0eSIsImV4cG9ydHMiLCJ2YWx1ZSIsIlJOU2NyZWVuc1R1cmJvTW9kdWxlIiwiX2luZGV4IiwicmVxdWlyZSIsIm5vb3BGYWN0b3J5IiwiZGVmYXVsdFJldHVyblZhbHVlIiwibG9nZ2VyIiwid2FybiIsImdsb2JhbCIsInN0YXJ0VHJhbnNpdGlvbiIsInRvcFNjcmVlbklkIiwiYmVsb3dUb3BTY3JlZW5JZCIsImNhblN0YXJ0VHJhbnNpdGlvbiIsInVwZGF0ZVRyYW5zaXRpb24iLCJmaW5pc2hUcmFuc2l0aW9uIl0sInNvdXJjZXMiOlsiLi4vLi4vLi4vc3JjL3NjcmVlblRyYW5zaXRpb24vUk5TY3JlZW5zVHVyYm9Nb2R1bGUudHMiXSwic291cmNlc0NvbnRlbnQiOltudWxsXSwibWFwcGluZ3MiOiJBQUFBLFlBQVk7O0FBQUFBLE1BQUEsQ0FBQUMsY0FBQSxDQUFBQyxPQUFBO0VBQUFDLEtBQUE7QUFBQTtBQUFBRCxPQUFBLENBQUFFLG9CQUFBO0FBR1osSUFBQUMsTUFBQSxHQUFBQyxPQUFBO0FBRUEsU0FBU0MsV0FBV0EsQ0FBSUMsa0JBQXNCLEVBQVc7RUFDdkQsT0FBTyxZQUFNO0lBQ1gsU0FBUzs7SUFDVEMsYUFBTSxDQUFDQyxJQUFJLENBQ1QscUpBQ0YsQ0FBQztJQUNELE9BQU9GLGtCQUFrQjtFQUMzQixDQUFDO0FBQ0g7QUFRTyxJQUFNSixvQkFBOEMsR0FBQUYsT0FBQSxDQUFBRSxvQkFBQSxHQUN6RE8sTUFBTSxDQUFDUCxvQkFBb0IsSUFBSTtFQUM3QlEsZUFBZSxFQUFFTCxXQUFXLENBQW9CO0lBQzlDTSxXQUFXLEVBQUUsQ0FBQyxDQUFDO0lBQ2ZDLGdCQUFnQixFQUFFLENBQUMsQ0FBQztJQUNwQkMsa0JBQWtCLEVBQUU7RUFDdEIsQ0FBQyxDQUFDO0VBQ0ZDLGdCQUFnQixFQUFFVCxXQUFXLENBQUMsQ0FBQztFQUMvQlUsZ0JBQWdCLEVBQUVWLFdBQVcsQ0FBQztBQUNoQyxDQUFDIiwiaWdub3JlTGlzdCI6W119