557d3c57e9aef0c94e4a3294c99b19a2
"use strict";

/* istanbul ignore next */
function cov_25rzxqeux1() {
  var path = "/Users/<USER>/projects/react-native/payment-module/src/data/models/get-my-bill-contact-recent-list/GetMyBillContactRecentListResponse.ts";
  var hash = "cfe04f5c2da33a990f1b570beca809f1697ecc16";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/projects/react-native/payment-module/src/data/models/get-my-bill-contact-recent-list/GetMyBillContactRecentListResponse.ts",
    statementMap: {
      "0": {
        start: {
          line: 3,
          column: 0
        },
        end: {
          line: 5,
          column: 3
        }
      }
    },
    fnMap: {},
    branchMap: {},
    s: {
      "0": 0
    },
    f: {},
    b: {},
    inputSourceMap: {
      version: 3,
      file: undefined,
      names: [],
      sourceRoot: undefined,
      sources: ["/Users/<USER>/projects/react-native/payment-module/src/data/models/get-my-bill-contact-recent-list/GetMyBillContactRecentListResponse.ts"],
      sourcesContent: ["export type GetMyBillContactRecentListResponse = GetMyBillContactRecentResponse[];\nexport interface GetMyBillContactRecentResponse {\n  id?: string;\n  billCode?: string;\n  category?: string;\n  subGroupId?: any;\n  customerName?: string;\n  totalAmount?: number;\n  period?: string;\n  paymentDate?: string;\n  accountNumber?: string;\n  coreRef?: string;\n  serviceCode?: string;\n  arrangementId?: string;\n  paymentOrderId?: string;\n  cifNo?: string;\n}\n"],
      mappings: "",
      ignoreList: []
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "cfe04f5c2da33a990f1b570beca809f1697ecc16"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_25rzxqeux1 = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_25rzxqeux1();
cov_25rzxqeux1().s[0]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6W10sInNvdXJjZXMiOlsiL1VzZXJzL3NvbnNhbXNldC9wcm9qZWN0cy9yZWFjdC1uYXRpdmUvcGF5bWVudC1tb2R1bGUvc3JjL2RhdGEvbW9kZWxzL2dldC1teS1iaWxsLWNvbnRhY3QtcmVjZW50LWxpc3QvR2V0TXlCaWxsQ29udGFjdFJlY2VudExpc3RSZXNwb25zZS50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgdHlwZSBHZXRNeUJpbGxDb250YWN0UmVjZW50TGlzdFJlc3BvbnNlID0gR2V0TXlCaWxsQ29udGFjdFJlY2VudFJlc3BvbnNlW107XG5leHBvcnQgaW50ZXJmYWNlIEdldE15QmlsbENvbnRhY3RSZWNlbnRSZXNwb25zZSB7XG4gIGlkPzogc3RyaW5nO1xuICBiaWxsQ29kZT86IHN0cmluZztcbiAgY2F0ZWdvcnk/OiBzdHJpbmc7XG4gIHN1Ykdyb3VwSWQ/OiBhbnk7XG4gIGN1c3RvbWVyTmFtZT86IHN0cmluZztcbiAgdG90YWxBbW91bnQ/OiBudW1iZXI7XG4gIHBlcmlvZD86IHN0cmluZztcbiAgcGF5bWVudERhdGU/OiBzdHJpbmc7XG4gIGFjY291bnROdW1iZXI/OiBzdHJpbmc7XG4gIGNvcmVSZWY/OiBzdHJpbmc7XG4gIHNlcnZpY2VDb2RlPzogc3RyaW5nO1xuICBhcnJhbmdlbWVudElkPzogc3RyaW5nO1xuICBwYXltZW50T3JkZXJJZD86IHN0cmluZztcbiAgY2lmTm8/OiBzdHJpbmc7XG59XG4iXSwibWFwcGluZ3MiOiIiLCJpZ25vcmVMaXN0IjpbXX0=