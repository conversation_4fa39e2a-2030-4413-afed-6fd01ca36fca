{"version": 3, "names": [], "sources": ["/Users/<USER>/projects/react-native/payment-module/src/data/models/payment-order-status/PaymentOrderStatusResponse.ts"], "sourcesContent": ["export interface PaymentOrderStatusResponse {\n  id?: string;\n  status?: string;\n  bankStatus?: string;\n  reasonCode?: string;\n  reasonText?: string;\n  createdBy?: string;\n  createdAt?: string;\n  updatedBy?: string;\n  updatedAt?: string;\n  permissibleActions?: PermissibleActions;\n  version?: number;\n  intraLegalEntity?: boolean;\n  originatorAccountCurrency?: string;\n  confirmationId?: string;\n  originator?: Originator;\n  originatorAccount?: OriginatorAccount;\n  instructionPriority?: string;\n  requestedExecutionDate?: string;\n  paymentMode?: string;\n  paymentType?: string;\n  transferTransactionInformation?: TransferTransactionInformation;\n  totalAmount?: TotalAmount;\n  edited?: boolean;\n  additions?: Additions;\n}\n\nexport interface PermissibleActions {\n  approve?: boolean;\n  finalApprove?: boolean;\n  reject?: boolean;\n  cancel?: boolean;\n  delete?: boolean;\n  edit?: boolean;\n  create?: boolean;\n  export?: boolean;\n}\n\nexport interface Originator {\n  name?: string;\n  role?: string;\n}\n\nexport interface OriginatorAccount {\n  arrangementId?: string;\n  identification?: Identification;\n}\n\nexport interface Identification {\n  identification?: string;\n  schemeName?: string;\n}\n\nexport interface TransferTransactionInformation {\n  counterparty?: Counterparty;\n  counterpartyAccount?: CounterpartyAccount;\n  counterpartyBank?: CounterpartyBank;\n  instructedAmount?: InstructedAmount;\n  remittanceInformation?: RemittanceInformation;\n}\n\nexport interface Counterparty {\n  name?: string;\n  role?: string;\n}\n\nexport interface CounterpartyAccount {\n  identification?: Identification2;\n}\n\nexport interface Identification2 {\n  identification?: string;\n  schemeName?: string;\n}\n\nexport interface CounterpartyBank {\n  bankBranchCode?: string;\n  name?: string;\n}\n\nexport interface InstructedAmount {\n  amount?: string;\n  currencyCode?: string;\n}\n\nexport interface RemittanceInformation {\n  type?: string;\n  content?: string;\n}\n\nexport interface TotalAmount {\n  amount?: string;\n  currencyCode?: string;\n}\n\nexport interface Additions {\n  t24TraceCode?: string;\n  napasTraceCode?: string;\n}\n"], "mappings": "", "ignoreList": []}