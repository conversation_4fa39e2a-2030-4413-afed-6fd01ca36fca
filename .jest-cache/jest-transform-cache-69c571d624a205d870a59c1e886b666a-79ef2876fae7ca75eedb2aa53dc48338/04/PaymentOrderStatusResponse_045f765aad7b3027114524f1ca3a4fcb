f0f31f8d25261dfff3acf638910c15a0
"use strict";

/* istanbul ignore next */
function cov_270afjpoa1() {
  var path = "/Users/<USER>/projects/react-native/payment-module/src/data/models/payment-order-status/PaymentOrderStatusResponse.ts";
  var hash = "938b956261368caef300543cf01c38b97ecb3fde";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/projects/react-native/payment-module/src/data/models/payment-order-status/PaymentOrderStatusResponse.ts",
    statementMap: {
      "0": {
        start: {
          line: 3,
          column: 0
        },
        end: {
          line: 5,
          column: 3
        }
      }
    },
    fnMap: {},
    branchMap: {},
    s: {
      "0": 0
    },
    f: {},
    b: {},
    inputSourceMap: {
      version: 3,
      file: undefined,
      names: [],
      sourceRoot: undefined,
      sources: ["/Users/<USER>/projects/react-native/payment-module/src/data/models/payment-order-status/PaymentOrderStatusResponse.ts"],
      sourcesContent: ["export interface PaymentOrderStatusResponse {\n  id?: string;\n  status?: string;\n  bankStatus?: string;\n  reasonCode?: string;\n  reasonText?: string;\n  createdBy?: string;\n  createdAt?: string;\n  updatedBy?: string;\n  updatedAt?: string;\n  permissibleActions?: PermissibleActions;\n  version?: number;\n  intraLegalEntity?: boolean;\n  originatorAccountCurrency?: string;\n  confirmationId?: string;\n  originator?: Originator;\n  originatorAccount?: OriginatorAccount;\n  instructionPriority?: string;\n  requestedExecutionDate?: string;\n  paymentMode?: string;\n  paymentType?: string;\n  transferTransactionInformation?: TransferTransactionInformation;\n  totalAmount?: TotalAmount;\n  edited?: boolean;\n  additions?: Additions;\n}\n\nexport interface PermissibleActions {\n  approve?: boolean;\n  finalApprove?: boolean;\n  reject?: boolean;\n  cancel?: boolean;\n  delete?: boolean;\n  edit?: boolean;\n  create?: boolean;\n  export?: boolean;\n}\n\nexport interface Originator {\n  name?: string;\n  role?: string;\n}\n\nexport interface OriginatorAccount {\n  arrangementId?: string;\n  identification?: Identification;\n}\n\nexport interface Identification {\n  identification?: string;\n  schemeName?: string;\n}\n\nexport interface TransferTransactionInformation {\n  counterparty?: Counterparty;\n  counterpartyAccount?: CounterpartyAccount;\n  counterpartyBank?: CounterpartyBank;\n  instructedAmount?: InstructedAmount;\n  remittanceInformation?: RemittanceInformation;\n}\n\nexport interface Counterparty {\n  name?: string;\n  role?: string;\n}\n\nexport interface CounterpartyAccount {\n  identification?: Identification2;\n}\n\nexport interface Identification2 {\n  identification?: string;\n  schemeName?: string;\n}\n\nexport interface CounterpartyBank {\n  bankBranchCode?: string;\n  name?: string;\n}\n\nexport interface InstructedAmount {\n  amount?: string;\n  currencyCode?: string;\n}\n\nexport interface RemittanceInformation {\n  type?: string;\n  content?: string;\n}\n\nexport interface TotalAmount {\n  amount?: string;\n  currencyCode?: string;\n}\n\nexport interface Additions {\n  t24TraceCode?: string;\n  napasTraceCode?: string;\n}\n"],
      mappings: "",
      ignoreList: []
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "938b956261368caef300543cf01c38b97ecb3fde"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_270afjpoa1 = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_270afjpoa1();
cov_270afjpoa1().s[0]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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