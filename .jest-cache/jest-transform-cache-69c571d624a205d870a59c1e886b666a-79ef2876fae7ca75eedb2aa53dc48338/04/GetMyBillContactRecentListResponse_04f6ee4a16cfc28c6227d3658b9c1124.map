{"version": 3, "names": [], "sources": ["/Users/<USER>/projects/react-native/payment-module/src/data/models/get-my-bill-contact-recent-list/GetMyBillContactRecentListResponse.ts"], "sourcesContent": ["export type GetMyBillContactRecentListResponse = GetMyBillContactRecentResponse[];\nexport interface GetMyBillContactRecentResponse {\n  id?: string;\n  billCode?: string;\n  category?: string;\n  subGroupId?: any;\n  customerName?: string;\n  totalAmount?: number;\n  period?: string;\n  paymentDate?: string;\n  accountNumber?: string;\n  coreRef?: string;\n  serviceCode?: string;\n  arrangementId?: string;\n  paymentOrderId?: string;\n  cifNo?: string;\n}\n"], "mappings": "", "ignoreList": []}