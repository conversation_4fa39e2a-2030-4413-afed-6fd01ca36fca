{"version": 3, "names": ["Object", "defineProperty", "exports", "value", "updatePropsJestWrapper", "default", "_Colors", "require", "_index", "_PlatformChecker", "_threads", "_errors", "updateProps", "shouldBeUseWeb", "viewDescriptors", "updates", "isAnimatedProps", "_viewDescriptors$valu", "for<PERSON>ach", "viewDescriptor", "component", "tag", "_updatePropsJS", "processColorsInProps", "global", "UpdatePropsManager", "update", "animatedStyle", "adapters", "adapter", "current", "assign", "_default", "createUpdatePropsManager", "isF<PERSON><PERSON>", "operations", "_this", "push", "shadowNodeWrapper", "length", "queueMicrotask", "flush", "_updatePropsFabric", "_this2", "name", "_updatePropsPaper", "maybeThrowError", "isJest", "ReanimatedError", "Proxy", "get", "set", "runOnUIImmediately"], "sources": ["../../src/UpdateProps.ts"], "sourcesContent": [null], "mappings": "AACA,YAAY;;AAAAA,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,KAAA;AAAA;AAAAD,OAAA,CAAAE,sBAAA,GAAAF,OAAA,CAAAG,OAAA;AAEZ,IAAAC,OAAA,GAAAC,OAAA;AAQA,IAAAC,MAAA,GAAAD,OAAA;AACA,IAAAE,gBAAA,GAAAF,OAAA;AACA,IAAAG,QAAA,GAAAH,OAAA;AACA,IAAAI,OAAA,GAAAJ,OAAA;AAEA,IAAIK,WAIK;AAET,IAAI,IAAAC,+BAAc,EAAC,CAAC,EAAE;EACpBD,WAAW,GAAG,SAAdA,WAAWA,CAAIE,eAAe,EAAEC,OAAO,EAAEC,eAAe,EAAK;IAC3D,SAAS;;IAAA,IAAAC,qBAAA;IACT,CAAAA,qBAAA,GAAAH,eAAe,CAACX,KAAK,aAArBc,qBAAA,CAAuBC,OAAO,CAAE,UAAAC,cAAc,EAAK;MACjD,IAAMC,SAAS,GAAGD,cAAc,CAACE,GAA4B;MAC7D,IAAAC,qBAAc,EAACP,OAAO,EAAEK,SAAS,EAAEJ,eAAe,CAAC;IACrD,CAAC,CAAC;EACJ,CAAC;AACH,CAAC,MAAM;EACLJ,WAAW,GAAG,SAAdA,WAAWA,CAAIE,eAAe,EAAEC,OAAO,EAAK;IAC1C,SAAS;;IACT,IAAAQ,4BAAoB,EAACR,OAAO,CAAC;IAC7BS,MAAM,CAACC,kBAAkB,CAACC,MAAM,CAACZ,eAAe,EAAEC,OAAO,CAAC;EAC5D,CAAC;AACH;AAEO,IAAMX,sBAAsB,GAAAF,OAAA,CAAAE,sBAAA,GAAG,SAAzBA,sBAAsBA,CACjCU,eAAuC,EACvCC,OAA2B,EAC3BY,aAAmD,EACnDC,QAAmD,EAC1C;EACTA,QAAQ,CAACV,OAAO,CAAE,UAAAW,OAAO,EAAK;IAC5BA,OAAO,CAACd,OAAO,CAAC;EAClB,CAAC,CAAC;EACFY,aAAa,CAACG,OAAO,CAAC3B,KAAK,GAAAH,MAAA,CAAA+B,MAAA,KACtBJ,aAAa,CAACG,OAAO,CAAC3B,KAAK,EAC3BY,OAAA,CACJ;EAEDH,WAAW,CAACE,eAAe,EAAEC,OAAO,CAAC;AACvC,CAAC;AAAA,IAAAiB,QAAA,GAAA9B,OAAA,CAAAG,OAAA,GAEcO,WAAW;AAE1B,IAAMqB,wBAAwB,GAAG,IAAAC,yBAAQ,EAAC,CAAC,GACvC,YAAM;EACJ,SAAS;EAET,IAAMC,UAGH,GAAG,EAAE;EACR,OAAO;IACLT,MAAM,WAANA,MAAMA,CACJZ,eAAuC,EACvCC,OAAwC,EACxC;MAAA,IAAAqB,KAAA;MACAtB,eAAe,CAACX,KAAK,CAACe,OAAO,CAAE,UAAAC,cAAc,EAAK;QAChDgB,UAAU,CAACE,IAAI,CAAC;UACdC,iBAAiB,EAAEnB,cAAc,CAACmB,iBAAiB;UACnDvB,OAAA,EAAAA;QACF,CAAC,CAAC;QACF,IAAIoB,UAAU,CAACI,MAAM,KAAK,CAAC,EAAE;UAC3BC,cAAc,CAACJ,KAAI,CAACK,KAAK,CAAC;QAC5B;MACF,CAAC,CAAC;IACJ,CAAC;IACDA,KAAK,WAALA,KAAKA,CAAA,EAAa;MAChBjB,MAAM,CAACkB,kBAAkB,CAAEP,UAAU,CAAC;MACtCA,UAAU,CAACI,MAAM,GAAG,CAAC;IACvB;EACF,CAAC;AACH,CAAC,GACD,YAAM;EACJ,SAAS;EAET,IAAMJ,UAIH,GAAG,EAAE;EACR,OAAO;IACLT,MAAM,WAANA,MAAMA,CACJZ,eAAuC,EACvCC,OAAwC,EACxC;MAAA,IAAA4B,MAAA;MACA7B,eAAe,CAACX,KAAK,CAACe,OAAO,CAAE,UAAAC,cAAc,EAAK;QAChDgB,UAAU,CAACE,IAAI,CAAC;UACdhB,GAAG,EAAEF,cAAc,CAACE,GAAa;UACjCuB,IAAI,EAAEzB,cAAc,CAACyB,IAAI,IAAI,SAAS;UACtC7B,OAAA,EAAAA;QACF,CAAC,CAAC;QACF,IAAIoB,UAAU,CAACI,MAAM,KAAK,CAAC,EAAE;UAC3BC,cAAc,CAACG,MAAI,CAACF,KAAK,CAAC;QAC5B;MACF,CAAC,CAAC;IACJ,CAAC;IACDA,KAAK,WAALA,KAAKA,CAAA,EAAa;MAChBjB,MAAM,CAACqB,iBAAiB,CAAEV,UAAU,CAAC;MACrCA,UAAU,CAACI,MAAM,GAAG,CAAC;IACvB;EACF,CAAC;AACH,CAAC;AAEL,IAAI,IAAA1B,+BAAc,EAAC,CAAC,EAAE;EACpB,IAAMiC,eAAe,GAAG,SAAlBA,eAAeA,CAAA,EAAS;IAG5B,IAAI,CAAC,IAAAC,uBAAM,EAAC,CAAC,EAAE;MACb,MAAM,IAAIC,uBAAe,CACvB,+DACF,CAAC;IACH;EACF,CAAC;EACDxB,MAAM,CAACC,kBAAkB,GAAG,IAAIwB,KAAK,CAAC,CAAC,CAAC,EAAwB;IAC9DC,GAAG,EAAEJ,eAAe;IACpBK,GAAG,EAAE,SAALA,GAAGA,CAAA,EAAQ;MACTL,eAAe,CAAC,CAAC;MACjB,OAAO,KAAK;IACd;EACF,CAAC,CAAC;AACJ,CAAC,MAAM;EACL,IAAAM,2BAAkB,EAAC,YAAM;IACvB,SAAS;;IACT5B,MAAM,CAACC,kBAAkB,GAAGQ,wBAAwB,CAAC,CAAC;EACxD,CAAC,CAAC,CAAC,CAAC;AACN", "ignoreList": []}