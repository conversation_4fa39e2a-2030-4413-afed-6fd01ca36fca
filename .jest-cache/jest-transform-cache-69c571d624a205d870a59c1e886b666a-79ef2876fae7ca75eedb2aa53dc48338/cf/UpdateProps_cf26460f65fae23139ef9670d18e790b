4d4fcbfddbd18e0a44993eeb4acc06e2
'use strict';

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.updatePropsJestWrapper = exports.default = void 0;
var _Colors = require("./Colors.js");
var _index = require("./js-reanimated/index.js");
var _PlatformChecker = require("./PlatformChecker.js");
var _threads = require("./threads.js");
var _errors = require("./errors.js");
var updateProps;
if ((0, _PlatformChecker.shouldBeUseWeb)()) {
  updateProps = function updateProps(viewDescriptors, updates, isAnimatedProps) {
    'worklet';

    var _viewDescriptors$valu;
    (_viewDescriptors$valu = viewDescriptors.value) == null || _viewDescriptors$valu.forEach(function (viewDescriptor) {
      var component = viewDescriptor.tag;
      (0, _index._updatePropsJS)(updates, component, isAnimatedProps);
    });
  };
} else {
  updateProps = function updateProps(viewDescriptors, updates) {
    'worklet';

    (0, _Colors.processColorsInProps)(updates);
    global.UpdatePropsManager.update(viewDescriptors, updates);
  };
}
var updatePropsJestWrapper = exports.updatePropsJestWrapper = function updatePropsJestWrapper(viewDescriptors, updates, animatedStyle, adapters) {
  adapters.forEach(function (adapter) {
    adapter(updates);
  });
  animatedStyle.current.value = Object.assign({}, animatedStyle.current.value, updates);
  updateProps(viewDescriptors, updates);
};
var _default = exports.default = updateProps;
var createUpdatePropsManager = (0, _PlatformChecker.isFabric)() ? function () {
  'worklet';
  var operations = [];
  return {
    update: function update(viewDescriptors, updates) {
      var _this = this;
      viewDescriptors.value.forEach(function (viewDescriptor) {
        operations.push({
          shadowNodeWrapper: viewDescriptor.shadowNodeWrapper,
          updates: updates
        });
        if (operations.length === 1) {
          queueMicrotask(_this.flush);
        }
      });
    },
    flush: function flush() {
      global._updatePropsFabric(operations);
      operations.length = 0;
    }
  };
} : function () {
  'worklet';
  var operations = [];
  return {
    update: function update(viewDescriptors, updates) {
      var _this2 = this;
      viewDescriptors.value.forEach(function (viewDescriptor) {
        operations.push({
          tag: viewDescriptor.tag,
          name: viewDescriptor.name || 'RCTView',
          updates: updates
        });
        if (operations.length === 1) {
          queueMicrotask(_this2.flush);
        }
      });
    },
    flush: function flush() {
      global._updatePropsPaper(operations);
      operations.length = 0;
    }
  };
};
if ((0, _PlatformChecker.shouldBeUseWeb)()) {
  var maybeThrowError = function maybeThrowError() {
    if (!(0, _PlatformChecker.isJest)()) {
      throw new _errors.ReanimatedError('`UpdatePropsManager` is not available on non-native platform.');
    }
  };
  global.UpdatePropsManager = new Proxy({}, {
    get: maybeThrowError,
    set: function set() {
      maybeThrowError();
      return false;
    }
  });
} else {
  (0, _threads.runOnUIImmediately)(function () {
    'worklet';

    global.UpdatePropsManager = createUpdatePropsManager();
  })();
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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