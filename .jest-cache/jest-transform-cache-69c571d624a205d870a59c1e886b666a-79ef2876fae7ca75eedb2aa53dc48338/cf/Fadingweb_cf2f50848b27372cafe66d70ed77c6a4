5a8a5a3185364a5586f8508914b59bab
'use strict';

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.FadingTransition = FadingTransition;
function FadingTransition(name, transitionData) {
  var translateX = transitionData.translateX,
    translateY = transitionData.translateY,
    scaleX = transitionData.scaleX,
    scaleY = transitionData.scaleY;
  var fadingTransition = {
    name: name,
    style: {
      0: {
        opacity: 1,
        transform: [{
          translateX: `${translateX}px`,
          translateY: `${translateY}px`,
          scale: `${scaleX},${scaleY}`
        }]
      },
      20: {
        opacity: 0,
        transform: [{
          translateX: `${translateX}px`,
          translateY: `${translateY}px`,
          scale: `${scaleX},${scaleY}`
        }]
      },
      60: {
        opacity: 0,
        transform: [{
          translateX: '0px',
          translateY: '0px',
          scale: `1,1`
        }]
      },
      100: {
        opacity: 1,
        transform: [{
          translateX: '0px',
          translateY: '0px',
          scale: `1,1`
        }]
      }
    },
    duration: 300
  };
  return fadingTransition;
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJPYmplY3QiLCJkZWZpbmVQcm9wZXJ0eSIsImV4cG9ydHMiLCJ2YWx1ZSIsIkZhZGluZ1RyYW5zaXRpb24iLCJuYW1lIiwidHJhbnNpdGlvbkRhdGEiLCJ0cmFuc2xhdGVYIiwidHJhbnNsYXRlWSIsInNjYWxlWCIsInNjYWxlWSIsImZhZGluZ1RyYW5zaXRpb24iLCJzdHlsZSIsIm9wYWNpdHkiLCJ0cmFuc2Zvcm0iLCJzY2FsZSIsImR1cmF0aW9uIl0sInNvdXJjZXMiOlsiLi4vLi4vLi4vLi4vLi4vc3JjL2xheW91dFJlYW5pbWF0aW9uL3dlYi90cmFuc2l0aW9uL0ZhZGluZy53ZWIudHMiXSwic291cmNlc0NvbnRlbnQiOltudWxsXSwibWFwcGluZ3MiOiJBQUFBLFlBQVk7O0FBQUFBLE1BQUEsQ0FBQUMsY0FBQSxDQUFBQyxPQUFBO0VBQUFDLEtBQUE7QUFBQTtBQUFBRCxPQUFBLENBQUFFLGdCQUFBLEdBQUFBLGdCQUFBO0FBR0wsU0FBU0EsZ0JBQWdCQSxDQUFDQyxJQUFZLEVBQUVDLGNBQThCLEVBQUU7RUFDN0UsSUFBUUMsVUFBVSxHQUFpQ0QsY0FBYyxDQUF6REMsVUFBVTtJQUFFQyxVQUFVLEdBQXFCRixjQUFjLENBQTdDRSxVQUFVO0lBQUVDLE1BQU0sR0FBYUgsY0FBYyxDQUFqQ0csTUFBTTtJQUFFQyxNQUFBLEdBQVdKLGNBQWMsQ0FBekJJLE1BQUE7RUFFeEMsSUFBTUMsZ0JBQWdCLEdBQUc7SUFDdkJOLElBQUksRUFBSkEsSUFBSTtJQUNKTyxLQUFLLEVBQUU7TUFDTCxDQUFDLEVBQUU7UUFDREMsT0FBTyxFQUFFLENBQUM7UUFDVkMsU0FBUyxFQUFFLENBQ1Q7VUFDRVAsVUFBVSxFQUFFLEdBQUdBLFVBQVUsSUFBSTtVQUM3QkMsVUFBVSxFQUFFLEdBQUdBLFVBQVUsSUFBSTtVQUM3Qk8sS0FBSyxFQUFFLEdBQUdOLE1BQU0sSUFBSUMsTUFBTTtRQUM1QixDQUFDO01BRUwsQ0FBQztNQUNELEVBQUUsRUFBRTtRQUNGRyxPQUFPLEVBQUUsQ0FBQztRQUNWQyxTQUFTLEVBQUUsQ0FDVDtVQUNFUCxVQUFVLEVBQUUsR0FBR0EsVUFBVSxJQUFJO1VBQzdCQyxVQUFVLEVBQUUsR0FBR0EsVUFBVSxJQUFJO1VBQzdCTyxLQUFLLEVBQUUsR0FBR04sTUFBTSxJQUFJQyxNQUFNO1FBQzVCLENBQUM7TUFFTCxDQUFDO01BQ0QsRUFBRSxFQUFFO1FBQ0ZHLE9BQU8sRUFBRSxDQUFDO1FBQ1ZDLFNBQVMsRUFBRSxDQUNUO1VBQ0VQLFVBQVUsRUFBRSxLQUFLO1VBQ2pCQyxVQUFVLEVBQUUsS0FBSztVQUNqQk8sS0FBSyxFQUFFO1FBQ1QsQ0FBQztNQUVMLENBQUM7TUFDRCxHQUFHLEVBQUU7UUFDSEYsT0FBTyxFQUFFLENBQUM7UUFDVkMsU0FBUyxFQUFFLENBQ1Q7VUFDRVAsVUFBVSxFQUFFLEtBQUs7VUFDakJDLFVBQVUsRUFBRSxLQUFLO1VBQ2pCTyxLQUFLLEVBQUU7UUFDVCxDQUFDO01BRUw7SUFDRixDQUFDO0lBQ0RDLFFBQVEsRUFBRTtFQUNaLENBQUM7RUFFRCxPQUFPTCxnQkFBZ0I7QUFDekIiLCJpZ25vcmVMaXN0IjpbXX0=