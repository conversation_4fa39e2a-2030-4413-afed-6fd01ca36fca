308f66926599f32c547fba008365423c
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _require = require('../ReactNative/RendererProxy'),
  dispatchCommand = _require.dispatchCommand;
function codegenNativeCommands(options) {
  var commandObj = {};
  options.supportedCommands.forEach(function (command) {
    commandObj[command] = function (ref) {
      for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {
        args[_key - 1] = arguments[_key];
      }
      dispatchCommand(ref, command, args);
    };
  });
  return commandObj;
}
var _default = exports.default = codegenNativeCommands;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJfcmVxdWlyZSIsInJlcXVpcmUiLCJkaXNwYXRjaENvbW1hbmQiLCJjb2RlZ2VuTmF0aXZlQ29tbWFuZHMiLCJvcHRpb25zIiwiY29tbWFuZE9iaiIsInN1cHBvcnRlZENvbW1hbmRzIiwiZm9yRWFjaCIsImNvbW1hbmQiLCJyZWYiLCJfbGVuIiwiYXJndW1lbnRzIiwibGVuZ3RoIiwiYXJncyIsIkFycmF5IiwiX2tleSIsIl9kZWZhdWx0IiwiZXhwb3J0cyIsImRlZmF1bHQiXSwic291cmNlcyI6WyJjb2RlZ2VuTmF0aXZlQ29tbWFuZHMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBDb3B5cmlnaHQgKGMpIE1ldGEgUGxhdGZvcm1zLCBJbmMuIGFuZCBhZmZpbGlhdGVzLlxuICpcbiAqIFRoaXMgc291cmNlIGNvZGUgaXMgbGljZW5zZWQgdW5kZXIgdGhlIE1JVCBsaWNlbnNlIGZvdW5kIGluIHRoZVxuICogTElDRU5TRSBmaWxlIGluIHRoZSByb290IGRpcmVjdG9yeSBvZiB0aGlzIHNvdXJjZSB0cmVlLlxuICpcbiAqIEBmb3JtYXRcbiAqIEBmbG93XG4gKi9cblxuY29uc3Qge2Rpc3BhdGNoQ29tbWFuZH0gPSByZXF1aXJlKCcuLi9SZWFjdE5hdGl2ZS9SZW5kZXJlclByb3h5Jyk7XG5cbnR5cGUgT3B0aW9uczxUID0gc3RyaW5nPiA9ICRSZWFkT25seTx7fFxuICBzdXBwb3J0ZWRDb21tYW5kczogJFJlYWRPbmx5QXJyYXk8VD4sXG58fT47XG5cbmZ1bmN0aW9uIGNvZGVnZW5OYXRpdmVDb21tYW5kczxUOiBpbnRlcmZhY2Uge30+KG9wdGlvbnM6IE9wdGlvbnM8JEtleXM8VD4+KTogVCB7XG4gIGNvbnN0IGNvbW1hbmRPYmo6IHtbJEtleXM8VD5dOiAoLi4uJFJlYWRPbmx5QXJyYXk8bWl4ZWQ+KSA9PiB2b2lkfSA9IHt9O1xuXG4gIG9wdGlvbnMuc3VwcG9ydGVkQ29tbWFuZHMuZm9yRWFjaChjb21tYW5kID0+IHtcbiAgICAvLyAkRmxvd0ZpeE1lW21pc3NpbmctbG9jYWwtYW5ub3RdXG4gICAgY29tbWFuZE9ialtjb21tYW5kXSA9IChyZWYsIC4uLmFyZ3MpID0+IHtcbiAgICAgIC8vICRGbG93Rml4TWVbaW5jb21wYXRpYmxlLWNhbGxdXG4gICAgICBkaXNwYXRjaENvbW1hbmQocmVmLCBjb21tYW5kLCBhcmdzKTtcbiAgICB9O1xuICB9KTtcblxuICByZXR1cm4gKChjb21tYW5kT2JqOiBhbnkpOiBUKTtcbn1cblxuZXhwb3J0IGRlZmF1bHQgY29kZWdlbk5hdGl2ZUNvbW1hbmRzO1xuIl0sIm1hcHBpbmdzIjoiOzs7O0FBVUEsSUFBQUEsUUFBQSxHQUEwQkMsT0FBTyxDQUFDLDhCQUE4QixDQUFDO0VBQTFEQyxlQUFlLEdBQUFGLFFBQUEsQ0FBZkUsZUFBZTtBQU10QixTQUFTQyxxQkFBcUJBLENBQWtCQyxPQUEwQixFQUFLO0VBQzdFLElBQU1DLFVBQTRELEdBQUcsQ0FBQyxDQUFDO0VBRXZFRCxPQUFPLENBQUNFLGlCQUFpQixDQUFDQyxPQUFPLENBQUMsVUFBQUMsT0FBTyxFQUFJO0lBRTNDSCxVQUFVLENBQUNHLE9BQU8sQ0FBQyxHQUFHLFVBQUNDLEdBQUcsRUFBYztNQUFBLFNBQUFDLElBQUEsR0FBQUMsU0FBQSxDQUFBQyxNQUFBLEVBQVRDLElBQUksT0FBQUMsS0FBQSxDQUFBSixJQUFBLE9BQUFBLElBQUEsV0FBQUssSUFBQSxNQUFBQSxJQUFBLEdBQUFMLElBQUEsRUFBQUssSUFBQTtRQUFKRixJQUFJLENBQUFFLElBQUEsUUFBQUosU0FBQSxDQUFBSSxJQUFBO01BQUE7TUFFakNiLGVBQWUsQ0FBQ08sR0FBRyxFQUFFRCxPQUFPLEVBQUVLLElBQUksQ0FBQztJQUNyQyxDQUFDO0VBQ0gsQ0FBQyxDQUFDO0VBRUYsT0FBU1IsVUFBVTtBQUNyQjtBQUFDLElBQUFXLFFBQUEsR0FBQUMsT0FBQSxDQUFBQyxPQUFBLEdBRWNmLHFCQUFxQiIsImlnbm9yZUxpc3QiOltdfQ==