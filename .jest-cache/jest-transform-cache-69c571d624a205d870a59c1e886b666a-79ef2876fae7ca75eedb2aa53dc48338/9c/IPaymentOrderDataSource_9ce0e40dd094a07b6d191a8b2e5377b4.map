{"version": 3, "names": [], "sources": ["/Users/<USER>/projects/react-native/payment-module/src/data/datasources/IPaymentOrderDataSource.ts"], "sourcesContent": ["import {PaymentOrderStatusResponse} from '../models/payment-order-status/PaymentOrderStatusResponse';\nimport {PaymentOrderResponse} from '../models/payment-order/PaymentOrderResponse';\nimport {BaseResponse} from '../../core/BaseResponse';\nimport {PaymentOrderRequest} from '../models/payment-order/PaymentOrderRequest';\nimport {PaymentOrderStatusRequest} from '../models/payment-order-status/PaymentOrderStatusRequest';\n\nexport interface IPaymentOrderDataSource {\n  paymentOrder(request: PaymentOrderRequest): Promise<BaseResponse<PaymentOrderResponse>>;\n  paymentOrderStatus(request: PaymentOrderStatusRequest): Promise<BaseResponse<PaymentOrderStatusResponse>>;\n}\n"], "mappings": "", "ignoreList": []}