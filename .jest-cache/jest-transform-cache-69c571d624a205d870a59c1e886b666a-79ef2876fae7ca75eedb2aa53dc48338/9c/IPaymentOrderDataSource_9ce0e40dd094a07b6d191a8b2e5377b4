aa70d74c1daaf672397986cf6f05a449
"use strict";

/* istanbul ignore next */
function cov_heh2uk0qc() {
  var path = "/Users/<USER>/projects/react-native/payment-module/src/data/datasources/IPaymentOrderDataSource.ts";
  var hash = "0ab41eaab56022322c0faf60f36537d526242108";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/projects/react-native/payment-module/src/data/datasources/IPaymentOrderDataSource.ts",
    statementMap: {
      "0": {
        start: {
          line: 3,
          column: 0
        },
        end: {
          line: 5,
          column: 3
        }
      }
    },
    fnMap: {},
    branchMap: {},
    s: {
      "0": 0
    },
    f: {},
    b: {},
    inputSourceMap: {
      version: 3,
      file: undefined,
      names: [],
      sourceRoot: undefined,
      sources: ["/Users/<USER>/projects/react-native/payment-module/src/data/datasources/IPaymentOrderDataSource.ts"],
      sourcesContent: ["import {PaymentOrderStatusResponse} from '../models/payment-order-status/PaymentOrderStatusResponse';\nimport {PaymentOrderResponse} from '../models/payment-order/PaymentOrderResponse';\nimport {BaseResponse} from '../../core/BaseResponse';\nimport {PaymentOrderRequest} from '../models/payment-order/PaymentOrderRequest';\nimport {PaymentOrderStatusRequest} from '../models/payment-order-status/PaymentOrderStatusRequest';\n\nexport interface IPaymentOrderDataSource {\n  paymentOrder(request: PaymentOrderRequest): Promise<BaseResponse<PaymentOrderResponse>>;\n  paymentOrderStatus(request: PaymentOrderStatusRequest): Promise<BaseResponse<PaymentOrderStatusResponse>>;\n}\n"],
      mappings: "",
      ignoreList: []
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "0ab41eaab56022322c0faf60f36537d526242108"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_heh2uk0qc = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_heh2uk0qc();
cov_heh2uk0qc().s[0]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6W10sInNvdXJjZXMiOlsiL1VzZXJzL3NvbnNhbXNldC9wcm9qZWN0cy9yZWFjdC1uYXRpdmUvcGF5bWVudC1tb2R1bGUvc3JjL2RhdGEvZGF0YXNvdXJjZXMvSVBheW1lbnRPcmRlckRhdGFTb3VyY2UudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHtQYXltZW50T3JkZXJTdGF0dXNSZXNwb25zZX0gZnJvbSAnLi4vbW9kZWxzL3BheW1lbnQtb3JkZXItc3RhdHVzL1BheW1lbnRPcmRlclN0YXR1c1Jlc3BvbnNlJztcbmltcG9ydCB7UGF5bWVudE9yZGVyUmVzcG9uc2V9IGZyb20gJy4uL21vZGVscy9wYXltZW50LW9yZGVyL1BheW1lbnRPcmRlclJlc3BvbnNlJztcbmltcG9ydCB7QmFzZVJlc3BvbnNlfSBmcm9tICcuLi8uLi9jb3JlL0Jhc2VSZXNwb25zZSc7XG5pbXBvcnQge1BheW1lbnRPcmRlclJlcXVlc3R9IGZyb20gJy4uL21vZGVscy9wYXltZW50LW9yZGVyL1BheW1lbnRPcmRlclJlcXVlc3QnO1xuaW1wb3J0IHtQYXltZW50T3JkZXJTdGF0dXNSZXF1ZXN0fSBmcm9tICcuLi9tb2RlbHMvcGF5bWVudC1vcmRlci1zdGF0dXMvUGF5bWVudE9yZGVyU3RhdHVzUmVxdWVzdCc7XG5cbmV4cG9ydCBpbnRlcmZhY2UgSVBheW1lbnRPcmRlckRhdGFTb3VyY2Uge1xuICBwYXltZW50T3JkZXIocmVxdWVzdDogUGF5bWVudE9yZGVyUmVxdWVzdCk6IFByb21pc2U8QmFzZVJlc3BvbnNlPFBheW1lbnRPcmRlclJlc3BvbnNlPj47XG4gIHBheW1lbnRPcmRlclN0YXR1cyhyZXF1ZXN0OiBQYXltZW50T3JkZXJTdGF0dXNSZXF1ZXN0KTogUHJvbWlzZTxCYXNlUmVzcG9uc2U8UGF5bWVudE9yZGVyU3RhdHVzUmVzcG9uc2U+Pjtcbn1cbiJdLCJtYXBwaW5ncyI6IiIsImlnbm9yZUxpc3QiOltdfQ==