{"version": 3, "names": ["_interopRequireDefault", "require", "Object", "defineProperty", "exports", "value", "default", "_classCallCheck2", "_createClass2", "_core", "_FrameCallbackRegistryUI", "FrameCallbackRegistryJS", "nextCallbackId", "prepareUIRegistry", "key", "registerFrameCallback", "callback", "callbackId", "runOnUI", "global", "_frameCallbackRegistry", "unregisterFrameCallback", "manageStateFrameCallback", "state"], "sources": ["../../../src/frameCallback/FrameCallbackRegistryJS.ts"], "sourcesContent": [null], "mappings": "AAAA,YAAY;;AAAA,IAAAA,sBAAA,GAAAC,OAAA;AAAAC,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,KAAA;AAAA;AAAAD,OAAA,CAAAE,OAAA;AAAA,IAAAC,gBAAA,GAAAP,sBAAA,CAAAC,OAAA;AAAA,IAAAO,aAAA,GAAAR,sBAAA,CAAAC,OAAA;AACZ,IAAAQ,KAAA,GAAAR,OAAA;AAEA,IAAAS,wBAAA,GAAAT,OAAA;AAA6D,IAExCU,uBAAuB,GAAAP,OAAA,CAAAE,OAAA;EAG1C,SAAAK,wBAAA,EAAc;IAAA,IAAAJ,gBAAA,CAAAD,OAAA,QAAAK,uBAAA;IAAA,KAFNC,cAAc,GAAG,CAAC;IAGxB,IAAAC,0CAAiB,EAAC,CAAC;EACrB;EAAA,WAAAL,aAAA,CAAAF,OAAA,EAAAK,uBAAA;IAAAG,GAAA;IAAAT,KAAA,EAEA,SAAAU,qBAAqBA,CAACC,QAAwC,EAAU;MACtE,IAAI,CAACA,QAAQ,EAAE;QACb,OAAO,CAAC,CAAC;MACX;MAEA,IAAMC,UAAU,GAAG,IAAI,CAACL,cAAc;MACtC,IAAI,CAACA,cAAc,EAAE;MAErB,IAAAM,aAAO,EAAC,YAAM;QACZC,MAAM,CAACC,sBAAsB,CAACL,qBAAqB,CAACC,QAAQ,EAAEC,UAAU,CAAC;MAC3E,CAAC,CAAC,CAAC,CAAC;MAEJ,OAAOA,UAAU;IACnB;EAAA;IAAAH,GAAA;IAAAT,KAAA,EAEA,SAAAgB,uBAAuBA,CAACJ,UAAkB,EAAQ;MAChD,IAAAC,aAAO,EAAC,YAAM;QACZC,MAAM,CAACC,sBAAsB,CAACC,uBAAuB,CAACJ,UAAU,CAAC;MACnE,CAAC,CAAC,CAAC,CAAC;IACN;EAAA;IAAAH,GAAA;IAAAT,KAAA,EAEA,SAAAiB,wBAAwBA,CAACL,UAAkB,EAAEM,KAAc,EAAQ;MACjE,IAAAL,aAAO,EAAC,YAAM;QACZC,MAAM,CAACC,sBAAsB,CAACE,wBAAwB,CAACL,UAAU,EAAEM,KAAK,CAAC;MAC3E,CAAC,CAAC,CAAC,CAAC;IACN;EAAA;AAAA", "ignoreList": []}