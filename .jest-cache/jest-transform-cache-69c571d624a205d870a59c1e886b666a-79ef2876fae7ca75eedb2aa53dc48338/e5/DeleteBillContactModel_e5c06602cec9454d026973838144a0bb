cd7e5be3cf4ed3141a0516c3ec8401d0
"use strict";

/* istanbul ignore next */
function cov_19xf2xaks5() {
  var path = "/Users/<USER>/projects/react-native/payment-module/src/domain/entities/delete-bill-contact/DeleteBillContactModel.ts";
  var hash = "8efa5b8248db8c4cc2fc40ca1a69d20adea07936";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/projects/react-native/payment-module/src/domain/entities/delete-bill-contact/DeleteBillContactModel.ts",
    statementMap: {
      "0": {
        start: {
          line: 3,
          column: 29
        },
        end: {
          line: 3,
          column: 84
        }
      },
      "1": {
        start: {
          line: 4,
          column: 20
        },
        end: {
          line: 4,
          column: 89
        }
      },
      "2": {
        start: {
          line: 5,
          column: 23
        },
        end: {
          line: 5,
          column: 95
        }
      },
      "3": {
        start: {
          line: 6,
          column: 0
        },
        end: {
          line: 8,
          column: 3
        }
      },
      "4": {
        start: {
          line: 9,
          column: 0
        },
        end: {
          line: 9,
          column: 40
        }
      },
      "5": {
        start: {
          line: 10,
          column: 29
        },
        end: {
          line: 12,
          column: 2
        }
      },
      "6": {
        start: {
          line: 11,
          column: 2
        },
        end: {
          line: 11,
          column: 62
        }
      },
      "7": {
        start: {
          line: 13,
          column: 0
        },
        end: {
          line: 13,
          column: 56
        }
      }
    },
    fnMap: {
      "0": {
        name: "DeleteBillContactModel",
        decl: {
          start: {
            line: 10,
            column: 65
          },
          end: {
            line: 10,
            column: 87
          }
        },
        loc: {
          start: {
            line: 10,
            column: 90
          },
          end: {
            line: 12,
            column: 1
          }
        },
        line: 10
      }
    },
    branchMap: {},
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0
    },
    f: {
      "0": 0
    },
    b: {},
    inputSourceMap: {
      version: 3,
      file: undefined,
      names: ["DeleteBillContactModel", "_createClass2", "default", "_classCallCheck2", "exports"],
      sourceRoot: undefined,
      sources: ["/Users/<USER>/projects/react-native/payment-module/src/domain/entities/delete-bill-contact/DeleteBillContactModel.ts"],
      sourcesContent: ["export class DeleteBillContactModel {\n  // TODO: define fields\n}\n"],
      mappings: ";;;;;;;;;IAAaA,sBAAsB,OAAAC,aAAA,CAAAC,OAAA,WAAAF,uBAAA;EAAA,IAAAG,gBAAA,CAAAD,OAAA,QAAAF,sBAAA;AAAA;AAAnCI,OAAA,CAAAJ,sBAAA,GAAAA,sBAAA",
      ignoreList: []
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "8efa5b8248db8c4cc2fc40ca1a69d20adea07936"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_19xf2xaks5 = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_19xf2xaks5();
var _interopRequireDefault =
/* istanbul ignore next */
(cov_19xf2xaks5().s[0]++, require("@babel/runtime/helpers/interopRequireDefault"));
var _createClass2 =
/* istanbul ignore next */
(cov_19xf2xaks5().s[1]++, _interopRequireDefault(require("@babel/runtime/helpers/createClass")));
var _classCallCheck2 =
/* istanbul ignore next */
(cov_19xf2xaks5().s[2]++, _interopRequireDefault(require("@babel/runtime/helpers/classCallCheck")));
/* istanbul ignore next */
cov_19xf2xaks5().s[3]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_19xf2xaks5().s[4]++;
exports.DeleteBillContactModel = void 0;
var DeleteBillContactModel =
/* istanbul ignore next */
(cov_19xf2xaks5().s[5]++, (0, _createClass2.default)(function DeleteBillContactModel() {
  /* istanbul ignore next */
  cov_19xf2xaks5().f[0]++;
  cov_19xf2xaks5().s[6]++;
  (0, _classCallCheck2.default)(this, DeleteBillContactModel);
}));
/* istanbul ignore next */
cov_19xf2xaks5().s[7]++;
exports.DeleteBillContactModel = DeleteBillContactModel;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJEZWxldGVCaWxsQ29udGFjdE1vZGVsIiwiY292XzE5eGYyeGFrczUiLCJzIiwiX2NyZWF0ZUNsYXNzMiIsImRlZmF1bHQiLCJmIiwiX2NsYXNzQ2FsbENoZWNrMiIsImV4cG9ydHMiXSwic291cmNlcyI6WyIvVXNlcnMvc29uc2Ftc2V0L3Byb2plY3RzL3JlYWN0LW5hdGl2ZS9wYXltZW50LW1vZHVsZS9zcmMvZG9tYWluL2VudGl0aWVzL2RlbGV0ZS1iaWxsLWNvbnRhY3QvRGVsZXRlQmlsbENvbnRhY3RNb2RlbC50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgY2xhc3MgRGVsZXRlQmlsbENvbnRhY3RNb2RlbCB7XG4gIC8vIFRPRE86IGRlZmluZSBmaWVsZHNcbn1cbiJdLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0lBQWFBLHNCQUFzQjtBQUFBO0FBQUEsQ0FBQUMsY0FBQSxHQUFBQyxDQUFBLFdBQUFDLGFBQUEsQ0FBQUMsT0FBQSxXQUFBSix1QkFBQTtFQUFBO0VBQUFDLGNBQUEsR0FBQUksQ0FBQTtFQUFBSixjQUFBLEdBQUFDLENBQUE7RUFBQSxJQUFBSSxnQkFBQSxDQUFBRixPQUFBLFFBQUFKLHNCQUFBO0FBQUE7QUFBQTtBQUFBQyxjQUFBLEdBQUFDLENBQUE7QUFBbkNLLE9BQUEsQ0FBQVAsc0JBQUEsR0FBQUEsc0JBQUEiLCJpZ25vcmVMaXN0IjpbXX0=