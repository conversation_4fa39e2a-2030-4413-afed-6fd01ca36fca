{"version": 3, "names": ["Object", "defineProperty", "exports", "value", "enumerable", "get", "_reactNative", "findNodeHandle", "require"], "sources": ["../../../src/platformFunctions/findNodeHandle.ts"], "sourcesContent": [null], "mappings": "AAAA,YAAY;;AAAAA,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,KAAA;AAAA;AAAAH,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAE,UAAA;EAAAC,GAAA,WAAAA,IAAA;IAAA,OAAAC,YAAA,CAAAC,cAAA;EAAA;AAAA;AACZ,IAAAD,YAAA,GAAAE,OAAA", "ignoreList": []}