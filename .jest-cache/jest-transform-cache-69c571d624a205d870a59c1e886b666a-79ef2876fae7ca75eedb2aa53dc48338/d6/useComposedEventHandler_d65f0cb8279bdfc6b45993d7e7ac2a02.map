{"version": 3, "names": ["Object", "defineProperty", "exports", "value", "useComposedEventHandler", "_useEvent", "require", "_useHandler2", "_WorkletEventHandler", "handlers", "workletsRecord", "composedEventNames", "Set", "workletsMap", "filter", "h", "for<PERSON>ach", "handler", "workletEventHandler", "WorkletEventHandler", "eventNames", "eventName", "add", "push", "worklet", "handler<PERSON>ame", "length", "_use<PERSON><PERSON>ler", "useHandler", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "useEvent", "event", "Array", "from"], "sources": ["../../../src/hook/useComposedEventHandler.ts"], "sourcesContent": [null], "mappings": "AAAA,YAAY;;AAAAA,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,KAAA;AAAA;AAAAD,OAAA,CAAAE,uBAAA,GAAAA,uBAAA;AACZ,IAAAC,SAAA,GAAAC,OAAA;AACA,IAAAC,YAAA,GAAAD,OAAA;AACA,IAAAE,oBAAA,GAAAF,OAAA;AAkCO,SAASF,uBAAuBA,CAGrCK,QAA0D,EAAE;EAE5D,IAAMC,cAA+C,GAAG,CAAC,CAAC;EAE1D,IAAMC,kBAAkB,GAAG,IAAIC,GAAG,CAAS,CAAC;EAE5C,IAAMC,WAEL,GAAG,CAAC,CAAC;EAENJ,QAAQ,CACLK,MAAM,CAAE,UAAAC,CAAC;IAAA,OAAKA,CAAC,KAAK,IAAI;EAAA,EAAC,CACzBC,OAAO,CAAE,UAAAC,OAAO,EAAK;IAEpB,IAAQC,mBAAA,GACND,OAAmD,CAD7CC,mBAAA;IAER,IAAIA,mBAAmB,YAAYC,wCAAmB,EAAE;MACtDD,mBAAmB,CAACE,UAAU,CAACJ,OAAO,CAAE,UAAAK,SAAS,EAAK;QACpDV,kBAAkB,CAACW,GAAG,CAACD,SAAS,CAAC;QAEjC,IAAIR,WAAW,CAACQ,SAAS,CAAC,EAAE;UAC1BR,WAAW,CAACQ,SAAS,CAAC,CAACE,IAAI,CAACL,mBAAmB,CAACM,OAAO,CAAC;QAC1D,CAAC,MAAM;UACLX,WAAW,CAACQ,SAAS,CAAC,GAAG,CAACH,mBAAmB,CAACM,OAAO,CAAC;QACxD;QAEA,IAAMC,WAAW,GAAGJ,SAAS,GAAG,GAAGR,WAAW,CAACQ,SAAS,CAAC,CAACK,MAAM,EAAE;QAClEhB,cAAc,CAACe,WAAW,CAAC,GACzBP,mBAAmB,CAACM,OAA0B;MAClD,CAAC,CAAC;IACJ;EACF,CAAC,CAAC;EAEJ,IAAAG,WAAA,GAAiC,IAAAC,uBAAU,EAAClB,cAAc,CAAC;IAAnDmB,oBAAA,GAAAF,WAAA,CAAAE,oBAAA;EAER,OAAO,IAAAC,kBAAQ,EACZ,UAAAC,KAAK,EAAK;IACT,SAAS;;IACT,IAAIlB,WAAW,CAACkB,KAAK,CAACV,SAAS,CAAC,EAAE;MAChCR,WAAW,CAACkB,KAAK,CAACV,SAAS,CAAC,CAACL,OAAO,CAAE,UAAAQ,OAAO;QAAA,OAAKA,OAAO,CAACO,KAAK,CAAC;MAAA,EAAC;IACnE;EACF,CAAC,EACDC,KAAK,CAACC,IAAI,CAACtB,kBAAkB,CAAC,EAC9BkB,oBACF,CAAC;AACH", "ignoreList": []}