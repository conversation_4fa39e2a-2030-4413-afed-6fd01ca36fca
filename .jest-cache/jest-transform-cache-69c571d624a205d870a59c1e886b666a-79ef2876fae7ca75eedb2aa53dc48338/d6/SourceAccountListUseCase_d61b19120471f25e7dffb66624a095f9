cd3226a6294d1bca2f0fe3c9cd271bbf
"use strict";

/* istanbul ignore next */
function cov_1jna3fzssz() {
  var path = "/Users/<USER>/projects/react-native/payment-module/src/domain/usecases/arrangement/SourceAccountListUseCase.ts";
  var hash = "f6380bee8062b619fe5ee12fa149a78640c06f25";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/projects/react-native/payment-module/src/domain/usecases/arrangement/SourceAccountListUseCase.ts",
    statementMap: {
      "0": {
        start: {
          line: 3,
          column: 29
        },
        end: {
          line: 3,
          column: 84
        }
      },
      "1": {
        start: {
          line: 4,
          column: 25
        },
        end: {
          line: 4,
          column: 99
        }
      },
      "2": {
        start: {
          line: 5,
          column: 23
        },
        end: {
          line: 5,
          column: 95
        }
      },
      "3": {
        start: {
          line: 6,
          column: 20
        },
        end: {
          line: 6,
          column: 89
        }
      },
      "4": {
        start: {
          line: 7,
          column: 0
        },
        end: {
          line: 9,
          column: 3
        }
      },
      "5": {
        start: {
          line: 10,
          column: 0
        },
        end: {
          line: 10,
          column: 42
        }
      },
      "6": {
        start: {
          line: 11,
          column: 26
        },
        end: {
          line: 11,
          column: 69
        }
      },
      "7": {
        start: {
          line: 12,
          column: 31
        },
        end: {
          line: 37,
          column: 3
        }
      },
      "8": {
        start: {
          line: 14,
          column: 4
        },
        end: {
          line: 14,
          column: 66
        }
      },
      "9": {
        start: {
          line: 15,
          column: 4
        },
        end: {
          line: 15,
          column: 33
        }
      },
      "10": {
        start: {
          line: 17,
          column: 2
        },
        end: {
          line: 36,
          column: 6
        }
      },
      "11": {
        start: {
          line: 20,
          column: 21
        },
        end: {
          line: 30,
          column: 8
        }
      },
      "12": {
        start: {
          line: 21,
          column: 20
        },
        end: {
          line: 21,
          column: 24
        }
      },
      "13": {
        start: {
          line: 22,
          column: 21
        },
        end: {
          line: 26,
          column: 9
        }
      },
      "14": {
        start: {
          line: 27,
          column: 8
        },
        end: {
          line: 29,
          column: 11
        }
      },
      "15": {
        start: {
          line: 28,
          column: 10
        },
        end: {
          line: 28,
          column: 88
        }
      },
      "16": {
        start: {
          line: 32,
          column: 8
        },
        end: {
          line: 32,
          column: 47
        }
      },
      "17": {
        start: {
          line: 34,
          column: 6
        },
        end: {
          line: 34,
          column: 21
        }
      },
      "18": {
        start: {
          line: 38,
          column: 0
        },
        end: {
          line: 38,
          column: 60
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 12,
            column: 31
          },
          end: {
            line: 12,
            column: 32
          }
        },
        loc: {
          start: {
            line: 12,
            column: 43
          },
          end: {
            line: 37,
            column: 1
          }
        },
        line: 12
      },
      "1": {
        name: "SourceAccountListUseCase",
        decl: {
          start: {
            line: 13,
            column: 11
          },
          end: {
            line: 13,
            column: 35
          }
        },
        loc: {
          start: {
            line: 13,
            column: 48
          },
          end: {
            line: 16,
            column: 3
          }
        },
        line: 13
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 19,
            column: 11
          },
          end: {
            line: 19,
            column: 12
          }
        },
        loc: {
          start: {
            line: 19,
            column: 23
          },
          end: {
            line: 35,
            column: 5
          }
        },
        line: 19
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 20,
            column: 53
          },
          end: {
            line: 20,
            column: 54
          }
        },
        loc: {
          start: {
            line: 20,
            column: 73
          },
          end: {
            line: 30,
            column: 7
          }
        },
        line: 20
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 27,
            column: 60
          },
          end: {
            line: 27,
            column: 61
          }
        },
        loc: {
          start: {
            line: 27,
            column: 72
          },
          end: {
            line: 29,
            column: 9
          }
        },
        line: 27
      },
      "5": {
        name: "execute",
        decl: {
          start: {
            line: 31,
            column: 15
          },
          end: {
            line: 31,
            column: 22
          }
        },
        loc: {
          start: {
            line: 31,
            column: 27
          },
          end: {
            line: 33,
            column: 7
          }
        },
        line: 31
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 28,
            column: 52
          },
          end: {
            line: 28,
            column: 86
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 28,
            column: 70
          },
          end: {
            line: 28,
            column: 77
          }
        }, {
          start: {
            line: 28,
            column: 80
          },
          end: {
            line: 28,
            column: 86
          }
        }],
        line: 28
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0
    },
    b: {
      "0": [0, 0]
    },
    inputSourceMap: {
      version: 3,
      file: undefined,
      names: ["ExcecutionHandler_1", "require", "SourceAccountListUseCase", "repository", "_classCallCheck2", "default", "_createClass2", "key", "value", "_execute", "_asyncToGenerator2", "request", "_this", "params", "externalStateIds", "externalProductKindIds", "currency", "ExecutionHandler", "execute", "sourceAccountList", "_x", "apply", "arguments", "exports"],
      sourceRoot: undefined,
      sources: ["/Users/<USER>/projects/react-native/payment-module/src/domain/usecases/arrangement/SourceAccountListUseCase.ts"],
      sourcesContent: ["import {IArrangementRepository} from '../../repositories/IArrangementRepository';\nimport {SourceAccountListModel} from '../../entities/source-account-list/SourceAccountListModel';\nimport {ResultState} from '../../../core/ResultState';\nimport {ExecutionHandler} from '../../../utils/ExcecutionHandler';\nimport {SourceAccountListRequest} from '../../../data/models/source-account-list/SourceAccountListRequest';\nexport class SourceAccountListUseCase {\n  private repository: IArrangementRepository;\n\n  constructor(repository: IArrangementRepository) {\n    this.repository = repository;\n  }\n\n  public async execute(request?: SourceAccountListRequest): Promise<ResultState<SourceAccountListModel>> {\n    const params: SourceAccountListRequest = {\n      externalStateIds: ['ACTIVE'],\n      externalProductKindIds: ['kind1', 'kind10'],\n      currency: 'VND',\n    };\n    return ExecutionHandler.execute(() => this.repository.sourceAccountList(request ?? params));\n  }\n}\n"],
      mappings: ";;;;;;;;;;AAGA,IAAAA,mBAAA,GAAAC,OAAA;AAAkE,IAErDC,wBAAwB;EAGnC,SAAAA,yBAAYC,UAAkC;IAAA,IAAAC,gBAAA,CAAAC,OAAA,QAAAH,wBAAA;IAC5C,IAAI,CAACC,UAAU,GAAGA,UAAU;EAC9B;EAAC,WAAAG,aAAA,CAAAD,OAAA,EAAAH,wBAAA;IAAAK,GAAA;IAAAC,KAAA;MAAA,IAAAC,QAAA,OAAAC,kBAAA,CAAAL,OAAA,EAEM,WAAcM,OAAkC;QAAA,IAAAC,KAAA;QACrD,IAAMC,MAAM,GAA6B;UACvCC,gBAAgB,EAAE,CAAC,QAAQ,CAAC;UAC5BC,sBAAsB,EAAE,CAAC,OAAO,EAAE,QAAQ,CAAC;UAC3CC,QAAQ,EAAE;SACX;QACD,OAAOhB,mBAAA,CAAAiB,gBAAgB,CAACC,OAAO,CAAC;UAAA,OAAMN,KAAI,CAACT,UAAU,CAACgB,iBAAiB,CAACR,OAAO,WAAPA,OAAO,GAAIE,MAAM,CAAC;QAAA,EAAC;MAC7F,CAAC;MAAA,SAPYK,OAAOA,CAAAE,EAAA;QAAA,OAAAX,QAAA,CAAAY,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAPJ,OAAO;IAAA;EAAA;AAAA;AAPtBK,OAAA,CAAArB,wBAAA,GAAAA,wBAAA",
      ignoreList: []
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "f6380bee8062b619fe5ee12fa149a78640c06f25"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_1jna3fzssz = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_1jna3fzssz();
var _interopRequireDefault =
/* istanbul ignore next */
(cov_1jna3fzssz().s[0]++, require("@babel/runtime/helpers/interopRequireDefault"));
var _asyncToGenerator2 =
/* istanbul ignore next */
(cov_1jna3fzssz().s[1]++, _interopRequireDefault(require("@babel/runtime/helpers/asyncToGenerator")));
var _classCallCheck2 =
/* istanbul ignore next */
(cov_1jna3fzssz().s[2]++, _interopRequireDefault(require("@babel/runtime/helpers/classCallCheck")));
var _createClass2 =
/* istanbul ignore next */
(cov_1jna3fzssz().s[3]++, _interopRequireDefault(require("@babel/runtime/helpers/createClass")));
/* istanbul ignore next */
cov_1jna3fzssz().s[4]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_1jna3fzssz().s[5]++;
exports.SourceAccountListUseCase = void 0;
var ExcecutionHandler_1 =
/* istanbul ignore next */
(cov_1jna3fzssz().s[6]++, require("../../../utils/ExcecutionHandler"));
var SourceAccountListUseCase =
/* istanbul ignore next */
(cov_1jna3fzssz().s[7]++, function () {
  /* istanbul ignore next */
  cov_1jna3fzssz().f[0]++;
  function SourceAccountListUseCase(repository) {
    /* istanbul ignore next */
    cov_1jna3fzssz().f[1]++;
    cov_1jna3fzssz().s[8]++;
    (0, _classCallCheck2.default)(this, SourceAccountListUseCase);
    /* istanbul ignore next */
    cov_1jna3fzssz().s[9]++;
    this.repository = repository;
  }
  /* istanbul ignore next */
  cov_1jna3fzssz().s[10]++;
  return (0, _createClass2.default)(SourceAccountListUseCase, [{
    key: "execute",
    value: function () {
      /* istanbul ignore next */
      cov_1jna3fzssz().f[2]++;
      var _execute =
      /* istanbul ignore next */
      (cov_1jna3fzssz().s[11]++, (0, _asyncToGenerator2.default)(function* (request) {
        /* istanbul ignore next */
        cov_1jna3fzssz().f[3]++;
        var _this =
        /* istanbul ignore next */
        (cov_1jna3fzssz().s[12]++, this);
        var params =
        /* istanbul ignore next */
        (cov_1jna3fzssz().s[13]++, {
          externalStateIds: ['ACTIVE'],
          externalProductKindIds: ['kind1', 'kind10'],
          currency: 'VND'
        });
        /* istanbul ignore next */
        cov_1jna3fzssz().s[14]++;
        return ExcecutionHandler_1.ExecutionHandler.execute(function () {
          /* istanbul ignore next */
          cov_1jna3fzssz().f[4]++;
          cov_1jna3fzssz().s[15]++;
          return _this.repository.sourceAccountList(request != null ?
          /* istanbul ignore next */
          (cov_1jna3fzssz().b[0][0]++, request) :
          /* istanbul ignore next */
          (cov_1jna3fzssz().b[0][1]++, params));
        });
      }));
      function execute(_x) {
        /* istanbul ignore next */
        cov_1jna3fzssz().f[5]++;
        cov_1jna3fzssz().s[16]++;
        return _execute.apply(this, arguments);
      }
      /* istanbul ignore next */
      cov_1jna3fzssz().s[17]++;
      return execute;
    }()
  }]);
}());
/* istanbul ignore next */
cov_1jna3fzssz().s[18]++;
exports.SourceAccountListUseCase = SourceAccountListUseCase;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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