{"version": 3, "names": ["cov_1jna3fzssz", "actualCoverage", "ExcecutionHandler_1", "s", "require", "SourceAccountListUseCase", "f", "repository", "_classCallCheck2", "default", "_createClass2", "key", "value", "_execute", "_asyncToGenerator2", "request", "_this", "params", "externalStateIds", "externalProductKindIds", "currency", "ExecutionHandler", "execute", "sourceAccountList", "b", "_x", "apply", "arguments", "exports"], "sources": ["/Users/<USER>/projects/react-native/payment-module/src/domain/usecases/arrangement/SourceAccountListUseCase.ts"], "sourcesContent": ["import {IArrangementRepository} from '../../repositories/IArrangementRepository';\nimport {SourceAccountListModel} from '../../entities/source-account-list/SourceAccountListModel';\nimport {ResultState} from '../../../core/ResultState';\nimport {ExecutionHandler} from '../../../utils/ExcecutionHandler';\nimport {SourceAccountListRequest} from '../../../data/models/source-account-list/SourceAccountListRequest';\nexport class SourceAccountListUseCase {\n  private repository: IArrangementRepository;\n\n  constructor(repository: IArrangementRepository) {\n    this.repository = repository;\n  }\n\n  public async execute(request?: SourceAccountListRequest): Promise<ResultState<SourceAccountListModel>> {\n    const params: SourceAccountListRequest = {\n      externalStateIds: ['ACTIVE'],\n      externalProductKindIds: ['kind1', 'kind10'],\n      currency: 'VND',\n    };\n    return ExecutionHandler.execute(() => this.repository.sourceAccountList(request ?? params));\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAUE;IAAAA,cAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,cAAA;;;;;;;;;;;;;;;;;;;;;AAPF,IAAAE,mBAAA;AAAA;AAAA,CAAAF,cAAA,GAAAG,CAAA,OAAAC,OAAA;AAAkE,IAErDC,wBAAwB;AAAA;AAAA,CAAAL,cAAA,GAAAG,CAAA;EAAA;EAAAH,cAAA,GAAAM,CAAA;EAGnC,SAAAD,yBAAYE,UAAkC;IAAA;IAAAP,cAAA,GAAAM,CAAA;IAAAN,cAAA,GAAAG,CAAA;IAAA,IAAAK,gBAAA,CAAAC,OAAA,QAAAJ,wBAAA;IAAA;IAAAL,cAAA,GAAAG,CAAA;IAC5C,IAAI,CAACI,UAAU,GAAGA,UAAU;EAC9B;EAAA;EAAAP,cAAA,GAAAG,CAAA;EAAC,WAAAO,aAAA,CAAAD,OAAA,EAAAJ,wBAAA;IAAAM,GAAA;IAAAC,KAAA;MAAA;MAAAZ,cAAA,GAAAM,CAAA;MAAA,IAAAO,QAAA;MAAA;MAAA,CAAAb,cAAA,GAAAG,CAAA,YAAAW,kBAAA,CAAAL,OAAA,EAEM,WAAcM,OAAkC;QAAA;QAAAf,cAAA,GAAAM,CAAA;QAAA,IAAAU,KAAA;QAAA;QAAA,CAAAhB,cAAA,GAAAG,CAAA;QACrD,IAAMc,MAAM;QAAA;QAAA,CAAAjB,cAAA,GAAAG,CAAA,QAA6B;UACvCe,gBAAgB,EAAE,CAAC,QAAQ,CAAC;UAC5BC,sBAAsB,EAAE,CAAC,OAAO,EAAE,QAAQ,CAAC;UAC3CC,QAAQ,EAAE;SACX;QAAA;QAAApB,cAAA,GAAAG,CAAA;QACD,OAAOD,mBAAA,CAAAmB,gBAAgB,CAACC,OAAO,CAAC;UAAA;UAAAtB,cAAA,GAAAM,CAAA;UAAAN,cAAA,GAAAG,CAAA;UAAA,OAAMa,KAAI,CAACT,UAAU,CAACgB,iBAAiB,CAACR,OAAO;UAAA;UAAA,CAAAf,cAAA,GAAAwB,CAAA,UAAPT,OAAO;UAAA;UAAA,CAAAf,cAAA,GAAAwB,CAAA,UAAIP,MAAM,EAAC;QAAA,EAAC;MAC7F,CAAC;MAAA,SAPYK,OAAOA,CAAAG,EAAA;QAAA;QAAAzB,cAAA,GAAAM,CAAA;QAAAN,cAAA,GAAAG,CAAA;QAAA,OAAAU,QAAA,CAAAa,KAAA,OAAAC,SAAA;MAAA;MAAA;MAAA3B,cAAA,GAAAG,CAAA;MAAA,OAAPmB,OAAO;IAAA;EAAA;AAAA;AAAA;AAAAtB,cAAA,GAAAG,CAAA;AAPtByB,OAAA,CAAAvB,wBAAA,GAAAA,wBAAA", "ignoreList": []}