{"version": 3, "names": ["cov_1re78nv0sv", "actualCoverage", "PaymentOrderStatusMapper_1", "s", "require", "PaymentOrderMapper_1", "HandleData_1", "PaymentOrderRepository", "f", "remoteDataSource", "_classCallCheck2", "default", "_createClass2", "key", "value", "_paymentOrder", "_asyncToGenerator2", "request", "handleData", "paymentOrder", "mapPaymentOrderResponseToModel", "_x", "apply", "arguments", "_paymentOrderStatus", "paymentOrderStatus", "mapPaymentOrderStatusResponseToModel", "_x2", "exports"], "sources": ["/Users/<USER>/projects/react-native/payment-module/src/data/repositories/PaymentOrderRepository.ts"], "sourcesContent": ["import {mapPaymentOrderStatusResponseToModel} from '../mappers/payment-order-status/PaymentOrderStatusMapper';\nimport {PaymentOrderStatusModel} from '../../domain/entities/payment-order-status/PaymentOrderStatusModel';\nimport {mapPaymentOrderResponseToModel} from '../mappers/payment-order/PaymentOrderMapper';\nimport {handleData} from '../../utils/HandleData';\nimport {PaymentOrderModel} from '../../domain/entities/payment-order/PaymentOrderModel';\nimport {IPaymentOrderDataSource} from '../datasources/IPaymentOrderDataSource';\nimport {BaseResponse} from '../../core/BaseResponse';\nimport {IPaymentOrderRepository} from '../../domain/repositories/IPaymentOrderRepository';\nimport {PaymentOrderRequest} from '../models/payment-order/PaymentOrderRequest';\nimport {PaymentOrderStatusRequest} from '../models/payment-order-status/PaymentOrderStatusRequest';\n\nexport class PaymentOrderRepository implements IPaymentOrderRepository {\n  private remoteDataSource: IPaymentOrderDataSource;\n\n  constructor(remoteDataSource: IPaymentOrderDataSource) {\n    this.remoteDataSource = remoteDataSource;\n  }\n\n  async paymentOrder(request: PaymentOrderRequest): Promise<BaseResponse<PaymentOrderModel>> {\n    return handleData<PaymentOrderModel>(this.remoteDataSource.paymentOrder(request), mapPaymentOrderResponseToModel);\n  }\n\n  async paymentOrderStatus(request: PaymentOrderStatusRequest): Promise<BaseResponse<PaymentOrderStatusModel>> {\n    return handleData<PaymentOrderStatusModel>(\n      this.remoteDataSource.paymentOrderStatus(request),\n      mapPaymentOrderStatusResponseToModel,\n    );\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAcuD;IAAAA,cAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,cAAA;;;;;;;;;;;;;;;;;;;;;AAdvD,IAAAE,0BAAA;AAAA;AAAA,CAAAF,cAAA,GAAAG,CAAA,OAAAC,OAAA;AAEA,IAAAC,oBAAA;AAAA;AAAA,CAAAL,cAAA,GAAAG,CAAA,OAAAC,OAAA;AACA,IAAAE,YAAA;AAAA;AAAA,CAAAN,cAAA,GAAAG,CAAA,OAAAC,OAAA;AAAkD,IAQrCG,sBAAsB;AAAA;AAAA,CAAAP,cAAA,GAAAG,CAAA;EAAA;EAAAH,cAAA,GAAAQ,CAAA;EAGjC,SAAAD,uBAAYE,gBAAyC;IAAA;IAAAT,cAAA,GAAAQ,CAAA;IAAAR,cAAA,GAAAG,CAAA;IAAA,IAAAO,gBAAA,CAAAC,OAAA,QAAAJ,sBAAA;IAAA;IAAAP,cAAA,GAAAG,CAAA;IACnD,IAAI,CAACM,gBAAgB,GAAGA,gBAAgB;EAC1C;EAAA;EAAAT,cAAA,GAAAG,CAAA;EAAC,WAAAS,aAAA,CAAAD,OAAA,EAAAJ,sBAAA;IAAAM,GAAA;IAAAC,KAAA;MAAA;MAAAd,cAAA,GAAAQ,CAAA;MAAA,IAAAO,aAAA;MAAA;MAAA,CAAAf,cAAA,GAAAG,CAAA,YAAAa,kBAAA,CAAAL,OAAA,EAED,WAAmBM,OAA4B;QAAA;QAAAjB,cAAA,GAAAQ,CAAA;QAAAR,cAAA,GAAAG,CAAA;QAC7C,OAAO,IAAAG,YAAA,CAAAY,UAAU,EAAoB,IAAI,CAACT,gBAAgB,CAACU,YAAY,CAACF,OAAO,CAAC,EAAEZ,oBAAA,CAAAe,8BAA8B,CAAC;MACnH,CAAC;MAAA,SAFKD,YAAYA,CAAAE,EAAA;QAAA;QAAArB,cAAA,GAAAQ,CAAA;QAAAR,cAAA,GAAAG,CAAA;QAAA,OAAAY,aAAA,CAAAO,KAAA,OAAAC,SAAA;MAAA;MAAA;MAAAvB,cAAA,GAAAG,CAAA;MAAA,OAAZgB,YAAY;IAAA;EAAA;IAAAN,GAAA;IAAAC,KAAA;MAAA;MAAAd,cAAA,GAAAQ,CAAA;MAAA,IAAAgB,mBAAA;MAAA;MAAA,CAAAxB,cAAA,GAAAG,CAAA,YAAAa,kBAAA,CAAAL,OAAA,EAIlB,WAAyBM,OAAkC;QAAA;QAAAjB,cAAA,GAAAQ,CAAA;QAAAR,cAAA,GAAAG,CAAA;QACzD,OAAO,IAAAG,YAAA,CAAAY,UAAU,EACf,IAAI,CAACT,gBAAgB,CAACgB,kBAAkB,CAACR,OAAO,CAAC,EACjDf,0BAAA,CAAAwB,oCAAoC,CACrC;MACH,CAAC;MAAA,SALKD,kBAAkBA,CAAAE,GAAA;QAAA;QAAA3B,cAAA,GAAAQ,CAAA;QAAAR,cAAA,GAAAG,CAAA;QAAA,OAAAqB,mBAAA,CAAAF,KAAA,OAAAC,SAAA;MAAA;MAAA;MAAAvB,cAAA,GAAAG,CAAA;MAAA,OAAlBsB,kBAAkB;IAAA;EAAA;AAAA;AAAA;AAAAzB,cAAA,GAAAG,CAAA;AAX1ByB,OAAA,CAAArB,sBAAA,GAAAA,sBAAA", "ignoreList": []}