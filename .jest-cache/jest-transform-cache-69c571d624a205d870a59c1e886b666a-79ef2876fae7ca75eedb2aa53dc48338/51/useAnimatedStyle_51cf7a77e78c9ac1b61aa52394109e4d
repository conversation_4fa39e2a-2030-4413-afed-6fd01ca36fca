883345f40f3814f0b20eaa2e85526646
'use strict';

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.useAnimatedStyle = useAnimatedStyle;
var _toConsumableArray2 = _interopRequireDefault(require("@babel/runtime/helpers/toConsumableArray"));
var _react = require("react");
var _core = require("../core.js");
var _UpdateProps = _interopRequireWildcard(require("../UpdateProps.js"));
var _index = require("../animation/index.js");
var _useSharedValue = require("./useSharedValue.js");
var _utils = require("./utils.js");
var _ViewDescriptorsSet = require("../ViewDescriptorsSet.js");
var _PlatformChecker = require("../PlatformChecker.js");
var _commonTypes = require("../commonTypes.js");
var _errors = require("../errors.js");
function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
var SHOULD_BE_USE_WEB = (0, _PlatformChecker.shouldBeUseWeb)();
function prepareAnimation(frameTimestamp, animatedProp, lastAnimation, lastValue) {
  'worklet';

  if (Array.isArray(animatedProp)) {
    animatedProp.forEach(function (prop, index) {
      prepareAnimation(frameTimestamp, prop, lastAnimation && lastAnimation[index], lastValue && lastValue[index]);
    });
  }
  if (typeof animatedProp === 'object' && animatedProp.onFrame) {
    var animation = animatedProp;
    var value = animation.current;
    if (lastValue !== undefined && lastValue !== null) {
      if (typeof lastValue === 'object') {
        if (lastValue.value !== undefined) {
          value = lastValue.value;
        } else if (lastValue.onFrame !== undefined) {
          if ((lastAnimation == null ? void 0 : lastAnimation.current) !== undefined) {
            value = lastAnimation.current;
          } else if ((lastValue == null ? void 0 : lastValue.current) !== undefined) {
            value = lastValue.current;
          }
        }
      } else {
        value = lastValue;
      }
    }
    animation.callStart = function (timestamp) {
      animation.onStart(animation, value, timestamp, lastAnimation);
    };
    animation.callStart(frameTimestamp);
    animation.callStart = null;
  } else if (typeof animatedProp === 'object') {
    Object.keys(animatedProp).forEach(function (key) {
      return prepareAnimation(frameTimestamp, animatedProp[key], lastAnimation && lastAnimation[key], lastValue && lastValue[key]);
    });
  }
}
function runAnimations(animation, timestamp, key, result, animationsActive) {
  'worklet';

  if (!animationsActive.value) {
    return true;
  }
  if (Array.isArray(animation)) {
    result[key] = [];
    var allFinished = true;
    animation.forEach(function (entry, index) {
      if (!runAnimations(entry, timestamp, index, result[key], animationsActive)) {
        allFinished = false;
      }
    });
    return allFinished;
  } else if (typeof animation === 'object' && animation.onFrame) {
    var finished = true;
    if (!animation.finished) {
      if (animation.callStart) {
        animation.callStart(timestamp);
        animation.callStart = null;
      }
      finished = animation.onFrame(animation, timestamp);
      animation.timestamp = timestamp;
      if (finished) {
        animation.finished = true;
        animation.callback && animation.callback(true);
      }
    }
    result[key] = animation.current;
    return finished;
  } else if (typeof animation === 'object') {
    result[key] = {};
    var _allFinished = true;
    Object.keys(animation).forEach(function (k) {
      if (!runAnimations(animation[k], timestamp, k, result[key], animationsActive)) {
        _allFinished = false;
      }
    });
    return _allFinished;
  } else {
    result[key] = animation;
    return true;
  }
}
function styleUpdater(viewDescriptors, updater, state, animationsActive) {
  'worklet';

  var _state$animations, _updater;
  var isAnimatedProps = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : false;
  var animations = (_state$animations = state.animations) != null ? _state$animations : {};
  var newValues = (_updater = updater()) != null ? _updater : {};
  var oldValues = state.last;
  var nonAnimatedNewValues = {};
  var hasAnimations = false;
  var frameTimestamp;
  var hasNonAnimatedValues = false;
  for (var key in newValues) {
    var value = newValues[key];
    if ((0, _utils.isAnimated)(value)) {
      frameTimestamp = global.__frameTimestamp || global._getAnimationTimestamp();
      prepareAnimation(frameTimestamp, value, animations[key], oldValues[key]);
      animations[key] = value;
      hasAnimations = true;
    } else {
      hasNonAnimatedValues = true;
      nonAnimatedNewValues[key] = value;
      delete animations[key];
    }
  }
  if (hasAnimations) {
    var _frame = function frame(timestamp) {
      var animations = state.animations,
        last = state.last,
        isAnimationCancelled = state.isAnimationCancelled;
      if (isAnimationCancelled) {
        state.isAnimationRunning = false;
        return;
      }
      var updates = {};
      var allFinished = true;
      for (var propName in animations) {
        var finished = runAnimations(animations[propName], timestamp, propName, updates, animationsActive);
        if (finished) {
          last[propName] = updates[propName];
          delete animations[propName];
        } else {
          allFinished = false;
        }
      }
      if (updates) {
        (0, _UpdateProps.default)(viewDescriptors, updates);
      }
      if (!allFinished) {
        requestAnimationFrame(_frame);
      } else {
        state.isAnimationRunning = false;
      }
    };
    state.animations = animations;
    if (!state.isAnimationRunning) {
      state.isAnimationCancelled = false;
      state.isAnimationRunning = true;
      _frame(frameTimestamp);
    }
    if (hasNonAnimatedValues) {
      (0, _UpdateProps.default)(viewDescriptors, nonAnimatedNewValues);
    }
  } else {
    state.isAnimationCancelled = true;
    state.animations = [];
    if (!(0, _utils.shallowEqual)(oldValues, newValues)) {
      (0, _UpdateProps.default)(viewDescriptors, newValues, isAnimatedProps);
    }
  }
  state.last = newValues;
}
function jestStyleUpdater(viewDescriptors, updater, state, animationsActive, animatedStyle, adapters) {
  'worklet';

  var _state$animations2, _updater2;
  var animations = (_state$animations2 = state.animations) != null ? _state$animations2 : {};
  var newValues = (_updater2 = updater()) != null ? _updater2 : {};
  var oldValues = state.last;
  var hasAnimations = false;
  var frameTimestamp;
  Object.keys(animations).forEach(function (key) {
    var value = newValues[key];
    if (!(0, _utils.isAnimated)(value)) {
      delete animations[key];
    }
  });
  Object.keys(newValues).forEach(function (key) {
    var value = newValues[key];
    if ((0, _utils.isAnimated)(value)) {
      frameTimestamp = global.__frameTimestamp || global._getAnimationTimestamp();
      prepareAnimation(frameTimestamp, value, animations[key], oldValues[key]);
      animations[key] = value;
      hasAnimations = true;
    }
  });
  function frame(timestamp) {
    var animations = state.animations,
      last = state.last,
      isAnimationCancelled = state.isAnimationCancelled;
    if (isAnimationCancelled) {
      state.isAnimationRunning = false;
      return;
    }
    var updates = {};
    var allFinished = true;
    Object.keys(animations).forEach(function (propName) {
      var finished = runAnimations(animations[propName], timestamp, propName, updates, animationsActive);
      if (finished) {
        last[propName] = updates[propName];
        delete animations[propName];
      } else {
        allFinished = false;
      }
    });
    if (Object.keys(updates).length) {
      (0, _UpdateProps.updatePropsJestWrapper)(viewDescriptors, updates, animatedStyle, adapters);
    }
    if (!allFinished) {
      requestAnimationFrame(frame);
    } else {
      state.isAnimationRunning = false;
    }
  }
  if (hasAnimations) {
    state.animations = animations;
    if (!state.isAnimationRunning) {
      state.isAnimationCancelled = false;
      state.isAnimationRunning = true;
      frame(frameTimestamp);
    }
  } else {
    state.isAnimationCancelled = true;
    state.animations = [];
  }
  state.last = newValues;
  if (!(0, _utils.shallowEqual)(oldValues, newValues)) {
    (0, _UpdateProps.updatePropsJestWrapper)(viewDescriptors, newValues, animatedStyle, adapters);
  }
}
function checkSharedValueUsage(prop, currentKey) {
  if (Array.isArray(prop)) {
    for (var element of prop) {
      checkSharedValueUsage(element, currentKey);
    }
  } else if (typeof prop === 'object' && prop !== null && prop.value === undefined) {
    for (var key of Object.keys(prop)) {
      checkSharedValueUsage(prop[key], key);
    }
  } else if (currentKey !== undefined && typeof prop === 'object' && prop !== null && prop.value !== undefined) {
    throw new _errors.ReanimatedError(`Invalid value passed to \`${currentKey}\`, maybe you forgot to use \`.value\`?`);
  }
}
function useAnimatedStyle(updater, dependencies, adapters) {
  var _updater$__closure;
  var isAnimatedProps = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : false;
  var animatedUpdaterData = (0, _react.useRef)();
  var inputs = Object.values((_updater$__closure = updater.__closure) != null ? _updater$__closure : {});
  if (SHOULD_BE_USE_WEB) {
    var _dependencies;
    if (!inputs.length && (_dependencies = dependencies) != null && _dependencies.length) {
      inputs = dependencies;
    }
    if (__DEV__ && !inputs.length && !dependencies && !(0, _commonTypes.isWorkletFunction)(updater)) {
      throw new _errors.ReanimatedError(`\`useAnimatedStyle\` was used without a dependency array or Babel plugin. Please explicitly pass a dependency array, or enable the Babel plugin.
For more, see the docs: \`https://docs.swmansion.com/react-native-reanimated/docs/guides/web-support#web-without-the-babel-plugin\`.`);
    }
  }
  var adaptersArray = adapters ? Array.isArray(adapters) ? adapters : [adapters] : [];
  var adaptersHash = adapters ? (0, _utils.buildWorkletsHash)(adaptersArray) : null;
  var areAnimationsActive = (0, _useSharedValue.useSharedValue)(true);
  var jestAnimatedStyle = (0, _react.useRef)({});
  if (!dependencies) {
    dependencies = [].concat((0, _toConsumableArray2.default)(inputs), [updater.__workletHash]);
  } else {
    dependencies.push(updater.__workletHash);
  }
  adaptersHash && dependencies.push(adaptersHash);
  if (!animatedUpdaterData.current) {
    var initialStyle = (0, _index.initialUpdaterRun)(updater);
    if (__DEV__) {
      (0, _utils.validateAnimatedStyles)(initialStyle);
    }
    animatedUpdaterData.current = {
      initial: {
        value: initialStyle,
        updater: updater
      },
      remoteState: (0, _core.makeShareable)({
        last: initialStyle,
        animations: {},
        isAnimationCancelled: false,
        isAnimationRunning: false
      }),
      viewDescriptors: (0, _ViewDescriptorsSet.makeViewDescriptorsSet)()
    };
  }
  var _animatedUpdaterData$ = animatedUpdaterData.current,
    initial = _animatedUpdaterData$.initial,
    remoteState = _animatedUpdaterData$.remoteState,
    viewDescriptors = _animatedUpdaterData$.viewDescriptors;
  var shareableViewDescriptors = viewDescriptors.shareableViewDescriptors;
  dependencies.push(shareableViewDescriptors);
  (0, _react.useEffect)(function () {
    var fun;
    var updaterFn = updater;
    if (adapters) {
      updaterFn = function updaterFn() {
        'worklet';

        var newValues = updater();
        adaptersArray.forEach(function (adapter) {
          adapter(newValues);
        });
        return newValues;
      };
    }
    if ((0, _PlatformChecker.isJest)()) {
      fun = function fun() {
        'worklet';

        jestStyleUpdater(shareableViewDescriptors, updater, remoteState, areAnimationsActive, jestAnimatedStyle, adaptersArray);
      };
    } else {
      fun = function fun() {
        'worklet';

        styleUpdater(shareableViewDescriptors, updaterFn, remoteState, areAnimationsActive, isAnimatedProps);
      };
    }
    var mapperId = (0, _core.startMapper)(fun, inputs);
    return function () {
      (0, _core.stopMapper)(mapperId);
    };
  }, dependencies);
  (0, _react.useEffect)(function () {
    areAnimationsActive.value = true;
    return function () {
      areAnimationsActive.value = false;
    };
  }, [areAnimationsActive]);
  checkSharedValueUsage(initial.value);
  var animatedStyleHandle = (0, _react.useRef)(null);
  if (!animatedStyleHandle.current) {
    animatedStyleHandle.current = (0, _PlatformChecker.isJest)() ? {
      viewDescriptors: viewDescriptors,
      initial: initial,
      jestAnimatedStyle: jestAnimatedStyle
    } : {
      viewDescriptors: viewDescriptors,
      initial: initial
    };
  }
  return animatedStyleHandle.current;
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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