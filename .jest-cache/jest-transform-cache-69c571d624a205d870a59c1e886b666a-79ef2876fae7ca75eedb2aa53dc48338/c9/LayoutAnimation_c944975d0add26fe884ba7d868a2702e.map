{"version": 3, "names": ["_interopRequireDefault", "require", "ReactNativeFeatureFlags", "_interopRequireWildcard", "_FabricUIManager", "_Platform", "_getRequireWildcardCache", "e", "WeakMap", "r", "t", "__esModule", "default", "has", "get", "n", "__proto__", "a", "Object", "defineProperty", "getOwnPropertyDescriptor", "u", "hasOwnProperty", "call", "i", "set", "UIManager", "isLayoutAnimationEnabled", "setEnabled", "value", "configureNext", "config", "onAnimationDidEnd", "onAnimationDidFail", "_config$duration", "Platform", "isDisableAnimations", "animationCompletionHasRun", "onAnimationComplete", "clearTimeout", "raceWithAnimationId", "setTimeout", "duration", "FabricUIManager", "getFabricUIManager", "configureNextLayoutAnimation", "_global", "global", "nativeFabricUIManager", "create", "type", "property", "update", "delete", "Presets", "easeInEaseOut", "linear", "spring", "springDamping", "LayoutAnimation", "Types", "freeze", "easeIn", "easeOut", "keyboard", "Properties", "opacity", "scaleX", "scaleY", "scaleXY", "checkConfig", "console", "error", "bind", "module", "exports"], "sources": ["LayoutAnimation.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @flow strict-local\n * @format\n */\n\n'use strict';\n\nimport type {\n  LayoutAnimationConfig as LayoutAnimationConfig_,\n  LayoutAnimationProperty,\n  LayoutAnimationType,\n} from '../Renderer/shims/ReactNativeTypes';\n\nimport * as ReactNativeFeatureFlags from '../../src/private/featureflags/ReactNativeFeatureFlags';\nimport {getFabricUIManager} from '../ReactNative/FabricUIManager';\nimport Platform from '../Utilities/Platform';\n\nconst UIManager = require('../ReactNative/UIManager');\n\n// Reexport type\nexport type LayoutAnimationConfig = LayoutAnimationConfig_;\n\ntype OnAnimationDidEndCallback = () => void;\ntype OnAnimationDidFailCallback = () => void;\n\nlet isLayoutAnimationEnabled: boolean =\n  ReactNativeFeatureFlags.isLayoutAnimationEnabled();\n\nfunction setEnabled(value: boolean) {\n  isLayoutAnimationEnabled = isLayoutAnimationEnabled;\n}\n\n/**\n * Configures the next commit to be animated.\n *\n * onAnimationDidEnd is guaranteed to be called when the animation completes.\n * onAnimationDidFail is *never* called in the classic, pre-Fabric renderer,\n * and never has been. In the new renderer (Fabric) it is called only if configuration\n * parsing fails.\n */\nfunction configureNext(\n  config: LayoutAnimationConfig,\n  onAnimationDidEnd?: OnAnimationDidEndCallback,\n  onAnimationDidFail?: OnAnimationDidFailCallback,\n) {\n  if (Platform.isDisableAnimations) {\n    return;\n  }\n\n  if (!isLayoutAnimationEnabled) {\n    return;\n  }\n\n  // Since LayoutAnimations may possibly be disabled for now on iOS (Fabric),\n  // or Android (non-Fabric) we race a setTimeout with animation completion,\n  // in case onComplete is never called\n  // from native. Once LayoutAnimations+Fabric unconditionally ship everywhere, we can\n  // delete this mechanism at least in the Fabric branch.\n  let animationCompletionHasRun = false;\n  const onAnimationComplete = () => {\n    if (animationCompletionHasRun) {\n      return;\n    }\n    animationCompletionHasRun = true;\n    clearTimeout(raceWithAnimationId);\n    onAnimationDidEnd?.();\n  };\n  const raceWithAnimationId = setTimeout(\n    onAnimationComplete,\n    (config.duration ?? 0) + 17 /* one frame + 1ms */,\n  );\n\n  // In Fabric, LayoutAnimations are unconditionally enabled for Android, and\n  // conditionally enabled on iOS (pending fully shipping; this is a temporary state).\n  const FabricUIManager = getFabricUIManager();\n  if (FabricUIManager?.configureNextLayoutAnimation) {\n    global?.nativeFabricUIManager?.configureNextLayoutAnimation(\n      config,\n      onAnimationComplete,\n      onAnimationDidFail ??\n        function () {} /* this will only be called if configuration parsing fails */,\n    );\n    return;\n  }\n\n  // This will only run if Fabric is *not* installed.\n  // If you have Fabric + non-Fabric running in the same VM, non-Fabric LayoutAnimations\n  // will not work.\n  if (UIManager?.configureNextLayoutAnimation) {\n    UIManager.configureNextLayoutAnimation(\n      config,\n      onAnimationComplete ?? function () {},\n      onAnimationDidFail ??\n        function () {} /* this should never be called in Non-Fabric */,\n    );\n  }\n}\n\nfunction create(\n  duration: number,\n  type: LayoutAnimationType,\n  property: LayoutAnimationProperty,\n): LayoutAnimationConfig {\n  return {\n    duration,\n    create: {type, property},\n    update: {type},\n    delete: {type, property},\n  };\n}\n\nconst Presets = {\n  easeInEaseOut: (create(\n    300,\n    'easeInEaseOut',\n    'opacity',\n  ): LayoutAnimationConfig),\n  linear: (create(500, 'linear', 'opacity'): LayoutAnimationConfig),\n  spring: ({\n    duration: 700,\n    create: {\n      type: 'linear',\n      property: 'opacity',\n    },\n    update: {\n      type: 'spring',\n      springDamping: 0.4,\n    },\n    delete: {\n      type: 'linear',\n      property: 'opacity',\n    },\n  }: LayoutAnimationConfig),\n};\n\n/**\n * Automatically animates views to their new positions when the\n * next layout happens.\n *\n * A common way to use this API is to call it before calling `setState`.\n *\n * Note that in order to get this to work on **Android** you need to set the following flags via `UIManager`:\n *\n *     UIManager.setLayoutAnimationEnabledExperimental && UIManager.setLayoutAnimationEnabledExperimental(true);\n */\nconst LayoutAnimation = {\n  /**\n   * Schedules an animation to happen on the next layout.\n   *\n   * @param config Specifies animation properties:\n   *\n   *   - `duration` in milliseconds\n   *   - `create`, `AnimationConfig` for animating in new views\n   *   - `update`, `AnimationConfig` for animating views that have been updated\n   *\n   * @param onAnimationDidEnd Called when the animation finished.\n   * Only supported on iOS.\n   * @param onError Called on error. Only supported on iOS.\n   */\n  configureNext,\n  /**\n   * Helper for creating a config for `configureNext`.\n   */\n  create,\n  Types: Object.freeze({\n    spring: 'spring',\n    linear: 'linear',\n    easeInEaseOut: 'easeInEaseOut',\n    easeIn: 'easeIn',\n    easeOut: 'easeOut',\n    keyboard: 'keyboard',\n  }),\n  Properties: Object.freeze({\n    opacity: 'opacity',\n    scaleX: 'scaleX',\n    scaleY: 'scaleY',\n    scaleXY: 'scaleXY',\n  }),\n  checkConfig(...args: Array<mixed>) {\n    console.error('LayoutAnimation.checkConfig(...) has been disabled.');\n  },\n  Presets,\n  easeInEaseOut: (configureNext.bind(null, Presets.easeInEaseOut): (\n    onAnimationDidEnd?: OnAnimationDidEndCallback,\n  ) => void),\n  linear: (configureNext.bind(null, Presets.linear): (\n    onAnimationDidEnd?: OnAnimationDidEndCallback,\n  ) => void),\n  spring: (configureNext.bind(null, Presets.spring): (\n    onAnimationDidEnd?: OnAnimationDidEndCallback,\n  ) => void),\n  setEnabled,\n};\n\nmodule.exports = LayoutAnimation;\n"], "mappings": "AAUA,YAAY;;AAAC,IAAAA,sBAAA,GAAAC,OAAA;AAQb,IAAAC,uBAAA,GAAAC,uBAAA,CAAAF,OAAA;AACA,IAAAG,gBAAA,GAAAH,OAAA;AACA,IAAAI,SAAA,GAAAL,sBAAA,CAAAC,OAAA;AAA6C,SAAAK,yBAAAC,CAAA,6BAAAC,OAAA,mBAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,yBAAAC,CAAA,WAAAA,CAAA,GAAAG,CAAA,GAAAD,CAAA,KAAAF,CAAA;AAAA,SAAAJ,wBAAAI,CAAA,EAAAE,CAAA,SAAAA,CAAA,IAAAF,CAAA,IAAAA,CAAA,CAAAI,UAAA,SAAAJ,CAAA,eAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,WAAAK,OAAA,EAAAL,CAAA,QAAAG,CAAA,GAAAJ,wBAAA,CAAAG,CAAA,OAAAC,CAAA,IAAAA,CAAA,CAAAG,GAAA,CAAAN,CAAA,UAAAG,CAAA,CAAAI,GAAA,CAAAP,CAAA,OAAAQ,CAAA,KAAAC,SAAA,UAAAC,CAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,CAAA,IAAAd,CAAA,oBAAAc,CAAA,OAAAC,cAAA,CAAAC,IAAA,CAAAhB,CAAA,EAAAc,CAAA,SAAAG,CAAA,GAAAP,CAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAb,CAAA,EAAAc,CAAA,UAAAG,CAAA,KAAAA,CAAA,CAAAV,GAAA,IAAAU,CAAA,CAAAC,GAAA,IAAAP,MAAA,CAAAC,cAAA,CAAAJ,CAAA,EAAAM,CAAA,EAAAG,CAAA,IAAAT,CAAA,CAAAM,CAAA,IAAAd,CAAA,CAAAc,CAAA,YAAAN,CAAA,CAAAH,OAAA,GAAAL,CAAA,EAAAG,CAAA,IAAAA,CAAA,CAAAe,GAAA,CAAAlB,CAAA,EAAAQ,CAAA,GAAAA,CAAA;AAE7C,IAAMW,SAAS,GAAGzB,OAAO,CAAC,0BAA0B,CAAC;AAQrD,IAAI0B,wBAAiC,GACnCzB,uBAAuB,CAACyB,wBAAwB,CAAC,CAAC;AAEpD,SAASC,UAAUA,CAACC,KAAc,EAAE;EAClCF,wBAAwB,GAAGA,wBAAwB;AACrD;AAUA,SAASG,aAAaA,CACpBC,MAA6B,EAC7BC,iBAA6C,EAC7CC,kBAA+C,EAC/C;EAAA,IAAAC,gBAAA;EACA,IAAIC,iBAAQ,CAACC,mBAAmB,EAAE;IAChC;EACF;EAEA,IAAI,CAACT,wBAAwB,EAAE;IAC7B;EACF;EAOA,IAAIU,yBAAyB,GAAG,KAAK;EACrC,IAAMC,mBAAmB,GAAG,SAAtBA,mBAAmBA,CAAA,EAAS;IAChC,IAAID,yBAAyB,EAAE;MAC7B;IACF;IACAA,yBAAyB,GAAG,IAAI;IAChCE,YAAY,CAACC,mBAAmB,CAAC;IACjCR,iBAAiB,YAAjBA,iBAAiB,CAAG,CAAC;EACvB,CAAC;EACD,IAAMQ,mBAAmB,GAAGC,UAAU,CACpCH,mBAAmB,EACnB,EAAAJ,gBAAA,GAACH,MAAM,CAACW,QAAQ,YAAAR,gBAAA,GAAI,CAAC,IAAI,EAC3B,CAAC;EAID,IAAMS,eAAe,GAAG,IAAAC,mCAAkB,EAAC,CAAC;EAC5C,IAAID,eAAe,YAAfA,eAAe,CAAEE,4BAA4B,EAAE;IAAA,IAAAC,OAAA;IACjD,CAAAA,OAAA,GAAAC,MAAM,cAAAD,OAAA,GAANA,OAAA,CAAQE,qBAAqB,aAA7BF,OAAA,CAA+BD,4BAA4B,CACzDd,MAAM,EACNO,mBAAmB,EACnBL,kBAAkB,WAAlBA,kBAAkB,GAChB,YAAY,CAAC,CACjB,CAAC;IACD;EACF;EAKA,IAAIP,SAAS,YAATA,SAAS,CAAEmB,4BAA4B,EAAE;IAC3CnB,SAAS,CAACmB,4BAA4B,CACpCd,MAAM,EACNO,mBAAmB,WAAnBA,mBAAmB,GAAI,YAAY,CAAC,CAAC,EACrCL,kBAAkB,WAAlBA,kBAAkB,GAChB,YAAY,CAAC,CACjB,CAAC;EACH;AACF;AAEA,SAASgB,MAAMA,CACbP,QAAgB,EAChBQ,IAAyB,EACzBC,QAAiC,EACV;EACvB,OAAO;IACLT,QAAQ,EAARA,QAAQ;IACRO,MAAM,EAAE;MAACC,IAAI,EAAJA,IAAI;MAAEC,QAAQ,EAARA;IAAQ,CAAC;IACxBC,MAAM,EAAE;MAACF,IAAI,EAAJA;IAAI,CAAC;IACdG,MAAM,EAAE;MAACH,IAAI,EAAJA,IAAI;MAAEC,QAAQ,EAARA;IAAQ;EACzB,CAAC;AACH;AAEA,IAAMG,OAAO,GAAG;EACdC,aAAa,EAAGN,MAAM,CACpB,GAAG,EACH,eAAe,EACf,SACF,CAAyB;EACzBO,MAAM,EAAGP,MAAM,CAAC,GAAG,EAAE,QAAQ,EAAE,SAAS,CAAyB;EACjEQ,MAAM,EAAG;IACPf,QAAQ,EAAE,GAAG;IACbO,MAAM,EAAE;MACNC,IAAI,EAAE,QAAQ;MACdC,QAAQ,EAAE;IACZ,CAAC;IACDC,MAAM,EAAE;MACNF,IAAI,EAAE,QAAQ;MACdQ,aAAa,EAAE;IACjB,CAAC;IACDL,MAAM,EAAE;MACNH,IAAI,EAAE,QAAQ;MACdC,QAAQ,EAAE;IACZ;EACF;AACF,CAAC;AAYD,IAAMQ,eAAe,GAAG;EActB7B,aAAa,EAAbA,aAAa;EAIbmB,MAAM,EAANA,MAAM;EACNW,KAAK,EAAE1C,MAAM,CAAC2C,MAAM,CAAC;IACnBJ,MAAM,EAAE,QAAQ;IAChBD,MAAM,EAAE,QAAQ;IAChBD,aAAa,EAAE,eAAe;IAC9BO,MAAM,EAAE,QAAQ;IAChBC,OAAO,EAAE,SAAS;IAClBC,QAAQ,EAAE;EACZ,CAAC,CAAC;EACFC,UAAU,EAAE/C,MAAM,CAAC2C,MAAM,CAAC;IACxBK,OAAO,EAAE,SAAS;IAClBC,MAAM,EAAE,QAAQ;IAChBC,MAAM,EAAE,QAAQ;IAChBC,OAAO,EAAE;EACX,CAAC,CAAC;EACFC,WAAW,WAAXA,WAAWA,CAAA,EAAwB;IACjCC,OAAO,CAACC,KAAK,CAAC,qDAAqD,CAAC;EACtE,CAAC;EACDlB,OAAO,EAAPA,OAAO;EACPC,aAAa,EAAGzB,aAAa,CAAC2C,IAAI,CAAC,IAAI,EAAEnB,OAAO,CAACC,aAAa,CAEpD;EACVC,MAAM,EAAG1B,aAAa,CAAC2C,IAAI,CAAC,IAAI,EAAEnB,OAAO,CAACE,MAAM,CAEtC;EACVC,MAAM,EAAG3B,aAAa,CAAC2C,IAAI,CAAC,IAAI,EAAEnB,OAAO,CAACG,MAAM,CAEtC;EACV7B,UAAU,EAAVA;AACF,CAAC;AAED8C,MAAM,CAACC,OAAO,GAAGhB,eAAe", "ignoreList": []}