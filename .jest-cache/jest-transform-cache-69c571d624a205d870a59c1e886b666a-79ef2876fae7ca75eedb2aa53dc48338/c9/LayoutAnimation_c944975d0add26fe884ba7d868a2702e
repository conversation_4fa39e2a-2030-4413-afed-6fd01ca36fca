5d277ff4e4fd348fda5d64686f25d2d7
'use strict';

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
var ReactNativeFeatureFlags = _interopRequireWildcard(require("../../src/private/featureflags/ReactNativeFeatureFlags"));
var _FabricUIManager = require("../ReactNative/FabricUIManager");
var _Platform = _interopRequireDefault(require("../Utilities/Platform"));
function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
var UIManager = require('../ReactNative/UIManager');
var isLayoutAnimationEnabled = ReactNativeFeatureFlags.isLayoutAnimationEnabled();
function setEnabled(value) {
  isLayoutAnimationEnabled = isLayoutAnimationEnabled;
}
function configureNext(config, onAnimationDidEnd, onAnimationDidFail) {
  var _config$duration;
  if (_Platform.default.isDisableAnimations) {
    return;
  }
  if (!isLayoutAnimationEnabled) {
    return;
  }
  var animationCompletionHasRun = false;
  var onAnimationComplete = function onAnimationComplete() {
    if (animationCompletionHasRun) {
      return;
    }
    animationCompletionHasRun = true;
    clearTimeout(raceWithAnimationId);
    onAnimationDidEnd == null || onAnimationDidEnd();
  };
  var raceWithAnimationId = setTimeout(onAnimationComplete, ((_config$duration = config.duration) != null ? _config$duration : 0) + 17);
  var FabricUIManager = (0, _FabricUIManager.getFabricUIManager)();
  if (FabricUIManager != null && FabricUIManager.configureNextLayoutAnimation) {
    var _global;
    (_global = global) == null || (_global = _global.nativeFabricUIManager) == null || _global.configureNextLayoutAnimation(config, onAnimationComplete, onAnimationDidFail != null ? onAnimationDidFail : function () {});
    return;
  }
  if (UIManager != null && UIManager.configureNextLayoutAnimation) {
    UIManager.configureNextLayoutAnimation(config, onAnimationComplete != null ? onAnimationComplete : function () {}, onAnimationDidFail != null ? onAnimationDidFail : function () {});
  }
}
function create(duration, type, property) {
  return {
    duration: duration,
    create: {
      type: type,
      property: property
    },
    update: {
      type: type
    },
    delete: {
      type: type,
      property: property
    }
  };
}
var Presets = {
  easeInEaseOut: create(300, 'easeInEaseOut', 'opacity'),
  linear: create(500, 'linear', 'opacity'),
  spring: {
    duration: 700,
    create: {
      type: 'linear',
      property: 'opacity'
    },
    update: {
      type: 'spring',
      springDamping: 0.4
    },
    delete: {
      type: 'linear',
      property: 'opacity'
    }
  }
};
var LayoutAnimation = {
  configureNext: configureNext,
  create: create,
  Types: Object.freeze({
    spring: 'spring',
    linear: 'linear',
    easeInEaseOut: 'easeInEaseOut',
    easeIn: 'easeIn',
    easeOut: 'easeOut',
    keyboard: 'keyboard'
  }),
  Properties: Object.freeze({
    opacity: 'opacity',
    scaleX: 'scaleX',
    scaleY: 'scaleY',
    scaleXY: 'scaleXY'
  }),
  checkConfig: function checkConfig() {
    console.error('LayoutAnimation.checkConfig(...) has been disabled.');
  },
  Presets: Presets,
  easeInEaseOut: configureNext.bind(null, Presets.easeInEaseOut),
  linear: configureNext.bind(null, Presets.linear),
  spring: configureNext.bind(null, Presets.spring),
  setEnabled: setEnabled
};
module.exports = LayoutAnimation;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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