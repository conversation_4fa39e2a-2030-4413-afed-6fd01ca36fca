bbd4f78a8412881cad9568afad17dd60
"use strict";

/* istanbul ignore next */
function cov_pq64vfggd() {
  var path = "/Users/<USER>/projects/react-native/payment-module/src/domain/repositories/ICustomerRepository.ts";
  var hash = "76381bbadabc4b73537f310dc60a012cd9f7372e";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/projects/react-native/payment-module/src/domain/repositories/ICustomerRepository.ts",
    statementMap: {
      "0": {
        start: {
          line: 3,
          column: 0
        },
        end: {
          line: 5,
          column: 3
        }
      }
    },
    fnMap: {},
    branchMap: {},
    s: {
      "0": 0
    },
    f: {},
    b: {},
    inputSourceMap: {
      version: 3,
      file: undefined,
      names: [],
      sourceRoot: undefined,
      sources: ["/Users/<USER>/projects/react-native/payment-module/src/domain/repositories/ICustomerRepository.ts"],
      sourcesContent: ["import {GetProfileModel} from '../entities/get-profile/GetProfileModel';\nimport {BaseResponse} from '../../core/BaseResponse';\n\nexport interface ICustomerRepository {\n  getProfile(): Promise<BaseResponse<GetProfileModel>>;\n}\n"],
      mappings: "",
      ignoreList: []
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "76381bbadabc4b73537f310dc60a012cd9f7372e"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_pq64vfggd = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_pq64vfggd();
cov_pq64vfggd().s[0]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6W10sInNvdXJjZXMiOlsiL1VzZXJzL3NvbnNhbXNldC9wcm9qZWN0cy9yZWFjdC1uYXRpdmUvcGF5bWVudC1tb2R1bGUvc3JjL2RvbWFpbi9yZXBvc2l0b3JpZXMvSUN1c3RvbWVyUmVwb3NpdG9yeS50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQge0dldFByb2ZpbGVNb2RlbH0gZnJvbSAnLi4vZW50aXRpZXMvZ2V0LXByb2ZpbGUvR2V0UHJvZmlsZU1vZGVsJztcbmltcG9ydCB7QmFzZVJlc3BvbnNlfSBmcm9tICcuLi8uLi9jb3JlL0Jhc2VSZXNwb25zZSc7XG5cbmV4cG9ydCBpbnRlcmZhY2UgSUN1c3RvbWVyUmVwb3NpdG9yeSB7XG4gIGdldFByb2ZpbGUoKTogUHJvbWlzZTxCYXNlUmVzcG9uc2U8R2V0UHJvZmlsZU1vZGVsPj47XG59XG4iXSwibWFwcGluZ3MiOiIiLCJpZ25vcmVMaXN0IjpbXX0=