{"version": 3, "names": [], "sources": ["/Users/<USER>/projects/react-native/payment-module/src/domain/repositories/ICustomerRepository.ts"], "sourcesContent": ["import {GetProfileModel} from '../entities/get-profile/GetProfileModel';\nimport {BaseResponse} from '../../core/BaseResponse';\n\nexport interface ICustomerRepository {\n  getProfile(): Promise<BaseResponse<GetProfileModel>>;\n}\n"], "mappings": "", "ignoreList": []}