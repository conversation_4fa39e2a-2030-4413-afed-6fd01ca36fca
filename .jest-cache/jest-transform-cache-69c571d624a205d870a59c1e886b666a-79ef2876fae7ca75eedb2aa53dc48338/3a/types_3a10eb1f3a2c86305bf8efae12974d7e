f05d121062d88dc72c894aea169400aa
"use strict";

/* istanbul ignore next */
function cov_2gpctsmbxj() {
  var path = "/Users/<USER>/projects/react-native/payment-module/src/components/contact-tab/types.ts";
  var hash = "487e3e9fe056f7820d025cafa22ee90813308ed2";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/projects/react-native/payment-module/src/components/contact-tab/types.ts",
    statementMap: {
      "0": {
        start: {
          line: 3,
          column: 0
        },
        end: {
          line: 5,
          column: 3
        }
      }
    },
    fnMap: {},
    branchMap: {},
    s: {
      "0": 0
    },
    f: {},
    b: {},
    inputSourceMap: {
      version: 3,
      file: undefined,
      names: [],
      sourceRoot: undefined,
      sources: ["/Users/<USER>/projects/react-native/payment-module/src/components/contact-tab/types.ts"],
      sourcesContent: ["import {ViewStyle} from 'react-native';\n\nimport {IBillContact} from '../../domain/entities/IBillContact';\n\nexport type ContabTabProps = {\n  style?: ViewStyle;\n  contactList: IBillContact[];\n  recentContact: IBillContact[];\n  onSelect?: (contactInfo?: IBillContact) => void;\n};\n"],
      mappings: "",
      ignoreList: []
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "487e3e9fe056f7820d025cafa22ee90813308ed2"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_2gpctsmbxj = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_2gpctsmbxj();
cov_2gpctsmbxj().s[0]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6W10sInNvdXJjZXMiOlsiL1VzZXJzL3NvbnNhbXNldC9wcm9qZWN0cy9yZWFjdC1uYXRpdmUvcGF5bWVudC1tb2R1bGUvc3JjL2NvbXBvbmVudHMvY29udGFjdC10YWIvdHlwZXMudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHtWaWV3U3R5bGV9IGZyb20gJ3JlYWN0LW5hdGl2ZSc7XG5cbmltcG9ydCB7SUJpbGxDb250YWN0fSBmcm9tICcuLi8uLi9kb21haW4vZW50aXRpZXMvSUJpbGxDb250YWN0JztcblxuZXhwb3J0IHR5cGUgQ29udGFiVGFiUHJvcHMgPSB7XG4gIHN0eWxlPzogVmlld1N0eWxlO1xuICBjb250YWN0TGlzdDogSUJpbGxDb250YWN0W107XG4gIHJlY2VudENvbnRhY3Q6IElCaWxsQ29udGFjdFtdO1xuICBvblNlbGVjdD86IChjb250YWN0SW5mbz86IElCaWxsQ29udGFjdCkgPT4gdm9pZDtcbn07XG4iXSwibWFwcGluZ3MiOiIiLCJpZ25vcmVMaXN0IjpbXX0=