{"version": 3, "names": ["cov_xtt3jw74c", "actualCoverage", "ExcecutionHandler_1", "s", "require", "SaveBillContactUseCase", "f", "repository", "_classCallCheck2", "default", "_createClass2", "key", "value", "_execute", "_asyncToGenerator2", "request", "_this", "ExecutionHandler", "execute", "saveBillContact", "_x", "apply", "arguments", "exports"], "sources": ["/Users/<USER>/projects/react-native/payment-module/src/domain/usecases/bill-contact/SaveBillContactUseCase.ts"], "sourcesContent": ["import {IBillContactRepository} from '../../repositories/IBillContactRepository';\nimport {SaveBillContactModel} from '../../entities/save-bill-contact/SaveBillContactModel';\nimport {ResultState} from '../../../core/ResultState';\nimport {ExecutionHandler} from '../../../utils/ExcecutionHandler';\nimport {SaveBillContactRequest} from '../../../data/models/save-bill-contact/SaveBillContactRequest';\nexport class SaveBillContactUseCase {\n  private repository: IBillContactRepository;\n\n  constructor(repository: IBillContactRepository) {\n    this.repository = repository;\n  }\n\n  public async execute(request: SaveBillContactRequest): Promise<ResultState<SaveBillContactModel>> {\n    // call this.repository.saveBillContact(...)\n    return ExecutionHandler.execute(() => this.repository.saveBillContact(request));\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAUE;IAAAA,aAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,aAAA;;;;;;;;;;;;;;;;;;;;;AAPF,IAAAE,mBAAA;AAAA;AAAA,CAAAF,aAAA,GAAAG,CAAA,OAAAC,OAAA;AAAkE,IAErDC,sBAAsB;AAAA;AAAA,CAAAL,aAAA,GAAAG,CAAA;EAAA;EAAAH,aAAA,GAAAM,CAAA;EAGjC,SAAAD,uBAAYE,UAAkC;IAAA;IAAAP,aAAA,GAAAM,CAAA;IAAAN,aAAA,GAAAG,CAAA;IAAA,IAAAK,gBAAA,CAAAC,OAAA,QAAAJ,sBAAA;IAAA;IAAAL,aAAA,GAAAG,CAAA;IAC5C,IAAI,CAACI,UAAU,GAAGA,UAAU;EAC9B;EAAA;EAAAP,aAAA,GAAAG,CAAA;EAAC,WAAAO,aAAA,CAAAD,OAAA,EAAAJ,sBAAA;IAAAM,GAAA;IAAAC,KAAA;MAAA;MAAAZ,aAAA,GAAAM,CAAA;MAAA,IAAAO,QAAA;MAAA;MAAA,CAAAb,aAAA,GAAAG,CAAA,YAAAW,kBAAA,CAAAL,OAAA,EAEM,WAAcM,OAA+B;QAAA;QAAAf,aAAA,GAAAM,CAAA;QAAA,IAAAU,KAAA;QAAA;QAAA,CAAAhB,aAAA,GAAAG,CAAA;QAAA;QAAAH,aAAA,GAAAG,CAAA;QAElD,OAAOD,mBAAA,CAAAe,gBAAgB,CAACC,OAAO,CAAC;UAAA;UAAAlB,aAAA,GAAAM,CAAA;UAAAN,aAAA,GAAAG,CAAA;UAAA,OAAMa,KAAI,CAACT,UAAU,CAACY,eAAe,CAACJ,OAAO,CAAC;QAAA,EAAC;MACjF,CAAC;MAAA,SAHYG,OAAOA,CAAAE,EAAA;QAAA;QAAApB,aAAA,GAAAM,CAAA;QAAAN,aAAA,GAAAG,CAAA;QAAA,OAAAU,QAAA,CAAAQ,KAAA,OAAAC,SAAA;MAAA;MAAA;MAAAtB,aAAA,GAAAG,CAAA;MAAA,OAAPe,OAAO;IAAA;EAAA;AAAA;AAAA;AAAAlB,aAAA,GAAAG,CAAA;AAPtBoB,OAAA,CAAAlB,sBAAA,GAAAA,sBAAA", "ignoreList": []}