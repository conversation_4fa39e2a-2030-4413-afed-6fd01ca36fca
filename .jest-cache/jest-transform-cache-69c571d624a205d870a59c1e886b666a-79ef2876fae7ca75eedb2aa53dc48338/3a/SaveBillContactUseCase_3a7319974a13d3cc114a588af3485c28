9c2b8918038b60a038a980cf7c0d6efc
"use strict";

/* istanbul ignore next */
function cov_xtt3jw74c() {
  var path = "/Users/<USER>/projects/react-native/payment-module/src/domain/usecases/bill-contact/SaveBillContactUseCase.ts";
  var hash = "54914de4bbcf1f166f8dea736d670311ae6473df";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/projects/react-native/payment-module/src/domain/usecases/bill-contact/SaveBillContactUseCase.ts",
    statementMap: {
      "0": {
        start: {
          line: 3,
          column: 29
        },
        end: {
          line: 3,
          column: 84
        }
      },
      "1": {
        start: {
          line: 4,
          column: 25
        },
        end: {
          line: 4,
          column: 99
        }
      },
      "2": {
        start: {
          line: 5,
          column: 23
        },
        end: {
          line: 5,
          column: 95
        }
      },
      "3": {
        start: {
          line: 6,
          column: 20
        },
        end: {
          line: 6,
          column: 89
        }
      },
      "4": {
        start: {
          line: 7,
          column: 0
        },
        end: {
          line: 9,
          column: 3
        }
      },
      "5": {
        start: {
          line: 10,
          column: 0
        },
        end: {
          line: 10,
          column: 40
        }
      },
      "6": {
        start: {
          line: 11,
          column: 26
        },
        end: {
          line: 11,
          column: 69
        }
      },
      "7": {
        start: {
          line: 12,
          column: 29
        },
        end: {
          line: 32,
          column: 3
        }
      },
      "8": {
        start: {
          line: 14,
          column: 4
        },
        end: {
          line: 14,
          column: 64
        }
      },
      "9": {
        start: {
          line: 15,
          column: 4
        },
        end: {
          line: 15,
          column: 33
        }
      },
      "10": {
        start: {
          line: 17,
          column: 2
        },
        end: {
          line: 31,
          column: 6
        }
      },
      "11": {
        start: {
          line: 20,
          column: 21
        },
        end: {
          line: 25,
          column: 8
        }
      },
      "12": {
        start: {
          line: 21,
          column: 20
        },
        end: {
          line: 21,
          column: 24
        }
      },
      "13": {
        start: {
          line: 22,
          column: 8
        },
        end: {
          line: 24,
          column: 11
        }
      },
      "14": {
        start: {
          line: 23,
          column: 10
        },
        end: {
          line: 23,
          column: 59
        }
      },
      "15": {
        start: {
          line: 27,
          column: 8
        },
        end: {
          line: 27,
          column: 47
        }
      },
      "16": {
        start: {
          line: 29,
          column: 6
        },
        end: {
          line: 29,
          column: 21
        }
      },
      "17": {
        start: {
          line: 33,
          column: 0
        },
        end: {
          line: 33,
          column: 56
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 12,
            column: 29
          },
          end: {
            line: 12,
            column: 30
          }
        },
        loc: {
          start: {
            line: 12,
            column: 41
          },
          end: {
            line: 32,
            column: 1
          }
        },
        line: 12
      },
      "1": {
        name: "SaveBillContactUseCase",
        decl: {
          start: {
            line: 13,
            column: 11
          },
          end: {
            line: 13,
            column: 33
          }
        },
        loc: {
          start: {
            line: 13,
            column: 46
          },
          end: {
            line: 16,
            column: 3
          }
        },
        line: 13
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 19,
            column: 11
          },
          end: {
            line: 19,
            column: 12
          }
        },
        loc: {
          start: {
            line: 19,
            column: 23
          },
          end: {
            line: 30,
            column: 5
          }
        },
        line: 19
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 20,
            column: 53
          },
          end: {
            line: 20,
            column: 54
          }
        },
        loc: {
          start: {
            line: 20,
            column: 73
          },
          end: {
            line: 25,
            column: 7
          }
        },
        line: 20
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 22,
            column: 60
          },
          end: {
            line: 22,
            column: 61
          }
        },
        loc: {
          start: {
            line: 22,
            column: 72
          },
          end: {
            line: 24,
            column: 9
          }
        },
        line: 22
      },
      "5": {
        name: "execute",
        decl: {
          start: {
            line: 26,
            column: 15
          },
          end: {
            line: 26,
            column: 22
          }
        },
        loc: {
          start: {
            line: 26,
            column: 27
          },
          end: {
            line: 28,
            column: 7
          }
        },
        line: 26
      }
    },
    branchMap: {},
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0
    },
    b: {},
    inputSourceMap: {
      version: 3,
      file: undefined,
      names: ["ExcecutionHandler_1", "require", "SaveBillContactUseCase", "repository", "_classCallCheck2", "default", "_createClass2", "key", "value", "_execute", "_asyncToGenerator2", "request", "_this", "ExecutionHandler", "execute", "saveBillContact", "_x", "apply", "arguments", "exports"],
      sourceRoot: undefined,
      sources: ["/Users/<USER>/projects/react-native/payment-module/src/domain/usecases/bill-contact/SaveBillContactUseCase.ts"],
      sourcesContent: ["import {IBillContactRepository} from '../../repositories/IBillContactRepository';\nimport {SaveBillContactModel} from '../../entities/save-bill-contact/SaveBillContactModel';\nimport {ResultState} from '../../../core/ResultState';\nimport {ExecutionHandler} from '../../../utils/ExcecutionHandler';\nimport {SaveBillContactRequest} from '../../../data/models/save-bill-contact/SaveBillContactRequest';\nexport class SaveBillContactUseCase {\n  private repository: IBillContactRepository;\n\n  constructor(repository: IBillContactRepository) {\n    this.repository = repository;\n  }\n\n  public async execute(request: SaveBillContactRequest): Promise<ResultState<SaveBillContactModel>> {\n    // call this.repository.saveBillContact(...)\n    return ExecutionHandler.execute(() => this.repository.saveBillContact(request));\n  }\n}\n"],
      mappings: ";;;;;;;;;;AAGA,IAAAA,mBAAA,GAAAC,OAAA;AAAkE,IAErDC,sBAAsB;EAGjC,SAAAA,uBAAYC,UAAkC;IAAA,IAAAC,gBAAA,CAAAC,OAAA,QAAAH,sBAAA;IAC5C,IAAI,CAACC,UAAU,GAAGA,UAAU;EAC9B;EAAC,WAAAG,aAAA,CAAAD,OAAA,EAAAH,sBAAA;IAAAK,GAAA;IAAAC,KAAA;MAAA,IAAAC,QAAA,OAAAC,kBAAA,CAAAL,OAAA,EAEM,WAAcM,OAA+B;QAAA,IAAAC,KAAA;QAElD,OAAOZ,mBAAA,CAAAa,gBAAgB,CAACC,OAAO,CAAC;UAAA,OAAMF,KAAI,CAACT,UAAU,CAACY,eAAe,CAACJ,OAAO,CAAC;QAAA,EAAC;MACjF,CAAC;MAAA,SAHYG,OAAOA,CAAAE,EAAA;QAAA,OAAAP,QAAA,CAAAQ,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAPJ,OAAO;IAAA;EAAA;AAAA;AAPtBK,OAAA,CAAAjB,sBAAA,GAAAA,sBAAA",
      ignoreList: []
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "54914de4bbcf1f166f8dea736d670311ae6473df"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_xtt3jw74c = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_xtt3jw74c();
var _interopRequireDefault =
/* istanbul ignore next */
(cov_xtt3jw74c().s[0]++, require("@babel/runtime/helpers/interopRequireDefault"));
var _asyncToGenerator2 =
/* istanbul ignore next */
(cov_xtt3jw74c().s[1]++, _interopRequireDefault(require("@babel/runtime/helpers/asyncToGenerator")));
var _classCallCheck2 =
/* istanbul ignore next */
(cov_xtt3jw74c().s[2]++, _interopRequireDefault(require("@babel/runtime/helpers/classCallCheck")));
var _createClass2 =
/* istanbul ignore next */
(cov_xtt3jw74c().s[3]++, _interopRequireDefault(require("@babel/runtime/helpers/createClass")));
/* istanbul ignore next */
cov_xtt3jw74c().s[4]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_xtt3jw74c().s[5]++;
exports.SaveBillContactUseCase = void 0;
var ExcecutionHandler_1 =
/* istanbul ignore next */
(cov_xtt3jw74c().s[6]++, require("../../../utils/ExcecutionHandler"));
var SaveBillContactUseCase =
/* istanbul ignore next */
(cov_xtt3jw74c().s[7]++, function () {
  /* istanbul ignore next */
  cov_xtt3jw74c().f[0]++;
  function SaveBillContactUseCase(repository) {
    /* istanbul ignore next */
    cov_xtt3jw74c().f[1]++;
    cov_xtt3jw74c().s[8]++;
    (0, _classCallCheck2.default)(this, SaveBillContactUseCase);
    /* istanbul ignore next */
    cov_xtt3jw74c().s[9]++;
    this.repository = repository;
  }
  /* istanbul ignore next */
  cov_xtt3jw74c().s[10]++;
  return (0, _createClass2.default)(SaveBillContactUseCase, [{
    key: "execute",
    value: function () {
      /* istanbul ignore next */
      cov_xtt3jw74c().f[2]++;
      var _execute =
      /* istanbul ignore next */
      (cov_xtt3jw74c().s[11]++, (0, _asyncToGenerator2.default)(function* (request) {
        /* istanbul ignore next */
        cov_xtt3jw74c().f[3]++;
        var _this =
        /* istanbul ignore next */
        (cov_xtt3jw74c().s[12]++, this);
        /* istanbul ignore next */
        cov_xtt3jw74c().s[13]++;
        return ExcecutionHandler_1.ExecutionHandler.execute(function () {
          /* istanbul ignore next */
          cov_xtt3jw74c().f[4]++;
          cov_xtt3jw74c().s[14]++;
          return _this.repository.saveBillContact(request);
        });
      }));
      function execute(_x) {
        /* istanbul ignore next */
        cov_xtt3jw74c().f[5]++;
        cov_xtt3jw74c().s[15]++;
        return _execute.apply(this, arguments);
      }
      /* istanbul ignore next */
      cov_xtt3jw74c().s[16]++;
      return execute;
    }()
  }]);
}());
/* istanbul ignore next */
cov_xtt3jw74c().s[17]++;
exports.SaveBillContactUseCase = SaveBillContactUseCase;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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