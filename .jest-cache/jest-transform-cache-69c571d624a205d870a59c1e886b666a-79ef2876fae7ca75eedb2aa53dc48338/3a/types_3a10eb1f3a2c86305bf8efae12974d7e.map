{"version": 3, "names": [], "sources": ["/Users/<USER>/projects/react-native/payment-module/src/components/contact-tab/types.ts"], "sourcesContent": ["import {ViewStyle} from 'react-native';\n\nimport {IBillContact} from '../../domain/entities/IBillContact';\n\nexport type ContabTabProps = {\n  style?: ViewStyle;\n  contactList: IBillContact[];\n  recentContact: IBillContact[];\n  onSelect?: (contactInfo?: IBillContact) => void;\n};\n"], "mappings": "", "ignoreList": []}