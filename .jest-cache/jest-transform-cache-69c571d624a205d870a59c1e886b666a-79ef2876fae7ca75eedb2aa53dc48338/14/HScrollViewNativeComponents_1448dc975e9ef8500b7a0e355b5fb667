bee2f69e5908457caac8e711caf6723e
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.HScrollViewNativeComponent = exports.HScrollContentViewNativeComponent = void 0;
var _AndroidHorizontalScrollViewNativeComponent = _interopRequireDefault(require("../../../Libraries/Components/ScrollView/AndroidHorizontalScrollViewNativeComponent"));
var _ScrollContentViewNativeComponent = _interopRequireDefault(require("../../../Libraries/Components/ScrollView/ScrollContentViewNativeComponent"));
var _ScrollViewNativeComponent = _interopRequireDefault(require("../../../Libraries/Components/ScrollView/ScrollViewNativeComponent"));
var _Platform = _interopRequireDefault(require("../../../Libraries/Utilities/Platform"));
var _AndroidHorizontalScrollContentViewNativeComponent = _interopRequireDefault(require("../specs/components/AndroidHorizontalScrollContentViewNativeComponent"));
var HScrollViewNativeComponent = exports.HScrollViewNativeComponent = _Platform.default.OS === 'android' ? _AndroidHorizontalScrollViewNativeComponent.default : _ScrollViewNativeComponent.default;
var HScrollContentViewNativeComponent = exports.HScrollContentViewNativeComponent = _Platform.default.OS === 'android' ? _AndroidHorizontalScrollContentViewNativeComponent.default : _ScrollContentViewNativeComponent.default;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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