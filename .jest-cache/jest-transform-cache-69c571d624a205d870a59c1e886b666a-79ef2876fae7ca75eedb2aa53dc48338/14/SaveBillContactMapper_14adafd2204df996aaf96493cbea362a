1ac712ec7bfddbc494770835c4e59e88
"use strict";

/* istanbul ignore next */
function cov_i5v9es7hn() {
  var path = "/Users/<USER>/projects/react-native/payment-module/src/data/mappers/save-bill-contact/SaveBillContactMapper.ts";
  var hash = "e5f296cca602ae4b29003842d9e062ba6c45cdd8";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/projects/react-native/payment-module/src/data/mappers/save-bill-contact/SaveBillContactMapper.ts",
    statementMap: {
      "0": {
        start: {
          line: 3,
          column: 0
        },
        end: {
          line: 5,
          column: 3
        }
      },
      "1": {
        start: {
          line: 6,
          column: 0
        },
        end: {
          line: 6,
          column: 78
        }
      },
      "2": {
        start: {
          line: 7,
          column: 29
        },
        end: {
          line: 7,
          column: 103
        }
      },
      "3": {
        start: {
          line: 9,
          column: 2
        },
        end: {
          line: 9,
          column: 59
        }
      }
    },
    fnMap: {
      "0": {
        name: "mapSaveBillContactResponseToModel",
        decl: {
          start: {
            line: 8,
            column: 9
          },
          end: {
            line: 8,
            column: 42
          }
        },
        loc: {
          start: {
            line: 8,
            column: 53
          },
          end: {
            line: 10,
            column: 1
          }
        },
        line: 8
      }
    },
    branchMap: {},
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0
    },
    f: {
      "0": 0
    },
    b: {},
    inputSourceMap: {
      version: 3,
      file: undefined,
      names: ["exports", "mapSaveBillContactResponseToModel", "SaveBillContactModel_1", "require", "response", "SaveBillContactModel"],
      sourceRoot: undefined,
      sources: ["/Users/<USER>/projects/react-native/payment-module/src/data/mappers/save-bill-contact/SaveBillContactMapper.ts"],
      sourcesContent: ["import {SaveBillContactResponse} from '../../models/save-bill-contact/SaveBillContactResponse';\nimport {SaveBillContactModel} from '../../../domain/entities/save-bill-contact/SaveBillContactModel';\n\nexport function mapSaveBillContactResponseToModel(response: SaveBillContactResponse): SaveBillContactModel {\n  return new SaveBillContactModel();\n}\n"],
      mappings: ";;;;;AAGAA,OAAA,CAAAC,iCAAA,GAAAA,iCAAA;AAFA,IAAAC,sBAAA,GAAAC,OAAA;AAEA,SAAgBF,iCAAiCA,CAACG,QAAiC;EACjF,OAAO,IAAIF,sBAAA,CAAAG,oBAAoB,EAAE;AACnC",
      ignoreList: []
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "e5f296cca602ae4b29003842d9e062ba6c45cdd8"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_i5v9es7hn = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_i5v9es7hn();
cov_i5v9es7hn().s[0]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_i5v9es7hn().s[1]++;
exports.mapSaveBillContactResponseToModel = mapSaveBillContactResponseToModel;
var SaveBillContactModel_1 =
/* istanbul ignore next */
(cov_i5v9es7hn().s[2]++, require("../../../domain/entities/save-bill-contact/SaveBillContactModel"));
function mapSaveBillContactResponseToModel(response) {
  /* istanbul ignore next */
  cov_i5v9es7hn().f[0]++;
  cov_i5v9es7hn().s[3]++;
  return new SaveBillContactModel_1.SaveBillContactModel();
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJleHBvcnRzIiwibWFwU2F2ZUJpbGxDb250YWN0UmVzcG9uc2VUb01vZGVsIiwiU2F2ZUJpbGxDb250YWN0TW9kZWxfMSIsImNvdl9pNXY5ZXM3aG4iLCJzIiwicmVxdWlyZSIsInJlc3BvbnNlIiwiZiIsIlNhdmVCaWxsQ29udGFjdE1vZGVsIl0sInNvdXJjZXMiOlsiL1VzZXJzL3NvbnNhbXNldC9wcm9qZWN0cy9yZWFjdC1uYXRpdmUvcGF5bWVudC1tb2R1bGUvc3JjL2RhdGEvbWFwcGVycy9zYXZlLWJpbGwtY29udGFjdC9TYXZlQmlsbENvbnRhY3RNYXBwZXIudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHtTYXZlQmlsbENvbnRhY3RSZXNwb25zZX0gZnJvbSAnLi4vLi4vbW9kZWxzL3NhdmUtYmlsbC1jb250YWN0L1NhdmVCaWxsQ29udGFjdFJlc3BvbnNlJztcbmltcG9ydCB7U2F2ZUJpbGxDb250YWN0TW9kZWx9IGZyb20gJy4uLy4uLy4uL2RvbWFpbi9lbnRpdGllcy9zYXZlLWJpbGwtY29udGFjdC9TYXZlQmlsbENvbnRhY3RNb2RlbCc7XG5cbmV4cG9ydCBmdW5jdGlvbiBtYXBTYXZlQmlsbENvbnRhY3RSZXNwb25zZVRvTW9kZWwocmVzcG9uc2U6IFNhdmVCaWxsQ29udGFjdFJlc3BvbnNlKTogU2F2ZUJpbGxDb250YWN0TW9kZWwge1xuICByZXR1cm4gbmV3IFNhdmVCaWxsQ29udGFjdE1vZGVsKCk7XG59XG4iXSwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBR0FBLE9BQUEsQ0FBQUMsaUNBQUEsR0FBQUEsaUNBQUE7QUFGQSxJQUFBQyxzQkFBQTtBQUFBO0FBQUEsQ0FBQUMsYUFBQSxHQUFBQyxDQUFBLE9BQUFDLE9BQUE7QUFFQSxTQUFnQkosaUNBQWlDQSxDQUFDSyxRQUFpQztFQUFBO0VBQUFILGFBQUEsR0FBQUksQ0FBQTtFQUFBSixhQUFBLEdBQUFDLENBQUE7RUFDakYsT0FBTyxJQUFJRixzQkFBQSxDQUFBTSxvQkFBb0IsRUFBRTtBQUNuQyIsImlnbm9yZUxpc3QiOltdfQ==