{"version": 3, "names": ["_AndroidHorizontalScrollViewNativeComponent", "_interopRequireDefault", "require", "_ScrollContentViewNativeComponent", "_ScrollViewNativeComponent", "_Platform", "_AndroidHorizontalScrollContentViewNativeComponent", "HScrollViewNativeComponent", "exports", "Platform", "OS", "AndroidHorizontalScrollViewNativeComponent", "ScrollViewNativeComponent", "HScrollContentViewNativeComponent", "AndroidHorizontalScrollContentViewNativeComponent", "ScrollContentViewNativeComponent"], "sources": ["HScrollViewNativeComponents.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @flow strict-local\n * @format\n * @oncall react_native\n */\n\nimport type {ScrollViewNativeProps} from '../../../Libraries/Components/ScrollView/ScrollViewNativeComponentType';\nimport type {ViewProps} from '../../../Libraries/Components/View/ViewPropTypes';\nimport type {HostComponent} from '../../../Libraries/Renderer/shims/ReactNativeTypes';\n\nimport AndroidHorizontalScrollViewNativeComponent from '../../../Libraries/Components/ScrollView/AndroidHorizontalScrollViewNativeComponent';\nimport ScrollContentViewNativeComponent from '../../../Libraries/Components/ScrollView/ScrollContentViewNativeComponent';\nimport ScrollViewNativeComponent from '../../../Libraries/Components/ScrollView/ScrollViewNativeComponent';\nimport Platform from '../../../Libraries/Utilities/Platform';\nimport AndroidHorizontalScrollContentViewNativeComponent from '../specs/components/AndroidHorizontalScrollContentViewNativeComponent';\n\nexport const HScrollViewNativeComponent: HostComponent<ScrollViewNativeProps> =\n  Platform.OS === 'android'\n    ? AndroidHorizontalScrollViewNativeComponent\n    : ScrollViewNativeComponent;\n\nexport const HScrollContentViewNativeComponent: HostComponent<ViewProps> =\n  Platform.OS === 'android'\n    ? AndroidHorizontalScrollContentViewNativeComponent\n    : ScrollContentViewNativeComponent;\n"], "mappings": ";;;;;AAeA,IAAAA,2CAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,iCAAA,GAAAF,sBAAA,CAAAC,OAAA;AACA,IAAAE,0BAAA,GAAAH,sBAAA,CAAAC,OAAA;AACA,IAAAG,SAAA,GAAAJ,sBAAA,CAAAC,OAAA;AACA,IAAAI,kDAAA,GAAAL,sBAAA,CAAAC,OAAA;AAEO,IAAMK,0BAAgE,GAAAC,OAAA,CAAAD,0BAAA,GAC3EE,iBAAQ,CAACC,EAAE,KAAK,SAAS,GACrBC,mDAA0C,GAC1CC,kCAAyB;AAExB,IAAMC,iCAA2D,GAAAL,OAAA,CAAAK,iCAAA,GACtEJ,iBAAQ,CAACC,EAAE,KAAK,SAAS,GACrBI,0DAAiD,GACjDC,yCAAgC", "ignoreList": []}