5195b930dbbb1fefbf285da5228a1fb8
'use strict';
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.getShadowNodeWrapperFromRef = getShadowNodeWrapperFromRef;
var _findHostInstance = require("./platform-specific/findHostInstance");
var getInternalInstanceHandleFromPublicInstance;
function getShadowNodeWrapperFromRef(ref, hostInstance) {
  var _ref$getScrollRespond, _ref$__internalInstan;
  if (getInternalInstanceHandleFromPublicInstance === undefined) {
    try {
      var _require$getInternalI;
      getInternalInstanceHandleFromPublicInstance = (_require$getInternalI = require('react-native/Libraries/ReactNative/ReactFabricPublicInstance/ReactFabricPublicInstance').getInternalInstanceHandleFromPublicInstance) != null ? _require$getInternalI : function (_ref) {
        return _ref._internalInstanceHandle;
      };
    } catch (e) {
      getInternalInstanceHandleFromPublicInstance = function getInternalInstanceHandleFromPublicInstance(_ref) {
        return _ref._internalInstanceHandle;
      };
    }
  }
  var scrollViewRef = ref == null || ref.getScrollResponder == null || (_ref$getScrollRespond = ref.getScrollResponder()) == null || _ref$getScrollRespond.getNativeScrollRef == null ? void 0 : _ref$getScrollRespond.getNativeScrollRef();
  var otherScrollViewRef = ref == null || ref.getNativeScrollRef == null ? void 0 : ref.getNativeScrollRef();
  var textInputRef = ref == null || (_ref$__internalInstan = ref.__internalInstanceHandle) == null || (_ref$__internalInstan = _ref$__internalInstan.stateNode) == null ? void 0 : _ref$__internalInstan.node;
  var resolvedRef;
  if (scrollViewRef) {
    resolvedRef = scrollViewRef.__internalInstanceHandle.stateNode.node;
  } else if (otherScrollViewRef) {
    resolvedRef = otherScrollViewRef.__internalInstanceHandle.stateNode.node;
  } else if (textInputRef) {
    resolvedRef = textInputRef;
  } else {
    var instance = hostInstance != null ? hostInstance : (0, _findHostInstance.findHostInstance)(ref);
    resolvedRef = getInternalInstanceHandleFromPublicInstance(instance).stateNode.node;
  }
  return resolvedRef;
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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