458eef2d05d7f84e20b11e91ae3be3e0
'use strict';

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.FadeOutData = exports.FadeOut = exports.FadeInData = exports.FadeIn = void 0;
var _animationParser = require("../animationParser.js");
var DEFAULT_FADE_TIME = 0.3;
var FadeInData = exports.FadeInData = {
  FadeIn: {
    name: 'FadeIn',
    style: {
      0: {
        opacity: 0
      },
      100: {
        opacity: 1
      }
    },
    duration: DEFAULT_FADE_TIME
  },
  FadeInRight: {
    name: 'FadeInRight',
    style: {
      0: {
        opacity: 0,
        transform: [{
          translateX: '25px'
        }]
      },
      100: {
        opacity: 1,
        transform: [{
          translateX: '0px'
        }]
      }
    },
    duration: DEFAULT_FADE_TIME
  },
  FadeInLeft: {
    name: 'FadeInLeft',
    style: {
      0: {
        opacity: 0,
        transform: [{
          translateX: '-25px'
        }]
      },
      100: {
        opacity: 1,
        transform: [{
          translateX: '0px'
        }]
      }
    },
    duration: DEFAULT_FADE_TIME
  },
  FadeInUp: {
    name: 'FadeInUp',
    style: {
      0: {
        opacity: 0,
        transform: [{
          translateY: '-25px'
        }]
      },
      100: {
        opacity: 1,
        transform: [{
          translateY: '0px'
        }]
      }
    },
    duration: DEFAULT_FADE_TIME
  },
  FadeInDown: {
    name: 'FadeInDown',
    style: {
      0: {
        opacity: 0,
        transform: [{
          translateY: '25px'
        }]
      },
      100: {
        opacity: 1,
        transform: [{
          translateY: '0px'
        }]
      }
    },
    duration: DEFAULT_FADE_TIME
  }
};
var FadeOutData = exports.FadeOutData = {
  FadeOut: {
    name: 'FadeOut',
    style: {
      0: {
        opacity: 1
      },
      100: {
        opacity: 0
      }
    },
    duration: DEFAULT_FADE_TIME
  },
  FadeOutRight: {
    name: 'FadeOutRight',
    style: {
      0: {
        opacity: 1,
        transform: [{
          translateX: '0px'
        }]
      },
      100: {
        opacity: 0,
        transform: [{
          translateX: '25px'
        }]
      }
    },
    duration: DEFAULT_FADE_TIME
  },
  FadeOutLeft: {
    name: 'FadeOutLeft',
    style: {
      0: {
        opacity: 1,
        transform: [{
          translateX: '0px'
        }]
      },
      100: {
        opacity: 0,
        transform: [{
          translateX: '-25px'
        }]
      }
    },
    duration: DEFAULT_FADE_TIME
  },
  FadeOutUp: {
    name: 'FadeOutUp',
    style: {
      0: {
        opacity: 1,
        transform: [{
          translateY: '0px'
        }]
      },
      100: {
        opacity: 0,
        transform: [{
          translateY: '-25px'
        }]
      }
    },
    duration: DEFAULT_FADE_TIME
  },
  FadeOutDown: {
    name: 'FadeOutDown',
    style: {
      0: {
        opacity: 1,
        transform: [{
          translateY: '0px'
        }]
      },
      100: {
        opacity: 0,
        transform: [{
          translateY: '25px'
        }]
      }
    },
    duration: DEFAULT_FADE_TIME
  }
};
var FadeIn = exports.FadeIn = {
  FadeIn: {
    style: (0, _animationParser.convertAnimationObjectToKeyframes)(FadeInData.FadeIn),
    duration: FadeInData.FadeIn.duration
  },
  FadeInRight: {
    style: (0, _animationParser.convertAnimationObjectToKeyframes)(FadeInData.FadeInRight),
    duration: FadeInData.FadeInRight.duration
  },
  FadeInLeft: {
    style: (0, _animationParser.convertAnimationObjectToKeyframes)(FadeInData.FadeInLeft),
    duration: FadeInData.FadeInLeft.duration
  },
  FadeInUp: {
    style: (0, _animationParser.convertAnimationObjectToKeyframes)(FadeInData.FadeInUp),
    duration: FadeInData.FadeInUp.duration
  },
  FadeInDown: {
    style: (0, _animationParser.convertAnimationObjectToKeyframes)(FadeInData.FadeInDown),
    duration: FadeInData.FadeInDown.duration
  }
};
var FadeOut = exports.FadeOut = {
  FadeOut: {
    style: (0, _animationParser.convertAnimationObjectToKeyframes)(FadeOutData.FadeOut),
    duration: FadeOutData.FadeOut.duration
  },
  FadeOutRight: {
    style: (0, _animationParser.convertAnimationObjectToKeyframes)(FadeOutData.FadeOutRight),
    duration: FadeOutData.FadeOutRight.duration
  },
  FadeOutLeft: {
    style: (0, _animationParser.convertAnimationObjectToKeyframes)(FadeOutData.FadeOutLeft),
    duration: FadeOutData.FadeOutLeft.duration
  },
  FadeOutUp: {
    style: (0, _animationParser.convertAnimationObjectToKeyframes)(FadeOutData.FadeOutUp),
    duration: FadeOutData.FadeOutUp.duration
  },
  FadeOutDown: {
    style: (0, _animationParser.convertAnimationObjectToKeyframes)(FadeOutData.FadeOutDown),
    duration: FadeOutData.FadeOutDown.duration
  }
};
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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