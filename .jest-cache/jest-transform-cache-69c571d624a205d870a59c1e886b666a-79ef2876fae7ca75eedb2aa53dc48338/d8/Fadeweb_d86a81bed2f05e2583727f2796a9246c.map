{"version": 3, "names": ["Object", "defineProperty", "exports", "value", "FadeOutData", "FadeOut", "FadeInData", "FadeIn", "_animation<PERSON><PERSON>er", "require", "DEFAULT_FADE_TIME", "name", "style", "opacity", "duration", "FadeInRight", "transform", "translateX", "FadeInLeft", "FadeInUp", "translateY", "FadeInDown", "FadeOutRight", "FadeOutLeft", "FadeOutUp", "FadeOutDown", "convertAnimationObjectToKeyframes"], "sources": ["../../../../../src/layoutReanimation/web/animation/Fade.web.ts"], "sourcesContent": [null], "mappings": "AAAA,YAAY;;AAAAA,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,KAAA;AAAA;AAAAD,OAAA,CAAAE,WAAA,GAAAF,OAAA,CAAAG,OAAA,GAAAH,OAAA,CAAAI,UAAA,GAAAJ,OAAA,CAAAK,MAAA;AACZ,IAAAC,gBAAA,GAAAC,OAAA;AAEA,IAAMC,iBAAiB,GAAG,GAAG;AAEtB,IAAMJ,UAAU,GAAAJ,OAAA,CAAAI,UAAA,GAAG;EACxBC,MAAM,EAAE;IACNI,IAAI,EAAE,QAAQ;IACdC,KAAK,EAAE;MACL,CAAC,EAAE;QAAEC,OAAO,EAAE;MAAE,CAAC;MACjB,GAAG,EAAE;QAAEA,OAAO,EAAE;MAAE;IACpB,CAAC;IACDC,QAAQ,EAAEJ;EACZ,CAAC;EAEDK,WAAW,EAAE;IACXJ,IAAI,EAAE,aAAa;IACnBC,KAAK,EAAE;MACL,CAAC,EAAE;QACDC,OAAO,EAAE,CAAC;QACVG,SAAS,EAAE,CAAC;UAAEC,UAAU,EAAE;QAAO,CAAC;MACpC,CAAC;MACD,GAAG,EAAE;QACHJ,OAAO,EAAE,CAAC;QACVG,SAAS,EAAE,CAAC;UAAEC,UAAU,EAAE;QAAM,CAAC;MACnC;IACF,CAAC;IACDH,QAAQ,EAAEJ;EACZ,CAAC;EAEDQ,UAAU,EAAE;IACVP,IAAI,EAAE,YAAY;IAClBC,KAAK,EAAE;MACL,CAAC,EAAE;QACDC,OAAO,EAAE,CAAC;QACVG,SAAS,EAAE,CAAC;UAAEC,UAAU,EAAE;QAAQ,CAAC;MACrC,CAAC;MACD,GAAG,EAAE;QACHJ,OAAO,EAAE,CAAC;QACVG,SAAS,EAAE,CAAC;UAAEC,UAAU,EAAE;QAAM,CAAC;MACnC;IACF,CAAC;IACDH,QAAQ,EAAEJ;EACZ,CAAC;EAEDS,QAAQ,EAAE;IACRR,IAAI,EAAE,UAAU;IAChBC,KAAK,EAAE;MACL,CAAC,EAAE;QACDC,OAAO,EAAE,CAAC;QACVG,SAAS,EAAE,CAAC;UAAEI,UAAU,EAAE;QAAQ,CAAC;MACrC,CAAC;MACD,GAAG,EAAE;QACHP,OAAO,EAAE,CAAC;QACVG,SAAS,EAAE,CAAC;UAAEI,UAAU,EAAE;QAAM,CAAC;MACnC;IACF,CAAC;IACDN,QAAQ,EAAEJ;EACZ,CAAC;EAEDW,UAAU,EAAE;IACVV,IAAI,EAAE,YAAY;IAClBC,KAAK,EAAE;MACL,CAAC,EAAE;QACDC,OAAO,EAAE,CAAC;QACVG,SAAS,EAAE,CAAC;UAAEI,UAAU,EAAE;QAAO,CAAC;MACpC,CAAC;MACD,GAAG,EAAE;QACHP,OAAO,EAAE,CAAC;QACVG,SAAS,EAAE,CAAC;UAAEI,UAAU,EAAE;QAAM,CAAC;MACnC;IACF,CAAC;IACDN,QAAQ,EAAEJ;EACZ;AACF,CAAC;AAEM,IAAMN,WAAW,GAAAF,OAAA,CAAAE,WAAA,GAAG;EACzBC,OAAO,EAAE;IACPM,IAAI,EAAE,SAAS;IACfC,KAAK,EAAE;MACL,CAAC,EAAE;QAAEC,OAAO,EAAE;MAAE,CAAC;MACjB,GAAG,EAAE;QAAEA,OAAO,EAAE;MAAE;IACpB,CAAC;IACDC,QAAQ,EAAEJ;EACZ,CAAC;EAEDY,YAAY,EAAE;IACZX,IAAI,EAAE,cAAc;IACpBC,KAAK,EAAE;MACL,CAAC,EAAE;QACDC,OAAO,EAAE,CAAC;QACVG,SAAS,EAAE,CAAC;UAAEC,UAAU,EAAE;QAAM,CAAC;MACnC,CAAC;MACD,GAAG,EAAE;QACHJ,OAAO,EAAE,CAAC;QACVG,SAAS,EAAE,CAAC;UAAEC,UAAU,EAAE;QAAO,CAAC;MACpC;IACF,CAAC;IACDH,QAAQ,EAAEJ;EACZ,CAAC;EAEDa,WAAW,EAAE;IACXZ,IAAI,EAAE,aAAa;IACnBC,KAAK,EAAE;MACL,CAAC,EAAE;QACDC,OAAO,EAAE,CAAC;QACVG,SAAS,EAAE,CAAC;UAAEC,UAAU,EAAE;QAAM,CAAC;MACnC,CAAC;MACD,GAAG,EAAE;QACHJ,OAAO,EAAE,CAAC;QACVG,SAAS,EAAE,CAAC;UAAEC,UAAU,EAAE;QAAQ,CAAC;MACrC;IACF,CAAC;IACDH,QAAQ,EAAEJ;EACZ,CAAC;EAEDc,SAAS,EAAE;IACTb,IAAI,EAAE,WAAW;IACjBC,KAAK,EAAE;MACL,CAAC,EAAE;QACDC,OAAO,EAAE,CAAC;QACVG,SAAS,EAAE,CAAC;UAAEI,UAAU,EAAE;QAAM,CAAC;MACnC,CAAC;MACD,GAAG,EAAE;QACHP,OAAO,EAAE,CAAC;QACVG,SAAS,EAAE,CAAC;UAAEI,UAAU,EAAE;QAAQ,CAAC;MACrC;IACF,CAAC;IACDN,QAAQ,EAAEJ;EACZ,CAAC;EAEDe,WAAW,EAAE;IACXd,IAAI,EAAE,aAAa;IACnBC,KAAK,EAAE;MACL,CAAC,EAAE;QACDC,OAAO,EAAE,CAAC;QACVG,SAAS,EAAE,CAAC;UAAEI,UAAU,EAAE;QAAM,CAAC;MACnC,CAAC;MACD,GAAG,EAAE;QACHP,OAAO,EAAE,CAAC;QACVG,SAAS,EAAE,CAAC;UAAEI,UAAU,EAAE;QAAO,CAAC;MACpC;IACF,CAAC;IACDN,QAAQ,EAAEJ;EACZ;AACF,CAAC;AAEM,IAAMH,MAAM,GAAAL,OAAA,CAAAK,MAAA,GAAG;EACpBA,MAAM,EAAE;IACNK,KAAK,EAAE,IAAAc,kDAAiC,EAACpB,UAAU,CAACC,MAAM,CAAC;IAC3DO,QAAQ,EAAER,UAAU,CAACC,MAAM,CAACO;EAC9B,CAAC;EACDC,WAAW,EAAE;IACXH,KAAK,EAAE,IAAAc,kDAAiC,EAACpB,UAAU,CAACS,WAAW,CAAC;IAChED,QAAQ,EAAER,UAAU,CAACS,WAAW,CAACD;EACnC,CAAC;EACDI,UAAU,EAAE;IACVN,KAAK,EAAE,IAAAc,kDAAiC,EAACpB,UAAU,CAACY,UAAU,CAAC;IAC/DJ,QAAQ,EAAER,UAAU,CAACY,UAAU,CAACJ;EAClC,CAAC;EACDK,QAAQ,EAAE;IACRP,KAAK,EAAE,IAAAc,kDAAiC,EAACpB,UAAU,CAACa,QAAQ,CAAC;IAC7DL,QAAQ,EAAER,UAAU,CAACa,QAAQ,CAACL;EAChC,CAAC;EACDO,UAAU,EAAE;IACVT,KAAK,EAAE,IAAAc,kDAAiC,EAACpB,UAAU,CAACe,UAAU,CAAC;IAC/DP,QAAQ,EAAER,UAAU,CAACe,UAAU,CAACP;EAClC;AACF,CAAC;AAEM,IAAMT,OAAO,GAAAH,OAAA,CAAAG,OAAA,GAAG;EACrBA,OAAO,EAAE;IACPO,KAAK,EAAE,IAAAc,kDAAiC,EAACtB,WAAW,CAACC,OAAO,CAAC;IAC7DS,QAAQ,EAAEV,WAAW,CAACC,OAAO,CAACS;EAChC,CAAC;EACDQ,YAAY,EAAE;IACZV,KAAK,EAAE,IAAAc,kDAAiC,EAACtB,WAAW,CAACkB,YAAY,CAAC;IAClER,QAAQ,EAAEV,WAAW,CAACkB,YAAY,CAACR;EACrC,CAAC;EACDS,WAAW,EAAE;IACXX,KAAK,EAAE,IAAAc,kDAAiC,EAACtB,WAAW,CAACmB,WAAW,CAAC;IACjET,QAAQ,EAAEV,WAAW,CAACmB,WAAW,CAACT;EACpC,CAAC;EACDU,SAAS,EAAE;IACTZ,KAAK,EAAE,IAAAc,kDAAiC,EAACtB,WAAW,CAACoB,SAAS,CAAC;IAC/DV,QAAQ,EAAEV,WAAW,CAACoB,SAAS,CAACV;EAClC,CAAC;EACDW,WAAW,EAAE;IACXb,KAAK,EAAE,IAAAc,kDAAiC,EAACtB,WAAW,CAACqB,WAAW,CAAC;IACjEX,QAAQ,EAAEV,WAAW,CAACqB,WAAW,CAACX;EACpC;AACF,CAAC", "ignoreList": []}