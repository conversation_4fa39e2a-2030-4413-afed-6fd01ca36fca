73c0342d9fd553b997e93cdb53650ef0
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.getUrlCacheBreaker = getUrlCacheBreaker;
exports.pickScale = pickScale;
exports.setUrlCacheBreaker = setUrlCacheBreaker;
var _PixelRatio = _interopRequireDefault(require("../Utilities/PixelRatio"));
var cacheBreaker;
var warnIfCacheBreakerUnset = true;
function pickScale(scales, deviceScale) {
  var requiredDeviceScale = deviceScale != null ? deviceScale : _PixelRatio.default.get();
  for (var i = 0; i < scales.length; i++) {
    if (scales[i] >= requiredDeviceScale) {
      return scales[i];
    }
  }
  return scales[scales.length - 1] || 1;
}
function setUrlCacheBreaker(appendage) {
  cacheBreaker = appendage;
}
function getUrlCacheBreaker() {
  if (cacheBreaker == null) {
    if (__DEV__ && warnIfCacheBreakerUnset) {
      warnIfCacheBreakerUnset = false;
      console.warn('AssetUtils.getUrlCacheBreaker: Cache breaker value is unset');
    }
    return '';
  }
  return cacheBreaker;
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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