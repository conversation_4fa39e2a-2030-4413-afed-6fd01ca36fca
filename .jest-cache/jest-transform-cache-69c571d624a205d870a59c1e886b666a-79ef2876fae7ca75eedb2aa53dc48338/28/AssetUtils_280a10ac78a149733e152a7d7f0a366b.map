{"version": 3, "names": ["_PixelRatio", "_interopRequireDefault", "require", "cacheBreaker", "warnIfCacheBreakerUnset", "pickScale", "scales", "deviceScale", "requiredDeviceScale", "PixelRatio", "get", "i", "length", "setUrlCacheBreaker", "appendage", "getUrlCacheBreaker", "__DEV__", "console", "warn"], "sources": ["AssetUtils.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @flow strict-local\n * @format\n */\n\nimport PixelRatio from '../Utilities/PixelRatio';\n\nlet cacheBreaker;\nlet warnIfCacheBreakerUnset = true;\n\nexport function pickScale(scales: Array<number>, deviceScale?: number): number {\n  const requiredDeviceScale = deviceScale ?? PixelRatio.get();\n\n  // Packager guarantees that `scales` array is sorted\n  for (let i = 0; i < scales.length; i++) {\n    if (scales[i] >= requiredDeviceScale) {\n      return scales[i];\n    }\n  }\n\n  // If nothing matches, device scale is larger than any available\n  // scales, so we return the biggest one. Unless the array is empty,\n  // in which case we default to 1\n  return scales[scales.length - 1] || 1;\n}\n\nexport function setUrlCacheBreaker(appendage: string) {\n  cacheBreaker = appendage;\n}\n\nexport function getUrlCacheBreaker(): string {\n  if (cacheBreaker == null) {\n    if (__DEV__ && warnIfCacheBreakerUnset) {\n      warnIfCacheBreakerUnset = false;\n      console.warn(\n        'AssetUtils.getUrlCacheBreaker: Cache breaker value is unset',\n      );\n    }\n    return '';\n  }\n  return cacheBreaker;\n}\n"], "mappings": ";;;;;;;AAUA,IAAAA,WAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA,IAAIC,YAAY;AAChB,IAAIC,uBAAuB,GAAG,IAAI;AAE3B,SAASC,SAASA,CAACC,MAAqB,EAAEC,WAAoB,EAAU;EAC7E,IAAMC,mBAAmB,GAAGD,WAAW,WAAXA,WAAW,GAAIE,mBAAU,CAACC,GAAG,CAAC,CAAC;EAG3D,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,MAAM,CAACM,MAAM,EAAED,CAAC,EAAE,EAAE;IACtC,IAAIL,MAAM,CAACK,CAAC,CAAC,IAAIH,mBAAmB,EAAE;MACpC,OAAOF,MAAM,CAACK,CAAC,CAAC;IAClB;EACF;EAKA,OAAOL,MAAM,CAACA,MAAM,CAACM,MAAM,GAAG,CAAC,CAAC,IAAI,CAAC;AACvC;AAEO,SAASC,kBAAkBA,CAACC,SAAiB,EAAE;EACpDX,YAAY,GAAGW,SAAS;AAC1B;AAEO,SAASC,kBAAkBA,CAAA,EAAW;EAC3C,IAAIZ,YAAY,IAAI,IAAI,EAAE;IACxB,IAAIa,OAAO,IAAIZ,uBAAuB,EAAE;MACtCA,uBAAuB,GAAG,KAAK;MAC/Ba,OAAO,CAACC,IAAI,CACV,6DACF,CAAC;IACH;IACA,OAAO,EAAE;EACX;EACA,OAAOf,YAAY;AACrB", "ignoreList": []}