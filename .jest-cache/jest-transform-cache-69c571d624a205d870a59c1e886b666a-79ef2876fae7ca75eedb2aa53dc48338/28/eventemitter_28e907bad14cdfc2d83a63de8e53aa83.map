{"version": 3, "names": ["cov_u2cdz3st5", "actualCoverage", "EventEmitter", "s", "f", "_classCallCheck2", "default", "events", "_createClass2", "key", "value", "on", "event", "callback", "_this", "b", "push", "filter", "cb", "emit", "data", "for<PERSON>ach", "exports", "eventEmitter"], "sources": ["/Users/<USER>/projects/react-native/payment-module/src/presentation/payment-home/components/bill-list/event-emitter.ts"], "sourcesContent": ["import {SafeAny} from '../../../../commons/Constants';\n\ntype EventCallback = (data: SafeAny) => void;\n\nclass EventEmitter {\n  private events: {[key: string]: EventCallback[]} = {};\n\n  on(event: string, callback: EventCallback) {\n    if (!this.events[event]) {\n      this.events[event] = [];\n    }\n    this.events[event].push(callback);\n\n    // Return unsubscribe function\n    return () => {\n      this.events[event] = this.events[event].filter(cb => cb !== callback);\n    };\n  }\n\n  emit(event: string, data: SafeAny) {\n    if (this.events[event]) {\n      this.events[event].forEach(callback => callback(data));\n    }\n  }\n}\n\nexport const eventEmitter = new EventEmitter();\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAKuD;IAAAA,aAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,aAAA;;;;;;;;;;;;;;;;;;IADjDE,YAAY;AAAA;AAAA,CAAAF,aAAA,GAAAG,CAAA;EAAA;EAAAH,aAAA,GAAAI,CAAA;EAAA,SAAAF,aAAA;IAAA;IAAAF,aAAA,GAAAI,CAAA;IAAAJ,aAAA,GAAAG,CAAA;IAAA,IAAAE,gBAAA,CAAAC,OAAA,QAAAJ,YAAA;IAAA;IAAAF,aAAA,GAAAG,CAAA;IAAA,KACRI,MAAM,GAAqC,EAAE;EAAA;EAAA;EAAAP,aAAA,GAAAG,CAAA;EAAA,WAAAK,aAAA,CAAAF,OAAA,EAAAJ,YAAA;IAAAO,GAAA;IAAAC,KAAA,EAErD,SAAAC,EAAEA,CAACC,KAAa,EAAEC,QAAuB;MAAA;MAAAb,aAAA,GAAAI,CAAA;MAAA,IAAAU,KAAA;MAAA;MAAA,CAAAd,aAAA,GAAAG,CAAA;MAAA;MAAAH,aAAA,GAAAG,CAAA;MACvC,IAAI,CAAC,IAAI,CAACI,MAAM,CAACK,KAAK,CAAC,EAAE;QAAA;QAAAZ,aAAA,GAAAe,CAAA;QAAAf,aAAA,GAAAG,CAAA;QACvB,IAAI,CAACI,MAAM,CAACK,KAAK,CAAC,GAAG,EAAE;MACzB;MAAA;MAAA;QAAAZ,aAAA,GAAAe,CAAA;MAAA;MAAAf,aAAA,GAAAG,CAAA;MACA,IAAI,CAACI,MAAM,CAACK,KAAK,CAAC,CAACI,IAAI,CAACH,QAAQ,CAAC;MAAA;MAAAb,aAAA,GAAAG,CAAA;MAGjC,OAAO,YAAK;QAAA;QAAAH,aAAA,GAAAI,CAAA;QAAAJ,aAAA,GAAAG,CAAA;QACVW,KAAI,CAACP,MAAM,CAACK,KAAK,CAAC,GAAGE,KAAI,CAACP,MAAM,CAACK,KAAK,CAAC,CAACK,MAAM,CAAC,UAAAC,EAAE;UAAA;UAAAlB,aAAA,GAAAI,CAAA;UAAAJ,aAAA,GAAAG,CAAA;UAAA,OAAIe,EAAE,KAAKL,QAAQ;QAAA,EAAC;MACvE,CAAC;IACH;EAAC;IAAAJ,GAAA;IAAAC,KAAA,EAED,SAAAS,IAAIA,CAACP,KAAa,EAAEQ,IAAa;MAAA;MAAApB,aAAA,GAAAI,CAAA;MAAAJ,aAAA,GAAAG,CAAA;MAC/B,IAAI,IAAI,CAACI,MAAM,CAACK,KAAK,CAAC,EAAE;QAAA;QAAAZ,aAAA,GAAAe,CAAA;QAAAf,aAAA,GAAAG,CAAA;QACtB,IAAI,CAACI,MAAM,CAACK,KAAK,CAAC,CAACS,OAAO,CAAC,UAAAR,QAAQ;UAAA;UAAAb,aAAA,GAAAI,CAAA;UAAAJ,aAAA,GAAAG,CAAA;UAAA,OAAIU,QAAQ,CAACO,IAAI,CAAC;QAAA,EAAC;MACxD;MAAA;MAAA;QAAApB,aAAA,GAAAe,CAAA;MAAA;IACF;EAAC;AAAA;AAAA;AAAAf,aAAA,GAAAG,CAAA;AAGUmB,OAAA,CAAAC,YAAY,GAAG,IAAIrB,YAAY,EAAE", "ignoreList": []}