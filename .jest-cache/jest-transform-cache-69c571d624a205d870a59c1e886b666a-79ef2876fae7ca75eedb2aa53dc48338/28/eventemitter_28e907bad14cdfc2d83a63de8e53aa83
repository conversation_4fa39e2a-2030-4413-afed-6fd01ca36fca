ebde186bae7646bd0148027821a00411
"use strict";

/* istanbul ignore next */
function cov_u2cdz3st5() {
  var path = "/Users/<USER>/projects/react-native/payment-module/src/presentation/payment-home/components/bill-list/event-emitter.ts";
  var hash = "4e88e4b126d9c7a10f039791c78790330790291b";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/projects/react-native/payment-module/src/presentation/payment-home/components/bill-list/event-emitter.ts",
    statementMap: {
      "0": {
        start: {
          line: 3,
          column: 29
        },
        end: {
          line: 3,
          column: 84
        }
      },
      "1": {
        start: {
          line: 4,
          column: 23
        },
        end: {
          line: 4,
          column: 95
        }
      },
      "2": {
        start: {
          line: 5,
          column: 20
        },
        end: {
          line: 5,
          column: 89
        }
      },
      "3": {
        start: {
          line: 6,
          column: 0
        },
        end: {
          line: 8,
          column: 3
        }
      },
      "4": {
        start: {
          line: 9,
          column: 0
        },
        end: {
          line: 9,
          column: 30
        }
      },
      "5": {
        start: {
          line: 10,
          column: 19
        },
        end: {
          line: 39,
          column: 3
        }
      },
      "6": {
        start: {
          line: 12,
          column: 4
        },
        end: {
          line: 12,
          column: 54
        }
      },
      "7": {
        start: {
          line: 13,
          column: 4
        },
        end: {
          line: 13,
          column: 21
        }
      },
      "8": {
        start: {
          line: 15,
          column: 2
        },
        end: {
          line: 38,
          column: 6
        }
      },
      "9": {
        start: {
          line: 18,
          column: 18
        },
        end: {
          line: 18,
          column: 22
        }
      },
      "10": {
        start: {
          line: 19,
          column: 6
        },
        end: {
          line: 21,
          column: 7
        }
      },
      "11": {
        start: {
          line: 20,
          column: 8
        },
        end: {
          line: 20,
          column: 32
        }
      },
      "12": {
        start: {
          line: 22,
          column: 6
        },
        end: {
          line: 22,
          column: 40
        }
      },
      "13": {
        start: {
          line: 23,
          column: 6
        },
        end: {
          line: 27,
          column: 8
        }
      },
      "14": {
        start: {
          line: 24,
          column: 8
        },
        end: {
          line: 26,
          column: 11
        }
      },
      "15": {
        start: {
          line: 25,
          column: 10
        },
        end: {
          line: 25,
          column: 33
        }
      },
      "16": {
        start: {
          line: 32,
          column: 6
        },
        end: {
          line: 36,
          column: 7
        }
      },
      "17": {
        start: {
          line: 33,
          column: 8
        },
        end: {
          line: 35,
          column: 11
        }
      },
      "18": {
        start: {
          line: 34,
          column: 10
        },
        end: {
          line: 34,
          column: 32
        }
      },
      "19": {
        start: {
          line: 40,
          column: 0
        },
        end: {
          line: 40,
          column: 42
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 10,
            column: 19
          },
          end: {
            line: 10,
            column: 20
          }
        },
        loc: {
          start: {
            line: 10,
            column: 31
          },
          end: {
            line: 39,
            column: 1
          }
        },
        line: 10
      },
      "1": {
        name: "EventEmitter",
        decl: {
          start: {
            line: 11,
            column: 11
          },
          end: {
            line: 11,
            column: 23
          }
        },
        loc: {
          start: {
            line: 11,
            column: 26
          },
          end: {
            line: 14,
            column: 3
          }
        },
        line: 11
      },
      "2": {
        name: "on",
        decl: {
          start: {
            line: 17,
            column: 20
          },
          end: {
            line: 17,
            column: 22
          }
        },
        loc: {
          start: {
            line: 17,
            column: 40
          },
          end: {
            line: 28,
            column: 5
          }
        },
        line: 17
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 23,
            column: 13
          },
          end: {
            line: 23,
            column: 14
          }
        },
        loc: {
          start: {
            line: 23,
            column: 25
          },
          end: {
            line: 27,
            column: 7
          }
        },
        line: 23
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 24,
            column: 57
          },
          end: {
            line: 24,
            column: 58
          }
        },
        loc: {
          start: {
            line: 24,
            column: 71
          },
          end: {
            line: 26,
            column: 9
          }
        },
        line: 24
      },
      "5": {
        name: "emit",
        decl: {
          start: {
            line: 31,
            column: 20
          },
          end: {
            line: 31,
            column: 24
          }
        },
        loc: {
          start: {
            line: 31,
            column: 38
          },
          end: {
            line: 37,
            column: 5
          }
        },
        line: 31
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 33,
            column: 35
          },
          end: {
            line: 33,
            column: 36
          }
        },
        loc: {
          start: {
            line: 33,
            column: 55
          },
          end: {
            line: 35,
            column: 9
          }
        },
        line: 33
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 19,
            column: 6
          },
          end: {
            line: 21,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 19,
            column: 6
          },
          end: {
            line: 21,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 19
      },
      "1": {
        loc: {
          start: {
            line: 32,
            column: 6
          },
          end: {
            line: 36,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 32,
            column: 6
          },
          end: {
            line: 36,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 32
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0
    },
    b: {
      "0": [0, 0],
      "1": [0, 0]
    },
    inputSourceMap: {
      version: 3,
      file: undefined,
      names: ["EventEmitter", "_classCallCheck2", "default", "events", "_createClass2", "key", "value", "on", "event", "callback", "_this", "push", "filter", "cb", "emit", "data", "forEach", "exports", "eventEmitter"],
      sourceRoot: undefined,
      sources: ["/Users/<USER>/projects/react-native/payment-module/src/presentation/payment-home/components/bill-list/event-emitter.ts"],
      sourcesContent: ["import {SafeAny} from '../../../../commons/Constants';\n\ntype EventCallback = (data: SafeAny) => void;\n\nclass EventEmitter {\n  private events: {[key: string]: EventCallback[]} = {};\n\n  on(event: string, callback: EventCallback) {\n    if (!this.events[event]) {\n      this.events[event] = [];\n    }\n    this.events[event].push(callback);\n\n    // Return unsubscribe function\n    return () => {\n      this.events[event] = this.events[event].filter(cb => cb !== callback);\n    };\n  }\n\n  emit(event: string, data: SafeAny) {\n    if (this.events[event]) {\n      this.events[event].forEach(callback => callback(data));\n    }\n  }\n}\n\nexport const eventEmitter = new EventEmitter();\n"],
      mappings: ";;;;;;;;;IAIMA,YAAY;EAAA,SAAAA,aAAA;IAAA,IAAAC,gBAAA,CAAAC,OAAA,QAAAF,YAAA;IAAA,KACRG,MAAM,GAAqC,EAAE;EAAA;EAAA,WAAAC,aAAA,CAAAF,OAAA,EAAAF,YAAA;IAAAK,GAAA;IAAAC,KAAA,EAErD,SAAAC,EAAEA,CAACC,KAAa,EAAEC,QAAuB;MAAA,IAAAC,KAAA;MACvC,IAAI,CAAC,IAAI,CAACP,MAAM,CAACK,KAAK,CAAC,EAAE;QACvB,IAAI,CAACL,MAAM,CAACK,KAAK,CAAC,GAAG,EAAE;MACzB;MACA,IAAI,CAACL,MAAM,CAACK,KAAK,CAAC,CAACG,IAAI,CAACF,QAAQ,CAAC;MAGjC,OAAO,YAAK;QACVC,KAAI,CAACP,MAAM,CAACK,KAAK,CAAC,GAAGE,KAAI,CAACP,MAAM,CAACK,KAAK,CAAC,CAACI,MAAM,CAAC,UAAAC,EAAE;UAAA,OAAIA,EAAE,KAAKJ,QAAQ;QAAA,EAAC;MACvE,CAAC;IACH;EAAC;IAAAJ,GAAA;IAAAC,KAAA,EAED,SAAAQ,IAAIA,CAACN,KAAa,EAAEO,IAAa;MAC/B,IAAI,IAAI,CAACZ,MAAM,CAACK,KAAK,CAAC,EAAE;QACtB,IAAI,CAACL,MAAM,CAACK,KAAK,CAAC,CAACQ,OAAO,CAAC,UAAAP,QAAQ;UAAA,OAAIA,QAAQ,CAACM,IAAI,CAAC;QAAA,EAAC;MACxD;IACF;EAAC;AAAA;AAGUE,OAAA,CAAAC,YAAY,GAAG,IAAIlB,YAAY,EAAE",
      ignoreList: []
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "4e88e4b126d9c7a10f039791c78790330790291b"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_u2cdz3st5 = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_u2cdz3st5();
var _interopRequireDefault =
/* istanbul ignore next */
(cov_u2cdz3st5().s[0]++, require("@babel/runtime/helpers/interopRequireDefault"));
var _classCallCheck2 =
/* istanbul ignore next */
(cov_u2cdz3st5().s[1]++, _interopRequireDefault(require("@babel/runtime/helpers/classCallCheck")));
var _createClass2 =
/* istanbul ignore next */
(cov_u2cdz3st5().s[2]++, _interopRequireDefault(require("@babel/runtime/helpers/createClass")));
/* istanbul ignore next */
cov_u2cdz3st5().s[3]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_u2cdz3st5().s[4]++;
exports.eventEmitter = void 0;
var EventEmitter =
/* istanbul ignore next */
(cov_u2cdz3st5().s[5]++, function () {
  /* istanbul ignore next */
  cov_u2cdz3st5().f[0]++;
  function EventEmitter() {
    /* istanbul ignore next */
    cov_u2cdz3st5().f[1]++;
    cov_u2cdz3st5().s[6]++;
    (0, _classCallCheck2.default)(this, EventEmitter);
    /* istanbul ignore next */
    cov_u2cdz3st5().s[7]++;
    this.events = {};
  }
  /* istanbul ignore next */
  cov_u2cdz3st5().s[8]++;
  return (0, _createClass2.default)(EventEmitter, [{
    key: "on",
    value: function on(event, callback) {
      /* istanbul ignore next */
      cov_u2cdz3st5().f[2]++;
      var _this =
      /* istanbul ignore next */
      (cov_u2cdz3st5().s[9]++, this);
      /* istanbul ignore next */
      cov_u2cdz3st5().s[10]++;
      if (!this.events[event]) {
        /* istanbul ignore next */
        cov_u2cdz3st5().b[0][0]++;
        cov_u2cdz3st5().s[11]++;
        this.events[event] = [];
      } else
      /* istanbul ignore next */
      {
        cov_u2cdz3st5().b[0][1]++;
      }
      cov_u2cdz3st5().s[12]++;
      this.events[event].push(callback);
      /* istanbul ignore next */
      cov_u2cdz3st5().s[13]++;
      return function () {
        /* istanbul ignore next */
        cov_u2cdz3st5().f[3]++;
        cov_u2cdz3st5().s[14]++;
        _this.events[event] = _this.events[event].filter(function (cb) {
          /* istanbul ignore next */
          cov_u2cdz3st5().f[4]++;
          cov_u2cdz3st5().s[15]++;
          return cb !== callback;
        });
      };
    }
  }, {
    key: "emit",
    value: function emit(event, data) {
      /* istanbul ignore next */
      cov_u2cdz3st5().f[5]++;
      cov_u2cdz3st5().s[16]++;
      if (this.events[event]) {
        /* istanbul ignore next */
        cov_u2cdz3st5().b[1][0]++;
        cov_u2cdz3st5().s[17]++;
        this.events[event].forEach(function (callback) {
          /* istanbul ignore next */
          cov_u2cdz3st5().f[6]++;
          cov_u2cdz3st5().s[18]++;
          return callback(data);
        });
      } else
      /* istanbul ignore next */
      {
        cov_u2cdz3st5().b[1][1]++;
      }
    }
  }]);
}());
/* istanbul ignore next */
cov_u2cdz3st5().s[19]++;
exports.eventEmitter = new EventEmitter();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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