{"version": 3, "names": ["Object", "defineProperty", "exports", "value", "addHTMLMutationObserver", "areDOMRectsEqual", "configureWebLayoutAnimations", "insertWebAnimation", "scheduleAnimationCleanup", "_PlatformChecker", "require", "_componentStyle", "_config", "_index", "_errors", "PREDEFINED_WEB_ANIMATIONS_ID", "CUSTOM_WEB_ANIMATIONS_ID", "animationNameToIndex", "Map", "animationNameList", "isObserverSet", "isWindowAvailable", "document", "getElementById", "predefinedAnimationsStyleTag", "createElement", "id", "onload", "sheet", "logger", "error", "animationName", "Animations", "insertRule", "style", "customAnimationsStyleTag", "head", "append<PERSON><PERSON><PERSON>", "keyframe", "styleTag", "unshift", "set", "i", "length", "nextAnimationName", "nextAnimationIndex", "get", "undefined", "ReanimatedError", "removeWebAnimation", "animationRemoveCallback", "_styleTag$sheet", "currentAnimationIndex", "deleteRule", "splice", "delete", "timeoutScale", "frameDurationMs", "minimumFrames", "animationDuration", "timeoutValue", "Math", "max", "setTimeout", "reattachElementToAncestor", "child", "parent", "childSnapshot", "snapshots", "removedAfterAnimation", "setElementPosition", "originalOnAnimationEnd", "onanimationend", "event", "<PERSON><PERSON><PERSON><PERSON>", "call", "findDescendantWithExitingAnimation", "node", "root", "HTMLElement", "reanimatedDummy", "children", "Array", "from", "checkIfScreenWasChanged", "<PERSON><PERSON><PERSON><PERSON>", "_mutationTarget$react", "reactFiberKey", "key", "keys", "startsWith", "memoizedProps", "navigation", "observer", "MutationObserver", "mutationsList", "rootMutation", "target", "removedNodes", "observe", "body", "childList", "subtree", "r1", "r2", "x", "y", "width", "height"], "sources": ["../../../../src/layoutReanimation/web/domUtils.ts"], "sourcesContent": [null], "mappings": "AAAA,YAAY;;AAAAA,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,KAAA;AAAA;AAAAD,OAAA,CAAAE,uBAAA,GAAAA,uBAAA;AAAAF,OAAA,CAAAG,gBAAA,GAAAA,gBAAA;AAAAH,OAAA,CAAAI,4BAAA,GAAAA,4BAAA;AAAAJ,OAAA,CAAAK,kBAAA,GAAAA,kBAAA;AAAAL,OAAA,CAAAM,wBAAA,GAAAA,wBAAA;AAIZ,IAAAC,gBAAA,GAAAC,OAAA;AACA,IAAAC,eAAA,GAAAD,OAAA;AACA,IAAAE,OAAA,GAAAF,OAAA;AAEA,IAAAG,MAAA,GAAAH,OAAA;AACA,IAAAI,OAAA,GAAAJ,OAAA;AAEA,IAAMK,4BAA4B,GAAG,wCAAwC;AAC7E,IAAMC,wBAAwB,GAAG,oCAAoC;AAGrE,IAAMC,oBAAoB,GAAG,IAAIC,GAAG,CAAiB,CAAC;AACtD,IAAMC,iBAA2B,GAAG,EAAE;AAEtC,IAAIC,aAAa,GAAG,KAAK;AAMlB,SAASd,4BAA4BA,CAAA,EAAG;EAC7C,IACE,CAAC,IAAAe,kCAAiB,EAAC,CAAC,IACpBC,QAAQ,CAACC,cAAc,CAACR,4BAA4B,CAAC,KAAK,IAAI,EAC9D;IACA;EACF;EAEA,IAAMS,4BAA4B,GAAGF,QAAQ,CAACG,aAAa,CAAC,OAAO,CAAC;EACpED,4BAA4B,CAACE,EAAE,GAAGX,4BAA4B;EAE9DS,4BAA4B,CAACG,MAAM,GAAG,YAAM;IAC1C,IAAI,CAACH,4BAA4B,CAACI,KAAK,EAAE;MACvCC,aAAM,CAACC,KAAK,CAAC,gDAAgD,CAAC;MAC9D;IACF;IAEA,KAAK,IAAMC,aAAa,IAAIC,kBAAU,EAAE;MACtCR,4BAA4B,CAACI,KAAK,CAACK,UAAU,CAC3CD,kBAAU,CAACD,aAAa,CAAmB,CAACG,KAC9C,CAAC;IACH;EACF,CAAC;EAED,IAAMC,wBAAwB,GAAGb,QAAQ,CAACG,aAAa,CAAC,OAAO,CAAC;EAChEU,wBAAwB,CAACT,EAAE,GAAGV,wBAAwB;EAEtDM,QAAQ,CAACc,IAAI,CAACC,WAAW,CAACb,4BAA4B,CAAC;EACvDF,QAAQ,CAACc,IAAI,CAACC,WAAW,CAACF,wBAAwB,CAAC;AACrD;AAEO,SAAS5B,kBAAkBA,CAACwB,aAAqB,EAAEO,QAAgB,EAAE;EAE1E,IAAI,CAAC,IAAAjB,kCAAiB,EAAC,CAAC,EAAE;IACxB;EACF;EAEA,IAAMkB,QAAQ,GAAGjB,QAAQ,CAACC,cAAc,CACtCP,wBACF,CAAqB;EAErB,IAAI,CAACuB,QAAQ,CAACX,KAAK,EAAE;IACnBC,aAAM,CAACC,KAAK,CAAC,gDAAgD,CAAC;IAC9D;EACF;EAEAS,QAAQ,CAACX,KAAK,CAACK,UAAU,CAACK,QAAQ,EAAE,CAAC,CAAC;EACtCnB,iBAAiB,CAACqB,OAAO,CAACT,aAAa,CAAC;EACxCd,oBAAoB,CAACwB,GAAG,CAACV,aAAa,EAAE,CAAC,CAAC;EAE1C,KAAK,IAAIW,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGvB,iBAAiB,CAACwB,MAAM,EAAE,EAAED,CAAC,EAAE;IACjD,IAAME,iBAAiB,GAAGzB,iBAAiB,CAACuB,CAAC,CAAC;IAC9C,IAAMG,kBAAkB,GAAG5B,oBAAoB,CAAC6B,GAAG,CAACF,iBAAiB,CAAC;IAEtE,IAAIC,kBAAkB,KAAKE,SAAS,EAAE;MACpC,MAAM,IAAIC,uBAAe,CAAC,mCAAmC,CAAC;IAChE;IAEA/B,oBAAoB,CAACwB,GAAG,CAACtB,iBAAiB,CAACuB,CAAC,CAAC,EAAEG,kBAAkB,GAAG,CAAC,CAAC;EACxE;AACF;AAEA,SAASI,kBAAkBA,CACzBlB,aAAqB,EACrBmB,uBAAmC,EACnC;EAAA,IAAAC,eAAA;EAEA,IAAI,CAAC,IAAA9B,kCAAiB,EAAC,CAAC,EAAE;IACxB;EACF;EAEA,IAAMkB,QAAQ,GAAGjB,QAAQ,CAACC,cAAc,CACtCP,wBACF,CAAqB;EAErB,IAAMoC,qBAAqB,GAAGnC,oBAAoB,CAAC6B,GAAG,CAACf,aAAa,CAAC;EAErE,IAAIqB,qBAAqB,KAAKL,SAAS,EAAE;IACvC,MAAM,IAAIC,uBAAe,CAAC,mCAAmC,CAAC;EAChE;EAEAE,uBAAuB,CAAC,CAAC;EAEzB,CAAAC,eAAA,GAAAZ,QAAQ,CAACX,KAAK,aAAduB,eAAA,CAAgBE,UAAU,CAACD,qBAAqB,CAAC;EAEjDjC,iBAAiB,CAACmC,MAAM,CAACF,qBAAqB,EAAE,CAAC,CAAC;EAClDnC,oBAAoB,CAACsC,MAAM,CAACxB,aAAa,CAAC;EAE1C,KAAK,IAAIW,CAAC,GAAGU,qBAAqB,EAAEV,CAAC,GAAGvB,iBAAiB,CAACwB,MAAM,EAAE,EAAED,CAAC,EAAE;IACrE,IAAME,iBAAiB,GAAGzB,iBAAiB,CAACuB,CAAC,CAAC;IAC9C,IAAMG,kBAAkB,GAAG5B,oBAAoB,CAAC6B,GAAG,CAACF,iBAAiB,CAAC;IAEtE,IAAIC,kBAAkB,KAAKE,SAAS,EAAE;MACpC,MAAM,IAAIC,uBAAe,CAAC,mCAAmC,CAAC;IAChE;IAEA/B,oBAAoB,CAACwB,GAAG,CAACtB,iBAAiB,CAACuB,CAAC,CAAC,EAAEG,kBAAkB,GAAG,CAAC,CAAC;EACxE;AACF;AAEA,IAAMW,YAAY,GAAG,IAAI;AACzB,IAAMC,eAAe,GAAG,EAAE;AAC1B,IAAMC,aAAa,GAAG,EAAE;AAEjB,SAASlD,wBAAwBA,CACtCuB,aAAqB,EACrB4B,iBAAyB,EACzBT,uBAAmC,EACnC;EAGA,IAAMU,YAAY,GAAGC,IAAI,CAACC,GAAG,CAC3BH,iBAAiB,GAAGH,YAAY,GAAG,IAAI,EACvCG,iBAAiB,GAAGF,eAAe,GAAGC,aACxC,CAAC;EAEDK,UAAU,CACR;IAAA,OAAMd,kBAAkB,CAAClB,aAAa,EAAEmB,uBAAuB,CAAC;EAAA,GAChEU,YACF,CAAC;AACH;AAEA,SAASI,yBAAyBA,CAACC,KAA4B,EAAEC,MAAY,EAAE;EAC7E,IAAMC,aAAa,GAAGC,yBAAS,CAACtB,GAAG,CAACmB,KAAK,CAAC;EAE1C,IAAI,CAACE,aAAa,EAAE;IAClBtC,aAAM,CAACC,KAAK,CAAC,4BAA4B,CAAC;IAC1C;EACF;EAGAmC,KAAK,CAACI,qBAAqB,GAAG,IAAI;EAClCH,MAAM,CAAC7B,WAAW,CAAC4B,KAAK,CAAC;EAEzB,IAAAK,kCAAkB,EAACL,KAAK,EAAEE,aAAa,CAAC;EAExC,IAAMI,sBAAsB,GAAGN,KAAK,CAACO,cAAc;EAEnDP,KAAK,CAACO,cAAc,GAAG,UAAUC,KAAqB,EAAE;IACtDP,MAAM,CAACQ,WAAW,CAACT,KAAK,CAAC;IAGzBM,sBAAsB,YAAtBA,sBAAsB,CAAEI,IAAI,CAAC,IAAI,EAAEF,KAAK,CAAC;EAC3C,CAAC;AACH;AAEA,SAASG,kCAAkCA,CACzCC,IAA2B,EAC3BC,IAAU,EACV;EAGA,IAAI,EAAED,IAAI,YAAYE,WAAW,CAAC,EAAE;IAClC;EACF;EAEA,IAAIF,IAAI,CAACG,eAAe,IAAIH,IAAI,CAACR,qBAAqB,KAAKtB,SAAS,EAAE;IACpEiB,yBAAyB,CAACa,IAAI,EAAEC,IAAI,CAAC;EACvC;EAEA,IAAMG,QAAQ,GAAGC,KAAK,CAACC,IAAI,CAACN,IAAI,CAACI,QAAQ,CAAC;EAE1C,KAAK,IAAIvC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGuC,QAAQ,CAACtC,MAAM,EAAE,EAAED,CAAC,EAAE;IACxCkC,kCAAkC,CAChCK,QAAQ,CAACvC,CAAC,CAAC,EACXoC,IACF,CAAC;EACH;AACF;AAkBA,SAASM,uBAAuBA,CAC9BC,cAA0D,EAC1D;EAAA,IAAAC,qBAAA;EACA,IAAIC,aAA2B,GAAG,cAAc;EAEhD,KAAK,IAAMC,GAAG,IAAIxF,MAAM,CAACyF,IAAI,CAACJ,cAAc,CAAC,EAAE;IAC7C,IAAIG,GAAG,CAACE,UAAU,CAAC,cAAc,CAAC,EAAE;MAClCH,aAAa,GAAGC,GAAmB;MACnC;IACF;EACF;EAEA,OACE,EAAAF,qBAAA,GAAAD,cAAc,CAACE,aAAa,CAAC,cAAAD,qBAAA,GAA7BA,qBAAA,CAA+BrB,KAAK,cAAAqB,qBAAA,GAApCA,qBAAA,CAAsCK,aAAa,qBAAnDL,qBAAA,CAAqDM,UAAU,MAC/D7C,SAAS;AAEb;AAEO,SAAS3C,uBAAuBA,CAAA,EAAG;EACxC,IAAIgB,aAAa,IAAI,CAAC,IAAAC,kCAAiB,EAAC,CAAC,EAAE;IACzC;EACF;EAEAD,aAAa,GAAG,IAAI;EAEpB,IAAMyE,QAAQ,GAAG,IAAIC,gBAAgB,CAAE,UAAAC,aAAa,EAAK;IACvD,IAAMC,YAAY,GAAGD,aAAa,CAACA,aAAa,CAACpD,MAAM,GAAG,CAAC,CAAC;IAE5D,IACEyC,uBAAuB,CACrBY,YAAY,CAACC,MACf,CAAC,EACD;MACA;IACF;IAEA,KAAK,IAAIvD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGsD,YAAY,CAACE,YAAY,CAACvD,MAAM,EAAE,EAAED,CAAC,EAAE;MACzDkC,kCAAkC,CAChCoB,YAAY,CAACE,YAAY,CAACxD,CAAC,CAAC,EAC5BsD,YAAY,CAACC,MACf,CAAC;IACH;EACF,CAAC,CAAC;EAEFJ,QAAQ,CAACM,OAAO,CAAC7E,QAAQ,CAAC8E,IAAI,EAAE;IAAEC,SAAS,EAAE,IAAI;IAAEC,OAAO,EAAE;EAAK,CAAC,CAAC;AACrE;AAEO,SAASjG,gBAAgBA,CAACkG,EAAW,EAAEC,EAAW,EAAE;EAEzD,OACED,EAAE,CAACE,CAAC,KAAKD,EAAE,CAACC,CAAC,IACbF,EAAE,CAACG,CAAC,KAAKF,EAAE,CAACE,CAAC,IACbH,EAAE,CAACI,KAAK,KAAKH,EAAE,CAACG,KAAK,IACrBJ,EAAE,CAACK,MAAM,KAAKJ,EAAE,CAACI,MAAM;AAE3B", "ignoreList": []}