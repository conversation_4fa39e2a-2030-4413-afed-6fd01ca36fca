07fe43224f8957107785e9ded9910549
"use strict";

/* istanbul ignore next */
function cov_fs3o6puw2() {
  var path = "/Users/<USER>/projects/react-native/payment-module/src/utils/ExcecutionHandler.ts";
  var hash = "5f33ea710006da4f46222d751037b0a34a2084cc";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/projects/react-native/payment-module/src/utils/ExcecutionHandler.ts",
    statementMap: {
      "0": {
        start: {
          line: 3,
          column: 29
        },
        end: {
          line: 3,
          column: 84
        }
      },
      "1": {
        start: {
          line: 4,
          column: 25
        },
        end: {
          line: 4,
          column: 99
        }
      },
      "2": {
        start: {
          line: 5,
          column: 23
        },
        end: {
          line: 5,
          column: 95
        }
      },
      "3": {
        start: {
          line: 6,
          column: 20
        },
        end: {
          line: 6,
          column: 89
        }
      },
      "4": {
        start: {
          line: 7,
          column: 0
        },
        end: {
          line: 9,
          column: 3
        }
      },
      "5": {
        start: {
          line: 10,
          column: 0
        },
        end: {
          line: 10,
          column: 34
        }
      },
      "6": {
        start: {
          line: 11,
          column: 31
        },
        end: {
          line: 11,
          column: 64
        }
      },
      "7": {
        start: {
          line: 12,
          column: 23
        },
        end: {
          line: 12,
          column: 56
        }
      },
      "8": {
        start: {
          line: 13,
          column: 23
        },
        end: {
          line: 66,
          column: 3
        }
      },
      "9": {
        start: {
          line: 15,
          column: 4
        },
        end: {
          line: 15,
          column: 58
        }
      },
      "10": {
        start: {
          line: 17,
          column: 2
        },
        end: {
          line: 65,
          column: 6
        }
      },
      "11": {
        start: {
          line: 20,
          column: 21
        },
        end: {
          line: 59,
          column: 8
        }
      },
      "12": {
        start: {
          line: 21,
          column: 8
        },
        end: {
          line: 58,
          column: 9
        }
      },
      "13": {
        start: {
          line: 22,
          column: 10
        },
        end: {
          line: 24,
          column: 11
        }
      },
      "14": {
        start: {
          line: 23,
          column: 12
        },
        end: {
          line: 23,
          column: 90
        }
      },
      "15": {
        start: {
          line: 25,
          column: 25
        },
        end: {
          line: 25,
          column: 46
        }
      },
      "16": {
        start: {
          line: 26,
          column: 10
        },
        end: {
          line: 26,
          column: 69
        }
      },
      "17": {
        start: {
          line: 27,
          column: 10
        },
        end: {
          line: 29,
          column: 11
        }
      },
      "18": {
        start: {
          line: 28,
          column: 12
        },
        end: {
          line: 28,
          column: 92
        }
      },
      "19": {
        start: {
          line: 30,
          column: 10
        },
        end: {
          line: 37,
          column: 11
        }
      },
      "20": {
        start: {
          line: 32,
          column: 12
        },
        end: {
          line: 32,
          column: 64
        }
      },
      "21": {
        start: {
          line: 33,
          column: 12
        },
        end: {
          line: 36,
          column: 14
        }
      },
      "22": {
        start: {
          line: 38,
          column: 10
        },
        end: {
          line: 41,
          column: 12
        }
      },
      "23": {
        start: {
          line: 43,
          column: 10
        },
        end: {
          line: 43,
          column: 68
        }
      },
      "24": {
        start: {
          line: 44,
          column: 10
        },
        end: {
          line: 49,
          column: 11
        }
      },
      "25": {
        start: {
          line: 45,
          column: 12
        },
        end: {
          line: 48,
          column: 14
        }
      },
      "26": {
        start: {
          line: 50,
          column: 10
        },
        end: {
          line: 53,
          column: 12
        }
      },
      "27": {
        start: {
          line: 55,
          column: 10
        },
        end: {
          line: 57,
          column: 11
        }
      },
      "28": {
        start: {
          line: 56,
          column: 12
        },
        end: {
          line: 56,
          column: 92
        }
      },
      "29": {
        start: {
          line: 61,
          column: 8
        },
        end: {
          line: 61,
          column: 47
        }
      },
      "30": {
        start: {
          line: 63,
          column: 6
        },
        end: {
          line: 63,
          column: 21
        }
      },
      "31": {
        start: {
          line: 67,
          column: 0
        },
        end: {
          line: 67,
          column: 44
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 13,
            column: 23
          },
          end: {
            line: 13,
            column: 24
          }
        },
        loc: {
          start: {
            line: 13,
            column: 35
          },
          end: {
            line: 66,
            column: 1
          }
        },
        line: 13
      },
      "1": {
        name: "ExecutionHandler",
        decl: {
          start: {
            line: 14,
            column: 11
          },
          end: {
            line: 14,
            column: 27
          }
        },
        loc: {
          start: {
            line: 14,
            column: 30
          },
          end: {
            line: 16,
            column: 3
          }
        },
        line: 14
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 19,
            column: 11
          },
          end: {
            line: 19,
            column: 12
          }
        },
        loc: {
          start: {
            line: 19,
            column: 23
          },
          end: {
            line: 64,
            column: 5
          }
        },
        line: 19
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 20,
            column: 53
          },
          end: {
            line: 20,
            column: 54
          }
        },
        loc: {
          start: {
            line: 20,
            column: 93
          },
          end: {
            line: 59,
            column: 7
          }
        },
        line: 20
      },
      "4": {
        name: "execute",
        decl: {
          start: {
            line: 60,
            column: 15
          },
          end: {
            line: 60,
            column: 22
          }
        },
        loc: {
          start: {
            line: 60,
            column: 32
          },
          end: {
            line: 62,
            column: 7
          }
        },
        line: 60
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 22,
            column: 10
          },
          end: {
            line: 24,
            column: 11
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 22,
            column: 10
          },
          end: {
            line: 24,
            column: 11
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 22
      },
      "1": {
        loc: {
          start: {
            line: 27,
            column: 10
          },
          end: {
            line: 29,
            column: 11
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 27,
            column: 10
          },
          end: {
            line: 29,
            column: 11
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 27
      },
      "2": {
        loc: {
          start: {
            line: 30,
            column: 10
          },
          end: {
            line: 37,
            column: 11
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 30,
            column: 10
          },
          end: {
            line: 37,
            column: 11
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 30
      },
      "3": {
        loc: {
          start: {
            line: 30,
            column: 14
          },
          end: {
            line: 30,
            column: 62
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 30,
            column: 14
          },
          end: {
            line: 30,
            column: 23
          }
        }, {
          start: {
            line: 30,
            column: 27
          },
          end: {
            line: 30,
            column: 43
          }
        }, {
          start: {
            line: 30,
            column: 47
          },
          end: {
            line: 30,
            column: 62
          }
        }],
        line: 30
      },
      "4": {
        loc: {
          start: {
            line: 35,
            column: 55
          },
          end: {
            line: 35,
            column: 217
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 35,
            column: 191
          },
          end: {
            line: 35,
            column: 212
          }
        }, {
          start: {
            line: 35,
            column: 215
          },
          end: {
            line: 35,
            column: 217
          }
        }],
        line: 35
      },
      "5": {
        loc: {
          start: {
            line: 35,
            column: 80
          },
          end: {
            line: 35,
            column: 179
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 35,
            column: 147
          },
          end: {
            line: 35,
            column: 153
          }
        }, {
          start: {
            line: 35,
            column: 156
          },
          end: {
            line: 35,
            column: 179
          }
        }],
        line: 35
      },
      "6": {
        loc: {
          start: {
            line: 35,
            column: 80
          },
          end: {
            line: 35,
            column: 144
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 35,
            column: 80
          },
          end: {
            line: 35,
            column: 96
          }
        }, {
          start: {
            line: 35,
            column: 100
          },
          end: {
            line: 35,
            column: 144
          }
        }],
        line: 35
      },
      "7": {
        loc: {
          start: {
            line: 44,
            column: 10
          },
          end: {
            line: 49,
            column: 11
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 44,
            column: 10
          },
          end: {
            line: 49,
            column: 11
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 44
      },
      "8": {
        loc: {
          start: {
            line: 52,
            column: 53
          },
          end: {
            line: 52,
            column: 88
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 52,
            column: 69
          },
          end: {
            line: 52,
            column: 75
          }
        }, {
          start: {
            line: 52,
            column: 78
          },
          end: {
            line: 52,
            column: 88
          }
        }],
        line: 52
      },
      "9": {
        loc: {
          start: {
            line: 55,
            column: 10
          },
          end: {
            line: 57,
            column: 11
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 55,
            column: 10
          },
          end: {
            line: 57,
            column: 11
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 55
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0
    },
    b: {
      "0": [0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0]
    },
    inputSourceMap: {
      version: 3,
      file: undefined,
      names: ["msb_host_shared_module_1", "require", "MSBCustomError_1", "ExecutionHandler", "_classCallCheck2", "default", "_createClass2", "key", "value", "_execute", "_asyncToGenerator2", "fetchFunction", "isUseLoading", "hostSharedModule", "d", "domainService", "addSpinnerRequest", "response", "console", "log", "addSpinnerCompleted", "errors", "_response$errors$0$ke", "_response$errors", "error", "status", "createError", "data", "CustomError", "code", "execute", "_x", "_x2", "apply", "arguments", "exports"],
      sourceRoot: undefined,
      sources: ["/Users/<USER>/projects/react-native/payment-module/src/utils/ExcecutionHandler.ts"],
      sourcesContent: ["import {ResultState} from '../core/ResultState';\nimport {BaseResponse} from '../core/BaseResponse';\nimport {hostSharedModule} from 'msb-host-shared-module';\nimport {createError, CustomError} from '../core/MSBCustomError';\n\nexport class ExecutionHandler {\n  static async execute<T>(\n    fetchFunction: () => Promise<BaseResponse<T>>,\n    isUseLoading?: boolean | null | undefined,\n  ): Promise<ResultState<T>> {\n    try {\n      if (isUseLoading) {\n        hostSharedModule.d.domainService.addSpinnerRequest();\n      }\n      const response = await fetchFunction();\n      console.log('\u2705 ExecutionHandler with response=', response);\n      if (isUseLoading) {\n        hostSharedModule.d.domainService.addSpinnerCompleted();\n      }\n      if (!response || response?.errors) {\n        console.error('\u274C ExecutionHandler Null data Error');\n        return {\n          status: 'ERROR',\n          error: createError(response?.errors?.[0].key ?? ''),\n        };\n      }\n      return {status: 'SUCCESS', data: response};\n    } catch (error) {\n      console.error('\u274C ExecutionHandler UseCase Error:', error);\n\n      if (error instanceof CustomError) {\n        return {\n          status: 'ERROR',\n          error: error,\n        };\n      }\n      return {\n        status: 'ERROR',\n        error: createError((error as any)?.code),\n      };\n    } finally {\n      if (isUseLoading) {\n        hostSharedModule.d.domainService.addSpinnerCompleted();\n      }\n    }\n  }\n}\n"],
      mappings: ";;;;;;;;;;AAEA,IAAAA,wBAAA,GAAAC,OAAA;AACA,IAAAC,gBAAA,GAAAD,OAAA;AAAgE,IAEnDE,gBAAgB;EAAA,SAAAA,iBAAA;IAAA,IAAAC,gBAAA,CAAAC,OAAA,QAAAF,gBAAA;EAAA;EAAA,WAAAG,aAAA,CAAAD,OAAA,EAAAF,gBAAA;IAAAI,GAAA;IAAAC,KAAA;MAAA,IAAAC,QAAA,OAAAC,kBAAA,CAAAL,OAAA,EAC3B,WACEM,aAA6C,EAC7CC,YAAyC;QAEzC,IAAI;UACF,IAAIA,YAAY,EAAE;YAChBZ,wBAAA,CAAAa,gBAAgB,CAACC,CAAC,CAACC,aAAa,CAACC,iBAAiB,EAAE;UACtD;UACA,IAAMC,QAAQ,SAASN,aAAa,EAAE;UACtCO,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAEF,QAAQ,CAAC;UAC1D,IAAIL,YAAY,EAAE;YAChBZ,wBAAA,CAAAa,gBAAgB,CAACC,CAAC,CAACC,aAAa,CAACK,mBAAmB,EAAE;UACxD;UACA,IAAI,CAACH,QAAQ,IAAIA,QAAQ,YAARA,QAAQ,CAAEI,MAAM,EAAE;YAAA,IAAAC,qBAAA,EAAAC,gBAAA;YACjCL,OAAO,CAACM,KAAK,CAAC,oCAAoC,CAAC;YACnD,OAAO;cACLC,MAAM,EAAE,OAAO;cACfD,KAAK,EAAE,IAAAtB,gBAAA,CAAAwB,WAAW,GAAAJ,qBAAA,GAACL,QAAQ,aAAAM,gBAAA,GAARN,QAAQ,CAAEI,MAAM,qBAAhBE,gBAAA,CAAmB,CAAC,CAAC,CAAChB,GAAG,YAAAe,qBAAA,GAAI,EAAE;aACnD;UACH;UACA,OAAO;YAACG,MAAM,EAAE,SAAS;YAAEE,IAAI,EAAEV;UAAQ,CAAC;QAC5C,CAAC,CAAC,OAAOO,KAAK,EAAE;UACdN,OAAO,CAACM,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;UAEzD,IAAIA,KAAK,YAAYtB,gBAAA,CAAA0B,WAAW,EAAE;YAChC,OAAO;cACLH,MAAM,EAAE,OAAO;cACfD,KAAK,EAAEA;aACR;UACH;UACA,OAAO;YACLC,MAAM,EAAE,OAAO;YACfD,KAAK,EAAE,IAAAtB,gBAAA,CAAAwB,WAAW,EAAEF,KAAa,oBAAbA,KAAa,CAAEK,IAAI;WACxC;QACH,CAAC,SAAS;UACR,IAAIjB,YAAY,EAAE;YAChBZ,wBAAA,CAAAa,gBAAgB,CAACC,CAAC,CAACC,aAAa,CAACK,mBAAmB,EAAE;UACxD;QACF;MACF,CAAC;MAAA,SAvCYU,OAAOA,CAAAC,EAAA,EAAAC,GAAA;QAAA,OAAAvB,QAAA,CAAAwB,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAPJ,OAAO;IAAA;EAAA;AAAA;AADtBK,OAAA,CAAAhC,gBAAA,GAAAA,gBAAA",
      ignoreList: []
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "5f33ea710006da4f46222d751037b0a34a2084cc"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_fs3o6puw2 = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_fs3o6puw2();
var _interopRequireDefault =
/* istanbul ignore next */
(cov_fs3o6puw2().s[0]++, require("@babel/runtime/helpers/interopRequireDefault"));
var _asyncToGenerator2 =
/* istanbul ignore next */
(cov_fs3o6puw2().s[1]++, _interopRequireDefault(require("@babel/runtime/helpers/asyncToGenerator")));
var _classCallCheck2 =
/* istanbul ignore next */
(cov_fs3o6puw2().s[2]++, _interopRequireDefault(require("@babel/runtime/helpers/classCallCheck")));
var _createClass2 =
/* istanbul ignore next */
(cov_fs3o6puw2().s[3]++, _interopRequireDefault(require("@babel/runtime/helpers/createClass")));
/* istanbul ignore next */
cov_fs3o6puw2().s[4]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_fs3o6puw2().s[5]++;
exports.ExecutionHandler = void 0;
var msb_host_shared_module_1 =
/* istanbul ignore next */
(cov_fs3o6puw2().s[6]++, require("msb-host-shared-module"));
var MSBCustomError_1 =
/* istanbul ignore next */
(cov_fs3o6puw2().s[7]++, require("../core/MSBCustomError"));
var ExecutionHandler =
/* istanbul ignore next */
(cov_fs3o6puw2().s[8]++, function () {
  /* istanbul ignore next */
  cov_fs3o6puw2().f[0]++;
  function ExecutionHandler() {
    /* istanbul ignore next */
    cov_fs3o6puw2().f[1]++;
    cov_fs3o6puw2().s[9]++;
    (0, _classCallCheck2.default)(this, ExecutionHandler);
  }
  /* istanbul ignore next */
  cov_fs3o6puw2().s[10]++;
  return (0, _createClass2.default)(ExecutionHandler, null, [{
    key: "execute",
    value: function () {
      /* istanbul ignore next */
      cov_fs3o6puw2().f[2]++;
      var _execute =
      /* istanbul ignore next */
      (cov_fs3o6puw2().s[11]++, (0, _asyncToGenerator2.default)(function* (fetchFunction, isUseLoading) {
        /* istanbul ignore next */
        cov_fs3o6puw2().f[3]++;
        cov_fs3o6puw2().s[12]++;
        try {
          /* istanbul ignore next */
          cov_fs3o6puw2().s[13]++;
          if (isUseLoading) {
            /* istanbul ignore next */
            cov_fs3o6puw2().b[0][0]++;
            cov_fs3o6puw2().s[14]++;
            msb_host_shared_module_1.hostSharedModule.d.domainService.addSpinnerRequest();
          } else
          /* istanbul ignore next */
          {
            cov_fs3o6puw2().b[0][1]++;
          }
          var response =
          /* istanbul ignore next */
          (cov_fs3o6puw2().s[15]++, yield fetchFunction());
          /* istanbul ignore next */
          cov_fs3o6puw2().s[16]++;
          console.log('✅ ExecutionHandler with response=', response);
          /* istanbul ignore next */
          cov_fs3o6puw2().s[17]++;
          if (isUseLoading) {
            /* istanbul ignore next */
            cov_fs3o6puw2().b[1][0]++;
            cov_fs3o6puw2().s[18]++;
            msb_host_shared_module_1.hostSharedModule.d.domainService.addSpinnerCompleted();
          } else
          /* istanbul ignore next */
          {
            cov_fs3o6puw2().b[1][1]++;
          }
          cov_fs3o6puw2().s[19]++;
          if (
          /* istanbul ignore next */
          (cov_fs3o6puw2().b[3][0]++, !response) ||
          /* istanbul ignore next */
          (cov_fs3o6puw2().b[3][1]++, response != null) &&
          /* istanbul ignore next */
          (cov_fs3o6puw2().b[3][2]++, response.errors)) {
            /* istanbul ignore next */
            cov_fs3o6puw2().b[2][0]++;
            var _response$errors$0$ke, _response$errors;
            /* istanbul ignore next */
            cov_fs3o6puw2().s[20]++;
            console.error('❌ ExecutionHandler Null data Error');
            /* istanbul ignore next */
            cov_fs3o6puw2().s[21]++;
            return {
              status: 'ERROR',
              error: (0, MSBCustomError_1.createError)((_response$errors$0$ke =
              /* istanbul ignore next */
              (cov_fs3o6puw2().b[6][0]++, response == null) ||
              /* istanbul ignore next */
              (cov_fs3o6puw2().b[6][1]++, (_response$errors = response.errors) == null) ?
              /* istanbul ignore next */
              (cov_fs3o6puw2().b[5][0]++, void 0) :
              /* istanbul ignore next */
              (cov_fs3o6puw2().b[5][1]++, _response$errors[0].key)) != null ?
              /* istanbul ignore next */
              (cov_fs3o6puw2().b[4][0]++, _response$errors$0$ke) :
              /* istanbul ignore next */
              (cov_fs3o6puw2().b[4][1]++, ''))
            };
          } else
          /* istanbul ignore next */
          {
            cov_fs3o6puw2().b[2][1]++;
          }
          cov_fs3o6puw2().s[22]++;
          return {
            status: 'SUCCESS',
            data: response
          };
        } catch (error) {
          /* istanbul ignore next */
          cov_fs3o6puw2().s[23]++;
          console.error('❌ ExecutionHandler UseCase Error:', error);
          /* istanbul ignore next */
          cov_fs3o6puw2().s[24]++;
          if (error instanceof MSBCustomError_1.CustomError) {
            /* istanbul ignore next */
            cov_fs3o6puw2().b[7][0]++;
            cov_fs3o6puw2().s[25]++;
            return {
              status: 'ERROR',
              error: error
            };
          } else
          /* istanbul ignore next */
          {
            cov_fs3o6puw2().b[7][1]++;
          }
          cov_fs3o6puw2().s[26]++;
          return {
            status: 'ERROR',
            error: (0, MSBCustomError_1.createError)(error == null ?
            /* istanbul ignore next */
            (cov_fs3o6puw2().b[8][0]++, void 0) :
            /* istanbul ignore next */
            (cov_fs3o6puw2().b[8][1]++, error.code))
          };
        } finally {
          /* istanbul ignore next */
          cov_fs3o6puw2().s[27]++;
          if (isUseLoading) {
            /* istanbul ignore next */
            cov_fs3o6puw2().b[9][0]++;
            cov_fs3o6puw2().s[28]++;
            msb_host_shared_module_1.hostSharedModule.d.domainService.addSpinnerCompleted();
          } else
          /* istanbul ignore next */
          {
            cov_fs3o6puw2().b[9][1]++;
          }
        }
      }));
      function execute(_x, _x2) {
        /* istanbul ignore next */
        cov_fs3o6puw2().f[4]++;
        cov_fs3o6puw2().s[29]++;
        return _execute.apply(this, arguments);
      }
      /* istanbul ignore next */
      cov_fs3o6puw2().s[30]++;
      return execute;
    }()
  }]);
}());
/* istanbul ignore next */
cov_fs3o6puw2().s[31]++;
exports.ExecutionHandler = ExecutionHandler;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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