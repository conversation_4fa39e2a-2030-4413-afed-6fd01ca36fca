{"version": 3, "names": ["cov_fs3o6puw2", "actualCoverage", "msb_host_shared_module_1", "s", "require", "MSBCustomError_1", "ExecutionHandler", "f", "_classCallCheck2", "default", "_createClass2", "key", "value", "_execute", "_asyncToGenerator2", "fetchFunction", "isUseLoading", "b", "hostSharedModule", "d", "domainService", "addSpinnerRequest", "response", "console", "log", "addSpinnerCompleted", "errors", "_response$errors$0$ke", "_response$errors", "error", "status", "createError", "data", "CustomError", "code", "execute", "_x", "_x2", "apply", "arguments", "exports"], "sources": ["/Users/<USER>/projects/react-native/payment-module/src/utils/ExcecutionHandler.ts"], "sourcesContent": ["import {ResultState} from '../core/ResultState';\nimport {BaseResponse} from '../core/BaseResponse';\nimport {hostSharedModule} from 'msb-host-shared-module';\nimport {createError, CustomError} from '../core/MSBCustomError';\n\nexport class ExecutionHandler {\n  static async execute<T>(\n    fetchFunction: () => Promise<BaseResponse<T>>,\n    isUseLoading?: boolean | null | undefined,\n  ): Promise<ResultState<T>> {\n    try {\n      if (isUseLoading) {\n        hostSharedModule.d.domainService.addSpinnerRequest();\n      }\n      const response = await fetchFunction();\n      console.log('✅ ExecutionHandler with response=', response);\n      if (isUseLoading) {\n        hostSharedModule.d.domainService.addSpinnerCompleted();\n      }\n      if (!response || response?.errors) {\n        console.error('❌ ExecutionHandler Null data Error');\n        return {\n          status: 'ERROR',\n          error: createError(response?.errors?.[0].key ?? ''),\n        };\n      }\n      return {status: 'SUCCESS', data: response};\n    } catch (error) {\n      console.error('❌ ExecutionHandler UseCase Error:', error);\n\n      if (error instanceof CustomError) {\n        return {\n          status: 'ERROR',\n          error: error,\n        };\n      }\n      return {\n        status: 'ERROR',\n        error: createError((error as any)?.code),\n      };\n    } finally {\n      if (isUseLoading) {\n        hostSharedModule.d.domainService.addSpinnerCompleted();\n      }\n    }\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAK6B;IAAAA,aAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,aAAA;;;;;;;;;;;;;;;;;;;;;AAH7B,IAAAE,wBAAA;AAAA;AAAA,CAAAF,aAAA,GAAAG,CAAA,OAAAC,OAAA;AACA,IAAAC,gBAAA;AAAA;AAAA,CAAAL,aAAA,GAAAG,CAAA,OAAAC,OAAA;AAAgE,IAEnDE,gBAAgB;AAAA;AAAA,CAAAN,aAAA,GAAAG,CAAA;EAAA;EAAAH,aAAA,GAAAO,CAAA;EAAA,SAAAD,iBAAA;IAAA;IAAAN,aAAA,GAAAO,CAAA;IAAAP,aAAA,GAAAG,CAAA;IAAA,IAAAK,gBAAA,CAAAC,OAAA,QAAAH,gBAAA;EAAA;EAAA;EAAAN,aAAA,GAAAG,CAAA;EAAA,WAAAO,aAAA,CAAAD,OAAA,EAAAH,gBAAA;IAAAK,GAAA;IAAAC,KAAA;MAAA;MAAAZ,aAAA,GAAAO,CAAA;MAAA,IAAAM,QAAA;MAAA;MAAA,CAAAb,aAAA,GAAAG,CAAA,YAAAW,kBAAA,CAAAL,OAAA,EAC3B,WACEM,aAA6C,EAC7CC,YAAyC;QAAA;QAAAhB,aAAA,GAAAO,CAAA;QAAAP,aAAA,GAAAG,CAAA;QAEzC,IAAI;UAAA;UAAAH,aAAA,GAAAG,CAAA;UACF,IAAIa,YAAY,EAAE;YAAA;YAAAhB,aAAA,GAAAiB,CAAA;YAAAjB,aAAA,GAAAG,CAAA;YAChBD,wBAAA,CAAAgB,gBAAgB,CAACC,CAAC,CAACC,aAAa,CAACC,iBAAiB,EAAE;UACtD;UAAA;UAAA;YAAArB,aAAA,GAAAiB,CAAA;UAAA;UACA,IAAMK,QAAQ;UAAA;UAAA,CAAAtB,aAAA,GAAAG,CAAA,cAASY,aAAa,EAAE;UAAA;UAAAf,aAAA,GAAAG,CAAA;UACtCoB,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAEF,QAAQ,CAAC;UAAA;UAAAtB,aAAA,GAAAG,CAAA;UAC1D,IAAIa,YAAY,EAAE;YAAA;YAAAhB,aAAA,GAAAiB,CAAA;YAAAjB,aAAA,GAAAG,CAAA;YAChBD,wBAAA,CAAAgB,gBAAgB,CAACC,CAAC,CAACC,aAAa,CAACK,mBAAmB,EAAE;UACxD;UAAA;UAAA;YAAAzB,aAAA,GAAAiB,CAAA;UAAA;UAAAjB,aAAA,GAAAG,CAAA;UACA;UAAI;UAAA,CAAAH,aAAA,GAAAiB,CAAA,WAACK,QAAQ;UAAI;UAAA,CAAAtB,aAAA,GAAAiB,CAAA,UAAAK,QAAQ;UAAA;UAAA,CAAAtB,aAAA,GAAAiB,CAAA,UAARK,QAAQ,CAAEI,MAAM,GAAE;YAAA;YAAA1B,aAAA,GAAAiB,CAAA;YAAA,IAAAU,qBAAA,EAAAC,gBAAA;YAAA;YAAA5B,aAAA,GAAAG,CAAA;YACjCoB,OAAO,CAACM,KAAK,CAAC,oCAAoC,CAAC;YAAA;YAAA7B,aAAA,GAAAG,CAAA;YACnD,OAAO;cACL2B,MAAM,EAAE,OAAO;cACfD,KAAK,EAAE,IAAAxB,gBAAA,CAAA0B,WAAW,GAAAJ,qBAAA;cAAC;cAAA,CAAA3B,aAAA,GAAAiB,CAAA,UAAAK,QAAQ;cAAA;cAAA,CAAAtB,aAAA,GAAAiB,CAAA,WAAAW,gBAAA,GAARN,QAAQ,CAAEI,MAAM;cAAA;cAAA,CAAA1B,aAAA,GAAAiB,CAAA;cAAA;cAAA,CAAAjB,aAAA,GAAAiB,CAAA,UAAhBW,gBAAA,CAAmB,CAAC,CAAC,CAACjB,GAAG;cAAA;cAAA,CAAAX,aAAA,GAAAiB,CAAA,UAAAU,qBAAA;cAAA;cAAA,CAAA3B,aAAA,GAAAiB,CAAA,UAAI,EAAE;aACnD;UACH;UAAA;UAAA;YAAAjB,aAAA,GAAAiB,CAAA;UAAA;UAAAjB,aAAA,GAAAG,CAAA;UACA,OAAO;YAAC2B,MAAM,EAAE,SAAS;YAAEE,IAAI,EAAEV;UAAQ,CAAC;QAC5C,CAAC,CAAC,OAAOO,KAAK,EAAE;UAAA;UAAA7B,aAAA,GAAAG,CAAA;UACdoB,OAAO,CAACM,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;UAAA;UAAA7B,aAAA,GAAAG,CAAA;UAEzD,IAAI0B,KAAK,YAAYxB,gBAAA,CAAA4B,WAAW,EAAE;YAAA;YAAAjC,aAAA,GAAAiB,CAAA;YAAAjB,aAAA,GAAAG,CAAA;YAChC,OAAO;cACL2B,MAAM,EAAE,OAAO;cACfD,KAAK,EAAEA;aACR;UACH;UAAA;UAAA;YAAA7B,aAAA,GAAAiB,CAAA;UAAA;UAAAjB,aAAA,GAAAG,CAAA;UACA,OAAO;YACL2B,MAAM,EAAE,OAAO;YACfD,KAAK,EAAE,IAAAxB,gBAAA,CAAA0B,WAAW,EAAEF,KAAa;YAAA;YAAA,CAAA7B,aAAA,GAAAiB,CAAA;YAAA;YAAA,CAAAjB,aAAA,GAAAiB,CAAA,UAAbY,KAAa,CAAEK,IAAI;WACxC;QACH,CAAC,SAAS;UAAA;UAAAlC,aAAA,GAAAG,CAAA;UACR,IAAIa,YAAY,EAAE;YAAA;YAAAhB,aAAA,GAAAiB,CAAA;YAAAjB,aAAA,GAAAG,CAAA;YAChBD,wBAAA,CAAAgB,gBAAgB,CAACC,CAAC,CAACC,aAAa,CAACK,mBAAmB,EAAE;UACxD;UAAA;UAAA;YAAAzB,aAAA,GAAAiB,CAAA;UAAA;QACF;MACF,CAAC;MAAA,SAvCYkB,OAAOA,CAAAC,EAAA,EAAAC,GAAA;QAAA;QAAArC,aAAA,GAAAO,CAAA;QAAAP,aAAA,GAAAG,CAAA;QAAA,OAAAU,QAAA,CAAAyB,KAAA,OAAAC,SAAA;MAAA;MAAA;MAAAvC,aAAA,GAAAG,CAAA;MAAA,OAAPgC,OAAO;IAAA;EAAA;AAAA;AAAA;AAAAnC,aAAA,GAAAG,CAAA;AADtBqC,OAAA,CAAAlC,gBAAA,GAAAA,gBAAA", "ignoreList": []}