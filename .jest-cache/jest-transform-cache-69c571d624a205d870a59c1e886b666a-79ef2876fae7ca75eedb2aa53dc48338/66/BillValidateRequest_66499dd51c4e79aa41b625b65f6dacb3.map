{"version": 3, "names": [], "sources": ["/Users/<USER>/projects/react-native/payment-module/src/data/models/bill-validate/BillValidateRequest.ts"], "sourcesContent": ["export interface BillValidateRequest {\n  originatorAccount: {\n    identification: {\n      identification: string;\n      schemeName: string;\n    };\n  };\n  requestedExecutionDate: string; // ISO 8601 format date string\n  paymentType: string;\n  transferTransactionInformation: {\n    instructedAmount: {\n      amount: string; // if dynamic binding like \"{{totalAmount}}\", use string\n      currencyCode: string;\n    };\n    counterparty: {\n      name: string;\n    };\n    counterpartyAccount: {\n      identification: {\n        identification: string;\n        schemeName: string;\n      };\n    };\n    additions: {\n      bpQueryRef: string;\n      bpBillList: string; // or stringified JSON if passed as a string\n      bpSummary: string;\n      bpServiceCode: string;\n      cifNo: string;\n      bpCategory: string;\n      bpQrContent?: string;\n      bpTranSeqCount?: number;\n    };\n  };\n}\n"], "mappings": "", "ignoreList": []}