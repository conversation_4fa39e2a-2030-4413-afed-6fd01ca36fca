c33af0ed8116b02e8007c21957a57d8c
"use strict";

/* istanbul ignore next */
function cov_2hqvnfhvii() {
  var path = "/Users/<USER>/projects/react-native/payment-module/src/data/models/bill-validate/BillValidateRequest.ts";
  var hash = "c6d0b4b7238017f91d8c5d384c24a8e8cd5780a4";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/projects/react-native/payment-module/src/data/models/bill-validate/BillValidateRequest.ts",
    statementMap: {
      "0": {
        start: {
          line: 3,
          column: 0
        },
        end: {
          line: 5,
          column: 3
        }
      }
    },
    fnMap: {},
    branchMap: {},
    s: {
      "0": 0
    },
    f: {},
    b: {},
    inputSourceMap: {
      version: 3,
      file: undefined,
      names: [],
      sourceRoot: undefined,
      sources: ["/Users/<USER>/projects/react-native/payment-module/src/data/models/bill-validate/BillValidateRequest.ts"],
      sourcesContent: ["export interface BillValidateRequest {\n  originatorAccount: {\n    identification: {\n      identification: string;\n      schemeName: string;\n    };\n  };\n  requestedExecutionDate: string; // ISO 8601 format date string\n  paymentType: string;\n  transferTransactionInformation: {\n    instructedAmount: {\n      amount: string; // if dynamic binding like \"{{totalAmount}}\", use string\n      currencyCode: string;\n    };\n    counterparty: {\n      name: string;\n    };\n    counterpartyAccount: {\n      identification: {\n        identification: string;\n        schemeName: string;\n      };\n    };\n    additions: {\n      bpQueryRef: string;\n      bpBillList: string; // or stringified JSON if passed as a string\n      bpSummary: string;\n      bpServiceCode: string;\n      cifNo: string;\n      bpCategory: string;\n      bpQrContent?: string;\n      bpTranSeqCount?: number;\n    };\n  };\n}\n"],
      mappings: "",
      ignoreList: []
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "c6d0b4b7238017f91d8c5d384c24a8e8cd5780a4"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_2hqvnfhvii = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_2hqvnfhvii();
cov_2hqvnfhvii().s[0]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6W10sInNvdXJjZXMiOlsiL1VzZXJzL3NvbnNhbXNldC9wcm9qZWN0cy9yZWFjdC1uYXRpdmUvcGF5bWVudC1tb2R1bGUvc3JjL2RhdGEvbW9kZWxzL2JpbGwtdmFsaWRhdGUvQmlsbFZhbGlkYXRlUmVxdWVzdC50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgaW50ZXJmYWNlIEJpbGxWYWxpZGF0ZVJlcXVlc3Qge1xuICBvcmlnaW5hdG9yQWNjb3VudDoge1xuICAgIGlkZW50aWZpY2F0aW9uOiB7XG4gICAgICBpZGVudGlmaWNhdGlvbjogc3RyaW5nO1xuICAgICAgc2NoZW1lTmFtZTogc3RyaW5nO1xuICAgIH07XG4gIH07XG4gIHJlcXVlc3RlZEV4ZWN1dGlvbkRhdGU6IHN0cmluZzsgLy8gSVNPIDg2MDEgZm9ybWF0IGRhdGUgc3RyaW5nXG4gIHBheW1lbnRUeXBlOiBzdHJpbmc7XG4gIHRyYW5zZmVyVHJhbnNhY3Rpb25JbmZvcm1hdGlvbjoge1xuICAgIGluc3RydWN0ZWRBbW91bnQ6IHtcbiAgICAgIGFtb3VudDogc3RyaW5nOyAvLyBpZiBkeW5hbWljIGJpbmRpbmcgbGlrZSBcInt7dG90YWxBbW91bnR9fVwiLCB1c2Ugc3RyaW5nXG4gICAgICBjdXJyZW5jeUNvZGU6IHN0cmluZztcbiAgICB9O1xuICAgIGNvdW50ZXJwYXJ0eToge1xuICAgICAgbmFtZTogc3RyaW5nO1xuICAgIH07XG4gICAgY291bnRlcnBhcnR5QWNjb3VudDoge1xuICAgICAgaWRlbnRpZmljYXRpb246IHtcbiAgICAgICAgaWRlbnRpZmljYXRpb246IHN0cmluZztcbiAgICAgICAgc2NoZW1lTmFtZTogc3RyaW5nO1xuICAgICAgfTtcbiAgICB9O1xuICAgIGFkZGl0aW9uczoge1xuICAgICAgYnBRdWVyeVJlZjogc3RyaW5nO1xuICAgICAgYnBCaWxsTGlzdDogc3RyaW5nOyAvLyBvciBzdHJpbmdpZmllZCBKU09OIGlmIHBhc3NlZCBhcyBhIHN0cmluZ1xuICAgICAgYnBTdW1tYXJ5OiBzdHJpbmc7XG4gICAgICBicFNlcnZpY2VDb2RlOiBzdHJpbmc7XG4gICAgICBjaWZObzogc3RyaW5nO1xuICAgICAgYnBDYXRlZ29yeTogc3RyaW5nO1xuICAgICAgYnBRckNvbnRlbnQ/OiBzdHJpbmc7XG4gICAgICBicFRyYW5TZXFDb3VudD86IG51bWJlcjtcbiAgICB9O1xuICB9O1xufVxuIl0sIm1hcHBpbmdzIjoiIiwiaWdub3JlTGlzdCI6W119