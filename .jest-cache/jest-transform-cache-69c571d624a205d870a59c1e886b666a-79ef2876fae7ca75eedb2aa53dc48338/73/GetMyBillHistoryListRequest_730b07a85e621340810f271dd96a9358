39445061cb710977d210ed3f41445023
"use strict";

/* istanbul ignore next */
function cov_2qfw3l7qa6() {
  var path = "/Users/<USER>/projects/react-native/payment-module/src/data/models/get-my-bill-history-list/GetMyBillHistoryListRequest.ts";
  var hash = "36e102de4058fba67a80d506de3862c58590adf3";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/projects/react-native/payment-module/src/data/models/get-my-bill-history-list/GetMyBillHistoryListRequest.ts",
    statementMap: {
      "0": {
        start: {
          line: 3,
          column: 0
        },
        end: {
          line: 5,
          column: 3
        }
      },
      "1": {
        start: {
          line: 6,
          column: 0
        },
        end: {
          line: 6,
          column: 29
        }
      },
      "2": {
        start: {
          line: 8,
          column: 0
        },
        end: {
          line: 12,
          column: 60
        }
      },
      "3": {
        start: {
          line: 9,
          column: 2
        },
        end: {
          line: 9,
          column: 27
        }
      },
      "4": {
        start: {
          line: 10,
          column: 2
        },
        end: {
          line: 10,
          column: 27
        }
      },
      "5": {
        start: {
          line: 11,
          column: 2
        },
        end: {
          line: 11,
          column: 31
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 8,
            column: 1
          },
          end: {
            line: 8,
            column: 2
          }
        },
        loc: {
          start: {
            line: 8,
            column: 24
          },
          end: {
            line: 12,
            column: 1
          }
        },
        line: 8
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 12,
            column: 3
          },
          end: {
            line: 12,
            column: 58
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 12,
            column: 3
          },
          end: {
            line: 12,
            column: 14
          }
        }, {
          start: {
            line: 12,
            column: 19
          },
          end: {
            line: 12,
            column: 57
          }
        }],
        line: 12
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0
    },
    f: {
      "0": 0
    },
    b: {
      "0": [0, 0]
    },
    inputSourceMap: {
      version: 3,
      file: undefined,
      names: ["BillChannel", "exports"],
      sourceRoot: undefined,
      sources: ["/Users/<USER>/projects/react-native/payment-module/src/data/models/get-my-bill-history-list/GetMyBillHistoryListRequest.ts"],
      sourcesContent: ["export interface GetMyBillHistoryListDataRequest {\n  from: number;\n  size: number;\n  bookingDateLessThan?: string; // From date (e.g., \"2025-01-01\")\n  bookingDateGreaterThan?: string; // From date (e.g., \"2025-01-01\")\n  creditDebitIndicator?: 'CRDT' | 'DBIT';\n  typeGroup: 'PAYMENT';\n  orderBy?: 'creationTime';\n  counterPartyName?: string;\n  description?: string;\n  counterPartyAccountNumber?: string;\n  direction?: 'ASC' | 'DESC';\n  arrangementId?: string;\n  billCode?: string;\n  serviceCode?: string;\n}\nexport type GetMyBillHistoryListRequest = Partial<GetMyBillHistoryListDataRequest>;\n\nexport enum BillChannel {\n  IB = 'IB',\n  MB = 'MB',\n  IBMB = 'IBMB',\n}\n"],
      mappings: ";;;;;;AAkBA,IAAYA,WAIX;AAJD,WAAYA,WAAW;EACrBA,WAAA,aAAS;EACTA,WAAA,aAAS;EACTA,WAAA,iBAAa;AACf,CAAC,EAJWA,WAAW,KAAAC,OAAA,CAAAD,WAAA,GAAXA,WAAW",
      ignoreList: []
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "36e102de4058fba67a80d506de3862c58590adf3"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_2qfw3l7qa6 = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_2qfw3l7qa6();
cov_2qfw3l7qa6().s[0]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_2qfw3l7qa6().s[1]++;
exports.BillChannel = void 0;
var BillChannel;
/* istanbul ignore next */
cov_2qfw3l7qa6().s[2]++;
(function (BillChannel) {
  /* istanbul ignore next */
  cov_2qfw3l7qa6().f[0]++;
  cov_2qfw3l7qa6().s[3]++;
  BillChannel["IB"] = "IB";
  /* istanbul ignore next */
  cov_2qfw3l7qa6().s[4]++;
  BillChannel["MB"] = "MB";
  /* istanbul ignore next */
  cov_2qfw3l7qa6().s[5]++;
  BillChannel["IBMB"] = "IBMB";
})(
/* istanbul ignore next */
(cov_2qfw3l7qa6().b[0][0]++, BillChannel) ||
/* istanbul ignore next */
(cov_2qfw3l7qa6().b[0][1]++, exports.BillChannel = BillChannel = {}));
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJCaWxsQ2hhbm5lbCIsImNvdl8ycWZ3M2w3cWE2IiwicyIsImYiLCJiIiwiZXhwb3J0cyJdLCJzb3VyY2VzIjpbIi9Vc2Vycy9zb25zYW1zZXQvcHJvamVjdHMvcmVhY3QtbmF0aXZlL3BheW1lbnQtbW9kdWxlL3NyYy9kYXRhL21vZGVscy9nZXQtbXktYmlsbC1oaXN0b3J5LWxpc3QvR2V0TXlCaWxsSGlzdG9yeUxpc3RSZXF1ZXN0LnRzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBpbnRlcmZhY2UgR2V0TXlCaWxsSGlzdG9yeUxpc3REYXRhUmVxdWVzdCB7XG4gIGZyb206IG51bWJlcjtcbiAgc2l6ZTogbnVtYmVyO1xuICBib29raW5nRGF0ZUxlc3NUaGFuPzogc3RyaW5nOyAvLyBGcm9tIGRhdGUgKGUuZy4sIFwiMjAyNS0wMS0wMVwiKVxuICBib29raW5nRGF0ZUdyZWF0ZXJUaGFuPzogc3RyaW5nOyAvLyBGcm9tIGRhdGUgKGUuZy4sIFwiMjAyNS0wMS0wMVwiKVxuICBjcmVkaXREZWJpdEluZGljYXRvcj86ICdDUkRUJyB8ICdEQklUJztcbiAgdHlwZUdyb3VwOiAnUEFZTUVOVCc7XG4gIG9yZGVyQnk/OiAnY3JlYXRpb25UaW1lJztcbiAgY291bnRlclBhcnR5TmFtZT86IHN0cmluZztcbiAgZGVzY3JpcHRpb24/OiBzdHJpbmc7XG4gIGNvdW50ZXJQYXJ0eUFjY291bnROdW1iZXI/OiBzdHJpbmc7XG4gIGRpcmVjdGlvbj86ICdBU0MnIHwgJ0RFU0MnO1xuICBhcnJhbmdlbWVudElkPzogc3RyaW5nO1xuICBiaWxsQ29kZT86IHN0cmluZztcbiAgc2VydmljZUNvZGU/OiBzdHJpbmc7XG59XG5leHBvcnQgdHlwZSBHZXRNeUJpbGxIaXN0b3J5TGlzdFJlcXVlc3QgPSBQYXJ0aWFsPEdldE15QmlsbEhpc3RvcnlMaXN0RGF0YVJlcXVlc3Q+O1xuXG5leHBvcnQgZW51bSBCaWxsQ2hhbm5lbCB7XG4gIElCID0gJ0lCJyxcbiAgTUIgPSAnTUInLFxuICBJQk1CID0gJ0lCTUInLFxufVxuIl0sIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBa0JBLElBQVlBLFdBSVg7QUFBQTtBQUFBQyxjQUFBLEdBQUFDLENBQUE7QUFKRCxXQUFZRixXQUFXO0VBQUE7RUFBQUMsY0FBQSxHQUFBRSxDQUFBO0VBQUFGLGNBQUEsR0FBQUMsQ0FBQTtFQUNyQkYsV0FBQSxhQUFTO0VBQUE7RUFBQUMsY0FBQSxHQUFBQyxDQUFBO0VBQ1RGLFdBQUEsYUFBUztFQUFBO0VBQUFDLGNBQUEsR0FBQUMsQ0FBQTtFQUNURixXQUFBLGlCQUFhO0FBQ2YsQ0FBQztBQUpXO0FBQUEsQ0FBQUMsY0FBQSxHQUFBRyxDQUFBLFVBQUFKLFdBQVc7QUFBQTtBQUFBLENBQUFDLGNBQUEsR0FBQUcsQ0FBQSxVQUFBQyxPQUFBLENBQUFMLFdBQUEsR0FBWEEsV0FBVyIsImlnbm9yZUxpc3QiOltdfQ==