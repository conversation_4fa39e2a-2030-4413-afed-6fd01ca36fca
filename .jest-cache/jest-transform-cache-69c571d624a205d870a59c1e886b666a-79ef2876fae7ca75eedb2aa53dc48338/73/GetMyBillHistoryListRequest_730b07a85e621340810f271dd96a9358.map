{"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON>", "cov_2qfw3l7qa6", "s", "f", "b", "exports"], "sources": ["/Users/<USER>/projects/react-native/payment-module/src/data/models/get-my-bill-history-list/GetMyBillHistoryListRequest.ts"], "sourcesContent": ["export interface GetMyBillHistoryListDataRequest {\n  from: number;\n  size: number;\n  bookingDateLessThan?: string; // From date (e.g., \"2025-01-01\")\n  bookingDateGreaterThan?: string; // From date (e.g., \"2025-01-01\")\n  creditDebitIndicator?: 'CRDT' | 'DBIT';\n  typeGroup: 'PAYMENT';\n  orderBy?: 'creationTime';\n  counterPartyName?: string;\n  description?: string;\n  counterPartyAccountNumber?: string;\n  direction?: 'ASC' | 'DESC';\n  arrangementId?: string;\n  billCode?: string;\n  serviceCode?: string;\n}\nexport type GetMyBillHistoryListRequest = Partial<GetMyBillHistoryListDataRequest>;\n\nexport enum BillChannel {\n  IB = 'IB',\n  MB = 'MB',\n  IBMB = 'IBMB',\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkBA,IAAYA,WAIX;AAAA;AAAAC,cAAA,GAAAC,CAAA;AAJD,WAAYF,WAAW;EAAA;EAAAC,cAAA,GAAAE,CAAA;EAAAF,cAAA,GAAAC,CAAA;EACrBF,WAAA,aAAS;EAAA;EAAAC,cAAA,GAAAC,CAAA;EACTF,WAAA,aAAS;EAAA;EAAAC,cAAA,GAAAC,CAAA;EACTF,WAAA,iBAAa;AACf,CAAC;AAJW;AAAA,CAAAC,cAAA,GAAAG,CAAA,UAAAJ,WAAW;AAAA;AAAA,CAAAC,cAAA,GAAAG,CAAA,UAAAC,OAAA,CAAAL,WAAA,GAAXA,WAAW", "ignoreList": []}