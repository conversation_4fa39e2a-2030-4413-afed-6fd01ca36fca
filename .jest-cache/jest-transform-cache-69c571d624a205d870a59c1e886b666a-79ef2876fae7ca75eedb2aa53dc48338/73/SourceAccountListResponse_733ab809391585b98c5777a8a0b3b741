8ee87faa84e071a2e501ce4e9bb3987a
"use strict";

/* istanbul ignore next */
function cov_eqk7h203f() {
  var path = "/Users/<USER>/projects/react-native/payment-module/src/data/models/source-account-list/SourceAccountListResponse.ts";
  var hash = "c26792b522f8fc776418a977ae69af1c8b7fe08a";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/projects/react-native/payment-module/src/data/models/source-account-list/SourceAccountListResponse.ts",
    statementMap: {
      "0": {
        start: {
          line: 3,
          column: 0
        },
        end: {
          line: 5,
          column: 3
        }
      }
    },
    fnMap: {},
    branchMap: {},
    s: {
      "0": 0
    },
    f: {},
    b: {},
    inputSourceMap: {
      version: 3,
      file: undefined,
      names: [],
      sourceRoot: undefined,
      sources: ["/Users/<USER>/projects/react-native/payment-module/src/data/models/source-account-list/SourceAccountListResponse.ts"],
      sourcesContent: ["export interface SourceAccountListResponse {\n  totalCount: number;\n  data: SourceAccountResponse[];\n}\n\nexport interface SourceAccountUserPreferencesResponse {\n  arrangementId?: string;\n  alias?: string;\n  visible?: boolean | null;\n  favorite?: boolean;\n  additions?: any | null;\n}\n\nexport interface SourceAccountProductKindResponse {\n  externalKindId: string;\n  kindName: string;\n  kindUri: string;\n  expectsChildren: boolean;\n  additions: any | null;\n}\n\nexport interface SourceAccountProductResponse {\n  id: string;\n  translations: any[];\n  additions: any | null;\n  externalId: string;\n  externalTypeId: string | null;\n  typeName: string;\n  productKind: SourceAccountProductKindResponse;\n}\n\nexport interface SourceAccountResponse {\n  id: string;\n  productKindName: string;\n  legalEntityIds: string[];\n  productId: string;\n  productTypeName: string;\n  externalProductId: string;\n  externalArrangementId: string;\n  userPreferences?: SourceAccountUserPreferencesResponse;\n  product: SourceAccountProductResponse;\n  state?: any | null;\n  parentId?: string | null;\n  subscriptions?: any | null;\n  isDefault: string;\n  cifNo: string;\n  virtualAccountInfos?: any[];\n  additions?: any | null;\n  name: string;\n  bookedBalance?: number | null;\n  availableBalance?: number | null;\n  creditLimit?: number | null;\n  currency: string;\n  externalTransferAllowed?: boolean | null;\n  urgentTransferAllowed?: boolean | null;\n  accountOpeningDate?: string | null;\n  accountHolderNames?: string | null;\n  bankAlias?: string;\n  BBAN?: string;\n  IBAN?: string | null;\n  BIC?: string | null;\n  amountQR?: string | null;\n  contentQR?: string | null;\n}\n"],
      mappings: "",
      ignoreList: []
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "c26792b522f8fc776418a977ae69af1c8b7fe08a"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_eqk7h203f = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_eqk7h203f();
cov_eqk7h203f().s[0]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6W10sInNvdXJjZXMiOlsiL1VzZXJzL3NvbnNhbXNldC9wcm9qZWN0cy9yZWFjdC1uYXRpdmUvcGF5bWVudC1tb2R1bGUvc3JjL2RhdGEvbW9kZWxzL3NvdXJjZS1hY2NvdW50LWxpc3QvU291cmNlQWNjb3VudExpc3RSZXNwb25zZS50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgaW50ZXJmYWNlIFNvdXJjZUFjY291bnRMaXN0UmVzcG9uc2Uge1xuICB0b3RhbENvdW50OiBudW1iZXI7XG4gIGRhdGE6IFNvdXJjZUFjY291bnRSZXNwb25zZVtdO1xufVxuXG5leHBvcnQgaW50ZXJmYWNlIFNvdXJjZUFjY291bnRVc2VyUHJlZmVyZW5jZXNSZXNwb25zZSB7XG4gIGFycmFuZ2VtZW50SWQ/OiBzdHJpbmc7XG4gIGFsaWFzPzogc3RyaW5nO1xuICB2aXNpYmxlPzogYm9vbGVhbiB8IG51bGw7XG4gIGZhdm9yaXRlPzogYm9vbGVhbjtcbiAgYWRkaXRpb25zPzogYW55IHwgbnVsbDtcbn1cblxuZXhwb3J0IGludGVyZmFjZSBTb3VyY2VBY2NvdW50UHJvZHVjdEtpbmRSZXNwb25zZSB7XG4gIGV4dGVybmFsS2luZElkOiBzdHJpbmc7XG4gIGtpbmROYW1lOiBzdHJpbmc7XG4gIGtpbmRVcmk6IHN0cmluZztcbiAgZXhwZWN0c0NoaWxkcmVuOiBib29sZWFuO1xuICBhZGRpdGlvbnM6IGFueSB8IG51bGw7XG59XG5cbmV4cG9ydCBpbnRlcmZhY2UgU291cmNlQWNjb3VudFByb2R1Y3RSZXNwb25zZSB7XG4gIGlkOiBzdHJpbmc7XG4gIHRyYW5zbGF0aW9uczogYW55W107XG4gIGFkZGl0aW9uczogYW55IHwgbnVsbDtcbiAgZXh0ZXJuYWxJZDogc3RyaW5nO1xuICBleHRlcm5hbFR5cGVJZDogc3RyaW5nIHwgbnVsbDtcbiAgdHlwZU5hbWU6IHN0cmluZztcbiAgcHJvZHVjdEtpbmQ6IFNvdXJjZUFjY291bnRQcm9kdWN0S2luZFJlc3BvbnNlO1xufVxuXG5leHBvcnQgaW50ZXJmYWNlIFNvdXJjZUFjY291bnRSZXNwb25zZSB7XG4gIGlkOiBzdHJpbmc7XG4gIHByb2R1Y3RLaW5kTmFtZTogc3RyaW5nO1xuICBsZWdhbEVudGl0eUlkczogc3RyaW5nW107XG4gIHByb2R1Y3RJZDogc3RyaW5nO1xuICBwcm9kdWN0VHlwZU5hbWU6IHN0cmluZztcbiAgZXh0ZXJuYWxQcm9kdWN0SWQ6IHN0cmluZztcbiAgZXh0ZXJuYWxBcnJhbmdlbWVudElkOiBzdHJpbmc7XG4gIHVzZXJQcmVmZXJlbmNlcz86IFNvdXJjZUFjY291bnRVc2VyUHJlZmVyZW5jZXNSZXNwb25zZTtcbiAgcHJvZHVjdDogU291cmNlQWNjb3VudFByb2R1Y3RSZXNwb25zZTtcbiAgc3RhdGU/OiBhbnkgfCBudWxsO1xuICBwYXJlbnRJZD86IHN0cmluZyB8IG51bGw7XG4gIHN1YnNjcmlwdGlvbnM/OiBhbnkgfCBudWxsO1xuICBpc0RlZmF1bHQ6IHN0cmluZztcbiAgY2lmTm86IHN0cmluZztcbiAgdmlydHVhbEFjY291bnRJbmZvcz86IGFueVtdO1xuICBhZGRpdGlvbnM/OiBhbnkgfCBudWxsO1xuICBuYW1lOiBzdHJpbmc7XG4gIGJvb2tlZEJhbGFuY2U/OiBudW1iZXIgfCBudWxsO1xuICBhdmFpbGFibGVCYWxhbmNlPzogbnVtYmVyIHwgbnVsbDtcbiAgY3JlZGl0TGltaXQ/OiBudW1iZXIgfCBudWxsO1xuICBjdXJyZW5jeTogc3RyaW5nO1xuICBleHRlcm5hbFRyYW5zZmVyQWxsb3dlZD86IGJvb2xlYW4gfCBudWxsO1xuICB1cmdlbnRUcmFuc2ZlckFsbG93ZWQ/OiBib29sZWFuIHwgbnVsbDtcbiAgYWNjb3VudE9wZW5pbmdEYXRlPzogc3RyaW5nIHwgbnVsbDtcbiAgYWNjb3VudEhvbGRlck5hbWVzPzogc3RyaW5nIHwgbnVsbDtcbiAgYmFua0FsaWFzPzogc3RyaW5nO1xuICBCQkFOPzogc3RyaW5nO1xuICBJQkFOPzogc3RyaW5nIHwgbnVsbDtcbiAgQklDPzogc3RyaW5nIHwgbnVsbDtcbiAgYW1vdW50UVI/OiBzdHJpbmcgfCBudWxsO1xuICBjb250ZW50UVI/OiBzdHJpbmcgfCBudWxsO1xufVxuIl0sIm1hcHBpbmdzIjoiIiwiaWdub3JlTGlzdCI6W119