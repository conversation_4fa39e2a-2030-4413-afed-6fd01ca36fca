{"version": 3, "names": ["cov_bar9n1s0e", "actualCoverage", "GetProfileModel", "s", "_createClass2", "default", "serviceGroup", "f", "_this", "_classCallCheck2", "isBlocked", "exports"], "sources": ["/Users/<USER>/projects/react-native/payment-module/src/domain/entities/get-profile/GetProfileModel.ts"], "sourcesContent": ["export class GetProfileModel {\n  serviceGroup: string | undefined | null | unknown;\n\n  constructor(serviceGroup: string | undefined | null | unknown) {\n    this.serviceGroup = serviceGroup;\n  }\n\n  isBlocked = () => {\n    return this.serviceGroup === 'M_INQUIRY';\n  };\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAIS;IAAAA,aAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,aAAA;;;;;;;;;;;;;;;;;;IAJIE,eAAe;AAAA;AAAA,CAAAF,aAAA,GAAAG,CAAA,WAAAC,aAAA,CAAAC,OAAA,EAG1B,SAAAH,gBAAYI,YAAiD;EAAA;EAAAN,aAAA,GAAAO,CAAA;EAAA,IAAAC,KAAA;EAAA;EAAA,CAAAR,aAAA,GAAAG,CAAA;EAAA;EAAAH,aAAA,GAAAG,CAAA;EAAA,IAAAM,gBAAA,CAAAJ,OAAA,QAAAH,eAAA;EAAA;EAAAF,aAAA,GAAAG,CAAA;EAAA,KAI7DO,SAAS,GAAG,YAAK;IAAA;IAAAV,aAAA,GAAAO,CAAA;IAAAP,aAAA,GAAAG,CAAA;IACf,OAAOK,KAAI,CAACF,YAAY,KAAK,WAAW;EAC1C,CAAC;EAAA;EAAAN,aAAA,GAAAG,CAAA;EALC,IAAI,CAACG,YAAY,GAAGA,YAAY;AAClC,CAAC;AAAA;AAAAN,aAAA,GAAAG,CAAA;AALHQ,OAAA,CAAAT,eAAA,GAAAA,eAAA", "ignoreList": []}