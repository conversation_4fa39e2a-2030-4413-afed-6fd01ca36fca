eaef65622d8d296e418a80b0ccdb6a95
'use strict';

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.useAnimatedSensor = useAnimatedSensor;
var _react = require("react");
var _core = require("../core.js");
var _commonTypes = require("../commonTypes.js");
var _threads = require("../threads.js");
function eulerToQuaternion(pitch, roll, yaw) {
  'worklet';

  var c1 = Math.cos(pitch / 2);
  var s1 = Math.sin(pitch / 2);
  var c2 = Math.cos(roll / 2);
  var s2 = Math.sin(roll / 2);
  var c3 = Math.cos(yaw / 2);
  var s3 = Math.sin(yaw / 2);
  return [s1 * c2 * c3 - c1 * s2 * s3, c1 * s2 * c3 + s1 * c2 * s3, c1 * c2 * s3 + s1 * s2 * c3, c1 * c2 * c3 - s1 * s2 * s3];
}
function adjustRotationToInterfaceOrientation(data) {
  'worklet';

  var interfaceOrientation = data.interfaceOrientation,
    pitch = data.pitch,
    roll = data.roll,
    yaw = data.yaw;
  if (interfaceOrientation === _commonTypes.InterfaceOrientation.ROTATION_90) {
    data.pitch = roll;
    data.roll = -pitch;
    data.yaw = yaw - Math.PI / 2;
  } else if (interfaceOrientation === _commonTypes.InterfaceOrientation.ROTATION_270) {
    data.pitch = -roll;
    data.roll = pitch;
    data.yaw = yaw + Math.PI / 2;
  } else if (interfaceOrientation === _commonTypes.InterfaceOrientation.ROTATION_180) {
    data.pitch *= -1;
    data.roll *= -1;
    data.yaw *= -1;
  }
  var q = eulerToQuaternion(data.pitch, data.roll, data.yaw);
  data.qx = q[0];
  data.qy = q[1];
  data.qz = q[2];
  data.qw = q[3];
  return data;
}
function adjustVectorToInterfaceOrientation(data) {
  'worklet';

  var interfaceOrientation = data.interfaceOrientation,
    x = data.x,
    y = data.y;
  if (interfaceOrientation === _commonTypes.InterfaceOrientation.ROTATION_90) {
    data.x = -y;
    data.y = x;
  } else if (interfaceOrientation === _commonTypes.InterfaceOrientation.ROTATION_270) {
    data.x = y;
    data.y = -x;
  } else if (interfaceOrientation === _commonTypes.InterfaceOrientation.ROTATION_180) {
    data.x *= -1;
    data.y *= -1;
  }
  return data;
}
function useAnimatedSensor(sensorType, userConfig) {
  var _userConfigRef$curren, _userConfigRef$curren2, _userConfigRef$curren3;
  var userConfigRef = (0, _react.useRef)(userConfig);
  var hasConfigChanged = ((_userConfigRef$curren = userConfigRef.current) == null ? void 0 : _userConfigRef$curren.adjustToInterfaceOrientation) !== (userConfig == null ? void 0 : userConfig.adjustToInterfaceOrientation) || ((_userConfigRef$curren2 = userConfigRef.current) == null ? void 0 : _userConfigRef$curren2.interval) !== (userConfig == null ? void 0 : userConfig.interval) || ((_userConfigRef$curren3 = userConfigRef.current) == null ? void 0 : _userConfigRef$curren3.iosReferenceFrame) !== (userConfig == null ? void 0 : userConfig.iosReferenceFrame);
  if (hasConfigChanged) {
    userConfigRef.current = Object.assign({}, userConfig);
  }
  var config = (0, _react.useMemo)(function () {
    return Object.assign({
      interval: 'auto',
      adjustToInterfaceOrientation: true,
      iosReferenceFrame: _commonTypes.IOSReferenceFrame.Auto
    }, userConfigRef.current);
  }, [userConfigRef.current]);
  var ref = (0, _react.useRef)({
    sensor: (0, _core.initializeSensor)(sensorType, config),
    unregister: function unregister() {},
    isAvailable: false,
    config: config
  });
  (0, _react.useEffect)(function () {
    ref.current = {
      sensor: (0, _core.initializeSensor)(sensorType, config),
      unregister: function unregister() {},
      isAvailable: false,
      config: config
    };
    var sensorData = ref.current.sensor;
    var adjustToInterfaceOrientation = ref.current.config.adjustToInterfaceOrientation;
    var id = (0, _core.registerSensor)(sensorType, config, function (data) {
      'worklet';

      if (adjustToInterfaceOrientation) {
        if (sensorType === _commonTypes.SensorType.ROTATION) {
          data = adjustRotationToInterfaceOrientation(data);
        } else {
          data = adjustVectorToInterfaceOrientation(data);
        }
      }
      sensorData.value = data;
      (0, _threads.callMicrotasks)();
    });
    if (id !== -1) {
      ref.current.unregister = function () {
        return (0, _core.unregisterSensor)(id);
      };
      ref.current.isAvailable = true;
    } else {
      ref.current.unregister = function () {};
      ref.current.isAvailable = false;
    }
    return function () {
      ref.current.unregister();
    };
  }, [sensorType, config]);
  return ref.current;
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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