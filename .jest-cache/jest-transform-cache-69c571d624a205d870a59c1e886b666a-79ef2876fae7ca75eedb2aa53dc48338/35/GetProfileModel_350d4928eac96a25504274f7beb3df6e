fab1f37213a69f1e89d01ab4d718bafb
"use strict";

/* istanbul ignore next */
function cov_bar9n1s0e() {
  var path = "/Users/<USER>/projects/react-native/payment-module/src/domain/entities/get-profile/GetProfileModel.ts";
  var hash = "0059e2ce71186f9d9c48cce5eb5d8132149bbd7c";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/projects/react-native/payment-module/src/domain/entities/get-profile/GetProfileModel.ts",
    statementMap: {
      "0": {
        start: {
          line: 3,
          column: 29
        },
        end: {
          line: 3,
          column: 84
        }
      },
      "1": {
        start: {
          line: 4,
          column: 20
        },
        end: {
          line: 4,
          column: 89
        }
      },
      "2": {
        start: {
          line: 5,
          column: 23
        },
        end: {
          line: 5,
          column: 95
        }
      },
      "3": {
        start: {
          line: 6,
          column: 0
        },
        end: {
          line: 8,
          column: 3
        }
      },
      "4": {
        start: {
          line: 9,
          column: 0
        },
        end: {
          line: 9,
          column: 33
        }
      },
      "5": {
        start: {
          line: 10,
          column: 22
        },
        end: {
          line: 17,
          column: 2
        }
      },
      "6": {
        start: {
          line: 11,
          column: 14
        },
        end: {
          line: 11,
          column: 18
        }
      },
      "7": {
        start: {
          line: 12,
          column: 2
        },
        end: {
          line: 12,
          column: 55
        }
      },
      "8": {
        start: {
          line: 13,
          column: 2
        },
        end: {
          line: 15,
          column: 4
        }
      },
      "9": {
        start: {
          line: 14,
          column: 4
        },
        end: {
          line: 14,
          column: 46
        }
      },
      "10": {
        start: {
          line: 16,
          column: 2
        },
        end: {
          line: 16,
          column: 35
        }
      },
      "11": {
        start: {
          line: 18,
          column: 0
        },
        end: {
          line: 18,
          column: 42
        }
      }
    },
    fnMap: {
      "0": {
        name: "GetProfileModel",
        decl: {
          start: {
            line: 10,
            column: 58
          },
          end: {
            line: 10,
            column: 73
          }
        },
        loc: {
          start: {
            line: 10,
            column: 88
          },
          end: {
            line: 17,
            column: 1
          }
        },
        line: 10
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 13,
            column: 19
          },
          end: {
            line: 13,
            column: 20
          }
        },
        loc: {
          start: {
            line: 13,
            column: 31
          },
          end: {
            line: 15,
            column: 3
          }
        },
        line: 13
      }
    },
    branchMap: {},
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0
    },
    f: {
      "0": 0,
      "1": 0
    },
    b: {},
    inputSourceMap: {
      version: 3,
      file: undefined,
      names: ["GetProfileModel", "_createClass2", "default", "serviceGroup", "_this", "_classCallCheck2", "isBlocked", "exports"],
      sourceRoot: undefined,
      sources: ["/Users/<USER>/projects/react-native/payment-module/src/domain/entities/get-profile/GetProfileModel.ts"],
      sourcesContent: ["export class GetProfileModel {\n  serviceGroup: string | undefined | null | unknown;\n\n  constructor(serviceGroup: string | undefined | null | unknown) {\n    this.serviceGroup = serviceGroup;\n  }\n\n  isBlocked = () => {\n    return this.serviceGroup === 'M_INQUIRY';\n  };\n}\n"],
      mappings: ";;;;;;;;;IAAaA,eAAe,OAAAC,aAAA,CAAAC,OAAA,EAG1B,SAAAF,gBAAYG,YAAiD;EAAA,IAAAC,KAAA;EAAA,IAAAC,gBAAA,CAAAH,OAAA,QAAAF,eAAA;EAAA,KAI7DM,SAAS,GAAG,YAAK;IACf,OAAOF,KAAI,CAACD,YAAY,KAAK,WAAW;EAC1C,CAAC;EALC,IAAI,CAACA,YAAY,GAAGA,YAAY;AAClC,CAAC;AALHI,OAAA,CAAAP,eAAA,GAAAA,eAAA",
      ignoreList: []
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "0059e2ce71186f9d9c48cce5eb5d8132149bbd7c"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_bar9n1s0e = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_bar9n1s0e();
var _interopRequireDefault =
/* istanbul ignore next */
(cov_bar9n1s0e().s[0]++, require("@babel/runtime/helpers/interopRequireDefault"));
var _createClass2 =
/* istanbul ignore next */
(cov_bar9n1s0e().s[1]++, _interopRequireDefault(require("@babel/runtime/helpers/createClass")));
var _classCallCheck2 =
/* istanbul ignore next */
(cov_bar9n1s0e().s[2]++, _interopRequireDefault(require("@babel/runtime/helpers/classCallCheck")));
/* istanbul ignore next */
cov_bar9n1s0e().s[3]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_bar9n1s0e().s[4]++;
exports.GetProfileModel = void 0;
var GetProfileModel =
/* istanbul ignore next */
(cov_bar9n1s0e().s[5]++, (0, _createClass2.default)(function GetProfileModel(serviceGroup) {
  /* istanbul ignore next */
  cov_bar9n1s0e().f[0]++;
  var _this =
  /* istanbul ignore next */
  (cov_bar9n1s0e().s[6]++, this);
  /* istanbul ignore next */
  cov_bar9n1s0e().s[7]++;
  (0, _classCallCheck2.default)(this, GetProfileModel);
  /* istanbul ignore next */
  cov_bar9n1s0e().s[8]++;
  this.isBlocked = function () {
    /* istanbul ignore next */
    cov_bar9n1s0e().f[1]++;
    cov_bar9n1s0e().s[9]++;
    return _this.serviceGroup === 'M_INQUIRY';
  };
  /* istanbul ignore next */
  cov_bar9n1s0e().s[10]++;
  this.serviceGroup = serviceGroup;
}));
/* istanbul ignore next */
cov_bar9n1s0e().s[11]++;
exports.GetProfileModel = GetProfileModel;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJjb3ZfYmFyOW4xczBlIiwiYWN0dWFsQ292ZXJhZ2UiLCJHZXRQcm9maWxlTW9kZWwiLCJzIiwiX2NyZWF0ZUNsYXNzMiIsImRlZmF1bHQiLCJzZXJ2aWNlR3JvdXAiLCJmIiwiX3RoaXMiLCJfY2xhc3NDYWxsQ2hlY2syIiwiaXNCbG9ja2VkIiwiZXhwb3J0cyJdLCJzb3VyY2VzIjpbIi9Vc2Vycy9zb25zYW1zZXQvcHJvamVjdHMvcmVhY3QtbmF0aXZlL3BheW1lbnQtbW9kdWxlL3NyYy9kb21haW4vZW50aXRpZXMvZ2V0LXByb2ZpbGUvR2V0UHJvZmlsZU1vZGVsLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBjbGFzcyBHZXRQcm9maWxlTW9kZWwge1xuICBzZXJ2aWNlR3JvdXA6IHN0cmluZyB8IHVuZGVmaW5lZCB8IG51bGwgfCB1bmtub3duO1xuXG4gIGNvbnN0cnVjdG9yKHNlcnZpY2VHcm91cDogc3RyaW5nIHwgdW5kZWZpbmVkIHwgbnVsbCB8IHVua25vd24pIHtcbiAgICB0aGlzLnNlcnZpY2VHcm91cCA9IHNlcnZpY2VHcm91cDtcbiAgfVxuXG4gIGlzQmxvY2tlZCA9ICgpID0+IHtcbiAgICByZXR1cm4gdGhpcy5zZXJ2aWNlR3JvdXAgPT09ICdNX0lOUVVJUlknO1xuICB9O1xufVxuIl0sIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztJQUlTO0lBQUFBLGFBQUEsWUFBQUEsQ0FBQTtNQUFBLE9BQUFDLGNBQUE7SUFBQTtFQUFBO0VBQUEsT0FBQUEsY0FBQTtBQUFBO0FBQUFELGFBQUE7Ozs7Ozs7Ozs7Ozs7Ozs7OztJQUpJRSxlQUFlO0FBQUE7QUFBQSxDQUFBRixhQUFBLEdBQUFHLENBQUEsV0FBQUMsYUFBQSxDQUFBQyxPQUFBLEVBRzFCLFNBQUFILGdCQUFZSSxZQUFpRDtFQUFBO0VBQUFOLGFBQUEsR0FBQU8sQ0FBQTtFQUFBLElBQUFDLEtBQUE7RUFBQTtFQUFBLENBQUFSLGFBQUEsR0FBQUcsQ0FBQTtFQUFBO0VBQUFILGFBQUEsR0FBQUcsQ0FBQTtFQUFBLElBQUFNLGdCQUFBLENBQUFKLE9BQUEsUUFBQUgsZUFBQTtFQUFBO0VBQUFGLGFBQUEsR0FBQUcsQ0FBQTtFQUFBLEtBSTdETyxTQUFTLEdBQUcsWUFBSztJQUFBO0lBQUFWLGFBQUEsR0FBQU8sQ0FBQTtJQUFBUCxhQUFBLEdBQUFHLENBQUE7SUFDZixPQUFPSyxLQUFJLENBQUNGLFlBQVksS0FBSyxXQUFXO0VBQzFDLENBQUM7RUFBQTtFQUFBTixhQUFBLEdBQUFHLENBQUE7RUFMQyxJQUFJLENBQUNHLFlBQVksR0FBR0EsWUFBWTtBQUNsQyxDQUFDO0FBQUE7QUFBQU4sYUFBQSxHQUFBRyxDQUFBO0FBTEhRLE9BQUEsQ0FBQVQsZUFBQSxHQUFBQSxlQUFBIiwiaWdub3JlTGlzdCI6W119