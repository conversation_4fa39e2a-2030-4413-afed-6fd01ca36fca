{"version": 3, "names": ["cov_1w02tnk0rs", "actualCoverage", "s", "msb_shared_component_1", "require", "exports", "makeStyle", "createMSBStyleSheet", "_ref", "f", "ColorGlobal", "ColorField", "ColorDataView", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Shadow", "Typography", "btnConfirm", "marginHorizontal", "SpacingSmall", "container", "flex", "contentContainer", "Object", "assign", "backgroundColor", "NeutralWhite", "borderRadius", "SpacingXSmall", "padding", "center", "contentContainerScrollView", "imageBackground", "resizeMode", "space", "marginTop", "txtName", "b", "base_semiBold", "color", "TextMain", "transferConfirm", "borderBottomColor", "BorderDefault", "borderBottomWidth", "paddingBottom", "txtAmount", "h4_bold", "Spacing4xSmall", "txtCurrency", "Neutral600", "txtAmountWords", "small_regular", "TextSub", "txtTransferConfirm", "txtTitle", "txtValue", "base_medium", "accountInfo", "SizeGlobal", "Size500"], "sources": ["/Users/<USER>/projects/react-native/payment-module/src/presentation/payment-confirm/styles.ts"], "sourcesContent": ["import {createMSBStyleSheet, SizeGlobal} from 'msb-shared-component';\n\nexport const makeStyle = createMSBStyleSheet(\n  ({ColorGlobal, ColorField, ColorDataView, SizeAlias, Shadow, Typography}) => {\n    return {\n      btnConfirm: {\n        marginHorizontal: SizeAlias.SpacingSmall,\n      },\n      container: {\n        flex: 1,\n      },\n\n      contentContainer: {\n        backgroundColor: ColorGlobal.NeutralWhite,\n        borderRadius: SizeAlias.SpacingXSmall,\n        padding: SizeAlias.SpacingSmall,\n        ...Shadow.center,\n      },\n      contentContainerScrollView: {\n        padding: SizeAlias.SpacingSmall,\n      },\n\n      imageBackground: {\n        flex: 1,\n        resizeMode: 'stretch',\n      },\n\n      space: {\n        marginTop: SizeAlias.SpacingSmall,\n      },\n      txtName: {\n        ...Typography?.base_semiBold,\n        color: ColorDataView.TextMain,\n      },\n      // transfer confirm\n      transferConfirm: {\n        borderBottomColor: ColorField.BorderDefault,\n        borderBottomWidth: 1,\n        paddingBottom: SizeAlias.SpacingSmall,\n      },\n      txtAmount: {\n        ...Typography?.h4_bold,\n        color: ColorDataView.TextMain,\n        marginTop: SizeAlias.Spacing4xSmall,\n      },\n      txtCurrency: {\n        ...Typography?.base_semiBold,\n        color: ColorGlobal.Neutral600,\n      },\n      txtAmountWords: {\n        ...Typography?.small_regular,\n        color: ColorDataView.TextSub,\n        marginTop: SizeAlias.Spacing4xSmall,\n      },\n      txtTransferConfirm: {\n        ...Typography?.base_semiBold,\n        color: ColorDataView.TextSub,\n      },\n      txtTitle: {\n        ...Typography?.small_regular,\n        color: ColorDataView.TextSub,\n        marginTop: SizeAlias.Spacing4xSmall,\n      },\n      txtValue: {\n        ...Typography?.base_medium,\n        color: ColorDataView.TextMain,\n        marginTop: SizeAlias.Spacing4xSmall,\n      },\n      accountInfo: {\n        backgroundColor: ColorGlobal.NeutralWhite,\n        marginTop: SizeGlobal.Size500,\n      },\n    };\n  },\n);\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAKM;IAAAA,cAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,cAAA;AAAAA,cAAA,GAAAE,CAAA;;;;;;;AALN,IAAAC,sBAAA;AAAA;AAAA,CAAAH,cAAA,GAAAE,CAAA,OAAAE,OAAA;AAAA;AAAAJ,cAAA,GAAAE,CAAA;AAEaG,OAAA,CAAAC,SAAS,GAAG,IAAAH,sBAAA,CAAAI,mBAAmB,EAC1C,UAAAC,IAAA,EAA4E;EAAA;EAAAR,cAAA,GAAAS,CAAA;EAAA,IAA1EC,WAAW;IAAA;IAAA,CAAAV,cAAA,GAAAE,CAAA,OAAAM,IAAA,CAAXE,WAAW;IAAEC,UAAU;IAAA;IAAA,CAAAX,cAAA,GAAAE,CAAA,OAAAM,IAAA,CAAVG,UAAU;IAAEC,aAAa;IAAA;IAAA,CAAAZ,cAAA,GAAAE,CAAA,OAAAM,IAAA,CAAbI,aAAa;IAAEC,SAAS;IAAA;IAAA,CAAAb,cAAA,GAAAE,CAAA,OAAAM,IAAA,CAATK,SAAS;IAAEC,MAAM;IAAA;IAAA,CAAAd,cAAA,GAAAE,CAAA,OAAAM,IAAA,CAANM,MAAM;IAAEC,UAAU;IAAA;IAAA,CAAAf,cAAA,GAAAE,CAAA,OAAAM,IAAA,CAAVO,UAAU;EAAA;EAAAf,cAAA,GAAAE,CAAA;EACrE,OAAO;IACLc,UAAU,EAAE;MACVC,gBAAgB,EAAEJ,SAAS,CAACK;KAC7B;IACDC,SAAS,EAAE;MACTC,IAAI,EAAE;KACP;IAEDC,gBAAgB,EAAAC,MAAA,CAAAC,MAAA;MACdC,eAAe,EAAEd,WAAW,CAACe,YAAY;MACzCC,YAAY,EAAEb,SAAS,CAACc,aAAa;MACrCC,OAAO,EAAEf,SAAS,CAACK;IAAY,GAC5BJ,MAAM,CAACe,MAAM,CACjB;IACDC,0BAA0B,EAAE;MAC1BF,OAAO,EAAEf,SAAS,CAACK;KACpB;IAEDa,eAAe,EAAE;MACfX,IAAI,EAAE,CAAC;MACPY,UAAU,EAAE;KACb;IAEDC,KAAK,EAAE;MACLC,SAAS,EAAErB,SAAS,CAACK;KACtB;IACDiB,OAAO,EAAAb,MAAA,CAAAC,MAAA,KACFR,UAAU;IAAA;IAAA,CAAAf,cAAA,GAAAoC,CAAA;IAAA;IAAA,CAAApC,cAAA,GAAAoC,CAAA,UAAVrB,UAAU,CAAEsB,aAAa;MAC5BC,KAAK,EAAE1B,aAAa,CAAC2B;IAAQ,EAC9B;IAEDC,eAAe,EAAE;MACfC,iBAAiB,EAAE9B,UAAU,CAAC+B,aAAa;MAC3CC,iBAAiB,EAAE,CAAC;MACpBC,aAAa,EAAE/B,SAAS,CAACK;KAC1B;IACD2B,SAAS,EAAAvB,MAAA,CAAAC,MAAA,KACJR,UAAU;IAAA;IAAA,CAAAf,cAAA,GAAAoC,CAAA;IAAA;IAAA,CAAApC,cAAA,GAAAoC,CAAA,UAAVrB,UAAU,CAAE+B,OAAO;MACtBR,KAAK,EAAE1B,aAAa,CAAC2B,QAAQ;MAC7BL,SAAS,EAAErB,SAAS,CAACkC;IAAc,EACpC;IACDC,WAAW,EAAA1B,MAAA,CAAAC,MAAA,KACNR,UAAU;IAAA;IAAA,CAAAf,cAAA,GAAAoC,CAAA;IAAA;IAAA,CAAApC,cAAA,GAAAoC,CAAA,UAAVrB,UAAU,CAAEsB,aAAa;MAC5BC,KAAK,EAAE5B,WAAW,CAACuC;IAAU,EAC9B;IACDC,cAAc,EAAA5B,MAAA,CAAAC,MAAA,KACTR,UAAU;IAAA;IAAA,CAAAf,cAAA,GAAAoC,CAAA;IAAA;IAAA,CAAApC,cAAA,GAAAoC,CAAA,UAAVrB,UAAU,CAAEoC,aAAa;MAC5Bb,KAAK,EAAE1B,aAAa,CAACwC,OAAO;MAC5BlB,SAAS,EAAErB,SAAS,CAACkC;IAAc,EACpC;IACDM,kBAAkB,EAAA/B,MAAA,CAAAC,MAAA,KACbR,UAAU;IAAA;IAAA,CAAAf,cAAA,GAAAoC,CAAA;IAAA;IAAA,CAAApC,cAAA,GAAAoC,CAAA,UAAVrB,UAAU,CAAEsB,aAAa;MAC5BC,KAAK,EAAE1B,aAAa,CAACwC;IAAO,EAC7B;IACDE,QAAQ,EAAAhC,MAAA,CAAAC,MAAA,KACHR,UAAU;IAAA;IAAA,CAAAf,cAAA,GAAAoC,CAAA;IAAA;IAAA,CAAApC,cAAA,GAAAoC,CAAA,UAAVrB,UAAU,CAAEoC,aAAa;MAC5Bb,KAAK,EAAE1B,aAAa,CAACwC,OAAO;MAC5BlB,SAAS,EAAErB,SAAS,CAACkC;IAAc,EACpC;IACDQ,QAAQ,EAAAjC,MAAA,CAAAC,MAAA,KACHR,UAAU;IAAA;IAAA,CAAAf,cAAA,GAAAoC,CAAA;IAAA;IAAA,CAAApC,cAAA,GAAAoC,CAAA,UAAVrB,UAAU,CAAEyC,WAAW;MAC1BlB,KAAK,EAAE1B,aAAa,CAAC2B,QAAQ;MAC7BL,SAAS,EAAErB,SAAS,CAACkC;IAAc,EACpC;IACDU,WAAW,EAAE;MACXjC,eAAe,EAAEd,WAAW,CAACe,YAAY;MACzCS,SAAS,EAAE/B,sBAAA,CAAAuD,UAAU,CAACC;;GAEzB;AACH,CAAC,CACF", "ignoreList": []}