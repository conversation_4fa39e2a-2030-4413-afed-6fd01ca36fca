d1f43fcf91b9c357b3291815161b24bd
"use strict";

/* istanbul ignore next */
function cov_1w02tnk0rs() {
  var path = "/Users/<USER>/projects/react-native/payment-module/src/presentation/payment-confirm/styles.ts";
  var hash = "ecb6189bce0f4a0a3ddeef659ef1c63f9102dff8";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/projects/react-native/payment-module/src/presentation/payment-confirm/styles.ts",
    statementMap: {
      "0": {
        start: {
          line: 3,
          column: 0
        },
        end: {
          line: 5,
          column: 3
        }
      },
      "1": {
        start: {
          line: 6,
          column: 0
        },
        end: {
          line: 6,
          column: 27
        }
      },
      "2": {
        start: {
          line: 7,
          column: 29
        },
        end: {
          line: 7,
          column: 60
        }
      },
      "3": {
        start: {
          line: 8,
          column: 0
        },
        end: {
          line: 72,
          column: 3
        }
      },
      "4": {
        start: {
          line: 9,
          column: 20
        },
        end: {
          line: 9,
          column: 36
        }
      },
      "5": {
        start: {
          line: 10,
          column: 17
        },
        end: {
          line: 10,
          column: 32
        }
      },
      "6": {
        start: {
          line: 11,
          column: 20
        },
        end: {
          line: 11,
          column: 38
        }
      },
      "7": {
        start: {
          line: 12,
          column: 16
        },
        end: {
          line: 12,
          column: 30
        }
      },
      "8": {
        start: {
          line: 13,
          column: 13
        },
        end: {
          line: 13,
          column: 24
        }
      },
      "9": {
        start: {
          line: 14,
          column: 17
        },
        end: {
          line: 14,
          column: 32
        }
      },
      "10": {
        start: {
          line: 15,
          column: 2
        },
        end: {
          line: 71,
          column: 4
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 8,
            column: 68
          },
          end: {
            line: 8,
            column: 69
          }
        },
        loc: {
          start: {
            line: 8,
            column: 84
          },
          end: {
            line: 72,
            column: 1
          }
        },
        line: 8
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 37,
            column: 31
          },
          end: {
            line: 37,
            column: 85
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 37,
            column: 52
          },
          end: {
            line: 37,
            column: 58
          }
        }, {
          start: {
            line: 37,
            column: 61
          },
          end: {
            line: 37,
            column: 85
          }
        }],
        line: 37
      },
      "1": {
        loc: {
          start: {
            line: 45,
            column: 33
          },
          end: {
            line: 45,
            column: 81
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 45,
            column: 54
          },
          end: {
            line: 45,
            column: 60
          }
        }, {
          start: {
            line: 45,
            column: 63
          },
          end: {
            line: 45,
            column: 81
          }
        }],
        line: 45
      },
      "2": {
        loc: {
          start: {
            line: 49,
            column: 35
          },
          end: {
            line: 49,
            column: 89
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 49,
            column: 56
          },
          end: {
            line: 49,
            column: 62
          }
        }, {
          start: {
            line: 49,
            column: 65
          },
          end: {
            line: 49,
            column: 89
          }
        }],
        line: 49
      },
      "3": {
        loc: {
          start: {
            line: 52,
            column: 38
          },
          end: {
            line: 52,
            column: 92
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 52,
            column: 59
          },
          end: {
            line: 52,
            column: 65
          }
        }, {
          start: {
            line: 52,
            column: 68
          },
          end: {
            line: 52,
            column: 92
          }
        }],
        line: 52
      },
      "4": {
        loc: {
          start: {
            line: 56,
            column: 42
          },
          end: {
            line: 56,
            column: 96
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 56,
            column: 63
          },
          end: {
            line: 56,
            column: 69
          }
        }, {
          start: {
            line: 56,
            column: 72
          },
          end: {
            line: 56,
            column: 96
          }
        }],
        line: 56
      },
      "5": {
        loc: {
          start: {
            line: 59,
            column: 32
          },
          end: {
            line: 59,
            column: 86
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 59,
            column: 53
          },
          end: {
            line: 59,
            column: 59
          }
        }, {
          start: {
            line: 59,
            column: 62
          },
          end: {
            line: 59,
            column: 86
          }
        }],
        line: 59
      },
      "6": {
        loc: {
          start: {
            line: 63,
            column: 32
          },
          end: {
            line: 63,
            column: 84
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 63,
            column: 53
          },
          end: {
            line: 63,
            column: 59
          }
        }, {
          start: {
            line: 63,
            column: 62
          },
          end: {
            line: 63,
            column: 84
          }
        }],
        line: 63
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0
    },
    f: {
      "0": 0
    },
    b: {
      "0": [0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0]
    },
    inputSourceMap: {
      version: 3,
      file: undefined,
      names: ["msb_shared_component_1", "require", "exports", "makeStyle", "createMSBStyleSheet", "_ref", "ColorGlobal", "ColorField", "ColorDataView", "SizeAlias", "Shadow", "Typography", "btnConfirm", "marginHorizontal", "SpacingSmall", "container", "flex", "contentContainer", "Object", "assign", "backgroundColor", "NeutralWhite", "borderRadius", "SpacingXSmall", "padding", "center", "contentContainerScrollView", "imageBackground", "resizeMode", "space", "marginTop", "txtName", "base_semiBold", "color", "TextMain", "transferConfirm", "borderBottomColor", "BorderDefault", "borderBottomWidth", "paddingBottom", "txtAmount", "h4_bold", "Spacing4xSmall", "txtCurrency", "Neutral600", "txtAmountWords", "small_regular", "TextSub", "txtTransferConfirm", "txtTitle", "txtValue", "base_medium", "accountInfo", "SizeGlobal", "Size500"],
      sourceRoot: undefined,
      sources: ["/Users/<USER>/projects/react-native/payment-module/src/presentation/payment-confirm/styles.ts"],
      sourcesContent: ["import {createMSBStyleSheet, SizeGlobal} from 'msb-shared-component';\n\nexport const makeStyle = createMSBStyleSheet(\n  ({ColorGlobal, ColorField, ColorDataView, SizeAlias, Shadow, Typography}) => {\n    return {\n      btnConfirm: {\n        marginHorizontal: SizeAlias.SpacingSmall,\n      },\n      container: {\n        flex: 1,\n      },\n\n      contentContainer: {\n        backgroundColor: ColorGlobal.NeutralWhite,\n        borderRadius: SizeAlias.SpacingXSmall,\n        padding: SizeAlias.SpacingSmall,\n        ...Shadow.center,\n      },\n      contentContainerScrollView: {\n        padding: SizeAlias.SpacingSmall,\n      },\n\n      imageBackground: {\n        flex: 1,\n        resizeMode: 'stretch',\n      },\n\n      space: {\n        marginTop: SizeAlias.SpacingSmall,\n      },\n      txtName: {\n        ...Typography?.base_semiBold,\n        color: ColorDataView.TextMain,\n      },\n      // transfer confirm\n      transferConfirm: {\n        borderBottomColor: ColorField.BorderDefault,\n        borderBottomWidth: 1,\n        paddingBottom: SizeAlias.SpacingSmall,\n      },\n      txtAmount: {\n        ...Typography?.h4_bold,\n        color: ColorDataView.TextMain,\n        marginTop: SizeAlias.Spacing4xSmall,\n      },\n      txtCurrency: {\n        ...Typography?.base_semiBold,\n        color: ColorGlobal.Neutral600,\n      },\n      txtAmountWords: {\n        ...Typography?.small_regular,\n        color: ColorDataView.TextSub,\n        marginTop: SizeAlias.Spacing4xSmall,\n      },\n      txtTransferConfirm: {\n        ...Typography?.base_semiBold,\n        color: ColorDataView.TextSub,\n      },\n      txtTitle: {\n        ...Typography?.small_regular,\n        color: ColorDataView.TextSub,\n        marginTop: SizeAlias.Spacing4xSmall,\n      },\n      txtValue: {\n        ...Typography?.base_medium,\n        color: ColorDataView.TextMain,\n        marginTop: SizeAlias.Spacing4xSmall,\n      },\n      accountInfo: {\n        backgroundColor: ColorGlobal.NeutralWhite,\n        marginTop: SizeGlobal.Size500,\n      },\n    };\n  },\n);\n"],
      mappings: ";;;;;;AAAA,IAAAA,sBAAA,GAAAC,OAAA;AAEaC,OAAA,CAAAC,SAAS,GAAG,IAAAH,sBAAA,CAAAI,mBAAmB,EAC1C,UAAAC,IAAA,EAA4E;EAAA,IAA1EC,WAAW,GAAAD,IAAA,CAAXC,WAAW;IAAEC,UAAU,GAAAF,IAAA,CAAVE,UAAU;IAAEC,aAAa,GAAAH,IAAA,CAAbG,aAAa;IAAEC,SAAS,GAAAJ,IAAA,CAATI,SAAS;IAAEC,MAAM,GAAAL,IAAA,CAANK,MAAM;IAAEC,UAAU,GAAAN,IAAA,CAAVM,UAAU;EACrE,OAAO;IACLC,UAAU,EAAE;MACVC,gBAAgB,EAAEJ,SAAS,CAACK;KAC7B;IACDC,SAAS,EAAE;MACTC,IAAI,EAAE;KACP;IAEDC,gBAAgB,EAAAC,MAAA,CAAAC,MAAA;MACdC,eAAe,EAAEd,WAAW,CAACe,YAAY;MACzCC,YAAY,EAAEb,SAAS,CAACc,aAAa;MACrCC,OAAO,EAAEf,SAAS,CAACK;IAAY,GAC5BJ,MAAM,CAACe,MAAM,CACjB;IACDC,0BAA0B,EAAE;MAC1BF,OAAO,EAAEf,SAAS,CAACK;KACpB;IAEDa,eAAe,EAAE;MACfX,IAAI,EAAE,CAAC;MACPY,UAAU,EAAE;KACb;IAEDC,KAAK,EAAE;MACLC,SAAS,EAAErB,SAAS,CAACK;KACtB;IACDiB,OAAO,EAAAb,MAAA,CAAAC,MAAA,KACFR,UAAU,oBAAVA,UAAU,CAAEqB,aAAa;MAC5BC,KAAK,EAAEzB,aAAa,CAAC0B;IAAQ,EAC9B;IAEDC,eAAe,EAAE;MACfC,iBAAiB,EAAE7B,UAAU,CAAC8B,aAAa;MAC3CC,iBAAiB,EAAE,CAAC;MACpBC,aAAa,EAAE9B,SAAS,CAACK;KAC1B;IACD0B,SAAS,EAAAtB,MAAA,CAAAC,MAAA,KACJR,UAAU,oBAAVA,UAAU,CAAE8B,OAAO;MACtBR,KAAK,EAAEzB,aAAa,CAAC0B,QAAQ;MAC7BJ,SAAS,EAAErB,SAAS,CAACiC;IAAc,EACpC;IACDC,WAAW,EAAAzB,MAAA,CAAAC,MAAA,KACNR,UAAU,oBAAVA,UAAU,CAAEqB,aAAa;MAC5BC,KAAK,EAAE3B,WAAW,CAACsC;IAAU,EAC9B;IACDC,cAAc,EAAA3B,MAAA,CAAAC,MAAA,KACTR,UAAU,oBAAVA,UAAU,CAAEmC,aAAa;MAC5Bb,KAAK,EAAEzB,aAAa,CAACuC,OAAO;MAC5BjB,SAAS,EAAErB,SAAS,CAACiC;IAAc,EACpC;IACDM,kBAAkB,EAAA9B,MAAA,CAAAC,MAAA,KACbR,UAAU,oBAAVA,UAAU,CAAEqB,aAAa;MAC5BC,KAAK,EAAEzB,aAAa,CAACuC;IAAO,EAC7B;IACDE,QAAQ,EAAA/B,MAAA,CAAAC,MAAA,KACHR,UAAU,oBAAVA,UAAU,CAAEmC,aAAa;MAC5Bb,KAAK,EAAEzB,aAAa,CAACuC,OAAO;MAC5BjB,SAAS,EAAErB,SAAS,CAACiC;IAAc,EACpC;IACDQ,QAAQ,EAAAhC,MAAA,CAAAC,MAAA,KACHR,UAAU,oBAAVA,UAAU,CAAEwC,WAAW;MAC1BlB,KAAK,EAAEzB,aAAa,CAAC0B,QAAQ;MAC7BJ,SAAS,EAAErB,SAAS,CAACiC;IAAc,EACpC;IACDU,WAAW,EAAE;MACXhC,eAAe,EAAEd,WAAW,CAACe,YAAY;MACzCS,SAAS,EAAE9B,sBAAA,CAAAqD,UAAU,CAACC;;GAEzB;AACH,CAAC,CACF",
      ignoreList: []
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "ecb6189bce0f4a0a3ddeef659ef1c63f9102dff8"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_1w02tnk0rs = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_1w02tnk0rs();
cov_1w02tnk0rs().s[0]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_1w02tnk0rs().s[1]++;
exports.makeStyle = void 0;
var msb_shared_component_1 =
/* istanbul ignore next */
(cov_1w02tnk0rs().s[2]++, require("msb-shared-component"));
/* istanbul ignore next */
cov_1w02tnk0rs().s[3]++;
exports.makeStyle = (0, msb_shared_component_1.createMSBStyleSheet)(function (_ref) {
  /* istanbul ignore next */
  cov_1w02tnk0rs().f[0]++;
  var ColorGlobal =
    /* istanbul ignore next */
    (cov_1w02tnk0rs().s[4]++, _ref.ColorGlobal),
    ColorField =
    /* istanbul ignore next */
    (cov_1w02tnk0rs().s[5]++, _ref.ColorField),
    ColorDataView =
    /* istanbul ignore next */
    (cov_1w02tnk0rs().s[6]++, _ref.ColorDataView),
    SizeAlias =
    /* istanbul ignore next */
    (cov_1w02tnk0rs().s[7]++, _ref.SizeAlias),
    Shadow =
    /* istanbul ignore next */
    (cov_1w02tnk0rs().s[8]++, _ref.Shadow),
    Typography =
    /* istanbul ignore next */
    (cov_1w02tnk0rs().s[9]++, _ref.Typography);
  /* istanbul ignore next */
  cov_1w02tnk0rs().s[10]++;
  return {
    btnConfirm: {
      marginHorizontal: SizeAlias.SpacingSmall
    },
    container: {
      flex: 1
    },
    contentContainer: Object.assign({
      backgroundColor: ColorGlobal.NeutralWhite,
      borderRadius: SizeAlias.SpacingXSmall,
      padding: SizeAlias.SpacingSmall
    }, Shadow.center),
    contentContainerScrollView: {
      padding: SizeAlias.SpacingSmall
    },
    imageBackground: {
      flex: 1,
      resizeMode: 'stretch'
    },
    space: {
      marginTop: SizeAlias.SpacingSmall
    },
    txtName: Object.assign({}, Typography == null ?
    /* istanbul ignore next */
    (cov_1w02tnk0rs().b[0][0]++, void 0) :
    /* istanbul ignore next */
    (cov_1w02tnk0rs().b[0][1]++, Typography.base_semiBold), {
      color: ColorDataView.TextMain
    }),
    transferConfirm: {
      borderBottomColor: ColorField.BorderDefault,
      borderBottomWidth: 1,
      paddingBottom: SizeAlias.SpacingSmall
    },
    txtAmount: Object.assign({}, Typography == null ?
    /* istanbul ignore next */
    (cov_1w02tnk0rs().b[1][0]++, void 0) :
    /* istanbul ignore next */
    (cov_1w02tnk0rs().b[1][1]++, Typography.h4_bold), {
      color: ColorDataView.TextMain,
      marginTop: SizeAlias.Spacing4xSmall
    }),
    txtCurrency: Object.assign({}, Typography == null ?
    /* istanbul ignore next */
    (cov_1w02tnk0rs().b[2][0]++, void 0) :
    /* istanbul ignore next */
    (cov_1w02tnk0rs().b[2][1]++, Typography.base_semiBold), {
      color: ColorGlobal.Neutral600
    }),
    txtAmountWords: Object.assign({}, Typography == null ?
    /* istanbul ignore next */
    (cov_1w02tnk0rs().b[3][0]++, void 0) :
    /* istanbul ignore next */
    (cov_1w02tnk0rs().b[3][1]++, Typography.small_regular), {
      color: ColorDataView.TextSub,
      marginTop: SizeAlias.Spacing4xSmall
    }),
    txtTransferConfirm: Object.assign({}, Typography == null ?
    /* istanbul ignore next */
    (cov_1w02tnk0rs().b[4][0]++, void 0) :
    /* istanbul ignore next */
    (cov_1w02tnk0rs().b[4][1]++, Typography.base_semiBold), {
      color: ColorDataView.TextSub
    }),
    txtTitle: Object.assign({}, Typography == null ?
    /* istanbul ignore next */
    (cov_1w02tnk0rs().b[5][0]++, void 0) :
    /* istanbul ignore next */
    (cov_1w02tnk0rs().b[5][1]++, Typography.small_regular), {
      color: ColorDataView.TextSub,
      marginTop: SizeAlias.Spacing4xSmall
    }),
    txtValue: Object.assign({}, Typography == null ?
    /* istanbul ignore next */
    (cov_1w02tnk0rs().b[6][0]++, void 0) :
    /* istanbul ignore next */
    (cov_1w02tnk0rs().b[6][1]++, Typography.base_medium), {
      color: ColorDataView.TextMain,
      marginTop: SizeAlias.Spacing4xSmall
    }),
    accountInfo: {
      backgroundColor: ColorGlobal.NeutralWhite,
      marginTop: msb_shared_component_1.SizeGlobal.Size500
    }
  };
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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