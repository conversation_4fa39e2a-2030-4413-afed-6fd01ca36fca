{"version": 3, "names": ["Object", "defineProperty", "exports", "value", "useAnimatedSensor", "_react", "require", "_core", "_commonTypes", "_threads", "eulerToQuaternion", "pitch", "roll", "yaw", "c1", "Math", "cos", "s1", "sin", "c2", "s2", "c3", "s3", "adjustRotationToInterfaceOrientation", "data", "interfaceOrientation", "InterfaceOrientation", "ROTATION_90", "PI", "ROTATION_270", "ROTATION_180", "q", "qx", "qy", "qz", "qw", "adjustVectorToInterfaceOrientation", "x", "y", "sensorType", "userConfig", "_userConfigRef$curren", "_userConfigRef$curren2", "_userConfigRef$curren3", "userConfigRef", "useRef", "hasConfigChanged", "current", "adjustToInterfaceOrientation", "interval", "iosReferenceFrame", "assign", "config", "useMemo", "IOSReferenceFrame", "Auto", "ref", "sensor", "initializeSensor", "unregister", "isAvailable", "useEffect", "sensorData", "id", "registerSensor", "SensorType", "ROTATION", "callMicrotasks", "unregisterSensor"], "sources": ["../../../src/hook/useAnimatedSensor.ts"], "sourcesContent": [null], "mappings": "AAAA,YAAY;;AAAAA,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,KAAA;AAAA;AAAAD,OAAA,CAAAE,iBAAA,GAAAA,iBAAA;AACZ,IAAAC,MAAA,GAAAC,OAAA;AACA,IAAAC,KAAA,GAAAD,OAAA;AAOA,IAAAE,YAAA,GAAAF,OAAA;AAKA,IAAAG,QAAA,GAAAH,OAAA;AAIA,SAASI,iBAAiBA,CAACC,KAAa,EAAEC,IAAY,EAAEC,GAAW,EAAE;EACnE,SAAS;;EACT,IAAMC,EAAE,GAAGC,IAAI,CAACC,GAAG,CAACL,KAAK,GAAG,CAAC,CAAC;EAC9B,IAAMM,EAAE,GAAGF,IAAI,CAACG,GAAG,CAACP,KAAK,GAAG,CAAC,CAAC;EAC9B,IAAMQ,EAAE,GAAGJ,IAAI,CAACC,GAAG,CAACJ,IAAI,GAAG,CAAC,CAAC;EAC7B,IAAMQ,EAAE,GAAGL,IAAI,CAACG,GAAG,CAACN,IAAI,GAAG,CAAC,CAAC;EAC7B,IAAMS,EAAE,GAAGN,IAAI,CAACC,GAAG,CAACH,GAAG,GAAG,CAAC,CAAC;EAC5B,IAAMS,EAAE,GAAGP,IAAI,CAACG,GAAG,CAACL,GAAG,GAAG,CAAC,CAAC;EAE5B,OAAO,CACLI,EAAE,GAAGE,EAAE,GAAGE,EAAE,GAAGP,EAAE,GAAGM,EAAE,GAAGE,EAAE,EAC3BR,EAAE,GAAGM,EAAE,GAAGC,EAAE,GAAGJ,EAAE,GAAGE,EAAE,GAAGG,EAAE,EAC3BR,EAAE,GAAGK,EAAE,GAAGG,EAAE,GAAGL,EAAE,GAAGG,EAAE,GAAGC,EAAE,EAC3BP,EAAE,GAAGK,EAAE,GAAGE,EAAE,GAAGJ,EAAE,GAAGG,EAAE,GAAGE,EAAE,CAC5B;AACH;AAEA,SAASC,oCAAoCA,CAACC,IAAmB,EAAE;EACjE,SAAS;;EACT,IAAQC,oBAAoB,GAAuBD,IAAI,CAA/CC,oBAAoB;IAAEd,KAAK,GAAgBa,IAAI,CAAzBb,KAAK;IAAEC,IAAI,GAAUY,IAAI,CAAlBZ,IAAI;IAAEC,GAAA,GAAQW,IAAI,CAAZX,GAAA;EAC3C,IAAIY,oBAAoB,KAAKC,iCAAoB,CAACC,WAAW,EAAE;IAC7DH,IAAI,CAACb,KAAK,GAAGC,IAAI;IACjBY,IAAI,CAACZ,IAAI,GAAG,CAACD,KAAK;IAClBa,IAAI,CAACX,GAAG,GAAGA,GAAG,GAAGE,IAAI,CAACa,EAAE,GAAG,CAAC;EAC9B,CAAC,MAAM,IAAIH,oBAAoB,KAAKC,iCAAoB,CAACG,YAAY,EAAE;IACrEL,IAAI,CAACb,KAAK,GAAG,CAACC,IAAI;IAClBY,IAAI,CAACZ,IAAI,GAAGD,KAAK;IACjBa,IAAI,CAACX,GAAG,GAAGA,GAAG,GAAGE,IAAI,CAACa,EAAE,GAAG,CAAC;EAC9B,CAAC,MAAM,IAAIH,oBAAoB,KAAKC,iCAAoB,CAACI,YAAY,EAAE;IACrEN,IAAI,CAACb,KAAK,IAAI,CAAC,CAAC;IAChBa,IAAI,CAACZ,IAAI,IAAI,CAAC,CAAC;IACfY,IAAI,CAACX,GAAG,IAAI,CAAC,CAAC;EAChB;EAEA,IAAMkB,CAAC,GAAGrB,iBAAiB,CAACc,IAAI,CAACb,KAAK,EAAEa,IAAI,CAACZ,IAAI,EAAEY,IAAI,CAACX,GAAG,CAAC;EAC5DW,IAAI,CAACQ,EAAE,GAAGD,CAAC,CAAC,CAAC,CAAC;EACdP,IAAI,CAACS,EAAE,GAAGF,CAAC,CAAC,CAAC,CAAC;EACdP,IAAI,CAACU,EAAE,GAAGH,CAAC,CAAC,CAAC,CAAC;EACdP,IAAI,CAACW,EAAE,GAAGJ,CAAC,CAAC,CAAC,CAAC;EACd,OAAOP,IAAI;AACb;AAEA,SAASY,kCAAkCA,CAACZ,IAAa,EAAE;EACzD,SAAS;;EACT,IAAQC,oBAAoB,GAAWD,IAAI,CAAnCC,oBAAoB;IAAEY,CAAC,GAAQb,IAAI,CAAba,CAAC;IAAEC,CAAA,GAAMd,IAAI,CAAVc,CAAA;EACjC,IAAIb,oBAAoB,KAAKC,iCAAoB,CAACC,WAAW,EAAE;IAC7DH,IAAI,CAACa,CAAC,GAAG,CAACC,CAAC;IACXd,IAAI,CAACc,CAAC,GAAGD,CAAC;EACZ,CAAC,MAAM,IAAIZ,oBAAoB,KAAKC,iCAAoB,CAACG,YAAY,EAAE;IACrEL,IAAI,CAACa,CAAC,GAAGC,CAAC;IACVd,IAAI,CAACc,CAAC,GAAG,CAACD,CAAC;EACb,CAAC,MAAM,IAAIZ,oBAAoB,KAAKC,iCAAoB,CAACI,YAAY,EAAE;IACrEN,IAAI,CAACa,CAAC,IAAI,CAAC,CAAC;IACZb,IAAI,CAACc,CAAC,IAAI,CAAC,CAAC;EACd;EACA,OAAOd,IAAI;AACb;AAqBO,SAASpB,iBAAiBA,CAC/BmC,UAAsB,EACtBC,UAAkC,EACuB;EAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA;EACzD,IAAMC,aAAa,GAAG,IAAAC,aAAM,EAACL,UAAU,CAAC;EAExC,IAAMM,gBAAgB,GACpB,EAAAL,qBAAA,GAAAG,aAAa,CAACG,OAAO,qBAArBN,qBAAA,CAAuBO,4BAA4B,OACjDR,UAAU,oBAAVA,UAAU,CAAEQ,4BAA4B,KAC1C,EAAAN,sBAAA,GAAAE,aAAa,CAACG,OAAO,qBAArBL,sBAAA,CAAuBO,QAAQ,OAAKT,UAAU,oBAAVA,UAAU,CAAES,QAAQ,KACxD,EAAAN,sBAAA,GAAAC,aAAa,CAACG,OAAO,qBAArBJ,sBAAA,CAAuBO,iBAAiB,OAAKV,UAAU,oBAAVA,UAAU,CAAEU,iBAAiB;EAE5E,IAAIJ,gBAAgB,EAAE;IACpBF,aAAa,CAACG,OAAO,GAAA/C,MAAA,CAAAmD,MAAA,KAAQX,UAAA,CAAY;EAC3C;EAEA,IAAMY,MAAoB,GAAG,IAAAC,cAAO,EAClC;IAAA,OAAArD,MAAA,CAAAmD,MAAA;MACEF,QAAQ,EAAE,MAAM;MAChBD,4BAA4B,EAAE,IAAI;MAClCE,iBAAiB,EAAEI,8BAAiB,CAACC;IAAI,GACtCX,aAAa,CAACG,OAAA;EAAA,CACjB,EACF,CAACH,aAAa,CAACG,OAAO,CACxB,CAAC;EAED,IAAMS,GAAG,GAAG,IAAAX,aAAM,EAA0C;IAC1DY,MAAM,EAAE,IAAAC,sBAAgB,EAACnB,UAAU,EAAEa,MAAM,CAAC;IAC5CO,UAAU,EAAE,SAAZA,UAAUA,CAAA,EAAQ,CAChB,CACD;IACDC,WAAW,EAAE,KAAK;IAClBR,MAAA,EAAAA;EACF,CAAC,CAAC;EAEF,IAAAS,gBAAS,EAAC,YAAM;IACdL,GAAG,CAACT,OAAO,GAAG;MACZU,MAAM,EAAE,IAAAC,sBAAgB,EAACnB,UAAU,EAAEa,MAAM,CAAC;MAC5CO,UAAU,EAAE,SAAZA,UAAUA,CAAA,EAAQ,CAChB,CACD;MACDC,WAAW,EAAE,KAAK;MAClBR,MAAA,EAAAA;IACF,CAAC;IAED,IAAMU,UAAU,GAAGN,GAAG,CAACT,OAAO,CAACU,MAAM;IACrC,IAAMT,4BAA4B,GAChCQ,GAAG,CAACT,OAAO,CAACK,MAAM,CAACJ,4BAA4B;IAEjD,IAAMe,EAAE,GAAG,IAAAC,oBAAc,EAACzB,UAAU,EAAEa,MAAM,EAAG,UAAA5B,IAAI,EAAK;MACtD,SAAS;;MACT,IAAIwB,4BAA4B,EAAE;QAChC,IAAIT,UAAU,KAAK0B,uBAAU,CAACC,QAAQ,EAAE;UACtC1C,IAAI,GAAGD,oCAAoC,CAACC,IAAqB,CAAC;QACpE,CAAC,MAAM;UACLA,IAAI,GAAGY,kCAAkC,CAACZ,IAAe,CAAC;QAC5D;MACF;MACAsC,UAAU,CAAC3D,KAAK,GAAGqB,IAAI;MACvB,IAAA2C,uBAAc,EAAC,CAAC;IAClB,CAAC,CAAC;IAEF,IAAIJ,EAAE,KAAK,CAAC,CAAC,EAAE;MAEbP,GAAG,CAACT,OAAO,CAACY,UAAU,GAAG;QAAA,OAAM,IAAAS,sBAAgB,EAACL,EAAE,CAAC;MAAA;MACnDP,GAAG,CAACT,OAAO,CAACa,WAAW,GAAG,IAAI;IAChC,CAAC,MAAM;MAELJ,GAAG,CAACT,OAAO,CAACY,UAAU,GAAG,YAAM,CAC7B,CACD;MACDH,GAAG,CAACT,OAAO,CAACa,WAAW,GAAG,KAAK;IACjC;IAEA,OAAO,YAAM;MACXJ,GAAG,CAACT,OAAO,CAACY,UAAU,CAAC,CAAC;IAC1B,CAAC;EACH,CAAC,EAAE,CAACpB,UAAU,EAAEa,MAAM,CAAC,CAAC;EAExB,OAAOI,GAAG,CAACT,OAAO;AACpB", "ignoreList": []}