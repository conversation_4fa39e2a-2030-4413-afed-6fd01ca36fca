{"version": 3, "names": ["_interopRequireDefault", "require", "Object", "defineProperty", "exports", "value", "CurvedTransition", "_classCallCheck2", "_createClass2", "_possibleConstructorReturn2", "_getPrototypeOf2", "_inherits2", "_index", "_Easing", "_index2", "_util", "_callSuper", "t", "o", "e", "default", "_isNativeReflectConstruct", "Reflect", "construct", "constructor", "apply", "Boolean", "prototype", "valueOf", "call", "_BaseAnimationBuilder", "_this", "_len", "arguments", "length", "args", "Array", "_key", "concat", "easingXV", "Easing", "in", "ease", "easingYV", "out", "easingWidthV", "exp", "easingHeightV", "build", "_this$durationV", "delayFunction", "getDelayFunction", "callback", "callbackV", "delay", "get<PERSON>elay", "duration", "durationV", "easing", "easingX", "easingY", "easingWidth", "easingHeight", "values", "initialValues", "originX", "currentOriginX", "originY", "currentOriginY", "width", "currentWidth", "height", "currentHeight", "animations", "withTiming", "targetOriginX", "targetOriginY", "targetWidth", "targetHeight", "key", "__DEV__", "assertEasingIsWorklet", "createInstance", "instance", "BaseAnimationBuilder", "presetName"], "sources": ["../../../../src/layoutReanimation/defaultTransitions/CurvedTransition.ts"], "sourcesContent": [null], "mappings": "AAAA,YAAY;;AAAA,IAAAA,sBAAA,GAAAC,OAAA;AAAAC,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,KAAA;AAAA;AAAAD,OAAA,CAAAE,gBAAA;AAAA,IAAAC,gBAAA,GAAAP,sBAAA,CAAAC,OAAA;AAAA,IAAAO,aAAA,GAAAR,sBAAA,CAAAC,OAAA;AAAA,IAAAQ,2BAAA,GAAAT,sBAAA,CAAAC,OAAA;AAAA,IAAAS,gBAAA,GAAAV,sBAAA,CAAAC,OAAA;AAAA,IAAAU,UAAA,GAAAX,sBAAA,CAAAC,OAAA;AAKZ,IAAAW,MAAA,GAAAX,OAAA;AAEA,IAAAY,OAAA,GAAAZ,OAAA;AACA,IAAAa,OAAA,GAAAb,OAAA;AACA,IAAAc,KAAA,GAAAd,OAAA;AAA4D,SAAAe,WAAAC,CAAA,EAAAC,CAAA,EAAAC,CAAA,WAAAD,CAAA,OAAAR,gBAAA,CAAAU,OAAA,EAAAF,CAAA,OAAAT,2BAAA,CAAAW,OAAA,EAAAH,CAAA,EAAAI,yBAAA,KAAAC,OAAA,CAAAC,SAAA,CAAAL,CAAA,EAAAC,CAAA,YAAAT,gBAAA,CAAAU,OAAA,EAAAH,CAAA,EAAAO,WAAA,IAAAN,CAAA,CAAAO,KAAA,CAAAR,CAAA,EAAAE,CAAA;AAAA,SAAAE,0BAAA,cAAAJ,CAAA,IAAAS,OAAA,CAAAC,SAAA,CAAAC,OAAA,CAAAC,IAAA,CAAAP,OAAA,CAAAC,SAAA,CAAAG,OAAA,iCAAAT,CAAA,aAAAI,yBAAA,YAAAA,0BAAA,aAAAJ,CAAA;AAAA,IAW/CX,gBAAgB,GAAAF,OAAA,CAAAE,gBAAA,aAAAwB,qBAAA;EAAA,SAAAxB,iBAAA;IAAA,IAAAyB,KAAA;IAAA,IAAAxB,gBAAA,CAAAa,OAAA,QAAAd,gBAAA;IAAA,SAAA0B,IAAA,GAAAC,SAAA,CAAAC,MAAA,EAAAC,IAAA,OAAAC,KAAA,CAAAJ,IAAA,GAAAK,IAAA,MAAAA,IAAA,GAAAL,IAAA,EAAAK,IAAA;MAAAF,IAAA,CAAAE,IAAA,IAAAJ,SAAA,CAAAI,IAAA;IAAA;IAAAN,KAAA,GAAAf,UAAA,OAAAV,gBAAA,KAAAgC,MAAA,CAAAH,IAAA;IAAAJ,KAAA,CAM3BQ,QAAQ,GAAmBC,cAAM,CAACC,EAAE,CAACD,cAAM,CAACE,IAAI,CAAC;IAAAX,KAAA,CACjDY,QAAQ,GAAmBH,cAAM,CAACI,GAAG,CAACJ,cAAM,CAACE,IAAI,CAAC;IAAAX,KAAA,CAClDc,YAAY,GAAmBL,cAAM,CAACC,EAAE,CAACD,cAAM,CAACM,GAAG,CAAC;IAAAf,KAAA,CACpDgB,aAAa,GAAmBP,cAAM,CAACI,GAAG,CAACJ,cAAM,CAACM,GAAG,CAAC;IAAAf,KAAA,CA4DtDiB,KAAK,GAAG,YAA+B;MAAA,IAAAC,eAAA;MACrC,IAAMC,aAAa,GAAGnB,KAAA,CAAKoB,gBAAgB,CAAC,CAAC;MAC7C,IAAMC,QAAQ,GAAGrB,KAAA,CAAKsB,SAAS;MAC/B,IAAMC,KAAK,GAAGvB,KAAA,CAAKwB,QAAQ,CAAC,CAAC;MAC7B,IAAMC,QAAQ,IAAAP,eAAA,GAAGlB,KAAA,CAAK0B,SAAS,YAAAR,eAAA,GAAI,GAAG;MACtC,IAAMS,MAAM,GAAG;QACbC,OAAO,EAAE5B,KAAA,CAAKQ,QAAQ;QACtBqB,OAAO,EAAE7B,KAAA,CAAKY,QAAQ;QACtBkB,WAAW,EAAE9B,KAAA,CAAKc,YAAY;QAC9BiB,YAAY,EAAE/B,KAAA,CAAKgB;MACrB,CAAC;MAED,OAAQ,UAAAgB,MAAM,EAAK;QACjB,SAAS;;QAET,OAAO;UACLC,aAAa,EAAE;YACbC,OAAO,EAAEF,MAAM,CAACG,cAAc;YAC9BC,OAAO,EAAEJ,MAAM,CAACK,cAAc;YAC9BC,KAAK,EAAEN,MAAM,CAACO,YAAY;YAC1BC,MAAM,EAAER,MAAM,CAACS;UACjB,CAAC;UACDC,UAAU,EAAE;YACVR,OAAO,EAAEf,aAAa,CACpBI,KAAK,EACL,IAAAoB,kBAAU,EAACX,MAAM,CAACY,aAAa,EAAE;cAC/BnB,QAAQ,EAARA,QAAQ;cACRE,MAAM,EAAEA,MAAM,CAACC;YACjB,CAAC,CACH,CAAC;YACDQ,OAAO,EAAEjB,aAAa,CACpBI,KAAK,EACL,IAAAoB,kBAAU,EAACX,MAAM,CAACa,aAAa,EAAE;cAC/BpB,QAAQ,EAARA,QAAQ;cACRE,MAAM,EAAEA,MAAM,CAACE;YACjB,CAAC,CACH,CAAC;YACDS,KAAK,EAAEnB,aAAa,CAClBI,KAAK,EACL,IAAAoB,kBAAU,EAACX,MAAM,CAACc,WAAW,EAAE;cAC7BrB,QAAQ,EAARA,QAAQ;cACRE,MAAM,EAAEA,MAAM,CAACG;YACjB,CAAC,CACH,CAAC;YACDU,MAAM,EAAErB,aAAa,CACnBI,KAAK,EACL,IAAAoB,kBAAU,EAACX,MAAM,CAACe,YAAY,EAAE;cAC9BtB,QAAQ,EAARA,QAAQ;cACRE,MAAM,EAAEA,MAAM,CAACI;YACjB,CAAC,CACH;UACF,CAAC;UACDV,QAAA,EAAAA;QACF,CAAC;MACH,CAAC;IACH,CAAC;IAAA,OAAArB,KAAA;EAAA;EAAA,IAAApB,UAAA,CAAAS,OAAA,EAAAd,gBAAA,EAAAwB,qBAAA;EAAA,WAAAtB,aAAA,CAAAY,OAAA,EAAAd,gBAAA;IAAAyE,GAAA;IAAA1E,KAAA,EAtGD,SAAAsD,OAAOA,CAACD,MAAsB,EAAoB;MAChD,IAAIsB,OAAO,EAAE;QACX,IAAAC,2BAAqB,EAACvB,MAAM,CAAC;MAC/B;MACA,IAAI,CAACnB,QAAQ,GAAGmB,MAAM;MACtB,OAAO,IAAI;IACb;EAAA;IAAAqB,GAAA;IAAA1E,KAAA,EAOA,SAAAuD,OAAOA,CAACF,MAAsB,EAAoB;MAChD,IAAIsB,OAAO,EAAE;QACX,IAAAC,2BAAqB,EAACvB,MAAM,CAAC;MAC/B;MACA,IAAI,CAACf,QAAQ,GAAGe,MAAM;MACtB,OAAO,IAAI;IACb;EAAA;IAAAqB,GAAA;IAAA1E,KAAA,EAOA,SAAAwD,WAAWA,CAACH,MAAsB,EAAoB;MACpD,IAAIsB,OAAO,EAAE;QACX,IAAAC,2BAAqB,EAACvB,MAAM,CAAC;MAC/B;MACA,IAAI,CAACb,YAAY,GAAGa,MAAM;MAC1B,OAAO,IAAI;IACb;EAAA;IAAAqB,GAAA;IAAA1E,KAAA,EAOA,SAAAyD,YAAYA,CAACJ,MAAsB,EAAoB;MACrD,IAAIsB,OAAO,EAAE;QACX,IAAAC,2BAAqB,EAACvB,MAAM,CAAC;MAC/B;MACA,IAAI,CAACX,aAAa,GAAGW,MAAM;MAC3B,OAAO,IAAI;IACb;EAAA;IAAAqB,GAAA;IAAA1E,KAAA,EAxDA,SAAO6E,cAAcA,CAAA,EAEF;MACjB,OAAO,IAAI5E,gBAAgB,CAAC,CAAC;IAC/B;EAAA;IAAAyE,GAAA;IAAA1E,KAAA,EAEA,SAAOsD,OAAOA,CAACD,MAAsB,EAAoB;MACvD,IAAMyB,QAAQ,GAAG,IAAI,CAACD,cAAc,CAAC,CAAC;MACtC,OAAOC,QAAQ,CAACxB,OAAO,CAACD,MAAM,CAAC;IACjC;EAAA;IAAAqB,GAAA;IAAA1E,KAAA,EAUA,SAAOuD,OAAOA,CAACF,MAAsB,EAAoB;MACvD,IAAMyB,QAAQ,GAAG,IAAI,CAACD,cAAc,CAAC,CAAC;MACtC,OAAOC,QAAQ,CAACvB,OAAO,CAACF,MAAM,CAAC;IACjC;EAAA;IAAAqB,GAAA;IAAA1E,KAAA,EAUA,SAAOwD,WAAWA,CAACH,MAAsB,EAAoB;MAC3D,IAAMyB,QAAQ,GAAG,IAAI,CAACD,cAAc,CAAC,CAAC;MACtC,OAAOC,QAAQ,CAACtB,WAAW,CAACH,MAAM,CAAC;IACrC;EAAA;IAAAqB,GAAA;IAAA1E,KAAA,EAUA,SAAOyD,YAAYA,CAACJ,MAAsB,EAAoB;MAC5D,IAAMyB,QAAQ,GAAG,IAAI,CAACD,cAAc,CAAC,CAAC;MACtC,OAAOC,QAAQ,CAACrB,YAAY,CAACJ,MAAM,CAAC;IACtC;EAAA;AAAA,EA1DQ0B,2BAAoB;AADjB9E,gBAAgB,CAIpB+E,UAAU,GAAG,kBAAkB", "ignoreList": []}