54bddea2c6b2368af138cc2d6617574c
'use strict';

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.CurvedTransition = void 0;
var _classCallCheck2 = _interopRequireDefault(require("@babel/runtime/helpers/classCallCheck"));
var _createClass2 = _interopRequireDefault(require("@babel/runtime/helpers/createClass"));
var _possibleConstructorReturn2 = _interopRequireDefault(require("@babel/runtime/helpers/possibleConstructorReturn"));
var _getPrototypeOf2 = _interopRequireDefault(require("@babel/runtime/helpers/getPrototypeOf"));
var _inherits2 = _interopRequireDefault(require("@babel/runtime/helpers/inherits"));
var _index = require("../animationBuilder/index.js");
var _Easing = require("../../Easing.js");
var _index2 = require("../../animation/index.js");
var _util = require("../../animation/util.js");
function _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }
function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }
var CurvedTransition = exports.CurvedTransition = function (_BaseAnimationBuilder) {
  function CurvedTransition() {
    var _this;
    (0, _classCallCheck2.default)(this, CurvedTransition);
    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {
      args[_key] = arguments[_key];
    }
    _this = _callSuper(this, CurvedTransition, [].concat(args));
    _this.easingXV = _Easing.Easing.in(_Easing.Easing.ease);
    _this.easingYV = _Easing.Easing.out(_Easing.Easing.ease);
    _this.easingWidthV = _Easing.Easing.in(_Easing.Easing.exp);
    _this.easingHeightV = _Easing.Easing.out(_Easing.Easing.exp);
    _this.build = function () {
      var _this$durationV;
      var delayFunction = _this.getDelayFunction();
      var callback = _this.callbackV;
      var delay = _this.getDelay();
      var duration = (_this$durationV = _this.durationV) != null ? _this$durationV : 300;
      var easing = {
        easingX: _this.easingXV,
        easingY: _this.easingYV,
        easingWidth: _this.easingWidthV,
        easingHeight: _this.easingHeightV
      };
      return function (values) {
        'worklet';

        return {
          initialValues: {
            originX: values.currentOriginX,
            originY: values.currentOriginY,
            width: values.currentWidth,
            height: values.currentHeight
          },
          animations: {
            originX: delayFunction(delay, (0, _index2.withTiming)(values.targetOriginX, {
              duration: duration,
              easing: easing.easingX
            })),
            originY: delayFunction(delay, (0, _index2.withTiming)(values.targetOriginY, {
              duration: duration,
              easing: easing.easingY
            })),
            width: delayFunction(delay, (0, _index2.withTiming)(values.targetWidth, {
              duration: duration,
              easing: easing.easingWidth
            })),
            height: delayFunction(delay, (0, _index2.withTiming)(values.targetHeight, {
              duration: duration,
              easing: easing.easingHeight
            }))
          },
          callback: callback
        };
      };
    };
    return _this;
  }
  (0, _inherits2.default)(CurvedTransition, _BaseAnimationBuilder);
  return (0, _createClass2.default)(CurvedTransition, [{
    key: "easingX",
    value: function easingX(easing) {
      if (__DEV__) {
        (0, _util.assertEasingIsWorklet)(easing);
      }
      this.easingXV = easing;
      return this;
    }
  }, {
    key: "easingY",
    value: function easingY(easing) {
      if (__DEV__) {
        (0, _util.assertEasingIsWorklet)(easing);
      }
      this.easingYV = easing;
      return this;
    }
  }, {
    key: "easingWidth",
    value: function easingWidth(easing) {
      if (__DEV__) {
        (0, _util.assertEasingIsWorklet)(easing);
      }
      this.easingWidthV = easing;
      return this;
    }
  }, {
    key: "easingHeight",
    value: function easingHeight(easing) {
      if (__DEV__) {
        (0, _util.assertEasingIsWorklet)(easing);
      }
      this.easingHeightV = easing;
      return this;
    }
  }], [{
    key: "createInstance",
    value: function createInstance() {
      return new CurvedTransition();
    }
  }, {
    key: "easingX",
    value: function easingX(easing) {
      var instance = this.createInstance();
      return instance.easingX(easing);
    }
  }, {
    key: "easingY",
    value: function easingY(easing) {
      var instance = this.createInstance();
      return instance.easingY(easing);
    }
  }, {
    key: "easingWidth",
    value: function easingWidth(easing) {
      var instance = this.createInstance();
      return instance.easingWidth(easing);
    }
  }, {
    key: "easingHeight",
    value: function easingHeight(easing) {
      var instance = this.createInstance();
      return instance.easingHeight(easing);
    }
  }]);
}(_index.BaseAnimationBuilder);
CurvedTransition.presetName = 'CurvedTransition';
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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