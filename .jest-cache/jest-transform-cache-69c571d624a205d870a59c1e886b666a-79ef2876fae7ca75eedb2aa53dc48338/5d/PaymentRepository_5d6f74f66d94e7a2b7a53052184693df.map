{"version": 3, "names": ["cov_25h9rd6ldq", "actualCoverage", "ValidateMapper_1", "s", "require", "HandleData_1", "PaymentRepository", "f", "remoteDataSource", "_classCallCheck2", "default", "_createClass2", "key", "value", "_validate", "_asyncToGenerator2", "request", "handleData", "validate", "mapValidateResponseToModel", "_x", "apply", "arguments", "exports"], "sources": ["/Users/<USER>/projects/react-native/payment-module/src/data/repositories/PaymentRepository.ts"], "sourcesContent": ["import {mapValidateResponseToModel} from '../mappers/validate/ValidateMapper';\nimport {handleData} from '../../utils/HandleData';\nimport {ValidateModel} from '../../domain/entities/validate/ValidateModel';\nimport {ValidateRequest} from '../models/validate/ValidateRequest';\nimport {IPaymentDataSource} from '../datasources/IPaymentDataSource';\nimport {BaseResponse} from '../../core/BaseResponse';\nimport {IPaymentRepository} from '../../domain/repositories/IPaymentRepository';\n\nexport class PaymentRepository implements IPaymentRepository {\n  private remoteDataSource: IPaymentDataSource;\n\n  constructor(remoteDataSource: IPaymentDataSource) {\n    this.remoteDataSource = remoteDataSource;\n  }\n\n  async validate(request: ValidateRequest): Promise<BaseResponse<ValidateModel>> {\n    return handleData<ValidateModel>(this.remoteDataSource.validate(request), mapValidateResponseToModel);\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAYS;IAAAA,cAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,cAAA;;;;;;;;;;;;;;;;;;;;;AAZT,IAAAE,gBAAA;AAAA;AAAA,CAAAF,cAAA,GAAAG,CAAA,OAAAC,OAAA;AACA,IAAAC,YAAA;AAAA;AAAA,CAAAL,cAAA,GAAAG,CAAA,OAAAC,OAAA;AAAkD,IAOrCE,iBAAiB;AAAA;AAAA,CAAAN,cAAA,GAAAG,CAAA;EAAA;EAAAH,cAAA,GAAAO,CAAA;EAG5B,SAAAD,kBAAYE,gBAAoC;IAAA;IAAAR,cAAA,GAAAO,CAAA;IAAAP,cAAA,GAAAG,CAAA;IAAA,IAAAM,gBAAA,CAAAC,OAAA,QAAAJ,iBAAA;IAAA;IAAAN,cAAA,GAAAG,CAAA;IAC9C,IAAI,CAACK,gBAAgB,GAAGA,gBAAgB;EAC1C;EAAA;EAAAR,cAAA,GAAAG,CAAA;EAAC,WAAAQ,aAAA,CAAAD,OAAA,EAAAJ,iBAAA;IAAAM,GAAA;IAAAC,KAAA;MAAA;MAAAb,cAAA,GAAAO,CAAA;MAAA,IAAAO,SAAA;MAAA;MAAA,CAAAd,cAAA,GAAAG,CAAA,YAAAY,kBAAA,CAAAL,OAAA,EAED,WAAeM,OAAwB;QAAA;QAAAhB,cAAA,GAAAO,CAAA;QAAAP,cAAA,GAAAG,CAAA;QACrC,OAAO,IAAAE,YAAA,CAAAY,UAAU,EAAgB,IAAI,CAACT,gBAAgB,CAACU,QAAQ,CAACF,OAAO,CAAC,EAAEd,gBAAA,CAAAiB,0BAA0B,CAAC;MACvG,CAAC;MAAA,SAFKD,QAAQA,CAAAE,EAAA;QAAA;QAAApB,cAAA,GAAAO,CAAA;QAAAP,cAAA,GAAAG,CAAA;QAAA,OAAAW,SAAA,CAAAO,KAAA,OAAAC,SAAA;MAAA;MAAA;MAAAtB,cAAA,GAAAG,CAAA;MAAA,OAARe,QAAQ;IAAA;EAAA;AAAA;AAAA;AAAAlB,cAAA,GAAAG,CAAA;AAPhBoB,OAAA,CAAAjB,iBAAA,GAAAA,iBAAA", "ignoreList": []}