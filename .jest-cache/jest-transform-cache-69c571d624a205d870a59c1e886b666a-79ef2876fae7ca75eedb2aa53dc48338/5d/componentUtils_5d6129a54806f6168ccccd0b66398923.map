{"version": 3, "names": ["Object", "defineProperty", "exports", "value", "getProcessedConfig", "getReducedMotionFromConfig", "handleExitingAnimation", "handleLayoutTransition", "maybeModifyStyleForKeyframe", "saveSnapshot", "setElementAnimation", "_config", "require", "_EasingWeb", "_createAnimation", "_domUtils", "_index", "_commonTypes", "_commonTypes2", "_componentStyle", "_index2", "_ReducedMotion", "_<PERSON>ur<PERSON><PERSON><PERSON>", "_Easing", "_index3", "getEasingFromConfig", "config", "easingV", "getEasingByName", "easingName", "EasingNameSymbol", "WebEasings", "logger", "warn", "getRandomDelay", "max<PERSON><PERSON><PERSON>", "arguments", "length", "undefined", "Math", "floor", "random", "getDelayFromConfig", "shouldRandomizeDelay", "randomizeDelay", "delay", "delayV", "reduceMotionV", "ReducedMotionManager", "jsValue", "ReduceMotion", "Never", "Always", "getDurationFromConfig", "animationName", "defaultDuration", "Animations", "duration", "durationV", "getCallbackFromConfig", "callbackV", "getReversedFromConfig", "reversed", "animationType", "easing", "callback", "element", "Keyframe", "style", "animationFillMode", "timestampRules", "values", "definitions", "position", "rect", "getBoundingClientRect", "snapshot", "top", "left", "width", "height", "scrollOffsets", "getElementScrollValue", "snapshots", "set", "animationConfig", "shouldSavePosition", "parent", "configureAnimation", "animationDuration", "animationDelay", "animationTimingFunction", "LayoutAnimationType", "ENTERING", "requestAnimationFrame", "onanimationend", "contains", "removedAfterAnimation", "<PERSON><PERSON><PERSON><PERSON>", "removeEventListener", "animationCancelHandler", "onanimationstart", "_updatePropsJS", "visibility", "addEventListener", "scheduleAnimationCleanup", "setElementPosition", "get", "transitionData", "TransitionType", "LINEAR", "SEQUENCED", "FADING", "JUMPING", "CURVED", "ENTRY_EXIT", "_TransitionGenerator", "TransitionGenerator", "transitionKeyframeName", "dummyTransitionKeyframeName", "_prepareCurvedTransit", "prepareCurvedTransition", "dummy", "dummyAnimationConfig", "current", "scrollTopOffset", "scrollLeftOffset", "scrollTop", "scrollLeft", "parentElement", "offsetParent", "cloneNode", "reanimatedDummy", "<PERSON><PERSON><PERSON><PERSON>", "append<PERSON><PERSON><PERSON>", "currentScrollTopOffset", "lastScrollTopOffset", "currentScrollLeftOffset", "lastScrollLeftOffset"], "sources": ["../../../../src/layoutReanimation/web/componentUtils.ts"], "sourcesContent": [null], "mappings": "AAAA,YAAY;;AAAAA,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,KAAA;AAAA;AAAAD,OAAA,CAAAE,kBAAA,GAAAA,kBAAA;AAAAF,OAAA,CAAAG,0BAAA,GAAAA,0BAAA;AAAAH,OAAA,CAAAI,sBAAA,GAAAA,sBAAA;AAAAJ,OAAA,CAAAK,sBAAA,GAAAA,sBAAA;AAAAL,OAAA,CAAAM,2BAAA,GAAAA,2BAAA;AAAAN,OAAA,CAAAO,YAAA,GAAAA,YAAA;AAAAP,OAAA,CAAAQ,mBAAA,GAAAA,mBAAA;AAEZ,IAAAC,OAAA,GAAAC,OAAA;AAQA,IAAAC,UAAA,GAAAD,OAAA;AAGA,IAAAE,gBAAA,GAAAF,OAAA;AACA,IAAAG,SAAA,GAAAH,OAAA;AACA,IAAAI,MAAA,GAAAJ,OAAA;AAEA,IAAAK,YAAA,GAAAL,OAAA;AACA,IAAAM,aAAA,GAAAN,OAAA;AAEA,IAAAO,eAAA,GAAAP,OAAA;AACA,IAAAQ,OAAA,GAAAR,OAAA;AACA,IAAAS,cAAA,GAAAT,OAAA;AACA,IAAAU,UAAA,GAAAV,OAAA;AACA,IAAAW,OAAA,GAAAX,OAAA;AACA,IAAAY,OAAA,GAAAZ,OAAA;AAEA,SAASa,mBAAmBA,CAACC,MAAoB,EAAU;EACzD,IAAI,CAACA,MAAM,CAACC,OAAO,EAAE;IACnB,OAAO,IAAAC,0BAAe,EAAC,QAAQ,CAAC;EAClC;EAEA,IAAMC,UAAU,GAAGH,MAAM,CAACC,OAAO,CAACG,wBAAgB,CAAC;EAEnD,IAAI,EAAED,UAAU,IAAIE,qBAAU,CAAC,EAAE;IAC/BC,cAAM,CAACC,IAAI,CAAC,oDAAoD,CAAC;IAEjE,OAAO,IAAAL,0BAAe,EAAC,QAAQ,CAAC;EAClC;EAEA,OAAO,IAAAA,0BAAe,EAACC,UAA6B,CAAC;AACvD;AAEA,SAASK,cAAcA,CAAA,EAAkB;EAAA,IAAjBC,QAAQ,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,IAAI;EACrC,OAAOG,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,IAAIN,QAAQ,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI;AAC1D;AAEA,SAASO,kBAAkBA,CAAChB,MAAoB,EAAU;EACxD,IAAMiB,oBAAoB,GAAGjB,MAAM,CAACkB,cAAc;EAElD,IAAMC,KAAK,GAAGF,oBAAoB,GAAGT,cAAc,CAAC,CAAC,GAAG,CAAC;EAEzD,IAAI,CAACR,MAAM,CAACoB,MAAM,EAAE;IAClB,OAAOD,KAAK;EACd;EAEA,OAAOF,oBAAoB,GACvBT,cAAc,CAACR,MAAM,CAACoB,MAAM,CAAC,GAC7BpB,MAAM,CAACoB,MAAM,GAAG,IAAI;AAC1B;AAEO,SAASzC,0BAA0BA,CAACqB,MAAoB,EAAE;EAC/D,IAAI,CAACA,MAAM,CAACqB,aAAa,EAAE;IACzB,OAAOC,mCAAoB,CAACC,OAAO;EACrC;EAEA,QAAQvB,MAAM,CAACqB,aAAa;IAC1B,KAAKG,yBAAY,CAACC,KAAK;MACrB,OAAO,KAAK;IACd,KAAKD,yBAAY,CAACE,MAAM;MACtB,OAAO,IAAI;IACb;MACE,OAAOJ,mCAAoB,CAACC,OAAO;EACvC;AACF;AAEA,SAASI,qBAAqBA,CAC5B3B,MAAoB,EACpB4B,aAAqB,EACb;EAIR,IAAMC,eAAe,GACnBD,aAAa,IAAIE,kBAAU,GACvBA,kBAAU,CAACF,aAAa,CAAmB,CAACG,QAAQ,GACpD,GAAG;EAET,OAAO/B,MAAM,CAACgC,SAAS,KAAKpB,SAAS,GACjCZ,MAAM,CAACgC,SAAS,GAAG,IAAI,GACvBH,eAAe;AACrB;AAEA,SAASI,qBAAqBA,CAACjC,MAAoB,EAAqB;EACtE,OAAOA,MAAM,CAACkC,SAAS,KAAKtB,SAAS,GAAGZ,MAAM,CAACkC,SAAS,GAAG,IAAI;AACjE;AAEA,SAASC,qBAAqBA,CAACnC,MAAoB,EAAE;EACnD,OAAO,CAAC,CAACA,MAAM,CAACoC,QAAQ;AAC1B;AAEO,SAAS1D,kBAAkBA,CAChCkD,aAAqB,EACrBS,aAAkC,EAClCrC,MAAoB,EACH;EACjB,OAAO;IACL4B,aAAa,EAAbA,aAAa;IACbS,aAAa,EAAbA,aAAa;IACbN,QAAQ,EAAEJ,qBAAqB,CAAC3B,MAAM,EAAE4B,aAAa,CAAC;IACtDT,KAAK,EAAEH,kBAAkB,CAAChB,MAAM,CAAC;IACjCsC,MAAM,EAAEvC,mBAAmB,CAACC,MAAM,CAAC;IACnCuC,QAAQ,EAAEN,qBAAqB,CAACjC,MAAM,CAAC;IACvCoC,QAAQ,EAAED,qBAAqB,CAACnC,MAAM;EACxC,CAAC;AACH;AAEO,SAASlB,2BAA2BA,CACzC0D,OAAoB,EACpBxC,MAAoB,EACpB;EACA,IAAI,EAAEA,MAAM,YAAYyC,gBAAQ,CAAC,EAAE;IACjC;EACF;EAIAD,OAAO,CAACE,KAAK,CAACC,iBAAiB,GAAG,UAAU;EAE5C,KAAK,IAAMC,cAAc,IAAItE,MAAM,CAACuE,MAAM,CACxC7C,MAAM,CAAC8C,WACT,CAAC,EAAE;IACD,IAAI,SAAS,IAAIF,cAAc,IAAI,SAAS,IAAIA,cAAc,EAAE;MAC9DJ,OAAO,CAACE,KAAK,CAACK,QAAQ,GAAG,UAAU;MACnC;IACF;EACF;AACF;AAEO,SAAShE,YAAYA,CAACyD,OAAoB,EAAE;EACjD,IAAMQ,IAAI,GAAGR,OAAO,CAACS,qBAAqB,CAAC,CAAC;EAE5C,IAAMC,QAA4B,GAAG;IACnCC,GAAG,EAAEH,IAAI,CAACG,GAAG;IACbC,IAAI,EAAEJ,IAAI,CAACI,IAAI;IACfC,KAAK,EAAEL,IAAI,CAACK,KAAK;IACjBC,MAAM,EAAEN,IAAI,CAACM,MAAM;IACnBC,aAAa,EAAEC,qBAAqB,CAAChB,OAAO;EAC9C,CAAC;EAEDiB,yBAAS,CAACC,GAAG,CAAClB,OAAO,EAAEU,QAAQ,CAAC;AAClC;AAEO,SAASlE,mBAAmBA,CACjCwD,OAA8B,EAC9BmB,eAAgC,EAGhC;EAAA,IAFAC,kBAAkB,GAAAlD,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,KAAK;EAAA,IAC1BmD,MAAsB,GAAAnD,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,IAAI;EAE7B,IAAQkB,aAAa,GAA8B+B,eAAe,CAA1D/B,aAAa;IAAEG,QAAQ,GAAoB4B,eAAe,CAA3C5B,QAAQ;IAAEZ,KAAK,GAAawC,eAAe,CAAjCxC,KAAK;IAAEmB,MAAA,GAAWqB,eAAe,CAA1BrB,MAAA;EAExC,IAAMwB,kBAAkB,GAAG,SAArBA,kBAAkBA,CAAA,EAAS;IAC/BtB,OAAO,CAACE,KAAK,CAACd,aAAa,GAAGA,aAAa;IAC3CY,OAAO,CAACE,KAAK,CAACqB,iBAAiB,GAAG,GAAGhC,QAAQ,GAAG;IAChDS,OAAO,CAACE,KAAK,CAACsB,cAAc,GAAG,GAAG7C,KAAK,GAAG;IAC1CqB,OAAO,CAACE,KAAK,CAACuB,uBAAuB,GAAG3B,MAAM;EAChD,CAAC;EAED,IAAIqB,eAAe,CAACtB,aAAa,KAAK6B,iCAAmB,CAACC,QAAQ,EAAE;IAGlEC,qBAAqB,CAACN,kBAAkB,CAAC;EAC3C,CAAC,MAAM;IACLA,kBAAkB,CAAC,CAAC;EACtB;EAEAtB,OAAO,CAAC6B,cAAc,GAAG,YAAM;IAC7B,IAAIT,kBAAkB,EAAE;MACtB7E,YAAY,CAACyD,OAAO,CAAC;IACvB;IAEA,IAAIqB,MAAM,YAANA,MAAM,CAAES,QAAQ,CAAC9B,OAAO,CAAC,EAAE;MAC7BA,OAAO,CAAC+B,qBAAqB,GAAG,IAAI;MACpCV,MAAM,CAACW,WAAW,CAAChC,OAAO,CAAC;IAC7B;IAEAmB,eAAe,CAACpB,QAAQ,YAAxBoB,eAAe,CAACpB,QAAQ,CAAG,IAAI,CAAC;IAChCC,OAAO,CAACiC,mBAAmB,CAAC,iBAAiB,EAAEC,uBAAsB,CAAC;EACxE,CAAC;EAED,IAAMA,uBAAsB,GAAG,SAAzBA,sBAAsBA,CAAA,EAAS;IACnCf,eAAe,CAACpB,QAAQ,YAAxBoB,eAAe,CAACpB,QAAQ,CAAG,KAAK,CAAC;IAEjC,IAAIsB,MAAM,YAANA,MAAM,CAAES,QAAQ,CAAC9B,OAAO,CAAC,EAAE;MAC7BA,OAAO,CAAC+B,qBAAqB,GAAG,IAAI;MACpCV,MAAM,CAACW,WAAW,CAAChC,OAAO,CAAC;IAC7B;IAEAA,OAAO,CAACiC,mBAAmB,CAAC,iBAAiB,EAAEC,uBAAsB,CAAC;EACxE,CAAC;EAGDlC,OAAO,CAACmC,gBAAgB,GAAG,YAAM;IAC/B,IAAIhB,eAAe,CAACtB,aAAa,KAAK6B,iCAAmB,CAACC,QAAQ,EAAE;MAClE,IAAAS,qBAAc,EAAC;QAAEC,UAAU,EAAE;MAAU,CAAC,EAAErC,OAAO,CAAC;IACpD;IAEAA,OAAO,CAACsC,gBAAgB,CAAC,iBAAiB,EAAEJ,uBAAsB,CAAC;EACrE,CAAC;EAED,IAAI,EAAE9C,aAAa,IAAIE,kBAAU,CAAC,EAAE;IAClC,IAAAiD,kCAAwB,EAACnD,aAAa,EAAEG,QAAQ,GAAGZ,KAAK,EAAE,YAAM;MAC9D,IAAIyC,kBAAkB,EAAE;QACtB,IAAAoB,kCAAkB,EAACxC,OAAO,EAAEiB,yBAAS,CAACwB,GAAG,CAACzC,OAAO,CAAE,CAAC;MACtD;IACF,CAAC,CAAC;EACJ;AACF;AAEO,SAAS3D,sBAAsBA,CACpC2D,OAA8B,EAC9BmB,eAAgC,EAChCuB,cAA8B,EAC9B;EACA,IAAQtD,aAAA,GAAkB+B,eAAe,CAAjC/B,aAAA;EAER,IAAIS,aAAa;EAEjB,QAAQT,aAAa;IACnB,KAAK,kBAAkB;MACrBS,aAAa,GAAG8C,sBAAc,CAACC,MAAM;MACrC;IACF,KAAK,qBAAqB;MACxB/C,aAAa,GAAG8C,sBAAc,CAACE,SAAS;MACxC;IACF,KAAK,kBAAkB;MACrBhD,aAAa,GAAG8C,sBAAc,CAACG,MAAM;MACrC;IACF,KAAK,mBAAmB;MACtBjD,aAAa,GAAG8C,sBAAc,CAACI,OAAO;MACtC;IACF,KAAK,kBAAkB;MACrBlD,aAAa,GAAG8C,sBAAc,CAACK,MAAM;MACrC;IACF,KAAK,qBAAqB;MACxBnD,aAAa,GAAG8C,sBAAc,CAACM,UAAU;MACzC;IACF;MACEpD,aAAa,GAAG8C,sBAAc,CAACC,MAAM;MACrC;EACJ;EAEA,IAAAM,oBAAA,GACE,IAAAC,oCAAmB,EAACtD,aAAa,EAAE6C,cAAc,CAAC;IAD5CU,sBAAsB,GAAAF,oBAAA,CAAtBE,sBAAsB;IAAEC,2BAAA,GAAAH,oBAAA,CAAAG,2BAAA;EAGhClC,eAAe,CAAC/B,aAAa,GAAGgE,sBAAsB;EAEtD,IAAIvD,aAAa,KAAK8C,sBAAc,CAACK,MAAM,EAAE;IAC3C,IAAAM,qBAAA,GAAwC,IAAAC,kCAAuB,EAC7DvD,OAAO,EACPmB,eAAe,EACfuB,cAAc,EACdW,2BACF,CAAC;MALOG,KAAK,GAAAF,qBAAA,CAALE,KAAK;MAAEC,oBAAA,GAAAH,qBAAA,CAAAG,oBAAA;IAOfjH,mBAAmB,CAACgH,KAAK,EAAEC,oBAAoB,CAAC;EAClD;EACAjH,mBAAmB,CAACwD,OAAO,EAAEmB,eAAe,CAAC;AAC/C;AAEA,SAASH,qBAAqBA,CAAChB,OAAoB,EAAiB;EAClE,IAAI0D,OAA2B,GAAG1D,OAAO;EAEzC,IAAMe,aAA4B,GAAG;IACnC4C,eAAe,EAAE,CAAC;IAClBC,gBAAgB,EAAE;EACpB,CAAC;EAED,OAAOF,OAAO,EAAE;IACd,IAAIA,OAAO,CAACG,SAAS,KAAK,CAAC,IAAI9C,aAAa,CAAC4C,eAAe,KAAK,CAAC,EAAE;MAClE5C,aAAa,CAAC4C,eAAe,GAAGD,OAAO,CAACG,SAAS;IACnD;IAEA,IAAIH,OAAO,CAACI,UAAU,KAAK,CAAC,IAAI/C,aAAa,CAAC6C,gBAAgB,KAAK,CAAC,EAAE;MACpE7C,aAAa,CAAC6C,gBAAgB,GAAGF,OAAO,CAACI,UAAU;IACrD;IAEAJ,OAAO,GAAGA,OAAO,CAACK,aAAa;EACjC;EAEA,OAAOhD,aAAa;AACtB;AAEO,SAAS3E,sBAAsBA,CACpC4D,OAAoB,EACpBmB,eAAgC,EAChC;EACA,IAAME,MAAM,GAAGrB,OAAO,CAACgE,YAAY;EACnC,IAAMR,KAAK,GAAGxD,OAAO,CAACiE,SAAS,CAAC,CAA0B;EAC1DT,KAAK,CAACU,eAAe,GAAG,IAAI;EAE5BlE,OAAO,CAACE,KAAK,CAACd,aAAa,GAAG,EAAE;EAChCoE,KAAK,CAACtD,KAAK,CAACd,aAAa,GAAG,EAAE;EAO9B,OAAOY,OAAO,CAACmE,UAAU,EAAE;IACzBX,KAAK,CAACY,WAAW,CAACpE,OAAO,CAACmE,UAAU,CAAC;EACvC;EAEA9C,MAAM,YAANA,MAAM,CAAE+C,WAAW,CAACZ,KAAK,CAAC;EAE1B,IAAM9C,QAAQ,GAAGO,yBAAS,CAACwB,GAAG,CAACzC,OAAO,CAAE;EAExC,IAAMe,aAAa,GAAGC,qBAAqB,CAAChB,OAAO,CAAC;EAOpD,IAAMqE,sBAAsB,GAAGtD,aAAa,CAAC4C,eAAe;EAC5D,IAAMW,mBAAmB,GAAG5D,QAAQ,CAACK,aAAa,CAAC4C,eAAe;EAElE,IAAIU,sBAAsB,KAAKC,mBAAmB,EAAE;IAClD5D,QAAQ,CAACC,GAAG,IAAI2D,mBAAmB,GAAGD,sBAAsB;EAC9D;EAEA,IAAME,uBAAuB,GAAGxD,aAAa,CAAC6C,gBAAgB;EAC9D,IAAMY,oBAAoB,GAAG9D,QAAQ,CAACK,aAAa,CAAC6C,gBAAgB;EAEpE,IAAIW,uBAAuB,KAAKC,oBAAoB,EAAE;IACpD9D,QAAQ,CAACE,IAAI,IAAI4D,oBAAoB,GAAGD,uBAAuB;EACjE;EAEAtD,yBAAS,CAACC,GAAG,CAACsC,KAAK,EAAE9C,QAAQ,CAAC;EAE9B,IAAA8B,kCAAkB,EAACgB,KAAK,EAAE9C,QAAQ,CAAC;EAEnClE,mBAAmB,CAACgH,KAAK,EAAErC,eAAe,EAAE,KAAK,EAAEE,MAAM,CAAC;AAC5D", "ignoreList": []}