cf1223f46750d5c1c20fdab74305f31a
'use strict';

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.getProcessedConfig = getProcessedConfig;
exports.getReducedMotionFromConfig = getReducedMotionFromConfig;
exports.handleExitingAnimation = handleExitingAnimation;
exports.handleLayoutTransition = handleLayoutTransition;
exports.maybeModifyStyleForKeyframe = maybeModifyStyleForKeyframe;
exports.saveSnapshot = saveSnapshot;
exports.setElementAnimation = setElementAnimation;
var _config = require("./config.js");
var _EasingWeb = require("./Easing.web.js");
var _createAnimation = require("./createAnimation.js");
var _domUtils = require("./domUtils.js");
var _index = require("../../js-reanimated/index.js");
var _commonTypes = require("../../commonTypes.js");
var _commonTypes2 = require("../animationBuilder/commonTypes.js");
var _componentStyle = require("./componentStyle.js");
var _index2 = require("../animationBuilder/index.js");
var _ReducedMotion = require("../../ReducedMotion.js");
var _CurvedWeb = require("./transition/Curved.web.js");
var _Easing = require("../../Easing.js");
var _index3 = require("../../logger/index.js");
function getEasingFromConfig(config) {
  if (!config.easingV) {
    return (0, _EasingWeb.getEasingByName)('linear');
  }
  var easingName = config.easingV[_Easing.EasingNameSymbol];
  if (!(easingName in _EasingWeb.WebEasings)) {
    _index3.logger.warn(`Selected easing is not currently supported on web.`);
    return (0, _EasingWeb.getEasingByName)('linear');
  }
  return (0, _EasingWeb.getEasingByName)(easingName);
}
function getRandomDelay() {
  var maxDelay = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 1000;
  return Math.floor(Math.random() * (maxDelay + 1)) / 1000;
}
function getDelayFromConfig(config) {
  var shouldRandomizeDelay = config.randomizeDelay;
  var delay = shouldRandomizeDelay ? getRandomDelay() : 0;
  if (!config.delayV) {
    return delay;
  }
  return shouldRandomizeDelay ? getRandomDelay(config.delayV) : config.delayV / 1000;
}
function getReducedMotionFromConfig(config) {
  if (!config.reduceMotionV) {
    return _ReducedMotion.ReducedMotionManager.jsValue;
  }
  switch (config.reduceMotionV) {
    case _commonTypes.ReduceMotion.Never:
      return false;
    case _commonTypes.ReduceMotion.Always:
      return true;
    default:
      return _ReducedMotion.ReducedMotionManager.jsValue;
  }
}
function getDurationFromConfig(config, animationName) {
  var defaultDuration = animationName in _config.Animations ? _config.Animations[animationName].duration : 0.3;
  return config.durationV !== undefined ? config.durationV / 1000 : defaultDuration;
}
function getCallbackFromConfig(config) {
  return config.callbackV !== undefined ? config.callbackV : null;
}
function getReversedFromConfig(config) {
  return !!config.reversed;
}
function getProcessedConfig(animationName, animationType, config) {
  return {
    animationName: animationName,
    animationType: animationType,
    duration: getDurationFromConfig(config, animationName),
    delay: getDelayFromConfig(config),
    easing: getEasingFromConfig(config),
    callback: getCallbackFromConfig(config),
    reversed: getReversedFromConfig(config)
  };
}
function maybeModifyStyleForKeyframe(element, config) {
  if (!(config instanceof _index2.Keyframe)) {
    return;
  }
  element.style.animationFillMode = 'forwards';
  for (var timestampRules of Object.values(config.definitions)) {
    if ('originX' in timestampRules || 'originY' in timestampRules) {
      element.style.position = 'absolute';
      return;
    }
  }
}
function saveSnapshot(element) {
  var rect = element.getBoundingClientRect();
  var snapshot = {
    top: rect.top,
    left: rect.left,
    width: rect.width,
    height: rect.height,
    scrollOffsets: getElementScrollValue(element)
  };
  _componentStyle.snapshots.set(element, snapshot);
}
function setElementAnimation(element, animationConfig) {
  var shouldSavePosition = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;
  var parent = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : null;
  var animationName = animationConfig.animationName,
    duration = animationConfig.duration,
    delay = animationConfig.delay,
    easing = animationConfig.easing;
  var configureAnimation = function configureAnimation() {
    element.style.animationName = animationName;
    element.style.animationDuration = `${duration}s`;
    element.style.animationDelay = `${delay}s`;
    element.style.animationTimingFunction = easing;
  };
  if (animationConfig.animationType === _commonTypes2.LayoutAnimationType.ENTERING) {
    requestAnimationFrame(configureAnimation);
  } else {
    configureAnimation();
  }
  element.onanimationend = function () {
    if (shouldSavePosition) {
      saveSnapshot(element);
    }
    if (parent != null && parent.contains(element)) {
      element.removedAfterAnimation = true;
      parent.removeChild(element);
    }
    animationConfig.callback == null || animationConfig.callback(true);
    element.removeEventListener('animationcancel', _animationCancelHandler);
  };
  var _animationCancelHandler = function animationCancelHandler() {
    animationConfig.callback == null || animationConfig.callback(false);
    if (parent != null && parent.contains(element)) {
      element.removedAfterAnimation = true;
      parent.removeChild(element);
    }
    element.removeEventListener('animationcancel', _animationCancelHandler);
  };
  element.onanimationstart = function () {
    if (animationConfig.animationType === _commonTypes2.LayoutAnimationType.ENTERING) {
      (0, _index._updatePropsJS)({
        visibility: 'initial'
      }, element);
    }
    element.addEventListener('animationcancel', _animationCancelHandler);
  };
  if (!(animationName in _config.Animations)) {
    (0, _domUtils.scheduleAnimationCleanup)(animationName, duration + delay, function () {
      if (shouldSavePosition) {
        (0, _componentStyle.setElementPosition)(element, _componentStyle.snapshots.get(element));
      }
    });
  }
}
function handleLayoutTransition(element, animationConfig, transitionData) {
  var animationName = animationConfig.animationName;
  var animationType;
  switch (animationName) {
    case 'LinearTransition':
      animationType = _config.TransitionType.LINEAR;
      break;
    case 'SequencedTransition':
      animationType = _config.TransitionType.SEQUENCED;
      break;
    case 'FadingTransition':
      animationType = _config.TransitionType.FADING;
      break;
    case 'JumpingTransition':
      animationType = _config.TransitionType.JUMPING;
      break;
    case 'CurvedTransition':
      animationType = _config.TransitionType.CURVED;
      break;
    case 'EntryExitTransition':
      animationType = _config.TransitionType.ENTRY_EXIT;
      break;
    default:
      animationType = _config.TransitionType.LINEAR;
      break;
  }
  var _TransitionGenerator = (0, _createAnimation.TransitionGenerator)(animationType, transitionData),
    transitionKeyframeName = _TransitionGenerator.transitionKeyframeName,
    dummyTransitionKeyframeName = _TransitionGenerator.dummyTransitionKeyframeName;
  animationConfig.animationName = transitionKeyframeName;
  if (animationType === _config.TransitionType.CURVED) {
    var _prepareCurvedTransit = (0, _CurvedWeb.prepareCurvedTransition)(element, animationConfig, transitionData, dummyTransitionKeyframeName),
      dummy = _prepareCurvedTransit.dummy,
      dummyAnimationConfig = _prepareCurvedTransit.dummyAnimationConfig;
    setElementAnimation(dummy, dummyAnimationConfig);
  }
  setElementAnimation(element, animationConfig);
}
function getElementScrollValue(element) {
  var current = element;
  var scrollOffsets = {
    scrollTopOffset: 0,
    scrollLeftOffset: 0
  };
  while (current) {
    if (current.scrollTop !== 0 && scrollOffsets.scrollTopOffset === 0) {
      scrollOffsets.scrollTopOffset = current.scrollTop;
    }
    if (current.scrollLeft !== 0 && scrollOffsets.scrollLeftOffset === 0) {
      scrollOffsets.scrollLeftOffset = current.scrollLeft;
    }
    current = current.parentElement;
  }
  return scrollOffsets;
}
function handleExitingAnimation(element, animationConfig) {
  var parent = element.offsetParent;
  var dummy = element.cloneNode();
  dummy.reanimatedDummy = true;
  element.style.animationName = '';
  dummy.style.animationName = '';
  while (element.firstChild) {
    dummy.appendChild(element.firstChild);
  }
  parent == null || parent.appendChild(dummy);
  var snapshot = _componentStyle.snapshots.get(element);
  var scrollOffsets = getElementScrollValue(element);
  var currentScrollTopOffset = scrollOffsets.scrollTopOffset;
  var lastScrollTopOffset = snapshot.scrollOffsets.scrollTopOffset;
  if (currentScrollTopOffset !== lastScrollTopOffset) {
    snapshot.top += lastScrollTopOffset - currentScrollTopOffset;
  }
  var currentScrollLeftOffset = scrollOffsets.scrollLeftOffset;
  var lastScrollLeftOffset = snapshot.scrollOffsets.scrollLeftOffset;
  if (currentScrollLeftOffset !== lastScrollLeftOffset) {
    snapshot.left += lastScrollLeftOffset - currentScrollLeftOffset;
  }
  _componentStyle.snapshots.set(dummy, snapshot);
  (0, _componentStyle.setElementPosition)(dummy, snapshot);
  setElementAnimation(dummy, animationConfig, false, parent);
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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