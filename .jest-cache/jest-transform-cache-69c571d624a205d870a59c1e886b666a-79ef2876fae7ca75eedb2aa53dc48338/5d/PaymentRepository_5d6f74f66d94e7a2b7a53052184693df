2c7f69f776cebdb3829545b11fc12c3d
"use strict";

/* istanbul ignore next */
function cov_25h9rd6ldq() {
  var path = "/Users/<USER>/projects/react-native/payment-module/src/data/repositories/PaymentRepository.ts";
  var hash = "b4448b096e5bdc4b9aa5072fad658cbb45f728ea";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/projects/react-native/payment-module/src/data/repositories/PaymentRepository.ts",
    statementMap: {
      "0": {
        start: {
          line: 3,
          column: 29
        },
        end: {
          line: 3,
          column: 84
        }
      },
      "1": {
        start: {
          line: 4,
          column: 25
        },
        end: {
          line: 4,
          column: 99
        }
      },
      "2": {
        start: {
          line: 5,
          column: 23
        },
        end: {
          line: 5,
          column: 95
        }
      },
      "3": {
        start: {
          line: 6,
          column: 20
        },
        end: {
          line: 6,
          column: 89
        }
      },
      "4": {
        start: {
          line: 7,
          column: 0
        },
        end: {
          line: 9,
          column: 3
        }
      },
      "5": {
        start: {
          line: 10,
          column: 0
        },
        end: {
          line: 10,
          column: 35
        }
      },
      "6": {
        start: {
          line: 11,
          column: 23
        },
        end: {
          line: 11,
          column: 68
        }
      },
      "7": {
        start: {
          line: 12,
          column: 19
        },
        end: {
          line: 12,
          column: 52
        }
      },
      "8": {
        start: {
          line: 13,
          column: 24
        },
        end: {
          line: 30,
          column: 3
        }
      },
      "9": {
        start: {
          line: 15,
          column: 4
        },
        end: {
          line: 15,
          column: 59
        }
      },
      "10": {
        start: {
          line: 16,
          column: 4
        },
        end: {
          line: 16,
          column: 45
        }
      },
      "11": {
        start: {
          line: 18,
          column: 2
        },
        end: {
          line: 29,
          column: 6
        }
      },
      "12": {
        start: {
          line: 21,
          column: 22
        },
        end: {
          line: 23,
          column: 8
        }
      },
      "13": {
        start: {
          line: 22,
          column: 8
        },
        end: {
          line: 22,
          column: 130
        }
      },
      "14": {
        start: {
          line: 25,
          column: 8
        },
        end: {
          line: 25,
          column: 48
        }
      },
      "15": {
        start: {
          line: 27,
          column: 6
        },
        end: {
          line: 27,
          column: 22
        }
      },
      "16": {
        start: {
          line: 31,
          column: 0
        },
        end: {
          line: 31,
          column: 46
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 13,
            column: 24
          },
          end: {
            line: 13,
            column: 25
          }
        },
        loc: {
          start: {
            line: 13,
            column: 36
          },
          end: {
            line: 30,
            column: 1
          }
        },
        line: 13
      },
      "1": {
        name: "PaymentRepository",
        decl: {
          start: {
            line: 14,
            column: 11
          },
          end: {
            line: 14,
            column: 28
          }
        },
        loc: {
          start: {
            line: 14,
            column: 47
          },
          end: {
            line: 17,
            column: 3
          }
        },
        line: 14
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 20,
            column: 11
          },
          end: {
            line: 20,
            column: 12
          }
        },
        loc: {
          start: {
            line: 20,
            column: 23
          },
          end: {
            line: 28,
            column: 5
          }
        },
        line: 20
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 21,
            column: 54
          },
          end: {
            line: 21,
            column: 55
          }
        },
        loc: {
          start: {
            line: 21,
            column: 74
          },
          end: {
            line: 23,
            column: 7
          }
        },
        line: 21
      },
      "4": {
        name: "validate",
        decl: {
          start: {
            line: 24,
            column: 15
          },
          end: {
            line: 24,
            column: 23
          }
        },
        loc: {
          start: {
            line: 24,
            column: 28
          },
          end: {
            line: 26,
            column: 7
          }
        },
        line: 24
      }
    },
    branchMap: {},
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0
    },
    b: {},
    inputSourceMap: {
      version: 3,
      file: undefined,
      names: ["ValidateMapper_1", "require", "HandleData_1", "PaymentRepository", "remoteDataSource", "_classCallCheck2", "default", "_createClass2", "key", "value", "_validate", "_asyncToGenerator2", "request", "handleData", "validate", "mapValidateResponseToModel", "_x", "apply", "arguments", "exports"],
      sourceRoot: undefined,
      sources: ["/Users/<USER>/projects/react-native/payment-module/src/data/repositories/PaymentRepository.ts"],
      sourcesContent: ["import {mapValidateResponseToModel} from '../mappers/validate/ValidateMapper';\nimport {handleData} from '../../utils/HandleData';\nimport {ValidateModel} from '../../domain/entities/validate/ValidateModel';\nimport {ValidateRequest} from '../models/validate/ValidateRequest';\nimport {IPaymentDataSource} from '../datasources/IPaymentDataSource';\nimport {BaseResponse} from '../../core/BaseResponse';\nimport {IPaymentRepository} from '../../domain/repositories/IPaymentRepository';\n\nexport class PaymentRepository implements IPaymentRepository {\n  private remoteDataSource: IPaymentDataSource;\n\n  constructor(remoteDataSource: IPaymentDataSource) {\n    this.remoteDataSource = remoteDataSource;\n  }\n\n  async validate(request: ValidateRequest): Promise<BaseResponse<ValidateModel>> {\n    return handleData<ValidateModel>(this.remoteDataSource.validate(request), mapValidateResponseToModel);\n  }\n}\n"],
      mappings: ";;;;;;;;;;AAAA,IAAAA,gBAAA,GAAAC,OAAA;AACA,IAAAC,YAAA,GAAAD,OAAA;AAAkD,IAOrCE,iBAAiB;EAG5B,SAAAA,kBAAYC,gBAAoC;IAAA,IAAAC,gBAAA,CAAAC,OAAA,QAAAH,iBAAA;IAC9C,IAAI,CAACC,gBAAgB,GAAGA,gBAAgB;EAC1C;EAAC,WAAAG,aAAA,CAAAD,OAAA,EAAAH,iBAAA;IAAAK,GAAA;IAAAC,KAAA;MAAA,IAAAC,SAAA,OAAAC,kBAAA,CAAAL,OAAA,EAED,WAAeM,OAAwB;QACrC,OAAO,IAAAV,YAAA,CAAAW,UAAU,EAAgB,IAAI,CAACT,gBAAgB,CAACU,QAAQ,CAACF,OAAO,CAAC,EAAEZ,gBAAA,CAAAe,0BAA0B,CAAC;MACvG,CAAC;MAAA,SAFKD,QAAQA,CAAAE,EAAA;QAAA,OAAAN,SAAA,CAAAO,KAAA,OAAAC,SAAA;MAAA;MAAA,OAARJ,QAAQ;IAAA;EAAA;AAAA;AAPhBK,OAAA,CAAAhB,iBAAA,GAAAA,iBAAA",
      ignoreList: []
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "b4448b096e5bdc4b9aa5072fad658cbb45f728ea"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_25h9rd6ldq = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_25h9rd6ldq();
var _interopRequireDefault =
/* istanbul ignore next */
(cov_25h9rd6ldq().s[0]++, require("@babel/runtime/helpers/interopRequireDefault"));
var _asyncToGenerator2 =
/* istanbul ignore next */
(cov_25h9rd6ldq().s[1]++, _interopRequireDefault(require("@babel/runtime/helpers/asyncToGenerator")));
var _classCallCheck2 =
/* istanbul ignore next */
(cov_25h9rd6ldq().s[2]++, _interopRequireDefault(require("@babel/runtime/helpers/classCallCheck")));
var _createClass2 =
/* istanbul ignore next */
(cov_25h9rd6ldq().s[3]++, _interopRequireDefault(require("@babel/runtime/helpers/createClass")));
/* istanbul ignore next */
cov_25h9rd6ldq().s[4]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_25h9rd6ldq().s[5]++;
exports.PaymentRepository = void 0;
var ValidateMapper_1 =
/* istanbul ignore next */
(cov_25h9rd6ldq().s[6]++, require("../mappers/validate/ValidateMapper"));
var HandleData_1 =
/* istanbul ignore next */
(cov_25h9rd6ldq().s[7]++, require("../../utils/HandleData"));
var PaymentRepository =
/* istanbul ignore next */
(cov_25h9rd6ldq().s[8]++, function () {
  /* istanbul ignore next */
  cov_25h9rd6ldq().f[0]++;
  function PaymentRepository(remoteDataSource) {
    /* istanbul ignore next */
    cov_25h9rd6ldq().f[1]++;
    cov_25h9rd6ldq().s[9]++;
    (0, _classCallCheck2.default)(this, PaymentRepository);
    /* istanbul ignore next */
    cov_25h9rd6ldq().s[10]++;
    this.remoteDataSource = remoteDataSource;
  }
  /* istanbul ignore next */
  cov_25h9rd6ldq().s[11]++;
  return (0, _createClass2.default)(PaymentRepository, [{
    key: "validate",
    value: function () {
      /* istanbul ignore next */
      cov_25h9rd6ldq().f[2]++;
      var _validate =
      /* istanbul ignore next */
      (cov_25h9rd6ldq().s[12]++, (0, _asyncToGenerator2.default)(function* (request) {
        /* istanbul ignore next */
        cov_25h9rd6ldq().f[3]++;
        cov_25h9rd6ldq().s[13]++;
        return (0, HandleData_1.handleData)(this.remoteDataSource.validate(request), ValidateMapper_1.mapValidateResponseToModel);
      }));
      function validate(_x) {
        /* istanbul ignore next */
        cov_25h9rd6ldq().f[4]++;
        cov_25h9rd6ldq().s[14]++;
        return _validate.apply(this, arguments);
      }
      /* istanbul ignore next */
      cov_25h9rd6ldq().s[15]++;
      return validate;
    }()
  }]);
}());
/* istanbul ignore next */
cov_25h9rd6ldq().s[16]++;
exports.PaymentRepository = PaymentRepository;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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