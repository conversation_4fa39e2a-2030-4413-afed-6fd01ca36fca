{"version": 3, "names": ["cov_298lvbvyzs", "actualCoverage", "MSBCustomError_1", "s", "require", "ValidateCustomerUseCase", "f", "repository", "arrangementRepository", "_classCallCheck2", "default", "_createClass2", "key", "value", "_execute", "_asyncToGenerator2", "request", "params", "externalStateIds", "externalProductKindIds", "currency", "_response$1$data", "response", "Promise", "all", "getProfile", "sourceAccountList", "b", "console", "log", "errors", "_response$", "status", "error", "createError", "isBlocked", "data", "length", "CustomError", "execute", "_x", "apply", "arguments", "exports"], "sources": ["/Users/<USER>/projects/react-native/payment-module/src/domain/usecases/customer/ValidateCustomerUseCase.ts"], "sourcesContent": ["import {ICustomerRepository} from '../../repositories/ICustomerRepository';\nimport {ResultState} from '../../../core/ResultState';\nimport {IArrangementRepository} from '../../repositories/IArrangementRepository';\nimport {SourceAccountListRequest} from '../../../data/models/source-account-list/SourceAccountListRequest';\nimport {createError, CustomError} from '../../../core/MSBCustomError';\nexport class ValidateCustomerUseCase {\n  private repository: ICustomerRepository;\n  private arrangementRepository: IArrangementRepository;\n\n  constructor(repository: ICustomerRepository, arrangementRepository: IArrangementRepository) {\n    this.arrangementRepository = arrangementRepository;\n    this.repository = repository;\n  }\n\n  public async execute(request?: SourceAccountListRequest): Promise<ResultState<boolean>> {\n    // call this.repository.getProfile(...)\n    const params: SourceAccountListRequest = {\n      externalStateIds: ['ACTIVE'],\n      externalProductKindIds: ['kind1', 'kind10'],\n      currency: 'VND',\n    };\n    try {\n      const response = await Promise.all([\n        this.repository.getProfile(),\n        this.arrangementRepository.sourceAccountList(request ?? params),\n      ]);\n      console.log('✅ ExecutionHandler with response=', response);\n      if (!response || !response[0] || !response[1] || response[0].errors || response[1].errors) {\n        return {\n          status: 'ERROR',\n          error: createError(response[0]?.errors?.[0]?.key),\n        };\n      }\n      if (response[0].isBlocked() === true) {\n        return {\n          status: 'ERROR',\n          error: createError(), //TODO: block user\n        };\n      }\n      if (response[1].data?.length === 0) {\n        return {\n          status: 'ERROR',\n          error: createError(), //TODO: khong co tai khoan nguon phu hop\n        };\n      }\n      return {\n        status: 'SUCCESS',\n        data: true,\n      };\n    } catch (error) {\n      console.error('❌ ExecutionHandler UseCase catch Error:', error);\n      return {\n        status: 'ERROR',\n        error: error instanceof CustomError ? error : createError(),\n      };\n    }\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAWS;IAAAA,cAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,cAAA;;;;;;;;;;;;;;;;;;;;;AAPT,IAAAE,gBAAA;AAAA;AAAA,CAAAF,cAAA,GAAAG,CAAA,OAAAC,OAAA;AAAsE,IACzDC,uBAAuB;AAAA;AAAA,CAAAL,cAAA,GAAAG,CAAA;EAAA;EAAAH,cAAA,GAAAM,CAAA;EAIlC,SAAAD,wBAAYE,UAA+B,EAAEC,qBAA6C;IAAA;IAAAR,cAAA,GAAAM,CAAA;IAAAN,cAAA,GAAAG,CAAA;IAAA,IAAAM,gBAAA,CAAAC,OAAA,QAAAL,uBAAA;IAAA;IAAAL,cAAA,GAAAG,CAAA;IACxF,IAAI,CAACK,qBAAqB,GAAGA,qBAAqB;IAAA;IAAAR,cAAA,GAAAG,CAAA;IAClD,IAAI,CAACI,UAAU,GAAGA,UAAU;EAC9B;EAAA;EAAAP,cAAA,GAAAG,CAAA;EAAC,WAAAQ,aAAA,CAAAD,OAAA,EAAAL,uBAAA;IAAAO,GAAA;IAAAC,KAAA;MAAA;MAAAb,cAAA,GAAAM,CAAA;MAAA,IAAAQ,QAAA;MAAA;MAAA,CAAAd,cAAA,GAAAG,CAAA,YAAAY,kBAAA,CAAAL,OAAA,EAEM,WAAcM,OAAkC;QAAA;QAAAhB,cAAA,GAAAM,CAAA;QAErD,IAAMW,MAAM;QAAA;QAAA,CAAAjB,cAAA,GAAAG,CAAA,QAA6B;UACvCe,gBAAgB,EAAE,CAAC,QAAQ,CAAC;UAC5BC,sBAAsB,EAAE,CAAC,OAAO,EAAE,QAAQ,CAAC;UAC3CC,QAAQ,EAAE;SACX;QAAA;QAAApB,cAAA,GAAAG,CAAA;QACD,IAAI;UAAA,IAAAkB,gBAAA;UACF,IAAMC,QAAQ;UAAA;UAAA,CAAAtB,cAAA,GAAAG,CAAA,cAASoB,OAAO,CAACC,GAAG,CAAC,CACjC,IAAI,CAACjB,UAAU,CAACkB,UAAU,EAAE,EAC5B,IAAI,CAACjB,qBAAqB,CAACkB,iBAAiB,CAACV,OAAO;UAAA;UAAA,CAAAhB,cAAA,GAAA2B,CAAA,UAAPX,OAAO;UAAA;UAAA,CAAAhB,cAAA,GAAA2B,CAAA,UAAIV,MAAM,EAAC,CAChE,CAAC;UAAA;UAAAjB,cAAA,GAAAG,CAAA;UACFyB,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAEP,QAAQ,CAAC;UAAA;UAAAtB,cAAA,GAAAG,CAAA;UAC1D;UAAI;UAAA,CAAAH,cAAA,GAAA2B,CAAA,WAACL,QAAQ;UAAA;UAAA,CAAAtB,cAAA,GAAA2B,CAAA,UAAI,CAACL,QAAQ,CAAC,CAAC,CAAC;UAAA;UAAA,CAAAtB,cAAA,GAAA2B,CAAA,UAAI,CAACL,QAAQ,CAAC,CAAC,CAAC;UAAA;UAAA,CAAAtB,cAAA,GAAA2B,CAAA,UAAIL,QAAQ,CAAC,CAAC,CAAC,CAACQ,MAAM;UAAA;UAAA,CAAA9B,cAAA,GAAA2B,CAAA,UAAIL,QAAQ,CAAC,CAAC,CAAC,CAACQ,MAAM,GAAE;YAAA;YAAA9B,cAAA,GAAA2B,CAAA;YAAA,IAAAI,UAAA;YAAA;YAAA/B,cAAA,GAAAG,CAAA;YACzF,OAAO;cACL6B,MAAM,EAAE,OAAO;cACfC,KAAK,EAAE,IAAA/B,gBAAA,CAAAgC,WAAW;cAAA;cAAA,CAAAlC,cAAA,GAAA2B,CAAA,WAAAI,UAAA,GAACT,QAAQ,CAAC,CAAC,CAAC;cAAA;cAAA,CAAAtB,cAAA,GAAA2B,CAAA,WAAAI,UAAA,GAAXA,UAAA,CAAaD,MAAM;cAAA;cAAA,CAAA9B,cAAA,GAAA2B,CAAA,WAAAI,UAAA,GAAnBA,UAAA,CAAsB,CAAC,CAAC;cAAA;cAAA,CAAA/B,cAAA,GAAA2B,CAAA;cAAA;cAAA,CAAA3B,cAAA,GAAA2B,CAAA,UAAxBI,UAAA,CAA0BnB,GAAG;aACjD;UACH;UAAA;UAAA;YAAAZ,cAAA,GAAA2B,CAAA;UAAA;UAAA3B,cAAA,GAAAG,CAAA;UACA,IAAImB,QAAQ,CAAC,CAAC,CAAC,CAACa,SAAS,EAAE,KAAK,IAAI,EAAE;YAAA;YAAAnC,cAAA,GAAA2B,CAAA;YAAA3B,cAAA,GAAAG,CAAA;YACpC,OAAO;cACL6B,MAAM,EAAE,OAAO;cACfC,KAAK,EAAE,IAAA/B,gBAAA,CAAAgC,WAAW;aACnB;UACH;UAAA;UAAA;YAAAlC,cAAA,GAAA2B,CAAA;UAAA;UAAA3B,cAAA,GAAAG,CAAA;UACA,IAAI,EAAAkB,gBAAA,GAAAC,QAAQ,CAAC,CAAC,CAAC,CAACc,IAAI;UAAA;UAAA,CAAApC,cAAA,GAAA2B,CAAA;UAAA;UAAA,CAAA3B,cAAA,GAAA2B,CAAA,UAAhBN,gBAAA,CAAkBgB,MAAM,OAAK,CAAC,EAAE;YAAA;YAAArC,cAAA,GAAA2B,CAAA;YAAA3B,cAAA,GAAAG,CAAA;YAClC,OAAO;cACL6B,MAAM,EAAE,OAAO;cACfC,KAAK,EAAE,IAAA/B,gBAAA,CAAAgC,WAAW;aACnB;UACH;UAAA;UAAA;YAAAlC,cAAA,GAAA2B,CAAA;UAAA;UAAA3B,cAAA,GAAAG,CAAA;UACA,OAAO;YACL6B,MAAM,EAAE,SAAS;YACjBI,IAAI,EAAE;WACP;QACH,CAAC,CAAC,OAAOH,KAAK,EAAE;UAAA;UAAAjC,cAAA,GAAAG,CAAA;UACdyB,OAAO,CAACK,KAAK,CAAC,yCAAyC,EAAEA,KAAK,CAAC;UAAA;UAAAjC,cAAA,GAAAG,CAAA;UAC/D,OAAO;YACL6B,MAAM,EAAE,OAAO;YACfC,KAAK,EAAEA,KAAK,YAAY/B,gBAAA,CAAAoC,WAAW;YAAA;YAAA,CAAAtC,cAAA,GAAA2B,CAAA,UAAGM,KAAK;YAAA;YAAA,CAAAjC,cAAA,GAAA2B,CAAA,UAAG,IAAAzB,gBAAA,CAAAgC,WAAW;WAC1D;QACH;MACF,CAAC;MAAA,SA1CYK,OAAOA,CAAAC,EAAA;QAAA;QAAAxC,cAAA,GAAAM,CAAA;QAAAN,cAAA,GAAAG,CAAA;QAAA,OAAAW,QAAA,CAAA2B,KAAA,OAAAC,SAAA;MAAA;MAAA;MAAA1C,cAAA,GAAAG,CAAA;MAAA,OAAPoC,OAAO;IAAA;EAAA;AAAA;AAAA;AAAAvC,cAAA,GAAAG,CAAA;AATtBwC,OAAA,CAAAtC,uBAAA,GAAAA,uBAAA", "ignoreList": []}