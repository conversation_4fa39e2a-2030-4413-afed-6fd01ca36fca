b5b476bd17831823dfd6e615487d9851
"use strict";

/* istanbul ignore next */
function cov_298lvbvyzs() {
  var path = "/Users/<USER>/projects/react-native/payment-module/src/domain/usecases/customer/ValidateCustomerUseCase.ts";
  var hash = "c1b3c199355ad28cd1dc2b47710b085bab82ba9c";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/projects/react-native/payment-module/src/domain/usecases/customer/ValidateCustomerUseCase.ts",
    statementMap: {
      "0": {
        start: {
          line: 3,
          column: 29
        },
        end: {
          line: 3,
          column: 84
        }
      },
      "1": {
        start: {
          line: 4,
          column: 25
        },
        end: {
          line: 4,
          column: 99
        }
      },
      "2": {
        start: {
          line: 5,
          column: 23
        },
        end: {
          line: 5,
          column: 95
        }
      },
      "3": {
        start: {
          line: 6,
          column: 20
        },
        end: {
          line: 6,
          column: 89
        }
      },
      "4": {
        start: {
          line: 7,
          column: 0
        },
        end: {
          line: 9,
          column: 3
        }
      },
      "5": {
        start: {
          line: 10,
          column: 0
        },
        end: {
          line: 10,
          column: 41
        }
      },
      "6": {
        start: {
          line: 11,
          column: 23
        },
        end: {
          line: 11,
          column: 62
        }
      },
      "7": {
        start: {
          line: 12,
          column: 30
        },
        end: {
          line: 68,
          column: 3
        }
      },
      "8": {
        start: {
          line: 14,
          column: 4
        },
        end: {
          line: 14,
          column: 65
        }
      },
      "9": {
        start: {
          line: 15,
          column: 4
        },
        end: {
          line: 15,
          column: 55
        }
      },
      "10": {
        start: {
          line: 16,
          column: 4
        },
        end: {
          line: 16,
          column: 33
        }
      },
      "11": {
        start: {
          line: 18,
          column: 2
        },
        end: {
          line: 67,
          column: 6
        }
      },
      "12": {
        start: {
          line: 21,
          column: 21
        },
        end: {
          line: 61,
          column: 8
        }
      },
      "13": {
        start: {
          line: 22,
          column: 21
        },
        end: {
          line: 26,
          column: 9
        }
      },
      "14": {
        start: {
          line: 27,
          column: 8
        },
        end: {
          line: 60,
          column: 9
        }
      },
      "15": {
        start: {
          line: 29,
          column: 25
        },
        end: {
          line: 29,
          column: 156
        }
      },
      "16": {
        start: {
          line: 30,
          column: 10
        },
        end: {
          line: 30,
          column: 69
        }
      },
      "17": {
        start: {
          line: 31,
          column: 10
        },
        end: {
          line: 37,
          column: 11
        }
      },
      "18": {
        start: {
          line: 33,
          column: 12
        },
        end: {
          line: 36,
          column: 14
        }
      },
      "19": {
        start: {
          line: 38,
          column: 10
        },
        end: {
          line: 43,
          column: 11
        }
      },
      "20": {
        start: {
          line: 39,
          column: 12
        },
        end: {
          line: 42,
          column: 14
        }
      },
      "21": {
        start: {
          line: 44,
          column: 10
        },
        end: {
          line: 49,
          column: 11
        }
      },
      "22": {
        start: {
          line: 45,
          column: 12
        },
        end: {
          line: 48,
          column: 14
        }
      },
      "23": {
        start: {
          line: 50,
          column: 10
        },
        end: {
          line: 53,
          column: 12
        }
      },
      "24": {
        start: {
          line: 55,
          column: 10
        },
        end: {
          line: 55,
          column: 74
        }
      },
      "25": {
        start: {
          line: 56,
          column: 10
        },
        end: {
          line: 59,
          column: 12
        }
      },
      "26": {
        start: {
          line: 63,
          column: 8
        },
        end: {
          line: 63,
          column: 47
        }
      },
      "27": {
        start: {
          line: 65,
          column: 6
        },
        end: {
          line: 65,
          column: 21
        }
      },
      "28": {
        start: {
          line: 69,
          column: 0
        },
        end: {
          line: 69,
          column: 58
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 12,
            column: 30
          },
          end: {
            line: 12,
            column: 31
          }
        },
        loc: {
          start: {
            line: 12,
            column: 42
          },
          end: {
            line: 68,
            column: 1
          }
        },
        line: 12
      },
      "1": {
        name: "ValidateCustomerUseCase",
        decl: {
          start: {
            line: 13,
            column: 11
          },
          end: {
            line: 13,
            column: 34
          }
        },
        loc: {
          start: {
            line: 13,
            column: 70
          },
          end: {
            line: 17,
            column: 3
          }
        },
        line: 13
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 20,
            column: 11
          },
          end: {
            line: 20,
            column: 12
          }
        },
        loc: {
          start: {
            line: 20,
            column: 23
          },
          end: {
            line: 66,
            column: 5
          }
        },
        line: 20
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 21,
            column: 53
          },
          end: {
            line: 21,
            column: 54
          }
        },
        loc: {
          start: {
            line: 21,
            column: 73
          },
          end: {
            line: 61,
            column: 7
          }
        },
        line: 21
      },
      "4": {
        name: "execute",
        decl: {
          start: {
            line: 62,
            column: 15
          },
          end: {
            line: 62,
            column: 22
          }
        },
        loc: {
          start: {
            line: 62,
            column: 27
          },
          end: {
            line: 64,
            column: 7
          }
        },
        line: 62
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 29,
            column: 119
          },
          end: {
            line: 29,
            column: 153
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 29,
            column: 137
          },
          end: {
            line: 29,
            column: 144
          }
        }, {
          start: {
            line: 29,
            column: 147
          },
          end: {
            line: 29,
            column: 153
          }
        }],
        line: 29
      },
      "1": {
        loc: {
          start: {
            line: 31,
            column: 10
          },
          end: {
            line: 37,
            column: 11
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 31,
            column: 10
          },
          end: {
            line: 37,
            column: 11
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 31
      },
      "2": {
        loc: {
          start: {
            line: 31,
            column: 14
          },
          end: {
            line: 31,
            column: 99
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 31,
            column: 14
          },
          end: {
            line: 31,
            column: 23
          }
        }, {
          start: {
            line: 31,
            column: 27
          },
          end: {
            line: 31,
            column: 39
          }
        }, {
          start: {
            line: 31,
            column: 43
          },
          end: {
            line: 31,
            column: 55
          }
        }, {
          start: {
            line: 31,
            column: 59
          },
          end: {
            line: 31,
            column: 77
          }
        }, {
          start: {
            line: 31,
            column: 81
          },
          end: {
            line: 31,
            column: 99
          }
        }],
        line: 31
      },
      "3": {
        loc: {
          start: {
            line: 35,
            column: 55
          },
          end: {
            line: 35,
            column: 199
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 35,
            column: 176
          },
          end: {
            line: 35,
            column: 182
          }
        }, {
          start: {
            line: 35,
            column: 185
          },
          end: {
            line: 35,
            column: 199
          }
        }],
        line: 35
      },
      "4": {
        loc: {
          start: {
            line: 35,
            column: 55
          },
          end: {
            line: 35,
            column: 173
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 35,
            column: 55
          },
          end: {
            line: 35,
            column: 89
          }
        }, {
          start: {
            line: 35,
            column: 93
          },
          end: {
            line: 35,
            column: 133
          }
        }, {
          start: {
            line: 35,
            column: 137
          },
          end: {
            line: 35,
            column: 173
          }
        }],
        line: 35
      },
      "5": {
        loc: {
          start: {
            line: 38,
            column: 10
          },
          end: {
            line: 43,
            column: 11
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 38,
            column: 10
          },
          end: {
            line: 43,
            column: 11
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 38
      },
      "6": {
        loc: {
          start: {
            line: 44,
            column: 10
          },
          end: {
            line: 49,
            column: 11
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 44,
            column: 10
          },
          end: {
            line: 49,
            column: 11
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 44
      },
      "7": {
        loc: {
          start: {
            line: 44,
            column: 15
          },
          end: {
            line: 44,
            column: 95
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 44,
            column: 63
          },
          end: {
            line: 44,
            column: 69
          }
        }, {
          start: {
            line: 44,
            column: 72
          },
          end: {
            line: 44,
            column: 95
          }
        }],
        line: 44
      },
      "8": {
        loc: {
          start: {
            line: 58,
            column: 19
          },
          end: {
            line: 58,
            column: 110
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 58,
            column: 67
          },
          end: {
            line: 58,
            column: 72
          }
        }, {
          start: {
            line: 58,
            column: 75
          },
          end: {
            line: 58,
            column: 110
          }
        }],
        line: 58
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0
    },
    b: {
      "0": [0, 0],
      "1": [0, 0],
      "2": [0, 0, 0, 0, 0],
      "3": [0, 0],
      "4": [0, 0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0]
    },
    inputSourceMap: {
      version: 3,
      file: undefined,
      names: ["MSBCustomError_1", "require", "ValidateCustomerUseCase", "repository", "arrangementRepository", "_classCallCheck2", "default", "_createClass2", "key", "value", "_execute", "_asyncToGenerator2", "request", "params", "externalStateIds", "externalProductKindIds", "currency", "_response$1$data", "response", "Promise", "all", "getProfile", "sourceAccountList", "console", "log", "errors", "_response$", "status", "error", "createError", "isBlocked", "data", "length", "CustomError", "execute", "_x", "apply", "arguments", "exports"],
      sourceRoot: undefined,
      sources: ["/Users/<USER>/projects/react-native/payment-module/src/domain/usecases/customer/ValidateCustomerUseCase.ts"],
      sourcesContent: ["import {ICustomerRepository} from '../../repositories/ICustomerRepository';\nimport {ResultState} from '../../../core/ResultState';\nimport {IArrangementRepository} from '../../repositories/IArrangementRepository';\nimport {SourceAccountListRequest} from '../../../data/models/source-account-list/SourceAccountListRequest';\nimport {createError, CustomError} from '../../../core/MSBCustomError';\nexport class ValidateCustomerUseCase {\n  private repository: ICustomerRepository;\n  private arrangementRepository: IArrangementRepository;\n\n  constructor(repository: ICustomerRepository, arrangementRepository: IArrangementRepository) {\n    this.arrangementRepository = arrangementRepository;\n    this.repository = repository;\n  }\n\n  public async execute(request?: SourceAccountListRequest): Promise<ResultState<boolean>> {\n    // call this.repository.getProfile(...)\n    const params: SourceAccountListRequest = {\n      externalStateIds: ['ACTIVE'],\n      externalProductKindIds: ['kind1', 'kind10'],\n      currency: 'VND',\n    };\n    try {\n      const response = await Promise.all([\n        this.repository.getProfile(),\n        this.arrangementRepository.sourceAccountList(request ?? params),\n      ]);\n      console.log('\u2705 ExecutionHandler with response=', response);\n      if (!response || !response[0] || !response[1] || response[0].errors || response[1].errors) {\n        return {\n          status: 'ERROR',\n          error: createError(response[0]?.errors?.[0]?.key),\n        };\n      }\n      if (response[0].isBlocked() === true) {\n        return {\n          status: 'ERROR',\n          error: createError(), //TODO: block user\n        };\n      }\n      if (response[1].data?.length === 0) {\n        return {\n          status: 'ERROR',\n          error: createError(), //TODO: khong co tai khoan nguon phu hop\n        };\n      }\n      return {\n        status: 'SUCCESS',\n        data: true,\n      };\n    } catch (error) {\n      console.error('\u274C ExecutionHandler UseCase catch Error:', error);\n      return {\n        status: 'ERROR',\n        error: error instanceof CustomError ? error : createError(),\n      };\n    }\n  }\n}\n"],
      mappings: ";;;;;;;;;;AAIA,IAAAA,gBAAA,GAAAC,OAAA;AAAsE,IACzDC,uBAAuB;EAIlC,SAAAA,wBAAYC,UAA+B,EAAEC,qBAA6C;IAAA,IAAAC,gBAAA,CAAAC,OAAA,QAAAJ,uBAAA;IACxF,IAAI,CAACE,qBAAqB,GAAGA,qBAAqB;IAClD,IAAI,CAACD,UAAU,GAAGA,UAAU;EAC9B;EAAC,WAAAI,aAAA,CAAAD,OAAA,EAAAJ,uBAAA;IAAAM,GAAA;IAAAC,KAAA;MAAA,IAAAC,QAAA,OAAAC,kBAAA,CAAAL,OAAA,EAEM,WAAcM,OAAkC;QAErD,IAAMC,MAAM,GAA6B;UACvCC,gBAAgB,EAAE,CAAC,QAAQ,CAAC;UAC5BC,sBAAsB,EAAE,CAAC,OAAO,EAAE,QAAQ,CAAC;UAC3CC,QAAQ,EAAE;SACX;QACD,IAAI;UAAA,IAAAC,gBAAA;UACF,IAAMC,QAAQ,SAASC,OAAO,CAACC,GAAG,CAAC,CACjC,IAAI,CAACjB,UAAU,CAACkB,UAAU,EAAE,EAC5B,IAAI,CAACjB,qBAAqB,CAACkB,iBAAiB,CAACV,OAAO,WAAPA,OAAO,GAAIC,MAAM,CAAC,CAChE,CAAC;UACFU,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAEN,QAAQ,CAAC;UAC1D,IAAI,CAACA,QAAQ,IAAI,CAACA,QAAQ,CAAC,CAAC,CAAC,IAAI,CAACA,QAAQ,CAAC,CAAC,CAAC,IAAIA,QAAQ,CAAC,CAAC,CAAC,CAACO,MAAM,IAAIP,QAAQ,CAAC,CAAC,CAAC,CAACO,MAAM,EAAE;YAAA,IAAAC,UAAA;YACzF,OAAO;cACLC,MAAM,EAAE,OAAO;cACfC,KAAK,EAAE,IAAA5B,gBAAA,CAAA6B,WAAW,GAAAH,UAAA,GAACR,QAAQ,CAAC,CAAC,CAAC,cAAAQ,UAAA,GAAXA,UAAA,CAAaD,MAAM,cAAAC,UAAA,GAAnBA,UAAA,CAAsB,CAAC,CAAC,qBAAxBA,UAAA,CAA0BlB,GAAG;aACjD;UACH;UACA,IAAIU,QAAQ,CAAC,CAAC,CAAC,CAACY,SAAS,EAAE,KAAK,IAAI,EAAE;YACpC,OAAO;cACLH,MAAM,EAAE,OAAO;cACfC,KAAK,EAAE,IAAA5B,gBAAA,CAAA6B,WAAW;aACnB;UACH;UACA,IAAI,EAAAZ,gBAAA,GAAAC,QAAQ,CAAC,CAAC,CAAC,CAACa,IAAI,qBAAhBd,gBAAA,CAAkBe,MAAM,MAAK,CAAC,EAAE;YAClC,OAAO;cACLL,MAAM,EAAE,OAAO;cACfC,KAAK,EAAE,IAAA5B,gBAAA,CAAA6B,WAAW;aACnB;UACH;UACA,OAAO;YACLF,MAAM,EAAE,SAAS;YACjBI,IAAI,EAAE;WACP;QACH,CAAC,CAAC,OAAOH,KAAK,EAAE;UACdL,OAAO,CAACK,KAAK,CAAC,yCAAyC,EAAEA,KAAK,CAAC;UAC/D,OAAO;YACLD,MAAM,EAAE,OAAO;YACfC,KAAK,EAAEA,KAAK,YAAY5B,gBAAA,CAAAiC,WAAW,GAAGL,KAAK,GAAG,IAAA5B,gBAAA,CAAA6B,WAAW;WAC1D;QACH;MACF,CAAC;MAAA,SA1CYK,OAAOA,CAAAC,EAAA;QAAA,OAAAzB,QAAA,CAAA0B,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAPH,OAAO;IAAA;EAAA;AAAA;AATtBI,OAAA,CAAApC,uBAAA,GAAAA,uBAAA",
      ignoreList: []
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "c1b3c199355ad28cd1dc2b47710b085bab82ba9c"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_298lvbvyzs = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_298lvbvyzs();
var _interopRequireDefault =
/* istanbul ignore next */
(cov_298lvbvyzs().s[0]++, require("@babel/runtime/helpers/interopRequireDefault"));
var _asyncToGenerator2 =
/* istanbul ignore next */
(cov_298lvbvyzs().s[1]++, _interopRequireDefault(require("@babel/runtime/helpers/asyncToGenerator")));
var _classCallCheck2 =
/* istanbul ignore next */
(cov_298lvbvyzs().s[2]++, _interopRequireDefault(require("@babel/runtime/helpers/classCallCheck")));
var _createClass2 =
/* istanbul ignore next */
(cov_298lvbvyzs().s[3]++, _interopRequireDefault(require("@babel/runtime/helpers/createClass")));
/* istanbul ignore next */
cov_298lvbvyzs().s[4]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_298lvbvyzs().s[5]++;
exports.ValidateCustomerUseCase = void 0;
var MSBCustomError_1 =
/* istanbul ignore next */
(cov_298lvbvyzs().s[6]++, require("../../../core/MSBCustomError"));
var ValidateCustomerUseCase =
/* istanbul ignore next */
(cov_298lvbvyzs().s[7]++, function () {
  /* istanbul ignore next */
  cov_298lvbvyzs().f[0]++;
  function ValidateCustomerUseCase(repository, arrangementRepository) {
    /* istanbul ignore next */
    cov_298lvbvyzs().f[1]++;
    cov_298lvbvyzs().s[8]++;
    (0, _classCallCheck2.default)(this, ValidateCustomerUseCase);
    /* istanbul ignore next */
    cov_298lvbvyzs().s[9]++;
    this.arrangementRepository = arrangementRepository;
    /* istanbul ignore next */
    cov_298lvbvyzs().s[10]++;
    this.repository = repository;
  }
  /* istanbul ignore next */
  cov_298lvbvyzs().s[11]++;
  return (0, _createClass2.default)(ValidateCustomerUseCase, [{
    key: "execute",
    value: function () {
      /* istanbul ignore next */
      cov_298lvbvyzs().f[2]++;
      var _execute =
      /* istanbul ignore next */
      (cov_298lvbvyzs().s[12]++, (0, _asyncToGenerator2.default)(function* (request) {
        /* istanbul ignore next */
        cov_298lvbvyzs().f[3]++;
        var params =
        /* istanbul ignore next */
        (cov_298lvbvyzs().s[13]++, {
          externalStateIds: ['ACTIVE'],
          externalProductKindIds: ['kind1', 'kind10'],
          currency: 'VND'
        });
        /* istanbul ignore next */
        cov_298lvbvyzs().s[14]++;
        try {
          var _response$1$data;
          var response =
          /* istanbul ignore next */
          (cov_298lvbvyzs().s[15]++, yield Promise.all([this.repository.getProfile(), this.arrangementRepository.sourceAccountList(request != null ?
          /* istanbul ignore next */
          (cov_298lvbvyzs().b[0][0]++, request) :
          /* istanbul ignore next */
          (cov_298lvbvyzs().b[0][1]++, params))]));
          /* istanbul ignore next */
          cov_298lvbvyzs().s[16]++;
          console.log('✅ ExecutionHandler with response=', response);
          /* istanbul ignore next */
          cov_298lvbvyzs().s[17]++;
          if (
          /* istanbul ignore next */
          (cov_298lvbvyzs().b[2][0]++, !response) ||
          /* istanbul ignore next */
          (cov_298lvbvyzs().b[2][1]++, !response[0]) ||
          /* istanbul ignore next */
          (cov_298lvbvyzs().b[2][2]++, !response[1]) ||
          /* istanbul ignore next */
          (cov_298lvbvyzs().b[2][3]++, response[0].errors) ||
          /* istanbul ignore next */
          (cov_298lvbvyzs().b[2][4]++, response[1].errors)) {
            /* istanbul ignore next */
            cov_298lvbvyzs().b[1][0]++;
            var _response$;
            /* istanbul ignore next */
            cov_298lvbvyzs().s[18]++;
            return {
              status: 'ERROR',
              error: (0, MSBCustomError_1.createError)(
              /* istanbul ignore next */
              (cov_298lvbvyzs().b[4][0]++, (_response$ = response[0]) == null) ||
              /* istanbul ignore next */
              (cov_298lvbvyzs().b[4][1]++, (_response$ = _response$.errors) == null) ||
              /* istanbul ignore next */
              (cov_298lvbvyzs().b[4][2]++, (_response$ = _response$[0]) == null) ?
              /* istanbul ignore next */
              (cov_298lvbvyzs().b[3][0]++, void 0) :
              /* istanbul ignore next */
              (cov_298lvbvyzs().b[3][1]++, _response$.key))
            };
          } else
          /* istanbul ignore next */
          {
            cov_298lvbvyzs().b[1][1]++;
          }
          cov_298lvbvyzs().s[19]++;
          if (response[0].isBlocked() === true) {
            /* istanbul ignore next */
            cov_298lvbvyzs().b[5][0]++;
            cov_298lvbvyzs().s[20]++;
            return {
              status: 'ERROR',
              error: (0, MSBCustomError_1.createError)()
            };
          } else
          /* istanbul ignore next */
          {
            cov_298lvbvyzs().b[5][1]++;
          }
          cov_298lvbvyzs().s[21]++;
          if (((_response$1$data = response[1].data) == null ?
          /* istanbul ignore next */
          (cov_298lvbvyzs().b[7][0]++, void 0) :
          /* istanbul ignore next */
          (cov_298lvbvyzs().b[7][1]++, _response$1$data.length)) === 0) {
            /* istanbul ignore next */
            cov_298lvbvyzs().b[6][0]++;
            cov_298lvbvyzs().s[22]++;
            return {
              status: 'ERROR',
              error: (0, MSBCustomError_1.createError)()
            };
          } else
          /* istanbul ignore next */
          {
            cov_298lvbvyzs().b[6][1]++;
          }
          cov_298lvbvyzs().s[23]++;
          return {
            status: 'SUCCESS',
            data: true
          };
        } catch (error) {
          /* istanbul ignore next */
          cov_298lvbvyzs().s[24]++;
          console.error('❌ ExecutionHandler UseCase catch Error:', error);
          /* istanbul ignore next */
          cov_298lvbvyzs().s[25]++;
          return {
            status: 'ERROR',
            error: error instanceof MSBCustomError_1.CustomError ?
            /* istanbul ignore next */
            (cov_298lvbvyzs().b[8][0]++, error) :
            /* istanbul ignore next */
            (cov_298lvbvyzs().b[8][1]++, (0, MSBCustomError_1.createError)())
          };
        }
      }));
      function execute(_x) {
        /* istanbul ignore next */
        cov_298lvbvyzs().f[4]++;
        cov_298lvbvyzs().s[26]++;
        return _execute.apply(this, arguments);
      }
      /* istanbul ignore next */
      cov_298lvbvyzs().s[27]++;
      return execute;
    }()
  }]);
}());
/* istanbul ignore next */
cov_298lvbvyzs().s[28]++;
exports.ValidateCustomerUseCase = ValidateCustomerUseCase;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJjb3ZfMjk4bHZidnl6cyIsImFjdHVhbENvdmVyYWdlIiwiTVNCQ3VzdG9tRXJyb3JfMSIsInMiLCJyZXF1aXJlIiwiVmFsaWRhdGVDdXN0b21lclVzZUNhc2UiLCJmIiwicmVwb3NpdG9yeSIsImFycmFuZ2VtZW50UmVwb3NpdG9yeSIsIl9jbGFzc0NhbGxDaGVjazIiLCJkZWZhdWx0IiwiX2NyZWF0ZUNsYXNzMiIsImtleSIsInZhbHVlIiwiX2V4ZWN1dGUiLCJfYXN5bmNUb0dlbmVyYXRvcjIiLCJyZXF1ZXN0IiwicGFyYW1zIiwiZXh0ZXJuYWxTdGF0ZUlkcyIsImV4dGVybmFsUHJvZHVjdEtpbmRJZHMiLCJjdXJyZW5jeSIsIl9yZXNwb25zZSQxJGRhdGEiLCJyZXNwb25zZSIsIlByb21pc2UiLCJhbGwiLCJnZXRQcm9maWxlIiwic291cmNlQWNjb3VudExpc3QiLCJiIiwiY29uc29sZSIsImxvZyIsImVycm9ycyIsIl9yZXNwb25zZSQiLCJzdGF0dXMiLCJlcnJvciIsImNyZWF0ZUVycm9yIiwiaXNCbG9ja2VkIiwiZGF0YSIsImxlbmd0aCIsIkN1c3RvbUVycm9yIiwiZXhlY3V0ZSIsIl94IiwiYXBwbHkiLCJhcmd1bWVudHMiLCJleHBvcnRzIl0sInNvdXJjZXMiOlsiL1VzZXJzL3NvbnNhbXNldC9wcm9qZWN0cy9yZWFjdC1uYXRpdmUvcGF5bWVudC1tb2R1bGUvc3JjL2RvbWFpbi91c2VjYXNlcy9jdXN0b21lci9WYWxpZGF0ZUN1c3RvbWVyVXNlQ2FzZS50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQge0lDdXN0b21lclJlcG9zaXRvcnl9IGZyb20gJy4uLy4uL3JlcG9zaXRvcmllcy9JQ3VzdG9tZXJSZXBvc2l0b3J5JztcbmltcG9ydCB7UmVzdWx0U3RhdGV9IGZyb20gJy4uLy4uLy4uL2NvcmUvUmVzdWx0U3RhdGUnO1xuaW1wb3J0IHtJQXJyYW5nZW1lbnRSZXBvc2l0b3J5fSBmcm9tICcuLi8uLi9yZXBvc2l0b3JpZXMvSUFycmFuZ2VtZW50UmVwb3NpdG9yeSc7XG5pbXBvcnQge1NvdXJjZUFjY291bnRMaXN0UmVxdWVzdH0gZnJvbSAnLi4vLi4vLi4vZGF0YS9tb2RlbHMvc291cmNlLWFjY291bnQtbGlzdC9Tb3VyY2VBY2NvdW50TGlzdFJlcXVlc3QnO1xuaW1wb3J0IHtjcmVhdGVFcnJvciwgQ3VzdG9tRXJyb3J9IGZyb20gJy4uLy4uLy4uL2NvcmUvTVNCQ3VzdG9tRXJyb3InO1xuZXhwb3J0IGNsYXNzIFZhbGlkYXRlQ3VzdG9tZXJVc2VDYXNlIHtcbiAgcHJpdmF0ZSByZXBvc2l0b3J5OiBJQ3VzdG9tZXJSZXBvc2l0b3J5O1xuICBwcml2YXRlIGFycmFuZ2VtZW50UmVwb3NpdG9yeTogSUFycmFuZ2VtZW50UmVwb3NpdG9yeTtcblxuICBjb25zdHJ1Y3RvcihyZXBvc2l0b3J5OiBJQ3VzdG9tZXJSZXBvc2l0b3J5LCBhcnJhbmdlbWVudFJlcG9zaXRvcnk6IElBcnJhbmdlbWVudFJlcG9zaXRvcnkpIHtcbiAgICB0aGlzLmFycmFuZ2VtZW50UmVwb3NpdG9yeSA9IGFycmFuZ2VtZW50UmVwb3NpdG9yeTtcbiAgICB0aGlzLnJlcG9zaXRvcnkgPSByZXBvc2l0b3J5O1xuICB9XG5cbiAgcHVibGljIGFzeW5jIGV4ZWN1dGUocmVxdWVzdD86IFNvdXJjZUFjY291bnRMaXN0UmVxdWVzdCk6IFByb21pc2U8UmVzdWx0U3RhdGU8Ym9vbGVhbj4+IHtcbiAgICAvLyBjYWxsIHRoaXMucmVwb3NpdG9yeS5nZXRQcm9maWxlKC4uLilcbiAgICBjb25zdCBwYXJhbXM6IFNvdXJjZUFjY291bnRMaXN0UmVxdWVzdCA9IHtcbiAgICAgIGV4dGVybmFsU3RhdGVJZHM6IFsnQUNUSVZFJ10sXG4gICAgICBleHRlcm5hbFByb2R1Y3RLaW5kSWRzOiBbJ2tpbmQxJywgJ2tpbmQxMCddLFxuICAgICAgY3VycmVuY3k6ICdWTkQnLFxuICAgIH07XG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgUHJvbWlzZS5hbGwoW1xuICAgICAgICB0aGlzLnJlcG9zaXRvcnkuZ2V0UHJvZmlsZSgpLFxuICAgICAgICB0aGlzLmFycmFuZ2VtZW50UmVwb3NpdG9yeS5zb3VyY2VBY2NvdW50TGlzdChyZXF1ZXN0ID8/IHBhcmFtcyksXG4gICAgICBdKTtcbiAgICAgIGNvbnNvbGUubG9nKCfinIUgRXhlY3V0aW9uSGFuZGxlciB3aXRoIHJlc3BvbnNlPScsIHJlc3BvbnNlKTtcbiAgICAgIGlmICghcmVzcG9uc2UgfHwgIXJlc3BvbnNlWzBdIHx8ICFyZXNwb25zZVsxXSB8fCByZXNwb25zZVswXS5lcnJvcnMgfHwgcmVzcG9uc2VbMV0uZXJyb3JzKSB7XG4gICAgICAgIHJldHVybiB7XG4gICAgICAgICAgc3RhdHVzOiAnRVJST1InLFxuICAgICAgICAgIGVycm9yOiBjcmVhdGVFcnJvcihyZXNwb25zZVswXT8uZXJyb3JzPy5bMF0/LmtleSksXG4gICAgICAgIH07XG4gICAgICB9XG4gICAgICBpZiAocmVzcG9uc2VbMF0uaXNCbG9ja2VkKCkgPT09IHRydWUpIHtcbiAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICBzdGF0dXM6ICdFUlJPUicsXG4gICAgICAgICAgZXJyb3I6IGNyZWF0ZUVycm9yKCksIC8vVE9ETzogYmxvY2sgdXNlclxuICAgICAgICB9O1xuICAgICAgfVxuICAgICAgaWYgKHJlc3BvbnNlWzFdLmRhdGE/Lmxlbmd0aCA9PT0gMCkge1xuICAgICAgICByZXR1cm4ge1xuICAgICAgICAgIHN0YXR1czogJ0VSUk9SJyxcbiAgICAgICAgICBlcnJvcjogY3JlYXRlRXJyb3IoKSwgLy9UT0RPOiBraG9uZyBjbyB0YWkga2hvYW4gbmd1b24gcGh1IGhvcFxuICAgICAgICB9O1xuICAgICAgfVxuICAgICAgcmV0dXJuIHtcbiAgICAgICAgc3RhdHVzOiAnU1VDQ0VTUycsXG4gICAgICAgIGRhdGE6IHRydWUsXG4gICAgICB9O1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCfinYwgRXhlY3V0aW9uSGFuZGxlciBVc2VDYXNlIGNhdGNoIEVycm9yOicsIGVycm9yKTtcbiAgICAgIHJldHVybiB7XG4gICAgICAgIHN0YXR1czogJ0VSUk9SJyxcbiAgICAgICAgZXJyb3I6IGVycm9yIGluc3RhbmNlb2YgQ3VzdG9tRXJyb3IgPyBlcnJvciA6IGNyZWF0ZUVycm9yKCksXG4gICAgICB9O1xuICAgIH1cbiAgfVxufVxuIl0sIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztJQVdTO0lBQUFBLGNBQUEsWUFBQUEsQ0FBQTtNQUFBLE9BQUFDLGNBQUE7SUFBQTtFQUFBO0VBQUEsT0FBQUEsY0FBQTtBQUFBO0FBQUFELGNBQUE7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQVBULElBQUFFLGdCQUFBO0FBQUE7QUFBQSxDQUFBRixjQUFBLEdBQUFHLENBQUEsT0FBQUMsT0FBQTtBQUFzRSxJQUN6REMsdUJBQXVCO0FBQUE7QUFBQSxDQUFBTCxjQUFBLEdBQUFHLENBQUE7RUFBQTtFQUFBSCxjQUFBLEdBQUFNLENBQUE7RUFJbEMsU0FBQUQsd0JBQVlFLFVBQStCLEVBQUVDLHFCQUE2QztJQUFBO0lBQUFSLGNBQUEsR0FBQU0sQ0FBQTtJQUFBTixjQUFBLEdBQUFHLENBQUE7SUFBQSxJQUFBTSxnQkFBQSxDQUFBQyxPQUFBLFFBQUFMLHVCQUFBO0lBQUE7SUFBQUwsY0FBQSxHQUFBRyxDQUFBO0lBQ3hGLElBQUksQ0FBQ0sscUJBQXFCLEdBQUdBLHFCQUFxQjtJQUFBO0lBQUFSLGNBQUEsR0FBQUcsQ0FBQTtJQUNsRCxJQUFJLENBQUNJLFVBQVUsR0FBR0EsVUFBVTtFQUM5QjtFQUFBO0VBQUFQLGNBQUEsR0FBQUcsQ0FBQTtFQUFDLFdBQUFRLGFBQUEsQ0FBQUQsT0FBQSxFQUFBTCx1QkFBQTtJQUFBTyxHQUFBO0lBQUFDLEtBQUE7TUFBQTtNQUFBYixjQUFBLEdBQUFNLENBQUE7TUFBQSxJQUFBUSxRQUFBO01BQUE7TUFBQSxDQUFBZCxjQUFBLEdBQUFHLENBQUEsWUFBQVksa0JBQUEsQ0FBQUwsT0FBQSxFQUVNLFdBQWNNLE9BQWtDO1FBQUE7UUFBQWhCLGNBQUEsR0FBQU0sQ0FBQTtRQUVyRCxJQUFNVyxNQUFNO1FBQUE7UUFBQSxDQUFBakIsY0FBQSxHQUFBRyxDQUFBLFFBQTZCO1VBQ3ZDZSxnQkFBZ0IsRUFBRSxDQUFDLFFBQVEsQ0FBQztVQUM1QkMsc0JBQXNCLEVBQUUsQ0FBQyxPQUFPLEVBQUUsUUFBUSxDQUFDO1VBQzNDQyxRQUFRLEVBQUU7U0FDWDtRQUFBO1FBQUFwQixjQUFBLEdBQUFHLENBQUE7UUFDRCxJQUFJO1VBQUEsSUFBQWtCLGdCQUFBO1VBQ0YsSUFBTUMsUUFBUTtVQUFBO1VBQUEsQ0FBQXRCLGNBQUEsR0FBQUcsQ0FBQSxjQUFTb0IsT0FBTyxDQUFDQyxHQUFHLENBQUMsQ0FDakMsSUFBSSxDQUFDakIsVUFBVSxDQUFDa0IsVUFBVSxFQUFFLEVBQzVCLElBQUksQ0FBQ2pCLHFCQUFxQixDQUFDa0IsaUJBQWlCLENBQUNWLE9BQU87VUFBQTtVQUFBLENBQUFoQixjQUFBLEdBQUEyQixDQUFBLFVBQVBYLE9BQU87VUFBQTtVQUFBLENBQUFoQixjQUFBLEdBQUEyQixDQUFBLFVBQUlWLE1BQU0sRUFBQyxDQUNoRSxDQUFDO1VBQUE7VUFBQWpCLGNBQUEsR0FBQUcsQ0FBQTtVQUNGeUIsT0FBTyxDQUFDQyxHQUFHLENBQUMsbUNBQW1DLEVBQUVQLFFBQVEsQ0FBQztVQUFBO1VBQUF0QixjQUFBLEdBQUFHLENBQUE7VUFDMUQ7VUFBSTtVQUFBLENBQUFILGNBQUEsR0FBQTJCLENBQUEsV0FBQ0wsUUFBUTtVQUFBO1VBQUEsQ0FBQXRCLGNBQUEsR0FBQTJCLENBQUEsVUFBSSxDQUFDTCxRQUFRLENBQUMsQ0FBQyxDQUFDO1VBQUE7VUFBQSxDQUFBdEIsY0FBQSxHQUFBMkIsQ0FBQSxVQUFJLENBQUNMLFFBQVEsQ0FBQyxDQUFDLENBQUM7VUFBQTtVQUFBLENBQUF0QixjQUFBLEdBQUEyQixDQUFBLFVBQUlMLFFBQVEsQ0FBQyxDQUFDLENBQUMsQ0FBQ1EsTUFBTTtVQUFBO1VBQUEsQ0FBQTlCLGNBQUEsR0FBQTJCLENBQUEsVUFBSUwsUUFBUSxDQUFDLENBQUMsQ0FBQyxDQUFDUSxNQUFNLEdBQUU7WUFBQTtZQUFBOUIsY0FBQSxHQUFBMkIsQ0FBQTtZQUFBLElBQUFJLFVBQUE7WUFBQTtZQUFBL0IsY0FBQSxHQUFBRyxDQUFBO1lBQ3pGLE9BQU87Y0FDTDZCLE1BQU0sRUFBRSxPQUFPO2NBQ2ZDLEtBQUssRUFBRSxJQUFBL0IsZ0JBQUEsQ0FBQWdDLFdBQVc7Y0FBQTtjQUFBLENBQUFsQyxjQUFBLEdBQUEyQixDQUFBLFdBQUFJLFVBQUEsR0FBQ1QsUUFBUSxDQUFDLENBQUMsQ0FBQztjQUFBO2NBQUEsQ0FBQXRCLGNBQUEsR0FBQTJCLENBQUEsV0FBQUksVUFBQSxHQUFYQSxVQUFBLENBQWFELE1BQU07Y0FBQTtjQUFBLENBQUE5QixjQUFBLEdBQUEyQixDQUFBLFdBQUFJLFVBQUEsR0FBbkJBLFVBQUEsQ0FBc0IsQ0FBQyxDQUFDO2NBQUE7Y0FBQSxDQUFBL0IsY0FBQSxHQUFBMkIsQ0FBQTtjQUFBO2NBQUEsQ0FBQTNCLGNBQUEsR0FBQTJCLENBQUEsVUFBeEJJLFVBQUEsQ0FBMEJuQixHQUFHO2FBQ2pEO1VBQ0g7VUFBQTtVQUFBO1lBQUFaLGNBQUEsR0FBQTJCLENBQUE7VUFBQTtVQUFBM0IsY0FBQSxHQUFBRyxDQUFBO1VBQ0EsSUFBSW1CLFFBQVEsQ0FBQyxDQUFDLENBQUMsQ0FBQ2EsU0FBUyxFQUFFLEtBQUssSUFBSSxFQUFFO1lBQUE7WUFBQW5DLGNBQUEsR0FBQTJCLENBQUE7WUFBQTNCLGNBQUEsR0FBQUcsQ0FBQTtZQUNwQyxPQUFPO2NBQ0w2QixNQUFNLEVBQUUsT0FBTztjQUNmQyxLQUFLLEVBQUUsSUFBQS9CLGdCQUFBLENBQUFnQyxXQUFXO2FBQ25CO1VBQ0g7VUFBQTtVQUFBO1lBQUFsQyxjQUFBLEdBQUEyQixDQUFBO1VBQUE7VUFBQTNCLGNBQUEsR0FBQUcsQ0FBQTtVQUNBLElBQUksRUFBQWtCLGdCQUFBLEdBQUFDLFFBQVEsQ0FBQyxDQUFDLENBQUMsQ0FBQ2MsSUFBSTtVQUFBO1VBQUEsQ0FBQXBDLGNBQUEsR0FBQTJCLENBQUE7VUFBQTtVQUFBLENBQUEzQixjQUFBLEdBQUEyQixDQUFBLFVBQWhCTixnQkFBQSxDQUFrQmdCLE1BQU0sT0FBSyxDQUFDLEVBQUU7WUFBQTtZQUFBckMsY0FBQSxHQUFBMkIsQ0FBQTtZQUFBM0IsY0FBQSxHQUFBRyxDQUFBO1lBQ2xDLE9BQU87Y0FDTDZCLE1BQU0sRUFBRSxPQUFPO2NBQ2ZDLEtBQUssRUFBRSxJQUFBL0IsZ0JBQUEsQ0FBQWdDLFdBQVc7YUFDbkI7VUFDSDtVQUFBO1VBQUE7WUFBQWxDLGNBQUEsR0FBQTJCLENBQUE7VUFBQTtVQUFBM0IsY0FBQSxHQUFBRyxDQUFBO1VBQ0EsT0FBTztZQUNMNkIsTUFBTSxFQUFFLFNBQVM7WUFDakJJLElBQUksRUFBRTtXQUNQO1FBQ0gsQ0FBQyxDQUFDLE9BQU9ILEtBQUssRUFBRTtVQUFBO1VBQUFqQyxjQUFBLEdBQUFHLENBQUE7VUFDZHlCLE9BQU8sQ0FBQ0ssS0FBSyxDQUFDLHlDQUF5QyxFQUFFQSxLQUFLLENBQUM7VUFBQTtVQUFBakMsY0FBQSxHQUFBRyxDQUFBO1VBQy9ELE9BQU87WUFDTDZCLE1BQU0sRUFBRSxPQUFPO1lBQ2ZDLEtBQUssRUFBRUEsS0FBSyxZQUFZL0IsZ0JBQUEsQ0FBQW9DLFdBQVc7WUFBQTtZQUFBLENBQUF0QyxjQUFBLEdBQUEyQixDQUFBLFVBQUdNLEtBQUs7WUFBQTtZQUFBLENBQUFqQyxjQUFBLEdBQUEyQixDQUFBLFVBQUcsSUFBQXpCLGdCQUFBLENBQUFnQyxXQUFXO1dBQzFEO1FBQ0g7TUFDRixDQUFDO01BQUEsU0ExQ1lLLE9BQU9BLENBQUFDLEVBQUE7UUFBQTtRQUFBeEMsY0FBQSxHQUFBTSxDQUFBO1FBQUFOLGNBQUEsR0FBQUcsQ0FBQTtRQUFBLE9BQUFXLFFBQUEsQ0FBQTJCLEtBQUEsT0FBQUMsU0FBQTtNQUFBO01BQUE7TUFBQTFDLGNBQUEsR0FBQUcsQ0FBQTtNQUFBLE9BQVBvQyxPQUFPO0lBQUE7RUFBQTtBQUFBO0FBQUE7QUFBQXZDLGNBQUEsR0FBQUcsQ0FBQTtBQVR0QndDLE9BQUEsQ0FBQXRDLHVCQUFBLEdBQUFBLHVCQUFBIiwiaWdub3JlTGlzdCI6W119