{"version": 3, "names": ["Object", "defineProperty", "exports", "value", "useWorkletCallback", "_react", "require", "worklet", "deps", "useCallback"], "sources": ["../../../src/hook/useWorkletCallback.ts"], "sourcesContent": [null], "mappings": "AAAA,YAAY;;AAAAA,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,KAAA;AAAA;AAAAD,OAAA,CAAAE,kBAAA,GAAAA,kBAAA;AACZ,IAAAC,MAAA,GAAAC,OAAA;AAIO,SAASF,kBAAkBA,CAChCG,OAAuC,EACvCC,IAAqB,EACrB;EACA,OAAO,IAAAC,kBAAW,EAACF,OAAO,EAAEC,IAAI,WAAJA,IAAI,GAAI,EAAE,CAAC;AACzC", "ignoreList": []}