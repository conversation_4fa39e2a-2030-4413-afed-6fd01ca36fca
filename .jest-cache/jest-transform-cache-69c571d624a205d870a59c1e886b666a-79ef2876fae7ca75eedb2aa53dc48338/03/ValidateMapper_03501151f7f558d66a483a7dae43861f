718690f14606fc1b5793c0a0636ef92d
"use strict";

/* istanbul ignore next */
function cov_21gseeoq1w() {
  var path = "/Users/<USER>/projects/react-native/payment-module/src/data/mappers/validate/ValidateMapper.ts";
  var hash = "ea27851b2f175985165d9531796bda98e25bfb84";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/projects/react-native/payment-module/src/data/mappers/validate/ValidateMapper.ts",
    statementMap: {
      "0": {
        start: {
          line: 3,
          column: 0
        },
        end: {
          line: 5,
          column: 3
        }
      },
      "1": {
        start: {
          line: 6,
          column: 0
        },
        end: {
          line: 6,
          column: 64
        }
      },
      "2": {
        start: {
          line: 7,
          column: 22
        },
        end: {
          line: 7,
          column: 80
        }
      },
      "3": {
        start: {
          line: 9,
          column: 2
        },
        end: {
          line: 9,
          column: 45
        }
      }
    },
    fnMap: {
      "0": {
        name: "mapValidateResponseToModel",
        decl: {
          start: {
            line: 8,
            column: 9
          },
          end: {
            line: 8,
            column: 35
          }
        },
        loc: {
          start: {
            line: 8,
            column: 46
          },
          end: {
            line: 10,
            column: 1
          }
        },
        line: 8
      }
    },
    branchMap: {},
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0
    },
    f: {
      "0": 0
    },
    b: {},
    inputSourceMap: {
      version: 3,
      file: undefined,
      names: ["exports", "mapValidateResponseToModel", "ValidateModel_1", "require", "response", "ValidateModel"],
      sourceRoot: undefined,
      sources: ["/Users/<USER>/projects/react-native/payment-module/src/data/mappers/validate/ValidateMapper.ts"],
      sourcesContent: ["import {ValidateResponse} from '../../models/validate/ValidateResponse';\nimport {ValidateModel} from '../../../domain/entities/validate/ValidateModel';\n\nexport function mapValidateResponseToModel(response: ValidateResponse): ValidateModel {\n  return new ValidateModel();\n}\n"],
      mappings: ";;;;;AAGAA,OAAA,CAAAC,0BAAA,GAAAA,0BAAA;AAFA,IAAAC,eAAA,GAAAC,OAAA;AAEA,SAAgBF,0BAA0BA,CAACG,QAA0B;EACnE,OAAO,IAAIF,eAAA,CAAAG,aAAa,EAAE;AAC5B",
      ignoreList: []
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "ea27851b2f175985165d9531796bda98e25bfb84"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_21gseeoq1w = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_21gseeoq1w();
cov_21gseeoq1w().s[0]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_21gseeoq1w().s[1]++;
exports.mapValidateResponseToModel = mapValidateResponseToModel;
var ValidateModel_1 =
/* istanbul ignore next */
(cov_21gseeoq1w().s[2]++, require("../../../domain/entities/validate/ValidateModel"));
function mapValidateResponseToModel(response) {
  /* istanbul ignore next */
  cov_21gseeoq1w().f[0]++;
  cov_21gseeoq1w().s[3]++;
  return new ValidateModel_1.ValidateModel();
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJleHBvcnRzIiwibWFwVmFsaWRhdGVSZXNwb25zZVRvTW9kZWwiLCJWYWxpZGF0ZU1vZGVsXzEiLCJjb3ZfMjFnc2Vlb3ExdyIsInMiLCJyZXF1aXJlIiwicmVzcG9uc2UiLCJmIiwiVmFsaWRhdGVNb2RlbCJdLCJzb3VyY2VzIjpbIi9Vc2Vycy9zb25zYW1zZXQvcHJvamVjdHMvcmVhY3QtbmF0aXZlL3BheW1lbnQtbW9kdWxlL3NyYy9kYXRhL21hcHBlcnMvdmFsaWRhdGUvVmFsaWRhdGVNYXBwZXIudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHtWYWxpZGF0ZVJlc3BvbnNlfSBmcm9tICcuLi8uLi9tb2RlbHMvdmFsaWRhdGUvVmFsaWRhdGVSZXNwb25zZSc7XG5pbXBvcnQge1ZhbGlkYXRlTW9kZWx9IGZyb20gJy4uLy4uLy4uL2RvbWFpbi9lbnRpdGllcy92YWxpZGF0ZS9WYWxpZGF0ZU1vZGVsJztcblxuZXhwb3J0IGZ1bmN0aW9uIG1hcFZhbGlkYXRlUmVzcG9uc2VUb01vZGVsKHJlc3BvbnNlOiBWYWxpZGF0ZVJlc3BvbnNlKTogVmFsaWRhdGVNb2RlbCB7XG4gIHJldHVybiBuZXcgVmFsaWRhdGVNb2RlbCgpO1xufVxuIl0sIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUdBQSxPQUFBLENBQUFDLDBCQUFBLEdBQUFBLDBCQUFBO0FBRkEsSUFBQUMsZUFBQTtBQUFBO0FBQUEsQ0FBQUMsY0FBQSxHQUFBQyxDQUFBLE9BQUFDLE9BQUE7QUFFQSxTQUFnQkosMEJBQTBCQSxDQUFDSyxRQUEwQjtFQUFBO0VBQUFILGNBQUEsR0FBQUksQ0FBQTtFQUFBSixjQUFBLEdBQUFDLENBQUE7RUFDbkUsT0FBTyxJQUFJRixlQUFBLENBQUFNLGFBQWEsRUFBRTtBQUM1QiIsImlnbm9yZUxpc3QiOltdfQ==