{"version": 3, "names": ["exports", "mapValidateResponseToModel", "ValidateModel_1", "cov_21gseeoq1w", "s", "require", "response", "f", "ValidateModel"], "sources": ["/Users/<USER>/projects/react-native/payment-module/src/data/mappers/validate/ValidateMapper.ts"], "sourcesContent": ["import {ValidateResponse} from '../../models/validate/ValidateResponse';\nimport {ValidateModel} from '../../../domain/entities/validate/ValidateModel';\n\nexport function mapValidateResponseToModel(response: ValidateResponse): ValidateModel {\n  return new ValidateModel();\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGAA,OAAA,CAAAC,0BAAA,GAAAA,0BAAA;AAFA,IAAAC,eAAA;AAAA;AAAA,CAAAC,cAAA,GAAAC,CAAA,OAAAC,OAAA;AAEA,SAAgBJ,0BAA0BA,CAACK,QAA0B;EAAA;EAAAH,cAAA,GAAAI,CAAA;EAAAJ,cAAA,GAAAC,CAAA;EACnE,OAAO,IAAIF,eAAA,CAAAM,aAAa,EAAE;AAC5B", "ignoreList": []}