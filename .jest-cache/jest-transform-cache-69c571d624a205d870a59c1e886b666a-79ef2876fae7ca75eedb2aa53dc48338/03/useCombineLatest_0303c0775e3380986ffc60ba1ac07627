2dea4f3cd75b63871bae8f9272a9bb65
"use strict";

/* istanbul ignore next */
function cov_241v89fb3q() {
  var path = "/Users/<USER>/projects/react-native/payment-module/src/presentation/payment-bill/hooks/useCombineLatest.ts";
  var hash = "5492e18cf8074328769e39ee2abbc415671b0f28";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/projects/react-native/payment-module/src/presentation/payment-bill/hooks/useCombineLatest.ts",
    statementMap: {
      "0": {
        start: {
          line: 3,
          column: 29
        },
        end: {
          line: 3,
          column: 84
        }
      },
      "1": {
        start: {
          line: 4,
          column: 26
        },
        end: {
          line: 4,
          column: 101
        }
      },
      "2": {
        start: {
          line: 5,
          column: 22
        },
        end: {
          line: 5,
          column: 93
        }
      },
      "3": {
        start: {
          line: 6,
          column: 0
        },
        end: {
          line: 8,
          column: 3
        }
      },
      "4": {
        start: {
          line: 9,
          column: 14
        },
        end: {
          line: 9,
          column: 30
        }
      },
      "5": {
        start: {
          line: 11,
          column: 13
        },
        end: {
          line: 11,
          column: 49
        }
      },
      "6": {
        start: {
          line: 12,
          column: 12
        },
        end: {
          line: 12,
          column: 49
        }
      },
      "7": {
        start: {
          line: 13,
          column: 21
        },
        end: {
          line: 13,
          column: 29
        }
      },
      "8": {
        start: {
          line: 14,
          column: 24
        },
        end: {
          line: 14,
          column: 32
        }
      },
      "9": {
        start: {
          line: 15,
          column: 19
        },
        end: {
          line: 15,
          column: 79
        }
      },
      "10": {
        start: {
          line: 16,
          column: 14
        },
        end: {
          line: 16,
          column: 42
        }
      },
      "11": {
        start: {
          line: 17,
          column: 12
        },
        end: {
          line: 17,
          column: 50
        }
      },
      "12": {
        start: {
          line: 18,
          column: 17
        },
        end: {
          line: 18,
          column: 25
        }
      },
      "13": {
        start: {
          line: 19,
          column: 20
        },
        end: {
          line: 19,
          column: 28
        }
      },
      "14": {
        start: {
          line: 20,
          column: 17
        },
        end: {
          line: 33,
          column: 4
        }
      },
      "15": {
        start: {
          line: 21,
          column: 4
        },
        end: {
          line: 32,
          column: 28
        }
      },
      "16": {
        start: {
          line: 22,
          column: 6
        },
        end: {
          line: 26,
          column: 9
        }
      },
      "17": {
        start: {
          line: 23,
          column: 24
        },
        end: {
          line: 23,
          column: 62
        }
      },
      "18": {
        start: {
          line: 24,
          column: 8
        },
        end: {
          line: 24,
          column: 36
        }
      },
      "19": {
        start: {
          line: 25,
          column: 8
        },
        end: {
          line: 25,
          column: 25
        }
      },
      "20": {
        start: {
          line: 27,
          column: 6
        },
        end: {
          line: 27,
          column: 39
        }
      },
      "21": {
        start: {
          line: 28,
          column: 23
        },
        end: {
          line: 28,
          column: 56
        }
      },
      "22": {
        start: {
          line: 29,
          column: 6
        },
        end: {
          line: 31,
          column: 7
        }
      },
      "23": {
        start: {
          line: 30,
          column: 8
        },
        end: {
          line: 30,
          column: 28
        }
      },
      "24": {
        start: {
          line: 34,
          column: 2
        },
        end: {
          line: 38,
          column: 4
        }
      },
      "25": {
        start: {
          line: 40,
          column: 0
        },
        end: {
          line: 40,
          column: 35
        }
      }
    },
    fnMap: {
      "0": {
        name: "useCombineLatest",
        decl: {
          start: {
            line: 10,
            column: 9
          },
          end: {
            line: 10,
            column: 25
          }
        },
        loc: {
          start: {
            line: 10,
            column: 41
          },
          end: {
            line: 39,
            column: 1
          }
        },
        line: 10
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 20,
            column: 35
          },
          end: {
            line: 20,
            column: 36
          }
        },
        loc: {
          start: {
            line: 20,
            column: 55
          },
          end: {
            line: 33,
            column: 3
          }
        },
        line: 20
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 21,
            column: 36
          },
          end: {
            line: 21,
            column: 37
          }
        },
        loc: {
          start: {
            line: 21,
            column: 56
          },
          end: {
            line: 32,
            column: 5
          }
        },
        line: 21
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 22,
            column: 24
          },
          end: {
            line: 22,
            column: 25
          }
        },
        loc: {
          start: {
            line: 22,
            column: 40
          },
          end: {
            line: 26,
            column: 7
          }
        },
        line: 22
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 29,
            column: 6
          },
          end: {
            line: 31,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 29,
            column: 6
          },
          end: {
            line: 31,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 29
      },
      "1": {
        loc: {
          start: {
            line: 29,
            column: 10
          },
          end: {
            line: 29,
            column: 35
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 29,
            column: 10
          },
          end: {
            line: 29,
            column: 20
          }
        }, {
          start: {
            line: 29,
            column: 24
          },
          end: {
            line: 29,
            column: 35
          }
        }],
        line: 29
      },
      "2": {
        loc: {
          start: {
            line: 35,
            column: 12
          },
          end: {
            line: 35,
            column: 46
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 35,
            column: 25
          },
          end: {
            line: 35,
            column: 39
          }
        }, {
          start: {
            line: 35,
            column: 42
          },
          end: {
            line: 35,
            column: 46
          }
        }],
        line: 35
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0
    },
    b: {
      "0": [0, 0],
      "1": [0, 0],
      "2": [0, 0]
    },
    inputSourceMap: {
      version: 3,
      file: undefined,
      names: ["react_1", "require", "useCombineLatest", "initialValues", "_ref", "useState", "_ref2", "_slicedToArray2", "default", "combinedValues", "setCombinedValues", "emittedRef", "useRef", "Array", "length", "fill", "_ref3", "_ref4", "isComplete", "setIsComplete", "updaters", "map", "_", "index", "useCallback", "newValue", "prev", "newValues", "_toConsumableArray2", "current", "allEmitted", "every", "Boolean", "values", "exports"],
      sourceRoot: undefined,
      sources: ["/Users/<USER>/projects/react-native/payment-module/src/presentation/payment-bill/hooks/useCombineLatest.ts"],
      sourcesContent: ["/* eslint-disable react-hooks/rules-of-hooks */\nimport {useState, useRef, useCallback} from 'react';\n\ntype Updater<T> = (value: T) => void;\n\nfunction useCombineLatest<T>(initialValues: T[]): {\n  values: T[] | null;\n  updaters: Updater<T>[];\n  isComplete: boolean;\n} {\n  const [combinedValues, setCombinedValues] = useState<T[]>(initialValues);\n  const emittedRef = useRef<boolean[]>(Array(initialValues.length).fill(false));\n  const [isComplete, setIsComplete] = useState(false);\n\n  const updaters: Updater<T>[] = initialValues.map((_, index) =>\n    useCallback(\n      (newValue: T) => {\n        setCombinedValues(prev => {\n          const newValues = [...prev];\n          newValues[index] = newValue;\n          return newValues;\n        });\n\n        emittedRef.current[index] = true;\n\n        const allEmitted = emittedRef.current.every(Boolean);\n        if (allEmitted && !isComplete) {\n          setIsComplete(true);\n        }\n      },\n      // eslint-disable-next-line react-hooks/exhaustive-deps\n      [index, isComplete],\n    ),\n  );\n\n  return {\n    values: isComplete ? combinedValues : null,\n    updaters,\n    isComplete,\n  };\n}\n\nexport default useCombineLatest;\n"],
      mappings: ";;;;;;;;AACA,IAAAA,OAAA,GAAAC,OAAA;AAIA,SAASC,gBAAgBA,CAAIC,aAAkB;EAK7C,IAAAC,IAAA,GAA4C,IAAAJ,OAAA,CAAAK,QAAQ,EAAMF,aAAa,CAAC;IAAAG,KAAA,OAAAC,eAAA,CAAAC,OAAA,EAAAJ,IAAA;IAAjEK,cAAc,GAAAH,KAAA;IAAEI,iBAAiB,GAAAJ,KAAA;EACxC,IAAMK,UAAU,GAAG,IAAAX,OAAA,CAAAY,MAAM,EAAYC,KAAK,CAACV,aAAa,CAACW,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC,CAAC;EAC7E,IAAAC,KAAA,GAAoC,IAAAhB,OAAA,CAAAK,QAAQ,EAAC,KAAK,CAAC;IAAAY,KAAA,OAAAV,eAAA,CAAAC,OAAA,EAAAQ,KAAA;IAA5CE,UAAU,GAAAD,KAAA;IAAEE,aAAa,GAAAF,KAAA;EAEhC,IAAMG,QAAQ,GAAiBjB,aAAa,CAACkB,GAAG,CAAC,UAACC,CAAC,EAAEC,KAAK;IAAA,OACxD,IAAAvB,OAAA,CAAAwB,WAAW,EACT,UAACC,QAAW,EAAI;MACdf,iBAAiB,CAAC,UAAAgB,IAAI,EAAG;QACvB,IAAMC,SAAS,OAAAC,mBAAA,CAAApB,OAAA,EAAOkB,IAAI,CAAC;QAC3BC,SAAS,CAACJ,KAAK,CAAC,GAAGE,QAAQ;QAC3B,OAAOE,SAAS;MAClB,CAAC,CAAC;MAEFhB,UAAU,CAACkB,OAAO,CAACN,KAAK,CAAC,GAAG,IAAI;MAEhC,IAAMO,UAAU,GAAGnB,UAAU,CAACkB,OAAO,CAACE,KAAK,CAACC,OAAO,CAAC;MACpD,IAAIF,UAAU,IAAI,CAACZ,UAAU,EAAE;QAC7BC,aAAa,CAAC,IAAI,CAAC;MACrB;IACF,CAAC,EAED,CAACI,KAAK,EAAEL,UAAU,CAAC,CACpB;EAAA,EACF;EAED,OAAO;IACLe,MAAM,EAAEf,UAAU,GAAGT,cAAc,GAAG,IAAI;IAC1CW,QAAQ,EAARA,QAAQ;IACRF,UAAU,EAAVA;GACD;AACH;AAEAgB,OAAA,CAAA1B,OAAA,GAAeN,gBAAgB",
      ignoreList: []
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "5492e18cf8074328769e39ee2abbc415671b0f28"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_241v89fb3q = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_241v89fb3q();
var _interopRequireDefault =
/* istanbul ignore next */
(cov_241v89fb3q().s[0]++, require("@babel/runtime/helpers/interopRequireDefault"));
var _toConsumableArray2 =
/* istanbul ignore next */
(cov_241v89fb3q().s[1]++, _interopRequireDefault(require("@babel/runtime/helpers/toConsumableArray")));
var _slicedToArray2 =
/* istanbul ignore next */
(cov_241v89fb3q().s[2]++, _interopRequireDefault(require("@babel/runtime/helpers/slicedToArray")));
/* istanbul ignore next */
cov_241v89fb3q().s[3]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
var react_1 =
/* istanbul ignore next */
(cov_241v89fb3q().s[4]++, require("react"));
function useCombineLatest(initialValues) {
  /* istanbul ignore next */
  cov_241v89fb3q().f[0]++;
  var _ref =
    /* istanbul ignore next */
    (cov_241v89fb3q().s[5]++, (0, react_1.useState)(initialValues)),
    _ref2 =
    /* istanbul ignore next */
    (cov_241v89fb3q().s[6]++, (0, _slicedToArray2.default)(_ref, 2)),
    combinedValues =
    /* istanbul ignore next */
    (cov_241v89fb3q().s[7]++, _ref2[0]),
    setCombinedValues =
    /* istanbul ignore next */
    (cov_241v89fb3q().s[8]++, _ref2[1]);
  var emittedRef =
  /* istanbul ignore next */
  (cov_241v89fb3q().s[9]++, (0, react_1.useRef)(Array(initialValues.length).fill(false)));
  var _ref3 =
    /* istanbul ignore next */
    (cov_241v89fb3q().s[10]++, (0, react_1.useState)(false)),
    _ref4 =
    /* istanbul ignore next */
    (cov_241v89fb3q().s[11]++, (0, _slicedToArray2.default)(_ref3, 2)),
    isComplete =
    /* istanbul ignore next */
    (cov_241v89fb3q().s[12]++, _ref4[0]),
    setIsComplete =
    /* istanbul ignore next */
    (cov_241v89fb3q().s[13]++, _ref4[1]);
  var updaters =
  /* istanbul ignore next */
  (cov_241v89fb3q().s[14]++, initialValues.map(function (_, index) {
    /* istanbul ignore next */
    cov_241v89fb3q().f[1]++;
    cov_241v89fb3q().s[15]++;
    return (0, react_1.useCallback)(function (newValue) {
      /* istanbul ignore next */
      cov_241v89fb3q().f[2]++;
      cov_241v89fb3q().s[16]++;
      setCombinedValues(function (prev) {
        /* istanbul ignore next */
        cov_241v89fb3q().f[3]++;
        var newValues =
        /* istanbul ignore next */
        (cov_241v89fb3q().s[17]++, (0, _toConsumableArray2.default)(prev));
        /* istanbul ignore next */
        cov_241v89fb3q().s[18]++;
        newValues[index] = newValue;
        /* istanbul ignore next */
        cov_241v89fb3q().s[19]++;
        return newValues;
      });
      /* istanbul ignore next */
      cov_241v89fb3q().s[20]++;
      emittedRef.current[index] = true;
      var allEmitted =
      /* istanbul ignore next */
      (cov_241v89fb3q().s[21]++, emittedRef.current.every(Boolean));
      /* istanbul ignore next */
      cov_241v89fb3q().s[22]++;
      if (
      /* istanbul ignore next */
      (cov_241v89fb3q().b[1][0]++, allEmitted) &&
      /* istanbul ignore next */
      (cov_241v89fb3q().b[1][1]++, !isComplete)) {
        /* istanbul ignore next */
        cov_241v89fb3q().b[0][0]++;
        cov_241v89fb3q().s[23]++;
        setIsComplete(true);
      } else
      /* istanbul ignore next */
      {
        cov_241v89fb3q().b[0][1]++;
      }
    }, [index, isComplete]);
  }));
  /* istanbul ignore next */
  cov_241v89fb3q().s[24]++;
  return {
    values: isComplete ?
    /* istanbul ignore next */
    (cov_241v89fb3q().b[2][0]++, combinedValues) :
    /* istanbul ignore next */
    (cov_241v89fb3q().b[2][1]++, null),
    updaters: updaters,
    isComplete: isComplete
  };
}
/* istanbul ignore next */
cov_241v89fb3q().s[25]++;
exports.default = useCombineLatest;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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