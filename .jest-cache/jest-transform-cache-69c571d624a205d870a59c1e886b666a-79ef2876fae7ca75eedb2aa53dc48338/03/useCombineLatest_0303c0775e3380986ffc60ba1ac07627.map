{"version": 3, "names": ["cov_241v89fb3q", "actualCoverage", "react_1", "s", "require", "useCombineLatest", "initialValues", "f", "_ref", "useState", "_ref2", "_slicedToArray2", "default", "combinedValues", "set<PERSON><PERSON>ined<PERSON><PERSON>ues", "emittedRef", "useRef", "Array", "length", "fill", "_ref3", "_ref4", "isComplete", "setIsComplete", "updaters", "map", "_", "index", "useCallback", "newValue", "prev", "newValues", "_toConsumableArray2", "current", "allEmitted", "every", "Boolean", "b", "values", "exports"], "sources": ["/Users/<USER>/projects/react-native/payment-module/src/presentation/payment-bill/hooks/useCombineLatest.ts"], "sourcesContent": ["/* eslint-disable react-hooks/rules-of-hooks */\nimport {useState, useRef, useCallback} from 'react';\n\ntype Updater<T> = (value: T) => void;\n\nfunction useCombineLatest<T>(initialValues: T[]): {\n  values: T[] | null;\n  updaters: Updater<T>[];\n  isComplete: boolean;\n} {\n  const [combinedValues, setCombinedValues] = useState<T[]>(initialValues);\n  const emittedRef = useRef<boolean[]>(Array(initialValues.length).fill(false));\n  const [isComplete, setIsComplete] = useState(false);\n\n  const updaters: Updater<T>[] = initialValues.map((_, index) =>\n    useCallback(\n      (newValue: T) => {\n        setCombinedValues(prev => {\n          const newValues = [...prev];\n          newValues[index] = newValue;\n          return newValues;\n        });\n\n        emittedRef.current[index] = true;\n\n        const allEmitted = emittedRef.current.every(Boolean);\n        if (allEmitted && !isComplete) {\n          setIsComplete(true);\n        }\n      },\n      // eslint-disable-next-line react-hooks/exhaustive-deps\n      [index, isComplete],\n    ),\n  );\n\n  return {\n    values: isComplete ? combinedValues : null,\n    updaters,\n    isComplete,\n  };\n}\n\nexport default useCombineLatest;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAYE;IAAAA,cAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,cAAA;;;;;;;;;;;;;;;AAXF,IAAAE,OAAA;AAAA;AAAA,CAAAF,cAAA,GAAAG,CAAA,OAAAC,OAAA;AAIA,SAASC,gBAAgBA,CAAIC,aAAkB;EAAA;EAAAN,cAAA,GAAAO,CAAA;EAK7C,IAAAC,IAAA;IAAA;IAAA,CAAAR,cAAA,GAAAG,CAAA,OAA4C,IAAAD,OAAA,CAAAO,QAAQ,EAAMH,aAAa,CAAC;IAAAI,KAAA;IAAA;IAAA,CAAAV,cAAA,GAAAG,CAAA,WAAAQ,eAAA,CAAAC,OAAA,EAAAJ,IAAA;IAAjEK,cAAc;IAAA;IAAA,CAAAb,cAAA,GAAAG,CAAA,OAAAO,KAAA;IAAEI,iBAAiB;IAAA;IAAA,CAAAd,cAAA,GAAAG,CAAA,OAAAO,KAAA;EACxC,IAAMK,UAAU;EAAA;EAAA,CAAAf,cAAA,GAAAG,CAAA,OAAG,IAAAD,OAAA,CAAAc,MAAM,EAAYC,KAAK,CAACX,aAAa,CAACY,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC,CAAC;EAC7E,IAAAC,KAAA;IAAA;IAAA,CAAApB,cAAA,GAAAG,CAAA,QAAoC,IAAAD,OAAA,CAAAO,QAAQ,EAAC,KAAK,CAAC;IAAAY,KAAA;IAAA;IAAA,CAAArB,cAAA,GAAAG,CAAA,YAAAQ,eAAA,CAAAC,OAAA,EAAAQ,KAAA;IAA5CE,UAAU;IAAA;IAAA,CAAAtB,cAAA,GAAAG,CAAA,QAAAkB,KAAA;IAAEE,aAAa;IAAA;IAAA,CAAAvB,cAAA,GAAAG,CAAA,QAAAkB,KAAA;EAEhC,IAAMG,QAAQ;EAAA;EAAA,CAAAxB,cAAA,GAAAG,CAAA,QAAiBG,aAAa,CAACmB,GAAG,CAAC,UAACC,CAAC,EAAEC,KAAK;IAAA;IAAA3B,cAAA,GAAAO,CAAA;IAAAP,cAAA,GAAAG,CAAA;IAAA,OACxD,IAAAD,OAAA,CAAA0B,WAAW,EACT,UAACC,QAAW,EAAI;MAAA;MAAA7B,cAAA,GAAAO,CAAA;MAAAP,cAAA,GAAAG,CAAA;MACdW,iBAAiB,CAAC,UAAAgB,IAAI,EAAG;QAAA;QAAA9B,cAAA,GAAAO,CAAA;QACvB,IAAMwB,SAAS;QAAA;QAAA,CAAA/B,cAAA,GAAAG,CAAA,YAAA6B,mBAAA,CAAApB,OAAA,EAAOkB,IAAI,CAAC;QAAA;QAAA9B,cAAA,GAAAG,CAAA;QAC3B4B,SAAS,CAACJ,KAAK,CAAC,GAAGE,QAAQ;QAAA;QAAA7B,cAAA,GAAAG,CAAA;QAC3B,OAAO4B,SAAS;MAClB,CAAC,CAAC;MAAA;MAAA/B,cAAA,GAAAG,CAAA;MAEFY,UAAU,CAACkB,OAAO,CAACN,KAAK,CAAC,GAAG,IAAI;MAEhC,IAAMO,UAAU;MAAA;MAAA,CAAAlC,cAAA,GAAAG,CAAA,QAAGY,UAAU,CAACkB,OAAO,CAACE,KAAK,CAACC,OAAO,CAAC;MAAA;MAAApC,cAAA,GAAAG,CAAA;MACpD;MAAI;MAAA,CAAAH,cAAA,GAAAqC,CAAA,UAAAH,UAAU;MAAA;MAAA,CAAAlC,cAAA,GAAAqC,CAAA,UAAI,CAACf,UAAU,GAAE;QAAA;QAAAtB,cAAA,GAAAqC,CAAA;QAAArC,cAAA,GAAAG,CAAA;QAC7BoB,aAAa,CAAC,IAAI,CAAC;MACrB;MAAA;MAAA;QAAAvB,cAAA,GAAAqC,CAAA;MAAA;IACF,CAAC,EAED,CAACV,KAAK,EAAEL,UAAU,CAAC,CACpB;EAAA,EACF;EAAA;EAAAtB,cAAA,GAAAG,CAAA;EAED,OAAO;IACLmC,MAAM,EAAEhB,UAAU;IAAA;IAAA,CAAAtB,cAAA,GAAAqC,CAAA,UAAGxB,cAAc;IAAA;IAAA,CAAAb,cAAA,GAAAqC,CAAA,UAAG,IAAI;IAC1Cb,QAAQ,EAARA,QAAQ;IACRF,UAAU,EAAVA;GACD;AACH;AAAA;AAAAtB,cAAA,GAAAG,CAAA;AAEAoC,OAAA,CAAA3B,OAAA,GAAeP,gBAAgB", "ignoreList": []}