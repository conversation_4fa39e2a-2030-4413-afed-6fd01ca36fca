81abd9440a29227a99f33d075a90e9ea
"use strict";

/* istanbul ignore next */
function cov_26sxxjqfxm() {
  var path = "/Users/<USER>/projects/react-native/payment-module/src/domain/states/get-my-bill-contact-recent-list/GetMyBillContactRecentListState.ts";
  var hash = "e343f05b6599459042f1911972494b771ec599f4";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/projects/react-native/payment-module/src/domain/states/get-my-bill-contact-recent-list/GetMyBillContactRecentListState.ts",
    statementMap: {
      "0": {
        start: {
          line: 3,
          column: 0
        },
        end: {
          line: 5,
          column: 3
        }
      }
    },
    fnMap: {},
    branchMap: {},
    s: {
      "0": 0
    },
    f: {},
    b: {},
    inputSourceMap: {
      version: 3,
      file: undefined,
      names: [],
      sourceRoot: undefined,
      sources: ["/Users/<USER>/projects/react-native/payment-module/src/domain/states/get-my-bill-contact-recent-list/GetMyBillContactRecentListState.ts"],
      sourcesContent: ["import {MSBError} from '../../../core/BaseResponse';\nimport {GetMyBillContactRecentListModel} from '../../entities/get-my-bill-contact-recent-list/GetMyBillContactRecentListModel';\n\nexport type GetMyBillContactRecentListState =\n  | {status: 'INIT'}\n  | {status: 'LOADING'}\n  | {status: 'SUCCESS'; data: GetMyBillContactRecentListModel[] | undefined | null}\n  | {status: 'ERROR'; error?: MSBError | undefined | null};\n"],
      mappings: "",
      ignoreList: []
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "e343f05b6599459042f1911972494b771ec599f4"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_26sxxjqfxm = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_26sxxjqfxm();
cov_26sxxjqfxm().s[0]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6W10sInNvdXJjZXMiOlsiL1VzZXJzL3NvbnNhbXNldC9wcm9qZWN0cy9yZWFjdC1uYXRpdmUvcGF5bWVudC1tb2R1bGUvc3JjL2RvbWFpbi9zdGF0ZXMvZ2V0LW15LWJpbGwtY29udGFjdC1yZWNlbnQtbGlzdC9HZXRNeUJpbGxDb250YWN0UmVjZW50TGlzdFN0YXRlLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7TVNCRXJyb3J9IGZyb20gJy4uLy4uLy4uL2NvcmUvQmFzZVJlc3BvbnNlJztcbmltcG9ydCB7R2V0TXlCaWxsQ29udGFjdFJlY2VudExpc3RNb2RlbH0gZnJvbSAnLi4vLi4vZW50aXRpZXMvZ2V0LW15LWJpbGwtY29udGFjdC1yZWNlbnQtbGlzdC9HZXRNeUJpbGxDb250YWN0UmVjZW50TGlzdE1vZGVsJztcblxuZXhwb3J0IHR5cGUgR2V0TXlCaWxsQ29udGFjdFJlY2VudExpc3RTdGF0ZSA9XG4gIHwge3N0YXR1czogJ0lOSVQnfVxuICB8IHtzdGF0dXM6ICdMT0FESU5HJ31cbiAgfCB7c3RhdHVzOiAnU1VDQ0VTUyc7IGRhdGE6IEdldE15QmlsbENvbnRhY3RSZWNlbnRMaXN0TW9kZWxbXSB8IHVuZGVmaW5lZCB8IG51bGx9XG4gIHwge3N0YXR1czogJ0VSUk9SJzsgZXJyb3I/OiBNU0JFcnJvciB8IHVuZGVmaW5lZCB8IG51bGx9O1xuIl0sIm1hcHBpbmdzIjoiIiwiaWdub3JlTGlzdCI6W119