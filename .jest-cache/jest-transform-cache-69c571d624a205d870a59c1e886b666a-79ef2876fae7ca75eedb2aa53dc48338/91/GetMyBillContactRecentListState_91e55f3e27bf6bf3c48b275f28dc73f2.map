{"version": 3, "names": [], "sources": ["/Users/<USER>/projects/react-native/payment-module/src/domain/states/get-my-bill-contact-recent-list/GetMyBillContactRecentListState.ts"], "sourcesContent": ["import {MSBError} from '../../../core/BaseResponse';\nimport {GetMyBillContactRecentListModel} from '../../entities/get-my-bill-contact-recent-list/GetMyBillContactRecentListModel';\n\nexport type GetMyBillContactRecentListState =\n  | {status: 'INIT'}\n  | {status: 'LOADING'}\n  | {status: 'SUCCESS'; data: GetMyBillContactRecentListModel[] | undefined | null}\n  | {status: 'ERROR'; error?: MSBError | undefined | null};\n"], "mappings": "", "ignoreList": []}