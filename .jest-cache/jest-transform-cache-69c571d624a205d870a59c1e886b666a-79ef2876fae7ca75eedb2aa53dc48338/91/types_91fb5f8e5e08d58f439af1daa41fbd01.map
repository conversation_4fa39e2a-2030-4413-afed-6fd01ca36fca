{"version": 3, "names": [], "sources": ["/Users/<USER>/projects/react-native/payment-module/src/presentation/payment-result/components/transfer-result-action/types.ts"], "sourcesContent": ["import {ViewStyle} from 'react-native';\n\nexport type TransferResultActionProps = {\n  style?: ViewStyle;\n  actions: ActionProps[];\n  onPress: (data: ActionProps) => void;\n};\n\nexport type ActionProps = {\n  type: string;\n  title: string;\n  icon: string;\n};\n"], "mappings": "", "ignoreList": []}