a470f36553998d8e73dfc31714a2470b
"use strict";

/* istanbul ignore next */
function cov_16bhol7nv() {
  var path = "/Users/<USER>/projects/react-native/payment-module/src/presentation/payment-result/components/transfer-result-action/types.ts";
  var hash = "90be1cc476f1e10fa7141626f0b5cecc854ef62e";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/projects/react-native/payment-module/src/presentation/payment-result/components/transfer-result-action/types.ts",
    statementMap: {
      "0": {
        start: {
          line: 3,
          column: 0
        },
        end: {
          line: 5,
          column: 3
        }
      }
    },
    fnMap: {},
    branchMap: {},
    s: {
      "0": 0
    },
    f: {},
    b: {},
    inputSourceMap: {
      version: 3,
      file: undefined,
      names: [],
      sourceRoot: undefined,
      sources: ["/Users/<USER>/projects/react-native/payment-module/src/presentation/payment-result/components/transfer-result-action/types.ts"],
      sourcesContent: ["import {ViewStyle} from 'react-native';\n\nexport type TransferResultActionProps = {\n  style?: ViewStyle;\n  actions: ActionProps[];\n  onPress: (data: ActionProps) => void;\n};\n\nexport type ActionProps = {\n  type: string;\n  title: string;\n  icon: string;\n};\n"],
      mappings: "",
      ignoreList: []
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "90be1cc476f1e10fa7141626f0b5cecc854ef62e"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_16bhol7nv = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_16bhol7nv();
cov_16bhol7nv().s[0]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6W10sInNvdXJjZXMiOlsiL1VzZXJzL3NvbnNhbXNldC9wcm9qZWN0cy9yZWFjdC1uYXRpdmUvcGF5bWVudC1tb2R1bGUvc3JjL3ByZXNlbnRhdGlvbi9wYXltZW50LXJlc3VsdC9jb21wb25lbnRzL3RyYW5zZmVyLXJlc3VsdC1hY3Rpb24vdHlwZXMudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHtWaWV3U3R5bGV9IGZyb20gJ3JlYWN0LW5hdGl2ZSc7XG5cbmV4cG9ydCB0eXBlIFRyYW5zZmVyUmVzdWx0QWN0aW9uUHJvcHMgPSB7XG4gIHN0eWxlPzogVmlld1N0eWxlO1xuICBhY3Rpb25zOiBBY3Rpb25Qcm9wc1tdO1xuICBvblByZXNzOiAoZGF0YTogQWN0aW9uUHJvcHMpID0+IHZvaWQ7XG59O1xuXG5leHBvcnQgdHlwZSBBY3Rpb25Qcm9wcyA9IHtcbiAgdHlwZTogc3RyaW5nO1xuICB0aXRsZTogc3RyaW5nO1xuICBpY29uOiBzdHJpbmc7XG59O1xuIl0sIm1hcHBpbmdzIjoiIiwiaWdub3JlTGlzdCI6W119