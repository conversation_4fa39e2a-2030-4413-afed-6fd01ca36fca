{"version": 3, "names": ["cov_wj2qsdq0q", "actualCoverage", "ExcecutionHandler_1", "s", "require", "ValidateUseCase", "f", "repository", "_classCallCheck2", "default", "_createClass2", "key", "value", "_execute", "_asyncToGenerator2", "request", "_this", "ExecutionHandler", "execute", "validate", "_x", "apply", "arguments", "exports"], "sources": ["/Users/<USER>/projects/react-native/payment-module/src/domain/usecases/payment/ValidateUseCase.ts"], "sourcesContent": ["import {IPaymentRepository} from '../../repositories/IPaymentRepository';\nimport {ValidateModel} from '../../entities/validate/ValidateModel';\nimport {ResultState} from '../../../core/ResultState';\nimport {ExecutionHandler} from '../../../utils/ExcecutionHandler';\nimport {ValidateRequest} from '../../../data/models/validate/ValidateRequest';\nexport class ValidateUseCase {\n  private repository: IPaymentRepository;\n\n  constructor(repository: IPaymentRepository) {\n    this.repository = repository;\n  }\n\n  public async execute(request: ValidateRequest): Promise<ResultState<ValidateModel>> {\n    // call this.repository.validate(...)\n    return ExecutionHandler.execute(() => this.repository.validate(request));\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAUE;IAAAA,aAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,aAAA;;;;;;;;;;;;;;;;;;;;;AAPF,IAAAE,mBAAA;AAAA;AAAA,CAAAF,aAAA,GAAAG,CAAA,OAAAC,OAAA;AAAkE,IAErDC,eAAe;AAAA;AAAA,CAAAL,aAAA,GAAAG,CAAA;EAAA;EAAAH,aAAA,GAAAM,CAAA;EAG1B,SAAAD,gBAAYE,UAA8B;IAAA;IAAAP,aAAA,GAAAM,CAAA;IAAAN,aAAA,GAAAG,CAAA;IAAA,IAAAK,gBAAA,CAAAC,OAAA,QAAAJ,eAAA;IAAA;IAAAL,aAAA,GAAAG,CAAA;IACxC,IAAI,CAACI,UAAU,GAAGA,UAAU;EAC9B;EAAA;EAAAP,aAAA,GAAAG,CAAA;EAAC,WAAAO,aAAA,CAAAD,OAAA,EAAAJ,eAAA;IAAAM,GAAA;IAAAC,KAAA;MAAA;MAAAZ,aAAA,GAAAM,CAAA;MAAA,IAAAO,QAAA;MAAA;MAAA,CAAAb,aAAA,GAAAG,CAAA,YAAAW,kBAAA,CAAAL,OAAA,EAEM,WAAcM,OAAwB;QAAA;QAAAf,aAAA,GAAAM,CAAA;QAAA,IAAAU,KAAA;QAAA;QAAA,CAAAhB,aAAA,GAAAG,CAAA;QAAA;QAAAH,aAAA,GAAAG,CAAA;QAE3C,OAAOD,mBAAA,CAAAe,gBAAgB,CAACC,OAAO,CAAC;UAAA;UAAAlB,aAAA,GAAAM,CAAA;UAAAN,aAAA,GAAAG,CAAA;UAAA,OAAMa,KAAI,CAACT,UAAU,CAACY,QAAQ,CAACJ,OAAO,CAAC;QAAA,EAAC;MAC1E,CAAC;MAAA,SAHYG,OAAOA,CAAAE,EAAA;QAAA;QAAApB,aAAA,GAAAM,CAAA;QAAAN,aAAA,GAAAG,CAAA;QAAA,OAAAU,QAAA,CAAAQ,KAAA,OAAAC,SAAA;MAAA;MAAA;MAAAtB,aAAA,GAAAG,CAAA;MAAA,OAAPe,OAAO;IAAA;EAAA;AAAA;AAAA;AAAAlB,aAAA,GAAAG,CAAA;AAPtBoB,OAAA,CAAAlB,eAAA,GAAAA,eAAA", "ignoreList": []}