{"version": 3, "names": [], "sources": ["/Users/<USER>/projects/react-native/payment-module/src/data/models/my-bill-contact-list/MyBillContactListResponse.ts"], "sourcesContent": ["export type MyBillContactListResponse = MyBillContactResponse[];\n\nexport interface MyBillContactResponse {\n  id?: string | null;\n  name?: string | null;\n  alias?: string | null;\n  category?: string | null;\n  activeStatus?: string | null;\n  accessContextScope?: string | null;\n  accounts: AccountResponse[];\n  additions: Additions;\n}\n\nexport interface Additions {\n  favoriteStatus: string;\n  reminderStatus: string;\n  payableAmount: string;\n}\nexport interface AccountResponse {\n  accountNumber: string;\n  bankCode: string;\n  accountType: string;\n  bankName?: string;\n  bankPostCode?: string;\n  externalId?: string;\n}\n"], "mappings": "", "ignoreList": []}