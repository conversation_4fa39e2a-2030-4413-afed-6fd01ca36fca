7bff8bd180d706597acf827d563b469c
"use strict";

/* istanbul ignore next */
function cov_njm58l26c() {
  var path = "/Users/<USER>/projects/react-native/payment-module/src/data/models/my-bill-contact-list/MyBillContactListResponse.ts";
  var hash = "d69ca17d237cd49083dca381f651978c4b0b3d8e";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/projects/react-native/payment-module/src/data/models/my-bill-contact-list/MyBillContactListResponse.ts",
    statementMap: {
      "0": {
        start: {
          line: 3,
          column: 0
        },
        end: {
          line: 5,
          column: 3
        }
      }
    },
    fnMap: {},
    branchMap: {},
    s: {
      "0": 0
    },
    f: {},
    b: {},
    inputSourceMap: {
      version: 3,
      file: undefined,
      names: [],
      sourceRoot: undefined,
      sources: ["/Users/<USER>/projects/react-native/payment-module/src/data/models/my-bill-contact-list/MyBillContactListResponse.ts"],
      sourcesContent: ["export type MyBillContactListResponse = MyBillContactResponse[];\n\nexport interface MyBillContactResponse {\n  id?: string | null;\n  name?: string | null;\n  alias?: string | null;\n  category?: string | null;\n  activeStatus?: string | null;\n  accessContextScope?: string | null;\n  accounts: AccountResponse[];\n  additions: Additions;\n}\n\nexport interface Additions {\n  favoriteStatus: string;\n  reminderStatus: string;\n  payableAmount: string;\n}\nexport interface AccountResponse {\n  accountNumber: string;\n  bankCode: string;\n  accountType: string;\n  bankName?: string;\n  bankPostCode?: string;\n  externalId?: string;\n}\n"],
      mappings: "",
      ignoreList: []
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "d69ca17d237cd49083dca381f651978c4b0b3d8e"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_njm58l26c = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_njm58l26c();
cov_njm58l26c().s[0]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6W10sInNvdXJjZXMiOlsiL1VzZXJzL3NvbnNhbXNldC9wcm9qZWN0cy9yZWFjdC1uYXRpdmUvcGF5bWVudC1tb2R1bGUvc3JjL2RhdGEvbW9kZWxzL215LWJpbGwtY29udGFjdC1saXN0L015QmlsbENvbnRhY3RMaXN0UmVzcG9uc2UudHMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IHR5cGUgTXlCaWxsQ29udGFjdExpc3RSZXNwb25zZSA9IE15QmlsbENvbnRhY3RSZXNwb25zZVtdO1xuXG5leHBvcnQgaW50ZXJmYWNlIE15QmlsbENvbnRhY3RSZXNwb25zZSB7XG4gIGlkPzogc3RyaW5nIHwgbnVsbDtcbiAgbmFtZT86IHN0cmluZyB8IG51bGw7XG4gIGFsaWFzPzogc3RyaW5nIHwgbnVsbDtcbiAgY2F0ZWdvcnk/OiBzdHJpbmcgfCBudWxsO1xuICBhY3RpdmVTdGF0dXM/OiBzdHJpbmcgfCBudWxsO1xuICBhY2Nlc3NDb250ZXh0U2NvcGU/OiBzdHJpbmcgfCBudWxsO1xuICBhY2NvdW50czogQWNjb3VudFJlc3BvbnNlW107XG4gIGFkZGl0aW9uczogQWRkaXRpb25zO1xufVxuXG5leHBvcnQgaW50ZXJmYWNlIEFkZGl0aW9ucyB7XG4gIGZhdm9yaXRlU3RhdHVzOiBzdHJpbmc7XG4gIHJlbWluZGVyU3RhdHVzOiBzdHJpbmc7XG4gIHBheWFibGVBbW91bnQ6IHN0cmluZztcbn1cbmV4cG9ydCBpbnRlcmZhY2UgQWNjb3VudFJlc3BvbnNlIHtcbiAgYWNjb3VudE51bWJlcjogc3RyaW5nO1xuICBiYW5rQ29kZTogc3RyaW5nO1xuICBhY2NvdW50VHlwZTogc3RyaW5nO1xuICBiYW5rTmFtZT86IHN0cmluZztcbiAgYmFua1Bvc3RDb2RlPzogc3RyaW5nO1xuICBleHRlcm5hbElkPzogc3RyaW5nO1xufVxuIl0sIm1hcHBpbmdzIjoiIiwiaWdub3JlTGlzdCI6W119