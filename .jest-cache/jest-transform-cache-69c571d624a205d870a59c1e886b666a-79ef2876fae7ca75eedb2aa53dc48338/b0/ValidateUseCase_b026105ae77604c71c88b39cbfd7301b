f78be23da123183d91cc0b7ca16317f5
"use strict";

/* istanbul ignore next */
function cov_wj2qsdq0q() {
  var path = "/Users/<USER>/projects/react-native/payment-module/src/domain/usecases/payment/ValidateUseCase.ts";
  var hash = "5a79af823230b0817115e123cbdb8a03308f1cc1";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/projects/react-native/payment-module/src/domain/usecases/payment/ValidateUseCase.ts",
    statementMap: {
      "0": {
        start: {
          line: 3,
          column: 29
        },
        end: {
          line: 3,
          column: 84
        }
      },
      "1": {
        start: {
          line: 4,
          column: 25
        },
        end: {
          line: 4,
          column: 99
        }
      },
      "2": {
        start: {
          line: 5,
          column: 23
        },
        end: {
          line: 5,
          column: 95
        }
      },
      "3": {
        start: {
          line: 6,
          column: 20
        },
        end: {
          line: 6,
          column: 89
        }
      },
      "4": {
        start: {
          line: 7,
          column: 0
        },
        end: {
          line: 9,
          column: 3
        }
      },
      "5": {
        start: {
          line: 10,
          column: 0
        },
        end: {
          line: 10,
          column: 33
        }
      },
      "6": {
        start: {
          line: 11,
          column: 26
        },
        end: {
          line: 11,
          column: 69
        }
      },
      "7": {
        start: {
          line: 12,
          column: 22
        },
        end: {
          line: 32,
          column: 3
        }
      },
      "8": {
        start: {
          line: 14,
          column: 4
        },
        end: {
          line: 14,
          column: 57
        }
      },
      "9": {
        start: {
          line: 15,
          column: 4
        },
        end: {
          line: 15,
          column: 33
        }
      },
      "10": {
        start: {
          line: 17,
          column: 2
        },
        end: {
          line: 31,
          column: 6
        }
      },
      "11": {
        start: {
          line: 20,
          column: 21
        },
        end: {
          line: 25,
          column: 8
        }
      },
      "12": {
        start: {
          line: 21,
          column: 20
        },
        end: {
          line: 21,
          column: 24
        }
      },
      "13": {
        start: {
          line: 22,
          column: 8
        },
        end: {
          line: 24,
          column: 11
        }
      },
      "14": {
        start: {
          line: 23,
          column: 10
        },
        end: {
          line: 23,
          column: 52
        }
      },
      "15": {
        start: {
          line: 27,
          column: 8
        },
        end: {
          line: 27,
          column: 47
        }
      },
      "16": {
        start: {
          line: 29,
          column: 6
        },
        end: {
          line: 29,
          column: 21
        }
      },
      "17": {
        start: {
          line: 33,
          column: 0
        },
        end: {
          line: 33,
          column: 42
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 12,
            column: 22
          },
          end: {
            line: 12,
            column: 23
          }
        },
        loc: {
          start: {
            line: 12,
            column: 34
          },
          end: {
            line: 32,
            column: 1
          }
        },
        line: 12
      },
      "1": {
        name: "ValidateUseCase",
        decl: {
          start: {
            line: 13,
            column: 11
          },
          end: {
            line: 13,
            column: 26
          }
        },
        loc: {
          start: {
            line: 13,
            column: 39
          },
          end: {
            line: 16,
            column: 3
          }
        },
        line: 13
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 19,
            column: 11
          },
          end: {
            line: 19,
            column: 12
          }
        },
        loc: {
          start: {
            line: 19,
            column: 23
          },
          end: {
            line: 30,
            column: 5
          }
        },
        line: 19
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 20,
            column: 53
          },
          end: {
            line: 20,
            column: 54
          }
        },
        loc: {
          start: {
            line: 20,
            column: 73
          },
          end: {
            line: 25,
            column: 7
          }
        },
        line: 20
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 22,
            column: 60
          },
          end: {
            line: 22,
            column: 61
          }
        },
        loc: {
          start: {
            line: 22,
            column: 72
          },
          end: {
            line: 24,
            column: 9
          }
        },
        line: 22
      },
      "5": {
        name: "execute",
        decl: {
          start: {
            line: 26,
            column: 15
          },
          end: {
            line: 26,
            column: 22
          }
        },
        loc: {
          start: {
            line: 26,
            column: 27
          },
          end: {
            line: 28,
            column: 7
          }
        },
        line: 26
      }
    },
    branchMap: {},
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0
    },
    b: {},
    inputSourceMap: {
      version: 3,
      file: undefined,
      names: ["ExcecutionHandler_1", "require", "ValidateUseCase", "repository", "_classCallCheck2", "default", "_createClass2", "key", "value", "_execute", "_asyncToGenerator2", "request", "_this", "ExecutionHandler", "execute", "validate", "_x", "apply", "arguments", "exports"],
      sourceRoot: undefined,
      sources: ["/Users/<USER>/projects/react-native/payment-module/src/domain/usecases/payment/ValidateUseCase.ts"],
      sourcesContent: ["import {IPaymentRepository} from '../../repositories/IPaymentRepository';\nimport {ValidateModel} from '../../entities/validate/ValidateModel';\nimport {ResultState} from '../../../core/ResultState';\nimport {ExecutionHandler} from '../../../utils/ExcecutionHandler';\nimport {ValidateRequest} from '../../../data/models/validate/ValidateRequest';\nexport class ValidateUseCase {\n  private repository: IPaymentRepository;\n\n  constructor(repository: IPaymentRepository) {\n    this.repository = repository;\n  }\n\n  public async execute(request: ValidateRequest): Promise<ResultState<ValidateModel>> {\n    // call this.repository.validate(...)\n    return ExecutionHandler.execute(() => this.repository.validate(request));\n  }\n}\n"],
      mappings: ";;;;;;;;;;AAGA,IAAAA,mBAAA,GAAAC,OAAA;AAAkE,IAErDC,eAAe;EAG1B,SAAAA,gBAAYC,UAA8B;IAAA,IAAAC,gBAAA,CAAAC,OAAA,QAAAH,eAAA;IACxC,IAAI,CAACC,UAAU,GAAGA,UAAU;EAC9B;EAAC,WAAAG,aAAA,CAAAD,OAAA,EAAAH,eAAA;IAAAK,GAAA;IAAAC,KAAA;MAAA,IAAAC,QAAA,OAAAC,kBAAA,CAAAL,OAAA,EAEM,WAAcM,OAAwB;QAAA,IAAAC,KAAA;QAE3C,OAAOZ,mBAAA,CAAAa,gBAAgB,CAACC,OAAO,CAAC;UAAA,OAAMF,KAAI,CAACT,UAAU,CAACY,QAAQ,CAACJ,OAAO,CAAC;QAAA,EAAC;MAC1E,CAAC;MAAA,SAHYG,OAAOA,CAAAE,EAAA;QAAA,OAAAP,QAAA,CAAAQ,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAPJ,OAAO;IAAA;EAAA;AAAA;AAPtBK,OAAA,CAAAjB,eAAA,GAAAA,eAAA",
      ignoreList: []
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "5a79af823230b0817115e123cbdb8a03308f1cc1"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_wj2qsdq0q = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_wj2qsdq0q();
var _interopRequireDefault =
/* istanbul ignore next */
(cov_wj2qsdq0q().s[0]++, require("@babel/runtime/helpers/interopRequireDefault"));
var _asyncToGenerator2 =
/* istanbul ignore next */
(cov_wj2qsdq0q().s[1]++, _interopRequireDefault(require("@babel/runtime/helpers/asyncToGenerator")));
var _classCallCheck2 =
/* istanbul ignore next */
(cov_wj2qsdq0q().s[2]++, _interopRequireDefault(require("@babel/runtime/helpers/classCallCheck")));
var _createClass2 =
/* istanbul ignore next */
(cov_wj2qsdq0q().s[3]++, _interopRequireDefault(require("@babel/runtime/helpers/createClass")));
/* istanbul ignore next */
cov_wj2qsdq0q().s[4]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_wj2qsdq0q().s[5]++;
exports.ValidateUseCase = void 0;
var ExcecutionHandler_1 =
/* istanbul ignore next */
(cov_wj2qsdq0q().s[6]++, require("../../../utils/ExcecutionHandler"));
var ValidateUseCase =
/* istanbul ignore next */
(cov_wj2qsdq0q().s[7]++, function () {
  /* istanbul ignore next */
  cov_wj2qsdq0q().f[0]++;
  function ValidateUseCase(repository) {
    /* istanbul ignore next */
    cov_wj2qsdq0q().f[1]++;
    cov_wj2qsdq0q().s[8]++;
    (0, _classCallCheck2.default)(this, ValidateUseCase);
    /* istanbul ignore next */
    cov_wj2qsdq0q().s[9]++;
    this.repository = repository;
  }
  /* istanbul ignore next */
  cov_wj2qsdq0q().s[10]++;
  return (0, _createClass2.default)(ValidateUseCase, [{
    key: "execute",
    value: function () {
      /* istanbul ignore next */
      cov_wj2qsdq0q().f[2]++;
      var _execute =
      /* istanbul ignore next */
      (cov_wj2qsdq0q().s[11]++, (0, _asyncToGenerator2.default)(function* (request) {
        /* istanbul ignore next */
        cov_wj2qsdq0q().f[3]++;
        var _this =
        /* istanbul ignore next */
        (cov_wj2qsdq0q().s[12]++, this);
        /* istanbul ignore next */
        cov_wj2qsdq0q().s[13]++;
        return ExcecutionHandler_1.ExecutionHandler.execute(function () {
          /* istanbul ignore next */
          cov_wj2qsdq0q().f[4]++;
          cov_wj2qsdq0q().s[14]++;
          return _this.repository.validate(request);
        });
      }));
      function execute(_x) {
        /* istanbul ignore next */
        cov_wj2qsdq0q().f[5]++;
        cov_wj2qsdq0q().s[15]++;
        return _execute.apply(this, arguments);
      }
      /* istanbul ignore next */
      cov_wj2qsdq0q().s[16]++;
      return execute;
    }()
  }]);
}());
/* istanbul ignore next */
cov_wj2qsdq0q().s[17]++;
exports.ValidateUseCase = ValidateUseCase;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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