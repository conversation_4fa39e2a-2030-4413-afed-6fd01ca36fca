576eca088781501aa81f9e6be0965d98
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.NativeVirtualText = exports.NativeText = void 0;
var _ViewConfig = require("../NativeComponent/ViewConfig");
var _UIManager = _interopRequireDefault(require("../ReactNative/UIManager"));
var _createReactNativeComponentClass = _interopRequireDefault(require("../Renderer/shims/createReactNativeComponentClass"));
var textViewConfig = {
  validAttributes: {
    isHighlighted: true,
    isPressable: true,
    numberOfLines: true,
    ellipsizeMode: true,
    allowFontScaling: true,
    dynamicTypeRamp: true,
    maxFontSizeMultiplier: true,
    disabled: true,
    selectable: true,
    selectionColor: true,
    adjustsFontSizeToFit: true,
    minimumFontScale: true,
    textBreakStrategy: true,
    onTextLayout: true,
    onInlineViewLayout: true,
    dataDetectorType: true,
    android_hyphenationFrequency: true,
    lineBreakStrategyIOS: true
  },
  directEventTypes: {
    topTextLayout: {
      registrationName: 'onTextLayout'
    },
    topInlineViewLayout: {
      registrationName: 'onInlineViewLayout'
    }
  },
  uiViewClassName: 'RCTText'
};
var virtualTextViewConfig = {
  validAttributes: {
    isHighlighted: true,
    isPressable: true,
    maxFontSizeMultiplier: true
  },
  uiViewClassName: 'RCTVirtualText'
};
var NativeText = exports.NativeText = (0, _createReactNativeComponentClass.default)('RCTText', function () {
  return (0, _ViewConfig.createViewConfig)(textViewConfig);
});
var NativeVirtualText = exports.NativeVirtualText = !global.RN$Bridgeless && !_UIManager.default.hasViewManagerConfig('RCTVirtualText') ? NativeText : (0, _createReactNativeComponentClass.default)('RCTVirtualText', function () {
  return (0, _ViewConfig.createViewConfig)(virtualTextViewConfig);
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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