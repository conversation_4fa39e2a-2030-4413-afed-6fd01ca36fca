{"version": 3, "names": ["exports", "mapPaymentOrderStatusResponseToModel", "PaymentOrderStatusModel_1", "cov_2knuyp92gq", "s", "require", "response", "f", "_response$status", "_response$additions$t", "_response$additions", "PaymentOrderStatusModel", "status", "b", "PaymentOrderStatusAdditionalModel", "additions", "t24TraceCode"], "sources": ["/Users/<USER>/projects/react-native/payment-module/src/data/mappers/payment-order-status/PaymentOrderStatusMapper.ts"], "sourcesContent": ["import {PaymentOrderStatusResponse} from '../../models/payment-order-status/PaymentOrderStatusResponse';\nimport {\n  PaymentOrderStatusAdditionalModel,\n  PaymentOrderStatusModel,\n} from '../../../domain/entities/payment-order-status/PaymentOrderStatusModel';\n\nexport function mapPaymentOrderStatusResponseToModel(response: PaymentOrderStatusResponse): PaymentOrderStatusModel {\n  return new PaymentOrderStatusModel(\n    response.status ?? '',\n    new PaymentOrderStatusAdditionalModel(response.additions?.t24TraceCode ?? ''),\n  );\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMAA,OAAA,CAAAC,oCAAA,GAAAA,oCAAA;AALA,IAAAC,yBAAA;AAAA;AAAA,CAAAC,cAAA,GAAAC,CAAA,OAAAC,OAAA;AAKA,SAAgBJ,oCAAoCA,CAACK,QAAoC;EAAA;EAAAH,cAAA,GAAAI,CAAA;EAAA,IAAAC,gBAAA,EAAAC,qBAAA,EAAAC,mBAAA;EAAA;EAAAP,cAAA,GAAAC,CAAA;EACvF,OAAO,IAAIF,yBAAA,CAAAS,uBAAuB,EAAAH,gBAAA,GAChCF,QAAQ,CAACM,MAAM;EAAA;EAAA,CAAAT,cAAA,GAAAU,CAAA,UAAAL,gBAAA;EAAA;EAAA,CAAAL,cAAA,GAAAU,CAAA,UAAI,EAAE,GACrB,IAAIX,yBAAA,CAAAY,iCAAiC,EAAAL,qBAAA,IAAAC,mBAAA,GAACJ,QAAQ,CAACS,SAAS;EAAA;EAAA,CAAAZ,cAAA,GAAAU,CAAA;EAAA;EAAA,CAAAV,cAAA,GAAAU,CAAA,UAAlBH,mBAAA,CAAoBM,YAAY;EAAA;EAAA,CAAAb,cAAA,GAAAU,CAAA,UAAAJ,qBAAA;EAAA;EAAA,CAAAN,cAAA,GAAAU,CAAA,UAAI,EAAE,EAAC,CAC9E;AACH", "ignoreList": []}