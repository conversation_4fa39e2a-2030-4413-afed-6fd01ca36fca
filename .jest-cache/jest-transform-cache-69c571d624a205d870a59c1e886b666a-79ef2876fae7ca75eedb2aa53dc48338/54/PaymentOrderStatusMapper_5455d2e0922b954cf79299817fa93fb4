8d4c98ee9fb59857cd0bbeec6b1a5d7b
"use strict";

/* istanbul ignore next */
function cov_2knuyp92gq() {
  var path = "/Users/<USER>/projects/react-native/payment-module/src/data/mappers/payment-order-status/PaymentOrderStatusMapper.ts";
  var hash = "1667f9f08983344765a4b8c0d53a1055d178de38";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/projects/react-native/payment-module/src/data/mappers/payment-order-status/PaymentOrderStatusMapper.ts",
    statementMap: {
      "0": {
        start: {
          line: 3,
          column: 0
        },
        end: {
          line: 5,
          column: 3
        }
      },
      "1": {
        start: {
          line: 6,
          column: 0
        },
        end: {
          line: 6,
          column: 84
        }
      },
      "2": {
        start: {
          line: 7,
          column: 32
        },
        end: {
          line: 7,
          column: 112
        }
      },
      "3": {
        start: {
          line: 10,
          column: 2
        },
        end: {
          line: 10,
          column: 357
        }
      }
    },
    fnMap: {
      "0": {
        name: "mapPaymentOrderStatusResponseToModel",
        decl: {
          start: {
            line: 8,
            column: 9
          },
          end: {
            line: 8,
            column: 45
          }
        },
        loc: {
          start: {
            line: 8,
            column: 56
          },
          end: {
            line: 11,
            column: 1
          }
        },
        line: 8
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 10,
            column: 63
          },
          end: {
            line: 10,
            column: 131
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 10,
            column: 110
          },
          end: {
            line: 10,
            column: 126
          }
        }, {
          start: {
            line: 10,
            column: 129
          },
          end: {
            line: 10,
            column: 131
          }
        }],
        line: 10
      },
      "1": {
        loc: {
          start: {
            line: 10,
            column: 197
          },
          end: {
            line: 10,
            column: 354
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 10,
            column: 328
          },
          end: {
            line: 10,
            column: 349
          }
        }, {
          start: {
            line: 10,
            column: 352
          },
          end: {
            line: 10,
            column: 354
          }
        }],
        line: 10
      },
      "2": {
        loc: {
          start: {
            line: 10,
            column: 222
          },
          end: {
            line: 10,
            column: 316
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 10,
            column: 275
          },
          end: {
            line: 10,
            column: 281
          }
        }, {
          start: {
            line: 10,
            column: 284
          },
          end: {
            line: 10,
            column: 316
          }
        }],
        line: 10
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0
    },
    f: {
      "0": 0
    },
    b: {
      "0": [0, 0],
      "1": [0, 0],
      "2": [0, 0]
    },
    inputSourceMap: {
      version: 3,
      file: undefined,
      names: ["exports", "mapPaymentOrderStatusResponseToModel", "PaymentOrderStatusModel_1", "require", "response", "_response$status", "_response$additions$t", "_response$additions", "PaymentOrderStatusModel", "status", "PaymentOrderStatusAdditionalModel", "additions", "t24TraceCode"],
      sourceRoot: undefined,
      sources: ["/Users/<USER>/projects/react-native/payment-module/src/data/mappers/payment-order-status/PaymentOrderStatusMapper.ts"],
      sourcesContent: ["import {PaymentOrderStatusResponse} from '../../models/payment-order-status/PaymentOrderStatusResponse';\nimport {\n  PaymentOrderStatusAdditionalModel,\n  PaymentOrderStatusModel,\n} from '../../../domain/entities/payment-order-status/PaymentOrderStatusModel';\n\nexport function mapPaymentOrderStatusResponseToModel(response: PaymentOrderStatusResponse): PaymentOrderStatusModel {\n  return new PaymentOrderStatusModel(\n    response.status ?? '',\n    new PaymentOrderStatusAdditionalModel(response.additions?.t24TraceCode ?? ''),\n  );\n}\n"],
      mappings: ";;;;;AAMAA,OAAA,CAAAC,oCAAA,GAAAA,oCAAA;AALA,IAAAC,yBAAA,GAAAC,OAAA;AAKA,SAAgBF,oCAAoCA,CAACG,QAAoC;EAAA,IAAAC,gBAAA,EAAAC,qBAAA,EAAAC,mBAAA;EACvF,OAAO,IAAIL,yBAAA,CAAAM,uBAAuB,EAAAH,gBAAA,GAChCD,QAAQ,CAACK,MAAM,YAAAJ,gBAAA,GAAI,EAAE,EACrB,IAAIH,yBAAA,CAAAQ,iCAAiC,EAAAJ,qBAAA,IAAAC,mBAAA,GAACH,QAAQ,CAACO,SAAS,qBAAlBJ,mBAAA,CAAoBK,YAAY,YAAAN,qBAAA,GAAI,EAAE,CAAC,CAC9E;AACH",
      ignoreList: []
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "1667f9f08983344765a4b8c0d53a1055d178de38"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_2knuyp92gq = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_2knuyp92gq();
cov_2knuyp92gq().s[0]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_2knuyp92gq().s[1]++;
exports.mapPaymentOrderStatusResponseToModel = mapPaymentOrderStatusResponseToModel;
var PaymentOrderStatusModel_1 =
/* istanbul ignore next */
(cov_2knuyp92gq().s[2]++, require("../../../domain/entities/payment-order-status/PaymentOrderStatusModel"));
function mapPaymentOrderStatusResponseToModel(response) {
  /* istanbul ignore next */
  cov_2knuyp92gq().f[0]++;
  var _response$status, _response$additions$t, _response$additions;
  /* istanbul ignore next */
  cov_2knuyp92gq().s[3]++;
  return new PaymentOrderStatusModel_1.PaymentOrderStatusModel((_response$status = response.status) != null ?
  /* istanbul ignore next */
  (cov_2knuyp92gq().b[0][0]++, _response$status) :
  /* istanbul ignore next */
  (cov_2knuyp92gq().b[0][1]++, ''), new PaymentOrderStatusModel_1.PaymentOrderStatusAdditionalModel((_response$additions$t = (_response$additions = response.additions) == null ?
  /* istanbul ignore next */
  (cov_2knuyp92gq().b[2][0]++, void 0) :
  /* istanbul ignore next */
  (cov_2knuyp92gq().b[2][1]++, _response$additions.t24TraceCode)) != null ?
  /* istanbul ignore next */
  (cov_2knuyp92gq().b[1][0]++, _response$additions$t) :
  /* istanbul ignore next */
  (cov_2knuyp92gq().b[1][1]++, '')));
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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