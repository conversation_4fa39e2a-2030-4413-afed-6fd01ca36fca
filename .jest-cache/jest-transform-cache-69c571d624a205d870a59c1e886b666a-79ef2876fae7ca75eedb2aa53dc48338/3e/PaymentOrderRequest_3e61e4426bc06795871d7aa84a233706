cbb27747faa3a3f3beabc8758c0bbbef
"use strict";

/* istanbul ignore next */
function cov_zot22gsyo() {
  var path = "/Users/<USER>/projects/react-native/payment-module/src/data/models/payment-order/PaymentOrderRequest.ts";
  var hash = "4fd43dd2f252f30ee3d9291326b469094e3a2353";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/projects/react-native/payment-module/src/data/models/payment-order/PaymentOrderRequest.ts",
    statementMap: {
      "0": {
        start: {
          line: 3,
          column: 0
        },
        end: {
          line: 5,
          column: 3
        }
      }
    },
    fnMap: {},
    branchMap: {},
    s: {
      "0": 0
    },
    f: {},
    b: {},
    inputSourceMap: {
      version: 3,
      file: undefined,
      names: [],
      sourceRoot: undefined,
      sources: ["/Users/<USER>/projects/react-native/payment-module/src/data/models/payment-order/PaymentOrderRequest.ts"],
      sourcesContent: ["export interface PaymentOrderRequest {\n  id?: string;\n  originatorAccount?: {\n    identification?: {\n      identification?: string;\n      schemeName?: string;\n    };\n  };\n  requestedExecutionDate?: string; // ISO 8601 format date string\n  paymentType?: string;\n  transferTransactionInformation?: {\n    instructedAmount?: {\n      amount?: string; // if dynamic binding like \"{{totalAmount}}\", use string\n      currencyCode?: string;\n    };\n    counterparty?: {\n      name?: string;\n    };\n    counterpartyAccount?: {\n      identification?: {\n        identification?: string;\n        schemeName?: string;\n      };\n    };\n    additions?: {\n      bpQueryRef?: string;\n      bpBillList?: string; // or stringified JSON if passed as a string\n      bpSummary?: string;\n      bpServiceCode?: string;\n      cifNo?: string;\n      bpCategory?: string;\n    };\n  };\n}\n"],
      mappings: "",
      ignoreList: []
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "4fd43dd2f252f30ee3d9291326b469094e3a2353"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_zot22gsyo = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_zot22gsyo();
cov_zot22gsyo().s[0]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6W10sInNvdXJjZXMiOlsiL1VzZXJzL3NvbnNhbXNldC9wcm9qZWN0cy9yZWFjdC1uYXRpdmUvcGF5bWVudC1tb2R1bGUvc3JjL2RhdGEvbW9kZWxzL3BheW1lbnQtb3JkZXIvUGF5bWVudE9yZGVyUmVxdWVzdC50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgaW50ZXJmYWNlIFBheW1lbnRPcmRlclJlcXVlc3Qge1xuICBpZD86IHN0cmluZztcbiAgb3JpZ2luYXRvckFjY291bnQ/OiB7XG4gICAgaWRlbnRpZmljYXRpb24/OiB7XG4gICAgICBpZGVudGlmaWNhdGlvbj86IHN0cmluZztcbiAgICAgIHNjaGVtZU5hbWU/OiBzdHJpbmc7XG4gICAgfTtcbiAgfTtcbiAgcmVxdWVzdGVkRXhlY3V0aW9uRGF0ZT86IHN0cmluZzsgLy8gSVNPIDg2MDEgZm9ybWF0IGRhdGUgc3RyaW5nXG4gIHBheW1lbnRUeXBlPzogc3RyaW5nO1xuICB0cmFuc2ZlclRyYW5zYWN0aW9uSW5mb3JtYXRpb24/OiB7XG4gICAgaW5zdHJ1Y3RlZEFtb3VudD86IHtcbiAgICAgIGFtb3VudD86IHN0cmluZzsgLy8gaWYgZHluYW1pYyBiaW5kaW5nIGxpa2UgXCJ7e3RvdGFsQW1vdW50fX1cIiwgdXNlIHN0cmluZ1xuICAgICAgY3VycmVuY3lDb2RlPzogc3RyaW5nO1xuICAgIH07XG4gICAgY291bnRlcnBhcnR5Pzoge1xuICAgICAgbmFtZT86IHN0cmluZztcbiAgICB9O1xuICAgIGNvdW50ZXJwYXJ0eUFjY291bnQ/OiB7XG4gICAgICBpZGVudGlmaWNhdGlvbj86IHtcbiAgICAgICAgaWRlbnRpZmljYXRpb24/OiBzdHJpbmc7XG4gICAgICAgIHNjaGVtZU5hbWU/OiBzdHJpbmc7XG4gICAgICB9O1xuICAgIH07XG4gICAgYWRkaXRpb25zPzoge1xuICAgICAgYnBRdWVyeVJlZj86IHN0cmluZztcbiAgICAgIGJwQmlsbExpc3Q/OiBzdHJpbmc7IC8vIG9yIHN0cmluZ2lmaWVkIEpTT04gaWYgcGFzc2VkIGFzIGEgc3RyaW5nXG4gICAgICBicFN1bW1hcnk/OiBzdHJpbmc7XG4gICAgICBicFNlcnZpY2VDb2RlPzogc3RyaW5nO1xuICAgICAgY2lmTm8/OiBzdHJpbmc7XG4gICAgICBicENhdGVnb3J5Pzogc3RyaW5nO1xuICAgIH07XG4gIH07XG59XG4iXSwibWFwcGluZ3MiOiIiLCJpZ25vcmVMaXN0IjpbXX0=