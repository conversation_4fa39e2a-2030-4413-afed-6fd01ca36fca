{"version": 3, "names": ["cov_2escz9fffg", "actualCoverage", "react_native_select_contact_1", "s", "require", "PermissionsAndroid_1", "requestContactPermission", "f", "_ref", "_asyncToGenerator2", "default", "granted", "PermissionsAndroid", "request", "PERMISSIONS", "READ_CONTACTS", "title", "message", "buttonNegative", "buttonPositive", "RESULTS", "GRANTED", "b", "console", "log", "err", "warn", "apply", "arguments", "exports", "getContact", "_ref2", "selectContactPhone", "then", "select", "_phoneNum$split", "contact", "selectedPhone", "phoneNum", "number", "phoneStr", "split", "join", "type", "name", "catch", "e", "undefined"], "sources": ["/Users/<USER>/projects/react-native/payment-module/src/utils/ContactUtils.ts"], "sourcesContent": ["import {selectContactPhone} from 'react-native-select-contact';\nimport {PermissionsAndroid} from 'react-native/Libraries/PermissionsAndroid/PermissionsAndroid';\n\nconst requestContactPermission = async (): Promise<boolean> => {\n  try {\n    const granted = await PermissionsAndroid.request(PermissionsAndroid.PERMISSIONS.READ_CONTACTS, {\n      title: 'Allow Access Contact?',\n      message: 'allow this app to read contact information',\n      buttonNegative: 'Cancel',\n      buttonPositive: 'OK',\n    });\n    if (granted === PermissionsAndroid.RESULTS.GRANTED) {\n      console.log('granted');\n      return true;\n    } else {\n      console.log('denied');\n      return false;\n    }\n  } catch (err) {\n    console.warn(err);\n    return false;\n  }\n};\n\nconst getContact = async (): Promise<string | undefined | null> => {\n  return selectContactPhone()\n    .then(select => {\n      if (!select) {\n        return null;\n      }\n      const {contact, selectedPhone} = select;\n      const phoneNum = selectedPhone?.number;\n      const phoneStr = phoneNum?.split(' ')?.join('');\n      console.log(`Selected ${selectedPhone.type} phone number ${selectedPhone.number} from ${contact.name}`);\n\n      return phoneStr;\n    })\n    .catch(e => {\n      console.log('====================================');\n      console.log(e);\n      console.log('====================================');\n      return undefined;\n    });\n};\n\nexport {requestContactPermission, getContact};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAOM;IAAAA,cAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,cAAA;;;;;;;;;;;;;;;AAPN,IAAAE,6BAAA;AAAA;AAAA,CAAAF,cAAA,GAAAG,CAAA,OAAAC,OAAA;AACA,IAAAC,oBAAA;AAAA;AAAA,CAAAL,cAAA,GAAAG,CAAA,OAAAC,OAAA;AAEA,IAAME,wBAAwB;AAAA;AAAA,CAAAN,cAAA,GAAAG,CAAA;EAAA;EAAAH,cAAA,GAAAO,CAAA;EAAA,IAAAC,IAAA;EAAA;EAAA,CAAAR,cAAA,GAAAG,CAAA,WAAAM,kBAAA,CAAAC,OAAA,EAAG,aAA6B;IAAA;IAAAV,cAAA,GAAAO,CAAA;IAAAP,cAAA,GAAAG,CAAA;IAC5D,IAAI;MACF,IAAMQ,OAAO;MAAA;MAAA,CAAAX,cAAA,GAAAG,CAAA,aAASE,oBAAA,CAAAO,kBAAkB,CAACC,OAAO,CAACR,oBAAA,CAAAO,kBAAkB,CAACE,WAAW,CAACC,aAAa,EAAE;QAC7FC,KAAK,EAAE,uBAAuB;QAC9BC,OAAO,EAAE,4CAA4C;QACrDC,cAAc,EAAE,QAAQ;QACxBC,cAAc,EAAE;OACjB,CAAC;MAAA;MAAAnB,cAAA,GAAAG,CAAA;MACF,IAAIQ,OAAO,KAAKN,oBAAA,CAAAO,kBAAkB,CAACQ,OAAO,CAACC,OAAO,EAAE;QAAA;QAAArB,cAAA,GAAAsB,CAAA;QAAAtB,cAAA,GAAAG,CAAA;QAClDoB,OAAO,CAACC,GAAG,CAAC,SAAS,CAAC;QAAA;QAAAxB,cAAA,GAAAG,CAAA;QACtB,OAAO,IAAI;MACb,CAAC,MAAM;QAAA;QAAAH,cAAA,GAAAsB,CAAA;QAAAtB,cAAA,GAAAG,CAAA;QACLoB,OAAO,CAACC,GAAG,CAAC,QAAQ,CAAC;QAAA;QAAAxB,cAAA,GAAAG,CAAA;QACrB,OAAO,KAAK;MACd;IACF,CAAC,CAAC,OAAOsB,GAAG,EAAE;MAAA;MAAAzB,cAAA,GAAAG,CAAA;MACZoB,OAAO,CAACG,IAAI,CAACD,GAAG,CAAC;MAAA;MAAAzB,cAAA,GAAAG,CAAA;MACjB,OAAO,KAAK;IACd;EACF,CAAC;EAAA;EAAAH,cAAA,GAAAG,CAAA;EAAA,gBAnBKG,wBAAwBA,CAAA;IAAA;IAAAN,cAAA,GAAAO,CAAA;IAAAP,cAAA,GAAAG,CAAA;IAAA,OAAAK,IAAA,CAAAmB,KAAA,OAAAC,SAAA;EAAA;AAAA,GAmB7B;AAAA;AAAA5B,cAAA,GAAAG,CAAA;AAuBO0B,OAAA,CAAAvB,wBAAA,GAAAA,wBAAA;AArBR,IAAMwB,UAAU;AAAA;AAAA,CAAA9B,cAAA,GAAAG,CAAA;EAAA;EAAAH,cAAA,GAAAO,CAAA;EAAA,IAAAwB,KAAA;EAAA;EAAA,CAAA/B,cAAA,GAAAG,CAAA,YAAAM,kBAAA,CAAAC,OAAA,EAAG,aAA+C;IAAA;IAAAV,cAAA,GAAAO,CAAA;IAAAP,cAAA,GAAAG,CAAA;IAChE,OAAO,IAAAD,6BAAA,CAAA8B,kBAAkB,GAAE,CACxBC,IAAI,CAAC,UAAAC,MAAM,EAAG;MAAA;MAAAlC,cAAA,GAAAO,CAAA;MAAA,IAAA4B,eAAA;MAAA;MAAAnC,cAAA,GAAAG,CAAA;MACb,IAAI,CAAC+B,MAAM,EAAE;QAAA;QAAAlC,cAAA,GAAAsB,CAAA;QAAAtB,cAAA,GAAAG,CAAA;QACX,OAAO,IAAI;MACb;MAAA;MAAA;QAAAH,cAAA,GAAAsB,CAAA;MAAA;MACA,IAAOc,OAAO;QAAA;QAAA,CAAApC,cAAA,GAAAG,CAAA,QAAmB+B,MAAM,CAAhCE,OAAO;QAAEC,aAAa;QAAA;QAAA,CAAArC,cAAA,GAAAG,CAAA,QAAI+B,MAAM,CAAvBG,aAAa;MAC7B,IAAMC,QAAQ;MAAA;MAAA,CAAAtC,cAAA,GAAAG,CAAA,QAAGkC,aAAa;MAAA;MAAA,CAAArC,cAAA,GAAAsB,CAAA;MAAA;MAAA,CAAAtB,cAAA,GAAAsB,CAAA,UAAbe,aAAa,CAAEE,MAAM;MACtC,IAAMC,QAAQ;MAAA;MAAA,CAAAxC,cAAA,GAAAG,CAAA;MAAG;MAAA,CAAAH,cAAA,GAAAsB,CAAA,UAAAgB,QAAQ;MAAA;MAAA,CAAAtC,cAAA,GAAAsB,CAAA,WAAAa,eAAA,GAARG,QAAQ,CAAEG,KAAK,CAAC,GAAG,CAAC;MAAA;MAAA,CAAAzC,cAAA,GAAAsB,CAAA;MAAA;MAAA,CAAAtB,cAAA,GAAAsB,CAAA,UAApBa,eAAA,CAAsBO,IAAI,CAAC,EAAE,CAAC;MAAA;MAAA1C,cAAA,GAAAG,CAAA;MAC/CoB,OAAO,CAACC,GAAG,CAAC,YAAYa,aAAa,CAACM,IAAI,iBAAiBN,aAAa,CAACE,MAAM,SAASH,OAAO,CAACQ,IAAI,EAAE,CAAC;MAAA;MAAA5C,cAAA,GAAAG,CAAA;MAEvG,OAAOqC,QAAQ;IACjB,CAAC,CAAC,CACDK,KAAK,CAAC,UAAAC,CAAC,EAAG;MAAA;MAAA9C,cAAA,GAAAO,CAAA;MAAAP,cAAA,GAAAG,CAAA;MACToB,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC;MAAA;MAAAxB,cAAA,GAAAG,CAAA;MACnDoB,OAAO,CAACC,GAAG,CAACsB,CAAC,CAAC;MAAA;MAAA9C,cAAA,GAAAG,CAAA;MACdoB,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC;MAAA;MAAAxB,cAAA,GAAAG,CAAA;MACnD,OAAO4C,SAAS;IAClB,CAAC,CAAC;EACN,CAAC;EAAA;EAAA/C,cAAA,GAAAG,CAAA;EAAA,gBAnBK2B,UAAUA,CAAA;IAAA;IAAA9B,cAAA,GAAAO,CAAA;IAAAP,cAAA,GAAAG,CAAA;IAAA,OAAA4B,KAAA,CAAAJ,KAAA,OAAAC,SAAA;EAAA;AAAA,GAmBf;AAAA;AAAA5B,cAAA,GAAAG,CAAA;AAEiC0B,OAAA,CAAAC,UAAA,GAAAA,UAAA", "ignoreList": []}