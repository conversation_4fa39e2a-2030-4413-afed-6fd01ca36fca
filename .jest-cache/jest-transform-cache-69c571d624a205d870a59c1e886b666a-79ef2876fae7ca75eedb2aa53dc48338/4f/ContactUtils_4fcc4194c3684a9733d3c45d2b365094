900946cbed830d34b0de2287dc9b70f5
"use strict";

/* istanbul ignore next */
function cov_2escz9fffg() {
  var path = "/Users/<USER>/projects/react-native/payment-module/src/utils/ContactUtils.ts";
  var hash = "32fef2307ba574327369324d8ff26cca3f37a21a";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/projects/react-native/payment-module/src/utils/ContactUtils.ts",
    statementMap: {
      "0": {
        start: {
          line: 3,
          column: 29
        },
        end: {
          line: 3,
          column: 84
        }
      },
      "1": {
        start: {
          line: 4,
          column: 25
        },
        end: {
          line: 4,
          column: 99
        }
      },
      "2": {
        start: {
          line: 5,
          column: 0
        },
        end: {
          line: 7,
          column: 3
        }
      },
      "3": {
        start: {
          line: 8,
          column: 0
        },
        end: {
          line: 8,
          column: 63
        }
      },
      "4": {
        start: {
          line: 9,
          column: 36
        },
        end: {
          line: 9,
          column: 74
        }
      },
      "5": {
        start: {
          line: 10,
          column: 27
        },
        end: {
          line: 10,
          column: 98
        }
      },
      "6": {
        start: {
          line: 11,
          column: 31
        },
        end: {
          line: 35,
          column: 3
        }
      },
      "7": {
        start: {
          line: 12,
          column: 13
        },
        end: {
          line: 31,
          column: 4
        }
      },
      "8": {
        start: {
          line: 13,
          column: 4
        },
        end: {
          line: 30,
          column: 5
        }
      },
      "9": {
        start: {
          line: 14,
          column: 20
        },
        end: {
          line: 19,
          column: 8
        }
      },
      "10": {
        start: {
          line: 20,
          column: 6
        },
        end: {
          line: 26,
          column: 7
        }
      },
      "11": {
        start: {
          line: 21,
          column: 8
        },
        end: {
          line: 21,
          column: 31
        }
      },
      "12": {
        start: {
          line: 22,
          column: 8
        },
        end: {
          line: 22,
          column: 20
        }
      },
      "13": {
        start: {
          line: 24,
          column: 8
        },
        end: {
          line: 24,
          column: 30
        }
      },
      "14": {
        start: {
          line: 25,
          column: 8
        },
        end: {
          line: 25,
          column: 21
        }
      },
      "15": {
        start: {
          line: 28,
          column: 6
        },
        end: {
          line: 28,
          column: 24
        }
      },
      "16": {
        start: {
          line: 29,
          column: 6
        },
        end: {
          line: 29,
          column: 19
        }
      },
      "17": {
        start: {
          line: 32,
          column: 2
        },
        end: {
          line: 34,
          column: 4
        }
      },
      "18": {
        start: {
          line: 33,
          column: 4
        },
        end: {
          line: 33,
          column: 39
        }
      },
      "19": {
        start: {
          line: 36,
          column: 0
        },
        end: {
          line: 36,
          column: 60
        }
      },
      "20": {
        start: {
          line: 37,
          column: 17
        },
        end: {
          line: 60,
          column: 3
        }
      },
      "21": {
        start: {
          line: 38,
          column: 14
        },
        end: {
          line: 56,
          column: 4
        }
      },
      "22": {
        start: {
          line: 39,
          column: 4
        },
        end: {
          line: 55,
          column: 7
        }
      },
      "23": {
        start: {
          line: 41,
          column: 6
        },
        end: {
          line: 43,
          column: 7
        }
      },
      "24": {
        start: {
          line: 42,
          column: 8
        },
        end: {
          line: 42,
          column: 20
        }
      },
      "25": {
        start: {
          line: 44,
          column: 20
        },
        end: {
          line: 44,
          column: 34
        }
      },
      "26": {
        start: {
          line: 45,
          column: 24
        },
        end: {
          line: 45,
          column: 44
        }
      },
      "27": {
        start: {
          line: 46,
          column: 21
        },
        end: {
          line: 46,
          column: 74
        }
      },
      "28": {
        start: {
          line: 47,
          column: 21
        },
        end: {
          line: 47,
          column: 124
        }
      },
      "29": {
        start: {
          line: 48,
          column: 6
        },
        end: {
          line: 48,
          column: 110
        }
      },
      "30": {
        start: {
          line: 49,
          column: 6
        },
        end: {
          line: 49,
          column: 22
        }
      },
      "31": {
        start: {
          line: 51,
          column: 6
        },
        end: {
          line: 51,
          column: 58
        }
      },
      "32": {
        start: {
          line: 52,
          column: 6
        },
        end: {
          line: 52,
          column: 21
        }
      },
      "33": {
        start: {
          line: 53,
          column: 6
        },
        end: {
          line: 53,
          column: 58
        }
      },
      "34": {
        start: {
          line: 54,
          column: 6
        },
        end: {
          line: 54,
          column: 23
        }
      },
      "35": {
        start: {
          line: 57,
          column: 2
        },
        end: {
          line: 59,
          column: 4
        }
      },
      "36": {
        start: {
          line: 58,
          column: 4
        },
        end: {
          line: 58,
          column: 40
        }
      },
      "37": {
        start: {
          line: 61,
          column: 0
        },
        end: {
          line: 61,
          column: 32
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 11,
            column: 31
          },
          end: {
            line: 11,
            column: 32
          }
        },
        loc: {
          start: {
            line: 11,
            column: 43
          },
          end: {
            line: 35,
            column: 1
          }
        },
        line: 11
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 12,
            column: 45
          },
          end: {
            line: 12,
            column: 46
          }
        },
        loc: {
          start: {
            line: 12,
            column: 58
          },
          end: {
            line: 31,
            column: 3
          }
        },
        line: 12
      },
      "2": {
        name: "requestContactPermission",
        decl: {
          start: {
            line: 32,
            column: 18
          },
          end: {
            line: 32,
            column: 42
          }
        },
        loc: {
          start: {
            line: 32,
            column: 45
          },
          end: {
            line: 34,
            column: 3
          }
        },
        line: 32
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 37,
            column: 17
          },
          end: {
            line: 37,
            column: 18
          }
        },
        loc: {
          start: {
            line: 37,
            column: 29
          },
          end: {
            line: 60,
            column: 1
          }
        },
        line: 37
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 38,
            column: 46
          },
          end: {
            line: 38,
            column: 47
          }
        },
        loc: {
          start: {
            line: 38,
            column: 59
          },
          end: {
            line: 56,
            column: 3
          }
        },
        line: 38
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 39,
            column: 72
          },
          end: {
            line: 39,
            column: 73
          }
        },
        loc: {
          start: {
            line: 39,
            column: 90
          },
          end: {
            line: 50,
            column: 5
          }
        },
        line: 39
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 50,
            column: 13
          },
          end: {
            line: 50,
            column: 14
          }
        },
        loc: {
          start: {
            line: 50,
            column: 26
          },
          end: {
            line: 55,
            column: 5
          }
        },
        line: 50
      },
      "7": {
        name: "getContact",
        decl: {
          start: {
            line: 57,
            column: 18
          },
          end: {
            line: 57,
            column: 28
          }
        },
        loc: {
          start: {
            line: 57,
            column: 31
          },
          end: {
            line: 59,
            column: 3
          }
        },
        line: 57
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 20,
            column: 6
          },
          end: {
            line: 26,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 20,
            column: 6
          },
          end: {
            line: 26,
            column: 7
          }
        }, {
          start: {
            line: 23,
            column: 13
          },
          end: {
            line: 26,
            column: 7
          }
        }],
        line: 20
      },
      "1": {
        loc: {
          start: {
            line: 41,
            column: 6
          },
          end: {
            line: 43,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 41,
            column: 6
          },
          end: {
            line: 43,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 41
      },
      "2": {
        loc: {
          start: {
            line: 46,
            column: 21
          },
          end: {
            line: 46,
            column: 74
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 46,
            column: 45
          },
          end: {
            line: 46,
            column: 51
          }
        }, {
          start: {
            line: 46,
            column: 54
          },
          end: {
            line: 46,
            column: 74
          }
        }],
        line: 46
      },
      "3": {
        loc: {
          start: {
            line: 47,
            column: 21
          },
          end: {
            line: 47,
            column: 124
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 47,
            column: 91
          },
          end: {
            line: 47,
            column: 97
          }
        }, {
          start: {
            line: 47,
            column: 100
          },
          end: {
            line: 47,
            column: 124
          }
        }],
        line: 47
      },
      "4": {
        loc: {
          start: {
            line: 47,
            column: 21
          },
          end: {
            line: 47,
            column: 88
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 47,
            column: 21
          },
          end: {
            line: 47,
            column: 37
          }
        }, {
          start: {
            line: 47,
            column: 41
          },
          end: {
            line: 47,
            column: 88
          }
        }],
        line: 47
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0
    },
    b: {
      "0": [0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0]
    },
    inputSourceMap: {
      version: 3,
      file: undefined,
      names: ["react_native_select_contact_1", "require", "PermissionsAndroid_1", "requestContactPermission", "_ref", "_asyncToGenerator2", "default", "granted", "PermissionsAndroid", "request", "PERMISSIONS", "READ_CONTACTS", "title", "message", "buttonNegative", "buttonPositive", "RESULTS", "GRANTED", "console", "log", "err", "warn", "apply", "arguments", "exports", "getContact", "_ref2", "selectContactPhone", "then", "select", "_phoneNum$split", "contact", "selectedPhone", "phoneNum", "number", "phoneStr", "split", "join", "type", "name", "catch", "e", "undefined"],
      sourceRoot: undefined,
      sources: ["/Users/<USER>/projects/react-native/payment-module/src/utils/ContactUtils.ts"],
      sourcesContent: ["import {selectContactPhone} from 'react-native-select-contact';\nimport {PermissionsAndroid} from 'react-native/Libraries/PermissionsAndroid/PermissionsAndroid';\n\nconst requestContactPermission = async (): Promise<boolean> => {\n  try {\n    const granted = await PermissionsAndroid.request(PermissionsAndroid.PERMISSIONS.READ_CONTACTS, {\n      title: 'Allow Access Contact?',\n      message: 'allow this app to read contact information',\n      buttonNegative: 'Cancel',\n      buttonPositive: 'OK',\n    });\n    if (granted === PermissionsAndroid.RESULTS.GRANTED) {\n      console.log('granted');\n      return true;\n    } else {\n      console.log('denied');\n      return false;\n    }\n  } catch (err) {\n    console.warn(err);\n    return false;\n  }\n};\n\nconst getContact = async (): Promise<string | undefined | null> => {\n  return selectContactPhone()\n    .then(select => {\n      if (!select) {\n        return null;\n      }\n      const {contact, selectedPhone} = select;\n      const phoneNum = selectedPhone?.number;\n      const phoneStr = phoneNum?.split(' ')?.join('');\n      console.log(`Selected ${selectedPhone.type} phone number ${selectedPhone.number} from ${contact.name}`);\n\n      return phoneStr;\n    })\n    .catch(e => {\n      console.log('====================================');\n      console.log(e);\n      console.log('====================================');\n      return undefined;\n    });\n};\n\nexport {requestContactPermission, getContact};\n"],
      mappings: ";;;;;;;;AAAA,IAAAA,6BAAA,GAAAC,OAAA;AACA,IAAAC,oBAAA,GAAAD,OAAA;AAEA,IAAME,wBAAwB;EAAA,IAAAC,IAAA,OAAAC,kBAAA,CAAAC,OAAA,EAAG,aAA6B;IAC5D,IAAI;MACF,IAAMC,OAAO,SAASL,oBAAA,CAAAM,kBAAkB,CAACC,OAAO,CAACP,oBAAA,CAAAM,kBAAkB,CAACE,WAAW,CAACC,aAAa,EAAE;QAC7FC,KAAK,EAAE,uBAAuB;QAC9BC,OAAO,EAAE,4CAA4C;QACrDC,cAAc,EAAE,QAAQ;QACxBC,cAAc,EAAE;OACjB,CAAC;MACF,IAAIR,OAAO,KAAKL,oBAAA,CAAAM,kBAAkB,CAACQ,OAAO,CAACC,OAAO,EAAE;QAClDC,OAAO,CAACC,GAAG,CAAC,SAAS,CAAC;QACtB,OAAO,IAAI;MACb,CAAC,MAAM;QACLD,OAAO,CAACC,GAAG,CAAC,QAAQ,CAAC;QACrB,OAAO,KAAK;MACd;IACF,CAAC,CAAC,OAAOC,GAAG,EAAE;MACZF,OAAO,CAACG,IAAI,CAACD,GAAG,CAAC;MACjB,OAAO,KAAK;IACd;EACF,CAAC;EAAA,gBAnBKjB,wBAAwBA,CAAA;IAAA,OAAAC,IAAA,CAAAkB,KAAA,OAAAC,SAAA;EAAA;AAAA,GAmB7B;AAuBOC,OAAA,CAAArB,wBAAA,GAAAA,wBAAA;AArBR,IAAMsB,UAAU;EAAA,IAAAC,KAAA,OAAArB,kBAAA,CAAAC,OAAA,EAAG,aAA+C;IAChE,OAAO,IAAAN,6BAAA,CAAA2B,kBAAkB,GAAE,CACxBC,IAAI,CAAC,UAAAC,MAAM,EAAG;MAAA,IAAAC,eAAA;MACb,IAAI,CAACD,MAAM,EAAE;QACX,OAAO,IAAI;MACb;MACA,IAAOE,OAAO,GAAmBF,MAAM,CAAhCE,OAAO;QAAEC,aAAa,GAAIH,MAAM,CAAvBG,aAAa;MAC7B,IAAMC,QAAQ,GAAGD,aAAa,oBAAbA,aAAa,CAAEE,MAAM;MACtC,IAAMC,QAAQ,GAAGF,QAAQ,aAAAH,eAAA,GAARG,QAAQ,CAAEG,KAAK,CAAC,GAAG,CAAC,qBAApBN,eAAA,CAAsBO,IAAI,CAAC,EAAE,CAAC;MAC/CnB,OAAO,CAACC,GAAG,CAAC,YAAYa,aAAa,CAACM,IAAI,iBAAiBN,aAAa,CAACE,MAAM,SAASH,OAAO,CAACQ,IAAI,EAAE,CAAC;MAEvG,OAAOJ,QAAQ;IACjB,CAAC,CAAC,CACDK,KAAK,CAAC,UAAAC,CAAC,EAAG;MACTvB,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC;MACnDD,OAAO,CAACC,GAAG,CAACsB,CAAC,CAAC;MACdvB,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC;MACnD,OAAOuB,SAAS;IAClB,CAAC,CAAC;EACN,CAAC;EAAA,gBAnBKjB,UAAUA,CAAA;IAAA,OAAAC,KAAA,CAAAJ,KAAA,OAAAC,SAAA;EAAA;AAAA,GAmBf;AAEiCC,OAAA,CAAAC,UAAA,GAAAA,UAAA",
      ignoreList: []
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "32fef2307ba574327369324d8ff26cca3f37a21a"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_2escz9fffg = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_2escz9fffg();
var _interopRequireDefault =
/* istanbul ignore next */
(cov_2escz9fffg().s[0]++, require("@babel/runtime/helpers/interopRequireDefault"));
var _asyncToGenerator2 =
/* istanbul ignore next */
(cov_2escz9fffg().s[1]++, _interopRequireDefault(require("@babel/runtime/helpers/asyncToGenerator")));
/* istanbul ignore next */
cov_2escz9fffg().s[2]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_2escz9fffg().s[3]++;
exports.getContact = exports.requestContactPermission = void 0;
var react_native_select_contact_1 =
/* istanbul ignore next */
(cov_2escz9fffg().s[4]++, require("react-native-select-contact"));
var PermissionsAndroid_1 =
/* istanbul ignore next */
(cov_2escz9fffg().s[5]++, require("react-native/Libraries/PermissionsAndroid/PermissionsAndroid"));
var requestContactPermission =
/* istanbul ignore next */
(cov_2escz9fffg().s[6]++, function () {
  /* istanbul ignore next */
  cov_2escz9fffg().f[0]++;
  var _ref =
  /* istanbul ignore next */
  (cov_2escz9fffg().s[7]++, (0, _asyncToGenerator2.default)(function* () {
    /* istanbul ignore next */
    cov_2escz9fffg().f[1]++;
    cov_2escz9fffg().s[8]++;
    try {
      var granted =
      /* istanbul ignore next */
      (cov_2escz9fffg().s[9]++, yield PermissionsAndroid_1.PermissionsAndroid.request(PermissionsAndroid_1.PermissionsAndroid.PERMISSIONS.READ_CONTACTS, {
        title: 'Allow Access Contact?',
        message: 'allow this app to read contact information',
        buttonNegative: 'Cancel',
        buttonPositive: 'OK'
      }));
      /* istanbul ignore next */
      cov_2escz9fffg().s[10]++;
      if (granted === PermissionsAndroid_1.PermissionsAndroid.RESULTS.GRANTED) {
        /* istanbul ignore next */
        cov_2escz9fffg().b[0][0]++;
        cov_2escz9fffg().s[11]++;
        console.log('granted');
        /* istanbul ignore next */
        cov_2escz9fffg().s[12]++;
        return true;
      } else {
        /* istanbul ignore next */
        cov_2escz9fffg().b[0][1]++;
        cov_2escz9fffg().s[13]++;
        console.log('denied');
        /* istanbul ignore next */
        cov_2escz9fffg().s[14]++;
        return false;
      }
    } catch (err) {
      /* istanbul ignore next */
      cov_2escz9fffg().s[15]++;
      console.warn(err);
      /* istanbul ignore next */
      cov_2escz9fffg().s[16]++;
      return false;
    }
  }));
  /* istanbul ignore next */
  cov_2escz9fffg().s[17]++;
  return function requestContactPermission() {
    /* istanbul ignore next */
    cov_2escz9fffg().f[2]++;
    cov_2escz9fffg().s[18]++;
    return _ref.apply(this, arguments);
  };
}());
/* istanbul ignore next */
cov_2escz9fffg().s[19]++;
exports.requestContactPermission = requestContactPermission;
var getContact =
/* istanbul ignore next */
(cov_2escz9fffg().s[20]++, function () {
  /* istanbul ignore next */
  cov_2escz9fffg().f[3]++;
  var _ref2 =
  /* istanbul ignore next */
  (cov_2escz9fffg().s[21]++, (0, _asyncToGenerator2.default)(function* () {
    /* istanbul ignore next */
    cov_2escz9fffg().f[4]++;
    cov_2escz9fffg().s[22]++;
    return (0, react_native_select_contact_1.selectContactPhone)().then(function (select) {
      /* istanbul ignore next */
      cov_2escz9fffg().f[5]++;
      var _phoneNum$split;
      /* istanbul ignore next */
      cov_2escz9fffg().s[23]++;
      if (!select) {
        /* istanbul ignore next */
        cov_2escz9fffg().b[1][0]++;
        cov_2escz9fffg().s[24]++;
        return null;
      } else
      /* istanbul ignore next */
      {
        cov_2escz9fffg().b[1][1]++;
      }
      var contact =
        /* istanbul ignore next */
        (cov_2escz9fffg().s[25]++, select.contact),
        selectedPhone =
        /* istanbul ignore next */
        (cov_2escz9fffg().s[26]++, select.selectedPhone);
      var phoneNum =
      /* istanbul ignore next */
      (cov_2escz9fffg().s[27]++, selectedPhone == null ?
      /* istanbul ignore next */
      (cov_2escz9fffg().b[2][0]++, void 0) :
      /* istanbul ignore next */
      (cov_2escz9fffg().b[2][1]++, selectedPhone.number));
      var phoneStr =
      /* istanbul ignore next */
      (cov_2escz9fffg().s[28]++,
      /* istanbul ignore next */
      (cov_2escz9fffg().b[4][0]++, phoneNum == null) ||
      /* istanbul ignore next */
      (cov_2escz9fffg().b[4][1]++, (_phoneNum$split = phoneNum.split(' ')) == null) ?
      /* istanbul ignore next */
      (cov_2escz9fffg().b[3][0]++, void 0) :
      /* istanbul ignore next */
      (cov_2escz9fffg().b[3][1]++, _phoneNum$split.join('')));
      /* istanbul ignore next */
      cov_2escz9fffg().s[29]++;
      console.log(`Selected ${selectedPhone.type} phone number ${selectedPhone.number} from ${contact.name}`);
      /* istanbul ignore next */
      cov_2escz9fffg().s[30]++;
      return phoneStr;
    }).catch(function (e) {
      /* istanbul ignore next */
      cov_2escz9fffg().f[6]++;
      cov_2escz9fffg().s[31]++;
      console.log('====================================');
      /* istanbul ignore next */
      cov_2escz9fffg().s[32]++;
      console.log(e);
      /* istanbul ignore next */
      cov_2escz9fffg().s[33]++;
      console.log('====================================');
      /* istanbul ignore next */
      cov_2escz9fffg().s[34]++;
      return undefined;
    });
  }));
  /* istanbul ignore next */
  cov_2escz9fffg().s[35]++;
  return function getContact() {
    /* istanbul ignore next */
    cov_2escz9fffg().f[7]++;
    cov_2escz9fffg().s[36]++;
    return _ref2.apply(this, arguments);
  };
}());
/* istanbul ignore next */
cov_2escz9fffg().s[37]++;
exports.getContact = getContact;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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