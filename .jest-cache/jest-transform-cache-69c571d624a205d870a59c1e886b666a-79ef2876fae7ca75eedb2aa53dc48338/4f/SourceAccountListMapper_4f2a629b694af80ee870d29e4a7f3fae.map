{"version": 3, "names": ["cov_o0bmx03kf", "actualCoverage", "s", "exports", "mapSourceAccountListResponseToModel", "response", "f", "_response$totalCount", "b", "totalCount", "Array", "isArray", "data", "map", "account", "mapSourceAccountResponseToModel", "filter", "_account$userPreferen", "userPreferences", "visible", "mapUserPreferencesResponseToModel", "arrangementId", "alias", "favorite", "additions", "mapProductKindResponseToModel", "externalKindId", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mapProductResponseToModel", "id", "translations", "externalId", "externalTypeId", "typeName", "productKind", "productKindName", "legalEntityIds", "productId", "productTypeName", "externalProductId", "externalArrangementId", "product", "state", "parentId", "subscriptions", "isDefault", "cifNo", "virtualAccountInfos", "name", "bookedBalance", "availableBalance", "creditLimit", "currency", "externalTransferAllowed", "urgentTransferAllowed", "accountOpeningDate", "accountHolderNames", "bankAlias", "BBAN", "IBAN", "BIC"], "sources": ["/Users/<USER>/projects/react-native/payment-module/src/data/mappers/source-account-list/SourceAccountListMapper.ts"], "sourcesContent": ["import {\n  SourceAccountProductKindResponse,\n  SourceAccountProductResponse,\n  SourceAccountListResponse,\n  SourceAccountResponse,\n  SourceAccountUserPreferencesResponse,\n} from '../../models/source-account-list/SourceAccountListResponse';\nimport {\n  SourceAccountProductKindModel,\n  SourceAccountProductModel,\n  SourceAccountListModel,\n  SourceAccountModel,\n  SourceAccountUserPreferencesModel,\n} from '../../../domain/entities/source-account-list/SourceAccountListModel';\n\nexport function mapSourceAccountListResponseToModel(response: SourceAccountListResponse): SourceAccountListModel {\n  if (!response.totalCount || !Array.isArray(response?.data)) {\n    return {totalCount: 0, data: []};\n  }\n  return {\n    totalCount: response.totalCount ?? 0,\n    data: response.data\n      .map(account => mapSourceAccountResponseToModel(account))\n      .filter(account => account?.userPreferences?.visible !== false),\n  };\n}\n\nexport const mapUserPreferencesResponseToModel = (\n  response?: SourceAccountUserPreferencesResponse,\n): SourceAccountUserPreferencesModel => {\n  return {\n    arrangementId: response?.arrangementId,\n    alias: response?.alias,\n    visible: response?.visible,\n    favorite: response?.favorite,\n    additions: response?.additions,\n  };\n};\n\nexport const mapProductKindResponseToModel = (\n  response: SourceAccountProductKindResponse,\n): SourceAccountProductKindModel => {\n  return {\n    externalKindId: response.externalKindId,\n    kindName: response.kindName,\n    kindUri: response.kindUri,\n    expectsChildren: response.expectsChildren,\n    additions: response.additions,\n  };\n};\n\nexport const mapProductResponseToModel = (response: SourceAccountProductResponse): SourceAccountProductModel => {\n  return {\n    id: response.id,\n    translations: response.translations,\n    additions: response.additions,\n    externalId: response.externalId,\n    externalTypeId: response.externalTypeId,\n    typeName: response.typeName,\n    productKind: mapProductKindResponseToModel(response.productKind),\n  };\n};\n\nexport const mapSourceAccountResponseToModel = (response: SourceAccountResponse): SourceAccountModel => {\n  return {\n    id: response.id,\n    productKindName: response.productKindName,\n    legalEntityIds: response.legalEntityIds,\n    productId: response.productId,\n    productTypeName: response.productTypeName,\n    externalProductId: response.externalProductId,\n    externalArrangementId: response.externalArrangementId,\n    userPreferences: mapUserPreferencesResponseToModel(response.userPreferences),\n    product: mapProductResponseToModel(response.product),\n    state: response.state,\n    parentId: response.parentId,\n    subscriptions: response.subscriptions,\n    isDefault: response.isDefault,\n    cifNo: response.cifNo,\n    virtualAccountInfos: response.virtualAccountInfos,\n    additions: response.additions,\n    name: response.name,\n    bookedBalance: response.bookedBalance,\n    availableBalance: response.availableBalance,\n    creditLimit: response.creditLimit,\n    currency: response.currency,\n    externalTransferAllowed: response.externalTransferAllowed,\n    urgentTransferAllowed: response.urgentTransferAllowed,\n    accountOpeningDate: response.accountOpeningDate,\n    accountHolderNames: response.accountHolderNames,\n    bankAlias: response.bankAlias,\n    BBAN: response.BBAN,\n    IBAN: response.IBAN,\n    BIC: response.BIC,\n  };\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAmBS;IAAAA,aAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,aAAA;AAAAA,aAAA,GAAAE,CAAA;;;;;;;;;AAJTC,OAAA,CAAAC,mCAAA,GAAAA,mCAAA;AAAA,SAAgBA,mCAAmCA,CAACC,QAAmC;EAAA;EAAAL,aAAA,GAAAM,CAAA;EAAA,IAAAC,oBAAA;EAAA;EAAAP,aAAA,GAAAE,CAAA;EACrF;EAAI;EAAA,CAAAF,aAAA,GAAAQ,CAAA,WAACH,QAAQ,CAACI,UAAU;EAAA;EAAA,CAAAT,aAAA,GAAAQ,CAAA,UAAI,CAACE,KAAK,CAACC,OAAO,CAACN,QAAQ;EAAA;EAAA,CAAAL,aAAA,GAAAQ,CAAA;EAAA;EAAA,CAAAR,aAAA,GAAAQ,CAAA,UAARH,QAAQ,CAAEO,IAAI,EAAC,GAAE;IAAA;IAAAZ,aAAA,GAAAQ,CAAA;IAAAR,aAAA,GAAAE,CAAA;IAC1D,OAAO;MAACO,UAAU,EAAE,CAAC;MAAEG,IAAI,EAAE;IAAE,CAAC;EAClC;EAAA;EAAA;IAAAZ,aAAA,GAAAQ,CAAA;EAAA;EAAAR,aAAA,GAAAE,CAAA;EACA,OAAO;IACLO,UAAU,GAAAF,oBAAA,GAAEF,QAAQ,CAACI,UAAU;IAAA;IAAA,CAAAT,aAAA,GAAAQ,CAAA,UAAAD,oBAAA;IAAA;IAAA,CAAAP,aAAA,GAAAQ,CAAA,UAAI,CAAC;IACpCI,IAAI,EAAEP,QAAQ,CAACO,IAAI,CAChBC,GAAG,CAAC,UAAAC,OAAO;MAAA;MAAAd,aAAA,GAAAM,CAAA;MAAAN,aAAA,GAAAE,CAAA;MAAA,OAAI,IAAAC,OAAA,CAAAY,+BAA+B,EAACD,OAAO,CAAC;IAAA,EAAC,CACxDE,MAAM,CAAC,UAAAF,OAAO;MAAA;MAAAd,aAAA,GAAAM,CAAA;MAAA,IAAAW,qBAAA;MAAA;MAAAjB,aAAA,GAAAE,CAAA;MAAA,OAAI;MAAA;MAAA,CAAAF,aAAA,GAAAQ,CAAA,UAAAM,OAAO;MAAA;MAAA,CAAAd,aAAA,GAAAQ,CAAA,WAAAS,qBAAA,GAAPH,OAAO,CAAEI,eAAe;MAAA;MAAA,CAAAlB,aAAA,GAAAQ,CAAA;MAAA;MAAA,CAAAR,aAAA,GAAAQ,CAAA,UAAxBS,qBAAA,CAA0BE,OAAO,OAAK,KAAK;IAAA;GACjE;AACH;AAAA;AAAAnB,aAAA,GAAAE,CAAA;AAEO,IAAMkB,iCAAiC,GAAG,SAApCA,iCAAiCA,CAC5Cf,QAA+C,EACV;EAAA;EAAAL,aAAA,GAAAM,CAAA;EAAAN,aAAA,GAAAE,CAAA;EACrC,OAAO;IACLmB,aAAa,EAAEhB,QAAQ;IAAA;IAAA,CAAAL,aAAA,GAAAQ,CAAA;IAAA;IAAA,CAAAR,aAAA,GAAAQ,CAAA,UAARH,QAAQ,CAAEgB,aAAa;IACtCC,KAAK,EAAEjB,QAAQ;IAAA;IAAA,CAAAL,aAAA,GAAAQ,CAAA;IAAA;IAAA,CAAAR,aAAA,GAAAQ,CAAA,UAARH,QAAQ,CAAEiB,KAAK;IACtBH,OAAO,EAAEd,QAAQ;IAAA;IAAA,CAAAL,aAAA,GAAAQ,CAAA;IAAA;IAAA,CAAAR,aAAA,GAAAQ,CAAA,UAARH,QAAQ,CAAEc,OAAO;IAC1BI,QAAQ,EAAElB,QAAQ;IAAA;IAAA,CAAAL,aAAA,GAAAQ,CAAA;IAAA;IAAA,CAAAR,aAAA,GAAAQ,CAAA,UAARH,QAAQ,CAAEkB,QAAQ;IAC5BC,SAAS,EAAEnB,QAAQ;IAAA;IAAA,CAAAL,aAAA,GAAAQ,CAAA;IAAA;IAAA,CAAAR,aAAA,GAAAQ,CAAA,WAARH,QAAQ,CAAEmB,SAAA;GACtB;AACH,CAAC;AAAA;AAAAxB,aAAA,GAAAE,CAAA;AAVYC,OAAA,CAAAiB,iCAAiC,GAAAA,iCAAA;AAAA;AAAApB,aAAA,GAAAE,CAAA;AAYvC,IAAMuB,6BAA6B,GAAG,SAAhCA,6BAA6BA,CACxCpB,QAA0C,EACT;EAAA;EAAAL,aAAA,GAAAM,CAAA;EAAAN,aAAA,GAAAE,CAAA;EACjC,OAAO;IACLwB,cAAc,EAAErB,QAAQ,CAACqB,cAAc;IACvCC,QAAQ,EAAEtB,QAAQ,CAACsB,QAAQ;IAC3BC,OAAO,EAAEvB,QAAQ,CAACuB,OAAO;IACzBC,eAAe,EAAExB,QAAQ,CAACwB,eAAe;IACzCL,SAAS,EAAEnB,QAAQ,CAACmB;GACrB;AACH,CAAC;AAAA;AAAAxB,aAAA,GAAAE,CAAA;AAVYC,OAAA,CAAAsB,6BAA6B,GAAAA,6BAAA;AAAA;AAAAzB,aAAA,GAAAE,CAAA;AAYnC,IAAM4B,yBAAyB,GAAG,SAA5BA,yBAAyBA,CAAIzB,QAAsC,EAA+B;EAAA;EAAAL,aAAA,GAAAM,CAAA;EAAAN,aAAA,GAAAE,CAAA;EAC7G,OAAO;IACL6B,EAAE,EAAE1B,QAAQ,CAAC0B,EAAE;IACfC,YAAY,EAAE3B,QAAQ,CAAC2B,YAAY;IACnCR,SAAS,EAAEnB,QAAQ,CAACmB,SAAS;IAC7BS,UAAU,EAAE5B,QAAQ,CAAC4B,UAAU;IAC/BC,cAAc,EAAE7B,QAAQ,CAAC6B,cAAc;IACvCC,QAAQ,EAAE9B,QAAQ,CAAC8B,QAAQ;IAC3BC,WAAW,EAAE,IAAAjC,OAAA,CAAAsB,6BAA6B,EAACpB,QAAQ,CAAC+B,WAAW;GAChE;AACH,CAAC;AAAA;AAAApC,aAAA,GAAAE,CAAA;AAVYC,OAAA,CAAA2B,yBAAyB,GAAAA,yBAAA;AAAA;AAAA9B,aAAA,GAAAE,CAAA;AAY/B,IAAMa,+BAA+B,GAAG,SAAlCA,+BAA+BA,CAAIV,QAA+B,EAAwB;EAAA;EAAAL,aAAA,GAAAM,CAAA;EAAAN,aAAA,GAAAE,CAAA;EACrG,OAAO;IACL6B,EAAE,EAAE1B,QAAQ,CAAC0B,EAAE;IACfM,eAAe,EAAEhC,QAAQ,CAACgC,eAAe;IACzCC,cAAc,EAAEjC,QAAQ,CAACiC,cAAc;IACvCC,SAAS,EAAElC,QAAQ,CAACkC,SAAS;IAC7BC,eAAe,EAAEnC,QAAQ,CAACmC,eAAe;IACzCC,iBAAiB,EAAEpC,QAAQ,CAACoC,iBAAiB;IAC7CC,qBAAqB,EAAErC,QAAQ,CAACqC,qBAAqB;IACrDxB,eAAe,EAAE,IAAAf,OAAA,CAAAiB,iCAAiC,EAACf,QAAQ,CAACa,eAAe,CAAC;IAC5EyB,OAAO,EAAE,IAAAxC,OAAA,CAAA2B,yBAAyB,EAACzB,QAAQ,CAACsC,OAAO,CAAC;IACpDC,KAAK,EAAEvC,QAAQ,CAACuC,KAAK;IACrBC,QAAQ,EAAExC,QAAQ,CAACwC,QAAQ;IAC3BC,aAAa,EAAEzC,QAAQ,CAACyC,aAAa;IACrCC,SAAS,EAAE1C,QAAQ,CAAC0C,SAAS;IAC7BC,KAAK,EAAE3C,QAAQ,CAAC2C,KAAK;IACrBC,mBAAmB,EAAE5C,QAAQ,CAAC4C,mBAAmB;IACjDzB,SAAS,EAAEnB,QAAQ,CAACmB,SAAS;IAC7B0B,IAAI,EAAE7C,QAAQ,CAAC6C,IAAI;IACnBC,aAAa,EAAE9C,QAAQ,CAAC8C,aAAa;IACrCC,gBAAgB,EAAE/C,QAAQ,CAAC+C,gBAAgB;IAC3CC,WAAW,EAAEhD,QAAQ,CAACgD,WAAW;IACjCC,QAAQ,EAAEjD,QAAQ,CAACiD,QAAQ;IAC3BC,uBAAuB,EAAElD,QAAQ,CAACkD,uBAAuB;IACzDC,qBAAqB,EAAEnD,QAAQ,CAACmD,qBAAqB;IACrDC,kBAAkB,EAAEpD,QAAQ,CAACoD,kBAAkB;IAC/CC,kBAAkB,EAAErD,QAAQ,CAACqD,kBAAkB;IAC/CC,SAAS,EAAEtD,QAAQ,CAACsD,SAAS;IAC7BC,IAAI,EAAEvD,QAAQ,CAACuD,IAAI;IACnBC,IAAI,EAAExD,QAAQ,CAACwD,IAAI;IACnBC,GAAG,EAAEzD,QAAQ,CAACyD;GACf;AACH,CAAC;AAAA;AAAA9D,aAAA,GAAAE,CAAA;AAhCYC,OAAA,CAAAY,+BAA+B,GAAAA,+BAAA", "ignoreList": []}