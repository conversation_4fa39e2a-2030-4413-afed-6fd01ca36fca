112a91def1f89bcc71940686146caab5
"use strict";

/* istanbul ignore next */
function cov_o0bmx03kf() {
  var path = "/Users/<USER>/projects/react-native/payment-module/src/data/mappers/source-account-list/SourceAccountListMapper.ts";
  var hash = "320f04d471f697500c0e8ddd57730fb1820ed0e3";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/projects/react-native/payment-module/src/data/mappers/source-account-list/SourceAccountListMapper.ts",
    statementMap: {
      "0": {
        start: {
          line: 3,
          column: 0
        },
        end: {
          line: 5,
          column: 3
        }
      },
      "1": {
        start: {
          line: 6,
          column: 0
        },
        end: {
          line: 6,
          column: 169
        }
      },
      "2": {
        start: {
          line: 7,
          column: 0
        },
        end: {
          line: 7,
          column: 82
        }
      },
      "3": {
        start: {
          line: 10,
          column: 2
        },
        end: {
          line: 15,
          column: 3
        }
      },
      "4": {
        start: {
          line: 11,
          column: 4
        },
        end: {
          line: 14,
          column: 6
        }
      },
      "5": {
        start: {
          line: 16,
          column: 2
        },
        end: {
          line: 24,
          column: 4
        }
      },
      "6": {
        start: {
          line: 19,
          column: 6
        },
        end: {
          line: 19,
          column: 67
        }
      },
      "7": {
        start: {
          line: 22,
          column: 6
        },
        end: {
          line: 22,
          column: 143
        }
      },
      "8": {
        start: {
          line: 26,
          column: 40
        },
        end: {
          line: 34,
          column: 1
        }
      },
      "9": {
        start: {
          line: 27,
          column: 2
        },
        end: {
          line: 33,
          column: 4
        }
      },
      "10": {
        start: {
          line: 35,
          column: 0
        },
        end: {
          line: 35,
          column: 78
        }
      },
      "11": {
        start: {
          line: 36,
          column: 36
        },
        end: {
          line: 44,
          column: 1
        }
      },
      "12": {
        start: {
          line: 37,
          column: 2
        },
        end: {
          line: 43,
          column: 4
        }
      },
      "13": {
        start: {
          line: 45,
          column: 0
        },
        end: {
          line: 45,
          column: 70
        }
      },
      "14": {
        start: {
          line: 46,
          column: 32
        },
        end: {
          line: 56,
          column: 1
        }
      },
      "15": {
        start: {
          line: 47,
          column: 2
        },
        end: {
          line: 55,
          column: 4
        }
      },
      "16": {
        start: {
          line: 57,
          column: 0
        },
        end: {
          line: 57,
          column: 62
        }
      },
      "17": {
        start: {
          line: 58,
          column: 38
        },
        end: {
          line: 90,
          column: 1
        }
      },
      "18": {
        start: {
          line: 59,
          column: 2
        },
        end: {
          line: 89,
          column: 4
        }
      },
      "19": {
        start: {
          line: 91,
          column: 0
        },
        end: {
          line: 91,
          column: 74
        }
      }
    },
    fnMap: {
      "0": {
        name: "mapSourceAccountListResponseToModel",
        decl: {
          start: {
            line: 8,
            column: 9
          },
          end: {
            line: 8,
            column: 44
          }
        },
        loc: {
          start: {
            line: 8,
            column: 55
          },
          end: {
            line: 25,
            column: 1
          }
        },
        line: 8
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 18,
            column: 28
          },
          end: {
            line: 18,
            column: 29
          }
        },
        loc: {
          start: {
            line: 18,
            column: 47
          },
          end: {
            line: 20,
            column: 5
          }
        },
        line: 18
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 20,
            column: 14
          },
          end: {
            line: 20,
            column: 15
          }
        },
        loc: {
          start: {
            line: 20,
            column: 33
          },
          end: {
            line: 23,
            column: 5
          }
        },
        line: 20
      },
      "3": {
        name: "mapUserPreferencesResponseToModel",
        decl: {
          start: {
            line: 26,
            column: 49
          },
          end: {
            line: 26,
            column: 82
          }
        },
        loc: {
          start: {
            line: 26,
            column: 93
          },
          end: {
            line: 34,
            column: 1
          }
        },
        line: 26
      },
      "4": {
        name: "mapProductKindResponseToModel",
        decl: {
          start: {
            line: 36,
            column: 45
          },
          end: {
            line: 36,
            column: 74
          }
        },
        loc: {
          start: {
            line: 36,
            column: 85
          },
          end: {
            line: 44,
            column: 1
          }
        },
        line: 36
      },
      "5": {
        name: "mapProductResponseToModel",
        decl: {
          start: {
            line: 46,
            column: 41
          },
          end: {
            line: 46,
            column: 66
          }
        },
        loc: {
          start: {
            line: 46,
            column: 77
          },
          end: {
            line: 56,
            column: 1
          }
        },
        line: 46
      },
      "6": {
        name: "mapSourceAccountResponseToModel",
        decl: {
          start: {
            line: 58,
            column: 47
          },
          end: {
            line: 58,
            column: 78
          }
        },
        loc: {
          start: {
            line: 58,
            column: 89
          },
          end: {
            line: 90,
            column: 1
          }
        },
        line: 58
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 10,
            column: 2
          },
          end: {
            line: 15,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 10,
            column: 2
          },
          end: {
            line: 15,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 10
      },
      "1": {
        loc: {
          start: {
            line: 10,
            column: 6
          },
          end: {
            line: 10,
            column: 87
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 10,
            column: 6
          },
          end: {
            line: 10,
            column: 26
          }
        }, {
          start: {
            line: 10,
            column: 30
          },
          end: {
            line: 10,
            column: 87
          }
        }],
        line: 10
      },
      "2": {
        loc: {
          start: {
            line: 10,
            column: 45
          },
          end: {
            line: 10,
            column: 86
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 10,
            column: 64
          },
          end: {
            line: 10,
            column: 70
          }
        }, {
          start: {
            line: 10,
            column: 73
          },
          end: {
            line: 10,
            column: 86
          }
        }],
        line: 10
      },
      "3": {
        loc: {
          start: {
            line: 17,
            column: 16
          },
          end: {
            line: 17,
            column: 95
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 17,
            column: 71
          },
          end: {
            line: 17,
            column: 91
          }
        }, {
          start: {
            line: 17,
            column: 94
          },
          end: {
            line: 17,
            column: 95
          }
        }],
        line: 17
      },
      "4": {
        loc: {
          start: {
            line: 22,
            column: 14
          },
          end: {
            line: 22,
            column: 131
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 22,
            column: 93
          },
          end: {
            line: 22,
            column: 99
          }
        }, {
          start: {
            line: 22,
            column: 102
          },
          end: {
            line: 22,
            column: 131
          }
        }],
        line: 22
      },
      "5": {
        loc: {
          start: {
            line: 22,
            column: 14
          },
          end: {
            line: 22,
            column: 90
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 22,
            column: 14
          },
          end: {
            line: 22,
            column: 29
          }
        }, {
          start: {
            line: 22,
            column: 33
          },
          end: {
            line: 22,
            column: 90
          }
        }],
        line: 22
      },
      "6": {
        loc: {
          start: {
            line: 28,
            column: 19
          },
          end: {
            line: 28,
            column: 69
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 28,
            column: 38
          },
          end: {
            line: 28,
            column: 44
          }
        }, {
          start: {
            line: 28,
            column: 47
          },
          end: {
            line: 28,
            column: 69
          }
        }],
        line: 28
      },
      "7": {
        loc: {
          start: {
            line: 29,
            column: 11
          },
          end: {
            line: 29,
            column: 53
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 29,
            column: 30
          },
          end: {
            line: 29,
            column: 36
          }
        }, {
          start: {
            line: 29,
            column: 39
          },
          end: {
            line: 29,
            column: 53
          }
        }],
        line: 29
      },
      "8": {
        loc: {
          start: {
            line: 30,
            column: 13
          },
          end: {
            line: 30,
            column: 57
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 30,
            column: 32
          },
          end: {
            line: 30,
            column: 38
          }
        }, {
          start: {
            line: 30,
            column: 41
          },
          end: {
            line: 30,
            column: 57
          }
        }],
        line: 30
      },
      "9": {
        loc: {
          start: {
            line: 31,
            column: 14
          },
          end: {
            line: 31,
            column: 59
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 31,
            column: 33
          },
          end: {
            line: 31,
            column: 39
          }
        }, {
          start: {
            line: 31,
            column: 42
          },
          end: {
            line: 31,
            column: 59
          }
        }],
        line: 31
      },
      "10": {
        loc: {
          start: {
            line: 32,
            column: 15
          },
          end: {
            line: 32,
            column: 61
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 32,
            column: 34
          },
          end: {
            line: 32,
            column: 40
          }
        }, {
          start: {
            line: 32,
            column: 43
          },
          end: {
            line: 32,
            column: 61
          }
        }],
        line: 32
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0
    },
    b: {
      "0": [0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0]
    },
    inputSourceMap: {
      version: 3,
      file: undefined,
      names: ["exports", "mapSourceAccountListResponseToModel", "response", "_response$totalCount", "totalCount", "Array", "isArray", "data", "map", "account", "mapSourceAccountResponseToModel", "filter", "_account$userPreferen", "userPreferences", "visible", "mapUserPreferencesResponseToModel", "arrangementId", "alias", "favorite", "additions", "mapProductKindResponseToModel", "externalKindId", "kindName", "kindUri", "expectsChildren", "mapProductResponseToModel", "id", "translations", "externalId", "externalTypeId", "typeName", "productKind", "productKindName", "legalEntityIds", "productId", "productTypeName", "externalProductId", "externalArrangementId", "product", "state", "parentId", "subscriptions", "isDefault", "cifNo", "virtualAccountInfos", "name", "bookedBalance", "availableBalance", "creditLimit", "currency", "externalTransferAllowed", "urgentTransferAllowed", "accountOpeningDate", "accountHolderNames", "bankAlias", "BBAN", "IBAN", "BIC"],
      sourceRoot: undefined,
      sources: ["/Users/<USER>/projects/react-native/payment-module/src/data/mappers/source-account-list/SourceAccountListMapper.ts"],
      sourcesContent: ["import {\n  SourceAccountProductKindResponse,\n  SourceAccountProductResponse,\n  SourceAccountListResponse,\n  SourceAccountResponse,\n  SourceAccountUserPreferencesResponse,\n} from '../../models/source-account-list/SourceAccountListResponse';\nimport {\n  SourceAccountProductKindModel,\n  SourceAccountProductModel,\n  SourceAccountListModel,\n  SourceAccountModel,\n  SourceAccountUserPreferencesModel,\n} from '../../../domain/entities/source-account-list/SourceAccountListModel';\n\nexport function mapSourceAccountListResponseToModel(response: SourceAccountListResponse): SourceAccountListModel {\n  if (!response.totalCount || !Array.isArray(response?.data)) {\n    return {totalCount: 0, data: []};\n  }\n  return {\n    totalCount: response.totalCount ?? 0,\n    data: response.data\n      .map(account => mapSourceAccountResponseToModel(account))\n      .filter(account => account?.userPreferences?.visible !== false),\n  };\n}\n\nexport const mapUserPreferencesResponseToModel = (\n  response?: SourceAccountUserPreferencesResponse,\n): SourceAccountUserPreferencesModel => {\n  return {\n    arrangementId: response?.arrangementId,\n    alias: response?.alias,\n    visible: response?.visible,\n    favorite: response?.favorite,\n    additions: response?.additions,\n  };\n};\n\nexport const mapProductKindResponseToModel = (\n  response: SourceAccountProductKindResponse,\n): SourceAccountProductKindModel => {\n  return {\n    externalKindId: response.externalKindId,\n    kindName: response.kindName,\n    kindUri: response.kindUri,\n    expectsChildren: response.expectsChildren,\n    additions: response.additions,\n  };\n};\n\nexport const mapProductResponseToModel = (response: SourceAccountProductResponse): SourceAccountProductModel => {\n  return {\n    id: response.id,\n    translations: response.translations,\n    additions: response.additions,\n    externalId: response.externalId,\n    externalTypeId: response.externalTypeId,\n    typeName: response.typeName,\n    productKind: mapProductKindResponseToModel(response.productKind),\n  };\n};\n\nexport const mapSourceAccountResponseToModel = (response: SourceAccountResponse): SourceAccountModel => {\n  return {\n    id: response.id,\n    productKindName: response.productKindName,\n    legalEntityIds: response.legalEntityIds,\n    productId: response.productId,\n    productTypeName: response.productTypeName,\n    externalProductId: response.externalProductId,\n    externalArrangementId: response.externalArrangementId,\n    userPreferences: mapUserPreferencesResponseToModel(response.userPreferences),\n    product: mapProductResponseToModel(response.product),\n    state: response.state,\n    parentId: response.parentId,\n    subscriptions: response.subscriptions,\n    isDefault: response.isDefault,\n    cifNo: response.cifNo,\n    virtualAccountInfos: response.virtualAccountInfos,\n    additions: response.additions,\n    name: response.name,\n    bookedBalance: response.bookedBalance,\n    availableBalance: response.availableBalance,\n    creditLimit: response.creditLimit,\n    currency: response.currency,\n    externalTransferAllowed: response.externalTransferAllowed,\n    urgentTransferAllowed: response.urgentTransferAllowed,\n    accountOpeningDate: response.accountOpeningDate,\n    accountHolderNames: response.accountHolderNames,\n    bankAlias: response.bankAlias,\n    BBAN: response.BBAN,\n    IBAN: response.IBAN,\n    BIC: response.BIC,\n  };\n};\n"],
      mappings: ";;;;;;AAeAA,OAAA,CAAAC,mCAAA,GAAAA,mCAAA;AAAA,SAAgBA,mCAAmCA,CAACC,QAAmC;EAAA,IAAAC,oBAAA;EACrF,IAAI,CAACD,QAAQ,CAACE,UAAU,IAAI,CAACC,KAAK,CAACC,OAAO,CAACJ,QAAQ,oBAARA,QAAQ,CAAEK,IAAI,CAAC,EAAE;IAC1D,OAAO;MAACH,UAAU,EAAE,CAAC;MAAEG,IAAI,EAAE;IAAE,CAAC;EAClC;EACA,OAAO;IACLH,UAAU,GAAAD,oBAAA,GAAED,QAAQ,CAACE,UAAU,YAAAD,oBAAA,GAAI,CAAC;IACpCI,IAAI,EAAEL,QAAQ,CAACK,IAAI,CAChBC,GAAG,CAAC,UAAAC,OAAO;MAAA,OAAI,IAAAT,OAAA,CAAAU,+BAA+B,EAACD,OAAO,CAAC;IAAA,EAAC,CACxDE,MAAM,CAAC,UAAAF,OAAO;MAAA,IAAAG,qBAAA;MAAA,OAAI,CAAAH,OAAO,aAAAG,qBAAA,GAAPH,OAAO,CAAEI,eAAe,qBAAxBD,qBAAA,CAA0BE,OAAO,MAAK,KAAK;IAAA;GACjE;AACH;AAEO,IAAMC,iCAAiC,GAAG,SAApCA,iCAAiCA,CAC5Cb,QAA+C,EACV;EACrC,OAAO;IACLc,aAAa,EAAEd,QAAQ,oBAARA,QAAQ,CAAEc,aAAa;IACtCC,KAAK,EAAEf,QAAQ,oBAARA,QAAQ,CAAEe,KAAK;IACtBH,OAAO,EAAEZ,QAAQ,oBAARA,QAAQ,CAAEY,OAAO;IAC1BI,QAAQ,EAAEhB,QAAQ,oBAARA,QAAQ,CAAEgB,QAAQ;IAC5BC,SAAS,EAAEjB,QAAQ,oBAARA,QAAQ,CAAEiB;GACtB;AACH,CAAC;AAVYnB,OAAA,CAAAe,iCAAiC,GAAAA,iCAAA;AAYvC,IAAMK,6BAA6B,GAAG,SAAhCA,6BAA6BA,CACxClB,QAA0C,EACT;EACjC,OAAO;IACLmB,cAAc,EAAEnB,QAAQ,CAACmB,cAAc;IACvCC,QAAQ,EAAEpB,QAAQ,CAACoB,QAAQ;IAC3BC,OAAO,EAAErB,QAAQ,CAACqB,OAAO;IACzBC,eAAe,EAAEtB,QAAQ,CAACsB,eAAe;IACzCL,SAAS,EAAEjB,QAAQ,CAACiB;GACrB;AACH,CAAC;AAVYnB,OAAA,CAAAoB,6BAA6B,GAAAA,6BAAA;AAYnC,IAAMK,yBAAyB,GAAG,SAA5BA,yBAAyBA,CAAIvB,QAAsC,EAA+B;EAC7G,OAAO;IACLwB,EAAE,EAAExB,QAAQ,CAACwB,EAAE;IACfC,YAAY,EAAEzB,QAAQ,CAACyB,YAAY;IACnCR,SAAS,EAAEjB,QAAQ,CAACiB,SAAS;IAC7BS,UAAU,EAAE1B,QAAQ,CAAC0B,UAAU;IAC/BC,cAAc,EAAE3B,QAAQ,CAAC2B,cAAc;IACvCC,QAAQ,EAAE5B,QAAQ,CAAC4B,QAAQ;IAC3BC,WAAW,EAAE,IAAA/B,OAAA,CAAAoB,6BAA6B,EAAClB,QAAQ,CAAC6B,WAAW;GAChE;AACH,CAAC;AAVY/B,OAAA,CAAAyB,yBAAyB,GAAAA,yBAAA;AAY/B,IAAMf,+BAA+B,GAAG,SAAlCA,+BAA+BA,CAAIR,QAA+B,EAAwB;EACrG,OAAO;IACLwB,EAAE,EAAExB,QAAQ,CAACwB,EAAE;IACfM,eAAe,EAAE9B,QAAQ,CAAC8B,eAAe;IACzCC,cAAc,EAAE/B,QAAQ,CAAC+B,cAAc;IACvCC,SAAS,EAAEhC,QAAQ,CAACgC,SAAS;IAC7BC,eAAe,EAAEjC,QAAQ,CAACiC,eAAe;IACzCC,iBAAiB,EAAElC,QAAQ,CAACkC,iBAAiB;IAC7CC,qBAAqB,EAAEnC,QAAQ,CAACmC,qBAAqB;IACrDxB,eAAe,EAAE,IAAAb,OAAA,CAAAe,iCAAiC,EAACb,QAAQ,CAACW,eAAe,CAAC;IAC5EyB,OAAO,EAAE,IAAAtC,OAAA,CAAAyB,yBAAyB,EAACvB,QAAQ,CAACoC,OAAO,CAAC;IACpDC,KAAK,EAAErC,QAAQ,CAACqC,KAAK;IACrBC,QAAQ,EAAEtC,QAAQ,CAACsC,QAAQ;IAC3BC,aAAa,EAAEvC,QAAQ,CAACuC,aAAa;IACrCC,SAAS,EAAExC,QAAQ,CAACwC,SAAS;IAC7BC,KAAK,EAAEzC,QAAQ,CAACyC,KAAK;IACrBC,mBAAmB,EAAE1C,QAAQ,CAAC0C,mBAAmB;IACjDzB,SAAS,EAAEjB,QAAQ,CAACiB,SAAS;IAC7B0B,IAAI,EAAE3C,QAAQ,CAAC2C,IAAI;IACnBC,aAAa,EAAE5C,QAAQ,CAAC4C,aAAa;IACrCC,gBAAgB,EAAE7C,QAAQ,CAAC6C,gBAAgB;IAC3CC,WAAW,EAAE9C,QAAQ,CAAC8C,WAAW;IACjCC,QAAQ,EAAE/C,QAAQ,CAAC+C,QAAQ;IAC3BC,uBAAuB,EAAEhD,QAAQ,CAACgD,uBAAuB;IACzDC,qBAAqB,EAAEjD,QAAQ,CAACiD,qBAAqB;IACrDC,kBAAkB,EAAElD,QAAQ,CAACkD,kBAAkB;IAC/CC,kBAAkB,EAAEnD,QAAQ,CAACmD,kBAAkB;IAC/CC,SAAS,EAAEpD,QAAQ,CAACoD,SAAS;IAC7BC,IAAI,EAAErD,QAAQ,CAACqD,IAAI;IACnBC,IAAI,EAAEtD,QAAQ,CAACsD,IAAI;IACnBC,GAAG,EAAEvD,QAAQ,CAACuD;GACf;AACH,CAAC;AAhCYzD,OAAA,CAAAU,+BAA+B,GAAAA,+BAAA",
      ignoreList: []
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "320f04d471f697500c0e8ddd57730fb1820ed0e3"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_o0bmx03kf = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_o0bmx03kf();
cov_o0bmx03kf().s[0]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_o0bmx03kf().s[1]++;
exports.mapSourceAccountResponseToModel = exports.mapProductResponseToModel = exports.mapProductKindResponseToModel = exports.mapUserPreferencesResponseToModel = void 0;
/* istanbul ignore next */
cov_o0bmx03kf().s[2]++;
exports.mapSourceAccountListResponseToModel = mapSourceAccountListResponseToModel;
function mapSourceAccountListResponseToModel(response) {
  /* istanbul ignore next */
  cov_o0bmx03kf().f[0]++;
  var _response$totalCount;
  /* istanbul ignore next */
  cov_o0bmx03kf().s[3]++;
  if (
  /* istanbul ignore next */
  (cov_o0bmx03kf().b[1][0]++, !response.totalCount) ||
  /* istanbul ignore next */
  (cov_o0bmx03kf().b[1][1]++, !Array.isArray(response == null ?
  /* istanbul ignore next */
  (cov_o0bmx03kf().b[2][0]++, void 0) :
  /* istanbul ignore next */
  (cov_o0bmx03kf().b[2][1]++, response.data)))) {
    /* istanbul ignore next */
    cov_o0bmx03kf().b[0][0]++;
    cov_o0bmx03kf().s[4]++;
    return {
      totalCount: 0,
      data: []
    };
  } else
  /* istanbul ignore next */
  {
    cov_o0bmx03kf().b[0][1]++;
  }
  cov_o0bmx03kf().s[5]++;
  return {
    totalCount: (_response$totalCount = response.totalCount) != null ?
    /* istanbul ignore next */
    (cov_o0bmx03kf().b[3][0]++, _response$totalCount) :
    /* istanbul ignore next */
    (cov_o0bmx03kf().b[3][1]++, 0),
    data: response.data.map(function (account) {
      /* istanbul ignore next */
      cov_o0bmx03kf().f[1]++;
      cov_o0bmx03kf().s[6]++;
      return (0, exports.mapSourceAccountResponseToModel)(account);
    }).filter(function (account) {
      /* istanbul ignore next */
      cov_o0bmx03kf().f[2]++;
      var _account$userPreferen;
      /* istanbul ignore next */
      cov_o0bmx03kf().s[7]++;
      return (
      /* istanbul ignore next */
      (cov_o0bmx03kf().b[5][0]++, account == null) ||
      /* istanbul ignore next */
      (cov_o0bmx03kf().b[5][1]++, (_account$userPreferen = account.userPreferences) == null) ?
      /* istanbul ignore next */
      (cov_o0bmx03kf().b[4][0]++, void 0) :
      /* istanbul ignore next */
      (cov_o0bmx03kf().b[4][1]++, _account$userPreferen.visible)) !== false;
    })
  };
}
/* istanbul ignore next */
cov_o0bmx03kf().s[8]++;
var mapUserPreferencesResponseToModel = function mapUserPreferencesResponseToModel(response) {
  /* istanbul ignore next */
  cov_o0bmx03kf().f[3]++;
  cov_o0bmx03kf().s[9]++;
  return {
    arrangementId: response == null ?
    /* istanbul ignore next */
    (cov_o0bmx03kf().b[6][0]++, void 0) :
    /* istanbul ignore next */
    (cov_o0bmx03kf().b[6][1]++, response.arrangementId),
    alias: response == null ?
    /* istanbul ignore next */
    (cov_o0bmx03kf().b[7][0]++, void 0) :
    /* istanbul ignore next */
    (cov_o0bmx03kf().b[7][1]++, response.alias),
    visible: response == null ?
    /* istanbul ignore next */
    (cov_o0bmx03kf().b[8][0]++, void 0) :
    /* istanbul ignore next */
    (cov_o0bmx03kf().b[8][1]++, response.visible),
    favorite: response == null ?
    /* istanbul ignore next */
    (cov_o0bmx03kf().b[9][0]++, void 0) :
    /* istanbul ignore next */
    (cov_o0bmx03kf().b[9][1]++, response.favorite),
    additions: response == null ?
    /* istanbul ignore next */
    (cov_o0bmx03kf().b[10][0]++, void 0) :
    /* istanbul ignore next */
    (cov_o0bmx03kf().b[10][1]++, response.additions)
  };
};
/* istanbul ignore next */
cov_o0bmx03kf().s[10]++;
exports.mapUserPreferencesResponseToModel = mapUserPreferencesResponseToModel;
/* istanbul ignore next */
cov_o0bmx03kf().s[11]++;
var mapProductKindResponseToModel = function mapProductKindResponseToModel(response) {
  /* istanbul ignore next */
  cov_o0bmx03kf().f[4]++;
  cov_o0bmx03kf().s[12]++;
  return {
    externalKindId: response.externalKindId,
    kindName: response.kindName,
    kindUri: response.kindUri,
    expectsChildren: response.expectsChildren,
    additions: response.additions
  };
};
/* istanbul ignore next */
cov_o0bmx03kf().s[13]++;
exports.mapProductKindResponseToModel = mapProductKindResponseToModel;
/* istanbul ignore next */
cov_o0bmx03kf().s[14]++;
var mapProductResponseToModel = function mapProductResponseToModel(response) {
  /* istanbul ignore next */
  cov_o0bmx03kf().f[5]++;
  cov_o0bmx03kf().s[15]++;
  return {
    id: response.id,
    translations: response.translations,
    additions: response.additions,
    externalId: response.externalId,
    externalTypeId: response.externalTypeId,
    typeName: response.typeName,
    productKind: (0, exports.mapProductKindResponseToModel)(response.productKind)
  };
};
/* istanbul ignore next */
cov_o0bmx03kf().s[16]++;
exports.mapProductResponseToModel = mapProductResponseToModel;
/* istanbul ignore next */
cov_o0bmx03kf().s[17]++;
var mapSourceAccountResponseToModel = function mapSourceAccountResponseToModel(response) {
  /* istanbul ignore next */
  cov_o0bmx03kf().f[6]++;
  cov_o0bmx03kf().s[18]++;
  return {
    id: response.id,
    productKindName: response.productKindName,
    legalEntityIds: response.legalEntityIds,
    productId: response.productId,
    productTypeName: response.productTypeName,
    externalProductId: response.externalProductId,
    externalArrangementId: response.externalArrangementId,
    userPreferences: (0, exports.mapUserPreferencesResponseToModel)(response.userPreferences),
    product: (0, exports.mapProductResponseToModel)(response.product),
    state: response.state,
    parentId: response.parentId,
    subscriptions: response.subscriptions,
    isDefault: response.isDefault,
    cifNo: response.cifNo,
    virtualAccountInfos: response.virtualAccountInfos,
    additions: response.additions,
    name: response.name,
    bookedBalance: response.bookedBalance,
    availableBalance: response.availableBalance,
    creditLimit: response.creditLimit,
    currency: response.currency,
    externalTransferAllowed: response.externalTransferAllowed,
    urgentTransferAllowed: response.urgentTransferAllowed,
    accountOpeningDate: response.accountOpeningDate,
    accountHolderNames: response.accountHolderNames,
    bankAlias: response.bankAlias,
    BBAN: response.BBAN,
    IBAN: response.IBAN,
    BIC: response.BIC
  };
};
/* istanbul ignore next */
cov_o0bmx03kf().s[19]++;
exports.mapSourceAccountResponseToModel = mapSourceAccountResponseToModel;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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