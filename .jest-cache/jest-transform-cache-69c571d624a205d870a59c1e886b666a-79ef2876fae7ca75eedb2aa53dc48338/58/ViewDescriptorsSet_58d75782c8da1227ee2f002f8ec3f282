e13d9d7badaf119ebbbb9b369c953cef
'use strict';

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.makeViewDescriptorsSet = makeViewDescriptorsSet;
var _core = require("./core.js");
function makeViewDescriptorsSet() {
  var shareableViewDescriptors = (0, _core.makeMutable)([]);
  var data = {
    shareableViewDescriptors: shareableViewDescriptors,
    add: function add(item) {
      shareableViewDescriptors.modify(function (descriptors) {
        'worklet';

        var index = descriptors.findIndex(function (descriptor) {
          return descriptor.tag === item.tag;
        });
        if (index !== -1) {
          descriptors[index] = item;
        } else {
          descriptors.push(item);
        }
        return descriptors;
      }, false);
    },
    remove: function remove(viewTag) {
      shareableViewDescriptors.modify(function (descriptors) {
        'worklet';

        var index = descriptors.findIndex(function (descriptor) {
          return descriptor.tag === viewTag;
        });
        if (index !== -1) {
          descriptors.splice(index, 1);
        }
        return descriptors;
      }, false);
    }
  };
  return data;
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJPYmplY3QiLCJkZWZpbmVQcm9wZXJ0eSIsImV4cG9ydHMiLCJ2YWx1ZSIsIm1ha2VWaWV3RGVzY3JpcHRvcnNTZXQiLCJfY29yZSIsInJlcXVpcmUiLCJzaGFyZWFibGVWaWV3RGVzY3JpcHRvcnMiLCJtYWtlTXV0YWJsZSIsImRhdGEiLCJhZGQiLCJpdGVtIiwibW9kaWZ5IiwiZGVzY3JpcHRvcnMiLCJpbmRleCIsImZpbmRJbmRleCIsImRlc2NyaXB0b3IiLCJ0YWciLCJwdXNoIiwicmVtb3ZlIiwidmlld1RhZyIsInNwbGljZSJdLCJzb3VyY2VzIjpbIi4uLy4uL3NyYy9WaWV3RGVzY3JpcHRvcnNTZXQudHMiXSwic291cmNlc0NvbnRlbnQiOltudWxsXSwibWFwcGluZ3MiOiJBQUFBLFlBQVk7O0FBQUFBLE1BQUEsQ0FBQUMsY0FBQSxDQUFBQyxPQUFBO0VBQUFDLEtBQUE7QUFBQTtBQUFBRCxPQUFBLENBQUFFLHNCQUFBLEdBQUFBLHNCQUFBO0FBQ1osSUFBQUMsS0FBQSxHQUFBQyxPQUFBO0FBVU8sU0FBU0Ysc0JBQXNCQSxDQUFBLEVBQXVCO0VBQzNELElBQU1HLHdCQUF3QixHQUFHLElBQUFDLGlCQUFXLEVBQWUsRUFBRSxDQUFDO0VBQzlELElBQU1DLElBQXdCLEdBQUc7SUFDL0JGLHdCQUF3QixFQUF4QkEsd0JBQXdCO0lBQ3hCRyxHQUFHLEVBQUcsU0FBTkEsR0FBR0EsQ0FBR0MsSUFBZ0IsRUFBSztNQUN6Qkosd0JBQXdCLENBQUNLLE1BQU0sQ0FBRSxVQUFBQyxXQUFXLEVBQUs7UUFDL0MsU0FBUzs7UUFDVCxJQUFNQyxLQUFLLEdBQUdELFdBQVcsQ0FBQ0UsU0FBUyxDQUNoQyxVQUFBQyxVQUFVO1VBQUEsT0FBS0EsVUFBVSxDQUFDQyxHQUFHLEtBQUtOLElBQUksQ0FBQ00sR0FDMUM7UUFBQSxFQUFDO1FBQ0QsSUFBSUgsS0FBSyxLQUFLLENBQUMsQ0FBQyxFQUFFO1VBQ2hCRCxXQUFXLENBQUNDLEtBQUssQ0FBQyxHQUFHSCxJQUFJO1FBQzNCLENBQUMsTUFBTTtVQUNMRSxXQUFXLENBQUNLLElBQUksQ0FBQ1AsSUFBSSxDQUFDO1FBQ3hCO1FBQ0EsT0FBT0UsV0FBVztNQUNwQixDQUFDLEVBQUUsS0FBSyxDQUFDO0lBQ1gsQ0FBQztJQUVETSxNQUFNLEVBQUcsU0FBVEEsTUFBTUEsQ0FBR0MsT0FBZSxFQUFLO01BQzNCYix3QkFBd0IsQ0FBQ0ssTUFBTSxDQUFFLFVBQUFDLFdBQVcsRUFBSztRQUMvQyxTQUFTOztRQUNULElBQU1DLEtBQUssR0FBR0QsV0FBVyxDQUFDRSxTQUFTLENBQ2hDLFVBQUFDLFVBQVU7VUFBQSxPQUFLQSxVQUFVLENBQUNDLEdBQUcsS0FBS0csT0FDckM7UUFBQSxFQUFDO1FBQ0QsSUFBSU4sS0FBSyxLQUFLLENBQUMsQ0FBQyxFQUFFO1VBQ2hCRCxXQUFXLENBQUNRLE1BQU0sQ0FBQ1AsS0FBSyxFQUFFLENBQUMsQ0FBQztRQUM5QjtRQUNBLE9BQU9ELFdBQVc7TUFDcEIsQ0FBQyxFQUFFLEtBQUssQ0FBQztJQUNYO0VBQ0YsQ0FBQztFQUNELE9BQU9KLElBQUk7QUFDYiIsImlnbm9yZUxpc3QiOltdfQ==