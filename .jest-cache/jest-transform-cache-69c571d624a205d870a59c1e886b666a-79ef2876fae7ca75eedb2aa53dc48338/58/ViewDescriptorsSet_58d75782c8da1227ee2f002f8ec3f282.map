{"version": 3, "names": ["Object", "defineProperty", "exports", "value", "makeViewDescriptorsSet", "_core", "require", "shareableViewDescriptors", "makeMutable", "data", "add", "item", "modify", "descriptors", "index", "findIndex", "descriptor", "tag", "push", "remove", "viewTag", "splice"], "sources": ["../../src/ViewDescriptorsSet.ts"], "sourcesContent": [null], "mappings": "AAAA,YAAY;;AAAAA,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,KAAA;AAAA;AAAAD,OAAA,CAAAE,sBAAA,GAAAA,sBAAA;AACZ,IAAAC,KAAA,GAAAC,OAAA;AAUO,SAASF,sBAAsBA,CAAA,EAAuB;EAC3D,IAAMG,wBAAwB,GAAG,IAAAC,iBAAW,EAAe,EAAE,CAAC;EAC9D,IAAMC,IAAwB,GAAG;IAC/BF,wBAAwB,EAAxBA,wBAAwB;IACxBG,GAAG,EAAG,SAANA,GAAGA,CAAGC,IAAgB,EAAK;MACzBJ,wBAAwB,CAACK,MAAM,CAAE,UAAAC,WAAW,EAAK;QAC/C,SAAS;;QACT,IAAMC,KAAK,GAAGD,WAAW,CAACE,SAAS,CAChC,UAAAC,UAAU;UAAA,OAAKA,UAAU,CAACC,GAAG,KAAKN,IAAI,CAACM,GAC1C;QAAA,EAAC;QACD,IAAIH,KAAK,KAAK,CAAC,CAAC,EAAE;UAChBD,WAAW,CAACC,KAAK,CAAC,GAAGH,IAAI;QAC3B,CAAC,MAAM;UACLE,WAAW,CAACK,IAAI,CAACP,IAAI,CAAC;QACxB;QACA,OAAOE,WAAW;MACpB,CAAC,EAAE,KAAK,CAAC;IACX,CAAC;IAEDM,MAAM,EAAG,SAATA,MAAMA,CAAGC,OAAe,EAAK;MAC3Bb,wBAAwB,CAACK,MAAM,CAAE,UAAAC,WAAW,EAAK;QAC/C,SAAS;;QACT,IAAMC,KAAK,GAAGD,WAAW,CAACE,SAAS,CAChC,UAAAC,UAAU;UAAA,OAAKA,UAAU,CAACC,GAAG,KAAKG,OACrC;QAAA,EAAC;QACD,IAAIN,KAAK,KAAK,CAAC,CAAC,EAAE;UAChBD,WAAW,CAACQ,MAAM,CAACP,KAAK,EAAE,CAAC,CAAC;QAC9B;QACA,OAAOD,WAAW;MACpB,CAAC,EAAE,KAAK,CAAC;IACX;EACF,CAAC;EACD,OAAOJ,IAAI;AACb", "ignoreList": []}