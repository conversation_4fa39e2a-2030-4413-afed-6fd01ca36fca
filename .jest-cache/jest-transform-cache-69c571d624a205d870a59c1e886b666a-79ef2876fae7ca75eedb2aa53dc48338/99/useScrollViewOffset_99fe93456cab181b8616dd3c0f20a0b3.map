{"version": 3, "names": ["Object", "defineProperty", "exports", "value", "useScrollViewOffset", "_react", "require", "_useEvent", "_useSharedValue", "_PlatformChecker", "IS_WEB", "isWeb", "useScrollViewOffsetWeb", "useScrollViewOffsetNative", "animatedRef", "providedOffset", "internalOffset", "useSharedValue", "offset", "useRef", "current", "<PERSON><PERSON><PERSON><PERSON>", "useCallback", "element", "getWebScrollableElement", "scrollLeft", "scrollTop", "useEffect", "addEventListener", "removeEventListener", "useEvent", "event", "contentOffset", "x", "y", "scrollNativeEventNames", "_animatedRef$getTag", "elementTag", "getTag", "workletEventHandler", "registerForEvents", "unregisterFromEvents", "scrollComponent", "_scrollComponent$getS", "getScrollableNode"], "sources": ["../../../src/hook/useScrollViewOffset.ts"], "sourcesContent": [null], "mappings": "AAAA,YAAY;;AAAAA,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,KAAA;AAAA;AAAAD,OAAA,CAAAE,mBAAA;AACZ,IAAAC,MAAA,GAAAC,OAAA;AAGA,IAAAC,SAAA,GAAAD,OAAA;AACA,IAAAE,eAAA,GAAAF,OAAA;AAOA,IAAAG,gBAAA,GAAAH,OAAA;AAEA,IAAMI,MAAM,GAAG,IAAAC,sBAAK,EAAC,CAAC;AAWf,IAAMP,mBAAmB,GAAAF,OAAA,CAAAE,mBAAA,GAAGM,MAAM,GACrCE,sBAAsB,GACtBC,yBAAyB;AAE7B,SAASD,sBAAsBA,CAC7BE,WAAmD,EACnDC,cAAoC,EACf;EACrB,IAAMC,cAAc,GAAG,IAAAC,8BAAc,EAAC,CAAC,CAAC;EACxC,IAAMC,MAAM,GAAG,IAAAC,aAAM,EAACJ,cAAc,WAAdA,cAAc,GAAIC,cAAc,CAAC,CAACI,OAAO;EAE/D,IAAMC,YAAY,GAAG,IAAAC,kBAAW,EAAC,YAAM;IACrC,SAAS;;IACT,IAAIR,WAAW,EAAE;MACf,IAAMS,OAAO,GAAGC,uBAAuB,CAACV,WAAW,CAACM,OAAO,CAAC;MAE5DF,MAAM,CAACf,KAAK,GACVoB,OAAO,CAACE,UAAU,KAAK,CAAC,GAAGF,OAAO,CAACG,SAAS,GAAGH,OAAO,CAACE,UAAU;IACrE;EAEF,CAAC,EAAE,CAACX,WAAW,EAAEA,WAAW,oBAAXA,WAAW,CAAEM,OAAO,CAAC,CAAC;EAEvC,IAAAO,gBAAS,EAAC,YAAM;IACd,IAAMJ,OAAO,GAAGT,WAAW,YAAXA,WAAW,CAAEM,OAAO,GAChCI,uBAAuB,CAACV,WAAW,CAACM,OAAO,CAAC,GAC5C,IAAI;IAER,IAAIG,OAAO,EAAE;MACXA,OAAO,CAACK,gBAAgB,CAAC,QAAQ,EAAEP,YAAY,CAAC;IAClD;IACA,OAAO,YAAM;MACX,IAAIE,OAAO,EAAE;QACXA,OAAO,CAACM,mBAAmB,CAAC,QAAQ,EAAER,YAAY,CAAC;MACrD;IACF,CAAC;EAKH,CAAC,EAAE,CAACP,WAAW,EAAEA,WAAW,oBAAXA,WAAW,CAAEM,OAAO,EAAEC,YAAY,CAAC,CAAC;EAErD,OAAOH,MAAM;AACf;AAEA,SAASL,yBAAyBA,CAChCC,WAAmD,EACnDC,cAAoC,EACf;EACrB,IAAMC,cAAc,GAAG,IAAAC,8BAAc,EAAC,CAAC,CAAC;EACxC,IAAMC,MAAM,GAAG,IAAAC,aAAM,EAACJ,cAAc,WAAdA,cAAc,GAAIC,cAAc,CAAC,CAACI,OAAO;EAE/D,IAAMC,YAAY,GAAG,IAAAS,kBAAQ,EAC1B,UAAAC,KAA4B,EAAK;IAChC,SAAS;;IACTb,MAAM,CAACf,KAAK,GACV4B,KAAK,CAACC,aAAa,CAACC,CAAC,KAAK,CAAC,GACvBF,KAAK,CAACC,aAAa,CAACE,CAAC,GACrBH,KAAK,CAACC,aAAa,CAACC,CAAC;EAC7B,CAAC,EACDE,sBAGF,CAA2D;EAE3D,IAAAR,gBAAS,EAAC,YAAM;IAAA,IAAAS,mBAAA;IACd,IAAMC,UAAU,IAAAD,mBAAA,GAAGtB,WAAW,oBAAXA,WAAW,CAAEwB,MAAM,CAAC,CAAC,YAAAF,mBAAA,GAAI,IAAI;IAEhD,IAAIC,UAAU,EAAE;MACdhB,YAAY,CAACkB,mBAAmB,CAACC,iBAAiB,CAACH,UAAU,CAAC;IAChE;IACA,OAAO,YAAM;MACX,IAAIA,UAAU,EAAE;QACdhB,YAAY,CAACkB,mBAAmB,CAACE,oBAAoB,CAACJ,UAAU,CAAC;MACnE;IACF,CAAC;EAKH,CAAC,EAAE,CAACvB,WAAW,EAAEA,WAAW,oBAAXA,WAAW,CAAEM,OAAO,EAAEC,YAAY,CAAC,CAAC;EAErD,OAAOH,MAAM;AACf;AAEA,SAASM,uBAAuBA,CAC9BkB,eAA0C,EAC7B;EAAA,IAAAC,qBAAA;EACb,QAAAA,qBAAA,GACGD,eAAe,oBAAfA,eAAe,CAAEE,iBAAiB,CAAC,CAAC,YAAAD,qBAAA,GACrCD,eAAe;AAEnB;AAEA,IAAMP,sBAAsB,GAAG,CAC7B,UAAU,EACV,mBAAmB,EACnB,iBAAiB,EACjB,uBAAuB,EACvB,qBAAqB,CACtB", "ignoreList": []}