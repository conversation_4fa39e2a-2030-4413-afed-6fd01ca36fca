{"version": 3, "names": ["msb_shared_component_1", "cov_bjdsoyf3z", "s", "require", "react_1", "__importStar", "react_native_1", "react_native_tab_view_1", "i18n_ts_1", "BillTab_tsx_1", "__importDefault", "msb_shared_component_2", "BillContainerTab", "_ref", "f", "isBlocked", "savedContacts", "recentContacts", "onAddNewContact", "layout", "useWindowDimensions", "_ref2", "useState", "_ref3", "_slicedToArray2", "default", "index", "setIndex", "_ref4", "key", "title", "translate", "_ref5", "routes", "_ref6", "useMSBStyles", "exports", "makeStyle", "styles", "createElement", "TabView", "testID", "onIndexChange", "navigationState", "renderScene", "_ref7", "route", "b", "isShowAddContact", "contacts", "initialLayout", "width", "swipeEnabled", "renderTabBar", "props", "TabBar", "Object", "assign", "style", "tabBar", "indicatorStyle", "indicator", "contentContainerStyle", "tabContainer", "renderTabBarItem", "itemProps", "onPress", "focused", "findIndex", "r", "MSBTouchable", "tabItem", "MSBTextBase", "tabText", "tabTextActive", "content", "View", "tabIndicator", "createMSBStyleSheet", "_ref8", "SizeGlobal", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Typography", "backgroundColor", "ColorGlobal", "Brand500", "White", "borderTopEndRadius", "Radius3", "borderTopStartRadius", "flexDirection", "justifyContent", "bottom", "height", "Size50", "position", "getSize", "alignItems", "flex", "paddingVertical", "Size300", "base_regular", "color", "Black", "base_semiBold"], "sources": ["/Users/<USER>/projects/react-native/payment-module/src/presentation/payment-home/components/BillContainerTab.tsx"], "sourcesContent": ["import {getSize, MSBTextBase, MSBTouchable, useMSBStyles} from 'msb-shared-component';\nimport React, {useState} from 'react';\nimport {useWindowDimensions, View} from 'react-native';\nimport {TabBar, TabView} from 'react-native-tab-view';\n\nimport {translate} from '../../../locales/i18n.ts';\nimport BillTab from './bill-list/BillTab.tsx';\n\nimport {createMSBStyleSheet, ColorGlobal} from 'msb-shared-component';\nimport {IBillContact} from '../../../domain/entities/IBillContact.ts';\n\ninterface BillContainerTabProps {\n  isBlocked: boolean;\n  savedContacts?: IBillContact[];\n  recentContacts?: IBillContact[];\n  onAddNewContact?: () => void;\n}\n\nconst BillContainerTab = ({isBlocked, savedContacts, recentContacts, onAddNewContact}: BillContainerTabProps) => {\n  const layout = useWindowDimensions();\n  const [index, setIndex] = useState(0);\n  const [routes] = useState([\n    {key: 'saved', title: translate('billingTab.titleSaved')},\n    {key: 'recent', title: translate('billingTab.titleRecent')},\n  ]);\n  const {styles} = useMSBStyles(makeStyle);\n\n  return (\n    <TabView\n      testID={'payment.ContactTab.changeTab'}\n      onIndexChange={setIndex}\n      navigationState={{index, routes}}\n      renderScene={({route}) => {\n        switch (route.key) {\n          case 'saved':\n            return (\n              <BillTab\n                isBlocked={isBlocked}\n                isShowAddContact={true}\n                contacts={savedContacts}\n                onAddNewContact={onAddNewContact}\n              />\n            );\n          case 'recent':\n            return <BillTab isBlocked={isBlocked} contacts={recentContacts} />;\n          default:\n            return null;\n        }\n      }}\n      initialLayout={{width: layout.width}}\n      swipeEnabled={false}\n      renderTabBar={props => (\n        <TabBar\n          {...props}\n          style={styles.tabBar}\n          indicatorStyle={styles.indicator}\n          contentContainerStyle={styles.tabContainer}\n          renderTabBarItem={itemProps => {\n            const {route, onPress} = itemProps;\n            const focused = index === itemProps.navigationState.routes.findIndex(r => r.key === route.key);\n            return (\n              <MSBTouchable onPress={onPress} style={styles.tabItem} testID={`payment.moneyHubScreen.${route.title}`}>\n                <MSBTextBase style={[styles.tabText, focused && styles.tabTextActive]} content={route.title} />\n                {focused && <View style={styles.tabIndicator} />}\n              </MSBTouchable>\n            );\n          }}\n        />\n      )}\n    />\n  );\n};\n\nexport const makeStyle = createMSBStyleSheet(({SizeGlobal, SizeAlias, Typography}) => {\n  return {\n    indicator: {\n      backgroundColor: ColorGlobal.Brand500,\n    },\n    tabBar: {\n      backgroundColor: ColorGlobal.White,\n      borderTopEndRadius: SizeAlias.Radius3,\n      borderTopStartRadius: SizeAlias.Radius3,\n    },\n    tabContainer: {\n      flexDirection: 'row',\n      justifyContent: 'space-around',\n    },\n    tabIndicator: {\n      backgroundColor: ColorGlobal.Brand500,\n      bottom: 0,\n      height: SizeGlobal.Size50,\n      position: 'absolute',\n      width: getSize(86),\n    },\n    tabItem: {\n      alignItems: 'center',\n      flex: 1,\n      paddingVertical: SizeGlobal.Size300,\n    },\n    tabText: {\n      // ...Tpg.base_regular,\n      ...Typography?.base_regular,\n      color: ColorGlobal.Black,\n    },\n    tabTextActive: {\n      ...Typography?.base_semiBold,\n      color: ColorGlobal.Brand500,\n\n      // ...Tpg.base_semiBold,\n      // color: ColorGlobal.Brand500,\n    },\n  };\n});\n\nexport default BillContainerTab;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAAA,sBAAA;AAAA;AAAA,CAAAC,aAAA,GAAAC,CAAA,QAAAC,OAAA;AACA,IAAAC,OAAA;AAAA;AAAA,CAAAH,aAAA,GAAAC,CAAA,QAAAG,YAAA,CAAAF,OAAA;AACA,IAAAG,cAAA;AAAA;AAAA,CAAAL,aAAA,GAAAC,CAAA,QAAAC,OAAA;AACA,IAAAI,uBAAA;AAAA;AAAA,CAAAN,aAAA,GAAAC,CAAA,QAAAC,OAAA;AAEA,IAAAK,SAAA;AAAA;AAAA,CAAAP,aAAA,GAAAC,CAAA,QAAAC,OAAA;AACA,IAAAM,aAAA;AAAA;AAAA,CAAAR,aAAA,GAAAC,CAAA,QAAAQ,eAAA,CAAAP,OAAA;AAEA,IAAAQ,sBAAA;AAAA;AAAA,CAAAV,aAAA,GAAAC,CAAA,QAAAC,OAAA;AAAA;AAAAF,aAAA,GAAAC,CAAA;AAUA,IAAMU,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAAAC,IAAA,EAA0F;EAAA;EAAAZ,aAAA,GAAAa,CAAA;EAAA,IAArFC,SAAS;IAAA;IAAA,CAAAd,aAAA,GAAAC,CAAA,QAAAW,IAAA,CAATE,SAAS;IAAEC,aAAa;IAAA;IAAA,CAAAf,aAAA,GAAAC,CAAA,QAAAW,IAAA,CAAbG,aAAa;IAAEC,cAAc;IAAA;IAAA,CAAAhB,aAAA,GAAAC,CAAA,QAAAW,IAAA,CAAdI,cAAc;IAAEC,eAAe;IAAA;IAAA,CAAAjB,aAAA,GAAAC,CAAA,QAAAW,IAAA,CAAfK,eAAe;EAClF,IAAMC,MAAM;EAAA;EAAA,CAAAlB,aAAA,GAAAC,CAAA,QAAG,IAAAI,cAAA,CAAAc,mBAAmB,GAAE;EACpC,IAAAC,KAAA;IAAA;IAAA,CAAApB,aAAA,GAAAC,CAAA,QAA0B,IAAAE,OAAA,CAAAkB,QAAQ,EAAC,CAAC,CAAC;IAAAC,KAAA;IAAA;IAAA,CAAAtB,aAAA,GAAAC,CAAA,YAAAsB,eAAA,CAAAC,OAAA,EAAAJ,KAAA;IAA9BK,KAAK;IAAA;IAAA,CAAAzB,aAAA,GAAAC,CAAA,QAAAqB,KAAA;IAAEI,QAAQ;IAAA;IAAA,CAAA1B,aAAA,GAAAC,CAAA,QAAAqB,KAAA;EACtB,IAAAK,KAAA;IAAA;IAAA,CAAA3B,aAAA,GAAAC,CAAA,QAAiB,IAAAE,OAAA,CAAAkB,QAAQ,EAAC,CACxB;MAACO,GAAG,EAAE,OAAO;MAAEC,KAAK,EAAE,IAAAtB,SAAA,CAAAuB,SAAS,EAAC,uBAAuB;IAAC,CAAC,EACzD;MAACF,GAAG,EAAE,QAAQ;MAAEC,KAAK,EAAE,IAAAtB,SAAA,CAAAuB,SAAS,EAAC,wBAAwB;IAAC,CAAC,CAC5D,CAAC;IAAAC,KAAA;IAAA;IAAA,CAAA/B,aAAA,GAAAC,CAAA,YAAAsB,eAAA,CAAAC,OAAA,EAAAG,KAAA;IAHKK,MAAM;IAAA;IAAA,CAAAhC,aAAA,GAAAC,CAAA,QAAA8B,KAAA;EAIb,IAAAE,KAAA;IAAA;IAAA,CAAAjC,aAAA,GAAAC,CAAA,QAAiB,IAAAF,sBAAA,CAAAmC,YAAY,EAACC,OAAA,CAAAC,SAAS,CAAC;IAAjCC,MAAM;IAAA;IAAA,CAAArC,aAAA,GAAAC,CAAA,QAAAgC,KAAA,CAANI,MAAM;EAAA;EAAArC,aAAA,GAAAC,CAAA;EAEb,OACEE,OAAA,CAAAqB,OAAA,CAAAc,aAAA,CAAChC,uBAAA,CAAAiC,OAAO;IACNC,MAAM,EAAE,8BAA8B;IACtCC,aAAa,EAAEf,QAAQ;IACvBgB,eAAe,EAAE;MAACjB,KAAK,EAALA,KAAK;MAAEO,MAAM,EAANA;IAAM,CAAC;IAChCW,WAAW,EAAE,SAAbA,WAAWA,CAAAC,KAAA,EAAc;MAAA;MAAA5C,aAAA,GAAAa,CAAA;MAAA,IAAVgC,KAAK;MAAA;MAAA,CAAA7C,aAAA,GAAAC,CAAA,QAAA2C,KAAA,CAALC,KAAK;MAAA;MAAA7C,aAAA,GAAAC,CAAA;MAClB,QAAQ4C,KAAK,CAACjB,GAAG;QACf,KAAK,OAAO;UAAA;UAAA5B,aAAA,GAAA8C,CAAA;UAAA9C,aAAA,GAAAC,CAAA;UACV,OACEE,OAAA,CAAAqB,OAAA,CAAAc,aAAA,CAAC9B,aAAA,CAAAgB,OAAO;YACNV,SAAS,EAAEA,SAAS;YACpBiC,gBAAgB,EAAE,IAAI;YACtBC,QAAQ,EAAEjC,aAAa;YACvBE,eAAe,EAAEA;UAAe,EAChC;QAEN,KAAK,QAAQ;UAAA;UAAAjB,aAAA,GAAA8C,CAAA;UAAA9C,aAAA,GAAAC,CAAA;UACX,OAAOE,OAAA,CAAAqB,OAAA,CAAAc,aAAA,CAAC9B,aAAA,CAAAgB,OAAO;YAACV,SAAS,EAAEA,SAAS;YAAEkC,QAAQ,EAAEhC;UAAc,EAAI;QACpE;UAAA;UAAAhB,aAAA,GAAA8C,CAAA;UAAA9C,aAAA,GAAAC,CAAA;UACE,OAAO,IAAI;MACf;IACF,CAAC;IACDgD,aAAa,EAAE;MAACC,KAAK,EAAEhC,MAAM,CAACgC;IAAK,CAAC;IACpCC,YAAY,EAAE,KAAK;IACnBC,YAAY,EAAE,SAAdA,YAAYA,CAAEC,KAAK;MAAA;MAAArD,aAAA,GAAAa,CAAA;MAAAb,aAAA,GAAAC,CAAA;MAAA,OACjBE,OAAA,CAAAqB,OAAA,CAAAc,aAAA,CAAChC,uBAAA,CAAAgD,MAAM,EAAAC,MAAA,CAAAC,MAAA,KACDH,KAAK;QACTI,KAAK,EAAEpB,MAAM,CAACqB,MAAM;QACpBC,cAAc,EAAEtB,MAAM,CAACuB,SAAS;QAChCC,qBAAqB,EAAExB,MAAM,CAACyB,YAAY;QAC1CC,gBAAgB,EAAE,SAAlBA,gBAAgBA,CAAEC,SAAS,EAAG;UAAA;UAAAhE,aAAA,GAAAa,CAAA;UAC5B,IAAOgC,KAAK;YAAA;YAAA,CAAA7C,aAAA,GAAAC,CAAA,QAAa+D,SAAS,CAA3BnB,KAAK;YAAEoB,OAAO;YAAA;YAAA,CAAAjE,aAAA,GAAAC,CAAA,QAAI+D,SAAS,CAApBC,OAAO;UACrB,IAAMC,OAAO;UAAA;UAAA,CAAAlE,aAAA,GAAAC,CAAA,QAAGwB,KAAK,KAAKuC,SAAS,CAACtB,eAAe,CAACV,MAAM,CAACmC,SAAS,CAAC,UAAAC,CAAC;YAAA;YAAApE,aAAA,GAAAa,CAAA;YAAAb,aAAA,GAAAC,CAAA;YAAA,OAAImE,CAAC,CAACxC,GAAG,KAAKiB,KAAK,CAACjB,GAAG;UAAA,EAAC;UAAA;UAAA5B,aAAA,GAAAC,CAAA;UAC9F,OACEE,OAAA,CAAAqB,OAAA,CAAAc,aAAA,CAACvC,sBAAA,CAAAsE,YAAY;YAACJ,OAAO,EAAEA,OAAO;YAAER,KAAK,EAAEpB,MAAM,CAACiC,OAAO;YAAE9B,MAAM,EAAE,0BAA0BK,KAAK,CAAChB,KAAK;UAAE,GACpG1B,OAAA,CAAAqB,OAAA,CAAAc,aAAA,CAACvC,sBAAA,CAAAwE,WAAW;YAACd,KAAK,EAAE,CAACpB,MAAM,CAACmC,OAAO;YAAE;YAAA,CAAAxE,aAAA,GAAA8C,CAAA,WAAAoB,OAAO;YAAA;YAAA,CAAAlE,aAAA,GAAA8C,CAAA,WAAIT,MAAM,CAACoC,aAAa,EAAC;YAAEC,OAAO,EAAE7B,KAAK,CAAChB;UAAK,EAAI;UAC9F;UAAA,CAAA7B,aAAA,GAAA8C,CAAA,WAAAoB,OAAO;UAAA;UAAA,CAAAlE,aAAA,GAAA8C,CAAA,WAAI3C,OAAA,CAAAqB,OAAA,CAAAc,aAAA,CAACjC,cAAA,CAAAsE,IAAI;YAAClB,KAAK,EAAEpB,MAAM,CAACuC;UAAY,EAAI,EACnC;QAEnB;MAAC,GACD;IAAA;EACH,EACD;AAEN,CAAC;AAAA;AAAA5E,aAAA,GAAAC,CAAA;AAEYkC,OAAA,CAAAC,SAAS,GAAG,IAAA1B,sBAAA,CAAAmE,mBAAmB,EAAC,UAAAC,KAAA,EAAwC;EAAA;EAAA9E,aAAA,GAAAa,CAAA;EAAA,IAAtCkE,UAAU;IAAA;IAAA,CAAA/E,aAAA,GAAAC,CAAA,QAAA6E,KAAA,CAAVC,UAAU;IAAEC,SAAS;IAAA;IAAA,CAAAhF,aAAA,GAAAC,CAAA,QAAA6E,KAAA,CAATE,SAAS;IAAEC,UAAU;IAAA;IAAA,CAAAjF,aAAA,GAAAC,CAAA,QAAA6E,KAAA,CAAVG,UAAU;EAAA;EAAAjF,aAAA,GAAAC,CAAA;EAC9E,OAAO;IACL2D,SAAS,EAAE;MACTsB,eAAe,EAAExE,sBAAA,CAAAyE,WAAW,CAACC;KAC9B;IACD1B,MAAM,EAAE;MACNwB,eAAe,EAAExE,sBAAA,CAAAyE,WAAW,CAACE,KAAK;MAClCC,kBAAkB,EAAEN,SAAS,CAACO,OAAO;MACrCC,oBAAoB,EAAER,SAAS,CAACO;KACjC;IACDzB,YAAY,EAAE;MACZ2B,aAAa,EAAE,KAAK;MACpBC,cAAc,EAAE;KACjB;IACDd,YAAY,EAAE;MACZM,eAAe,EAAExE,sBAAA,CAAAyE,WAAW,CAACC,QAAQ;MACrCO,MAAM,EAAE,CAAC;MACTC,MAAM,EAAEb,UAAU,CAACc,MAAM;MACzBC,QAAQ,EAAE,UAAU;MACpB5C,KAAK,EAAE,IAAAnD,sBAAA,CAAAgG,OAAO,EAAC,EAAE;KAClB;IACDzB,OAAO,EAAE;MACP0B,UAAU,EAAE,QAAQ;MACpBC,IAAI,EAAE,CAAC;MACPC,eAAe,EAAEnB,UAAU,CAACoB;KAC7B;IACD3B,OAAO,EAAAjB,MAAA,CAAAC,MAAA,KAEFyB,UAAU;IAAA;IAAA,CAAAjF,aAAA,GAAA8C,CAAA;IAAA;IAAA,CAAA9C,aAAA,GAAA8C,CAAA,WAAVmC,UAAU,CAAEmB,YAAY;MAC3BC,KAAK,EAAE3F,sBAAA,CAAAyE,WAAW,CAACmB;IAAK,EACzB;IACD7B,aAAa,EAAAlB,MAAA,CAAAC,MAAA,KACRyB,UAAU;IAAA;IAAA,CAAAjF,aAAA,GAAA8C,CAAA;IAAA;IAAA,CAAA9C,aAAA,GAAA8C,CAAA,WAAVmC,UAAU,CAAEsB,aAAa;MAC5BF,KAAK,EAAE3F,sBAAA,CAAAyE,WAAW,CAACC;IAAQ;GAK9B;AACH,CAAC,CAAC;AAAA;AAAApF,aAAA,GAAAC,CAAA;AAEFkC,OAAA,CAAAX,OAAA,GAAeb,gBAAgB", "ignoreList": []}