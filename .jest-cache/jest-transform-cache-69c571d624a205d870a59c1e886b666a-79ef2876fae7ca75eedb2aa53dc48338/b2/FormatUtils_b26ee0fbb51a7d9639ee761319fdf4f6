6ee064e9e4a6accdc1407765673eb1e9
"use strict";

/* istanbul ignore next */
function cov_290w1pjv1v() {
  var path = "/Users/<USER>/projects/react-native/payment-module/src/utils/FormatUtils.ts";
  var hash = "0d50c21dc6d445852bed0e592c2a3cefbff0d60f";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/projects/react-native/payment-module/src/utils/FormatUtils.ts",
    statementMap: {
      "0": {
        start: {
          line: 3,
          column: 29
        },
        end: {
          line: 3,
          column: 84
        }
      },
      "1": {
        start: {
          line: 4,
          column: 22
        },
        end: {
          line: 4,
          column: 93
        }
      },
      "2": {
        start: {
          line: 5,
          column: 26
        },
        end: {
          line: 5,
          column: 101
        }
      },
      "3": {
        start: {
          line: 6,
          column: 22
        },
        end: {
          line: 10,
          column: 1
        }
      },
      "4": {
        start: {
          line: 7,
          column: 2
        },
        end: {
          line: 9,
          column: 4
        }
      },
      "5": {
        start: {
          line: 11,
          column: 0
        },
        end: {
          line: 13,
          column: 3
        }
      },
      "6": {
        start: {
          line: 14,
          column: 15
        },
        end: {
          line: 14,
          column: 49
        }
      },
      "7": {
        start: {
          line: 15,
          column: 13
        },
        end: {
          line: 15,
          column: 39
        }
      },
      "8": {
        start: {
          line: 16,
          column: 18
        },
        end: {
          line: 25,
          column: 1
        }
      },
      "9": {
        start: {
          line: 17,
          column: 2
        },
        end: {
          line: 19,
          column: 3
        }
      },
      "10": {
        start: {
          line: 18,
          column: 4
        },
        end: {
          line: 18,
          column: 15
        }
      },
      "11": {
        start: {
          line: 20,
          column: 12
        },
        end: {
          line: 20,
          column: 64
        }
      },
      "12": {
        start: {
          line: 21,
          column: 2
        },
        end: {
          line: 23,
          column: 3
        }
      },
      "13": {
        start: {
          line: 22,
          column: 4
        },
        end: {
          line: 22,
          column: 15
        }
      },
      "14": {
        start: {
          line: 24,
          column: 2
        },
        end: {
          line: 24,
          column: 57
        }
      },
      "15": {
        start: {
          line: 26,
          column: 22
        },
        end: {
          line: 28,
          column: 1
        }
      },
      "16": {
        start: {
          line: 27,
          column: 2
        },
        end: {
          line: 27,
          column: 39
        }
      },
      "17": {
        start: {
          line: 29,
          column: 21
        },
        end: {
          line: 31,
          column: 1
        }
      },
      "18": {
        start: {
          line: 30,
          column: 2
        },
        end: {
          line: 30,
          column: 58
        }
      },
      "19": {
        start: {
          line: 32,
          column: 25
        },
        end: {
          line: 34,
          column: 1
        }
      },
      "20": {
        start: {
          line: 33,
          column: 2
        },
        end: {
          line: 33,
          column: 63
        }
      },
      "21": {
        start: {
          line: 35,
          column: 22
        },
        end: {
          line: 86,
          column: 1
        }
      },
      "22": {
        start: {
          line: 37,
          column: 17
        },
        end: {
          line: 37,
          column: 90
        }
      },
      "23": {
        start: {
          line: 38,
          column: 14
        },
        end: {
          line: 38,
          column: 81
        }
      },
      "24": {
        start: {
          line: 39,
          column: 14
        },
        end: {
          line: 39,
          column: 130
        }
      },
      "25": {
        start: {
          line: 40,
          column: 13
        },
        end: {
          line: 40,
          column: 117
        }
      },
      "26": {
        start: {
          line: 41,
          column: 15
        },
        end: {
          line: 41,
          column: 73
        }
      },
      "27": {
        start: {
          line: 43,
          column: 4
        },
        end: {
          line: 45,
          column: 5
        }
      },
      "28": {
        start: {
          line: 44,
          column: 6
        },
        end: {
          line: 44,
          column: 16
        }
      },
      "29": {
        start: {
          line: 46,
          column: 16
        },
        end: {
          line: 46,
          column: 34
        }
      },
      "30": {
        start: {
          line: 47,
          column: 18
        },
        end: {
          line: 47,
          column: 37
        }
      },
      "31": {
        start: {
          line: 48,
          column: 20
        },
        end: {
          line: 48,
          column: 27
        }
      },
      "32": {
        start: {
          line: 49,
          column: 17
        },
        end: {
          line: 49,
          column: 19
        }
      },
      "33": {
        start: {
          line: 50,
          column: 4
        },
        end: {
          line: 52,
          column: 5
        }
      },
      "34": {
        start: {
          line: 51,
          column: 6
        },
        end: {
          line: 51,
          column: 42
        }
      },
      "35": {
        start: {
          line: 53,
          column: 4
        },
        end: {
          line: 63,
          column: 5
        }
      },
      "36": {
        start: {
          line: 54,
          column: 6
        },
        end: {
          line: 62,
          column: 7
        }
      },
      "37": {
        start: {
          line: 55,
          column: 8
        },
        end: {
          line: 55,
          column: 41
        }
      },
      "38": {
        start: {
          line: 56,
          column: 13
        },
        end: {
          line: 62,
          column: 7
        }
      },
      "39": {
        start: {
          line: 57,
          column: 8
        },
        end: {
          line: 57,
          column: 46
        }
      },
      "40": {
        start: {
          line: 59,
          column: 18
        },
        end: {
          line: 59,
          column: 44
        }
      },
      "41": {
        start: {
          line: 60,
          column: 18
        },
        end: {
          line: 60,
          column: 32
        }
      },
      "42": {
        start: {
          line: 61,
          column: 8
        },
        end: {
          line: 61,
          column: 53
        }
      },
      "43": {
        start: {
          line: 64,
          column: 4
        },
        end: {
          line: 64,
          column: 32
        }
      },
      "44": {
        start: {
          line: 66,
          column: 2
        },
        end: {
          line: 68,
          column: 3
        }
      },
      "45": {
        start: {
          line: 67,
          column: 4
        },
        end: {
          line: 67,
          column: 63
        }
      },
      "46": {
        start: {
          line: 69,
          column: 15
        },
        end: {
          line: 69,
          column: 17
        }
      },
      "47": {
        start: {
          line: 70,
          column: 19
        },
        end: {
          line: 70,
          column: 20
        }
      },
      "48": {
        start: {
          line: 71,
          column: 21
        },
        end: {
          line: 71,
          column: 23
        }
      },
      "49": {
        start: {
          line: 72,
          column: 2
        },
        end: {
          line: 79,
          column: 3
        }
      },
      "50": {
        start: {
          line: 73,
          column: 16
        },
        end: {
          line: 73,
          column: 29
        }
      },
      "51": {
        start: {
          line: 74,
          column: 4
        },
        end: {
          line: 76,
          column: 5
        }
      },
      "52": {
        start: {
          line: 75,
          column: 6
        },
        end: {
          line: 75,
          column: 58
        }
      },
      "53": {
        start: {
          line: 77,
          column: 4
        },
        end: {
          line: 77,
          column: 39
        }
      },
      "54": {
        start: {
          line: 78,
          column: 4
        },
        end: {
          line: 78,
          column: 17
        }
      },
      "55": {
        start: {
          line: 80,
          column: 2
        },
        end: {
          line: 84,
          column: 3
        }
      },
      "56": {
        start: {
          line: 81,
          column: 4
        },
        end: {
          line: 81,
          column: 74
        }
      },
      "57": {
        start: {
          line: 82,
          column: 9
        },
        end: {
          line: 84,
          column: 3
        }
      },
      "58": {
        start: {
          line: 83,
          column: 4
        },
        end: {
          line: 83,
          column: 74
        }
      },
      "59": {
        start: {
          line: 85,
          column: 2
        },
        end: {
          line: 85,
          column: 139
        }
      },
      "60": {
        start: {
          line: 87,
          column: 24
        },
        end: {
          line: 119,
          column: 1
        }
      },
      "61": {
        start: {
          line: 88,
          column: 21
        },
        end: {
          line: 88,
          column: 23
        }
      },
      "62": {
        start: {
          line: 89,
          column: 19
        },
        end: {
          line: 89,
          column: 51
        }
      },
      "63": {
        start: {
          line: 90,
          column: 19
        },
        end: {
          line: 90,
          column: 39
        }
      },
      "64": {
        start: {
          line: 91,
          column: 2
        },
        end: {
          line: 94,
          column: 3
        }
      },
      "65": {
        start: {
          line: 92,
          column: 4
        },
        end: {
          line: 92,
          column: 17
        }
      },
      "66": {
        start: {
          line: 93,
          column: 4
        },
        end: {
          line: 93,
          column: 30
        }
      },
      "67": {
        start: {
          line: 95,
          column: 2
        },
        end: {
          line: 97,
          column: 3
        }
      },
      "68": {
        start: {
          line: 96,
          column: 4
        },
        end: {
          line: 96,
          column: 113
        }
      },
      "69": {
        start: {
          line: 98,
          column: 2
        },
        end: {
          line: 100,
          column: 3
        }
      },
      "70": {
        start: {
          line: 99,
          column: 4
        },
        end: {
          line: 99,
          column: 34
        }
      },
      "71": {
        start: {
          line: 101,
          column: 28
        },
        end: {
          line: 101,
          column: 37
        }
      },
      "72": {
        start: {
          line: 102,
          column: 2
        },
        end: {
          line: 104,
          column: 3
        }
      },
      "73": {
        start: {
          line: 103,
          column: 4
        },
        end: {
          line: 103,
          column: 78
        }
      },
      "74": {
        start: {
          line: 105,
          column: 25
        },
        end: {
          line: 113,
          column: 4
        }
      },
      "75": {
        start: {
          line: 106,
          column: 16
        },
        end: {
          line: 106,
          column: 53
        }
      },
      "76": {
        start: {
          line: 107,
          column: 15
        },
        end: {
          line: 107,
          column: 23
        }
      },
      "77": {
        start: {
          line: 108,
          column: 14
        },
        end: {
          line: 108,
          column: 22
        }
      },
      "78": {
        start: {
          line: 109,
          column: 4
        },
        end: {
          line: 112,
          column: 6
        }
      },
      "79": {
        start: {
          line: 114,
          column: 2
        },
        end: {
          line: 118,
          column: 4
        }
      },
      "80": {
        start: {
          line: 120,
          column: 28
        },
        end: {
          line: 125,
          column: 1
        }
      },
      "81": {
        start: {
          line: 121,
          column: 2
        },
        end: {
          line: 123,
          column: 3
        }
      },
      "82": {
        start: {
          line: 122,
          column: 4
        },
        end: {
          line: 122,
          column: 14
        }
      },
      "83": {
        start: {
          line: 124,
          column: 2
        },
        end: {
          line: 124,
          column: 107
        }
      },
      "84": {
        start: {
          line: 126,
          column: 34
        },
        end: {
          line: 132,
          column: 1
        }
      },
      "85": {
        start: {
          line: 128,
          column: 2
        },
        end: {
          line: 130,
          column: 3
        }
      },
      "86": {
        start: {
          line: 129,
          column: 4
        },
        end: {
          line: 129,
          column: 14
        }
      },
      "87": {
        start: {
          line: 131,
          column: 2
        },
        end: {
          line: 131,
          column: 139
        }
      },
      "88": {
        start: {
          line: 133,
          column: 33
        },
        end: {
          line: 138,
          column: 1
        }
      },
      "89": {
        start: {
          line: 134,
          column: 2
        },
        end: {
          line: 136,
          column: 3
        }
      },
      "90": {
        start: {
          line: 135,
          column: 4
        },
        end: {
          line: 135,
          column: 14
        }
      },
      "91": {
        start: {
          line: 137,
          column: 2
        },
        end: {
          line: 137,
          column: 7881
        }
      },
      "92": {
        start: {
          line: 139,
          column: 18
        },
        end: {
          line: 145,
          column: 1
        }
      },
      "93": {
        start: {
          line: 140,
          column: 2
        },
        end: {
          line: 140,
          column: 28
        }
      },
      "94": {
        start: {
          line: 141,
          column: 20
        },
        end: {
          line: 141,
          column: 48
        }
      },
      "95": {
        start: {
          line: 142,
          column: 2
        },
        end: {
          line: 142,
          column: 42
        }
      },
      "96": {
        start: {
          line: 143,
          column: 28
        },
        end: {
          line: 143,
          column: 65
        }
      },
      "97": {
        start: {
          line: 144,
          column: 2
        },
        end: {
          line: 144,
          column: 67
        }
      },
      "98": {
        start: {
          line: 146,
          column: 32
        },
        end: {
          line: 179,
          column: 1
        }
      },
      "99": {
        start: {
          line: 147,
          column: 2
        },
        end: {
          line: 149,
          column: 3
        }
      },
      "100": {
        start: {
          line: 148,
          column: 4
        },
        end: {
          line: 148,
          column: 247
        }
      },
      "101": {
        start: {
          line: 150,
          column: 12
        },
        end: {
          line: 150,
          column: 31
        }
      },
      "102": {
        start: {
          line: 151,
          column: 2
        },
        end: {
          line: 151,
          column: 26
        }
      },
      "103": {
        start: {
          line: 152,
          column: 2
        },
        end: {
          line: 154,
          column: 3
        }
      },
      "104": {
        start: {
          line: 153,
          column: 4
        },
        end: {
          line: 153,
          column: 247
        }
      },
      "105": {
        start: {
          line: 155,
          column: 15
        },
        end: {
          line: 155,
          column: 27
        }
      },
      "106": {
        start: {
          line: 156,
          column: 20
        },
        end: {
          line: 156,
          column: 22
        }
      },
      "107": {
        start: {
          line: 157,
          column: 2
        },
        end: {
          line: 176,
          column: 3
        }
      },
      "108": {
        start: {
          line: 158,
          column: 4
        },
        end: {
          line: 158,
          column: 60
        }
      },
      "109": {
        start: {
          line: 159,
          column: 4
        },
        end: {
          line: 159,
          column: 61
        }
      },
      "110": {
        start: {
          line: 160,
          column: 4
        },
        end: {
          line: 160,
          column: 62
        }
      },
      "111": {
        start: {
          line: 161,
          column: 9
        },
        end: {
          line: 176,
          column: 3
        }
      },
      "112": {
        start: {
          line: 162,
          column: 4
        },
        end: {
          line: 162,
          column: 59
        }
      },
      "113": {
        start: {
          line: 163,
          column: 4
        },
        end: {
          line: 163,
          column: 60
        }
      },
      "114": {
        start: {
          line: 164,
          column: 4
        },
        end: {
          line: 164,
          column: 61
        }
      },
      "115": {
        start: {
          line: 165,
          column: 9
        },
        end: {
          line: 176,
          column: 3
        }
      },
      "116": {
        start: {
          line: 166,
          column: 4
        },
        end: {
          line: 166,
          column: 58
        }
      },
      "117": {
        start: {
          line: 167,
          column: 4
        },
        end: {
          line: 167,
          column: 59
        }
      },
      "118": {
        start: {
          line: 168,
          column: 4
        },
        end: {
          line: 168,
          column: 61
        }
      },
      "119": {
        start: {
          line: 169,
          column: 9
        },
        end: {
          line: 176,
          column: 3
        }
      },
      "120": {
        start: {
          line: 170,
          column: 4
        },
        end: {
          line: 170,
          column: 58
        }
      },
      "121": {
        start: {
          line: 171,
          column: 4
        },
        end: {
          line: 171,
          column: 59
        }
      },
      "122": {
        start: {
          line: 172,
          column: 4
        },
        end: {
          line: 172,
          column: 60
        }
      },
      "123": {
        start: {
          line: 174,
          column: 4
        },
        end: {
          line: 174,
          column: 57
        }
      },
      "124": {
        start: {
          line: 175,
          column: 4
        },
        end: {
          line: 175,
          column: 58
        }
      },
      "125": {
        start: {
          line: 177,
          column: 2
        },
        end: {
          line: 177,
          column: 68
        }
      },
      "126": {
        start: {
          line: 178,
          column: 2
        },
        end: {
          line: 178,
          column: 21
        }
      },
      "127": {
        start: {
          line: 180,
          column: 0
        },
        end: {
          line: 192,
          column: 2
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 6,
            column: 54
          },
          end: {
            line: 6,
            column: 55
          }
        },
        loc: {
          start: {
            line: 6,
            column: 69
          },
          end: {
            line: 10,
            column: 1
          }
        },
        line: 6
      },
      "1": {
        name: "formatPrice",
        decl: {
          start: {
            line: 16,
            column: 27
          },
          end: {
            line: 16,
            column: 38
          }
        },
        loc: {
          start: {
            line: 16,
            column: 47
          },
          end: {
            line: 25,
            column: 1
          }
        },
        line: 16
      },
      "2": {
        name: "formattedNumber",
        decl: {
          start: {
            line: 26,
            column: 31
          },
          end: {
            line: 26,
            column: 46
          }
        },
        loc: {
          start: {
            line: 26,
            column: 52
          },
          end: {
            line: 28,
            column: 1
          }
        },
        line: 26
      },
      "3": {
        name: "formatDateHHMM",
        decl: {
          start: {
            line: 29,
            column: 30
          },
          end: {
            line: 29,
            column: 44
          }
        },
        loc: {
          start: {
            line: 29,
            column: 56
          },
          end: {
            line: 31,
            column: 1
          }
        },
        line: 29
      },
      "4": {
        name: "formatDateDDMMYYYY",
        decl: {
          start: {
            line: 32,
            column: 34
          },
          end: {
            line: 32,
            column: 52
          }
        },
        loc: {
          start: {
            line: 32,
            column: 64
          },
          end: {
            line: 34,
            column: 1
          }
        },
        line: 32
      },
      "5": {
        name: "numberToWordsVi",
        decl: {
          start: {
            line: 35,
            column: 31
          },
          end: {
            line: 35,
            column: 46
          }
        },
        loc: {
          start: {
            line: 35,
            column: 55
          },
          end: {
            line: 86,
            column: 1
          }
        },
        line: 35
      },
      "6": {
        name: "convertToWords",
        decl: {
          start: {
            line: 42,
            column: 11
          },
          end: {
            line: 42,
            column: 25
          }
        },
        loc: {
          start: {
            line: 42,
            column: 41
          },
          end: {
            line: 65,
            column: 3
          }
        },
        line: 42
      },
      "7": {
        name: "splitTransactions",
        decl: {
          start: {
            line: 87,
            column: 33
          },
          end: {
            line: 87,
            column: 50
          }
        },
        loc: {
          start: {
            line: 87,
            column: 85
          },
          end: {
            line: 119,
            column: 1
          }
        },
        line: 87
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 105,
            column: 57
          },
          end: {
            line: 105,
            column: 58
          }
        },
        loc: {
          start: {
            line: 105,
            column: 73
          },
          end: {
            line: 113,
            column: 3
          }
        },
        line: 105
      },
      "9": {
        name: "removeVietnameseTones",
        decl: {
          start: {
            line: 120,
            column: 37
          },
          end: {
            line: 120,
            column: 58
          }
        },
        loc: {
          start: {
            line: 120,
            column: 64
          },
          end: {
            line: 125,
            column: 1
          }
        },
        line: 120
      },
      "10": {
        name: "formatRemittanceInformation",
        decl: {
          start: {
            line: 126,
            column: 43
          },
          end: {
            line: 126,
            column: 70
          }
        },
        loc: {
          start: {
            line: 126,
            column: 80
          },
          end: {
            line: 132,
            column: 1
          }
        },
        line: 126
      },
      "11": {
        name: "removeSpecialCharsAndEmoji",
        decl: {
          start: {
            line: 133,
            column: 42
          },
          end: {
            line: 133,
            column: 68
          }
        },
        loc: {
          start: {
            line: 133,
            column: 78
          },
          end: {
            line: 138,
            column: 1
          }
        },
        line: 133
      },
      "12": {
        name: "formatMoney",
        decl: {
          start: {
            line: 139,
            column: 27
          },
          end: {
            line: 139,
            column: 38
          }
        },
        loc: {
          start: {
            line: 139,
            column: 45
          },
          end: {
            line: 145,
            column: 1
          }
        },
        line: 139
      },
      "13": {
        name: "generateAmountSuggestions",
        decl: {
          start: {
            line: 146,
            column: 41
          },
          end: {
            line: 146,
            column: 66
          }
        },
        loc: {
          start: {
            line: 146,
            column: 74
          },
          end: {
            line: 179,
            column: 1
          }
        },
        line: 146
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 6,
            column: 22
          },
          end: {
            line: 10,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 6,
            column: 22
          },
          end: {
            line: 6,
            column: 26
          }
        }, {
          start: {
            line: 6,
            column: 30
          },
          end: {
            line: 6,
            column: 50
          }
        }, {
          start: {
            line: 6,
            column: 54
          },
          end: {
            line: 10,
            column: 1
          }
        }],
        line: 6
      },
      "1": {
        loc: {
          start: {
            line: 7,
            column: 9
          },
          end: {
            line: 9,
            column: 3
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 7,
            column: 33
          },
          end: {
            line: 7,
            column: 36
          }
        }, {
          start: {
            line: 7,
            column: 39
          },
          end: {
            line: 9,
            column: 3
          }
        }],
        line: 7
      },
      "2": {
        loc: {
          start: {
            line: 7,
            column: 9
          },
          end: {
            line: 7,
            column: 30
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 7,
            column: 9
          },
          end: {
            line: 7,
            column: 12
          }
        }, {
          start: {
            line: 7,
            column: 16
          },
          end: {
            line: 7,
            column: 30
          }
        }],
        line: 7
      },
      "3": {
        loc: {
          start: {
            line: 17,
            column: 2
          },
          end: {
            line: 19,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 17,
            column: 2
          },
          end: {
            line: 19,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 17
      },
      "4": {
        loc: {
          start: {
            line: 17,
            column: 6
          },
          end: {
            line: 17,
            column: 43
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 17,
            column: 6
          },
          end: {
            line: 17,
            column: 20
          }
        }, {
          start: {
            line: 17,
            column: 24
          },
          end: {
            line: 17,
            column: 43
          }
        }],
        line: 17
      },
      "5": {
        loc: {
          start: {
            line: 20,
            column: 12
          },
          end: {
            line: 20,
            column: 64
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 20,
            column: 41
          },
          end: {
            line: 20,
            column: 55
          }
        }, {
          start: {
            line: 20,
            column: 58
          },
          end: {
            line: 20,
            column: 64
          }
        }],
        line: 20
      },
      "6": {
        loc: {
          start: {
            line: 21,
            column: 2
          },
          end: {
            line: 23,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 21,
            column: 2
          },
          end: {
            line: 23,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 21
      },
      "7": {
        loc: {
          start: {
            line: 37,
            column: 17
          },
          end: {
            line: 37,
            column: 90
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 37,
            column: 70
          },
          end: {
            line: 37,
            column: 82
          }
        }, {
          start: {
            line: 37,
            column: 85
          },
          end: {
            line: 37,
            column: 90
          }
        }],
        line: 37
      },
      "8": {
        loc: {
          start: {
            line: 37,
            column: 17
          },
          end: {
            line: 37,
            column: 67
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 37,
            column: 17
          },
          end: {
            line: 37,
            column: 37
          }
        }, {
          start: {
            line: 37,
            column: 41
          },
          end: {
            line: 37,
            column: 67
          }
        }],
        line: 37
      },
      "9": {
        loc: {
          start: {
            line: 43,
            column: 4
          },
          end: {
            line: 45,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 43,
            column: 4
          },
          end: {
            line: 45,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 43
      },
      "10": {
        loc: {
          start: {
            line: 50,
            column: 4
          },
          end: {
            line: 52,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 50,
            column: 4
          },
          end: {
            line: 52,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 50
      },
      "11": {
        loc: {
          start: {
            line: 53,
            column: 4
          },
          end: {
            line: 63,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 53,
            column: 4
          },
          end: {
            line: 63,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 53
      },
      "12": {
        loc: {
          start: {
            line: 54,
            column: 6
          },
          end: {
            line: 62,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 54,
            column: 6
          },
          end: {
            line: 62,
            column: 7
          }
        }, {
          start: {
            line: 56,
            column: 13
          },
          end: {
            line: 62,
            column: 7
          }
        }],
        line: 54
      },
      "13": {
        loc: {
          start: {
            line: 56,
            column: 13
          },
          end: {
            line: 62,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 56,
            column: 13
          },
          end: {
            line: 62,
            column: 7
          }
        }, {
          start: {
            line: 58,
            column: 13
          },
          end: {
            line: 62,
            column: 7
          }
        }],
        line: 56
      },
      "14": {
        loc: {
          start: {
            line: 66,
            column: 2
          },
          end: {
            line: 68,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 66,
            column: 2
          },
          end: {
            line: 68,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 66
      },
      "15": {
        loc: {
          start: {
            line: 74,
            column: 4
          },
          end: {
            line: 76,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 74,
            column: 4
          },
          end: {
            line: 76,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 74
      },
      "16": {
        loc: {
          start: {
            line: 80,
            column: 2
          },
          end: {
            line: 84,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 80,
            column: 2
          },
          end: {
            line: 84,
            column: 3
          }
        }, {
          start: {
            line: 82,
            column: 9
          },
          end: {
            line: 84,
            column: 3
          }
        }],
        line: 80
      },
      "17": {
        loc: {
          start: {
            line: 82,
            column: 9
          },
          end: {
            line: 84,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 82,
            column: 9
          },
          end: {
            line: 84,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 82
      },
      "18": {
        loc: {
          start: {
            line: 85,
            column: 10
          },
          end: {
            line: 85,
            column: 91
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 85,
            column: 55
          },
          end: {
            line: 85,
            column: 61
          }
        }, {
          start: {
            line: 85,
            column: 64
          },
          end: {
            line: 85,
            column: 91
          }
        }],
        line: 85
      },
      "19": {
        loc: {
          start: {
            line: 91,
            column: 2
          },
          end: {
            line: 94,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 91,
            column: 2
          },
          end: {
            line: 94,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 91
      },
      "20": {
        loc: {
          start: {
            line: 91,
            column: 6
          },
          end: {
            line: 91,
            column: 65
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 91,
            column: 6
          },
          end: {
            line: 91,
            column: 22
          }
        }, {
          start: {
            line: 91,
            column: 26
          },
          end: {
            line: 91,
            column: 65
          }
        }],
        line: 91
      },
      "21": {
        loc: {
          start: {
            line: 95,
            column: 2
          },
          end: {
            line: 97,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 95,
            column: 2
          },
          end: {
            line: 97,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 95
      },
      "22": {
        loc: {
          start: {
            line: 98,
            column: 2
          },
          end: {
            line: 100,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 98,
            column: 2
          },
          end: {
            line: 100,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 98
      },
      "23": {
        loc: {
          start: {
            line: 103,
            column: 36
          },
          end: {
            line: 103,
            column: 71
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 103,
            column: 36
          },
          end: {
            line: 103,
            column: 66
          }
        }, {
          start: {
            line: 103,
            column: 70
          },
          end: {
            line: 103,
            column: 71
          }
        }],
        line: 103
      },
      "24": {
        loc: {
          start: {
            line: 121,
            column: 2
          },
          end: {
            line: 123,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 121,
            column: 2
          },
          end: {
            line: 123,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 121
      },
      "25": {
        loc: {
          start: {
            line: 128,
            column: 2
          },
          end: {
            line: 130,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 128,
            column: 2
          },
          end: {
            line: 130,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 128
      },
      "26": {
        loc: {
          start: {
            line: 131,
            column: 9
          },
          end: {
            line: 131,
            column: 138
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 131,
            column: 92
          },
          end: {
            line: 131,
            column: 98
          }
        }, {
          start: {
            line: 131,
            column: 101
          },
          end: {
            line: 131,
            column: 138
          }
        }],
        line: 131
      },
      "27": {
        loc: {
          start: {
            line: 134,
            column: 2
          },
          end: {
            line: 136,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 134,
            column: 2
          },
          end: {
            line: 136,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 134
      },
      "28": {
        loc: {
          start: {
            line: 143,
            column: 28
          },
          end: {
            line: 143,
            column: 65
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 143,
            column: 28
          },
          end: {
            line: 143,
            column: 58
          }
        }, {
          start: {
            line: 143,
            column: 62
          },
          end: {
            line: 143,
            column: 65
          }
        }],
        line: 143
      },
      "29": {
        loc: {
          start: {
            line: 147,
            column: 2
          },
          end: {
            line: 149,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 147,
            column: 2
          },
          end: {
            line: 149,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 147
      },
      "30": {
        loc: {
          start: {
            line: 147,
            column: 6
          },
          end: {
            line: 147,
            column: 34
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 147,
            column: 6
          },
          end: {
            line: 147,
            column: 12
          }
        }, {
          start: {
            line: 147,
            column: 16
          },
          end: {
            line: 147,
            column: 34
          }
        }],
        line: 147
      },
      "31": {
        loc: {
          start: {
            line: 152,
            column: 2
          },
          end: {
            line: 154,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 152,
            column: 2
          },
          end: {
            line: 154,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 152
      },
      "32": {
        loc: {
          start: {
            line: 152,
            column: 6
          },
          end: {
            line: 152,
            column: 29
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 152,
            column: 6
          },
          end: {
            line: 152,
            column: 16
          }
        }, {
          start: {
            line: 152,
            column: 20
          },
          end: {
            line: 152,
            column: 29
          }
        }],
        line: 152
      },
      "33": {
        loc: {
          start: {
            line: 157,
            column: 2
          },
          end: {
            line: 176,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 157,
            column: 2
          },
          end: {
            line: 176,
            column: 3
          }
        }, {
          start: {
            line: 161,
            column: 9
          },
          end: {
            line: 176,
            column: 3
          }
        }],
        line: 157
      },
      "34": {
        loc: {
          start: {
            line: 161,
            column: 9
          },
          end: {
            line: 176,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 161,
            column: 9
          },
          end: {
            line: 176,
            column: 3
          }
        }, {
          start: {
            line: 165,
            column: 9
          },
          end: {
            line: 176,
            column: 3
          }
        }],
        line: 161
      },
      "35": {
        loc: {
          start: {
            line: 165,
            column: 9
          },
          end: {
            line: 176,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 165,
            column: 9
          },
          end: {
            line: 176,
            column: 3
          }
        }, {
          start: {
            line: 169,
            column: 9
          },
          end: {
            line: 176,
            column: 3
          }
        }],
        line: 165
      },
      "36": {
        loc: {
          start: {
            line: 165,
            column: 13
          },
          end: {
            line: 165,
            column: 41
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 165,
            column: 13
          },
          end: {
            line: 165,
            column: 25
          }
        }, {
          start: {
            line: 165,
            column: 29
          },
          end: {
            line: 165,
            column: 41
          }
        }],
        line: 165
      },
      "37": {
        loc: {
          start: {
            line: 169,
            column: 9
          },
          end: {
            line: 176,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 169,
            column: 9
          },
          end: {
            line: 176,
            column: 3
          }
        }, {
          start: {
            line: 173,
            column: 9
          },
          end: {
            line: 176,
            column: 3
          }
        }],
        line: 169
      },
      "38": {
        loc: {
          start: {
            line: 169,
            column: 13
          },
          end: {
            line: 169,
            column: 41
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 169,
            column: 13
          },
          end: {
            line: 169,
            column: 25
          }
        }, {
          start: {
            line: 169,
            column: 29
          },
          end: {
            line: 169,
            column: 41
          }
        }],
        line: 169
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0,
      "108": 0,
      "109": 0,
      "110": 0,
      "111": 0,
      "112": 0,
      "113": 0,
      "114": 0,
      "115": 0,
      "116": 0,
      "117": 0,
      "118": 0,
      "119": 0,
      "120": 0,
      "121": 0,
      "122": 0,
      "123": 0,
      "124": 0,
      "125": 0,
      "126": 0,
      "127": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0
    },
    b: {
      "0": [0, 0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0],
      "20": [0, 0],
      "21": [0, 0],
      "22": [0, 0],
      "23": [0, 0],
      "24": [0, 0],
      "25": [0, 0],
      "26": [0, 0],
      "27": [0, 0],
      "28": [0, 0],
      "29": [0, 0],
      "30": [0, 0],
      "31": [0, 0],
      "32": [0, 0],
      "33": [0, 0],
      "34": [0, 0],
      "35": [0, 0],
      "36": [0, 0],
      "37": [0, 0],
      "38": [0, 0]
    },
    inputSourceMap: {
      version: 3,
      file: undefined,
      names: ["moment_1", "__importDefault", "require", "i18n_1", "formatPrice", "amount", "undefined", "num", "Number", "isNaN", "toLocaleString", "replace", "formattedNumber", "str", "formatDateHHMM", "isoString", "default", "format", "formatDateDDMMYYYY", "numberToWordsVi", "number", "_result$trim$", "currency", "arguments", "length", "units", "teens", "tens", "scales", "convertToWords", "n", "scaleIndex", "scale", "hundred", "Math", "floor", "remainder", "result", "ten", "one", "translate", "currencyWord", "chunk", "trim", "toUpperCase", "slice", "splitTransactions", "maxLimit247", "splitAmount", "transactions", "splitTrans", "finalTrans", "push", "apply", "_toConsumableArray2", "Array", "fill", "transactionItemsMap", "Map", "trans", "set", "get", "transactionItems", "from", "_ref", "_ref2", "_slicedToArray2", "count", "totalTransaction", "removeVietnameseTones", "normalize", "formatRemittanceInformation", "content", "_content$replace", "removeSpecialCharsAndEmoji", "formatMoney", "text", "console", "log", "cleanedText", "withoutLeadingZeros", "generateAmountSuggestions", "input", "parseInt", "suggestions", "toString", "exports"],
      sourceRoot: undefined,
      sources: ["/Users/<USER>/projects/react-native/payment-module/src/utils/FormatUtils.ts"],
      sourcesContent: ["import moment from 'moment';\nimport {translate} from '../locales/i18n';\n\nconst formatPrice = (amount: number | string | null | undefined) => {\n  if (amount == null || amount == undefined) {\n    return '0';\n  }\n  const num = typeof amount === 'string' ? Number(amount) : amount;\n  if (isNaN(num)) {\n    return '0';\n  }\n  return num.toLocaleString('vi-VN').replace(/\\./g, ',');\n};\nconst formattedNumber = (str: string): number => {\n  return Number(str.replace(/,/g, ''));\n};\n\nconst formatDateHHMM = (isoString: string): string => {\n  return moment(isoString).format('HH:mm');\n};\n\nconst formatDateDDMMYYYY = (isoString: string): string => {\n  return moment(isoString).format('DD/MM/YYYY');\n};\n\nconst numberToWordsVi = (number: number, currency = 'VND') => {\n  const units = ['', 'm\u1ED9t', 'hai', 'ba', 'b\u1ED1n', 'n\u0103m', 's\xE1u', 'b\u1EA3y', 't\xE1m', 'ch\xEDn'];\n  const teens = [\n    'm\u01B0\u1EDDi',\n    'm\u01B0\u1EDDi m\u1ED9t',\n    'm\u01B0\u1EDDi hai',\n    'm\u01B0\u1EDDi ba',\n    'm\u01B0\u1EDDi b\u1ED1n',\n    'm\u01B0\u1EDDi l\u0103m',\n    'm\u01B0\u1EDDi s\xE1u',\n    'm\u01B0\u1EDDi b\u1EA3y',\n    'm\u01B0\u1EDDi t\xE1m',\n    'm\u01B0\u1EDDi ch\xEDn',\n  ];\n  const tens = ['', '', 'hai m\u01B0\u01A1i', 'ba m\u01B0\u01A1i', 'b\u1ED1n m\u01B0\u01A1i', 'n\u0103m m\u01B0\u01A1i', 's\xE1u m\u01B0\u01A1i', 'b\u1EA3y m\u01B0\u01A1i', 't\xE1m m\u01B0\u01A1i', 'ch\xEDn m\u01B0\u01A1i'];\n  const scales = ['', 'ngh\xECn', 'tri\u1EC7u', 't\u1EF7', 'ngh\xECn t\u1EF7', 'ngh\xECn ngh\xECn t\u1EF7'];\n\n  function convertToWords(n: any, scaleIndex: number) {\n    if (n === 0) {\n      return '';\n    }\n\n    const scale = scales[scaleIndex];\n    const hundred = Math.floor(n / 100);\n    const remainder = n % 100;\n\n    let result = '';\n\n    if (hundred > 0) {\n      result += units[hundred] + ' tr\u0103m ';\n    }\n\n    if (remainder > 0) {\n      if (remainder < 10) {\n        result += units[remainder] + ' ';\n      } else if (remainder < 20) {\n        result += teens[remainder - 10] + ' ';\n      } else {\n        const ten = Math.floor(remainder / 10);\n        const one = remainder % 10;\n        result += tens[ten] + ' ' + units[one] + ' ';\n      }\n    }\n\n    return result + scale + ' ';\n  }\n\n  if (number === 0) {\n    return translate('utils.formatUtils.noAmount');\n  }\n\n  let result = '';\n  let scaleIndex = 0;\n  let currencyWord = '';\n\n  while (number > 0) {\n    const chunk = number % 1000;\n    if (chunk > 0) {\n      result = convertToWords(chunk, scaleIndex) + result;\n    }\n    number = Math.floor(number / 1000);\n    scaleIndex++;\n  }\n\n  if (currency === 'VND') {\n    currencyWord = translate('utils.formatUtils.currencyVnd');\n  } else if (currency === 'USD') {\n    currencyWord = translate('utils.formatUtils.currencyUsd');\n  }\n\n  return result.trim()[0]?.toUpperCase() + result.trim().slice(1) + ' ' + currencyWord;\n};\n\nconst splitTransactions = (amount: number, maxLimit247: number, splitAmount: number) => {\n  const transactions: number[] = [];\n\n  let splitTrans = Math.floor(amount / splitAmount);\n  let finalTrans = amount % splitAmount;\n\n  // N\u1EBFu finalTrans + splitAmount <= maxLimit247, g\u1ED9p final v\xE0o giao d\u1ECBch cu\u1ED1i\n  if (finalTrans !== 0 && finalTrans + splitAmount <= maxLimit247) {\n    splitTrans--;\n    finalTrans += splitAmount;\n  }\n\n  if (splitTrans > 0) {\n    transactions.push(...Array(splitTrans).fill(splitAmount));\n  }\n\n  // Th\xEAm giao d\u1ECBch cu\u1ED1i n\u1EBFu c\xF3\n  if (finalTrans > 0) {\n    transactions.push(finalTrans);\n  }\n\n  const transactionItemsMap = new Map<number, number>();\n\n  for (const trans of transactions) {\n    transactionItemsMap.set(trans, (transactionItemsMap.get(trans) || 0) + 1);\n  }\n\n  const transactionItems = Array.from(transactionItemsMap, ([amount, count]) => ({amount, count}));\n\n  return {\n    totalTransaction: transactions.length,\n    transactions,\n    transactionItems,\n  };\n};\n\n// chuy\u1EC3n ti\u1EBFng vi\u1EC7t c\xF3 d\u1EA5u sang ti\u1EBFng vi\u1EC7t kh\xF4ng d\u1EA5u\nconst removeVietnameseTones = (str: string): string => {\n  if (!str) {\n    return '';\n  }\n  return str\n    .normalize('NFD')\n    .replace(/[\\u0300-\\u036f]/g, '')\n    .replace(/\u0111/g, 'd')\n    .replace(/\u0110/g, 'D')\n    .trim();\n};\n\n// ch\u1EC9 cho ph\xE9p nh\u1EADp c\xE1c t\u1EF1 \u0111\u1EB7c bi\u1EC7t /().+_,\n// kh\xF4ng cho nh\u1EADp c\xE1c k\xED t\u1EF1 \u0111\u1EB7c bi\u1EC7t c\xF2n l\u1EA1i v\xE0 icon\n// d\xF9ng cho nh\u1EADp n\u1ED9i dung chuy\u1EC3n kho\u1EA3n\nconst formatRemittanceInformation = (content?: string | null) => {\n  if (!content) {\n    return '';\n  }\n  return content.replace(/[^a-zA-Z0-9\xC0-\u1EF9\\-\\\\/().+_, ]/g, '')?.replace(/\\s+/g, ' ');\n};\n\nconst removeSpecialCharsAndEmoji = (content?: string | null) => {\n  if (!content) {\n    return '';\n  }\n  return content.replace(/[^\\p{L} ]+/gu, '');\n};\n\nconst formatMoney = (text: string): string => {\n  console.log('text', text);\n  const cleanedText = text.replace(/[^0-9.]/g, '');\n  console.log('cleanedText', cleanedText);\n  const withoutLeadingZeros = cleanedText.replace(/^0+/, '') || '0';\n  return withoutLeadingZeros.replace(/\\B(?=(\\d{3})+(?!\\d))/g, ',');\n};\n\nconst generateAmountSuggestions = (input: string): string[] => {\n  if (!input || input.length === 0) {\n    return [\n      translate('utils.formatUtils.amountSuggestion1'),\n      translate('utils.formatUtils.amountSuggestion2'),\n      translate('utils.formatUtils.amountSuggestion3'),\n      translate('utils.formatUtils.done'),\n    ];\n  }\n\n  const num = parseInt(input, 10);\n  console.log('num', num);\n  if (isNaN(num) || num === 0) {\n    return [\n      translate('utils.formatUtils.amountSuggestion1'),\n      translate('utils.formatUtils.amountSuggestion2'),\n      translate('utils.formatUtils.amountSuggestion3'),\n      translate('utils.formatUtils.done'),\n    ];\n  }\n\n  const length = input.length;\n  const suggestions: string[] = [];\n\n  if (length === 1) {\n    suggestions.push(formatMoney((num * 10000).toString()));\n    suggestions.push(formatMoney((num * 100000).toString()));\n    suggestions.push(formatMoney((num * 1000000).toString()));\n  } else if (length === 2) {\n    suggestions.push(formatMoney((num * 1000).toString()));\n    suggestions.push(formatMoney((num * 10000).toString()));\n    suggestions.push(formatMoney((num * 100000).toString()));\n  } else if (length === 3 || length === 4) {\n    suggestions.push(formatMoney((num * 100).toString()));\n    suggestions.push(formatMoney((num * 1000).toString()));\n    suggestions.push(formatMoney((num * 100000).toString()));\n  } else if (length === 5 || length === 6) {\n    suggestions.push(formatMoney((num * 100).toString()));\n    suggestions.push(formatMoney((num * 1000).toString()));\n    suggestions.push(formatMoney((num * 10000).toString()));\n  } else {\n    suggestions.push(formatMoney((num * 10).toString()));\n    suggestions.push(formatMoney((num * 100).toString()));\n  }\n\n  suggestions.push(translate('utils.formatUtils.done'));\n  return suggestions;\n};\n\nexport default {\n  formatPrice,\n  formatMoney,\n  formattedNumber,\n  numberToWordsVi,\n  splitTransactions,\n  formatDateDDMMYYYY,\n  formatDateHHMM,\n  formatRemittanceInformation,\n  generateAmountSuggestions,\n  removeVietnameseTones,\n  removeSpecialCharsAndEmoji,\n};\n"],
      mappings: ";;;;;;;;;;;;;AAAA,IAAAA,QAAA,GAAAC,eAAA,CAAAC,OAAA;AACA,IAAAC,MAAA,GAAAD,OAAA;AAEA,IAAME,WAAW,GAAG,SAAdA,WAAWA,CAAIC,MAA0C,EAAI;EACjE,IAAIA,MAAM,IAAI,IAAI,IAAIA,MAAM,IAAIC,SAAS,EAAE;IACzC,OAAO,GAAG;EACZ;EACA,IAAMC,GAAG,GAAG,OAAOF,MAAM,KAAK,QAAQ,GAAGG,MAAM,CAACH,MAAM,CAAC,GAAGA,MAAM;EAChE,IAAII,KAAK,CAACF,GAAG,CAAC,EAAE;IACd,OAAO,GAAG;EACZ;EACA,OAAOA,GAAG,CAACG,cAAc,CAAC,OAAO,CAAC,CAACC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC;AACxD,CAAC;AACD,IAAMC,eAAe,GAAG,SAAlBA,eAAeA,CAAIC,GAAW,EAAY;EAC9C,OAAOL,MAAM,CAACK,GAAG,CAACF,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;AACtC,CAAC;AAED,IAAMG,cAAc,GAAG,SAAjBA,cAAcA,CAAIC,SAAiB,EAAY;EACnD,OAAO,IAAAf,QAAA,CAAAgB,OAAM,EAACD,SAAS,CAAC,CAACE,MAAM,CAAC,OAAO,CAAC;AAC1C,CAAC;AAED,IAAMC,kBAAkB,GAAG,SAArBA,kBAAkBA,CAAIH,SAAiB,EAAY;EACvD,OAAO,IAAAf,QAAA,CAAAgB,OAAM,EAACD,SAAS,CAAC,CAACE,MAAM,CAAC,YAAY,CAAC;AAC/C,CAAC;AAED,IAAME,eAAe,GAAG,SAAlBA,eAAeA,CAAIC,MAAc,EAAsB;EAAA,IAAAC,aAAA;EAAA,IAApBC,QAAQ,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAjB,SAAA,GAAAiB,SAAA,MAAG,KAAK;EACvD,IAAME,KAAK,GAAG,CAAC,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,CAAC;EACjF,IAAMC,KAAK,GAAG,CACZ,MAAM,EACN,UAAU,EACV,UAAU,EACV,SAAS,EACT,UAAU,EACV,UAAU,EACV,UAAU,EACV,UAAU,EACV,UAAU,EACV,WAAW,CACZ;EACD,IAAMC,IAAI,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE,UAAU,EAAE,SAAS,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,WAAW,CAAC;EACrH,IAAMC,MAAM,GAAG,CAAC,EAAE,EAAE,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,EAAE,gBAAgB,CAAC;EAEzE,SAASC,cAAcA,CAACC,CAAM,EAAEC,UAAkB;IAChD,IAAID,CAAC,KAAK,CAAC,EAAE;MACX,OAAO,EAAE;IACX;IAEA,IAAME,KAAK,GAAGJ,MAAM,CAACG,UAAU,CAAC;IAChC,IAAME,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACL,CAAC,GAAG,GAAG,CAAC;IACnC,IAAMM,SAAS,GAAGN,CAAC,GAAG,GAAG;IAEzB,IAAIO,MAAM,GAAG,EAAE;IAEf,IAAIJ,OAAO,GAAG,CAAC,EAAE;MACfI,MAAM,IAAIZ,KAAK,CAACQ,OAAO,CAAC,GAAG,QAAQ;IACrC;IAEA,IAAIG,SAAS,GAAG,CAAC,EAAE;MACjB,IAAIA,SAAS,GAAG,EAAE,EAAE;QAClBC,MAAM,IAAIZ,KAAK,CAACW,SAAS,CAAC,GAAG,GAAG;MAClC,CAAC,MAAM,IAAIA,SAAS,GAAG,EAAE,EAAE;QACzBC,MAAM,IAAIX,KAAK,CAACU,SAAS,GAAG,EAAE,CAAC,GAAG,GAAG;MACvC,CAAC,MAAM;QACL,IAAME,GAAG,GAAGJ,IAAI,CAACC,KAAK,CAACC,SAAS,GAAG,EAAE,CAAC;QACtC,IAAMG,GAAG,GAAGH,SAAS,GAAG,EAAE;QAC1BC,MAAM,IAAIV,IAAI,CAACW,GAAG,CAAC,GAAG,GAAG,GAAGb,KAAK,CAACc,GAAG,CAAC,GAAG,GAAG;MAC9C;IACF;IAEA,OAAOF,MAAM,GAAGL,KAAK,GAAG,GAAG;EAC7B;EAEA,IAAIZ,MAAM,KAAK,CAAC,EAAE;IAChB,OAAO,IAAAjB,MAAA,CAAAqC,SAAS,EAAC,4BAA4B,CAAC;EAChD;EAEA,IAAIH,MAAM,GAAG,EAAE;EACf,IAAIN,UAAU,GAAG,CAAC;EAClB,IAAIU,YAAY,GAAG,EAAE;EAErB,OAAOrB,MAAM,GAAG,CAAC,EAAE;IACjB,IAAMsB,KAAK,GAAGtB,MAAM,GAAG,IAAI;IAC3B,IAAIsB,KAAK,GAAG,CAAC,EAAE;MACbL,MAAM,GAAGR,cAAc,CAACa,KAAK,EAAEX,UAAU,CAAC,GAAGM,MAAM;IACrD;IACAjB,MAAM,GAAGc,IAAI,CAACC,KAAK,CAACf,MAAM,GAAG,IAAI,CAAC;IAClCW,UAAU,EAAE;EACd;EAEA,IAAIT,QAAQ,KAAK,KAAK,EAAE;IACtBmB,YAAY,GAAG,IAAAtC,MAAA,CAAAqC,SAAS,EAAC,+BAA+B,CAAC;EAC3D,CAAC,MAAM,IAAIlB,QAAQ,KAAK,KAAK,EAAE;IAC7BmB,YAAY,GAAG,IAAAtC,MAAA,CAAAqC,SAAS,EAAC,+BAA+B,CAAC;EAC3D;EAEA,OAAO,EAAAnB,aAAA,GAAAgB,MAAM,CAACM,IAAI,EAAE,CAAC,CAAC,CAAC,qBAAhBtB,aAAA,CAAkBuB,WAAW,EAAE,IAAGP,MAAM,CAACM,IAAI,EAAE,CAACE,KAAK,CAAC,CAAC,CAAC,GAAG,GAAG,GAAGJ,YAAY;AACtF,CAAC;AAED,IAAMK,iBAAiB,GAAG,SAApBA,iBAAiBA,CAAIzC,MAAc,EAAE0C,WAAmB,EAAEC,WAAmB,EAAI;EACrF,IAAMC,YAAY,GAAa,EAAE;EAEjC,IAAIC,UAAU,GAAGhB,IAAI,CAACC,KAAK,CAAC9B,MAAM,GAAG2C,WAAW,CAAC;EACjD,IAAIG,UAAU,GAAG9C,MAAM,GAAG2C,WAAW;EAGrC,IAAIG,UAAU,KAAK,CAAC,IAAIA,UAAU,GAAGH,WAAW,IAAID,WAAW,EAAE;IAC/DG,UAAU,EAAE;IACZC,UAAU,IAAIH,WAAW;EAC3B;EAEA,IAAIE,UAAU,GAAG,CAAC,EAAE;IAClBD,YAAY,CAACG,IAAI,CAAAC,KAAA,CAAjBJ,YAAY,MAAAK,mBAAA,CAAAtC,OAAA,EAASuC,KAAK,CAACL,UAAU,CAAC,CAACM,IAAI,CAACR,WAAW,CAAC,EAAC;EAC3D;EAGA,IAAIG,UAAU,GAAG,CAAC,EAAE;IAClBF,YAAY,CAACG,IAAI,CAACD,UAAU,CAAC;EAC/B;EAEA,IAAMM,mBAAmB,GAAG,IAAIC,GAAG,EAAkB;EAErD,KAAK,IAAMC,KAAK,IAAIV,YAAY,EAAE;IAChCQ,mBAAmB,CAACG,GAAG,CAACD,KAAK,EAAE,CAACF,mBAAmB,CAACI,GAAG,CAACF,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;EAC3E;EAEA,IAAMG,gBAAgB,GAAGP,KAAK,CAACQ,IAAI,CAACN,mBAAmB,EAAE,UAAAO,IAAA;IAAA,IAAAC,KAAA,OAAAC,eAAA,CAAAlD,OAAA,EAAAgD,IAAA;MAAE3D,MAAM,GAAA4D,KAAA;MAAEE,KAAK,GAAAF,KAAA;IAAA,OAAO;MAAC5D,MAAM,EAANA,MAAM;MAAE8D,KAAK,EAALA;IAAK,CAAC;EAAA,CAAC,CAAC;EAEhG,OAAO;IACLC,gBAAgB,EAAEnB,YAAY,CAACzB,MAAM;IACrCyB,YAAY,EAAZA,YAAY;IACZa,gBAAgB,EAAhBA;GACD;AACH,CAAC;AAGD,IAAMO,qBAAqB,GAAG,SAAxBA,qBAAqBA,CAAIxD,GAAW,EAAY;EACpD,IAAI,CAACA,GAAG,EAAE;IACR,OAAO,EAAE;EACX;EACA,OAAOA,GAAG,CACPyD,SAAS,CAAC,KAAK,CAAC,CAChB3D,OAAO,CAAC,kBAAkB,EAAE,EAAE,CAAC,CAC/BA,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAClBA,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAClBgC,IAAI,EAAE;AACX,CAAC;AAKD,IAAM4B,2BAA2B,GAAG,SAA9BA,2BAA2BA,CAAIC,OAAuB,EAAI;EAAA,IAAAC,gBAAA;EAC9D,IAAI,CAACD,OAAO,EAAE;IACZ,OAAO,EAAE;EACX;EACA,QAAAC,gBAAA,GAAOD,OAAO,CAAC7D,OAAO,CAAC,8BAA8B,EAAE,EAAE,CAAC,qBAAnD8D,gBAAA,CAAqD9D,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC;AAClF,CAAC;AAED,IAAM+D,0BAA0B,GAAG,SAA7BA,0BAA0BA,CAAIF,OAAuB,EAAI;EAC7D,IAAI,CAACA,OAAO,EAAE;IACZ,OAAO,EAAE;EACX;EACA,OAAOA,OAAO,CAAC7D,OAAO,CAAC,0qPAAc,EAAE,EAAE,CAAC;AAC5C,CAAC;AAED,IAAMgE,WAAW,GAAG,SAAdA,WAAWA,CAAIC,IAAY,EAAY;EAC3CC,OAAO,CAACC,GAAG,CAAC,MAAM,EAAEF,IAAI,CAAC;EACzB,IAAMG,WAAW,GAAGH,IAAI,CAACjE,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC;EAChDkE,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEC,WAAW,CAAC;EACvC,IAAMC,mBAAmB,GAAGD,WAAW,CAACpE,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,IAAI,GAAG;EACjE,OAAOqE,mBAAmB,CAACrE,OAAO,CAAC,uBAAuB,EAAE,GAAG,CAAC;AAClE,CAAC;AAED,IAAMsE,yBAAyB,GAAG,SAA5BA,yBAAyBA,CAAIC,KAAa,EAAc;EAC5D,IAAI,CAACA,KAAK,IAAIA,KAAK,CAAC1D,MAAM,KAAK,CAAC,EAAE;IAChC,OAAO,CACL,IAAArB,MAAA,CAAAqC,SAAS,EAAC,qCAAqC,CAAC,EAChD,IAAArC,MAAA,CAAAqC,SAAS,EAAC,qCAAqC,CAAC,EAChD,IAAArC,MAAA,CAAAqC,SAAS,EAAC,qCAAqC,CAAC,EAChD,IAAArC,MAAA,CAAAqC,SAAS,EAAC,wBAAwB,CAAC,CACpC;EACH;EAEA,IAAMjC,GAAG,GAAG4E,QAAQ,CAACD,KAAK,EAAE,EAAE,CAAC;EAC/BL,OAAO,CAACC,GAAG,CAAC,KAAK,EAAEvE,GAAG,CAAC;EACvB,IAAIE,KAAK,CAACF,GAAG,CAAC,IAAIA,GAAG,KAAK,CAAC,EAAE;IAC3B,OAAO,CACL,IAAAJ,MAAA,CAAAqC,SAAS,EAAC,qCAAqC,CAAC,EAChD,IAAArC,MAAA,CAAAqC,SAAS,EAAC,qCAAqC,CAAC,EAChD,IAAArC,MAAA,CAAAqC,SAAS,EAAC,qCAAqC,CAAC,EAChD,IAAArC,MAAA,CAAAqC,SAAS,EAAC,wBAAwB,CAAC,CACpC;EACH;EAEA,IAAMhB,MAAM,GAAG0D,KAAK,CAAC1D,MAAM;EAC3B,IAAM4D,WAAW,GAAa,EAAE;EAEhC,IAAI5D,MAAM,KAAK,CAAC,EAAE;IAChB4D,WAAW,CAAChC,IAAI,CAACuB,WAAW,CAAC,CAACpE,GAAG,GAAG,KAAK,EAAE8E,QAAQ,EAAE,CAAC,CAAC;IACvDD,WAAW,CAAChC,IAAI,CAACuB,WAAW,CAAC,CAACpE,GAAG,GAAG,MAAM,EAAE8E,QAAQ,EAAE,CAAC,CAAC;IACxDD,WAAW,CAAChC,IAAI,CAACuB,WAAW,CAAC,CAACpE,GAAG,GAAG,OAAO,EAAE8E,QAAQ,EAAE,CAAC,CAAC;EAC3D,CAAC,MAAM,IAAI7D,MAAM,KAAK,CAAC,EAAE;IACvB4D,WAAW,CAAChC,IAAI,CAACuB,WAAW,CAAC,CAACpE,GAAG,GAAG,IAAI,EAAE8E,QAAQ,EAAE,CAAC,CAAC;IACtDD,WAAW,CAAChC,IAAI,CAACuB,WAAW,CAAC,CAACpE,GAAG,GAAG,KAAK,EAAE8E,QAAQ,EAAE,CAAC,CAAC;IACvDD,WAAW,CAAChC,IAAI,CAACuB,WAAW,CAAC,CAACpE,GAAG,GAAG,MAAM,EAAE8E,QAAQ,EAAE,CAAC,CAAC;EAC1D,CAAC,MAAM,IAAI7D,MAAM,KAAK,CAAC,IAAIA,MAAM,KAAK,CAAC,EAAE;IACvC4D,WAAW,CAAChC,IAAI,CAACuB,WAAW,CAAC,CAACpE,GAAG,GAAG,GAAG,EAAE8E,QAAQ,EAAE,CAAC,CAAC;IACrDD,WAAW,CAAChC,IAAI,CAACuB,WAAW,CAAC,CAACpE,GAAG,GAAG,IAAI,EAAE8E,QAAQ,EAAE,CAAC,CAAC;IACtDD,WAAW,CAAChC,IAAI,CAACuB,WAAW,CAAC,CAACpE,GAAG,GAAG,MAAM,EAAE8E,QAAQ,EAAE,CAAC,CAAC;EAC1D,CAAC,MAAM,IAAI7D,MAAM,KAAK,CAAC,IAAIA,MAAM,KAAK,CAAC,EAAE;IACvC4D,WAAW,CAAChC,IAAI,CAACuB,WAAW,CAAC,CAACpE,GAAG,GAAG,GAAG,EAAE8E,QAAQ,EAAE,CAAC,CAAC;IACrDD,WAAW,CAAChC,IAAI,CAACuB,WAAW,CAAC,CAACpE,GAAG,GAAG,IAAI,EAAE8E,QAAQ,EAAE,CAAC,CAAC;IACtDD,WAAW,CAAChC,IAAI,CAACuB,WAAW,CAAC,CAACpE,GAAG,GAAG,KAAK,EAAE8E,QAAQ,EAAE,CAAC,CAAC;EACzD,CAAC,MAAM;IACLD,WAAW,CAAChC,IAAI,CAACuB,WAAW,CAAC,CAACpE,GAAG,GAAG,EAAE,EAAE8E,QAAQ,EAAE,CAAC,CAAC;IACpDD,WAAW,CAAChC,IAAI,CAACuB,WAAW,CAAC,CAACpE,GAAG,GAAG,GAAG,EAAE8E,QAAQ,EAAE,CAAC,CAAC;EACvD;EAEAD,WAAW,CAAChC,IAAI,CAAC,IAAAjD,MAAA,CAAAqC,SAAS,EAAC,wBAAwB,CAAC,CAAC;EACrD,OAAO4C,WAAW;AACpB,CAAC;AAEDE,OAAA,CAAAtE,OAAA,GAAe;EACbZ,WAAW,EAAXA,WAAW;EACXuE,WAAW,EAAXA,WAAW;EACX/D,eAAe,EAAfA,eAAe;EACfO,eAAe,EAAfA,eAAe;EACf2B,iBAAiB,EAAjBA,iBAAiB;EACjB5B,kBAAkB,EAAlBA,kBAAkB;EAClBJ,cAAc,EAAdA,cAAc;EACdyD,2BAA2B,EAA3BA,2BAA2B;EAC3BU,yBAAyB,EAAzBA,yBAAyB;EACzBZ,qBAAqB,EAArBA,qBAAqB;EACrBK,0BAA0B,EAA1BA;CACD",
      ignoreList: []
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "0d50c21dc6d445852bed0e592c2a3cefbff0d60f"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_290w1pjv1v = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_290w1pjv1v();
var _interopRequireDefault =
/* istanbul ignore next */
(cov_290w1pjv1v().s[0]++, require("@babel/runtime/helpers/interopRequireDefault"));
var _slicedToArray2 =
/* istanbul ignore next */
(cov_290w1pjv1v().s[1]++, _interopRequireDefault(require("@babel/runtime/helpers/slicedToArray")));
var _toConsumableArray2 =
/* istanbul ignore next */
(cov_290w1pjv1v().s[2]++, _interopRequireDefault(require("@babel/runtime/helpers/toConsumableArray")));
var __importDefault =
/* istanbul ignore next */
(cov_290w1pjv1v().s[3]++,
/* istanbul ignore next */
(cov_290w1pjv1v().b[0][0]++, this) &&
/* istanbul ignore next */
(cov_290w1pjv1v().b[0][1]++, this.__importDefault) ||
/* istanbul ignore next */
(cov_290w1pjv1v().b[0][2]++, function (mod) {
  /* istanbul ignore next */
  cov_290w1pjv1v().f[0]++;
  cov_290w1pjv1v().s[4]++;
  return /* istanbul ignore next */(cov_290w1pjv1v().b[2][0]++, mod) &&
  /* istanbul ignore next */
  (cov_290w1pjv1v().b[2][1]++, mod.__esModule) ?
  /* istanbul ignore next */
  (cov_290w1pjv1v().b[1][0]++, mod) :
  /* istanbul ignore next */
  (cov_290w1pjv1v().b[1][1]++, {
    "default": mod
  });
}));
/* istanbul ignore next */
cov_290w1pjv1v().s[5]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
var moment_1 =
/* istanbul ignore next */
(cov_290w1pjv1v().s[6]++, __importDefault(require("moment")));
var i18n_1 =
/* istanbul ignore next */
(cov_290w1pjv1v().s[7]++, require("../locales/i18n"));
/* istanbul ignore next */
cov_290w1pjv1v().s[8]++;
var formatPrice = function formatPrice(amount) {
  /* istanbul ignore next */
  cov_290w1pjv1v().f[1]++;
  cov_290w1pjv1v().s[9]++;
  if (
  /* istanbul ignore next */
  (cov_290w1pjv1v().b[4][0]++, amount == null) ||
  /* istanbul ignore next */
  (cov_290w1pjv1v().b[4][1]++, amount == undefined)) {
    /* istanbul ignore next */
    cov_290w1pjv1v().b[3][0]++;
    cov_290w1pjv1v().s[10]++;
    return '0';
  } else
  /* istanbul ignore next */
  {
    cov_290w1pjv1v().b[3][1]++;
  }
  var num =
  /* istanbul ignore next */
  (cov_290w1pjv1v().s[11]++, typeof amount === 'string' ?
  /* istanbul ignore next */
  (cov_290w1pjv1v().b[5][0]++, Number(amount)) :
  /* istanbul ignore next */
  (cov_290w1pjv1v().b[5][1]++, amount));
  /* istanbul ignore next */
  cov_290w1pjv1v().s[12]++;
  if (isNaN(num)) {
    /* istanbul ignore next */
    cov_290w1pjv1v().b[6][0]++;
    cov_290w1pjv1v().s[13]++;
    return '0';
  } else
  /* istanbul ignore next */
  {
    cov_290w1pjv1v().b[6][1]++;
  }
  cov_290w1pjv1v().s[14]++;
  return num.toLocaleString('vi-VN').replace(/\./g, ',');
};
/* istanbul ignore next */
cov_290w1pjv1v().s[15]++;
var formattedNumber = function formattedNumber(str) {
  /* istanbul ignore next */
  cov_290w1pjv1v().f[2]++;
  cov_290w1pjv1v().s[16]++;
  return Number(str.replace(/,/g, ''));
};
/* istanbul ignore next */
cov_290w1pjv1v().s[17]++;
var formatDateHHMM = function formatDateHHMM(isoString) {
  /* istanbul ignore next */
  cov_290w1pjv1v().f[3]++;
  cov_290w1pjv1v().s[18]++;
  return (0, moment_1.default)(isoString).format('HH:mm');
};
/* istanbul ignore next */
cov_290w1pjv1v().s[19]++;
var formatDateDDMMYYYY = function formatDateDDMMYYYY(isoString) {
  /* istanbul ignore next */
  cov_290w1pjv1v().f[4]++;
  cov_290w1pjv1v().s[20]++;
  return (0, moment_1.default)(isoString).format('DD/MM/YYYY');
};
/* istanbul ignore next */
cov_290w1pjv1v().s[21]++;
var numberToWordsVi = function numberToWordsVi(number) {
  /* istanbul ignore next */
  cov_290w1pjv1v().f[5]++;
  var _result$trim$;
  var currency =
  /* istanbul ignore next */
  (cov_290w1pjv1v().s[22]++,
  /* istanbul ignore next */
  (cov_290w1pjv1v().b[8][0]++, arguments.length > 1) &&
  /* istanbul ignore next */
  (cov_290w1pjv1v().b[8][1]++, arguments[1] !== undefined) ?
  /* istanbul ignore next */
  (cov_290w1pjv1v().b[7][0]++, arguments[1]) :
  /* istanbul ignore next */
  (cov_290w1pjv1v().b[7][1]++, 'VND'));
  var units =
  /* istanbul ignore next */
  (cov_290w1pjv1v().s[23]++, ['', 'một', 'hai', 'ba', 'bốn', 'năm', 'sáu', 'bảy', 'tám', 'chín']);
  var teens =
  /* istanbul ignore next */
  (cov_290w1pjv1v().s[24]++, ['mười', 'mười một', 'mười hai', 'mười ba', 'mười bốn', 'mười lăm', 'mười sáu', 'mười bảy', 'mười tám', 'mười chín']);
  var tens =
  /* istanbul ignore next */
  (cov_290w1pjv1v().s[25]++, ['', '', 'hai mươi', 'ba mươi', 'bốn mươi', 'năm mươi', 'sáu mươi', 'bảy mươi', 'tám mươi', 'chín mươi']);
  var scales =
  /* istanbul ignore next */
  (cov_290w1pjv1v().s[26]++, ['', 'nghìn', 'triệu', 'tỷ', 'nghìn tỷ', 'nghìn nghìn tỷ']);
  function convertToWords(n, scaleIndex) {
    /* istanbul ignore next */
    cov_290w1pjv1v().f[6]++;
    cov_290w1pjv1v().s[27]++;
    if (n === 0) {
      /* istanbul ignore next */
      cov_290w1pjv1v().b[9][0]++;
      cov_290w1pjv1v().s[28]++;
      return '';
    } else
    /* istanbul ignore next */
    {
      cov_290w1pjv1v().b[9][1]++;
    }
    var scale =
    /* istanbul ignore next */
    (cov_290w1pjv1v().s[29]++, scales[scaleIndex]);
    var hundred =
    /* istanbul ignore next */
    (cov_290w1pjv1v().s[30]++, Math.floor(n / 100));
    var remainder =
    /* istanbul ignore next */
    (cov_290w1pjv1v().s[31]++, n % 100);
    var result =
    /* istanbul ignore next */
    (cov_290w1pjv1v().s[32]++, '');
    /* istanbul ignore next */
    cov_290w1pjv1v().s[33]++;
    if (hundred > 0) {
      /* istanbul ignore next */
      cov_290w1pjv1v().b[10][0]++;
      cov_290w1pjv1v().s[34]++;
      result += units[hundred] + ' trăm ';
    } else
    /* istanbul ignore next */
    {
      cov_290w1pjv1v().b[10][1]++;
    }
    cov_290w1pjv1v().s[35]++;
    if (remainder > 0) {
      /* istanbul ignore next */
      cov_290w1pjv1v().b[11][0]++;
      cov_290w1pjv1v().s[36]++;
      if (remainder < 10) {
        /* istanbul ignore next */
        cov_290w1pjv1v().b[12][0]++;
        cov_290w1pjv1v().s[37]++;
        result += units[remainder] + ' ';
      } else {
        /* istanbul ignore next */
        cov_290w1pjv1v().b[12][1]++;
        cov_290w1pjv1v().s[38]++;
        if (remainder < 20) {
          /* istanbul ignore next */
          cov_290w1pjv1v().b[13][0]++;
          cov_290w1pjv1v().s[39]++;
          result += teens[remainder - 10] + ' ';
        } else {
          /* istanbul ignore next */
          cov_290w1pjv1v().b[13][1]++;
          var ten =
          /* istanbul ignore next */
          (cov_290w1pjv1v().s[40]++, Math.floor(remainder / 10));
          var one =
          /* istanbul ignore next */
          (cov_290w1pjv1v().s[41]++, remainder % 10);
          /* istanbul ignore next */
          cov_290w1pjv1v().s[42]++;
          result += tens[ten] + ' ' + units[one] + ' ';
        }
      }
    } else
    /* istanbul ignore next */
    {
      cov_290w1pjv1v().b[11][1]++;
    }
    cov_290w1pjv1v().s[43]++;
    return result + scale + ' ';
  }
  /* istanbul ignore next */
  cov_290w1pjv1v().s[44]++;
  if (number === 0) {
    /* istanbul ignore next */
    cov_290w1pjv1v().b[14][0]++;
    cov_290w1pjv1v().s[45]++;
    return (0, i18n_1.translate)('utils.formatUtils.noAmount');
  } else
  /* istanbul ignore next */
  {
    cov_290w1pjv1v().b[14][1]++;
  }
  var result =
  /* istanbul ignore next */
  (cov_290w1pjv1v().s[46]++, '');
  var scaleIndex =
  /* istanbul ignore next */
  (cov_290w1pjv1v().s[47]++, 0);
  var currencyWord =
  /* istanbul ignore next */
  (cov_290w1pjv1v().s[48]++, '');
  /* istanbul ignore next */
  cov_290w1pjv1v().s[49]++;
  while (number > 0) {
    var chunk =
    /* istanbul ignore next */
    (cov_290w1pjv1v().s[50]++, number % 1000);
    /* istanbul ignore next */
    cov_290w1pjv1v().s[51]++;
    if (chunk > 0) {
      /* istanbul ignore next */
      cov_290w1pjv1v().b[15][0]++;
      cov_290w1pjv1v().s[52]++;
      result = convertToWords(chunk, scaleIndex) + result;
    } else
    /* istanbul ignore next */
    {
      cov_290w1pjv1v().b[15][1]++;
    }
    cov_290w1pjv1v().s[53]++;
    number = Math.floor(number / 1000);
    /* istanbul ignore next */
    cov_290w1pjv1v().s[54]++;
    scaleIndex++;
  }
  /* istanbul ignore next */
  cov_290w1pjv1v().s[55]++;
  if (currency === 'VND') {
    /* istanbul ignore next */
    cov_290w1pjv1v().b[16][0]++;
    cov_290w1pjv1v().s[56]++;
    currencyWord = (0, i18n_1.translate)('utils.formatUtils.currencyVnd');
  } else {
    /* istanbul ignore next */
    cov_290w1pjv1v().b[16][1]++;
    cov_290w1pjv1v().s[57]++;
    if (currency === 'USD') {
      /* istanbul ignore next */
      cov_290w1pjv1v().b[17][0]++;
      cov_290w1pjv1v().s[58]++;
      currencyWord = (0, i18n_1.translate)('utils.formatUtils.currencyUsd');
    } else
    /* istanbul ignore next */
    {
      cov_290w1pjv1v().b[17][1]++;
    }
  }
  /* istanbul ignore next */
  cov_290w1pjv1v().s[59]++;
  return ((_result$trim$ = result.trim()[0]) == null ?
  /* istanbul ignore next */
  (cov_290w1pjv1v().b[18][0]++, void 0) :
  /* istanbul ignore next */
  (cov_290w1pjv1v().b[18][1]++, _result$trim$.toUpperCase())) + result.trim().slice(1) + ' ' + currencyWord;
};
/* istanbul ignore next */
cov_290w1pjv1v().s[60]++;
var splitTransactions = function splitTransactions(amount, maxLimit247, splitAmount) {
  /* istanbul ignore next */
  cov_290w1pjv1v().f[7]++;
  var transactions =
  /* istanbul ignore next */
  (cov_290w1pjv1v().s[61]++, []);
  var splitTrans =
  /* istanbul ignore next */
  (cov_290w1pjv1v().s[62]++, Math.floor(amount / splitAmount));
  var finalTrans =
  /* istanbul ignore next */
  (cov_290w1pjv1v().s[63]++, amount % splitAmount);
  /* istanbul ignore next */
  cov_290w1pjv1v().s[64]++;
  if (
  /* istanbul ignore next */
  (cov_290w1pjv1v().b[20][0]++, finalTrans !== 0) &&
  /* istanbul ignore next */
  (cov_290w1pjv1v().b[20][1]++, finalTrans + splitAmount <= maxLimit247)) {
    /* istanbul ignore next */
    cov_290w1pjv1v().b[19][0]++;
    cov_290w1pjv1v().s[65]++;
    splitTrans--;
    /* istanbul ignore next */
    cov_290w1pjv1v().s[66]++;
    finalTrans += splitAmount;
  } else
  /* istanbul ignore next */
  {
    cov_290w1pjv1v().b[19][1]++;
  }
  cov_290w1pjv1v().s[67]++;
  if (splitTrans > 0) {
    /* istanbul ignore next */
    cov_290w1pjv1v().b[21][0]++;
    cov_290w1pjv1v().s[68]++;
    transactions.push.apply(transactions, (0, _toConsumableArray2.default)(Array(splitTrans).fill(splitAmount)));
  } else
  /* istanbul ignore next */
  {
    cov_290w1pjv1v().b[21][1]++;
  }
  cov_290w1pjv1v().s[69]++;
  if (finalTrans > 0) {
    /* istanbul ignore next */
    cov_290w1pjv1v().b[22][0]++;
    cov_290w1pjv1v().s[70]++;
    transactions.push(finalTrans);
  } else
  /* istanbul ignore next */
  {
    cov_290w1pjv1v().b[22][1]++;
  }
  var transactionItemsMap =
  /* istanbul ignore next */
  (cov_290w1pjv1v().s[71]++, new Map());
  /* istanbul ignore next */
  cov_290w1pjv1v().s[72]++;
  for (var trans of transactions) {
    /* istanbul ignore next */
    cov_290w1pjv1v().s[73]++;
    transactionItemsMap.set(trans, (
    /* istanbul ignore next */
    (cov_290w1pjv1v().b[23][0]++, transactionItemsMap.get(trans)) ||
    /* istanbul ignore next */
    (cov_290w1pjv1v().b[23][1]++, 0)) + 1);
  }
  var transactionItems =
  /* istanbul ignore next */
  (cov_290w1pjv1v().s[74]++, Array.from(transactionItemsMap, function (_ref) {
    /* istanbul ignore next */
    cov_290w1pjv1v().f[8]++;
    var _ref2 =
      /* istanbul ignore next */
      (cov_290w1pjv1v().s[75]++, (0, _slicedToArray2.default)(_ref, 2)),
      amount =
      /* istanbul ignore next */
      (cov_290w1pjv1v().s[76]++, _ref2[0]),
      count =
      /* istanbul ignore next */
      (cov_290w1pjv1v().s[77]++, _ref2[1]);
    /* istanbul ignore next */
    cov_290w1pjv1v().s[78]++;
    return {
      amount: amount,
      count: count
    };
  }));
  /* istanbul ignore next */
  cov_290w1pjv1v().s[79]++;
  return {
    totalTransaction: transactions.length,
    transactions: transactions,
    transactionItems: transactionItems
  };
};
/* istanbul ignore next */
cov_290w1pjv1v().s[80]++;
var removeVietnameseTones = function removeVietnameseTones(str) {
  /* istanbul ignore next */
  cov_290w1pjv1v().f[9]++;
  cov_290w1pjv1v().s[81]++;
  if (!str) {
    /* istanbul ignore next */
    cov_290w1pjv1v().b[24][0]++;
    cov_290w1pjv1v().s[82]++;
    return '';
  } else
  /* istanbul ignore next */
  {
    cov_290w1pjv1v().b[24][1]++;
  }
  cov_290w1pjv1v().s[83]++;
  return str.normalize('NFD').replace(/[\u0300-\u036f]/g, '').replace(/đ/g, 'd').replace(/Đ/g, 'D').trim();
};
/* istanbul ignore next */
cov_290w1pjv1v().s[84]++;
var formatRemittanceInformation = function formatRemittanceInformation(content) {
  /* istanbul ignore next */
  cov_290w1pjv1v().f[10]++;
  var _content$replace;
  /* istanbul ignore next */
  cov_290w1pjv1v().s[85]++;
  if (!content) {
    /* istanbul ignore next */
    cov_290w1pjv1v().b[25][0]++;
    cov_290w1pjv1v().s[86]++;
    return '';
  } else
  /* istanbul ignore next */
  {
    cov_290w1pjv1v().b[25][1]++;
  }
  cov_290w1pjv1v().s[87]++;
  return (_content$replace = content.replace(/[^a-zA-Z0-9À-ỹ\-\\/().+_, ]/g, '')) == null ?
  /* istanbul ignore next */
  (cov_290w1pjv1v().b[26][0]++, void 0) :
  /* istanbul ignore next */
  (cov_290w1pjv1v().b[26][1]++, _content$replace.replace(/\s+/g, ' '));
};
/* istanbul ignore next */
cov_290w1pjv1v().s[88]++;
var removeSpecialCharsAndEmoji = function removeSpecialCharsAndEmoji(content) {
  /* istanbul ignore next */
  cov_290w1pjv1v().f[11]++;
  cov_290w1pjv1v().s[89]++;
  if (!content) {
    /* istanbul ignore next */
    cov_290w1pjv1v().b[27][0]++;
    cov_290w1pjv1v().s[90]++;
    return '';
  } else
  /* istanbul ignore next */
  {
    cov_290w1pjv1v().b[27][1]++;
  }
  cov_290w1pjv1v().s[91]++;
  return content.replace(/(?:[\0-\x1F!-@\[-`\{-\xA9\xAB-\xB4\xB6-\xB9\xBB-\xBF\xD7\xF7\u02C2-\u02C5\u02D2-\u02DF\u02E5-\u02EB\u02ED\u02EF-\u036F\u0375\u0378\u0379\u037E\u0380-\u0385\u0387\u038B\u038D\u03A2\u03F6\u0482-\u0489\u0530\u0557\u0558\u055A-\u055F\u0589-\u05CF\u05EB-\u05EE\u05F3-\u061F\u064B-\u066D\u0670\u06D4\u06D6-\u06E4\u06E7-\u06ED\u06F0-\u06F9\u06FD\u06FE\u0700-\u070F\u0711\u0730-\u074C\u07A6-\u07B0\u07B2-\u07C9\u07EB-\u07F3\u07F6-\u07F9\u07FB-\u07FF\u0816-\u0819\u081B-\u0823\u0825-\u0827\u0829-\u083F\u0859-\u085F\u086B-\u086F\u0888\u088F-\u089F\u08CA-\u0903\u093A-\u093C\u093E-\u094F\u0951-\u0957\u0962-\u0970\u0981-\u0984\u098D\u098E\u0991\u0992\u09A9\u09B1\u09B3-\u09B5\u09BA-\u09BC\u09BE-\u09CD\u09CF-\u09DB\u09DE\u09E2-\u09EF\u09F2-\u09FB\u09FD-\u0A04\u0A0B-\u0A0E\u0A11\u0A12\u0A29\u0A31\u0A34\u0A37\u0A3A-\u0A58\u0A5D\u0A5F-\u0A71\u0A75-\u0A84\u0A8E\u0A92\u0AA9\u0AB1\u0AB4\u0ABA-\u0ABC\u0ABE-\u0ACF\u0AD1-\u0ADF\u0AE2-\u0AF8\u0AFA-\u0B04\u0B0D\u0B0E\u0B11\u0B12\u0B29\u0B31\u0B34\u0B3A-\u0B3C\u0B3E-\u0B5B\u0B5E\u0B62-\u0B70\u0B72-\u0B82\u0B84\u0B8B-\u0B8D\u0B91\u0B96-\u0B98\u0B9B\u0B9D\u0BA0-\u0BA2\u0BA5-\u0BA7\u0BAB-\u0BAD\u0BBA-\u0BCF\u0BD1-\u0C04\u0C0D\u0C11\u0C29\u0C3A-\u0C3C\u0C3E-\u0C57\u0C5B\u0C5C\u0C5E\u0C5F\u0C62-\u0C7F\u0C81-\u0C84\u0C8D\u0C91\u0CA9\u0CB4\u0CBA-\u0CBC\u0CBE-\u0CDC\u0CDF\u0CE2-\u0CF0\u0CF3-\u0D03\u0D0D\u0D11\u0D3B\u0D3C\u0D3E-\u0D4D\u0D4F-\u0D53\u0D57-\u0D5E\u0D62-\u0D79\u0D80-\u0D84\u0D97-\u0D99\u0DB2\u0DBC\u0DBE\u0DBF\u0DC7-\u0E00\u0E31\u0E34-\u0E3F\u0E47-\u0E80\u0E83\u0E85\u0E8B\u0EA4\u0EA6\u0EB1\u0EB4-\u0EBC\u0EBE\u0EBF\u0EC5\u0EC7-\u0EDB\u0EE0-\u0EFF\u0F01-\u0F3F\u0F48\u0F6D-\u0F87\u0F8D-\u0FFF\u102B-\u103E\u1040-\u104F\u1056-\u1059\u105E-\u1060\u1062-\u1064\u1067-\u106D\u1071-\u1074\u1082-\u108D\u108F-\u109F\u10C6\u10C8-\u10CC\u10CE\u10CF\u10FB\u1249\u124E\u124F\u1257\u1259\u125E\u125F\u1289\u128E\u128F\u12B1\u12B6\u12B7\u12BF\u12C1\u12C6\u12C7\u12D7\u1311\u1316\u1317\u135B-\u137F\u1390-\u139F\u13F6\u13F7\u13FE-\u1400\u166D\u166E\u1680\u169B-\u169F\u16EB-\u16F0\u16F9-\u16FF\u1712-\u171E\u1732-\u173F\u1752-\u175F\u176D\u1771-\u177F\u17B4-\u17D6\u17D8-\u17DB\u17DD-\u181F\u1879-\u187F\u1885\u1886\u18A9\u18AB-\u18AF\u18F6-\u18FF\u191F-\u194F\u196E\u196F\u1975-\u197F\u19AC-\u19AF\u19CA-\u19FF\u1A17-\u1A1F\u1A55-\u1AA6\u1AA8-\u1B04\u1B34-\u1B44\u1B4D-\u1B82\u1BA1-\u1BAD\u1BB0-\u1BB9\u1BE6-\u1BFF\u1C24-\u1C4C\u1C50-\u1C59\u1C7E\u1C7F\u1C8B-\u1C8F\u1CBB\u1CBC\u1CC0-\u1CE8\u1CED\u1CF4\u1CF7-\u1CF9\u1CFB-\u1CFF\u1DC0-\u1DFF\u1F16\u1F17\u1F1E\u1F1F\u1F46\u1F47\u1F4E\u1F4F\u1F58\u1F5A\u1F5C\u1F5E\u1F7E\u1F7F\u1FB5\u1FBD\u1FBF-\u1FC1\u1FC5\u1FCD-\u1FCF\u1FD4\u1FD5\u1FDC-\u1FDF\u1FED-\u1FF1\u1FF5\u1FFD-\u2070\u2072-\u207E\u2080-\u208F\u209D-\u2101\u2103-\u2106\u2108\u2109\u2114\u2116-\u2118\u211E-\u2123\u2125\u2127\u2129\u212E\u213A\u213B\u2140-\u2144\u214A-\u214D\u214F-\u2182\u2185-\u2BFF\u2CE5-\u2CEA\u2CEF-\u2CF1\u2CF4-\u2CFF\u2D26\u2D28-\u2D2C\u2D2E\u2D2F\u2D68-\u2D6E\u2D70-\u2D7F\u2D97-\u2D9F\u2DA7\u2DAF\u2DB7\u2DBF\u2DC7\u2DCF\u2DD7\u2DDF-\u2E2E\u2E30-\u3004\u3007-\u3030\u3036-\u303A\u303D-\u3040\u3097-\u309C\u30A0\u30FB\u3100-\u3104\u3130\u318F-\u319F\u31C0-\u31EF\u3200-\u33FF\u4DC0-\u4DFF\uA48D-\uA4CF\uA4FE\uA4FF\uA60D-\uA60F\uA620-\uA629\uA62C-\uA63F\uA66F-\uA67E\uA69E\uA69F\uA6E6-\uA716\uA720\uA721\uA789\uA78A\uA7CE\uA7CF\uA7D2\uA7D4\uA7DD-\uA7F1\uA802\uA806\uA80B\uA823-\uA83F\uA874-\uA881\uA8B4-\uA8F1\uA8F8-\uA8FA\uA8FC\uA8FF-\uA909\uA926-\uA92F\uA947-\uA95F\uA97D-\uA983\uA9B3-\uA9CE\uA9D0-\uA9DF\uA9E5\uA9F0-\uA9F9\uA9FF\uAA29-\uAA3F\uAA43\uAA4C-\uAA5F\uAA77-\uAA79\uAA7B-\uAA7D\uAAB0\uAAB2-\uAAB4\uAAB7\uAAB8\uAABE\uAABF\uAAC1\uAAC3-\uAADA\uAADE\uAADF\uAAEB-\uAAF1\uAAF5-\uAB00\uAB07\uAB08\uAB0F\uAB10\uAB17-\uAB1F\uAB27\uAB2F\uAB5B\uAB6A-\uAB6F\uABE3-\uABFF\uD7A4-\uD7AF\uD7C7-\uD7CA\uD7FC-\uD7FF\uE000-\uF8FF\uFA6E\uFA6F\uFADA-\uFAFF\uFB07-\uFB12\uFB18-\uFB1C\uFB1E\uFB29\uFB37\uFB3D\uFB3F\uFB42\uFB45\uFBB2-\uFBD2\uFD3E-\uFD4F\uFD90\uFD91\uFDC8-\uFDEF\uFDFC-\uFE6F\uFE75\uFEFD-\uFF20\uFF3B-\uFF40\uFF5B-\uFF65\uFFBF-\uFFC1\uFFC8\uFFC9\uFFD0\uFFD1\uFFD8\uFFD9\uFFDD-\uFFFF]|\uD800[\uDC0C\uDC27\uDC3B\uDC3E\uDC4E\uDC4F\uDC5E-\uDC7F\uDCFB-\uDE7F\uDE9D-\uDE9F\uDED1-\uDEFF\uDF20-\uDF2C\uDF41\uDF4A-\uDF4F\uDF76-\uDF7F\uDF9E\uDF9F\uDFC4-\uDFC7\uDFD0-\uDFFF]|\uD801[\uDC9E-\uDCAF\uDCD4-\uDCD7\uDCFC-\uDCFF\uDD28-\uDD2F\uDD64-\uDD6F\uDD7B\uDD8B\uDD93\uDD96\uDDA2\uDDB2\uDDBA\uDDBD-\uDDBF\uDDF4-\uDDFF\uDF37-\uDF3F\uDF56-\uDF5F\uDF68-\uDF7F\uDF86\uDFB1\uDFBB-\uDFFF]|\uD802[\uDC06\uDC07\uDC09\uDC36\uDC39-\uDC3B\uDC3D\uDC3E\uDC56-\uDC5F\uDC77-\uDC7F\uDC9F-\uDCDF\uDCF3\uDCF6-\uDCFF\uDD16-\uDD1F\uDD3A-\uDD7F\uDDB8-\uDDBD\uDDC0-\uDDFF\uDE01-\uDE0F\uDE14\uDE18\uDE36-\uDE5F\uDE7D-\uDE7F\uDE9D-\uDEBF\uDEC8\uDEE5-\uDEFF\uDF36-\uDF3F\uDF56-\uDF5F\uDF73-\uDF7F\uDF92-\uDFFF]|\uD803[\uDC49-\uDC7F\uDCB3-\uDCBF\uDCF3-\uDCFF\uDD24-\uDD49\uDD66-\uDD6E\uDD86-\uDE7F\uDEAA-\uDEAF\uDEB2-\uDEC1\uDEC5-\uDEFF\uDF1D-\uDF26\uDF28-\uDF2F\uDF46-\uDF6F\uDF82-\uDFAF\uDFC5-\uDFDF\uDFF7-\uDFFF]|\uD804[\uDC00-\uDC02\uDC38-\uDC70\uDC73\uDC74\uDC76-\uDC82\uDCB0-\uDCCF\uDCE9-\uDD02\uDD27-\uDD43\uDD45\uDD46\uDD48-\uDD4F\uDD73-\uDD75\uDD77-\uDD82\uDDB3-\uDDC0\uDDC5-\uDDD9\uDDDB\uDDDD-\uDDFF\uDE12\uDE2C-\uDE3E\uDE41-\uDE7F\uDE87\uDE89\uDE8E\uDE9E\uDEA9-\uDEAF\uDEDF-\uDF04\uDF0D\uDF0E\uDF11\uDF12\uDF29\uDF31\uDF34\uDF3A-\uDF3C\uDF3E-\uDF4F\uDF51-\uDF5C\uDF62-\uDF7F\uDF8A\uDF8C\uDF8D\uDF8F\uDFB6\uDFB8-\uDFD0\uDFD2\uDFD4-\uDFFF]|\uD805[\uDC35-\uDC46\uDC4B-\uDC5E\uDC62-\uDC7F\uDCB0-\uDCC3\uDCC6\uDCC8-\uDD7F\uDDAF-\uDDD7\uDDDC-\uDDFF\uDE30-\uDE43\uDE45-\uDE7F\uDEAB-\uDEB7\uDEB9-\uDEFF\uDF1B-\uDF3F\uDF47-\uDFFF]|\uD806[\uDC2C-\uDC9F\uDCE0-\uDCFE\uDD07\uDD08\uDD0A\uDD0B\uDD14\uDD17\uDD30-\uDD3E\uDD40\uDD42-\uDD9F\uDDA8\uDDA9\uDDD1-\uDDE0\uDDE2\uDDE4-\uDDFF\uDE01-\uDE0A\uDE33-\uDE39\uDE3B-\uDE4F\uDE51-\uDE5B\uDE8A-\uDE9C\uDE9E-\uDEAF\uDEF9-\uDFBF\uDFE1-\uDFFF]|\uD807[\uDC09\uDC2F-\uDC3F\uDC41-\uDC71\uDC90-\uDCFF\uDD07\uDD0A\uDD31-\uDD45\uDD47-\uDD5F\uDD66\uDD69\uDD8A-\uDD97\uDD99-\uDEDF\uDEF3-\uDF01\uDF03\uDF11\uDF34-\uDFAF\uDFB1-\uDFFF]|\uD808[\uDF9A-\uDFFF]|\uD809[\uDC00-\uDC7F\uDD44-\uDFFF]|[\uD80A\uD812-\uD817\uD819\uD824-\uD82A\uD82D\uD82E\uD830-\uD834\uD836\uD83C-\uD83F\uD87C\uD87D\uD87F\uD889-\uDBFF][\uDC00-\uDFFF]|\uD80B[\uDC00-\uDF8F\uDFF1-\uDFFF]|\uD80D[\uDC30-\uDC40\uDC47-\uDC5F]|\uD810[\uDFFB-\uDFFF]|\uD811[\uDE47-\uDFFF]|\uD818[\uDC00-\uDCFF\uDD1E-\uDFFF]|\uD81A[\uDE39-\uDE3F\uDE5F-\uDE6F\uDEBF-\uDECF\uDEEE-\uDEFF\uDF30-\uDF3F\uDF44-\uDF62\uDF78-\uDF7C\uDF90-\uDFFF]|\uD81B[\uDC00-\uDD3F\uDD6D-\uDE3F\uDE80-\uDEFF\uDF4B-\uDF4F\uDF51-\uDF92\uDFA0-\uDFDF\uDFE2\uDFE4-\uDFFF]|\uD821[\uDFF8-\uDFFF]|\uD823[\uDCD6-\uDCFE\uDD09-\uDFFF]|\uD82B[\uDC00-\uDFEF\uDFF4\uDFFC\uDFFF]|\uD82C[\uDD23-\uDD31\uDD33-\uDD4F\uDD53\uDD54\uDD56-\uDD63\uDD68-\uDD6F\uDEFC-\uDFFF]|\uD82F[\uDC6B-\uDC6F\uDC7D-\uDC7F\uDC89-\uDC8F\uDC9A-\uDFFF]|\uD835[\uDC55\uDC9D\uDCA0\uDCA1\uDCA3\uDCA4\uDCA7\uDCA8\uDCAD\uDCBA\uDCBC\uDCC4\uDD06\uDD0B\uDD0C\uDD15\uDD1D\uDD3A\uDD3F\uDD45\uDD47-\uDD49\uDD51\uDEA6\uDEA7\uDEC1\uDEDB\uDEFB\uDF15\uDF35\uDF4F\uDF6F\uDF89\uDFA9\uDFC3\uDFCC-\uDFFF]|\uD837[\uDC00-\uDEFF\uDF1F-\uDF24\uDF2B-\uDFFF]|\uD838[\uDC00-\uDC2F\uDC6E-\uDCFF\uDD2D-\uDD36\uDD3E-\uDD4D\uDD4F-\uDE8F\uDEAE-\uDEBF\uDEEC-\uDFFF]|\uD839[\uDC00-\uDCCF\uDCEC-\uDDCF\uDDEE\uDDEF\uDDF1-\uDFDF\uDFE7\uDFEC\uDFEF\uDFFF]|\uD83A[\uDCC5-\uDCFF\uDD44-\uDD4A\uDD4C-\uDFFF]|\uD83B[\uDC00-\uDDFF\uDE04\uDE20\uDE23\uDE25\uDE26\uDE28\uDE33\uDE38\uDE3A\uDE3C-\uDE41\uDE43-\uDE46\uDE48\uDE4A\uDE4C\uDE50\uDE53\uDE55\uDE56\uDE58\uDE5A\uDE5C\uDE5E\uDE60\uDE63\uDE65\uDE66\uDE6B\uDE73\uDE78\uDE7D\uDE7F\uDE8A\uDE9C-\uDEA0\uDEA4\uDEAA\uDEBC-\uDFFF]|\uD869[\uDEE0-\uDEFF]|\uD86D[\uDF3A-\uDF3F]|\uD86E[\uDC1E\uDC1F]|\uD873[\uDEA2-\uDEAF]|\uD87A[\uDFE1-\uDFEF]|\uD87B[\uDE5E-\uDFFF]|\uD87E[\uDE1E-\uDFFF]|\uD884[\uDF4B-\uDF4F]|\uD888[\uDFB0-\uDFFF]|[\uD800-\uDBFF](?![\uDC00-\uDFFF])|(?:[^\uD800-\uDBFF]|^)[\uDC00-\uDFFF])+/g, '');
};
/* istanbul ignore next */
cov_290w1pjv1v().s[92]++;
var formatMoney = function formatMoney(text) {
  /* istanbul ignore next */
  cov_290w1pjv1v().f[12]++;
  cov_290w1pjv1v().s[93]++;
  console.log('text', text);
  var cleanedText =
  /* istanbul ignore next */
  (cov_290w1pjv1v().s[94]++, text.replace(/[^0-9.]/g, ''));
  /* istanbul ignore next */
  cov_290w1pjv1v().s[95]++;
  console.log('cleanedText', cleanedText);
  var withoutLeadingZeros =
  /* istanbul ignore next */
  (cov_290w1pjv1v().s[96]++,
  /* istanbul ignore next */
  (cov_290w1pjv1v().b[28][0]++, cleanedText.replace(/^0+/, '')) ||
  /* istanbul ignore next */
  (cov_290w1pjv1v().b[28][1]++, '0'));
  /* istanbul ignore next */
  cov_290w1pjv1v().s[97]++;
  return withoutLeadingZeros.replace(/\B(?=(\d{3})+(?!\d))/g, ',');
};
/* istanbul ignore next */
cov_290w1pjv1v().s[98]++;
var generateAmountSuggestions = function generateAmountSuggestions(input) {
  /* istanbul ignore next */
  cov_290w1pjv1v().f[13]++;
  cov_290w1pjv1v().s[99]++;
  if (
  /* istanbul ignore next */
  (cov_290w1pjv1v().b[30][0]++, !input) ||
  /* istanbul ignore next */
  (cov_290w1pjv1v().b[30][1]++, input.length === 0)) {
    /* istanbul ignore next */
    cov_290w1pjv1v().b[29][0]++;
    cov_290w1pjv1v().s[100]++;
    return [(0, i18n_1.translate)('utils.formatUtils.amountSuggestion1'), (0, i18n_1.translate)('utils.formatUtils.amountSuggestion2'), (0, i18n_1.translate)('utils.formatUtils.amountSuggestion3'), (0, i18n_1.translate)('utils.formatUtils.done')];
  } else
  /* istanbul ignore next */
  {
    cov_290w1pjv1v().b[29][1]++;
  }
  var num =
  /* istanbul ignore next */
  (cov_290w1pjv1v().s[101]++, parseInt(input, 10));
  /* istanbul ignore next */
  cov_290w1pjv1v().s[102]++;
  console.log('num', num);
  /* istanbul ignore next */
  cov_290w1pjv1v().s[103]++;
  if (
  /* istanbul ignore next */
  (cov_290w1pjv1v().b[32][0]++, isNaN(num)) ||
  /* istanbul ignore next */
  (cov_290w1pjv1v().b[32][1]++, num === 0)) {
    /* istanbul ignore next */
    cov_290w1pjv1v().b[31][0]++;
    cov_290w1pjv1v().s[104]++;
    return [(0, i18n_1.translate)('utils.formatUtils.amountSuggestion1'), (0, i18n_1.translate)('utils.formatUtils.amountSuggestion2'), (0, i18n_1.translate)('utils.formatUtils.amountSuggestion3'), (0, i18n_1.translate)('utils.formatUtils.done')];
  } else
  /* istanbul ignore next */
  {
    cov_290w1pjv1v().b[31][1]++;
  }
  var length =
  /* istanbul ignore next */
  (cov_290w1pjv1v().s[105]++, input.length);
  var suggestions =
  /* istanbul ignore next */
  (cov_290w1pjv1v().s[106]++, []);
  /* istanbul ignore next */
  cov_290w1pjv1v().s[107]++;
  if (length === 1) {
    /* istanbul ignore next */
    cov_290w1pjv1v().b[33][0]++;
    cov_290w1pjv1v().s[108]++;
    suggestions.push(formatMoney((num * 10000).toString()));
    /* istanbul ignore next */
    cov_290w1pjv1v().s[109]++;
    suggestions.push(formatMoney((num * 100000).toString()));
    /* istanbul ignore next */
    cov_290w1pjv1v().s[110]++;
    suggestions.push(formatMoney((num * 1000000).toString()));
  } else {
    /* istanbul ignore next */
    cov_290w1pjv1v().b[33][1]++;
    cov_290w1pjv1v().s[111]++;
    if (length === 2) {
      /* istanbul ignore next */
      cov_290w1pjv1v().b[34][0]++;
      cov_290w1pjv1v().s[112]++;
      suggestions.push(formatMoney((num * 1000).toString()));
      /* istanbul ignore next */
      cov_290w1pjv1v().s[113]++;
      suggestions.push(formatMoney((num * 10000).toString()));
      /* istanbul ignore next */
      cov_290w1pjv1v().s[114]++;
      suggestions.push(formatMoney((num * 100000).toString()));
    } else {
      /* istanbul ignore next */
      cov_290w1pjv1v().b[34][1]++;
      cov_290w1pjv1v().s[115]++;
      if (
      /* istanbul ignore next */
      (cov_290w1pjv1v().b[36][0]++, length === 3) ||
      /* istanbul ignore next */
      (cov_290w1pjv1v().b[36][1]++, length === 4)) {
        /* istanbul ignore next */
        cov_290w1pjv1v().b[35][0]++;
        cov_290w1pjv1v().s[116]++;
        suggestions.push(formatMoney((num * 100).toString()));
        /* istanbul ignore next */
        cov_290w1pjv1v().s[117]++;
        suggestions.push(formatMoney((num * 1000).toString()));
        /* istanbul ignore next */
        cov_290w1pjv1v().s[118]++;
        suggestions.push(formatMoney((num * 100000).toString()));
      } else {
        /* istanbul ignore next */
        cov_290w1pjv1v().b[35][1]++;
        cov_290w1pjv1v().s[119]++;
        if (
        /* istanbul ignore next */
        (cov_290w1pjv1v().b[38][0]++, length === 5) ||
        /* istanbul ignore next */
        (cov_290w1pjv1v().b[38][1]++, length === 6)) {
          /* istanbul ignore next */
          cov_290w1pjv1v().b[37][0]++;
          cov_290w1pjv1v().s[120]++;
          suggestions.push(formatMoney((num * 100).toString()));
          /* istanbul ignore next */
          cov_290w1pjv1v().s[121]++;
          suggestions.push(formatMoney((num * 1000).toString()));
          /* istanbul ignore next */
          cov_290w1pjv1v().s[122]++;
          suggestions.push(formatMoney((num * 10000).toString()));
        } else {
          /* istanbul ignore next */
          cov_290w1pjv1v().b[37][1]++;
          cov_290w1pjv1v().s[123]++;
          suggestions.push(formatMoney((num * 10).toString()));
          /* istanbul ignore next */
          cov_290w1pjv1v().s[124]++;
          suggestions.push(formatMoney((num * 100).toString()));
        }
      }
    }
  }
  /* istanbul ignore next */
  cov_290w1pjv1v().s[125]++;
  suggestions.push((0, i18n_1.translate)('utils.formatUtils.done'));
  /* istanbul ignore next */
  cov_290w1pjv1v().s[126]++;
  return suggestions;
};
/* istanbul ignore next */
cov_290w1pjv1v().s[127]++;
exports.default = {
  formatPrice: formatPrice,
  formatMoney: formatMoney,
  formattedNumber: formattedNumber,
  numberToWordsVi: numberToWordsVi,
  splitTransactions: splitTransactions,
  formatDateDDMMYYYY: formatDateDDMMYYYY,
  formatDateHHMM: formatDateHHMM,
  formatRemittanceInformation: formatRemittanceInformation,
  generateAmountSuggestions: generateAmountSuggestions,
  removeVietnameseTones: removeVietnameseTones,
  removeSpecialCharsAndEmoji: removeSpecialCharsAndEmoji
};
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJjb3ZfMjkwdzFwanYxdiIsImFjdHVhbENvdmVyYWdlIiwibW9tZW50XzEiLCJzIiwiX19pbXBvcnREZWZhdWx0IiwicmVxdWlyZSIsImkxOG5fMSIsImZvcm1hdFByaWNlIiwiYW1vdW50IiwiZiIsImIiLCJ1bmRlZmluZWQiLCJudW0iLCJOdW1iZXIiLCJpc05hTiIsInRvTG9jYWxlU3RyaW5nIiwicmVwbGFjZSIsImZvcm1hdHRlZE51bWJlciIsInN0ciIsImZvcm1hdERhdGVISE1NIiwiaXNvU3RyaW5nIiwiZGVmYXVsdCIsImZvcm1hdCIsImZvcm1hdERhdGVERE1NWVlZWSIsIm51bWJlclRvV29yZHNWaSIsIm51bWJlciIsIl9yZXN1bHQkdHJpbSQiLCJjdXJyZW5jeSIsImFyZ3VtZW50cyIsImxlbmd0aCIsInVuaXRzIiwidGVlbnMiLCJ0ZW5zIiwic2NhbGVzIiwiY29udmVydFRvV29yZHMiLCJuIiwic2NhbGVJbmRleCIsInNjYWxlIiwiaHVuZHJlZCIsIk1hdGgiLCJmbG9vciIsInJlbWFpbmRlciIsInJlc3VsdCIsInRlbiIsIm9uZSIsInRyYW5zbGF0ZSIsImN1cnJlbmN5V29yZCIsImNodW5rIiwidHJpbSIsInRvVXBwZXJDYXNlIiwic2xpY2UiLCJzcGxpdFRyYW5zYWN0aW9ucyIsIm1heExpbWl0MjQ3Iiwic3BsaXRBbW91bnQiLCJ0cmFuc2FjdGlvbnMiLCJzcGxpdFRyYW5zIiwiZmluYWxUcmFucyIsInB1c2giLCJhcHBseSIsIl90b0NvbnN1bWFibGVBcnJheTIiLCJBcnJheSIsImZpbGwiLCJ0cmFuc2FjdGlvbkl0ZW1zTWFwIiwiTWFwIiwidHJhbnMiLCJzZXQiLCJnZXQiLCJ0cmFuc2FjdGlvbkl0ZW1zIiwiZnJvbSIsIl9yZWYiLCJfcmVmMiIsIl9zbGljZWRUb0FycmF5MiIsImNvdW50IiwidG90YWxUcmFuc2FjdGlvbiIsInJlbW92ZVZpZXRuYW1lc2VUb25lcyIsIm5vcm1hbGl6ZSIsImZvcm1hdFJlbWl0dGFuY2VJbmZvcm1hdGlvbiIsImNvbnRlbnQiLCJfY29udGVudCRyZXBsYWNlIiwicmVtb3ZlU3BlY2lhbENoYXJzQW5kRW1vamkiLCJmb3JtYXRNb25leSIsInRleHQiLCJjb25zb2xlIiwibG9nIiwiY2xlYW5lZFRleHQiLCJ3aXRob3V0TGVhZGluZ1plcm9zIiwiZ2VuZXJhdGVBbW91bnRTdWdnZXN0aW9ucyIsImlucHV0IiwicGFyc2VJbnQiLCJzdWdnZXN0aW9ucyIsInRvU3RyaW5nIiwiZXhwb3J0cyJdLCJzb3VyY2VzIjpbIi9Vc2Vycy9zb25zYW1zZXQvcHJvamVjdHMvcmVhY3QtbmF0aXZlL3BheW1lbnQtbW9kdWxlL3NyYy91dGlscy9Gb3JtYXRVdGlscy50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgbW9tZW50IGZyb20gJ21vbWVudCc7XG5pbXBvcnQge3RyYW5zbGF0ZX0gZnJvbSAnLi4vbG9jYWxlcy9pMThuJztcblxuY29uc3QgZm9ybWF0UHJpY2UgPSAoYW1vdW50OiBudW1iZXIgfCBzdHJpbmcgfCBudWxsIHwgdW5kZWZpbmVkKSA9PiB7XG4gIGlmIChhbW91bnQgPT0gbnVsbCB8fCBhbW91bnQgPT0gdW5kZWZpbmVkKSB7XG4gICAgcmV0dXJuICcwJztcbiAgfVxuICBjb25zdCBudW0gPSB0eXBlb2YgYW1vdW50ID09PSAnc3RyaW5nJyA/IE51bWJlcihhbW91bnQpIDogYW1vdW50O1xuICBpZiAoaXNOYU4obnVtKSkge1xuICAgIHJldHVybiAnMCc7XG4gIH1cbiAgcmV0dXJuIG51bS50b0xvY2FsZVN0cmluZygndmktVk4nKS5yZXBsYWNlKC9cXC4vZywgJywnKTtcbn07XG5jb25zdCBmb3JtYXR0ZWROdW1iZXIgPSAoc3RyOiBzdHJpbmcpOiBudW1iZXIgPT4ge1xuICByZXR1cm4gTnVtYmVyKHN0ci5yZXBsYWNlKC8sL2csICcnKSk7XG59O1xuXG5jb25zdCBmb3JtYXREYXRlSEhNTSA9IChpc29TdHJpbmc6IHN0cmluZyk6IHN0cmluZyA9PiB7XG4gIHJldHVybiBtb21lbnQoaXNvU3RyaW5nKS5mb3JtYXQoJ0hIOm1tJyk7XG59O1xuXG5jb25zdCBmb3JtYXREYXRlRERNTVlZWVkgPSAoaXNvU3RyaW5nOiBzdHJpbmcpOiBzdHJpbmcgPT4ge1xuICByZXR1cm4gbW9tZW50KGlzb1N0cmluZykuZm9ybWF0KCdERC9NTS9ZWVlZJyk7XG59O1xuXG5jb25zdCBudW1iZXJUb1dvcmRzVmkgPSAobnVtYmVyOiBudW1iZXIsIGN1cnJlbmN5ID0gJ1ZORCcpID0+IHtcbiAgY29uc3QgdW5pdHMgPSBbJycsICdt4buZdCcsICdoYWknLCAnYmEnLCAnYuG7kW4nLCAnbsSDbScsICdzw6F1JywgJ2LhuqN5JywgJ3TDoW0nLCAnY2jDrW4nXTtcbiAgY29uc3QgdGVlbnMgPSBbXG4gICAgJ23GsOG7nWknLFxuICAgICdtxrDhu51pIG3hu5l0JyxcbiAgICAnbcaw4budaSBoYWknLFxuICAgICdtxrDhu51pIGJhJyxcbiAgICAnbcaw4budaSBi4buRbicsXG4gICAgJ23GsOG7nWkgbMSDbScsXG4gICAgJ23GsOG7nWkgc8OhdScsXG4gICAgJ23GsOG7nWkgYuG6o3knLFxuICAgICdtxrDhu51pIHTDoW0nLFxuICAgICdtxrDhu51pIGNow61uJyxcbiAgXTtcbiAgY29uc3QgdGVucyA9IFsnJywgJycsICdoYWkgbcawxqFpJywgJ2JhIG3GsMahaScsICdi4buRbiBtxrDGoWknLCAnbsSDbSBtxrDGoWknLCAnc8OhdSBtxrDGoWknLCAnYuG6o3kgbcawxqFpJywgJ3TDoW0gbcawxqFpJywgJ2Now61uIG3GsMahaSddO1xuICBjb25zdCBzY2FsZXMgPSBbJycsICduZ2jDrG4nLCAndHJp4buHdScsICd04bu3JywgJ25naMOsbiB04bu3JywgJ25naMOsbiBuZ2jDrG4gdOG7tyddO1xuXG4gIGZ1bmN0aW9uIGNvbnZlcnRUb1dvcmRzKG46IGFueSwgc2NhbGVJbmRleDogbnVtYmVyKSB7XG4gICAgaWYgKG4gPT09IDApIHtcbiAgICAgIHJldHVybiAnJztcbiAgICB9XG5cbiAgICBjb25zdCBzY2FsZSA9IHNjYWxlc1tzY2FsZUluZGV4XTtcbiAgICBjb25zdCBodW5kcmVkID0gTWF0aC5mbG9vcihuIC8gMTAwKTtcbiAgICBjb25zdCByZW1haW5kZXIgPSBuICUgMTAwO1xuXG4gICAgbGV0IHJlc3VsdCA9ICcnO1xuXG4gICAgaWYgKGh1bmRyZWQgPiAwKSB7XG4gICAgICByZXN1bHQgKz0gdW5pdHNbaHVuZHJlZF0gKyAnIHRyxINtICc7XG4gICAgfVxuXG4gICAgaWYgKHJlbWFpbmRlciA+IDApIHtcbiAgICAgIGlmIChyZW1haW5kZXIgPCAxMCkge1xuICAgICAgICByZXN1bHQgKz0gdW5pdHNbcmVtYWluZGVyXSArICcgJztcbiAgICAgIH0gZWxzZSBpZiAocmVtYWluZGVyIDwgMjApIHtcbiAgICAgICAgcmVzdWx0ICs9IHRlZW5zW3JlbWFpbmRlciAtIDEwXSArICcgJztcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIGNvbnN0IHRlbiA9IE1hdGguZmxvb3IocmVtYWluZGVyIC8gMTApO1xuICAgICAgICBjb25zdCBvbmUgPSByZW1haW5kZXIgJSAxMDtcbiAgICAgICAgcmVzdWx0ICs9IHRlbnNbdGVuXSArICcgJyArIHVuaXRzW29uZV0gKyAnICc7XG4gICAgICB9XG4gICAgfVxuXG4gICAgcmV0dXJuIHJlc3VsdCArIHNjYWxlICsgJyAnO1xuICB9XG5cbiAgaWYgKG51bWJlciA9PT0gMCkge1xuICAgIHJldHVybiB0cmFuc2xhdGUoJ3V0aWxzLmZvcm1hdFV0aWxzLm5vQW1vdW50Jyk7XG4gIH1cblxuICBsZXQgcmVzdWx0ID0gJyc7XG4gIGxldCBzY2FsZUluZGV4ID0gMDtcbiAgbGV0IGN1cnJlbmN5V29yZCA9ICcnO1xuXG4gIHdoaWxlIChudW1iZXIgPiAwKSB7XG4gICAgY29uc3QgY2h1bmsgPSBudW1iZXIgJSAxMDAwO1xuICAgIGlmIChjaHVuayA+IDApIHtcbiAgICAgIHJlc3VsdCA9IGNvbnZlcnRUb1dvcmRzKGNodW5rLCBzY2FsZUluZGV4KSArIHJlc3VsdDtcbiAgICB9XG4gICAgbnVtYmVyID0gTWF0aC5mbG9vcihudW1iZXIgLyAxMDAwKTtcbiAgICBzY2FsZUluZGV4Kys7XG4gIH1cblxuICBpZiAoY3VycmVuY3kgPT09ICdWTkQnKSB7XG4gICAgY3VycmVuY3lXb3JkID0gdHJhbnNsYXRlKCd1dGlscy5mb3JtYXRVdGlscy5jdXJyZW5jeVZuZCcpO1xuICB9IGVsc2UgaWYgKGN1cnJlbmN5ID09PSAnVVNEJykge1xuICAgIGN1cnJlbmN5V29yZCA9IHRyYW5zbGF0ZSgndXRpbHMuZm9ybWF0VXRpbHMuY3VycmVuY3lVc2QnKTtcbiAgfVxuXG4gIHJldHVybiByZXN1bHQudHJpbSgpWzBdPy50b1VwcGVyQ2FzZSgpICsgcmVzdWx0LnRyaW0oKS5zbGljZSgxKSArICcgJyArIGN1cnJlbmN5V29yZDtcbn07XG5cbmNvbnN0IHNwbGl0VHJhbnNhY3Rpb25zID0gKGFtb3VudDogbnVtYmVyLCBtYXhMaW1pdDI0NzogbnVtYmVyLCBzcGxpdEFtb3VudDogbnVtYmVyKSA9PiB7XG4gIGNvbnN0IHRyYW5zYWN0aW9uczogbnVtYmVyW10gPSBbXTtcblxuICBsZXQgc3BsaXRUcmFucyA9IE1hdGguZmxvb3IoYW1vdW50IC8gc3BsaXRBbW91bnQpO1xuICBsZXQgZmluYWxUcmFucyA9IGFtb3VudCAlIHNwbGl0QW1vdW50O1xuXG4gIC8vIE7hur91IGZpbmFsVHJhbnMgKyBzcGxpdEFtb3VudCA8PSBtYXhMaW1pdDI0NywgZ+G7mXAgZmluYWwgdsOgbyBnaWFvIGThu4tjaCBjdeG7kWlcbiAgaWYgKGZpbmFsVHJhbnMgIT09IDAgJiYgZmluYWxUcmFucyArIHNwbGl0QW1vdW50IDw9IG1heExpbWl0MjQ3KSB7XG4gICAgc3BsaXRUcmFucy0tO1xuICAgIGZpbmFsVHJhbnMgKz0gc3BsaXRBbW91bnQ7XG4gIH1cblxuICBpZiAoc3BsaXRUcmFucyA+IDApIHtcbiAgICB0cmFuc2FjdGlvbnMucHVzaCguLi5BcnJheShzcGxpdFRyYW5zKS5maWxsKHNwbGl0QW1vdW50KSk7XG4gIH1cblxuICAvLyBUaMOqbSBnaWFvIGThu4tjaCBjdeG7kWkgbuG6v3UgY8OzXG4gIGlmIChmaW5hbFRyYW5zID4gMCkge1xuICAgIHRyYW5zYWN0aW9ucy5wdXNoKGZpbmFsVHJhbnMpO1xuICB9XG5cbiAgY29uc3QgdHJhbnNhY3Rpb25JdGVtc01hcCA9IG5ldyBNYXA8bnVtYmVyLCBudW1iZXI+KCk7XG5cbiAgZm9yIChjb25zdCB0cmFucyBvZiB0cmFuc2FjdGlvbnMpIHtcbiAgICB0cmFuc2FjdGlvbkl0ZW1zTWFwLnNldCh0cmFucywgKHRyYW5zYWN0aW9uSXRlbXNNYXAuZ2V0KHRyYW5zKSB8fCAwKSArIDEpO1xuICB9XG5cbiAgY29uc3QgdHJhbnNhY3Rpb25JdGVtcyA9IEFycmF5LmZyb20odHJhbnNhY3Rpb25JdGVtc01hcCwgKFthbW91bnQsIGNvdW50XSkgPT4gKHthbW91bnQsIGNvdW50fSkpO1xuXG4gIHJldHVybiB7XG4gICAgdG90YWxUcmFuc2FjdGlvbjogdHJhbnNhY3Rpb25zLmxlbmd0aCxcbiAgICB0cmFuc2FjdGlvbnMsXG4gICAgdHJhbnNhY3Rpb25JdGVtcyxcbiAgfTtcbn07XG5cbi8vIGNodXnhu4NuIHRp4bq/bmcgdmnhu4d0IGPDsyBk4bqldSBzYW5nIHRp4bq/bmcgdmnhu4d0IGtow7RuZyBk4bqldVxuY29uc3QgcmVtb3ZlVmlldG5hbWVzZVRvbmVzID0gKHN0cjogc3RyaW5nKTogc3RyaW5nID0+IHtcbiAgaWYgKCFzdHIpIHtcbiAgICByZXR1cm4gJyc7XG4gIH1cbiAgcmV0dXJuIHN0clxuICAgIC5ub3JtYWxpemUoJ05GRCcpXG4gICAgLnJlcGxhY2UoL1tcXHUwMzAwLVxcdTAzNmZdL2csICcnKVxuICAgIC5yZXBsYWNlKC/EkS9nLCAnZCcpXG4gICAgLnJlcGxhY2UoL8SQL2csICdEJylcbiAgICAudHJpbSgpO1xufTtcblxuLy8gY2jhu4kgY2hvIHBow6lwIG5o4bqtcCBjw6FjIHThu7EgxJHhurdjIGJp4buHdCAvKCkuK18sXG4vLyBraMO0bmcgY2hvIG5o4bqtcCBjw6FjIGvDrSB04buxIMSR4bq3YyBiaeG7h3QgY8OybiBs4bqhaSB2w6AgaWNvblxuLy8gZMO5bmcgY2hvIG5o4bqtcCBu4buZaSBkdW5nIGNodXnhu4NuIGtob+G6o25cbmNvbnN0IGZvcm1hdFJlbWl0dGFuY2VJbmZvcm1hdGlvbiA9IChjb250ZW50Pzogc3RyaW5nIHwgbnVsbCkgPT4ge1xuICBpZiAoIWNvbnRlbnQpIHtcbiAgICByZXR1cm4gJyc7XG4gIH1cbiAgcmV0dXJuIGNvbnRlbnQucmVwbGFjZSgvW15hLXpBLVowLTnDgC3hu7lcXC1cXFxcLygpLitfLCBdL2csICcnKT8ucmVwbGFjZSgvXFxzKy9nLCAnICcpO1xufTtcblxuY29uc3QgcmVtb3ZlU3BlY2lhbENoYXJzQW5kRW1vamkgPSAoY29udGVudD86IHN0cmluZyB8IG51bGwpID0+IHtcbiAgaWYgKCFjb250ZW50KSB7XG4gICAgcmV0dXJuICcnO1xuICB9XG4gIHJldHVybiBjb250ZW50LnJlcGxhY2UoL1teXFxwe0x9IF0rL2d1LCAnJyk7XG59O1xuXG5jb25zdCBmb3JtYXRNb25leSA9ICh0ZXh0OiBzdHJpbmcpOiBzdHJpbmcgPT4ge1xuICBjb25zb2xlLmxvZygndGV4dCcsIHRleHQpO1xuICBjb25zdCBjbGVhbmVkVGV4dCA9IHRleHQucmVwbGFjZSgvW14wLTkuXS9nLCAnJyk7XG4gIGNvbnNvbGUubG9nKCdjbGVhbmVkVGV4dCcsIGNsZWFuZWRUZXh0KTtcbiAgY29uc3Qgd2l0aG91dExlYWRpbmdaZXJvcyA9IGNsZWFuZWRUZXh0LnJlcGxhY2UoL14wKy8sICcnKSB8fCAnMCc7XG4gIHJldHVybiB3aXRob3V0TGVhZGluZ1plcm9zLnJlcGxhY2UoL1xcQig/PShcXGR7M30pKyg/IVxcZCkpL2csICcsJyk7XG59O1xuXG5jb25zdCBnZW5lcmF0ZUFtb3VudFN1Z2dlc3Rpb25zID0gKGlucHV0OiBzdHJpbmcpOiBzdHJpbmdbXSA9PiB7XG4gIGlmICghaW5wdXQgfHwgaW5wdXQubGVuZ3RoID09PSAwKSB7XG4gICAgcmV0dXJuIFtcbiAgICAgIHRyYW5zbGF0ZSgndXRpbHMuZm9ybWF0VXRpbHMuYW1vdW50U3VnZ2VzdGlvbjEnKSxcbiAgICAgIHRyYW5zbGF0ZSgndXRpbHMuZm9ybWF0VXRpbHMuYW1vdW50U3VnZ2VzdGlvbjInKSxcbiAgICAgIHRyYW5zbGF0ZSgndXRpbHMuZm9ybWF0VXRpbHMuYW1vdW50U3VnZ2VzdGlvbjMnKSxcbiAgICAgIHRyYW5zbGF0ZSgndXRpbHMuZm9ybWF0VXRpbHMuZG9uZScpLFxuICAgIF07XG4gIH1cblxuICBjb25zdCBudW0gPSBwYXJzZUludChpbnB1dCwgMTApO1xuICBjb25zb2xlLmxvZygnbnVtJywgbnVtKTtcbiAgaWYgKGlzTmFOKG51bSkgfHwgbnVtID09PSAwKSB7XG4gICAgcmV0dXJuIFtcbiAgICAgIHRyYW5zbGF0ZSgndXRpbHMuZm9ybWF0VXRpbHMuYW1vdW50U3VnZ2VzdGlvbjEnKSxcbiAgICAgIHRyYW5zbGF0ZSgndXRpbHMuZm9ybWF0VXRpbHMuYW1vdW50U3VnZ2VzdGlvbjInKSxcbiAgICAgIHRyYW5zbGF0ZSgndXRpbHMuZm9ybWF0VXRpbHMuYW1vdW50U3VnZ2VzdGlvbjMnKSxcbiAgICAgIHRyYW5zbGF0ZSgndXRpbHMuZm9ybWF0VXRpbHMuZG9uZScpLFxuICAgIF07XG4gIH1cblxuICBjb25zdCBsZW5ndGggPSBpbnB1dC5sZW5ndGg7XG4gIGNvbnN0IHN1Z2dlc3Rpb25zOiBzdHJpbmdbXSA9IFtdO1xuXG4gIGlmIChsZW5ndGggPT09IDEpIHtcbiAgICBzdWdnZXN0aW9ucy5wdXNoKGZvcm1hdE1vbmV5KChudW0gKiAxMDAwMCkudG9TdHJpbmcoKSkpO1xuICAgIHN1Z2dlc3Rpb25zLnB1c2goZm9ybWF0TW9uZXkoKG51bSAqIDEwMDAwMCkudG9TdHJpbmcoKSkpO1xuICAgIHN1Z2dlc3Rpb25zLnB1c2goZm9ybWF0TW9uZXkoKG51bSAqIDEwMDAwMDApLnRvU3RyaW5nKCkpKTtcbiAgfSBlbHNlIGlmIChsZW5ndGggPT09IDIpIHtcbiAgICBzdWdnZXN0aW9ucy5wdXNoKGZvcm1hdE1vbmV5KChudW0gKiAxMDAwKS50b1N0cmluZygpKSk7XG4gICAgc3VnZ2VzdGlvbnMucHVzaChmb3JtYXRNb25leSgobnVtICogMTAwMDApLnRvU3RyaW5nKCkpKTtcbiAgICBzdWdnZXN0aW9ucy5wdXNoKGZvcm1hdE1vbmV5KChudW0gKiAxMDAwMDApLnRvU3RyaW5nKCkpKTtcbiAgfSBlbHNlIGlmIChsZW5ndGggPT09IDMgfHwgbGVuZ3RoID09PSA0KSB7XG4gICAgc3VnZ2VzdGlvbnMucHVzaChmb3JtYXRNb25leSgobnVtICogMTAwKS50b1N0cmluZygpKSk7XG4gICAgc3VnZ2VzdGlvbnMucHVzaChmb3JtYXRNb25leSgobnVtICogMTAwMCkudG9TdHJpbmcoKSkpO1xuICAgIHN1Z2dlc3Rpb25zLnB1c2goZm9ybWF0TW9uZXkoKG51bSAqIDEwMDAwMCkudG9TdHJpbmcoKSkpO1xuICB9IGVsc2UgaWYgKGxlbmd0aCA9PT0gNSB8fCBsZW5ndGggPT09IDYpIHtcbiAgICBzdWdnZXN0aW9ucy5wdXNoKGZvcm1hdE1vbmV5KChudW0gKiAxMDApLnRvU3RyaW5nKCkpKTtcbiAgICBzdWdnZXN0aW9ucy5wdXNoKGZvcm1hdE1vbmV5KChudW0gKiAxMDAwKS50b1N0cmluZygpKSk7XG4gICAgc3VnZ2VzdGlvbnMucHVzaChmb3JtYXRNb25leSgobnVtICogMTAwMDApLnRvU3RyaW5nKCkpKTtcbiAgfSBlbHNlIHtcbiAgICBzdWdnZXN0aW9ucy5wdXNoKGZvcm1hdE1vbmV5KChudW0gKiAxMCkudG9TdHJpbmcoKSkpO1xuICAgIHN1Z2dlc3Rpb25zLnB1c2goZm9ybWF0TW9uZXkoKG51bSAqIDEwMCkudG9TdHJpbmcoKSkpO1xuICB9XG5cbiAgc3VnZ2VzdGlvbnMucHVzaCh0cmFuc2xhdGUoJ3V0aWxzLmZvcm1hdFV0aWxzLmRvbmUnKSk7XG4gIHJldHVybiBzdWdnZXN0aW9ucztcbn07XG5cbmV4cG9ydCBkZWZhdWx0IHtcbiAgZm9ybWF0UHJpY2UsXG4gIGZvcm1hdE1vbmV5LFxuICBmb3JtYXR0ZWROdW1iZXIsXG4gIG51bWJlclRvV29yZHNWaSxcbiAgc3BsaXRUcmFuc2FjdGlvbnMsXG4gIGZvcm1hdERhdGVERE1NWVlZWSxcbiAgZm9ybWF0RGF0ZUhITU0sXG4gIGZvcm1hdFJlbWl0dGFuY2VJbmZvcm1hdGlvbixcbiAgZ2VuZXJhdGVBbW91bnRTdWdnZXN0aW9ucyxcbiAgcmVtb3ZlVmlldG5hbWVzZVRvbmVzLFxuICByZW1vdmVTcGVjaWFsQ2hhcnNBbmRFbW9qaSxcbn07XG4iXSwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0lBR007SUFBQUEsY0FBQSxZQUFBQSxDQUFBO01BQUEsT0FBQUMsY0FBQTtJQUFBO0VBQUE7RUFBQSxPQUFBQSxjQUFBO0FBQUE7QUFBQUQsY0FBQTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUhOLElBQUFFLFFBQUE7QUFBQTtBQUFBLENBQUFGLGNBQUEsR0FBQUcsQ0FBQSxPQUFBQyxlQUFBLENBQUFDLE9BQUE7QUFDQSxJQUFBQyxNQUFBO0FBQUE7QUFBQSxDQUFBTixjQUFBLEdBQUFHLENBQUEsT0FBQUUsT0FBQTtBQUFBO0FBQUFMLGNBQUEsR0FBQUcsQ0FBQTtBQUVBLElBQU1JLFdBQVcsR0FBRyxTQUFkQSxXQUFXQSxDQUFJQyxNQUEwQyxFQUFJO0VBQUE7RUFBQVIsY0FBQSxHQUFBUyxDQUFBO0VBQUFULGNBQUEsR0FBQUcsQ0FBQTtFQUNqRTtFQUFJO0VBQUEsQ0FBQUgsY0FBQSxHQUFBVSxDQUFBLFVBQUFGLE1BQU0sSUFBSSxJQUFJO0VBQUE7RUFBQSxDQUFBUixjQUFBLEdBQUFVLENBQUEsVUFBSUYsTUFBTSxJQUFJRyxTQUFTLEdBQUU7SUFBQTtJQUFBWCxjQUFBLEdBQUFVLENBQUE7SUFBQVYsY0FBQSxHQUFBRyxDQUFBO0lBQ3pDLE9BQU8sR0FBRztFQUNaO0VBQUE7RUFBQTtJQUFBSCxjQUFBLEdBQUFVLENBQUE7RUFBQTtFQUNBLElBQU1FLEdBQUc7RUFBQTtFQUFBLENBQUFaLGNBQUEsR0FBQUcsQ0FBQSxRQUFHLE9BQU9LLE1BQU0sS0FBSyxRQUFRO0VBQUE7RUFBQSxDQUFBUixjQUFBLEdBQUFVLENBQUEsVUFBR0csTUFBTSxDQUFDTCxNQUFNLENBQUM7RUFBQTtFQUFBLENBQUFSLGNBQUEsR0FBQVUsQ0FBQSxVQUFHRixNQUFNO0VBQUE7RUFBQVIsY0FBQSxHQUFBRyxDQUFBO0VBQ2hFLElBQUlXLEtBQUssQ0FBQ0YsR0FBRyxDQUFDLEVBQUU7SUFBQTtJQUFBWixjQUFBLEdBQUFVLENBQUE7SUFBQVYsY0FBQSxHQUFBRyxDQUFBO0lBQ2QsT0FBTyxHQUFHO0VBQ1o7RUFBQTtFQUFBO0lBQUFILGNBQUEsR0FBQVUsQ0FBQTtFQUFBO0VBQUFWLGNBQUEsR0FBQUcsQ0FBQTtFQUNBLE9BQU9TLEdBQUcsQ0FBQ0csY0FBYyxDQUFDLE9BQU8sQ0FBQyxDQUFDQyxPQUFPLENBQUMsS0FBSyxFQUFFLEdBQUcsQ0FBQztBQUN4RCxDQUFDO0FBQUE7QUFBQWhCLGNBQUEsR0FBQUcsQ0FBQTtBQUNELElBQU1jLGVBQWUsR0FBRyxTQUFsQkEsZUFBZUEsQ0FBSUMsR0FBVyxFQUFZO0VBQUE7RUFBQWxCLGNBQUEsR0FBQVMsQ0FBQTtFQUFBVCxjQUFBLEdBQUFHLENBQUE7RUFDOUMsT0FBT1UsTUFBTSxDQUFDSyxHQUFHLENBQUNGLE9BQU8sQ0FBQyxJQUFJLEVBQUUsRUFBRSxDQUFDLENBQUM7QUFDdEMsQ0FBQztBQUFBO0FBQUFoQixjQUFBLEdBQUFHLENBQUE7QUFFRCxJQUFNZ0IsY0FBYyxHQUFHLFNBQWpCQSxjQUFjQSxDQUFJQyxTQUFpQixFQUFZO0VBQUE7RUFBQXBCLGNBQUEsR0FBQVMsQ0FBQTtFQUFBVCxjQUFBLEdBQUFHLENBQUE7RUFDbkQsT0FBTyxJQUFBRCxRQUFBLENBQUFtQixPQUFNLEVBQUNELFNBQVMsQ0FBQyxDQUFDRSxNQUFNLENBQUMsT0FBTyxDQUFDO0FBQzFDLENBQUM7QUFBQTtBQUFBdEIsY0FBQSxHQUFBRyxDQUFBO0FBRUQsSUFBTW9CLGtCQUFrQixHQUFHLFNBQXJCQSxrQkFBa0JBLENBQUlILFNBQWlCLEVBQVk7RUFBQTtFQUFBcEIsY0FBQSxHQUFBUyxDQUFBO0VBQUFULGNBQUEsR0FBQUcsQ0FBQTtFQUN2RCxPQUFPLElBQUFELFFBQUEsQ0FBQW1CLE9BQU0sRUFBQ0QsU0FBUyxDQUFDLENBQUNFLE1BQU0sQ0FBQyxZQUFZLENBQUM7QUFDL0MsQ0FBQztBQUFBO0FBQUF0QixjQUFBLEdBQUFHLENBQUE7QUFFRCxJQUFNcUIsZUFBZSxHQUFHLFNBQWxCQSxlQUFlQSxDQUFJQyxNQUFjLEVBQXNCO0VBQUE7RUFBQXpCLGNBQUEsR0FBQVMsQ0FBQTtFQUFBLElBQUFpQixhQUFBO0VBQUEsSUFBcEJDLFFBQVE7RUFBQTtFQUFBLENBQUEzQixjQUFBLEdBQUFHLENBQUE7RUFBQTtFQUFBLENBQUFILGNBQUEsR0FBQVUsQ0FBQSxVQUFBa0IsU0FBQSxDQUFBQyxNQUFBO0VBQUE7RUFBQSxDQUFBN0IsY0FBQSxHQUFBVSxDQUFBLFVBQUFrQixTQUFBLFFBQUFqQixTQUFBO0VBQUE7RUFBQSxDQUFBWCxjQUFBLEdBQUFVLENBQUEsVUFBQWtCLFNBQUE7RUFBQTtFQUFBLENBQUE1QixjQUFBLEdBQUFVLENBQUEsVUFBRyxLQUFLO0VBQ3ZELElBQU1vQixLQUFLO0VBQUE7RUFBQSxDQUFBOUIsY0FBQSxHQUFBRyxDQUFBLFFBQUcsQ0FBQyxFQUFFLEVBQUUsS0FBSyxFQUFFLEtBQUssRUFBRSxJQUFJLEVBQUUsS0FBSyxFQUFFLEtBQUssRUFBRSxLQUFLLEVBQUUsS0FBSyxFQUFFLEtBQUssRUFBRSxNQUFNLENBQUM7RUFDakYsSUFBTTRCLEtBQUs7RUFBQTtFQUFBLENBQUEvQixjQUFBLEdBQUFHLENBQUEsUUFBRyxDQUNaLE1BQU0sRUFDTixVQUFVLEVBQ1YsVUFBVSxFQUNWLFNBQVMsRUFDVCxVQUFVLEVBQ1YsVUFBVSxFQUNWLFVBQVUsRUFDVixVQUFVLEVBQ1YsVUFBVSxFQUNWLFdBQVcsQ0FDWjtFQUNELElBQU02QixJQUFJO0VBQUE7RUFBQSxDQUFBaEMsY0FBQSxHQUFBRyxDQUFBLFFBQUcsQ0FBQyxFQUFFLEVBQUUsRUFBRSxFQUFFLFVBQVUsRUFBRSxTQUFTLEVBQUUsVUFBVSxFQUFFLFVBQVUsRUFBRSxVQUFVLEVBQUUsVUFBVSxFQUFFLFVBQVUsRUFBRSxXQUFXLENBQUM7RUFDckgsSUFBTThCLE1BQU07RUFBQTtFQUFBLENBQUFqQyxjQUFBLEdBQUFHLENBQUEsUUFBRyxDQUFDLEVBQUUsRUFBRSxPQUFPLEVBQUUsT0FBTyxFQUFFLElBQUksRUFBRSxVQUFVLEVBQUUsZ0JBQWdCLENBQUM7RUFFekUsU0FBUytCLGNBQWNBLENBQUNDLENBQU0sRUFBRUMsVUFBa0I7SUFBQTtJQUFBcEMsY0FBQSxHQUFBUyxDQUFBO0lBQUFULGNBQUEsR0FBQUcsQ0FBQTtJQUNoRCxJQUFJZ0MsQ0FBQyxLQUFLLENBQUMsRUFBRTtNQUFBO01BQUFuQyxjQUFBLEdBQUFVLENBQUE7TUFBQVYsY0FBQSxHQUFBRyxDQUFBO01BQ1gsT0FBTyxFQUFFO0lBQ1g7SUFBQTtJQUFBO01BQUFILGNBQUEsR0FBQVUsQ0FBQTtJQUFBO0lBRUEsSUFBTTJCLEtBQUs7SUFBQTtJQUFBLENBQUFyQyxjQUFBLEdBQUFHLENBQUEsUUFBRzhCLE1BQU0sQ0FBQ0csVUFBVSxDQUFDO0lBQ2hDLElBQU1FLE9BQU87SUFBQTtJQUFBLENBQUF0QyxjQUFBLEdBQUFHLENBQUEsUUFBR29DLElBQUksQ0FBQ0MsS0FBSyxDQUFDTCxDQUFDLEdBQUcsR0FBRyxDQUFDO0lBQ25DLElBQU1NLFNBQVM7SUFBQTtJQUFBLENBQUF6QyxjQUFBLEdBQUFHLENBQUEsUUFBR2dDLENBQUMsR0FBRyxHQUFHO0lBRXpCLElBQUlPLE1BQU07SUFBQTtJQUFBLENBQUExQyxjQUFBLEdBQUFHLENBQUEsUUFBRyxFQUFFO0lBQUE7SUFBQUgsY0FBQSxHQUFBRyxDQUFBO0lBRWYsSUFBSW1DLE9BQU8sR0FBRyxDQUFDLEVBQUU7TUFBQTtNQUFBdEMsY0FBQSxHQUFBVSxDQUFBO01BQUFWLGNBQUEsR0FBQUcsQ0FBQTtNQUNmdUMsTUFBTSxJQUFJWixLQUFLLENBQUNRLE9BQU8sQ0FBQyxHQUFHLFFBQVE7SUFDckM7SUFBQTtJQUFBO01BQUF0QyxjQUFBLEdBQUFVLENBQUE7SUFBQTtJQUFBVixjQUFBLEdBQUFHLENBQUE7SUFFQSxJQUFJc0MsU0FBUyxHQUFHLENBQUMsRUFBRTtNQUFBO01BQUF6QyxjQUFBLEdBQUFVLENBQUE7TUFBQVYsY0FBQSxHQUFBRyxDQUFBO01BQ2pCLElBQUlzQyxTQUFTLEdBQUcsRUFBRSxFQUFFO1FBQUE7UUFBQXpDLGNBQUEsR0FBQVUsQ0FBQTtRQUFBVixjQUFBLEdBQUFHLENBQUE7UUFDbEJ1QyxNQUFNLElBQUlaLEtBQUssQ0FBQ1csU0FBUyxDQUFDLEdBQUcsR0FBRztNQUNsQyxDQUFDLE1BQU07UUFBQTtRQUFBekMsY0FBQSxHQUFBVSxDQUFBO1FBQUFWLGNBQUEsR0FBQUcsQ0FBQTtRQUFBLElBQUlzQyxTQUFTLEdBQUcsRUFBRSxFQUFFO1VBQUE7VUFBQXpDLGNBQUEsR0FBQVUsQ0FBQTtVQUFBVixjQUFBLEdBQUFHLENBQUE7VUFDekJ1QyxNQUFNLElBQUlYLEtBQUssQ0FBQ1UsU0FBUyxHQUFHLEVBQUUsQ0FBQyxHQUFHLEdBQUc7UUFDdkMsQ0FBQyxNQUFNO1VBQUE7VUFBQXpDLGNBQUEsR0FBQVUsQ0FBQTtVQUNMLElBQU1pQyxHQUFHO1VBQUE7VUFBQSxDQUFBM0MsY0FBQSxHQUFBRyxDQUFBLFFBQUdvQyxJQUFJLENBQUNDLEtBQUssQ0FBQ0MsU0FBUyxHQUFHLEVBQUUsQ0FBQztVQUN0QyxJQUFNRyxHQUFHO1VBQUE7VUFBQSxDQUFBNUMsY0FBQSxHQUFBRyxDQUFBLFFBQUdzQyxTQUFTLEdBQUcsRUFBRTtVQUFBO1VBQUF6QyxjQUFBLEdBQUFHLENBQUE7VUFDMUJ1QyxNQUFNLElBQUlWLElBQUksQ0FBQ1csR0FBRyxDQUFDLEdBQUcsR0FBRyxHQUFHYixLQUFLLENBQUNjLEdBQUcsQ0FBQyxHQUFHLEdBQUc7UUFDOUM7TUFBQTtJQUNGO0lBQUE7SUFBQTtNQUFBNUMsY0FBQSxHQUFBVSxDQUFBO0lBQUE7SUFBQVYsY0FBQSxHQUFBRyxDQUFBO0lBRUEsT0FBT3VDLE1BQU0sR0FBR0wsS0FBSyxHQUFHLEdBQUc7RUFDN0I7RUFBQTtFQUFBckMsY0FBQSxHQUFBRyxDQUFBO0VBRUEsSUFBSXNCLE1BQU0sS0FBSyxDQUFDLEVBQUU7SUFBQTtJQUFBekIsY0FBQSxHQUFBVSxDQUFBO0lBQUFWLGNBQUEsR0FBQUcsQ0FBQTtJQUNoQixPQUFPLElBQUFHLE1BQUEsQ0FBQXVDLFNBQVMsRUFBQyw0QkFBNEIsQ0FBQztFQUNoRDtFQUFBO0VBQUE7SUFBQTdDLGNBQUEsR0FBQVUsQ0FBQTtFQUFBO0VBRUEsSUFBSWdDLE1BQU07RUFBQTtFQUFBLENBQUExQyxjQUFBLEdBQUFHLENBQUEsUUFBRyxFQUFFO0VBQ2YsSUFBSWlDLFVBQVU7RUFBQTtFQUFBLENBQUFwQyxjQUFBLEdBQUFHLENBQUEsUUFBRyxDQUFDO0VBQ2xCLElBQUkyQyxZQUFZO0VBQUE7RUFBQSxDQUFBOUMsY0FBQSxHQUFBRyxDQUFBLFFBQUcsRUFBRTtFQUFBO0VBQUFILGNBQUEsR0FBQUcsQ0FBQTtFQUVyQixPQUFPc0IsTUFBTSxHQUFHLENBQUMsRUFBRTtJQUNqQixJQUFNc0IsS0FBSztJQUFBO0lBQUEsQ0FBQS9DLGNBQUEsR0FBQUcsQ0FBQSxRQUFHc0IsTUFBTSxHQUFHLElBQUk7SUFBQTtJQUFBekIsY0FBQSxHQUFBRyxDQUFBO0lBQzNCLElBQUk0QyxLQUFLLEdBQUcsQ0FBQyxFQUFFO01BQUE7TUFBQS9DLGNBQUEsR0FBQVUsQ0FBQTtNQUFBVixjQUFBLEdBQUFHLENBQUE7TUFDYnVDLE1BQU0sR0FBR1IsY0FBYyxDQUFDYSxLQUFLLEVBQUVYLFVBQVUsQ0FBQyxHQUFHTSxNQUFNO0lBQ3JEO0lBQUE7SUFBQTtNQUFBMUMsY0FBQSxHQUFBVSxDQUFBO0lBQUE7SUFBQVYsY0FBQSxHQUFBRyxDQUFBO0lBQ0FzQixNQUFNLEdBQUdjLElBQUksQ0FBQ0MsS0FBSyxDQUFDZixNQUFNLEdBQUcsSUFBSSxDQUFDO0lBQUE7SUFBQXpCLGNBQUEsR0FBQUcsQ0FBQTtJQUNsQ2lDLFVBQVUsRUFBRTtFQUNkO0VBQUE7RUFBQXBDLGNBQUEsR0FBQUcsQ0FBQTtFQUVBLElBQUl3QixRQUFRLEtBQUssS0FBSyxFQUFFO0lBQUE7SUFBQTNCLGNBQUEsR0FBQVUsQ0FBQTtJQUFBVixjQUFBLEdBQUFHLENBQUE7SUFDdEIyQyxZQUFZLEdBQUcsSUFBQXhDLE1BQUEsQ0FBQXVDLFNBQVMsRUFBQywrQkFBK0IsQ0FBQztFQUMzRCxDQUFDLE1BQU07SUFBQTtJQUFBN0MsY0FBQSxHQUFBVSxDQUFBO0lBQUFWLGNBQUEsR0FBQUcsQ0FBQTtJQUFBLElBQUl3QixRQUFRLEtBQUssS0FBSyxFQUFFO01BQUE7TUFBQTNCLGNBQUEsR0FBQVUsQ0FBQTtNQUFBVixjQUFBLEdBQUFHLENBQUE7TUFDN0IyQyxZQUFZLEdBQUcsSUFBQXhDLE1BQUEsQ0FBQXVDLFNBQVMsRUFBQywrQkFBK0IsQ0FBQztJQUMzRDtJQUFBO0lBQUE7TUFBQTdDLGNBQUEsR0FBQVUsQ0FBQTtJQUFBO0VBQUE7RUFBQTtFQUFBVixjQUFBLEdBQUFHLENBQUE7RUFFQSxPQUFPLEVBQUF1QixhQUFBLEdBQUFnQixNQUFNLENBQUNNLElBQUksRUFBRSxDQUFDLENBQUMsQ0FBQztFQUFBO0VBQUEsQ0FBQWhELGNBQUEsR0FBQVUsQ0FBQTtFQUFBO0VBQUEsQ0FBQVYsY0FBQSxHQUFBVSxDQUFBLFdBQWhCZ0IsYUFBQSxDQUFrQnVCLFdBQVcsRUFBRSxLQUFHUCxNQUFNLENBQUNNLElBQUksRUFBRSxDQUFDRSxLQUFLLENBQUMsQ0FBQyxDQUFDLEdBQUcsR0FBRyxHQUFHSixZQUFZO0FBQ3RGLENBQUM7QUFBQTtBQUFBOUMsY0FBQSxHQUFBRyxDQUFBO0FBRUQsSUFBTWdELGlCQUFpQixHQUFHLFNBQXBCQSxpQkFBaUJBLENBQUkzQyxNQUFjLEVBQUU0QyxXQUFtQixFQUFFQyxXQUFtQixFQUFJO0VBQUE7RUFBQXJELGNBQUEsR0FBQVMsQ0FBQTtFQUNyRixJQUFNNkMsWUFBWTtFQUFBO0VBQUEsQ0FBQXRELGNBQUEsR0FBQUcsQ0FBQSxRQUFhLEVBQUU7RUFFakMsSUFBSW9ELFVBQVU7RUFBQTtFQUFBLENBQUF2RCxjQUFBLEdBQUFHLENBQUEsUUFBR29DLElBQUksQ0FBQ0MsS0FBSyxDQUFDaEMsTUFBTSxHQUFHNkMsV0FBVyxDQUFDO0VBQ2pELElBQUlHLFVBQVU7RUFBQTtFQUFBLENBQUF4RCxjQUFBLEdBQUFHLENBQUEsUUFBR0ssTUFBTSxHQUFHNkMsV0FBVztFQUFBO0VBQUFyRCxjQUFBLEdBQUFHLENBQUE7RUFHckM7RUFBSTtFQUFBLENBQUFILGNBQUEsR0FBQVUsQ0FBQSxXQUFBOEMsVUFBVSxLQUFLLENBQUM7RUFBQTtFQUFBLENBQUF4RCxjQUFBLEdBQUFVLENBQUEsV0FBSThDLFVBQVUsR0FBR0gsV0FBVyxJQUFJRCxXQUFXLEdBQUU7SUFBQTtJQUFBcEQsY0FBQSxHQUFBVSxDQUFBO0lBQUFWLGNBQUEsR0FBQUcsQ0FBQTtJQUMvRG9ELFVBQVUsRUFBRTtJQUFBO0lBQUF2RCxjQUFBLEdBQUFHLENBQUE7SUFDWnFELFVBQVUsSUFBSUgsV0FBVztFQUMzQjtFQUFBO0VBQUE7SUFBQXJELGNBQUEsR0FBQVUsQ0FBQTtFQUFBO0VBQUFWLGNBQUEsR0FBQUcsQ0FBQTtFQUVBLElBQUlvRCxVQUFVLEdBQUcsQ0FBQyxFQUFFO0lBQUE7SUFBQXZELGNBQUEsR0FBQVUsQ0FBQTtJQUFBVixjQUFBLEdBQUFHLENBQUE7SUFDbEJtRCxZQUFZLENBQUNHLElBQUksQ0FBQUMsS0FBQSxDQUFqQkosWUFBWSxNQUFBSyxtQkFBQSxDQUFBdEMsT0FBQSxFQUFTdUMsS0FBSyxDQUFDTCxVQUFVLENBQUMsQ0FBQ00sSUFBSSxDQUFDUixXQUFXLENBQUMsRUFBQztFQUMzRDtFQUFBO0VBQUE7SUFBQXJELGNBQUEsR0FBQVUsQ0FBQTtFQUFBO0VBQUFWLGNBQUEsR0FBQUcsQ0FBQTtFQUdBLElBQUlxRCxVQUFVLEdBQUcsQ0FBQyxFQUFFO0lBQUE7SUFBQXhELGNBQUEsR0FBQVUsQ0FBQTtJQUFBVixjQUFBLEdBQUFHLENBQUE7SUFDbEJtRCxZQUFZLENBQUNHLElBQUksQ0FBQ0QsVUFBVSxDQUFDO0VBQy9CO0VBQUE7RUFBQTtJQUFBeEQsY0FBQSxHQUFBVSxDQUFBO0VBQUE7RUFFQSxJQUFNb0QsbUJBQW1CO0VBQUE7RUFBQSxDQUFBOUQsY0FBQSxHQUFBRyxDQUFBLFFBQUcsSUFBSTRELEdBQUcsRUFBa0I7RUFBQTtFQUFBL0QsY0FBQSxHQUFBRyxDQUFBO0VBRXJELEtBQUssSUFBTTZELEtBQUssSUFBSVYsWUFBWSxFQUFFO0lBQUE7SUFBQXRELGNBQUEsR0FBQUcsQ0FBQTtJQUNoQzJELG1CQUFtQixDQUFDRyxHQUFHLENBQUNELEtBQUssRUFBRTtJQUFDO0lBQUEsQ0FBQWhFLGNBQUEsR0FBQVUsQ0FBQSxXQUFBb0QsbUJBQW1CLENBQUNJLEdBQUcsQ0FBQ0YsS0FBSyxDQUFDO0lBQUE7SUFBQSxDQUFBaEUsY0FBQSxHQUFBVSxDQUFBLFdBQUksQ0FBQyxLQUFJLENBQUMsQ0FBQztFQUMzRTtFQUVBLElBQU15RCxnQkFBZ0I7RUFBQTtFQUFBLENBQUFuRSxjQUFBLEdBQUFHLENBQUEsUUFBR3lELEtBQUssQ0FBQ1EsSUFBSSxDQUFDTixtQkFBbUIsRUFBRSxVQUFBTyxJQUFBO0lBQUE7SUFBQXJFLGNBQUEsR0FBQVMsQ0FBQTtJQUFBLElBQUE2RCxLQUFBO01BQUE7TUFBQSxDQUFBdEUsY0FBQSxHQUFBRyxDQUFBLFlBQUFvRSxlQUFBLENBQUFsRCxPQUFBLEVBQUFnRCxJQUFBO01BQUU3RCxNQUFNO01BQUE7TUFBQSxDQUFBUixjQUFBLEdBQUFHLENBQUEsUUFBQW1FLEtBQUE7TUFBRUUsS0FBSztNQUFBO01BQUEsQ0FBQXhFLGNBQUEsR0FBQUcsQ0FBQSxRQUFBbUUsS0FBQTtJQUFBO0lBQUF0RSxjQUFBLEdBQUFHLENBQUE7SUFBQSxPQUFPO01BQUNLLE1BQU0sRUFBTkEsTUFBTTtNQUFFZ0UsS0FBSyxFQUFMQTtJQUFLLENBQUM7RUFBQSxDQUFDLENBQUM7RUFBQTtFQUFBeEUsY0FBQSxHQUFBRyxDQUFBO0VBRWhHLE9BQU87SUFDTHNFLGdCQUFnQixFQUFFbkIsWUFBWSxDQUFDekIsTUFBTTtJQUNyQ3lCLFlBQVksRUFBWkEsWUFBWTtJQUNaYSxnQkFBZ0IsRUFBaEJBO0dBQ0Q7QUFDSCxDQUFDO0FBQUE7QUFBQW5FLGNBQUEsR0FBQUcsQ0FBQTtBQUdELElBQU11RSxxQkFBcUIsR0FBRyxTQUF4QkEscUJBQXFCQSxDQUFJeEQsR0FBVyxFQUFZO0VBQUE7RUFBQWxCLGNBQUEsR0FBQVMsQ0FBQTtFQUFBVCxjQUFBLEdBQUFHLENBQUE7RUFDcEQsSUFBSSxDQUFDZSxHQUFHLEVBQUU7SUFBQTtJQUFBbEIsY0FBQSxHQUFBVSxDQUFBO0lBQUFWLGNBQUEsR0FBQUcsQ0FBQTtJQUNSLE9BQU8sRUFBRTtFQUNYO0VBQUE7RUFBQTtJQUFBSCxjQUFBLEdBQUFVLENBQUE7RUFBQTtFQUFBVixjQUFBLEdBQUFHLENBQUE7RUFDQSxPQUFPZSxHQUFHLENBQ1B5RCxTQUFTLENBQUMsS0FBSyxDQUFDLENBQ2hCM0QsT0FBTyxDQUFDLGtCQUFrQixFQUFFLEVBQUUsQ0FBQyxDQUMvQkEsT0FBTyxDQUFDLElBQUksRUFBRSxHQUFHLENBQUMsQ0FDbEJBLE9BQU8sQ0FBQyxJQUFJLEVBQUUsR0FBRyxDQUFDLENBQ2xCZ0MsSUFBSSxFQUFFO0FBQ1gsQ0FBQztBQUFBO0FBQUFoRCxjQUFBLEdBQUFHLENBQUE7QUFLRCxJQUFNeUUsMkJBQTJCLEdBQUcsU0FBOUJBLDJCQUEyQkEsQ0FBSUMsT0FBdUIsRUFBSTtFQUFBO0VBQUE3RSxjQUFBLEdBQUFTLENBQUE7RUFBQSxJQUFBcUUsZ0JBQUE7RUFBQTtFQUFBOUUsY0FBQSxHQUFBRyxDQUFBO0VBQzlELElBQUksQ0FBQzBFLE9BQU8sRUFBRTtJQUFBO0lBQUE3RSxjQUFBLEdBQUFVLENBQUE7SUFBQVYsY0FBQSxHQUFBRyxDQUFBO0lBQ1osT0FBTyxFQUFFO0VBQ1g7RUFBQTtFQUFBO0lBQUFILGNBQUEsR0FBQVUsQ0FBQTtFQUFBO0VBQUFWLGNBQUEsR0FBQUcsQ0FBQTtFQUNBLFFBQUEyRSxnQkFBQSxHQUFPRCxPQUFPLENBQUM3RCxPQUFPLENBQUMsOEJBQThCLEVBQUUsRUFBRSxDQUFDO0VBQUE7RUFBQSxDQUFBaEIsY0FBQSxHQUFBVSxDQUFBO0VBQUE7RUFBQSxDQUFBVixjQUFBLEdBQUFVLENBQUEsV0FBbkRvRSxnQkFBQSxDQUFxRDlELE9BQU8sQ0FBQyxNQUFNLEVBQUUsR0FBRyxDQUFDO0FBQ2xGLENBQUM7QUFBQTtBQUFBaEIsY0FBQSxHQUFBRyxDQUFBO0FBRUQsSUFBTTRFLDBCQUEwQixHQUFHLFNBQTdCQSwwQkFBMEJBLENBQUlGLE9BQXVCLEVBQUk7RUFBQTtFQUFBN0UsY0FBQSxHQUFBUyxDQUFBO0VBQUFULGNBQUEsR0FBQUcsQ0FBQTtFQUM3RCxJQUFJLENBQUMwRSxPQUFPLEVBQUU7SUFBQTtJQUFBN0UsY0FBQSxHQUFBVSxDQUFBO0lBQUFWLGNBQUEsR0FBQUcsQ0FBQTtJQUNaLE9BQU8sRUFBRTtFQUNYO0VBQUE7RUFBQTtJQUFBSCxjQUFBLEdBQUFVLENBQUE7RUFBQTtFQUFBVixjQUFBLEdBQUFHLENBQUE7RUFDQSxPQUFPMEUsT0FBTyxDQUFDN0QsT0FBTyxDQUFDLDBxUEFBYyxFQUFFLEVBQUUsQ0FBQztBQUM1QyxDQUFDO0FBQUE7QUFBQWhCLGNBQUEsR0FBQUcsQ0FBQTtBQUVELElBQU02RSxXQUFXLEdBQUcsU0FBZEEsV0FBV0EsQ0FBSUMsSUFBWSxFQUFZO0VBQUE7RUFBQWpGLGNBQUEsR0FBQVMsQ0FBQTtFQUFBVCxjQUFBLEdBQUFHLENBQUE7RUFDM0MrRSxPQUFPLENBQUNDLEdBQUcsQ0FBQyxNQUFNLEVBQUVGLElBQUksQ0FBQztFQUN6QixJQUFNRyxXQUFXO0VBQUE7RUFBQSxDQUFBcEYsY0FBQSxHQUFBRyxDQUFBLFFBQUc4RSxJQUFJLENBQUNqRSxPQUFPLENBQUMsVUFBVSxFQUFFLEVBQUUsQ0FBQztFQUFBO0VBQUFoQixjQUFBLEdBQUFHLENBQUE7RUFDaEQrRSxPQUFPLENBQUNDLEdBQUcsQ0FBQyxhQUFhLEVBQUVDLFdBQVcsQ0FBQztFQUN2QyxJQUFNQyxtQkFBbUI7RUFBQTtFQUFBLENBQUFyRixjQUFBLEdBQUFHLENBQUE7RUFBRztFQUFBLENBQUFILGNBQUEsR0FBQVUsQ0FBQSxXQUFBMEUsV0FBVyxDQUFDcEUsT0FBTyxDQUFDLEtBQUssRUFBRSxFQUFFLENBQUM7RUFBQTtFQUFBLENBQUFoQixjQUFBLEdBQUFVLENBQUEsV0FBSSxHQUFHO0VBQUE7RUFBQVYsY0FBQSxHQUFBRyxDQUFBO0VBQ2pFLE9BQU9rRixtQkFBbUIsQ0FBQ3JFLE9BQU8sQ0FBQyx1QkFBdUIsRUFBRSxHQUFHLENBQUM7QUFDbEUsQ0FBQztBQUFBO0FBQUFoQixjQUFBLEdBQUFHLENBQUE7QUFFRCxJQUFNbUYseUJBQXlCLEdBQUcsU0FBNUJBLHlCQUF5QkEsQ0FBSUMsS0FBYSxFQUFjO0VBQUE7RUFBQXZGLGNBQUEsR0FBQVMsQ0FBQTtFQUFBVCxjQUFBLEdBQUFHLENBQUE7RUFDNUQ7RUFBSTtFQUFBLENBQUFILGNBQUEsR0FBQVUsQ0FBQSxZQUFDNkUsS0FBSztFQUFBO0VBQUEsQ0FBQXZGLGNBQUEsR0FBQVUsQ0FBQSxXQUFJNkUsS0FBSyxDQUFDMUQsTUFBTSxLQUFLLENBQUMsR0FBRTtJQUFBO0lBQUE3QixjQUFBLEdBQUFVLENBQUE7SUFBQVYsY0FBQSxHQUFBRyxDQUFBO0lBQ2hDLE9BQU8sQ0FDTCxJQUFBRyxNQUFBLENBQUF1QyxTQUFTLEVBQUMscUNBQXFDLENBQUMsRUFDaEQsSUFBQXZDLE1BQUEsQ0FBQXVDLFNBQVMsRUFBQyxxQ0FBcUMsQ0FBQyxFQUNoRCxJQUFBdkMsTUFBQSxDQUFBdUMsU0FBUyxFQUFDLHFDQUFxQyxDQUFDLEVBQ2hELElBQUF2QyxNQUFBLENBQUF1QyxTQUFTLEVBQUMsd0JBQXdCLENBQUMsQ0FDcEM7RUFDSDtFQUFBO0VBQUE7SUFBQTdDLGNBQUEsR0FBQVUsQ0FBQTtFQUFBO0VBRUEsSUFBTUUsR0FBRztFQUFBO0VBQUEsQ0FBQVosY0FBQSxHQUFBRyxDQUFBLFNBQUdxRixRQUFRLENBQUNELEtBQUssRUFBRSxFQUFFLENBQUM7RUFBQTtFQUFBdkYsY0FBQSxHQUFBRyxDQUFBO0VBQy9CK0UsT0FBTyxDQUFDQyxHQUFHLENBQUMsS0FBSyxFQUFFdkUsR0FBRyxDQUFDO0VBQUE7RUFBQVosY0FBQSxHQUFBRyxDQUFBO0VBQ3ZCO0VBQUk7RUFBQSxDQUFBSCxjQUFBLEdBQUFVLENBQUEsV0FBQUksS0FBSyxDQUFDRixHQUFHLENBQUM7RUFBQTtFQUFBLENBQUFaLGNBQUEsR0FBQVUsQ0FBQSxXQUFJRSxHQUFHLEtBQUssQ0FBQyxHQUFFO0lBQUE7SUFBQVosY0FBQSxHQUFBVSxDQUFBO0lBQUFWLGNBQUEsR0FBQUcsQ0FBQTtJQUMzQixPQUFPLENBQ0wsSUFBQUcsTUFBQSxDQUFBdUMsU0FBUyxFQUFDLHFDQUFxQyxDQUFDLEVBQ2hELElBQUF2QyxNQUFBLENBQUF1QyxTQUFTLEVBQUMscUNBQXFDLENBQUMsRUFDaEQsSUFBQXZDLE1BQUEsQ0FBQXVDLFNBQVMsRUFBQyxxQ0FBcUMsQ0FBQyxFQUNoRCxJQUFBdkMsTUFBQSxDQUFBdUMsU0FBUyxFQUFDLHdCQUF3QixDQUFDLENBQ3BDO0VBQ0g7RUFBQTtFQUFBO0lBQUE3QyxjQUFBLEdBQUFVLENBQUE7RUFBQTtFQUVBLElBQU1tQixNQUFNO0VBQUE7RUFBQSxDQUFBN0IsY0FBQSxHQUFBRyxDQUFBLFNBQUdvRixLQUFLLENBQUMxRCxNQUFNO0VBQzNCLElBQU00RCxXQUFXO0VBQUE7RUFBQSxDQUFBekYsY0FBQSxHQUFBRyxDQUFBLFNBQWEsRUFBRTtFQUFBO0VBQUFILGNBQUEsR0FBQUcsQ0FBQTtFQUVoQyxJQUFJMEIsTUFBTSxLQUFLLENBQUMsRUFBRTtJQUFBO0lBQUE3QixjQUFBLEdBQUFVLENBQUE7SUFBQVYsY0FBQSxHQUFBRyxDQUFBO0lBQ2hCc0YsV0FBVyxDQUFDaEMsSUFBSSxDQUFDdUIsV0FBVyxDQUFDLENBQUNwRSxHQUFHLEdBQUcsS0FBSyxFQUFFOEUsUUFBUSxFQUFFLENBQUMsQ0FBQztJQUFBO0lBQUExRixjQUFBLEdBQUFHLENBQUE7SUFDdkRzRixXQUFXLENBQUNoQyxJQUFJLENBQUN1QixXQUFXLENBQUMsQ0FBQ3BFLEdBQUcsR0FBRyxNQUFNLEVBQUU4RSxRQUFRLEVBQUUsQ0FBQyxDQUFDO0lBQUE7SUFBQTFGLGNBQUEsR0FBQUcsQ0FBQTtJQUN4RHNGLFdBQVcsQ0FBQ2hDLElBQUksQ0FBQ3VCLFdBQVcsQ0FBQyxDQUFDcEUsR0FBRyxHQUFHLE9BQU8sRUFBRThFLFFBQVEsRUFBRSxDQUFDLENBQUM7RUFDM0QsQ0FBQyxNQUFNO0lBQUE7SUFBQTFGLGNBQUEsR0FBQVUsQ0FBQTtJQUFBVixjQUFBLEdBQUFHLENBQUE7SUFBQSxJQUFJMEIsTUFBTSxLQUFLLENBQUMsRUFBRTtNQUFBO01BQUE3QixjQUFBLEdBQUFVLENBQUE7TUFBQVYsY0FBQSxHQUFBRyxDQUFBO01BQ3ZCc0YsV0FBVyxDQUFDaEMsSUFBSSxDQUFDdUIsV0FBVyxDQUFDLENBQUNwRSxHQUFHLEdBQUcsSUFBSSxFQUFFOEUsUUFBUSxFQUFFLENBQUMsQ0FBQztNQUFBO01BQUExRixjQUFBLEdBQUFHLENBQUE7TUFDdERzRixXQUFXLENBQUNoQyxJQUFJLENBQUN1QixXQUFXLENBQUMsQ0FBQ3BFLEdBQUcsR0FBRyxLQUFLLEVBQUU4RSxRQUFRLEVBQUUsQ0FBQyxDQUFDO01BQUE7TUFBQTFGLGNBQUEsR0FBQUcsQ0FBQTtNQUN2RHNGLFdBQVcsQ0FBQ2hDLElBQUksQ0FBQ3VCLFdBQVcsQ0FBQyxDQUFDcEUsR0FBRyxHQUFHLE1BQU0sRUFBRThFLFFBQVEsRUFBRSxDQUFDLENBQUM7SUFDMUQsQ0FBQyxNQUFNO01BQUE7TUFBQTFGLGNBQUEsR0FBQVUsQ0FBQTtNQUFBVixjQUFBLEdBQUFHLENBQUE7TUFBQTtNQUFJO01BQUEsQ0FBQUgsY0FBQSxHQUFBVSxDQUFBLFdBQUFtQixNQUFNLEtBQUssQ0FBQztNQUFBO01BQUEsQ0FBQTdCLGNBQUEsR0FBQVUsQ0FBQSxXQUFJbUIsTUFBTSxLQUFLLENBQUMsR0FBRTtRQUFBO1FBQUE3QixjQUFBLEdBQUFVLENBQUE7UUFBQVYsY0FBQSxHQUFBRyxDQUFBO1FBQ3ZDc0YsV0FBVyxDQUFDaEMsSUFBSSxDQUFDdUIsV0FBVyxDQUFDLENBQUNwRSxHQUFHLEdBQUcsR0FBRyxFQUFFOEUsUUFBUSxFQUFFLENBQUMsQ0FBQztRQUFBO1FBQUExRixjQUFBLEdBQUFHLENBQUE7UUFDckRzRixXQUFXLENBQUNoQyxJQUFJLENBQUN1QixXQUFXLENBQUMsQ0FBQ3BFLEdBQUcsR0FBRyxJQUFJLEVBQUU4RSxRQUFRLEVBQUUsQ0FBQyxDQUFDO1FBQUE7UUFBQTFGLGNBQUEsR0FBQUcsQ0FBQTtRQUN0RHNGLFdBQVcsQ0FBQ2hDLElBQUksQ0FBQ3VCLFdBQVcsQ0FBQyxDQUFDcEUsR0FBRyxHQUFHLE1BQU0sRUFBRThFLFFBQVEsRUFBRSxDQUFDLENBQUM7TUFDMUQsQ0FBQyxNQUFNO1FBQUE7UUFBQTFGLGNBQUEsR0FBQVUsQ0FBQTtRQUFBVixjQUFBLEdBQUFHLENBQUE7UUFBQTtRQUFJO1FBQUEsQ0FBQUgsY0FBQSxHQUFBVSxDQUFBLFdBQUFtQixNQUFNLEtBQUssQ0FBQztRQUFBO1FBQUEsQ0FBQTdCLGNBQUEsR0FBQVUsQ0FBQSxXQUFJbUIsTUFBTSxLQUFLLENBQUMsR0FBRTtVQUFBO1VBQUE3QixjQUFBLEdBQUFVLENBQUE7VUFBQVYsY0FBQSxHQUFBRyxDQUFBO1VBQ3ZDc0YsV0FBVyxDQUFDaEMsSUFBSSxDQUFDdUIsV0FBVyxDQUFDLENBQUNwRSxHQUFHLEdBQUcsR0FBRyxFQUFFOEUsUUFBUSxFQUFFLENBQUMsQ0FBQztVQUFBO1VBQUExRixjQUFBLEdBQUFHLENBQUE7VUFDckRzRixXQUFXLENBQUNoQyxJQUFJLENBQUN1QixXQUFXLENBQUMsQ0FBQ3BFLEdBQUcsR0FBRyxJQUFJLEVBQUU4RSxRQUFRLEVBQUUsQ0FBQyxDQUFDO1VBQUE7VUFBQTFGLGNBQUEsR0FBQUcsQ0FBQTtVQUN0RHNGLFdBQVcsQ0FBQ2hDLElBQUksQ0FBQ3VCLFdBQVcsQ0FBQyxDQUFDcEUsR0FBRyxHQUFHLEtBQUssRUFBRThFLFFBQVEsRUFBRSxDQUFDLENBQUM7UUFDekQsQ0FBQyxNQUFNO1VBQUE7VUFBQTFGLGNBQUEsR0FBQVUsQ0FBQTtVQUFBVixjQUFBLEdBQUFHLENBQUE7VUFDTHNGLFdBQVcsQ0FBQ2hDLElBQUksQ0FBQ3VCLFdBQVcsQ0FBQyxDQUFDcEUsR0FBRyxHQUFHLEVBQUUsRUFBRThFLFFBQVEsRUFBRSxDQUFDLENBQUM7VUFBQTtVQUFBMUYsY0FBQSxHQUFBRyxDQUFBO1VBQ3BEc0YsV0FBVyxDQUFDaEMsSUFBSSxDQUFDdUIsV0FBVyxDQUFDLENBQUNwRSxHQUFHLEdBQUcsR0FBRyxFQUFFOEUsUUFBUSxFQUFFLENBQUMsQ0FBQztRQUN2RDtNQUFBO0lBQUE7RUFBQTtFQUFBO0VBQUExRixjQUFBLEdBQUFHLENBQUE7RUFFQXNGLFdBQVcsQ0FBQ2hDLElBQUksQ0FBQyxJQUFBbkQsTUFBQSxDQUFBdUMsU0FBUyxFQUFDLHdCQUF3QixDQUFDLENBQUM7RUFBQTtFQUFBN0MsY0FBQSxHQUFBRyxDQUFBO0VBQ3JELE9BQU9zRixXQUFXO0FBQ3BCLENBQUM7QUFBQTtBQUFBekYsY0FBQSxHQUFBRyxDQUFBO0FBRUR3RixPQUFBLENBQUF0RSxPQUFBLEdBQWU7RUFDYmQsV0FBVyxFQUFYQSxXQUFXO0VBQ1h5RSxXQUFXLEVBQVhBLFdBQVc7RUFDWC9ELGVBQWUsRUFBZkEsZUFBZTtFQUNmTyxlQUFlLEVBQWZBLGVBQWU7RUFDZjJCLGlCQUFpQixFQUFqQkEsaUJBQWlCO0VBQ2pCNUIsa0JBQWtCLEVBQWxCQSxrQkFBa0I7RUFDbEJKLGNBQWMsRUFBZEEsY0FBYztFQUNkeUQsMkJBQTJCLEVBQTNCQSwyQkFBMkI7RUFDM0JVLHlCQUF5QixFQUF6QkEseUJBQXlCO0VBQ3pCWixxQkFBcUIsRUFBckJBLHFCQUFxQjtFQUNyQkssMEJBQTBCLEVBQTFCQTtDQUNEIiwiaWdub3JlTGlzdCI6W119