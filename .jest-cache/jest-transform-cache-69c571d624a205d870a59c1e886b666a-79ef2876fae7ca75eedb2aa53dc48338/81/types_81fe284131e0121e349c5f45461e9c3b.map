{"version": 3, "names": [], "sources": ["/Users/<USER>/projects/react-native/payment-module/src/components/payment-bill-info/types.ts"], "sourcesContent": ["import {ViewStyle} from 'react-native';\n\nexport type PaymentBillInfoProps = {\n  style?: ViewStyle;\n  merchantName: string;\n  storeId: string;\n};\n\nexport type CustomerInfoProps = {\n  fullName: string;\n  categoryName: string;\n  billCode: string;\n};\n\nexport type BillListProps = {\n  amount: number;\n  dateTime: string;\n};\n"], "mappings": "", "ignoreList": []}