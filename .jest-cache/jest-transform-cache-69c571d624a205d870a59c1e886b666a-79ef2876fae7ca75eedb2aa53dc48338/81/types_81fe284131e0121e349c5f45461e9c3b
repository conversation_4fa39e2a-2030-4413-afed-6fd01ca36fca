ed47fc67db467f537cf9034708d40910
"use strict";

/* istanbul ignore next */
function cov_2ckk30dv16() {
  var path = "/Users/<USER>/projects/react-native/payment-module/src/components/payment-bill-info/types.ts";
  var hash = "a71107b302493961724ff10c19256175f039bad8";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/projects/react-native/payment-module/src/components/payment-bill-info/types.ts",
    statementMap: {
      "0": {
        start: {
          line: 3,
          column: 0
        },
        end: {
          line: 5,
          column: 3
        }
      }
    },
    fnMap: {},
    branchMap: {},
    s: {
      "0": 0
    },
    f: {},
    b: {},
    inputSourceMap: {
      version: 3,
      file: undefined,
      names: [],
      sourceRoot: undefined,
      sources: ["/Users/<USER>/projects/react-native/payment-module/src/components/payment-bill-info/types.ts"],
      sourcesContent: ["import {ViewStyle} from 'react-native';\n\nexport type PaymentBillInfoProps = {\n  style?: ViewStyle;\n  merchantName: string;\n  storeId: string;\n};\n\nexport type CustomerInfoProps = {\n  fullName: string;\n  categoryName: string;\n  billCode: string;\n};\n\nexport type BillListProps = {\n  amount: number;\n  dateTime: string;\n};\n"],
      mappings: "",
      ignoreList: []
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "a71107b302493961724ff10c19256175f039bad8"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_2ckk30dv16 = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_2ckk30dv16();
cov_2ckk30dv16().s[0]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6W10sInNvdXJjZXMiOlsiL1VzZXJzL3NvbnNhbXNldC9wcm9qZWN0cy9yZWFjdC1uYXRpdmUvcGF5bWVudC1tb2R1bGUvc3JjL2NvbXBvbmVudHMvcGF5bWVudC1iaWxsLWluZm8vdHlwZXMudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHtWaWV3U3R5bGV9IGZyb20gJ3JlYWN0LW5hdGl2ZSc7XG5cbmV4cG9ydCB0eXBlIFBheW1lbnRCaWxsSW5mb1Byb3BzID0ge1xuICBzdHlsZT86IFZpZXdTdHlsZTtcbiAgbWVyY2hhbnROYW1lOiBzdHJpbmc7XG4gIHN0b3JlSWQ6IHN0cmluZztcbn07XG5cbmV4cG9ydCB0eXBlIEN1c3RvbWVySW5mb1Byb3BzID0ge1xuICBmdWxsTmFtZTogc3RyaW5nO1xuICBjYXRlZ29yeU5hbWU6IHN0cmluZztcbiAgYmlsbENvZGU6IHN0cmluZztcbn07XG5cbmV4cG9ydCB0eXBlIEJpbGxMaXN0UHJvcHMgPSB7XG4gIGFtb3VudDogbnVtYmVyO1xuICBkYXRlVGltZTogc3RyaW5nO1xufTtcbiJdLCJtYXBwaW5ncyI6IiIsImlnbm9yZUxpc3QiOltdfQ==