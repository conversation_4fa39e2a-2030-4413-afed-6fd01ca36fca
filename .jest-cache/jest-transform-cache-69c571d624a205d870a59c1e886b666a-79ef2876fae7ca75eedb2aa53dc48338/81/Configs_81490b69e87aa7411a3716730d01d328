3748a9443407be828a4ef7f2e4b96cf5
"use strict";

/* istanbul ignore next */
function cov_2csltwb4z7() {
  var path = "/Users/<USER>/projects/react-native/payment-module/src/commons/Configs.ts";
  var hash = "65ce81e67aa00c94100a58666869c347af305a0a";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/projects/react-native/payment-module/src/commons/Configs.ts",
    statementMap: {
      "0": {
        start: {
          line: 3,
          column: 0
        },
        end: {
          line: 5,
          column: 3
        }
      },
      "1": {
        start: {
          line: 6,
          column: 0
        },
        end: {
          line: 6,
          column: 25
        }
      },
      "2": {
        start: {
          line: 7,
          column: 0
        },
        end: {
          line: 9,
          column: 2
        }
      }
    },
    fnMap: {},
    branchMap: {},
    s: {
      "0": 0,
      "1": 0,
      "2": 0
    },
    f: {},
    b: {},
    inputSourceMap: {
      version: 3,
      file: undefined,
      names: ["exports", "Configs", "MSB_BANKID"],
      sourceRoot: undefined,
      sources: ["/Users/<USER>/projects/react-native/payment-module/src/commons/Configs.ts"],
      sourcesContent: ["export const Configs = {\n  MSB_BANKID: '970426',\n};\n"],
      mappings: ";;;;;;AAAaA,OAAA,CAAAC,OAAO,GAAG;EACrBC,UAAU,EAAE;CACb",
      ignoreList: []
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "65ce81e67aa00c94100a58666869c347af305a0a"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_2csltwb4z7 = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_2csltwb4z7();
cov_2csltwb4z7().s[0]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_2csltwb4z7().s[1]++;
exports.Configs = void 0;
/* istanbul ignore next */
cov_2csltwb4z7().s[2]++;
exports.Configs = {
  MSB_BANKID: '970426'
};
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJleHBvcnRzIiwiQ29uZmlncyIsIk1TQl9CQU5LSUQiXSwic291cmNlcyI6WyIvVXNlcnMvc29uc2Ftc2V0L3Byb2plY3RzL3JlYWN0LW5hdGl2ZS9wYXltZW50LW1vZHVsZS9zcmMvY29tbW9ucy9Db25maWdzLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBjb25zdCBDb25maWdzID0ge1xuICBNU0JfQkFOS0lEOiAnOTcwNDI2Jyxcbn07XG4iXSwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUFhQSxPQUFBLENBQUFDLE9BQU8sR0FBRztFQUNyQkMsVUFBVSxFQUFFO0NBQ2IiLCJpZ25vcmVMaXN0IjpbXX0=