{"version": 3, "names": ["NativeComponentRegistry", "_interopRequireWildcard", "require", "_getRequireWildcardCache", "e", "WeakMap", "r", "t", "__esModule", "default", "has", "get", "n", "__proto__", "a", "Object", "defineProperty", "getOwnPropertyDescriptor", "u", "hasOwnProperty", "call", "i", "set", "__INTERNAL_VIEW_CONFIG", "exports", "uiViewClassName", "bubblingEventTypes", "directEventTypes", "validAttributes", "ScrollContentViewNativeComponent", "_default"], "sources": ["ScrollContentViewNativeComponent.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @format\n * @flow strict-local\n */\n\nimport type {\n  HostComponent,\n  PartialViewConfig,\n} from '../../Renderer/shims/ReactNativeTypes';\nimport type {ViewProps as Props} from '../View/ViewPropTypes';\n\nimport * as NativeComponentRegistry from '../../NativeComponent/NativeComponentRegistry';\n\nexport const __INTERNAL_VIEW_CONFIG: PartialViewConfig = {\n  uiViewClassName: 'RCTScrollContentView',\n  bubblingEventTypes: {},\n  directEventTypes: {},\n  validAttributes: {},\n};\n\nconst ScrollContentViewNativeComponent: HostComponent<Props> =\n  NativeComponentRegistry.get<Props>(\n    'RCTScrollContentView',\n    () => __INTERNAL_VIEW_CONFIG,\n  );\n\nexport default ScrollContentViewNativeComponent;\n"], "mappings": ";;;;AAgBA,IAAAA,uBAAA,GAAAC,uBAAA,CAAAC,OAAA;AAAyF,SAAAC,yBAAAC,CAAA,6BAAAC,OAAA,mBAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,yBAAAC,CAAA,WAAAA,CAAA,GAAAG,CAAA,GAAAD,CAAA,KAAAF,CAAA;AAAA,SAAAH,wBAAAG,CAAA,EAAAE,CAAA,SAAAA,CAAA,IAAAF,CAAA,IAAAA,CAAA,CAAAI,UAAA,SAAAJ,CAAA,eAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,WAAAK,OAAA,EAAAL,CAAA,QAAAG,CAAA,GAAAJ,wBAAA,CAAAG,CAAA,OAAAC,CAAA,IAAAA,CAAA,CAAAG,GAAA,CAAAN,CAAA,UAAAG,CAAA,CAAAI,GAAA,CAAAP,CAAA,OAAAQ,CAAA,KAAAC,SAAA,UAAAC,CAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,CAAA,IAAAd,CAAA,oBAAAc,CAAA,OAAAC,cAAA,CAAAC,IAAA,CAAAhB,CAAA,EAAAc,CAAA,SAAAG,CAAA,GAAAP,CAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAb,CAAA,EAAAc,CAAA,UAAAG,CAAA,KAAAA,CAAA,CAAAV,GAAA,IAAAU,CAAA,CAAAC,GAAA,IAAAP,MAAA,CAAAC,cAAA,CAAAJ,CAAA,EAAAM,CAAA,EAAAG,CAAA,IAAAT,CAAA,CAAAM,CAAA,IAAAd,CAAA,CAAAc,CAAA,YAAAN,CAAA,CAAAH,OAAA,GAAAL,CAAA,EAAAG,CAAA,IAAAA,CAAA,CAAAe,GAAA,CAAAlB,CAAA,EAAAQ,CAAA,GAAAA,CAAA;AAElF,IAAMW,sBAAyC,GAAAC,OAAA,CAAAD,sBAAA,GAAG;EACvDE,eAAe,EAAE,sBAAsB;EACvCC,kBAAkB,EAAE,CAAC,CAAC;EACtBC,gBAAgB,EAAE,CAAC,CAAC;EACpBC,eAAe,EAAE,CAAC;AACpB,CAAC;AAED,IAAMC,gCAAsD,GAC1D7B,uBAAuB,CAACW,GAAG,CACzB,sBAAsB,EACtB;EAAA,OAAMY,sBAAsB;AAAA,CAC9B,CAAC;AAAC,IAAAO,QAAA,GAAAN,OAAA,CAAAf,OAAA,GAEWoB,gCAAgC", "ignoreList": []}