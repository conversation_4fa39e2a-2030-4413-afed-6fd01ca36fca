{"version": 3, "names": ["Object", "defineProperty", "exports", "value", "useEvent", "_react", "require", "_WorkletEventHandler", "handler", "eventNames", "arguments", "length", "undefined", "rebuild", "initRef", "useRef", "current", "workletEventHandler", "WorkletEventHandler", "updateEventHandler"], "sources": ["../../../src/hook/useEvent.ts"], "sourcesContent": [null], "mappings": "AAAA,YAAY;;AAAAA,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,KAAA;AAAA;AAAAD,OAAA,CAAAE,QAAA,GAAAA,QAAA;AACZ,IAAAC,MAAA,GAAAC,OAAA;AACA,IAAAC,oBAAA,GAAAD,OAAA;AA6CO,SAASF,QAAQA,CACtBI,OAAmE,EAGtC;EAAA,IAF7BC,UAAoB,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,EAAE;EAAA,IACzBG,OAAO,GAAAH,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,KAAK;EAEf,IAAMI,OAAO,GAAG,IAAAC,aAAM,EAA8B,IAAK,CAAC;EAC1D,IAAID,OAAO,CAACE,OAAO,KAAK,IAAI,EAAE;IAC5B,IAAMC,mBAAmB,GAAG,IAAIC,wCAAmB,CACjDV,OAAO,EACPC,UACF,CAAC;IACDK,OAAO,CAACE,OAAO,GAAG;MAAEC,mBAAA,EAAAA;IAAoB,CAAC;EAC3C,CAAC,MAAM,IAAIJ,OAAO,EAAE;IAClB,IAAMI,oBAAmB,GAAGH,OAAO,CAACE,OAAO,CAACC,mBAAmB;IAC/DA,oBAAmB,CAACE,kBAAkB,CAACX,OAAO,EAAEC,UAAU,CAAC;IAC3DK,OAAO,CAACE,OAAO,GAAG;MAAEC,mBAAA,EAAAA;IAAoB,CAAC;EAC3C;EAEA,OAAOH,OAAO,CAACE,OAAO;AACxB", "ignoreList": []}