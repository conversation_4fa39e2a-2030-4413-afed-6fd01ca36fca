afe49b5eaa3f19634f15ae32d9865154
'use strict';

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.useEvent = useEvent;
var _react = require("react");
var _WorkletEventHandler = require("../WorkletEventHandler.js");
function useEvent(handler) {
  var eventNames = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : [];
  var rebuild = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;
  var initRef = (0, _react.useRef)(null);
  if (initRef.current === null) {
    var workletEventHandler = new _WorkletEventHandler.WorkletEventHandler(handler, eventNames);
    initRef.current = {
      workletEventHandler: workletEventHandler
    };
  } else if (rebuild) {
    var _workletEventHandler = initRef.current.workletEventHandler;
    _workletEventHandler.updateEventHandler(handler, eventNames);
    initRef.current = {
      workletEventHandler: _workletEventHandler
    };
  }
  return initRef.current;
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJPYmplY3QiLCJkZWZpbmVQcm9wZXJ0eSIsImV4cG9ydHMiLCJ2YWx1ZSIsInVzZUV2ZW50IiwiX3JlYWN0IiwicmVxdWlyZSIsIl9Xb3JrbGV0RXZlbnRIYW5kbGVyIiwiaGFuZGxlciIsImV2ZW50TmFtZXMiLCJhcmd1bWVudHMiLCJsZW5ndGgiLCJ1bmRlZmluZWQiLCJyZWJ1aWxkIiwiaW5pdFJlZiIsInVzZVJlZiIsImN1cnJlbnQiLCJ3b3JrbGV0RXZlbnRIYW5kbGVyIiwiV29ya2xldEV2ZW50SGFuZGxlciIsInVwZGF0ZUV2ZW50SGFuZGxlciJdLCJzb3VyY2VzIjpbIi4uLy4uLy4uL3NyYy9ob29rL3VzZUV2ZW50LnRzIl0sInNvdXJjZXNDb250ZW50IjpbbnVsbF0sIm1hcHBpbmdzIjoiQUFBQSxZQUFZOztBQUFBQSxNQUFBLENBQUFDLGNBQUEsQ0FBQUMsT0FBQTtFQUFBQyxLQUFBO0FBQUE7QUFBQUQsT0FBQSxDQUFBRSxRQUFBLEdBQUFBLFFBQUE7QUFDWixJQUFBQyxNQUFBLEdBQUFDLE9BQUE7QUFDQSxJQUFBQyxvQkFBQSxHQUFBRCxPQUFBO0FBNkNPLFNBQVNGLFFBQVFBLENBQ3RCSSxPQUFtRSxFQUd0QztFQUFBLElBRjdCQyxVQUFvQixHQUFBQyxTQUFBLENBQUFDLE1BQUEsUUFBQUQsU0FBQSxRQUFBRSxTQUFBLEdBQUFGLFNBQUEsTUFBRyxFQUFFO0VBQUEsSUFDekJHLE9BQU8sR0FBQUgsU0FBQSxDQUFBQyxNQUFBLFFBQUFELFNBQUEsUUFBQUUsU0FBQSxHQUFBRixTQUFBLE1BQUcsS0FBSztFQUVmLElBQU1JLE9BQU8sR0FBRyxJQUFBQyxhQUFNLEVBQThCLElBQUssQ0FBQztFQUMxRCxJQUFJRCxPQUFPLENBQUNFLE9BQU8sS0FBSyxJQUFJLEVBQUU7SUFDNUIsSUFBTUMsbUJBQW1CLEdBQUcsSUFBSUMsd0NBQW1CLENBQ2pEVixPQUFPLEVBQ1BDLFVBQ0YsQ0FBQztJQUNESyxPQUFPLENBQUNFLE9BQU8sR0FBRztNQUFFQyxtQkFBQSxFQUFBQTtJQUFvQixDQUFDO0VBQzNDLENBQUMsTUFBTSxJQUFJSixPQUFPLEVBQUU7SUFDbEIsSUFBTUksb0JBQW1CLEdBQUdILE9BQU8sQ0FBQ0UsT0FBTyxDQUFDQyxtQkFBbUI7SUFDL0RBLG9CQUFtQixDQUFDRSxrQkFBa0IsQ0FBQ1gsT0FBTyxFQUFFQyxVQUFVLENBQUM7SUFDM0RLLE9BQU8sQ0FBQ0UsT0FBTyxHQUFHO01BQUVDLG1CQUFBLEVBQUFBO0lBQW9CLENBQUM7RUFDM0M7RUFFQSxPQUFPSCxPQUFPLENBQUNFLE9BQU87QUFDeEIiLCJpZ25vcmVMaXN0IjpbXX0=