{"version": 3, "names": ["_interopRequireDefault", "require", "Object", "defineProperty", "exports", "value", "computeWindowedRenderLimits", "elementsThatOverlapOffsets", "keyExtractor", "newRangeCount", "_slicedToArray2", "ReactNativeFeatureFlags", "_interopRequireWildcard", "_getRequireWildcardCache", "e", "WeakMap", "r", "t", "__esModule", "default", "has", "get", "n", "__proto__", "a", "getOwnPropertyDescriptor", "u", "hasOwnProperty", "call", "i", "set", "offsets", "props", "listMetrics", "zoomScale", "arguments", "length", "undefined", "itemCount", "getItemCount", "data", "result", "offsetIndex", "currentOffset", "left", "right", "mid", "Math", "floor", "frame", "getCellMetricsApprox", "scaledOffsetStart", "offset", "scaledOffsetEnd", "prev", "next", "last", "first", "max", "min", "maxToRenderPerBatch", "windowSize", "scrollMetrics", "velocity", "<PERSON><PERSON><PERSON><PERSON>", "_scrollMetrics$zoomSc", "visibleBegin", "visibleEnd", "overscanLength", "leadFactor", "fillPreference", "overscanBegin", "overscanEnd", "lastItemOffset", "_elementsThatOverlapO", "_elementsThatOverlapO2", "overscanFirst", "overscanLast", "visible", "newCellCount", "max<PERSON><PERSON><PERSON><PERSON><PERSON>", "firstWillAddMore", "lastWillAddMore", "fixVirtualizeListCollapseWindowSize", "firstShouldIncrement", "lastShouldIncrement", "Error", "JSON", "stringify", "item", "index", "key", "id", "String"], "sources": ["VirtualizeUtils.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @flow\n * @format\n */\n\n'use strict';\n\nimport type ListMetricsAggregator, {\n  CellMetricProps,\n} from './ListMetricsAggregator';\n\nimport * as ReactNativeFeatureFlags from 'react-native/src/private/featureflags/ReactNativeFeatureFlags';\n\n/**\n * Used to find the indices of the frames that overlap the given offsets. Useful for finding the\n * items that bound different windows of content, such as the visible area or the buffered overscan\n * area.\n */\nexport function elementsThatOverlapOffsets(\n  offsets: Array<number>,\n  props: CellMetricProps,\n  listMetrics: ListMetricsAggregator,\n  zoomScale: number = 1,\n): Array<number> {\n  const itemCount = props.getItemCount(props.data);\n  const result = [];\n  for (let offsetIndex = 0; offsetIndex < offsets.length; offsetIndex++) {\n    const currentOffset = offsets[offsetIndex];\n    let left = 0;\n    let right = itemCount - 1;\n\n    while (left <= right) {\n      const mid = left + Math.floor((right - left) / 2);\n      const frame = listMetrics.getCellMetricsApprox(mid, props);\n      const scaledOffsetStart = frame.offset * zoomScale;\n      const scaledOffsetEnd = (frame.offset + frame.length) * zoomScale;\n\n      // We want the first frame that contains the offset, with inclusive bounds. Thus, for the\n      // first frame the scaledOffsetStart is inclusive, while for other frames it is exclusive.\n      if (\n        (mid === 0 && currentOffset < scaledOffsetStart) ||\n        (mid !== 0 && currentOffset <= scaledOffsetStart)\n      ) {\n        right = mid - 1;\n      } else if (currentOffset > scaledOffsetEnd) {\n        left = mid + 1;\n      } else {\n        result[offsetIndex] = mid;\n        break;\n      }\n    }\n  }\n\n  return result;\n}\n\n/**\n * Computes the number of elements in the `next` range that are new compared to the `prev` range.\n * Handy for calculating how many new items will be rendered when the render window changes so we\n * can restrict the number of new items render at once so that content can appear on the screen\n * faster.\n */\nexport function newRangeCount(\n  prev: {\n    first: number,\n    last: number,\n    ...\n  },\n  next: {\n    first: number,\n    last: number,\n    ...\n  },\n): number {\n  return (\n    next.last -\n    next.first +\n    1 -\n    Math.max(\n      0,\n      1 + Math.min(next.last, prev.last) - Math.max(next.first, prev.first),\n    )\n  );\n}\n\n/**\n * Custom logic for determining which items should be rendered given the current frame and scroll\n * metrics, as well as the previous render state. The algorithm may evolve over time, but generally\n * prioritizes the visible area first, then expands that with overscan regions ahead and behind,\n * biased in the direction of scroll.\n */\nexport function computeWindowedRenderLimits(\n  props: CellMetricProps,\n  maxToRenderPerBatch: number,\n  windowSize: number,\n  prev: {\n    first: number,\n    last: number,\n  },\n  listMetrics: ListMetricsAggregator,\n  scrollMetrics: {\n    dt: number,\n    offset: number,\n    velocity: number,\n    visibleLength: number,\n    zoomScale: number,\n    ...\n  },\n): {\n  first: number,\n  last: number,\n} {\n  const itemCount = props.getItemCount(props.data);\n  if (itemCount === 0) {\n    return {first: 0, last: -1};\n  }\n  const {offset, velocity, visibleLength, zoomScale = 1} = scrollMetrics;\n\n  // Start with visible area, then compute maximum overscan region by expanding from there, biased\n  // in the direction of scroll. Total overscan area is capped, which should cap memory consumption\n  // too.\n  const visibleBegin = Math.max(0, offset);\n  const visibleEnd = visibleBegin + visibleLength;\n  const overscanLength = (windowSize - 1) * visibleLength;\n\n  // Considering velocity seems to introduce more churn than it's worth.\n  const leadFactor = 0.5; // Math.max(0, Math.min(1, velocity / 25 + 0.5));\n\n  const fillPreference =\n    velocity > 1 ? 'after' : velocity < -1 ? 'before' : 'none';\n\n  const overscanBegin = Math.max(\n    0,\n    visibleBegin - (1 - leadFactor) * overscanLength,\n  );\n  const overscanEnd = Math.max(0, visibleEnd + leadFactor * overscanLength);\n\n  const lastItemOffset =\n    listMetrics.getCellMetricsApprox(itemCount - 1, props).offset * zoomScale;\n  if (lastItemOffset < overscanBegin) {\n    // Entire list is before our overscan window\n    return {\n      first: Math.max(0, itemCount - 1 - maxToRenderPerBatch),\n      last: itemCount - 1,\n    };\n  }\n\n  // Find the indices that correspond to the items at the render boundaries we're targeting.\n  let [overscanFirst, first, last, overscanLast] = elementsThatOverlapOffsets(\n    [overscanBegin, visibleBegin, visibleEnd, overscanEnd],\n    props,\n    listMetrics,\n    zoomScale,\n  );\n  overscanFirst = overscanFirst == null ? 0 : overscanFirst;\n  first = first == null ? Math.max(0, overscanFirst) : first;\n  overscanLast = overscanLast == null ? itemCount - 1 : overscanLast;\n  last =\n    last == null\n      ? Math.min(overscanLast, first + maxToRenderPerBatch - 1)\n      : last;\n  const visible = {first, last};\n\n  // We want to limit the number of new cells we're rendering per batch so that we can fill the\n  // content on the screen quickly. If we rendered the entire overscan window at once, the user\n  // could be staring at white space for a long time waiting for a bunch of offscreen content to\n  // render.\n  let newCellCount = newRangeCount(prev, visible);\n\n  while (true) {\n    if (first <= overscanFirst && last >= overscanLast) {\n      // If we fill the entire overscan range, we're done.\n      break;\n    }\n    const maxNewCells = newCellCount >= maxToRenderPerBatch;\n\n    let firstWillAddMore;\n    let lastWillAddMore;\n\n    if (ReactNativeFeatureFlags.fixVirtualizeListCollapseWindowSize()) {\n      firstWillAddMore = first <= prev.first;\n      lastWillAddMore = last >= prev.last;\n    } else {\n      firstWillAddMore = first <= prev.first || first > prev.last;\n      lastWillAddMore = last >= prev.last || last < prev.first;\n    }\n\n    const firstShouldIncrement =\n      first > overscanFirst && (!maxNewCells || !firstWillAddMore);\n    const lastShouldIncrement =\n      last < overscanLast && (!maxNewCells || !lastWillAddMore);\n    if (maxNewCells && !firstShouldIncrement && !lastShouldIncrement) {\n      // We only want to stop if we've hit maxNewCells AND we cannot increment first or last\n      // without rendering new items. This let's us preserve as many already rendered items as\n      // possible, reducing render churn and keeping the rendered overscan range as large as\n      // possible.\n      break;\n    }\n    if (\n      firstShouldIncrement &&\n      !(fillPreference === 'after' && lastShouldIncrement && lastWillAddMore)\n    ) {\n      if (firstWillAddMore) {\n        newCellCount++;\n      }\n      first--;\n    }\n    if (\n      lastShouldIncrement &&\n      !(fillPreference === 'before' && firstShouldIncrement && firstWillAddMore)\n    ) {\n      if (lastWillAddMore) {\n        newCellCount++;\n      }\n      last++;\n    }\n  }\n  if (\n    !(\n      last >= first &&\n      first >= 0 &&\n      last < itemCount &&\n      first >= overscanFirst &&\n      last <= overscanLast &&\n      first <= visible.first &&\n      last >= visible.last\n    )\n  ) {\n    throw new Error(\n      'Bad window calculation ' +\n        JSON.stringify({\n          first,\n          last,\n          itemCount,\n          overscanFirst,\n          overscanLast,\n          visible,\n        }),\n    );\n  }\n  return {first, last};\n}\n\nexport function keyExtractor(item: any, index: number): string {\n  if (typeof item === 'object' && item?.key != null) {\n    return item.key;\n  }\n  if (typeof item === 'object' && item?.id != null) {\n    return item.id;\n  }\n  return String(index);\n}\n"], "mappings": "AAUA,YAAY;;AAAC,IAAAA,sBAAA,GAAAC,OAAA;AAAAC,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,KAAA;AAAA;AAAAD,OAAA,CAAAE,2BAAA,GAAAA,2BAAA;AAAAF,OAAA,CAAAG,0BAAA,GAAAA,0BAAA;AAAAH,OAAA,CAAAI,YAAA,GAAAA,YAAA;AAAAJ,OAAA,CAAAK,aAAA,GAAAA,aAAA;AAAA,IAAAC,eAAA,GAAAV,sBAAA,CAAAC,OAAA;AAMb,IAAAU,uBAAA,GAAAC,uBAAA,CAAAX,OAAA;AAAyG,SAAAY,yBAAAC,CAAA,6BAAAC,OAAA,mBAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,yBAAAC,CAAA,WAAAA,CAAA,GAAAG,CAAA,GAAAD,CAAA,KAAAF,CAAA;AAAA,SAAAF,wBAAAE,CAAA,EAAAE,CAAA,SAAAA,CAAA,IAAAF,CAAA,IAAAA,CAAA,CAAAI,UAAA,SAAAJ,CAAA,eAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,WAAAK,OAAA,EAAAL,CAAA,QAAAG,CAAA,GAAAJ,wBAAA,CAAAG,CAAA,OAAAC,CAAA,IAAAA,CAAA,CAAAG,GAAA,CAAAN,CAAA,UAAAG,CAAA,CAAAI,GAAA,CAAAP,CAAA,OAAAQ,CAAA,KAAAC,SAAA,UAAAC,CAAA,GAAAtB,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAuB,wBAAA,WAAAC,CAAA,IAAAZ,CAAA,oBAAAY,CAAA,OAAAC,cAAA,CAAAC,IAAA,CAAAd,CAAA,EAAAY,CAAA,SAAAG,CAAA,GAAAL,CAAA,GAAAtB,MAAA,CAAAuB,wBAAA,CAAAX,CAAA,EAAAY,CAAA,UAAAG,CAAA,KAAAA,CAAA,CAAAR,GAAA,IAAAQ,CAAA,CAAAC,GAAA,IAAA5B,MAAA,CAAAC,cAAA,CAAAmB,CAAA,EAAAI,CAAA,EAAAG,CAAA,IAAAP,CAAA,CAAAI,CAAA,IAAAZ,CAAA,CAAAY,CAAA,YAAAJ,CAAA,CAAAH,OAAA,GAAAL,CAAA,EAAAG,CAAA,IAAAA,CAAA,CAAAa,GAAA,CAAAhB,CAAA,EAAAQ,CAAA,GAAAA,CAAA;AAOlG,SAASf,0BAA0BA,CACxCwB,OAAsB,EACtBC,KAAsB,EACtBC,WAAkC,EAEnB;EAAA,IADfC,SAAiB,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC;EAErB,IAAMG,SAAS,GAAGN,KAAK,CAACO,YAAY,CAACP,KAAK,CAACQ,IAAI,CAAC;EAChD,IAAMC,MAAM,GAAG,EAAE;EACjB,KAAK,IAAIC,WAAW,GAAG,CAAC,EAAEA,WAAW,GAAGX,OAAO,CAACK,MAAM,EAAEM,WAAW,EAAE,EAAE;IACrE,IAAMC,aAAa,GAAGZ,OAAO,CAACW,WAAW,CAAC;IAC1C,IAAIE,IAAI,GAAG,CAAC;IACZ,IAAIC,KAAK,GAAGP,SAAS,GAAG,CAAC;IAEzB,OAAOM,IAAI,IAAIC,KAAK,EAAE;MACpB,IAAMC,GAAG,GAAGF,IAAI,GAAGG,IAAI,CAACC,KAAK,CAAC,CAACH,KAAK,GAAGD,IAAI,IAAI,CAAC,CAAC;MACjD,IAAMK,KAAK,GAAGhB,WAAW,CAACiB,oBAAoB,CAACJ,GAAG,EAAEd,KAAK,CAAC;MAC1D,IAAMmB,iBAAiB,GAAGF,KAAK,CAACG,MAAM,GAAGlB,SAAS;MAClD,IAAMmB,eAAe,GAAG,CAACJ,KAAK,CAACG,MAAM,GAAGH,KAAK,CAACb,MAAM,IAAIF,SAAS;MAIjE,IACGY,GAAG,KAAK,CAAC,IAAIH,aAAa,GAAGQ,iBAAiB,IAC9CL,GAAG,KAAK,CAAC,IAAIH,aAAa,IAAIQ,iBAAkB,EACjD;QACAN,KAAK,GAAGC,GAAG,GAAG,CAAC;MACjB,CAAC,MAAM,IAAIH,aAAa,GAAGU,eAAe,EAAE;QAC1CT,IAAI,GAAGE,GAAG,GAAG,CAAC;MAChB,CAAC,MAAM;QACLL,MAAM,CAACC,WAAW,CAAC,GAAGI,GAAG;QACzB;MACF;IACF;EACF;EAEA,OAAOL,MAAM;AACf;AAQO,SAAShC,aAAaA,CAC3B6C,IAIC,EACDC,IAIC,EACO;EACR,OACEA,IAAI,CAACC,IAAI,GACTD,IAAI,CAACE,KAAK,GACV,CAAC,GACDV,IAAI,CAACW,GAAG,CACN,CAAC,EACD,CAAC,GAAGX,IAAI,CAACY,GAAG,CAACJ,IAAI,CAACC,IAAI,EAAEF,IAAI,CAACE,IAAI,CAAC,GAAGT,IAAI,CAACW,GAAG,CAACH,IAAI,CAACE,KAAK,EAAEH,IAAI,CAACG,KAAK,CACtE,CAAC;AAEL;AAQO,SAASnD,2BAA2BA,CACzC0B,KAAsB,EACtB4B,mBAA2B,EAC3BC,UAAkB,EAClBP,IAGC,EACDrB,WAAkC,EAClC6B,aAOC,EAID;EACA,IAAMxB,SAAS,GAAGN,KAAK,CAACO,YAAY,CAACP,KAAK,CAACQ,IAAI,CAAC;EAChD,IAAIF,SAAS,KAAK,CAAC,EAAE;IACnB,OAAO;MAACmB,KAAK,EAAE,CAAC;MAAED,IAAI,EAAE,CAAC;IAAC,CAAC;EAC7B;EACA,IAAOJ,MAAM,GAA4CU,aAAa,CAA/DV,MAAM;IAAEW,QAAQ,GAAkCD,aAAa,CAAvDC,QAAQ;IAAEC,aAAa,GAAmBF,aAAa,CAA7CE,aAAa;IAAAC,qBAAA,GAAmBH,aAAa,CAA9B5B,SAAS;IAATA,SAAS,GAAA+B,qBAAA,cAAG,CAAC,GAAAA,qBAAA;EAKrD,IAAMC,YAAY,GAAGnB,IAAI,CAACW,GAAG,CAAC,CAAC,EAAEN,MAAM,CAAC;EACxC,IAAMe,UAAU,GAAGD,YAAY,GAAGF,aAAa;EAC/C,IAAMI,cAAc,GAAG,CAACP,UAAU,GAAG,CAAC,IAAIG,aAAa;EAGvD,IAAMK,UAAU,GAAG,GAAG;EAEtB,IAAMC,cAAc,GAClBP,QAAQ,GAAG,CAAC,GAAG,OAAO,GAAGA,QAAQ,GAAG,CAAC,CAAC,GAAG,QAAQ,GAAG,MAAM;EAE5D,IAAMQ,aAAa,GAAGxB,IAAI,CAACW,GAAG,CAC5B,CAAC,EACDQ,YAAY,GAAG,CAAC,CAAC,GAAGG,UAAU,IAAID,cACpC,CAAC;EACD,IAAMI,WAAW,GAAGzB,IAAI,CAACW,GAAG,CAAC,CAAC,EAAES,UAAU,GAAGE,UAAU,GAAGD,cAAc,CAAC;EAEzE,IAAMK,cAAc,GAClBxC,WAAW,CAACiB,oBAAoB,CAACZ,SAAS,GAAG,CAAC,EAAEN,KAAK,CAAC,CAACoB,MAAM,GAAGlB,SAAS;EAC3E,IAAIuC,cAAc,GAAGF,aAAa,EAAE;IAElC,OAAO;MACLd,KAAK,EAAEV,IAAI,CAACW,GAAG,CAAC,CAAC,EAAEpB,SAAS,GAAG,CAAC,GAAGsB,mBAAmB,CAAC;MACvDJ,IAAI,EAAElB,SAAS,GAAG;IACpB,CAAC;EACH;EAGA,IAAAoC,qBAAA,GAAiDnE,0BAA0B,CACzE,CAACgE,aAAa,EAAEL,YAAY,EAAEC,UAAU,EAAEK,WAAW,CAAC,EACtDxC,KAAK,EACLC,WAAW,EACXC,SACF,CAAC;IAAAyC,sBAAA,OAAAjE,eAAA,CAAAS,OAAA,EAAAuD,qBAAA;IALIE,aAAa,GAAAD,sBAAA;IAAElB,KAAK,GAAAkB,sBAAA;IAAEnB,IAAI,GAAAmB,sBAAA;IAAEE,YAAY,GAAAF,sBAAA;EAM7CC,aAAa,GAAGA,aAAa,IAAI,IAAI,GAAG,CAAC,GAAGA,aAAa;EACzDnB,KAAK,GAAGA,KAAK,IAAI,IAAI,GAAGV,IAAI,CAACW,GAAG,CAAC,CAAC,EAAEkB,aAAa,CAAC,GAAGnB,KAAK;EAC1DoB,YAAY,GAAGA,YAAY,IAAI,IAAI,GAAGvC,SAAS,GAAG,CAAC,GAAGuC,YAAY;EAClErB,IAAI,GACFA,IAAI,IAAI,IAAI,GACRT,IAAI,CAACY,GAAG,CAACkB,YAAY,EAAEpB,KAAK,GAAGG,mBAAmB,GAAG,CAAC,CAAC,GACvDJ,IAAI;EACV,IAAMsB,OAAO,GAAG;IAACrB,KAAK,EAALA,KAAK;IAAED,IAAI,EAAJA;EAAI,CAAC;EAM7B,IAAIuB,YAAY,GAAGtE,aAAa,CAAC6C,IAAI,EAAEwB,OAAO,CAAC;EAE/C,OAAO,IAAI,EAAE;IACX,IAAIrB,KAAK,IAAImB,aAAa,IAAIpB,IAAI,IAAIqB,YAAY,EAAE;MAElD;IACF;IACA,IAAMG,WAAW,GAAGD,YAAY,IAAInB,mBAAmB;IAEvD,IAAIqB,gBAAgB;IACpB,IAAIC,eAAe;IAEnB,IAAIvE,uBAAuB,CAACwE,mCAAmC,CAAC,CAAC,EAAE;MACjEF,gBAAgB,GAAGxB,KAAK,IAAIH,IAAI,CAACG,KAAK;MACtCyB,eAAe,GAAG1B,IAAI,IAAIF,IAAI,CAACE,IAAI;IACrC,CAAC,MAAM;MACLyB,gBAAgB,GAAGxB,KAAK,IAAIH,IAAI,CAACG,KAAK,IAAIA,KAAK,GAAGH,IAAI,CAACE,IAAI;MAC3D0B,eAAe,GAAG1B,IAAI,IAAIF,IAAI,CAACE,IAAI,IAAIA,IAAI,GAAGF,IAAI,CAACG,KAAK;IAC1D;IAEA,IAAM2B,oBAAoB,GACxB3B,KAAK,GAAGmB,aAAa,KAAK,CAACI,WAAW,IAAI,CAACC,gBAAgB,CAAC;IAC9D,IAAMI,mBAAmB,GACvB7B,IAAI,GAAGqB,YAAY,KAAK,CAACG,WAAW,IAAI,CAACE,eAAe,CAAC;IAC3D,IAAIF,WAAW,IAAI,CAACI,oBAAoB,IAAI,CAACC,mBAAmB,EAAE;MAKhE;IACF;IACA,IACED,oBAAoB,IACpB,EAAEd,cAAc,KAAK,OAAO,IAAIe,mBAAmB,IAAIH,eAAe,CAAC,EACvE;MACA,IAAID,gBAAgB,EAAE;QACpBF,YAAY,EAAE;MAChB;MACAtB,KAAK,EAAE;IACT;IACA,IACE4B,mBAAmB,IACnB,EAAEf,cAAc,KAAK,QAAQ,IAAIc,oBAAoB,IAAIH,gBAAgB,CAAC,EAC1E;MACA,IAAIC,eAAe,EAAE;QACnBH,YAAY,EAAE;MAChB;MACAvB,IAAI,EAAE;IACR;EACF;EACA,IACE,EACEA,IAAI,IAAIC,KAAK,IACbA,KAAK,IAAI,CAAC,IACVD,IAAI,GAAGlB,SAAS,IAChBmB,KAAK,IAAImB,aAAa,IACtBpB,IAAI,IAAIqB,YAAY,IACpBpB,KAAK,IAAIqB,OAAO,CAACrB,KAAK,IACtBD,IAAI,IAAIsB,OAAO,CAACtB,IAAI,CACrB,EACD;IACA,MAAM,IAAI8B,KAAK,CACb,yBAAyB,GACvBC,IAAI,CAACC,SAAS,CAAC;MACb/B,KAAK,EAALA,KAAK;MACLD,IAAI,EAAJA,IAAI;MACJlB,SAAS,EAATA,SAAS;MACTsC,aAAa,EAAbA,aAAa;MACbC,YAAY,EAAZA,YAAY;MACZC,OAAO,EAAPA;IACF,CAAC,CACL,CAAC;EACH;EACA,OAAO;IAACrB,KAAK,EAALA,KAAK;IAAED,IAAI,EAAJA;EAAI,CAAC;AACtB;AAEO,SAAShD,YAAYA,CAACiF,IAAS,EAAEC,KAAa,EAAU;EAC7D,IAAI,OAAOD,IAAI,KAAK,QAAQ,IAAI,CAAAA,IAAI,oBAAJA,IAAI,CAAEE,GAAG,KAAI,IAAI,EAAE;IACjD,OAAOF,IAAI,CAACE,GAAG;EACjB;EACA,IAAI,OAAOF,IAAI,KAAK,QAAQ,IAAI,CAAAA,IAAI,oBAAJA,IAAI,CAAEG,EAAE,KAAI,IAAI,EAAE;IAChD,OAAOH,IAAI,CAACG,EAAE;EAChB;EACA,OAAOC,MAAM,CAACH,KAAK,CAAC;AACtB", "ignoreList": []}