68a710fe1c87d9bbe98ff6e93d90d0b2
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.VScrollViewNativeComponent = exports.VScrollContentViewNativeComponent = void 0;
var _ScrollContentViewNativeComponent = _interopRequireDefault(require("../../../Libraries/Components/ScrollView/ScrollContentViewNativeComponent"));
var _ScrollViewNativeComponent = _interopRequireDefault(require("../../../Libraries/Components/ScrollView/ScrollViewNativeComponent"));
var _View = _interopRequireDefault(require("../../../Libraries/Components/View/View"));
var _Platform = _interopRequireDefault(require("../../../Libraries/Utilities/Platform"));
var VScrollViewNativeComponent = exports.VScrollViewNativeComponent = _ScrollViewNativeComponent.default;
var VScrollContentViewNativeComponent = exports.VScrollContentViewNativeComponent = _Platform.default.OS === 'android' ? _View.default : _ScrollContentViewNativeComponent.default;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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