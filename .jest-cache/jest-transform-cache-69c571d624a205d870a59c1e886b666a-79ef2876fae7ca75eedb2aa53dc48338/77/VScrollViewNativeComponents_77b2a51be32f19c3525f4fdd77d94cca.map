{"version": 3, "names": ["_ScrollContentViewNativeComponent", "_interopRequireDefault", "require", "_ScrollViewNativeComponent", "_View", "_Platform", "VScrollViewNativeComponent", "exports", "ScrollViewNativeComponent", "VScrollContentViewNativeComponent", "Platform", "OS", "View", "ScrollContentViewNativeComponent"], "sources": ["VScrollViewNativeComponents.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @flow strict-local\n * @format\n * @oncall react_native\n */\n\nimport type {ScrollViewNativeProps} from '../../../Libraries/Components/ScrollView/ScrollViewNativeComponentType';\nimport type {ViewProps} from '../../../Libraries/Components/View/ViewPropTypes';\nimport type {HostComponent} from '../../../Libraries/Renderer/shims/ReactNativeTypes';\n\nimport ScrollContentViewNativeComponent from '../../../Libraries/Components/ScrollView/ScrollContentViewNativeComponent';\nimport ScrollViewNativeComponent from '../../../Libraries/Components/ScrollView/ScrollViewNativeComponent';\nimport View from '../../../Libraries/Components/View/View';\nimport Platform from '../../../Libraries/Utilities/Platform';\n\nexport const VScrollViewNativeComponent: HostComponent<ScrollViewNativeProps> =\n  ScrollViewNativeComponent;\n\nexport const VScrollContentViewNativeComponent: HostComponent<ViewProps> =\n  Platform.OS === 'android' ? View : ScrollContentViewNativeComponent;\n"], "mappings": ";;;;;AAeA,IAAAA,iCAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,0BAAA,GAAAF,sBAAA,CAAAC,OAAA;AACA,IAAAE,KAAA,GAAAH,sBAAA,CAAAC,OAAA;AACA,IAAAG,SAAA,GAAAJ,sBAAA,CAAAC,OAAA;AAEO,IAAMI,0BAAgE,GAAAC,OAAA,CAAAD,0BAAA,GAC3EE,kCAAyB;AAEpB,IAAMC,iCAA2D,GAAAF,OAAA,CAAAE,iCAAA,GACtEC,iBAAQ,CAACC,EAAE,KAAK,SAAS,GAAGC,aAAI,GAAGC,yCAAgC", "ignoreList": []}