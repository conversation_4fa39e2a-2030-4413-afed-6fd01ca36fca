9c381eaf69fd0cf13eee2615e2410918
'use strict';

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.computeWindowedRenderLimits = computeWindowedRenderLimits;
exports.elementsThatOverlapOffsets = elementsThatOverlapOffsets;
exports.keyExtractor = keyExtractor;
exports.newRangeCount = newRangeCount;
var _slicedToArray2 = _interopRequireDefault(require("@babel/runtime/helpers/slicedToArray"));
var ReactNativeFeatureFlags = _interopRequireWildcard(require("react-native/src/private/featureflags/ReactNativeFeatureFlags"));
function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
function elementsThatOverlapOffsets(offsets, props, listMetrics) {
  var zoomScale = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : 1;
  var itemCount = props.getItemCount(props.data);
  var result = [];
  for (var offsetIndex = 0; offsetIndex < offsets.length; offsetIndex++) {
    var currentOffset = offsets[offsetIndex];
    var left = 0;
    var right = itemCount - 1;
    while (left <= right) {
      var mid = left + Math.floor((right - left) / 2);
      var frame = listMetrics.getCellMetricsApprox(mid, props);
      var scaledOffsetStart = frame.offset * zoomScale;
      var scaledOffsetEnd = (frame.offset + frame.length) * zoomScale;
      if (mid === 0 && currentOffset < scaledOffsetStart || mid !== 0 && currentOffset <= scaledOffsetStart) {
        right = mid - 1;
      } else if (currentOffset > scaledOffsetEnd) {
        left = mid + 1;
      } else {
        result[offsetIndex] = mid;
        break;
      }
    }
  }
  return result;
}
function newRangeCount(prev, next) {
  return next.last - next.first + 1 - Math.max(0, 1 + Math.min(next.last, prev.last) - Math.max(next.first, prev.first));
}
function computeWindowedRenderLimits(props, maxToRenderPerBatch, windowSize, prev, listMetrics, scrollMetrics) {
  var itemCount = props.getItemCount(props.data);
  if (itemCount === 0) {
    return {
      first: 0,
      last: -1
    };
  }
  var offset = scrollMetrics.offset,
    velocity = scrollMetrics.velocity,
    visibleLength = scrollMetrics.visibleLength,
    _scrollMetrics$zoomSc = scrollMetrics.zoomScale,
    zoomScale = _scrollMetrics$zoomSc === void 0 ? 1 : _scrollMetrics$zoomSc;
  var visibleBegin = Math.max(0, offset);
  var visibleEnd = visibleBegin + visibleLength;
  var overscanLength = (windowSize - 1) * visibleLength;
  var leadFactor = 0.5;
  var fillPreference = velocity > 1 ? 'after' : velocity < -1 ? 'before' : 'none';
  var overscanBegin = Math.max(0, visibleBegin - (1 - leadFactor) * overscanLength);
  var overscanEnd = Math.max(0, visibleEnd + leadFactor * overscanLength);
  var lastItemOffset = listMetrics.getCellMetricsApprox(itemCount - 1, props).offset * zoomScale;
  if (lastItemOffset < overscanBegin) {
    return {
      first: Math.max(0, itemCount - 1 - maxToRenderPerBatch),
      last: itemCount - 1
    };
  }
  var _elementsThatOverlapO = elementsThatOverlapOffsets([overscanBegin, visibleBegin, visibleEnd, overscanEnd], props, listMetrics, zoomScale),
    _elementsThatOverlapO2 = (0, _slicedToArray2.default)(_elementsThatOverlapO, 4),
    overscanFirst = _elementsThatOverlapO2[0],
    first = _elementsThatOverlapO2[1],
    last = _elementsThatOverlapO2[2],
    overscanLast = _elementsThatOverlapO2[3];
  overscanFirst = overscanFirst == null ? 0 : overscanFirst;
  first = first == null ? Math.max(0, overscanFirst) : first;
  overscanLast = overscanLast == null ? itemCount - 1 : overscanLast;
  last = last == null ? Math.min(overscanLast, first + maxToRenderPerBatch - 1) : last;
  var visible = {
    first: first,
    last: last
  };
  var newCellCount = newRangeCount(prev, visible);
  while (true) {
    if (first <= overscanFirst && last >= overscanLast) {
      break;
    }
    var maxNewCells = newCellCount >= maxToRenderPerBatch;
    var firstWillAddMore = void 0;
    var lastWillAddMore = void 0;
    if (ReactNativeFeatureFlags.fixVirtualizeListCollapseWindowSize()) {
      firstWillAddMore = first <= prev.first;
      lastWillAddMore = last >= prev.last;
    } else {
      firstWillAddMore = first <= prev.first || first > prev.last;
      lastWillAddMore = last >= prev.last || last < prev.first;
    }
    var firstShouldIncrement = first > overscanFirst && (!maxNewCells || !firstWillAddMore);
    var lastShouldIncrement = last < overscanLast && (!maxNewCells || !lastWillAddMore);
    if (maxNewCells && !firstShouldIncrement && !lastShouldIncrement) {
      break;
    }
    if (firstShouldIncrement && !(fillPreference === 'after' && lastShouldIncrement && lastWillAddMore)) {
      if (firstWillAddMore) {
        newCellCount++;
      }
      first--;
    }
    if (lastShouldIncrement && !(fillPreference === 'before' && firstShouldIncrement && firstWillAddMore)) {
      if (lastWillAddMore) {
        newCellCount++;
      }
      last++;
    }
  }
  if (!(last >= first && first >= 0 && last < itemCount && first >= overscanFirst && last <= overscanLast && first <= visible.first && last >= visible.last)) {
    throw new Error('Bad window calculation ' + JSON.stringify({
      first: first,
      last: last,
      itemCount: itemCount,
      overscanFirst: overscanFirst,
      overscanLast: overscanLast,
      visible: visible
    }));
  }
  return {
    first: first,
    last: last
  };
}
function keyExtractor(item, index) {
  if (typeof item === 'object' && (item == null ? void 0 : item.key) != null) {
    return item.key;
  }
  if (typeof item === 'object' && (item == null ? void 0 : item.id) != null) {
    return item.id;
  }
  return String(index);
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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