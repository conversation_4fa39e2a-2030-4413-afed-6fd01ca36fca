{"version": 3, "names": ["Object", "defineProperty", "exports", "value", "startMapper", "stopMapper", "_PlatformChecker", "require", "_threads", "_isSharedValue", "IS_JEST", "isJest", "createMapperRegistry", "mappers", "Map", "sortedMappers", "runRequested", "processingMappers", "updateMappersOrder", "pre", "for<PERSON>ach", "mapper", "outputs", "output", "preMappers", "get", "undefined", "set", "push", "visited", "Set", "newOrder", "dfs", "add", "input", "inputs", "preMapper", "has", "mapperRun", "size", "length", "dirty", "worklet", "maybeRequestUpdates", "requestAnimationFrame", "queueMicrotask", "extractInputs", "resultArray", "Array", "isArray", "isSharedValue", "getPrototypeOf", "prototype", "element", "values", "start", "mapperID", "id", "sv", "addListener", "stop", "delete", "removeListener", "MAPPER_ID", "arguments", "runOnUI", "mapperRegistry", "global", "__mapperRegistry"], "sources": ["../../src/mappers.ts"], "sourcesContent": [null], "mappings": "AAAA,YAAY;;AAAAA,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,KAAA;AAAA;AAAAD,OAAA,CAAAE,WAAA,GAAAA,WAAA;AAAAF,OAAA,CAAAG,UAAA,GAAAA,UAAA;AAMZ,IAAAC,gBAAA,GAAAC,OAAA;AACA,IAAAC,QAAA,GAAAD,OAAA;AACA,IAAAE,cAAA,GAAAF,OAAA;AAEA,IAAMG,OAAO,GAAG,IAAAC,uBAAM,EAAC,CAAC;AAYxB,SAASC,oBAAoBA,CAAA,EAAG;EAC9B,SAAS;;EACT,IAAMC,OAAO,GAAG,IAAIC,GAAG,CAAiB,CAAC;EACzC,IAAIC,aAAuB,GAAG,EAAE;EAEhC,IAAIC,YAAY,GAAG,KAAK;EACxB,IAAIC,iBAAiB,GAAG,KAAK;EAE7B,SAASC,kBAAkBA,CAAA,EAAG;IAqB5B,IAAMC,GAAG,GAAG,IAAIL,GAAG,CAAC,CAAC;IACrBD,OAAO,CAACO,OAAO,CAAE,UAAAC,MAAM,EAAK;MAC1B,IAAIA,MAAM,CAACC,OAAO,EAAE;QAClB,KAAK,IAAMC,MAAM,IAAIF,MAAM,CAACC,OAAO,EAAE;UACnC,IAAME,UAAU,GAAGL,GAAG,CAACM,GAAG,CAACF,MAAM,CAAC;UAClC,IAAIC,UAAU,KAAKE,SAAS,EAAE;YAC5BP,GAAG,CAACQ,GAAG,CAACJ,MAAM,EAAE,CAACF,MAAM,CAAC,CAAC;UAC3B,CAAC,MAAM;YACLG,UAAU,CAACI,IAAI,CAACP,MAAM,CAAC;UACzB;QACF;MACF;IACF,CAAC,CAAC;IACF,IAAMQ,OAAO,GAAG,IAAIC,GAAG,CAAC,CAAC;IACzB,IAAMC,QAAkB,GAAG,EAAE;IAC7B,SAASC,GAAGA,CAACX,MAAc,EAAE;MAC3BQ,OAAO,CAACI,GAAG,CAACZ,MAAM,CAAC;MACnB,KAAK,IAAMa,KAAK,IAAIb,MAAM,CAACc,MAAM,EAAE;QACjC,IAAMX,UAAU,GAAGL,GAAG,CAACM,GAAG,CAACS,KAAK,CAAC;QACjC,IAAIV,UAAU,EAAE;UACd,KAAK,IAAMY,SAAS,IAAIZ,UAAU,EAAE;YAClC,IAAI,CAACK,OAAO,CAACQ,GAAG,CAACD,SAAS,CAAC,EAAE;cAC3BJ,GAAG,CAACI,SAAS,CAAC;YAChB;UACF;QACF;MACF;MACAL,QAAQ,CAACH,IAAI,CAACP,MAAM,CAAC;IACvB;IACAR,OAAO,CAACO,OAAO,CAAE,UAAAC,MAAM,EAAK;MAC1B,IAAI,CAACQ,OAAO,CAACQ,GAAG,CAAChB,MAAM,CAAC,EAAE;QACxBW,GAAG,CAACX,MAAM,CAAC;MACb;IACF,CAAC,CAAC;IACFN,aAAa,GAAGgB,QAAQ;EAC1B;EAEA,SAASO,SAASA,CAAA,EAAG;IACnBtB,YAAY,GAAG,KAAK;IACpB,IAAIC,iBAAiB,EAAE;MACrB;IACF;IACA,IAAI;MACFA,iBAAiB,GAAG,IAAI;MACxB,IAAIJ,OAAO,CAAC0B,IAAI,KAAKxB,aAAa,CAACyB,MAAM,EAAE;QACzCtB,kBAAkB,CAAC,CAAC;MACtB;MACA,KAAK,IAAMG,MAAM,IAAIN,aAAa,EAAE;QAClC,IAAIM,MAAM,CAACoB,KAAK,EAAE;UAChBpB,MAAM,CAACoB,KAAK,GAAG,KAAK;UACpBpB,MAAM,CAACqB,OAAO,CAAC,CAAC;QAClB;MACF;IACF,CAAC,SAAS;MACRzB,iBAAiB,GAAG,KAAK;IAC3B;EACF;EAEA,SAAS0B,mBAAmBA,CAAA,EAAG;IAC7B,IAAIjC,OAAO,EAAE;MAOX4B,SAAS,CAAC,CAAC;IACb,CAAC,MAAM,IAAI,CAACtB,YAAY,EAAE;MACxB,IAAIC,iBAAiB,EAAE;QAYrB2B,qBAAqB,CAACN,SAAS,CAAC;MAClC,CAAC,MAAM;QACLO,cAAc,CAACP,SAAS,CAAC;MAC3B;MACAtB,YAAY,GAAG,IAAI;IACrB;EACF;EAEA,SAAS8B,aAAaA,CACpBX,MAAe,EACfY,WAAkC,EACX;IACvB,IAAIC,KAAK,CAACC,OAAO,CAACd,MAAM,CAAC,EAAE;MACzB,KAAK,IAAMD,KAAK,IAAIC,MAAM,EAAE;QAC1BD,KAAK,IAAIY,aAAa,CAACZ,KAAK,EAAEa,WAAW,CAAC;MAC5C;IACF,CAAC,MAAM,IAAI,IAAAG,4BAAa,EAACf,MAAM,CAAC,EAAE;MAChCY,WAAW,CAACnB,IAAI,CAACO,MAAM,CAAC;IAC1B,CAAC,MAAM,IAAInC,MAAM,CAACmD,cAAc,CAAChB,MAAM,CAAC,KAAKnC,MAAM,CAACoD,SAAS,EAAE;MAI7D,KAAK,IAAMC,OAAO,IAAIrD,MAAM,CAACsD,MAAM,CAACnB,MAAiC,CAAC,EAAE;QACtEkB,OAAO,IAAIP,aAAa,CAACO,OAAO,EAAEN,WAAW,CAAC;MAChD;IACF;IACA,OAAOA,WAAW;EACpB;EAEA,OAAO;IACLQ,KAAK,EAAE,SAAPA,KAAKA,CACHC,QAAgB,EAChBd,OAAmB,EACnBP,MAAuB,EACvBb,OAAuB,EACpB;MACH,IAAMD,MAAc,GAAG;QACrBoC,EAAE,EAAED,QAAQ;QACZf,KAAK,EAAE,IAAI;QACXC,OAAO,EAAPA,OAAO;QACPP,MAAM,EAAEW,aAAa,CAACX,MAAM,EAAE,EAAE,CAAC;QACjCb,OAAA,EAAAA;MACF,CAAC;MACDT,OAAO,CAACc,GAAG,CAACN,MAAM,CAACoC,EAAE,EAAEpC,MAAM,CAAC;MAC9BN,aAAa,GAAG,EAAE;MAClB,KAAK,IAAM2C,EAAE,IAAIrC,MAAM,CAACc,MAAM,EAAE;QAC9BuB,EAAE,CAACC,WAAW,CAACtC,MAAM,CAACoC,EAAE,EAAE,YAAM;UAC9BpC,MAAM,CAACoB,KAAK,GAAG,IAAI;UACnBE,mBAAmB,CAAC,CAAC;QACvB,CAAC,CAAC;MACJ;MACAA,mBAAmB,CAAC,CAAC;IACvB,CAAC;IACDiB,IAAI,EAAG,SAAPA,IAAIA,CAAGJ,QAAgB,EAAK;MAC1B,IAAMnC,MAAM,GAAGR,OAAO,CAACY,GAAG,CAAC+B,QAAQ,CAAC;MACpC,IAAInC,MAAM,EAAE;QACVR,OAAO,CAACgD,MAAM,CAACxC,MAAM,CAACoC,EAAE,CAAC;QACzB1C,aAAa,GAAG,EAAE;QAClB,KAAK,IAAM2C,EAAE,IAAIrC,MAAM,CAACc,MAAM,EAAE;UAC9BuB,EAAE,CAACI,cAAc,CAACzC,MAAM,CAACoC,EAAE,CAAC;QAC9B;MACF;IACF;EACF,CAAC;AACH;AAEA,IAAIM,SAAS,GAAG,IAAI;AAEb,SAAS3D,WAAWA,CACzBsC,OAAmB,EAGX;EAAA,IAFRP,MAAuB,GAAA6B,SAAA,CAAAxB,MAAA,QAAAwB,SAAA,QAAAtC,SAAA,GAAAsC,SAAA,MAAG,EAAE;EAAA,IAC5B1C,OAAsB,GAAA0C,SAAA,CAAAxB,MAAA,QAAAwB,SAAA,QAAAtC,SAAA,GAAAsC,SAAA,MAAG,EAAE;EAE3B,IAAMR,QAAQ,GAAIO,SAAS,IAAI,CAAE;EAEjC,IAAAE,gBAAO,EAAC,YAAM;IACZ,IAAIC,cAAc,GAAGC,MAAM,CAACC,gBAAgB;IAC5C,IAAIF,cAAc,KAAKxC,SAAS,EAAE;MAChCwC,cAAc,GAAGC,MAAM,CAACC,gBAAgB,GAAGxD,oBAAoB,CAAC,CAAC;IACnE;IACAsD,cAAc,CAACX,KAAK,CAACC,QAAQ,EAAEd,OAAO,EAAEP,MAAM,EAAEb,OAAO,CAAC;EAC1D,CAAC,CAAC,CAAC,CAAC;EAEJ,OAAOkC,QAAQ;AACjB;AAEO,SAASnD,UAAUA,CAACmD,QAAgB,EAAQ;EACjD,IAAAS,gBAAO,EAAC,YAAM;IACZ,IAAMC,cAAc,GAAGC,MAAM,CAACC,gBAAgB;IAC9CF,cAAc,YAAdA,cAAc,CAAEN,IAAI,CAACJ,QAAQ,CAAC;EAChC,CAAC,CAAC,CAAC,CAAC;AACN", "ignoreList": []}