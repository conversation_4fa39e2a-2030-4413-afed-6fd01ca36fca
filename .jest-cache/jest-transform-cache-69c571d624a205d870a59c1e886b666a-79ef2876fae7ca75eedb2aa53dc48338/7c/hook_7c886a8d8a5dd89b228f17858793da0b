87bc5e19f2bff3b7aeee0be2e7d857ef
"use strict";

/* istanbul ignore next */
function cov_1qtmglwphc() {
  var path = "/Users/<USER>/projects/react-native/payment-module/src/presentation/payment-home/components/hook.tsx";
  var hash = "c0fbe963f199cbb76ac601de869dce672b3b8894";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/projects/react-native/payment-module/src/presentation/payment-home/components/hook.tsx",
    statementMap: {
      "0": {
        start: {
          line: 3,
          column: 29
        },
        end: {
          line: 3,
          column: 84
        }
      },
      "1": {
        start: {
          line: 4,
          column: 22
        },
        end: {
          line: 4,
          column: 93
        }
      },
      "2": {
        start: {
          line: 5,
          column: 22
        },
        end: {
          line: 20,
          column: 2
        }
      },
      "3": {
        start: {
          line: 6,
          column: 2
        },
        end: {
          line: 6,
          column: 31
        }
      },
      "4": {
        start: {
          line: 6,
          column: 24
        },
        end: {
          line: 6,
          column: 31
        }
      },
      "5": {
        start: {
          line: 7,
          column: 13
        },
        end: {
          line: 7,
          column: 50
        }
      },
      "6": {
        start: {
          line: 8,
          column: 2
        },
        end: {
          line: 15,
          column: 3
        }
      },
      "7": {
        start: {
          line: 9,
          column: 4
        },
        end: {
          line: 14,
          column: 6
        }
      },
      "8": {
        start: {
          line: 12,
          column: 8
        },
        end: {
          line: 12,
          column: 20
        }
      },
      "9": {
        start: {
          line: 16,
          column: 2
        },
        end: {
          line: 16,
          column: 37
        }
      },
      "10": {
        start: {
          line: 18,
          column: 2
        },
        end: {
          line: 18,
          column: 31
        }
      },
      "11": {
        start: {
          line: 18,
          column: 24
        },
        end: {
          line: 18,
          column: 31
        }
      },
      "12": {
        start: {
          line: 19,
          column: 2
        },
        end: {
          line: 19,
          column: 15
        }
      },
      "13": {
        start: {
          line: 21,
          column: 25
        },
        end: {
          line: 28,
          column: 2
        }
      },
      "14": {
        start: {
          line: 22,
          column: 2
        },
        end: {
          line: 25,
          column: 5
        }
      },
      "15": {
        start: {
          line: 27,
          column: 2
        },
        end: {
          line: 27,
          column: 19
        }
      },
      "16": {
        start: {
          line: 29,
          column: 19
        },
        end: {
          line: 45,
          column: 3
        }
      },
      "17": {
        start: {
          line: 30,
          column: 17
        },
        end: {
          line: 37,
          column: 3
        }
      },
      "18": {
        start: {
          line: 31,
          column: 4
        },
        end: {
          line: 35,
          column: 6
        }
      },
      "19": {
        start: {
          line: 32,
          column: 15
        },
        end: {
          line: 32,
          column: 17
        }
      },
      "20": {
        start: {
          line: 33,
          column: 6
        },
        end: {
          line: 33,
          column: 89
        }
      },
      "21": {
        start: {
          line: 33,
          column: 23
        },
        end: {
          line: 33,
          column: 89
        }
      },
      "22": {
        start: {
          line: 33,
          column: 71
        },
        end: {
          line: 33,
          column: 89
        }
      },
      "23": {
        start: {
          line: 34,
          column: 6
        },
        end: {
          line: 34,
          column: 16
        }
      },
      "24": {
        start: {
          line: 36,
          column: 4
        },
        end: {
          line: 36,
          column: 23
        }
      },
      "25": {
        start: {
          line: 38,
          column: 2
        },
        end: {
          line: 44,
          column: 4
        }
      },
      "26": {
        start: {
          line: 39,
          column: 4
        },
        end: {
          line: 39,
          column: 42
        }
      },
      "27": {
        start: {
          line: 39,
          column: 31
        },
        end: {
          line: 39,
          column: 42
        }
      },
      "28": {
        start: {
          line: 40,
          column: 17
        },
        end: {
          line: 40,
          column: 19
        }
      },
      "29": {
        start: {
          line: 41,
          column: 4
        },
        end: {
          line: 41,
          column: 134
        }
      },
      "30": {
        start: {
          line: 41,
          column: 21
        },
        end: {
          line: 41,
          column: 134
        }
      },
      "31": {
        start: {
          line: 41,
          column: 34
        },
        end: {
          line: 41,
          column: 47
        }
      },
      "32": {
        start: {
          line: 41,
          column: 53
        },
        end: {
          line: 41,
          column: 54
        }
      },
      "33": {
        start: {
          line: 41,
          column: 75
        },
        end: {
          line: 41,
          column: 134
        }
      },
      "34": {
        start: {
          line: 41,
          column: 99
        },
        end: {
          line: 41,
          column: 134
        }
      },
      "35": {
        start: {
          line: 42,
          column: 4
        },
        end: {
          line: 42,
          column: 36
        }
      },
      "36": {
        start: {
          line: 43,
          column: 4
        },
        end: {
          line: 43,
          column: 18
        }
      },
      "37": {
        start: {
          line: 46,
          column: 22
        },
        end: {
          line: 50,
          column: 1
        }
      },
      "38": {
        start: {
          line: 47,
          column: 2
        },
        end: {
          line: 49,
          column: 4
        }
      },
      "39": {
        start: {
          line: 51,
          column: 0
        },
        end: {
          line: 53,
          column: 3
        }
      },
      "40": {
        start: {
          line: 54,
          column: 0
        },
        end: {
          line: 54,
          column: 96
        }
      },
      "41": {
        start: {
          line: 55,
          column: 14
        },
        end: {
          line: 55,
          column: 44
        }
      },
      "42": {
        start: {
          line: 56,
          column: 14
        },
        end: {
          line: 56,
          column: 30
        }
      },
      "43": {
        start: {
          line: 57,
          column: 20
        },
        end: {
          line: 57,
          column: 76
        }
      },
      "44": {
        start: {
          line: 58,
          column: 0
        },
        end: {
          line: 58,
          column: 62
        }
      },
      "45": {
        start: {
          line: 59,
          column: 26
        },
        end: {
          line: 77,
          column: 1
        }
      },
      "46": {
        start: {
          line: 60,
          column: 17
        },
        end: {
          line: 60,
          column: 30
        }
      },
      "47": {
        start: {
          line: 61,
          column: 17
        },
        end: {
          line: 61,
          column: 32
        }
      },
      "48": {
        start: {
          line: 62,
          column: 25
        },
        end: {
          line: 62,
          column: 99
        }
      },
      "49": {
        start: {
          line: 63,
          column: 14
        },
        end: {
          line: 63,
          column: 42
        }
      },
      "50": {
        start: {
          line: 64,
          column: 12
        },
        end: {
          line: 64,
          column: 50
        }
      },
      "51": {
        start: {
          line: 65,
          column: 20
        },
        end: {
          line: 65,
          column: 28
        }
      },
      "52": {
        start: {
          line: 66,
          column: 23
        },
        end: {
          line: 66,
          column: 31
        }
      },
      "53": {
        start: {
          line: 67,
          column: 2
        },
        end: {
          line: 69,
          column: 25
        }
      },
      "54": {
        start: {
          line: 68,
          column: 4
        },
        end: {
          line: 68,
          column: 83
        }
      },
      "55": {
        start: {
          line: 70,
          column: 2
        },
        end: {
          line: 76,
          column: 15
        }
      },
      "56": {
        start: {
          line: 78,
          column: 0
        },
        end: {
          line: 78,
          column: 50
        }
      },
      "57": {
        start: {
          line: 79,
          column: 0
        },
        end: {
          line: 79,
          column: 62
        }
      },
      "58": {
        start: {
          line: 80,
          column: 26
        },
        end: {
          line: 83,
          column: 1
        }
      },
      "59": {
        start: {
          line: 81,
          column: 14
        },
        end: {
          line: 81,
          column: 65
        }
      },
      "60": {
        start: {
          line: 82,
          column: 2
        },
        end: {
          line: 82,
          column: 15
        }
      },
      "61": {
        start: {
          line: 84,
          column: 0
        },
        end: {
          line: 84,
          column: 50
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 5,
            column: 71
          },
          end: {
            line: 5,
            column: 72
          }
        },
        loc: {
          start: {
            line: 5,
            column: 94
          },
          end: {
            line: 17,
            column: 1
          }
        },
        line: 5
      },
      "1": {
        name: "get",
        decl: {
          start: {
            line: 11,
            column: 20
          },
          end: {
            line: 11,
            column: 23
          }
        },
        loc: {
          start: {
            line: 11,
            column: 26
          },
          end: {
            line: 13,
            column: 7
          }
        },
        line: 11
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 17,
            column: 4
          },
          end: {
            line: 17,
            column: 5
          }
        },
        loc: {
          start: {
            line: 17,
            column: 27
          },
          end: {
            line: 20,
            column: 1
          }
        },
        line: 17
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 21,
            column: 77
          },
          end: {
            line: 21,
            column: 78
          }
        },
        loc: {
          start: {
            line: 21,
            column: 93
          },
          end: {
            line: 26,
            column: 1
          }
        },
        line: 21
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 26,
            column: 4
          },
          end: {
            line: 26,
            column: 5
          }
        },
        loc: {
          start: {
            line: 26,
            column: 20
          },
          end: {
            line: 28,
            column: 1
          }
        },
        line: 26
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 29,
            column: 48
          },
          end: {
            line: 29,
            column: 49
          }
        },
        loc: {
          start: {
            line: 29,
            column: 60
          },
          end: {
            line: 45,
            column: 1
          }
        },
        line: 29
      },
      "6": {
        name: "ownKeys",
        decl: {
          start: {
            line: 30,
            column: 26
          },
          end: {
            line: 30,
            column: 33
          }
        },
        loc: {
          start: {
            line: 30,
            column: 37
          },
          end: {
            line: 37,
            column: 3
          }
        },
        line: 30
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 31,
            column: 45
          },
          end: {
            line: 31,
            column: 46
          }
        },
        loc: {
          start: {
            line: 31,
            column: 58
          },
          end: {
            line: 35,
            column: 5
          }
        },
        line: 31
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 38,
            column: 9
          },
          end: {
            line: 38,
            column: 10
          }
        },
        loc: {
          start: {
            line: 38,
            column: 24
          },
          end: {
            line: 44,
            column: 3
          }
        },
        line: 38
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 46,
            column: 54
          },
          end: {
            line: 46,
            column: 55
          }
        },
        loc: {
          start: {
            line: 46,
            column: 69
          },
          end: {
            line: 50,
            column: 1
          }
        },
        line: 46
      },
      "10": {
        name: "BeneficiaryProvider",
        decl: {
          start: {
            line: 59,
            column: 35
          },
          end: {
            line: 59,
            column: 54
          }
        },
        loc: {
          start: {
            line: 59,
            column: 61
          },
          end: {
            line: 77,
            column: 1
          }
        },
        line: 59
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 67,
            column: 25
          },
          end: {
            line: 67,
            column: 26
          }
        },
        loc: {
          start: {
            line: 67,
            column: 37
          },
          end: {
            line: 69,
            column: 3
          }
        },
        line: 67
      },
      "12": {
        name: "useBeneficiaryStore",
        decl: {
          start: {
            line: 80,
            column: 35
          },
          end: {
            line: 80,
            column: 54
          }
        },
        loc: {
          start: {
            line: 80,
            column: 57
          },
          end: {
            line: 83,
            column: 1
          }
        },
        line: 80
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 5,
            column: 22
          },
          end: {
            line: 20,
            column: 2
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 5,
            column: 22
          },
          end: {
            line: 5,
            column: 26
          }
        }, {
          start: {
            line: 5,
            column: 30
          },
          end: {
            line: 5,
            column: 50
          }
        }, {
          start: {
            line: 5,
            column: 55
          },
          end: {
            line: 20,
            column: 1
          }
        }],
        line: 5
      },
      "1": {
        loc: {
          start: {
            line: 5,
            column: 55
          },
          end: {
            line: 20,
            column: 1
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 5,
            column: 71
          },
          end: {
            line: 17,
            column: 1
          }
        }, {
          start: {
            line: 17,
            column: 4
          },
          end: {
            line: 20,
            column: 1
          }
        }],
        line: 5
      },
      "2": {
        loc: {
          start: {
            line: 6,
            column: 2
          },
          end: {
            line: 6,
            column: 31
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 6,
            column: 2
          },
          end: {
            line: 6,
            column: 31
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 6
      },
      "3": {
        loc: {
          start: {
            line: 8,
            column: 2
          },
          end: {
            line: 15,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 8,
            column: 2
          },
          end: {
            line: 15,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 8
      },
      "4": {
        loc: {
          start: {
            line: 8,
            column: 6
          },
          end: {
            line: 8,
            column: 83
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 8,
            column: 6
          },
          end: {
            line: 8,
            column: 11
          }
        }, {
          start: {
            line: 8,
            column: 16
          },
          end: {
            line: 8,
            column: 82
          }
        }],
        line: 8
      },
      "5": {
        loc: {
          start: {
            line: 8,
            column: 16
          },
          end: {
            line: 8,
            column: 82
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 8,
            column: 32
          },
          end: {
            line: 8,
            column: 45
          }
        }, {
          start: {
            line: 8,
            column: 48
          },
          end: {
            line: 8,
            column: 82
          }
        }],
        line: 8
      },
      "6": {
        loc: {
          start: {
            line: 8,
            column: 48
          },
          end: {
            line: 8,
            column: 82
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 8,
            column: 48
          },
          end: {
            line: 8,
            column: 61
          }
        }, {
          start: {
            line: 8,
            column: 65
          },
          end: {
            line: 8,
            column: 82
          }
        }],
        line: 8
      },
      "7": {
        loc: {
          start: {
            line: 18,
            column: 2
          },
          end: {
            line: 18,
            column: 31
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 18,
            column: 2
          },
          end: {
            line: 18,
            column: 31
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 18
      },
      "8": {
        loc: {
          start: {
            line: 21,
            column: 25
          },
          end: {
            line: 28,
            column: 2
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 21,
            column: 25
          },
          end: {
            line: 21,
            column: 29
          }
        }, {
          start: {
            line: 21,
            column: 33
          },
          end: {
            line: 21,
            column: 56
          }
        }, {
          start: {
            line: 21,
            column: 61
          },
          end: {
            line: 28,
            column: 1
          }
        }],
        line: 21
      },
      "9": {
        loc: {
          start: {
            line: 21,
            column: 61
          },
          end: {
            line: 28,
            column: 1
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 21,
            column: 77
          },
          end: {
            line: 26,
            column: 1
          }
        }, {
          start: {
            line: 26,
            column: 4
          },
          end: {
            line: 28,
            column: 1
          }
        }],
        line: 21
      },
      "10": {
        loc: {
          start: {
            line: 29,
            column: 19
          },
          end: {
            line: 45,
            column: 3
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 29,
            column: 19
          },
          end: {
            line: 29,
            column: 23
          }
        }, {
          start: {
            line: 29,
            column: 27
          },
          end: {
            line: 29,
            column: 44
          }
        }, {
          start: {
            line: 29,
            column: 48
          },
          end: {
            line: 45,
            column: 3
          }
        }],
        line: 29
      },
      "11": {
        loc: {
          start: {
            line: 31,
            column: 15
          },
          end: {
            line: 35,
            column: 5
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 31,
            column: 15
          },
          end: {
            line: 31,
            column: 41
          }
        }, {
          start: {
            line: 31,
            column: 45
          },
          end: {
            line: 35,
            column: 5
          }
        }],
        line: 31
      },
      "12": {
        loc: {
          start: {
            line: 33,
            column: 23
          },
          end: {
            line: 33,
            column: 89
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 33,
            column: 23
          },
          end: {
            line: 33,
            column: 89
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 33
      },
      "13": {
        loc: {
          start: {
            line: 39,
            column: 4
          },
          end: {
            line: 39,
            column: 42
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 39,
            column: 4
          },
          end: {
            line: 39,
            column: 42
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 39
      },
      "14": {
        loc: {
          start: {
            line: 39,
            column: 8
          },
          end: {
            line: 39,
            column: 29
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 39,
            column: 8
          },
          end: {
            line: 39,
            column: 11
          }
        }, {
          start: {
            line: 39,
            column: 15
          },
          end: {
            line: 39,
            column: 29
          }
        }],
        line: 39
      },
      "15": {
        loc: {
          start: {
            line: 41,
            column: 4
          },
          end: {
            line: 41,
            column: 134
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 41,
            column: 4
          },
          end: {
            line: 41,
            column: 134
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 41
      },
      "16": {
        loc: {
          start: {
            line: 41,
            column: 75
          },
          end: {
            line: 41,
            column: 134
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 41,
            column: 75
          },
          end: {
            line: 41,
            column: 134
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 41
      },
      "17": {
        loc: {
          start: {
            line: 46,
            column: 22
          },
          end: {
            line: 50,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 46,
            column: 22
          },
          end: {
            line: 46,
            column: 26
          }
        }, {
          start: {
            line: 46,
            column: 30
          },
          end: {
            line: 46,
            column: 50
          }
        }, {
          start: {
            line: 46,
            column: 54
          },
          end: {
            line: 50,
            column: 1
          }
        }],
        line: 46
      },
      "18": {
        loc: {
          start: {
            line: 47,
            column: 9
          },
          end: {
            line: 49,
            column: 3
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 47,
            column: 33
          },
          end: {
            line: 47,
            column: 36
          }
        }, {
          start: {
            line: 47,
            column: 39
          },
          end: {
            line: 49,
            column: 3
          }
        }],
        line: 47
      },
      "19": {
        loc: {
          start: {
            line: 47,
            column: 9
          },
          end: {
            line: 47,
            column: 30
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 47,
            column: 9
          },
          end: {
            line: 47,
            column: 12
          }
        }, {
          start: {
            line: 47,
            column: 16
          },
          end: {
            line: 47,
            column: 30
          }
        }],
        line: 47
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0
    },
    b: {
      "0": [0, 0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0, 0],
      "9": [0, 0],
      "10": [0, 0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0, 0],
      "18": [0, 0],
      "19": [0, 0]
    },
    inputSourceMap: {
      version: 3,
      file: undefined,
      names: ["react_1", "__importStar", "require", "react_2", "ScreenNames_1", "__importDefault", "exports", "BeneficiaryContext", "createContext", "BeneficiaryProvider", "_ref", "children", "navigation", "currentRouteName", "getState", "routes", "length", "name", "_ref2", "useState", "_ref3", "_slicedToArray2", "default", "isBeneficiary", "setIsBeneficiary", "useEffect", "PaymentBillScreen", "createElement", "Provider", "value", "displayName", "useBeneficiaryStore", "store", "useContext"],
      sourceRoot: undefined,
      sources: ["/Users/<USER>/projects/react-native/payment-module/src/presentation/payment-home/components/hook.tsx"],
      sourcesContent: ["import React, {useEffect, useState} from 'react';\nimport {createContext, PropsWithChildren, useContext} from 'react';\n\nimport {SafeAny} from '../../../commons/Constants';\nimport ScreenNames from '../../../commons/ScreenNames';\n\ntype BeneficiaryContextType = {\n  navigation: SafeAny;\n  isBeneficiary: boolean;\n  setIsBeneficiary: (isBeneficiary: boolean) => void;\n};\nexport const BeneficiaryContext = createContext<BeneficiaryContextType | null>(null);\n\nexport const BeneficiaryProvider = ({children, navigation}: PropsWithChildren & {navigation: any}) => {\n  const currentRouteName = navigation.getState().routes[navigation.getState().routes.length - 1].name;\n\n  const [isBeneficiary, setIsBeneficiary] = useState(false);\n\n  useEffect(() => {\n    setIsBeneficiary(currentRouteName === ScreenNames.PaymentBillScreen);\n  }, [currentRouteName]);\n\n  return (\n    <BeneficiaryContext.Provider value={{navigation, isBeneficiary, setIsBeneficiary}}>\n      {children}\n    </BeneficiaryContext.Provider>\n  );\n};\nBeneficiaryContext.displayName = 'BeneficiaryContext';\n\nexport const useBeneficiaryStore = () => {\n  const store = useContext(BeneficiaryContext);\n  return store;\n};\n"],
      mappings: ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAAA,OAAA,GAAAC,YAAA,CAAAC,OAAA;AACA,IAAAC,OAAA,GAAAD,OAAA;AAGA,IAAAE,aAAA,GAAAC,eAAA,CAAAH,OAAA;AAOaI,OAAA,CAAAC,kBAAkB,GAAG,IAAAJ,OAAA,CAAAK,aAAa,EAAgC,IAAI,CAAC;AAE7E,IAAMC,mBAAmB,GAAG,SAAtBA,mBAAmBA,CAAAC,IAAA,EAAqE;EAAA,IAAhEC,QAAQ,GAAAD,IAAA,CAARC,QAAQ;IAAEC,UAAU,GAAAF,IAAA,CAAVE,UAAU;EACvD,IAAMC,gBAAgB,GAAGD,UAAU,CAACE,QAAQ,EAAE,CAACC,MAAM,CAACH,UAAU,CAACE,QAAQ,EAAE,CAACC,MAAM,CAACC,MAAM,GAAG,CAAC,CAAC,CAACC,IAAI;EAEnG,IAAAC,KAAA,GAA0C,IAAAlB,OAAA,CAAAmB,QAAQ,EAAC,KAAK,CAAC;IAAAC,KAAA,OAAAC,eAAA,CAAAC,OAAA,EAAAJ,KAAA;IAAlDK,aAAa,GAAAH,KAAA;IAAEI,gBAAgB,GAAAJ,KAAA;EAEtC,IAAApB,OAAA,CAAAyB,SAAS,EAAC,YAAK;IACbD,gBAAgB,CAACX,gBAAgB,KAAKT,aAAA,CAAAkB,OAAW,CAACI,iBAAiB,CAAC;EACtE,CAAC,EAAE,CAACb,gBAAgB,CAAC,CAAC;EAEtB,OACEb,OAAA,CAAAsB,OAAA,CAAAK,aAAA,CAACrB,OAAA,CAAAC,kBAAkB,CAACqB,QAAQ;IAACC,KAAK,EAAE;MAACjB,UAAU,EAAVA,UAAU;MAAEW,aAAa,EAAbA,aAAa;MAAEC,gBAAgB,EAAhBA;IAAgB;EAAC,GAC9Eb,QAAQ,CACmB;AAElC,CAAC;AAdYL,OAAA,CAAAG,mBAAmB,GAAAA,mBAAA;AAehCH,OAAA,CAAAC,kBAAkB,CAACuB,WAAW,GAAG,oBAAoB;AAE9C,IAAMC,mBAAmB,GAAG,SAAtBA,mBAAmBA,CAAA,EAAQ;EACtC,IAAMC,KAAK,GAAG,IAAA7B,OAAA,CAAA8B,UAAU,EAAC3B,OAAA,CAAAC,kBAAkB,CAAC;EAC5C,OAAOyB,KAAK;AACd,CAAC;AAHY1B,OAAA,CAAAyB,mBAAmB,GAAAA,mBAAA",
      ignoreList: []
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "c0fbe963f199cbb76ac601de869dce672b3b8894"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_1qtmglwphc = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_1qtmglwphc();
var _interopRequireDefault =
/* istanbul ignore next */
(cov_1qtmglwphc().s[0]++, require("@babel/runtime/helpers/interopRequireDefault"));
var _slicedToArray2 =
/* istanbul ignore next */
(cov_1qtmglwphc().s[1]++, _interopRequireDefault(require("@babel/runtime/helpers/slicedToArray")));
var __createBinding =
/* istanbul ignore next */
(cov_1qtmglwphc().s[2]++,
/* istanbul ignore next */
(cov_1qtmglwphc().b[0][0]++, this) &&
/* istanbul ignore next */
(cov_1qtmglwphc().b[0][1]++, this.__createBinding) ||
/* istanbul ignore next */
(cov_1qtmglwphc().b[0][2]++, Object.create ?
/* istanbul ignore next */
(cov_1qtmglwphc().b[1][0]++, function (o, m, k, k2) {
  /* istanbul ignore next */
  cov_1qtmglwphc().f[0]++;
  cov_1qtmglwphc().s[3]++;
  if (k2 === undefined) {
    /* istanbul ignore next */
    cov_1qtmglwphc().b[2][0]++;
    cov_1qtmglwphc().s[4]++;
    k2 = k;
  } else
  /* istanbul ignore next */
  {
    cov_1qtmglwphc().b[2][1]++;
  }
  var desc =
  /* istanbul ignore next */
  (cov_1qtmglwphc().s[5]++, Object.getOwnPropertyDescriptor(m, k));
  /* istanbul ignore next */
  cov_1qtmglwphc().s[6]++;
  if (
  /* istanbul ignore next */
  (cov_1qtmglwphc().b[4][0]++, !desc) ||
  /* istanbul ignore next */
  (cov_1qtmglwphc().b[4][1]++, "get" in desc ?
  /* istanbul ignore next */
  (cov_1qtmglwphc().b[5][0]++, !m.__esModule) :
  /* istanbul ignore next */
  (cov_1qtmglwphc().b[5][1]++,
  /* istanbul ignore next */
  (cov_1qtmglwphc().b[6][0]++, desc.writable) ||
  /* istanbul ignore next */
  (cov_1qtmglwphc().b[6][1]++, desc.configurable)))) {
    /* istanbul ignore next */
    cov_1qtmglwphc().b[3][0]++;
    cov_1qtmglwphc().s[7]++;
    desc = {
      enumerable: true,
      get: function get() {
        /* istanbul ignore next */
        cov_1qtmglwphc().f[1]++;
        cov_1qtmglwphc().s[8]++;
        return m[k];
      }
    };
  } else
  /* istanbul ignore next */
  {
    cov_1qtmglwphc().b[3][1]++;
  }
  cov_1qtmglwphc().s[9]++;
  Object.defineProperty(o, k2, desc);
}) :
/* istanbul ignore next */
(cov_1qtmglwphc().b[1][1]++, function (o, m, k, k2) {
  /* istanbul ignore next */
  cov_1qtmglwphc().f[2]++;
  cov_1qtmglwphc().s[10]++;
  if (k2 === undefined) {
    /* istanbul ignore next */
    cov_1qtmglwphc().b[7][0]++;
    cov_1qtmglwphc().s[11]++;
    k2 = k;
  } else
  /* istanbul ignore next */
  {
    cov_1qtmglwphc().b[7][1]++;
  }
  cov_1qtmglwphc().s[12]++;
  o[k2] = m[k];
})));
var __setModuleDefault =
/* istanbul ignore next */
(cov_1qtmglwphc().s[13]++,
/* istanbul ignore next */
(cov_1qtmglwphc().b[8][0]++, this) &&
/* istanbul ignore next */
(cov_1qtmglwphc().b[8][1]++, this.__setModuleDefault) ||
/* istanbul ignore next */
(cov_1qtmglwphc().b[8][2]++, Object.create ?
/* istanbul ignore next */
(cov_1qtmglwphc().b[9][0]++, function (o, v) {
  /* istanbul ignore next */
  cov_1qtmglwphc().f[3]++;
  cov_1qtmglwphc().s[14]++;
  Object.defineProperty(o, "default", {
    enumerable: true,
    value: v
  });
}) :
/* istanbul ignore next */
(cov_1qtmglwphc().b[9][1]++, function (o, v) {
  /* istanbul ignore next */
  cov_1qtmglwphc().f[4]++;
  cov_1qtmglwphc().s[15]++;
  o["default"] = v;
})));
var __importStar =
/* istanbul ignore next */
(cov_1qtmglwphc().s[16]++,
/* istanbul ignore next */
(cov_1qtmglwphc().b[10][0]++, this) &&
/* istanbul ignore next */
(cov_1qtmglwphc().b[10][1]++, this.__importStar) ||
/* istanbul ignore next */
(cov_1qtmglwphc().b[10][2]++, function () {
  /* istanbul ignore next */
  cov_1qtmglwphc().f[5]++;
  cov_1qtmglwphc().s[17]++;
  var _ownKeys = function ownKeys(o) {
    /* istanbul ignore next */
    cov_1qtmglwphc().f[6]++;
    cov_1qtmglwphc().s[18]++;
    _ownKeys =
    /* istanbul ignore next */
    (cov_1qtmglwphc().b[11][0]++, Object.getOwnPropertyNames) ||
    /* istanbul ignore next */
    (cov_1qtmglwphc().b[11][1]++, function (o) {
      /* istanbul ignore next */
      cov_1qtmglwphc().f[7]++;
      var ar =
      /* istanbul ignore next */
      (cov_1qtmglwphc().s[19]++, []);
      /* istanbul ignore next */
      cov_1qtmglwphc().s[20]++;
      for (var k in o) {
        /* istanbul ignore next */
        cov_1qtmglwphc().s[21]++;
        if (Object.prototype.hasOwnProperty.call(o, k)) {
          /* istanbul ignore next */
          cov_1qtmglwphc().b[12][0]++;
          cov_1qtmglwphc().s[22]++;
          ar[ar.length] = k;
        } else
        /* istanbul ignore next */
        {
          cov_1qtmglwphc().b[12][1]++;
        }
      }
      /* istanbul ignore next */
      cov_1qtmglwphc().s[23]++;
      return ar;
    });
    /* istanbul ignore next */
    cov_1qtmglwphc().s[24]++;
    return _ownKeys(o);
  };
  /* istanbul ignore next */
  cov_1qtmglwphc().s[25]++;
  return function (mod) {
    /* istanbul ignore next */
    cov_1qtmglwphc().f[8]++;
    cov_1qtmglwphc().s[26]++;
    if (
    /* istanbul ignore next */
    (cov_1qtmglwphc().b[14][0]++, mod) &&
    /* istanbul ignore next */
    (cov_1qtmglwphc().b[14][1]++, mod.__esModule)) {
      /* istanbul ignore next */
      cov_1qtmglwphc().b[13][0]++;
      cov_1qtmglwphc().s[27]++;
      return mod;
    } else
    /* istanbul ignore next */
    {
      cov_1qtmglwphc().b[13][1]++;
    }
    var result =
    /* istanbul ignore next */
    (cov_1qtmglwphc().s[28]++, {});
    /* istanbul ignore next */
    cov_1qtmglwphc().s[29]++;
    if (mod != null) {
      /* istanbul ignore next */
      cov_1qtmglwphc().b[15][0]++;
      cov_1qtmglwphc().s[30]++;
      for (var k =
        /* istanbul ignore next */
        (cov_1qtmglwphc().s[31]++, _ownKeys(mod)), i =
        /* istanbul ignore next */
        (cov_1qtmglwphc().s[32]++, 0); i < k.length; i++) {
        /* istanbul ignore next */
        cov_1qtmglwphc().s[33]++;
        if (k[i] !== "default") {
          /* istanbul ignore next */
          cov_1qtmglwphc().b[16][0]++;
          cov_1qtmglwphc().s[34]++;
          __createBinding(result, mod, k[i]);
        } else
        /* istanbul ignore next */
        {
          cov_1qtmglwphc().b[16][1]++;
        }
      }
    } else
    /* istanbul ignore next */
    {
      cov_1qtmglwphc().b[15][1]++;
    }
    cov_1qtmglwphc().s[35]++;
    __setModuleDefault(result, mod);
    /* istanbul ignore next */
    cov_1qtmglwphc().s[36]++;
    return result;
  };
}()));
var __importDefault =
/* istanbul ignore next */
(cov_1qtmglwphc().s[37]++,
/* istanbul ignore next */
(cov_1qtmglwphc().b[17][0]++, this) &&
/* istanbul ignore next */
(cov_1qtmglwphc().b[17][1]++, this.__importDefault) ||
/* istanbul ignore next */
(cov_1qtmglwphc().b[17][2]++, function (mod) {
  /* istanbul ignore next */
  cov_1qtmglwphc().f[9]++;
  cov_1qtmglwphc().s[38]++;
  return /* istanbul ignore next */(cov_1qtmglwphc().b[19][0]++, mod) &&
  /* istanbul ignore next */
  (cov_1qtmglwphc().b[19][1]++, mod.__esModule) ?
  /* istanbul ignore next */
  (cov_1qtmglwphc().b[18][0]++, mod) :
  /* istanbul ignore next */
  (cov_1qtmglwphc().b[18][1]++, {
    "default": mod
  });
}));
/* istanbul ignore next */
cov_1qtmglwphc().s[39]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_1qtmglwphc().s[40]++;
exports.useBeneficiaryStore = exports.BeneficiaryProvider = exports.BeneficiaryContext = void 0;
var react_1 =
/* istanbul ignore next */
(cov_1qtmglwphc().s[41]++, __importStar(require("react")));
var react_2 =
/* istanbul ignore next */
(cov_1qtmglwphc().s[42]++, require("react"));
var ScreenNames_1 =
/* istanbul ignore next */
(cov_1qtmglwphc().s[43]++, __importDefault(require("../../../commons/ScreenNames")));
/* istanbul ignore next */
cov_1qtmglwphc().s[44]++;
exports.BeneficiaryContext = (0, react_2.createContext)(null);
/* istanbul ignore next */
cov_1qtmglwphc().s[45]++;
var BeneficiaryProvider = function BeneficiaryProvider(_ref) {
  /* istanbul ignore next */
  cov_1qtmglwphc().f[10]++;
  var children =
    /* istanbul ignore next */
    (cov_1qtmglwphc().s[46]++, _ref.children),
    navigation =
    /* istanbul ignore next */
    (cov_1qtmglwphc().s[47]++, _ref.navigation);
  var currentRouteName =
  /* istanbul ignore next */
  (cov_1qtmglwphc().s[48]++, navigation.getState().routes[navigation.getState().routes.length - 1].name);
  var _ref2 =
    /* istanbul ignore next */
    (cov_1qtmglwphc().s[49]++, (0, react_1.useState)(false)),
    _ref3 =
    /* istanbul ignore next */
    (cov_1qtmglwphc().s[50]++, (0, _slicedToArray2.default)(_ref2, 2)),
    isBeneficiary =
    /* istanbul ignore next */
    (cov_1qtmglwphc().s[51]++, _ref3[0]),
    setIsBeneficiary =
    /* istanbul ignore next */
    (cov_1qtmglwphc().s[52]++, _ref3[1]);
  /* istanbul ignore next */
  cov_1qtmglwphc().s[53]++;
  (0, react_1.useEffect)(function () {
    /* istanbul ignore next */
    cov_1qtmglwphc().f[11]++;
    cov_1qtmglwphc().s[54]++;
    setIsBeneficiary(currentRouteName === ScreenNames_1.default.PaymentBillScreen);
  }, [currentRouteName]);
  /* istanbul ignore next */
  cov_1qtmglwphc().s[55]++;
  return react_1.default.createElement(exports.BeneficiaryContext.Provider, {
    value: {
      navigation: navigation,
      isBeneficiary: isBeneficiary,
      setIsBeneficiary: setIsBeneficiary
    }
  }, children);
};
/* istanbul ignore next */
cov_1qtmglwphc().s[56]++;
exports.BeneficiaryProvider = BeneficiaryProvider;
/* istanbul ignore next */
cov_1qtmglwphc().s[57]++;
exports.BeneficiaryContext.displayName = 'BeneficiaryContext';
/* istanbul ignore next */
cov_1qtmglwphc().s[58]++;
var useBeneficiaryStore = function useBeneficiaryStore() {
  /* istanbul ignore next */
  cov_1qtmglwphc().f[12]++;
  var store =
  /* istanbul ignore next */
  (cov_1qtmglwphc().s[59]++, (0, react_2.useContext)(exports.BeneficiaryContext));
  /* istanbul ignore next */
  cov_1qtmglwphc().s[60]++;
  return store;
};
/* istanbul ignore next */
cov_1qtmglwphc().s[61]++;
exports.useBeneficiaryStore = useBeneficiaryStore;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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