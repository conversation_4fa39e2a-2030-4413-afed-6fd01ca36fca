{"version": 3, "names": ["react_1", "cov_1qtmglwphc", "s", "__importStar", "require", "react_2", "ScreenNames_1", "__importDefault", "exports", "BeneficiaryContext", "createContext", "Beneficiary<PERSON><PERSON><PERSON>", "_ref", "f", "children", "navigation", "currentRouteName", "getState", "routes", "length", "name", "_ref2", "useState", "_ref3", "_slicedToArray2", "default", "isBeneficiary", "setIsBeneficiary", "useEffect", "PaymentBillScreen", "createElement", "Provider", "value", "displayName", "useBeneficiaryStore", "store", "useContext"], "sources": ["/Users/<USER>/projects/react-native/payment-module/src/presentation/payment-home/components/hook.tsx"], "sourcesContent": ["import React, {useEffect, useState} from 'react';\nimport {createContext, PropsWithChildren, useContext} from 'react';\n\nimport {SafeAny} from '../../../commons/Constants';\nimport ScreenNames from '../../../commons/ScreenNames';\n\ntype BeneficiaryContextType = {\n  navigation: SafeAny;\n  isBeneficiary: boolean;\n  setIsBeneficiary: (isBeneficiary: boolean) => void;\n};\nexport const BeneficiaryContext = createContext<BeneficiaryContextType | null>(null);\n\nexport const BeneficiaryProvider = ({children, navigation}: PropsWithChildren & {navigation: any}) => {\n  const currentRouteName = navigation.getState().routes[navigation.getState().routes.length - 1].name;\n\n  const [isBeneficiary, setIsBeneficiary] = useState(false);\n\n  useEffect(() => {\n    setIsBeneficiary(currentRouteName === ScreenNames.PaymentBillScreen);\n  }, [currentRouteName]);\n\n  return (\n    <BeneficiaryContext.Provider value={{navigation, isBeneficiary, setIsBeneficiary}}>\n      {children}\n    </BeneficiaryContext.Provider>\n  );\n};\nBeneficiaryContext.displayName = 'BeneficiaryContext';\n\nexport const useBeneficiaryStore = () => {\n  const store = useContext(BeneficiaryContext);\n  return store;\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAAA,OAAA;AAAA;AAAA,CAAAC,cAAA,GAAAC,CAAA,QAAAC,YAAA,CAAAC,OAAA;AACA,IAAAC,OAAA;AAAA;AAAA,CAAAJ,cAAA,GAAAC,CAAA,QAAAE,OAAA;AAGA,IAAAE,aAAA;AAAA;AAAA,CAAAL,cAAA,GAAAC,CAAA,QAAAK,eAAA,CAAAH,OAAA;AAAA;AAAAH,cAAA,GAAAC,CAAA;AAOaM,OAAA,CAAAC,kBAAkB,GAAG,IAAAJ,OAAA,CAAAK,aAAa,EAAgC,IAAI,CAAC;AAAA;AAAAT,cAAA,GAAAC,CAAA;AAE7E,IAAMS,mBAAmB,GAAG,SAAtBA,mBAAmBA,CAAAC,IAAA,EAAqE;EAAA;EAAAX,cAAA,GAAAY,CAAA;EAAA,IAAhEC,QAAQ;IAAA;IAAA,CAAAb,cAAA,GAAAC,CAAA,QAAAU,IAAA,CAARE,QAAQ;IAAEC,UAAU;IAAA;IAAA,CAAAd,cAAA,GAAAC,CAAA,QAAAU,IAAA,CAAVG,UAAU;EACvD,IAAMC,gBAAgB;EAAA;EAAA,CAAAf,cAAA,GAAAC,CAAA,QAAGa,UAAU,CAACE,QAAQ,EAAE,CAACC,MAAM,CAACH,UAAU,CAACE,QAAQ,EAAE,CAACC,MAAM,CAACC,MAAM,GAAG,CAAC,CAAC,CAACC,IAAI;EAEnG,IAAAC,KAAA;IAAA;IAAA,CAAApB,cAAA,GAAAC,CAAA,QAA0C,IAAAF,OAAA,CAAAsB,QAAQ,EAAC,KAAK,CAAC;IAAAC,KAAA;IAAA;IAAA,CAAAtB,cAAA,GAAAC,CAAA,YAAAsB,eAAA,CAAAC,OAAA,EAAAJ,KAAA;IAAlDK,aAAa;IAAA;IAAA,CAAAzB,cAAA,GAAAC,CAAA,QAAAqB,KAAA;IAAEI,gBAAgB;IAAA;IAAA,CAAA1B,cAAA,GAAAC,CAAA,QAAAqB,KAAA;EAAA;EAAAtB,cAAA,GAAAC,CAAA;EAEtC,IAAAF,OAAA,CAAA4B,SAAS,EAAC,YAAK;IAAA;IAAA3B,cAAA,GAAAY,CAAA;IAAAZ,cAAA,GAAAC,CAAA;IACbyB,gBAAgB,CAACX,gBAAgB,KAAKV,aAAA,CAAAmB,OAAW,CAACI,iBAAiB,CAAC;EACtE,CAAC,EAAE,CAACb,gBAAgB,CAAC,CAAC;EAAA;EAAAf,cAAA,GAAAC,CAAA;EAEtB,OACEF,OAAA,CAAAyB,OAAA,CAAAK,aAAA,CAACtB,OAAA,CAAAC,kBAAkB,CAACsB,QAAQ;IAACC,KAAK,EAAE;MAACjB,UAAU,EAAVA,UAAU;MAAEW,aAAa,EAAbA,aAAa;MAAEC,gBAAgB,EAAhBA;IAAgB;EAAC,GAC9Eb,QAAQ,CACmB;AAElC,CAAC;AAAA;AAAAb,cAAA,GAAAC,CAAA;AAdYM,OAAA,CAAAG,mBAAmB,GAAAA,mBAAA;AAAA;AAAAV,cAAA,GAAAC,CAAA;AAehCM,OAAA,CAAAC,kBAAkB,CAACwB,WAAW,GAAG,oBAAoB;AAAA;AAAAhC,cAAA,GAAAC,CAAA;AAE9C,IAAMgC,mBAAmB,GAAG,SAAtBA,mBAAmBA,CAAA,EAAQ;EAAA;EAAAjC,cAAA,GAAAY,CAAA;EACtC,IAAMsB,KAAK;EAAA;EAAA,CAAAlC,cAAA,GAAAC,CAAA,QAAG,IAAAG,OAAA,CAAA+B,UAAU,EAAC5B,OAAA,CAAAC,kBAAkB,CAAC;EAAA;EAAAR,cAAA,GAAAC,CAAA;EAC5C,OAAOiC,KAAK;AACd,CAAC;AAAA;AAAAlC,cAAA,GAAAC,CAAA;AAHYM,OAAA,CAAA0B,mBAAmB,GAAAA,mBAAA", "ignoreList": []}