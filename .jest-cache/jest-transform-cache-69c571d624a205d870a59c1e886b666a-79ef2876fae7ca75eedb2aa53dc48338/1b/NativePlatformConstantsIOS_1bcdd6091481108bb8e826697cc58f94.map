{"version": 3, "names": ["TurboModuleRegistry", "_interopRequireWildcard", "require", "_getRequireWildcardCache", "e", "WeakMap", "r", "t", "__esModule", "default", "has", "get", "n", "__proto__", "a", "Object", "defineProperty", "getOwnPropertyDescriptor", "u", "hasOwnProperty", "call", "i", "set", "_default", "exports", "getEnforcing"], "sources": ["NativePlatformConstantsIOS.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @flow strict\n * @format\n */\n\nimport type {TurboModule} from '../../../../Libraries/TurboModule/RCTExport';\n\nimport * as TurboModuleRegistry from '../../../../Libraries/TurboModule/TurboModuleRegistry';\n\nexport type PlatformConstantsIOS = {|\n  isTesting: boolean,\n  isDisableAnimations?: boolean,\n  reactNativeVersion: {|\n    major: number,\n    minor: number,\n    patch: number,\n    prerelease: ?string,\n  |},\n  forceTouchAvailable: boolean,\n  osVersion: string,\n  systemName: string,\n  interfaceIdiom: string,\n  isMacCatalyst?: boolean,\n|};\n\nexport interface Spec extends TurboModule {\n  +getConstants: () => PlatformConstantsIOS;\n}\n\nexport default (TurboModuleRegistry.getEnforcing<Spec>(\n  'PlatformConstants',\n): Spec);\n"], "mappings": ";;;;AAYA,IAAAA,mBAAA,GAAAC,uBAAA,CAAAC,OAAA;AAA6F,SAAAC,yBAAAC,CAAA,6BAAAC,OAAA,mBAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,yBAAAC,CAAA,WAAAA,CAAA,GAAAG,CAAA,GAAAD,CAAA,KAAAF,CAAA;AAAA,SAAAH,wBAAAG,CAAA,EAAAE,CAAA,SAAAA,CAAA,IAAAF,CAAA,IAAAA,CAAA,CAAAI,UAAA,SAAAJ,CAAA,eAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,WAAAK,OAAA,EAAAL,CAAA,QAAAG,CAAA,GAAAJ,wBAAA,CAAAG,CAAA,OAAAC,CAAA,IAAAA,CAAA,CAAAG,GAAA,CAAAN,CAAA,UAAAG,CAAA,CAAAI,GAAA,CAAAP,CAAA,OAAAQ,CAAA,KAAAC,SAAA,UAAAC,CAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,CAAA,IAAAd,CAAA,oBAAAc,CAAA,OAAAC,cAAA,CAAAC,IAAA,CAAAhB,CAAA,EAAAc,CAAA,SAAAG,CAAA,GAAAP,CAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAb,CAAA,EAAAc,CAAA,UAAAG,CAAA,KAAAA,CAAA,CAAAV,GAAA,IAAAU,CAAA,CAAAC,GAAA,IAAAP,MAAA,CAAAC,cAAA,CAAAJ,CAAA,EAAAM,CAAA,EAAAG,CAAA,IAAAT,CAAA,CAAAM,CAAA,IAAAd,CAAA,CAAAc,CAAA,YAAAN,CAAA,CAAAH,OAAA,GAAAL,CAAA,EAAAG,CAAA,IAAAA,CAAA,CAAAe,GAAA,CAAAlB,CAAA,EAAAQ,CAAA,GAAAA,CAAA;AAAA,IAAAW,QAAA,GAAAC,OAAA,CAAAf,OAAA,GAsB7ET,mBAAmB,CAACyB,YAAY,CAC9C,mBACF,CAAC", "ignoreList": []}