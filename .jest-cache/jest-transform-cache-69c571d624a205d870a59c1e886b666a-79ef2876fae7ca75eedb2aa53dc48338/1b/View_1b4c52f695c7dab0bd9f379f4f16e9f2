0729285cb1679bfac5256f6899210fdf
'use strict';

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.AnimatedView = void 0;
var _reactNative = require("react-native");
var _index = require("../createAnimatedComponent/index.js");
var AnimatedView = exports.AnimatedView = (0, _index.createAnimatedComponent)(_reactNative.View);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJPYmplY3QiLCJkZWZpbmVQcm9wZXJ0eSIsImV4cG9ydHMiLCJ2YWx1ZSIsIkFuaW1hdGVkVmlldyIsIl9yZWFjdE5hdGl2ZSIsInJlcXVpcmUiLCJfaW5kZXgiLCJjcmVhdGVBbmltYXRlZENvbXBvbmVudCIsIlZpZXciXSwic291cmNlcyI6WyIuLi8uLi8uLi9zcmMvY29tcG9uZW50L1ZpZXcudHMiXSwic291cmNlc0NvbnRlbnQiOltudWxsXSwibWFwcGluZ3MiOiJBQUFBLFlBQVk7O0FBQUFBLE1BQUEsQ0FBQUMsY0FBQSxDQUFBQyxPQUFBO0VBQUFDLEtBQUE7QUFBQTtBQUFBRCxPQUFBLENBQUFFLFlBQUE7QUFDWixJQUFBQyxZQUFBLEdBQUFDLE9BQUE7QUFDQSxJQUFBQyxNQUFBLEdBQUFELE9BQUE7QUFRTyxJQUFNRixZQUFZLEdBQUFGLE9BQUEsQ0FBQUUsWUFBQSxHQUFHLElBQUFJLDhCQUF1QixFQUFDQyxpQkFBSSxDQUFDIiwiaWdub3JlTGlzdCI6W119