17d683ab07110f601c6b36c20e5249be
"use strict";

/* istanbul ignore next */
function cov_ds79t4pzk() {
  var path = "/Users/<USER>/projects/react-native/payment-module/src/domain/states/get-my-bill-history-list/GetMyBillHistoryListState.ts";
  var hash = "aad7f1967d3cb388718fcee1b6ed0da74fba3faa";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/projects/react-native/payment-module/src/domain/states/get-my-bill-history-list/GetMyBillHistoryListState.ts",
    statementMap: {
      "0": {
        start: {
          line: 3,
          column: 0
        },
        end: {
          line: 5,
          column: 3
        }
      }
    },
    fnMap: {},
    branchMap: {},
    s: {
      "0": 0
    },
    f: {},
    b: {},
    inputSourceMap: {
      version: 3,
      file: undefined,
      names: [],
      sourceRoot: undefined,
      sources: ["/Users/<USER>/projects/react-native/payment-module/src/domain/states/get-my-bill-history-list/GetMyBillHistoryListState.ts"],
      sourcesContent: ["import {MSBError} from '../../../core/BaseResponse';\nimport {GetMyBillHistoryListModel} from '../../entities/get-my-bill-history-list/GetMyBillHistoryListModel';\n\nexport type GetMyBillHistoryListState =\n  | {status: 'INIT'}\n  | {status: 'LOADING'}\n  | {status: 'SUCCESS'; data: GetMyBillHistoryListModel | undefined | null}\n  | {status: 'ERROR'; error?: MSBError | undefined | null};\n"],
      mappings: "",
      ignoreList: []
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "aad7f1967d3cb388718fcee1b6ed0da74fba3faa"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_ds79t4pzk = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_ds79t4pzk();
cov_ds79t4pzk().s[0]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6W10sInNvdXJjZXMiOlsiL1VzZXJzL3NvbnNhbXNldC9wcm9qZWN0cy9yZWFjdC1uYXRpdmUvcGF5bWVudC1tb2R1bGUvc3JjL2RvbWFpbi9zdGF0ZXMvZ2V0LW15LWJpbGwtaGlzdG9yeS1saXN0L0dldE15QmlsbEhpc3RvcnlMaXN0U3RhdGUudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHtNU0JFcnJvcn0gZnJvbSAnLi4vLi4vLi4vY29yZS9CYXNlUmVzcG9uc2UnO1xuaW1wb3J0IHtHZXRNeUJpbGxIaXN0b3J5TGlzdE1vZGVsfSBmcm9tICcuLi8uLi9lbnRpdGllcy9nZXQtbXktYmlsbC1oaXN0b3J5LWxpc3QvR2V0TXlCaWxsSGlzdG9yeUxpc3RNb2RlbCc7XG5cbmV4cG9ydCB0eXBlIEdldE15QmlsbEhpc3RvcnlMaXN0U3RhdGUgPVxuICB8IHtzdGF0dXM6ICdJTklUJ31cbiAgfCB7c3RhdHVzOiAnTE9BRElORyd9XG4gIHwge3N0YXR1czogJ1NVQ0NFU1MnOyBkYXRhOiBHZXRNeUJpbGxIaXN0b3J5TGlzdE1vZGVsIHwgdW5kZWZpbmVkIHwgbnVsbH1cbiAgfCB7c3RhdHVzOiAnRVJST1InOyBlcnJvcj86IE1TQkVycm9yIHwgdW5kZWZpbmVkIHwgbnVsbH07XG4iXSwibWFwcGluZ3MiOiIiLCJpZ25vcmVMaXN0IjpbXX0=