775cc00c50629c1c16ff1602b7d1f843
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var TurboModuleRegistry = _interopRequireWildcard(require("../../../../Libraries/TurboModule/TurboModuleRegistry"));
function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
var _default = exports.default = TurboModuleRegistry.getEnforcing('PlatformConstants');
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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