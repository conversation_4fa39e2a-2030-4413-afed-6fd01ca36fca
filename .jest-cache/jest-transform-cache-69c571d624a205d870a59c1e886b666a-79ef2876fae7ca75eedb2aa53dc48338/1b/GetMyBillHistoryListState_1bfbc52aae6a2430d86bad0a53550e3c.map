{"version": 3, "names": [], "sources": ["/Users/<USER>/projects/react-native/payment-module/src/domain/states/get-my-bill-history-list/GetMyBillHistoryListState.ts"], "sourcesContent": ["import {MSBError} from '../../../core/BaseResponse';\nimport {GetMyBillHistoryListModel} from '../../entities/get-my-bill-history-list/GetMyBillHistoryListModel';\n\nexport type GetMyBillHistoryListState =\n  | {status: 'INIT'}\n  | {status: 'LOADING'}\n  | {status: 'SUCCESS'; data: GetMyBillHistoryListModel | undefined | null}\n  | {status: 'ERROR'; error?: MSBError | undefined | null};\n"], "mappings": "", "ignoreList": []}