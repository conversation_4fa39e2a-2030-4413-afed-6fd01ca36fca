afa325988e253346d1969dc4f6bdffaf
"use strict";

/* istanbul ignore next */
function cov_2as9ofencf() {
  var path = "/Users/<USER>/projects/react-native/payment-module/src/presentation/qr-payment-info/components/transfer-limits/types.ts";
  var hash = "21d0f7614ead4422325428ea2907fca29c8ae2ca";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/projects/react-native/payment-module/src/presentation/qr-payment-info/components/transfer-limits/types.ts",
    statementMap: {},
    fnMap: {},
    branchMap: {},
    s: {},
    f: {},
    b: {},
    inputSourceMap: {
      version: 3,
      file: undefined,
      names: [],
      sourceRoot: undefined,
      sources: ["/Users/<USER>/projects/react-native/payment-module/src/presentation/qr-payment-info/components/transfer-limits/types.ts"],
      sourcesContent: ["// import {ViewStyle} from 'react-native';\n// import {TransferLimitsModel} from '@domain/entities/get-transfer-limits/GetTransferLimitsModel';\n\n// export type TransferLimitProps = {\n//   style?: ViewStyle;\n//   limits: TransferLimitsModel;\n//   onChangeTransferLimits: () => void;\n// };\n"],
      mappings: "",
      ignoreList: []
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "21d0f7614ead4422325428ea2907fca29c8ae2ca"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_2as9ofencf = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_2as9ofencf();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6W10sInNvdXJjZXMiOlsiL1VzZXJzL3NvbnNhbXNldC9wcm9qZWN0cy9yZWFjdC1uYXRpdmUvcGF5bWVudC1tb2R1bGUvc3JjL3ByZXNlbnRhdGlvbi9xci1wYXltZW50LWluZm8vY29tcG9uZW50cy90cmFuc2Zlci1saW1pdHMvdHlwZXMudHMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gaW1wb3J0IHtWaWV3U3R5bGV9IGZyb20gJ3JlYWN0LW5hdGl2ZSc7XG4vLyBpbXBvcnQge1RyYW5zZmVyTGltaXRzTW9kZWx9IGZyb20gJ0Bkb21haW4vZW50aXRpZXMvZ2V0LXRyYW5zZmVyLWxpbWl0cy9HZXRUcmFuc2ZlckxpbWl0c01vZGVsJztcblxuLy8gZXhwb3J0IHR5cGUgVHJhbnNmZXJMaW1pdFByb3BzID0ge1xuLy8gICBzdHlsZT86IFZpZXdTdHlsZTtcbi8vICAgbGltaXRzOiBUcmFuc2ZlckxpbWl0c01vZGVsO1xuLy8gICBvbkNoYW5nZVRyYW5zZmVyTGltaXRzOiAoKSA9PiB2b2lkO1xuLy8gfTtcbiJdLCJtYXBwaW5ncyI6IiIsImlnbm9yZUxpc3QiOltdfQ==