{"version": 3, "names": [], "sources": ["/Users/<USER>/projects/react-native/payment-module/src/presentation/qr-payment-info/components/transfer-limits/types.ts"], "sourcesContent": ["// import {ViewStyle} from 'react-native';\n// import {TransferLimitsModel} from '@domain/entities/get-transfer-limits/GetTransferLimitsModel';\n\n// export type TransferLimitProps = {\n//   style?: ViewStyle;\n//   limits: TransferLimitsModel;\n//   onChangeTransferLimits: () => void;\n// };\n"], "mappings": "", "ignoreList": []}