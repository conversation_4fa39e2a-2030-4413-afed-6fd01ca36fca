bdf60c0bbc87f410d2460f8e918d2064
"use strict";

/* istanbul ignore next */
function cov_1p95obf1qz() {
  var path = "/Users/<USER>/projects/react-native/payment-module/src/components/recent-tab/types.ts";
  var hash = "9f8983cb975a2c9c75a15d9fe65bede8ef85b1e6";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/projects/react-native/payment-module/src/components/recent-tab/types.ts",
    statementMap: {
      "0": {
        start: {
          line: 3,
          column: 0
        },
        end: {
          line: 5,
          column: 3
        }
      }
    },
    fnMap: {},
    branchMap: {},
    s: {
      "0": 0
    },
    f: {},
    b: {},
    inputSourceMap: {
      version: 3,
      file: undefined,
      names: [],
      sourceRoot: undefined,
      sources: ["/Users/<USER>/projects/react-native/payment-module/src/components/recent-tab/types.ts"],
      sourcesContent: ["import {ViewStyle} from 'react-native';\nimport {IBillContact} from '../../domain/entities/IBillContact';\n\nexport type RecentTabProps = {\n  style?: ViewStyle;\n  recentContact: IBillContact[];\n  onSelect?: (contactInfo?: IBillContact) => void;\n};\n"],
      mappings: "",
      ignoreList: []
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "9f8983cb975a2c9c75a15d9fe65bede8ef85b1e6"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_1p95obf1qz = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_1p95obf1qz();
cov_1p95obf1qz().s[0]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6W10sInNvdXJjZXMiOlsiL1VzZXJzL3NvbnNhbXNldC9wcm9qZWN0cy9yZWFjdC1uYXRpdmUvcGF5bWVudC1tb2R1bGUvc3JjL2NvbXBvbmVudHMvcmVjZW50LXRhYi90eXBlcy50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQge1ZpZXdTdHlsZX0gZnJvbSAncmVhY3QtbmF0aXZlJztcbmltcG9ydCB7SUJpbGxDb250YWN0fSBmcm9tICcuLi8uLi9kb21haW4vZW50aXRpZXMvSUJpbGxDb250YWN0JztcblxuZXhwb3J0IHR5cGUgUmVjZW50VGFiUHJvcHMgPSB7XG4gIHN0eWxlPzogVmlld1N0eWxlO1xuICByZWNlbnRDb250YWN0OiBJQmlsbENvbnRhY3RbXTtcbiAgb25TZWxlY3Q/OiAoY29udGFjdEluZm8/OiBJQmlsbENvbnRhY3QpID0+IHZvaWQ7XG59O1xuIl0sIm1hcHBpbmdzIjoiIiwiaWdub3JlTGlzdCI6W119