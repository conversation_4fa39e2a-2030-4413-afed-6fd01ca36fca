5239ceb9886caad99f95056a2efc5538
"use strict";

/* istanbul ignore next */
function cov_cj8a9u7ky() {
  var path = "/Users/<USER>/projects/react-native/payment-module/src/utils/CheckPermission.ts";
  var hash = "a1990bcd50679c77780aa869e4237e02b09c1f2a";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/projects/react-native/payment-module/src/utils/CheckPermission.ts",
    statementMap: {},
    fnMap: {},
    branchMap: {},
    s: {},
    f: {},
    b: {},
    inputSourceMap: {
      version: 3,
      file: undefined,
      names: [],
      sourceRoot: undefined,
      sources: ["/Users/<USER>/projects/react-native/payment-module/src/utils/CheckPermission.ts"],
      sourcesContent: ["// handle permission\n"],
      mappings: "",
      ignoreList: []
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "a1990bcd50679c77780aa869e4237e02b09c1f2a"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_cj8a9u7ky = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_cj8a9u7ky();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6W10sInNvdXJjZXMiOlsiL1VzZXJzL3NvbnNhbXNldC9wcm9qZWN0cy9yZWFjdC1uYXRpdmUvcGF5bWVudC1tb2R1bGUvc3JjL3V0aWxzL0NoZWNrUGVybWlzc2lvbi50cyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBoYW5kbGUgcGVybWlzc2lvblxuIl0sIm1hcHBpbmdzIjoiIiwiaWdub3JlTGlzdCI6W119