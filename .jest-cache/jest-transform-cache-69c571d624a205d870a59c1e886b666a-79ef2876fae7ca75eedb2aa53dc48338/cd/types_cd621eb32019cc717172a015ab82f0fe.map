{"version": 3, "names": [], "sources": ["/Users/<USER>/projects/react-native/payment-module/src/components/recent-tab/types.ts"], "sourcesContent": ["import {ViewStyle} from 'react-native';\nimport {IBillContact} from '../../domain/entities/IBillContact';\n\nexport type RecentTabProps = {\n  style?: ViewStyle;\n  recentContact: IBillContact[];\n  onSelect?: (contactInfo?: IBillContact) => void;\n};\n"], "mappings": "", "ignoreList": []}