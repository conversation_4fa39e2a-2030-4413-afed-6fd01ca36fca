{"version": 3, "names": ["Object", "defineProperty", "exports", "value", "prepareUIRegistry", "_threads", "require", "runOnUIImmediately", "frameCallbackRegistry", "Map", "activeFrameCallbacks", "Set", "previousFrameTimestamp", "nextCallId", "runCallbacks", "callId", "_this", "loop", "timestamp", "delta", "for<PERSON>ach", "callbackId", "callbackDetails", "get", "startTime", "callback", "timeSincePreviousFrame", "timeSinceFirstFrame", "size", "requestAnimationFrame", "registerFrameCallback", "set", "unregisterFrameCallback", "manageStateFrameCallback", "delete", "state", "add", "global", "_frameCallbackRegistry"], "sources": ["../../../src/frameCallback/FrameCallbackRegistryUI.ts"], "sourcesContent": [null], "mappings": "AAAA,YAAY;;AAAAA,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,KAAA;AAAA;AAAAD,OAAA,CAAAE,iBAAA;AACZ,IAAAC,QAAA,GAAAC,OAAA;AA2BO,IAAMF,iBAAiB,GAAAF,OAAA,CAAAE,iBAAA,GAAG,IAAAG,2BAAkB,EAAC,YAAM;EACxD,SAAS;;EAET,IAAMC,qBAA8C,GAAG;IACrDA,qBAAqB,EAAE,IAAIC,GAAG,CAA0B,CAAC;IACzDC,oBAAoB,EAAE,IAAIC,GAAG,CAAS,CAAC;IACvCC,sBAAsB,EAAE,IAAI;IAC5BC,UAAU,EAAE,CAAC;IAEbC,YAAY,WAAZA,YAAYA,CAACC,MAAM,EAAE;MAAA,IAAAC,KAAA;MACnB,IAAMC,KAAI,GAAI,SAARA,IAAIA,CAAIC,SAAiB,EAAK;QAClC,IAAIH,MAAM,KAAKC,KAAI,CAACH,UAAU,EAAE;UAC9B;QACF;QACA,IAAIG,KAAI,CAACJ,sBAAsB,KAAK,IAAI,EAAE;UACxCI,KAAI,CAACJ,sBAAsB,GAAGM,SAAS;QACzC;QAEA,IAAMC,KAAK,GAAGD,SAAS,GAAGF,KAAI,CAACJ,sBAAsB;QAErDI,KAAI,CAACN,oBAAoB,CAACU,OAAO,CAAE,UAAAC,UAAkB,EAAK;UACxD,IAAMC,eAAe,GAAGN,KAAI,CAACR,qBAAqB,CAACe,GAAG,CAACF,UAAU,CAAE;UAEnE,IAAQG,SAAA,GAAcF,eAAe,CAA7BE,SAAA;UAER,IAAIA,SAAS,KAAK,IAAI,EAAE;YAEtBF,eAAe,CAACE,SAAS,GAAGN,SAAS;YAErCI,eAAe,CAACG,QAAQ,CAAC;cACvBP,SAAS,EAATA,SAAS;cACTQ,sBAAsB,EAAE,IAAI;cAC5BC,mBAAmB,EAAE;YACvB,CAAC,CAAC;UACJ,CAAC,MAAM;YAELL,eAAe,CAACG,QAAQ,CAAC;cACvBP,SAAS,EAATA,SAAS;cACTQ,sBAAsB,EAAEP,KAAK;cAC7BQ,mBAAmB,EAAET,SAAS,GAAGM;YACnC,CAAC,CAAC;UACJ;QACF,CAAC,CAAC;QAEF,IAAIR,KAAI,CAACN,oBAAoB,CAACkB,IAAI,GAAG,CAAC,EAAE;UACtCZ,KAAI,CAACJ,sBAAsB,GAAGM,SAAS;UACvCW,qBAAqB,CAACZ,KAAI,CAAC;QAC7B,CAAC,MAAM;UACLD,KAAI,CAACJ,sBAAsB,GAAG,IAAI;QACpC;MACF,CAAC;MAKD,IAAI,IAAI,CAACF,oBAAoB,CAACkB,IAAI,KAAK,CAAC,IAAIb,MAAM,KAAK,IAAI,CAACF,UAAU,EAAE;QACtEgB,qBAAqB,CAACZ,KAAI,CAAC;MAC7B;IACF,CAAC;IAEDa,qBAAqB,WAArBA,qBAAqBA,CACnBL,QAAwC,EACxCJ,UAAkB,EAClB;MACA,IAAI,CAACb,qBAAqB,CAACuB,GAAG,CAACV,UAAU,EAAE;QACzCI,QAAQ,EAARA,QAAQ;QACRD,SAAS,EAAE;MACb,CAAC,CAAC;IACJ,CAAC;IAEDQ,uBAAuB,WAAvBA,uBAAuBA,CAACX,UAAkB,EAAE;MAC1C,IAAI,CAACY,wBAAwB,CAACZ,UAAU,EAAE,KAAK,CAAC;MAChD,IAAI,CAACb,qBAAqB,CAAC0B,MAAM,CAACb,UAAU,CAAC;IAC/C,CAAC;IAEDY,wBAAwB,WAAxBA,wBAAwBA,CAACZ,UAAkB,EAAEc,KAAc,EAAE;MAC3D,IAAId,UAAU,KAAK,CAAC,CAAC,EAAE;QACrB;MACF;MACA,IAAIc,KAAK,EAAE;QACT,IAAI,CAACzB,oBAAoB,CAAC0B,GAAG,CAACf,UAAU,CAAC;QACzC,IAAI,CAACP,YAAY,CAAC,IAAI,CAACD,UAAU,CAAC;MACpC,CAAC,MAAM;QACL,IAAMY,QAAQ,GAAG,IAAI,CAACjB,qBAAqB,CAACe,GAAG,CAACF,UAAU,CAAE;QAC5DI,QAAQ,CAACD,SAAS,GAAG,IAAI;QAEzB,IAAI,CAACd,oBAAoB,CAACwB,MAAM,CAACb,UAAU,CAAC;QAC5C,IAAI,IAAI,CAACX,oBAAoB,CAACkB,IAAI,KAAK,CAAC,EAAE;UACxC,IAAI,CAACf,UAAU,IAAI,CAAC;QACtB;MACF;IACF;EACF,CAAC;EAEDwB,MAAM,CAACC,sBAAsB,GAAG9B,qBAAqB;AACvD,CAAC,CAAC", "ignoreList": []}