5883b16643a60d5d356b9fce317b1cf7
"use strict";

/* istanbul ignore next */
function cov_1hfzlpiip7() {
  var path = "/Users/<USER>/projects/react-native/payment-module/src/domain/usecases/bill-pay/CategoryListUseCase.ts";
  var hash = "099f6eb4f97ca245b0e5f73afcc1c57282a1a29d";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/projects/react-native/payment-module/src/domain/usecases/bill-pay/CategoryListUseCase.ts",
    statementMap: {
      "0": {
        start: {
          line: 3,
          column: 29
        },
        end: {
          line: 3,
          column: 84
        }
      },
      "1": {
        start: {
          line: 4,
          column: 25
        },
        end: {
          line: 4,
          column: 99
        }
      },
      "2": {
        start: {
          line: 5,
          column: 23
        },
        end: {
          line: 5,
          column: 95
        }
      },
      "3": {
        start: {
          line: 6,
          column: 20
        },
        end: {
          line: 6,
          column: 89
        }
      },
      "4": {
        start: {
          line: 7,
          column: 0
        },
        end: {
          line: 9,
          column: 3
        }
      },
      "5": {
        start: {
          line: 10,
          column: 0
        },
        end: {
          line: 10,
          column: 37
        }
      },
      "6": {
        start: {
          line: 11,
          column: 26
        },
        end: {
          line: 11,
          column: 69
        }
      },
      "7": {
        start: {
          line: 12,
          column: 26
        },
        end: {
          line: 32,
          column: 3
        }
      },
      "8": {
        start: {
          line: 14,
          column: 4
        },
        end: {
          line: 14,
          column: 61
        }
      },
      "9": {
        start: {
          line: 15,
          column: 4
        },
        end: {
          line: 15,
          column: 33
        }
      },
      "10": {
        start: {
          line: 17,
          column: 2
        },
        end: {
          line: 31,
          column: 6
        }
      },
      "11": {
        start: {
          line: 20,
          column: 21
        },
        end: {
          line: 25,
          column: 8
        }
      },
      "12": {
        start: {
          line: 21,
          column: 20
        },
        end: {
          line: 21,
          column: 24
        }
      },
      "13": {
        start: {
          line: 22,
          column: 8
        },
        end: {
          line: 24,
          column: 11
        }
      },
      "14": {
        start: {
          line: 23,
          column: 10
        },
        end: {
          line: 23,
          column: 49
        }
      },
      "15": {
        start: {
          line: 27,
          column: 8
        },
        end: {
          line: 27,
          column: 47
        }
      },
      "16": {
        start: {
          line: 29,
          column: 6
        },
        end: {
          line: 29,
          column: 21
        }
      },
      "17": {
        start: {
          line: 33,
          column: 0
        },
        end: {
          line: 33,
          column: 50
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 12,
            column: 26
          },
          end: {
            line: 12,
            column: 27
          }
        },
        loc: {
          start: {
            line: 12,
            column: 38
          },
          end: {
            line: 32,
            column: 1
          }
        },
        line: 12
      },
      "1": {
        name: "CategoryListUseCase",
        decl: {
          start: {
            line: 13,
            column: 11
          },
          end: {
            line: 13,
            column: 30
          }
        },
        loc: {
          start: {
            line: 13,
            column: 43
          },
          end: {
            line: 16,
            column: 3
          }
        },
        line: 13
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 19,
            column: 11
          },
          end: {
            line: 19,
            column: 12
          }
        },
        loc: {
          start: {
            line: 19,
            column: 23
          },
          end: {
            line: 30,
            column: 5
          }
        },
        line: 19
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 20,
            column: 53
          },
          end: {
            line: 20,
            column: 54
          }
        },
        loc: {
          start: {
            line: 20,
            column: 66
          },
          end: {
            line: 25,
            column: 7
          }
        },
        line: 20
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 22,
            column: 60
          },
          end: {
            line: 22,
            column: 61
          }
        },
        loc: {
          start: {
            line: 22,
            column: 72
          },
          end: {
            line: 24,
            column: 9
          }
        },
        line: 22
      },
      "5": {
        name: "execute",
        decl: {
          start: {
            line: 26,
            column: 15
          },
          end: {
            line: 26,
            column: 22
          }
        },
        loc: {
          start: {
            line: 26,
            column: 25
          },
          end: {
            line: 28,
            column: 7
          }
        },
        line: 26
      }
    },
    branchMap: {},
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0
    },
    b: {},
    inputSourceMap: {
      version: 3,
      file: undefined,
      names: ["ExcecutionHandler_1", "require", "CategoryListUseCase", "repository", "_classCallCheck2", "default", "_createClass2", "key", "value", "_execute", "_asyncToGenerator2", "_this", "ExecutionHandler", "execute", "categoryList", "apply", "arguments", "exports"],
      sourceRoot: undefined,
      sources: ["/Users/<USER>/projects/react-native/payment-module/src/domain/usecases/bill-pay/CategoryListUseCase.ts"],
      sourcesContent: ["import {IBillPayRepository} from '../../repositories/IBillPayRepository';\nimport {CategoryListModel} from '../../entities/category-list/CategoryListModel';\nimport {ResultState} from '../../../core/ResultState';\nimport {ExecutionHandler} from '../../../utils/ExcecutionHandler';\nexport class CategoryListUseCase {\n  private repository: IBillPayRepository;\n\n  constructor(repository: IBillPayRepository) {\n    this.repository = repository;\n  }\n\n  public async execute(): Promise<ResultState<CategoryListModel>> {\n    // call this.repository.categoryList(...)\n    return ExecutionHandler.execute(() => this.repository.categoryList());\n  }\n}\n"],
      mappings: ";;;;;;;;;;AAGA,IAAAA,mBAAA,GAAAC,OAAA;AAAkE,IACrDC,mBAAmB;EAG9B,SAAAA,oBAAYC,UAA8B;IAAA,IAAAC,gBAAA,CAAAC,OAAA,QAAAH,mBAAA;IACxC,IAAI,CAACC,UAAU,GAAGA,UAAU;EAC9B;EAAC,WAAAG,aAAA,CAAAD,OAAA,EAAAH,mBAAA;IAAAK,GAAA;IAAAC,KAAA;MAAA,IAAAC,QAAA,OAAAC,kBAAA,CAAAL,OAAA,EAEM,aAAa;QAAA,IAAAM,KAAA;QAElB,OAAOX,mBAAA,CAAAY,gBAAgB,CAACC,OAAO,CAAC;UAAA,OAAMF,KAAI,CAACR,UAAU,CAACW,YAAY,EAAE;QAAA,EAAC;MACvE,CAAC;MAAA,SAHYD,OAAOA,CAAA;QAAA,OAAAJ,QAAA,CAAAM,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAPH,OAAO;IAAA;EAAA;AAAA;AAPtBI,OAAA,CAAAf,mBAAA,GAAAA,mBAAA",
      ignoreList: []
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "099f6eb4f97ca245b0e5f73afcc1c57282a1a29d"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_1hfzlpiip7 = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_1hfzlpiip7();
var _interopRequireDefault =
/* istanbul ignore next */
(cov_1hfzlpiip7().s[0]++, require("@babel/runtime/helpers/interopRequireDefault"));
var _asyncToGenerator2 =
/* istanbul ignore next */
(cov_1hfzlpiip7().s[1]++, _interopRequireDefault(require("@babel/runtime/helpers/asyncToGenerator")));
var _classCallCheck2 =
/* istanbul ignore next */
(cov_1hfzlpiip7().s[2]++, _interopRequireDefault(require("@babel/runtime/helpers/classCallCheck")));
var _createClass2 =
/* istanbul ignore next */
(cov_1hfzlpiip7().s[3]++, _interopRequireDefault(require("@babel/runtime/helpers/createClass")));
/* istanbul ignore next */
cov_1hfzlpiip7().s[4]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_1hfzlpiip7().s[5]++;
exports.CategoryListUseCase = void 0;
var ExcecutionHandler_1 =
/* istanbul ignore next */
(cov_1hfzlpiip7().s[6]++, require("../../../utils/ExcecutionHandler"));
var CategoryListUseCase =
/* istanbul ignore next */
(cov_1hfzlpiip7().s[7]++, function () {
  /* istanbul ignore next */
  cov_1hfzlpiip7().f[0]++;
  function CategoryListUseCase(repository) {
    /* istanbul ignore next */
    cov_1hfzlpiip7().f[1]++;
    cov_1hfzlpiip7().s[8]++;
    (0, _classCallCheck2.default)(this, CategoryListUseCase);
    /* istanbul ignore next */
    cov_1hfzlpiip7().s[9]++;
    this.repository = repository;
  }
  /* istanbul ignore next */
  cov_1hfzlpiip7().s[10]++;
  return (0, _createClass2.default)(CategoryListUseCase, [{
    key: "execute",
    value: function () {
      /* istanbul ignore next */
      cov_1hfzlpiip7().f[2]++;
      var _execute =
      /* istanbul ignore next */
      (cov_1hfzlpiip7().s[11]++, (0, _asyncToGenerator2.default)(function* () {
        /* istanbul ignore next */
        cov_1hfzlpiip7().f[3]++;
        var _this =
        /* istanbul ignore next */
        (cov_1hfzlpiip7().s[12]++, this);
        /* istanbul ignore next */
        cov_1hfzlpiip7().s[13]++;
        return ExcecutionHandler_1.ExecutionHandler.execute(function () {
          /* istanbul ignore next */
          cov_1hfzlpiip7().f[4]++;
          cov_1hfzlpiip7().s[14]++;
          return _this.repository.categoryList();
        });
      }));
      function execute() {
        /* istanbul ignore next */
        cov_1hfzlpiip7().f[5]++;
        cov_1hfzlpiip7().s[15]++;
        return _execute.apply(this, arguments);
      }
      /* istanbul ignore next */
      cov_1hfzlpiip7().s[16]++;
      return execute;
    }()
  }]);
}());
/* istanbul ignore next */
cov_1hfzlpiip7().s[17]++;
exports.CategoryListUseCase = CategoryListUseCase;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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