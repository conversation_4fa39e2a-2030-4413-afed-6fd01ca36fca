{"version": 3, "names": ["cov_1hfzlpiip7", "actualCoverage", "ExcecutionHandler_1", "s", "require", "CategoryListUseCase", "f", "repository", "_classCallCheck2", "default", "_createClass2", "key", "value", "_execute", "_asyncToGenerator2", "_this", "ExecutionHandler", "execute", "categoryList", "apply", "arguments", "exports"], "sources": ["/Users/<USER>/projects/react-native/payment-module/src/domain/usecases/bill-pay/CategoryListUseCase.ts"], "sourcesContent": ["import {IBillPayRepository} from '../../repositories/IBillPayRepository';\nimport {CategoryListModel} from '../../entities/category-list/CategoryListModel';\nimport {ResultState} from '../../../core/ResultState';\nimport {ExecutionHandler} from '../../../utils/ExcecutionHandler';\nexport class CategoryListUseCase {\n  private repository: IBillPayRepository;\n\n  constructor(repository: IBillPayRepository) {\n    this.repository = repository;\n  }\n\n  public async execute(): Promise<ResultState<CategoryListModel>> {\n    // call this.repository.categoryList(...)\n    return ExecutionHandler.execute(() => this.repository.categoryList());\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IASE;IAAAA,cAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,cAAA;;;;;;;;;;;;;;;;;;;;;AANF,IAAAE,mBAAA;AAAA;AAAA,CAAAF,cAAA,GAAAG,CAAA,OAAAC,OAAA;AAAkE,IACrDC,mBAAmB;AAAA;AAAA,CAAAL,cAAA,GAAAG,CAAA;EAAA;EAAAH,cAAA,GAAAM,CAAA;EAG9B,SAAAD,oBAAYE,UAA8B;IAAA;IAAAP,cAAA,GAAAM,CAAA;IAAAN,cAAA,GAAAG,CAAA;IAAA,IAAAK,gBAAA,CAAAC,OAAA,QAAAJ,mBAAA;IAAA;IAAAL,cAAA,GAAAG,CAAA;IACxC,IAAI,CAACI,UAAU,GAAGA,UAAU;EAC9B;EAAA;EAAAP,cAAA,GAAAG,CAAA;EAAC,WAAAO,aAAA,CAAAD,OAAA,EAAAJ,mBAAA;IAAAM,GAAA;IAAAC,KAAA;MAAA;MAAAZ,cAAA,GAAAM,CAAA;MAAA,IAAAO,QAAA;MAAA;MAAA,CAAAb,cAAA,GAAAG,CAAA,YAAAW,kBAAA,CAAAL,OAAA,EAEM,aAAa;QAAA;QAAAT,cAAA,GAAAM,CAAA;QAAA,IAAAS,KAAA;QAAA;QAAA,CAAAf,cAAA,GAAAG,CAAA;QAAA;QAAAH,cAAA,GAAAG,CAAA;QAElB,OAAOD,mBAAA,CAAAc,gBAAgB,CAACC,OAAO,CAAC;UAAA;UAAAjB,cAAA,GAAAM,CAAA;UAAAN,cAAA,GAAAG,CAAA;UAAA,OAAMY,KAAI,CAACR,UAAU,CAACW,YAAY,EAAE;QAAA,EAAC;MACvE,CAAC;MAAA,SAHYD,OAAOA,CAAA;QAAA;QAAAjB,cAAA,GAAAM,CAAA;QAAAN,cAAA,GAAAG,CAAA;QAAA,OAAAU,QAAA,CAAAM,KAAA,OAAAC,SAAA;MAAA;MAAA;MAAApB,cAAA,GAAAG,CAAA;MAAA,OAAPc,OAAO;IAAA;EAAA;AAAA;AAAA;AAAAjB,cAAA,GAAAG,CAAA;AAPtBkB,OAAA,CAAAhB,mBAAA,GAAAA,mBAAA", "ignoreList": []}