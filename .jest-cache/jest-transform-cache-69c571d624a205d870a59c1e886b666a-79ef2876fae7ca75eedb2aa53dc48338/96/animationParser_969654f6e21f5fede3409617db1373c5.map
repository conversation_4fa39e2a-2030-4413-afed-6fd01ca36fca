{"version": 3, "names": ["_interopRequireDefault", "require", "Object", "defineProperty", "exports", "value", "convertAnimationObjectToKeyframes", "_slicedToArray2", "_EasingWeb", "animationObject", "keyframe", "name", "_ref", "entries", "style", "_ref2", "default", "timestamp", "step", "_ref3", "_ref4", "property", "values", "easingName", "WebEasings", "toString", "for<PERSON>ach", "_ref5", "_ref6", "transformProperty", "transformPropertyValue"], "sources": ["../../../../src/layoutReanimation/web/animationParser.ts"], "sourcesContent": [null], "mappings": "AAAA,YAAY;;AAAA,IAAAA,sBAAA,GAAAC,OAAA;AAAAC,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,KAAA;AAAA;AAAAD,OAAA,CAAAE,iCAAA,GAAAA,iCAAA;AAAA,IAAAC,eAAA,GAAAP,sBAAA,CAAAC,OAAA;AAEZ,IAAAO,UAAA,GAAAP,OAAA;AAwCO,SAASK,iCAAiCA,CAC/CG,eAA8B,EAC9B;EACA,IAAIC,QAAQ,GAAG,cAAcD,eAAe,CAACE,IAAI,KAAK;EAEtD,SAAAC,IAAA,IAAiCV,MAAM,CAACW,OAAO,CAACJ,eAAe,CAACK,KAAK,CAAC,EAAE;IAAA,IAAAC,KAAA,OAAAR,eAAA,CAAAS,OAAA,EAAAJ,IAAA;IAAA,IAA5DK,SAAS,GAAAF,KAAA;IAAA,IAAED,KAAK,GAAAC,KAAA;IAC1B,IAAMG,IAAI,GACRD,SAAS,KAAK,MAAM,GAAG,CAAC,GAAGA,SAAS,KAAK,IAAI,GAAG,GAAG,GAAGA,SAAS;IAEjEP,QAAQ,IAAI,GAAGQ,IAAI,MAAM;IAEzB,SAAAC,KAAA,IAAiCjB,MAAM,CAACW,OAAO,CAACC,KAAK,CAAC,EAAE;MAAA,IAAAM,KAAA,OAAAb,eAAA,CAAAS,OAAA,EAAAG,KAAA;MAAA,IAA5CE,QAAQ,GAAAD,KAAA;MAAA,IAAEE,MAAM,GAAAF,KAAA;MAC1B,IAAIC,QAAQ,KAAK,QAAQ,EAAE;QACzB,IAAIE,UAA2B,GAAG,QAAQ;QAE1C,IAAID,MAAM,IAAIE,qBAAU,EAAE;UACxBD,UAAU,GAAGD,MAAM;QACrB,CAAC,MAAM,IAAIA,MAAM,CAACX,IAAI,IAAIa,qBAAU,EAAE;UACpCD,UAAU,GAAGD,MAAM,CAACX,IAAI;QAC1B;QAEAD,QAAQ,IAAI,2CAA2Cc,qBAAU,CAC/DD,UAAU,CACX,CAACE,QAAQ,CAAC,CAAC,IAAI;QAEhB;MACF;MAEA,IAAIJ,QAAQ,KAAK,SAAS,EAAE;QAC1BX,QAAQ,IAAI,SAASY,MAAM,MAAM;QACjC;MACF;MAEA,IAAID,QAAQ,KAAK,SAAS,EAAE;QAC1BX,QAAQ,IAAI,QAAQY,MAAM,MAAM;QAChC;MACF;MAEA,IAAID,QAAQ,KAAK,WAAW,EAAE;QAC5BX,QAAQ,IAAI,GAAGW,QAAQ,KAAKC,MAAM,IAAI;QACtC;MACF;MAEAZ,QAAQ,IAAI,YAAY;MAExBY,MAAM,CAACI,OAAO,CAAE,UAAArB,KAAuC,EAAK;QAC1D,SAAAsB,KAAA,IAGKzB,MAAM,CAACW,OAAO,CAACR,KAAK,CAAC,EAAE;UAAA,IAAAuB,KAAA,OAAArB,eAAA,CAAAS,OAAA,EAAAW,KAAA;UAAA,IAF1BE,iBAAiB,GAAAD,KAAA;UAAA,IACjBE,sBAAsB,GAAAF,KAAA;UAEtBlB,QAAQ,IAAI,IAAImB,iBAAiB,IAAIC,sBAAsB,GAAG;QAChE;MACF,CAAC,CAAC;MACFpB,QAAQ,IAAI,IAAI;IAClB;IACAA,QAAQ,IAAI,IAAI;EAClB;EACAA,QAAQ,IAAI,IAAI;EAEhB,OAAOA,QAAQ;AACjB", "ignoreList": []}