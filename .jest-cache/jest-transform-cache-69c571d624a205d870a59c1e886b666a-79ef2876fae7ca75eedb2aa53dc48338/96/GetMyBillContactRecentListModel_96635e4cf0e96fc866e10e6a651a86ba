0e8560c8d85193478bfacddabbbf1dbb
"use strict";

/* istanbul ignore next */
function cov_kl87w7jav() {
  var path = "/Users/<USER>/projects/react-native/payment-module/src/domain/entities/get-my-bill-contact-recent-list/GetMyBillContactRecentListModel.ts";
  var hash = "5906fe2cdbb0c8fbe4a62d8c1660421ae92ecf37";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/projects/react-native/payment-module/src/domain/entities/get-my-bill-contact-recent-list/GetMyBillContactRecentListModel.ts",
    statementMap: {
      "0": {
        start: {
          line: 3,
          column: 29
        },
        end: {
          line: 3,
          column: 84
        }
      },
      "1": {
        start: {
          line: 4,
          column: 23
        },
        end: {
          line: 4,
          column: 95
        }
      },
      "2": {
        start: {
          line: 5,
          column: 20
        },
        end: {
          line: 5,
          column: 89
        }
      },
      "3": {
        start: {
          line: 6,
          column: 0
        },
        end: {
          line: 8,
          column: 3
        }
      },
      "4": {
        start: {
          line: 9,
          column: 0
        },
        end: {
          line: 9,
          column: 45
        }
      },
      "5": {
        start: {
          line: 10,
          column: 34
        },
        end: {
          line: 124,
          column: 3
        }
      },
      "6": {
        start: {
          line: 12,
          column: 4
        },
        end: {
          line: 12,
          column: 69
        }
      },
      "7": {
        start: {
          line: 13,
          column: 4
        },
        end: {
          line: 13,
          column: 17
        }
      },
      "8": {
        start: {
          line: 14,
          column: 4
        },
        end: {
          line: 14,
          column: 29
        }
      },
      "9": {
        start: {
          line: 15,
          column: 4
        },
        end: {
          line: 15,
          column: 29
        }
      },
      "10": {
        start: {
          line: 16,
          column: 4
        },
        end: {
          line: 16,
          column: 33
        }
      },
      "11": {
        start: {
          line: 17,
          column: 4
        },
        end: {
          line: 17,
          column: 37
        }
      },
      "12": {
        start: {
          line: 18,
          column: 4
        },
        end: {
          line: 18,
          column: 35
        }
      },
      "13": {
        start: {
          line: 19,
          column: 4
        },
        end: {
          line: 19,
          column: 25
        }
      },
      "14": {
        start: {
          line: 20,
          column: 4
        },
        end: {
          line: 20,
          column: 35
        }
      },
      "15": {
        start: {
          line: 21,
          column: 4
        },
        end: {
          line: 21,
          column: 39
        }
      },
      "16": {
        start: {
          line: 22,
          column: 4
        },
        end: {
          line: 22,
          column: 27
        }
      },
      "17": {
        start: {
          line: 23,
          column: 4
        },
        end: {
          line: 23,
          column: 35
        }
      },
      "18": {
        start: {
          line: 24,
          column: 4
        },
        end: {
          line: 24,
          column: 39
        }
      },
      "19": {
        start: {
          line: 25,
          column: 4
        },
        end: {
          line: 25,
          column: 41
        }
      },
      "20": {
        start: {
          line: 26,
          column: 4
        },
        end: {
          line: 26,
          column: 23
        }
      },
      "21": {
        start: {
          line: 28,
          column: 2
        },
        end: {
          line: 123,
          column: 6
        }
      },
      "22": {
        start: {
          line: 31,
          column: 6
        },
        end: {
          line: 31,
          column: 18
        }
      },
      "23": {
        start: {
          line: 37,
          column: 6
        },
        end: {
          line: 37,
          column: 85
        }
      },
      "24": {
        start: {
          line: 43,
          column: 6
        },
        end: {
          line: 43,
          column: 164
        }
      },
      "25": {
        start: {
          line: 48,
          column: 6
        },
        end: {
          line: 48,
          column: 24
        }
      },
      "26": {
        start: {
          line: 53,
          column: 6
        },
        end: {
          line: 53,
          column: 24
        }
      },
      "27": {
        start: {
          line: 58,
          column: 6
        },
        end: {
          line: 58,
          column: 16
        }
      },
      "28": {
        start: {
          line: 67,
          column: 6
        },
        end: {
          line: 67,
          column: 58
        }
      },
      "29": {
        start: {
          line: 73,
          column: 6
        },
        end: {
          line: 73,
          column: 88
        }
      },
      "30": {
        start: {
          line: 79,
          column: 6
        },
        end: {
          line: 79,
          column: 76
        }
      },
      "31": {
        start: {
          line: 85,
          column: 6
        },
        end: {
          line: 85,
          column: 82
        }
      },
      "32": {
        start: {
          line: 90,
          column: 6
        },
        end: {
          line: 90,
          column: 16
        }
      },
      "33": {
        start: {
          line: 95,
          column: 6
        },
        end: {
          line: 95,
          column: 19
        }
      },
      "34": {
        start: {
          line: 100,
          column: 6
        },
        end: {
          line: 100,
          column: 16
        }
      },
      "35": {
        start: {
          line: 105,
          column: 6
        },
        end: {
          line: 105,
          column: 16
        }
      },
      "36": {
        start: {
          line: 110,
          column: 6
        },
        end: {
          line: 110,
          column: 19
        }
      },
      "37": {
        start: {
          line: 116,
          column: 6
        },
        end: {
          line: 116,
          column: 76
        }
      },
      "38": {
        start: {
          line: 121,
          column: 6
        },
        end: {
          line: 121,
          column: 16
        }
      },
      "39": {
        start: {
          line: 125,
          column: 0
        },
        end: {
          line: 125,
          column: 66
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 10,
            column: 34
          },
          end: {
            line: 10,
            column: 35
          }
        },
        loc: {
          start: {
            line: 10,
            column: 46
          },
          end: {
            line: 124,
            column: 1
          }
        },
        line: 10
      },
      "1": {
        name: "GetMyBillContactRecentModel",
        decl: {
          start: {
            line: 11,
            column: 11
          },
          end: {
            line: 11,
            column: 38
          }
        },
        loc: {
          start: {
            line: 11,
            column: 198
          },
          end: {
            line: 27,
            column: 3
          }
        },
        line: 11
      },
      "2": {
        name: "isTopup",
        decl: {
          start: {
            line: 30,
            column: 20
          },
          end: {
            line: 30,
            column: 27
          }
        },
        loc: {
          start: {
            line: 30,
            column: 30
          },
          end: {
            line: 32,
            column: 5
          }
        },
        line: 30
      },
      "3": {
        name: "getServiceCode",
        decl: {
          start: {
            line: 35,
            column: 20
          },
          end: {
            line: 35,
            column: 34
          }
        },
        loc: {
          start: {
            line: 35,
            column: 37
          },
          end: {
            line: 38,
            column: 5
          }
        },
        line: 35
      },
      "4": {
        name: "getPayableAmount",
        decl: {
          start: {
            line: 41,
            column: 20
          },
          end: {
            line: 41,
            column: 36
          }
        },
        loc: {
          start: {
            line: 41,
            column: 39
          },
          end: {
            line: 44,
            column: 5
          }
        },
        line: 41
      },
      "5": {
        name: "getFavoriteStatus",
        decl: {
          start: {
            line: 47,
            column: 20
          },
          end: {
            line: 47,
            column: 37
          }
        },
        loc: {
          start: {
            line: 47,
            column: 40
          },
          end: {
            line: 49,
            column: 5
          }
        },
        line: 47
      },
      "6": {
        name: "getReminderStatus",
        decl: {
          start: {
            line: 52,
            column: 20
          },
          end: {
            line: 52,
            column: 37
          }
        },
        loc: {
          start: {
            line: 52,
            column: 40
          },
          end: {
            line: 54,
            column: 5
          }
        },
        line: 52
      },
      "7": {
        name: "getPartnerCode",
        decl: {
          start: {
            line: 57,
            column: 20
          },
          end: {
            line: 57,
            column: 34
          }
        },
        loc: {
          start: {
            line: 57,
            column: 37
          },
          end: {
            line: 59,
            column: 5
          }
        },
        line: 57
      },
      "8": {
        name: "setBillList",
        decl: {
          start: {
            line: 62,
            column: 20
          },
          end: {
            line: 62,
            column: 31
          }
        },
        loc: {
          start: {
            line: 62,
            column: 42
          },
          end: {
            line: 62,
            column: 44
          }
        },
        line: 62
      },
      "9": {
        name: "getId",
        decl: {
          start: {
            line: 65,
            column: 20
          },
          end: {
            line: 65,
            column: 25
          }
        },
        loc: {
          start: {
            line: 65,
            column: 28
          },
          end: {
            line: 68,
            column: 5
          }
        },
        line: 65
      },
      "10": {
        name: "getCustomerName",
        decl: {
          start: {
            line: 71,
            column: 20
          },
          end: {
            line: 71,
            column: 35
          }
        },
        loc: {
          start: {
            line: 71,
            column: 38
          },
          end: {
            line: 74,
            column: 5
          }
        },
        line: 71
      },
      "11": {
        name: "getSubtitle",
        decl: {
          start: {
            line: 77,
            column: 20
          },
          end: {
            line: 77,
            column: 31
          }
        },
        loc: {
          start: {
            line: 77,
            column: 34
          },
          end: {
            line: 80,
            column: 5
          }
        },
        line: 77
      },
      "12": {
        name: "getExternalId",
        decl: {
          start: {
            line: 83,
            column: 20
          },
          end: {
            line: 83,
            column: 33
          }
        },
        loc: {
          start: {
            line: 83,
            column: 36
          },
          end: {
            line: 86,
            column: 5
          }
        },
        line: 83
      },
      "13": {
        name: "getIcon",
        decl: {
          start: {
            line: 89,
            column: 20
          },
          end: {
            line: 89,
            column: 27
          }
        },
        loc: {
          start: {
            line: 89,
            column: 30
          },
          end: {
            line: 91,
            column: 5
          }
        },
        line: 89
      },
      "14": {
        name: "isPair",
        decl: {
          start: {
            line: 94,
            column: 20
          },
          end: {
            line: 94,
            column: 26
          }
        },
        loc: {
          start: {
            line: 94,
            column: 29
          },
          end: {
            line: 96,
            column: 5
          }
        },
        line: 94
      },
      "15": {
        name: "getType",
        decl: {
          start: {
            line: 99,
            column: 20
          },
          end: {
            line: 99,
            column: 27
          }
        },
        loc: {
          start: {
            line: 99,
            column: 30
          },
          end: {
            line: 101,
            column: 5
          }
        },
        line: 99
      },
      "16": {
        name: "getSearchContent",
        decl: {
          start: {
            line: 104,
            column: 20
          },
          end: {
            line: 104,
            column: 36
          }
        },
        loc: {
          start: {
            line: 104,
            column: 39
          },
          end: {
            line: 106,
            column: 5
          }
        },
        line: 104
      },
      "17": {
        name: "isEditable",
        decl: {
          start: {
            line: 109,
            column: 20
          },
          end: {
            line: 109,
            column: 30
          }
        },
        loc: {
          start: {
            line: 109,
            column: 33
          },
          end: {
            line: 111,
            column: 5
          }
        },
        line: 109
      },
      "18": {
        name: "getBillCode",
        decl: {
          start: {
            line: 114,
            column: 20
          },
          end: {
            line: 114,
            column: 31
          }
        },
        loc: {
          start: {
            line: 114,
            column: 34
          },
          end: {
            line: 117,
            column: 5
          }
        },
        line: 114
      },
      "19": {
        name: "getCategoryCode",
        decl: {
          start: {
            line: 120,
            column: 20
          },
          end: {
            line: 120,
            column: 35
          }
        },
        loc: {
          start: {
            line: 120,
            column: 38
          },
          end: {
            line: 122,
            column: 5
          }
        },
        line: 120
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 37,
            column: 13
          },
          end: {
            line: 37,
            column: 84
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 37,
            column: 62
          },
          end: {
            line: 37,
            column: 79
          }
        }, {
          start: {
            line: 37,
            column: 82
          },
          end: {
            line: 37,
            column: 84
          }
        }],
        line: 37
      },
      "1": {
        loc: {
          start: {
            line: 43,
            column: 13
          },
          end: {
            line: 43,
            column: 163
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 43,
            column: 136
          },
          end: {
            line: 43,
            column: 157
          }
        }, {
          start: {
            line: 43,
            column: 160
          },
          end: {
            line: 43,
            column: 163
          }
        }],
        line: 43
      },
      "2": {
        loc: {
          start: {
            line: 43,
            column: 38
          },
          end: {
            line: 43,
            column: 124
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 43,
            column: 87
          },
          end: {
            line: 43,
            column: 93
          }
        }, {
          start: {
            line: 43,
            column: 96
          },
          end: {
            line: 43,
            column: 124
          }
        }],
        line: 43
      },
      "3": {
        loc: {
          start: {
            line: 67,
            column: 13
          },
          end: {
            line: 67,
            column: 57
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 67,
            column: 44
          },
          end: {
            line: 67,
            column: 52
          }
        }, {
          start: {
            line: 67,
            column: 55
          },
          end: {
            line: 67,
            column: 57
          }
        }],
        line: 67
      },
      "4": {
        loc: {
          start: {
            line: 73,
            column: 13
          },
          end: {
            line: 73,
            column: 87
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 73,
            column: 64
          },
          end: {
            line: 73,
            column: 82
          }
        }, {
          start: {
            line: 73,
            column: 85
          },
          end: {
            line: 73,
            column: 87
          }
        }],
        line: 73
      },
      "5": {
        loc: {
          start: {
            line: 79,
            column: 13
          },
          end: {
            line: 79,
            column: 75
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 79,
            column: 56
          },
          end: {
            line: 79,
            column: 70
          }
        }, {
          start: {
            line: 79,
            column: 73
          },
          end: {
            line: 79,
            column: 75
          }
        }],
        line: 79
      },
      "6": {
        loc: {
          start: {
            line: 85,
            column: 13
          },
          end: {
            line: 85,
            column: 81
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 85,
            column: 60
          },
          end: {
            line: 85,
            column: 76
          }
        }, {
          start: {
            line: 85,
            column: 79
          },
          end: {
            line: 85,
            column: 81
          }
        }],
        line: 85
      },
      "7": {
        loc: {
          start: {
            line: 116,
            column: 13
          },
          end: {
            line: 116,
            column: 75
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 116,
            column: 56
          },
          end: {
            line: 116,
            column: 70
          }
        }, {
          start: {
            line: 116,
            column: 73
          },
          end: {
            line: 116,
            column: 75
          }
        }],
        line: 116
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0
    },
    b: {
      "0": [0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0]
    },
    inputSourceMap: {
      version: 3,
      file: undefined,
      names: ["GetMyBillContactRecentModel", "id", "billCode", "category", "subGroupId", "customerName", "totalAmount", "period", "paymentDate", "accountNumber", "coreRef", "serviceCode", "arrangementId", "paymentOrderId", "cifNo", "_classCallCheck2", "default", "_createClass2", "key", "value", "isTopup", "getServiceCode", "_this$serviceCode", "getPayableAmount", "_this$totalAmount$toS", "_this$totalAmount", "toString", "getFavoriteStatus", "getReminderStatus", "getPartnerCode", "setBillList", "billList", "getId", "_this$id", "getCustomerName", "_this$customerName", "getSubtitle", "_this$category", "getExternalId", "_this$subGroupId", "getIcon", "isPair", "getType", "getSearchContent", "isEditable", "getBillCode", "_this$billCode", "getCategoryCode", "exports"],
      sourceRoot: undefined,
      sources: ["/Users/<USER>/projects/react-native/payment-module/src/domain/entities/get-my-bill-contact-recent-list/GetMyBillContactRecentListModel.ts"],
      sourcesContent: ["import {IBillContact} from '../IBillContact';\n\nexport type GetMyBillContactRecentListModel = GetMyBillContactRecentModel[];\n\nexport class GetMyBillContactRecentModel implements IBillContact {\n  constructor(\n    public id?: string,\n    public billCode?: string,\n    public category?: string,\n    public subGroupId?: string,\n    public customerName?: string,\n    public totalAmount?: number,\n    public period?: string,\n    public paymentDate?: string,\n    public accountNumber?: string,\n    public coreRef?: string,\n    public serviceCode?: string,\n    public arrangementId?: string,\n    public paymentOrderId?: string,\n    public cifNo?: string,\n  ) {}\n\n  //TODO: enhance this method to return the correct value after the backend is ready\n  isTopup(): boolean {\n    return true;\n  }\n\n  getServiceCode(): string {\n    return this.serviceCode ?? '';\n  }\n\n  getPayableAmount(): string {\n    return this.totalAmount?.toString() ?? '0';\n  }\n  getFavoriteStatus(): 'ACTIVE' | 'INACTIVE' {\n    return 'INACTIVE';\n  }\n  getReminderStatus(): 'ACTIVE' | 'INACTIVE' {\n    return 'INACTIVE';\n  }\n\n  getPartnerCode(): string {\n    return '';\n  }\n\n  setBillList(billList?: any[]) {}\n  getId(): string {\n    return this.id ?? '';\n  }\n\n  getCustomerName(): string {\n    return this.customerName ?? '';\n  }\n  getSubtitle(): string {\n    return this.category ?? '';\n  }\n\n  getExternalId(): string {\n    return this.subGroupId ?? '';\n  }\n  getIcon(): string {\n    return '';\n  }\n  isPair(): boolean {\n    return false;\n  }\n  getType(): string {\n    return '';\n  }\n  getSearchContent(): string {\n    return '';\n  }\n  isEditable(): boolean {\n    return false;\n  }\n  getBillCode?(): string {\n    return this.billCode ?? '';\n  }\n\n  getCategoryCode?(): string {\n    return '';\n  }\n}\n"],
      mappings: ";;;;;;;;;IAIaA,2BAA2B;EACtC,SAAAA,4BACSC,EAAW,EACXC,QAAiB,EACjBC,QAAiB,EACjBC,UAAmB,EACnBC,YAAqB,EACrBC,WAAoB,EACpBC,MAAe,EACfC,WAAoB,EACpBC,aAAsB,EACtBC,OAAgB,EAChBC,WAAoB,EACpBC,aAAsB,EACtBC,cAAuB,EACvBC,KAAc;IAAA,IAAAC,gBAAA,CAAAC,OAAA,QAAAhB,2BAAA;IAbd,KAAAC,EAAE,GAAFA,EAAE;IACF,KAAAC,QAAQ,GAARA,QAAQ;IACR,KAAAC,QAAQ,GAARA,QAAQ;IACR,KAAAC,UAAU,GAAVA,UAAU;IACV,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,KAAK,GAALA,KAAK;EACX;EAAC,WAAAG,aAAA,CAAAD,OAAA,EAAAhB,2BAAA;IAAAkB,GAAA;IAAAC,KAAA,EAGJ,SAAAC,OAAOA,CAAA;MACL,OAAO,IAAI;IACb;EAAC;IAAAF,GAAA;IAAAC,KAAA,EAED,SAAAE,cAAcA,CAAA;MAAA,IAAAC,iBAAA;MACZ,QAAAA,iBAAA,GAAO,IAAI,CAACX,WAAW,YAAAW,iBAAA,GAAI,EAAE;IAC/B;EAAC;IAAAJ,GAAA;IAAAC,KAAA,EAED,SAAAI,gBAAgBA,CAAA;MAAA,IAAAC,qBAAA,EAAAC,iBAAA;MACd,QAAAD,qBAAA,IAAAC,iBAAA,GAAO,IAAI,CAACnB,WAAW,qBAAhBmB,iBAAA,CAAkBC,QAAQ,EAAE,YAAAF,qBAAA,GAAI,GAAG;IAC5C;EAAC;IAAAN,GAAA;IAAAC,KAAA,EACD,SAAAQ,iBAAiBA,CAAA;MACf,OAAO,UAAU;IACnB;EAAC;IAAAT,GAAA;IAAAC,KAAA,EACD,SAAAS,iBAAiBA,CAAA;MACf,OAAO,UAAU;IACnB;EAAC;IAAAV,GAAA;IAAAC,KAAA,EAED,SAAAU,cAAcA,CAAA;MACZ,OAAO,EAAE;IACX;EAAC;IAAAX,GAAA;IAAAC,KAAA,EAED,SAAAW,WAAWA,CAACC,QAAgB,GAAG;EAAC;IAAAb,GAAA;IAAAC,KAAA,EAChC,SAAAa,KAAKA,CAAA;MAAA,IAAAC,QAAA;MACH,QAAAA,QAAA,GAAO,IAAI,CAAChC,EAAE,YAAAgC,QAAA,GAAI,EAAE;IACtB;EAAC;IAAAf,GAAA;IAAAC,KAAA,EAED,SAAAe,eAAeA,CAAA;MAAA,IAAAC,kBAAA;MACb,QAAAA,kBAAA,GAAO,IAAI,CAAC9B,YAAY,YAAA8B,kBAAA,GAAI,EAAE;IAChC;EAAC;IAAAjB,GAAA;IAAAC,KAAA,EACD,SAAAiB,WAAWA,CAAA;MAAA,IAAAC,cAAA;MACT,QAAAA,cAAA,GAAO,IAAI,CAAClC,QAAQ,YAAAkC,cAAA,GAAI,EAAE;IAC5B;EAAC;IAAAnB,GAAA;IAAAC,KAAA,EAED,SAAAmB,aAAaA,CAAA;MAAA,IAAAC,gBAAA;MACX,QAAAA,gBAAA,GAAO,IAAI,CAACnC,UAAU,YAAAmC,gBAAA,GAAI,EAAE;IAC9B;EAAC;IAAArB,GAAA;IAAAC,KAAA,EACD,SAAAqB,OAAOA,CAAA;MACL,OAAO,EAAE;IACX;EAAC;IAAAtB,GAAA;IAAAC,KAAA,EACD,SAAAsB,MAAMA,CAAA;MACJ,OAAO,KAAK;IACd;EAAC;IAAAvB,GAAA;IAAAC,KAAA,EACD,SAAAuB,OAAOA,CAAA;MACL,OAAO,EAAE;IACX;EAAC;IAAAxB,GAAA;IAAAC,KAAA,EACD,SAAAwB,gBAAgBA,CAAA;MACd,OAAO,EAAE;IACX;EAAC;IAAAzB,GAAA;IAAAC,KAAA,EACD,SAAAyB,UAAUA,CAAA;MACR,OAAO,KAAK;IACd;EAAC;IAAA1B,GAAA;IAAAC,KAAA,EACD,SAAA0B,WAAWA,CAAA;MAAA,IAAAC,cAAA;MACT,QAAAA,cAAA,GAAO,IAAI,CAAC5C,QAAQ,YAAA4C,cAAA,GAAI,EAAE;IAC5B;EAAC;IAAA5B,GAAA;IAAAC,KAAA,EAED,SAAA4B,eAAeA,CAAA;MACb,OAAO,EAAE;IACX;EAAC;AAAA;AA7EHC,OAAA,CAAAhD,2BAAA,GAAAA,2BAAA",
      ignoreList: []
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "5906fe2cdbb0c8fbe4a62d8c1660421ae92ecf37"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_kl87w7jav = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_kl87w7jav();
var _interopRequireDefault =
/* istanbul ignore next */
(cov_kl87w7jav().s[0]++, require("@babel/runtime/helpers/interopRequireDefault"));
var _classCallCheck2 =
/* istanbul ignore next */
(cov_kl87w7jav().s[1]++, _interopRequireDefault(require("@babel/runtime/helpers/classCallCheck")));
var _createClass2 =
/* istanbul ignore next */
(cov_kl87w7jav().s[2]++, _interopRequireDefault(require("@babel/runtime/helpers/createClass")));
/* istanbul ignore next */
cov_kl87w7jav().s[3]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_kl87w7jav().s[4]++;
exports.GetMyBillContactRecentModel = void 0;
var GetMyBillContactRecentModel =
/* istanbul ignore next */
(cov_kl87w7jav().s[5]++, function () {
  /* istanbul ignore next */
  cov_kl87w7jav().f[0]++;
  function GetMyBillContactRecentModel(id, billCode, category, subGroupId, customerName, totalAmount, period, paymentDate, accountNumber, coreRef, serviceCode, arrangementId, paymentOrderId, cifNo) {
    /* istanbul ignore next */
    cov_kl87w7jav().f[1]++;
    cov_kl87w7jav().s[6]++;
    (0, _classCallCheck2.default)(this, GetMyBillContactRecentModel);
    /* istanbul ignore next */
    cov_kl87w7jav().s[7]++;
    this.id = id;
    /* istanbul ignore next */
    cov_kl87w7jav().s[8]++;
    this.billCode = billCode;
    /* istanbul ignore next */
    cov_kl87w7jav().s[9]++;
    this.category = category;
    /* istanbul ignore next */
    cov_kl87w7jav().s[10]++;
    this.subGroupId = subGroupId;
    /* istanbul ignore next */
    cov_kl87w7jav().s[11]++;
    this.customerName = customerName;
    /* istanbul ignore next */
    cov_kl87w7jav().s[12]++;
    this.totalAmount = totalAmount;
    /* istanbul ignore next */
    cov_kl87w7jav().s[13]++;
    this.period = period;
    /* istanbul ignore next */
    cov_kl87w7jav().s[14]++;
    this.paymentDate = paymentDate;
    /* istanbul ignore next */
    cov_kl87w7jav().s[15]++;
    this.accountNumber = accountNumber;
    /* istanbul ignore next */
    cov_kl87w7jav().s[16]++;
    this.coreRef = coreRef;
    /* istanbul ignore next */
    cov_kl87w7jav().s[17]++;
    this.serviceCode = serviceCode;
    /* istanbul ignore next */
    cov_kl87w7jav().s[18]++;
    this.arrangementId = arrangementId;
    /* istanbul ignore next */
    cov_kl87w7jav().s[19]++;
    this.paymentOrderId = paymentOrderId;
    /* istanbul ignore next */
    cov_kl87w7jav().s[20]++;
    this.cifNo = cifNo;
  }
  /* istanbul ignore next */
  cov_kl87w7jav().s[21]++;
  return (0, _createClass2.default)(GetMyBillContactRecentModel, [{
    key: "isTopup",
    value: function isTopup() {
      /* istanbul ignore next */
      cov_kl87w7jav().f[2]++;
      cov_kl87w7jav().s[22]++;
      return true;
    }
  }, {
    key: "getServiceCode",
    value: function getServiceCode() {
      /* istanbul ignore next */
      cov_kl87w7jav().f[3]++;
      var _this$serviceCode;
      /* istanbul ignore next */
      cov_kl87w7jav().s[23]++;
      return (_this$serviceCode = this.serviceCode) != null ?
      /* istanbul ignore next */
      (cov_kl87w7jav().b[0][0]++, _this$serviceCode) :
      /* istanbul ignore next */
      (cov_kl87w7jav().b[0][1]++, '');
    }
  }, {
    key: "getPayableAmount",
    value: function getPayableAmount() {
      /* istanbul ignore next */
      cov_kl87w7jav().f[4]++;
      var _this$totalAmount$toS, _this$totalAmount;
      /* istanbul ignore next */
      cov_kl87w7jav().s[24]++;
      return (_this$totalAmount$toS = (_this$totalAmount = this.totalAmount) == null ?
      /* istanbul ignore next */
      (cov_kl87w7jav().b[2][0]++, void 0) :
      /* istanbul ignore next */
      (cov_kl87w7jav().b[2][1]++, _this$totalAmount.toString())) != null ?
      /* istanbul ignore next */
      (cov_kl87w7jav().b[1][0]++, _this$totalAmount$toS) :
      /* istanbul ignore next */
      (cov_kl87w7jav().b[1][1]++, '0');
    }
  }, {
    key: "getFavoriteStatus",
    value: function getFavoriteStatus() {
      /* istanbul ignore next */
      cov_kl87w7jav().f[5]++;
      cov_kl87w7jav().s[25]++;
      return 'INACTIVE';
    }
  }, {
    key: "getReminderStatus",
    value: function getReminderStatus() {
      /* istanbul ignore next */
      cov_kl87w7jav().f[6]++;
      cov_kl87w7jav().s[26]++;
      return 'INACTIVE';
    }
  }, {
    key: "getPartnerCode",
    value: function getPartnerCode() {
      /* istanbul ignore next */
      cov_kl87w7jav().f[7]++;
      cov_kl87w7jav().s[27]++;
      return '';
    }
  }, {
    key: "setBillList",
    value: function setBillList(billList) {
      /* istanbul ignore next */
      cov_kl87w7jav().f[8]++;
    }
  }, {
    key: "getId",
    value: function getId() {
      /* istanbul ignore next */
      cov_kl87w7jav().f[9]++;
      var _this$id;
      /* istanbul ignore next */
      cov_kl87w7jav().s[28]++;
      return (_this$id = this.id) != null ?
      /* istanbul ignore next */
      (cov_kl87w7jav().b[3][0]++, _this$id) :
      /* istanbul ignore next */
      (cov_kl87w7jav().b[3][1]++, '');
    }
  }, {
    key: "getCustomerName",
    value: function getCustomerName() {
      /* istanbul ignore next */
      cov_kl87w7jav().f[10]++;
      var _this$customerName;
      /* istanbul ignore next */
      cov_kl87w7jav().s[29]++;
      return (_this$customerName = this.customerName) != null ?
      /* istanbul ignore next */
      (cov_kl87w7jav().b[4][0]++, _this$customerName) :
      /* istanbul ignore next */
      (cov_kl87w7jav().b[4][1]++, '');
    }
  }, {
    key: "getSubtitle",
    value: function getSubtitle() {
      /* istanbul ignore next */
      cov_kl87w7jav().f[11]++;
      var _this$category;
      /* istanbul ignore next */
      cov_kl87w7jav().s[30]++;
      return (_this$category = this.category) != null ?
      /* istanbul ignore next */
      (cov_kl87w7jav().b[5][0]++, _this$category) :
      /* istanbul ignore next */
      (cov_kl87w7jav().b[5][1]++, '');
    }
  }, {
    key: "getExternalId",
    value: function getExternalId() {
      /* istanbul ignore next */
      cov_kl87w7jav().f[12]++;
      var _this$subGroupId;
      /* istanbul ignore next */
      cov_kl87w7jav().s[31]++;
      return (_this$subGroupId = this.subGroupId) != null ?
      /* istanbul ignore next */
      (cov_kl87w7jav().b[6][0]++, _this$subGroupId) :
      /* istanbul ignore next */
      (cov_kl87w7jav().b[6][1]++, '');
    }
  }, {
    key: "getIcon",
    value: function getIcon() {
      /* istanbul ignore next */
      cov_kl87w7jav().f[13]++;
      cov_kl87w7jav().s[32]++;
      return '';
    }
  }, {
    key: "isPair",
    value: function isPair() {
      /* istanbul ignore next */
      cov_kl87w7jav().f[14]++;
      cov_kl87w7jav().s[33]++;
      return false;
    }
  }, {
    key: "getType",
    value: function getType() {
      /* istanbul ignore next */
      cov_kl87w7jav().f[15]++;
      cov_kl87w7jav().s[34]++;
      return '';
    }
  }, {
    key: "getSearchContent",
    value: function getSearchContent() {
      /* istanbul ignore next */
      cov_kl87w7jav().f[16]++;
      cov_kl87w7jav().s[35]++;
      return '';
    }
  }, {
    key: "isEditable",
    value: function isEditable() {
      /* istanbul ignore next */
      cov_kl87w7jav().f[17]++;
      cov_kl87w7jav().s[36]++;
      return false;
    }
  }, {
    key: "getBillCode",
    value: function getBillCode() {
      /* istanbul ignore next */
      cov_kl87w7jav().f[18]++;
      var _this$billCode;
      /* istanbul ignore next */
      cov_kl87w7jav().s[37]++;
      return (_this$billCode = this.billCode) != null ?
      /* istanbul ignore next */
      (cov_kl87w7jav().b[7][0]++, _this$billCode) :
      /* istanbul ignore next */
      (cov_kl87w7jav().b[7][1]++, '');
    }
  }, {
    key: "getCategoryCode",
    value: function getCategoryCode() {
      /* istanbul ignore next */
      cov_kl87w7jav().f[19]++;
      cov_kl87w7jav().s[38]++;
      return '';
    }
  }]);
}());
/* istanbul ignore next */
cov_kl87w7jav().s[39]++;
exports.GetMyBillContactRecentModel = GetMyBillContactRecentModel;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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