{"version": 3, "names": ["cov_kl87w7jav", "actualCoverage", "GetMyBillContactRecentModel", "s", "f", "id", "billCode", "category", "subGroupId", "customerName", "totalAmount", "period", "paymentDate", "accountNumber", "coreRef", "serviceCode", "arrangementId", "paymentOrderId", "cifNo", "_classCallCheck2", "default", "_createClass2", "key", "value", "isTopup", "getServiceCode", "_this$serviceCode", "b", "getPayableAmount", "_this$totalAmount$toS", "_this$totalAmount", "toString", "getFavoriteStatus", "getReminderStatus", "getPartnerCode", "setBillList", "billList", "getId", "_this$id", "getCustomerName", "_this$customerName", "getSubtitle", "_this$category", "getExternalId", "_this$subGroupId", "getIcon", "isPair", "getType", "getSearchContent", "isEditable", "getBillCode", "_this$billCode", "getCategoryCode", "exports"], "sources": ["/Users/<USER>/projects/react-native/payment-module/src/domain/entities/get-my-bill-contact-recent-list/GetMyBillContactRecentListModel.ts"], "sourcesContent": ["import {IBillContact} from '../IBillContact';\n\nexport type GetMyBillContactRecentListModel = GetMyBillContactRecentModel[];\n\nexport class GetMyBillContactRecentModel implements IBillContact {\n  constructor(\n    public id?: string,\n    public billCode?: string,\n    public category?: string,\n    public subGroupId?: string,\n    public customerName?: string,\n    public totalAmount?: number,\n    public period?: string,\n    public paymentDate?: string,\n    public accountNumber?: string,\n    public coreRef?: string,\n    public serviceCode?: string,\n    public arrangementId?: string,\n    public paymentOrderId?: string,\n    public cifNo?: string,\n  ) {}\n\n  //TODO: enhance this method to return the correct value after the backend is ready\n  isTopup(): boolean {\n    return true;\n  }\n\n  getServiceCode(): string {\n    return this.serviceCode ?? '';\n  }\n\n  getPayableAmount(): string {\n    return this.totalAmount?.toString() ?? '0';\n  }\n  getFavoriteStatus(): 'ACTIVE' | 'INACTIVE' {\n    return 'INACTIVE';\n  }\n  getReminderStatus(): 'ACTIVE' | 'INACTIVE' {\n    return 'INACTIVE';\n  }\n\n  getPartnerCode(): string {\n    return '';\n  }\n\n  setBillList(billList?: any[]) {}\n  getId(): string {\n    return this.id ?? '';\n  }\n\n  getCustomerName(): string {\n    return this.customerName ?? '';\n  }\n  getSubtitle(): string {\n    return this.category ?? '';\n  }\n\n  getExternalId(): string {\n    return this.subGroupId ?? '';\n  }\n  getIcon(): string {\n    return '';\n  }\n  isPair(): boolean {\n    return false;\n  }\n  getType(): string {\n    return '';\n  }\n  getSearchContent(): string {\n    return '';\n  }\n  isEditable(): boolean {\n    return false;\n  }\n  getBillCode?(): string {\n    return this.billCode ?? '';\n  }\n\n  getCategoryCode?(): string {\n    return '';\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IASW;IAAAA,aAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,aAAA;;;;;;;;;;;;;;;;;;IALEE,2BAA2B;AAAA;AAAA,CAAAF,aAAA,GAAAG,CAAA;EAAA;EAAAH,aAAA,GAAAI,CAAA;EACtC,SAAAF,4BACSG,EAAW,EACXC,QAAiB,EACjBC,QAAiB,EACjBC,UAAmB,EACnBC,YAAqB,EACrBC,WAAoB,EACpBC,MAAe,EACfC,WAAoB,EACpBC,aAAsB,EACtBC,OAAgB,EAChBC,WAAoB,EACpBC,aAAsB,EACtBC,cAAuB,EACvBC,KAAc;IAAA;IAAAlB,aAAA,GAAAI,CAAA;IAAAJ,aAAA,GAAAG,CAAA;IAAA,IAAAgB,gBAAA,CAAAC,OAAA,QAAAlB,2BAAA;IAAA;IAAAF,aAAA,GAAAG,CAAA;IAbd,KAAAE,EAAE,GAAFA,EAAE;IAAA;IAAAL,aAAA,GAAAG,CAAA;IACF,KAAAG,QAAQ,GAARA,QAAQ;IAAA;IAAAN,aAAA,GAAAG,CAAA;IACR,KAAAI,QAAQ,GAARA,QAAQ;IAAA;IAAAP,aAAA,GAAAG,CAAA;IACR,KAAAK,UAAU,GAAVA,UAAU;IAAA;IAAAR,aAAA,GAAAG,CAAA;IACV,KAAAM,YAAY,GAAZA,YAAY;IAAA;IAAAT,aAAA,GAAAG,CAAA;IACZ,KAAAO,WAAW,GAAXA,WAAW;IAAA;IAAAV,aAAA,GAAAG,CAAA;IACX,KAAAQ,MAAM,GAANA,MAAM;IAAA;IAAAX,aAAA,GAAAG,CAAA;IACN,KAAAS,WAAW,GAAXA,WAAW;IAAA;IAAAZ,aAAA,GAAAG,CAAA;IACX,KAAAU,aAAa,GAAbA,aAAa;IAAA;IAAAb,aAAA,GAAAG,CAAA;IACb,KAAAW,OAAO,GAAPA,OAAO;IAAA;IAAAd,aAAA,GAAAG,CAAA;IACP,KAAAY,WAAW,GAAXA,WAAW;IAAA;IAAAf,aAAA,GAAAG,CAAA;IACX,KAAAa,aAAa,GAAbA,aAAa;IAAA;IAAAhB,aAAA,GAAAG,CAAA;IACb,KAAAc,cAAc,GAAdA,cAAc;IAAA;IAAAjB,aAAA,GAAAG,CAAA;IACd,KAAAe,KAAK,GAALA,KAAK;EACX;EAAA;EAAAlB,aAAA,GAAAG,CAAA;EAAC,WAAAkB,aAAA,CAAAD,OAAA,EAAAlB,2BAAA;IAAAoB,GAAA;IAAAC,KAAA,EAGJ,SAAAC,OAAOA,CAAA;MAAA;MAAAxB,aAAA,GAAAI,CAAA;MAAAJ,aAAA,GAAAG,CAAA;MACL,OAAO,IAAI;IACb;EAAC;IAAAmB,GAAA;IAAAC,KAAA,EAED,SAAAE,cAAcA,CAAA;MAAA;MAAAzB,aAAA,GAAAI,CAAA;MAAA,IAAAsB,iBAAA;MAAA;MAAA1B,aAAA,GAAAG,CAAA;MACZ,QAAAuB,iBAAA,GAAO,IAAI,CAACX,WAAW;MAAA;MAAA,CAAAf,aAAA,GAAA2B,CAAA,UAAAD,iBAAA;MAAA;MAAA,CAAA1B,aAAA,GAAA2B,CAAA,UAAI,EAAE;IAC/B;EAAC;IAAAL,GAAA;IAAAC,KAAA,EAED,SAAAK,gBAAgBA,CAAA;MAAA;MAAA5B,aAAA,GAAAI,CAAA;MAAA,IAAAyB,qBAAA,EAAAC,iBAAA;MAAA;MAAA9B,aAAA,GAAAG,CAAA;MACd,QAAA0B,qBAAA,IAAAC,iBAAA,GAAO,IAAI,CAACpB,WAAW;MAAA;MAAA,CAAAV,aAAA,GAAA2B,CAAA;MAAA;MAAA,CAAA3B,aAAA,GAAA2B,CAAA,UAAhBG,iBAAA,CAAkBC,QAAQ,EAAE;MAAA;MAAA,CAAA/B,aAAA,GAAA2B,CAAA,UAAAE,qBAAA;MAAA;MAAA,CAAA7B,aAAA,GAAA2B,CAAA,UAAI,GAAG;IAC5C;EAAC;IAAAL,GAAA;IAAAC,KAAA,EACD,SAAAS,iBAAiBA,CAAA;MAAA;MAAAhC,aAAA,GAAAI,CAAA;MAAAJ,aAAA,GAAAG,CAAA;MACf,OAAO,UAAU;IACnB;EAAC;IAAAmB,GAAA;IAAAC,KAAA,EACD,SAAAU,iBAAiBA,CAAA;MAAA;MAAAjC,aAAA,GAAAI,CAAA;MAAAJ,aAAA,GAAAG,CAAA;MACf,OAAO,UAAU;IACnB;EAAC;IAAAmB,GAAA;IAAAC,KAAA,EAED,SAAAW,cAAcA,CAAA;MAAA;MAAAlC,aAAA,GAAAI,CAAA;MAAAJ,aAAA,GAAAG,CAAA;MACZ,OAAO,EAAE;IACX;EAAC;IAAAmB,GAAA;IAAAC,KAAA,EAED,SAAAY,WAAWA,CAACC,QAAgB;MAAA;MAAApC,aAAA,GAAAI,CAAA;IAAG;EAAC;IAAAkB,GAAA;IAAAC,KAAA,EAChC,SAAAc,KAAKA,CAAA;MAAA;MAAArC,aAAA,GAAAI,CAAA;MAAA,IAAAkC,QAAA;MAAA;MAAAtC,aAAA,GAAAG,CAAA;MACH,QAAAmC,QAAA,GAAO,IAAI,CAACjC,EAAE;MAAA;MAAA,CAAAL,aAAA,GAAA2B,CAAA,UAAAW,QAAA;MAAA;MAAA,CAAAtC,aAAA,GAAA2B,CAAA,UAAI,EAAE;IACtB;EAAC;IAAAL,GAAA;IAAAC,KAAA,EAED,SAAAgB,eAAeA,CAAA;MAAA;MAAAvC,aAAA,GAAAI,CAAA;MAAA,IAAAoC,kBAAA;MAAA;MAAAxC,aAAA,GAAAG,CAAA;MACb,QAAAqC,kBAAA,GAAO,IAAI,CAAC/B,YAAY;MAAA;MAAA,CAAAT,aAAA,GAAA2B,CAAA,UAAAa,kBAAA;MAAA;MAAA,CAAAxC,aAAA,GAAA2B,CAAA,UAAI,EAAE;IAChC;EAAC;IAAAL,GAAA;IAAAC,KAAA,EACD,SAAAkB,WAAWA,CAAA;MAAA;MAAAzC,aAAA,GAAAI,CAAA;MAAA,IAAAsC,cAAA;MAAA;MAAA1C,aAAA,GAAAG,CAAA;MACT,QAAAuC,cAAA,GAAO,IAAI,CAACnC,QAAQ;MAAA;MAAA,CAAAP,aAAA,GAAA2B,CAAA,UAAAe,cAAA;MAAA;MAAA,CAAA1C,aAAA,GAAA2B,CAAA,UAAI,EAAE;IAC5B;EAAC;IAAAL,GAAA;IAAAC,KAAA,EAED,SAAAoB,aAAaA,CAAA;MAAA;MAAA3C,aAAA,GAAAI,CAAA;MAAA,IAAAwC,gBAAA;MAAA;MAAA5C,aAAA,GAAAG,CAAA;MACX,QAAAyC,gBAAA,GAAO,IAAI,CAACpC,UAAU;MAAA;MAAA,CAAAR,aAAA,GAAA2B,CAAA,UAAAiB,gBAAA;MAAA;MAAA,CAAA5C,aAAA,GAAA2B,CAAA,UAAI,EAAE;IAC9B;EAAC;IAAAL,GAAA;IAAAC,KAAA,EACD,SAAAsB,OAAOA,CAAA;MAAA;MAAA7C,aAAA,GAAAI,CAAA;MAAAJ,aAAA,GAAAG,CAAA;MACL,OAAO,EAAE;IACX;EAAC;IAAAmB,GAAA;IAAAC,KAAA,EACD,SAAAuB,MAAMA,CAAA;MAAA;MAAA9C,aAAA,GAAAI,CAAA;MAAAJ,aAAA,GAAAG,CAAA;MACJ,OAAO,KAAK;IACd;EAAC;IAAAmB,GAAA;IAAAC,KAAA,EACD,SAAAwB,OAAOA,CAAA;MAAA;MAAA/C,aAAA,GAAAI,CAAA;MAAAJ,aAAA,GAAAG,CAAA;MACL,OAAO,EAAE;IACX;EAAC;IAAAmB,GAAA;IAAAC,KAAA,EACD,SAAAyB,gBAAgBA,CAAA;MAAA;MAAAhD,aAAA,GAAAI,CAAA;MAAAJ,aAAA,GAAAG,CAAA;MACd,OAAO,EAAE;IACX;EAAC;IAAAmB,GAAA;IAAAC,KAAA,EACD,SAAA0B,UAAUA,CAAA;MAAA;MAAAjD,aAAA,GAAAI,CAAA;MAAAJ,aAAA,GAAAG,CAAA;MACR,OAAO,KAAK;IACd;EAAC;IAAAmB,GAAA;IAAAC,KAAA,EACD,SAAA2B,WAAWA,CAAA;MAAA;MAAAlD,aAAA,GAAAI,CAAA;MAAA,IAAA+C,cAAA;MAAA;MAAAnD,aAAA,GAAAG,CAAA;MACT,QAAAgD,cAAA,GAAO,IAAI,CAAC7C,QAAQ;MAAA;MAAA,CAAAN,aAAA,GAAA2B,CAAA,UAAAwB,cAAA;MAAA;MAAA,CAAAnD,aAAA,GAAA2B,CAAA,UAAI,EAAE;IAC5B;EAAC;IAAAL,GAAA;IAAAC,KAAA,EAED,SAAA6B,eAAeA,CAAA;MAAA;MAAApD,aAAA,GAAAI,CAAA;MAAAJ,aAAA,GAAAG,CAAA;MACb,OAAO,EAAE;IACX;EAAC;AAAA;AAAA;AAAAH,aAAA,GAAAG,CAAA;AA7EHkD,OAAA,CAAAnD,2BAAA,GAAAA,2BAAA", "ignoreList": []}