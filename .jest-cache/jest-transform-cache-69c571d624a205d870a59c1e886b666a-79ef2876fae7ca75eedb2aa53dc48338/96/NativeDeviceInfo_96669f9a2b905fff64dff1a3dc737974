4e85030d9e40ac039184014842945c25
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var TurboModuleRegistry = _interopRequireWildcard(require("../../../../Libraries/TurboModule/TurboModuleRegistry"));
function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
var NativeModule = TurboModuleRegistry.getEnforcing('DeviceInfo');
var constants = null;
var NativeDeviceInfo = {
  getConstants: function getConstants() {
    if (constants == null) {
      constants = NativeModule.getConstants();
    }
    return constants;
  }
};
var _default = exports.default = NativeDeviceInfo;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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