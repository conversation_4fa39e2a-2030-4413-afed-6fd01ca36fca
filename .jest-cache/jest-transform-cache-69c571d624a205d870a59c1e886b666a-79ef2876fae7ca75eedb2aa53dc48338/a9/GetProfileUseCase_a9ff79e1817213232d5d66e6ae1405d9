1e7760268ef2bdff41819c358fa036a0
"use strict";

/* istanbul ignore next */
function cov_vnsmzgqqm() {
  var path = "/Users/<USER>/projects/react-native/payment-module/src/domain/usecases/customer/GetProfileUseCase.ts";
  var hash = "b4d2db277cf2feb236e22dfecfdd0835f2ebf5c5";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/projects/react-native/payment-module/src/domain/usecases/customer/GetProfileUseCase.ts",
    statementMap: {
      "0": {
        start: {
          line: 3,
          column: 29
        },
        end: {
          line: 3,
          column: 84
        }
      },
      "1": {
        start: {
          line: 4,
          column: 25
        },
        end: {
          line: 4,
          column: 99
        }
      },
      "2": {
        start: {
          line: 5,
          column: 23
        },
        end: {
          line: 5,
          column: 95
        }
      },
      "3": {
        start: {
          line: 6,
          column: 20
        },
        end: {
          line: 6,
          column: 89
        }
      },
      "4": {
        start: {
          line: 7,
          column: 0
        },
        end: {
          line: 9,
          column: 3
        }
      },
      "5": {
        start: {
          line: 10,
          column: 0
        },
        end: {
          line: 10,
          column: 35
        }
      },
      "6": {
        start: {
          line: 11,
          column: 26
        },
        end: {
          line: 11,
          column: 69
        }
      },
      "7": {
        start: {
          line: 12,
          column: 24
        },
        end: {
          line: 32,
          column: 3
        }
      },
      "8": {
        start: {
          line: 14,
          column: 4
        },
        end: {
          line: 14,
          column: 59
        }
      },
      "9": {
        start: {
          line: 15,
          column: 4
        },
        end: {
          line: 15,
          column: 33
        }
      },
      "10": {
        start: {
          line: 17,
          column: 2
        },
        end: {
          line: 31,
          column: 6
        }
      },
      "11": {
        start: {
          line: 20,
          column: 21
        },
        end: {
          line: 25,
          column: 8
        }
      },
      "12": {
        start: {
          line: 21,
          column: 20
        },
        end: {
          line: 21,
          column: 24
        }
      },
      "13": {
        start: {
          line: 22,
          column: 8
        },
        end: {
          line: 24,
          column: 11
        }
      },
      "14": {
        start: {
          line: 23,
          column: 10
        },
        end: {
          line: 23,
          column: 47
        }
      },
      "15": {
        start: {
          line: 27,
          column: 8
        },
        end: {
          line: 27,
          column: 47
        }
      },
      "16": {
        start: {
          line: 29,
          column: 6
        },
        end: {
          line: 29,
          column: 21
        }
      },
      "17": {
        start: {
          line: 33,
          column: 0
        },
        end: {
          line: 33,
          column: 46
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 12,
            column: 24
          },
          end: {
            line: 12,
            column: 25
          }
        },
        loc: {
          start: {
            line: 12,
            column: 36
          },
          end: {
            line: 32,
            column: 1
          }
        },
        line: 12
      },
      "1": {
        name: "GetProfileUseCase",
        decl: {
          start: {
            line: 13,
            column: 11
          },
          end: {
            line: 13,
            column: 28
          }
        },
        loc: {
          start: {
            line: 13,
            column: 41
          },
          end: {
            line: 16,
            column: 3
          }
        },
        line: 13
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 19,
            column: 11
          },
          end: {
            line: 19,
            column: 12
          }
        },
        loc: {
          start: {
            line: 19,
            column: 23
          },
          end: {
            line: 30,
            column: 5
          }
        },
        line: 19
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 20,
            column: 53
          },
          end: {
            line: 20,
            column: 54
          }
        },
        loc: {
          start: {
            line: 20,
            column: 66
          },
          end: {
            line: 25,
            column: 7
          }
        },
        line: 20
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 22,
            column: 60
          },
          end: {
            line: 22,
            column: 61
          }
        },
        loc: {
          start: {
            line: 22,
            column: 72
          },
          end: {
            line: 24,
            column: 9
          }
        },
        line: 22
      },
      "5": {
        name: "execute",
        decl: {
          start: {
            line: 26,
            column: 15
          },
          end: {
            line: 26,
            column: 22
          }
        },
        loc: {
          start: {
            line: 26,
            column: 25
          },
          end: {
            line: 28,
            column: 7
          }
        },
        line: 26
      }
    },
    branchMap: {},
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0
    },
    b: {},
    inputSourceMap: {
      version: 3,
      file: undefined,
      names: ["ExcecutionHandler_1", "require", "GetProfileUseCase", "repository", "_classCallCheck2", "default", "_createClass2", "key", "value", "_execute", "_asyncToGenerator2", "_this", "ExecutionHandler", "execute", "getProfile", "apply", "arguments", "exports"],
      sourceRoot: undefined,
      sources: ["/Users/<USER>/projects/react-native/payment-module/src/domain/usecases/customer/GetProfileUseCase.ts"],
      sourcesContent: ["import {ICustomerRepository} from '../../repositories/ICustomerRepository';\nimport {GetProfileModel} from '../../entities/get-profile/GetProfileModel';\nimport {ResultState} from '../../../core/ResultState';\nimport {ExecutionHandler} from '../../../utils/ExcecutionHandler';\nexport class GetProfileUseCase {\n  private repository: ICustomerRepository;\n\n  constructor(repository: ICustomerRepository) {\n    this.repository = repository;\n  }\n\n  public async execute(): Promise<ResultState<GetProfileModel>> {\n    // call this.repository.getProfile(...)\n    return ExecutionHandler.execute(() => this.repository.getProfile());\n  }\n}\n"],
      mappings: ";;;;;;;;;;AAGA,IAAAA,mBAAA,GAAAC,OAAA;AAAkE,IACrDC,iBAAiB;EAG5B,SAAAA,kBAAYC,UAA+B;IAAA,IAAAC,gBAAA,CAAAC,OAAA,QAAAH,iBAAA;IACzC,IAAI,CAACC,UAAU,GAAGA,UAAU;EAC9B;EAAC,WAAAG,aAAA,CAAAD,OAAA,EAAAH,iBAAA;IAAAK,GAAA;IAAAC,KAAA;MAAA,IAAAC,QAAA,OAAAC,kBAAA,CAAAL,OAAA,EAEM,aAAa;QAAA,IAAAM,KAAA;QAElB,OAAOX,mBAAA,CAAAY,gBAAgB,CAACC,OAAO,CAAC;UAAA,OAAMF,KAAI,CAACR,UAAU,CAACW,UAAU,EAAE;QAAA,EAAC;MACrE,CAAC;MAAA,SAHYD,OAAOA,CAAA;QAAA,OAAAJ,QAAA,CAAAM,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAPH,OAAO;IAAA;EAAA;AAAA;AAPtBI,OAAA,CAAAf,iBAAA,GAAAA,iBAAA",
      ignoreList: []
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "b4d2db277cf2feb236e22dfecfdd0835f2ebf5c5"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_vnsmzgqqm = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_vnsmzgqqm();
var _interopRequireDefault =
/* istanbul ignore next */
(cov_vnsmzgqqm().s[0]++, require("@babel/runtime/helpers/interopRequireDefault"));
var _asyncToGenerator2 =
/* istanbul ignore next */
(cov_vnsmzgqqm().s[1]++, _interopRequireDefault(require("@babel/runtime/helpers/asyncToGenerator")));
var _classCallCheck2 =
/* istanbul ignore next */
(cov_vnsmzgqqm().s[2]++, _interopRequireDefault(require("@babel/runtime/helpers/classCallCheck")));
var _createClass2 =
/* istanbul ignore next */
(cov_vnsmzgqqm().s[3]++, _interopRequireDefault(require("@babel/runtime/helpers/createClass")));
/* istanbul ignore next */
cov_vnsmzgqqm().s[4]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_vnsmzgqqm().s[5]++;
exports.GetProfileUseCase = void 0;
var ExcecutionHandler_1 =
/* istanbul ignore next */
(cov_vnsmzgqqm().s[6]++, require("../../../utils/ExcecutionHandler"));
var GetProfileUseCase =
/* istanbul ignore next */
(cov_vnsmzgqqm().s[7]++, function () {
  /* istanbul ignore next */
  cov_vnsmzgqqm().f[0]++;
  function GetProfileUseCase(repository) {
    /* istanbul ignore next */
    cov_vnsmzgqqm().f[1]++;
    cov_vnsmzgqqm().s[8]++;
    (0, _classCallCheck2.default)(this, GetProfileUseCase);
    /* istanbul ignore next */
    cov_vnsmzgqqm().s[9]++;
    this.repository = repository;
  }
  /* istanbul ignore next */
  cov_vnsmzgqqm().s[10]++;
  return (0, _createClass2.default)(GetProfileUseCase, [{
    key: "execute",
    value: function () {
      /* istanbul ignore next */
      cov_vnsmzgqqm().f[2]++;
      var _execute =
      /* istanbul ignore next */
      (cov_vnsmzgqqm().s[11]++, (0, _asyncToGenerator2.default)(function* () {
        /* istanbul ignore next */
        cov_vnsmzgqqm().f[3]++;
        var _this =
        /* istanbul ignore next */
        (cov_vnsmzgqqm().s[12]++, this);
        /* istanbul ignore next */
        cov_vnsmzgqqm().s[13]++;
        return ExcecutionHandler_1.ExecutionHandler.execute(function () {
          /* istanbul ignore next */
          cov_vnsmzgqqm().f[4]++;
          cov_vnsmzgqqm().s[14]++;
          return _this.repository.getProfile();
        });
      }));
      function execute() {
        /* istanbul ignore next */
        cov_vnsmzgqqm().f[5]++;
        cov_vnsmzgqqm().s[15]++;
        return _execute.apply(this, arguments);
      }
      /* istanbul ignore next */
      cov_vnsmzgqqm().s[16]++;
      return execute;
    }()
  }]);
}());
/* istanbul ignore next */
cov_vnsmzgqqm().s[17]++;
exports.GetProfileUseCase = GetProfileUseCase;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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