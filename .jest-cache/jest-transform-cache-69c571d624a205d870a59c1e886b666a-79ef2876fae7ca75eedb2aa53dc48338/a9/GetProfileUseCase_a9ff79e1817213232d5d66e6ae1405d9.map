{"version": 3, "names": ["cov_vnsmzgqqm", "actualCoverage", "ExcecutionHandler_1", "s", "require", "GetProfileUseCase", "f", "repository", "_classCallCheck2", "default", "_createClass2", "key", "value", "_execute", "_asyncToGenerator2", "_this", "ExecutionHandler", "execute", "getProfile", "apply", "arguments", "exports"], "sources": ["/Users/<USER>/projects/react-native/payment-module/src/domain/usecases/customer/GetProfileUseCase.ts"], "sourcesContent": ["import {ICustomerRepository} from '../../repositories/ICustomerRepository';\nimport {GetProfileModel} from '../../entities/get-profile/GetProfileModel';\nimport {ResultState} from '../../../core/ResultState';\nimport {ExecutionHandler} from '../../../utils/ExcecutionHandler';\nexport class GetProfileUseCase {\n  private repository: ICustomerRepository;\n\n  constructor(repository: ICustomerRepository) {\n    this.repository = repository;\n  }\n\n  public async execute(): Promise<ResultState<GetProfileModel>> {\n    // call this.repository.getProfile(...)\n    return ExecutionHandler.execute(() => this.repository.getProfile());\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IASE;IAAAA,aAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,aAAA;;;;;;;;;;;;;;;;;;;;;AANF,IAAAE,mBAAA;AAAA;AAAA,CAAAF,aAAA,GAAAG,CAAA,OAAAC,OAAA;AAAkE,IACrDC,iBAAiB;AAAA;AAAA,CAAAL,aAAA,GAAAG,CAAA;EAAA;EAAAH,aAAA,GAAAM,CAAA;EAG5B,SAAAD,kBAAYE,UAA+B;IAAA;IAAAP,aAAA,GAAAM,CAAA;IAAAN,aAAA,GAAAG,CAAA;IAAA,IAAAK,gBAAA,CAAAC,OAAA,QAAAJ,iBAAA;IAAA;IAAAL,aAAA,GAAAG,CAAA;IACzC,IAAI,CAACI,UAAU,GAAGA,UAAU;EAC9B;EAAA;EAAAP,aAAA,GAAAG,CAAA;EAAC,WAAAO,aAAA,CAAAD,OAAA,EAAAJ,iBAAA;IAAAM,GAAA;IAAAC,KAAA;MAAA;MAAAZ,aAAA,GAAAM,CAAA;MAAA,IAAAO,QAAA;MAAA;MAAA,CAAAb,aAAA,GAAAG,CAAA,YAAAW,kBAAA,CAAAL,OAAA,EAEM,aAAa;QAAA;QAAAT,aAAA,GAAAM,CAAA;QAAA,IAAAS,KAAA;QAAA;QAAA,CAAAf,aAAA,GAAAG,CAAA;QAAA;QAAAH,aAAA,GAAAG,CAAA;QAElB,OAAOD,mBAAA,CAAAc,gBAAgB,CAACC,OAAO,CAAC;UAAA;UAAAjB,aAAA,GAAAM,CAAA;UAAAN,aAAA,GAAAG,CAAA;UAAA,OAAMY,KAAI,CAACR,UAAU,CAACW,UAAU,EAAE;QAAA,EAAC;MACrE,CAAC;MAAA,SAHYD,OAAOA,CAAA;QAAA;QAAAjB,aAAA,GAAAM,CAAA;QAAAN,aAAA,GAAAG,CAAA;QAAA,OAAAU,QAAA,CAAAM,KAAA,OAAAC,SAAA;MAAA;MAAA;MAAApB,aAAA,GAAAG,CAAA;MAAA,OAAPc,OAAO;IAAA;EAAA;AAAA;AAAA;AAAAjB,aAAA,GAAAG,CAAA;AAPtBkB,OAAA,CAAAhB,iBAAA,GAAAA,iBAAA", "ignoreList": []}