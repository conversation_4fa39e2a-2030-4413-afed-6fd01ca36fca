a0ede37b7950b4e23e8ef65b85604fad
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _classCallCheck2 = _interopRequireDefault(require("@babel/runtime/helpers/classCallCheck"));
var _createClass2 = _interopRequireDefault(require("@babel/runtime/helpers/createClass"));
var LogBoxSymbolication = _interopRequireWildcard(require("./LogBoxSymbolication"));
function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
function convertComponentStateToStack(componentStack) {
  return componentStack.map(function (frame) {
    var _frame$location, _frame$location2;
    return {
      column: frame == null || (_frame$location = frame.location) == null ? void 0 : _frame$location.column,
      file: frame.fileName,
      lineNumber: frame == null || (_frame$location2 = frame.location) == null ? void 0 : _frame$location2.row,
      methodName: frame.content,
      collapse: false
    };
  });
}
function convertStackToComponentStack(stack) {
  var componentStack = [];
  for (var i = 0; i < stack.length; i++) {
    var frame = stack[i];
    if (frame.lineNumber != null && frame.column != null) {
      componentStack.push({
        fileName: (frame == null ? void 0 : frame.file) || '',
        location: {
          row: frame.lineNumber,
          column: frame.column
        },
        content: frame.methodName,
        collapse: false
      });
    }
  }
  return componentStack;
}
var LogBoxLog = function () {
  function LogBoxLog(data) {
    (0, _classCallCheck2.default)(this, LogBoxLog);
    this.symbolicated = {
      error: null,
      stack: null,
      status: 'NONE'
    };
    this.symbolicatedComponentStack = {
      error: null,
      componentStack: null,
      status: 'NONE'
    };
    this.level = data.level;
    this.type = data.type;
    this.message = data.message;
    this.stack = data.stack;
    this.category = data.category;
    this.componentStack = data.componentStack;
    this.componentStackType = data.componentStackType || 'legacy';
    this.codeFrame = data.codeFrame;
    this.isComponentError = data.isComponentError;
    this.extraData = data.extraData;
    this.count = 1;
    this.onNotificationPress = data.onNotificationPress;
  }
  return (0, _createClass2.default)(LogBoxLog, [{
    key: "incrementCount",
    value: function incrementCount() {
      this.count += 1;
    }
  }, {
    key: "getAvailableStack",
    value: function getAvailableStack() {
      return this.symbolicated.status === 'COMPLETE' ? this.symbolicated.stack : this.stack;
    }
  }, {
    key: "getAvailableComponentStack",
    value: function getAvailableComponentStack() {
      if (this.componentStackType === 'legacy') {
        return this.componentStack;
      }
      return this.symbolicatedComponentStack.status === 'COMPLETE' ? this.symbolicatedComponentStack.componentStack : this.componentStack;
    }
  }, {
    key: "retrySymbolicate",
    value: function retrySymbolicate(callback) {
      if (this.symbolicated.status !== 'COMPLETE') {
        LogBoxSymbolication.deleteStack(this.stack);
        this.handleSymbolicate(callback);
      }
    }
  }, {
    key: "symbolicate",
    value: function symbolicate(callback) {
      if (this.symbolicated.status === 'NONE') {
        this.handleSymbolicate(callback);
      }
    }
  }, {
    key: "handleSymbolicate",
    value: function handleSymbolicate(callback) {
      var _this = this;
      if (this.symbolicated.status !== 'PENDING') {
        this.updateStatus(null, null, null, callback);
        LogBoxSymbolication.symbolicate(this.stack, this.extraData).then(function (data) {
          _this.updateStatus(null, data == null ? void 0 : data.stack, data == null ? void 0 : data.codeFrame, callback);
        }, function (error) {
          _this.updateStatus(error, null, null, callback);
        });
        if (this.componentStack != null && this.componentStackType === 'stack') {
          this.updateComponentStackStatus(null, null, null, callback);
          var componentStackFrames = convertComponentStateToStack(this.componentStack);
          LogBoxSymbolication.symbolicate(componentStackFrames, []).then(function (data) {
            _this.updateComponentStackStatus(null, convertStackToComponentStack(data.stack), null, callback);
          }, function (error) {
            _this.updateComponentStackStatus(error, null, null, callback);
          });
        }
      }
    }
  }, {
    key: "updateStatus",
    value: function updateStatus(error, stack, codeFrame, callback) {
      var lastStatus = this.symbolicated.status;
      if (error != null) {
        this.symbolicated = {
          error: error,
          stack: null,
          status: 'FAILED'
        };
      } else if (stack != null) {
        if (codeFrame) {
          this.codeFrame = codeFrame;
        }
        this.symbolicated = {
          error: null,
          stack: stack,
          status: 'COMPLETE'
        };
      } else {
        this.symbolicated = {
          error: null,
          stack: null,
          status: 'PENDING'
        };
      }
      if (callback && lastStatus !== this.symbolicated.status) {
        callback(this.symbolicated.status);
      }
    }
  }, {
    key: "updateComponentStackStatus",
    value: function updateComponentStackStatus(error, componentStack, codeFrame, callback) {
      var lastStatus = this.symbolicatedComponentStack.status;
      if (error != null) {
        this.symbolicatedComponentStack = {
          error: error,
          componentStack: null,
          status: 'FAILED'
        };
      } else if (componentStack != null) {
        this.symbolicatedComponentStack = {
          error: null,
          componentStack: componentStack,
          status: 'COMPLETE'
        };
      } else {
        this.symbolicatedComponentStack = {
          error: null,
          componentStack: null,
          status: 'PENDING'
        };
      }
      if (callback && lastStatus !== this.symbolicatedComponentStack.status) {
        callback(this.symbolicatedComponentStack.status);
      }
    }
  }]);
}();
var _default = exports.default = LogBoxLog;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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