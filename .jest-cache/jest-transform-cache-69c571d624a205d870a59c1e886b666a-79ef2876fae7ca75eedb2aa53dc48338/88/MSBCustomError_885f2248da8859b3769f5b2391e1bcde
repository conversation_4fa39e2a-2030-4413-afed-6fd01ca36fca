869ac0b4837a77747369a4cbcc9b0a76
"use strict";

/* istanbul ignore next */
function cov_1blejgonk8() {
  var path = "/Users/<USER>/projects/react-native/payment-module/src/core/MSBCustomError.ts";
  var hash = "d7e9abeff253b4b37b097652063ffc88af869fcb";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/projects/react-native/payment-module/src/core/MSBCustomError.ts",
    statementMap: {
      "0": {
        start: {
          line: 2,
          column: 29
        },
        end: {
          line: 2,
          column: 84
        }
      },
      "1": {
        start: {
          line: 3,
          column: 23
        },
        end: {
          line: 3,
          column: 95
        }
      },
      "2": {
        start: {
          line: 4,
          column: 23
        },
        end: {
          line: 4,
          column: 95
        }
      },
      "3": {
        start: {
          line: 5,
          column: 20
        },
        end: {
          line: 5,
          column: 89
        }
      },
      "4": {
        start: {
          line: 6,
          column: 34
        },
        end: {
          line: 6,
          column: 117
        }
      },
      "5": {
        start: {
          line: 7,
          column: 23
        },
        end: {
          line: 7,
          column: 95
        }
      },
      "6": {
        start: {
          line: 8,
          column: 17
        },
        end: {
          line: 8,
          column: 83
        }
      },
      "7": {
        start: {
          line: 9,
          column: 24
        },
        end: {
          line: 9,
          column: 97
        }
      },
      "8": {
        start: {
          line: 10,
          column: 31
        },
        end: {
          line: 10,
          column: 243
        }
      },
      "9": {
        start: {
          line: 11,
          column: 39
        },
        end: {
          line: 11,
          column: 148
        }
      },
      "10": {
        start: {
          line: 11,
          column: 53
        },
        end: {
          line: 11,
          column: 132
        }
      },
      "11": {
        start: {
          line: 11,
          column: 149
        },
        end: {
          line: 11,
          column: 241
        }
      },
      "12": {
        start: {
          line: 11,
          column: 224
        },
        end: {
          line: 11,
          column: 235
        }
      },
      "13": {
        start: {
          line: 12,
          column: 0
        },
        end: {
          line: 14,
          column: 3
        }
      },
      "14": {
        start: {
          line: 15,
          column: 0
        },
        end: {
          line: 15,
          column: 173
        }
      },
      "15": {
        start: {
          line: 16,
          column: 21
        },
        end: {
          line: 16,
          column: 46
        }
      },
      "16": {
        start: {
          line: 18,
          column: 0
        },
        end: {
          line: 25,
          column: 66
        }
      },
      "17": {
        start: {
          line: 19,
          column: 2
        },
        end: {
          line: 19,
          column: 39
        }
      },
      "18": {
        start: {
          line: 20,
          column: 2
        },
        end: {
          line: 20,
          column: 31
        }
      },
      "19": {
        start: {
          line: 21,
          column: 2
        },
        end: {
          line: 21,
          column: 41
        }
      },
      "20": {
        start: {
          line: 22,
          column: 2
        },
        end: {
          line: 22,
          column: 45
        }
      },
      "21": {
        start: {
          line: 23,
          column: 2
        },
        end: {
          line: 23,
          column: 37
        }
      },
      "22": {
        start: {
          line: 24,
          column: 2
        },
        end: {
          line: 24,
          column: 39
        }
      },
      "23": {
        start: {
          line: 26,
          column: 18
        },
        end: {
          line: 56,
          column: 40
        }
      },
      "24": {
        start: {
          line: 29,
          column: 4
        },
        end: {
          line: 29,
          column: 53
        }
      },
      "25": {
        start: {
          line: 30,
          column: 4
        },
        end: {
          line: 30,
          column: 57
        }
      },
      "26": {
        start: {
          line: 31,
          column: 4
        },
        end: {
          line: 31,
          column: 31
        }
      },
      "27": {
        start: {
          line: 32,
          column: 4
        },
        end: {
          line: 32,
          column: 22
        }
      },
      "28": {
        start: {
          line: 33,
          column: 4
        },
        end: {
          line: 33,
          column: 30
        }
      },
      "29": {
        start: {
          line: 34,
          column: 4
        },
        end: {
          line: 34,
          column: 24
        }
      },
      "30": {
        start: {
          line: 35,
          column: 4
        },
        end: {
          line: 35,
          column: 36
        }
      },
      "31": {
        start: {
          line: 36,
          column: 4
        },
        end: {
          line: 36,
          column: 32
        }
      },
      "32": {
        start: {
          line: 37,
          column: 4
        },
        end: {
          line: 37,
          column: 34
        }
      },
      "33": {
        start: {
          line: 38,
          column: 4
        },
        end: {
          line: 38,
          column: 17
        }
      },
      "34": {
        start: {
          line: 40,
          column: 2
        },
        end: {
          line: 40,
          column: 47
        }
      },
      "35": {
        start: {
          line: 41,
          column: 2
        },
        end: {
          line: 55,
          column: 6
        }
      },
      "36": {
        start: {
          line: 44,
          column: 6
        },
        end: {
          line: 46,
          column: 36
        }
      },
      "37": {
        start: {
          line: 45,
          column: 8
        },
        end: {
          line: 45,
          column: 30
        }
      },
      "38": {
        start: {
          line: 51,
          column: 6
        },
        end: {
          line: 53,
          column: 36
        }
      },
      "39": {
        start: {
          line: 52,
          column: 8
        },
        end: {
          line: 52,
          column: 31
        }
      },
      "40": {
        start: {
          line: 57,
          column: 0
        },
        end: {
          line: 57,
          column: 34
        }
      },
      "41": {
        start: {
          line: 58,
          column: 23
        },
        end: {
          line: 88,
          column: 171
        }
      },
      "42": {
        start: {
          line: 89,
          column: 18
        },
        end: {
          line: 151,
          column: 3
        }
      },
      "43": {
        start: {
          line: 91,
          column: 4
        },
        end: {
          line: 91,
          column: 53
        }
      },
      "44": {
        start: {
          line: 93,
          column: 2
        },
        end: {
          line: 150,
          column: 6
        }
      },
      "45": {
        start: {
          line: 96,
          column: 22
        },
        end: {
          line: 96,
          column: 71
        }
      },
      "46": {
        start: {
          line: 97,
          column: 28
        },
        end: {
          line: 97,
          column: 118
        }
      },
      "47": {
        start: {
          line: 98,
          column: 6
        },
        end: {
          line: 98,
          column: 170
        }
      },
      "48": {
        start: {
          line: 103,
          column: 6
        },
        end: {
          line: 105,
          column: 7
        }
      },
      "49": {
        start: {
          line: 104,
          column: 8
        },
        end: {
          line: 104,
          column: 20
        }
      },
      "50": {
        start: {
          line: 106,
          column: 23
        },
        end: {
          line: 106,
          column: 41
        }
      },
      "51": {
        start: {
          line: 107,
          column: 22
        },
        end: {
          line: 107,
          column: 81
        }
      },
      "52": {
        start: {
          line: 108,
          column: 22
        },
        end: {
          line: 108,
          column: 56
        }
      },
      "53": {
        start: {
          line: 109,
          column: 24
        },
        end: {
          line: 109,
          column: 45
        }
      },
      "54": {
        start: {
          line: 110,
          column: 6
        },
        end: {
          line: 112,
          column: 7
        }
      },
      "55": {
        start: {
          line: 111,
          column: 8
        },
        end: {
          line: 111,
          column: 84
        }
      },
      "56": {
        start: {
          line: 113,
          column: 6
        },
        end: {
          line: 113,
          column: 135
        }
      },
      "57": {
        start: {
          line: 118,
          column: 18
        },
        end: {
          line: 118,
          column: 47
        }
      },
      "58": {
        start: {
          line: 119,
          column: 6
        },
        end: {
          line: 119,
          column: 29
        }
      },
      "59": {
        start: {
          line: 124,
          column: 18
        },
        end: {
          line: 124,
          column: 47
        }
      },
      "60": {
        start: {
          line: 125,
          column: 6
        },
        end: {
          line: 125,
          column: 28
        }
      },
      "61": {
        start: {
          line: 130,
          column: 6
        },
        end: {
          line: 130,
          column: 83
        }
      },
      "62": {
        start: {
          line: 135,
          column: 6
        },
        end: {
          line: 137,
          column: 7
        }
      },
      "63": {
        start: {
          line: 136,
          column: 8
        },
        end: {
          line: 136,
          column: 20
        }
      },
      "64": {
        start: {
          line: 138,
          column: 6
        },
        end: {
          line: 138,
          column: 32
        }
      },
      "65": {
        start: {
          line: 143,
          column: 6
        },
        end: {
          line: 143,
          column: 37
        }
      },
      "66": {
        start: {
          line: 148,
          column: 6
        },
        end: {
          line: 148,
          column: 43
        }
      },
      "67": {
        start: {
          line: 152,
          column: 0
        },
        end: {
          line: 152,
          column: 34
        }
      },
      "68": {
        start: {
          line: 153,
          column: 0
        },
        end: {
          line: 153,
          column: 46
        }
      },
      "69": {
        start: {
          line: 154,
          column: 0
        },
        end: {
          line: 154,
          column: 72
        }
      },
      "70": {
        start: {
          line: 155,
          column: 0
        },
        end: {
          line: 155,
          column: 46
        }
      },
      "71": {
        start: {
          line: 156,
          column: 0
        },
        end: {
          line: 156,
          column: 40
        }
      }
    },
    fnMap: {
      "0": {
        name: "_callSuper",
        decl: {
          start: {
            line: 10,
            column: 9
          },
          end: {
            line: 10,
            column: 19
          }
        },
        loc: {
          start: {
            line: 10,
            column: 29
          },
          end: {
            line: 10,
            column: 245
          }
        },
        line: 10
      },
      "1": {
        name: "_isNativeReflectConstruct",
        decl: {
          start: {
            line: 11,
            column: 9
          },
          end: {
            line: 11,
            column: 34
          }
        },
        loc: {
          start: {
            line: 11,
            column: 37
          },
          end: {
            line: 11,
            column: 243
          }
        },
        line: 11
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 11,
            column: 116
          },
          end: {
            line: 11,
            column: 117
          }
        },
        loc: {
          start: {
            line: 11,
            column: 128
          },
          end: {
            line: 11,
            column: 130
          }
        },
        line: 11
      },
      "3": {
        name: "_isNativeReflectConstruct",
        decl: {
          start: {
            line: 11,
            column: 194
          },
          end: {
            line: 11,
            column: 219
          }
        },
        loc: {
          start: {
            line: 11,
            column: 222
          },
          end: {
            line: 11,
            column: 237
          }
        },
        line: 11
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 18,
            column: 1
          },
          end: {
            line: 18,
            column: 2
          }
        },
        loc: {
          start: {
            line: 18,
            column: 26
          },
          end: {
            line: 25,
            column: 1
          }
        },
        line: 18
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 26,
            column: 18
          },
          end: {
            line: 26,
            column: 19
          }
        },
        loc: {
          start: {
            line: 26,
            column: 36
          },
          end: {
            line: 56,
            column: 1
          }
        },
        line: 26
      },
      "6": {
        name: "CustomError",
        decl: {
          start: {
            line: 27,
            column: 11
          },
          end: {
            line: 27,
            column: 22
          }
        },
        loc: {
          start: {
            line: 27,
            column: 79
          },
          end: {
            line: 39,
            column: 3
          }
        },
        line: 27
      },
      "7": {
        name: "getPrimaryAction",
        decl: {
          start: {
            line: 43,
            column: 20
          },
          end: {
            line: 43,
            column: 36
          }
        },
        loc: {
          start: {
            line: 43,
            column: 39
          },
          end: {
            line: 47,
            column: 5
          }
        },
        line: 43
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 44,
            column: 31
          },
          end: {
            line: 44,
            column: 32
          }
        },
        loc: {
          start: {
            line: 44,
            column: 49
          },
          end: {
            line: 46,
            column: 7
          }
        },
        line: 44
      },
      "9": {
        name: "getSecondaryAction",
        decl: {
          start: {
            line: 50,
            column: 20
          },
          end: {
            line: 50,
            column: 38
          }
        },
        loc: {
          start: {
            line: 50,
            column: 41
          },
          end: {
            line: 54,
            column: 5
          }
        },
        line: 50
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 51,
            column: 31
          },
          end: {
            line: 51,
            column: 32
          }
        },
        loc: {
          start: {
            line: 51,
            column: 49
          },
          end: {
            line: 53,
            column: 7
          }
        },
        line: 51
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 89,
            column: 18
          },
          end: {
            line: 89,
            column: 19
          }
        },
        loc: {
          start: {
            line: 89,
            column: 30
          },
          end: {
            line: 151,
            column: 1
          }
        },
        line: 89
      },
      "12": {
        name: "ErrorMapper",
        decl: {
          start: {
            line: 90,
            column: 11
          },
          end: {
            line: 90,
            column: 22
          }
        },
        loc: {
          start: {
            line: 90,
            column: 25
          },
          end: {
            line: 92,
            column: 3
          }
        },
        line: 90
      },
      "13": {
        name: "createError",
        decl: {
          start: {
            line: 95,
            column: 20
          },
          end: {
            line: 95,
            column: 31
          }
        },
        loc: {
          start: {
            line: 95,
            column: 38
          },
          end: {
            line: 99,
            column: 5
          }
        },
        line: 95
      },
      "14": {
        name: "extractErrorFromResponse",
        decl: {
          start: {
            line: 102,
            column: 20
          },
          end: {
            line: 102,
            column: 44
          }
        },
        loc: {
          start: {
            line: 102,
            column: 55
          },
          end: {
            line: 114,
            column: 5
          }
        },
        line: 102
      },
      "15": {
        name: "isRetryable",
        decl: {
          start: {
            line: 117,
            column: 20
          },
          end: {
            line: 117,
            column: 31
          }
        },
        loc: {
          start: {
            line: 117,
            column: 38
          },
          end: {
            line: 120,
            column: 5
          }
        },
        line: 117
      },
      "16": {
        name: "getCategory",
        decl: {
          start: {
            line: 123,
            column: 20
          },
          end: {
            line: 123,
            column: 31
          }
        },
        loc: {
          start: {
            line: 123,
            column: 38
          },
          end: {
            line: 126,
            column: 5
          }
        },
        line: 123
      },
      "17": {
        name: "hasError",
        decl: {
          start: {
            line: 129,
            column: 20
          },
          end: {
            line: 129,
            column: 28
          }
        },
        loc: {
          start: {
            line: 129,
            column: 39
          },
          end: {
            line: 131,
            column: 5
          }
        },
        line: 129
      },
      "18": {
        name: "getFirstError",
        decl: {
          start: {
            line: 134,
            column: 20
          },
          end: {
            line: 134,
            column: 33
          }
        },
        loc: {
          start: {
            line: 134,
            column: 44
          },
          end: {
            line: 139,
            column: 5
          }
        },
        line: 134
      },
      "19": {
        name: "addPredefinedError",
        decl: {
          start: {
            line: 142,
            column: 20
          },
          end: {
            line: 142,
            column: 38
          }
        },
        loc: {
          start: {
            line: 142,
            column: 52
          },
          end: {
            line: 144,
            column: 5
          }
        },
        line: 142
      },
      "20": {
        name: "getAvailableErrorCodes",
        decl: {
          start: {
            line: 147,
            column: 20
          },
          end: {
            line: 147,
            column: 42
          }
        },
        loc: {
          start: {
            line: 147,
            column: 45
          },
          end: {
            line: 149,
            column: 5
          }
        },
        line: 147
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 10,
            column: 120
          },
          end: {
            line: 10,
            column: 241
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 10,
            column: 150
          },
          end: {
            line: 10,
            column: 225
          }
        }, {
          start: {
            line: 10,
            column: 228
          },
          end: {
            line: 10,
            column: 241
          }
        }],
        line: 10
      },
      "1": {
        loc: {
          start: {
            line: 10,
            column: 171
          },
          end: {
            line: 10,
            column: 178
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 10,
            column: 171
          },
          end: {
            line: 10,
            column: 172
          }
        }, {
          start: {
            line: 10,
            column: 176
          },
          end: {
            line: 10,
            column: 178
          }
        }],
        line: 10
      },
      "2": {
        loc: {
          start: {
            line: 25,
            column: 3
          },
          end: {
            line: 25,
            column: 64
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 25,
            column: 3
          },
          end: {
            line: 25,
            column: 16
          }
        }, {
          start: {
            line: 25,
            column: 21
          },
          end: {
            line: 25,
            column: 63
          }
        }],
        line: 25
      },
      "3": {
        loc: {
          start: {
            line: 37,
            column: 20
          },
          end: {
            line: 37,
            column: 33
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 37,
            column: 20
          },
          end: {
            line: 37,
            column: 27
          }
        }, {
          start: {
            line: 37,
            column: 31
          },
          end: {
            line: 37,
            column: 33
          }
        }],
        line: 37
      },
      "4": {
        loc: {
          start: {
            line: 44,
            column: 13
          },
          end: {
            line: 46,
            column: 35
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 44,
            column: 13
          },
          end: {
            line: 46,
            column: 8
          }
        }, {
          start: {
            line: 46,
            column: 12
          },
          end: {
            line: 46,
            column: 27
          }
        }, {
          start: {
            line: 46,
            column: 31
          },
          end: {
            line: 46,
            column: 35
          }
        }],
        line: 44
      },
      "5": {
        loc: {
          start: {
            line: 51,
            column: 13
          },
          end: {
            line: 53,
            column: 35
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 51,
            column: 13
          },
          end: {
            line: 53,
            column: 8
          }
        }, {
          start: {
            line: 53,
            column: 12
          },
          end: {
            line: 53,
            column: 27
          }
        }, {
          start: {
            line: 53,
            column: 31
          },
          end: {
            line: 53,
            column: 35
          }
        }],
        line: 51
      },
      "6": {
        loc: {
          start: {
            line: 96,
            column: 22
          },
          end: {
            line: 96,
            column: 71
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 96,
            column: 22
          },
          end: {
            line: 96,
            column: 26
          }
        }, {
          start: {
            line: 96,
            column: 30
          },
          end: {
            line: 96,
            column: 71
          }
        }],
        line: 96
      },
      "7": {
        loc: {
          start: {
            line: 97,
            column: 28
          },
          end: {
            line: 97,
            column: 118
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 97,
            column: 28
          },
          end: {
            line: 97,
            column: 55
          }
        }, {
          start: {
            line: 97,
            column: 59
          },
          end: {
            line: 97,
            column: 118
          }
        }],
        line: 97
      },
      "8": {
        loc: {
          start: {
            line: 103,
            column: 6
          },
          end: {
            line: 105,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 103,
            column: 6
          },
          end: {
            line: 105,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 103
      },
      "9": {
        loc: {
          start: {
            line: 103,
            column: 10
          },
          end: {
            line: 103,
            column: 79
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 103,
            column: 10
          },
          end: {
            line: 103,
            column: 48
          }
        }, {
          start: {
            line: 103,
            column: 52
          },
          end: {
            line: 103,
            column: 79
          }
        }],
        line: 103
      },
      "10": {
        loc: {
          start: {
            line: 103,
            column: 12
          },
          end: {
            line: 103,
            column: 47
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 103,
            column: 12
          },
          end: {
            line: 103,
            column: 28
          }
        }, {
          start: {
            line: 103,
            column: 32
          },
          end: {
            line: 103,
            column: 47
          }
        }],
        line: 103
      },
      "11": {
        loc: {
          start: {
            line: 107,
            column: 22
          },
          end: {
            line: 107,
            column: 81
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 107,
            column: 22
          },
          end: {
            line: 107,
            column: 36
          }
        }, {
          start: {
            line: 107,
            column: 40
          },
          end: {
            line: 107,
            column: 81
          }
        }],
        line: 107
      },
      "12": {
        loc: {
          start: {
            line: 110,
            column: 6
          },
          end: {
            line: 112,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 110,
            column: 6
          },
          end: {
            line: 112,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 110
      },
      "13": {
        loc: {
          start: {
            line: 111,
            column: 22
          },
          end: {
            line: 111,
            column: 83
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 111,
            column: 22
          },
          end: {
            line: 111,
            column: 43
          }
        }, {
          start: {
            line: 111,
            column: 47
          },
          end: {
            line: 111,
            column: 68
          }
        }, {
          start: {
            line: 111,
            column: 72
          },
          end: {
            line: 111,
            column: 83
          }
        }],
        line: 111
      },
      "14": {
        loc: {
          start: {
            line: 130,
            column: 16
          },
          end: {
            line: 130,
            column: 81
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 130,
            column: 16
          },
          end: {
            line: 130,
            column: 32
          }
        }, {
          start: {
            line: 130,
            column: 36
          },
          end: {
            line: 130,
            column: 51
          }
        }, {
          start: {
            line: 130,
            column: 55
          },
          end: {
            line: 130,
            column: 81
          }
        }],
        line: 130
      },
      "15": {
        loc: {
          start: {
            line: 135,
            column: 6
          },
          end: {
            line: 137,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 135,
            column: 6
          },
          end: {
            line: 137,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 135
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0
    },
    b: {
      "0": [0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0, 0],
      "5": [0, 0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0, 0],
      "14": [0, 0, 0],
      "15": [0, 0]
    },
    inputSourceMap: {
      version: 3,
      file: undefined,
      names: ["MSBErrorCode_1", "require", "ErrorCategory", "exports", "CustomError", "_Error", "code", "category", "title", "userMessage", "retryable", "actions", "_this", "_classCallCheck2", "default", "_callSuper", "name", "_inherits2", "_createClass2", "key", "value", "getPrimaryAction", "find", "action", "primary", "getSecondaryAction", "_wrapNativeSuper2", "Error", "PredefinedErrors", "_defineProperty2", "MSBErrorCode", "UNKNOWN_ERROR", "UNKNOWN", "type", "label", "PIS0101", "BUSINESS", "PIS0106", "NETWORK", "PIS0103", "VALIDATION", "A05", "SYSTEM", "FTES0008", "EMPTY_DATA", "API", "ErrorMapper", "createError", "errorCode", "predefinedError", "extractErrorFromResponse", "response", "errors", "length", "firstError", "baseError", "context", "vi", "en", "isRetryable", "error", "getCategory", "hasError", "getFirstError", "addPredefinedError", "getAvailableErrorCodes", "Object", "keys"],
      sourceRoot: undefined,
      sources: ["/Users/<USER>/projects/react-native/payment-module/src/core/MSBCustomError.ts"],
      sourcesContent: ["/**\n * Error System - Single source of truth cho error handling\n * CustomError + ErrorMapper + ActionHandlers\n */\n\nimport {MSBErrorCode} from './MSBErrorCode';\nimport {BaseResponse, MSBError} from './BaseResponse';\n\n/**\n * Error categories\n */\nexport enum ErrorCategory {\n  NETWORK = 'NETWORK',\n  API = 'API',\n  BUSINESS = 'BUSINESS',\n  VALIDATION = 'VALIDATION',\n  SYSTEM = 'SYSTEM',\n  UNKNOWN = 'UNKNOWN',\n}\n\n/**\n * Action definition - \u0111\u01A1n gi\u1EA3n, max 2 actions\n */\nexport interface ErrorAction {\n  type: string; // Action type, e.g. 'RETRY', 'CONTACT_SUPPORT', 'BACK', 'GO_HOME', etc.\n  label: string;\n  primary?: boolean; // true = confirm button, false = cancel button\n}\n\n/**\n * CustomError - Simple error class v\u1EDBi support null/undefined actions\n */\nexport class CustomError extends Error {\n  public readonly code: string;\n  public readonly category: ErrorCategory;\n  public readonly title: string;\n  public readonly userMessage: string;\n  public readonly retryable: boolean;\n  public readonly actions: ErrorAction[];\n\n  constructor(\n    code: string,\n    category: ErrorCategory,\n    title: string,\n    userMessage: string,\n    retryable: boolean,\n    actions?: ErrorAction[] | null,\n  ) {\n    super(userMessage);\n    this.name = 'CustomError';\n    this.code = code;\n    this.category = category;\n    this.title = title;\n    this.userMessage = userMessage;\n    this.retryable = retryable;\n    this.actions = actions || []; // \u2705 Default to empty array\n  }\n\n  /**\n   * Get primary action (confirm button)\n   */\n  getPrimaryAction(): ErrorAction | null {\n    return this.actions.find(action => action.primary) || this.actions[0] || null;\n  }\n\n  /**\n   * Get secondary action (cancel button)\n   */\n  getSecondaryAction(): ErrorAction | null {\n    return this.actions.find(action => !action.primary) || this.actions[1] || null;\n  }\n}\n\n/**\n * Predefined CustomError instances - no intermediate definitions\n */\nconst PredefinedErrors: Record<string, CustomError> = {\n  // Common errors\n  [MSBErrorCode.UNKNOWN_ERROR]: new CustomError(\n    MSBErrorCode.UNKNOWN_ERROR,\n    ErrorCategory.UNKNOWN,\n    'error.oops',\n    'error.errorOccurred',\n    true,\n    [\n      {type: 'RETRY', label: 'error.action.rety', primary: true},\n      {type: 'CONTACT_SUPPORT', label: 'error.action.contact'},\n    ],\n  ),\n\n  [MSBErrorCode.PIS0101]: new CustomError(\n    MSBErrorCode.PIS0101,\n    ErrorCategory.BUSINESS,\n    'error.oops',\n    'error.errorOccurred',\n    false,\n    [{type: 'NAVIGATE_BACK', label: 'error.action.back'}],\n  ),\n\n  [MSBErrorCode.PIS0106]: new CustomError(\n    MSBErrorCode.PIS0106,\n    ErrorCategory.NETWORK,\n    'error.oops',\n    'error.errorOccurred',\n    true,\n    [\n      {type: 'RETRY', label: 'error.action.retry', primary: true},\n      {type: 'NAVIGATE_BACK', label: 'error.action.back'},\n    ],\n  ),\n\n  [MSBErrorCode.PIS0103]: new CustomError(\n    MSBErrorCode.PIS0103,\n    ErrorCategory.VALIDATION,\n    'error.oops',\n    'error.errorOccurred',\n    false,\n    [{type: 'CLOSE', label: 'error.action.close'}],\n  ),\n\n  [MSBErrorCode.A05]: new CustomError(\n    MSBErrorCode.A05,\n    ErrorCategory.SYSTEM,\n    'error.oops',\n    'error.errorOccurred',\n    true,\n    [\n      {type: 'RETRY', label: 'error.action.retry', primary: true},\n      {type: 'CLOSE', label: 'error.action.close'},\n    ],\n  ),\n\n  [MSBErrorCode.FTES0008]: new CustomError(\n    MSBErrorCode.FTES0008,\n    ErrorCategory.BUSINESS,\n    'error.oops',\n    'error.errorOccurred',\n    true,\n    [{type: 'CLOSE', label: 'error.action.close'}],\n  ),\n\n  // \u2705 Example v\u1EDBi null actions - default close only\n  [MSBErrorCode.EMPTY_DATA]: new CustomError(\n    MSBErrorCode.EMPTY_DATA,\n    ErrorCategory.API,\n    'error.oops',\n    'error.errorOccurred',\n    false,\n    null, //\n  ),\n};\n\n/**\n * Error Mapper - Main utility\n */\nexport class ErrorMapper {\n  /**\n   * Create CustomError t\u1EEB error code\n   */\n  static createError(code?: string): CustomError {\n    const errorCode = code || MSBErrorCode.UNKNOWN_ERROR;\n    const predefinedError = PredefinedErrors[errorCode] || PredefinedErrors[MSBErrorCode.UNKNOWN_ERROR];\n\n    // Return a copy v\u1EDBi correct code\n    return new CustomError(\n      errorCode,\n      predefinedError.category,\n      predefinedError.title,\n      predefinedError.userMessage,\n      predefinedError.retryable,\n      predefinedError.actions,\n    );\n  }\n\n  /**\n   * Extract error t\u1EEB BaseResponse\n   */\n  static extractErrorFromResponse<T>(response: BaseResponse<T>): CustomError | null {\n    if (!response?.errors || response.errors.length <= 0) {\n      return null;\n    }\n\n    const firstError = response.errors[0];\n    let errorCode = firstError.key || MSBErrorCode.UNKNOWN_ERROR;\n\n    // Create base error\n    const baseError = ErrorMapper.createError(errorCode);\n\n    // Override message if available in response\n    let userMessage = baseError.userMessage;\n    if (firstError.context) {\n      userMessage = firstError.context.vi || firstError.context.en || userMessage;\n    }\n\n    // Return new error v\u1EDBi customized message\n    return new CustomError(\n      baseError.code,\n      baseError.category,\n      baseError.title,\n      userMessage,\n      baseError.retryable,\n      baseError.actions,\n    );\n  }\n\n  /**\n   * Check if error is retryable\n   */\n  static isRetryable(code?: string): boolean {\n    const error = ErrorMapper.createError(code);\n    return error.retryable;\n  }\n\n  /**\n   * Get error category\n   */\n  static getCategory(code?: string): ErrorCategory {\n    const error = ErrorMapper.createError(code);\n    return error.category;\n  }\n\n  /**\n   * Check if response has error\n   */\n  static hasError<T>(response: BaseResponse<T>): boolean {\n    return !!(response?.errors && response.errors.length > 0);\n  }\n\n  /**\n   * Get first error from response\n   */\n  static getFirstError<T>(response: BaseResponse<T>): MSBError | null {\n    if (!ErrorMapper.hasError(response)) {\n      return null;\n    }\n    return response.errors![0];\n  }\n\n  /**\n   * Add new predefined error\n   */\n  static addPredefinedError(code: string, error: CustomError): void {\n    PredefinedErrors[code] = error;\n  }\n\n  /**\n   * Get all available error codes\n   */\n  static getAvailableErrorCodes(): string[] {\n    return Object.keys(PredefinedErrors);\n  }\n}\n\n// Export convenience functions\nexport const createError = ErrorMapper.createError;\nexport const extractErrorFromResponse = ErrorMapper.extractErrorFromResponse;\nexport const isRetryable = ErrorMapper.isRetryable;\nexport const hasError = ErrorMapper.hasError;\n"],
      mappings: ";;;;;;;;;;;;;;;AAKA,IAAAA,cAAA,GAAAC,OAAA;AAMA,IAAYC,aAOX;AAPD,WAAYA,aAAa;EACvBA,aAAA,uBAAmB;EACnBA,aAAA,eAAW;EACXA,aAAA,yBAAqB;EACrBA,aAAA,6BAAyB;EACzBA,aAAA,qBAAiB;EACjBA,aAAA,uBAAmB;AACrB,CAAC,EAPWA,aAAa,KAAAC,OAAA,CAAAD,aAAA,GAAbA,aAAa;AAOxB,IAcYE,WAAY,aAAAC,MAAA;EAQvB,SAAAD,YACEE,IAAY,EACZC,QAAuB,EACvBC,KAAa,EACbC,WAAmB,EACnBC,SAAkB,EAClBC,OAA8B;IAAA,IAAAC,KAAA;IAAA,IAAAC,gBAAA,CAAAC,OAAA,QAAAV,WAAA;IAE9BQ,KAAA,GAAAG,UAAA,OAAAX,WAAA,GAAMK,WAAW;IACjBG,KAAA,CAAKI,IAAI,GAAG,aAAa;IACzBJ,KAAA,CAAKN,IAAI,GAAGA,IAAI;IAChBM,KAAA,CAAKL,QAAQ,GAAGA,QAAQ;IACxBK,KAAA,CAAKJ,KAAK,GAAGA,KAAK;IAClBI,KAAA,CAAKH,WAAW,GAAGA,WAAW;IAC9BG,KAAA,CAAKF,SAAS,GAAGA,SAAS;IAC1BE,KAAA,CAAKD,OAAO,GAAGA,OAAO,IAAI,EAAE;IAAC,OAAAC,KAAA;EAC/B;EAAC,IAAAK,UAAA,CAAAH,OAAA,EAAAV,WAAA,EAAAC,MAAA;EAAA,WAAAa,aAAA,CAAAJ,OAAA,EAAAV,WAAA;IAAAe,GAAA;IAAAC,KAAA,EAKD,SAAAC,gBAAgBA,CAAA;MACd,OAAO,IAAI,CAACV,OAAO,CAACW,IAAI,CAAC,UAAAC,MAAM;QAAA,OAAIA,MAAM,CAACC,OAAO;MAAA,EAAC,IAAI,IAAI,CAACb,OAAO,CAAC,CAAC,CAAC,IAAI,IAAI;IAC/E;EAAC;IAAAQ,GAAA;IAAAC,KAAA,EAKD,SAAAK,kBAAkBA,CAAA;MAChB,OAAO,IAAI,CAACd,OAAO,CAACW,IAAI,CAAC,UAAAC,MAAM;QAAA,OAAI,CAACA,MAAM,CAACC,OAAO;MAAA,EAAC,IAAI,IAAI,CAACb,OAAO,CAAC,CAAC,CAAC,IAAI,IAAI;IAChF;EAAC;AAAA,MAAAe,iBAAA,CAAAZ,OAAA,EAtC8Ba,KAAK;AAAtCxB,OAAA,CAAAC,WAAA,GAAAA,WAAA;AA4CA,IAAMwB,gBAAgB,OAAAC,gBAAA,CAAAf,OAAA,MAAAe,gBAAA,CAAAf,OAAA,MAAAe,gBAAA,CAAAf,OAAA,MAAAe,gBAAA,CAAAf,OAAA,MAAAe,gBAAA,CAAAf,OAAA,MAAAe,gBAAA,CAAAf,OAAA,MAAAe,gBAAA,CAAAf,OAAA,MAEnBd,cAAA,CAAA8B,YAAY,CAACC,aAAa,EAAG,IAAI3B,WAAW,CAC3CJ,cAAA,CAAA8B,YAAY,CAACC,aAAa,EAC1B7B,aAAa,CAAC8B,OAAO,EACrB,YAAY,EACZ,qBAAqB,EACrB,IAAI,EACJ,CACE;EAACC,IAAI,EAAE,OAAO;EAAEC,KAAK,EAAE,mBAAmB;EAAEV,OAAO,EAAE;AAAI,CAAC,EAC1D;EAACS,IAAI,EAAE,iBAAiB;EAAEC,KAAK,EAAE;AAAsB,CAAC,CACzD,CACF,GAEAlC,cAAA,CAAA8B,YAAY,CAACK,OAAO,EAAG,IAAI/B,WAAW,CACrCJ,cAAA,CAAA8B,YAAY,CAACK,OAAO,EACpBjC,aAAa,CAACkC,QAAQ,EACtB,YAAY,EACZ,qBAAqB,EACrB,KAAK,EACL,CAAC;EAACH,IAAI,EAAE,eAAe;EAAEC,KAAK,EAAE;AAAmB,CAAC,CAAC,CACtD,GAEAlC,cAAA,CAAA8B,YAAY,CAACO,OAAO,EAAG,IAAIjC,WAAW,CACrCJ,cAAA,CAAA8B,YAAY,CAACO,OAAO,EACpBnC,aAAa,CAACoC,OAAO,EACrB,YAAY,EACZ,qBAAqB,EACrB,IAAI,EACJ,CACE;EAACL,IAAI,EAAE,OAAO;EAAEC,KAAK,EAAE,oBAAoB;EAAEV,OAAO,EAAE;AAAI,CAAC,EAC3D;EAACS,IAAI,EAAE,eAAe;EAAEC,KAAK,EAAE;AAAmB,CAAC,CACpD,CACF,GAEAlC,cAAA,CAAA8B,YAAY,CAACS,OAAO,EAAG,IAAInC,WAAW,CACrCJ,cAAA,CAAA8B,YAAY,CAACS,OAAO,EACpBrC,aAAa,CAACsC,UAAU,EACxB,YAAY,EACZ,qBAAqB,EACrB,KAAK,EACL,CAAC;EAACP,IAAI,EAAE,OAAO;EAAEC,KAAK,EAAE;AAAoB,CAAC,CAAC,CAC/C,GAEAlC,cAAA,CAAA8B,YAAY,CAACW,GAAG,EAAG,IAAIrC,WAAW,CACjCJ,cAAA,CAAA8B,YAAY,CAACW,GAAG,EAChBvC,aAAa,CAACwC,MAAM,EACpB,YAAY,EACZ,qBAAqB,EACrB,IAAI,EACJ,CACE;EAACT,IAAI,EAAE,OAAO;EAAEC,KAAK,EAAE,oBAAoB;EAAEV,OAAO,EAAE;AAAI,CAAC,EAC3D;EAACS,IAAI,EAAE,OAAO;EAAEC,KAAK,EAAE;AAAoB,CAAC,CAC7C,CACF,GAEAlC,cAAA,CAAA8B,YAAY,CAACa,QAAQ,EAAG,IAAIvC,WAAW,CACtCJ,cAAA,CAAA8B,YAAY,CAACa,QAAQ,EACrBzC,aAAa,CAACkC,QAAQ,EACtB,YAAY,EACZ,qBAAqB,EACrB,IAAI,EACJ,CAAC;EAACH,IAAI,EAAE,OAAO;EAAEC,KAAK,EAAE;AAAoB,CAAC,CAAC,CAC/C,GAGAlC,cAAA,CAAA8B,YAAY,CAACc,UAAU,EAAG,IAAIxC,WAAW,CACxCJ,cAAA,CAAA8B,YAAY,CAACc,UAAU,EACvB1C,aAAa,CAAC2C,GAAG,EACjB,YAAY,EACZ,qBAAqB,EACrB,KAAK,EACL,IAAI,CACL,CACF;AAAC,IAKWC,WAAW;EAAA,SAAAA,YAAA;IAAA,IAAAjC,gBAAA,CAAAC,OAAA,QAAAgC,WAAA;EAAA;EAAA,WAAA5B,aAAA,CAAAJ,OAAA,EAAAgC,WAAA;IAAA3B,GAAA;IAAAC,KAAA,EAItB,SAAO2B,WAAWA,CAACzC,IAAa;MAC9B,IAAM0C,SAAS,GAAG1C,IAAI,IAAIN,cAAA,CAAA8B,YAAY,CAACC,aAAa;MACpD,IAAMkB,eAAe,GAAGrB,gBAAgB,CAACoB,SAAS,CAAC,IAAIpB,gBAAgB,CAAC5B,cAAA,CAAA8B,YAAY,CAACC,aAAa,CAAC;MAGnG,OAAO,IAAI3B,WAAW,CACpB4C,SAAS,EACTC,eAAe,CAAC1C,QAAQ,EACxB0C,eAAe,CAACzC,KAAK,EACrByC,eAAe,CAACxC,WAAW,EAC3BwC,eAAe,CAACvC,SAAS,EACzBuC,eAAe,CAACtC,OAAO,CACxB;IACH;EAAC;IAAAQ,GAAA;IAAAC,KAAA,EAKD,SAAO8B,wBAAwBA,CAAIC,QAAyB;MAC1D,IAAI,EAACA,QAAQ,YAARA,QAAQ,CAAEC,MAAM,KAAID,QAAQ,CAACC,MAAM,CAACC,MAAM,IAAI,CAAC,EAAE;QACpD,OAAO,IAAI;MACb;MAEA,IAAMC,UAAU,GAAGH,QAAQ,CAACC,MAAM,CAAC,CAAC,CAAC;MACrC,IAAIJ,SAAS,GAAGM,UAAU,CAACnC,GAAG,IAAInB,cAAA,CAAA8B,YAAY,CAACC,aAAa;MAG5D,IAAMwB,SAAS,GAAGT,WAAW,CAACC,WAAW,CAACC,SAAS,CAAC;MAGpD,IAAIvC,WAAW,GAAG8C,SAAS,CAAC9C,WAAW;MACvC,IAAI6C,UAAU,CAACE,OAAO,EAAE;QACtB/C,WAAW,GAAG6C,UAAU,CAACE,OAAO,CAACC,EAAE,IAAIH,UAAU,CAACE,OAAO,CAACE,EAAE,IAAIjD,WAAW;MAC7E;MAGA,OAAO,IAAIL,WAAW,CACpBmD,SAAS,CAACjD,IAAI,EACdiD,SAAS,CAAChD,QAAQ,EAClBgD,SAAS,CAAC/C,KAAK,EACfC,WAAW,EACX8C,SAAS,CAAC7C,SAAS,EACnB6C,SAAS,CAAC5C,OAAO,CAClB;IACH;EAAC;IAAAQ,GAAA;IAAAC,KAAA,EAKD,SAAOuC,WAAWA,CAACrD,IAAa;MAC9B,IAAMsD,KAAK,GAAGd,WAAW,CAACC,WAAW,CAACzC,IAAI,CAAC;MAC3C,OAAOsD,KAAK,CAAClD,SAAS;IACxB;EAAC;IAAAS,GAAA;IAAAC,KAAA,EAKD,SAAOyC,WAAWA,CAACvD,IAAa;MAC9B,IAAMsD,KAAK,GAAGd,WAAW,CAACC,WAAW,CAACzC,IAAI,CAAC;MAC3C,OAAOsD,KAAK,CAACrD,QAAQ;IACvB;EAAC;IAAAY,GAAA;IAAAC,KAAA,EAKD,SAAO0C,QAAQA,CAAIX,QAAyB;MAC1C,OAAO,CAAC,EAAEA,QAAQ,YAARA,QAAQ,CAAEC,MAAM,IAAID,QAAQ,CAACC,MAAM,CAACC,MAAM,GAAG,CAAC,CAAC;IAC3D;EAAC;IAAAlC,GAAA;IAAAC,KAAA,EAKD,SAAO2C,aAAaA,CAAIZ,QAAyB;MAC/C,IAAI,CAACL,WAAW,CAACgB,QAAQ,CAACX,QAAQ,CAAC,EAAE;QACnC,OAAO,IAAI;MACb;MACA,OAAOA,QAAQ,CAACC,MAAO,CAAC,CAAC,CAAC;IAC5B;EAAC;IAAAjC,GAAA;IAAAC,KAAA,EAKD,SAAO4C,kBAAkBA,CAAC1D,IAAY,EAAEsD,KAAkB;MACxDhC,gBAAgB,CAACtB,IAAI,CAAC,GAAGsD,KAAK;IAChC;EAAC;IAAAzC,GAAA;IAAAC,KAAA,EAKD,SAAO6C,sBAAsBA,CAAA;MAC3B,OAAOC,MAAM,CAACC,IAAI,CAACvC,gBAAgB,CAAC;IACtC;EAAC;AAAA;AA/FHzB,OAAA,CAAA2C,WAAA,GAAAA,WAAA;AAmGa3C,OAAA,CAAA4C,WAAW,GAAGD,WAAW,CAACC,WAAW;AACrC5C,OAAA,CAAA+C,wBAAwB,GAAGJ,WAAW,CAACI,wBAAwB;AAC/D/C,OAAA,CAAAwD,WAAW,GAAGb,WAAW,CAACa,WAAW;AACrCxD,OAAA,CAAA2D,QAAQ,GAAGhB,WAAW,CAACgB,QAAQ",
      ignoreList: []
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "d7e9abeff253b4b37b097652063ffc88af869fcb"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_1blejgonk8 = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_1blejgonk8();
var _interopRequireDefault =
/* istanbul ignore next */
(cov_1blejgonk8().s[0]++, require("@babel/runtime/helpers/interopRequireDefault"));
var _defineProperty2 =
/* istanbul ignore next */
(cov_1blejgonk8().s[1]++, _interopRequireDefault(require("@babel/runtime/helpers/defineProperty")));
var _classCallCheck2 =
/* istanbul ignore next */
(cov_1blejgonk8().s[2]++, _interopRequireDefault(require("@babel/runtime/helpers/classCallCheck")));
var _createClass2 =
/* istanbul ignore next */
(cov_1blejgonk8().s[3]++, _interopRequireDefault(require("@babel/runtime/helpers/createClass")));
var _possibleConstructorReturn2 =
/* istanbul ignore next */
(cov_1blejgonk8().s[4]++, _interopRequireDefault(require("@babel/runtime/helpers/possibleConstructorReturn")));
var _getPrototypeOf2 =
/* istanbul ignore next */
(cov_1blejgonk8().s[5]++, _interopRequireDefault(require("@babel/runtime/helpers/getPrototypeOf")));
var _inherits2 =
/* istanbul ignore next */
(cov_1blejgonk8().s[6]++, _interopRequireDefault(require("@babel/runtime/helpers/inherits")));
var _wrapNativeSuper2 =
/* istanbul ignore next */
(cov_1blejgonk8().s[7]++, _interopRequireDefault(require("@babel/runtime/helpers/wrapNativeSuper")));
function _callSuper(t, o, e) {
  /* istanbul ignore next */
  cov_1blejgonk8().f[0]++;
  cov_1blejgonk8().s[8]++;
  return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ?
  /* istanbul ignore next */
  (cov_1blejgonk8().b[0][0]++, Reflect.construct(o,
  /* istanbul ignore next */
  (cov_1blejgonk8().b[1][0]++, e) ||
  /* istanbul ignore next */
  (cov_1blejgonk8().b[1][1]++, []), (0, _getPrototypeOf2.default)(t).constructor)) :
  /* istanbul ignore next */
  (cov_1blejgonk8().b[0][1]++, o.apply(t, e)));
}
function _isNativeReflectConstruct() {
  /* istanbul ignore next */
  cov_1blejgonk8().f[1]++;
  cov_1blejgonk8().s[9]++;
  try {
    var t =
    /* istanbul ignore next */
    (cov_1blejgonk8().s[10]++, !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {
      /* istanbul ignore next */
      cov_1blejgonk8().f[2]++;
    })));
  } catch (t) {}
  /* istanbul ignore next */
  cov_1blejgonk8().s[11]++;
  return (_isNativeReflectConstruct = function _isNativeReflectConstruct() {
    /* istanbul ignore next */
    cov_1blejgonk8().f[3]++;
    cov_1blejgonk8().s[12]++;
    return !!t;
  })();
}
/* istanbul ignore next */
cov_1blejgonk8().s[13]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_1blejgonk8().s[14]++;
exports.hasError = exports.isRetryable = exports.extractErrorFromResponse = exports.createError = exports.ErrorMapper = exports.CustomError = exports.ErrorCategory = void 0;
var MSBErrorCode_1 =
/* istanbul ignore next */
(cov_1blejgonk8().s[15]++, require("./MSBErrorCode"));
var ErrorCategory;
/* istanbul ignore next */
cov_1blejgonk8().s[16]++;
(function (ErrorCategory) {
  /* istanbul ignore next */
  cov_1blejgonk8().f[4]++;
  cov_1blejgonk8().s[17]++;
  ErrorCategory["NETWORK"] = "NETWORK";
  /* istanbul ignore next */
  cov_1blejgonk8().s[18]++;
  ErrorCategory["API"] = "API";
  /* istanbul ignore next */
  cov_1blejgonk8().s[19]++;
  ErrorCategory["BUSINESS"] = "BUSINESS";
  /* istanbul ignore next */
  cov_1blejgonk8().s[20]++;
  ErrorCategory["VALIDATION"] = "VALIDATION";
  /* istanbul ignore next */
  cov_1blejgonk8().s[21]++;
  ErrorCategory["SYSTEM"] = "SYSTEM";
  /* istanbul ignore next */
  cov_1blejgonk8().s[22]++;
  ErrorCategory["UNKNOWN"] = "UNKNOWN";
})(
/* istanbul ignore next */
(cov_1blejgonk8().b[2][0]++, ErrorCategory) ||
/* istanbul ignore next */
(cov_1blejgonk8().b[2][1]++, exports.ErrorCategory = ErrorCategory = {}));
var CustomError =
/* istanbul ignore next */
(cov_1blejgonk8().s[23]++, function (_Error) {
  /* istanbul ignore next */
  cov_1blejgonk8().f[5]++;
  function CustomError(code, category, title, userMessage, retryable, actions) {
    /* istanbul ignore next */
    cov_1blejgonk8().f[6]++;
    var _this;
    /* istanbul ignore next */
    cov_1blejgonk8().s[24]++;
    (0, _classCallCheck2.default)(this, CustomError);
    /* istanbul ignore next */
    cov_1blejgonk8().s[25]++;
    _this = _callSuper(this, CustomError, [userMessage]);
    /* istanbul ignore next */
    cov_1blejgonk8().s[26]++;
    _this.name = 'CustomError';
    /* istanbul ignore next */
    cov_1blejgonk8().s[27]++;
    _this.code = code;
    /* istanbul ignore next */
    cov_1blejgonk8().s[28]++;
    _this.category = category;
    /* istanbul ignore next */
    cov_1blejgonk8().s[29]++;
    _this.title = title;
    /* istanbul ignore next */
    cov_1blejgonk8().s[30]++;
    _this.userMessage = userMessage;
    /* istanbul ignore next */
    cov_1blejgonk8().s[31]++;
    _this.retryable = retryable;
    /* istanbul ignore next */
    cov_1blejgonk8().s[32]++;
    _this.actions =
    /* istanbul ignore next */
    (cov_1blejgonk8().b[3][0]++, actions) ||
    /* istanbul ignore next */
    (cov_1blejgonk8().b[3][1]++, []);
    /* istanbul ignore next */
    cov_1blejgonk8().s[33]++;
    return _this;
  }
  /* istanbul ignore next */
  cov_1blejgonk8().s[34]++;
  (0, _inherits2.default)(CustomError, _Error);
  /* istanbul ignore next */
  cov_1blejgonk8().s[35]++;
  return (0, _createClass2.default)(CustomError, [{
    key: "getPrimaryAction",
    value: function getPrimaryAction() {
      /* istanbul ignore next */
      cov_1blejgonk8().f[7]++;
      cov_1blejgonk8().s[36]++;
      return /* istanbul ignore next */(cov_1blejgonk8().b[4][0]++, this.actions.find(function (action) {
        /* istanbul ignore next */
        cov_1blejgonk8().f[8]++;
        cov_1blejgonk8().s[37]++;
        return action.primary;
      })) ||
      /* istanbul ignore next */
      (cov_1blejgonk8().b[4][1]++, this.actions[0]) ||
      /* istanbul ignore next */
      (cov_1blejgonk8().b[4][2]++, null);
    }
  }, {
    key: "getSecondaryAction",
    value: function getSecondaryAction() {
      /* istanbul ignore next */
      cov_1blejgonk8().f[9]++;
      cov_1blejgonk8().s[38]++;
      return /* istanbul ignore next */(cov_1blejgonk8().b[5][0]++, this.actions.find(function (action) {
        /* istanbul ignore next */
        cov_1blejgonk8().f[10]++;
        cov_1blejgonk8().s[39]++;
        return !action.primary;
      })) ||
      /* istanbul ignore next */
      (cov_1blejgonk8().b[5][1]++, this.actions[1]) ||
      /* istanbul ignore next */
      (cov_1blejgonk8().b[5][2]++, null);
    }
  }]);
}((0, _wrapNativeSuper2.default)(Error)));
/* istanbul ignore next */
cov_1blejgonk8().s[40]++;
exports.CustomError = CustomError;
var PredefinedErrors =
/* istanbul ignore next */
(cov_1blejgonk8().s[41]++, (0, _defineProperty2.default)((0, _defineProperty2.default)((0, _defineProperty2.default)((0, _defineProperty2.default)((0, _defineProperty2.default)((0, _defineProperty2.default)((0, _defineProperty2.default)({}, MSBErrorCode_1.MSBErrorCode.UNKNOWN_ERROR, new CustomError(MSBErrorCode_1.MSBErrorCode.UNKNOWN_ERROR, ErrorCategory.UNKNOWN, 'error.oops', 'error.errorOccurred', true, [{
  type: 'RETRY',
  label: 'error.action.rety',
  primary: true
}, {
  type: 'CONTACT_SUPPORT',
  label: 'error.action.contact'
}])), MSBErrorCode_1.MSBErrorCode.PIS0101, new CustomError(MSBErrorCode_1.MSBErrorCode.PIS0101, ErrorCategory.BUSINESS, 'error.oops', 'error.errorOccurred', false, [{
  type: 'NAVIGATE_BACK',
  label: 'error.action.back'
}])), MSBErrorCode_1.MSBErrorCode.PIS0106, new CustomError(MSBErrorCode_1.MSBErrorCode.PIS0106, ErrorCategory.NETWORK, 'error.oops', 'error.errorOccurred', true, [{
  type: 'RETRY',
  label: 'error.action.retry',
  primary: true
}, {
  type: 'NAVIGATE_BACK',
  label: 'error.action.back'
}])), MSBErrorCode_1.MSBErrorCode.PIS0103, new CustomError(MSBErrorCode_1.MSBErrorCode.PIS0103, ErrorCategory.VALIDATION, 'error.oops', 'error.errorOccurred', false, [{
  type: 'CLOSE',
  label: 'error.action.close'
}])), MSBErrorCode_1.MSBErrorCode.A05, new CustomError(MSBErrorCode_1.MSBErrorCode.A05, ErrorCategory.SYSTEM, 'error.oops', 'error.errorOccurred', true, [{
  type: 'RETRY',
  label: 'error.action.retry',
  primary: true
}, {
  type: 'CLOSE',
  label: 'error.action.close'
}])), MSBErrorCode_1.MSBErrorCode.FTES0008, new CustomError(MSBErrorCode_1.MSBErrorCode.FTES0008, ErrorCategory.BUSINESS, 'error.oops', 'error.errorOccurred', true, [{
  type: 'CLOSE',
  label: 'error.action.close'
}])), MSBErrorCode_1.MSBErrorCode.EMPTY_DATA, new CustomError(MSBErrorCode_1.MSBErrorCode.EMPTY_DATA, ErrorCategory.API, 'error.oops', 'error.errorOccurred', false, null)));
var ErrorMapper =
/* istanbul ignore next */
(cov_1blejgonk8().s[42]++, function () {
  /* istanbul ignore next */
  cov_1blejgonk8().f[11]++;
  function ErrorMapper() {
    /* istanbul ignore next */
    cov_1blejgonk8().f[12]++;
    cov_1blejgonk8().s[43]++;
    (0, _classCallCheck2.default)(this, ErrorMapper);
  }
  /* istanbul ignore next */
  cov_1blejgonk8().s[44]++;
  return (0, _createClass2.default)(ErrorMapper, null, [{
    key: "createError",
    value: function createError(code) {
      /* istanbul ignore next */
      cov_1blejgonk8().f[13]++;
      var errorCode =
      /* istanbul ignore next */
      (cov_1blejgonk8().s[45]++,
      /* istanbul ignore next */
      (cov_1blejgonk8().b[6][0]++, code) ||
      /* istanbul ignore next */
      (cov_1blejgonk8().b[6][1]++, MSBErrorCode_1.MSBErrorCode.UNKNOWN_ERROR));
      var predefinedError =
      /* istanbul ignore next */
      (cov_1blejgonk8().s[46]++,
      /* istanbul ignore next */
      (cov_1blejgonk8().b[7][0]++, PredefinedErrors[errorCode]) ||
      /* istanbul ignore next */
      (cov_1blejgonk8().b[7][1]++, PredefinedErrors[MSBErrorCode_1.MSBErrorCode.UNKNOWN_ERROR]));
      /* istanbul ignore next */
      cov_1blejgonk8().s[47]++;
      return new CustomError(errorCode, predefinedError.category, predefinedError.title, predefinedError.userMessage, predefinedError.retryable, predefinedError.actions);
    }
  }, {
    key: "extractErrorFromResponse",
    value: function extractErrorFromResponse(response) {
      /* istanbul ignore next */
      cov_1blejgonk8().f[14]++;
      cov_1blejgonk8().s[48]++;
      if (
      /* istanbul ignore next */
      (cov_1blejgonk8().b[9][0]++, !(
      /* istanbul ignore next */
      (cov_1blejgonk8().b[10][0]++, response != null) &&
      /* istanbul ignore next */
      (cov_1blejgonk8().b[10][1]++, response.errors))) ||
      /* istanbul ignore next */
      (cov_1blejgonk8().b[9][1]++, response.errors.length <= 0)) {
        /* istanbul ignore next */
        cov_1blejgonk8().b[8][0]++;
        cov_1blejgonk8().s[49]++;
        return null;
      } else
      /* istanbul ignore next */
      {
        cov_1blejgonk8().b[8][1]++;
      }
      var firstError =
      /* istanbul ignore next */
      (cov_1blejgonk8().s[50]++, response.errors[0]);
      var errorCode =
      /* istanbul ignore next */
      (cov_1blejgonk8().s[51]++,
      /* istanbul ignore next */
      (cov_1blejgonk8().b[11][0]++, firstError.key) ||
      /* istanbul ignore next */
      (cov_1blejgonk8().b[11][1]++, MSBErrorCode_1.MSBErrorCode.UNKNOWN_ERROR));
      var baseError =
      /* istanbul ignore next */
      (cov_1blejgonk8().s[52]++, ErrorMapper.createError(errorCode));
      var userMessage =
      /* istanbul ignore next */
      (cov_1blejgonk8().s[53]++, baseError.userMessage);
      /* istanbul ignore next */
      cov_1blejgonk8().s[54]++;
      if (firstError.context) {
        /* istanbul ignore next */
        cov_1blejgonk8().b[12][0]++;
        cov_1blejgonk8().s[55]++;
        userMessage =
        /* istanbul ignore next */
        (cov_1blejgonk8().b[13][0]++, firstError.context.vi) ||
        /* istanbul ignore next */
        (cov_1blejgonk8().b[13][1]++, firstError.context.en) ||
        /* istanbul ignore next */
        (cov_1blejgonk8().b[13][2]++, userMessage);
      } else
      /* istanbul ignore next */
      {
        cov_1blejgonk8().b[12][1]++;
      }
      cov_1blejgonk8().s[56]++;
      return new CustomError(baseError.code, baseError.category, baseError.title, userMessage, baseError.retryable, baseError.actions);
    }
  }, {
    key: "isRetryable",
    value: function isRetryable(code) {
      /* istanbul ignore next */
      cov_1blejgonk8().f[15]++;
      var error =
      /* istanbul ignore next */
      (cov_1blejgonk8().s[57]++, ErrorMapper.createError(code));
      /* istanbul ignore next */
      cov_1blejgonk8().s[58]++;
      return error.retryable;
    }
  }, {
    key: "getCategory",
    value: function getCategory(code) {
      /* istanbul ignore next */
      cov_1blejgonk8().f[16]++;
      var error =
      /* istanbul ignore next */
      (cov_1blejgonk8().s[59]++, ErrorMapper.createError(code));
      /* istanbul ignore next */
      cov_1blejgonk8().s[60]++;
      return error.category;
    }
  }, {
    key: "hasError",
    value: function hasError(response) {
      /* istanbul ignore next */
      cov_1blejgonk8().f[17]++;
      cov_1blejgonk8().s[61]++;
      return !!(
      /* istanbul ignore next */
      (cov_1blejgonk8().b[14][0]++, response != null) &&
      /* istanbul ignore next */
      (cov_1blejgonk8().b[14][1]++, response.errors) &&
      /* istanbul ignore next */
      (cov_1blejgonk8().b[14][2]++, response.errors.length > 0));
    }
  }, {
    key: "getFirstError",
    value: function getFirstError(response) {
      /* istanbul ignore next */
      cov_1blejgonk8().f[18]++;
      cov_1blejgonk8().s[62]++;
      if (!ErrorMapper.hasError(response)) {
        /* istanbul ignore next */
        cov_1blejgonk8().b[15][0]++;
        cov_1blejgonk8().s[63]++;
        return null;
      } else
      /* istanbul ignore next */
      {
        cov_1blejgonk8().b[15][1]++;
      }
      cov_1blejgonk8().s[64]++;
      return response.errors[0];
    }
  }, {
    key: "addPredefinedError",
    value: function addPredefinedError(code, error) {
      /* istanbul ignore next */
      cov_1blejgonk8().f[19]++;
      cov_1blejgonk8().s[65]++;
      PredefinedErrors[code] = error;
    }
  }, {
    key: "getAvailableErrorCodes",
    value: function getAvailableErrorCodes() {
      /* istanbul ignore next */
      cov_1blejgonk8().f[20]++;
      cov_1blejgonk8().s[66]++;
      return Object.keys(PredefinedErrors);
    }
  }]);
}());
/* istanbul ignore next */
cov_1blejgonk8().s[67]++;
exports.ErrorMapper = ErrorMapper;
/* istanbul ignore next */
cov_1blejgonk8().s[68]++;
exports.createError = ErrorMapper.createError;
/* istanbul ignore next */
cov_1blejgonk8().s[69]++;
exports.extractErrorFromResponse = ErrorMapper.extractErrorFromResponse;
/* istanbul ignore next */
cov_1blejgonk8().s[70]++;
exports.isRetryable = ErrorMapper.isRetryable;
/* istanbul ignore next */
cov_1blejgonk8().s[71]++;
exports.hasError = ErrorMapper.hasError;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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