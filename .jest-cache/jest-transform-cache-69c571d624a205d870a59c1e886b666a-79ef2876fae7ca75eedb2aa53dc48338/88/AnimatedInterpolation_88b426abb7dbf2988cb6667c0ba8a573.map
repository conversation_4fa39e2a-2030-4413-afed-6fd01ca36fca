{"version": 3, "names": ["_interopRequireDefault", "require", "Object", "defineProperty", "exports", "value", "default", "_classCallCheck2", "_createClass2", "_possibleConstructorReturn2", "_getPrototypeOf2", "_get2", "_inherits2", "_NativeAnimatedHelper", "_NativeAnimatedValidation", "_normalizeColor", "_processColor", "_Easing", "_AnimatedWithChildren2", "_invariant", "_callSuper", "t", "o", "e", "_isNativeReflectConstruct", "Reflect", "construct", "constructor", "apply", "Boolean", "prototype", "valueOf", "call", "_superPropGet", "r", "p", "createNumericInterpolation", "config", "outputRange", "inputRange", "easing", "Easing", "linear", "extrapolateLeft", "undefined", "extrapolate", "extrapolateRight", "input", "invariant", "range", "find<PERSON><PERSON><PERSON>", "interpolate", "inputMin", "inputMax", "outputMin", "outputMax", "result", "Infinity", "numericComponentRegex", "mapStringToNumericComponents", "normalizedColor", "normalizeColor", "g", "b", "a", "isColor", "components", "lastMatchEnd", "match", "exec", "index", "push", "substring", "parseFloat", "length", "createStringInterpolation", "map", "__DEV__", "every", "output", "firstOutput", "component", "i", "numericComponents", "filter", "c", "interpolations", "_", "assign", "values", "interpolation", "join", "Math", "round", "checkValidRanges", "checkInfiniteRange", "checkValidInputRange", "arr", "message", "String", "name", "AnimatedInterpolation", "_Animated<PERSON>ith<PERSON><PERSON><PERSON><PERSON>", "parent", "_this", "_parent", "_config", "_getInterpolation", "key", "_interpolation", "__makeNative", "platformConfig", "__getValue", "parentValue", "__attach", "__add<PERSON><PERSON>d", "__detach", "__remove<PERSON><PERSON>d", "__getNativeConfig", "validateInterpolation", "outputType", "processedColor", "processColor", "NativeAnimatedHelper", "transformDataType", "type", "debugID", "__getDebugID", "AnimatedWithChildren"], "sources": ["AnimatedInterpolation.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @flow\n * @format\n */\n\n/* eslint no-bitwise: 0 */\n\n'use strict';\n\nimport type {PlatformConfig} from '../AnimatedPlatformConfig';\nimport type AnimatedNode from './AnimatedNode';\nimport type {AnimatedNodeConfig} from './AnimatedNode';\n\nimport NativeAnimatedHelper from '../../../src/private/animated/NativeAnimatedHelper';\nimport {validateInterpolation} from '../../../src/private/animated/NativeAnimatedValidation';\nimport normalizeColor from '../../StyleSheet/normalizeColor';\nimport processColor from '../../StyleSheet/processColor';\nimport Easing from '../Easing';\nimport AnimatedWithChildren from './AnimatedWithChildren';\nimport invariant from 'invariant';\n\ntype ExtrapolateType = 'extend' | 'identity' | 'clamp';\n\nexport type InterpolationConfigType<OutputT: number | string> = $ReadOnly<{\n  ...AnimatedNodeConfig,\n  inputRange: $ReadOnlyArray<number>,\n  outputRange: $ReadOnlyArray<OutputT>,\n  easing?: (input: number) => number,\n  extrapolate?: ExtrapolateType,\n  extrapolateLeft?: ExtrapolateType,\n  extrapolateRight?: ExtrapolateType,\n}>;\n\n/**\n * Very handy helper to map input ranges to output ranges with an easing\n * function and custom behavior outside of the ranges.\n */\nfunction createNumericInterpolation(\n  config: InterpolationConfigType<number>,\n): (input: number) => number {\n  const outputRange: $ReadOnlyArray<number> = (config.outputRange: any);\n  const inputRange = config.inputRange;\n\n  const easing = config.easing || Easing.linear;\n\n  let extrapolateLeft: ExtrapolateType = 'extend';\n  if (config.extrapolateLeft !== undefined) {\n    extrapolateLeft = config.extrapolateLeft;\n  } else if (config.extrapolate !== undefined) {\n    extrapolateLeft = config.extrapolate;\n  }\n\n  let extrapolateRight: ExtrapolateType = 'extend';\n  if (config.extrapolateRight !== undefined) {\n    extrapolateRight = config.extrapolateRight;\n  } else if (config.extrapolate !== undefined) {\n    extrapolateRight = config.extrapolate;\n  }\n\n  return input => {\n    invariant(\n      typeof input === 'number',\n      'Cannot interpolation an input which is not a number',\n    );\n\n    const range = findRange(input, inputRange);\n    return (interpolate(\n      input,\n      inputRange[range],\n      inputRange[range + 1],\n      outputRange[range],\n      outputRange[range + 1],\n      easing,\n      extrapolateLeft,\n      extrapolateRight,\n    ): any);\n  };\n}\n\nfunction interpolate(\n  input: number,\n  inputMin: number,\n  inputMax: number,\n  outputMin: number,\n  outputMax: number,\n  easing: (input: number) => number,\n  extrapolateLeft: ExtrapolateType,\n  extrapolateRight: ExtrapolateType,\n) {\n  let result = input;\n\n  // Extrapolate\n  if (result < inputMin) {\n    if (extrapolateLeft === 'identity') {\n      return result;\n    } else if (extrapolateLeft === 'clamp') {\n      result = inputMin;\n    } else if (extrapolateLeft === 'extend') {\n      // noop\n    }\n  }\n\n  if (result > inputMax) {\n    if (extrapolateRight === 'identity') {\n      return result;\n    } else if (extrapolateRight === 'clamp') {\n      result = inputMax;\n    } else if (extrapolateRight === 'extend') {\n      // noop\n    }\n  }\n\n  if (outputMin === outputMax) {\n    return outputMin;\n  }\n\n  if (inputMin === inputMax) {\n    if (input <= inputMin) {\n      return outputMin;\n    }\n    return outputMax;\n  }\n\n  // Input Range\n  if (inputMin === -Infinity) {\n    result = -result;\n  } else if (inputMax === Infinity) {\n    result = result - inputMin;\n  } else {\n    result = (result - inputMin) / (inputMax - inputMin);\n  }\n\n  // Easing\n  result = easing(result);\n\n  // Output Range\n  if (outputMin === -Infinity) {\n    result = -result;\n  } else if (outputMax === Infinity) {\n    result = result + outputMin;\n  } else {\n    result = result * (outputMax - outputMin) + outputMin;\n  }\n\n  return result;\n}\n\nconst numericComponentRegex = /[+-]?(?:\\d+\\.?\\d*|\\.\\d+)(?:[eE][+-]?\\d+)?/g;\n\n// Maps string inputs an RGBA color or an array of numeric components\nfunction mapStringToNumericComponents(\n  input: string,\n):\n  | {isColor: true, components: [number, number, number, number]}\n  | {isColor: false, components: $ReadOnlyArray<number | string>} {\n  let normalizedColor = normalizeColor(input);\n  invariant(\n    normalizedColor == null || typeof normalizedColor !== 'object',\n    'PlatformColors are not supported',\n  );\n\n  if (typeof normalizedColor === 'number') {\n    normalizedColor = normalizedColor || 0;\n    const r = (normalizedColor & 0xff000000) >>> 24;\n    const g = (normalizedColor & 0x00ff0000) >>> 16;\n    const b = (normalizedColor & 0x0000ff00) >>> 8;\n    const a = (normalizedColor & 0x000000ff) / 255;\n    return {isColor: true, components: [r, g, b, a]};\n  } else {\n    const components: Array<string | number> = [];\n    let lastMatchEnd = 0;\n    let match: RegExp$matchResult;\n    while ((match = (numericComponentRegex.exec(input): any)) != null) {\n      if (match.index > lastMatchEnd) {\n        components.push(input.substring(lastMatchEnd, match.index));\n      }\n      components.push(parseFloat(match[0]));\n      lastMatchEnd = match.index + match[0].length;\n    }\n    invariant(\n      components.length > 0,\n      'outputRange must contain color or value with numeric component',\n    );\n    if (lastMatchEnd < input.length) {\n      components.push(input.substring(lastMatchEnd, input.length));\n    }\n    return {isColor: false, components};\n  }\n}\n\n/**\n * Supports string shapes by extracting numbers so new values can be computed,\n * and recombines those values into new strings of the same shape.  Supports\n * things like:\n *\n *   rgba(123, 42, 99, 0.36) // colors\n *   -45deg                  // values with units\n */\nfunction createStringInterpolation(\n  config: InterpolationConfigType<string>,\n): (input: number) => string {\n  invariant(config.outputRange.length >= 2, 'Bad output range');\n  const outputRange = config.outputRange.map(mapStringToNumericComponents);\n\n  const isColor = outputRange[0].isColor;\n  if (__DEV__) {\n    invariant(\n      outputRange.every(output => output.isColor === isColor),\n      'All elements of output range should either be a color or a string with numeric components',\n    );\n    const firstOutput = outputRange[0].components;\n    invariant(\n      outputRange.every(\n        output => output.components.length === firstOutput.length,\n      ),\n      'All elements of output range should have the same number of components',\n    );\n    invariant(\n      outputRange.every(output =>\n        output.components.every(\n          (component, i) =>\n            // $FlowIgnoreMe[invalid-compare]\n            typeof component === 'number' || component === firstOutput[i],\n        ),\n      ),\n      'All elements of output range should have the same non-numeric components',\n    );\n  }\n\n  const numericComponents: $ReadOnlyArray<$ReadOnlyArray<number>> =\n    outputRange.map(output =>\n      isColor\n        ? // $FlowIgnoreMe[incompatible-call]\n          output.components\n        : // $FlowIgnoreMe[incompatible-call]\n          output.components.filter(c => typeof c === 'number'),\n    );\n  const interpolations = numericComponents[0].map((_, i) =>\n    createNumericInterpolation({\n      ...config,\n      outputRange: numericComponents.map(components => components[i]),\n    }),\n  );\n  if (!isColor) {\n    return input => {\n      const values = interpolations.map(interpolation => interpolation(input));\n      let i = 0;\n      return outputRange[0].components\n        .map(c => (typeof c === 'number' ? values[i++] : c))\n        .join('');\n    };\n  } else {\n    return input => {\n      const result = interpolations.map((interpolation, i) => {\n        const value = interpolation(input);\n        // rgba requires that the r,g,b are integers.... so we want to round them, but we *dont* want to\n        // round the opacity (4th column).\n        return i < 3 ? Math.round(value) : Math.round(value * 1000) / 1000;\n      });\n      return `rgba(${result[0]}, ${result[1]}, ${result[2]}, ${result[3]})`;\n    };\n  }\n}\n\nfunction findRange(input: number, inputRange: $ReadOnlyArray<number>) {\n  let i;\n  for (i = 1; i < inputRange.length - 1; ++i) {\n    if (inputRange[i] >= input) {\n      break;\n    }\n  }\n  return i - 1;\n}\n\nfunction checkValidRanges<OutputT: number | string>(\n  inputRange: $ReadOnlyArray<number>,\n  outputRange: $ReadOnlyArray<OutputT>,\n) {\n  checkInfiniteRange('outputRange', outputRange);\n  checkInfiniteRange('inputRange', inputRange);\n  checkValidInputRange(inputRange);\n\n  invariant(\n    inputRange.length === outputRange.length,\n    'inputRange (' +\n      inputRange.length +\n      ') and outputRange (' +\n      outputRange.length +\n      ') must have the same length',\n  );\n}\n\nfunction checkValidInputRange(arr: $ReadOnlyArray<number>) {\n  invariant(arr.length >= 2, 'inputRange must have at least 2 elements');\n  const message =\n    'inputRange must be monotonically non-decreasing ' + String(arr);\n  for (let i = 1; i < arr.length; ++i) {\n    invariant(arr[i] >= arr[i - 1], message);\n  }\n}\n\nfunction checkInfiniteRange<OutputT: number | string>(\n  name: string,\n  arr: $ReadOnlyArray<OutputT>,\n) {\n  invariant(arr.length >= 2, name + ' must have at least 2 elements');\n  invariant(\n    arr.length !== 2 || arr[0] !== -Infinity || arr[1] !== Infinity,\n    /* $FlowFixMe[incompatible-type] (>=0.13.0) - In the addition expression\n     * below this comment, one or both of the operands may be something that\n     * doesn't cleanly convert to a string, like undefined, null, and object,\n     * etc. If you really mean this implicit string conversion, you can do\n     * something like String(myThing) */\n    // $FlowFixMe[unsafe-addition]\n    name + 'cannot be ]-infinity;+infinity[ ' + arr,\n  );\n}\n\nexport default class AnimatedInterpolation<\n  OutputT: number | string,\n> extends AnimatedWithChildren {\n  _parent: AnimatedNode;\n  _config: InterpolationConfigType<OutputT>;\n  _interpolation: ?(input: number) => OutputT;\n\n  constructor(parent: AnimatedNode, config: InterpolationConfigType<OutputT>) {\n    super(config);\n    this._parent = parent;\n    this._config = config;\n\n    if (__DEV__) {\n      checkValidRanges(config.inputRange, config.outputRange);\n\n      // Create interpolation eagerly in dev, so we can signal errors faster\n      // even when using the native driver\n      this._getInterpolation();\n    }\n  }\n\n  _getInterpolation(): number => OutputT {\n    if (!this._interpolation) {\n      const config = this._config;\n      if (config.outputRange && typeof config.outputRange[0] === 'string') {\n        this._interpolation = (createStringInterpolation((config: any)): any);\n      } else {\n        this._interpolation = (createNumericInterpolation((config: any)): any);\n      }\n    }\n    return this._interpolation;\n  }\n\n  __makeNative(platformConfig: ?PlatformConfig) {\n    this._parent.__makeNative(platformConfig);\n    super.__makeNative(platformConfig);\n  }\n\n  __getValue(): OutputT {\n    const parentValue: number = this._parent.__getValue();\n    invariant(\n      typeof parentValue === 'number',\n      'Cannot interpolate an input which is not a number.',\n    );\n    return this._getInterpolation()(parentValue);\n  }\n\n  interpolate<NewOutputT: number | string>(\n    config: InterpolationConfigType<NewOutputT>,\n  ): AnimatedInterpolation<NewOutputT> {\n    return new AnimatedInterpolation(this, config);\n  }\n\n  __attach(): void {\n    this._parent.__addChild(this);\n    super.__attach();\n  }\n\n  __detach(): void {\n    this._parent.__removeChild(this);\n    super.__detach();\n  }\n\n  __getNativeConfig(): any {\n    if (__DEV__) {\n      validateInterpolation(this._config);\n    }\n\n    // Only the `outputRange` can contain strings so we don't need to transform `inputRange` here\n    let outputRange = this._config.outputRange;\n    let outputType = null;\n    if (typeof outputRange[0] === 'string') {\n      // $FlowIgnoreMe[incompatible-cast]\n      outputRange = ((outputRange: $ReadOnlyArray<string>).map(value => {\n        const processedColor = processColor(value);\n        if (typeof processedColor === 'number') {\n          outputType = 'color';\n          return processedColor;\n        } else {\n          return NativeAnimatedHelper.transformDataType(value);\n        }\n      }): any);\n    }\n\n    return {\n      inputRange: this._config.inputRange,\n      outputRange,\n      outputType,\n      extrapolateLeft:\n        this._config.extrapolateLeft || this._config.extrapolate || 'extend',\n      extrapolateRight:\n        this._config.extrapolateRight || this._config.extrapolate || 'extend',\n      type: 'interpolation',\n      debugID: this.__getDebugID(),\n    };\n  }\n}\n"], "mappings": "AAYA,YAAY;;AAAC,IAAAA,sBAAA,GAAAC,OAAA;AAAAC,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,KAAA;AAAA;AAAAD,OAAA,CAAAE,OAAA;AAAA,IAAAC,gBAAA,GAAAP,sBAAA,CAAAC,OAAA;AAAA,IAAAO,aAAA,GAAAR,sBAAA,CAAAC,OAAA;AAAA,IAAAQ,2BAAA,GAAAT,sBAAA,CAAAC,OAAA;AAAA,IAAAS,gBAAA,GAAAV,sBAAA,CAAAC,OAAA;AAAA,IAAAU,KAAA,GAAAX,sBAAA,CAAAC,OAAA;AAAA,IAAAW,UAAA,GAAAZ,sBAAA,CAAAC,OAAA;AAMb,IAAAY,qBAAA,GAAAb,sBAAA,CAAAC,OAAA;AACA,IAAAa,yBAAA,GAAAb,OAAA;AACA,IAAAc,eAAA,GAAAf,sBAAA,CAAAC,OAAA;AACA,IAAAe,aAAA,GAAAhB,sBAAA,CAAAC,OAAA;AACA,IAAAgB,OAAA,GAAAjB,sBAAA,CAAAC,OAAA;AACA,IAAAiB,sBAAA,GAAAlB,sBAAA,CAAAC,OAAA;AACA,IAAAkB,UAAA,GAAAnB,sBAAA,CAAAC,OAAA;AAAkC,SAAAmB,WAAAC,CAAA,EAAAC,CAAA,EAAAC,CAAA,WAAAD,CAAA,OAAAZ,gBAAA,CAAAJ,OAAA,EAAAgB,CAAA,OAAAb,2BAAA,CAAAH,OAAA,EAAAe,CAAA,EAAAG,yBAAA,KAAAC,OAAA,CAAAC,SAAA,CAAAJ,CAAA,EAAAC,CAAA,YAAAb,gBAAA,CAAAJ,OAAA,EAAAe,CAAA,EAAAM,WAAA,IAAAL,CAAA,CAAAM,KAAA,CAAAP,CAAA,EAAAE,CAAA;AAAA,SAAAC,0BAAA,cAAAH,CAAA,IAAAQ,OAAA,CAAAC,SAAA,CAAAC,OAAA,CAAAC,IAAA,CAAAP,OAAA,CAAAC,SAAA,CAAAG,OAAA,iCAAAR,CAAA,aAAAG,yBAAA,YAAAA,0BAAA,aAAAH,CAAA;AAAA,SAAAY,cAAAZ,CAAA,EAAAC,CAAA,EAAAC,CAAA,EAAAW,CAAA,QAAAC,CAAA,OAAAxB,KAAA,CAAAL,OAAA,MAAAI,gBAAA,CAAAJ,OAAA,MAAA4B,CAAA,GAAAb,CAAA,CAAAS,SAAA,GAAAT,CAAA,GAAAC,CAAA,EAAAC,CAAA,cAAAW,CAAA,yBAAAC,CAAA,aAAAd,CAAA,WAAAc,CAAA,CAAAP,KAAA,CAAAL,CAAA,EAAAF,CAAA,OAAAc,CAAA;AAkBlC,SAASC,0BAA0BA,CACjCC,MAAuC,EACZ;EAC3B,IAAMC,WAAmC,GAAID,MAAM,CAACC,WAAiB;EACrE,IAAMC,UAAU,GAAGF,MAAM,CAACE,UAAU;EAEpC,IAAMC,MAAM,GAAGH,MAAM,CAACG,MAAM,IAAIC,eAAM,CAACC,MAAM;EAE7C,IAAIC,eAAgC,GAAG,QAAQ;EAC/C,IAAIN,MAAM,CAACM,eAAe,KAAKC,SAAS,EAAE;IACxCD,eAAe,GAAGN,MAAM,CAACM,eAAe;EAC1C,CAAC,MAAM,IAAIN,MAAM,CAACQ,WAAW,KAAKD,SAAS,EAAE;IAC3CD,eAAe,GAAGN,MAAM,CAACQ,WAAW;EACtC;EAEA,IAAIC,gBAAiC,GAAG,QAAQ;EAChD,IAAIT,MAAM,CAACS,gBAAgB,KAAKF,SAAS,EAAE;IACzCE,gBAAgB,GAAGT,MAAM,CAACS,gBAAgB;EAC5C,CAAC,MAAM,IAAIT,MAAM,CAACQ,WAAW,KAAKD,SAAS,EAAE;IAC3CE,gBAAgB,GAAGT,MAAM,CAACQ,WAAW;EACvC;EAEA,OAAO,UAAAE,KAAK,EAAI;IACd,IAAAC,kBAAS,EACP,OAAOD,KAAK,KAAK,QAAQ,EACzB,qDACF,CAAC;IAED,IAAME,KAAK,GAAGC,SAAS,CAACH,KAAK,EAAER,UAAU,CAAC;IAC1C,OAAQY,WAAW,CACjBJ,KAAK,EACLR,UAAU,CAACU,KAAK,CAAC,EACjBV,UAAU,CAACU,KAAK,GAAG,CAAC,CAAC,EACrBX,WAAW,CAACW,KAAK,CAAC,EAClBX,WAAW,CAACW,KAAK,GAAG,CAAC,CAAC,EACtBT,MAAM,EACNG,eAAe,EACfG,gBACF,CAAC;EACH,CAAC;AACH;AAEA,SAASK,WAAWA,CAClBJ,KAAa,EACbK,QAAgB,EAChBC,QAAgB,EAChBC,SAAiB,EACjBC,SAAiB,EACjBf,MAAiC,EACjCG,eAAgC,EAChCG,gBAAiC,EACjC;EACA,IAAIU,MAAM,GAAGT,KAAK;EAGlB,IAAIS,MAAM,GAAGJ,QAAQ,EAAE;IACrB,IAAIT,eAAe,KAAK,UAAU,EAAE;MAClC,OAAOa,MAAM;IACf,CAAC,MAAM,IAAIb,eAAe,KAAK,OAAO,EAAE;MACtCa,MAAM,GAAGJ,QAAQ;IACnB,CAAC,MAAM,IAAIT,eAAe,KAAK,QAAQ,EAAE,CAEzC;EACF;EAEA,IAAIa,MAAM,GAAGH,QAAQ,EAAE;IACrB,IAAIP,gBAAgB,KAAK,UAAU,EAAE;MACnC,OAAOU,MAAM;IACf,CAAC,MAAM,IAAIV,gBAAgB,KAAK,OAAO,EAAE;MACvCU,MAAM,GAAGH,QAAQ;IACnB,CAAC,MAAM,IAAIP,gBAAgB,KAAK,QAAQ,EAAE,CAE1C;EACF;EAEA,IAAIQ,SAAS,KAAKC,SAAS,EAAE;IAC3B,OAAOD,SAAS;EAClB;EAEA,IAAIF,QAAQ,KAAKC,QAAQ,EAAE;IACzB,IAAIN,KAAK,IAAIK,QAAQ,EAAE;MACrB,OAAOE,SAAS;IAClB;IACA,OAAOC,SAAS;EAClB;EAGA,IAAIH,QAAQ,KAAK,CAACK,QAAQ,EAAE;IAC1BD,MAAM,GAAG,CAACA,MAAM;EAClB,CAAC,MAAM,IAAIH,QAAQ,KAAKI,QAAQ,EAAE;IAChCD,MAAM,GAAGA,MAAM,GAAGJ,QAAQ;EAC5B,CAAC,MAAM;IACLI,MAAM,GAAG,CAACA,MAAM,GAAGJ,QAAQ,KAAKC,QAAQ,GAAGD,QAAQ,CAAC;EACtD;EAGAI,MAAM,GAAGhB,MAAM,CAACgB,MAAM,CAAC;EAGvB,IAAIF,SAAS,KAAK,CAACG,QAAQ,EAAE;IAC3BD,MAAM,GAAG,CAACA,MAAM;EAClB,CAAC,MAAM,IAAID,SAAS,KAAKE,QAAQ,EAAE;IACjCD,MAAM,GAAGA,MAAM,GAAGF,SAAS;EAC7B,CAAC,MAAM;IACLE,MAAM,GAAGA,MAAM,IAAID,SAAS,GAAGD,SAAS,CAAC,GAAGA,SAAS;EACvD;EAEA,OAAOE,MAAM;AACf;AAEA,IAAME,qBAAqB,GAAG,4CAA4C;AAG1E,SAASC,4BAA4BA,CACnCZ,KAAa,EAGmD;EAChE,IAAIa,eAAe,GAAG,IAAAC,uBAAc,EAACd,KAAK,CAAC;EAC3C,IAAAC,kBAAS,EACPY,eAAe,IAAI,IAAI,IAAI,OAAOA,eAAe,KAAK,QAAQ,EAC9D,kCACF,CAAC;EAED,IAAI,OAAOA,eAAe,KAAK,QAAQ,EAAE;IACvCA,eAAe,GAAGA,eAAe,IAAI,CAAC;IACtC,IAAM1B,CAAC,GAAG,CAAC0B,eAAe,GAAG,UAAU,MAAM,EAAE;IAC/C,IAAME,CAAC,GAAG,CAACF,eAAe,GAAG,UAAU,MAAM,EAAE;IAC/C,IAAMG,CAAC,GAAG,CAACH,eAAe,GAAG,UAAU,MAAM,CAAC;IAC9C,IAAMI,CAAC,GAAG,CAACJ,eAAe,GAAG,UAAU,IAAI,GAAG;IAC9C,OAAO;MAACK,OAAO,EAAE,IAAI;MAAEC,UAAU,EAAE,CAAChC,CAAC,EAAE4B,CAAC,EAAEC,CAAC,EAAEC,CAAC;IAAC,CAAC;EAClD,CAAC,MAAM;IACL,IAAME,UAAkC,GAAG,EAAE;IAC7C,IAAIC,YAAY,GAAG,CAAC;IACpB,IAAIC,KAAyB;IAC7B,OAAO,CAACA,KAAK,GAAIV,qBAAqB,CAACW,IAAI,CAACtB,KAAK,CAAO,KAAK,IAAI,EAAE;MACjE,IAAIqB,KAAK,CAACE,KAAK,GAAGH,YAAY,EAAE;QAC9BD,UAAU,CAACK,IAAI,CAACxB,KAAK,CAACyB,SAAS,CAACL,YAAY,EAAEC,KAAK,CAACE,KAAK,CAAC,CAAC;MAC7D;MACAJ,UAAU,CAACK,IAAI,CAACE,UAAU,CAACL,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;MACrCD,YAAY,GAAGC,KAAK,CAACE,KAAK,GAAGF,KAAK,CAAC,CAAC,CAAC,CAACM,MAAM;IAC9C;IACA,IAAA1B,kBAAS,EACPkB,UAAU,CAACQ,MAAM,GAAG,CAAC,EACrB,gEACF,CAAC;IACD,IAAIP,YAAY,GAAGpB,KAAK,CAAC2B,MAAM,EAAE;MAC/BR,UAAU,CAACK,IAAI,CAACxB,KAAK,CAACyB,SAAS,CAACL,YAAY,EAAEpB,KAAK,CAAC2B,MAAM,CAAC,CAAC;IAC9D;IACA,OAAO;MAACT,OAAO,EAAE,KAAK;MAAEC,UAAU,EAAVA;IAAU,CAAC;EACrC;AACF;AAUA,SAASS,yBAAyBA,CAChCtC,MAAuC,EACZ;EAC3B,IAAAW,kBAAS,EAACX,MAAM,CAACC,WAAW,CAACoC,MAAM,IAAI,CAAC,EAAE,kBAAkB,CAAC;EAC7D,IAAMpC,WAAW,GAAGD,MAAM,CAACC,WAAW,CAACsC,GAAG,CAACjB,4BAA4B,CAAC;EAExE,IAAMM,OAAO,GAAG3B,WAAW,CAAC,CAAC,CAAC,CAAC2B,OAAO;EACtC,IAAIY,OAAO,EAAE;IACX,IAAA7B,kBAAS,EACPV,WAAW,CAACwC,KAAK,CAAC,UAAAC,MAAM;MAAA,OAAIA,MAAM,CAACd,OAAO,KAAKA,OAAO;IAAA,EAAC,EACvD,2FACF,CAAC;IACD,IAAMe,WAAW,GAAG1C,WAAW,CAAC,CAAC,CAAC,CAAC4B,UAAU;IAC7C,IAAAlB,kBAAS,EACPV,WAAW,CAACwC,KAAK,CACf,UAAAC,MAAM;MAAA,OAAIA,MAAM,CAACb,UAAU,CAACQ,MAAM,KAAKM,WAAW,CAACN,MAAM;IAAA,CAC3D,CAAC,EACD,wEACF,CAAC;IACD,IAAA1B,kBAAS,EACPV,WAAW,CAACwC,KAAK,CAAC,UAAAC,MAAM;MAAA,OACtBA,MAAM,CAACb,UAAU,CAACY,KAAK,CACrB,UAACG,SAAS,EAAEC,CAAC;QAAA,OAEX,OAAOD,SAAS,KAAK,QAAQ,IAAIA,SAAS,KAAKD,WAAW,CAACE,CAAC,CAAC;MAAA,CACjE,CAAC;IAAA,CACH,CAAC,EACD,0EACF,CAAC;EACH;EAEA,IAAMC,iBAAyD,GAC7D7C,WAAW,CAACsC,GAAG,CAAC,UAAAG,MAAM;IAAA,OACpBd,OAAO,GAEHc,MAAM,CAACb,UAAU,GAEjBa,MAAM,CAACb,UAAU,CAACkB,MAAM,CAAC,UAAAC,CAAC;MAAA,OAAI,OAAOA,CAAC,KAAK,QAAQ;IAAA,EAAC;EAAA,CAC1D,CAAC;EACH,IAAMC,cAAc,GAAGH,iBAAiB,CAAC,CAAC,CAAC,CAACP,GAAG,CAAC,UAACW,CAAC,EAAEL,CAAC;IAAA,OACnD9C,0BAA0B,CAAAlC,MAAA,CAAAsF,MAAA,KACrBnD,MAAM;MACTC,WAAW,EAAE6C,iBAAiB,CAACP,GAAG,CAAC,UAAAV,UAAU;QAAA,OAAIA,UAAU,CAACgB,CAAC,CAAC;MAAA;IAAC,EAChE,CAAC;EAAA,CACJ,CAAC;EACD,IAAI,CAACjB,OAAO,EAAE;IACZ,OAAO,UAAAlB,KAAK,EAAI;MACd,IAAM0C,MAAM,GAAGH,cAAc,CAACV,GAAG,CAAC,UAAAc,aAAa;QAAA,OAAIA,aAAa,CAAC3C,KAAK,CAAC;MAAA,EAAC;MACxE,IAAImC,CAAC,GAAG,CAAC;MACT,OAAO5C,WAAW,CAAC,CAAC,CAAC,CAAC4B,UAAU,CAC7BU,GAAG,CAAC,UAAAS,CAAC;QAAA,OAAK,OAAOA,CAAC,KAAK,QAAQ,GAAGI,MAAM,CAACP,CAAC,EAAE,CAAC,GAAGG,CAAC;MAAA,CAAC,CAAC,CACnDM,IAAI,CAAC,EAAE,CAAC;IACb,CAAC;EACH,CAAC,MAAM;IACL,OAAO,UAAA5C,KAAK,EAAI;MACd,IAAMS,MAAM,GAAG8B,cAAc,CAACV,GAAG,CAAC,UAACc,aAAa,EAAER,CAAC,EAAK;QACtD,IAAM7E,KAAK,GAAGqF,aAAa,CAAC3C,KAAK,CAAC;QAGlC,OAAOmC,CAAC,GAAG,CAAC,GAAGU,IAAI,CAACC,KAAK,CAACxF,KAAK,CAAC,GAAGuF,IAAI,CAACC,KAAK,CAACxF,KAAK,GAAG,IAAI,CAAC,GAAG,IAAI;MACpE,CAAC,CAAC;MACF,OAAO,QAAQmD,MAAM,CAAC,CAAC,CAAC,KAAKA,MAAM,CAAC,CAAC,CAAC,KAAKA,MAAM,CAAC,CAAC,CAAC,KAAKA,MAAM,CAAC,CAAC,CAAC,GAAG;IACvE,CAAC;EACH;AACF;AAEA,SAASN,SAASA,CAACH,KAAa,EAAER,UAAkC,EAAE;EACpE,IAAI2C,CAAC;EACL,KAAKA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG3C,UAAU,CAACmC,MAAM,GAAG,CAAC,EAAE,EAAEQ,CAAC,EAAE;IAC1C,IAAI3C,UAAU,CAAC2C,CAAC,CAAC,IAAInC,KAAK,EAAE;MAC1B;IACF;EACF;EACA,OAAOmC,CAAC,GAAG,CAAC;AACd;AAEA,SAASY,gBAAgBA,CACvBvD,UAAkC,EAClCD,WAAoC,EACpC;EACAyD,kBAAkB,CAAC,aAAa,EAAEzD,WAAW,CAAC;EAC9CyD,kBAAkB,CAAC,YAAY,EAAExD,UAAU,CAAC;EAC5CyD,oBAAoB,CAACzD,UAAU,CAAC;EAEhC,IAAAS,kBAAS,EACPT,UAAU,CAACmC,MAAM,KAAKpC,WAAW,CAACoC,MAAM,EACxC,cAAc,GACZnC,UAAU,CAACmC,MAAM,GACjB,qBAAqB,GACrBpC,WAAW,CAACoC,MAAM,GAClB,6BACJ,CAAC;AACH;AAEA,SAASsB,oBAAoBA,CAACC,GAA2B,EAAE;EACzD,IAAAjD,kBAAS,EAACiD,GAAG,CAACvB,MAAM,IAAI,CAAC,EAAE,0CAA0C,CAAC;EACtE,IAAMwB,OAAO,GACX,kDAAkD,GAAGC,MAAM,CAACF,GAAG,CAAC;EAClE,KAAK,IAAIf,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGe,GAAG,CAACvB,MAAM,EAAE,EAAEQ,CAAC,EAAE;IACnC,IAAAlC,kBAAS,EAACiD,GAAG,CAACf,CAAC,CAAC,IAAIe,GAAG,CAACf,CAAC,GAAG,CAAC,CAAC,EAAEgB,OAAO,CAAC;EAC1C;AACF;AAEA,SAASH,kBAAkBA,CACzBK,IAAY,EACZH,GAA4B,EAC5B;EACA,IAAAjD,kBAAS,EAACiD,GAAG,CAACvB,MAAM,IAAI,CAAC,EAAE0B,IAAI,GAAG,gCAAgC,CAAC;EACnE,IAAApD,kBAAS,EACPiD,GAAG,CAACvB,MAAM,KAAK,CAAC,IAAIuB,GAAG,CAAC,CAAC,CAAC,KAAK,CAACxC,QAAQ,IAAIwC,GAAG,CAAC,CAAC,CAAC,KAAKxC,QAAQ,EAO/D2C,IAAI,GAAG,kCAAkC,GAAGH,GAC9C,CAAC;AACH;AAAC,IAEoBI,qBAAqB,GAAAjG,OAAA,CAAAE,OAAA,aAAAgG,qBAAA;EAOxC,SAAAD,sBAAYE,MAAoB,EAAElE,MAAwC,EAAE;IAAA,IAAAmE,KAAA;IAAA,IAAAjG,gBAAA,CAAAD,OAAA,QAAA+F,qBAAA;IAC1EG,KAAA,GAAApF,UAAA,OAAAiF,qBAAA,GAAMhE,MAAM;IACZmE,KAAA,CAAKC,OAAO,GAAGF,MAAM;IACrBC,KAAA,CAAKE,OAAO,GAAGrE,MAAM;IAErB,IAAIwC,OAAO,EAAE;MACXiB,gBAAgB,CAACzD,MAAM,CAACE,UAAU,EAAEF,MAAM,CAACC,WAAW,CAAC;MAIvDkE,KAAA,CAAKG,iBAAiB,CAAC,CAAC;IAC1B;IAAC,OAAAH,KAAA;EACH;EAAC,IAAA5F,UAAA,CAAAN,OAAA,EAAA+F,qBAAA,EAAAC,qBAAA;EAAA,WAAA9F,aAAA,CAAAF,OAAA,EAAA+F,qBAAA;IAAAO,GAAA;IAAAvG,KAAA,EAED,SAAAsG,iBAAiBA,CAAA,EAAsB;MACrC,IAAI,CAAC,IAAI,CAACE,cAAc,EAAE;QACxB,IAAMxE,MAAM,GAAG,IAAI,CAACqE,OAAO;QAC3B,IAAIrE,MAAM,CAACC,WAAW,IAAI,OAAOD,MAAM,CAACC,WAAW,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAE;UACnE,IAAI,CAACuE,cAAc,GAAIlC,yBAAyB,CAAEtC,MAAY,CAAO;QACvE,CAAC,MAAM;UACL,IAAI,CAACwE,cAAc,GAAIzE,0BAA0B,CAAEC,MAAY,CAAO;QACxE;MACF;MACA,OAAO,IAAI,CAACwE,cAAc;IAC5B;EAAC;IAAAD,GAAA;IAAAvG,KAAA,EAED,SAAAyG,YAAYA,CAACC,cAA+B,EAAE;MAC5C,IAAI,CAACN,OAAO,CAACK,YAAY,CAACC,cAAc,CAAC;MACzC9E,aAAA,CAAAoE,qBAAA,4BAAmBU,cAAc;IACnC;EAAC;IAAAH,GAAA;IAAAvG,KAAA,EAED,SAAA2G,UAAUA,CAAA,EAAY;MACpB,IAAMC,WAAmB,GAAG,IAAI,CAACR,OAAO,CAACO,UAAU,CAAC,CAAC;MACrD,IAAAhE,kBAAS,EACP,OAAOiE,WAAW,KAAK,QAAQ,EAC/B,oDACF,CAAC;MACD,OAAO,IAAI,CAACN,iBAAiB,CAAC,CAAC,CAACM,WAAW,CAAC;IAC9C;EAAC;IAAAL,GAAA;IAAAvG,KAAA,EAED,SAAA8C,WAAWA,CACTd,MAA2C,EACR;MACnC,OAAO,IAAIgE,qBAAqB,CAAC,IAAI,EAAEhE,MAAM,CAAC;IAChD;EAAC;IAAAuE,GAAA;IAAAvG,KAAA,EAED,SAAA6G,QAAQA,CAAA,EAAS;MACf,IAAI,CAACT,OAAO,CAACU,UAAU,CAAC,IAAI,CAAC;MAC7BlF,aAAA,CAAAoE,qBAAA;IACF;EAAC;IAAAO,GAAA;IAAAvG,KAAA,EAED,SAAA+G,QAAQA,CAAA,EAAS;MACf,IAAI,CAACX,OAAO,CAACY,aAAa,CAAC,IAAI,CAAC;MAChCpF,aAAA,CAAAoE,qBAAA;IACF;EAAC;IAAAO,GAAA;IAAAvG,KAAA,EAED,SAAAiH,iBAAiBA,CAAA,EAAQ;MACvB,IAAIzC,OAAO,EAAE;QACX,IAAA0C,+CAAqB,EAAC,IAAI,CAACb,OAAO,CAAC;MACrC;MAGA,IAAIpE,WAAW,GAAG,IAAI,CAACoE,OAAO,CAACpE,WAAW;MAC1C,IAAIkF,UAAU,GAAG,IAAI;MACrB,IAAI,OAAOlF,WAAW,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAE;QAEtCA,WAAW,GAAKA,WAAW,CAA0BsC,GAAG,CAAC,UAAAvE,KAAK,EAAI;UAChE,IAAMoH,cAAc,GAAG,IAAAC,qBAAY,EAACrH,KAAK,CAAC;UAC1C,IAAI,OAAOoH,cAAc,KAAK,QAAQ,EAAE;YACtCD,UAAU,GAAG,OAAO;YACpB,OAAOC,cAAc;UACvB,CAAC,MAAM;YACL,OAAOE,6BAAoB,CAACC,iBAAiB,CAACvH,KAAK,CAAC;UACtD;QACF,CAAC,CAAO;MACV;MAEA,OAAO;QACLkC,UAAU,EAAE,IAAI,CAACmE,OAAO,CAACnE,UAAU;QACnCD,WAAW,EAAXA,WAAW;QACXkF,UAAU,EAAVA,UAAU;QACV7E,eAAe,EACb,IAAI,CAAC+D,OAAO,CAAC/D,eAAe,IAAI,IAAI,CAAC+D,OAAO,CAAC7D,WAAW,IAAI,QAAQ;QACtEC,gBAAgB,EACd,IAAI,CAAC4D,OAAO,CAAC5D,gBAAgB,IAAI,IAAI,CAAC4D,OAAO,CAAC7D,WAAW,IAAI,QAAQ;QACvEgF,IAAI,EAAE,eAAe;QACrBC,OAAO,EAAE,IAAI,CAACC,YAAY,CAAC;MAC7B,CAAC;IACH;EAAC;AAAA,EA7FOC,8BAAoB", "ignoreList": []}