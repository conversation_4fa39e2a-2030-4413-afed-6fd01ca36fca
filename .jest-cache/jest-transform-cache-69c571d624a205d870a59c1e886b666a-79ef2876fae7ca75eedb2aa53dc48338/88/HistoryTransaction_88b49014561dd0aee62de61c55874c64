d4210ab9484ef1c0b67b1de59ebda196
"use strict";

/* istanbul ignore next */
function cov_1m4fsnvrkv() {
  var path = "/Users/<USER>/projects/react-native/payment-module/src/presentation/bill-detail/components/HistoryTransaction.tsx";
  var hash = "6d400d84c9c8f4d8f8504d414bce97197d5aa934";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/projects/react-native/payment-module/src/presentation/bill-detail/components/HistoryTransaction.tsx",
    statementMap: {
      "0": {
        start: {
          line: 3,
          column: 22
        },
        end: {
          line: 7,
          column: 1
        }
      },
      "1": {
        start: {
          line: 4,
          column: 2
        },
        end: {
          line: 6,
          column: 4
        }
      },
      "2": {
        start: {
          line: 8,
          column: 0
        },
        end: {
          line: 10,
          column: 3
        }
      },
      "3": {
        start: {
          line: 11,
          column: 14
        },
        end: {
          line: 11,
          column: 47
        }
      },
      "4": {
        start: {
          line: 12,
          column: 21
        },
        end: {
          line: 12,
          column: 44
        }
      },
      "5": {
        start: {
          line: 13,
          column: 29
        },
        end: {
          line: 13,
          column: 60
        }
      },
      "6": {
        start: {
          line: 14,
          column: 18
        },
        end: {
          line: 14,
          column: 78
        }
      },
      "7": {
        start: {
          line: 15,
          column: 20
        },
        end: {
          line: 15,
          column: 74
        }
      },
      "8": {
        start: {
          line: 16,
          column: 21
        },
        end: {
          line: 16,
          column: 58
        }
      },
      "9": {
        start: {
          line: 17,
          column: 13
        },
        end: {
          line: 17,
          column: 45
        }
      },
      "10": {
        start: {
          line: 18,
          column: 25
        },
        end: {
          line: 91,
          column: 1
        }
      },
      "11": {
        start: {
          line: 19,
          column: 13
        },
        end: {
          line: 19,
          column: 22
        }
      },
      "12": {
        start: {
          line: 20,
          column: 14
        },
        end: {
          line: 20,
          column: 26
        }
      },
      "13": {
        start: {
          line: 21,
          column: 15
        },
        end: {
          line: 21,
          column: 28
        }
      },
      "14": {
        start: {
          line: 22,
          column: 21
        },
        end: {
          line: 22,
          column: 40
        }
      },
      "15": {
        start: {
          line: 23,
          column: 12
        },
        end: {
          line: 23,
          column: 22
        }
      },
      "16": {
        start: {
          line: 24,
          column: 14
        },
        end: {
          line: 24,
          column: 65
        }
      },
      "17": {
        start: {
          line: 25,
          column: 13
        },
        end: {
          line: 25,
          column: 25
        }
      },
      "18": {
        start: {
          line: 26,
          column: 12
        },
        end: {
          line: 26,
          column: 23
        }
      },
      "19": {
        start: {
          line: 27,
          column: 2
        },
        end: {
          line: 34,
          column: 3
        }
      },
      "20": {
        start: {
          line: 28,
          column: 4
        },
        end: {
          line: 33,
          column: 8
        }
      },
      "21": {
        start: {
          line: 35,
          column: 28
        },
        end: {
          line: 46,
          column: 3
        }
      },
      "22": {
        start: {
          line: 36,
          column: 18
        },
        end: {
          line: 36,
          column: 31
        }
      },
      "23": {
        start: {
          line: 37,
          column: 4
        },
        end: {
          line: 39,
          column: 5
        }
      },
      "24": {
        start: {
          line: 38,
          column: 6
        },
        end: {
          line: 38,
          column: 18
        }
      },
      "25": {
        start: {
          line: 40,
          column: 4
        },
        end: {
          line: 45,
          column: 8
        }
      },
      "26": {
        start: {
          line: 47,
          column: 19
        },
        end: {
          line: 68,
          column: 3
        }
      },
      "27": {
        start: {
          line: 48,
          column: 15
        },
        end: {
          line: 48,
          column: 25
        }
      },
      "28": {
        start: {
          line: 49,
          column: 4
        },
        end: {
          line: 67,
          column: 9
        }
      },
      "29": {
        start: {
          line: 69,
          column: 2
        },
        end: {
          line: 75,
          column: 3
        }
      },
      "30": {
        start: {
          line: 70,
          column: 4
        },
        end: {
          line: 74,
          column: 7
        }
      },
      "31": {
        start: {
          line: 76,
          column: 2
        },
        end: {
          line: 90,
          column: 5
        }
      },
      "32": {
        start: {
          line: 79,
          column: 6
        },
        end: {
          line: 79,
          column: 21
        }
      },
      "33": {
        start: {
          line: 85,
          column: 6
        },
        end: {
          line: 87,
          column: 9
        }
      },
      "34": {
        start: {
          line: 92,
          column: 16
        },
        end: {
          line: 154,
          column: 2
        }
      },
      "35": {
        start: {
          line: 93,
          column: 20
        },
        end: {
          line: 93,
          column: 37
        }
      },
      "36": {
        start: {
          line: 94,
          column: 17
        },
        end: {
          line: 94,
          column: 33
        }
      },
      "37": {
        start: {
          line: 95,
          column: 17
        },
        end: {
          line: 95,
          column: 33
        }
      },
      "38": {
        start: {
          line: 96,
          column: 2
        },
        end: {
          line: 153,
          column: 4
        }
      },
      "39": {
        start: {
          line: 155,
          column: 0
        },
        end: {
          line: 155,
          column: 37
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 3,
            column: 54
          },
          end: {
            line: 3,
            column: 55
          }
        },
        loc: {
          start: {
            line: 3,
            column: 69
          },
          end: {
            line: 7,
            column: 1
          }
        },
        line: 3
      },
      "1": {
        name: "HistoryTransaction",
        decl: {
          start: {
            line: 18,
            column: 34
          },
          end: {
            line: 18,
            column: 52
          }
        },
        loc: {
          start: {
            line: 18,
            column: 59
          },
          end: {
            line: 91,
            column: 1
          }
        },
        line: 18
      },
      "2": {
        name: "renderSectionHeader",
        decl: {
          start: {
            line: 35,
            column: 37
          },
          end: {
            line: 35,
            column: 56
          }
        },
        loc: {
          start: {
            line: 35,
            column: 64
          },
          end: {
            line: 46,
            column: 3
          }
        },
        line: 35
      },
      "3": {
        name: "renderItem",
        decl: {
          start: {
            line: 47,
            column: 28
          },
          end: {
            line: 47,
            column: 38
          }
        },
        loc: {
          start: {
            line: 47,
            column: 46
          },
          end: {
            line: 68,
            column: 3
          }
        },
        line: 47
      },
      "4": {
        name: "keyExtractor",
        decl: {
          start: {
            line: 78,
            column: 27
          },
          end: {
            line: 78,
            column: 39
          }
        },
        loc: {
          start: {
            line: 78,
            column: 46
          },
          end: {
            line: 80,
            column: 5
          }
        },
        line: 78
      },
      "5": {
        name: "ItemSeparatorComponent",
        decl: {
          start: {
            line: 84,
            column: 37
          },
          end: {
            line: 84,
            column: 59
          }
        },
        loc: {
          start: {
            line: 84,
            column: 62
          },
          end: {
            line: 88,
            column: 5
          }
        },
        line: 84
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 92,
            column: 64
          },
          end: {
            line: 92,
            column: 65
          }
        },
        loc: {
          start: {
            line: 92,
            column: 81
          },
          end: {
            line: 154,
            column: 1
          }
        },
        line: 92
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 3,
            column: 22
          },
          end: {
            line: 7,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 3,
            column: 22
          },
          end: {
            line: 3,
            column: 26
          }
        }, {
          start: {
            line: 3,
            column: 30
          },
          end: {
            line: 3,
            column: 50
          }
        }, {
          start: {
            line: 3,
            column: 54
          },
          end: {
            line: 7,
            column: 1
          }
        }],
        line: 3
      },
      "1": {
        loc: {
          start: {
            line: 4,
            column: 9
          },
          end: {
            line: 6,
            column: 3
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 4,
            column: 33
          },
          end: {
            line: 4,
            column: 36
          }
        }, {
          start: {
            line: 4,
            column: 39
          },
          end: {
            line: 6,
            column: 3
          }
        }],
        line: 4
      },
      "2": {
        loc: {
          start: {
            line: 4,
            column: 9
          },
          end: {
            line: 4,
            column: 30
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 4,
            column: 9
          },
          end: {
            line: 4,
            column: 12
          }
        }, {
          start: {
            line: 4,
            column: 16
          },
          end: {
            line: 4,
            column: 30
          }
        }],
        line: 4
      },
      "3": {
        loc: {
          start: {
            line: 27,
            column: 2
          },
          end: {
            line: 34,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 27,
            column: 2
          },
          end: {
            line: 34,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 27
      },
      "4": {
        loc: {
          start: {
            line: 27,
            column: 6
          },
          end: {
            line: 27,
            column: 25
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 27,
            column: 6
          },
          end: {
            line: 27,
            column: 13
          }
        }, {
          start: {
            line: 27,
            column: 17
          },
          end: {
            line: 27,
            column: 25
          }
        }],
        line: 27
      },
      "5": {
        loc: {
          start: {
            line: 37,
            column: 4
          },
          end: {
            line: 39,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 37,
            column: 4
          },
          end: {
            line: 39,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 37
      },
      "6": {
        loc: {
          start: {
            line: 69,
            column: 2
          },
          end: {
            line: 75,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 69,
            column: 2
          },
          end: {
            line: 75,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 69
      },
      "7": {
        loc: {
          start: {
            line: 71,
            column: 12
          },
          end: {
            line: 71,
            column: 155
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 71,
            column: 68
          },
          end: {
            line: 71,
            column: 109
          }
        }, {
          start: {
            line: 71,
            column: 112
          },
          end: {
            line: 71,
            column: 155
          }
        }],
        line: 71
      },
      "8": {
        loc: {
          start: {
            line: 102,
            column: 41
          },
          end: {
            line: 102,
            column: 96
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 102,
            column: 62
          },
          end: {
            line: 102,
            column: 68
          }
        }, {
          start: {
            line: 102,
            column: 71
          },
          end: {
            line: 102,
            column: 96
          }
        }],
        line: 102
      },
      "9": {
        loc: {
          start: {
            line: 118,
            column: 33
          },
          end: {
            line: 118,
            column: 88
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 118,
            column: 54
          },
          end: {
            line: 118,
            column: 60
          }
        }, {
          start: {
            line: 118,
            column: 63
          },
          end: {
            line: 118,
            column: 88
          }
        }],
        line: 118
      },
      "10": {
        loc: {
          start: {
            line: 122,
            column: 31
          },
          end: {
            line: 122,
            column: 85
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 122,
            column: 52
          },
          end: {
            line: 122,
            column: 58
          }
        }, {
          start: {
            line: 122,
            column: 61
          },
          end: {
            line: 122,
            column: 85
          }
        }],
        line: 122
      },
      "11": {
        loc: {
          start: {
            line: 129,
            column: 30
          },
          end: {
            line: 129,
            column: 85
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 129,
            column: 51
          },
          end: {
            line: 129,
            column: 57
          }
        }, {
          start: {
            line: 129,
            column: 60
          },
          end: {
            line: 129,
            column: 85
          }
        }],
        line: 129
      },
      "12": {
        loc: {
          start: {
            line: 133,
            column: 28
          },
          end: {
            line: 133,
            column: 81
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 133,
            column: 49
          },
          end: {
            line: 133,
            column: 55
          }
        }, {
          start: {
            line: 133,
            column: 58
          },
          end: {
            line: 133,
            column: 81
          }
        }],
        line: 133
      },
      "13": {
        loc: {
          start: {
            line: 150,
            column: 33
          },
          end: {
            line: 150,
            column: 87
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 150,
            column: 54
          },
          end: {
            line: 150,
            column: 60
          }
        }, {
          start: {
            line: 150,
            column: 63
          },
          end: {
            line: 150,
            column: 87
          }
        }],
        line: 150
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0
    },
    b: {
      "0": [0, 0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0]
    },
    inputSourceMap: {
      version: 3,
      file: undefined,
      names: ["react_1", "__importDefault", "require", "react_native_1", "msb_shared_component_1", "EmptyBill_1", "FormatUtils_1", "MSBErrorCode_1", "i18n_1", "HistoryTransaction", "_ref", "data", "loading", "fetching", "containerStyle", "error", "_ref2", "useMSBStyles", "makeStyle", "styles", "theme", "default", "createElement", "View", "style", "loadingContainer", "ActivityIndicator", "size", "color", "ColorGlobal", "Neutral600", "renderSectionHeader", "_ref3", "section", "title", "sectionHeader", "MSBTextBase", "sectionHeaderText", "content", "renderItem", "_ref4", "item", "itemContainer", "itemLeft", "transName", "itemRight", "amount", "formatMoney", "time", "formatDateHHMM", "transDate", "EmptyTransactionHistoryScreen", "type", "code", "MSBErrorCode", "EMPTY_DATA", "EmptyType", "Editable", "Connection", "translate", "message", "SectionList", "sections", "keyExtractor", "id", "ItemSeparatorComponent", "separator", "stickySectionHeadersEnabled", "createMSBStyleSheet", "_ref5", "SizeGlobal", "Typography", "backgroundColor", "Neutral50", "paddingVertical", "Size300", "paddingHorizontal", "Size400", "Object", "assign", "caption_medium", "flexDirection", "justifyContent", "alignItems", "borderBottomWidth", "borderBottomColor", "Neutral100", "marginHorizontal", "flex", "marginRight", "Size200", "small_semiBold", "Neutral800", "marginBottom", "Size100", "small_regular", "Green500", "small_medium", "height", "getSize", "emptyContainer", "emptyText", "Neutral400", "exports"],
      sourceRoot: undefined,
      sources: ["/Users/<USER>/projects/react-native/payment-module/src/presentation/bill-detail/components/HistoryTransaction.tsx"],
      sourcesContent: ["import React from 'react';\nimport {View, ActivityIndicator, SectionList} from 'react-native';\nimport {EmptyType, MSBTextBase, createMSBStyleSheet, getSize, useMSBStyles} from 'msb-shared-component';\nimport {EmptyTransactionHistoryScreen} from '../../payment-home/components/bill-list/EmptyBill';\nimport FormatUtils from '../../../utils/FormatUtils';\nimport {CustomError} from '../../../core/MSBCustomError';\nimport {MSBErrorCode} from '../../../core/MSBErrorCode';\nimport {I18nKeys, translate} from '../../../locales/i18n';\n\nexport interface TransactionItem {\n  id: string;\n  transName: string;\n  content: string;\n  amount: string;\n  transDate: string;\n}\n\nexport interface HistoryTransactionProps {\n  data: {id: string; title?: string; data: TransactionItem[]}[];\n  loading?: boolean;\n  fetching?: boolean;\n  containerStyle?: any;\n  error?: CustomError;\n}\n\nconst HistoryTransaction: React.FC<HistoryTransactionProps> = ({data, loading, fetching, containerStyle, error}) => {\n  const {styles, theme} = useMSBStyles(makeStyle);\n\n  if (loading || fetching) {\n    return (\n      <View style={[styles.loadingContainer, containerStyle]}>\n        <ActivityIndicator size=\"small\" color={theme.ColorGlobal.Neutral600} />\n      </View>\n    );\n  }\n\n  const renderSectionHeader = ({section}: {section: {title?: string}}) => {\n    if (!section.title) {\n      return null;\n    }\n\n    return (\n      <View style={styles.sectionHeader}>\n        <MSBTextBase style={styles.sectionHeaderText} content={section.title} />\n      </View>\n    );\n  };\n\n  const renderItem = ({item}: {item: TransactionItem}) => (\n    <View style={styles.itemContainer}>\n      <View style={[styles.itemLeft]}>\n        <MSBTextBase style={styles.transName} content={item.transName} />\n        <MSBTextBase style={styles.content} content={item.content} />\n      </View>\n      <View style={styles.itemRight}>\n        <MSBTextBase style={styles.amount} content={'+' + FormatUtils.formatMoney(item.amount)} />\n        <MSBTextBase style={styles.time} content={FormatUtils.formatDateHHMM(item.transDate)} />\n      </View>\n    </View>\n  );\n\n  if (error) {\n    return (\n      <EmptyTransactionHistoryScreen\n        type={error.code === MSBErrorCode.EMPTY_DATA ? EmptyType.Editable : EmptyType.Connection}\n        title={translate(error.title as I18nKeys)}\n        content={translate(error.message as I18nKeys)}\n      />\n    );\n  }\n\n  return (\n    <SectionList\n      sections={data}\n      keyExtractor={item => item.id}\n      style={containerStyle}\n      renderItem={renderItem}\n      renderSectionHeader={renderSectionHeader}\n      ItemSeparatorComponent={() => <View style={styles.separator} />}\n      stickySectionHeadersEnabled={false}\n    />\n  );\n};\n\nconst makeStyle = createMSBStyleSheet(({ColorGlobal, SizeGlobal, Typography}) => ({\n  sectionHeader: {\n    backgroundColor: ColorGlobal.Neutral50,\n    paddingVertical: SizeGlobal.Size300,\n    paddingHorizontal: SizeGlobal.Size400,\n  },\n  sectionHeaderText: {\n    ...Typography?.caption_medium,\n    color: ColorGlobal.Neutral600,\n  },\n  itemContainer: {\n    flexDirection: 'row',\n    justifyContent: 'space-between',\n    alignItems: 'flex-start',\n    paddingVertical: SizeGlobal.Size400,\n    borderBottomWidth: 1,\n    borderBottomColor: ColorGlobal.Neutral100,\n    marginHorizontal: 0,\n  },\n  itemLeft: {\n    flex: 0.7,\n    marginRight: SizeGlobal.Size200,\n  },\n  transName: {\n    ...Typography?.small_semiBold,\n    color: ColorGlobal.Neutral800,\n    marginBottom: SizeGlobal.Size100,\n  },\n  content: {\n    ...Typography?.small_regular,\n    color: ColorGlobal.Neutral600,\n  },\n  itemRight: {\n    alignItems: 'flex-end',\n    flex: 0.3,\n  },\n  amount: {\n    ...Typography?.small_semiBold,\n    color: ColorGlobal.Green500,\n    marginBottom: SizeGlobal.Size100,\n  },\n  time: {\n    ...Typography?.small_medium,\n    color: ColorGlobal.Neutral600,\n  },\n  separator: {\n    height: 1,\n    backgroundColor: ColorGlobal.Neutral100,\n  },\n  loadingContainer: {\n    justifyContent: 'center',\n    alignItems: 'center',\n    height: getSize(120),\n  },\n  emptyContainer: {\n    justifyContent: 'center',\n    alignItems: 'center',\n    height: getSize(120),\n  },\n  emptyText: {\n    ...Typography?.small_regular,\n    color: ColorGlobal.Neutral400,\n  },\n}));\n\nexport default HistoryTransaction;\n"],
      mappings: ";;;;;;;;;;AAAA,IAAAA,OAAA,GAAAC,eAAA,CAAAC,OAAA;AACA,IAAAC,cAAA,GAAAD,OAAA;AACA,IAAAE,sBAAA,GAAAF,OAAA;AACA,IAAAG,WAAA,GAAAH,OAAA;AACA,IAAAI,aAAA,GAAAL,eAAA,CAAAC,OAAA;AAEA,IAAAK,cAAA,GAAAL,OAAA;AACA,IAAAM,MAAA,GAAAN,OAAA;AAkBA,IAAMO,kBAAkB,GAAsC,SAAxDA,kBAAkBA,CAAAC,IAAA,EAA2F;EAAA,IAAnDC,IAAI,GAAAD,IAAA,CAAJC,IAAI;IAAEC,OAAO,GAAAF,IAAA,CAAPE,OAAO;IAAEC,QAAQ,GAAAH,IAAA,CAARG,QAAQ;IAAEC,cAAc,GAAAJ,IAAA,CAAdI,cAAc;IAAEC,KAAK,GAAAL,IAAA,CAALK,KAAK;EAC5G,IAAAC,KAAA,GAAwB,IAAAZ,sBAAA,CAAAa,YAAY,EAACC,SAAS,CAAC;IAAxCC,MAAM,GAAAH,KAAA,CAANG,MAAM;IAAEC,KAAK,GAAAJ,KAAA,CAALI,KAAK;EAEpB,IAAIR,OAAO,IAAIC,QAAQ,EAAE;IACvB,OACEb,OAAA,CAAAqB,OAAA,CAAAC,aAAA,CAACnB,cAAA,CAAAoB,IAAI;MAACC,KAAK,EAAE,CAACL,MAAM,CAACM,gBAAgB,EAAEX,cAAc;IAAC,GACpDd,OAAA,CAAAqB,OAAA,CAAAC,aAAA,CAACnB,cAAA,CAAAuB,iBAAiB;MAACC,IAAI,EAAC,OAAO;MAACC,KAAK,EAAER,KAAK,CAACS,WAAW,CAACC;IAAU,EAAI,CAClE;EAEX;EAEA,IAAMC,mBAAmB,GAAG,SAAtBA,mBAAmBA,CAAAC,KAAA,EAA8C;IAAA,IAAzCC,OAAO,GAAAD,KAAA,CAAPC,OAAO;IACnC,IAAI,CAACA,OAAO,CAACC,KAAK,EAAE;MAClB,OAAO,IAAI;IACb;IAEA,OACElC,OAAA,CAAAqB,OAAA,CAAAC,aAAA,CAACnB,cAAA,CAAAoB,IAAI;MAACC,KAAK,EAAEL,MAAM,CAACgB;IAAa,GAC/BnC,OAAA,CAAAqB,OAAA,CAAAC,aAAA,CAAClB,sBAAA,CAAAgC,WAAW;MAACZ,KAAK,EAAEL,MAAM,CAACkB,iBAAiB;MAAEC,OAAO,EAAEL,OAAO,CAACC;IAAK,EAAI,CACnE;EAEX,CAAC;EAED,IAAMK,UAAU,GAAG,SAAbA,UAAUA,CAAAC,KAAA;IAAA,IAAKC,IAAI,GAAAD,KAAA,CAAJC,IAAI;IAAA,OACvBzC,OAAA,CAAAqB,OAAA,CAAAC,aAAA,CAACnB,cAAA,CAAAoB,IAAI;MAACC,KAAK,EAAEL,MAAM,CAACuB;IAAa,GAC/B1C,OAAA,CAAAqB,OAAA,CAAAC,aAAA,CAACnB,cAAA,CAAAoB,IAAI;MAACC,KAAK,EAAE,CAACL,MAAM,CAACwB,QAAQ;IAAC,GAC5B3C,OAAA,CAAAqB,OAAA,CAAAC,aAAA,CAAClB,sBAAA,CAAAgC,WAAW;MAACZ,KAAK,EAAEL,MAAM,CAACyB,SAAS;MAAEN,OAAO,EAAEG,IAAI,CAACG;IAAS,EAAI,EACjE5C,OAAA,CAAAqB,OAAA,CAAAC,aAAA,CAAClB,sBAAA,CAAAgC,WAAW;MAACZ,KAAK,EAAEL,MAAM,CAACmB,OAAO;MAAEA,OAAO,EAAEG,IAAI,CAACH;IAAO,EAAI,CACxD,EACPtC,OAAA,CAAAqB,OAAA,CAAAC,aAAA,CAACnB,cAAA,CAAAoB,IAAI;MAACC,KAAK,EAAEL,MAAM,CAAC0B;IAAS,GAC3B7C,OAAA,CAAAqB,OAAA,CAAAC,aAAA,CAAClB,sBAAA,CAAAgC,WAAW;MAACZ,KAAK,EAAEL,MAAM,CAAC2B,MAAM;MAAER,OAAO,EAAE,GAAG,GAAGhC,aAAA,CAAAe,OAAW,CAAC0B,WAAW,CAACN,IAAI,CAACK,MAAM;IAAC,EAAI,EAC1F9C,OAAA,CAAAqB,OAAA,CAAAC,aAAA,CAAClB,sBAAA,CAAAgC,WAAW;MAACZ,KAAK,EAAEL,MAAM,CAAC6B,IAAI;MAAEV,OAAO,EAAEhC,aAAA,CAAAe,OAAW,CAAC4B,cAAc,CAACR,IAAI,CAACS,SAAS;IAAC,EAAI,CACnF,CACF;EAAA,CACR;EAED,IAAInC,KAAK,EAAE;IACT,OACEf,OAAA,CAAAqB,OAAA,CAAAC,aAAA,CAACjB,WAAA,CAAA8C,6BAA6B;MAC5BC,IAAI,EAAErC,KAAK,CAACsC,IAAI,KAAK9C,cAAA,CAAA+C,YAAY,CAACC,UAAU,GAAGnD,sBAAA,CAAAoD,SAAS,CAACC,QAAQ,GAAGrD,sBAAA,CAAAoD,SAAS,CAACE,UAAU;MACxFxB,KAAK,EAAE,IAAA1B,MAAA,CAAAmD,SAAS,EAAC5C,KAAK,CAACmB,KAAiB,CAAC;MACzCI,OAAO,EAAE,IAAA9B,MAAA,CAAAmD,SAAS,EAAC5C,KAAK,CAAC6C,OAAmB;IAAC,EAC7C;EAEN;EAEA,OACE5D,OAAA,CAAAqB,OAAA,CAAAC,aAAA,CAACnB,cAAA,CAAA0D,WAAW;IACVC,QAAQ,EAAEnD,IAAI;IACdoD,YAAY,EAAE,SAAdA,YAAYA,CAAEtB,IAAI;MAAA,OAAIA,IAAI,CAACuB,EAAE;IAAA;IAC7BxC,KAAK,EAAEV,cAAc;IACrByB,UAAU,EAAEA,UAAU;IACtBR,mBAAmB,EAAEA,mBAAmB;IACxCkC,sBAAsB,EAAE,SAAxBA,sBAAsBA,CAAA;MAAA,OAAQjE,OAAA,CAAAqB,OAAA,CAAAC,aAAA,CAACnB,cAAA,CAAAoB,IAAI;QAACC,KAAK,EAAEL,MAAM,CAAC+C;MAAS,EAAI;IAAA;IAC/DC,2BAA2B,EAAE;EAAK,EAClC;AAEN,CAAC;AAED,IAAMjD,SAAS,GAAG,IAAAd,sBAAA,CAAAgE,mBAAmB,EAAC,UAAAC,KAAA;EAAA,IAAExC,WAAW,GAAAwC,KAAA,CAAXxC,WAAW;IAAEyC,UAAU,GAAAD,KAAA,CAAVC,UAAU;IAAEC,UAAU,GAAAF,KAAA,CAAVE,UAAU;EAAA,OAAO;IAChFpC,aAAa,EAAE;MACbqC,eAAe,EAAE3C,WAAW,CAAC4C,SAAS;MACtCC,eAAe,EAAEJ,UAAU,CAACK,OAAO;MACnCC,iBAAiB,EAAEN,UAAU,CAACO;KAC/B;IACDxC,iBAAiB,EAAAyC,MAAA,CAAAC,MAAA,KACZR,UAAU,oBAAVA,UAAU,CAAES,cAAc;MAC7BpD,KAAK,EAAEC,WAAW,CAACC;IAAU,EAC9B;IACDY,aAAa,EAAE;MACbuC,aAAa,EAAE,KAAK;MACpBC,cAAc,EAAE,eAAe;MAC/BC,UAAU,EAAE,YAAY;MACxBT,eAAe,EAAEJ,UAAU,CAACO,OAAO;MACnCO,iBAAiB,EAAE,CAAC;MACpBC,iBAAiB,EAAExD,WAAW,CAACyD,UAAU;MACzCC,gBAAgB,EAAE;KACnB;IACD5C,QAAQ,EAAE;MACR6C,IAAI,EAAE,GAAG;MACTC,WAAW,EAAEnB,UAAU,CAACoB;KACzB;IACD9C,SAAS,EAAAkC,MAAA,CAAAC,MAAA,KACJR,UAAU,oBAAVA,UAAU,CAAEoB,cAAc;MAC7B/D,KAAK,EAAEC,WAAW,CAAC+D,UAAU;MAC7BC,YAAY,EAAEvB,UAAU,CAACwB;IAAO,EACjC;IACDxD,OAAO,EAAAwC,MAAA,CAAAC,MAAA,KACFR,UAAU,oBAAVA,UAAU,CAAEwB,aAAa;MAC5BnE,KAAK,EAAEC,WAAW,CAACC;IAAU,EAC9B;IACDe,SAAS,EAAE;MACTsC,UAAU,EAAE,UAAU;MACtBK,IAAI,EAAE;KACP;IACD1C,MAAM,EAAAgC,MAAA,CAAAC,MAAA,KACDR,UAAU,oBAAVA,UAAU,CAAEoB,cAAc;MAC7B/D,KAAK,EAAEC,WAAW,CAACmE,QAAQ;MAC3BH,YAAY,EAAEvB,UAAU,CAACwB;IAAO,EACjC;IACD9C,IAAI,EAAA8B,MAAA,CAAAC,MAAA,KACCR,UAAU,oBAAVA,UAAU,CAAE0B,YAAY;MAC3BrE,KAAK,EAAEC,WAAW,CAACC;IAAU,EAC9B;IACDoC,SAAS,EAAE;MACTgC,MAAM,EAAE,CAAC;MACT1B,eAAe,EAAE3C,WAAW,CAACyD;KAC9B;IACD7D,gBAAgB,EAAE;MAChByD,cAAc,EAAE,QAAQ;MACxBC,UAAU,EAAE,QAAQ;MACpBe,MAAM,EAAE,IAAA9F,sBAAA,CAAA+F,OAAO,EAAC,GAAG;KACpB;IACDC,cAAc,EAAE;MACdlB,cAAc,EAAE,QAAQ;MACxBC,UAAU,EAAE,QAAQ;MACpBe,MAAM,EAAE,IAAA9F,sBAAA,CAAA+F,OAAO,EAAC,GAAG;KACpB;IACDE,SAAS,EAAAvB,MAAA,CAAAC,MAAA,KACJR,UAAU,oBAAVA,UAAU,CAAEwB,aAAa;MAC5BnE,KAAK,EAAEC,WAAW,CAACyE;IAAU;GAEhC;AAAA,CAAC,CAAC;AAEHC,OAAA,CAAAlF,OAAA,GAAeZ,kBAAkB",
      ignoreList: []
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "6d400d84c9c8f4d8f8504d414bce97197d5aa934"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_1m4fsnvrkv = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_1m4fsnvrkv();
var __importDefault =
/* istanbul ignore next */
(cov_1m4fsnvrkv().s[0]++,
/* istanbul ignore next */
(cov_1m4fsnvrkv().b[0][0]++, this) &&
/* istanbul ignore next */
(cov_1m4fsnvrkv().b[0][1]++, this.__importDefault) ||
/* istanbul ignore next */
(cov_1m4fsnvrkv().b[0][2]++, function (mod) {
  /* istanbul ignore next */
  cov_1m4fsnvrkv().f[0]++;
  cov_1m4fsnvrkv().s[1]++;
  return /* istanbul ignore next */(cov_1m4fsnvrkv().b[2][0]++, mod) &&
  /* istanbul ignore next */
  (cov_1m4fsnvrkv().b[2][1]++, mod.__esModule) ?
  /* istanbul ignore next */
  (cov_1m4fsnvrkv().b[1][0]++, mod) :
  /* istanbul ignore next */
  (cov_1m4fsnvrkv().b[1][1]++, {
    "default": mod
  });
}));
/* istanbul ignore next */
cov_1m4fsnvrkv().s[2]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
var react_1 =
/* istanbul ignore next */
(cov_1m4fsnvrkv().s[3]++, __importDefault(require("react")));
var react_native_1 =
/* istanbul ignore next */
(cov_1m4fsnvrkv().s[4]++, require("react-native"));
var msb_shared_component_1 =
/* istanbul ignore next */
(cov_1m4fsnvrkv().s[5]++, require("msb-shared-component"));
var EmptyBill_1 =
/* istanbul ignore next */
(cov_1m4fsnvrkv().s[6]++, require("../../payment-home/components/bill-list/EmptyBill"));
var FormatUtils_1 =
/* istanbul ignore next */
(cov_1m4fsnvrkv().s[7]++, __importDefault(require("../../../utils/FormatUtils")));
var MSBErrorCode_1 =
/* istanbul ignore next */
(cov_1m4fsnvrkv().s[8]++, require("../../../core/MSBErrorCode"));
var i18n_1 =
/* istanbul ignore next */
(cov_1m4fsnvrkv().s[9]++, require("../../../locales/i18n"));
/* istanbul ignore next */
cov_1m4fsnvrkv().s[10]++;
var HistoryTransaction = function HistoryTransaction(_ref) {
  /* istanbul ignore next */
  cov_1m4fsnvrkv().f[1]++;
  var data =
    /* istanbul ignore next */
    (cov_1m4fsnvrkv().s[11]++, _ref.data),
    loading =
    /* istanbul ignore next */
    (cov_1m4fsnvrkv().s[12]++, _ref.loading),
    fetching =
    /* istanbul ignore next */
    (cov_1m4fsnvrkv().s[13]++, _ref.fetching),
    containerStyle =
    /* istanbul ignore next */
    (cov_1m4fsnvrkv().s[14]++, _ref.containerStyle),
    error =
    /* istanbul ignore next */
    (cov_1m4fsnvrkv().s[15]++, _ref.error);
  var _ref2 =
    /* istanbul ignore next */
    (cov_1m4fsnvrkv().s[16]++, (0, msb_shared_component_1.useMSBStyles)(makeStyle)),
    styles =
    /* istanbul ignore next */
    (cov_1m4fsnvrkv().s[17]++, _ref2.styles),
    theme =
    /* istanbul ignore next */
    (cov_1m4fsnvrkv().s[18]++, _ref2.theme);
  /* istanbul ignore next */
  cov_1m4fsnvrkv().s[19]++;
  if (
  /* istanbul ignore next */
  (cov_1m4fsnvrkv().b[4][0]++, loading) ||
  /* istanbul ignore next */
  (cov_1m4fsnvrkv().b[4][1]++, fetching)) {
    /* istanbul ignore next */
    cov_1m4fsnvrkv().b[3][0]++;
    cov_1m4fsnvrkv().s[20]++;
    return react_1.default.createElement(react_native_1.View, {
      style: [styles.loadingContainer, containerStyle]
    }, react_1.default.createElement(react_native_1.ActivityIndicator, {
      size: "small",
      color: theme.ColorGlobal.Neutral600
    }));
  } else
  /* istanbul ignore next */
  {
    cov_1m4fsnvrkv().b[3][1]++;
  }
  cov_1m4fsnvrkv().s[21]++;
  var renderSectionHeader = function renderSectionHeader(_ref3) {
    /* istanbul ignore next */
    cov_1m4fsnvrkv().f[2]++;
    var section =
    /* istanbul ignore next */
    (cov_1m4fsnvrkv().s[22]++, _ref3.section);
    /* istanbul ignore next */
    cov_1m4fsnvrkv().s[23]++;
    if (!section.title) {
      /* istanbul ignore next */
      cov_1m4fsnvrkv().b[5][0]++;
      cov_1m4fsnvrkv().s[24]++;
      return null;
    } else
    /* istanbul ignore next */
    {
      cov_1m4fsnvrkv().b[5][1]++;
    }
    cov_1m4fsnvrkv().s[25]++;
    return react_1.default.createElement(react_native_1.View, {
      style: styles.sectionHeader
    }, react_1.default.createElement(msb_shared_component_1.MSBTextBase, {
      style: styles.sectionHeaderText,
      content: section.title
    }));
  };
  /* istanbul ignore next */
  cov_1m4fsnvrkv().s[26]++;
  var renderItem = function renderItem(_ref4) {
    /* istanbul ignore next */
    cov_1m4fsnvrkv().f[3]++;
    var item =
    /* istanbul ignore next */
    (cov_1m4fsnvrkv().s[27]++, _ref4.item);
    /* istanbul ignore next */
    cov_1m4fsnvrkv().s[28]++;
    return react_1.default.createElement(react_native_1.View, {
      style: styles.itemContainer
    }, react_1.default.createElement(react_native_1.View, {
      style: [styles.itemLeft]
    }, react_1.default.createElement(msb_shared_component_1.MSBTextBase, {
      style: styles.transName,
      content: item.transName
    }), react_1.default.createElement(msb_shared_component_1.MSBTextBase, {
      style: styles.content,
      content: item.content
    })), react_1.default.createElement(react_native_1.View, {
      style: styles.itemRight
    }, react_1.default.createElement(msb_shared_component_1.MSBTextBase, {
      style: styles.amount,
      content: '+' + FormatUtils_1.default.formatMoney(item.amount)
    }), react_1.default.createElement(msb_shared_component_1.MSBTextBase, {
      style: styles.time,
      content: FormatUtils_1.default.formatDateHHMM(item.transDate)
    })));
  };
  /* istanbul ignore next */
  cov_1m4fsnvrkv().s[29]++;
  if (error) {
    /* istanbul ignore next */
    cov_1m4fsnvrkv().b[6][0]++;
    cov_1m4fsnvrkv().s[30]++;
    return react_1.default.createElement(EmptyBill_1.EmptyTransactionHistoryScreen, {
      type: error.code === MSBErrorCode_1.MSBErrorCode.EMPTY_DATA ?
      /* istanbul ignore next */
      (cov_1m4fsnvrkv().b[7][0]++, msb_shared_component_1.EmptyType.Editable) :
      /* istanbul ignore next */
      (cov_1m4fsnvrkv().b[7][1]++, msb_shared_component_1.EmptyType.Connection),
      title: (0, i18n_1.translate)(error.title),
      content: (0, i18n_1.translate)(error.message)
    });
  } else
  /* istanbul ignore next */
  {
    cov_1m4fsnvrkv().b[6][1]++;
  }
  cov_1m4fsnvrkv().s[31]++;
  return react_1.default.createElement(react_native_1.SectionList, {
    sections: data,
    keyExtractor: function keyExtractor(item) {
      /* istanbul ignore next */
      cov_1m4fsnvrkv().f[4]++;
      cov_1m4fsnvrkv().s[32]++;
      return item.id;
    },
    style: containerStyle,
    renderItem: renderItem,
    renderSectionHeader: renderSectionHeader,
    ItemSeparatorComponent: function ItemSeparatorComponent() {
      /* istanbul ignore next */
      cov_1m4fsnvrkv().f[5]++;
      cov_1m4fsnvrkv().s[33]++;
      return react_1.default.createElement(react_native_1.View, {
        style: styles.separator
      });
    },
    stickySectionHeadersEnabled: false
  });
};
var makeStyle =
/* istanbul ignore next */
(cov_1m4fsnvrkv().s[34]++, (0, msb_shared_component_1.createMSBStyleSheet)(function (_ref5) {
  /* istanbul ignore next */
  cov_1m4fsnvrkv().f[6]++;
  var ColorGlobal =
    /* istanbul ignore next */
    (cov_1m4fsnvrkv().s[35]++, _ref5.ColorGlobal),
    SizeGlobal =
    /* istanbul ignore next */
    (cov_1m4fsnvrkv().s[36]++, _ref5.SizeGlobal),
    Typography =
    /* istanbul ignore next */
    (cov_1m4fsnvrkv().s[37]++, _ref5.Typography);
  /* istanbul ignore next */
  cov_1m4fsnvrkv().s[38]++;
  return {
    sectionHeader: {
      backgroundColor: ColorGlobal.Neutral50,
      paddingVertical: SizeGlobal.Size300,
      paddingHorizontal: SizeGlobal.Size400
    },
    sectionHeaderText: Object.assign({}, Typography == null ?
    /* istanbul ignore next */
    (cov_1m4fsnvrkv().b[8][0]++, void 0) :
    /* istanbul ignore next */
    (cov_1m4fsnvrkv().b[8][1]++, Typography.caption_medium), {
      color: ColorGlobal.Neutral600
    }),
    itemContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'flex-start',
      paddingVertical: SizeGlobal.Size400,
      borderBottomWidth: 1,
      borderBottomColor: ColorGlobal.Neutral100,
      marginHorizontal: 0
    },
    itemLeft: {
      flex: 0.7,
      marginRight: SizeGlobal.Size200
    },
    transName: Object.assign({}, Typography == null ?
    /* istanbul ignore next */
    (cov_1m4fsnvrkv().b[9][0]++, void 0) :
    /* istanbul ignore next */
    (cov_1m4fsnvrkv().b[9][1]++, Typography.small_semiBold), {
      color: ColorGlobal.Neutral800,
      marginBottom: SizeGlobal.Size100
    }),
    content: Object.assign({}, Typography == null ?
    /* istanbul ignore next */
    (cov_1m4fsnvrkv().b[10][0]++, void 0) :
    /* istanbul ignore next */
    (cov_1m4fsnvrkv().b[10][1]++, Typography.small_regular), {
      color: ColorGlobal.Neutral600
    }),
    itemRight: {
      alignItems: 'flex-end',
      flex: 0.3
    },
    amount: Object.assign({}, Typography == null ?
    /* istanbul ignore next */
    (cov_1m4fsnvrkv().b[11][0]++, void 0) :
    /* istanbul ignore next */
    (cov_1m4fsnvrkv().b[11][1]++, Typography.small_semiBold), {
      color: ColorGlobal.Green500,
      marginBottom: SizeGlobal.Size100
    }),
    time: Object.assign({}, Typography == null ?
    /* istanbul ignore next */
    (cov_1m4fsnvrkv().b[12][0]++, void 0) :
    /* istanbul ignore next */
    (cov_1m4fsnvrkv().b[12][1]++, Typography.small_medium), {
      color: ColorGlobal.Neutral600
    }),
    separator: {
      height: 1,
      backgroundColor: ColorGlobal.Neutral100
    },
    loadingContainer: {
      justifyContent: 'center',
      alignItems: 'center',
      height: (0, msb_shared_component_1.getSize)(120)
    },
    emptyContainer: {
      justifyContent: 'center',
      alignItems: 'center',
      height: (0, msb_shared_component_1.getSize)(120)
    },
    emptyText: Object.assign({}, Typography == null ?
    /* istanbul ignore next */
    (cov_1m4fsnvrkv().b[13][0]++, void 0) :
    /* istanbul ignore next */
    (cov_1m4fsnvrkv().b[13][1]++, Typography.small_regular), {
      color: ColorGlobal.Neutral400
    })
  };
}));
/* istanbul ignore next */
cov_1m4fsnvrkv().s[39]++;
exports.default = HistoryTransaction;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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