5df512309205d1f7c537420ae6e4d14e
"use strict";

/* istanbul ignore next */
function cov_s77168lb6() {
  var path = "/Users/<USER>/projects/react-native/payment-module/src/presentation/payment-info/components/biometric-authentication/types.ts";
  var hash = "37530ea22d65250690bc7f870a4d9c10ece6e879";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/projects/react-native/payment-module/src/presentation/payment-info/components/biometric-authentication/types.ts",
    statementMap: {
      "0": {
        start: {
          line: 3,
          column: 0
        },
        end: {
          line: 5,
          column: 3
        }
      }
    },
    fnMap: {},
    branchMap: {},
    s: {
      "0": 0
    },
    f: {},
    b: {},
    inputSourceMap: {
      version: 3,
      file: undefined,
      names: [],
      sourceRoot: undefined,
      sources: ["/Users/<USER>/projects/react-native/payment-module/src/presentation/payment-info/components/biometric-authentication/types.ts"],
      sourcesContent: ["import {ViewStyle} from 'react-native';\n\nexport type BiometricAuthenticationProps = {\n  style?: ViewStyle;\n  splitTransfer?: () => void;\n  normalTransfer?: () => void;\n  amount?: number;\n  content?: string;\n};\n"],
      mappings: "",
      ignoreList: []
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "37530ea22d65250690bc7f870a4d9c10ece6e879"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_s77168lb6 = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_s77168lb6();
cov_s77168lb6().s[0]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6W10sInNvdXJjZXMiOlsiL1VzZXJzL3NvbnNhbXNldC9wcm9qZWN0cy9yZWFjdC1uYXRpdmUvcGF5bWVudC1tb2R1bGUvc3JjL3ByZXNlbnRhdGlvbi9wYXltZW50LWluZm8vY29tcG9uZW50cy9iaW9tZXRyaWMtYXV0aGVudGljYXRpb24vdHlwZXMudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHtWaWV3U3R5bGV9IGZyb20gJ3JlYWN0LW5hdGl2ZSc7XG5cbmV4cG9ydCB0eXBlIEJpb21ldHJpY0F1dGhlbnRpY2F0aW9uUHJvcHMgPSB7XG4gIHN0eWxlPzogVmlld1N0eWxlO1xuICBzcGxpdFRyYW5zZmVyPzogKCkgPT4gdm9pZDtcbiAgbm9ybWFsVHJhbnNmZXI/OiAoKSA9PiB2b2lkO1xuICBhbW91bnQ/OiBudW1iZXI7XG4gIGNvbnRlbnQ/OiBzdHJpbmc7XG59O1xuIl0sIm1hcHBpbmdzIjoiIiwiaWdub3JlTGlzdCI6W119