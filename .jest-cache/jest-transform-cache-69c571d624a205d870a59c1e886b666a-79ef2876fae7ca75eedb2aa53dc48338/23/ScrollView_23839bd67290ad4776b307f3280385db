34d18bb801551c6c6decdd775524554b
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
var _objectWithoutProperties2 = _interopRequireDefault(require("@babel/runtime/helpers/objectWithoutProperties"));
var _classCallCheck2 = _interopRequireDefault(require("@babel/runtime/helpers/classCallCheck"));
var _createClass2 = _interopRequireDefault(require("@babel/runtime/helpers/createClass"));
var _possibleConstructorReturn2 = _interopRequireDefault(require("@babel/runtime/helpers/possibleConstructorReturn"));
var _getPrototypeOf2 = _interopRequireDefault(require("@babel/runtime/helpers/getPrototypeOf"));
var _inherits2 = _interopRequireDefault(require("@babel/runtime/helpers/inherits"));
var _HScrollViewNativeComponents = require("../../../src/private/components/HScrollViewNativeComponents");
var _VScrollViewNativeComponents = require("../../../src/private/components/VScrollViewNativeComponents");
var _AnimatedImplementation = _interopRequireDefault(require("../../Animated/AnimatedImplementation"));
var _FrameRateLogger = _interopRequireDefault(require("../../Interaction/FrameRateLogger"));
var _RendererProxy = require("../../ReactNative/RendererProxy");
var _UIManager = _interopRequireDefault(require("../../ReactNative/UIManager"));
var _flattenStyle = _interopRequireDefault(require("../../StyleSheet/flattenStyle"));
var _splitLayoutProps2 = _interopRequireDefault(require("../../StyleSheet/splitLayoutProps"));
var _StyleSheet = _interopRequireDefault(require("../../StyleSheet/StyleSheet"));
var _Dimensions = _interopRequireDefault(require("../../Utilities/Dimensions"));
var _dismissKeyboard = _interopRequireDefault(require("../../Utilities/dismissKeyboard"));
var _Platform = _interopRequireDefault(require("../../Utilities/Platform"));
var _Keyboard = _interopRequireDefault(require("../Keyboard/Keyboard"));
var _TextInputState = _interopRequireDefault(require("../TextInput/TextInputState"));
var _processDecelerationRate = _interopRequireDefault(require("./processDecelerationRate"));
var _ScrollViewCommands = _interopRequireDefault(require("./ScrollViewCommands"));
var _ScrollViewContext = _interopRequireWildcard(require("./ScrollViewContext"));
var _ScrollViewStickyHeader = _interopRequireDefault(require("./ScrollViewStickyHeader"));
var _invariant = _interopRequireDefault(require("invariant"));
var _memoizeOne = _interopRequireDefault(require("memoize-one"));
var _nullthrows = _interopRequireDefault(require("nullthrows"));
var React = _interopRequireWildcard(require("react"));
var _jsxRuntime = require("react/jsx-runtime");
var _excluded = ["experimental_endDraggingSensitivityMultiplier"];
function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
function _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }
function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }
var IS_ANIMATING_TOUCH_START_THRESHOLD_MS = 16;
var ScrollView = function (_React$Component) {
  function ScrollView(props) {
    var _this$props$contentOf, _this$props$contentOf2, _this$props$contentIn, _this$props$contentIn2;
    var _this;
    (0, _classCallCheck2.default)(this, ScrollView);
    _this = _callSuper(this, ScrollView, [props]);
    _this._scrollAnimatedValueAttachment = null;
    _this._stickyHeaderRefs = new Map();
    _this._headerLayoutYs = new Map();
    _this._keyboardMetrics = null;
    _this._additionalScrollOffset = 0;
    _this._isTouching = false;
    _this._lastMomentumScrollBeginTime = 0;
    _this._lastMomentumScrollEndTime = 0;
    _this._observedScrollSinceBecomingResponder = false;
    _this._becameResponderWhileAnimating = false;
    _this._preventNegativeScrollOffset = null;
    _this._animated = null;
    _this._subscriptionKeyboardWillShow = null;
    _this._subscriptionKeyboardWillHide = null;
    _this._subscriptionKeyboardDidShow = null;
    _this._subscriptionKeyboardDidHide = null;
    _this.state = {
      layoutHeight: null
    };
    _this.getScrollResponder = function () {
      return _this;
    };
    _this.getScrollableNode = function () {
      return (0, _RendererProxy.findNodeHandle)(_this.getNativeScrollRef());
    };
    _this.getInnerViewNode = function () {
      return (0, _RendererProxy.findNodeHandle)(_this._innerView.nativeInstance);
    };
    _this.getInnerViewRef = function () {
      return _this._innerView.nativeInstance;
    };
    _this.getNativeScrollRef = function () {
      return _this._scrollView.nativeInstance;
    };
    _this.scrollTo = function (options, deprecatedX, deprecatedAnimated) {
      var x, y, animated;
      if (typeof options === 'number') {
        console.warn('`scrollTo(y, x, animated)` is deprecated. Use `scrollTo({x: 5, y: 5, ' + 'animated: true})` instead.');
        y = options;
        x = deprecatedX;
        animated = deprecatedAnimated;
      } else if (options) {
        y = options.y;
        x = options.x;
        animated = options.animated;
      }
      var component = _this.getNativeScrollRef();
      if (component == null) {
        return;
      }
      _ScrollViewCommands.default.scrollTo(component, x || 0, y || 0, animated !== false);
    };
    _this.scrollToEnd = function (options) {
      var animated = (options && options.animated) !== false;
      var component = _this.getNativeScrollRef();
      if (component == null) {
        return;
      }
      _ScrollViewCommands.default.scrollToEnd(component, animated);
    };
    _this.flashScrollIndicators = function () {
      var component = _this.getNativeScrollRef();
      if (component == null) {
        return;
      }
      _ScrollViewCommands.default.flashScrollIndicators(component);
    };
    _this.scrollResponderScrollNativeHandleToKeyboard = function (nodeHandle, additionalOffset, preventNegativeScrollOffset) {
      _this._additionalScrollOffset = additionalOffset || 0;
      _this._preventNegativeScrollOffset = !!preventNegativeScrollOffset;
      if (_this._innerView.nativeInstance == null) {
        return;
      }
      if (typeof nodeHandle === 'number') {
        _UIManager.default.measureLayout(nodeHandle, (0, _nullthrows.default)((0, _RendererProxy.findNodeHandle)(_this)), _this._textInputFocusError, _this._inputMeasureAndScrollToKeyboard);
      } else {
        nodeHandle.measureLayout(_this._innerView.nativeInstance, _this._inputMeasureAndScrollToKeyboard, _this._textInputFocusError);
      }
    };
    _this.scrollResponderZoomTo = function (rect, animated) {
      (0, _invariant.default)(_Platform.default.OS === 'ios', 'zoomToRect is not implemented');
      if ('animated' in rect) {
        _this._animated = rect.animated;
        delete rect.animated;
      } else if (typeof animated !== 'undefined') {
        console.warn('`scrollResponderZoomTo` `animated` argument is deprecated. Use `options.animated` instead');
      }
      var component = _this.getNativeScrollRef();
      if (component == null) {
        return;
      }
      _ScrollViewCommands.default.zoomToRect(component, rect, animated !== false);
    };
    _this._inputMeasureAndScrollToKeyboard = function (left, top, width, height) {
      var keyboardScreenY = _Dimensions.default.get('window').height;
      var scrollTextInputIntoVisibleRect = function scrollTextInputIntoVisibleRect() {
        if (_this._keyboardMetrics != null) {
          keyboardScreenY = _this._keyboardMetrics.screenY;
        }
        var scrollOffsetY = top - keyboardScreenY + height + _this._additionalScrollOffset;
        if (_this._preventNegativeScrollOffset === true) {
          scrollOffsetY = Math.max(0, scrollOffsetY);
        }
        _this.scrollTo({
          x: 0,
          y: scrollOffsetY,
          animated: true
        });
        _this._additionalScrollOffset = 0;
        _this._preventNegativeScrollOffset = false;
      };
      if (_this._keyboardMetrics == null) {
        setTimeout(function () {
          scrollTextInputIntoVisibleRect();
        }, 0);
      } else {
        scrollTextInputIntoVisibleRect();
      }
    };
    _this._handleScroll = function (e) {
      _this._observedScrollSinceBecomingResponder = true;
      _this.props.onScroll && _this.props.onScroll(e);
    };
    _this._handleLayout = function (e) {
      if (_this.props.invertStickyHeaders === true) {
        _this.setState({
          layoutHeight: e.nativeEvent.layout.height
        });
      }
      if (_this.props.onLayout) {
        _this.props.onLayout(e);
      }
    };
    _this._handleContentOnLayout = function (e) {
      var _e$nativeEvent$layout = e.nativeEvent.layout,
        width = _e$nativeEvent$layout.width,
        height = _e$nativeEvent$layout.height;
      _this.props.onContentSizeChange && _this.props.onContentSizeChange(width, height);
    };
    _this._innerView = createRefForwarder(function (instance) {
      return instance;
    });
    _this._scrollView = createRefForwarder(function (nativeInstance) {
      var publicInstance = Object.assign(nativeInstance, {
        getScrollResponder: _this.getScrollResponder,
        getScrollableNode: _this.getScrollableNode,
        getInnerViewNode: _this.getInnerViewNode,
        getInnerViewRef: _this.getInnerViewRef,
        getNativeScrollRef: _this.getNativeScrollRef,
        scrollTo: _this.scrollTo,
        scrollToEnd: _this.scrollToEnd,
        flashScrollIndicators: _this.flashScrollIndicators,
        scrollResponderZoomTo: _this.scrollResponderZoomTo,
        scrollResponderScrollNativeHandleToKeyboard: _this.scrollResponderScrollNativeHandleToKeyboard
      });
      return publicInstance;
    });
    _this.scrollResponderKeyboardWillShow = function (e) {
      _this._keyboardMetrics = e.endCoordinates;
      _this.props.onKeyboardWillShow && _this.props.onKeyboardWillShow(e);
    };
    _this.scrollResponderKeyboardWillHide = function (e) {
      _this._keyboardMetrics = null;
      _this.props.onKeyboardWillHide && _this.props.onKeyboardWillHide(e);
    };
    _this.scrollResponderKeyboardDidShow = function (e) {
      _this._keyboardMetrics = e.endCoordinates;
      _this.props.onKeyboardDidShow && _this.props.onKeyboardDidShow(e);
    };
    _this.scrollResponderKeyboardDidHide = function (e) {
      _this._keyboardMetrics = null;
      _this.props.onKeyboardDidHide && _this.props.onKeyboardDidHide(e);
    };
    _this._handleMomentumScrollBegin = function (e) {
      _this._lastMomentumScrollBeginTime = global.performance.now();
      _this.props.onMomentumScrollBegin && _this.props.onMomentumScrollBegin(e);
    };
    _this._handleMomentumScrollEnd = function (e) {
      _FrameRateLogger.default.endScroll();
      _this._lastMomentumScrollEndTime = global.performance.now();
      _this.props.onMomentumScrollEnd && _this.props.onMomentumScrollEnd(e);
    };
    _this._handleScrollBeginDrag = function (e) {
      _FrameRateLogger.default.beginScroll();
      if (_Platform.default.OS === 'android' && _this.props.keyboardDismissMode === 'on-drag') {
        (0, _dismissKeyboard.default)();
      }
      _this.props.onScrollBeginDrag && _this.props.onScrollBeginDrag(e);
    };
    _this._handleScrollEndDrag = function (e) {
      var velocity = e.nativeEvent.velocity;
      if (!_this._isAnimating() && (!velocity || velocity.x === 0 && velocity.y === 0)) {
        _FrameRateLogger.default.endScroll();
      }
      _this.props.onScrollEndDrag && _this.props.onScrollEndDrag(e);
    };
    _this._isAnimating = function () {
      var now = global.performance.now();
      var timeSinceLastMomentumScrollEnd = now - _this._lastMomentumScrollEndTime;
      var isAnimating = timeSinceLastMomentumScrollEnd < IS_ANIMATING_TOUCH_START_THRESHOLD_MS || _this._lastMomentumScrollEndTime < _this._lastMomentumScrollBeginTime;
      return isAnimating;
    };
    _this._handleResponderGrant = function (e) {
      _this._observedScrollSinceBecomingResponder = false;
      _this.props.onResponderGrant && _this.props.onResponderGrant(e);
      _this._becameResponderWhileAnimating = _this._isAnimating();
    };
    _this._handleResponderReject = function () {};
    _this._handleResponderRelease = function (e) {
      _this._isTouching = e.nativeEvent.touches.length !== 0;
      _this.props.onResponderRelease && _this.props.onResponderRelease(e);
      if (typeof e.target === 'number') {
        if (__DEV__) {
          console.error('Did not expect event target to be a number. Should have been a native component');
        }
        return;
      }
      var currentlyFocusedTextInput = _TextInputState.default.currentlyFocusedInput();
      if (currentlyFocusedTextInput != null && _this.props.keyboardShouldPersistTaps !== true && _this.props.keyboardShouldPersistTaps !== 'always' && _this._keyboardIsDismissible() && e.target !== currentlyFocusedTextInput && !_this._observedScrollSinceBecomingResponder && !_this._becameResponderWhileAnimating) {
        _TextInputState.default.blurTextInput(currentlyFocusedTextInput);
      }
    };
    _this._handleResponderTerminationRequest = function () {
      return !_this._observedScrollSinceBecomingResponder;
    };
    _this._handleScrollShouldSetResponder = function () {
      if (_this.props.disableScrollViewPanResponder === true) {
        return false;
      }
      return _this._isTouching;
    };
    _this._handleStartShouldSetResponder = function (e) {
      if (_this.props.disableScrollViewPanResponder === true) {
        return false;
      }
      var currentlyFocusedInput = _TextInputState.default.currentlyFocusedInput();
      if (_this.props.keyboardShouldPersistTaps === 'handled' && _this._keyboardIsDismissible() && e.target !== currentlyFocusedInput) {
        return true;
      }
      return false;
    };
    _this._handleStartShouldSetResponderCapture = function (e) {
      if (_this._isAnimating()) {
        return true;
      }
      if (_this.props.disableScrollViewPanResponder === true) {
        return false;
      }
      var keyboardShouldPersistTaps = _this.props.keyboardShouldPersistTaps;
      var keyboardNeverPersistTaps = !keyboardShouldPersistTaps || keyboardShouldPersistTaps === 'never';
      if (typeof e.target === 'number') {
        if (__DEV__) {
          console.error('Did not expect event target to be a number. Should have been a native component');
        }
        return false;
      }
      if (_this._softKeyboardIsDetached()) {
        return false;
      }
      if (keyboardNeverPersistTaps && _this._keyboardIsDismissible() && e.target != null && !_TextInputState.default.isTextInput(e.target)) {
        return true;
      }
      return false;
    };
    _this._keyboardIsDismissible = function () {
      var currentlyFocusedInput = _TextInputState.default.currentlyFocusedInput();
      var hasFocusedTextInput = currentlyFocusedInput != null && _TextInputState.default.isTextInput(currentlyFocusedInput);
      var softKeyboardMayBeOpen = _this._keyboardMetrics != null || _this._keyboardEventsAreUnreliable();
      return hasFocusedTextInput && softKeyboardMayBeOpen;
    };
    _this._softKeyboardIsDetached = function () {
      return _this._keyboardMetrics != null && _this._keyboardMetrics.height === 0;
    };
    _this._keyboardEventsAreUnreliable = function () {
      return _Platform.default.OS === 'android' && _Platform.default.Version < 30;
    };
    _this._handleTouchEnd = function (e) {
      var nativeEvent = e.nativeEvent;
      _this._isTouching = nativeEvent.touches.length !== 0;
      var keyboardShouldPersistTaps = _this.props.keyboardShouldPersistTaps;
      var keyboardNeverPersistsTaps = !keyboardShouldPersistTaps || keyboardShouldPersistTaps === 'never';
      var currentlyFocusedTextInput = _TextInputState.default.currentlyFocusedInput();
      if (currentlyFocusedTextInput != null && e.target !== currentlyFocusedTextInput && _this._softKeyboardIsDetached() && _this._keyboardIsDismissible() && keyboardNeverPersistsTaps) {
        _TextInputState.default.blurTextInput(currentlyFocusedTextInput);
      }
      _this.props.onTouchEnd && _this.props.onTouchEnd(e);
    };
    _this._handleTouchCancel = function (e) {
      _this._isTouching = false;
      _this.props.onTouchCancel && _this.props.onTouchCancel(e);
    };
    _this._handleTouchStart = function (e) {
      _this._isTouching = true;
      _this.props.onTouchStart && _this.props.onTouchStart(e);
    };
    _this._handleTouchMove = function (e) {
      _this.props.onTouchMove && _this.props.onTouchMove(e);
    };
    _this._scrollAnimatedValue = new _AnimatedImplementation.default.Value((_this$props$contentOf = (_this$props$contentOf2 = _this.props.contentOffset) == null ? void 0 : _this$props$contentOf2.y) != null ? _this$props$contentOf : 0);
    _this._scrollAnimatedValue.setOffset((_this$props$contentIn = (_this$props$contentIn2 = _this.props.contentInset) == null ? void 0 : _this$props$contentIn2.top) != null ? _this$props$contentIn : 0);
    return _this;
  }
  (0, _inherits2.default)(ScrollView, _React$Component);
  return (0, _createClass2.default)(ScrollView, [{
    key: "componentDidMount",
    value: function componentDidMount() {
      if (typeof this.props.keyboardShouldPersistTaps === 'boolean') {
        console.warn(`'keyboardShouldPersistTaps={${this.props.keyboardShouldPersistTaps === true ? 'true' : 'false'}}' is deprecated. ` + `Use 'keyboardShouldPersistTaps="${this.props.keyboardShouldPersistTaps ? 'always' : 'never'}"' instead`);
      }
      this._keyboardMetrics = _Keyboard.default.metrics();
      this._additionalScrollOffset = 0;
      this._subscriptionKeyboardWillShow = _Keyboard.default.addListener('keyboardWillShow', this.scrollResponderKeyboardWillShow);
      this._subscriptionKeyboardWillHide = _Keyboard.default.addListener('keyboardWillHide', this.scrollResponderKeyboardWillHide);
      this._subscriptionKeyboardDidShow = _Keyboard.default.addListener('keyboardDidShow', this.scrollResponderKeyboardDidShow);
      this._subscriptionKeyboardDidHide = _Keyboard.default.addListener('keyboardDidHide', this.scrollResponderKeyboardDidHide);
      this._updateAnimatedNodeAttachment();
    }
  }, {
    key: "componentDidUpdate",
    value: function componentDidUpdate(prevProps) {
      var prevContentInsetTop = prevProps.contentInset ? prevProps.contentInset.top : 0;
      var newContentInsetTop = this.props.contentInset ? this.props.contentInset.top : 0;
      if (prevContentInsetTop !== newContentInsetTop) {
        this._scrollAnimatedValue.setOffset(newContentInsetTop || 0);
      }
      this._updateAnimatedNodeAttachment();
    }
  }, {
    key: "componentWillUnmount",
    value: function componentWillUnmount() {
      if (this._subscriptionKeyboardWillShow != null) {
        this._subscriptionKeyboardWillShow.remove();
      }
      if (this._subscriptionKeyboardWillHide != null) {
        this._subscriptionKeyboardWillHide.remove();
      }
      if (this._subscriptionKeyboardDidShow != null) {
        this._subscriptionKeyboardDidShow.remove();
      }
      if (this._subscriptionKeyboardDidHide != null) {
        this._subscriptionKeyboardDidHide.remove();
      }
      if (this._scrollAnimatedValueAttachment) {
        this._scrollAnimatedValueAttachment.detach();
      }
    }
  }, {
    key: "_textInputFocusError",
    value: function _textInputFocusError() {
      console.warn('Error measuring text field.');
    }
  }, {
    key: "_getKeyForIndex",
    value: function _getKeyForIndex(index, childArray) {
      var child = childArray[index];
      return child && child.key;
    }
  }, {
    key: "_updateAnimatedNodeAttachment",
    value: function _updateAnimatedNodeAttachment() {
      if (this._scrollAnimatedValueAttachment) {
        this._scrollAnimatedValueAttachment.detach();
      }
      if (this.props.stickyHeaderIndices && this.props.stickyHeaderIndices.length > 0) {
        this._scrollAnimatedValueAttachment = _AnimatedImplementation.default.attachNativeEvent(this.getNativeScrollRef(), 'onScroll', [{
          nativeEvent: {
            contentOffset: {
              y: this._scrollAnimatedValue
            }
          }
        }]);
      }
    }
  }, {
    key: "_setStickyHeaderRef",
    value: function _setStickyHeaderRef(key, ref) {
      if (ref) {
        this._stickyHeaderRefs.set(key, ref);
      } else {
        this._stickyHeaderRefs.delete(key);
      }
    }
  }, {
    key: "_onStickyHeaderLayout",
    value: function _onStickyHeaderLayout(index, event, key) {
      var stickyHeaderIndices = this.props.stickyHeaderIndices;
      if (!stickyHeaderIndices) {
        return;
      }
      var childArray = React.Children.toArray(this.props.children);
      if (key !== this._getKeyForIndex(index, childArray)) {
        return;
      }
      var layoutY = event.nativeEvent.layout.y;
      this._headerLayoutYs.set(key, layoutY);
      var indexOfIndex = stickyHeaderIndices.indexOf(index);
      var previousHeaderIndex = stickyHeaderIndices[indexOfIndex - 1];
      if (previousHeaderIndex != null) {
        var previousHeader = this._stickyHeaderRefs.get(this._getKeyForIndex(previousHeaderIndex, childArray));
        previousHeader && previousHeader.setNextHeaderY && previousHeader.setNextHeaderY(layoutY);
      }
    }
  }, {
    key: "render",
    value: function render() {
      var _this2 = this;
      var horizontal = this.props.horizontal === true;
      var NativeScrollView = horizontal ? _HScrollViewNativeComponents.HScrollViewNativeComponent : _VScrollViewNativeComponents.VScrollViewNativeComponent;
      var NativeScrollContentView = horizontal ? _HScrollViewNativeComponents.HScrollContentViewNativeComponent : _VScrollViewNativeComponents.VScrollContentViewNativeComponent;
      var contentContainerStyle = [horizontal && styles.contentContainerHorizontal, this.props.contentContainerStyle];
      if (__DEV__ && this.props.style !== undefined) {
        var style = (0, _flattenStyle.default)(this.props.style);
        var childLayoutProps = ['alignItems', 'justifyContent'].filter(function (prop) {
          return style && style[prop] !== undefined;
        });
        (0, _invariant.default)(childLayoutProps.length === 0, 'ScrollView child layout (' + JSON.stringify(childLayoutProps) + ') must be applied through the contentContainerStyle prop.');
      }
      var contentSizeChangeProps = this.props.onContentSizeChange == null ? null : {
        onLayout: this._handleContentOnLayout
      };
      var stickyHeaderIndices = this.props.stickyHeaderIndices;
      var children = this.props.children;
      children = React.Children.toArray(children);
      if (stickyHeaderIndices != null && stickyHeaderIndices.length > 0) {
        children = children.map(function (child, index) {
          var indexOfIndex = child ? stickyHeaderIndices.indexOf(index) : -1;
          if (indexOfIndex > -1) {
            var key = child.key;
            var nextIndex = stickyHeaderIndices[indexOfIndex + 1];
            var StickyHeaderComponent = _this2.props.StickyHeaderComponent || _ScrollViewStickyHeader.default;
            return (0, _jsxRuntime.jsx)(StickyHeaderComponent, {
              ref: function ref(_ref) {
                return _this2._setStickyHeaderRef(key, _ref);
              },
              nextHeaderLayoutY: _this2._headerLayoutYs.get(_this2._getKeyForIndex(nextIndex, children)),
              onLayout: function onLayout(event) {
                return _this2._onStickyHeaderLayout(index, event, key);
              },
              scrollAnimatedValue: _this2._scrollAnimatedValue,
              inverted: _this2.props.invertStickyHeaders,
              hiddenOnScroll: _this2.props.stickyHeaderHiddenOnScroll,
              scrollViewHeight: _this2.state.layoutHeight,
              children: child
            }, key);
          } else {
            return child;
          }
        });
      }
      children = (0, _jsxRuntime.jsx)(_ScrollViewContext.default.Provider, {
        value: horizontal ? _ScrollViewContext.HORIZONTAL : _ScrollViewContext.VERTICAL,
        children: children
      });
      var hasStickyHeaders = Array.isArray(stickyHeaderIndices) && stickyHeaderIndices.length > 0;
      var preserveChildren = this.props.maintainVisibleContentPosition != null || _Platform.default.OS === 'android' && this.props.snapToAlignment != null;
      var contentContainer = (0, _jsxRuntime.jsx)(NativeScrollContentView, Object.assign({}, contentSizeChangeProps, {
        ref: this._innerView.getForwardingRef(this.props.innerViewRef),
        style: contentContainerStyle,
        removeClippedSubviews: _Platform.default.OS === 'android' && hasStickyHeaders ? false : this.props.removeClippedSubviews,
        collapsable: false,
        collapsableChildren: !preserveChildren,
        children: children
      }));
      var alwaysBounceHorizontal = this.props.alwaysBounceHorizontal !== undefined ? this.props.alwaysBounceHorizontal : this.props.horizontal;
      var alwaysBounceVertical = this.props.alwaysBounceVertical !== undefined ? this.props.alwaysBounceVertical : !this.props.horizontal;
      var baseStyle = horizontal ? styles.baseHorizontal : styles.baseVertical;
      var _this$props = this.props,
        experimental_endDraggingSensitivityMultiplier = _this$props.experimental_endDraggingSensitivityMultiplier,
        otherProps = (0, _objectWithoutProperties2.default)(_this$props, _excluded);
      var props = Object.assign({}, otherProps, {
        alwaysBounceHorizontal: alwaysBounceHorizontal,
        alwaysBounceVertical: alwaysBounceVertical,
        style: _StyleSheet.default.compose(baseStyle, this.props.style),
        onContentSizeChange: null,
        onLayout: this._handleLayout,
        onMomentumScrollBegin: this._handleMomentumScrollBegin,
        onMomentumScrollEnd: this._handleMomentumScrollEnd,
        onResponderGrant: this._handleResponderGrant,
        onResponderReject: this._handleResponderReject,
        onResponderRelease: this._handleResponderRelease,
        onResponderTerminationRequest: this._handleResponderTerminationRequest,
        onScrollBeginDrag: this._handleScrollBeginDrag,
        onScrollEndDrag: this._handleScrollEndDrag,
        onScrollShouldSetResponder: this._handleScrollShouldSetResponder,
        onStartShouldSetResponder: this._handleStartShouldSetResponder,
        onStartShouldSetResponderCapture: this._handleStartShouldSetResponderCapture,
        onTouchEnd: this._handleTouchEnd,
        onTouchMove: this._handleTouchMove,
        onTouchStart: this._handleTouchStart,
        onTouchCancel: this._handleTouchCancel,
        onScroll: this._handleScroll,
        endDraggingSensitivityMultiplier: experimental_endDraggingSensitivityMultiplier,
        scrollEventThrottle: hasStickyHeaders ? 1 : this.props.scrollEventThrottle,
        sendMomentumEvents: this.props.onMomentumScrollBegin || this.props.onMomentumScrollEnd ? true : false,
        snapToStart: this.props.snapToStart !== false,
        snapToEnd: this.props.snapToEnd !== false,
        pagingEnabled: _Platform.default.select({
          ios: this.props.pagingEnabled === true && this.props.snapToInterval == null && this.props.snapToOffsets == null,
          android: this.props.pagingEnabled === true || this.props.snapToInterval != null || this.props.snapToOffsets != null
        })
      });
      var decelerationRate = this.props.decelerationRate;
      if (decelerationRate != null) {
        props.decelerationRate = (0, _processDecelerationRate.default)(decelerationRate);
      }
      var refreshControl = this.props.refreshControl;
      var scrollViewRef = this._scrollView.getForwardingRef(this.props.scrollViewRef);
      if (refreshControl) {
        if (_Platform.default.OS === 'ios') {
          return (0, _jsxRuntime.jsxs)(NativeScrollView, Object.assign({}, props, {
            ref: scrollViewRef,
            children: [refreshControl, contentContainer]
          }));
        } else if (_Platform.default.OS === 'android') {
          var _splitLayoutProps = (0, _splitLayoutProps2.default)((0, _flattenStyle.default)(props.style)),
            outer = _splitLayoutProps.outer,
            inner = _splitLayoutProps.inner;
          return React.cloneElement(refreshControl, {
            style: _StyleSheet.default.compose(baseStyle, outer)
          }, (0, _jsxRuntime.jsx)(NativeScrollView, Object.assign({}, props, {
            style: _StyleSheet.default.compose(baseStyle, inner),
            ref: scrollViewRef,
            children: contentContainer
          })));
        }
      }
      return (0, _jsxRuntime.jsx)(NativeScrollView, Object.assign({}, props, {
        ref: scrollViewRef,
        children: contentContainer
      }));
    }
  }]);
}(React.Component);
ScrollView.Context = _ScrollViewContext.default;
var styles = _StyleSheet.default.create({
  baseVertical: {
    flexGrow: 1,
    flexShrink: 1,
    flexDirection: 'column',
    overflow: 'scroll'
  },
  baseHorizontal: {
    flexGrow: 1,
    flexShrink: 1,
    flexDirection: 'row',
    overflow: 'scroll'
  },
  contentContainerHorizontal: {
    flexDirection: 'row'
  }
});
function createRefForwarder(mutator) {
  var state = {
    getForwardingRef: (0, _memoizeOne.default)(function (forwardedRef) {
      return function (nativeInstance) {
        var publicInstance = nativeInstance == null ? null : mutator(nativeInstance);
        state.nativeInstance = nativeInstance;
        state.publicInstance = publicInstance;
        if (forwardedRef != null) {
          if (typeof forwardedRef === 'function') {
            forwardedRef(publicInstance);
          } else {
            forwardedRef.current = publicInstance;
          }
        }
      };
    }),
    nativeInstance: null,
    publicInstance: null
  };
  return state;
}
var Wrapper = React.forwardRef(function Wrapper(props, ref) {
  return ref == null ? (0, _jsxRuntime.jsx)(ScrollView, Object.assign({}, props)) : (0, _jsxRuntime.jsx)(ScrollView, Object.assign({}, props, {
    scrollViewRef: ref
  }));
});
Wrapper.displayName = 'ScrollView';
Wrapper.Context = _ScrollViewContext.default;
module.exports = Wrapper;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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