{"version": 3, "names": [], "sources": ["/Users/<USER>/projects/react-native/payment-module/src/data/datasources/IBillPayDataSource.ts"], "sourcesContent": ["import {GetBillDetailResponse} from '../models/get-bill-detail/GetBillDetailResponse';\nimport {GetBillDetailRequest} from '../models/get-bill-detail/GetBillDetailRequest';\nimport {BillValidateResponse} from '../models/bill-validate/BillValidateResponse';\nimport {MyBillListResponse} from '../models/my-bill-list/MyBillListResponse';\nimport {MyBillListRequest} from '../models/my-bill-list/MyBillListRequest';\nimport {ProviderListResponse} from '../models/provider-list/ProviderListResponse';\nimport {ProviderListRequest} from '../models/provider-list/ProviderListRequest';\nimport {CategoryListResponse} from '../models/category-list/CategoryListResponse';\nimport {BaseResponse} from '../../core/BaseResponse';\nimport {BillValidateRequest} from '../models/bill-validate/BillValidateRequest';\n\nexport interface IBillPayDataSource {\n  categoryList(): Promise<BaseResponse<CategoryListResponse>>;\n  providerList(request: ProviderListRequest): Promise<BaseResponse<ProviderListResponse>>;\n  myBillList(request: MyBillListRequest): Promise<BaseResponse<MyBillListResponse>>;\n  getBillDetail(request: GetBillDetailRequest): Promise<BaseResponse<GetBillDetailResponse>>;\n  billValidate(request: BillValidateRequest): Promise<BaseResponse<BillValidateResponse>>;\n}\n"], "mappings": "", "ignoreList": []}