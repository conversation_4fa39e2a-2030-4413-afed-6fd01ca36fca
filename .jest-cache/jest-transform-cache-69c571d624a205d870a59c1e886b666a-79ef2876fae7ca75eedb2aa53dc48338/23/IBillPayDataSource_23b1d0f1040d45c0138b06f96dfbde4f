80d34924974e32ae2f7d62ecff75730f
"use strict";

/* istanbul ignore next */
function cov_nfua4oq0z() {
  var path = "/Users/<USER>/projects/react-native/payment-module/src/data/datasources/IBillPayDataSource.ts";
  var hash = "e303868e5d1f2d174b0be88480c2548ddece870e";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/projects/react-native/payment-module/src/data/datasources/IBillPayDataSource.ts",
    statementMap: {
      "0": {
        start: {
          line: 3,
          column: 0
        },
        end: {
          line: 5,
          column: 3
        }
      }
    },
    fnMap: {},
    branchMap: {},
    s: {
      "0": 0
    },
    f: {},
    b: {},
    inputSourceMap: {
      version: 3,
      file: undefined,
      names: [],
      sourceRoot: undefined,
      sources: ["/Users/<USER>/projects/react-native/payment-module/src/data/datasources/IBillPayDataSource.ts"],
      sourcesContent: ["import {GetBillDetailResponse} from '../models/get-bill-detail/GetBillDetailResponse';\nimport {GetBillDetailRequest} from '../models/get-bill-detail/GetBillDetailRequest';\nimport {BillValidateResponse} from '../models/bill-validate/BillValidateResponse';\nimport {MyBillListResponse} from '../models/my-bill-list/MyBillListResponse';\nimport {MyBillListRequest} from '../models/my-bill-list/MyBillListRequest';\nimport {ProviderListResponse} from '../models/provider-list/ProviderListResponse';\nimport {ProviderListRequest} from '../models/provider-list/ProviderListRequest';\nimport {CategoryListResponse} from '../models/category-list/CategoryListResponse';\nimport {BaseResponse} from '../../core/BaseResponse';\nimport {BillValidateRequest} from '../models/bill-validate/BillValidateRequest';\n\nexport interface IBillPayDataSource {\n  categoryList(): Promise<BaseResponse<CategoryListResponse>>;\n  providerList(request: ProviderListRequest): Promise<BaseResponse<ProviderListResponse>>;\n  myBillList(request: MyBillListRequest): Promise<BaseResponse<MyBillListResponse>>;\n  getBillDetail(request: GetBillDetailRequest): Promise<BaseResponse<GetBillDetailResponse>>;\n  billValidate(request: BillValidateRequest): Promise<BaseResponse<BillValidateResponse>>;\n}\n"],
      mappings: "",
      ignoreList: []
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "e303868e5d1f2d174b0be88480c2548ddece870e"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_nfua4oq0z = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_nfua4oq0z();
cov_nfua4oq0z().s[0]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6W10sInNvdXJjZXMiOlsiL1VzZXJzL3NvbnNhbXNldC9wcm9qZWN0cy9yZWFjdC1uYXRpdmUvcGF5bWVudC1tb2R1bGUvc3JjL2RhdGEvZGF0YXNvdXJjZXMvSUJpbGxQYXlEYXRhU291cmNlLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7R2V0QmlsbERldGFpbFJlc3BvbnNlfSBmcm9tICcuLi9tb2RlbHMvZ2V0LWJpbGwtZGV0YWlsL0dldEJpbGxEZXRhaWxSZXNwb25zZSc7XG5pbXBvcnQge0dldEJpbGxEZXRhaWxSZXF1ZXN0fSBmcm9tICcuLi9tb2RlbHMvZ2V0LWJpbGwtZGV0YWlsL0dldEJpbGxEZXRhaWxSZXF1ZXN0JztcbmltcG9ydCB7QmlsbFZhbGlkYXRlUmVzcG9uc2V9IGZyb20gJy4uL21vZGVscy9iaWxsLXZhbGlkYXRlL0JpbGxWYWxpZGF0ZVJlc3BvbnNlJztcbmltcG9ydCB7TXlCaWxsTGlzdFJlc3BvbnNlfSBmcm9tICcuLi9tb2RlbHMvbXktYmlsbC1saXN0L015QmlsbExpc3RSZXNwb25zZSc7XG5pbXBvcnQge015QmlsbExpc3RSZXF1ZXN0fSBmcm9tICcuLi9tb2RlbHMvbXktYmlsbC1saXN0L015QmlsbExpc3RSZXF1ZXN0JztcbmltcG9ydCB7UHJvdmlkZXJMaXN0UmVzcG9uc2V9IGZyb20gJy4uL21vZGVscy9wcm92aWRlci1saXN0L1Byb3ZpZGVyTGlzdFJlc3BvbnNlJztcbmltcG9ydCB7UHJvdmlkZXJMaXN0UmVxdWVzdH0gZnJvbSAnLi4vbW9kZWxzL3Byb3ZpZGVyLWxpc3QvUHJvdmlkZXJMaXN0UmVxdWVzdCc7XG5pbXBvcnQge0NhdGVnb3J5TGlzdFJlc3BvbnNlfSBmcm9tICcuLi9tb2RlbHMvY2F0ZWdvcnktbGlzdC9DYXRlZ29yeUxpc3RSZXNwb25zZSc7XG5pbXBvcnQge0Jhc2VSZXNwb25zZX0gZnJvbSAnLi4vLi4vY29yZS9CYXNlUmVzcG9uc2UnO1xuaW1wb3J0IHtCaWxsVmFsaWRhdGVSZXF1ZXN0fSBmcm9tICcuLi9tb2RlbHMvYmlsbC12YWxpZGF0ZS9CaWxsVmFsaWRhdGVSZXF1ZXN0JztcblxuZXhwb3J0IGludGVyZmFjZSBJQmlsbFBheURhdGFTb3VyY2Uge1xuICBjYXRlZ29yeUxpc3QoKTogUHJvbWlzZTxCYXNlUmVzcG9uc2U8Q2F0ZWdvcnlMaXN0UmVzcG9uc2U+PjtcbiAgcHJvdmlkZXJMaXN0KHJlcXVlc3Q6IFByb3ZpZGVyTGlzdFJlcXVlc3QpOiBQcm9taXNlPEJhc2VSZXNwb25zZTxQcm92aWRlckxpc3RSZXNwb25zZT4+O1xuICBteUJpbGxMaXN0KHJlcXVlc3Q6IE15QmlsbExpc3RSZXF1ZXN0KTogUHJvbWlzZTxCYXNlUmVzcG9uc2U8TXlCaWxsTGlzdFJlc3BvbnNlPj47XG4gIGdldEJpbGxEZXRhaWwocmVxdWVzdDogR2V0QmlsbERldGFpbFJlcXVlc3QpOiBQcm9taXNlPEJhc2VSZXNwb25zZTxHZXRCaWxsRGV0YWlsUmVzcG9uc2U+PjtcbiAgYmlsbFZhbGlkYXRlKHJlcXVlc3Q6IEJpbGxWYWxpZGF0ZVJlcXVlc3QpOiBQcm9taXNlPEJhc2VSZXNwb25zZTxCaWxsVmFsaWRhdGVSZXNwb25zZT4+O1xufVxuIl0sIm1hcHBpbmdzIjoiIiwiaWdub3JlTGlzdCI6W119