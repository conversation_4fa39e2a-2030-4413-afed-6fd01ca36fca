{"version": 3, "names": [], "sources": ["/Users/<USER>/projects/react-native/payment-module/src/presentation/payment-info/components/biometric-authentication/types.ts"], "sourcesContent": ["import {ViewStyle} from 'react-native';\n\nexport type BiometricAuthenticationProps = {\n  style?: ViewStyle;\n  splitTransfer?: () => void;\n  normalTransfer?: () => void;\n  amount?: number;\n  content?: string;\n};\n"], "mappings": "", "ignoreList": []}