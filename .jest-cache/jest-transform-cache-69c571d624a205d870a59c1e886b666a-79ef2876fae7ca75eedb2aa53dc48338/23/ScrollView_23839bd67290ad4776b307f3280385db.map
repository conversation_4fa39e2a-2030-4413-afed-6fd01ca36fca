{"version": 3, "names": ["_HScrollViewNativeComponents", "require", "_VScrollViewNativeComponents", "_AnimatedImplementation", "_interopRequireDefault", "_FrameRateLogger", "_RendererProxy", "_UIManager", "_flattenStyle", "_splitLayoutProps2", "_StyleSheet", "_Dimensions", "_dismissKeyboard", "_Platform", "_Keyboard", "_TextInputState", "_processDecelerationRate", "_ScrollViewCommands", "_ScrollViewContext", "_interopRequireWildcard", "_ScrollViewStickyHeader", "_invariant", "_memoizeOne", "_nullthrows", "React", "_jsxRuntime", "_excluded", "_getRequireWildcardCache", "e", "WeakMap", "r", "t", "__esModule", "default", "has", "get", "n", "__proto__", "a", "Object", "defineProperty", "getOwnPropertyDescriptor", "u", "hasOwnProperty", "call", "i", "set", "_callSuper", "o", "_getPrototypeOf2", "_possibleConstructorReturn2", "_isNativeReflectConstruct", "Reflect", "construct", "constructor", "apply", "Boolean", "prototype", "valueOf", "IS_ANIMATING_TOUCH_START_THRESHOLD_MS", "ScrollView", "_React$Component", "props", "_this$props$contentOf", "_this$props$contentOf2", "_this$props$contentIn", "_this$props$contentIn2", "_this", "_classCallCheck2", "_scrollAnimatedValueAttachment", "_stickyHeaderRefs", "Map", "_headerLayoutYs", "_keyboardMetrics", "_additionalScrollOffset", "_isTouching", "_lastMomentumScrollBeginTime", "_lastMomentumScrollEndTime", "_observedScrollSinceBecomingResponder", "_becameResponderWhileAnimating", "_preventNegativeScrollOffset", "_animated", "_subscriptionKeyboardWillShow", "_subscriptionKeyboardWillHide", "_subscriptionKeyboardDidShow", "_subscriptionKeyboardDidHide", "state", "layoutHeight", "getScrollResponder", "getScrollableNode", "findNodeHandle", "getNativeScrollRef", "getInnerViewNode", "_innerView", "nativeInstance", "getInnerViewRef", "_scrollView", "scrollTo", "options", "deprecatedX", "deprecatedAnimated", "x", "y", "animated", "console", "warn", "component", "Commands", "scrollToEnd", "flashScrollIndicators", "scrollResponderScrollNativeHandleToKeyboard", "nodeHandle", "additionalOffset", "preventNegativeScrollOffset", "UIManager", "measureLayout", "nullthrows", "_textInputFocusError", "_inputMeasureAndScrollToKeyboard", "scrollResponderZoomTo", "rect", "invariant", "Platform", "OS", "zoomToRect", "left", "top", "width", "height", "keyboardScreenY", "Dimensions", "scrollTextInputIntoVisibleRect", "screenY", "scrollOffsetY", "Math", "max", "setTimeout", "_handleScroll", "onScroll", "_handleLayout", "invertStickyHeaders", "setState", "nativeEvent", "layout", "onLayout", "_handleContentOnLayout", "_e$nativeEvent$layout", "onContentSizeChange", "createRefForwarder", "instance", "publicInstance", "assign", "scrollResponderKeyboardWillShow", "endCoordinates", "onKeyboardWillShow", "scrollResponderKeyboardWillHide", "onKeyboardWillHide", "scrollResponderKeyboardDidShow", "onKeyboardDidShow", "scrollResponderKeyboardDidHide", "onKeyboardDidHide", "_handleMomentumScrollBegin", "global", "performance", "now", "onMomentumScrollBegin", "_handleMomentumScrollEnd", "FrameRateLogger", "endScroll", "onMomentumScrollEnd", "_handleScrollBeginDrag", "beginScroll", "keyboardDismissMode", "dismissKeyboard", "onScrollBeginDrag", "_handleScrollEndDrag", "velocity", "_isAnimating", "onScrollEndDrag", "timeSinceLastMomentumScrollEnd", "isAnimating", "_handleResponderGrant", "onResponderGrant", "_handleResponderReject", "_handleResponderRelease", "touches", "length", "onResponderRelease", "target", "__DEV__", "error", "currentlyFocusedTextInput", "TextInputState", "currentlyFocusedInput", "keyboardShouldPersistTaps", "_keyboardIsDismissible", "blurTextInput", "_handleResponderTerminationRequest", "_handleScrollShouldSetResponder", "disableScrollViewPanResponder", "_handleStartShouldSetResponder", "_handleStartShouldSetResponderCapture", "keyboardNeverPersistTaps", "_softKeyboardIsDetached", "isTextInput", "hasFocusedTextInput", "softKeyboardMayBeOpen", "_keyboardEventsAreUnreliable", "Version", "_handleTouchEnd", "keyboardNeverPersistsTaps", "onTouchEnd", "_handleTouchCancel", "onTouchCancel", "_handleTouchStart", "onTouchStart", "_handleTouchMove", "onTouchMove", "_scrollAnimatedValue", "AnimatedImplementation", "Value", "contentOffset", "setOffset", "contentInset", "_inherits2", "_createClass2", "key", "value", "componentDidMount", "Keyboard", "metrics", "addListener", "_updateAnimatedNodeAttachment", "componentDidUpdate", "prevProps", "prevContentInsetTop", "newContentInsetTop", "componentWillUnmount", "remove", "detach", "_getKeyForIndex", "index", "<PERSON><PERSON><PERSON><PERSON>", "child", "stickyHeaderIndices", "attachNativeEvent", "_setStickyHeaderRef", "ref", "delete", "_onStickyHeaderLayout", "event", "Children", "toArray", "children", "layoutY", "indexOfIndex", "indexOf", "previousHeaderIndex", "previousHeader", "setNextHeaderY", "render", "_this2", "horizontal", "NativeScrollView", "HScrollViewNativeComponent", "VScrollViewNativeComponent", "NativeScrollContentView", "HScrollContentViewNativeComponent", "VScrollContentViewNativeComponent", "contentContainerStyle", "styles", "contentContainerHorizontal", "style", "undefined", "flattenStyle", "childLayoutProps", "filter", "prop", "JSON", "stringify", "contentSizeChangeProps", "map", "nextIndex", "StickyHeaderComponent", "ScrollViewStickyHeader", "jsx", "nextHeaderLayoutY", "scrollAnimatedValue", "inverted", "hiddenOnScroll", "stickyHeaderHiddenOnScroll", "scrollViewHeight", "Provider", "HORIZONTAL", "VERTICAL", "hasStickyHeaders", "Array", "isArray", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "maintainVisibleContentPosition", "snapToAlignment", "contentContainer", "getForwardingRef", "innerViewRef", "removeClippedSubviews", "collapsable", "collaps<PERSON><PERSON><PERSON><PERSON><PERSON>", "alwaysBounceHorizontal", "alwaysBounceVertical", "baseStyle", "baseHorizontal", "baseVertical", "_this$props", "experimental_endDraggingSensitivityMultiplier", "otherProps", "_objectWithoutProperties2", "StyleSheet", "compose", "onResponderReject", "onResponderTerminationRequest", "onScrollShouldSetResponder", "onStartShouldSetResponder", "onStartShouldSetResponderCapture", "endDraggingSensitivityMultiplier", "scrollEventThrottle", "sendMomentumEvents", "snapToStart", "snapToEnd", "pagingEnabled", "select", "ios", "snapToInterval", "snapToOffsets", "android", "decelerationRate", "processDecelerationRate", "refreshControl", "scrollViewRef", "jsxs", "_splitLayoutProps", "splitLayoutProps", "outer", "inner", "cloneElement", "Component", "Context", "ScrollViewContext", "create", "flexGrow", "flexShrink", "flexDirection", "overflow", "mutator", "memoize", "forwardedRef", "current", "Wrapper", "forwardRef", "displayName", "module", "exports"], "sources": ["ScrollView.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @format\n * @flow strict-local\n */\n\nimport type {HostInstance} from '../../Renderer/shims/ReactNativeTypes';\nimport type {EdgeInsetsProp} from '../../StyleSheet/EdgeInsetsPropType';\nimport type {PointProp} from '../../StyleSheet/PointPropType';\nimport type {ViewStyleProp} from '../../StyleSheet/StyleSheet';\nimport type {ColorValue} from '../../StyleSheet/StyleSheet';\nimport type {\n  LayoutEvent,\n  PressEvent,\n  ScrollEvent,\n} from '../../Types/CoreEventTypes';\nimport type {EventSubscription} from '../../vendor/emitter/EventEmitter';\nimport type {KeyboardEvent, KeyboardMetrics} from '../Keyboard/Keyboard';\nimport typeof View from '../View/View';\nimport type {ViewProps} from '../View/ViewPropTypes';\nimport type {Props as ScrollViewStickyHeaderProps} from './ScrollViewStickyHeader';\n\nimport {\n  HScrollContentViewNativeComponent,\n  HScrollViewNativeComponent,\n} from '../../../src/private/components/HScrollViewNativeComponents';\nimport {\n  VScrollContentViewNativeComponent,\n  VScrollViewNativeComponent,\n} from '../../../src/private/components/VScrollViewNativeComponents';\nimport AnimatedImplementation from '../../Animated/AnimatedImplementation';\nimport FrameRateLogger from '../../Interaction/FrameRateLogger';\nimport {findNodeHandle} from '../../ReactNative/RendererProxy';\nimport UIManager from '../../ReactNative/UIManager';\nimport flattenStyle from '../../StyleSheet/flattenStyle';\nimport splitLayoutProps from '../../StyleSheet/splitLayoutProps';\nimport StyleSheet from '../../StyleSheet/StyleSheet';\nimport Dimensions from '../../Utilities/Dimensions';\nimport dismissKeyboard from '../../Utilities/dismissKeyboard';\nimport Platform from '../../Utilities/Platform';\nimport Keyboard from '../Keyboard/Keyboard';\nimport TextInputState from '../TextInput/TextInputState';\nimport processDecelerationRate from './processDecelerationRate';\nimport Commands from './ScrollViewCommands';\nimport ScrollViewContext, {HORIZONTAL, VERTICAL} from './ScrollViewContext';\nimport ScrollViewStickyHeader from './ScrollViewStickyHeader';\nimport invariant from 'invariant';\nimport memoize from 'memoize-one';\nimport nullthrows from 'nullthrows';\nimport * as React from 'react';\n\n/*\n * iOS scroll event timing nuances:\n * ===============================\n *\n *\n * Scrolling without bouncing, if you touch down:\n * -------------------------------\n *\n * 1. `onMomentumScrollBegin` (when animation begins after letting up)\n *    ... physical touch starts ...\n * 2. `onTouchStartCapture`   (when you press down to stop the scroll)\n * 3. `onTouchStart`          (same, but bubble phase)\n * 4. `onResponderRelease`    (when lifting up - you could pause forever before * lifting)\n * 5. `onMomentumScrollEnd`\n *\n *\n * Scrolling with bouncing, if you touch down:\n * -------------------------------\n *\n * 1. `onMomentumScrollBegin` (when animation begins after letting up)\n *    ... bounce begins ...\n *    ... some time elapses ...\n *    ... physical touch during bounce ...\n * 2. `onMomentumScrollEnd`   (Makes no sense why this occurs first during bounce)\n * 3. `onTouchStartCapture`   (immediately after `onMomentumScrollEnd`)\n * 4. `onTouchStart`          (same, but bubble phase)\n * 5. `onTouchEnd`            (You could hold the touch start for a long time)\n * 6. `onMomentumScrollBegin` (When releasing the view starts bouncing back)\n *\n * So when we receive an `onTouchStart`, how can we tell if we are touching\n * *during* an animation (which then causes the animation to stop)? The only way\n * to tell is if the `touchStart` occurred immediately after the\n * `onMomentumScrollEnd`.\n *\n * This is abstracted out for you, so you can just call this.scrollResponderIsAnimating() if\n * necessary\n *\n * `ScrollView` also includes logic for blurring a currently focused input\n * if one is focused while scrolling. This is a natural place\n * to put this logic since it can support not dismissing the keyboard while\n * scrolling, unless a recognized \"tap\"-like gesture has occurred.\n *\n * The public lifecycle API includes events for keyboard interaction, responder\n * interaction, and scrolling (among others). The keyboard callbacks\n * `onKeyboardWill/Did/*` are *global* events, but are invoked on scroll\n * responder's props so that you can guarantee that the scroll responder's\n * internal state has been updated accordingly (and deterministically) by\n * the time the props callbacks are invoke. Otherwise, you would always wonder\n * if the scroll responder is currently in a state where it recognizes new\n * keyboard positions etc. If coordinating scrolling with keyboard movement,\n * *always* use these hooks instead of listening to your own global keyboard\n * events.\n *\n * Public keyboard lifecycle API: (props callbacks)\n *\n * Standard Keyboard Appearance Sequence:\n *\n *   this.props.onKeyboardWillShow\n *   this.props.onKeyboardDidShow\n *\n * `onScrollResponderKeyboardDismissed` will be invoked if an appropriate\n * tap inside the scroll responder's scrollable region was responsible\n * for the dismissal of the keyboard. There are other reasons why the\n * keyboard could be dismissed.\n *\n *   this.props.onScrollResponderKeyboardDismissed\n *\n * Standard Keyboard Hide Sequence:\n *\n *   this.props.onKeyboardWillHide\n *   this.props.onKeyboardDidHide\n */\n\n// Public methods for ScrollView\nexport type ScrollViewImperativeMethods = $ReadOnly<{|\n  getScrollResponder: $PropertyType<ScrollView, 'getScrollResponder'>,\n  getScrollableNode: $PropertyType<ScrollView, 'getScrollableNode'>,\n  getInnerViewNode: $PropertyType<ScrollView, 'getInnerViewNode'>,\n  getInnerViewRef: $PropertyType<ScrollView, 'getInnerViewRef'>,\n  getNativeScrollRef: $PropertyType<ScrollView, 'getNativeScrollRef'>,\n  scrollTo: $PropertyType<ScrollView, 'scrollTo'>,\n  scrollToEnd: $PropertyType<ScrollView, 'scrollToEnd'>,\n  flashScrollIndicators: $PropertyType<ScrollView, 'flashScrollIndicators'>,\n  scrollResponderZoomTo: $PropertyType<ScrollView, 'scrollResponderZoomTo'>,\n  scrollResponderScrollNativeHandleToKeyboard: $PropertyType<\n    ScrollView,\n    'scrollResponderScrollNativeHandleToKeyboard',\n  >,\n|}>;\n\nexport type DecelerationRateType = 'fast' | 'normal' | number;\nexport type ScrollResponderType = ScrollViewImperativeMethods;\n\ntype PublicScrollViewInstance = $ReadOnly<{|\n  ...HostInstance,\n  ...ScrollViewImperativeMethods,\n|}>;\n\ntype InnerViewInstance = React.ElementRef<View>;\n\ntype IOSProps = $ReadOnly<{|\n  /**\n   * Controls whether iOS should automatically adjust the content inset\n   * for scroll views that are placed behind a navigation bar or\n   * tab bar/ toolbar. The default value is true.\n   * @platform ios\n   */\n  automaticallyAdjustContentInsets?: ?boolean,\n  /**\n   * Controls whether the ScrollView should automatically adjust its `contentInset`\n   * and `scrollViewInsets` when the Keyboard changes its size. The default value is false.\n   * @platform ios\n   */\n  automaticallyAdjustKeyboardInsets?: ?boolean,\n  /**\n   * Controls whether iOS should automatically adjust the scroll indicator\n   * insets. The default value is true. Available on iOS 13 and later.\n   * @platform ios\n   */\n  automaticallyAdjustsScrollIndicatorInsets?: ?boolean,\n  /**\n   * The amount by which the scroll view content is inset from the edges\n   * of the scroll view. Defaults to `{top: 0, left: 0, bottom: 0, right: 0}`.\n   * @platform ios\n   */\n  contentInset?: ?EdgeInsetsProp,\n  /**\n   * When true, the scroll view bounces when it reaches the end of the\n   * content if the content is larger then the scroll view along the axis of\n   * the scroll direction. When false, it disables all bouncing even if\n   * the `alwaysBounce*` props are true. The default value is true.\n   * @platform ios\n   */\n  bounces?: ?boolean,\n  /**\n   * By default, ScrollView has an active pan responder that hijacks panresponders\n   * deeper in the render tree in order to prevent accidental touches while scrolling.\n   * However, in certain occasions (such as when using snapToInterval) in a vertical scrollview\n   * You may want to disable this behavior in order to prevent the ScrollView from blocking touches\n   */\n  disableScrollViewPanResponder?: ?boolean,\n  /**\n   * When true, gestures can drive zoom past min/max and the zoom will animate\n   * to the min/max value at gesture end, otherwise the zoom will not exceed\n   * the limits.\n   * @platform ios\n   */\n  bouncesZoom?: ?boolean,\n  /**\n   * When true, the scroll view bounces horizontally when it reaches the end\n   * even if the content is smaller than the scroll view itself. The default\n   * value is true when `horizontal={true}` and false otherwise.\n   * @platform ios\n   */\n  alwaysBounceHorizontal?: ?boolean,\n  /**\n   * When true, the scroll view bounces vertically when it reaches the end\n   * even if the content is smaller than the scroll view itself. The default\n   * value is false when `horizontal={true}` and true otherwise.\n   * @platform ios\n   */\n  alwaysBounceVertical?: ?boolean,\n  /**\n   * When true, the scroll view automatically centers the content when the\n   * content is smaller than the scroll view bounds; when the content is\n   * larger than the scroll view, this property has no effect. The default\n   * value is false.\n   * @platform ios\n   */\n  centerContent?: ?boolean,\n  /**\n   * The style of the scroll indicators.\n   *\n   *   - `'default'` (the default), same as `black`.\n   *   - `'black'`, scroll indicator is black. This style is good against a light background.\n   *   - `'white'`, scroll indicator is white. This style is good against a dark background.\n   *\n   * @platform ios\n   */\n  indicatorStyle?: ?('default' | 'black' | 'white'),\n  /**\n   * When true, the ScrollView will try to lock to only vertical or horizontal\n   * scrolling while dragging.  The default value is false.\n   * @platform ios\n   */\n  directionalLockEnabled?: ?boolean,\n  /**\n   * When false, once tracking starts, won't try to drag if the touch moves.\n   * The default value is true.\n   * @platform ios\n   */\n  canCancelContentTouches?: ?boolean,\n  /**\n   * The maximum allowed zoom scale. The default value is 1.0.\n   * @platform ios\n   */\n  maximumZoomScale?: ?number,\n  /**\n   * The minimum allowed zoom scale. The default value is 1.0.\n   * @platform ios\n   */\n  minimumZoomScale?: ?number,\n  /**\n   * When true, ScrollView allows use of pinch gestures to zoom in and out.\n   * The default value is true.\n   * @platform ios\n   */\n  pinchGestureEnabled?: ?boolean,\n  /**\n   * The amount by which the scroll view indicators are inset from the edges\n   * of the scroll view. This should normally be set to the same value as\n   * the `contentInset`. Defaults to `{0, 0, 0, 0}`.\n   * @platform ios\n   */\n  scrollIndicatorInsets?: ?EdgeInsetsProp,\n  /**\n   * When true, the scroll view can be programmatically scrolled beyond its\n   * content size. The default value is false.\n   * @platform ios\n   */\n  scrollToOverflowEnabled?: ?boolean,\n  /**\n   * When true, the scroll view scrolls to top when the status bar is tapped.\n   * The default value is true.\n   * @platform ios\n   */\n  scrollsToTop?: ?boolean,\n  /**\n   * Fires when the scroll view scrolls to top after the status bar has been tapped\n   * @platform ios\n   */\n  onScrollToTop?: (event: ScrollEvent) => void,\n  /**\n   * When true, shows a horizontal scroll indicator.\n   * The default value is true.\n   */\n  showsHorizontalScrollIndicator?: ?boolean,\n  /**\n   * The current scale of the scroll view content. The default value is 1.0.\n   * @platform ios\n   */\n  zoomScale?: ?number,\n  /**\n   * This property specifies how the safe area insets are used to modify the\n   * content area of the scroll view. The default value of this property is\n   * \"never\". Available on iOS 11 and later.\n   * @platform ios\n   */\n  contentInsetAdjustmentBehavior?: ?(\n    | 'automatic'\n    | 'scrollableAxes'\n    | 'never'\n    | 'always'\n  ),\n|}>;\n\ntype AndroidProps = $ReadOnly<{|\n  /**\n   * Enables nested scrolling for Android API level 21+.\n   * Nested scrolling is supported by default on iOS\n   * @platform android\n   */\n  nestedScrollEnabled?: ?boolean,\n  /**\n   * Sometimes a scrollview takes up more space than its content fills. When this is\n   * the case, this prop will fill the rest of the scrollview with a color to avoid setting\n   * a background and creating unnecessary overdraw. This is an advanced optimization\n   * that is not needed in the general case.\n   * @platform android\n   */\n  endFillColor?: ?ColorValue,\n  /**\n   * Tag used to log scroll performance on this scroll view. Will force\n   * momentum events to be turned on (see sendMomentumEvents). This doesn't do\n   * anything out of the box and you need to implement a custom native\n   * FpsListener for it to be useful.\n   * @platform android\n   */\n  scrollPerfTag?: ?string,\n  /**\n   * Used to override default value of overScroll mode.\n   *\n   * Possible values:\n   *\n   *  - `'auto'` - Default value, allow a user to over-scroll\n   *    this view only if the content is large enough to meaningfully scroll.\n   *  - `'always'` - Always allow a user to over-scroll this view.\n   *  - `'never'` - Never allow a user to over-scroll this view.\n   *\n   * @platform android\n   */\n  overScrollMode?: ?('auto' | 'always' | 'never'),\n  /**\n   * Causes the scrollbars not to turn transparent when they are not in use.\n   * The default value is false.\n   *\n   * @platform android\n   */\n  persistentScrollbar?: ?boolean,\n  /**\n   * Fades out the edges of the scroll content.\n   *\n   * If the value is greater than 0, the fading edges will be set accordingly\n   * to the current scroll direction and position,\n   * indicating if there is more content to show.\n   *\n   * The default value is 0.\n   *\n   * @platform android\n   */\n  fadingEdgeLength?: ?number,\n|}>;\n\ntype StickyHeaderComponentType = component(\n  ref?: React.RefSetter<$ReadOnly<interface {setNextHeaderY: number => void}>>,\n  ...ScrollViewStickyHeaderProps\n);\n\nexport type Props = $ReadOnly<{|\n  ...ViewProps,\n  ...IOSProps,\n  ...AndroidProps,\n\n  /**\n   * These styles will be applied to the scroll view content container which\n   * wraps all of the child views. Example:\n   *\n   * ```\n   * return (\n   *   <ScrollView contentContainerStyle={styles.contentContainer}>\n   *   </ScrollView>\n   * );\n   * ...\n   * const styles = StyleSheet.create({\n   *   contentContainer: {\n   *     paddingVertical: 20\n   *   }\n   * });\n   * ```\n   */\n  contentContainerStyle?: ?ViewStyleProp,\n  /**\n   * Used to manually set the starting scroll offset.\n   * The default value is `{x: 0, y: 0}`.\n   */\n  contentOffset?: ?PointProp,\n  /**\n   * When true, the scroll view stops on the next index (in relation to scroll\n   * position at release) regardless of how fast the gesture is. This can be\n   * used for pagination when the page is less than the width of the\n   * horizontal ScrollView or the height of the vertical ScrollView. The default value is false.\n   */\n  disableIntervalMomentum?: ?boolean,\n  /**\n   * A floating-point number that determines how quickly the scroll view\n   * decelerates after the user lifts their finger. You may also use string\n   * shortcuts `\"normal\"` and `\"fast\"` which match the underlying iOS settings\n   * for `UIScrollViewDecelerationRateNormal` and\n   * `UIScrollViewDecelerationRateFast` respectively.\n   *\n   *   - `'normal'`: 0.998 on iOS, 0.985 on Android (the default)\n   *   - `'fast'`: 0.99 on iOS, 0.9 on Android\n   */\n  decelerationRate?: ?DecelerationRateType,\n\n  /**\n   * *Experimental, iOS Only*. The API is experimental and will change in future releases.\n   *\n   * Controls how much distance is travelled after user stops scrolling.\n   * Value greater than 1 will increase the distance travelled.\n   * Value less than 1 will decrease the distance travelled.\n   *\n   * @deprecated\n   *\n   * The default value is 1.\n   */\n  experimental_endDraggingSensitivityMultiplier?: ?number,\n\n  /**\n   * When true, the scroll view's children are arranged horizontally in a row\n   * instead of vertically in a column. The default value is false.\n   */\n  horizontal?: ?boolean,\n  /**\n   * If sticky headers should stick at the bottom instead of the top of the\n   * ScrollView. This is usually used with inverted ScrollViews.\n   */\n  invertStickyHeaders?: ?boolean,\n  /**\n   * Determines whether the keyboard gets dismissed in response to a drag.\n   *\n   * *Cross platform*\n   *\n   *   - `'none'` (the default), drags do not dismiss the keyboard.\n   *   - `'on-drag'`, the keyboard is dismissed when a drag begins.\n   *\n   * *iOS Only*\n   *\n   *   - `'interactive'`, the keyboard is dismissed interactively with the drag and moves in\n   *     synchrony with the touch; dragging upwards cancels the dismissal.\n   *     On android this is not supported and it will have the same behavior as 'none'.\n   */\n  keyboardDismissMode?: ?// default\n  // cross-platform\n  ('none' | 'on-drag' | 'interactive'), // ios only\n  /**\n   * Determines when the keyboard should stay visible after a tap.\n   *\n   *   - `'never'` (the default), tapping outside of the focused text input when the keyboard\n   *     is up dismisses the keyboard. When this happens, children won't receive the tap.\n   *   - `'always'`, the keyboard will not dismiss automatically, and the scroll view will not\n   *     catch taps, but children of the scroll view can catch taps.\n   *   - `'handled'`, the keyboard will not dismiss automatically when the tap was handled by\n   *     a children, (or captured by an ancestor).\n   *   - `false`, deprecated, use 'never' instead\n   *   - `true`, deprecated, use 'always' instead\n   */\n  keyboardShouldPersistTaps?: ?('always' | 'never' | 'handled' | true | false),\n  /**\n   * When set, the scroll view will adjust the scroll position so that the first child that is\n   * partially or fully visible and at or beyond `minIndexForVisible` will not change position.\n   * This is useful for lists that are loading content in both directions, e.g. a chat thread,\n   * where new messages coming in might otherwise cause the scroll position to jump. A value of 0\n   * is common, but other values such as 1 can be used to skip loading spinners or other content\n   * that should not maintain position.\n   *\n   * The optional `autoscrollToTopThreshold` can be used to make the content automatically scroll\n   * to the top after making the adjustment if the user was within the threshold of the top before\n   * the adjustment was made. This is also useful for chat-like applications where you want to see\n   * new messages scroll into place, but not if the user has scrolled up a ways and it would be\n   * disruptive to scroll a bunch.\n   *\n   * Caveat 1: Reordering elements in the scrollview with this enabled will probably cause\n   * jumpiness and jank. It can be fixed, but there are currently no plans to do so. For now,\n   * don't re-order the content of any ScrollViews or Lists that use this feature.\n   *\n   * Caveat 2: This simply uses `contentOffset` and `frame.origin` in native code to compute\n   * visibility. Occlusion, transforms, and other complexity won't be taken into account as to\n   * whether content is \"visible\" or not.\n   *\n   */\n  maintainVisibleContentPosition?: ?$ReadOnly<{|\n    minIndexForVisible: number,\n    autoscrollToTopThreshold?: ?number,\n  |}>,\n  /**\n   * Called when the momentum scroll starts (scroll which occurs as the ScrollView glides to a stop).\n   */\n  onMomentumScrollBegin?: ?(event: ScrollEvent) => void,\n  /**\n   * Called when the momentum scroll ends (scroll which occurs as the ScrollView glides to a stop).\n   */\n  onMomentumScrollEnd?: ?(event: ScrollEvent) => void,\n\n  /**\n   * Fires at most once per frame during scrolling.\n   */\n  onScroll?: ?(event: ScrollEvent) => void,\n  /**\n   * Called when the user begins to drag the scroll view.\n   */\n  onScrollBeginDrag?: ?(event: ScrollEvent) => void,\n  /**\n   * Called when the user stops dragging the scroll view and it either stops\n   * or begins to glide.\n   */\n  onScrollEndDrag?: ?(event: ScrollEvent) => void,\n  /**\n   * Called when scrollable content view of the ScrollView changes.\n   *\n   * Handler function is passed the content width and content height as parameters:\n   * `(contentWidth, contentHeight)`\n   *\n   * It's implemented using onLayout handler attached to the content container\n   * which this ScrollView renders.\n   */\n  onContentSizeChange?: (contentWidth: number, contentHeight: number) => void,\n  onKeyboardDidShow?: (event: KeyboardEvent) => void,\n  onKeyboardDidHide?: (event: KeyboardEvent) => void,\n  onKeyboardWillShow?: (event: KeyboardEvent) => void,\n  onKeyboardWillHide?: (event: KeyboardEvent) => void,\n  /**\n   * When true, the scroll view stops on multiples of the scroll view's size\n   * when scrolling. This can be used for horizontal pagination. The default\n   * value is false.\n   */\n  pagingEnabled?: ?boolean,\n  /**\n   * When false, the view cannot be scrolled via touch interaction.\n   * The default value is true.\n   *\n   * Note that the view can always be scrolled by calling `scrollTo`.\n   */\n  scrollEnabled?: ?boolean,\n  /**\n   * Limits how often scroll events will be fired while scrolling, specified as\n   * a time interval in ms. This may be useful when expensive work is performed\n   * in response to scrolling. Values <= `16` will disable throttling,\n   * regardless of the refresh rate of the device.\n   */\n  scrollEventThrottle?: ?number,\n  /**\n   * When true, shows a vertical scroll indicator.\n   * The default value is true.\n   */\n  showsVerticalScrollIndicator?: ?boolean,\n  /**\n   * When true, Sticky header is hidden when scrolling down, and dock at the top\n   * when scrolling up\n   */\n  stickyHeaderHiddenOnScroll?: ?boolean,\n  /**\n   * An array of child indices determining which children get docked to the\n   * top of the screen when scrolling. For example, passing\n   * `stickyHeaderIndices={[0]}` will cause the first child to be fixed to the\n   * top of the scroll view. This property is not supported in conjunction\n   * with `horizontal={true}`.\n   */\n  stickyHeaderIndices?: ?$ReadOnlyArray<number>,\n  /**\n   * A React Component that will be used to render sticky headers.\n   * To be used together with `stickyHeaderIndices` or with `SectionList`, defaults to `ScrollViewStickyHeader`.\n   * You may need to set this if your sticky header uses custom transforms (eg. translation),\n   * for example when you want your list to have an animated hidable header.\n   */\n  StickyHeaderComponent?: StickyHeaderComponentType,\n  /**\n   * When `snapToInterval` is set, `snapToAlignment` will define the relationship\n   * of the snapping to the scroll view.\n   *\n   *   - `'start'` (the default) will align the snap at the left (horizontal) or top (vertical)\n   *   - `'center'` will align the snap in the center\n   *   - `'end'` will align the snap at the right (horizontal) or bottom (vertical)\n   */\n  snapToAlignment?: ?('start' | 'center' | 'end'),\n  /**\n   * When set, causes the scroll view to stop at multiples of the value of\n   * `snapToInterval`. This can be used for paginating through children\n   * that have lengths smaller than the scroll view. Typically used in\n   * combination with `snapToAlignment` and `decelerationRate=\"fast\"`.\n   *\n   * Overrides less configurable `pagingEnabled` prop.\n   */\n  snapToInterval?: ?number,\n  /**\n   * When set, causes the scroll view to stop at the defined offsets.\n   * This can be used for paginating through variously sized children\n   * that have lengths smaller than the scroll view. Typically used in\n   * combination with `decelerationRate=\"fast\"`.\n   *\n   * Overrides less configurable `pagingEnabled` and `snapToInterval` props.\n   */\n  snapToOffsets?: ?$ReadOnlyArray<number>,\n  /**\n   * Use in conjunction with `snapToOffsets`. By default, the beginning\n   * of the list counts as a snap offset. Set `snapToStart` to false to disable\n   * this behavior and allow the list to scroll freely between its start and\n   * the first `snapToOffsets` offset.\n   * The default value is true.\n   */\n  snapToStart?: ?boolean,\n  /**\n   * Use in conjunction with `snapToOffsets`. By default, the end\n   * of the list counts as a snap offset. Set `snapToEnd` to false to disable\n   * this behavior and allow the list to scroll freely between its end and\n   * the last `snapToOffsets` offset.\n   * The default value is true.\n   */\n  snapToEnd?: ?boolean,\n  /**\n   * Experimental: When true, offscreen child views (whose `overflow` value is\n   * `hidden`) are removed from their native backing superview when offscreen.\n   * This can improve scrolling performance on long lists. The default value is\n   * true.\n   */\n  removeClippedSubviews?: ?boolean,\n  /**\n   * A RefreshControl component, used to provide pull-to-refresh\n   * functionality for the ScrollView. Only works for vertical ScrollViews\n   * (`horizontal` prop must be `false`).\n   *\n   * See [RefreshControl](docs/refreshcontrol.html).\n   */\n  /* $FlowFixMe[unclear-type] - how to handle generic type without existential\n   * operator? */\n  refreshControl?: ?ExactReactElement_DEPRECATED<any>,\n  children?: React.Node,\n  /**\n   * A ref to the inner View element of the ScrollView. This should be used\n   * instead of calling `getInnerViewRef`.\n   */\n  innerViewRef?: React.RefSetter<InnerViewInstance>,\n  /**\n   * A ref to the Native ScrollView component. This ref can be used to call\n   * all of ScrollView's public methods, in addition to native methods like\n   * measure, measureLayout, etc.\n   */\n  scrollViewRef?: React.RefSetter<PublicScrollViewInstance>,\n|}>;\n\ntype State = {|\n  layoutHeight: ?number,\n|};\n\nconst IS_ANIMATING_TOUCH_START_THRESHOLD_MS = 16;\n\nexport type ScrollViewComponentStatics = $ReadOnly<{|\n  Context: typeof ScrollViewContext,\n|}>;\n\n/**\n * Component that wraps platform ScrollView while providing\n * integration with touch locking \"responder\" system.\n *\n * Keep in mind that ScrollViews must have a bounded height in order to work,\n * since they contain unbounded-height children into a bounded container (via\n * a scroll interaction). In order to bound the height of a ScrollView, either\n * set the height of the view directly (discouraged) or make sure all parent\n * views have bounded height. Forgetting to transfer `{flex: 1}` down the\n * view stack can lead to errors here, which the element inspector makes\n * easy to debug.\n *\n * Doesn't yet support other contained responders from blocking this scroll\n * view from becoming the responder.\n *\n *\n * `<ScrollView>` vs [`<FlatList>`](https://reactnative.dev/docs/flatlist) - which one to use?\n *\n * `ScrollView` simply renders all its react child components at once. That\n * makes it very easy to understand and use.\n *\n * On the other hand, this has a performance downside. Imagine you have a very\n * long list of items you want to display, maybe several screens worth of\n * content. Creating JS components and native views for everything all at once,\n * much of which may not even be shown, will contribute to slow rendering and\n * increased memory usage.\n *\n * This is where `FlatList` comes into play. `FlatList` renders items lazily,\n * just when they are about to appear, and removes items that scroll way off\n * screen to save memory and processing time.\n *\n * `FlatList` is also handy if you want to render separators between your items,\n * multiple columns, infinite scroll loading, or any number of other features it\n * supports out of the box.\n */\nclass ScrollView extends React.Component<Props, State> {\n  static Context: typeof ScrollViewContext = ScrollViewContext;\n\n  constructor(props: Props) {\n    super(props);\n\n    this._scrollAnimatedValue = new AnimatedImplementation.Value(\n      this.props.contentOffset?.y ?? 0,\n    );\n    this._scrollAnimatedValue.setOffset(this.props.contentInset?.top ?? 0);\n  }\n\n  _scrollAnimatedValue: AnimatedImplementation.Value;\n  _scrollAnimatedValueAttachment: ?{detach: () => void, ...} = null;\n  _stickyHeaderRefs: Map<string, React.ElementRef<StickyHeaderComponentType>> =\n    new Map();\n  _headerLayoutYs: Map<string, number> = new Map();\n\n  _keyboardMetrics: ?KeyboardMetrics = null;\n  _additionalScrollOffset: number = 0;\n  _isTouching: boolean = false;\n  _lastMomentumScrollBeginTime: number = 0;\n  _lastMomentumScrollEndTime: number = 0;\n\n  // Reset to false every time becomes responder. This is used to:\n  // - Determine if the scroll view has been scrolled and therefore should\n  // refuse to give up its responder lock.\n  // - Determine if releasing should dismiss the keyboard when we are in\n  // tap-to-dismiss mode (this.props.keyboardShouldPersistTaps !== 'always').\n  _observedScrollSinceBecomingResponder: boolean = false;\n  _becameResponderWhileAnimating: boolean = false;\n  _preventNegativeScrollOffset: ?boolean = null;\n\n  _animated: ?boolean = null;\n\n  _subscriptionKeyboardWillShow: ?EventSubscription = null;\n  _subscriptionKeyboardWillHide: ?EventSubscription = null;\n  _subscriptionKeyboardDidShow: ?EventSubscription = null;\n  _subscriptionKeyboardDidHide: ?EventSubscription = null;\n\n  state: State = {\n    layoutHeight: null,\n  };\n\n  componentDidMount() {\n    if (typeof this.props.keyboardShouldPersistTaps === 'boolean') {\n      console.warn(\n        `'keyboardShouldPersistTaps={${\n          this.props.keyboardShouldPersistTaps === true ? 'true' : 'false'\n        }}' is deprecated. ` +\n          `Use 'keyboardShouldPersistTaps=\"${\n            this.props.keyboardShouldPersistTaps ? 'always' : 'never'\n          }\"' instead`,\n      );\n    }\n\n    this._keyboardMetrics = Keyboard.metrics();\n    this._additionalScrollOffset = 0;\n\n    this._subscriptionKeyboardWillShow = Keyboard.addListener(\n      'keyboardWillShow',\n      this.scrollResponderKeyboardWillShow,\n    );\n    this._subscriptionKeyboardWillHide = Keyboard.addListener(\n      'keyboardWillHide',\n      this.scrollResponderKeyboardWillHide,\n    );\n    this._subscriptionKeyboardDidShow = Keyboard.addListener(\n      'keyboardDidShow',\n      this.scrollResponderKeyboardDidShow,\n    );\n    this._subscriptionKeyboardDidHide = Keyboard.addListener(\n      'keyboardDidHide',\n      this.scrollResponderKeyboardDidHide,\n    );\n\n    this._updateAnimatedNodeAttachment();\n  }\n\n  componentDidUpdate(prevProps: Props) {\n    const prevContentInsetTop = prevProps.contentInset\n      ? prevProps.contentInset.top\n      : 0;\n    const newContentInsetTop = this.props.contentInset\n      ? this.props.contentInset.top\n      : 0;\n    if (prevContentInsetTop !== newContentInsetTop) {\n      this._scrollAnimatedValue.setOffset(newContentInsetTop || 0);\n    }\n\n    this._updateAnimatedNodeAttachment();\n  }\n\n  componentWillUnmount() {\n    if (this._subscriptionKeyboardWillShow != null) {\n      this._subscriptionKeyboardWillShow.remove();\n    }\n    if (this._subscriptionKeyboardWillHide != null) {\n      this._subscriptionKeyboardWillHide.remove();\n    }\n    if (this._subscriptionKeyboardDidShow != null) {\n      this._subscriptionKeyboardDidShow.remove();\n    }\n    if (this._subscriptionKeyboardDidHide != null) {\n      this._subscriptionKeyboardDidHide.remove();\n    }\n\n    if (this._scrollAnimatedValueAttachment) {\n      this._scrollAnimatedValueAttachment.detach();\n    }\n  }\n\n  /**\n   * Returns a reference to the underlying scroll responder, which supports\n   * operations like `scrollTo`. All ScrollView-like components should\n   * implement this method so that they can be composed while providing access\n   * to the underlying scroll responder's methods.\n   */\n  getScrollResponder: () => ScrollResponderType = () => {\n    // $FlowFixMe[unclear-type]\n    return ((this: any): ScrollResponderType);\n  };\n\n  getScrollableNode: () => ?number = () => {\n    return findNodeHandle(this.getNativeScrollRef());\n  };\n\n  getInnerViewNode: () => ?number = () => {\n    return findNodeHandle(this._innerView.nativeInstance);\n  };\n\n  getInnerViewRef: () => InnerViewInstance | null = () => {\n    return this._innerView.nativeInstance;\n  };\n\n  getNativeScrollRef: () => HostInstance | null = () => {\n    return this._scrollView.nativeInstance;\n  };\n\n  /**\n   * Scrolls to a given x, y offset, either immediately or with a smooth animation.\n   *\n   * Example:\n   *\n   * `scrollTo({x: 0, y: 0, animated: true})`\n   *\n   * Note: The weird function signature is due to the fact that, for historical reasons,\n   * the function also accepts separate arguments as an alternative to the options object.\n   * This is deprecated due to ambiguity (y before x), and SHOULD NOT BE USED.\n   */\n  scrollTo: (\n    options?:\n      | {\n          x?: number,\n          y?: number,\n          animated?: boolean,\n          ...\n        }\n      | number,\n    deprecatedX?: number,\n    deprecatedAnimated?: boolean,\n  ) => void = (\n    options?:\n      | {\n          x?: number,\n          y?: number,\n          animated?: boolean,\n          ...\n        }\n      | number,\n    deprecatedX?: number,\n    deprecatedAnimated?: boolean,\n  ) => {\n    let x, y, animated;\n    if (typeof options === 'number') {\n      console.warn(\n        '`scrollTo(y, x, animated)` is deprecated. Use `scrollTo({x: 5, y: 5, ' +\n          'animated: true})` instead.',\n      );\n      y = options;\n      x = deprecatedX;\n      animated = deprecatedAnimated;\n    } else if (options) {\n      y = options.y;\n      x = options.x;\n      animated = options.animated;\n    }\n    const component = this.getNativeScrollRef();\n    if (component == null) {\n      return;\n    }\n    Commands.scrollTo(component, x || 0, y || 0, animated !== false);\n  };\n\n  /**\n   * If this is a vertical ScrollView scrolls to the bottom.\n   * If this is a horizontal ScrollView scrolls to the right.\n   *\n   * Use `scrollToEnd({animated: true})` for smooth animated scrolling,\n   * `scrollToEnd({animated: false})` for immediate scrolling.\n   * If no options are passed, `animated` defaults to true.\n   */\n  scrollToEnd: (options?: ?{animated?: boolean, ...}) => void = (\n    options?: ?{animated?: boolean, ...},\n  ) => {\n    // Default to true\n    const animated = (options && options.animated) !== false;\n    const component = this.getNativeScrollRef();\n    if (component == null) {\n      return;\n    }\n    Commands.scrollToEnd(component, animated);\n  };\n\n  /**\n   * Displays the scroll indicators momentarily.\n   *\n   * @platform ios\n   */\n  flashScrollIndicators: () => void = () => {\n    const component = this.getNativeScrollRef();\n    if (component == null) {\n      return;\n    }\n    Commands.flashScrollIndicators(component);\n  };\n\n  /**\n   * This method should be used as the callback to onFocus in a TextInputs'\n   * parent view. Note that any module using this mixin needs to return\n   * the parent view's ref in getScrollViewRef() in order to use this method.\n   * @param {number} nodeHandle The TextInput node handle\n   * @param {number} additionalOffset The scroll view's bottom \"contentInset\".\n   *        Default is 0.\n   * @param {bool} preventNegativeScrolling Whether to allow pulling the content\n   *        down to make it meet the keyboard's top. Default is false.\n   */\n  scrollResponderScrollNativeHandleToKeyboard: (\n    nodeHandle: number | HostInstance,\n    additionalOffset?: number,\n    preventNegativeScrollOffset?: boolean,\n  ) => void = (\n    nodeHandle: number | HostInstance,\n    additionalOffset?: number,\n    preventNegativeScrollOffset?: boolean,\n  ) => {\n    this._additionalScrollOffset = additionalOffset || 0;\n    this._preventNegativeScrollOffset = !!preventNegativeScrollOffset;\n\n    if (this._innerView.nativeInstance == null) {\n      return;\n    }\n\n    if (typeof nodeHandle === 'number') {\n      UIManager.measureLayout(\n        nodeHandle,\n        nullthrows(findNodeHandle(this)),\n        // $FlowFixMe[method-unbinding] added when improving typing for this parameters\n        this._textInputFocusError,\n        this._inputMeasureAndScrollToKeyboard,\n      );\n    } else {\n      nodeHandle.measureLayout(\n        this._innerView.nativeInstance,\n        this._inputMeasureAndScrollToKeyboard,\n        // $FlowFixMe[method-unbinding] added when improving typing for this parameters\n        this._textInputFocusError,\n      );\n    }\n  };\n\n  /**\n   * A helper function to zoom to a specific rect in the scrollview. The argument has the shape\n   * {x: number; y: number; width: number; height: number; animated: boolean = true}\n   *\n   * @platform ios\n   */\n  scrollResponderZoomTo: (\n    rect: {|\n      x: number,\n      y: number,\n      width: number,\n      height: number,\n      animated?: boolean,\n    |},\n    animated?: boolean, // deprecated, put this inside the rect argument instead\n  ) => void = (\n    rect: {|\n      x: number,\n      y: number,\n      width: number,\n      height: number,\n      animated?: boolean,\n    |},\n    animated?: boolean, // deprecated, put this inside the rect argument instead\n  ) => {\n    invariant(Platform.OS === 'ios', 'zoomToRect is not implemented');\n    if ('animated' in rect) {\n      this._animated = rect.animated;\n      delete rect.animated;\n    } else if (typeof animated !== 'undefined') {\n      console.warn(\n        '`scrollResponderZoomTo` `animated` argument is deprecated. Use `options.animated` instead',\n      );\n    }\n\n    const component = this.getNativeScrollRef();\n    if (component == null) {\n      return;\n    }\n    Commands.zoomToRect(component, rect, animated !== false);\n  };\n\n  _textInputFocusError() {\n    console.warn('Error measuring text field.');\n  }\n\n  /**\n   * The calculations performed here assume the scroll view takes up the entire\n   * screen - even if has some content inset. We then measure the offsets of the\n   * keyboard, and compensate both for the scroll view's \"contentInset\".\n   *\n   * @param {number} left Position of input w.r.t. table view.\n   * @param {number} top Position of input w.r.t. table view.\n   * @param {number} width Width of the text input.\n   * @param {number} height Height of the text input.\n   */\n  _inputMeasureAndScrollToKeyboard: (\n    left: number,\n    top: number,\n    width: number,\n    height: number,\n  ) => void = (left: number, top: number, width: number, height: number) => {\n    let keyboardScreenY = Dimensions.get('window').height;\n\n    const scrollTextInputIntoVisibleRect = () => {\n      if (this._keyboardMetrics != null) {\n        keyboardScreenY = this._keyboardMetrics.screenY;\n      }\n      let scrollOffsetY =\n        top - keyboardScreenY + height + this._additionalScrollOffset;\n\n      // By default, this can scroll with negative offset, pulling the content\n      // down so that the target component's bottom meets the keyboard's top.\n      // If requested otherwise, cap the offset at 0 minimum to avoid content\n      // shifting down.\n      if (this._preventNegativeScrollOffset === true) {\n        scrollOffsetY = Math.max(0, scrollOffsetY);\n      }\n      this.scrollTo({x: 0, y: scrollOffsetY, animated: true});\n\n      this._additionalScrollOffset = 0;\n      this._preventNegativeScrollOffset = false;\n    };\n\n    if (this._keyboardMetrics == null) {\n      // `_keyboardMetrics` is set inside `scrollResponderKeyboardWillShow` which\n      // is not guaranteed to be called before `_inputMeasureAndScrollToKeyboard` but native has already scheduled it.\n      // In case it was not called before `_inputMeasureAndScrollToKeyboard`, we postpone scrolling to\n      // text input.\n      setTimeout(() => {\n        scrollTextInputIntoVisibleRect();\n      }, 0);\n    } else {\n      scrollTextInputIntoVisibleRect();\n    }\n  };\n\n  _getKeyForIndex(index: $FlowFixMe, childArray: $FlowFixMe): $FlowFixMe {\n    const child = childArray[index];\n    return child && child.key;\n  }\n\n  _updateAnimatedNodeAttachment() {\n    if (this._scrollAnimatedValueAttachment) {\n      this._scrollAnimatedValueAttachment.detach();\n    }\n    if (\n      this.props.stickyHeaderIndices &&\n      this.props.stickyHeaderIndices.length > 0\n    ) {\n      this._scrollAnimatedValueAttachment =\n        AnimatedImplementation.attachNativeEvent(\n          this.getNativeScrollRef(),\n          'onScroll',\n          [{nativeEvent: {contentOffset: {y: this._scrollAnimatedValue}}}],\n        );\n    }\n  }\n\n  _setStickyHeaderRef(\n    key: string,\n    ref: ?React.ElementRef<StickyHeaderComponentType>,\n  ) {\n    if (ref) {\n      this._stickyHeaderRefs.set(key, ref);\n    } else {\n      this._stickyHeaderRefs.delete(key);\n    }\n  }\n\n  _onStickyHeaderLayout(index: $FlowFixMe, event: $FlowFixMe, key: $FlowFixMe) {\n    const {stickyHeaderIndices} = this.props;\n    if (!stickyHeaderIndices) {\n      return;\n    }\n    const childArray = React.Children.toArray<$FlowFixMe>(this.props.children);\n    if (key !== this._getKeyForIndex(index, childArray)) {\n      // ignore stale layout update\n      return;\n    }\n\n    const layoutY = event.nativeEvent.layout.y;\n    this._headerLayoutYs.set(key, layoutY);\n\n    const indexOfIndex = stickyHeaderIndices.indexOf(index);\n    const previousHeaderIndex = stickyHeaderIndices[indexOfIndex - 1];\n    if (previousHeaderIndex != null) {\n      const previousHeader = this._stickyHeaderRefs.get(\n        this._getKeyForIndex(previousHeaderIndex, childArray),\n      );\n      previousHeader &&\n        previousHeader.setNextHeaderY &&\n        previousHeader.setNextHeaderY(layoutY);\n    }\n  }\n\n  _handleScroll = (e: ScrollEvent) => {\n    this._observedScrollSinceBecomingResponder = true;\n    this.props.onScroll && this.props.onScroll(e);\n  };\n\n  _handleLayout = (e: LayoutEvent) => {\n    if (this.props.invertStickyHeaders === true) {\n      this.setState({layoutHeight: e.nativeEvent.layout.height});\n    }\n    if (this.props.onLayout) {\n      this.props.onLayout(e);\n    }\n  };\n\n  _handleContentOnLayout = (e: LayoutEvent) => {\n    const {width, height} = e.nativeEvent.layout;\n    this.props.onContentSizeChange &&\n      this.props.onContentSizeChange(width, height);\n  };\n\n  _innerView: RefForwarder<InnerViewInstance, InnerViewInstance> =\n    createRefForwarder(\n      (instance: InnerViewInstance): InnerViewInstance => instance,\n    );\n\n  _scrollView: RefForwarder<HostInstance, PublicScrollViewInstance | null> =\n    createRefForwarder(nativeInstance => {\n      // This is a hack. Ideally we would forwardRef  to the underlying\n      // host component. However, since ScrollView has it's own methods that can be\n      // called as well, if we used the standard forwardRef then these\n      // methods wouldn't be accessible and thus be a breaking change.\n      //\n      // Therefore we edit ref to include ScrollView's public methods so that\n      // they are callable from the ref.\n\n      // $FlowFixMe[prop-missing] - Known issue with appending custom methods.\n      const publicInstance: PublicScrollViewInstance = Object.assign(\n        nativeInstance,\n        {\n          getScrollResponder: this.getScrollResponder,\n          getScrollableNode: this.getScrollableNode,\n          getInnerViewNode: this.getInnerViewNode,\n          getInnerViewRef: this.getInnerViewRef,\n          getNativeScrollRef: this.getNativeScrollRef,\n          scrollTo: this.scrollTo,\n          scrollToEnd: this.scrollToEnd,\n          flashScrollIndicators: this.flashScrollIndicators,\n          scrollResponderZoomTo: this.scrollResponderZoomTo,\n          scrollResponderScrollNativeHandleToKeyboard:\n            this.scrollResponderScrollNativeHandleToKeyboard,\n        },\n      );\n\n      return publicInstance;\n    });\n\n  /**\n   * Warning, this may be called several times for a single keyboard opening.\n   * It's best to store the information in this method and then take any action\n   * at a later point (either in `keyboardDidShow` or other).\n   *\n   * Here's the order that events occur in:\n   * - focus\n   * - willShow {startCoordinates, endCoordinates} several times\n   * - didShow several times\n   * - blur\n   * - willHide {startCoordinates, endCoordinates} several times\n   * - didHide several times\n   *\n   * The `ScrollResponder` module callbacks for each of these events.\n   * Even though any user could have easily listened to keyboard events\n   * themselves, using these `props` callbacks ensures that ordering of events\n   * is consistent - and not dependent on the order that the keyboard events are\n   * subscribed to. This matters when telling the scroll view to scroll to where\n   * the keyboard is headed - the scroll responder better have been notified of\n   * the keyboard destination before being instructed to scroll to where the\n   * keyboard will be. Stick to the `ScrollResponder` callbacks, and everything\n   * will work.\n   *\n   * WARNING: These callbacks will fire even if a keyboard is displayed in a\n   * different navigation pane. Filter out the events to determine if they are\n   * relevant to you. (For example, only if you receive these callbacks after\n   * you had explicitly focused a node etc).\n   */\n\n  scrollResponderKeyboardWillShow: (e: KeyboardEvent) => void = (\n    e: KeyboardEvent,\n  ) => {\n    this._keyboardMetrics = e.endCoordinates;\n    this.props.onKeyboardWillShow && this.props.onKeyboardWillShow(e);\n  };\n\n  scrollResponderKeyboardWillHide: (e: KeyboardEvent) => void = (\n    e: KeyboardEvent,\n  ) => {\n    this._keyboardMetrics = null;\n    this.props.onKeyboardWillHide && this.props.onKeyboardWillHide(e);\n  };\n\n  scrollResponderKeyboardDidShow: (e: KeyboardEvent) => void = (\n    e: KeyboardEvent,\n  ) => {\n    this._keyboardMetrics = e.endCoordinates;\n    this.props.onKeyboardDidShow && this.props.onKeyboardDidShow(e);\n  };\n\n  scrollResponderKeyboardDidHide: (e: KeyboardEvent) => void = (\n    e: KeyboardEvent,\n  ) => {\n    this._keyboardMetrics = null;\n    this.props.onKeyboardDidHide && this.props.onKeyboardDidHide(e);\n  };\n\n  /**\n   * Invoke this from an `onMomentumScrollBegin` event.\n   */\n  _handleMomentumScrollBegin: (e: ScrollEvent) => void = (e: ScrollEvent) => {\n    this._lastMomentumScrollBeginTime = global.performance.now();\n    this.props.onMomentumScrollBegin && this.props.onMomentumScrollBegin(e);\n  };\n\n  /**\n   * Invoke this from an `onMomentumScrollEnd` event.\n   */\n  _handleMomentumScrollEnd: (e: ScrollEvent) => void = (e: ScrollEvent) => {\n    FrameRateLogger.endScroll();\n    this._lastMomentumScrollEndTime = global.performance.now();\n    this.props.onMomentumScrollEnd && this.props.onMomentumScrollEnd(e);\n  };\n\n  /**\n   * Unfortunately, `onScrollBeginDrag` also fires when *stopping* the scroll\n   * animation, and there's not an easy way to distinguish a drag vs. stopping\n   * momentum.\n   *\n   * Invoke this from an `onScrollBeginDrag` event.\n   */\n  _handleScrollBeginDrag: (e: ScrollEvent) => void = (e: ScrollEvent) => {\n    FrameRateLogger.beginScroll(); // TODO: track all scrolls after implementing onScrollEndAnimation\n\n    if (\n      Platform.OS === 'android' &&\n      this.props.keyboardDismissMode === 'on-drag'\n    ) {\n      dismissKeyboard();\n    }\n\n    this.props.onScrollBeginDrag && this.props.onScrollBeginDrag(e);\n  };\n\n  /**\n   * Invoke this from an `onScrollEndDrag` event.\n   */\n  _handleScrollEndDrag: (e: ScrollEvent) => void = (e: ScrollEvent) => {\n    const {velocity} = e.nativeEvent;\n    // - If we are animating, then this is a \"drag\" that is stopping the scrollview and momentum end\n    //   will fire.\n    // - If velocity is non-zero, then the interaction will stop when momentum scroll ends or\n    //   another drag starts and ends.\n    // - If we don't get velocity, better to stop the interaction twice than not stop it.\n    if (\n      !this._isAnimating() &&\n      (!velocity || (velocity.x === 0 && velocity.y === 0))\n    ) {\n      FrameRateLogger.endScroll();\n    }\n    this.props.onScrollEndDrag && this.props.onScrollEndDrag(e);\n  };\n\n  /**\n   * A helper function for this class that lets us quickly determine if the\n   * view is currently animating. This is particularly useful to know when\n   * a touch has just started or ended.\n   */\n  _isAnimating: () => boolean = () => {\n    const now = global.performance.now();\n    const timeSinceLastMomentumScrollEnd =\n      now - this._lastMomentumScrollEndTime;\n    const isAnimating =\n      timeSinceLastMomentumScrollEnd < IS_ANIMATING_TOUCH_START_THRESHOLD_MS ||\n      this._lastMomentumScrollEndTime < this._lastMomentumScrollBeginTime;\n    return isAnimating;\n  };\n\n  /**\n   * Invoke this from an `onResponderGrant` event.\n   */\n  _handleResponderGrant: (e: PressEvent) => void = (e: PressEvent) => {\n    this._observedScrollSinceBecomingResponder = false;\n    this.props.onResponderGrant && this.props.onResponderGrant(e);\n    this._becameResponderWhileAnimating = this._isAnimating();\n  };\n\n  /**\n   * Invoke this from an `onResponderReject` event.\n   *\n   * Some other element is not yielding its role as responder. Normally, we'd\n   * just disable the `UIScrollView`, but a touch has already began on it, the\n   * `UIScrollView` will not accept being disabled after that. The easiest\n   * solution for now is to accept the limitation of disallowing this\n   * altogether. To improve this, find a way to disable the `UIScrollView` after\n   * a touch has already started.\n   */\n  _handleResponderReject: () => void = () => {};\n\n  /**\n   * Invoke this from an `onResponderRelease` event.\n   */\n  _handleResponderRelease: (e: PressEvent) => void = (e: PressEvent) => {\n    this._isTouching = e.nativeEvent.touches.length !== 0;\n    this.props.onResponderRelease && this.props.onResponderRelease(e);\n\n    if (typeof e.target === 'number') {\n      if (__DEV__) {\n        console.error(\n          'Did not expect event target to be a number. Should have been a native component',\n        );\n      }\n\n      return;\n    }\n\n    // By default scroll views will unfocus a textField\n    // if another touch occurs outside of it\n    const currentlyFocusedTextInput = TextInputState.currentlyFocusedInput();\n    if (\n      currentlyFocusedTextInput != null &&\n      this.props.keyboardShouldPersistTaps !== true &&\n      this.props.keyboardShouldPersistTaps !== 'always' &&\n      this._keyboardIsDismissible() &&\n      e.target !== currentlyFocusedTextInput &&\n      !this._observedScrollSinceBecomingResponder &&\n      !this._becameResponderWhileAnimating\n    ) {\n      TextInputState.blurTextInput(currentlyFocusedTextInput);\n    }\n  };\n\n  /**\n   * We will allow the scroll view to give up its lock iff it acquired the lock\n   * during an animation. This is a very useful default that happens to satisfy\n   * many common user experiences.\n   *\n   * - Stop a scroll on the left edge, then turn that into an outer view's\n   *   backswipe.\n   * - Stop a scroll mid-bounce at the top, continue pulling to have the outer\n   *   view dismiss.\n   * - However, without catching the scroll view mid-bounce (while it is\n   *   motionless), if you drag far enough for the scroll view to become\n   *   responder (and therefore drag the scroll view a bit), any backswipe\n   *   navigation of a swipe gesture higher in the view hierarchy, should be\n   *   rejected.\n   */\n  _handleResponderTerminationRequest: () => boolean = () => {\n    return !this._observedScrollSinceBecomingResponder;\n  };\n\n  /**\n   * Invoke this from an `onScroll` event.\n   */\n  _handleScrollShouldSetResponder: () => boolean = () => {\n    // Allow any event touch pass through if the default pan responder is disabled\n    if (this.props.disableScrollViewPanResponder === true) {\n      return false;\n    }\n    return this._isTouching;\n  };\n\n  /**\n   * Merely touch starting is not sufficient for a scroll view to become the\n   * responder. Being the \"responder\" means that the very next touch move/end\n   * event will result in an action/movement.\n   *\n   * Invoke this from an `onStartShouldSetResponder` event.\n   *\n   * `onStartShouldSetResponder` is used when the next move/end will trigger\n   * some UI movement/action, but when you want to yield priority to views\n   * nested inside of the view.\n   *\n   * There may be some cases where scroll views actually should return `true`\n   * from `onStartShouldSetResponder`: Any time we are detecting a standard tap\n   * that gives priority to nested views.\n   *\n   * - If a single tap on the scroll view triggers an action such as\n   *   recentering a map style view yet wants to give priority to interaction\n   *   views inside (such as dropped pins or labels), then we would return true\n   *   from this method when there is a single touch.\n   *\n   * - Similar to the previous case, if a two finger \"tap\" should trigger a\n   *   zoom, we would check the `touches` count, and if `>= 2`, we would return\n   *   true.\n   *\n   */\n  _handleStartShouldSetResponder: (e: PressEvent) => boolean = (\n    e: PressEvent,\n  ) => {\n    // Allow any event touch pass through if the default pan responder is disabled\n    if (this.props.disableScrollViewPanResponder === true) {\n      return false;\n    }\n\n    const currentlyFocusedInput = TextInputState.currentlyFocusedInput();\n    if (\n      this.props.keyboardShouldPersistTaps === 'handled' &&\n      this._keyboardIsDismissible() &&\n      e.target !== currentlyFocusedInput\n    ) {\n      return true;\n    }\n    return false;\n  };\n\n  /**\n   * There are times when the scroll view wants to become the responder\n   * (meaning respond to the next immediate `touchStart/touchEnd`), in a way\n   * that *doesn't* give priority to nested views (hence the capture phase):\n   *\n   * - Currently animating.\n   * - Tapping anywhere that is not a text input, while the keyboard is\n   *   up (which should dismiss the keyboard).\n   *\n   * Invoke this from an `onStartShouldSetResponderCapture` event.\n   */\n  _handleStartShouldSetResponderCapture: (e: PressEvent) => boolean = (\n    e: PressEvent,\n  ) => {\n    // The scroll view should receive taps instead of its descendants if:\n    // * it is already animating/decelerating\n    if (this._isAnimating()) {\n      return true;\n    }\n\n    // Allow any event touch pass through if the default pan responder is disabled\n    if (this.props.disableScrollViewPanResponder === true) {\n      return false;\n    }\n\n    // * the keyboard is up, keyboardShouldPersistTaps is 'never' (the default),\n    // and a new touch starts with a non-textinput target (in which case the\n    // first tap should be sent to the scroll view and dismiss the keyboard,\n    // then the second tap goes to the actual interior view)\n    const {keyboardShouldPersistTaps} = this.props;\n    const keyboardNeverPersistTaps =\n      !keyboardShouldPersistTaps || keyboardShouldPersistTaps === 'never';\n\n    if (typeof e.target === 'number') {\n      if (__DEV__) {\n        console.error(\n          'Did not expect event target to be a number. Should have been a native component',\n        );\n      }\n\n      return false;\n    }\n\n    // Let presses through if the soft keyboard is detached from the viewport\n    if (this._softKeyboardIsDetached()) {\n      return false;\n    }\n\n    if (\n      keyboardNeverPersistTaps &&\n      this._keyboardIsDismissible() &&\n      e.target != null &&\n      // $FlowFixMe[incompatible-call]\n      !TextInputState.isTextInput(e.target)\n    ) {\n      return true;\n    }\n\n    return false;\n  };\n\n  /**\n   * Do we consider there to be a dismissible soft-keyboard open?\n   */\n  _keyboardIsDismissible: () => boolean = () => {\n    const currentlyFocusedInput = TextInputState.currentlyFocusedInput();\n\n    // We cannot dismiss the keyboard without an input to blur, even if a soft\n    // keyboard is open (e.g. when keyboard is open due to a native component\n    // not participating in TextInputState). It's also possible that the\n    // currently focused input isn't a TextInput (such as by calling ref.focus\n    // on a non-TextInput).\n    const hasFocusedTextInput =\n      currentlyFocusedInput != null &&\n      TextInputState.isTextInput(currentlyFocusedInput);\n\n    // Even if an input is focused, we may not have a keyboard to dismiss. E.g\n    // when using a physical keyboard. Ensure we have an event for an opened\n    // keyboard.\n    const softKeyboardMayBeOpen =\n      this._keyboardMetrics != null || this._keyboardEventsAreUnreliable();\n\n    return hasFocusedTextInput && softKeyboardMayBeOpen;\n  };\n\n  /**\n   * Whether an open soft keyboard is present which does not overlap the\n   * viewport. E.g. for a VR soft-keyboard which is detached from the app\n   * viewport.\n   */\n  _softKeyboardIsDetached: () => boolean = () => {\n    return this._keyboardMetrics != null && this._keyboardMetrics.height === 0;\n  };\n\n  _keyboardEventsAreUnreliable: () => boolean = () => {\n    // Android versions prior to API 30 rely on observing layout changes when\n    // `android:windowSoftInputMode` is set to `adjustResize` or `adjustPan`.\n    return Platform.OS === 'android' && Platform.Version < 30;\n  };\n\n  /**\n   * Invoke this from an `onTouchEnd` event.\n   *\n   * @param {PressEvent} e Event.\n   */\n  _handleTouchEnd: (e: PressEvent) => void = (e: PressEvent) => {\n    const nativeEvent = e.nativeEvent;\n    this._isTouching = nativeEvent.touches.length !== 0;\n\n    const {keyboardShouldPersistTaps} = this.props;\n    const keyboardNeverPersistsTaps =\n      !keyboardShouldPersistTaps || keyboardShouldPersistTaps === 'never';\n\n    // Dismiss the keyboard now if we didn't become responder in capture phase\n    // to eat presses, but still want to dismiss on interaction.\n    // Don't do anything if the target of the touch event is the current input.\n    const currentlyFocusedTextInput = TextInputState.currentlyFocusedInput();\n    if (\n      currentlyFocusedTextInput != null &&\n      e.target !== currentlyFocusedTextInput &&\n      this._softKeyboardIsDetached() &&\n      this._keyboardIsDismissible() &&\n      keyboardNeverPersistsTaps\n    ) {\n      TextInputState.blurTextInput(currentlyFocusedTextInput);\n    }\n\n    this.props.onTouchEnd && this.props.onTouchEnd(e);\n  };\n\n  /**\n   * Invoke this from an `onTouchCancel` event.\n   *\n   * @param {PressEvent} e Event.\n   */\n  _handleTouchCancel: (e: PressEvent) => void = (e: PressEvent) => {\n    this._isTouching = false;\n    this.props.onTouchCancel && this.props.onTouchCancel(e);\n  };\n\n  /**\n   * Invoke this from an `onTouchStart` event.\n   *\n   * Since we know that the `SimpleEventPlugin` occurs later in the plugin\n   * order, after `ResponderEventPlugin`, we can detect that we were *not*\n   * permitted to be the responder (presumably because a contained view became\n   * responder). The `onResponderReject` won't fire in that case - it only\n   * fires when a *current* responder rejects our request.\n   *\n   * @param {PressEvent} e Touch Start event.\n   */\n  _handleTouchStart: (e: PressEvent) => void = (e: PressEvent) => {\n    this._isTouching = true;\n    this.props.onTouchStart && this.props.onTouchStart(e);\n  };\n\n  /**\n   * Invoke this from an `onTouchMove` event.\n   *\n   * Since we know that the `SimpleEventPlugin` occurs later in the plugin\n   * order, after `ResponderEventPlugin`, we can detect that we were *not*\n   * permitted to be the responder (presumably because a contained view became\n   * responder). The `onResponderReject` won't fire in that case - it only\n   * fires when a *current* responder rejects our request.\n   *\n   * @param {PressEvent} e Touch Start event.\n   */\n  _handleTouchMove: (e: PressEvent) => void = (e: PressEvent) => {\n    this.props.onTouchMove && this.props.onTouchMove(e);\n  };\n\n  render(): React.Node {\n    const horizontal = this.props.horizontal === true;\n\n    const NativeScrollView = horizontal\n      ? HScrollViewNativeComponent\n      : VScrollViewNativeComponent;\n\n    const NativeScrollContentView = horizontal\n      ? HScrollContentViewNativeComponent\n      : VScrollContentViewNativeComponent;\n\n    const contentContainerStyle = [\n      horizontal && styles.contentContainerHorizontal,\n      this.props.contentContainerStyle,\n    ];\n    if (__DEV__ && this.props.style !== undefined) {\n      // $FlowFixMe[underconstrained-implicit-instantiation]\n      const style = flattenStyle(this.props.style);\n      const childLayoutProps = ['alignItems', 'justifyContent'].filter(\n        // $FlowFixMe[incompatible-use]\n        prop => style && style[prop] !== undefined,\n      );\n      invariant(\n        childLayoutProps.length === 0,\n        'ScrollView child layout (' +\n          JSON.stringify(childLayoutProps) +\n          ') must be applied through the contentContainerStyle prop.',\n      );\n    }\n\n    const contentSizeChangeProps =\n      this.props.onContentSizeChange == null\n        ? null\n        : {\n            onLayout: this._handleContentOnLayout,\n          };\n\n    const {stickyHeaderIndices} = this.props;\n    let children = this.props.children;\n    /**\n     * This function can cause unnecessary remount when nested in conditionals as it causes remap of children keys.\n     * https://react.dev/reference/react/Children#children-toarray-caveats\n     */\n    children = React.Children.toArray<$FlowFixMe>(children);\n\n    if (stickyHeaderIndices != null && stickyHeaderIndices.length > 0) {\n      children = children.map((child, index) => {\n        const indexOfIndex = child ? stickyHeaderIndices.indexOf(index) : -1;\n        if (indexOfIndex > -1) {\n          const key = child.key;\n          const nextIndex = stickyHeaderIndices[indexOfIndex + 1];\n          const StickyHeaderComponent =\n            this.props.StickyHeaderComponent || ScrollViewStickyHeader;\n          return (\n            <StickyHeaderComponent\n              key={key}\n              ref={ref => this._setStickyHeaderRef(key, ref)}\n              nextHeaderLayoutY={this._headerLayoutYs.get(\n                this._getKeyForIndex(nextIndex, children),\n              )}\n              onLayout={event => this._onStickyHeaderLayout(index, event, key)}\n              scrollAnimatedValue={this._scrollAnimatedValue}\n              inverted={this.props.invertStickyHeaders}\n              hiddenOnScroll={this.props.stickyHeaderHiddenOnScroll}\n              scrollViewHeight={this.state.layoutHeight}>\n              {child}\n            </StickyHeaderComponent>\n          );\n        } else {\n          return child;\n        }\n      });\n    }\n    children = (\n      <ScrollViewContext.Provider value={horizontal ? HORIZONTAL : VERTICAL}>\n        {children}\n      </ScrollViewContext.Provider>\n    );\n\n    const hasStickyHeaders =\n      Array.isArray(stickyHeaderIndices) && stickyHeaderIndices.length > 0;\n\n    // Some ScrollView native component behaviors rely on using the metrics\n    // of mounted views for anchoring. Make sure not to flatten children if\n    // this is the case.\n    const preserveChildren =\n      this.props.maintainVisibleContentPosition != null ||\n      (Platform.OS === 'android' && this.props.snapToAlignment != null);\n\n    const contentContainer = (\n      <NativeScrollContentView\n        {...contentSizeChangeProps}\n        ref={this._innerView.getForwardingRef(this.props.innerViewRef)}\n        style={contentContainerStyle}\n        removeClippedSubviews={\n          // Subview clipping causes issues with sticky headers on Android and\n          // would be hard to fix properly in a performant way.\n          Platform.OS === 'android' && hasStickyHeaders\n            ? false\n            : this.props.removeClippedSubviews\n        }\n        collapsable={false}\n        collapsableChildren={!preserveChildren}>\n        {children}\n      </NativeScrollContentView>\n    );\n\n    const alwaysBounceHorizontal =\n      this.props.alwaysBounceHorizontal !== undefined\n        ? this.props.alwaysBounceHorizontal\n        : this.props.horizontal;\n\n    const alwaysBounceVertical =\n      this.props.alwaysBounceVertical !== undefined\n        ? this.props.alwaysBounceVertical\n        : !this.props.horizontal;\n\n    const baseStyle = horizontal ? styles.baseHorizontal : styles.baseVertical;\n\n    const {experimental_endDraggingSensitivityMultiplier, ...otherProps} =\n      this.props;\n    const props = {\n      ...otherProps,\n      alwaysBounceHorizontal,\n      alwaysBounceVertical,\n      style: StyleSheet.compose(baseStyle, this.props.style),\n      // Override the onContentSizeChange from props, since this event can\n      // bubble up from TextInputs\n      onContentSizeChange: null,\n      onLayout: this._handleLayout,\n      onMomentumScrollBegin: this._handleMomentumScrollBegin,\n      onMomentumScrollEnd: this._handleMomentumScrollEnd,\n      onResponderGrant: this._handleResponderGrant,\n      onResponderReject: this._handleResponderReject,\n      onResponderRelease: this._handleResponderRelease,\n      onResponderTerminationRequest: this._handleResponderTerminationRequest,\n      onScrollBeginDrag: this._handleScrollBeginDrag,\n      onScrollEndDrag: this._handleScrollEndDrag,\n      onScrollShouldSetResponder: this._handleScrollShouldSetResponder,\n      onStartShouldSetResponder: this._handleStartShouldSetResponder,\n      onStartShouldSetResponderCapture:\n        this._handleStartShouldSetResponderCapture,\n      onTouchEnd: this._handleTouchEnd,\n      onTouchMove: this._handleTouchMove,\n      onTouchStart: this._handleTouchStart,\n      onTouchCancel: this._handleTouchCancel,\n      onScroll: this._handleScroll,\n      endDraggingSensitivityMultiplier:\n        experimental_endDraggingSensitivityMultiplier,\n      scrollEventThrottle: hasStickyHeaders\n        ? 1\n        : this.props.scrollEventThrottle,\n      sendMomentumEvents:\n        this.props.onMomentumScrollBegin || this.props.onMomentumScrollEnd\n          ? true\n          : false,\n      // default to true\n      snapToStart: this.props.snapToStart !== false,\n      // default to true\n      snapToEnd: this.props.snapToEnd !== false,\n      // pagingEnabled is overridden by snapToInterval / snapToOffsets\n      pagingEnabled: Platform.select({\n        // on iOS, pagingEnabled must be set to false to have snapToInterval / snapToOffsets work\n        ios:\n          this.props.pagingEnabled === true &&\n          this.props.snapToInterval == null &&\n          this.props.snapToOffsets == null,\n        // on Android, pagingEnabled must be set to true to have snapToInterval / snapToOffsets work\n        android:\n          this.props.pagingEnabled === true ||\n          this.props.snapToInterval != null ||\n          this.props.snapToOffsets != null,\n      }),\n    };\n\n    const {decelerationRate} = this.props;\n    if (decelerationRate != null) {\n      props.decelerationRate = processDecelerationRate(decelerationRate);\n    }\n\n    const refreshControl = this.props.refreshControl;\n    const scrollViewRef = this._scrollView.getForwardingRef(\n      this.props.scrollViewRef,\n    );\n\n    if (refreshControl) {\n      if (Platform.OS === 'ios') {\n        // On iOS the RefreshControl is a child of the ScrollView.\n        return (\n          // $FlowFixMe[incompatible-type] - Flow only knows element refs.\n          <NativeScrollView {...props} ref={scrollViewRef}>\n            {refreshControl}\n            {contentContainer}\n          </NativeScrollView>\n        );\n      } else if (Platform.OS === 'android') {\n        // On Android wrap the ScrollView with a AndroidSwipeRefreshLayout.\n        // Since the ScrollView is wrapped add the style props to the\n        // AndroidSwipeRefreshLayout and use flex: 1 for the ScrollView.\n        // Note: we should split props.style on the inner and outer props\n        // however, the ScrollView still needs the baseStyle to be scrollable\n        // $FlowFixMe[underconstrained-implicit-instantiation]\n        // $FlowFixMe[incompatible-call]\n        const {outer, inner} = splitLayoutProps(flattenStyle(props.style));\n        return React.cloneElement(\n          refreshControl,\n          {style: StyleSheet.compose(baseStyle, outer)},\n          <NativeScrollView\n            {...props}\n            style={StyleSheet.compose(baseStyle, inner)}\n            // $FlowFixMe[incompatible-type] - Flow only knows element refs.\n            ref={scrollViewRef}>\n            {contentContainer}\n          </NativeScrollView>,\n        );\n      }\n    }\n    return (\n      // $FlowFixMe[incompatible-type] - Flow only knows element refs.\n      <NativeScrollView {...props} ref={scrollViewRef}>\n        {contentContainer}\n      </NativeScrollView>\n    );\n  }\n}\n\nconst styles = StyleSheet.create({\n  baseVertical: {\n    flexGrow: 1,\n    flexShrink: 1,\n    flexDirection: 'column',\n    overflow: 'scroll',\n  },\n  baseHorizontal: {\n    flexGrow: 1,\n    flexShrink: 1,\n    flexDirection: 'row',\n    overflow: 'scroll',\n  },\n  contentContainerHorizontal: {\n    flexDirection: 'row',\n  },\n});\n\ntype RefForwarder<TNativeInstance, TPublicInstance> = {\n  getForwardingRef: (\n    ?React.RefSetter<TPublicInstance>,\n  ) => (TNativeInstance | null) => void,\n  nativeInstance: TNativeInstance | null,\n  publicInstance: TPublicInstance | null,\n};\n\n/**\n * Helper function that should be replaced with `useCallback` and `useMergeRefs`\n * once `ScrollView` is reimplemented as a functional component.\n */\nfunction createRefForwarder<TNativeInstance, TPublicInstance>(\n  mutator: TNativeInstance => TPublicInstance,\n): RefForwarder<TNativeInstance, TPublicInstance> {\n  const state: RefForwarder<TNativeInstance, TPublicInstance> = {\n    getForwardingRef: memoize(forwardedRef => {\n      return (nativeInstance: TNativeInstance | null): void => {\n        const publicInstance =\n          nativeInstance == null ? null : mutator(nativeInstance);\n\n        state.nativeInstance = nativeInstance;\n        state.publicInstance = publicInstance;\n\n        if (forwardedRef != null) {\n          if (typeof forwardedRef === 'function') {\n            forwardedRef(publicInstance);\n          } else {\n            forwardedRef.current = publicInstance;\n          }\n        }\n      };\n    }),\n    nativeInstance: null,\n    publicInstance: null,\n  };\n\n  return state;\n}\n\n// TODO: After upgrading to React 19, remove `forwardRef` from this component.\n// NOTE: This wrapper component is necessary because `ScrollView` is a class\n// component and we need to map `ref` to a differently named prop. This can be\n// removed when `ScrollView` is a functional component.\nconst Wrapper: component(\n  ref: React.RefSetter<PublicScrollViewInstance>,\n  ...props: Props\n) = React.forwardRef(function Wrapper(\n  props: Props,\n  ref: ?React.RefSetter<PublicScrollViewInstance>,\n): React.Node {\n  return ref == null ? (\n    <ScrollView {...props} />\n  ) : (\n    <ScrollView {...props} scrollViewRef={ref} />\n  );\n});\nWrapper.displayName = 'ScrollView';\n// $FlowExpectedError[prop-missing]\nWrapper.Context = ScrollViewContext;\n\nmodule.exports = ((Wrapper: $FlowFixMe): typeof Wrapper &\n  ScrollViewComponentStatics);\n"], "mappings": ";;;;;;;AA0BA,IAAAA,4BAAA,GAAAC,OAAA;AAIA,IAAAC,4BAAA,GAAAD,OAAA;AAIA,IAAAE,uBAAA,GAAAC,sBAAA,CAAAH,OAAA;AACA,IAAAI,gBAAA,GAAAD,sBAAA,CAAAH,OAAA;AACA,IAAAK,cAAA,GAAAL,OAAA;AACA,IAAAM,UAAA,GAAAH,sBAAA,CAAAH,OAAA;AACA,IAAAO,aAAA,GAAAJ,sBAAA,CAAAH,OAAA;AACA,IAAAQ,kBAAA,GAAAL,sBAAA,CAAAH,OAAA;AACA,IAAAS,WAAA,GAAAN,sBAAA,CAAAH,OAAA;AACA,IAAAU,WAAA,GAAAP,sBAAA,CAAAH,OAAA;AACA,IAAAW,gBAAA,GAAAR,sBAAA,CAAAH,OAAA;AACA,IAAAY,SAAA,GAAAT,sBAAA,CAAAH,OAAA;AACA,IAAAa,SAAA,GAAAV,sBAAA,CAAAH,OAAA;AACA,IAAAc,eAAA,GAAAX,sBAAA,CAAAH,OAAA;AACA,IAAAe,wBAAA,GAAAZ,sBAAA,CAAAH,OAAA;AACA,IAAAgB,mBAAA,GAAAb,sBAAA,CAAAH,OAAA;AACA,IAAAiB,kBAAA,GAAAC,uBAAA,CAAAlB,OAAA;AACA,IAAAmB,uBAAA,GAAAhB,sBAAA,CAAAH,OAAA;AACA,IAAAoB,UAAA,GAAAjB,sBAAA,CAAAH,OAAA;AACA,IAAAqB,WAAA,GAAAlB,sBAAA,CAAAH,OAAA;AACA,IAAAsB,WAAA,GAAAnB,sBAAA,CAAAH,OAAA;AACA,IAAAuB,KAAA,GAAAL,uBAAA,CAAAlB,OAAA;AAA+B,IAAAwB,WAAA,GAAAxB,OAAA;AAAA,IAAAyB,SAAA;AAAA,SAAAC,yBAAAC,CAAA,6BAAAC,OAAA,mBAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,yBAAAC,CAAA,WAAAA,CAAA,GAAAG,CAAA,GAAAD,CAAA,KAAAF,CAAA;AAAA,SAAAT,wBAAAS,CAAA,EAAAE,CAAA,SAAAA,CAAA,IAAAF,CAAA,IAAAA,CAAA,CAAAI,UAAA,SAAAJ,CAAA,eAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,WAAAK,OAAA,EAAAL,CAAA,QAAAG,CAAA,GAAAJ,wBAAA,CAAAG,CAAA,OAAAC,CAAA,IAAAA,CAAA,CAAAG,GAAA,CAAAN,CAAA,UAAAG,CAAA,CAAAI,GAAA,CAAAP,CAAA,OAAAQ,CAAA,KAAAC,SAAA,UAAAC,CAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,CAAA,IAAAd,CAAA,oBAAAc,CAAA,OAAAC,cAAA,CAAAC,IAAA,CAAAhB,CAAA,EAAAc,CAAA,SAAAG,CAAA,GAAAP,CAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAb,CAAA,EAAAc,CAAA,UAAAG,CAAA,KAAAA,CAAA,CAAAV,GAAA,IAAAU,CAAA,CAAAC,GAAA,IAAAP,MAAA,CAAAC,cAAA,CAAAJ,CAAA,EAAAM,CAAA,EAAAG,CAAA,IAAAT,CAAA,CAAAM,CAAA,IAAAd,CAAA,CAAAc,CAAA,YAAAN,CAAA,CAAAH,OAAA,GAAAL,CAAA,EAAAG,CAAA,IAAAA,CAAA,CAAAe,GAAA,CAAAlB,CAAA,EAAAQ,CAAA,GAAAA,CAAA;AAAA,SAAAW,WAAAhB,CAAA,EAAAiB,CAAA,EAAApB,CAAA,WAAAoB,CAAA,OAAAC,gBAAA,CAAAhB,OAAA,EAAAe,CAAA,OAAAE,2BAAA,CAAAjB,OAAA,EAAAF,CAAA,EAAAoB,yBAAA,KAAAC,OAAA,CAAAC,SAAA,CAAAL,CAAA,EAAApB,CAAA,YAAAqB,gBAAA,CAAAhB,OAAA,EAAAF,CAAA,EAAAuB,WAAA,IAAAN,CAAA,CAAAO,KAAA,CAAAxB,CAAA,EAAAH,CAAA;AAAA,SAAAuB,0BAAA,cAAApB,CAAA,IAAAyB,OAAA,CAAAC,SAAA,CAAAC,OAAA,CAAAd,IAAA,CAAAQ,OAAA,CAAAC,SAAA,CAAAG,OAAA,iCAAAzB,CAAA,aAAAoB,yBAAA,YAAAA,0BAAA,aAAApB,CAAA;AA8lB/B,IAAM4B,qCAAqC,GAAG,EAAE;AAAC,IAyC3CC,UAAU,aAAAC,gBAAA;EAGd,SAAAD,WAAYE,KAAY,EAAE;IAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,qBAAA,EAAAC,sBAAA;IAAA,IAAAC,KAAA;IAAA,IAAAC,gBAAA,CAAAnC,OAAA,QAAA2B,UAAA;IACxBO,KAAA,GAAApB,UAAA,OAAAa,UAAA,GAAME,KAAK;IAAEK,KAAA,CASfE,8BAA8B,GAA+B,IAAI;IAAAF,KAAA,CACjEG,iBAAiB,GACf,IAAIC,GAAG,CAAC,CAAC;IAAAJ,KAAA,CACXK,eAAe,GAAwB,IAAID,GAAG,CAAC,CAAC;IAAAJ,KAAA,CAEhDM,gBAAgB,GAAqB,IAAI;IAAAN,KAAA,CACzCO,uBAAuB,GAAW,CAAC;IAAAP,KAAA,CACnCQ,WAAW,GAAY,KAAK;IAAAR,KAAA,CAC5BS,4BAA4B,GAAW,CAAC;IAAAT,KAAA,CACxCU,0BAA0B,GAAW,CAAC;IAAAV,KAAA,CAOtCW,qCAAqC,GAAY,KAAK;IAAAX,KAAA,CACtDY,8BAA8B,GAAY,KAAK;IAAAZ,KAAA,CAC/Ca,4BAA4B,GAAa,IAAI;IAAAb,KAAA,CAE7Cc,SAAS,GAAa,IAAI;IAAAd,KAAA,CAE1Be,6BAA6B,GAAuB,IAAI;IAAAf,KAAA,CACxDgB,6BAA6B,GAAuB,IAAI;IAAAhB,KAAA,CACxDiB,4BAA4B,GAAuB,IAAI;IAAAjB,KAAA,CACvDkB,4BAA4B,GAAuB,IAAI;IAAAlB,KAAA,CAEvDmB,KAAK,GAAU;MACbC,YAAY,EAAE;IAChB,CAAC;IAAApB,KAAA,CA4EDqB,kBAAkB,GAA8B,YAAM;MAEpD,OAAArB,KAAA;IACF,CAAC;IAAAA,KAAA,CAEDsB,iBAAiB,GAAkB,YAAM;MACvC,OAAO,IAAAC,6BAAc,EAACvB,KAAA,CAAKwB,kBAAkB,CAAC,CAAC,CAAC;IAClD,CAAC;IAAAxB,KAAA,CAEDyB,gBAAgB,GAAkB,YAAM;MACtC,OAAO,IAAAF,6BAAc,EAACvB,KAAA,CAAK0B,UAAU,CAACC,cAAc,CAAC;IACvD,CAAC;IAAA3B,KAAA,CAED4B,eAAe,GAAmC,YAAM;MACtD,OAAO5B,KAAA,CAAK0B,UAAU,CAACC,cAAc;IACvC,CAAC;IAAA3B,KAAA,CAEDwB,kBAAkB,GAA8B,YAAM;MACpD,OAAOxB,KAAA,CAAK6B,WAAW,CAACF,cAAc;IACxC,CAAC;IAAA3B,KAAA,CAaD8B,QAAQ,GAWI,UACVC,OAOU,EACVC,WAAoB,EACpBC,kBAA4B,EACzB;MACH,IAAIC,CAAC,EAAEC,CAAC,EAAEC,QAAQ;MAClB,IAAI,OAAOL,OAAO,KAAK,QAAQ,EAAE;QAC/BM,OAAO,CAACC,IAAI,CACV,uEAAuE,GACrE,4BACJ,CAAC;QACDH,CAAC,GAAGJ,OAAO;QACXG,CAAC,GAAGF,WAAW;QACfI,QAAQ,GAAGH,kBAAkB;MAC/B,CAAC,MAAM,IAAIF,OAAO,EAAE;QAClBI,CAAC,GAAGJ,OAAO,CAACI,CAAC;QACbD,CAAC,GAAGH,OAAO,CAACG,CAAC;QACbE,QAAQ,GAAGL,OAAO,CAACK,QAAQ;MAC7B;MACA,IAAMG,SAAS,GAAGvC,KAAA,CAAKwB,kBAAkB,CAAC,CAAC;MAC3C,IAAIe,SAAS,IAAI,IAAI,EAAE;QACrB;MACF;MACAC,2BAAQ,CAACV,QAAQ,CAACS,SAAS,EAAEL,CAAC,IAAI,CAAC,EAAEC,CAAC,IAAI,CAAC,EAAEC,QAAQ,KAAK,KAAK,CAAC;IAClE,CAAC;IAAApC,KAAA,CAUDyC,WAAW,GAAmD,UAC5DV,OAAoC,EACjC;MAEH,IAAMK,QAAQ,GAAG,CAACL,OAAO,IAAIA,OAAO,CAACK,QAAQ,MAAM,KAAK;MACxD,IAAMG,SAAS,GAAGvC,KAAA,CAAKwB,kBAAkB,CAAC,CAAC;MAC3C,IAAIe,SAAS,IAAI,IAAI,EAAE;QACrB;MACF;MACAC,2BAAQ,CAACC,WAAW,CAACF,SAAS,EAAEH,QAAQ,CAAC;IAC3C,CAAC;IAAApC,KAAA,CAOD0C,qBAAqB,GAAe,YAAM;MACxC,IAAMH,SAAS,GAAGvC,KAAA,CAAKwB,kBAAkB,CAAC,CAAC;MAC3C,IAAIe,SAAS,IAAI,IAAI,EAAE;QACrB;MACF;MACAC,2BAAQ,CAACE,qBAAqB,CAACH,SAAS,CAAC;IAC3C,CAAC;IAAAvC,KAAA,CAYD2C,2CAA2C,GAI/B,UACVC,UAAiC,EACjCC,gBAAyB,EACzBC,2BAAqC,EAClC;MACH9C,KAAA,CAAKO,uBAAuB,GAAGsC,gBAAgB,IAAI,CAAC;MACpD7C,KAAA,CAAKa,4BAA4B,GAAG,CAAC,CAACiC,2BAA2B;MAEjE,IAAI9C,KAAA,CAAK0B,UAAU,CAACC,cAAc,IAAI,IAAI,EAAE;QAC1C;MACF;MAEA,IAAI,OAAOiB,UAAU,KAAK,QAAQ,EAAE;QAClCG,kBAAS,CAACC,aAAa,CACrBJ,UAAU,EACV,IAAAK,mBAAU,EAAC,IAAA1B,6BAAc,EAAAvB,KAAK,CAAC,CAAC,EAEhCA,KAAA,CAAKkD,oBAAoB,EACzBlD,KAAA,CAAKmD,gCACP,CAAC;MACH,CAAC,MAAM;QACLP,UAAU,CAACI,aAAa,CACtBhD,KAAA,CAAK0B,UAAU,CAACC,cAAc,EAC9B3B,KAAA,CAAKmD,gCAAgC,EAErCnD,KAAA,CAAKkD,oBACP,CAAC;MACH;IACF,CAAC;IAAAlD,KAAA,CAQDoD,qBAAqB,GAST,UACVC,IAME,EACFjB,QAAkB,EACf;MACH,IAAAkB,kBAAS,EAACC,iBAAQ,CAACC,EAAE,KAAK,KAAK,EAAE,+BAA+B,CAAC;MACjE,IAAI,UAAU,IAAIH,IAAI,EAAE;QACtBrD,KAAA,CAAKc,SAAS,GAAGuC,IAAI,CAACjB,QAAQ;QAC9B,OAAOiB,IAAI,CAACjB,QAAQ;MACtB,CAAC,MAAM,IAAI,OAAOA,QAAQ,KAAK,WAAW,EAAE;QAC1CC,OAAO,CAACC,IAAI,CACV,2FACF,CAAC;MACH;MAEA,IAAMC,SAAS,GAAGvC,KAAA,CAAKwB,kBAAkB,CAAC,CAAC;MAC3C,IAAIe,SAAS,IAAI,IAAI,EAAE;QACrB;MACF;MACAC,2BAAQ,CAACiB,UAAU,CAAClB,SAAS,EAAEc,IAAI,EAAEjB,QAAQ,KAAK,KAAK,CAAC;IAC1D,CAAC;IAAApC,KAAA,CAgBDmD,gCAAgC,GAKpB,UAACO,IAAY,EAAEC,GAAW,EAAEC,KAAa,EAAEC,MAAc,EAAK;MACxE,IAAIC,eAAe,GAAGC,mBAAU,CAAC/F,GAAG,CAAC,QAAQ,CAAC,CAAC6F,MAAM;MAErD,IAAMG,8BAA8B,GAAG,SAAjCA,8BAA8BA,CAAA,EAAS;QAC3C,IAAIhE,KAAA,CAAKM,gBAAgB,IAAI,IAAI,EAAE;UACjCwD,eAAe,GAAG9D,KAAA,CAAKM,gBAAgB,CAAC2D,OAAO;QACjD;QACA,IAAIC,aAAa,GACfP,GAAG,GAAGG,eAAe,GAAGD,MAAM,GAAG7D,KAAA,CAAKO,uBAAuB;QAM/D,IAAIP,KAAA,CAAKa,4BAA4B,KAAK,IAAI,EAAE;UAC9CqD,aAAa,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEF,aAAa,CAAC;QAC5C;QACAlE,KAAA,CAAK8B,QAAQ,CAAC;UAACI,CAAC,EAAE,CAAC;UAAEC,CAAC,EAAE+B,aAAa;UAAE9B,QAAQ,EAAE;QAAI,CAAC,CAAC;QAEvDpC,KAAA,CAAKO,uBAAuB,GAAG,CAAC;QAChCP,KAAA,CAAKa,4BAA4B,GAAG,KAAK;MAC3C,CAAC;MAED,IAAIb,KAAA,CAAKM,gBAAgB,IAAI,IAAI,EAAE;QAKjC+D,UAAU,CAAC,YAAM;UACfL,8BAA8B,CAAC,CAAC;QAClC,CAAC,EAAE,CAAC,CAAC;MACP,CAAC,MAAM;QACLA,8BAA8B,CAAC,CAAC;MAClC;IACF,CAAC;IAAAhE,KAAA,CA6DDsE,aAAa,GAAG,UAAC7G,CAAc,EAAK;MAClCuC,KAAA,CAAKW,qCAAqC,GAAG,IAAI;MACjDX,KAAA,CAAKL,KAAK,CAAC4E,QAAQ,IAAIvE,KAAA,CAAKL,KAAK,CAAC4E,QAAQ,CAAC9G,CAAC,CAAC;IAC/C,CAAC;IAAAuC,KAAA,CAEDwE,aAAa,GAAG,UAAC/G,CAAc,EAAK;MAClC,IAAIuC,KAAA,CAAKL,KAAK,CAAC8E,mBAAmB,KAAK,IAAI,EAAE;QAC3CzE,KAAA,CAAK0E,QAAQ,CAAC;UAACtD,YAAY,EAAE3D,CAAC,CAACkH,WAAW,CAACC,MAAM,CAACf;QAAM,CAAC,CAAC;MAC5D;MACA,IAAI7D,KAAA,CAAKL,KAAK,CAACkF,QAAQ,EAAE;QACvB7E,KAAA,CAAKL,KAAK,CAACkF,QAAQ,CAACpH,CAAC,CAAC;MACxB;IACF,CAAC;IAAAuC,KAAA,CAED8E,sBAAsB,GAAG,UAACrH,CAAc,EAAK;MAC3C,IAAAsH,qBAAA,GAAwBtH,CAAC,CAACkH,WAAW,CAACC,MAAM;QAArChB,KAAK,GAAAmB,qBAAA,CAALnB,KAAK;QAAEC,MAAM,GAAAkB,qBAAA,CAANlB,MAAM;MACpB7D,KAAA,CAAKL,KAAK,CAACqF,mBAAmB,IAC5BhF,KAAA,CAAKL,KAAK,CAACqF,mBAAmB,CAACpB,KAAK,EAAEC,MAAM,CAAC;IACjD,CAAC;IAAA7D,KAAA,CAED0B,UAAU,GACRuD,kBAAkB,CAChB,UAACC,QAA2B;MAAA,OAAwBA,QAAQ;IAAA,CAC9D,CAAC;IAAAlF,KAAA,CAEH6B,WAAW,GACToD,kBAAkB,CAAC,UAAAtD,cAAc,EAAI;MAUnC,IAAMwD,cAAwC,GAAG/G,MAAM,CAACgH,MAAM,CAC5DzD,cAAc,EACd;QACEN,kBAAkB,EAAErB,KAAA,CAAKqB,kBAAkB;QAC3CC,iBAAiB,EAAEtB,KAAA,CAAKsB,iBAAiB;QACzCG,gBAAgB,EAAEzB,KAAA,CAAKyB,gBAAgB;QACvCG,eAAe,EAAE5B,KAAA,CAAK4B,eAAe;QACrCJ,kBAAkB,EAAExB,KAAA,CAAKwB,kBAAkB;QAC3CM,QAAQ,EAAE9B,KAAA,CAAK8B,QAAQ;QACvBW,WAAW,EAAEzC,KAAA,CAAKyC,WAAW;QAC7BC,qBAAqB,EAAE1C,KAAA,CAAK0C,qBAAqB;QACjDU,qBAAqB,EAAEpD,KAAA,CAAKoD,qBAAqB;QACjDT,2CAA2C,EACzC3C,KAAA,CAAK2C;MACT,CACF,CAAC;MAED,OAAOwC,cAAc;IACvB,CAAC,CAAC;IAAAnF,KAAA,CA+BJqF,+BAA+B,GAA+B,UAC5D5H,CAAgB,EACb;MACHuC,KAAA,CAAKM,gBAAgB,GAAG7C,CAAC,CAAC6H,cAAc;MACxCtF,KAAA,CAAKL,KAAK,CAAC4F,kBAAkB,IAAIvF,KAAA,CAAKL,KAAK,CAAC4F,kBAAkB,CAAC9H,CAAC,CAAC;IACnE,CAAC;IAAAuC,KAAA,CAEDwF,+BAA+B,GAA+B,UAC5D/H,CAAgB,EACb;MACHuC,KAAA,CAAKM,gBAAgB,GAAG,IAAI;MAC5BN,KAAA,CAAKL,KAAK,CAAC8F,kBAAkB,IAAIzF,KAAA,CAAKL,KAAK,CAAC8F,kBAAkB,CAAChI,CAAC,CAAC;IACnE,CAAC;IAAAuC,KAAA,CAED0F,8BAA8B,GAA+B,UAC3DjI,CAAgB,EACb;MACHuC,KAAA,CAAKM,gBAAgB,GAAG7C,CAAC,CAAC6H,cAAc;MACxCtF,KAAA,CAAKL,KAAK,CAACgG,iBAAiB,IAAI3F,KAAA,CAAKL,KAAK,CAACgG,iBAAiB,CAAClI,CAAC,CAAC;IACjE,CAAC;IAAAuC,KAAA,CAED4F,8BAA8B,GAA+B,UAC3DnI,CAAgB,EACb;MACHuC,KAAA,CAAKM,gBAAgB,GAAG,IAAI;MAC5BN,KAAA,CAAKL,KAAK,CAACkG,iBAAiB,IAAI7F,KAAA,CAAKL,KAAK,CAACkG,iBAAiB,CAACpI,CAAC,CAAC;IACjE,CAAC;IAAAuC,KAAA,CAKD8F,0BAA0B,GAA6B,UAACrI,CAAc,EAAK;MACzEuC,KAAA,CAAKS,4BAA4B,GAAGsF,MAAM,CAACC,WAAW,CAACC,GAAG,CAAC,CAAC;MAC5DjG,KAAA,CAAKL,KAAK,CAACuG,qBAAqB,IAAIlG,KAAA,CAAKL,KAAK,CAACuG,qBAAqB,CAACzI,CAAC,CAAC;IACzE,CAAC;IAAAuC,KAAA,CAKDmG,wBAAwB,GAA6B,UAAC1I,CAAc,EAAK;MACvE2I,wBAAe,CAACC,SAAS,CAAC,CAAC;MAC3BrG,KAAA,CAAKU,0BAA0B,GAAGqF,MAAM,CAACC,WAAW,CAACC,GAAG,CAAC,CAAC;MAC1DjG,KAAA,CAAKL,KAAK,CAAC2G,mBAAmB,IAAItG,KAAA,CAAKL,KAAK,CAAC2G,mBAAmB,CAAC7I,CAAC,CAAC;IACrE,CAAC;IAAAuC,KAAA,CASDuG,sBAAsB,GAA6B,UAAC9I,CAAc,EAAK;MACrE2I,wBAAe,CAACI,WAAW,CAAC,CAAC;MAE7B,IACEjD,iBAAQ,CAACC,EAAE,KAAK,SAAS,IACzBxD,KAAA,CAAKL,KAAK,CAAC8G,mBAAmB,KAAK,SAAS,EAC5C;QACA,IAAAC,wBAAe,EAAC,CAAC;MACnB;MAEA1G,KAAA,CAAKL,KAAK,CAACgH,iBAAiB,IAAI3G,KAAA,CAAKL,KAAK,CAACgH,iBAAiB,CAAClJ,CAAC,CAAC;IACjE,CAAC;IAAAuC,KAAA,CAKD4G,oBAAoB,GAA6B,UAACnJ,CAAc,EAAK;MACnE,IAAOoJ,QAAQ,GAAIpJ,CAAC,CAACkH,WAAW,CAAzBkC,QAAQ;MAMf,IACE,CAAC7G,KAAA,CAAK8G,YAAY,CAAC,CAAC,KACnB,CAACD,QAAQ,IAAKA,QAAQ,CAAC3E,CAAC,KAAK,CAAC,IAAI2E,QAAQ,CAAC1E,CAAC,KAAK,CAAE,CAAC,EACrD;QACAiE,wBAAe,CAACC,SAAS,CAAC,CAAC;MAC7B;MACArG,KAAA,CAAKL,KAAK,CAACoH,eAAe,IAAI/G,KAAA,CAAKL,KAAK,CAACoH,eAAe,CAACtJ,CAAC,CAAC;IAC7D,CAAC;IAAAuC,KAAA,CAOD8G,YAAY,GAAkB,YAAM;MAClC,IAAMb,GAAG,GAAGF,MAAM,CAACC,WAAW,CAACC,GAAG,CAAC,CAAC;MACpC,IAAMe,8BAA8B,GAClCf,GAAG,GAAGjG,KAAA,CAAKU,0BAA0B;MACvC,IAAMuG,WAAW,GACfD,8BAA8B,GAAGxH,qCAAqC,IACtEQ,KAAA,CAAKU,0BAA0B,GAAGV,KAAA,CAAKS,4BAA4B;MACrE,OAAOwG,WAAW;IACpB,CAAC;IAAAjH,KAAA,CAKDkH,qBAAqB,GAA4B,UAACzJ,CAAa,EAAK;MAClEuC,KAAA,CAAKW,qCAAqC,GAAG,KAAK;MAClDX,KAAA,CAAKL,KAAK,CAACwH,gBAAgB,IAAInH,KAAA,CAAKL,KAAK,CAACwH,gBAAgB,CAAC1J,CAAC,CAAC;MAC7DuC,KAAA,CAAKY,8BAA8B,GAAGZ,KAAA,CAAK8G,YAAY,CAAC,CAAC;IAC3D,CAAC;IAAA9G,KAAA,CAYDoH,sBAAsB,GAAe,YAAM,CAAC,CAAC;IAAApH,KAAA,CAK7CqH,uBAAuB,GAA4B,UAAC5J,CAAa,EAAK;MACpEuC,KAAA,CAAKQ,WAAW,GAAG/C,CAAC,CAACkH,WAAW,CAAC2C,OAAO,CAACC,MAAM,KAAK,CAAC;MACrDvH,KAAA,CAAKL,KAAK,CAAC6H,kBAAkB,IAAIxH,KAAA,CAAKL,KAAK,CAAC6H,kBAAkB,CAAC/J,CAAC,CAAC;MAEjE,IAAI,OAAOA,CAAC,CAACgK,MAAM,KAAK,QAAQ,EAAE;QAChC,IAAIC,OAAO,EAAE;UACXrF,OAAO,CAACsF,KAAK,CACX,iFACF,CAAC;QACH;QAEA;MACF;MAIA,IAAMC,yBAAyB,GAAGC,uBAAc,CAACC,qBAAqB,CAAC,CAAC;MACxE,IACEF,yBAAyB,IAAI,IAAI,IACjC5H,KAAA,CAAKL,KAAK,CAACoI,yBAAyB,KAAK,IAAI,IAC7C/H,KAAA,CAAKL,KAAK,CAACoI,yBAAyB,KAAK,QAAQ,IACjD/H,KAAA,CAAKgI,sBAAsB,CAAC,CAAC,IAC7BvK,CAAC,CAACgK,MAAM,KAAKG,yBAAyB,IACtC,CAAC5H,KAAA,CAAKW,qCAAqC,IAC3C,CAACX,KAAA,CAAKY,8BAA8B,EACpC;QACAiH,uBAAc,CAACI,aAAa,CAACL,yBAAyB,CAAC;MACzD;IACF,CAAC;IAAA5H,KAAA,CAiBDkI,kCAAkC,GAAkB,YAAM;MACxD,OAAO,CAAClI,KAAA,CAAKW,qCAAqC;IACpD,CAAC;IAAAX,KAAA,CAKDmI,+BAA+B,GAAkB,YAAM;MAErD,IAAInI,KAAA,CAAKL,KAAK,CAACyI,6BAA6B,KAAK,IAAI,EAAE;QACrD,OAAO,KAAK;MACd;MACA,OAAOpI,KAAA,CAAKQ,WAAW;IACzB,CAAC;IAAAR,KAAA,CA2BDqI,8BAA8B,GAA+B,UAC3D5K,CAAa,EACV;MAEH,IAAIuC,KAAA,CAAKL,KAAK,CAACyI,6BAA6B,KAAK,IAAI,EAAE;QACrD,OAAO,KAAK;MACd;MAEA,IAAMN,qBAAqB,GAAGD,uBAAc,CAACC,qBAAqB,CAAC,CAAC;MACpE,IACE9H,KAAA,CAAKL,KAAK,CAACoI,yBAAyB,KAAK,SAAS,IAClD/H,KAAA,CAAKgI,sBAAsB,CAAC,CAAC,IAC7BvK,CAAC,CAACgK,MAAM,KAAKK,qBAAqB,EAClC;QACA,OAAO,IAAI;MACb;MACA,OAAO,KAAK;IACd,CAAC;IAAA9H,KAAA,CAaDsI,qCAAqC,GAA+B,UAClE7K,CAAa,EACV;MAGH,IAAIuC,KAAA,CAAK8G,YAAY,CAAC,CAAC,EAAE;QACvB,OAAO,IAAI;MACb;MAGA,IAAI9G,KAAA,CAAKL,KAAK,CAACyI,6BAA6B,KAAK,IAAI,EAAE;QACrD,OAAO,KAAK;MACd;MAMA,IAAOL,yBAAyB,GAAI/H,KAAA,CAAKL,KAAK,CAAvCoI,yBAAyB;MAChC,IAAMQ,wBAAwB,GAC5B,CAACR,yBAAyB,IAAIA,yBAAyB,KAAK,OAAO;MAErE,IAAI,OAAOtK,CAAC,CAACgK,MAAM,KAAK,QAAQ,EAAE;QAChC,IAAIC,OAAO,EAAE;UACXrF,OAAO,CAACsF,KAAK,CACX,iFACF,CAAC;QACH;QAEA,OAAO,KAAK;MACd;MAGA,IAAI3H,KAAA,CAAKwI,uBAAuB,CAAC,CAAC,EAAE;QAClC,OAAO,KAAK;MACd;MAEA,IACED,wBAAwB,IACxBvI,KAAA,CAAKgI,sBAAsB,CAAC,CAAC,IAC7BvK,CAAC,CAACgK,MAAM,IAAI,IAAI,IAEhB,CAACI,uBAAc,CAACY,WAAW,CAAChL,CAAC,CAACgK,MAAM,CAAC,EACrC;QACA,OAAO,IAAI;MACb;MAEA,OAAO,KAAK;IACd,CAAC;IAAAzH,KAAA,CAKDgI,sBAAsB,GAAkB,YAAM;MAC5C,IAAMF,qBAAqB,GAAGD,uBAAc,CAACC,qBAAqB,CAAC,CAAC;MAOpE,IAAMY,mBAAmB,GACvBZ,qBAAqB,IAAI,IAAI,IAC7BD,uBAAc,CAACY,WAAW,CAACX,qBAAqB,CAAC;MAKnD,IAAMa,qBAAqB,GACzB3I,KAAA,CAAKM,gBAAgB,IAAI,IAAI,IAAIN,KAAA,CAAK4I,4BAA4B,CAAC,CAAC;MAEtE,OAAOF,mBAAmB,IAAIC,qBAAqB;IACrD,CAAC;IAAA3I,KAAA,CAODwI,uBAAuB,GAAkB,YAAM;MAC7C,OAAOxI,KAAA,CAAKM,gBAAgB,IAAI,IAAI,IAAIN,KAAA,CAAKM,gBAAgB,CAACuD,MAAM,KAAK,CAAC;IAC5E,CAAC;IAAA7D,KAAA,CAED4I,4BAA4B,GAAkB,YAAM;MAGlD,OAAOrF,iBAAQ,CAACC,EAAE,KAAK,SAAS,IAAID,iBAAQ,CAACsF,OAAO,GAAG,EAAE;IAC3D,CAAC;IAAA7I,KAAA,CAOD8I,eAAe,GAA4B,UAACrL,CAAa,EAAK;MAC5D,IAAMkH,WAAW,GAAGlH,CAAC,CAACkH,WAAW;MACjC3E,KAAA,CAAKQ,WAAW,GAAGmE,WAAW,CAAC2C,OAAO,CAACC,MAAM,KAAK,CAAC;MAEnD,IAAOQ,yBAAyB,GAAI/H,KAAA,CAAKL,KAAK,CAAvCoI,yBAAyB;MAChC,IAAMgB,yBAAyB,GAC7B,CAAChB,yBAAyB,IAAIA,yBAAyB,KAAK,OAAO;MAKrE,IAAMH,yBAAyB,GAAGC,uBAAc,CAACC,qBAAqB,CAAC,CAAC;MACxE,IACEF,yBAAyB,IAAI,IAAI,IACjCnK,CAAC,CAACgK,MAAM,KAAKG,yBAAyB,IACtC5H,KAAA,CAAKwI,uBAAuB,CAAC,CAAC,IAC9BxI,KAAA,CAAKgI,sBAAsB,CAAC,CAAC,IAC7Be,yBAAyB,EACzB;QACAlB,uBAAc,CAACI,aAAa,CAACL,yBAAyB,CAAC;MACzD;MAEA5H,KAAA,CAAKL,KAAK,CAACqJ,UAAU,IAAIhJ,KAAA,CAAKL,KAAK,CAACqJ,UAAU,CAACvL,CAAC,CAAC;IACnD,CAAC;IAAAuC,KAAA,CAODiJ,kBAAkB,GAA4B,UAACxL,CAAa,EAAK;MAC/DuC,KAAA,CAAKQ,WAAW,GAAG,KAAK;MACxBR,KAAA,CAAKL,KAAK,CAACuJ,aAAa,IAAIlJ,KAAA,CAAKL,KAAK,CAACuJ,aAAa,CAACzL,CAAC,CAAC;IACzD,CAAC;IAAAuC,KAAA,CAaDmJ,iBAAiB,GAA4B,UAAC1L,CAAa,EAAK;MAC9DuC,KAAA,CAAKQ,WAAW,GAAG,IAAI;MACvBR,KAAA,CAAKL,KAAK,CAACyJ,YAAY,IAAIpJ,KAAA,CAAKL,KAAK,CAACyJ,YAAY,CAAC3L,CAAC,CAAC;IACvD,CAAC;IAAAuC,KAAA,CAaDqJ,gBAAgB,GAA4B,UAAC5L,CAAa,EAAK;MAC7DuC,KAAA,CAAKL,KAAK,CAAC2J,WAAW,IAAItJ,KAAA,CAAKL,KAAK,CAAC2J,WAAW,CAAC7L,CAAC,CAAC;IACrD,CAAC;IAt4BCuC,KAAA,CAAKuJ,oBAAoB,GAAG,IAAIC,+BAAsB,CAACC,KAAK,EAAA7J,qBAAA,IAAAC,sBAAA,GAC1DG,KAAA,CAAKL,KAAK,CAAC+J,aAAa,qBAAxB7J,sBAAA,CAA0BsC,CAAC,YAAAvC,qBAAA,GAAI,CACjC,CAAC;IACDI,KAAA,CAAKuJ,oBAAoB,CAACI,SAAS,EAAA7J,qBAAA,IAAAC,sBAAA,GAACC,KAAA,CAAKL,KAAK,CAACiK,YAAY,qBAAvB7J,sBAAA,CAAyB4D,GAAG,YAAA7D,qBAAA,GAAI,CAAC,CAAC;IAAC,OAAAE,KAAA;EACzE;EAAC,IAAA6J,UAAA,CAAA/L,OAAA,EAAA2B,UAAA,EAAAC,gBAAA;EAAA,WAAAoK,aAAA,CAAAhM,OAAA,EAAA2B,UAAA;IAAAsK,GAAA;IAAAC,KAAA,EAkCD,SAAAC,iBAAiBA,CAAA,EAAG;MAClB,IAAI,OAAO,IAAI,CAACtK,KAAK,CAACoI,yBAAyB,KAAK,SAAS,EAAE;QAC7D1F,OAAO,CAACC,IAAI,CACV,+BACE,IAAI,CAAC3C,KAAK,CAACoI,yBAAyB,KAAK,IAAI,GAAG,MAAM,GAAG,OAAO,oBAC9C,GAClB,mCACE,IAAI,CAACpI,KAAK,CAACoI,yBAAyB,GAAG,QAAQ,GAAG,OAAO,YAE/D,CAAC;MACH;MAEA,IAAI,CAACzH,gBAAgB,GAAG4J,iBAAQ,CAACC,OAAO,CAAC,CAAC;MAC1C,IAAI,CAAC5J,uBAAuB,GAAG,CAAC;MAEhC,IAAI,CAACQ,6BAA6B,GAAGmJ,iBAAQ,CAACE,WAAW,CACvD,kBAAkB,EAClB,IAAI,CAAC/E,+BACP,CAAC;MACD,IAAI,CAACrE,6BAA6B,GAAGkJ,iBAAQ,CAACE,WAAW,CACvD,kBAAkB,EAClB,IAAI,CAAC5E,+BACP,CAAC;MACD,IAAI,CAACvE,4BAA4B,GAAGiJ,iBAAQ,CAACE,WAAW,CACtD,iBAAiB,EACjB,IAAI,CAAC1E,8BACP,CAAC;MACD,IAAI,CAACxE,4BAA4B,GAAGgJ,iBAAQ,CAACE,WAAW,CACtD,iBAAiB,EACjB,IAAI,CAACxE,8BACP,CAAC;MAED,IAAI,CAACyE,6BAA6B,CAAC,CAAC;IACtC;EAAC;IAAAN,GAAA;IAAAC,KAAA,EAED,SAAAM,kBAAkBA,CAACC,SAAgB,EAAE;MACnC,IAAMC,mBAAmB,GAAGD,SAAS,CAACX,YAAY,GAC9CW,SAAS,CAACX,YAAY,CAACjG,GAAG,GAC1B,CAAC;MACL,IAAM8G,kBAAkB,GAAG,IAAI,CAAC9K,KAAK,CAACiK,YAAY,GAC9C,IAAI,CAACjK,KAAK,CAACiK,YAAY,CAACjG,GAAG,GAC3B,CAAC;MACL,IAAI6G,mBAAmB,KAAKC,kBAAkB,EAAE;QAC9C,IAAI,CAAClB,oBAAoB,CAACI,SAAS,CAACc,kBAAkB,IAAI,CAAC,CAAC;MAC9D;MAEA,IAAI,CAACJ,6BAA6B,CAAC,CAAC;IACtC;EAAC;IAAAN,GAAA;IAAAC,KAAA,EAED,SAAAU,oBAAoBA,CAAA,EAAG;MACrB,IAAI,IAAI,CAAC3J,6BAA6B,IAAI,IAAI,EAAE;QAC9C,IAAI,CAACA,6BAA6B,CAAC4J,MAAM,CAAC,CAAC;MAC7C;MACA,IAAI,IAAI,CAAC3J,6BAA6B,IAAI,IAAI,EAAE;QAC9C,IAAI,CAACA,6BAA6B,CAAC2J,MAAM,CAAC,CAAC;MAC7C;MACA,IAAI,IAAI,CAAC1J,4BAA4B,IAAI,IAAI,EAAE;QAC7C,IAAI,CAACA,4BAA4B,CAAC0J,MAAM,CAAC,CAAC;MAC5C;MACA,IAAI,IAAI,CAACzJ,4BAA4B,IAAI,IAAI,EAAE;QAC7C,IAAI,CAACA,4BAA4B,CAACyJ,MAAM,CAAC,CAAC;MAC5C;MAEA,IAAI,IAAI,CAACzK,8BAA8B,EAAE;QACvC,IAAI,CAACA,8BAA8B,CAAC0K,MAAM,CAAC,CAAC;MAC9C;IACF;EAAC;IAAAb,GAAA;IAAAC,KAAA,EA2MD,SAAA9G,oBAAoBA,CAAA,EAAG;MACrBb,OAAO,CAACC,IAAI,CAAC,6BAA6B,CAAC;IAC7C;EAAC;IAAAyH,GAAA;IAAAC,KAAA,EAqDD,SAAAa,eAAeA,CAACC,KAAiB,EAAEC,UAAsB,EAAc;MACrE,IAAMC,KAAK,GAAGD,UAAU,CAACD,KAAK,CAAC;MAC/B,OAAOE,KAAK,IAAIA,KAAK,CAACjB,GAAG;IAC3B;EAAC;IAAAA,GAAA;IAAAC,KAAA,EAED,SAAAK,6BAA6BA,CAAA,EAAG;MAC9B,IAAI,IAAI,CAACnK,8BAA8B,EAAE;QACvC,IAAI,CAACA,8BAA8B,CAAC0K,MAAM,CAAC,CAAC;MAC9C;MACA,IACE,IAAI,CAACjL,KAAK,CAACsL,mBAAmB,IAC9B,IAAI,CAACtL,KAAK,CAACsL,mBAAmB,CAAC1D,MAAM,GAAG,CAAC,EACzC;QACA,IAAI,CAACrH,8BAA8B,GACjCsJ,+BAAsB,CAAC0B,iBAAiB,CACtC,IAAI,CAAC1J,kBAAkB,CAAC,CAAC,EACzB,UAAU,EACV,CAAC;UAACmD,WAAW,EAAE;YAAC+E,aAAa,EAAE;cAACvH,CAAC,EAAE,IAAI,CAACoH;YAAoB;UAAC;QAAC,CAAC,CACjE,CAAC;MACL;IACF;EAAC;IAAAQ,GAAA;IAAAC,KAAA,EAED,SAAAmB,mBAAmBA,CACjBpB,GAAW,EACXqB,GAAiD,EACjD;MACA,IAAIA,GAAG,EAAE;QACP,IAAI,CAACjL,iBAAiB,CAACxB,GAAG,CAACoL,GAAG,EAAEqB,GAAG,CAAC;MACtC,CAAC,MAAM;QACL,IAAI,CAACjL,iBAAiB,CAACkL,MAAM,CAACtB,GAAG,CAAC;MACpC;IACF;EAAC;IAAAA,GAAA;IAAAC,KAAA,EAED,SAAAsB,qBAAqBA,CAACR,KAAiB,EAAES,KAAiB,EAAExB,GAAe,EAAE;MAC3E,IAAOkB,mBAAmB,GAAI,IAAI,CAACtL,KAAK,CAAjCsL,mBAAmB;MAC1B,IAAI,CAACA,mBAAmB,EAAE;QACxB;MACF;MACA,IAAMF,UAAU,GAAG1N,KAAK,CAACmO,QAAQ,CAACC,OAAO,CAAa,IAAI,CAAC9L,KAAK,CAAC+L,QAAQ,CAAC;MAC1E,IAAI3B,GAAG,KAAK,IAAI,CAACc,eAAe,CAACC,KAAK,EAAEC,UAAU,CAAC,EAAE;QAEnD;MACF;MAEA,IAAMY,OAAO,GAAGJ,KAAK,CAAC5G,WAAW,CAACC,MAAM,CAACzC,CAAC;MAC1C,IAAI,CAAC9B,eAAe,CAAC1B,GAAG,CAACoL,GAAG,EAAE4B,OAAO,CAAC;MAEtC,IAAMC,YAAY,GAAGX,mBAAmB,CAACY,OAAO,CAACf,KAAK,CAAC;MACvD,IAAMgB,mBAAmB,GAAGb,mBAAmB,CAACW,YAAY,GAAG,CAAC,CAAC;MACjE,IAAIE,mBAAmB,IAAI,IAAI,EAAE;QAC/B,IAAMC,cAAc,GAAG,IAAI,CAAC5L,iBAAiB,CAACnC,GAAG,CAC/C,IAAI,CAAC6M,eAAe,CAACiB,mBAAmB,EAAEf,UAAU,CACtD,CAAC;QACDgB,cAAc,IACZA,cAAc,CAACC,cAAc,IAC7BD,cAAc,CAACC,cAAc,CAACL,OAAO,CAAC;MAC1C;IACF;EAAC;IAAA5B,GAAA;IAAAC,KAAA,EAqeD,SAAAiC,MAAMA,CAAA,EAAe;MAAA,IAAAC,MAAA;MACnB,IAAMC,UAAU,GAAG,IAAI,CAACxM,KAAK,CAACwM,UAAU,KAAK,IAAI;MAEjD,IAAMC,gBAAgB,GAAGD,UAAU,GAC/BE,uDAA0B,GAC1BC,uDAA0B;MAE9B,IAAMC,uBAAuB,GAAGJ,UAAU,GACtCK,8DAAiC,GACjCC,8DAAiC;MAErC,IAAMC,qBAAqB,GAAG,CAC5BP,UAAU,IAAIQ,MAAM,CAACC,0BAA0B,EAC/C,IAAI,CAACjN,KAAK,CAAC+M,qBAAqB,CACjC;MACD,IAAIhF,OAAO,IAAI,IAAI,CAAC/H,KAAK,CAACkN,KAAK,KAAKC,SAAS,EAAE;QAE7C,IAAMD,KAAK,GAAG,IAAAE,qBAAY,EAAC,IAAI,CAACpN,KAAK,CAACkN,KAAK,CAAC;QAC5C,IAAMG,gBAAgB,GAAG,CAAC,YAAY,EAAE,gBAAgB,CAAC,CAACC,MAAM,CAE9D,UAAAC,IAAI;UAAA,OAAIL,KAAK,IAAIA,KAAK,CAACK,IAAI,CAAC,KAAKJ,SAAS;QAAA,CAC5C,CAAC;QACD,IAAAxJ,kBAAS,EACP0J,gBAAgB,CAACzF,MAAM,KAAK,CAAC,EAC7B,2BAA2B,GACzB4F,IAAI,CAACC,SAAS,CAACJ,gBAAgB,CAAC,GAChC,2DACJ,CAAC;MACH;MAEA,IAAMK,sBAAsB,GAC1B,IAAI,CAAC1N,KAAK,CAACqF,mBAAmB,IAAI,IAAI,GAClC,IAAI,GACJ;QACEH,QAAQ,EAAE,IAAI,CAACC;MACjB,CAAC;MAEP,IAAOmG,mBAAmB,GAAI,IAAI,CAACtL,KAAK,CAAjCsL,mBAAmB;MAC1B,IAAIS,QAAQ,GAAG,IAAI,CAAC/L,KAAK,CAAC+L,QAAQ;MAKlCA,QAAQ,GAAGrO,KAAK,CAACmO,QAAQ,CAACC,OAAO,CAAaC,QAAQ,CAAC;MAEvD,IAAIT,mBAAmB,IAAI,IAAI,IAAIA,mBAAmB,CAAC1D,MAAM,GAAG,CAAC,EAAE;QACjEmE,QAAQ,GAAGA,QAAQ,CAAC4B,GAAG,CAAC,UAACtC,KAAK,EAAEF,KAAK,EAAK;UACxC,IAAMc,YAAY,GAAGZ,KAAK,GAAGC,mBAAmB,CAACY,OAAO,CAACf,KAAK,CAAC,GAAG,CAAC,CAAC;UACpE,IAAIc,YAAY,GAAG,CAAC,CAAC,EAAE;YACrB,IAAM7B,GAAG,GAAGiB,KAAK,CAACjB,GAAG;YACrB,IAAMwD,SAAS,GAAGtC,mBAAmB,CAACW,YAAY,GAAG,CAAC,CAAC;YACvD,IAAM4B,qBAAqB,GACzBtB,MAAI,CAACvM,KAAK,CAAC6N,qBAAqB,IAAIC,+BAAsB;YAC5D,OACE,IAAAnQ,WAAA,CAAAoQ,GAAA,EAACF,qBAAqB;cAEpBpC,GAAG,EAAE,SAALA,GAAGA,CAAEA,IAAG;gBAAA,OAAIc,MAAI,CAACf,mBAAmB,CAACpB,GAAG,EAAEqB,IAAG,CAAC;cAAA,CAAC;cAC/CuC,iBAAiB,EAAEzB,MAAI,CAAC7L,eAAe,CAACrC,GAAG,CACzCkO,MAAI,CAACrB,eAAe,CAAC0C,SAAS,EAAE7B,QAAQ,CAC1C,CAAE;cACF7G,QAAQ,EAAE,SAAVA,QAAQA,CAAE0G,KAAK;gBAAA,OAAIW,MAAI,CAACZ,qBAAqB,CAACR,KAAK,EAAES,KAAK,EAAExB,GAAG,CAAC;cAAA,CAAC;cACjE6D,mBAAmB,EAAE1B,MAAI,CAAC3C,oBAAqB;cAC/CsE,QAAQ,EAAE3B,MAAI,CAACvM,KAAK,CAAC8E,mBAAoB;cACzCqJ,cAAc,EAAE5B,MAAI,CAACvM,KAAK,CAACoO,0BAA2B;cACtDC,gBAAgB,EAAE9B,MAAI,CAAC/K,KAAK,CAACC,YAAa;cAAAsK,QAAA,EACzCV;YAAK,GAVDjB,GAWgB,CAAC;UAE5B,CAAC,MAAM;YACL,OAAOiB,KAAK;UACd;QACF,CAAC,CAAC;MACJ;MACAU,QAAQ,GACN,IAAApO,WAAA,CAAAoQ,GAAA,EAAC3Q,kBAAA,CAAAe,OAAiB,CAACmQ,QAAQ;QAACjE,KAAK,EAAEmC,UAAU,GAAG+B,6BAAU,GAAGC,2BAAS;QAAAzC,QAAA,EACnEA;MAAQ,CACiB,CAC7B;MAED,IAAM0C,gBAAgB,GACpBC,KAAK,CAACC,OAAO,CAACrD,mBAAmB,CAAC,IAAIA,mBAAmB,CAAC1D,MAAM,GAAG,CAAC;MAKtE,IAAMgH,gBAAgB,GACpB,IAAI,CAAC5O,KAAK,CAAC6O,8BAA8B,IAAI,IAAI,IAChDjL,iBAAQ,CAACC,EAAE,KAAK,SAAS,IAAI,IAAI,CAAC7D,KAAK,CAAC8O,eAAe,IAAI,IAAK;MAEnE,IAAMC,gBAAgB,GACpB,IAAApR,WAAA,CAAAoQ,GAAA,EAACnB,uBAAuB,EAAAnO,MAAA,CAAAgH,MAAA,KAClBiI,sBAAsB;QAC1BjC,GAAG,EAAE,IAAI,CAAC1J,UAAU,CAACiN,gBAAgB,CAAC,IAAI,CAAChP,KAAK,CAACiP,YAAY,CAAE;QAC/D/B,KAAK,EAAEH,qBAAsB;QAC7BmC,qBAAqB,EAGnBtL,iBAAQ,CAACC,EAAE,KAAK,SAAS,IAAI4K,gBAAgB,GACzC,KAAK,GACL,IAAI,CAACzO,KAAK,CAACkP,qBAChB;QACDC,WAAW,EAAE,KAAM;QACnBC,mBAAmB,EAAE,CAACR,gBAAiB;QAAA7C,QAAA,EACtCA;MAAQ,EACc,CAC1B;MAED,IAAMsD,sBAAsB,GAC1B,IAAI,CAACrP,KAAK,CAACqP,sBAAsB,KAAKlC,SAAS,GAC3C,IAAI,CAACnN,KAAK,CAACqP,sBAAsB,GACjC,IAAI,CAACrP,KAAK,CAACwM,UAAU;MAE3B,IAAM8C,oBAAoB,GACxB,IAAI,CAACtP,KAAK,CAACsP,oBAAoB,KAAKnC,SAAS,GACzC,IAAI,CAACnN,KAAK,CAACsP,oBAAoB,GAC/B,CAAC,IAAI,CAACtP,KAAK,CAACwM,UAAU;MAE5B,IAAM+C,SAAS,GAAG/C,UAAU,GAAGQ,MAAM,CAACwC,cAAc,GAAGxC,MAAM,CAACyC,YAAY;MAE1E,IAAAC,WAAA,GACE,IAAI,CAAC1P,KAAK;QADL2P,6CAA6C,GAAAD,WAAA,CAA7CC,6CAA6C;QAAKC,UAAU,OAAAC,yBAAA,CAAA1R,OAAA,EAAAuR,WAAA,EAAA9R,SAAA;MAEnE,IAAMoC,KAAK,GAAAvB,MAAA,CAAAgH,MAAA,KACNmK,UAAU;QACbP,sBAAsB,EAAtBA,sBAAsB;QACtBC,oBAAoB,EAApBA,oBAAoB;QACpBpC,KAAK,EAAE4C,mBAAU,CAACC,OAAO,CAACR,SAAS,EAAE,IAAI,CAACvP,KAAK,CAACkN,KAAK,CAAC;QAGtD7H,mBAAmB,EAAE,IAAI;QACzBH,QAAQ,EAAE,IAAI,CAACL,aAAa;QAC5B0B,qBAAqB,EAAE,IAAI,CAACJ,0BAA0B;QACtDQ,mBAAmB,EAAE,IAAI,CAACH,wBAAwB;QAClDgB,gBAAgB,EAAE,IAAI,CAACD,qBAAqB;QAC5CyI,iBAAiB,EAAE,IAAI,CAACvI,sBAAsB;QAC9CI,kBAAkB,EAAE,IAAI,CAACH,uBAAuB;QAChDuI,6BAA6B,EAAE,IAAI,CAAC1H,kCAAkC;QACtEvB,iBAAiB,EAAE,IAAI,CAACJ,sBAAsB;QAC9CQ,eAAe,EAAE,IAAI,CAACH,oBAAoB;QAC1CiJ,0BAA0B,EAAE,IAAI,CAAC1H,+BAA+B;QAChE2H,yBAAyB,EAAE,IAAI,CAACzH,8BAA8B;QAC9D0H,gCAAgC,EAC9B,IAAI,CAACzH,qCAAqC;QAC5CU,UAAU,EAAE,IAAI,CAACF,eAAe;QAChCQ,WAAW,EAAE,IAAI,CAACD,gBAAgB;QAClCD,YAAY,EAAE,IAAI,CAACD,iBAAiB;QACpCD,aAAa,EAAE,IAAI,CAACD,kBAAkB;QACtC1E,QAAQ,EAAE,IAAI,CAACD,aAAa;QAC5B0L,gCAAgC,EAC9BV,6CAA6C;QAC/CW,mBAAmB,EAAE7B,gBAAgB,GACjC,CAAC,GACD,IAAI,CAACzO,KAAK,CAACsQ,mBAAmB;QAClCC,kBAAkB,EAChB,IAAI,CAACvQ,KAAK,CAACuG,qBAAqB,IAAI,IAAI,CAACvG,KAAK,CAAC2G,mBAAmB,GAC9D,IAAI,GACJ,KAAK;QAEX6J,WAAW,EAAE,IAAI,CAACxQ,KAAK,CAACwQ,WAAW,KAAK,KAAK;QAE7CC,SAAS,EAAE,IAAI,CAACzQ,KAAK,CAACyQ,SAAS,KAAK,KAAK;QAEzCC,aAAa,EAAE9M,iBAAQ,CAAC+M,MAAM,CAAC;UAE7BC,GAAG,EACD,IAAI,CAAC5Q,KAAK,CAAC0Q,aAAa,KAAK,IAAI,IACjC,IAAI,CAAC1Q,KAAK,CAAC6Q,cAAc,IAAI,IAAI,IACjC,IAAI,CAAC7Q,KAAK,CAAC8Q,aAAa,IAAI,IAAI;UAElCC,OAAO,EACL,IAAI,CAAC/Q,KAAK,CAAC0Q,aAAa,KAAK,IAAI,IACjC,IAAI,CAAC1Q,KAAK,CAAC6Q,cAAc,IAAI,IAAI,IACjC,IAAI,CAAC7Q,KAAK,CAAC8Q,aAAa,IAAI;QAChC,CAAC;MAAC,EACH;MAED,IAAOE,gBAAgB,GAAI,IAAI,CAAChR,KAAK,CAA9BgR,gBAAgB;MACvB,IAAIA,gBAAgB,IAAI,IAAI,EAAE;QAC5BhR,KAAK,CAACgR,gBAAgB,GAAG,IAAAC,gCAAuB,EAACD,gBAAgB,CAAC;MACpE;MAEA,IAAME,cAAc,GAAG,IAAI,CAAClR,KAAK,CAACkR,cAAc;MAChD,IAAMC,aAAa,GAAG,IAAI,CAACjP,WAAW,CAAC8M,gBAAgB,CACrD,IAAI,CAAChP,KAAK,CAACmR,aACb,CAAC;MAED,IAAID,cAAc,EAAE;QAClB,IAAItN,iBAAQ,CAACC,EAAE,KAAK,KAAK,EAAE;UAEzB,OAEE,IAAAlG,WAAA,CAAAyT,IAAA,EAAC3E,gBAAgB,EAAAhO,MAAA,CAAAgH,MAAA,KAAKzF,KAAK;YAAEyL,GAAG,EAAE0F,aAAc;YAAApF,QAAA,GAC7CmF,cAAc,EACdnC,gBAAgB;UAAA,EACD,CAAC;QAEvB,CAAC,MAAM,IAAInL,iBAAQ,CAACC,EAAE,KAAK,SAAS,EAAE;UAQpC,IAAAwN,iBAAA,GAAuB,IAAAC,0BAAgB,EAAC,IAAAlE,qBAAY,EAACpN,KAAK,CAACkN,KAAK,CAAC,CAAC;YAA3DqE,KAAK,GAAAF,iBAAA,CAALE,KAAK;YAAEC,KAAK,GAAAH,iBAAA,CAALG,KAAK;UACnB,OAAO9T,KAAK,CAAC+T,YAAY,CACvBP,cAAc,EACd;YAAChE,KAAK,EAAE4C,mBAAU,CAACC,OAAO,CAACR,SAAS,EAAEgC,KAAK;UAAC,CAAC,EAC7C,IAAA5T,WAAA,CAAAoQ,GAAA,EAACtB,gBAAgB,EAAAhO,MAAA,CAAAgH,MAAA,KACXzF,KAAK;YACTkN,KAAK,EAAE4C,mBAAU,CAACC,OAAO,CAACR,SAAS,EAAEiC,KAAK,CAAE;YAE5C/F,GAAG,EAAE0F,aAAc;YAAApF,QAAA,EAClBgD;UAAgB,EACD,CACpB,CAAC;QACH;MACF;MACA,OAEE,IAAApR,WAAA,CAAAoQ,GAAA,EAACtB,gBAAgB,EAAAhO,MAAA,CAAAgH,MAAA,KAAKzF,KAAK;QAAEyL,GAAG,EAAE0F,aAAc;QAAApF,QAAA,EAC7CgD;MAAgB,EACD,CAAC;IAEvB;EAAC;AAAA,EA7mCsBrR,KAAK,CAACgU,SAAS;AAAlC5R,UAAU,CACP6R,OAAO,GAA6BC,0BAAiB;AA+mC9D,IAAM5E,MAAM,GAAG8C,mBAAU,CAAC+B,MAAM,CAAC;EAC/BpC,YAAY,EAAE;IACZqC,QAAQ,EAAE,CAAC;IACXC,UAAU,EAAE,CAAC;IACbC,aAAa,EAAE,QAAQ;IACvBC,QAAQ,EAAE;EACZ,CAAC;EACDzC,cAAc,EAAE;IACdsC,QAAQ,EAAE,CAAC;IACXC,UAAU,EAAE,CAAC;IACbC,aAAa,EAAE,KAAK;IACpBC,QAAQ,EAAE;EACZ,CAAC;EACDhF,0BAA0B,EAAE;IAC1B+E,aAAa,EAAE;EACjB;AACF,CAAC,CAAC;AAcF,SAAS1M,kBAAkBA,CACzB4M,OAA2C,EACK;EAChD,IAAM1Q,KAAqD,GAAG;IAC5DwN,gBAAgB,EAAE,IAAAmD,mBAAO,EAAC,UAAAC,YAAY,EAAI;MACxC,OAAO,UAACpQ,cAAsC,EAAW;QACvD,IAAMwD,cAAc,GAClBxD,cAAc,IAAI,IAAI,GAAG,IAAI,GAAGkQ,OAAO,CAAClQ,cAAc,CAAC;QAEzDR,KAAK,CAACQ,cAAc,GAAGA,cAAc;QACrCR,KAAK,CAACgE,cAAc,GAAGA,cAAc;QAErC,IAAI4M,YAAY,IAAI,IAAI,EAAE;UACxB,IAAI,OAAOA,YAAY,KAAK,UAAU,EAAE;YACtCA,YAAY,CAAC5M,cAAc,CAAC;UAC9B,CAAC,MAAM;YACL4M,YAAY,CAACC,OAAO,GAAG7M,cAAc;UACvC;QACF;MACF,CAAC;IACH,CAAC,CAAC;IACFxD,cAAc,EAAE,IAAI;IACpBwD,cAAc,EAAE;EAClB,CAAC;EAED,OAAOhE,KAAK;AACd;AAMA,IAAM8Q,OAGL,GAAG5U,KAAK,CAAC6U,UAAU,CAAC,SAASD,OAAOA,CACnCtS,KAAY,EACZyL,GAA+C,EACnC;EACZ,OAAOA,GAAG,IAAI,IAAI,GAChB,IAAA9N,WAAA,CAAAoQ,GAAA,EAACjO,UAAU,EAAArB,MAAA,CAAAgH,MAAA,KAAKzF,KAAK,CAAG,CAAC,GAEzB,IAAArC,WAAA,CAAAoQ,GAAA,EAACjO,UAAU,EAAArB,MAAA,CAAAgH,MAAA,KAAKzF,KAAK;IAAEmR,aAAa,EAAE1F;EAAI,EAAE,CAC7C;AACH,CAAC,CAAC;AACF6G,OAAO,CAACE,WAAW,GAAG,YAAY;AAElCF,OAAO,CAACX,OAAO,GAAGC,0BAAiB;AAEnCa,MAAM,CAACC,OAAO,GAAKJ,OACU", "ignoreList": []}