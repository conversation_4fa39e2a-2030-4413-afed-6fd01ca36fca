882d862414f525de6500fb932f7bce4a
'use strict';

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.StretchOutY = exports.StretchOutX = exports.StretchInY = exports.StretchInX = void 0;
var _slicedToArray2 = _interopRequireDefault(require("@babel/runtime/helpers/slicedToArray"));
var _classCallCheck2 = _interopRequireDefault(require("@babel/runtime/helpers/classCallCheck"));
var _createClass2 = _interopRequireDefault(require("@babel/runtime/helpers/createClass"));
var _possibleConstructorReturn2 = _interopRequireDefault(require("@babel/runtime/helpers/possibleConstructorReturn"));
var _getPrototypeOf2 = _interopRequireDefault(require("@babel/runtime/helpers/getPrototypeOf"));
var _inherits2 = _interopRequireDefault(require("@babel/runtime/helpers/inherits"));
var _index = require("../animationBuilder/index.js");
function _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }
function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }
var StretchInX = exports.StretchInX = function (_ComplexAnimationBuil) {
  function StretchInX() {
    var _this;
    (0, _classCallCheck2.default)(this, StretchInX);
    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {
      args[_key] = arguments[_key];
    }
    _this = _callSuper(this, StretchInX, [].concat(args));
    _this.build = function () {
      var delayFunction = _this.getDelayFunction();
      var _this$getAnimationAnd = _this.getAnimationAndConfig(),
        _this$getAnimationAnd2 = (0, _slicedToArray2.default)(_this$getAnimationAnd, 2),
        animation = _this$getAnimationAnd2[0],
        config = _this$getAnimationAnd2[1];
      var delay = _this.getDelay();
      var callback = _this.callbackV;
      var initialValues = _this.initialValues;
      return function () {
        'worklet';

        return {
          animations: {
            transform: [{
              scaleX: delayFunction(delay, animation(1, config))
            }]
          },
          initialValues: Object.assign({
            transform: [{
              scaleX: 0
            }]
          }, initialValues),
          callback: callback
        };
      };
    };
    return _this;
  }
  (0, _inherits2.default)(StretchInX, _ComplexAnimationBuil);
  return (0, _createClass2.default)(StretchInX, null, [{
    key: "createInstance",
    value: function createInstance() {
      return new StretchInX();
    }
  }]);
}(_index.ComplexAnimationBuilder);
StretchInX.presetName = 'StretchInX';
var StretchInY = exports.StretchInY = function (_ComplexAnimationBuil2) {
  function StretchInY() {
    var _this2;
    (0, _classCallCheck2.default)(this, StretchInY);
    for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {
      args[_key2] = arguments[_key2];
    }
    _this2 = _callSuper(this, StretchInY, [].concat(args));
    _this2.build = function () {
      var delayFunction = _this2.getDelayFunction();
      var _this2$getAnimationAn = _this2.getAnimationAndConfig(),
        _this2$getAnimationAn2 = (0, _slicedToArray2.default)(_this2$getAnimationAn, 2),
        animation = _this2$getAnimationAn2[0],
        config = _this2$getAnimationAn2[1];
      var delay = _this2.getDelay();
      var callback = _this2.callbackV;
      var initialValues = _this2.initialValues;
      return function () {
        'worklet';

        return {
          animations: {
            transform: [{
              scaleY: delayFunction(delay, animation(1, config))
            }]
          },
          initialValues: Object.assign({
            transform: [{
              scaleY: 0
            }]
          }, initialValues),
          callback: callback
        };
      };
    };
    return _this2;
  }
  (0, _inherits2.default)(StretchInY, _ComplexAnimationBuil2);
  return (0, _createClass2.default)(StretchInY, null, [{
    key: "createInstance",
    value: function createInstance() {
      return new StretchInY();
    }
  }]);
}(_index.ComplexAnimationBuilder);
StretchInY.presetName = 'StretchInY';
var StretchOutX = exports.StretchOutX = function (_ComplexAnimationBuil3) {
  function StretchOutX() {
    var _this3;
    (0, _classCallCheck2.default)(this, StretchOutX);
    for (var _len3 = arguments.length, args = new Array(_len3), _key3 = 0; _key3 < _len3; _key3++) {
      args[_key3] = arguments[_key3];
    }
    _this3 = _callSuper(this, StretchOutX, [].concat(args));
    _this3.build = function () {
      var delayFunction = _this3.getDelayFunction();
      var _this3$getAnimationAn = _this3.getAnimationAndConfig(),
        _this3$getAnimationAn2 = (0, _slicedToArray2.default)(_this3$getAnimationAn, 2),
        animation = _this3$getAnimationAn2[0],
        config = _this3$getAnimationAn2[1];
      var delay = _this3.getDelay();
      var callback = _this3.callbackV;
      var initialValues = _this3.initialValues;
      return function () {
        'worklet';

        return {
          animations: {
            transform: [{
              scaleX: delayFunction(delay, animation(0, config))
            }]
          },
          initialValues: Object.assign({
            transform: [{
              scaleX: 1
            }]
          }, initialValues),
          callback: callback
        };
      };
    };
    return _this3;
  }
  (0, _inherits2.default)(StretchOutX, _ComplexAnimationBuil3);
  return (0, _createClass2.default)(StretchOutX, null, [{
    key: "createInstance",
    value: function createInstance() {
      return new StretchOutX();
    }
  }]);
}(_index.ComplexAnimationBuilder);
StretchOutX.presetName = 'StretchOutX';
var StretchOutY = exports.StretchOutY = function (_ComplexAnimationBuil4) {
  function StretchOutY() {
    var _this4;
    (0, _classCallCheck2.default)(this, StretchOutY);
    for (var _len4 = arguments.length, args = new Array(_len4), _key4 = 0; _key4 < _len4; _key4++) {
      args[_key4] = arguments[_key4];
    }
    _this4 = _callSuper(this, StretchOutY, [].concat(args));
    _this4.build = function () {
      var delayFunction = _this4.getDelayFunction();
      var _this4$getAnimationAn = _this4.getAnimationAndConfig(),
        _this4$getAnimationAn2 = (0, _slicedToArray2.default)(_this4$getAnimationAn, 2),
        animation = _this4$getAnimationAn2[0],
        config = _this4$getAnimationAn2[1];
      var delay = _this4.getDelay();
      var callback = _this4.callbackV;
      var initialValues = _this4.initialValues;
      return function () {
        'worklet';

        return {
          animations: {
            transform: [{
              scaleY: delayFunction(delay, animation(0, config))
            }]
          },
          initialValues: Object.assign({
            transform: [{
              scaleY: 1
            }]
          }, initialValues),
          callback: callback
        };
      };
    };
    return _this4;
  }
  (0, _inherits2.default)(StretchOutY, _ComplexAnimationBuil4);
  return (0, _createClass2.default)(StretchOutY, null, [{
    key: "createInstance",
    value: function createInstance() {
      return new StretchOutY();
    }
  }]);
}(_index.ComplexAnimationBuilder);
StretchOutY.presetName = 'StretchOutY';
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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