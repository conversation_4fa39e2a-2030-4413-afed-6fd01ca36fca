c7d2c28fd7402c4b96a61af52e3ccdf2
'use strict';

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.AnimatedScrollView = void 0;
var _objectWithoutProperties2 = _interopRequireDefault(require("@babel/runtime/helpers/objectWithoutProperties"));
var _react = _interopRequireWildcard(require("react"));
var _reactNative = require("react-native");
var _index = require("../createAnimatedComponent/index.js");
var _index2 = require("../hook/index.js");
var _excluded = ["scrollViewOffset"];
function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
function _extends() {
  return _extends = Object.assign ? Object.assign.bind() : function (n) {
    for (var e = 1; e < arguments.length; e++) {
      var t = arguments[e];
      for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]);
    }
    return n;
  }, _extends.apply(null, arguments);
}
var AnimatedScrollViewComponent = (0, _index.createAnimatedComponent)(_reactNative.ScrollView);
var AnimatedScrollView = exports.AnimatedScrollView = (0, _react.forwardRef)(function (props, ref) {
  var scrollViewOffset = props.scrollViewOffset,
    restProps = (0, _objectWithoutProperties2.default)(props, _excluded);
  var animatedRef = ref === null ? (0, _index2.useAnimatedRef)() : ref;
  if (scrollViewOffset) {
    (0, _index2.useScrollViewOffset)(animatedRef, scrollViewOffset);
  }
  if (!('scrollEventThrottle' in restProps)) {
    restProps.scrollEventThrottle = 1;
  }
  return _react.default.createElement(AnimatedScrollViewComponent, _extends({
    ref: animatedRef
  }, restProps));
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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