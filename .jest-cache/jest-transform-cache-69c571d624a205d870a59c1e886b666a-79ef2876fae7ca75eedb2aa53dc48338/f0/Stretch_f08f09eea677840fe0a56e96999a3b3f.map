{"version": 3, "names": ["_interopRequireDefault", "require", "Object", "defineProperty", "exports", "value", "StretchOutY", "StretchOutX", "StretchInY", "StretchInX", "_slicedToArray2", "_classCallCheck2", "_createClass2", "_possibleConstructorReturn2", "_getPrototypeOf2", "_inherits2", "_index", "_callSuper", "t", "o", "e", "default", "_isNativeReflectConstruct", "Reflect", "construct", "constructor", "apply", "Boolean", "prototype", "valueOf", "call", "_ComplexAnimationBuil", "_this", "_len", "arguments", "length", "args", "Array", "_key", "concat", "build", "delayFunction", "getDelayFunction", "_this$getAnimationAnd", "getAnimationAndConfig", "_this$getAnimationAnd2", "animation", "config", "delay", "get<PERSON>elay", "callback", "callbackV", "initialValues", "animations", "transform", "scaleX", "assign", "key", "createInstance", "ComplexAnimationBuilder", "presetName", "_ComplexAnimationBuil2", "_this2", "_len2", "_key2", "_this2$getAnimationAn", "_this2$getAnimationAn2", "scaleY", "_ComplexAnimationBuil3", "_this3", "_len3", "_key3", "_this3$getAnimationAn", "_this3$getAnimationAn2", "_ComplexAnimationBuil4", "_this4", "_len4", "_key4", "_this4$getAnimationAn", "_this4$getAnimationAn2"], "sources": ["../../../../src/layoutReanimation/defaultAnimations/Stretch.ts"], "sourcesContent": [null], "mappings": "AAAA,YAAY;;AAAA,IAAAA,sBAAA,GAAAC,OAAA;AAAAC,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,KAAA;AAAA;AAAAD,OAAA,CAAAE,WAAA,GAAAF,OAAA,CAAAG,WAAA,GAAAH,OAAA,CAAAI,UAAA,GAAAJ,OAAA,CAAAK,UAAA;AAAA,IAAAC,eAAA,GAAAV,sBAAA,CAAAC,OAAA;AAAA,IAAAU,gBAAA,GAAAX,sBAAA,CAAAC,OAAA;AAAA,IAAAW,aAAA,GAAAZ,sBAAA,CAAAC,OAAA;AAAA,IAAAY,2BAAA,GAAAb,sBAAA,CAAAC,OAAA;AAAA,IAAAa,gBAAA,GAAAd,sBAAA,CAAAC,OAAA;AAAA,IAAAc,UAAA,GAAAf,sBAAA,CAAAC,OAAA;AAMZ,IAAAe,MAAA,GAAAf,OAAA;AAA6D,SAAAgB,WAAAC,CAAA,EAAAC,CAAA,EAAAC,CAAA,WAAAD,CAAA,OAAAL,gBAAA,CAAAO,OAAA,EAAAF,CAAA,OAAAN,2BAAA,CAAAQ,OAAA,EAAAH,CAAA,EAAAI,yBAAA,KAAAC,OAAA,CAAAC,SAAA,CAAAL,CAAA,EAAAC,CAAA,YAAAN,gBAAA,CAAAO,OAAA,EAAAH,CAAA,EAAAO,WAAA,IAAAN,CAAA,CAAAO,KAAA,CAAAR,CAAA,EAAAE,CAAA;AAAA,SAAAE,0BAAA,cAAAJ,CAAA,IAAAS,OAAA,CAAAC,SAAA,CAAAC,OAAA,CAAAC,IAAA,CAAAP,OAAA,CAAAC,SAAA,CAAAG,OAAA,iCAAAT,CAAA,aAAAI,yBAAA,YAAAA,0BAAA,aAAAJ,CAAA;AAAA,IAWhDT,UAAU,GAAAL,OAAA,CAAAK,UAAA,aAAAsB,qBAAA;EAAA,SAAAtB,WAAA;IAAA,IAAAuB,KAAA;IAAA,IAAArB,gBAAA,CAAAU,OAAA,QAAAZ,UAAA;IAAA,SAAAwB,IAAA,GAAAC,SAAA,CAAAC,MAAA,EAAAC,IAAA,OAAAC,KAAA,CAAAJ,IAAA,GAAAK,IAAA,MAAAA,IAAA,GAAAL,IAAA,EAAAK,IAAA;MAAAF,IAAA,CAAAE,IAAA,IAAAJ,SAAA,CAAAI,IAAA;IAAA;IAAAN,KAAA,GAAAf,UAAA,OAAAR,UAAA,KAAA8B,MAAA,CAAAH,IAAA;IAAAJ,KAAA,CAYrBQ,KAAK,GAAG,YAAkC;MACxC,IAAMC,aAAa,GAAGT,KAAA,CAAKU,gBAAgB,CAAC,CAAC;MAC7C,IAAAC,qBAAA,GAA4BX,KAAA,CAAKY,qBAAqB,CAAC,CAAC;QAAAC,sBAAA,OAAAnC,eAAA,CAAAW,OAAA,EAAAsB,qBAAA;QAAjDG,SAAS,GAAAD,sBAAA;QAAEE,MAAM,GAAAF,sBAAA;MACxB,IAAMG,KAAK,GAAGhB,KAAA,CAAKiB,QAAQ,CAAC,CAAC;MAC7B,IAAMC,QAAQ,GAAGlB,KAAA,CAAKmB,SAAS;MAC/B,IAAMC,aAAa,GAAGpB,KAAA,CAAKoB,aAAa;MAExC,OAAO,YAAM;QACX,SAAS;;QACT,OAAO;UACLC,UAAU,EAAE;YACVC,SAAS,EAAE,CAAC;cAAEC,MAAM,EAAEd,aAAa,CAACO,KAAK,EAAEF,SAAS,CAAC,CAAC,EAAEC,MAAM,CAAC;YAAE,CAAC;UACpE,CAAC;UACDK,aAAa,EAAAlD,MAAA,CAAAsD,MAAA;YACXF,SAAS,EAAE,CAAC;cAAEC,MAAM,EAAE;YAAE,CAAC;UAAC,GACvBH,aAAA,CACJ;UACDF,QAAA,EAAAA;QACF,CAAC;MACH,CAAC;IACH,CAAC;IAAA,OAAAlB,KAAA;EAAA;EAAA,IAAAjB,UAAA,CAAAM,OAAA,EAAAZ,UAAA,EAAAsB,qBAAA;EAAA,WAAAnB,aAAA,CAAAS,OAAA,EAAAZ,UAAA;IAAAgD,GAAA;IAAApD,KAAA,EA1BD,SAAOqD,cAAcA,CAAA,EAEF;MACjB,OAAO,IAAIjD,UAAU,CAAC,CAAC;IACzB;EAAA;AAAA,EATQkD,8BAAuB;AADpBlD,UAAU,CAIdmD,UAAU,GAAG,YAAY;AAAA,IAwCrBpD,UAAU,GAAAJ,OAAA,CAAAI,UAAA,aAAAqD,sBAAA;EAAA,SAAArD,WAAA;IAAA,IAAAsD,MAAA;IAAA,IAAAnD,gBAAA,CAAAU,OAAA,QAAAb,UAAA;IAAA,SAAAuD,KAAA,GAAA7B,SAAA,CAAAC,MAAA,EAAAC,IAAA,OAAAC,KAAA,CAAA0B,KAAA,GAAAC,KAAA,MAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA;MAAA5B,IAAA,CAAA4B,KAAA,IAAA9B,SAAA,CAAA8B,KAAA;IAAA;IAAAF,MAAA,GAAA7C,UAAA,OAAAT,UAAA,KAAA+B,MAAA,CAAAH,IAAA;IAAA0B,MAAA,CAYrBtB,KAAK,GAAG,YAAkC;MACxC,IAAMC,aAAa,GAAGqB,MAAA,CAAKpB,gBAAgB,CAAC,CAAC;MAC7C,IAAAuB,qBAAA,GAA4BH,MAAA,CAAKlB,qBAAqB,CAAC,CAAC;QAAAsB,sBAAA,OAAAxD,eAAA,CAAAW,OAAA,EAAA4C,qBAAA;QAAjDnB,SAAS,GAAAoB,sBAAA;QAAEnB,MAAM,GAAAmB,sBAAA;MACxB,IAAMlB,KAAK,GAAGc,MAAA,CAAKb,QAAQ,CAAC,CAAC;MAC7B,IAAMC,QAAQ,GAAGY,MAAA,CAAKX,SAAS;MAC/B,IAAMC,aAAa,GAAGU,MAAA,CAAKV,aAAa;MAExC,OAAO,YAAM;QACX,SAAS;;QACT,OAAO;UACLC,UAAU,EAAE;YACVC,SAAS,EAAE,CAAC;cAAEa,MAAM,EAAE1B,aAAa,CAACO,KAAK,EAAEF,SAAS,CAAC,CAAC,EAAEC,MAAM,CAAC;YAAE,CAAC;UACpE,CAAC;UACDK,aAAa,EAAAlD,MAAA,CAAAsD,MAAA;YACXF,SAAS,EAAE,CAAC;cAAEa,MAAM,EAAE;YAAE,CAAC;UAAC,GACvBf,aAAA,CACJ;UACDF,QAAA,EAAAA;QACF,CAAC;MACH,CAAC;IACH,CAAC;IAAA,OAAAY,MAAA;EAAA;EAAA,IAAA/C,UAAA,CAAAM,OAAA,EAAAb,UAAA,EAAAqD,sBAAA;EAAA,WAAAjD,aAAA,CAAAS,OAAA,EAAAb,UAAA;IAAAiD,GAAA;IAAApD,KAAA,EA1BD,SAAOqD,cAAcA,CAAA,EAEF;MACjB,OAAO,IAAIlD,UAAU,CAAC,CAAC;IACzB;EAAA;AAAA,EATQmD,8BAAuB;AADpBnD,UAAU,CAIdoD,UAAU,GAAG,YAAY;AAAA,IAwCrBrD,WAAW,GAAAH,OAAA,CAAAG,WAAA,aAAA6D,sBAAA;EAAA,SAAA7D,YAAA;IAAA,IAAA8D,MAAA;IAAA,IAAA1D,gBAAA,CAAAU,OAAA,QAAAd,WAAA;IAAA,SAAA+D,KAAA,GAAApC,SAAA,CAAAC,MAAA,EAAAC,IAAA,OAAAC,KAAA,CAAAiC,KAAA,GAAAC,KAAA,MAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA;MAAAnC,IAAA,CAAAmC,KAAA,IAAArC,SAAA,CAAAqC,KAAA;IAAA;IAAAF,MAAA,GAAApD,UAAA,OAAAV,WAAA,KAAAgC,MAAA,CAAAH,IAAA;IAAAiC,MAAA,CAYtB7B,KAAK,GAAG,YAAkC;MACxC,IAAMC,aAAa,GAAG4B,MAAA,CAAK3B,gBAAgB,CAAC,CAAC;MAC7C,IAAA8B,qBAAA,GAA4BH,MAAA,CAAKzB,qBAAqB,CAAC,CAAC;QAAA6B,sBAAA,OAAA/D,eAAA,CAAAW,OAAA,EAAAmD,qBAAA;QAAjD1B,SAAS,GAAA2B,sBAAA;QAAE1B,MAAM,GAAA0B,sBAAA;MACxB,IAAMzB,KAAK,GAAGqB,MAAA,CAAKpB,QAAQ,CAAC,CAAC;MAC7B,IAAMC,QAAQ,GAAGmB,MAAA,CAAKlB,SAAS;MAC/B,IAAMC,aAAa,GAAGiB,MAAA,CAAKjB,aAAa;MAExC,OAAO,YAAM;QACX,SAAS;;QACT,OAAO;UACLC,UAAU,EAAE;YACVC,SAAS,EAAE,CAAC;cAAEC,MAAM,EAAEd,aAAa,CAACO,KAAK,EAAEF,SAAS,CAAC,CAAC,EAAEC,MAAM,CAAC;YAAE,CAAC;UACpE,CAAC;UACDK,aAAa,EAAAlD,MAAA,CAAAsD,MAAA;YACXF,SAAS,EAAE,CAAC;cAAEC,MAAM,EAAE;YAAE,CAAC;UAAC,GACvBH,aAAA,CACJ;UACDF,QAAA,EAAAA;QACF,CAAC;MACH,CAAC;IACH,CAAC;IAAA,OAAAmB,MAAA;EAAA;EAAA,IAAAtD,UAAA,CAAAM,OAAA,EAAAd,WAAA,EAAA6D,sBAAA;EAAA,WAAAxD,aAAA,CAAAS,OAAA,EAAAd,WAAA;IAAAkD,GAAA;IAAApD,KAAA,EA1BD,SAAOqD,cAAcA,CAAA,EAEF;MACjB,OAAO,IAAInD,WAAW,CAAC,CAAC;IAC1B;EAAA;AAAA,EATQoD,8BAAuB;AADpBpD,WAAW,CAIfqD,UAAU,GAAG,aAAa;AAAA,IAwCtBtD,WAAW,GAAAF,OAAA,CAAAE,WAAA,aAAAoE,sBAAA;EAAA,SAAApE,YAAA;IAAA,IAAAqE,MAAA;IAAA,IAAAhE,gBAAA,CAAAU,OAAA,QAAAf,WAAA;IAAA,SAAAsE,KAAA,GAAA1C,SAAA,CAAAC,MAAA,EAAAC,IAAA,OAAAC,KAAA,CAAAuC,KAAA,GAAAC,KAAA,MAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA;MAAAzC,IAAA,CAAAyC,KAAA,IAAA3C,SAAA,CAAA2C,KAAA;IAAA;IAAAF,MAAA,GAAA1D,UAAA,OAAAX,WAAA,KAAAiC,MAAA,CAAAH,IAAA;IAAAuC,MAAA,CAYtBnC,KAAK,GAAG,YAAkC;MACxC,IAAMC,aAAa,GAAGkC,MAAA,CAAKjC,gBAAgB,CAAC,CAAC;MAC7C,IAAAoC,qBAAA,GAA4BH,MAAA,CAAK/B,qBAAqB,CAAC,CAAC;QAAAmC,sBAAA,OAAArE,eAAA,CAAAW,OAAA,EAAAyD,qBAAA;QAAjDhC,SAAS,GAAAiC,sBAAA;QAAEhC,MAAM,GAAAgC,sBAAA;MACxB,IAAM/B,KAAK,GAAG2B,MAAA,CAAK1B,QAAQ,CAAC,CAAC;MAC7B,IAAMC,QAAQ,GAAGyB,MAAA,CAAKxB,SAAS;MAC/B,IAAMC,aAAa,GAAGuB,MAAA,CAAKvB,aAAa;MAExC,OAAO,YAAM;QACX,SAAS;;QACT,OAAO;UACLC,UAAU,EAAE;YACVC,SAAS,EAAE,CAAC;cAAEa,MAAM,EAAE1B,aAAa,CAACO,KAAK,EAAEF,SAAS,CAAC,CAAC,EAAEC,MAAM,CAAC;YAAE,CAAC;UACpE,CAAC;UACDK,aAAa,EAAAlD,MAAA,CAAAsD,MAAA;YACXF,SAAS,EAAE,CAAC;cAAEa,MAAM,EAAE;YAAE,CAAC;UAAC,GACvBf,aAAA,CACJ;UACDF,QAAA,EAAAA;QACF,CAAC;MACH,CAAC;IACH,CAAC;IAAA,OAAAyB,MAAA;EAAA;EAAA,IAAA5D,UAAA,CAAAM,OAAA,EAAAf,WAAA,EAAAoE,sBAAA;EAAA,WAAA9D,aAAA,CAAAS,OAAA,EAAAf,WAAA;IAAAmD,GAAA;IAAApD,KAAA,EA1BD,SAAOqD,cAAcA,CAAA,EAEF;MACjB,OAAO,IAAIpD,WAAW,CAAC,CAAC;IAC1B;EAAA;AAAA,EATQqD,8BAAuB;AADpBrD,WAAW,CAIfsD,UAAU,GAAG,aAAa", "ignoreList": []}