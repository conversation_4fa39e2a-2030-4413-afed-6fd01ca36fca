efba6fc8c3f1d7b7e10923ec7588ae8d
"use strict";

/* istanbul ignore next */
function cov_1dg0ksounf() {
  var path = "/Users/<USER>/projects/react-native/payment-module/src/data/models/provider-list/ProviderListRequest.ts";
  var hash = "165f110a7fda88908e0c8f0cc619fa7bc0e2c2c5";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/projects/react-native/payment-module/src/data/models/provider-list/ProviderListRequest.ts",
    statementMap: {
      "0": {
        start: {
          line: 3,
          column: 0
        },
        end: {
          line: 5,
          column: 3
        }
      }
    },
    fnMap: {},
    branchMap: {},
    s: {
      "0": 0
    },
    f: {},
    b: {},
    inputSourceMap: {
      version: 3,
      file: undefined,
      names: [],
      sourceRoot: undefined,
      sources: ["/Users/<USER>/projects/react-native/payment-module/src/data/models/provider-list/ProviderListRequest.ts"],
      sourcesContent: ["export interface ProviderListRequest {\n  code: string;\n}\n"],
      mappings: "",
      ignoreList: []
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "165f110a7fda88908e0c8f0cc619fa7bc0e2c2c5"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_1dg0ksounf = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_1dg0ksounf();
cov_1dg0ksounf().s[0]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6W10sInNvdXJjZXMiOlsiL1VzZXJzL3NvbnNhbXNldC9wcm9qZWN0cy9yZWFjdC1uYXRpdmUvcGF5bWVudC1tb2R1bGUvc3JjL2RhdGEvbW9kZWxzL3Byb3ZpZGVyLWxpc3QvUHJvdmlkZXJMaXN0UmVxdWVzdC50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgaW50ZXJmYWNlIFByb3ZpZGVyTGlzdFJlcXVlc3Qge1xuICBjb2RlOiBzdHJpbmc7XG59XG4iXSwibWFwcGluZ3MiOiIiLCJpZ25vcmVMaXN0IjpbXX0=