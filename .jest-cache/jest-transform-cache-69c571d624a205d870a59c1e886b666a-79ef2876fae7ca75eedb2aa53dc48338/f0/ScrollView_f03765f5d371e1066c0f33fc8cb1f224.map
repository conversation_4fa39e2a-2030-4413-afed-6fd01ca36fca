{"version": 3, "names": ["_interopRequireDefault", "require", "Object", "defineProperty", "exports", "value", "AnimatedScrollView", "_objectWithoutProperties2", "_react", "_interopRequireWildcard", "_reactNative", "_index", "_index2", "_excluded", "_getRequireWildcardCache", "e", "WeakMap", "r", "t", "__esModule", "default", "has", "get", "n", "__proto__", "a", "getOwnPropertyDescriptor", "u", "hasOwnProperty", "call", "i", "set", "_extends", "assign", "bind", "arguments", "length", "apply", "AnimatedScrollViewComponent", "createAnimatedComponent", "ScrollView", "forwardRef", "props", "ref", "scrollViewOffset", "restProps", "animatedRef", "useAnimatedRef", "useScrollViewOffset", "scrollEventThrottle", "React", "createElement"], "sources": ["../../../src/component/ScrollView.tsx"], "sourcesContent": [null], "mappings": "AAAA,YAAY;;AAAA,IAAAA,sBAAA,GAAAC,OAAA;AAAAC,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,KAAA;AAAA;AAAAD,OAAA,CAAAE,kBAAA;AAAA,IAAAC,yBAAA,GAAAP,sBAAA,CAAAC,OAAA;AAEZ,IAAAO,MAAA,GAAAC,uBAAA,CAAAR,OAAA;AAEA,IAAAS,YAAA,GAAAT,OAAA;AACA,IAAAU,MAAA,GAAAV,OAAA;AAGA,IAAAW,OAAA,GAAAX,OAAA;AAA6D,IAAAY,SAAA;AAAA,SAAAC,yBAAAC,CAAA,6BAAAC,OAAA,mBAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,yBAAAC,CAAA,WAAAA,CAAA,GAAAG,CAAA,GAAAD,CAAA,KAAAF,CAAA;AAAA,SAAAN,wBAAAM,CAAA,EAAAE,CAAA,SAAAA,CAAA,IAAAF,CAAA,IAAAA,CAAA,CAAAI,UAAA,SAAAJ,CAAA,eAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,WAAAK,OAAA,EAAAL,CAAA,QAAAG,CAAA,GAAAJ,wBAAA,CAAAG,CAAA,OAAAC,CAAA,IAAAA,CAAA,CAAAG,GAAA,CAAAN,CAAA,UAAAG,CAAA,CAAAI,GAAA,CAAAP,CAAA,OAAAQ,CAAA,KAAAC,SAAA,UAAAC,CAAA,GAAAvB,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAwB,wBAAA,WAAAC,CAAA,IAAAZ,CAAA,oBAAAY,CAAA,OAAAC,cAAA,CAAAC,IAAA,CAAAd,CAAA,EAAAY,CAAA,SAAAG,CAAA,GAAAL,CAAA,GAAAvB,MAAA,CAAAwB,wBAAA,CAAAX,CAAA,EAAAY,CAAA,UAAAG,CAAA,KAAAA,CAAA,CAAAR,GAAA,IAAAQ,CAAA,CAAAC,GAAA,IAAA7B,MAAA,CAAAC,cAAA,CAAAoB,CAAA,EAAAI,CAAA,EAAAG,CAAA,IAAAP,CAAA,CAAAI,CAAA,IAAAZ,CAAA,CAAAY,CAAA,YAAAJ,CAAA,CAAAH,OAAA,GAAAL,CAAA,EAAAG,CAAA,IAAAA,CAAA,CAAAa,GAAA,CAAAhB,CAAA,EAAAQ,CAAA,GAAAA,CAAA;AARhD,SAAAS,SAAA;EAAA,OAAAA,QAAA,GAAA9B,MAAA,CAAA+B,MAAA,GAAA/B,MAAA,CAAA+B,MAAA,CAAAC,IAAA,eAAAX,CAAA;IAAA,SAAAR,CAAA,MAAAA,CAAA,GAAAoB,SAAA,CAAAC,MAAA,EAAArB,CAAA;MAAA,IAAAG,CAAA,GAAAiB,SAAA,CAAApB,CAAA;MAAA,SAAAE,CAAA,IAAAC,CAAA,OAAAU,cAAA,CAAAC,IAAA,CAAAX,CAAA,EAAAD,CAAA,MAAAM,CAAA,CAAAN,CAAA,IAAAC,CAAA,CAAAD,CAAA;IAAA;IAAA,OAAAM,CAAA;EAAA,GAAAS,QAAA,CAAAK,KAAA,OAAAF,SAAA;AAAA;AAsBb,IAAMG,2BAA2B,GAAG,IAAAC,8BAAuB,EAACC,uBAAU,CAAC;AAEhE,IAAMlC,kBAAkB,GAAAF,OAAA,CAAAE,kBAAA,GAAG,IAAAmC,iBAAU,EAC1C,UAACC,KAA8B,EAAEC,GAAqC,EAAK;EACzE,IAAQC,gBAAgB,GAAmBF,KAAK,CAAxCE,gBAAgB;IAAKC,SAAA,OAAAtC,yBAAA,CAAAa,OAAA,EAAcsB,KAAK,EAAA7B,SAAA;EAChD,IAAMiC,WAAW,GACfH,GAAG,KAAK,IAAI,GAER,IAAAI,sBAAc,EAAa,CAAC,GAC5BJ,GAC8B;EAEpC,IAAIC,gBAAgB,EAAE;IAEpB,IAAAI,2BAAmB,EAACF,WAAW,EAAEF,gBAAgB,CAAC;EACpD;EAMA,IAAI,EAAE,qBAAqB,IAAIC,SAAS,CAAC,EAAE;IACzCA,SAAS,CAACI,mBAAmB,GAAG,CAAC;EACnC;EAEA,OAAOC,cAAA,CAAAC,aAAA,CAACb,2BAA2B,EAAAN,QAAA;IAACW,GAAG,EAAEG;EAAY,GAAKD,SAAS,CAAG,CAAC;AACzE,CACF,CAAC", "ignoreList": []}