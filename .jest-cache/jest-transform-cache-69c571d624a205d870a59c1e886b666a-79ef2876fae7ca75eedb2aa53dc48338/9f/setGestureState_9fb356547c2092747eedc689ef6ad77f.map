{"version": 3, "names": ["Object", "defineProperty", "exports", "value", "setGestureState", "_PlatformChecker", "require", "_index", "setGestureStateNative", "handlerTag", "newState", "_WORKLET", "logger", "warn", "global", "_setGestureState", "setGestureStateJest", "setGestureStateChromeDebugger", "setGestureStateDefault", "shouldBeUseWeb", "isJest", "isChromeDebugger"], "sources": ["../../../src/platformFunctions/setGestureState.ts"], "sourcesContent": [null], "mappings": "AAAA,YAAY;;AAAAA,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,KAAA;AAAA;AAAAD,OAAA,CAAAE,eAAA;AAEZ,IAAAC,gBAAA,GAAAC,OAAA;AACA,IAAAC,MAAA,GAAAD,OAAA;AAIO,IAAIF,eAAgC;AAE3C,SAASI,qBAAqBA,CAACC,UAAkB,EAAEC,QAAgB,EAAE;EACnE,SAAS;;EACT,IAAI,CAACC,QAAQ,EAAE;IACbC,aAAM,CAACC,IAAI,CAAC,0DAA0D,CAAC;IACvE;EACF;EACAC,MAAM,CAACC,gBAAgB,CAACN,UAAU,EAAEC,QAAQ,CAAC;AAC/C;AAEA,SAASM,mBAAmBA,CAAA,EAAG;EAC7BJ,aAAM,CAACC,IAAI,CAAC,6CAA6C,CAAC;AAC5D;AAEA,SAASI,6BAA6BA,CAAA,EAAG;EACvCL,aAAM,CAACC,IAAI,CAAC,wDAAwD,CAAC;AACvE;AAEA,SAASK,sBAAsBA,CAAA,EAAG;EAChCN,aAAM,CAACC,IAAI,CAAC,2DAA2D,CAAC;AAC1E;AAEA,IAAI,CAAC,IAAAM,+BAAc,EAAC,CAAC,EAAE;EACrBjB,OAAA,CAAAE,eAAA,GAAAA,eAAe,GAAGI,qBAAqB;AACzC,CAAC,MAAM,IAAI,IAAAY,uBAAM,EAAC,CAAC,EAAE;EACnBlB,OAAA,CAAAE,eAAA,GAAAA,eAAe,GAAGY,mBAAmB;AACvC,CAAC,MAAM,IAAI,IAAAK,iCAAgB,EAAC,CAAC,EAAE;EAC7BnB,OAAA,CAAAE,eAAA,GAAAA,eAAe,GAAGa,6BAA6B;AACjD,CAAC,MAAM;EACLf,OAAA,CAAAE,eAAA,GAAAA,eAAe,GAAGc,sBAAsB;AAC1C", "ignoreList": []}