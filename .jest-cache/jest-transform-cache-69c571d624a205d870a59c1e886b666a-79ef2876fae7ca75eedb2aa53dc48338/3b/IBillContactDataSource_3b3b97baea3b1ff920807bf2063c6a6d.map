{"version": 3, "names": [], "sources": ["/Users/<USER>/projects/react-native/payment-module/src/data/datasources/IBillContactDataSource.ts"], "sourcesContent": ["import {GetMyBillHistoryListResponse} from '../models/get-my-bill-history-list/GetMyBillHistoryListResponse';\nimport {GetMyBillHistoryListRequest} from '../models/get-my-bill-history-list/GetMyBillHistoryListRequest';\nimport {GetMyBillContactRecentListResponse} from '../models/get-my-bill-contact-recent-list/GetMyBillContactRecentListResponse';\nimport {GetMyBillContactRecentListRequest} from '../models/get-my-bill-contact-recent-list/GetMyBillContactRecentListRequest';\nimport {MyBillContactListResponse} from '../models/my-bill-contact-list/MyBillContactListResponse';\nimport {EditBillContactResponse} from '../models/edit-bill-contact/EditBillContactResponse';\nimport {EditBillContactRequest} from '../models/edit-bill-contact/EditBillContactRequest';\nimport {DeleteBillContactResponse} from '../models/delete-bill-contact/DeleteBillContactResponse';\nimport {DeleteBillContactRequest} from '../models/delete-bill-contact/DeleteBillContactRequest';\nimport {BaseResponse} from '../../core/BaseResponse';\nimport {SaveBillContactResponse} from '../models/save-bill-contact/SaveBillContactResponse';\nimport {SaveBillContactRequest} from '../models/save-bill-contact/SaveBillContactRequest';\n\nexport interface IBillContactDataSource {\n  saveBillContact(request: SaveBillContactRequest): Promise<BaseResponse<SaveBillContactResponse>>;\n  deleteBillContact(request: DeleteBillContactRequest): Promise<BaseResponse<DeleteBillContactResponse>>;\n  editBillContact(request: EditBillContactRequest): Promise<BaseResponse<EditBillContactResponse>>;\n  myBillContactList(): Promise<BaseResponse<MyBillContactListResponse>>;\n  getMyBillContactRecentList(\n    request: GetMyBillContactRecentListRequest,\n  ): Promise<BaseResponse<GetMyBillContactRecentListResponse>>;\n  getMyBillHistoryList(request: GetMyBillHistoryListRequest): Promise<BaseResponse<GetMyBillHistoryListResponse>>;\n}\n"], "mappings": "", "ignoreList": []}