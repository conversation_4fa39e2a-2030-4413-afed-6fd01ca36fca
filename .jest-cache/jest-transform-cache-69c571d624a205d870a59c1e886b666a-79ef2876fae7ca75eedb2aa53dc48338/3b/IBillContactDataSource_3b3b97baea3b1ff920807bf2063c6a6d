99ea893f5e07bd8ec543f762e490c415
"use strict";

/* istanbul ignore next */
function cov_1mdxbi4t1w() {
  var path = "/Users/<USER>/projects/react-native/payment-module/src/data/datasources/IBillContactDataSource.ts";
  var hash = "42c61dcf696f46f68c842c2e2667077c2a33a05b";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/projects/react-native/payment-module/src/data/datasources/IBillContactDataSource.ts",
    statementMap: {
      "0": {
        start: {
          line: 3,
          column: 0
        },
        end: {
          line: 5,
          column: 3
        }
      }
    },
    fnMap: {},
    branchMap: {},
    s: {
      "0": 0
    },
    f: {},
    b: {},
    inputSourceMap: {
      version: 3,
      file: undefined,
      names: [],
      sourceRoot: undefined,
      sources: ["/Users/<USER>/projects/react-native/payment-module/src/data/datasources/IBillContactDataSource.ts"],
      sourcesContent: ["import {GetMyBillHistoryListResponse} from '../models/get-my-bill-history-list/GetMyBillHistoryListResponse';\nimport {GetMyBillHistoryListRequest} from '../models/get-my-bill-history-list/GetMyBillHistoryListRequest';\nimport {GetMyBillContactRecentListResponse} from '../models/get-my-bill-contact-recent-list/GetMyBillContactRecentListResponse';\nimport {GetMyBillContactRecentListRequest} from '../models/get-my-bill-contact-recent-list/GetMyBillContactRecentListRequest';\nimport {MyBillContactListResponse} from '../models/my-bill-contact-list/MyBillContactListResponse';\nimport {EditBillContactResponse} from '../models/edit-bill-contact/EditBillContactResponse';\nimport {EditBillContactRequest} from '../models/edit-bill-contact/EditBillContactRequest';\nimport {DeleteBillContactResponse} from '../models/delete-bill-contact/DeleteBillContactResponse';\nimport {DeleteBillContactRequest} from '../models/delete-bill-contact/DeleteBillContactRequest';\nimport {BaseResponse} from '../../core/BaseResponse';\nimport {SaveBillContactResponse} from '../models/save-bill-contact/SaveBillContactResponse';\nimport {SaveBillContactRequest} from '../models/save-bill-contact/SaveBillContactRequest';\n\nexport interface IBillContactDataSource {\n  saveBillContact(request: SaveBillContactRequest): Promise<BaseResponse<SaveBillContactResponse>>;\n  deleteBillContact(request: DeleteBillContactRequest): Promise<BaseResponse<DeleteBillContactResponse>>;\n  editBillContact(request: EditBillContactRequest): Promise<BaseResponse<EditBillContactResponse>>;\n  myBillContactList(): Promise<BaseResponse<MyBillContactListResponse>>;\n  getMyBillContactRecentList(\n    request: GetMyBillContactRecentListRequest,\n  ): Promise<BaseResponse<GetMyBillContactRecentListResponse>>;\n  getMyBillHistoryList(request: GetMyBillHistoryListRequest): Promise<BaseResponse<GetMyBillHistoryListResponse>>;\n}\n"],
      mappings: "",
      ignoreList: []
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "42c61dcf696f46f68c842c2e2667077c2a33a05b"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_1mdxbi4t1w = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_1mdxbi4t1w();
cov_1mdxbi4t1w().s[0]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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