{"version": 3, "names": ["cov_1tu3azvkwe", "actualCoverage", "PathResolver_1", "s", "require", "ResponseHandler_1", "MSBCustomError_1", "ArrangementRemoteDataSource", "f", "httpClient", "_classCallCheck2", "default", "_createClass2", "key", "value", "_sourceAccountList", "_asyncToGenerator2", "request", "url", "PathResolver", "arrangement", "sourceAccountList", "response", "post", "handleResponse", "error", "CustomError", "b", "createError", "_x", "apply", "arguments", "exports"], "sources": ["/Users/<USER>/projects/react-native/payment-module/src/data/datasources/remote/ArrangementRemoteDataSource.ts"], "sourcesContent": ["import {BaseResponse} from '../../../core/BaseResponse';\nimport {PathResolver} from '../../../utils/PathResolver';\nimport {IHttpClient} from 'msb-host-shared-module';\nimport {handleResponse} from '../../../utils/ResponseHandler';\nimport {SourceAccountListResponse} from '../../models/source-account-list/SourceAccountListResponse';\nimport {SourceAccountListRequest} from '../../models/source-account-list/SourceAccountListRequest';\nimport {IArrangementDataSource} from '../IArrangementDataSource';\nimport {createError, CustomError} from '../../../core/MSBCustomError';\n\nexport class ArrangementRemoteDataSource implements IArrangementDataSource {\n  constructor(private httpClient: IHttpClient) {}\n\n  async sourceAccountList(request: SourceAccountListRequest): Promise<BaseResponse<SourceAccountListResponse>> {\n    try {\n      const url = PathResolver.arrangement.sourceAccountList();\n      const response = await this.httpClient.post(url, request);\n      return handleResponse(response);\n    } catch (error: any) {\n      if (error instanceof CustomError) {\n        throw error;\n      }\n      throw createError();\n    }\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAU6C;IAAAA,cAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,cAAA;;;;;;;;;;;;;;;;;;;;;AAT7C,IAAAE,cAAA;AAAA;AAAA,CAAAF,cAAA,GAAAG,CAAA,OAAAC,OAAA;AAEA,IAAAC,iBAAA;AAAA;AAAA,CAAAL,cAAA,GAAAG,CAAA,OAAAC,OAAA;AAIA,IAAAE,gBAAA;AAAA;AAAA,CAAAN,cAAA,GAAAG,CAAA,OAAAC,OAAA;AAAsE,IAEzDG,2BAA2B;AAAA;AAAA,CAAAP,cAAA,GAAAG,CAAA;EAAA;EAAAH,cAAA,GAAAQ,CAAA;EACtC,SAAAD,4BAAoBE,UAAuB;IAAA;IAAAT,cAAA,GAAAQ,CAAA;IAAAR,cAAA,GAAAG,CAAA;IAAA,IAAAO,gBAAA,CAAAC,OAAA,QAAAJ,2BAAA;IAAA;IAAAP,cAAA,GAAAG,CAAA;IAAvB,KAAAM,UAAU,GAAVA,UAAU;EAAgB;EAAA;EAAAT,cAAA,GAAAG,CAAA;EAAC,WAAAS,aAAA,CAAAD,OAAA,EAAAJ,2BAAA;IAAAM,GAAA;IAAAC,KAAA;MAAA;MAAAd,cAAA,GAAAQ,CAAA;MAAA,IAAAO,kBAAA;MAAA;MAAA,CAAAf,cAAA,GAAAG,CAAA,YAAAa,kBAAA,CAAAL,OAAA,EAE/C,WAAwBM,OAAiC;QAAA;QAAAjB,cAAA,GAAAQ,CAAA;QAAAR,cAAA,GAAAG,CAAA;QACvD,IAAI;UACF,IAAMe,GAAG;UAAA;UAAA,CAAAlB,cAAA,GAAAG,CAAA,QAAGD,cAAA,CAAAiB,YAAY,CAACC,WAAW,CAACC,iBAAiB,EAAE;UACxD,IAAMC,QAAQ;UAAA;UAAA,CAAAtB,cAAA,GAAAG,CAAA,cAAS,IAAI,CAACM,UAAU,CAACc,IAAI,CAACL,GAAG,EAAED,OAAO,CAAC;UAAA;UAAAjB,cAAA,GAAAG,CAAA;UACzD,OAAO,IAAAE,iBAAA,CAAAmB,cAAc,EAACF,QAAQ,CAAC;QACjC,CAAC,CAAC,OAAOG,KAAU,EAAE;UAAA;UAAAzB,cAAA,GAAAG,CAAA;UACnB,IAAIsB,KAAK,YAAYnB,gBAAA,CAAAoB,WAAW,EAAE;YAAA;YAAA1B,cAAA,GAAA2B,CAAA;YAAA3B,cAAA,GAAAG,CAAA;YAChC,MAAMsB,KAAK;UACb;UAAA;UAAA;YAAAzB,cAAA,GAAA2B,CAAA;UAAA;UAAA3B,cAAA,GAAAG,CAAA;UACA,MAAM,IAAAG,gBAAA,CAAAsB,WAAW,GAAE;QACrB;MACF,CAAC;MAAA,SAXKP,iBAAiBA,CAAAQ,EAAA;QAAA;QAAA7B,cAAA,GAAAQ,CAAA;QAAAR,cAAA,GAAAG,CAAA;QAAA,OAAAY,kBAAA,CAAAe,KAAA,OAAAC,SAAA;MAAA;MAAA;MAAA/B,cAAA,GAAAG,CAAA;MAAA,OAAjBkB,iBAAiB;IAAA;EAAA;AAAA;AAAA;AAAArB,cAAA,GAAAG,CAAA;AAHzB6B,OAAA,CAAAzB,2BAAA,GAAAA,2BAAA", "ignoreList": []}