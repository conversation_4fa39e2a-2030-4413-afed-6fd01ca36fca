{"version": 3, "names": ["Object", "defineProperty", "exports", "value", "BounceOutData", "BounceOut", "BounceInData", "BounceIn", "_animation<PERSON><PERSON>er", "require", "DEFAULT_BOUNCE_TIME", "name", "style", "transform", "scale", "duration", "BounceInRight", "translateX", "BounceInLeft", "BounceInUp", "translateY", "BounceInDown", "BounceOutRight", "BounceOutLeft", "BounceOutUp", "BounceOutDown", "convertAnimationObjectToKeyframes"], "sources": ["../../../../../src/layoutReanimation/web/animation/Bounce.web.ts"], "sourcesContent": [null], "mappings": "AAAA,YAAY;;AAAAA,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,KAAA;AAAA;AAAAD,OAAA,CAAAE,aAAA,GAAAF,OAAA,CAAAG,SAAA,GAAAH,OAAA,CAAAI,YAAA,GAAAJ,OAAA,CAAAK,QAAA;AACZ,IAAAC,gBAAA,GAAAC,OAAA;AAEA,IAAMC,mBAAmB,GAAG,GAAG;AAExB,IAAMJ,YAAY,GAAAJ,OAAA,CAAAI,YAAA,GAAG;EAC1BC,QAAQ,EAAE;IACRI,IAAI,EAAE,UAAU;IAChBC,KAAK,EAAE;MACL,CAAC,EAAE;QAAEC,SAAS,EAAE,CAAC;UAAEC,KAAK,EAAE;QAAE,CAAC;MAAE,CAAC;MAChC,EAAE,EAAE;QAAED,SAAS,EAAE,CAAC;UAAEC,KAAK,EAAE;QAAI,CAAC;MAAE,CAAC;MACnC,EAAE,EAAE;QAAED,SAAS,EAAE,CAAC;UAAEC,KAAK,EAAE;QAAI,CAAC;MAAE,CAAC;MACnC,EAAE,EAAE;QAAED,SAAS,EAAE,CAAC;UAAEC,KAAK,EAAE;QAAI,CAAC;MAAE,CAAC;MACnC,GAAG,EAAE;QAAED,SAAS,EAAE,CAAC;UAAEC,KAAK,EAAE;QAAE,CAAC;MAAE;IACnC,CAAC;IACDC,QAAQ,EAAEL;EACZ,CAAC;EAEDM,aAAa,EAAE;IACbL,IAAI,EAAE,eAAe;IACrBC,KAAK,EAAE;MACL,CAAC,EAAE;QAAEC,SAAS,EAAE,CAAC;UAAEI,UAAU,EAAE;QAAQ,CAAC;MAAE,CAAC;MAC3C,EAAE,EAAE;QAAEJ,SAAS,EAAE,CAAC;UAAEI,UAAU,EAAE;QAAQ,CAAC;MAAE,CAAC;MAC5C,EAAE,EAAE;QAAEJ,SAAS,EAAE,CAAC;UAAEI,UAAU,EAAE;QAAO,CAAC;MAAE,CAAC;MAC3C,EAAE,EAAE;QAAEJ,SAAS,EAAE,CAAC;UAAEI,UAAU,EAAE;QAAQ,CAAC;MAAE,CAAC;MAC5C,GAAG,EAAE;QAAEJ,SAAS,EAAE,CAAC;UAAEI,UAAU,EAAE;QAAM,CAAC;MAAE;IAC5C,CAAC;IACDF,QAAQ,EAAEL;EACZ,CAAC;EAEDQ,YAAY,EAAE;IACZP,IAAI,EAAE,cAAc;IACpBC,KAAK,EAAE;MACL,CAAC,EAAE;QAAEC,SAAS,EAAE,CAAC;UAAEI,UAAU,EAAE;QAAS,CAAC;MAAE,CAAC;MAC5C,EAAE,EAAE;QAAEJ,SAAS,EAAE,CAAC;UAAEI,UAAU,EAAE;QAAO,CAAC;MAAE,CAAC;MAC3C,EAAE,EAAE;QAAEJ,SAAS,EAAE,CAAC;UAAEI,UAAU,EAAE;QAAQ,CAAC;MAAE,CAAC;MAC5C,EAAE,EAAE;QAAEJ,SAAS,EAAE,CAAC;UAAEI,UAAU,EAAE;QAAO,CAAC;MAAE,CAAC;MAC3C,GAAG,EAAE;QAAEJ,SAAS,EAAE,CAAC;UAAEI,UAAU,EAAE;QAAM,CAAC;MAAE;IAC5C,CAAC;IACDF,QAAQ,EAAEL;EACZ,CAAC;EAEDS,UAAU,EAAE;IACVR,IAAI,EAAE,YAAY;IAClBC,KAAK,EAAE;MACL,CAAC,EAAE;QAAEC,SAAS,EAAE,CAAC;UAAEO,UAAU,EAAE;QAAS,CAAC;MAAE,CAAC;MAC5C,EAAE,EAAE;QAAEP,SAAS,EAAE,CAAC;UAAEO,UAAU,EAAE;QAAO,CAAC;MAAE,CAAC;MAC3C,EAAE,EAAE;QAAEP,SAAS,EAAE,CAAC;UAAEO,UAAU,EAAE;QAAQ,CAAC;MAAE,CAAC;MAC5C,EAAE,EAAE;QAAEP,SAAS,EAAE,CAAC;UAAEO,UAAU,EAAE;QAAO,CAAC;MAAE,CAAC;MAC3C,GAAG,EAAE;QAAEP,SAAS,EAAE,CAAC;UAAEO,UAAU,EAAE;QAAM,CAAC;MAAE;IAC5C,CAAC;IACDL,QAAQ,EAAEL;EACZ,CAAC;EAEDW,YAAY,EAAE;IACZV,IAAI,EAAE,cAAc;IACpBC,KAAK,EAAE;MACL,CAAC,EAAE;QAAEC,SAAS,EAAE,CAAC;UAAEO,UAAU,EAAE;QAAQ,CAAC;MAAE,CAAC;MAC3C,EAAE,EAAE;QAAEP,SAAS,EAAE,CAAC;UAAEO,UAAU,EAAE;QAAQ,CAAC;MAAE,CAAC;MAC5C,EAAE,EAAE;QAAEP,SAAS,EAAE,CAAC;UAAEO,UAAU,EAAE;QAAO,CAAC;MAAE,CAAC;MAC3C,EAAE,EAAE;QAAEP,SAAS,EAAE,CAAC;UAAEO,UAAU,EAAE;QAAQ,CAAC;MAAE,CAAC;MAC5C,GAAG,EAAE;QAAEP,SAAS,EAAE,CAAC;UAAEO,UAAU,EAAE;QAAM,CAAC;MAAE;IAC5C,CAAC;IACDL,QAAQ,EAAEL;EACZ;AACF,CAAC;AAEM,IAAMN,aAAa,GAAAF,OAAA,CAAAE,aAAA,GAAG;EAC3BC,SAAS,EAAE;IACTM,IAAI,EAAE,WAAW;IACjBC,KAAK,EAAE;MACL,CAAC,EAAE;QAAEC,SAAS,EAAE,CAAC;UAAEC,KAAK,EAAE;QAAE,CAAC;MAAE,CAAC;MAChC,EAAE,EAAE;QAAED,SAAS,EAAE,CAAC;UAAEC,KAAK,EAAE;QAAI,CAAC;MAAE,CAAC;MACnC,EAAE,EAAE;QAAED,SAAS,EAAE,CAAC;UAAEC,KAAK,EAAE;QAAI,CAAC;MAAE,CAAC;MACnC,EAAE,EAAE;QAAED,SAAS,EAAE,CAAC;UAAEC,KAAK,EAAE;QAAI,CAAC;MAAE,CAAC;MACnC,GAAG,EAAE;QAAED,SAAS,EAAE,CAAC;UAAEC,KAAK,EAAE;QAAI,CAAC;MAAE;IACrC,CAAC;IACDC,QAAQ,EAAEL;EACZ,CAAC;EAEDY,cAAc,EAAE;IACdX,IAAI,EAAE,gBAAgB;IACtBC,KAAK,EAAE;MACL,CAAC,EAAE;QAAEC,SAAS,EAAE,CAAC;UAAEI,UAAU,EAAE;QAAM,CAAC;MAAE,CAAC;MACzC,EAAE,EAAE;QAAEJ,SAAS,EAAE,CAAC;UAAEI,UAAU,EAAE;QAAQ,CAAC;MAAE,CAAC;MAC5C,EAAE,EAAE;QAAEJ,SAAS,EAAE,CAAC;UAAEI,UAAU,EAAE;QAAO,CAAC;MAAE,CAAC;MAC3C,EAAE,EAAE;QAAEJ,SAAS,EAAE,CAAC;UAAEI,UAAU,EAAE;QAAQ,CAAC;MAAE,CAAC;MAC5C,GAAG,EAAE;QAAEJ,SAAS,EAAE,CAAC;UAAEI,UAAU,EAAE;QAAQ,CAAC;MAAE;IAC9C,CAAC;IACDF,QAAQ,EAAEL;EACZ,CAAC;EAEDa,aAAa,EAAE;IACbZ,IAAI,EAAE,eAAe;IACrBC,KAAK,EAAE;MACL,CAAC,EAAE;QAAEC,SAAS,EAAE,CAAC;UAAEI,UAAU,EAAE;QAAM,CAAC;MAAE,CAAC;MACzC,EAAE,EAAE;QAAEJ,SAAS,EAAE,CAAC;UAAEI,UAAU,EAAE;QAAO,CAAC;MAAE,CAAC;MAC3C,EAAE,EAAE;QAAEJ,SAAS,EAAE,CAAC;UAAEI,UAAU,EAAE;QAAQ,CAAC;MAAE,CAAC;MAC5C,EAAE,EAAE;QAAEJ,SAAS,EAAE,CAAC;UAAEI,UAAU,EAAE;QAAO,CAAC;MAAE,CAAC;MAC3C,GAAG,EAAE;QAAEJ,SAAS,EAAE,CAAC;UAAEI,UAAU,EAAE;QAAS,CAAC;MAAE;IAC/C,CAAC;IACDF,QAAQ,EAAEL;EACZ,CAAC;EAEDc,WAAW,EAAE;IACXb,IAAI,EAAE,aAAa;IACnBC,KAAK,EAAE;MACL,CAAC,EAAE;QAAEC,SAAS,EAAE,CAAC;UAAEO,UAAU,EAAE;QAAM,CAAC;MAAE,CAAC;MACzC,EAAE,EAAE;QAAEP,SAAS,EAAE,CAAC;UAAEO,UAAU,EAAE;QAAO,CAAC;MAAE,CAAC;MAC3C,EAAE,EAAE;QAAEP,SAAS,EAAE,CAAC;UAAEO,UAAU,EAAE;QAAQ,CAAC;MAAE,CAAC;MAC5C,EAAE,EAAE;QAAEP,SAAS,EAAE,CAAC;UAAEO,UAAU,EAAE;QAAO,CAAC;MAAE,CAAC;MAC3C,GAAG,EAAE;QAAEP,SAAS,EAAE,CAAC;UAAEO,UAAU,EAAE;QAAS,CAAC;MAAE;IAC/C,CAAC;IACDL,QAAQ,EAAEL;EACZ,CAAC;EAEDe,aAAa,EAAE;IACbd,IAAI,EAAE,eAAe;IACrBC,KAAK,EAAE;MACL,CAAC,EAAE;QAAEC,SAAS,EAAE,CAAC;UAAEO,UAAU,EAAE;QAAM,CAAC;MAAE,CAAC;MACzC,EAAE,EAAE;QAAEP,SAAS,EAAE,CAAC;UAAEO,UAAU,EAAE;QAAQ,CAAC;MAAE,CAAC;MAC5C,EAAE,EAAE;QAAEP,SAAS,EAAE,CAAC;UAAEO,UAAU,EAAE;QAAO,CAAC;MAAE,CAAC;MAC3C,EAAE,EAAE;QAAEP,SAAS,EAAE,CAAC;UAAEO,UAAU,EAAE;QAAQ,CAAC;MAAE,CAAC;MAC5C,GAAG,EAAE;QAAEP,SAAS,EAAE,CAAC;UAAEO,UAAU,EAAE;QAAQ,CAAC;MAAE;IAC9C,CAAC;IACDL,QAAQ,EAAEL;EACZ;AACF,CAAC;AAEM,IAAMH,QAAQ,GAAAL,OAAA,CAAAK,QAAA,GAAG;EACtBA,QAAQ,EAAE;IACRK,KAAK,EAAE,IAAAc,kDAAiC,EAACpB,YAAY,CAACC,QAAQ,CAAC;IAC/DQ,QAAQ,EAAET,YAAY,CAACC,QAAQ,CAACQ;EAClC,CAAC;EACDC,aAAa,EAAE;IACbJ,KAAK,EAAE,IAAAc,kDAAiC,EAACpB,YAAY,CAACU,aAAa,CAAC;IACpED,QAAQ,EAAET,YAAY,CAACU,aAAa,CAACD;EACvC,CAAC;EACDG,YAAY,EAAE;IACZN,KAAK,EAAE,IAAAc,kDAAiC,EAACpB,YAAY,CAACY,YAAY,CAAC;IACnEH,QAAQ,EAAET,YAAY,CAACY,YAAY,CAACH;EACtC,CAAC;EACDI,UAAU,EAAE;IACVP,KAAK,EAAE,IAAAc,kDAAiC,EAACpB,YAAY,CAACa,UAAU,CAAC;IACjEJ,QAAQ,EAAET,YAAY,CAACa,UAAU,CAACJ;EACpC,CAAC;EACDM,YAAY,EAAE;IACZT,KAAK,EAAE,IAAAc,kDAAiC,EAACpB,YAAY,CAACe,YAAY,CAAC;IACnEN,QAAQ,EAAET,YAAY,CAACe,YAAY,CAACN;EACtC;AACF,CAAC;AAEM,IAAMV,SAAS,GAAAH,OAAA,CAAAG,SAAA,GAAG;EACvBA,SAAS,EAAE;IACTO,KAAK,EAAE,IAAAc,kDAAiC,EAACtB,aAAa,CAACC,SAAS,CAAC;IACjEU,QAAQ,EAAEX,aAAa,CAACC,SAAS,CAACU;EACpC,CAAC;EACDO,cAAc,EAAE;IACdV,KAAK,EAAE,IAAAc,kDAAiC,EAACtB,aAAa,CAACkB,cAAc,CAAC;IACtEP,QAAQ,EAAEX,aAAa,CAACkB,cAAc,CAACP;EACzC,CAAC;EACDQ,aAAa,EAAE;IACbX,KAAK,EAAE,IAAAc,kDAAiC,EAACtB,aAAa,CAACmB,aAAa,CAAC;IACrER,QAAQ,EAAEX,aAAa,CAACmB,aAAa,CAACR;EACxC,CAAC;EACDS,WAAW,EAAE;IACXZ,KAAK,EAAE,IAAAc,kDAAiC,EAACtB,aAAa,CAACoB,WAAW,CAAC;IACnET,QAAQ,EAAEX,aAAa,CAACoB,WAAW,CAACT;EACtC,CAAC;EACDU,aAAa,EAAE;IACbb,KAAK,EAAE,IAAAc,kDAAiC,EAACtB,aAAa,CAACqB,aAAa,CAAC;IACrEV,QAAQ,EAAEX,aAAa,CAACqB,aAAa,CAACV;EACxC;AACF,CAAC", "ignoreList": []}