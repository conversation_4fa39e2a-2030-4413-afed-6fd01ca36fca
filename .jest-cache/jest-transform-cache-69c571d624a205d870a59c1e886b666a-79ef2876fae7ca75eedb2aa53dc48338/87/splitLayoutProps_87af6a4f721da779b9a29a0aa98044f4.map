{"version": 3, "names": ["splitLayoutProps", "props", "outer", "inner", "prop", "Object", "keys"], "sources": ["splitLayoutProps.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @flow strict-local\n * @format\n */\n\nimport type {____ViewStyle_Internal} from './StyleSheetTypes';\n\nexport default function splitLayoutProps(props: ?____ViewStyle_Internal): {\n  outer: ?____ViewStyle_Internal,\n  inner: ?____ViewStyle_Internal,\n} {\n  let outer: ?____ViewStyle_Internal = null;\n  let inner: ?____ViewStyle_Internal = null;\n\n  if (props != null) {\n    // $FlowIgnore[incompatible-exact] Will contain a subset of keys from `props`.\n    outer = {};\n    // $FlowIgnore[incompatible-exact] Will contain a subset of keys from `props`.\n    inner = {};\n\n    for (const prop of Object.keys(props)) {\n      switch (prop) {\n        case 'margin':\n        case 'marginHorizontal':\n        case 'marginVertical':\n        case 'marginBottom':\n        case 'marginTop':\n        case 'marginLeft':\n        case 'marginRight':\n        case 'flex':\n        case 'flexGrow':\n        case 'flexShrink':\n        case 'flexBasis':\n        case 'alignSelf':\n        case 'height':\n        case 'minHeight':\n        case 'maxHeight':\n        case 'width':\n        case 'minWidth':\n        case 'maxWidth':\n        case 'position':\n        case 'left':\n        case 'right':\n        case 'bottom':\n        case 'top':\n        case 'transform':\n        case 'transformOrigin':\n        case 'rowGap':\n        case 'columnGap':\n        case 'gap':\n          // $FlowFixMe[cannot-write]\n          // $FlowFixMe[incompatible-use]\n          // $FlowFixMe[prop-missing]\n          outer[prop] = props[prop];\n          break;\n        default:\n          // $FlowFixMe[cannot-write]\n          // $FlowFixMe[incompatible-use]\n          // $FlowFixMe[prop-missing]\n          inner[prop] = props[prop];\n          break;\n      }\n    }\n  }\n\n  return {outer, inner};\n}\n"], "mappings": ";;;;AAYe,SAASA,gBAAgBA,CAACC,KAA8B,EAGrE;EACA,IAAIC,KAA8B,GAAG,IAAI;EACzC,IAAIC,KAA8B,GAAG,IAAI;EAEzC,IAAIF,KAAK,IAAI,IAAI,EAAE;IAEjBC,KAAK,GAAG,CAAC,CAAC;IAEVC,KAAK,GAAG,CAAC,CAAC;IAEV,KAAK,IAAMC,IAAI,IAAIC,MAAM,CAACC,IAAI,CAACL,KAAK,CAAC,EAAE;MACrC,QAAQG,IAAI;QACV,KAAK,QAAQ;QACb,KAAK,kBAAkB;QACvB,KAAK,gBAAgB;QACrB,KAAK,cAAc;QACnB,KAAK,WAAW;QAChB,KAAK,YAAY;QACjB,KAAK,aAAa;QAClB,KAAK,MAAM;QACX,KAAK,UAAU;QACf,KAAK,YAAY;QACjB,KAAK,WAAW;QAChB,KAAK,WAAW;QAChB,KAAK,QAAQ;QACb,KAAK,WAAW;QAChB,KAAK,WAAW;QAChB,KAAK,OAAO;QACZ,KAAK,UAAU;QACf,KAAK,UAAU;QACf,KAAK,UAAU;QACf,KAAK,MAAM;QACX,KAAK,OAAO;QACZ,KAAK,QAAQ;QACb,KAAK,KAAK;QACV,KAAK,WAAW;QAChB,KAAK,iBAAiB;QACtB,KAAK,QAAQ;QACb,KAAK,WAAW;QAChB,KAAK,KAAK;UAIRF,KAAK,CAACE,IAAI,CAAC,GAAGH,KAAK,CAACG,IAAI,CAAC;UACzB;QACF;UAIED,KAAK,CAACC,IAAI,CAAC,GAAGH,KAAK,CAACG,IAAI,CAAC;UACzB;MACJ;IACF;EACF;EAEA,OAAO;IAACF,KAAK,EAALA,KAAK;IAAEC,KAAK,EAALA;EAAK,CAAC;AACvB", "ignoreList": []}