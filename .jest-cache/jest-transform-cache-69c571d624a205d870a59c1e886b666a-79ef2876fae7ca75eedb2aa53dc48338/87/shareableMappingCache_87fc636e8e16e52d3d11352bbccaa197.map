{"version": 3, "names": ["Object", "defineProperty", "exports", "value", "shareableMappingFlag", "shareableMappingCache", "_PlatformChecker", "require", "SHOULD_BE_USE_WEB", "shouldBeUseWeb", "Symbol", "cache", "WeakMap", "set", "get", "shareable", "shareableRef", "bind"], "sources": ["../../src/shareableMappingCache.ts"], "sourcesContent": [null], "mappings": "AAAA,YAAY;;AAAAA,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,KAAA;AAAA;AAAAD,OAAA,CAAAE,oBAAA,GAAAF,OAAA,CAAAG,qBAAA;AACZ,IAAAC,gBAAA,GAAAC,OAAA;AAGA,IAAMC,iBAAiB,GAAG,IAAAC,+BAAc,EAAC,CAAC;AAQnC,IAAML,oBAAoB,GAAAF,OAAA,CAAAE,oBAAA,GAAGM,MAAM,CAAC,gBAAgB,CAAC;AAa5D,IAAMC,KAAK,GAAGH,iBAAiB,GAC3B,IAAI,GACJ,IAAII,OAAO,CAAgC,CAAC;AAEzC,IAAMP,qBAAqB,GAAAH,OAAA,CAAAG,qBAAA,GAAGG,iBAAiB,GAClD;EACEK,GAAG,WAAHA,GAAGA,CAAA,EAAG,CACJ,CACD;EACDC,GAAG,WAAHA,GAAGA,CAAA,EAAG;IACJ,OAAO,IAAI;EACb;AACF,CAAC,GACD;EACED,GAAG,WAAHA,GAAGA,CAACE,SAAiB,EAAEC,YAA2B,EAAQ;IACxDL,KAAK,CAAEE,GAAG,CAACE,SAAS,EAAEC,YAAY,IAAIZ,oBAAoB,CAAC;EAC7D,CAAC;EACDU,GAAG,EAAEH,KAAK,CAAEG,GAAG,CAACG,IAAI,CAACN,KAAK;AAC5B,CAAC", "ignoreList": []}