d203e0fc63fa41d0cf81adef6e9c37f0
'use strict';

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.BounceOutData = exports.BounceOut = exports.BounceInData = exports.BounceIn = void 0;
var _animationParser = require("../animationParser.js");
var DEFAULT_BOUNCE_TIME = 0.6;
var BounceInData = exports.BounceInData = {
  BounceIn: {
    name: 'BounceIn',
    style: {
      0: {
        transform: [{
          scale: 0
        }]
      },
      55: {
        transform: [{
          scale: 1.2
        }]
      },
      70: {
        transform: [{
          scale: 0.9
        }]
      },
      85: {
        transform: [{
          scale: 1.1
        }]
      },
      100: {
        transform: [{
          scale: 1
        }]
      }
    },
    duration: DEFAULT_BOUNCE_TIME
  },
  BounceInRight: {
    name: 'BounceInRight',
    style: {
      0: {
        transform: [{
          translateX: '100vw'
        }]
      },
      55: {
        transform: [{
          translateX: '-20px'
        }]
      },
      70: {
        transform: [{
          translateX: '10px'
        }]
      },
      85: {
        transform: [{
          translateX: '-10px'
        }]
      },
      100: {
        transform: [{
          translateX: '0px'
        }]
      }
    },
    duration: DEFAULT_BOUNCE_TIME
  },
  BounceInLeft: {
    name: 'BounceInLeft',
    style: {
      0: {
        transform: [{
          translateX: '-100vw'
        }]
      },
      55: {
        transform: [{
          translateX: '20px'
        }]
      },
      70: {
        transform: [{
          translateX: '-10px'
        }]
      },
      85: {
        transform: [{
          translateX: '10px'
        }]
      },
      100: {
        transform: [{
          translateX: '0px'
        }]
      }
    },
    duration: DEFAULT_BOUNCE_TIME
  },
  BounceInUp: {
    name: 'BounceInUp',
    style: {
      0: {
        transform: [{
          translateY: '-100vh'
        }]
      },
      55: {
        transform: [{
          translateY: '20px'
        }]
      },
      70: {
        transform: [{
          translateY: '-10px'
        }]
      },
      85: {
        transform: [{
          translateY: '10px'
        }]
      },
      100: {
        transform: [{
          translateY: '0px'
        }]
      }
    },
    duration: DEFAULT_BOUNCE_TIME
  },
  BounceInDown: {
    name: 'BounceInDown',
    style: {
      0: {
        transform: [{
          translateY: '100vh'
        }]
      },
      55: {
        transform: [{
          translateY: '-20px'
        }]
      },
      70: {
        transform: [{
          translateY: '10px'
        }]
      },
      85: {
        transform: [{
          translateY: '-10px'
        }]
      },
      100: {
        transform: [{
          translateY: '0px'
        }]
      }
    },
    duration: DEFAULT_BOUNCE_TIME
  }
};
var BounceOutData = exports.BounceOutData = {
  BounceOut: {
    name: 'BounceOut',
    style: {
      0: {
        transform: [{
          scale: 1
        }]
      },
      15: {
        transform: [{
          scale: 1.1
        }]
      },
      30: {
        transform: [{
          scale: 0.9
        }]
      },
      45: {
        transform: [{
          scale: 1.2
        }]
      },
      100: {
        transform: [{
          scale: 0.1
        }]
      }
    },
    duration: DEFAULT_BOUNCE_TIME
  },
  BounceOutRight: {
    name: 'BounceOutRight',
    style: {
      0: {
        transform: [{
          translateX: '0px'
        }]
      },
      15: {
        transform: [{
          translateX: '-10px'
        }]
      },
      30: {
        transform: [{
          translateX: '10px'
        }]
      },
      45: {
        transform: [{
          translateX: '-20px'
        }]
      },
      100: {
        transform: [{
          translateX: '100vh'
        }]
      }
    },
    duration: DEFAULT_BOUNCE_TIME
  },
  BounceOutLeft: {
    name: 'BounceOutLeft',
    style: {
      0: {
        transform: [{
          translateX: '0px'
        }]
      },
      15: {
        transform: [{
          translateX: '10px'
        }]
      },
      30: {
        transform: [{
          translateX: '-10px'
        }]
      },
      45: {
        transform: [{
          translateX: '20px'
        }]
      },
      100: {
        transform: [{
          translateX: '-100vh'
        }]
      }
    },
    duration: DEFAULT_BOUNCE_TIME
  },
  BounceOutUp: {
    name: 'BounceOutUp',
    style: {
      0: {
        transform: [{
          translateY: '0px'
        }]
      },
      15: {
        transform: [{
          translateY: '10px'
        }]
      },
      30: {
        transform: [{
          translateY: '-10px'
        }]
      },
      45: {
        transform: [{
          translateY: '20px'
        }]
      },
      100: {
        transform: [{
          translateY: '-100vh'
        }]
      }
    },
    duration: DEFAULT_BOUNCE_TIME
  },
  BounceOutDown: {
    name: 'BounceOutDown',
    style: {
      0: {
        transform: [{
          translateY: '0px'
        }]
      },
      15: {
        transform: [{
          translateY: '-10px'
        }]
      },
      30: {
        transform: [{
          translateY: '10px'
        }]
      },
      45: {
        transform: [{
          translateY: '-20px'
        }]
      },
      100: {
        transform: [{
          translateY: '100vh'
        }]
      }
    },
    duration: DEFAULT_BOUNCE_TIME
  }
};
var BounceIn = exports.BounceIn = {
  BounceIn: {
    style: (0, _animationParser.convertAnimationObjectToKeyframes)(BounceInData.BounceIn),
    duration: BounceInData.BounceIn.duration
  },
  BounceInRight: {
    style: (0, _animationParser.convertAnimationObjectToKeyframes)(BounceInData.BounceInRight),
    duration: BounceInData.BounceInRight.duration
  },
  BounceInLeft: {
    style: (0, _animationParser.convertAnimationObjectToKeyframes)(BounceInData.BounceInLeft),
    duration: BounceInData.BounceInLeft.duration
  },
  BounceInUp: {
    style: (0, _animationParser.convertAnimationObjectToKeyframes)(BounceInData.BounceInUp),
    duration: BounceInData.BounceInUp.duration
  },
  BounceInDown: {
    style: (0, _animationParser.convertAnimationObjectToKeyframes)(BounceInData.BounceInDown),
    duration: BounceInData.BounceInDown.duration
  }
};
var BounceOut = exports.BounceOut = {
  BounceOut: {
    style: (0, _animationParser.convertAnimationObjectToKeyframes)(BounceOutData.BounceOut),
    duration: BounceOutData.BounceOut.duration
  },
  BounceOutRight: {
    style: (0, _animationParser.convertAnimationObjectToKeyframes)(BounceOutData.BounceOutRight),
    duration: BounceOutData.BounceOutRight.duration
  },
  BounceOutLeft: {
    style: (0, _animationParser.convertAnimationObjectToKeyframes)(BounceOutData.BounceOutLeft),
    duration: BounceOutData.BounceOutLeft.duration
  },
  BounceOutUp: {
    style: (0, _animationParser.convertAnimationObjectToKeyframes)(BounceOutData.BounceOutUp),
    duration: BounceOutData.BounceOutUp.duration
  },
  BounceOutDown: {
    style: (0, _animationParser.convertAnimationObjectToKeyframes)(BounceOutData.BounceOutDown),
    duration: BounceOutData.BounceOutDown.duration
  }
};
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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