984e13def756e6a1cad1c122c2bcc6a6
"use strict";

/* istanbul ignore next */
function cov_1tc5g0gcot() {
  var path = "/Users/<USER>/projects/react-native/payment-module/src/data/models/category-list/CategoryListResponse.ts";
  var hash = "862be200744feb165485e657907177a3ce602483";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/projects/react-native/payment-module/src/data/models/category-list/CategoryListResponse.ts",
    statementMap: {
      "0": {
        start: {
          line: 3,
          column: 0
        },
        end: {
          line: 5,
          column: 3
        }
      }
    },
    fnMap: {},
    branchMap: {},
    s: {
      "0": 0
    },
    f: {},
    b: {},
    inputSourceMap: {
      version: 3,
      file: undefined,
      names: [],
      sourceRoot: undefined,
      sources: ["/Users/<USER>/projects/react-native/payment-module/src/data/models/category-list/CategoryListResponse.ts"],
      sourcesContent: ["export type CategoryListResponse = {\n  items: CategoryResponse[];\n  totalPage: number;\n  totalElements: number;\n  page: number;\n  size: number;\n};\n\nexport interface ProductParam {\n  id: number;\n  code: string;\n  name: string;\n  dataType: string;\n  dataValue: string;\n  status: string;\n  version: number;\n  createdBy: string;\n  updatedBy: string;\n  createdAt: string;\n  updatedAt: string;\n  productParamHistories: any[];\n}\nexport interface CategoryResponse {\n  id: number;\n  subDomainCode: string;\n  subDomainName: string;\n  domainCode: string;\n  domainName: string;\n  code: string;\n  status: string;\n  name: string;\n  description: string | null;\n  createdBy: string;\n  updatedBy: string;\n  createdAt: string;\n  updatedAt: string;\n  productParams: ProductParam[];\n}\n"],
      mappings: "",
      ignoreList: []
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "862be200744feb165485e657907177a3ce602483"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_1tc5g0gcot = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_1tc5g0gcot();
cov_1tc5g0gcot().s[0]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6W10sInNvdXJjZXMiOlsiL1VzZXJzL3NvbnNhbXNldC9wcm9qZWN0cy9yZWFjdC1uYXRpdmUvcGF5bWVudC1tb2R1bGUvc3JjL2RhdGEvbW9kZWxzL2NhdGVnb3J5LWxpc3QvQ2F0ZWdvcnlMaXN0UmVzcG9uc2UudHMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IHR5cGUgQ2F0ZWdvcnlMaXN0UmVzcG9uc2UgPSB7XG4gIGl0ZW1zOiBDYXRlZ29yeVJlc3BvbnNlW107XG4gIHRvdGFsUGFnZTogbnVtYmVyO1xuICB0b3RhbEVsZW1lbnRzOiBudW1iZXI7XG4gIHBhZ2U6IG51bWJlcjtcbiAgc2l6ZTogbnVtYmVyO1xufTtcblxuZXhwb3J0IGludGVyZmFjZSBQcm9kdWN0UGFyYW0ge1xuICBpZDogbnVtYmVyO1xuICBjb2RlOiBzdHJpbmc7XG4gIG5hbWU6IHN0cmluZztcbiAgZGF0YVR5cGU6IHN0cmluZztcbiAgZGF0YVZhbHVlOiBzdHJpbmc7XG4gIHN0YXR1czogc3RyaW5nO1xuICB2ZXJzaW9uOiBudW1iZXI7XG4gIGNyZWF0ZWRCeTogc3RyaW5nO1xuICB1cGRhdGVkQnk6IHN0cmluZztcbiAgY3JlYXRlZEF0OiBzdHJpbmc7XG4gIHVwZGF0ZWRBdDogc3RyaW5nO1xuICBwcm9kdWN0UGFyYW1IaXN0b3JpZXM6IGFueVtdO1xufVxuZXhwb3J0IGludGVyZmFjZSBDYXRlZ29yeVJlc3BvbnNlIHtcbiAgaWQ6IG51bWJlcjtcbiAgc3ViRG9tYWluQ29kZTogc3RyaW5nO1xuICBzdWJEb21haW5OYW1lOiBzdHJpbmc7XG4gIGRvbWFpbkNvZGU6IHN0cmluZztcbiAgZG9tYWluTmFtZTogc3RyaW5nO1xuICBjb2RlOiBzdHJpbmc7XG4gIHN0YXR1czogc3RyaW5nO1xuICBuYW1lOiBzdHJpbmc7XG4gIGRlc2NyaXB0aW9uOiBzdHJpbmcgfCBudWxsO1xuICBjcmVhdGVkQnk6IHN0cmluZztcbiAgdXBkYXRlZEJ5OiBzdHJpbmc7XG4gIGNyZWF0ZWRBdDogc3RyaW5nO1xuICB1cGRhdGVkQXQ6IHN0cmluZztcbiAgcHJvZHVjdFBhcmFtczogUHJvZHVjdFBhcmFtW107XG59XG4iXSwibWFwcGluZ3MiOiIiLCJpZ25vcmVMaXN0IjpbXX0=