fdba5dd7c2c4f792fa0824e3fbd52ab3
'use strict';

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.Bezier = Bezier;
var _errors = require("./errors.js");
var NEWTON_ITERATIONS = 4;
var NEWTON_MIN_SLOPE = 0.001;
var SUBDIVISION_PRECISION = 0.0000001;
var SUBDIVISION_MAX_ITERATIONS = 10;
var kSplineTableSize = 11;
var kSampleStepSize = 1.0 / (kSplineTableSize - 1.0);
function A(aA1, aA2) {
  'worklet';

  return 1.0 - 3.0 * aA2 + 3.0 * aA1;
}
function B(aA1, aA2) {
  'worklet';

  return 3.0 * aA2 - 6.0 * aA1;
}
function C(aA1) {
  'worklet';

  return 3.0 * aA1;
}
function calcBezier(aT, aA1, aA2) {
  'worklet';

  return ((A(aA1, aA2) * aT + B(aA1, aA2)) * aT + C(aA1)) * aT;
}
function getSlope(aT, aA1, aA2) {
  'worklet';

  return 3.0 * A(aA1, aA2) * aT * aT + 2.0 * B(aA1, aA2) * aT + C(aA1);
}
function binarySubdivide(aX, aA, aB, mX1, mX2) {
  'worklet';

  var currentX;
  var currentT;
  var i = 0;
  do {
    currentT = aA + (aB - aA) / 2.0;
    currentX = calcBezier(currentT, mX1, mX2) - aX;
    if (currentX > 0.0) {
      aB = currentT;
    } else {
      aA = currentT;
    }
  } while (Math.abs(currentX) > SUBDIVISION_PRECISION && ++i < SUBDIVISION_MAX_ITERATIONS);
  return currentT;
}
function newtonRaphsonIterate(aX, aGuessT, mX1, mX2) {
  'worklet';

  for (var i = 0; i < NEWTON_ITERATIONS; ++i) {
    var currentSlope = getSlope(aGuessT, mX1, mX2);
    if (currentSlope === 0.0) {
      return aGuessT;
    }
    var currentX = calcBezier(aGuessT, mX1, mX2) - aX;
    aGuessT -= currentX / currentSlope;
  }
  return aGuessT;
}
function Bezier(mX1, mY1, mX2, mY2) {
  'worklet';

  function LinearEasing(x) {
    'worklet';

    return x;
  }
  if (!(mX1 >= 0 && mX1 <= 1 && mX2 >= 0 && mX2 <= 1)) {
    throw new _errors.ReanimatedError('Bezier x values must be in [0, 1] range.');
  }
  if (mX1 === mY1 && mX2 === mY2) {
    return LinearEasing;
  }
  var sampleValues = new Array(kSplineTableSize);
  for (var i = 0; i < kSplineTableSize; ++i) {
    sampleValues[i] = calcBezier(i * kSampleStepSize, mX1, mX2);
  }
  function getTForX(aX) {
    'worklet';

    var intervalStart = 0.0;
    var currentSample = 1;
    var lastSample = kSplineTableSize - 1;
    for (; currentSample !== lastSample && sampleValues[currentSample] <= aX; ++currentSample) {
      intervalStart += kSampleStepSize;
    }
    --currentSample;
    var dist = (aX - sampleValues[currentSample]) / (sampleValues[currentSample + 1] - sampleValues[currentSample]);
    var guessForT = intervalStart + dist * kSampleStepSize;
    var initialSlope = getSlope(guessForT, mX1, mX2);
    if (initialSlope >= NEWTON_MIN_SLOPE) {
      return newtonRaphsonIterate(aX, guessForT, mX1, mX2);
    } else if (initialSlope === 0.0) {
      return guessForT;
    } else {
      return binarySubdivide(aX, intervalStart, intervalStart + kSampleStepSize, mX1, mX2);
    }
  }
  return function BezierEasing(x) {
    'worklet';

    if (mX1 === mY1 && mX2 === mY2) {
      return x;
    }
    if (x === 0) {
      return 0;
    }
    if (x === 1) {
      return 1;
    }
    return calcBezier(getTForX(x), mY1, mY2);
  };
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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