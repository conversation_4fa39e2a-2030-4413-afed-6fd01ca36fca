f4fc29310b51f7b1f2ea4438442961be
Object.defineProperty(exports, "__esModule", {
  value: true
});
var _exportNames = {};
exports.default = void 0;
var _NativeFrameRateLogger = _interopRequireWildcard(require("../../src/private/specs/modules/NativeFrameRateLogger"));
Object.keys(_NativeFrameRateLogger).forEach(function (key) {
  if (key === "default" || key === "__esModule") return;
  if (Object.prototype.hasOwnProperty.call(_exportNames, key)) return;
  if (key in exports && exports[key] === _NativeFrameRateLogger[key]) return;
  Object.defineProperty(exports, key, {
    enumerable: true,
    get: function get() {
      return _NativeFrameRateLogger[key];
    }
  });
});
function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
var _default = exports.default = _NativeFrameRateLogger.default;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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