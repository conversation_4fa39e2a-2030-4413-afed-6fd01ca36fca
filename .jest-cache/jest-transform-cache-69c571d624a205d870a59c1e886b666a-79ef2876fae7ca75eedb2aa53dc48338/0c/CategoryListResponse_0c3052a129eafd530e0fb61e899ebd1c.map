{"version": 3, "names": [], "sources": ["/Users/<USER>/projects/react-native/payment-module/src/data/models/category-list/CategoryListResponse.ts"], "sourcesContent": ["export type CategoryListResponse = {\n  items: CategoryResponse[];\n  totalPage: number;\n  totalElements: number;\n  page: number;\n  size: number;\n};\n\nexport interface ProductParam {\n  id: number;\n  code: string;\n  name: string;\n  dataType: string;\n  dataValue: string;\n  status: string;\n  version: number;\n  createdBy: string;\n  updatedBy: string;\n  createdAt: string;\n  updatedAt: string;\n  productParamHistories: any[];\n}\nexport interface CategoryResponse {\n  id: number;\n  subDomainCode: string;\n  subDomainName: string;\n  domainCode: string;\n  domainName: string;\n  code: string;\n  status: string;\n  name: string;\n  description: string | null;\n  createdBy: string;\n  updatedBy: string;\n  createdAt: string;\n  updatedAt: string;\n  productParams: ProductParam[];\n}\n"], "mappings": "", "ignoreList": []}