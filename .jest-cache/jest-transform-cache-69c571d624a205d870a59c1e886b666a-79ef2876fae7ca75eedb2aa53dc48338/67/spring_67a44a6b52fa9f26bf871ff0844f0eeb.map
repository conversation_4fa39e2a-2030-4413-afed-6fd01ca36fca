{"version": 3, "names": ["Object", "defineProperty", "exports", "value", "with<PERSON><PERSON><PERSON>", "_util", "require", "_springUtils", "toValue", "userConfig", "callback", "defineAnimation", "defaultConfig", "damping", "mass", "stiffness", "overshootClamping", "restDisplacementThreshold", "restSpeedThreshold", "velocity", "duration", "dampingRatio", "reduceMotion", "undefined", "clamp", "config", "assign", "useDuration", "skipAnimation", "checkIfConfigIsValid", "springOnFrame", "animation", "now", "startTimestamp", "current", "timeFromStart", "lastTimestamp", "deltaTime", "Math", "min", "t", "v0", "x0", "zeta", "omega0", "omega1", "_ref", "underDampedSpringCalculations", "criticallyDampedSpringCalculations", "newPosition", "position", "newVelocity", "_isAnimationTerminati", "isAnimationTerminatingCalculation", "isOvershooting", "isVelocity", "isDisplacement", "springIsNotInMove", "isTriggeredTwice", "previousAnimation", "onStart", "startValue", "triggeredTwice", "Number", "actualDuration", "calculateNewMassToMatchDuration", "_initialCalculations", "initialCalculations", "scaleZetaToMatchClamps", "onFrame", "getReduceMotionForAnimation"], "sources": ["../../../src/animation/spring.ts"], "sourcesContent": [null], "mappings": "AAAA,YAAY;;AAAAA,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,KAAA;AAAA;AAAAD,OAAA,CAAAE,UAAA;AACZ,IAAAC,KAAA,GAAAC,OAAA;AAcA,IAAAC,YAAA,GAAAD,OAAA;AA8BO,IAAMF,UAAU,GAAAF,OAAA,CAAAE,UAAA,GAAI,SAAdA,UAAUA,CACrBI,OAAwB,EACxBC,UAAyB,EACzBC,QAA4B,EACG;EAC/B,SAAS;;EAET,OAAO,IAAAC,qBAAe,EAAkBH,OAAO,EAAE,YAAM;IACrD,SAAS;;IACT,IAAMI,aAAkC,GAAG;MACzCC,OAAO,EAAE,EAAE;MACXC,IAAI,EAAE,CAAC;MACPC,SAAS,EAAE,GAAG;MACdC,iBAAiB,EAAE,KAAK;MACxBC,yBAAyB,EAAE,IAAI;MAC/BC,kBAAkB,EAAE,CAAC;MACrBC,QAAQ,EAAE,CAAC;MACXC,QAAQ,EAAE,IAAI;MACdC,YAAY,EAAE,GAAG;MACjBC,YAAY,EAAEC,SAAS;MACvBC,KAAK,EAAED;IACT,CAAU;IAEV,IAAME,MAA+C,GAAAzB,MAAA,CAAA0B,MAAA,KAChDd,aAAa,EACbH,UAAU;MACbkB,WAAW,EAAE,CAAC,EAAElB,UAAU,YAAVA,UAAU,CAAEW,QAAQ,IAAIX,UAAU,YAAVA,UAAU,CAAEY,YAAY,CAAC;MACjEO,aAAa,EAAE;IAAA,EAChB;IAEDH,MAAM,CAACG,aAAa,GAAG,CAAC,IAAAC,iCAAoB,EAACJ,MAAM,CAAC;IAEpD,IAAIA,MAAM,CAACL,QAAQ,KAAK,CAAC,EAAE;MACzBK,MAAM,CAACG,aAAa,GAAG,IAAI;IAC7B;IAEA,SAASE,aAAaA,CACpBC,SAA+B,EAC/BC,GAAc,EACL;MAET,IAAQxB,OAAO,GAA8BuB,SAAS,CAA9CvB,OAAO;QAAEyB,cAAc,GAAcF,SAAS,CAArCE,cAAc;QAAEC,OAAA,GAAYH,SAAS,CAArBG,OAAA;MAEjC,IAAMC,aAAa,GAAGH,GAAG,GAAGC,cAAc;MAE1C,IAAIR,MAAM,CAACE,WAAW,IAAIQ,aAAa,IAAIV,MAAM,CAACL,QAAQ,EAAE;QAC1DW,SAAS,CAACG,OAAO,GAAG1B,OAAO;QAE3BuB,SAAS,CAACK,aAAa,GAAG,CAAC;QAC3B,OAAO,IAAI;MACb;MAEA,IAAIX,MAAM,CAACG,aAAa,EAAE;QACxBG,SAAS,CAACG,OAAO,GAAG1B,OAAO;QAC3BuB,SAAS,CAACK,aAAa,GAAG,CAAC;QAC3B,OAAO,IAAI;MACb;MACA,IAAQA,aAAa,GAAeL,SAAS,CAArCK,aAAa;QAAEjB,QAAA,GAAaY,SAAS,CAAtBZ,QAAA;MAEvB,IAAMkB,SAAS,GAAGC,IAAI,CAACC,GAAG,CAACP,GAAG,GAAGI,aAAa,EAAE,EAAE,CAAC;MACnDL,SAAS,CAACK,aAAa,GAAGJ,GAAG;MAE7B,IAAMQ,CAAC,GAAGH,SAAS,GAAG,IAAI;MAC1B,IAAMI,EAAE,GAAG,CAACtB,QAAQ;MACpB,IAAMuB,EAAE,GAAGlC,OAAO,GAAG0B,OAAO;MAE5B,IAAQS,IAAI,GAAqBZ,SAAS,CAAlCY,IAAI;QAAEC,MAAM,GAAab,SAAS,CAA5Ba,MAAM;QAAEC,MAAA,GAAWd,SAAS,CAApBc,MAAA;MAEtB,IAAAC,IAAA,GACEH,IAAI,GAAG,CAAC,GACJ,IAAAI,0CAA6B,EAAChB,SAAS,EAAE;UACvCY,IAAI,EAAJA,IAAI;UACJF,EAAE,EAAFA,EAAE;UACFC,EAAE,EAAFA,EAAE;UACFE,MAAM,EAANA,MAAM;UACNC,MAAM,EAANA,MAAM;UACNL,CAAA,EAAAA;QACF,CAAC,CAAC,GACF,IAAAQ,+CAAkC,EAACjB,SAAS,EAAE;UAC5CU,EAAE,EAAFA,EAAE;UACFC,EAAE,EAAFA,EAAE;UACFE,MAAM,EAANA,MAAM;UACNJ,CAAA,EAAAA;QACF,CAAC,CAAC;QAfUS,WAAW,GAAAH,IAAA,CAArBI,QAAQ;QAAyBC,WAAA,GAAAL,IAAA,CAAV3B,QAAQ;MAiBvCY,SAAS,CAACG,OAAO,GAAGe,WAAW;MAC/BlB,SAAS,CAACZ,QAAQ,GAAGgC,WAAW;MAEhC,IAAAC,qBAAA,GACE,IAAAC,8CAAiC,EAACtB,SAAS,EAAEN,MAAM,CAAC;QAD9C6B,cAAc,GAAAF,qBAAA,CAAdE,cAAc;QAAEC,UAAU,GAAAH,qBAAA,CAAVG,UAAU;QAAEC,cAAA,GAAAJ,qBAAA,CAAAI,cAAA;MAGpC,IAAMC,iBAAiB,GACrBH,cAAc,IAAKC,UAAU,IAAIC,cAAe;MAElD,IAAI,CAAC/B,MAAM,CAACE,WAAW,IAAI8B,iBAAiB,EAAE;QAC5C1B,SAAS,CAACZ,QAAQ,GAAG,CAAC;QACtBY,SAAS,CAACG,OAAO,GAAG1B,OAAO;QAE3BuB,SAAS,CAACK,aAAa,GAAG,CAAC;QAC3B,OAAO,IAAI;MACb;MAEA,OAAO,KAAK;IACd;IAEA,SAASsB,gBAAgBA,CACvBC,iBAA8C,EAC9C5B,SAA0B,EAC1B;MACA,OACE,CAAA4B,iBAAiB,oBAAjBA,iBAAiB,CAAEvB,aAAa,MAChCuB,iBAAiB,oBAAjBA,iBAAiB,CAAE1B,cAAc,KACjC,CAAA0B,iBAAiB,oBAAjBA,iBAAiB,CAAEnD,OAAO,MAAKuB,SAAS,CAACvB,OAAO,IAChD,CAAAmD,iBAAiB,oBAAjBA,iBAAiB,CAAEvC,QAAQ,MAAKW,SAAS,CAACX,QAAQ,IAClD,CAAAuC,iBAAiB,oBAAjBA,iBAAiB,CAAEtC,YAAY,MAAKU,SAAS,CAACV,YAAY;IAE9D;IAEA,SAASuC,OAAOA,CACd7B,SAA0B,EAC1B5B,KAAa,EACb6B,GAAc,EACd2B,iBAA8C,EACxC;MACN5B,SAAS,CAACG,OAAO,GAAG/B,KAAK;MACzB4B,SAAS,CAAC8B,UAAU,GAAG1D,KAAK;MAE5B,IAAIW,IAAI,GAAGW,MAAM,CAACX,IAAI;MACtB,IAAMgD,cAAc,GAAGJ,gBAAgB,CAACC,iBAAiB,EAAE5B,SAAS,CAAC;MAErE,IAAMX,QAAQ,GAAGK,MAAM,CAACL,QAAQ;MAEhC,IAAMsB,EAAE,GAAGoB,cAAc,GAGrBH,iBAAiB,oBAAjBA,iBAAiB,CAAEE,UAAU,GAC7BE,MAAM,CAAChC,SAAS,CAACvB,OAAO,CAAC,GAAGL,KAAK;MAErC,IAAIwD,iBAAiB,EAAE;QACrB5B,SAAS,CAACZ,QAAQ,GAChB,CAAC2C,cAAc,GACXH,iBAAiB,oBAAjBA,iBAAiB,CAAExC,QAAQ,GAC3B,CAAAwC,iBAAiB,oBAAjBA,iBAAiB,CAAExC,QAAQ,IAAGM,MAAM,CAACN,QAAQ,KAAK,CAAC;MAC3D,CAAC,MAAM;QACLY,SAAS,CAACZ,QAAQ,GAAGM,MAAM,CAACN,QAAQ,IAAI,CAAC;MAC3C;MAEA,IAAI2C,cAAc,EAAE;QAClB/B,SAAS,CAACY,IAAI,GAAG,CAAAgB,iBAAiB,oBAAjBA,iBAAiB,CAAEhB,IAAI,KAAI,CAAC;QAC7CZ,SAAS,CAACa,MAAM,GAAG,CAAAe,iBAAiB,oBAAjBA,iBAAiB,CAAEf,MAAM,KAAI,CAAC;QACjDb,SAAS,CAACc,MAAM,GAAG,CAAAc,iBAAiB,oBAAjBA,iBAAiB,CAAEd,MAAM,KAAI,CAAC;MACnD,CAAC,MAAM;QACL,IAAIpB,MAAM,CAACE,WAAW,EAAE;UACtB,IAAMqC,cAAc,GAAGF,cAAc,GAGjC1C,QAAQ,IACP,CAAC,CAAAuC,iBAAiB,oBAAjBA,iBAAiB,CAAEvB,aAAa,KAAI,CAAC,KACpC,CAAAuB,iBAAiB,oBAAjBA,iBAAiB,CAAE1B,cAAc,KAAI,CAAC,CAAC,CAAC,GAC3Cb,QAAQ;UAEZK,MAAM,CAACL,QAAQ,GAAG4C,cAAc;UAChClD,IAAI,GAAG,IAAAmD,4CAA+B,EACpCvB,EAAE,EACFjB,MAAM,EACNM,SAAS,CAACZ,QACZ,CAAC;QACH;QAEA,IAAA+C,oBAAA,GAAiC,IAAAC,gCAAmB,EAACrD,IAAI,EAAEW,MAAM,CAAC;UAA1DkB,IAAI,GAAAuB,oBAAA,CAAJvB,IAAI;UAAEC,MAAM,GAAAsB,oBAAA,CAANtB,MAAM;UAAEC,MAAA,GAAAqB,oBAAA,CAAArB,MAAA;QACtBd,SAAS,CAACY,IAAI,GAAGA,IAAI;QACrBZ,SAAS,CAACa,MAAM,GAAGA,MAAM;QACzBb,SAAS,CAACc,MAAM,GAAGA,MAAM;QAEzB,IAAIpB,MAAM,CAACD,KAAK,KAAKD,SAAS,EAAE;UAC9BQ,SAAS,CAACY,IAAI,GAAG,IAAAyB,mCAAsB,EAACrC,SAAS,EAAEN,MAAM,CAACD,KAAK,CAAC;QAClE;MACF;MAEAO,SAAS,CAACK,aAAa,GAAG,CAAAuB,iBAAiB,oBAAjBA,iBAAiB,CAAEvB,aAAa,KAAIJ,GAAG;MAEjED,SAAS,CAACE,cAAc,GAAG6B,cAAc,GACrC,CAAAH,iBAAiB,oBAAjBA,iBAAiB,CAAE1B,cAAc,KAAID,GAAG,GACxCA,GAAG;IACT;IAEA,OAAO;MACLqC,OAAO,EAAEvC,aAAa;MACtB8B,OAAO,EAAPA,OAAO;MACPpD,OAAO,EAAPA,OAAO;MACPW,QAAQ,EAAEM,MAAM,CAACN,QAAQ,IAAI,CAAC;MAC9Be,OAAO,EAAE1B,OAAO;MAChBqD,UAAU,EAAE,CAAC;MACbnD,QAAQ,EAARA,QAAQ;MACR0B,aAAa,EAAE,CAAC;MAChBH,cAAc,EAAE,CAAC;MACjBU,IAAI,EAAE,CAAC;MACPC,MAAM,EAAE,CAAC;MACTC,MAAM,EAAE,CAAC;MACTvB,YAAY,EAAE,IAAAgD,iCAA2B,EAAC7C,MAAM,CAACH,YAAY;IAC/D,CAAC;EACH,CAAC,CAAC;AACJ,CAAoB", "ignoreList": []}