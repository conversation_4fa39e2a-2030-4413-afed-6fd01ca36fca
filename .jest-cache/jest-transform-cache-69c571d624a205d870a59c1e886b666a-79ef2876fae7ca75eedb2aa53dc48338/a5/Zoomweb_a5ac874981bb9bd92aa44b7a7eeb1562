397c7eb4e00963ba2e3aa358d975a4bb
'use strict';

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.ZoomOutData = exports.ZoomOut = exports.ZoomInData = exports.ZoomIn = void 0;
var _animationParser = require("../animationParser.js");
var DEFAULT_ZOOM_TIME = 0.3;
var ZoomInData = exports.ZoomInData = {
  ZoomIn: {
    name: 'ZoomIn',
    style: {
      0: {
        transform: [{
          scale: 0
        }]
      },
      100: {
        transform: [{
          scale: 1
        }]
      }
    },
    duration: DEFAULT_ZOOM_TIME
  },
  ZoomInRotate: {
    name: 'ZoomInRotate',
    style: {
      0: {
        transform: [{
          scale: 0,
          rotate: '0.3rad'
        }]
      },
      100: {
        transform: [{
          scale: 1,
          rotate: '0deg'
        }]
      }
    },
    duration: DEFAULT_ZOOM_TIME
  },
  ZoomInRight: {
    name: 'ZoomInRight',
    style: {
      0: {
        transform: [{
          translateX: '100vw',
          scale: 0
        }]
      },
      100: {
        transform: [{
          translateX: '0%',
          scale: 1
        }]
      }
    },
    duration: DEFAULT_ZOOM_TIME
  },
  ZoomInLeft: {
    name: 'ZoomInLeft',
    style: {
      0: {
        transform: [{
          translateX: '-100vw',
          scale: 0
        }]
      },
      100: {
        transform: [{
          translateX: '0%',
          scale: 1
        }]
      }
    },
    duration: DEFAULT_ZOOM_TIME
  },
  ZoomInUp: {
    name: 'ZoomInUp',
    style: {
      0: {
        transform: [{
          translateY: '-100vh',
          scale: 0
        }]
      },
      100: {
        transform: [{
          translateY: '0%',
          scale: 1
        }]
      }
    },
    duration: DEFAULT_ZOOM_TIME
  },
  ZoomInDown: {
    name: 'ZoomInDown',
    style: {
      0: {
        transform: [{
          translateY: '100vh',
          scale: 0
        }]
      },
      100: {
        transform: [{
          translateY: '0%',
          scale: 1
        }]
      }
    },
    duration: DEFAULT_ZOOM_TIME
  },
  ZoomInEasyUp: {
    name: 'ZoomInEasyUp',
    style: {
      0: {
        transform: [{
          translateY: '-100%',
          scale: 0
        }]
      },
      100: {
        transform: [{
          translateY: '0%',
          scale: 1
        }]
      }
    },
    duration: DEFAULT_ZOOM_TIME
  },
  ZoomInEasyDown: {
    name: 'ZoomInEasyDown',
    style: {
      0: {
        transform: [{
          translateY: '100%',
          scale: 0
        }]
      },
      100: {
        transform: [{
          translateY: '0%',
          scale: 1
        }]
      }
    },
    duration: DEFAULT_ZOOM_TIME
  }
};
var ZoomOutData = exports.ZoomOutData = {
  ZoomOut: {
    name: 'ZoomOut',
    style: {
      0: {
        transform: [{
          scale: 1
        }]
      },
      100: {
        transform: [{
          scale: 0
        }]
      }
    },
    duration: DEFAULT_ZOOM_TIME
  },
  ZoomOutRotate: {
    name: 'ZoomOutRotate',
    style: {
      0: {
        transform: [{
          scale: 1,
          rotate: '0rad'
        }]
      },
      100: {
        transform: [{
          scale: 0,
          rotate: '0.3rad'
        }]
      }
    },
    duration: DEFAULT_ZOOM_TIME
  },
  ZoomOutRight: {
    name: 'ZoomOutRight',
    style: {
      0: {
        transform: [{
          translateX: '0vw',
          scale: 1
        }]
      },
      100: {
        transform: [{
          translateX: '100vw',
          scale: 0
        }]
      }
    },
    duration: DEFAULT_ZOOM_TIME
  },
  ZoomOutLeft: {
    name: 'ZoomOutLeft',
    style: {
      0: {
        transform: [{
          translateX: '0vw',
          scale: 1
        }]
      },
      100: {
        transform: [{
          translateX: '-100vw',
          scale: 0
        }]
      }
    },
    duration: DEFAULT_ZOOM_TIME
  },
  ZoomOutUp: {
    name: 'ZoomOutUp',
    style: {
      0: {
        transform: [{
          translateX: '0vh',
          scale: 1
        }]
      },
      100: {
        transform: [{
          translateY: '-100vh',
          scale: 0
        }]
      }
    },
    duration: DEFAULT_ZOOM_TIME
  },
  ZoomOutDown: {
    name: 'ZoomOutDown',
    style: {
      0: {
        transform: [{
          translateX: '0vh',
          scale: 1
        }]
      },
      100: {
        transform: [{
          translateY: '100vh',
          scale: 0
        }]
      }
    },
    duration: DEFAULT_ZOOM_TIME
  },
  ZoomOutEasyUp: {
    name: 'ZoomOutEasyUp',
    style: {
      0: {
        transform: [{
          translateY: '0%',
          scale: 1
        }]
      },
      100: {
        transform: [{
          translateY: '-100%',
          scale: 0
        }]
      }
    },
    duration: DEFAULT_ZOOM_TIME
  },
  ZoomOutEasyDown: {
    name: 'ZoomOutEasyDown',
    style: {
      0: {
        transform: [{
          translateY: '0%',
          scale: 1
        }]
      },
      100: {
        transform: [{
          translateY: '100%',
          scale: 0
        }]
      }
    },
    duration: DEFAULT_ZOOM_TIME
  }
};
var ZoomIn = exports.ZoomIn = {
  ZoomIn: {
    style: (0, _animationParser.convertAnimationObjectToKeyframes)(ZoomInData.ZoomIn),
    duration: ZoomInData.ZoomIn.duration
  },
  ZoomInRotate: {
    style: (0, _animationParser.convertAnimationObjectToKeyframes)(ZoomInData.ZoomInRotate),
    duration: ZoomInData.ZoomInRotate.duration
  },
  ZoomInRight: {
    style: (0, _animationParser.convertAnimationObjectToKeyframes)(ZoomInData.ZoomInRight),
    duration: ZoomInData.ZoomInRight.duration
  },
  ZoomInLeft: {
    style: (0, _animationParser.convertAnimationObjectToKeyframes)(ZoomInData.ZoomInLeft),
    duration: ZoomInData.ZoomInLeft.duration
  },
  ZoomInUp: {
    style: (0, _animationParser.convertAnimationObjectToKeyframes)(ZoomInData.ZoomInUp),
    duration: ZoomInData.ZoomInUp.duration
  },
  ZoomInDown: {
    style: (0, _animationParser.convertAnimationObjectToKeyframes)(ZoomInData.ZoomInDown),
    duration: ZoomInData.ZoomInDown.duration
  },
  ZoomInEasyUp: {
    style: (0, _animationParser.convertAnimationObjectToKeyframes)(ZoomInData.ZoomInEasyUp),
    duration: ZoomInData.ZoomInEasyUp.duration
  },
  ZoomInEasyDown: {
    style: (0, _animationParser.convertAnimationObjectToKeyframes)(ZoomInData.ZoomInEasyDown),
    duration: ZoomInData.ZoomInEasyDown.duration
  }
};
var ZoomOut = exports.ZoomOut = {
  ZoomOut: {
    style: (0, _animationParser.convertAnimationObjectToKeyframes)(ZoomOutData.ZoomOut),
    duration: ZoomOutData.ZoomOut.duration
  },
  ZoomOutRotate: {
    style: (0, _animationParser.convertAnimationObjectToKeyframes)(ZoomOutData.ZoomOutRotate),
    duration: ZoomOutData.ZoomOutRotate.duration
  },
  ZoomOutRight: {
    style: (0, _animationParser.convertAnimationObjectToKeyframes)(ZoomOutData.ZoomOutRight),
    duration: ZoomOutData.ZoomOutRight.duration
  },
  ZoomOutLeft: {
    style: (0, _animationParser.convertAnimationObjectToKeyframes)(ZoomOutData.ZoomOutLeft),
    duration: ZoomOutData.ZoomOutLeft.duration
  },
  ZoomOutUp: {
    style: (0, _animationParser.convertAnimationObjectToKeyframes)(ZoomOutData.ZoomOutUp),
    duration: ZoomOutData.ZoomOutUp.duration
  },
  ZoomOutDown: {
    style: (0, _animationParser.convertAnimationObjectToKeyframes)(ZoomOutData.ZoomOutDown),
    duration: ZoomOutData.ZoomOutDown.duration
  },
  ZoomOutEasyUp: {
    style: (0, _animationParser.convertAnimationObjectToKeyframes)(ZoomOutData.ZoomOutEasyUp),
    duration: ZoomOutData.ZoomOutEasyUp.duration
  },
  ZoomOutEasyDown: {
    style: (0, _animationParser.convertAnimationObjectToKeyframes)(ZoomOutData.ZoomOutEasyDown),
    duration: ZoomOutData.ZoomOutEasyDown.duration
  }
};
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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