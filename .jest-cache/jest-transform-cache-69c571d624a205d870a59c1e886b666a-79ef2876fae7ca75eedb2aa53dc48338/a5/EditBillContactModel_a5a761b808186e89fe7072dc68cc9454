c8aed281a864a6c944300674498303fc
"use strict";

/* istanbul ignore next */
function cov_11e2t6mpox() {
  var path = "/Users/<USER>/projects/react-native/payment-module/src/domain/entities/edit-bill-contact/EditBillContactModel.ts";
  var hash = "c351e9ec0c3bae05e3398a8e03b65851979b4d7f";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/projects/react-native/payment-module/src/domain/entities/edit-bill-contact/EditBillContactModel.ts",
    statementMap: {
      "0": {
        start: {
          line: 3,
          column: 29
        },
        end: {
          line: 3,
          column: 84
        }
      },
      "1": {
        start: {
          line: 4,
          column: 20
        },
        end: {
          line: 4,
          column: 89
        }
      },
      "2": {
        start: {
          line: 5,
          column: 23
        },
        end: {
          line: 5,
          column: 95
        }
      },
      "3": {
        start: {
          line: 6,
          column: 0
        },
        end: {
          line: 8,
          column: 3
        }
      },
      "4": {
        start: {
          line: 9,
          column: 0
        },
        end: {
          line: 9,
          column: 38
        }
      },
      "5": {
        start: {
          line: 10,
          column: 27
        },
        end: {
          line: 12,
          column: 2
        }
      },
      "6": {
        start: {
          line: 11,
          column: 2
        },
        end: {
          line: 11,
          column: 60
        }
      },
      "7": {
        start: {
          line: 13,
          column: 0
        },
        end: {
          line: 13,
          column: 52
        }
      }
    },
    fnMap: {
      "0": {
        name: "EditBillContactModel",
        decl: {
          start: {
            line: 10,
            column: 63
          },
          end: {
            line: 10,
            column: 83
          }
        },
        loc: {
          start: {
            line: 10,
            column: 86
          },
          end: {
            line: 12,
            column: 1
          }
        },
        line: 10
      }
    },
    branchMap: {},
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0
    },
    f: {
      "0": 0
    },
    b: {},
    inputSourceMap: {
      version: 3,
      file: undefined,
      names: ["EditBillContactModel", "_createClass2", "default", "_classCallCheck2", "exports"],
      sourceRoot: undefined,
      sources: ["/Users/<USER>/projects/react-native/payment-module/src/domain/entities/edit-bill-contact/EditBillContactModel.ts"],
      sourcesContent: ["export class EditBillContactModel {\n  // TODO: define fields\n}\n"],
      mappings: ";;;;;;;;;IAAaA,oBAAoB,OAAAC,aAAA,CAAAC,OAAA,WAAAF,qBAAA;EAAA,IAAAG,gBAAA,CAAAD,OAAA,QAAAF,oBAAA;AAAA;AAAjCI,OAAA,CAAAJ,oBAAA,GAAAA,oBAAA",
      ignoreList: []
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "c351e9ec0c3bae05e3398a8e03b65851979b4d7f"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_11e2t6mpox = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_11e2t6mpox();
var _interopRequireDefault =
/* istanbul ignore next */
(cov_11e2t6mpox().s[0]++, require("@babel/runtime/helpers/interopRequireDefault"));
var _createClass2 =
/* istanbul ignore next */
(cov_11e2t6mpox().s[1]++, _interopRequireDefault(require("@babel/runtime/helpers/createClass")));
var _classCallCheck2 =
/* istanbul ignore next */
(cov_11e2t6mpox().s[2]++, _interopRequireDefault(require("@babel/runtime/helpers/classCallCheck")));
/* istanbul ignore next */
cov_11e2t6mpox().s[3]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_11e2t6mpox().s[4]++;
exports.EditBillContactModel = void 0;
var EditBillContactModel =
/* istanbul ignore next */
(cov_11e2t6mpox().s[5]++, (0, _createClass2.default)(function EditBillContactModel() {
  /* istanbul ignore next */
  cov_11e2t6mpox().f[0]++;
  cov_11e2t6mpox().s[6]++;
  (0, _classCallCheck2.default)(this, EditBillContactModel);
}));
/* istanbul ignore next */
cov_11e2t6mpox().s[7]++;
exports.EditBillContactModel = EditBillContactModel;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJFZGl0QmlsbENvbnRhY3RNb2RlbCIsImNvdl8xMWUydDZtcG94IiwicyIsIl9jcmVhdGVDbGFzczIiLCJkZWZhdWx0IiwiZiIsIl9jbGFzc0NhbGxDaGVjazIiLCJleHBvcnRzIl0sInNvdXJjZXMiOlsiL1VzZXJzL3NvbnNhbXNldC9wcm9qZWN0cy9yZWFjdC1uYXRpdmUvcGF5bWVudC1tb2R1bGUvc3JjL2RvbWFpbi9lbnRpdGllcy9lZGl0LWJpbGwtY29udGFjdC9FZGl0QmlsbENvbnRhY3RNb2RlbC50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgY2xhc3MgRWRpdEJpbGxDb250YWN0TW9kZWwge1xuICAvLyBUT0RPOiBkZWZpbmUgZmllbGRzXG59XG4iXSwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztJQUFhQSxvQkFBb0I7QUFBQTtBQUFBLENBQUFDLGNBQUEsR0FBQUMsQ0FBQSxXQUFBQyxhQUFBLENBQUFDLE9BQUEsV0FBQUoscUJBQUE7RUFBQTtFQUFBQyxjQUFBLEdBQUFJLENBQUE7RUFBQUosY0FBQSxHQUFBQyxDQUFBO0VBQUEsSUFBQUksZ0JBQUEsQ0FBQUYsT0FBQSxRQUFBSixvQkFBQTtBQUFBO0FBQUE7QUFBQUMsY0FBQSxHQUFBQyxDQUFBO0FBQWpDSyxPQUFBLENBQUFQLG9CQUFBLEdBQUFBLG9CQUFBIiwiaWdub3JlTGlzdCI6W119