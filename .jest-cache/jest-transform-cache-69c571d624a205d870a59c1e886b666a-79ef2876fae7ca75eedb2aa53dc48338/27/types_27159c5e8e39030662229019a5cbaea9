414ce9382a3088a49c7db145e50e78d2
"use strict";

/* istanbul ignore next */
function cov_emsk6aaye() {
  var path = "/Users/<USER>/projects/react-native/payment-module/src/components/highlight-text/types.ts";
  var hash = "182011526283646a00fe5812b90f7de967393632";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/projects/react-native/payment-module/src/components/highlight-text/types.ts",
    statementMap: {
      "0": {
        start: {
          line: 3,
          column: 0
        },
        end: {
          line: 5,
          column: 3
        }
      }
    },
    fnMap: {},
    branchMap: {},
    s: {
      "0": 0
    },
    f: {},
    b: {},
    inputSourceMap: {
      version: 3,
      file: undefined,
      names: [],
      sourceRoot: undefined,
      sources: ["/Users/<USER>/projects/react-native/payment-module/src/components/highlight-text/types.ts"],
      sourcesContent: ["import {StyleProp} from 'react-native';\n\nexport interface HighlightTextProps {\n  text: string;\n  search: string;\n  style: StyleProp<any>;\n}\n"],
      mappings: "",
      ignoreList: []
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "182011526283646a00fe5812b90f7de967393632"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_emsk6aaye = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_emsk6aaye();
cov_emsk6aaye().s[0]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6W10sInNvdXJjZXMiOlsiL1VzZXJzL3NvbnNhbXNldC9wcm9qZWN0cy9yZWFjdC1uYXRpdmUvcGF5bWVudC1tb2R1bGUvc3JjL2NvbXBvbmVudHMvaGlnaGxpZ2h0LXRleHQvdHlwZXMudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHtTdHlsZVByb3B9IGZyb20gJ3JlYWN0LW5hdGl2ZSc7XG5cbmV4cG9ydCBpbnRlcmZhY2UgSGlnaGxpZ2h0VGV4dFByb3BzIHtcbiAgdGV4dDogc3RyaW5nO1xuICBzZWFyY2g6IHN0cmluZztcbiAgc3R5bGU6IFN0eWxlUHJvcDxhbnk+O1xufVxuIl0sIm1hcHBpbmdzIjoiIiwiaWdub3JlTGlzdCI6W119