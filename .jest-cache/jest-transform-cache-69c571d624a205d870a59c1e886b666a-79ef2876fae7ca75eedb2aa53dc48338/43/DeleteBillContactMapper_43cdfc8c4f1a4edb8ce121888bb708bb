ae9dcd5621337380d7f1207a77bcb8d5
"use strict";

/* istanbul ignore next */
function cov_14omvq1bd1() {
  var path = "/Users/<USER>/projects/react-native/payment-module/src/data/mappers/delete-bill-contact/DeleteBillContactMapper.ts";
  var hash = "39ccc8be117cd13bac09e58639207ce32f183264";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/projects/react-native/payment-module/src/data/mappers/delete-bill-contact/DeleteBillContactMapper.ts",
    statementMap: {
      "0": {
        start: {
          line: 3,
          column: 0
        },
        end: {
          line: 5,
          column: 3
        }
      },
      "1": {
        start: {
          line: 6,
          column: 0
        },
        end: {
          line: 6,
          column: 82
        }
      },
      "2": {
        start: {
          line: 7,
          column: 31
        },
        end: {
          line: 7,
          column: 109
        }
      },
      "3": {
        start: {
          line: 9,
          column: 2
        },
        end: {
          line: 9,
          column: 63
        }
      }
    },
    fnMap: {
      "0": {
        name: "mapDeleteBillContactResponseToModel",
        decl: {
          start: {
            line: 8,
            column: 9
          },
          end: {
            line: 8,
            column: 44
          }
        },
        loc: {
          start: {
            line: 8,
            column: 55
          },
          end: {
            line: 10,
            column: 1
          }
        },
        line: 8
      }
    },
    branchMap: {},
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0
    },
    f: {
      "0": 0
    },
    b: {},
    inputSourceMap: {
      version: 3,
      file: undefined,
      names: ["exports", "mapDeleteBillContactResponseToModel", "DeleteBillContactModel_1", "require", "response", "DeleteBillContactModel"],
      sourceRoot: undefined,
      sources: ["/Users/<USER>/projects/react-native/payment-module/src/data/mappers/delete-bill-contact/DeleteBillContactMapper.ts"],
      sourcesContent: ["import {DeleteBillContactResponse} from '../../models/delete-bill-contact/DeleteBillContactResponse';\nimport {DeleteBillContactModel} from '../../../domain/entities/delete-bill-contact/DeleteBillContactModel';\n\nexport function mapDeleteBillContactResponseToModel(response: DeleteBillContactResponse): DeleteBillContactModel {\n  return new DeleteBillContactModel();\n}\n"],
      mappings: ";;;;;AAGAA,OAAA,CAAAC,mCAAA,GAAAA,mCAAA;AAFA,IAAAC,wBAAA,GAAAC,OAAA;AAEA,SAAgBF,mCAAmCA,CAACG,QAAmC;EACrF,OAAO,IAAIF,wBAAA,CAAAG,sBAAsB,EAAE;AACrC",
      ignoreList: []
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "39ccc8be117cd13bac09e58639207ce32f183264"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_14omvq1bd1 = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_14omvq1bd1();
cov_14omvq1bd1().s[0]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_14omvq1bd1().s[1]++;
exports.mapDeleteBillContactResponseToModel = mapDeleteBillContactResponseToModel;
var DeleteBillContactModel_1 =
/* istanbul ignore next */
(cov_14omvq1bd1().s[2]++, require("../../../domain/entities/delete-bill-contact/DeleteBillContactModel"));
function mapDeleteBillContactResponseToModel(response) {
  /* istanbul ignore next */
  cov_14omvq1bd1().f[0]++;
  cov_14omvq1bd1().s[3]++;
  return new DeleteBillContactModel_1.DeleteBillContactModel();
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJleHBvcnRzIiwibWFwRGVsZXRlQmlsbENvbnRhY3RSZXNwb25zZVRvTW9kZWwiLCJEZWxldGVCaWxsQ29udGFjdE1vZGVsXzEiLCJjb3ZfMTRvbXZxMWJkMSIsInMiLCJyZXF1aXJlIiwicmVzcG9uc2UiLCJmIiwiRGVsZXRlQmlsbENvbnRhY3RNb2RlbCJdLCJzb3VyY2VzIjpbIi9Vc2Vycy9zb25zYW1zZXQvcHJvamVjdHMvcmVhY3QtbmF0aXZlL3BheW1lbnQtbW9kdWxlL3NyYy9kYXRhL21hcHBlcnMvZGVsZXRlLWJpbGwtY29udGFjdC9EZWxldGVCaWxsQ29udGFjdE1hcHBlci50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQge0RlbGV0ZUJpbGxDb250YWN0UmVzcG9uc2V9IGZyb20gJy4uLy4uL21vZGVscy9kZWxldGUtYmlsbC1jb250YWN0L0RlbGV0ZUJpbGxDb250YWN0UmVzcG9uc2UnO1xuaW1wb3J0IHtEZWxldGVCaWxsQ29udGFjdE1vZGVsfSBmcm9tICcuLi8uLi8uLi9kb21haW4vZW50aXRpZXMvZGVsZXRlLWJpbGwtY29udGFjdC9EZWxldGVCaWxsQ29udGFjdE1vZGVsJztcblxuZXhwb3J0IGZ1bmN0aW9uIG1hcERlbGV0ZUJpbGxDb250YWN0UmVzcG9uc2VUb01vZGVsKHJlc3BvbnNlOiBEZWxldGVCaWxsQ29udGFjdFJlc3BvbnNlKTogRGVsZXRlQmlsbENvbnRhY3RNb2RlbCB7XG4gIHJldHVybiBuZXcgRGVsZXRlQmlsbENvbnRhY3RNb2RlbCgpO1xufVxuIl0sIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUdBQSxPQUFBLENBQUFDLG1DQUFBLEdBQUFBLG1DQUFBO0FBRkEsSUFBQUMsd0JBQUE7QUFBQTtBQUFBLENBQUFDLGNBQUEsR0FBQUMsQ0FBQSxPQUFBQyxPQUFBO0FBRUEsU0FBZ0JKLG1DQUFtQ0EsQ0FBQ0ssUUFBbUM7RUFBQTtFQUFBSCxjQUFBLEdBQUFJLENBQUE7RUFBQUosY0FBQSxHQUFBQyxDQUFBO0VBQ3JGLE9BQU8sSUFBSUYsd0JBQUEsQ0FBQU0sc0JBQXNCLEVBQUU7QUFDckMiLCJpZ25vcmVMaXN0IjpbXX0=