{"version": 3, "names": ["cov_hdv4lf8ub", "actualCoverage", "react_1", "s", "__importDefault", "require", "native_stack_1", "LocaleContext_1", "ScreenNames_1", "payment_home_1", "payment_bill_1", "payment_confirm_1", "payment_info_1", "payment_result_1", "payment_result_detail_1", "payment_phone_1", "savecontact_1", "editcontact_1", "bill_detail_1", "qr_payment_info_1", "<PERSON><PERSON>", "createNativeStackNavigator", "PaymentStack", "f", "_ref", "useLocale", "locale", "default", "createElement", "Navigator", "screenOptions", "animation", "key", "Screen", "component", "name", "PaymentHomePage", "options", "headerShown", "BillDetailScreen", "PaymentBillScreen", "PaymentInfoScreen", "PaymentConfirmScreen", "PaymentResultScreen", "PaymentResultDetailScreen", "gestureEnabled", "PaymentPhoneScreen", "SaveBillContactScreen", "EditBillContactScreen", "QRPaymentInfoScreen", "exports"], "sources": ["/Users/<USER>/projects/react-native/payment-module/src/navigation/PaymentStack.tsx"], "sourcesContent": ["import React from 'react';\nimport {createNativeStackNavigator} from '@react-navigation/native-stack';\nimport {useLocale} from 'msb-communication-lib/dist/locales/LocaleContext';\nimport ScreenNames from '../commons/ScreenNames';\nimport PaymentHomePage from '../presentation/payment-home';\nimport PaymentBillScreen from '../presentation/payment-bill';\nimport PaymentConfirmScreen from '../presentation/payment-confirm';\nimport PaymentInfoScreen from '../presentation/payment-info';\nimport PaymentResultScreen from '../presentation/payment-result';\nimport {PaymentInfoModel, QRPaymentInfoModel} from './types';\nimport PaymentResultDetailScreen from '../presentation/payment-result-detail';\nimport PaymentPhoneScreen from '../presentation/payment-phone';\nimport SaveBillContactScreen from '../presentation/savecontact';\nimport {CategoryModel} from '../domain/entities/category-list/CategoryListModel';\nimport EditBillContactScreen from '../presentation/editcontact';\nimport BillDetailScreen from '../presentation/bill-detail';\nimport {AccountModel} from '../domain/entities/my-bill-contact-list/MyBillContactListModel';\nimport {IBillContact} from '../domain/entities/IBillContact';\nimport QRPaymentInfoScreen from '../presentation/qr-payment-info';\nconst Stack = createNativeStackNavigator<PaymentStackParamList>();\n\nexport type MobilePaymentStackParamList = {\n  [ScreenNames.PostpaidMobileScreen]: undefined;\n  [ScreenNames.PostpaidMobileInfoScreen]: {\n    phoneNumber: string;\n    provider: string;\n    amount: number;\n  };\n  [ScreenNames.PrepaidMobileScreen]: undefined;\n};\nexport type PaymentStackParamList = {\n  [ScreenNames.PaymentHomePage]: undefined;\n  [ScreenNames.BillDetailScreen]: {\n    account: AccountModel;\n    contact: IBillContact;\n  };\n  [ScreenNames.PaymentBillScreen]: any;\n  [ScreenNames.SaveBillContactScreen]: any;\n  [ScreenNames.EditBillContactScreen]: any;\n\n  [ScreenNames.PaymentConfirmScreen]: {\n    paymentInfo: PaymentInfoModel;\n    hasPeriod?: boolean;\n  };\n  [ScreenNames.PaymentInfoScreen]: {\n    paymentInfo: PaymentInfoModel;\n  };\n  [ScreenNames.QRPaymentInfoScreen]: {\n    paymentInfo: QRPaymentInfoModel;\n  };\n  [ScreenNames.PaymentResultScreen]: {\n    paymentInfo: PaymentInfoModel;\n  };\n  [ScreenNames.PaymentResultDetailScreen]: {\n    paymentInfo: PaymentInfoModel;\n  };\n  SegmentStack: undefined;\n  PaymentStack: undefined;\n  BottomTabs: undefined;\n  PaymentPhoneScreen: {\n    category: CategoryModel;\n  };\n};\n\nconst PaymentStack = () => {\n  const {locale} = useLocale();\n\n  return (\n    <Stack.Navigator screenOptions={{animation: 'slide_from_right'}} key={locale}>\n      <Stack.Screen component={PaymentHomePage} name={ScreenNames.PaymentHomePage} options={{headerShown: false}} />\n      <Stack.Screen component={BillDetailScreen} name={ScreenNames.BillDetailScreen} options={{headerShown: false}} />\n      <Stack.Screen component={PaymentBillScreen} name={ScreenNames.PaymentBillScreen} options={{headerShown: false}} />\n      <Stack.Screen component={PaymentInfoScreen} name={ScreenNames.PaymentInfoScreen} options={{headerShown: false}} />\n      <Stack.Screen\n        component={PaymentConfirmScreen}\n        name={ScreenNames.PaymentConfirmScreen}\n        options={{headerShown: false}}\n      />\n      <Stack.Screen\n        component={PaymentResultScreen}\n        name={ScreenNames.PaymentResultScreen}\n        options={{headerShown: false}}\n      />\n      <Stack.Screen\n        component={PaymentResultDetailScreen}\n        name={ScreenNames.PaymentResultDetailScreen}\n        options={{\n          headerShown: false,\n          animation: 'slide_from_bottom',\n          gestureEnabled: false,\n        }}\n      />\n      <Stack.Screen\n        component={PaymentPhoneScreen}\n        name={ScreenNames.PaymentPhoneScreen}\n        options={{headerShown: false}}\n      />\n      <Stack.Screen\n        component={SaveBillContactScreen}\n        name={ScreenNames.SaveBillContactScreen}\n        options={{headerShown: false}}\n      />\n      <Stack.Screen\n        component={EditBillContactScreen}\n        name={ScreenNames.EditBillContactScreen}\n        options={{headerShown: false}}\n      />\n      <Stack.Screen\n        component={QRPaymentInfoScreen}\n        name={ScreenNames.QRPaymentInfoScreen}\n        options={{headerShown: false}}\n      />\n    </Stack.Navigator>\n  );\n};\n\nexport default PaymentStack;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAKA;IAAAA,aAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,aAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;AALA,IAAAE,OAAA;AAAA;AAAA,CAAAF,aAAA,GAAAG,CAAA,OAAAC,eAAA,CAAAC,OAAA;AACA,IAAAC,cAAA;AAAA;AAAA,CAAAN,aAAA,GAAAG,CAAA,OAAAE,OAAA;AACA,IAAAE,eAAA;AAAA;AAAA,CAAAP,aAAA,GAAAG,CAAA,OAAAE,OAAA;AACA,IAAAG,aAAA;AAAA;AAAA,CAAAR,aAAA,GAAAG,CAAA,OAAAC,eAAA,CAAAC,OAAA;AACA,IAAAI,cAAA;AAAA;AAAA,CAAAT,aAAA,GAAAG,CAAA,OAAAC,eAAA,CAAAC,OAAA;AACA,IAAAK,cAAA;AAAA;AAAA,CAAAV,aAAA,GAAAG,CAAA,OAAAC,eAAA,CAAAC,OAAA;AACA,IAAAM,iBAAA;AAAA;AAAA,CAAAX,aAAA,GAAAG,CAAA,OAAAC,eAAA,CAAAC,OAAA;AACA,IAAAO,cAAA;AAAA;AAAA,CAAAZ,aAAA,GAAAG,CAAA,QAAAC,eAAA,CAAAC,OAAA;AACA,IAAAQ,gBAAA;AAAA;AAAA,CAAAb,aAAA,GAAAG,CAAA,QAAAC,eAAA,CAAAC,OAAA;AAEA,IAAAS,uBAAA;AAAA;AAAA,CAAAd,aAAA,GAAAG,CAAA,QAAAC,eAAA,CAAAC,OAAA;AACA,IAAAU,eAAA;AAAA;AAAA,CAAAf,aAAA,GAAAG,CAAA,QAAAC,eAAA,CAAAC,OAAA;AACA,IAAAW,aAAA;AAAA;AAAA,CAAAhB,aAAA,GAAAG,CAAA,QAAAC,eAAA,CAAAC,OAAA;AAEA,IAAAY,aAAA;AAAA;AAAA,CAAAjB,aAAA,GAAAG,CAAA,QAAAC,eAAA,CAAAC,OAAA;AACA,IAAAa,aAAA;AAAA;AAAA,CAAAlB,aAAA,GAAAG,CAAA,QAAAC,eAAA,CAAAC,OAAA;AAGA,IAAAc,iBAAA;AAAA;AAAA,CAAAnB,aAAA,GAAAG,CAAA,QAAAC,eAAA,CAAAC,OAAA;AACA,IAAMe,KAAK;AAAA;AAAA,CAAApB,aAAA,GAAAG,CAAA,QAAG,IAAAG,cAAA,CAAAe,0BAA0B,GAAyB;AAAA;AAAArB,aAAA,GAAAG,CAAA;AA6CjE,IAAMmB,YAAY,GAAG,SAAfA,YAAYA,CAAA,EAAQ;EAAA;EAAAtB,aAAA,GAAAuB,CAAA;EACxB,IAAAC,IAAA;IAAA;IAAA,CAAAxB,aAAA,GAAAG,CAAA,QAAiB,IAAAI,eAAA,CAAAkB,SAAS,GAAE;IAArBC,MAAM;IAAA;IAAA,CAAA1B,aAAA,GAAAG,CAAA,QAAAqB,IAAA,CAANE,MAAM;EAAA;EAAA1B,aAAA,GAAAG,CAAA;EAEb,OACED,OAAA,CAAAyB,OAAA,CAAAC,aAAA,CAACR,KAAK,CAACS,SAAS;IAACC,aAAa,EAAE;MAACC,SAAS,EAAE;IAAkB,CAAC;IAAEC,GAAG,EAAEN;EAAM,GAC1ExB,OAAA,CAAAyB,OAAA,CAAAC,aAAA,CAACR,KAAK,CAACa,MAAM;IAACC,SAAS,EAAEzB,cAAA,CAAAkB,OAAe;IAAEQ,IAAI,EAAE3B,aAAA,CAAAmB,OAAW,CAACS,eAAe;IAAEC,OAAO,EAAE;MAACC,WAAW,EAAE;IAAK;EAAC,EAAI,EAC9GpC,OAAA,CAAAyB,OAAA,CAAAC,aAAA,CAACR,KAAK,CAACa,MAAM;IAACC,SAAS,EAAEhB,aAAA,CAAAS,OAAgB;IAAEQ,IAAI,EAAE3B,aAAA,CAAAmB,OAAW,CAACY,gBAAgB;IAAEF,OAAO,EAAE;MAACC,WAAW,EAAE;IAAK;EAAC,EAAI,EAChHpC,OAAA,CAAAyB,OAAA,CAAAC,aAAA,CAACR,KAAK,CAACa,MAAM;IAACC,SAAS,EAAExB,cAAA,CAAAiB,OAAiB;IAAEQ,IAAI,EAAE3B,aAAA,CAAAmB,OAAW,CAACa,iBAAiB;IAAEH,OAAO,EAAE;MAACC,WAAW,EAAE;IAAK;EAAC,EAAI,EAClHpC,OAAA,CAAAyB,OAAA,CAAAC,aAAA,CAACR,KAAK,CAACa,MAAM;IAACC,SAAS,EAAEtB,cAAA,CAAAe,OAAiB;IAAEQ,IAAI,EAAE3B,aAAA,CAAAmB,OAAW,CAACc,iBAAiB;IAAEJ,OAAO,EAAE;MAACC,WAAW,EAAE;IAAK;EAAC,EAAI,EAClHpC,OAAA,CAAAyB,OAAA,CAAAC,aAAA,CAACR,KAAK,CAACa,MAAM;IACXC,SAAS,EAAEvB,iBAAA,CAAAgB,OAAoB;IAC/BQ,IAAI,EAAE3B,aAAA,CAAAmB,OAAW,CAACe,oBAAoB;IACtCL,OAAO,EAAE;MAACC,WAAW,EAAE;IAAK;EAAC,EAC7B,EACFpC,OAAA,CAAAyB,OAAA,CAAAC,aAAA,CAACR,KAAK,CAACa,MAAM;IACXC,SAAS,EAAErB,gBAAA,CAAAc,OAAmB;IAC9BQ,IAAI,EAAE3B,aAAA,CAAAmB,OAAW,CAACgB,mBAAmB;IACrCN,OAAO,EAAE;MAACC,WAAW,EAAE;IAAK;EAAC,EAC7B,EACFpC,OAAA,CAAAyB,OAAA,CAAAC,aAAA,CAACR,KAAK,CAACa,MAAM;IACXC,SAAS,EAAEpB,uBAAA,CAAAa,OAAyB;IACpCQ,IAAI,EAAE3B,aAAA,CAAAmB,OAAW,CAACiB,yBAAyB;IAC3CP,OAAO,EAAE;MACPC,WAAW,EAAE,KAAK;MAClBP,SAAS,EAAE,mBAAmB;MAC9Bc,cAAc,EAAE;;EACjB,EACD,EACF3C,OAAA,CAAAyB,OAAA,CAAAC,aAAA,CAACR,KAAK,CAACa,MAAM;IACXC,SAAS,EAAEnB,eAAA,CAAAY,OAAkB;IAC7BQ,IAAI,EAAE3B,aAAA,CAAAmB,OAAW,CAACmB,kBAAkB;IACpCT,OAAO,EAAE;MAACC,WAAW,EAAE;IAAK;EAAC,EAC7B,EACFpC,OAAA,CAAAyB,OAAA,CAAAC,aAAA,CAACR,KAAK,CAACa,MAAM;IACXC,SAAS,EAAElB,aAAA,CAAAW,OAAqB;IAChCQ,IAAI,EAAE3B,aAAA,CAAAmB,OAAW,CAACoB,qBAAqB;IACvCV,OAAO,EAAE;MAACC,WAAW,EAAE;IAAK;EAAC,EAC7B,EACFpC,OAAA,CAAAyB,OAAA,CAAAC,aAAA,CAACR,KAAK,CAACa,MAAM;IACXC,SAAS,EAAEjB,aAAA,CAAAU,OAAqB;IAChCQ,IAAI,EAAE3B,aAAA,CAAAmB,OAAW,CAACqB,qBAAqB;IACvCX,OAAO,EAAE;MAACC,WAAW,EAAE;IAAK;EAAC,EAC7B,EACFpC,OAAA,CAAAyB,OAAA,CAAAC,aAAA,CAACR,KAAK,CAACa,MAAM;IACXC,SAAS,EAAEf,iBAAA,CAAAQ,OAAmB;IAC9BQ,IAAI,EAAE3B,aAAA,CAAAmB,OAAW,CAACsB,mBAAmB;IACrCZ,OAAO,EAAE;MAACC,WAAW,EAAE;IAAK;EAAC,EAC7B,CACc;AAEtB,CAAC;AAAA;AAAAtC,aAAA,GAAAG,CAAA;AAED+C,OAAA,CAAAvB,OAAA,GAAeL,YAAY", "ignoreList": []}