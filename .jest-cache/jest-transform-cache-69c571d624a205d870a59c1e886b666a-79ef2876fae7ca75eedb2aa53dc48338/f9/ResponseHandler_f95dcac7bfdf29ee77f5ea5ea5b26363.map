{"version": 3, "names": ["cov_1lco4dccom", "actualCoverage", "MSBCustomError_1", "s", "require", "MSBErrorCode_1", "handleResponse", "f", "_ref", "_asyncToGenerator2", "default", "response", "console", "log", "status", "b", "ok", "data", "json", "errors", "Object", "assign", "key", "MSBErrorCode", "UNKNOWN_ERROR", "error", "CustomError", "createError", "_x", "apply", "arguments", "exports"], "sources": ["/Users/<USER>/projects/react-native/payment-module/src/utils/ResponseHandler.ts"], "sourcesContent": ["import {createError, CustomError} from '../core/MSBCustomError';\nimport {MSBErrorCode} from '../core/MSBErrorCode';\n\nexport const handleResponse = async (response: Response): Promise<any> => {\n  try {\n    console.log('✅✅✅✅✅✅✅✅✅✅✅DataSource Response Successs', response);\n    if (response.status === 204) {\n      return {};\n    } else if (response.ok === false) {\n      const data = await response.json();\n      if (data.errors) {\n        return data;\n      } else {\n        console.log('ADDED UNKNOWN ERROR TO HANDLE DATA');\n        return {\n          errors: [\n            {\n              key: MSBErrorCode.UNKNOWN_ERROR,\n            },\n          ],\n          ...data,\n        };\n      }\n    }\n    return await response.json();\n  } catch (error) {\n    console.log('❌❌❌❌❌❌❌❌❌❌ DataSource HTTP or Json mapper Error', response);\n    if (error instanceof CustomError) {\n      throw error;\n    }\n    throw createError();\n  }\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAOM;IAAAA,cAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,cAAA;;;;;;;;;;;;;;;AAPN,IAAAE,gBAAA;AAAA;AAAA,CAAAF,cAAA,GAAAG,CAAA,OAAAC,OAAA;AACA,IAAAC,cAAA;AAAA;AAAA,CAAAL,cAAA,GAAAG,CAAA,OAAAC,OAAA;AAEO,IAAME,cAAc;AAAA;AAAA,CAAAN,cAAA,GAAAG,CAAA;EAAA;EAAAH,cAAA,GAAAO,CAAA;EAAA,IAAAC,IAAA;EAAA;EAAA,CAAAR,cAAA,GAAAG,CAAA,WAAAM,kBAAA,CAAAC,OAAA,EAAG,WAAOC,QAAkB,EAAkB;IAAA;IAAAX,cAAA,GAAAO,CAAA;IAAAP,cAAA,GAAAG,CAAA;IACvE,IAAI;MAAA;MAAAH,cAAA,GAAAG,CAAA;MACFS,OAAO,CAACC,GAAG,CAAC,yCAAyC,EAAEF,QAAQ,CAAC;MAAA;MAAAX,cAAA,GAAAG,CAAA;MAChE,IAAIQ,QAAQ,CAACG,MAAM,KAAK,GAAG,EAAE;QAAA;QAAAd,cAAA,GAAAe,CAAA;QAAAf,cAAA,GAAAG,CAAA;QAC3B,OAAO,EAAE;MACX,CAAC,MAAM;QAAA;QAAAH,cAAA,GAAAe,CAAA;QAAAf,cAAA,GAAAG,CAAA;QAAA,IAAIQ,QAAQ,CAACK,EAAE,KAAK,KAAK,EAAE;UAAA;UAAAhB,cAAA,GAAAe,CAAA;UAChC,IAAME,IAAI;UAAA;UAAA,CAAAjB,cAAA,GAAAG,CAAA,cAASQ,QAAQ,CAACO,IAAI,EAAE;UAAA;UAAAlB,cAAA,GAAAG,CAAA;UAClC,IAAIc,IAAI,CAACE,MAAM,EAAE;YAAA;YAAAnB,cAAA,GAAAe,CAAA;YAAAf,cAAA,GAAAG,CAAA;YACf,OAAOc,IAAI;UACb,CAAC,MAAM;YAAA;YAAAjB,cAAA,GAAAe,CAAA;YAAAf,cAAA,GAAAG,CAAA;YACLS,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC;YAAA;YAAAb,cAAA,GAAAG,CAAA;YACjD,OAAAiB,MAAA,CAAAC,MAAA;cACEF,MAAM,EAAE,CACN;gBACEG,GAAG,EAAEjB,cAAA,CAAAkB,YAAY,CAACC;eACnB;YACF,GACEP,IAAI;UAEX;QACF;QAAA;QAAA;UAAAjB,cAAA,GAAAe,CAAA;QAAA;MAAA;MAAA;MAAAf,cAAA,GAAAG,CAAA;MACA,aAAaQ,QAAQ,CAACO,IAAI,EAAE;IAC9B,CAAC,CAAC,OAAOO,KAAK,EAAE;MAAA;MAAAzB,cAAA,GAAAG,CAAA;MACdS,OAAO,CAACC,GAAG,CAAC,iDAAiD,EAAEF,QAAQ,CAAC;MAAA;MAAAX,cAAA,GAAAG,CAAA;MACxE,IAAIsB,KAAK,YAAYvB,gBAAA,CAAAwB,WAAW,EAAE;QAAA;QAAA1B,cAAA,GAAAe,CAAA;QAAAf,cAAA,GAAAG,CAAA;QAChC,MAAMsB,KAAK;MACb;MAAA;MAAA;QAAAzB,cAAA,GAAAe,CAAA;MAAA;MAAAf,cAAA,GAAAG,CAAA;MACA,MAAM,IAAAD,gBAAA,CAAAyB,WAAW,GAAE;IACrB;EACF,CAAC;EAAA;EAAA3B,cAAA,GAAAG,CAAA;EAAA,gBA7BYG,cAAcA,CAAAsB,EAAA;IAAA;IAAA5B,cAAA,GAAAO,CAAA;IAAAP,cAAA,GAAAG,CAAA;IAAA,OAAAK,IAAA,CAAAqB,KAAA,OAAAC,SAAA;EAAA;AAAA,GA6B1B;AAAA;AAAA9B,cAAA,GAAAG,CAAA;AA7BY4B,OAAA,CAAAzB,cAAc,GAAAA,cAAA", "ignoreList": []}