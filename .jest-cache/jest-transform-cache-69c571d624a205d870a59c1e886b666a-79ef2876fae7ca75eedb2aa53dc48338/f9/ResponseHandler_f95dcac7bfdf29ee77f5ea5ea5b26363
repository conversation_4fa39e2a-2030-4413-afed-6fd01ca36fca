f8794f3e48446537d9500e223f9527e2
"use strict";

/* istanbul ignore next */
function cov_1lco4dccom() {
  var path = "/Users/<USER>/projects/react-native/payment-module/src/utils/ResponseHandler.ts";
  var hash = "07bc550af34f22f044c376aaa7e240cbba9ecfb9";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/projects/react-native/payment-module/src/utils/ResponseHandler.ts",
    statementMap: {
      "0": {
        start: {
          line: 3,
          column: 29
        },
        end: {
          line: 3,
          column: 84
        }
      },
      "1": {
        start: {
          line: 4,
          column: 25
        },
        end: {
          line: 4,
          column: 99
        }
      },
      "2": {
        start: {
          line: 5,
          column: 0
        },
        end: {
          line: 7,
          column: 3
        }
      },
      "3": {
        start: {
          line: 8,
          column: 0
        },
        end: {
          line: 8,
          column: 32
        }
      },
      "4": {
        start: {
          line: 9,
          column: 23
        },
        end: {
          line: 9,
          column: 56
        }
      },
      "5": {
        start: {
          line: 10,
          column: 21
        },
        end: {
          line: 10,
          column: 52
        }
      },
      "6": {
        start: {
          line: 11,
          column: 21
        },
        end: {
          line: 42,
          column: 3
        }
      },
      "7": {
        start: {
          line: 12,
          column: 13
        },
        end: {
          line: 38,
          column: 4
        }
      },
      "8": {
        start: {
          line: 13,
          column: 4
        },
        end: {
          line: 37,
          column: 5
        }
      },
      "9": {
        start: {
          line: 14,
          column: 6
        },
        end: {
          line: 14,
          column: 71
        }
      },
      "10": {
        start: {
          line: 15,
          column: 6
        },
        end: {
          line: 29,
          column: 7
        }
      },
      "11": {
        start: {
          line: 16,
          column: 8
        },
        end: {
          line: 16,
          column: 18
        }
      },
      "12": {
        start: {
          line: 17,
          column: 13
        },
        end: {
          line: 29,
          column: 7
        }
      },
      "13": {
        start: {
          line: 18,
          column: 19
        },
        end: {
          line: 18,
          column: 40
        }
      },
      "14": {
        start: {
          line: 19,
          column: 8
        },
        end: {
          line: 28,
          column: 9
        }
      },
      "15": {
        start: {
          line: 20,
          column: 10
        },
        end: {
          line: 20,
          column: 22
        }
      },
      "16": {
        start: {
          line: 22,
          column: 10
        },
        end: {
          line: 22,
          column: 60
        }
      },
      "17": {
        start: {
          line: 23,
          column: 10
        },
        end: {
          line: 27,
          column: 19
        }
      },
      "18": {
        start: {
          line: 30,
          column: 6
        },
        end: {
          line: 30,
          column: 35
        }
      },
      "19": {
        start: {
          line: 32,
          column: 6
        },
        end: {
          line: 32,
          column: 79
        }
      },
      "20": {
        start: {
          line: 33,
          column: 6
        },
        end: {
          line: 35,
          column: 7
        }
      },
      "21": {
        start: {
          line: 34,
          column: 8
        },
        end: {
          line: 34,
          column: 20
        }
      },
      "22": {
        start: {
          line: 36,
          column: 6
        },
        end: {
          line: 36,
          column: 48
        }
      },
      "23": {
        start: {
          line: 39,
          column: 2
        },
        end: {
          line: 41,
          column: 4
        }
      },
      "24": {
        start: {
          line: 40,
          column: 4
        },
        end: {
          line: 40,
          column: 39
        }
      },
      "25": {
        start: {
          line: 43,
          column: 0
        },
        end: {
          line: 43,
          column: 40
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 11,
            column: 21
          },
          end: {
            line: 11,
            column: 22
          }
        },
        loc: {
          start: {
            line: 11,
            column: 33
          },
          end: {
            line: 42,
            column: 1
          }
        },
        line: 11
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 12,
            column: 45
          },
          end: {
            line: 12,
            column: 46
          }
        },
        loc: {
          start: {
            line: 12,
            column: 66
          },
          end: {
            line: 38,
            column: 3
          }
        },
        line: 12
      },
      "2": {
        name: "handleResponse",
        decl: {
          start: {
            line: 39,
            column: 18
          },
          end: {
            line: 39,
            column: 32
          }
        },
        loc: {
          start: {
            line: 39,
            column: 37
          },
          end: {
            line: 41,
            column: 3
          }
        },
        line: 39
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 15,
            column: 6
          },
          end: {
            line: 29,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 15,
            column: 6
          },
          end: {
            line: 29,
            column: 7
          }
        }, {
          start: {
            line: 17,
            column: 13
          },
          end: {
            line: 29,
            column: 7
          }
        }],
        line: 15
      },
      "1": {
        loc: {
          start: {
            line: 17,
            column: 13
          },
          end: {
            line: 29,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 17,
            column: 13
          },
          end: {
            line: 29,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 17
      },
      "2": {
        loc: {
          start: {
            line: 19,
            column: 8
          },
          end: {
            line: 28,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 19,
            column: 8
          },
          end: {
            line: 28,
            column: 9
          }
        }, {
          start: {
            line: 21,
            column: 15
          },
          end: {
            line: 28,
            column: 9
          }
        }],
        line: 19
      },
      "3": {
        loc: {
          start: {
            line: 33,
            column: 6
          },
          end: {
            line: 35,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 33,
            column: 6
          },
          end: {
            line: 35,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 33
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0
    },
    b: {
      "0": [0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0]
    },
    inputSourceMap: {
      version: 3,
      file: undefined,
      names: ["MSBCustomError_1", "require", "MSBErrorCode_1", "handleResponse", "_ref", "_asyncToGenerator2", "default", "response", "console", "log", "status", "ok", "data", "json", "errors", "Object", "assign", "key", "MSBErrorCode", "UNKNOWN_ERROR", "error", "CustomError", "createError", "_x", "apply", "arguments", "exports"],
      sourceRoot: undefined,
      sources: ["/Users/<USER>/projects/react-native/payment-module/src/utils/ResponseHandler.ts"],
      sourcesContent: ["import {createError, CustomError} from '../core/MSBCustomError';\nimport {MSBErrorCode} from '../core/MSBErrorCode';\n\nexport const handleResponse = async (response: Response): Promise<any> => {\n  try {\n    console.log('\u2705\u2705\u2705\u2705\u2705\u2705\u2705\u2705\u2705\u2705\u2705DataSource Response Successs', response);\n    if (response.status === 204) {\n      return {};\n    } else if (response.ok === false) {\n      const data = await response.json();\n      if (data.errors) {\n        return data;\n      } else {\n        console.log('ADDED UNKNOWN ERROR TO HANDLE DATA');\n        return {\n          errors: [\n            {\n              key: MSBErrorCode.UNKNOWN_ERROR,\n            },\n          ],\n          ...data,\n        };\n      }\n    }\n    return await response.json();\n  } catch (error) {\n    console.log('\u274C\u274C\u274C\u274C\u274C\u274C\u274C\u274C\u274C\u274C DataSource HTTP or Json mapper Error', response);\n    if (error instanceof CustomError) {\n      throw error;\n    }\n    throw createError();\n  }\n};\n"],
      mappings: ";;;;;;;;AAAA,IAAAA,gBAAA,GAAAC,OAAA;AACA,IAAAC,cAAA,GAAAD,OAAA;AAEO,IAAME,cAAc;EAAA,IAAAC,IAAA,OAAAC,kBAAA,CAAAC,OAAA,EAAG,WAAOC,QAAkB,EAAkB;IACvE,IAAI;MACFC,OAAO,CAACC,GAAG,CAAC,yCAAyC,EAAEF,QAAQ,CAAC;MAChE,IAAIA,QAAQ,CAACG,MAAM,KAAK,GAAG,EAAE;QAC3B,OAAO,EAAE;MACX,CAAC,MAAM,IAAIH,QAAQ,CAACI,EAAE,KAAK,KAAK,EAAE;QAChC,IAAMC,IAAI,SAASL,QAAQ,CAACM,IAAI,EAAE;QAClC,IAAID,IAAI,CAACE,MAAM,EAAE;UACf,OAAOF,IAAI;QACb,CAAC,MAAM;UACLJ,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC;UACjD,OAAAM,MAAA,CAAAC,MAAA;YACEF,MAAM,EAAE,CACN;cACEG,GAAG,EAAEf,cAAA,CAAAgB,YAAY,CAACC;aACnB;UACF,GACEP,IAAI;QAEX;MACF;MACA,aAAaL,QAAQ,CAACM,IAAI,EAAE;IAC9B,CAAC,CAAC,OAAOO,KAAK,EAAE;MACdZ,OAAO,CAACC,GAAG,CAAC,iDAAiD,EAAEF,QAAQ,CAAC;MACxE,IAAIa,KAAK,YAAYpB,gBAAA,CAAAqB,WAAW,EAAE;QAChC,MAAMD,KAAK;MACb;MACA,MAAM,IAAApB,gBAAA,CAAAsB,WAAW,GAAE;IACrB;EACF,CAAC;EAAA,gBA7BYnB,cAAcA,CAAAoB,EAAA;IAAA,OAAAnB,IAAA,CAAAoB,KAAA,OAAAC,SAAA;EAAA;AAAA,GA6B1B;AA7BYC,OAAA,CAAAvB,cAAc,GAAAA,cAAA",
      ignoreList: []
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "07bc550af34f22f044c376aaa7e240cbba9ecfb9"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_1lco4dccom = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_1lco4dccom();
var _interopRequireDefault =
/* istanbul ignore next */
(cov_1lco4dccom().s[0]++, require("@babel/runtime/helpers/interopRequireDefault"));
var _asyncToGenerator2 =
/* istanbul ignore next */
(cov_1lco4dccom().s[1]++, _interopRequireDefault(require("@babel/runtime/helpers/asyncToGenerator")));
/* istanbul ignore next */
cov_1lco4dccom().s[2]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_1lco4dccom().s[3]++;
exports.handleResponse = void 0;
var MSBCustomError_1 =
/* istanbul ignore next */
(cov_1lco4dccom().s[4]++, require("../core/MSBCustomError"));
var MSBErrorCode_1 =
/* istanbul ignore next */
(cov_1lco4dccom().s[5]++, require("../core/MSBErrorCode"));
var handleResponse =
/* istanbul ignore next */
(cov_1lco4dccom().s[6]++, function () {
  /* istanbul ignore next */
  cov_1lco4dccom().f[0]++;
  var _ref =
  /* istanbul ignore next */
  (cov_1lco4dccom().s[7]++, (0, _asyncToGenerator2.default)(function* (response) {
    /* istanbul ignore next */
    cov_1lco4dccom().f[1]++;
    cov_1lco4dccom().s[8]++;
    try {
      /* istanbul ignore next */
      cov_1lco4dccom().s[9]++;
      console.log('✅✅✅✅✅✅✅✅✅✅✅DataSource Response Successs', response);
      /* istanbul ignore next */
      cov_1lco4dccom().s[10]++;
      if (response.status === 204) {
        /* istanbul ignore next */
        cov_1lco4dccom().b[0][0]++;
        cov_1lco4dccom().s[11]++;
        return {};
      } else {
        /* istanbul ignore next */
        cov_1lco4dccom().b[0][1]++;
        cov_1lco4dccom().s[12]++;
        if (response.ok === false) {
          /* istanbul ignore next */
          cov_1lco4dccom().b[1][0]++;
          var data =
          /* istanbul ignore next */
          (cov_1lco4dccom().s[13]++, yield response.json());
          /* istanbul ignore next */
          cov_1lco4dccom().s[14]++;
          if (data.errors) {
            /* istanbul ignore next */
            cov_1lco4dccom().b[2][0]++;
            cov_1lco4dccom().s[15]++;
            return data;
          } else {
            /* istanbul ignore next */
            cov_1lco4dccom().b[2][1]++;
            cov_1lco4dccom().s[16]++;
            console.log('ADDED UNKNOWN ERROR TO HANDLE DATA');
            /* istanbul ignore next */
            cov_1lco4dccom().s[17]++;
            return Object.assign({
              errors: [{
                key: MSBErrorCode_1.MSBErrorCode.UNKNOWN_ERROR
              }]
            }, data);
          }
        } else
        /* istanbul ignore next */
        {
          cov_1lco4dccom().b[1][1]++;
        }
      }
      /* istanbul ignore next */
      cov_1lco4dccom().s[18]++;
      return yield response.json();
    } catch (error) {
      /* istanbul ignore next */
      cov_1lco4dccom().s[19]++;
      console.log('❌❌❌❌❌❌❌❌❌❌ DataSource HTTP or Json mapper Error', response);
      /* istanbul ignore next */
      cov_1lco4dccom().s[20]++;
      if (error instanceof MSBCustomError_1.CustomError) {
        /* istanbul ignore next */
        cov_1lco4dccom().b[3][0]++;
        cov_1lco4dccom().s[21]++;
        throw error;
      } else
      /* istanbul ignore next */
      {
        cov_1lco4dccom().b[3][1]++;
      }
      cov_1lco4dccom().s[22]++;
      throw (0, MSBCustomError_1.createError)();
    }
  }));
  /* istanbul ignore next */
  cov_1lco4dccom().s[23]++;
  return function handleResponse(_x) {
    /* istanbul ignore next */
    cov_1lco4dccom().f[2]++;
    cov_1lco4dccom().s[24]++;
    return _ref.apply(this, arguments);
  };
}());
/* istanbul ignore next */
cov_1lco4dccom().s[25]++;
exports.handleResponse = handleResponse;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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