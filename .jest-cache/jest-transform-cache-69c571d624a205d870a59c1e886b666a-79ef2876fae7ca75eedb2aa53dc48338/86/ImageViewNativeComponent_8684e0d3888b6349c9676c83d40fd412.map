{"version": 3, "names": ["NativeComponentRegistry", "_interopRequireWildcard", "require", "_ViewConfigIgnore", "_codegenNativeCommands", "_interopRequireDefault", "_Platform", "_getRequireWildcardCache", "e", "WeakMap", "r", "t", "__esModule", "default", "has", "get", "n", "__proto__", "a", "Object", "defineProperty", "getOwnPropertyDescriptor", "u", "hasOwnProperty", "call", "i", "set", "Commands", "exports", "codegenNativeCommands", "supportedCommands", "__INTERNAL_VIEW_CONFIG", "Platform", "OS", "uiViewClassName", "bubblingEventTypes", "directEventTypes", "topLoadStart", "registrationName", "topProgress", "topError", "topLoad", "topLoadEnd", "validAttributes", "blurRadius", "defaultSource", "internal_analyticTag", "resizeMethod", "resizeMode", "resizeMultiplier", "tintColor", "process", "borderBottomLeftRadius", "borderTopLeftRadius", "src", "source", "borderRadius", "headers", "shouldNotifyLoadEvents", "overlayColor", "borderColor", "accessible", "progressiveRenderingEnabled", "fadeDuration", "borderBottomRightRadius", "borderTopRightRadius", "loadingIndicatorSrc", "topPartialLoad", "assign", "capInsets", "diff", "ConditionallyIgnoredEventHandlers", "onLoadStart", "onLoad", "onLoadEnd", "onProgress", "onError", "onPartialLoad", "ImageViewNativeComponent", "_default"], "sources": ["ImageViewNativeComponent.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @flow strict-local\n * @format\n */\n\nimport type {ViewProps} from '../Components/View/ViewPropTypes';\nimport type {\n  HostComponent,\n  HostInstance,\n  PartialViewConfig,\n} from '../Renderer/shims/ReactNativeTypes';\nimport type {\n  ColorValue,\n  DangerouslyImpreciseStyle,\n  ImageStyleProp,\n} from '../StyleSheet/StyleSheet';\nimport type {ResolvedAssetSource} from './AssetSourceResolver';\nimport type {ImageProps} from './ImageProps';\nimport type {ImageSource} from './ImageSource';\n\nimport * as NativeComponentRegistry from '../NativeComponent/NativeComponentRegistry';\nimport {ConditionallyIgnoredEventHandlers} from '../NativeComponent/ViewConfigIgnore';\nimport codegenNativeCommands from '../Utilities/codegenNativeCommands';\nimport Platform from '../Utilities/Platform';\n\ntype Props = $ReadOnly<{\n  ...ImageProps,\n  ...ViewProps,\n\n  style?: ImageStyleProp | DangerouslyImpreciseStyle,\n\n  // iOS native props\n  tintColor?: ColorValue,\n\n  // Android native props\n  shouldNotifyLoadEvents?: boolean,\n  src?:\n    | ?ResolvedAssetSource\n    | ?$ReadOnlyArray<?$ReadOnly<{uri?: ?string, ...}>>,\n  headers?: ?{[string]: string},\n  defaultSource?: ?ImageSource | ?string,\n  loadingIndicatorSrc?: ?string,\n}>;\n\ninterface NativeCommands {\n  +setIsVisible_EXPERIMENTAL: (\n    viewRef: HostInstance,\n    isVisible: boolean,\n    time: number,\n  ) => void;\n}\n\nexport const Commands: NativeCommands = codegenNativeCommands<NativeCommands>({\n  supportedCommands: ['setIsVisible_EXPERIMENTAL'],\n});\n\nexport const __INTERNAL_VIEW_CONFIG: PartialViewConfig =\n  Platform.OS === 'android'\n    ? {\n        uiViewClassName: 'RCTImageView',\n        bubblingEventTypes: {},\n        directEventTypes: {\n          topLoadStart: {\n            registrationName: 'onLoadStart',\n          },\n          topProgress: {\n            registrationName: 'onProgress',\n          },\n          topError: {\n            registrationName: 'onError',\n          },\n          topLoad: {\n            registrationName: 'onLoad',\n          },\n          topLoadEnd: {\n            registrationName: 'onLoadEnd',\n          },\n        },\n        validAttributes: {\n          blurRadius: true,\n          defaultSource: true,\n          internal_analyticTag: true,\n          resizeMethod: true,\n          resizeMode: true,\n          resizeMultiplier: true,\n          tintColor: {\n            process: require('../StyleSheet/processColor').default,\n          },\n          borderBottomLeftRadius: true,\n          borderTopLeftRadius: true,\n          src: true,\n          // NOTE: New Architecture expects this to be called `source`,\n          // regardless of the platform, therefore propagate it as well.\n          // For the backwards compatibility reasons, we keep both `src`\n          // and `source`, which will be identical at this stage.\n          source: true,\n          borderRadius: true,\n          headers: true,\n          shouldNotifyLoadEvents: true,\n          overlayColor: {\n            process: require('../StyleSheet/processColor').default,\n          },\n          borderColor: {\n            process: require('../StyleSheet/processColor').default,\n          },\n          accessible: true,\n          progressiveRenderingEnabled: true,\n          fadeDuration: true,\n          borderBottomRightRadius: true,\n          borderTopRightRadius: true,\n          loadingIndicatorSrc: true,\n        },\n      }\n    : {\n        uiViewClassName: 'RCTImageView',\n        bubblingEventTypes: {},\n        directEventTypes: {\n          topLoadStart: {\n            registrationName: 'onLoadStart',\n          },\n          topProgress: {\n            registrationName: 'onProgress',\n          },\n          topError: {\n            registrationName: 'onError',\n          },\n          topPartialLoad: {\n            registrationName: 'onPartialLoad',\n          },\n          topLoad: {\n            registrationName: 'onLoad',\n          },\n          topLoadEnd: {\n            registrationName: 'onLoadEnd',\n          },\n        },\n        validAttributes: {\n          blurRadius: true,\n          capInsets: {\n            diff: require('../Utilities/differ/insetsDiffer'),\n          },\n          defaultSource: {\n            process: require('./resolveAssetSource'),\n          },\n          internal_analyticTag: true,\n          resizeMode: true,\n          source: true,\n          tintColor: {\n            process: require('../StyleSheet/processColor').default,\n          },\n          ...ConditionallyIgnoredEventHandlers({\n            onLoadStart: true,\n            onLoad: true,\n            onLoadEnd: true,\n            onProgress: true,\n            onError: true,\n            onPartialLoad: true,\n          }),\n        },\n      };\n\nconst ImageViewNativeComponent: HostComponent<Props> =\n  NativeComponentRegistry.get<Props>(\n    'RCTImageView',\n    () => __INTERNAL_VIEW_CONFIG,\n  );\n\nexport default ImageViewNativeComponent;\n"], "mappings": ";;;;;AAyBA,IAAAA,uBAAA,GAAAC,uBAAA,CAAAC,OAAA;AACA,IAAAC,iBAAA,GAAAD,OAAA;AACA,IAAAE,sBAAA,GAAAC,sBAAA,CAAAH,OAAA;AACA,IAAAI,SAAA,GAAAD,sBAAA,CAAAH,OAAA;AAA6C,SAAAK,yBAAAC,CAAA,6BAAAC,OAAA,mBAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,yBAAAC,CAAA,WAAAA,CAAA,GAAAG,CAAA,GAAAD,CAAA,KAAAF,CAAA;AAAA,SAAAP,wBAAAO,CAAA,EAAAE,CAAA,SAAAA,CAAA,IAAAF,CAAA,IAAAA,CAAA,CAAAI,UAAA,SAAAJ,CAAA,eAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,WAAAK,OAAA,EAAAL,CAAA,QAAAG,CAAA,GAAAJ,wBAAA,CAAAG,CAAA,OAAAC,CAAA,IAAAA,CAAA,CAAAG,GAAA,CAAAN,CAAA,UAAAG,CAAA,CAAAI,GAAA,CAAAP,CAAA,OAAAQ,CAAA,KAAAC,SAAA,UAAAC,CAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,CAAA,IAAAd,CAAA,oBAAAc,CAAA,OAAAC,cAAA,CAAAC,IAAA,CAAAhB,CAAA,EAAAc,CAAA,SAAAG,CAAA,GAAAP,CAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAb,CAAA,EAAAc,CAAA,UAAAG,CAAA,KAAAA,CAAA,CAAAV,GAAA,IAAAU,CAAA,CAAAC,GAAA,IAAAP,MAAA,CAAAC,cAAA,CAAAJ,CAAA,EAAAM,CAAA,EAAAG,CAAA,IAAAT,CAAA,CAAAM,CAAA,IAAAd,CAAA,CAAAc,CAAA,YAAAN,CAAA,CAAAH,OAAA,GAAAL,CAAA,EAAAG,CAAA,IAAAA,CAAA,CAAAe,GAAA,CAAAlB,CAAA,EAAAQ,CAAA,GAAAA,CAAA;AA6BtC,IAAMW,QAAwB,GAAAC,OAAA,CAAAD,QAAA,GAAG,IAAAE,8BAAqB,EAAiB;EAC5EC,iBAAiB,EAAE,CAAC,2BAA2B;AACjD,CAAC,CAAC;AAEK,IAAMC,sBAAyC,GAAAH,OAAA,CAAAG,sBAAA,GACpDC,iBAAQ,CAACC,EAAE,KAAK,SAAS,GACrB;EACEC,eAAe,EAAE,cAAc;EAC/BC,kBAAkB,EAAE,CAAC,CAAC;EACtBC,gBAAgB,EAAE;IAChBC,YAAY,EAAE;MACZC,gBAAgB,EAAE;IACpB,CAAC;IACDC,WAAW,EAAE;MACXD,gBAAgB,EAAE;IACpB,CAAC;IACDE,QAAQ,EAAE;MACRF,gBAAgB,EAAE;IACpB,CAAC;IACDG,OAAO,EAAE;MACPH,gBAAgB,EAAE;IACpB,CAAC;IACDI,UAAU,EAAE;MACVJ,gBAAgB,EAAE;IACpB;EACF,CAAC;EACDK,eAAe,EAAE;IACfC,UAAU,EAAE,IAAI;IAChBC,aAAa,EAAE,IAAI;IACnBC,oBAAoB,EAAE,IAAI;IAC1BC,YAAY,EAAE,IAAI;IAClBC,UAAU,EAAE,IAAI;IAChBC,gBAAgB,EAAE,IAAI;IACtBC,SAAS,EAAE;MACTC,OAAO,EAAEjD,OAAO,CAAC,4BAA4B,CAAC,CAACW;IACjD,CAAC;IACDuC,sBAAsB,EAAE,IAAI;IAC5BC,mBAAmB,EAAE,IAAI;IACzBC,GAAG,EAAE,IAAI;IAKTC,MAAM,EAAE,IAAI;IACZC,YAAY,EAAE,IAAI;IAClBC,OAAO,EAAE,IAAI;IACbC,sBAAsB,EAAE,IAAI;IAC5BC,YAAY,EAAE;MACZR,OAAO,EAAEjD,OAAO,CAAC,4BAA4B,CAAC,CAACW;IACjD,CAAC;IACD+C,WAAW,EAAE;MACXT,OAAO,EAAEjD,OAAO,CAAC,4BAA4B,CAAC,CAACW;IACjD,CAAC;IACDgD,UAAU,EAAE,IAAI;IAChBC,2BAA2B,EAAE,IAAI;IACjCC,YAAY,EAAE,IAAI;IAClBC,uBAAuB,EAAE,IAAI;IAC7BC,oBAAoB,EAAE,IAAI;IAC1BC,mBAAmB,EAAE;EACvB;AACF,CAAC,GACD;EACEhC,eAAe,EAAE,cAAc;EAC/BC,kBAAkB,EAAE,CAAC,CAAC;EACtBC,gBAAgB,EAAE;IAChBC,YAAY,EAAE;MACZC,gBAAgB,EAAE;IACpB,CAAC;IACDC,WAAW,EAAE;MACXD,gBAAgB,EAAE;IACpB,CAAC;IACDE,QAAQ,EAAE;MACRF,gBAAgB,EAAE;IACpB,CAAC;IACD6B,cAAc,EAAE;MACd7B,gBAAgB,EAAE;IACpB,CAAC;IACDG,OAAO,EAAE;MACPH,gBAAgB,EAAE;IACpB,CAAC;IACDI,UAAU,EAAE;MACVJ,gBAAgB,EAAE;IACpB;EACF,CAAC;EACDK,eAAe,EAAAxB,MAAA,CAAAiD,MAAA;IACbxB,UAAU,EAAE,IAAI;IAChByB,SAAS,EAAE;MACTC,IAAI,EAAEpE,OAAO,CAAC,kCAAkC;IAClD,CAAC;IACD2C,aAAa,EAAE;MACbM,OAAO,EAAEjD,OAAO,CAAC,sBAAsB;IACzC,CAAC;IACD4C,oBAAoB,EAAE,IAAI;IAC1BE,UAAU,EAAE,IAAI;IAChBO,MAAM,EAAE,IAAI;IACZL,SAAS,EAAE;MACTC,OAAO,EAAEjD,OAAO,CAAC,4BAA4B,CAAC,CAACW;IACjD;EAAC,GACE,IAAA0D,mDAAiC,EAAC;IACnCC,WAAW,EAAE,IAAI;IACjBC,MAAM,EAAE,IAAI;IACZC,SAAS,EAAE,IAAI;IACfC,UAAU,EAAE,IAAI;IAChBC,OAAO,EAAE,IAAI;IACbC,aAAa,EAAE;EACjB,CAAC,CAAC;AAEN,CAAC;AAEP,IAAMC,wBAA8C,GAClD9E,uBAAuB,CAACe,GAAG,CACzB,cAAc,EACd;EAAA,OAAMgB,sBAAsB;AAAA,CAC9B,CAAC;AAAC,IAAAgD,QAAA,GAAAnD,OAAA,CAAAf,OAAA,GAEWiE,wBAAwB", "ignoreList": []}