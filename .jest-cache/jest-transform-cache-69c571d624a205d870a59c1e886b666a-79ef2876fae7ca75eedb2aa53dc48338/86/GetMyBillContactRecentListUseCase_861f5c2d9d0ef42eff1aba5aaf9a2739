8b89eb88a11a6aac1152d9fccd812afc
"use strict";

/* istanbul ignore next */
function cov_1vi7mgrcuu() {
  var path = "/Users/<USER>/projects/react-native/payment-module/src/domain/usecases/bill-contact/GetMyBillContactRecentListUseCase.ts";
  var hash = "20c66eed548c301ebde4143e869c0abf5535c8a4";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/projects/react-native/payment-module/src/domain/usecases/bill-contact/GetMyBillContactRecentListUseCase.ts",
    statementMap: {
      "0": {
        start: {
          line: 3,
          column: 29
        },
        end: {
          line: 3,
          column: 84
        }
      },
      "1": {
        start: {
          line: 4,
          column: 25
        },
        end: {
          line: 4,
          column: 99
        }
      },
      "2": {
        start: {
          line: 5,
          column: 23
        },
        end: {
          line: 5,
          column: 95
        }
      },
      "3": {
        start: {
          line: 6,
          column: 20
        },
        end: {
          line: 6,
          column: 89
        }
      },
      "4": {
        start: {
          line: 7,
          column: 0
        },
        end: {
          line: 9,
          column: 3
        }
      },
      "5": {
        start: {
          line: 10,
          column: 0
        },
        end: {
          line: 10,
          column: 51
        }
      },
      "6": {
        start: {
          line: 11,
          column: 26
        },
        end: {
          line: 11,
          column: 69
        }
      },
      "7": {
        start: {
          line: 12,
          column: 40
        },
        end: {
          line: 32,
          column: 3
        }
      },
      "8": {
        start: {
          line: 14,
          column: 4
        },
        end: {
          line: 14,
          column: 75
        }
      },
      "9": {
        start: {
          line: 15,
          column: 4
        },
        end: {
          line: 15,
          column: 33
        }
      },
      "10": {
        start: {
          line: 17,
          column: 2
        },
        end: {
          line: 31,
          column: 6
        }
      },
      "11": {
        start: {
          line: 20,
          column: 21
        },
        end: {
          line: 25,
          column: 8
        }
      },
      "12": {
        start: {
          line: 21,
          column: 20
        },
        end: {
          line: 21,
          column: 24
        }
      },
      "13": {
        start: {
          line: 22,
          column: 8
        },
        end: {
          line: 24,
          column: 11
        }
      },
      "14": {
        start: {
          line: 23,
          column: 10
        },
        end: {
          line: 23,
          column: 70
        }
      },
      "15": {
        start: {
          line: 27,
          column: 8
        },
        end: {
          line: 27,
          column: 47
        }
      },
      "16": {
        start: {
          line: 29,
          column: 6
        },
        end: {
          line: 29,
          column: 21
        }
      },
      "17": {
        start: {
          line: 33,
          column: 0
        },
        end: {
          line: 33,
          column: 78
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 12,
            column: 40
          },
          end: {
            line: 12,
            column: 41
          }
        },
        loc: {
          start: {
            line: 12,
            column: 52
          },
          end: {
            line: 32,
            column: 1
          }
        },
        line: 12
      },
      "1": {
        name: "GetMyBillContactRecentListUseCase",
        decl: {
          start: {
            line: 13,
            column: 11
          },
          end: {
            line: 13,
            column: 44
          }
        },
        loc: {
          start: {
            line: 13,
            column: 57
          },
          end: {
            line: 16,
            column: 3
          }
        },
        line: 13
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 19,
            column: 11
          },
          end: {
            line: 19,
            column: 12
          }
        },
        loc: {
          start: {
            line: 19,
            column: 23
          },
          end: {
            line: 30,
            column: 5
          }
        },
        line: 19
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 20,
            column: 53
          },
          end: {
            line: 20,
            column: 54
          }
        },
        loc: {
          start: {
            line: 20,
            column: 73
          },
          end: {
            line: 25,
            column: 7
          }
        },
        line: 20
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 22,
            column: 60
          },
          end: {
            line: 22,
            column: 61
          }
        },
        loc: {
          start: {
            line: 22,
            column: 72
          },
          end: {
            line: 24,
            column: 9
          }
        },
        line: 22
      },
      "5": {
        name: "execute",
        decl: {
          start: {
            line: 26,
            column: 15
          },
          end: {
            line: 26,
            column: 22
          }
        },
        loc: {
          start: {
            line: 26,
            column: 27
          },
          end: {
            line: 28,
            column: 7
          }
        },
        line: 26
      }
    },
    branchMap: {},
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0
    },
    b: {},
    inputSourceMap: {
      version: 3,
      file: undefined,
      names: ["ExcecutionHandler_1", "require", "GetMyBillContactRecentListUseCase", "repository", "_classCallCheck2", "default", "_createClass2", "key", "value", "_execute", "_asyncToGenerator2", "request", "_this", "ExecutionHandler", "execute", "getMyBillContactRecentList", "_x", "apply", "arguments", "exports"],
      sourceRoot: undefined,
      sources: ["/Users/<USER>/projects/react-native/payment-module/src/domain/usecases/bill-contact/GetMyBillContactRecentListUseCase.ts"],
      sourcesContent: ["import {ExecutionHandler} from '../../../utils/ExcecutionHandler';\nimport {GetMyBillContactRecentListRequest} from '../../../data/models/get-my-bill-contact-recent-list/GetMyBillContactRecentListRequest';\nimport {IBillContactRepository} from '../../repositories/IBillContactRepository';\nimport {GetMyBillContactRecentListModel} from '../../entities/get-my-bill-contact-recent-list/GetMyBillContactRecentListModel';\nimport {ResultState} from '../../../core/ResultState';\nexport class GetMyBillContactRecentListUseCase {\n  private repository: IBillContactRepository;\n\n  constructor(repository: IBillContactRepository) {\n    this.repository = repository;\n  }\n\n  public async execute(\n    request: GetMyBillContactRecentListRequest,\n  ): Promise<ResultState<GetMyBillContactRecentListModel>> {\n    //TODO: implement state\n    return ExecutionHandler.execute(() => this.repository.getMyBillContactRecentList(request));\n  }\n}\n"],
      mappings: ";;;;;;;;;;AAAA,IAAAA,mBAAA,GAAAC,OAAA;AAAkE,IAKrDC,iCAAiC;EAG5C,SAAAA,kCAAYC,UAAkC;IAAA,IAAAC,gBAAA,CAAAC,OAAA,QAAAH,iCAAA;IAC5C,IAAI,CAACC,UAAU,GAAGA,UAAU;EAC9B;EAAC,WAAAG,aAAA,CAAAD,OAAA,EAAAH,iCAAA;IAAAK,GAAA;IAAAC,KAAA;MAAA,IAAAC,QAAA,OAAAC,kBAAA,CAAAL,OAAA,EAEM,WACLM,OAA0C;QAAA,IAAAC,KAAA;QAG1C,OAAOZ,mBAAA,CAAAa,gBAAgB,CAACC,OAAO,CAAC;UAAA,OAAMF,KAAI,CAACT,UAAU,CAACY,0BAA0B,CAACJ,OAAO,CAAC;QAAA,EAAC;MAC5F,CAAC;MAAA,SALYG,OAAOA,CAAAE,EAAA;QAAA,OAAAP,QAAA,CAAAQ,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAPJ,OAAO;IAAA;EAAA;AAAA;AAPtBK,OAAA,CAAAjB,iCAAA,GAAAA,iCAAA",
      ignoreList: []
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "20c66eed548c301ebde4143e869c0abf5535c8a4"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_1vi7mgrcuu = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_1vi7mgrcuu();
var _interopRequireDefault =
/* istanbul ignore next */
(cov_1vi7mgrcuu().s[0]++, require("@babel/runtime/helpers/interopRequireDefault"));
var _asyncToGenerator2 =
/* istanbul ignore next */
(cov_1vi7mgrcuu().s[1]++, _interopRequireDefault(require("@babel/runtime/helpers/asyncToGenerator")));
var _classCallCheck2 =
/* istanbul ignore next */
(cov_1vi7mgrcuu().s[2]++, _interopRequireDefault(require("@babel/runtime/helpers/classCallCheck")));
var _createClass2 =
/* istanbul ignore next */
(cov_1vi7mgrcuu().s[3]++, _interopRequireDefault(require("@babel/runtime/helpers/createClass")));
/* istanbul ignore next */
cov_1vi7mgrcuu().s[4]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_1vi7mgrcuu().s[5]++;
exports.GetMyBillContactRecentListUseCase = void 0;
var ExcecutionHandler_1 =
/* istanbul ignore next */
(cov_1vi7mgrcuu().s[6]++, require("../../../utils/ExcecutionHandler"));
var GetMyBillContactRecentListUseCase =
/* istanbul ignore next */
(cov_1vi7mgrcuu().s[7]++, function () {
  /* istanbul ignore next */
  cov_1vi7mgrcuu().f[0]++;
  function GetMyBillContactRecentListUseCase(repository) {
    /* istanbul ignore next */
    cov_1vi7mgrcuu().f[1]++;
    cov_1vi7mgrcuu().s[8]++;
    (0, _classCallCheck2.default)(this, GetMyBillContactRecentListUseCase);
    /* istanbul ignore next */
    cov_1vi7mgrcuu().s[9]++;
    this.repository = repository;
  }
  /* istanbul ignore next */
  cov_1vi7mgrcuu().s[10]++;
  return (0, _createClass2.default)(GetMyBillContactRecentListUseCase, [{
    key: "execute",
    value: function () {
      /* istanbul ignore next */
      cov_1vi7mgrcuu().f[2]++;
      var _execute =
      /* istanbul ignore next */
      (cov_1vi7mgrcuu().s[11]++, (0, _asyncToGenerator2.default)(function* (request) {
        /* istanbul ignore next */
        cov_1vi7mgrcuu().f[3]++;
        var _this =
        /* istanbul ignore next */
        (cov_1vi7mgrcuu().s[12]++, this);
        /* istanbul ignore next */
        cov_1vi7mgrcuu().s[13]++;
        return ExcecutionHandler_1.ExecutionHandler.execute(function () {
          /* istanbul ignore next */
          cov_1vi7mgrcuu().f[4]++;
          cov_1vi7mgrcuu().s[14]++;
          return _this.repository.getMyBillContactRecentList(request);
        });
      }));
      function execute(_x) {
        /* istanbul ignore next */
        cov_1vi7mgrcuu().f[5]++;
        cov_1vi7mgrcuu().s[15]++;
        return _execute.apply(this, arguments);
      }
      /* istanbul ignore next */
      cov_1vi7mgrcuu().s[16]++;
      return execute;
    }()
  }]);
}());
/* istanbul ignore next */
cov_1vi7mgrcuu().s[17]++;
exports.GetMyBillContactRecentListUseCase = GetMyBillContactRecentListUseCase;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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