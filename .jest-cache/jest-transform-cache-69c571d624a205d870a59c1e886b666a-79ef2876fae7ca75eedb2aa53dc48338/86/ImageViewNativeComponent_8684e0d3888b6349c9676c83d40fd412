c3d86cf073634c384e8091716dc7b198
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = exports.__INTERNAL_VIEW_CONFIG = exports.Commands = void 0;
var NativeComponentRegistry = _interopRequireWildcard(require("../NativeComponent/NativeComponentRegistry"));
var _ViewConfigIgnore = require("../NativeComponent/ViewConfigIgnore");
var _codegenNativeCommands = _interopRequireDefault(require("../Utilities/codegenNativeCommands"));
var _Platform = _interopRequireDefault(require("../Utilities/Platform"));
function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
var Commands = exports.Commands = (0, _codegenNativeCommands.default)({
  supportedCommands: ['setIsVisible_EXPERIMENTAL']
});
var __INTERNAL_VIEW_CONFIG = exports.__INTERNAL_VIEW_CONFIG = _Platform.default.OS === 'android' ? {
  uiViewClassName: 'RCTImageView',
  bubblingEventTypes: {},
  directEventTypes: {
    topLoadStart: {
      registrationName: 'onLoadStart'
    },
    topProgress: {
      registrationName: 'onProgress'
    },
    topError: {
      registrationName: 'onError'
    },
    topLoad: {
      registrationName: 'onLoad'
    },
    topLoadEnd: {
      registrationName: 'onLoadEnd'
    }
  },
  validAttributes: {
    blurRadius: true,
    defaultSource: true,
    internal_analyticTag: true,
    resizeMethod: true,
    resizeMode: true,
    resizeMultiplier: true,
    tintColor: {
      process: require('../StyleSheet/processColor').default
    },
    borderBottomLeftRadius: true,
    borderTopLeftRadius: true,
    src: true,
    source: true,
    borderRadius: true,
    headers: true,
    shouldNotifyLoadEvents: true,
    overlayColor: {
      process: require('../StyleSheet/processColor').default
    },
    borderColor: {
      process: require('../StyleSheet/processColor').default
    },
    accessible: true,
    progressiveRenderingEnabled: true,
    fadeDuration: true,
    borderBottomRightRadius: true,
    borderTopRightRadius: true,
    loadingIndicatorSrc: true
  }
} : {
  uiViewClassName: 'RCTImageView',
  bubblingEventTypes: {},
  directEventTypes: {
    topLoadStart: {
      registrationName: 'onLoadStart'
    },
    topProgress: {
      registrationName: 'onProgress'
    },
    topError: {
      registrationName: 'onError'
    },
    topPartialLoad: {
      registrationName: 'onPartialLoad'
    },
    topLoad: {
      registrationName: 'onLoad'
    },
    topLoadEnd: {
      registrationName: 'onLoadEnd'
    }
  },
  validAttributes: Object.assign({
    blurRadius: true,
    capInsets: {
      diff: require('../Utilities/differ/insetsDiffer')
    },
    defaultSource: {
      process: require('./resolveAssetSource')
    },
    internal_analyticTag: true,
    resizeMode: true,
    source: true,
    tintColor: {
      process: require('../StyleSheet/processColor').default
    }
  }, (0, _ViewConfigIgnore.ConditionallyIgnoredEventHandlers)({
    onLoadStart: true,
    onLoad: true,
    onLoadEnd: true,
    onProgress: true,
    onError: true,
    onPartialLoad: true
  }))
};
var ImageViewNativeComponent = NativeComponentRegistry.get('RCTImageView', function () {
  return __INTERNAL_VIEW_CONFIG;
});
var _default = exports.default = ImageViewNativeComponent;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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