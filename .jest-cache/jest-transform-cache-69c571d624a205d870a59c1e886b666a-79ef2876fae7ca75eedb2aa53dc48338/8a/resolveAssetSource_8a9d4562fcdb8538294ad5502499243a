128398af08c46ae21705c0aac3db172d
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
var _NativeSourceCode = _interopRequireDefault(require("../NativeModules/specs/NativeSourceCode"));
var AssetSourceResolver = require('./AssetSourceResolver');
var _require = require('./AssetUtils'),
  pickScale = _require.pickScale;
var AssetRegistry = require('@react-native/assets-registry/registry');
var _customSourceTransformers = [];
var _serverURL;
var _scriptURL;
var _sourceCodeScriptURL;
function getSourceCodeScriptURL() {
  if (_sourceCodeScriptURL != null) {
    return _sourceCodeScriptURL;
  }
  _sourceCodeScriptURL = _NativeSourceCode.default.getConstants().scriptURL;
  return _sourceCodeScriptURL;
}
function getDevServerURL() {
  if (_serverURL === undefined) {
    var sourceCodeScriptURL = getSourceCodeScriptURL();
    var match = sourceCodeScriptURL == null ? void 0 : sourceCodeScriptURL.match(/^https?:\/\/.*?\//);
    if (match) {
      _serverURL = match[0];
    } else {
      _serverURL = null;
    }
  }
  return _serverURL;
}
function _coerceLocalScriptURL(scriptURL) {
  var normalizedScriptURL = scriptURL;
  if (normalizedScriptURL != null) {
    if (normalizedScriptURL.startsWith('assets://')) {
      return null;
    }
    normalizedScriptURL = normalizedScriptURL.substring(0, normalizedScriptURL.lastIndexOf('/') + 1);
    if (!normalizedScriptURL.includes('://')) {
      normalizedScriptURL = 'file://' + normalizedScriptURL;
    }
  }
  return normalizedScriptURL;
}
function getScriptURL() {
  if (_scriptURL === undefined) {
    _scriptURL = _coerceLocalScriptURL(getSourceCodeScriptURL());
  }
  return _scriptURL;
}
function setCustomSourceTransformer(transformer) {
  _customSourceTransformers = [transformer];
}
function addCustomSourceTransformer(transformer) {
  _customSourceTransformers.push(transformer);
}
function resolveAssetSource(source) {
  if (source == null || typeof source === 'object') {
    return source;
  }
  var asset = AssetRegistry.getAssetByID(source);
  if (!asset) {
    return null;
  }
  var resolver = new AssetSourceResolver(getDevServerURL(), getScriptURL(), asset);
  if (_customSourceTransformers) {
    for (var customSourceTransformer of _customSourceTransformers) {
      var transformedSource = customSourceTransformer(resolver);
      if (transformedSource != null) {
        return transformedSource;
      }
    }
  }
  return resolver.defaultAsset();
}
resolveAssetSource.pickScale = pickScale;
resolveAssetSource.setCustomSourceTransformer = setCustomSourceTransformer;
resolveAssetSource.addCustomSourceTransformer = addCustomSourceTransformer;
module.exports = resolveAssetSource;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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