{"version": 3, "names": ["_NativeSourceCode", "_interopRequireDefault", "require", "AssetSourceResolver", "_require", "pickScale", "AssetRegistry", "_customSourceTransformers", "_serverURL", "_scriptURL", "_sourceCodeScriptURL", "getSourceCodeScriptURL", "SourceCode", "getConstants", "scriptURL", "getDevServerURL", "undefined", "sourceCodeScriptURL", "match", "_coerceLocalScriptURL", "normalizedScriptURL", "startsWith", "substring", "lastIndexOf", "includes", "getScriptURL", "setCustomSourceTransformer", "transformer", "addCustomSourceTransformer", "push", "resolveAssetSource", "source", "asset", "getAssetByID", "resolver", "customSourceTransformer", "transformedSource", "defaultAsset", "module", "exports"], "sources": ["resolveAssetSource.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @format\n * @flow strict-local\n */\n\n// Utilities for resolving an asset into a `source` for e.g. `Image`\n\nimport type {ResolvedAssetSource} from './AssetSourceResolver';\nimport type {ImageSource} from './ImageSource';\n\nimport SourceCode from '../NativeModules/specs/NativeSourceCode';\n\nconst AssetSourceResolver = require('./AssetSourceResolver');\nconst {pickScale} = require('./AssetUtils');\nconst AssetRegistry = require('@react-native/assets-registry/registry');\n\ntype CustomSourceTransformer = (\n  resolver: AssetSourceResolver,\n) => ?ResolvedAssetSource;\n\nlet _customSourceTransformers: Array<CustomSourceTransformer> = [];\nlet _serverURL: ?string;\nlet _scriptURL: ?string;\nlet _sourceCodeScriptURL: ?string;\n\nfunction getSourceCodeScriptURL(): ?string {\n  if (_sourceCodeScriptURL != null) {\n    return _sourceCodeScriptURL;\n  }\n\n  _sourceCodeScriptURL = SourceCode.getConstants().scriptURL;\n  return _sourceCodeScriptURL;\n}\n\nfunction getDevServerURL(): ?string {\n  if (_serverURL === undefined) {\n    const sourceCodeScriptURL = getSourceCodeScriptURL();\n    const match = sourceCodeScriptURL?.match(/^https?:\\/\\/.*?\\//);\n    if (match) {\n      // jsBundle was loaded from network\n      _serverURL = match[0];\n    } else {\n      // jsBundle was loaded from file\n      _serverURL = null;\n    }\n  }\n  return _serverURL;\n}\n\nfunction _coerceLocalScriptURL(scriptURL: ?string): ?string {\n  let normalizedScriptURL = scriptURL;\n\n  if (normalizedScriptURL != null) {\n    if (normalizedScriptURL.startsWith('assets://')) {\n      // android: running from within assets, no offline path to use\n      return null;\n    }\n    normalizedScriptURL = normalizedScriptURL.substring(\n      0,\n      normalizedScriptURL.lastIndexOf('/') + 1,\n    );\n    if (!normalizedScriptURL.includes('://')) {\n      // Add file protocol in case we have an absolute file path and not a URL.\n      // This shouldn't really be necessary. scriptURL should be a URL.\n      normalizedScriptURL = 'file://' + normalizedScriptURL;\n    }\n  }\n\n  return normalizedScriptURL;\n}\n\nfunction getScriptURL(): ?string {\n  if (_scriptURL === undefined) {\n    _scriptURL = _coerceLocalScriptURL(getSourceCodeScriptURL());\n  }\n  return _scriptURL;\n}\n\n/**\n * `transformer` can optionally be used to apply a custom transformation when\n * resolving an asset source. This methods overrides all other custom transformers\n * that may have been previously registered.\n */\nfunction setCustomSourceTransformer(\n  transformer: CustomSourceTransformer,\n): void {\n  _customSourceTransformers = [transformer];\n}\n\n/**\n * Adds a `transformer` into the chain of custom source transformers, which will\n * be applied in the order registered, until one returns a non-null value.\n */\nfunction addCustomSourceTransformer(\n  transformer: CustomSourceTransformer,\n): void {\n  _customSourceTransformers.push(transformer);\n}\n\n/**\n * `source` is either a number (opaque type returned by require('./foo.png'))\n * or an `ImageSource` like { uri: '<http location || file path>' }\n */\nfunction resolveAssetSource(source: ?ImageSource): ?ResolvedAssetSource {\n  if (source == null || typeof source === 'object') {\n    // $FlowFixMe[incompatible-exact] `source` doesn't exactly match `ResolvedAssetSource`\n    // $FlowFixMe[incompatible-return] `source` doesn't exactly match `ResolvedAssetSource`\n    return source;\n  }\n\n  const asset = AssetRegistry.getAssetByID(source);\n  if (!asset) {\n    return null;\n  }\n\n  const resolver = new AssetSourceResolver(\n    getDevServerURL(),\n    getScriptURL(),\n    asset,\n  );\n\n  // Apply (chained) custom source transformers, if any\n  if (_customSourceTransformers) {\n    for (const customSourceTransformer of _customSourceTransformers) {\n      const transformedSource = customSourceTransformer(resolver);\n      if (transformedSource != null) {\n        return transformedSource;\n      }\n    }\n  }\n\n  return resolver.defaultAsset();\n}\n\nresolveAssetSource.pickScale = pickScale;\nresolveAssetSource.setCustomSourceTransformer = setCustomSourceTransformer;\nresolveAssetSource.addCustomSourceTransformer = addCustomSourceTransformer;\nmodule.exports = resolveAssetSource;\n"], "mappings": ";AAeA,IAAAA,iBAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA,IAAMC,mBAAmB,GAAGD,OAAO,CAAC,uBAAuB,CAAC;AAC5D,IAAAE,QAAA,GAAoBF,OAAO,CAAC,cAAc,CAAC;EAApCG,SAAS,GAAAD,QAAA,CAATC,SAAS;AAChB,IAAMC,aAAa,GAAGJ,OAAO,CAAC,wCAAwC,CAAC;AAMvE,IAAIK,yBAAyD,GAAG,EAAE;AAClE,IAAIC,UAAmB;AACvB,IAAIC,UAAmB;AACvB,IAAIC,oBAA6B;AAEjC,SAASC,sBAAsBA,CAAA,EAAY;EACzC,IAAID,oBAAoB,IAAI,IAAI,EAAE;IAChC,OAAOA,oBAAoB;EAC7B;EAEAA,oBAAoB,GAAGE,yBAAU,CAACC,YAAY,CAAC,CAAC,CAACC,SAAS;EAC1D,OAAOJ,oBAAoB;AAC7B;AAEA,SAASK,eAAeA,CAAA,EAAY;EAClC,IAAIP,UAAU,KAAKQ,SAAS,EAAE;IAC5B,IAAMC,mBAAmB,GAAGN,sBAAsB,CAAC,CAAC;IACpD,IAAMO,KAAK,GAAGD,mBAAmB,oBAAnBA,mBAAmB,CAAEC,KAAK,CAAC,mBAAmB,CAAC;IAC7D,IAAIA,KAAK,EAAE;MAETV,UAAU,GAAGU,KAAK,CAAC,CAAC,CAAC;IACvB,CAAC,MAAM;MAELV,UAAU,GAAG,IAAI;IACnB;EACF;EACA,OAAOA,UAAU;AACnB;AAEA,SAASW,qBAAqBA,CAACL,SAAkB,EAAW;EAC1D,IAAIM,mBAAmB,GAAGN,SAAS;EAEnC,IAAIM,mBAAmB,IAAI,IAAI,EAAE;IAC/B,IAAIA,mBAAmB,CAACC,UAAU,CAAC,WAAW,CAAC,EAAE;MAE/C,OAAO,IAAI;IACb;IACAD,mBAAmB,GAAGA,mBAAmB,CAACE,SAAS,CACjD,CAAC,EACDF,mBAAmB,CAACG,WAAW,CAAC,GAAG,CAAC,GAAG,CACzC,CAAC;IACD,IAAI,CAACH,mBAAmB,CAACI,QAAQ,CAAC,KAAK,CAAC,EAAE;MAGxCJ,mBAAmB,GAAG,SAAS,GAAGA,mBAAmB;IACvD;EACF;EAEA,OAAOA,mBAAmB;AAC5B;AAEA,SAASK,YAAYA,CAAA,EAAY;EAC/B,IAAIhB,UAAU,KAAKO,SAAS,EAAE;IAC5BP,UAAU,GAAGU,qBAAqB,CAACR,sBAAsB,CAAC,CAAC,CAAC;EAC9D;EACA,OAAOF,UAAU;AACnB;AAOA,SAASiB,0BAA0BA,CACjCC,WAAoC,EAC9B;EACNpB,yBAAyB,GAAG,CAACoB,WAAW,CAAC;AAC3C;AAMA,SAASC,0BAA0BA,CACjCD,WAAoC,EAC9B;EACNpB,yBAAyB,CAACsB,IAAI,CAACF,WAAW,CAAC;AAC7C;AAMA,SAASG,kBAAkBA,CAACC,MAAoB,EAAwB;EACtE,IAAIA,MAAM,IAAI,IAAI,IAAI,OAAOA,MAAM,KAAK,QAAQ,EAAE;IAGhD,OAAOA,MAAM;EACf;EAEA,IAAMC,KAAK,GAAG1B,aAAa,CAAC2B,YAAY,CAACF,MAAM,CAAC;EAChD,IAAI,CAACC,KAAK,EAAE;IACV,OAAO,IAAI;EACb;EAEA,IAAME,QAAQ,GAAG,IAAI/B,mBAAmB,CACtCY,eAAe,CAAC,CAAC,EACjBU,YAAY,CAAC,CAAC,EACdO,KACF,CAAC;EAGD,IAAIzB,yBAAyB,EAAE;IAC7B,KAAK,IAAM4B,uBAAuB,IAAI5B,yBAAyB,EAAE;MAC/D,IAAM6B,iBAAiB,GAAGD,uBAAuB,CAACD,QAAQ,CAAC;MAC3D,IAAIE,iBAAiB,IAAI,IAAI,EAAE;QAC7B,OAAOA,iBAAiB;MAC1B;IACF;EACF;EAEA,OAAOF,QAAQ,CAACG,YAAY,CAAC,CAAC;AAChC;AAEAP,kBAAkB,CAACzB,SAAS,GAAGA,SAAS;AACxCyB,kBAAkB,CAACJ,0BAA0B,GAAGA,0BAA0B;AAC1EI,kBAAkB,CAACF,0BAA0B,GAAGA,0BAA0B;AAC1EU,MAAM,CAACC,OAAO,GAAGT,kBAAkB", "ignoreList": []}