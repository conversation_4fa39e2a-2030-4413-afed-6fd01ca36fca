{"version": 3, "names": ["cov_h0t89y1fc", "actualCoverage", "s", "exports", "PAYMENT_TYPE", "QR_PAYMENT", "BILLING_ACCOUNT", "BILLING_CREDIT", "TOPUP_ACCOUNT", "TOPUP_CREDIT", "A05_CODE", "RED", "YELLOW", "WHITE", "UNKNOWN", "PAYMENT_ORDER_STATUS", "PROCESSED", "REJECTED", "ACCEPTED", "DIALOG_CODE", "USER_NOT_FOUND", "USER_NOT_INVALID", "USER_NOT_FOUND_NAPAS", "ONLY_CITAD", "INTERRUPT_ALL", "INTERRUPT_247", "INTERRUPT_247_IN_WORKING_TIME", "INTERRUPT_247_OUT_WORKING_TIME", "INTERRUPT_COMMON", "ERROR_401", "DUPLICATE_ACCOUNT_SOURCE", "DUPLICATE_CONTACT", "ONE_ACCOUNT_SOURCE", "ACCOUNT_IS_CARD", "ERROR_ACCOUNT_SOURCE", "ERROR_KEY", "A05", "FTES0009", "FTES0008", "FTES0001", "BMS009", "BMS010", "BMS014", "BMS011", "BMS0017", "FTES0006", "TRANSFER_RESULT_ACTION", "TRANSFER_SHARE", "TRANSER_SAVE_TEMPLATE", "TRANSER_SAVE_BENEFICIARY", "TRANSFER_MANAGER", "TRANSFER_SUPPORT", "QR_CODE_ACTION", "SHARE", "SAVE_IMAGE", "THEME_CHANGE", "MSB_BANK_CODE_NAPAS", "CONTACT_TYPE", "SOURCE_ACCOUNT", "FAVOURITE", "OTHER", "CommonState", "f", "b", "ContactType", "CONTACT_GROUP_TYPE", "ACCOUNT_TYPE", "VIETTEL_SERVICE_CODE"], "sources": ["/Users/<USER>/projects/react-native/payment-module/src/commons/Constants.ts"], "sourcesContent": ["export const PAYMENT_TYPE = {\n  QR_PAYMENT: 'QR_PAYMENT',\n  BILLING_ACCOUNT: 'BILLING_ACCOUNT',\n  BILLING_CREDIT: 'BILLING_CREDIT',\n  TOPUP_ACCOUNT: 'TOPUP_ACCOUNT',\n  TOPUP_CREDIT: 'TOPUP_CREDIT',\n};\nexport const A05_CODE = {\n  RED: 'RED',\n  YELLOW: 'YELLOW',\n  WHITE: 'WHITE',\n  UNKNOWN: 'UNKNOWN',\n};\nexport const PAYMENT_ORDER_STATUS = {\n  PROCESSED: 'PROCESSED',\n  REJECTED: 'REJECTED',\n  ACCEPTED: 'ACCEPTED',\n};\n// export const CONTACT_TAB = {\n//   MONEY: 'MONEY',\n//   BOTTOMSHEET: 'BOTTOMSHEET',\n// };\n\nexport const DIALOG_CODE = {\n  USER_NOT_FOUND: 'USER_NOT_FOUND',\n  USER_NOT_INVALID: 'USER_NOT_INVALID',\n  USER_NOT_FOUND_NAPAS: 'USER_NOT_FOUND_NAPAS',\n  ONLY_CITAD: 'ONLY_CITAD',\n  INTERRUPT_ALL: 'INTERRUPT_ALL',\n  INTERRUPT_247: 'INTERRUPT_247',\n  INTERRUPT_247_IN_WORKING_TIME: 'INTERRUPT_247_IN_WORKING_TIME',\n  INTERRUPT_247_OUT_WORKING_TIME: 'INTERRUPT_247_OUT_WORKING_TIME',\n  INTERRUPT_COMMON: 'INTERRUPT_COMMON',\n  ERROR_401: 'ERROR_401',\n  DUPLICATE_ACCOUNT_SOURCE: 'DUPLICATE_ACCOUNT_SOURCE',\n  DUPLICATE_CONTACT: 'DUPLICATE_CONTACT',\n  ONE_ACCOUNT_SOURCE: 'ONE_ACCOUNT_SOURCE',\n  ACCOUNT_IS_CARD: 'ACCOUNT_IS_CARD',\n  ERROR_ACCOUNT_SOURCE: 'ERROR_ACCOUNT_SOURCE',\n};\n\nexport const ERROR_KEY = {\n  A05: 'A05',\n  FTES0009: 'FTES-0009', // Giấy tờ tuỳ thân\n  FTES0008: 'FTES-0008', // Sinh trắc học\n  FTES0001: 'FTES-0001', // Gói truy vấn\n  BMS009: 'BMS-0009', // Napas gián đoạn\n  BMS010: 'BMS-0010', // Khong tim thay nguoi nhan (Nội bộ)\n  BMS014: 'BMS-0010', // Khong tim thay nguoi nhan (Nội bộ)\n  BMS011: 'BMS-0011', // Khong tim thay nguoi nhan (Napas)\n  BMS0017: 'BMS-0017',\n  FTES0006: 'FTES-0006', // Citad gián đoạn\n};\n\nexport const TRANSFER_RESULT_ACTION = {\n  TRANSFER_SHARE: 'TRANSFER_SHARE',\n  TRANSER_SAVE_TEMPLATE: 'TRANSER_SAVE_TEMPLATE',\n  TRANSER_SAVE_BENEFICIARY: 'TRANSER_SAVE_BENEFICIARY',\n  TRANSFER_MANAGER: 'TRANSFER_MANAGER',\n  TRANSFER_SUPPORT: 'TRANSFER_SUPPORT',\n};\n\nexport const QR_CODE_ACTION = {\n  SHARE: 'SHARE',\n  SAVE_IMAGE: 'SAVE_IMAGE',\n  THEME_CHANGE: 'THEME_CHANGE',\n};\n\nexport const MSB_BANK_CODE_NAPAS = '970426';\n\nexport const CONTACT_TYPE = {\n  SOURCE_ACCOUNT: '1',\n  FAVOURITE: '2',\n  OTHER: '3',\n};\n\nexport enum CommonState {\n  SUCCESS = 'SUCCESS',\n  ERROR = 'ERROR',\n  RETRY = 'RETRY',\n}\n\nexport enum ContactType {\n  BILLPAY = 'billpay',\n  ACCOUNT = 'account',\n}\n\nexport enum CONTACT_GROUP_TYPE {\n  PAYMENT = 'PAYMENT',\n  TRANSFER = 'TRANSFER',\n}\nexport enum ACCOUNT_TYPE {\n  ACCT = 'ACCT',\n  CREDIT = 'CREDIT',\n}\n\nexport const VIETTEL_SERVICE_CODE = '810001'; //TODO: stupid solution check with back-end later\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nexport type SafeAny = any;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IASU;IAAAA,aAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,aAAA;AAAAA,aAAA,GAAAE,CAAA;;;;;;;;;AATGC,OAAA,CAAAC,YAAY,GAAG;EAC1BC,UAAU,EAAE,YAAY;EACxBC,eAAe,EAAE,iBAAiB;EAClCC,cAAc,EAAE,gBAAgB;EAChCC,aAAa,EAAE,eAAe;EAC9BC,YAAY,EAAE;CACf;AAAA;AAAAT,aAAA,GAAAE,CAAA;AACYC,OAAA,CAAAO,QAAQ,GAAG;EACtBC,GAAG,EAAE,KAAK;EACVC,MAAM,EAAE,QAAQ;EAChBC,KAAK,EAAE,OAAO;EACdC,OAAO,EAAE;CACV;AAAA;AAAAd,aAAA,GAAAE,CAAA;AACYC,OAAA,CAAAY,oBAAoB,GAAG;EAClCC,SAAS,EAAE,WAAW;EACtBC,QAAQ,EAAE,UAAU;EACpBC,QAAQ,EAAE;CACX;AAAA;AAAAlB,aAAA,GAAAE,CAAA;AAMYC,OAAA,CAAAgB,WAAW,GAAG;EACzBC,cAAc,EAAE,gBAAgB;EAChCC,gBAAgB,EAAE,kBAAkB;EACpCC,oBAAoB,EAAE,sBAAsB;EAC5CC,UAAU,EAAE,YAAY;EACxBC,aAAa,EAAE,eAAe;EAC9BC,aAAa,EAAE,eAAe;EAC9BC,6BAA6B,EAAE,+BAA+B;EAC9DC,8BAA8B,EAAE,gCAAgC;EAChEC,gBAAgB,EAAE,kBAAkB;EACpCC,SAAS,EAAE,WAAW;EACtBC,wBAAwB,EAAE,0BAA0B;EACpDC,iBAAiB,EAAE,mBAAmB;EACtCC,kBAAkB,EAAE,oBAAoB;EACxCC,eAAe,EAAE,iBAAiB;EAClCC,oBAAoB,EAAE;CACvB;AAAA;AAAAlC,aAAA,GAAAE,CAAA;AAEYC,OAAA,CAAAgC,SAAS,GAAG;EACvBC,GAAG,EAAE,KAAK;EACVC,QAAQ,EAAE,WAAW;EACrBC,QAAQ,EAAE,WAAW;EACrBC,QAAQ,EAAE,WAAW;EACrBC,MAAM,EAAE,UAAU;EAClBC,MAAM,EAAE,UAAU;EAClBC,MAAM,EAAE,UAAU;EAClBC,MAAM,EAAE,UAAU;EAClBC,OAAO,EAAE,UAAU;EACnBC,QAAQ,EAAE;CACX;AAAA;AAAA7C,aAAA,GAAAE,CAAA;AAEYC,OAAA,CAAA2C,sBAAsB,GAAG;EACpCC,cAAc,EAAE,gBAAgB;EAChCC,qBAAqB,EAAE,uBAAuB;EAC9CC,wBAAwB,EAAE,0BAA0B;EACpDC,gBAAgB,EAAE,kBAAkB;EACpCC,gBAAgB,EAAE;CACnB;AAAA;AAAAnD,aAAA,GAAAE,CAAA;AAEYC,OAAA,CAAAiD,cAAc,GAAG;EAC5BC,KAAK,EAAE,OAAO;EACdC,UAAU,EAAE,YAAY;EACxBC,YAAY,EAAE;CACf;AAAA;AAAAvD,aAAA,GAAAE,CAAA;AAEYC,OAAA,CAAAqD,mBAAmB,GAAG,QAAQ;AAAA;AAAAxD,aAAA,GAAAE,CAAA;AAE9BC,OAAA,CAAAsD,YAAY,GAAG;EAC1BC,cAAc,EAAE,GAAG;EACnBC,SAAS,EAAE,GAAG;EACdC,KAAK,EAAE;CACR;AAED,IAAYC,WAIX;AAAA;AAAA7D,aAAA,GAAAE,CAAA;AAJD,WAAY2D,WAAW;EAAA;EAAA7D,aAAA,GAAA8D,CAAA;EAAA9D,aAAA,GAAAE,CAAA;EACrB2D,WAAA,uBAAmB;EAAA;EAAA7D,aAAA,GAAAE,CAAA;EACnB2D,WAAA,mBAAe;EAAA;EAAA7D,aAAA,GAAAE,CAAA;EACf2D,WAAA,mBAAe;AACjB,CAAC;AAJW;AAAA,CAAA7D,aAAA,GAAA+D,CAAA,UAAAF,WAAW;AAAA;AAAA,CAAA7D,aAAA,GAAA+D,CAAA,UAAA5D,OAAA,CAAA0D,WAAA,GAAXA,WAAW;AAMvB,IAAYG,WAGX;AAAA;AAAAhE,aAAA,GAAAE,CAAA;AAHD,WAAY8D,WAAW;EAAA;EAAAhE,aAAA,GAAA8D,CAAA;EAAA9D,aAAA,GAAAE,CAAA;EACrB8D,WAAA,uBAAmB;EAAA;EAAAhE,aAAA,GAAAE,CAAA;EACnB8D,WAAA,uBAAmB;AACrB,CAAC;AAHW;AAAA,CAAAhE,aAAA,GAAA+D,CAAA,UAAAC,WAAW;AAAA;AAAA,CAAAhE,aAAA,GAAA+D,CAAA,UAAA5D,OAAA,CAAA6D,WAAA,GAAXA,WAAW;AAKvB,IAAYC,kBAGX;AAAA;AAAAjE,aAAA,GAAAE,CAAA;AAHD,WAAY+D,kBAAkB;EAAA;EAAAjE,aAAA,GAAA8D,CAAA;EAAA9D,aAAA,GAAAE,CAAA;EAC5B+D,kBAAA,uBAAmB;EAAA;EAAAjE,aAAA,GAAAE,CAAA;EACnB+D,kBAAA,yBAAqB;AACvB,CAAC;AAHW;AAAA,CAAAjE,aAAA,GAAA+D,CAAA,UAAAE,kBAAkB;AAAA;AAAA,CAAAjE,aAAA,GAAA+D,CAAA,UAAA5D,OAAA,CAAA8D,kBAAA,GAAlBA,kBAAkB;AAI9B,IAAYC,YAGX;AAAA;AAAAlE,aAAA,GAAAE,CAAA;AAHD,WAAYgE,YAAY;EAAA;EAAAlE,aAAA,GAAA8D,CAAA;EAAA9D,aAAA,GAAAE,CAAA;EACtBgE,YAAA,iBAAa;EAAA;EAAAlE,aAAA,GAAAE,CAAA;EACbgE,YAAA,qBAAiB;AACnB,CAAC;AAHW;AAAA,CAAAlE,aAAA,GAAA+D,CAAA,UAAAG,YAAY;AAAA;AAAA,CAAAlE,aAAA,GAAA+D,CAAA,UAAA5D,OAAA,CAAA+D,YAAA,GAAZA,YAAY;AAAA;AAAAlE,aAAA,GAAAE,CAAA;AAKXC,OAAA,CAAAgE,oBAAoB,GAAG,QAAQ", "ignoreList": []}