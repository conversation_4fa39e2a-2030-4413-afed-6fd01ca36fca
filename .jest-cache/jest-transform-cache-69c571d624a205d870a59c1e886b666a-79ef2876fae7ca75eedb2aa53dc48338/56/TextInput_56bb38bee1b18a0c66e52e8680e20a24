c582e297b3a4650f155a8ada0b1d2655
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
var _objectWithoutProperties2 = _interopRequireDefault(require("@babel/runtime/helpers/objectWithoutProperties"));
var _slicedToArray2 = _interopRequireDefault(require("@babel/runtime/helpers/slicedToArray"));
var ReactNativeFeatureFlags = _interopRequireWildcard(require("../../../src/private/featureflags/ReactNativeFeatureFlags"));
var _usePressability2 = _interopRequireDefault(require("../../Pressability/usePressability"));
var _flattenStyle = _interopRequireDefault(require("../../StyleSheet/flattenStyle"));
var _StyleSheet = _interopRequireDefault(require("../../StyleSheet/StyleSheet"));
var _Text = _interopRequireDefault(require("../../Text/Text"));
var _TextAncestor = _interopRequireDefault(require("../../Text/TextAncestor"));
var _Platform = _interopRequireDefault(require("../../Utilities/Platform"));
var _useMergeRefs = _interopRequireDefault(require("../../Utilities/useMergeRefs"));
var _TextInputState = _interopRequireDefault(require("./TextInputState"));
var _invariant = _interopRequireDefault(require("invariant"));
var _nullthrows = _interopRequireDefault(require("nullthrows"));
var _react = _interopRequireWildcard(require("react"));
var React = _react;
var _jsxRuntime = require("react/jsx-runtime");
var _excluded = ["aria-busy", "aria-checked", "aria-disabled", "aria-expanded", "aria-selected", "accessibilityState", "id", "tabIndex", "selection", "selectionColor", "selectionHandleColor", "cursorColor"],
  _excluded2 = ["onBlur", "onFocus"],
  _excluded3 = ["allowFontScaling", "rejectResponderTermination", "underlineColorAndroid", "autoComplete", "textContentType", "readOnly", "editable", "enterKeyHint", "returnKeyType", "inputMode", "showSoftInputOnFocus", "keyboardType"];
function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
var AndroidTextInput;
var AndroidTextInputCommands;
var RCTSinglelineTextInputView;
var RCTSinglelineTextInputNativeCommands;
var RCTMultilineTextInputView;
var RCTMultilineTextInputNativeCommands;
if (_Platform.default.OS === 'android') {
  AndroidTextInput = require('./AndroidTextInputNativeComponent').default;
  AndroidTextInputCommands = require('./AndroidTextInputNativeComponent').Commands;
} else if (_Platform.default.OS === 'ios') {
  RCTSinglelineTextInputView = require('./RCTSingelineTextInputNativeComponent').default;
  RCTSinglelineTextInputNativeCommands = require('./RCTSingelineTextInputNativeComponent').Commands;
  RCTMultilineTextInputView = require('./RCTMultilineTextInputNativeComponent').default;
  RCTMultilineTextInputNativeCommands = require('./RCTMultilineTextInputNativeComponent').Commands;
}
var emptyFunctionThatReturnsTrue = function emptyFunctionThatReturnsTrue() {
  return true;
};
function useTextInputStateSynchronization_STATE(_ref) {
  var props = _ref.props,
    mostRecentEventCount = _ref.mostRecentEventCount,
    selection = _ref.selection,
    inputRef = _ref.inputRef,
    text = _ref.text,
    viewCommands = _ref.viewCommands;
  var _useState = (0, _react.useState)(props.value),
    _useState2 = (0, _slicedToArray2.default)(_useState, 2),
    lastNativeText = _useState2[0],
    setLastNativeText = _useState2[1];
  var _useState3 = (0, _react.useState)({
      selection: {
        start: -1,
        end: -1
      },
      mostRecentEventCount: mostRecentEventCount
    }),
    _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
    lastNativeSelectionState = _useState4[0],
    setLastNativeSelection = _useState4[1];
  var lastNativeSelection = lastNativeSelectionState.selection;
  (0, _react.useLayoutEffect)(function () {
    var nativeUpdate = {};
    if (lastNativeText !== props.value && typeof props.value === 'string') {
      nativeUpdate.text = props.value;
      setLastNativeText(props.value);
    }
    if (selection && lastNativeSelection && (lastNativeSelection.start !== selection.start || lastNativeSelection.end !== selection.end)) {
      nativeUpdate.selection = selection;
      setLastNativeSelection({
        selection: selection,
        mostRecentEventCount: mostRecentEventCount
      });
    }
    if (Object.keys(nativeUpdate).length === 0) {
      return;
    }
    if (inputRef.current != null) {
      var _selection$start, _selection$end;
      viewCommands.setTextAndSelection(inputRef.current, mostRecentEventCount, text, (_selection$start = selection == null ? void 0 : selection.start) != null ? _selection$start : -1, (_selection$end = selection == null ? void 0 : selection.end) != null ? _selection$end : -1);
    }
  }, [mostRecentEventCount, inputRef, props.value, props.defaultValue, lastNativeText, selection, lastNativeSelection, text, viewCommands]);
  return {
    setLastNativeText: setLastNativeText,
    setLastNativeSelection: setLastNativeSelection
  };
}
function useTextInputStateSynchronization_REFS(_ref2) {
  var props = _ref2.props,
    mostRecentEventCount = _ref2.mostRecentEventCount,
    selection = _ref2.selection,
    inputRef = _ref2.inputRef,
    text = _ref2.text,
    viewCommands = _ref2.viewCommands;
  var lastNativeTextRef = (0, _react.useRef)(props.value);
  var lastNativeSelectionRef = (0, _react.useRef)({
    selection: {
      start: -1,
      end: -1
    },
    mostRecentEventCount: mostRecentEventCount
  });
  (0, _react.useLayoutEffect)(function () {
    var nativeUpdate = {};
    var lastNativeSelection = lastNativeSelectionRef.current.selection;
    if (lastNativeTextRef.current !== props.value && typeof props.value === 'string') {
      nativeUpdate.text = props.value;
      lastNativeTextRef.current = props.value;
    }
    if (selection && lastNativeSelection && (lastNativeSelection.start !== selection.start || lastNativeSelection.end !== selection.end)) {
      nativeUpdate.selection = selection;
      lastNativeSelectionRef.current = {
        selection: selection,
        mostRecentEventCount: mostRecentEventCount
      };
    }
    if (Object.keys(nativeUpdate).length === 0) {
      return;
    }
    if (inputRef.current != null) {
      var _selection$start2, _selection$end2;
      viewCommands.setTextAndSelection(inputRef.current, mostRecentEventCount, text, (_selection$start2 = selection == null ? void 0 : selection.start) != null ? _selection$start2 : -1, (_selection$end2 = selection == null ? void 0 : selection.end) != null ? _selection$end2 : -1);
    }
  }, [mostRecentEventCount, inputRef, props.value, props.defaultValue, selection, text, viewCommands]);
  return {
    setLastNativeText: function setLastNativeText(lastNativeText) {
      lastNativeTextRef.current = lastNativeText;
    },
    setLastNativeSelection: function setLastNativeSelection(lastNativeSelection) {
      lastNativeSelectionRef.current = lastNativeSelection;
    }
  };
}
function InternalTextInput(props) {
  var _propsSelection$end, _props$multiline;
  var ariaBusy = props['aria-busy'],
    ariaChecked = props['aria-checked'],
    ariaDisabled = props['aria-disabled'],
    ariaExpanded = props['aria-expanded'],
    ariaSelected = props['aria-selected'],
    accessibilityState = props.accessibilityState,
    id = props.id,
    tabIndex = props.tabIndex,
    propsSelection = props.selection,
    selectionColor = props.selectionColor,
    selectionHandleColor = props.selectionHandleColor,
    cursorColor = props.cursorColor,
    otherProps = (0, _objectWithoutProperties2.default)(props, _excluded);
  var inputRef = (0, _react.useRef)(null);
  var selection = propsSelection == null ? null : {
    start: propsSelection.start,
    end: (_propsSelection$end = propsSelection.end) != null ? _propsSelection$end : propsSelection.start
  };
  var text = typeof props.value === 'string' ? props.value : typeof props.defaultValue === 'string' ? props.defaultValue : undefined;
  var viewCommands = AndroidTextInputCommands || (props.multiline === true ? RCTMultilineTextInputNativeCommands : RCTSinglelineTextInputNativeCommands);
  var _useState5 = (0, _react.useState)(0),
    _useState6 = (0, _slicedToArray2.default)(_useState5, 2),
    mostRecentEventCount = _useState6[0],
    setMostRecentEventCount = _useState6[1];
  var useTextInputStateSynchronization = ReactNativeFeatureFlags.useRefsForTextInputState() ? useTextInputStateSynchronization_REFS : useTextInputStateSynchronization_STATE;
  var _useTextInputStateSyn = useTextInputStateSynchronization({
      props: props,
      inputRef: inputRef,
      mostRecentEventCount: mostRecentEventCount,
      selection: selection,
      text: text,
      viewCommands: viewCommands
    }),
    setLastNativeText = _useTextInputStateSyn.setLastNativeText,
    setLastNativeSelection = _useTextInputStateSyn.setLastNativeSelection;
  (0, _react.useLayoutEffect)(function () {
    var inputRefValue = inputRef.current;
    if (inputRefValue != null) {
      _TextInputState.default.registerInput(inputRefValue);
      return function () {
        _TextInputState.default.unregisterInput(inputRefValue);
        if (_TextInputState.default.currentlyFocusedInput() === inputRefValue) {
          (0, _nullthrows.default)(inputRefValue).blur();
        }
      };
    }
  }, []);
  var setLocalRef = (0, _react.useCallback)(function (instance) {
    inputRef.current = instance;
    if (instance != null) {
      Object.assign(instance, {
        clear: function clear() {
          if (inputRef.current != null) {
            viewCommands.setTextAndSelection(inputRef.current, mostRecentEventCount, '', 0, 0);
          }
        },
        isFocused: function isFocused() {
          return _TextInputState.default.currentlyFocusedInput() === inputRef.current;
        },
        getNativeRef: function getNativeRef() {
          return inputRef.current;
        },
        setSelection: function setSelection(start, end) {
          if (inputRef.current != null) {
            viewCommands.setTextAndSelection(inputRef.current, mostRecentEventCount, null, start, end);
          }
        }
      });
    }
  }, [mostRecentEventCount, viewCommands]);
  var ref = (0, _useMergeRefs.default)(setLocalRef, props.forwardedRef);
  var _onChange = function _onChange(event) {
    var currentText = event.nativeEvent.text;
    props.onChange && props.onChange(event);
    props.onChangeText && props.onChangeText(currentText);
    if (inputRef.current == null) {
      return;
    }
    setLastNativeText(currentText);
    setMostRecentEventCount(event.nativeEvent.eventCount);
  };
  var _onSelectionChange = function _onSelectionChange(event) {
    props.onSelectionChange && props.onSelectionChange(event);
    if (inputRef.current == null) {
      return;
    }
    setLastNativeSelection({
      selection: event.nativeEvent.selection,
      mostRecentEventCount: mostRecentEventCount
    });
  };
  var _onFocus = function _onFocus(event) {
    _TextInputState.default.focusInput(inputRef.current);
    if (props.onFocus) {
      props.onFocus(event);
    }
  };
  var _onBlur = function _onBlur(event) {
    _TextInputState.default.blurInput(inputRef.current);
    if (props.onBlur) {
      props.onBlur(event);
    }
  };
  var _onScroll = function _onScroll(event) {
    props.onScroll && props.onScroll(event);
  };
  var textInput = null;
  var multiline = (_props$multiline = props.multiline) != null ? _props$multiline : false;
  var submitBehavior;
  if (props.submitBehavior != null) {
    if (!multiline && props.submitBehavior === 'newline') {
      submitBehavior = 'blurAndSubmit';
    } else {
      submitBehavior = props.submitBehavior;
    }
  } else if (multiline) {
    if (props.blurOnSubmit === true) {
      submitBehavior = 'blurAndSubmit';
    } else {
      submitBehavior = 'newline';
    }
  } else {
    if (props.blurOnSubmit !== false) {
      submitBehavior = 'blurAndSubmit';
    } else {
      submitBehavior = 'submit';
    }
  }
  var accessible = props.accessible !== false;
  var focusable = props.focusable !== false;
  var editable = props.editable,
    hitSlop = props.hitSlop,
    _onPress = props.onPress,
    onPressIn = props.onPressIn,
    onPressOut = props.onPressOut,
    rejectResponderTermination = props.rejectResponderTermination;
  var config = React.useMemo(function () {
    return {
      hitSlop: hitSlop,
      onPress: function onPress(event) {
        _onPress == null || _onPress(event);
        if (editable !== false) {
          if (inputRef.current != null) {
            inputRef.current.focus();
          }
        }
      },
      onPressIn: onPressIn,
      onPressOut: onPressOut,
      cancelable: _Platform.default.OS === 'ios' ? !rejectResponderTermination : null
    };
  }, [editable, hitSlop, _onPress, onPressIn, onPressOut, rejectResponderTermination]);
  var caretHidden = props.caretHidden;
  if (_Platform.default.isTesting) {
    caretHidden = true;
  }
  var _usePressability = (0, _usePressability2.default)(config),
    onBlur = _usePressability.onBlur,
    onFocus = _usePressability.onFocus,
    eventHandlers = (0, _objectWithoutProperties2.default)(_usePressability, _excluded2);
  var _accessibilityState;
  if (accessibilityState != null || ariaBusy != null || ariaChecked != null || ariaDisabled != null || ariaExpanded != null || ariaSelected != null) {
    _accessibilityState = {
      busy: ariaBusy != null ? ariaBusy : accessibilityState == null ? void 0 : accessibilityState.busy,
      checked: ariaChecked != null ? ariaChecked : accessibilityState == null ? void 0 : accessibilityState.checked,
      disabled: ariaDisabled != null ? ariaDisabled : accessibilityState == null ? void 0 : accessibilityState.disabled,
      expanded: ariaExpanded != null ? ariaExpanded : accessibilityState == null ? void 0 : accessibilityState.expanded,
      selected: ariaSelected != null ? ariaSelected : accessibilityState == null ? void 0 : accessibilityState.selected
    };
  }
  var _style = props.style;
  var flattenedStyle = (0, _flattenStyle.default)(props.style);
  if (flattenedStyle != null) {
    var overrides = null;
    if (typeof (flattenedStyle == null ? void 0 : flattenedStyle.fontWeight) === 'number') {
      overrides = overrides || {};
      overrides.fontWeight = flattenedStyle.fontWeight.toString();
    }
    if (flattenedStyle.verticalAlign != null) {
      overrides = overrides || {};
      overrides.textAlignVertical = verticalAlignToTextAlignVerticalMap[flattenedStyle.verticalAlign];
      overrides.verticalAlign = undefined;
    }
    if (overrides != null) {
      _style = [_style, overrides];
    }
  }
  if (_Platform.default.OS === 'ios') {
    var RCTTextInputView = props.multiline === true ? RCTMultilineTextInputView : RCTSinglelineTextInputView;
    var useMultilineDefaultStyle = props.multiline === true && (flattenedStyle == null || flattenedStyle.padding == null && flattenedStyle.paddingVertical == null && flattenedStyle.paddingTop == null);
    textInput = (0, _jsxRuntime.jsx)(RCTTextInputView, Object.assign({
      ref: ref
    }, otherProps, eventHandlers, {
      accessibilityState: _accessibilityState,
      accessible: accessible,
      submitBehavior: submitBehavior,
      caretHidden: caretHidden,
      dataDetectorTypes: props.dataDetectorTypes,
      focusable: tabIndex !== undefined ? !tabIndex : focusable,
      mostRecentEventCount: mostRecentEventCount,
      nativeID: id != null ? id : props.nativeID,
      onBlur: _onBlur,
      onChange: _onChange,
      onContentSizeChange: props.onContentSizeChange,
      onFocus: _onFocus,
      onScroll: _onScroll,
      onSelectionChange: _onSelectionChange,
      onSelectionChangeShouldSetResponder: emptyFunctionThatReturnsTrue,
      selection: selection,
      selectionColor: selectionColor,
      style: _StyleSheet.default.compose(useMultilineDefaultStyle ? styles.multilineDefault : null, _style),
      text: text
    }));
  } else if (_Platform.default.OS === 'android') {
    var _props$ariaLabelledb, _props$placeholder, _props$rows;
    var autoCapitalize = props.autoCapitalize || 'sentences';
    var _accessibilityLabelledBy = (_props$ariaLabelledb = props == null ? void 0 : props['aria-labelledby']) != null ? _props$ariaLabelledb : props == null ? void 0 : props.accessibilityLabelledBy;
    var placeholder = (_props$placeholder = props.placeholder) != null ? _props$placeholder : '';
    var children = props.children;
    var childCount = React.Children.count(children);
    (0, _invariant.default)(!(props.value != null && childCount), 'Cannot specify both value and children.');
    if (childCount > 1) {
      children = (0, _jsxRuntime.jsx)(_Text.default, {
        children: children
      });
    }
    var colorProps = {
      selectionColor: selectionColor,
      selectionHandleColor: selectionHandleColor === undefined ? selectionColor : selectionHandleColor,
      cursorColor: cursorColor === undefined ? selectionColor : cursorColor
    };
    textInput = (0, _jsxRuntime.jsx)(AndroidTextInput, Object.assign({
      ref: ref
    }, otherProps, colorProps, eventHandlers, {
      accessibilityState: _accessibilityState,
      accessibilityLabelledBy: _accessibilityLabelledBy,
      accessible: accessible,
      autoCapitalize: autoCapitalize,
      submitBehavior: submitBehavior,
      caretHidden: caretHidden,
      children: children,
      disableFullscreenUI: props.disableFullscreenUI,
      focusable: tabIndex !== undefined ? !tabIndex : focusable,
      mostRecentEventCount: mostRecentEventCount,
      nativeID: id != null ? id : props.nativeID,
      numberOfLines: (_props$rows = props.rows) != null ? _props$rows : props.numberOfLines,
      onBlur: _onBlur,
      onChange: _onChange,
      onFocus: _onFocus,
      onScroll: _onScroll,
      onSelectionChange: _onSelectionChange,
      placeholder: placeholder,
      style: _style,
      text: text,
      textBreakStrategy: props.textBreakStrategy
    }));
  }
  return (0, _jsxRuntime.jsx)(_TextAncestor.default.Provider, {
    value: true,
    children: textInput
  });
}
var enterKeyHintToReturnTypeMap = {
  enter: 'default',
  done: 'done',
  go: 'go',
  next: 'next',
  previous: 'previous',
  search: 'search',
  send: 'send'
};
var inputModeToKeyboardTypeMap = {
  none: 'default',
  text: 'default',
  decimal: 'decimal-pad',
  numeric: 'number-pad',
  tel: 'phone-pad',
  search: _Platform.default.OS === 'ios' ? 'web-search' : 'default',
  email: 'email-address',
  url: 'url'
};
var autoCompleteWebToAutoCompleteAndroidMap = {
  'address-line1': 'postal-address-region',
  'address-line2': 'postal-address-locality',
  bday: 'birthdate-full',
  'bday-day': 'birthdate-day',
  'bday-month': 'birthdate-month',
  'bday-year': 'birthdate-year',
  'cc-csc': 'cc-csc',
  'cc-exp': 'cc-exp',
  'cc-exp-month': 'cc-exp-month',
  'cc-exp-year': 'cc-exp-year',
  'cc-number': 'cc-number',
  country: 'postal-address-country',
  'current-password': 'password',
  email: 'email',
  'honorific-prefix': 'name-prefix',
  'honorific-suffix': 'name-suffix',
  name: 'name',
  'additional-name': 'name-middle',
  'family-name': 'name-family',
  'given-name': 'name-given',
  'new-password': 'password-new',
  off: 'off',
  'one-time-code': 'sms-otp',
  'postal-code': 'postal-code',
  sex: 'gender',
  'street-address': 'street-address',
  tel: 'tel',
  'tel-country-code': 'tel-country-code',
  'tel-national': 'tel-national',
  username: 'username'
};
var autoCompleteWebToTextContentTypeMap = {
  'address-line1': 'streetAddressLine1',
  'address-line2': 'streetAddressLine2',
  bday: 'birthdate',
  'bday-day': 'birthdateDay',
  'bday-month': 'birthdateMonth',
  'bday-year': 'birthdateYear',
  'cc-csc': 'creditCardSecurityCode',
  'cc-exp-month': 'creditCardExpirationMonth',
  'cc-exp-year': 'creditCardExpirationYear',
  'cc-exp': 'creditCardExpiration',
  'cc-given-name': 'creditCardGivenName',
  'cc-additional-name': 'creditCardMiddleName',
  'cc-family-name': 'creditCardFamilyName',
  'cc-name': 'creditCardName',
  'cc-number': 'creditCardNumber',
  'cc-type': 'creditCardType',
  'current-password': 'password',
  country: 'countryName',
  email: 'emailAddress',
  name: 'name',
  'additional-name': 'middleName',
  'family-name': 'familyName',
  'given-name': 'givenName',
  nickname: 'nickname',
  'honorific-prefix': 'namePrefix',
  'honorific-suffix': 'nameSuffix',
  'new-password': 'newPassword',
  off: 'none',
  'one-time-code': 'oneTimeCode',
  organization: 'organizationName',
  'organization-title': 'jobTitle',
  'postal-code': 'postalCode',
  'street-address': 'fullStreetAddress',
  tel: 'telephoneNumber',
  url: 'URL',
  username: 'username'
};
var ExportedForwardRef = React.forwardRef(function TextInput(_ref3, forwardedRef) {
  var _autoCompleteWebToAut;
  var _ref3$allowFontScalin = _ref3.allowFontScaling,
    allowFontScaling = _ref3$allowFontScalin === void 0 ? true : _ref3$allowFontScalin,
    _ref3$rejectResponder = _ref3.rejectResponderTermination,
    rejectResponderTermination = _ref3$rejectResponder === void 0 ? true : _ref3$rejectResponder,
    _ref3$underlineColorA = _ref3.underlineColorAndroid,
    underlineColorAndroid = _ref3$underlineColorA === void 0 ? 'transparent' : _ref3$underlineColorA,
    autoComplete = _ref3.autoComplete,
    textContentType = _ref3.textContentType,
    readOnly = _ref3.readOnly,
    editable = _ref3.editable,
    enterKeyHint = _ref3.enterKeyHint,
    returnKeyType = _ref3.returnKeyType,
    inputMode = _ref3.inputMode,
    showSoftInputOnFocus = _ref3.showSoftInputOnFocus,
    keyboardType = _ref3.keyboardType,
    restProps = (0, _objectWithoutProperties2.default)(_ref3, _excluded3);
  return (0, _jsxRuntime.jsx)(InternalTextInput, Object.assign({
    allowFontScaling: allowFontScaling,
    rejectResponderTermination: rejectResponderTermination,
    underlineColorAndroid: underlineColorAndroid,
    editable: readOnly !== undefined ? !readOnly : editable,
    returnKeyType: enterKeyHint ? enterKeyHintToReturnTypeMap[enterKeyHint] : returnKeyType,
    keyboardType: inputMode ? inputModeToKeyboardTypeMap[inputMode] : keyboardType,
    showSoftInputOnFocus: inputMode == null ? showSoftInputOnFocus : inputMode !== 'none',
    autoComplete: _Platform.default.OS === 'android' ? (_autoCompleteWebToAut = autoCompleteWebToAutoCompleteAndroidMap[autoComplete]) != null ? _autoCompleteWebToAut : autoComplete : undefined,
    textContentType: textContentType != null ? textContentType : _Platform.default.OS === 'ios' && autoComplete && autoComplete in autoCompleteWebToTextContentTypeMap ? autoCompleteWebToTextContentTypeMap[autoComplete] : textContentType
  }, restProps, {
    forwardedRef: forwardedRef
  }));
});
ExportedForwardRef.displayName = 'TextInput';
ExportedForwardRef.State = {
  currentlyFocusedInput: _TextInputState.default.currentlyFocusedInput,
  currentlyFocusedField: _TextInputState.default.currentlyFocusedField,
  focusTextInput: _TextInputState.default.focusTextInput,
  blurTextInput: _TextInputState.default.blurTextInput
};
var styles = _StyleSheet.default.create({
  multilineDefault: {
    paddingTop: 5
  }
});
var verticalAlignToTextAlignVerticalMap = {
  auto: 'auto',
  top: 'top',
  bottom: 'bottom',
  middle: 'center'
};
module.exports = ExportedForwardRef;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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