7baa554ca34e641befc07e01cf4e3279
"use strict";

/* istanbul ignore next */
function cov_qlsmx0rdg() {
  var path = "/Users/<USER>/projects/react-native/payment-module/src/data/models/payment-order-status/PaymentOrderStatusRequest.ts";
  var hash = "11bd2e0320a8e8b2c6f579354b8135e42b244d94";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/projects/react-native/payment-module/src/data/models/payment-order-status/PaymentOrderStatusRequest.ts",
    statementMap: {
      "0": {
        start: {
          line: 3,
          column: 0
        },
        end: {
          line: 5,
          column: 3
        }
      }
    },
    fnMap: {},
    branchMap: {},
    s: {
      "0": 0
    },
    f: {},
    b: {},
    inputSourceMap: {
      version: 3,
      file: undefined,
      names: [],
      sourceRoot: undefined,
      sources: ["/Users/<USER>/projects/react-native/payment-module/src/data/models/payment-order-status/PaymentOrderStatusRequest.ts"],
      sourcesContent: ["export interface PaymentOrderStatusRequest {\n  paymentMode: string;\n  id?: string;\n}\n"],
      mappings: "",
      ignoreList: []
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "11bd2e0320a8e8b2c6f579354b8135e42b244d94"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_qlsmx0rdg = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_qlsmx0rdg();
cov_qlsmx0rdg().s[0]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6W10sInNvdXJjZXMiOlsiL1VzZXJzL3NvbnNhbXNldC9wcm9qZWN0cy9yZWFjdC1uYXRpdmUvcGF5bWVudC1tb2R1bGUvc3JjL2RhdGEvbW9kZWxzL3BheW1lbnQtb3JkZXItc3RhdHVzL1BheW1lbnRPcmRlclN0YXR1c1JlcXVlc3QudHMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGludGVyZmFjZSBQYXltZW50T3JkZXJTdGF0dXNSZXF1ZXN0IHtcbiAgcGF5bWVudE1vZGU6IHN0cmluZztcbiAgaWQ/OiBzdHJpbmc7XG59XG4iXSwibWFwcGluZ3MiOiIiLCJpZ25vcmVMaXN0IjpbXX0=