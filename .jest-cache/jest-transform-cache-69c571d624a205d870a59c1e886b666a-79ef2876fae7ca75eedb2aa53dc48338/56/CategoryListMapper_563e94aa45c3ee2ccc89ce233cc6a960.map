{"version": 3, "names": ["cov_2dajbii3nb", "actualCoverage", "s", "exports", "mapCategoryListResponseToModel", "CategoryListModel_1", "require", "response", "f", "_items$map", "items", "b", "map", "item", "_item$productParams$f", "_item$productParams$f2", "categoryCode", "productParams", "find", "param", "code", "dataValue", "categoryNameVn", "subCategories", "filter", "subItem", "_subItem$productParam", "subCategoryCode", "includes", "CategoryModel", "id", "toString", "name", "status"], "sources": ["/Users/<USER>/projects/react-native/payment-module/src/data/mappers/category-list/CategoryListMapper.ts"], "sourcesContent": ["import {CategoryListModel, CategoryModel} from '../../../domain/entities/category-list/CategoryListModel';\nimport {CategoryListResponse} from '../../models/category-list/CategoryListResponse';\n\nexport function mapCategoryListResponseToModel(response: Partial<CategoryListResponse>): CategoryListModel {\n  const items = response.items;\n  return (\n    items?.map(item => {\n      const categoryCode = item.productParams.find(param => param.code === 'CODE')?.dataValue;\n      const categoryNameVn = item.productParams.find(param => param.code === 'VI')?.dataValue;\n      const subCategories = items?.filter(subItem => {\n        const subCategoryCode = subItem.productParams.find(param => param.code === 'CODE')?.dataValue;\n        return subItem.code.includes('SUB') && categoryCode?.includes(subCategoryCode || '');\n      });\n\n      if (item.code.includes('SUB')) {\n        return new CategoryModel(\n          categoryCode || '',\n          item.id.toString() || '',\n          item.code || '',\n          item.name || '',\n          categoryNameVn || '',\n          item.status === 'ACTIVE' || false,\n          mapCategoryListResponseToModel({items: []}),\n        );\n      }\n      return new CategoryModel(\n        categoryCode || '',\n        item.id.toString() || '',\n        item.code || '',\n        item.name || '',\n        categoryNameVn || '',\n        item.status === 'ACTIVE' || false,\n        mapCategoryListResponseToModel({items: subCategories}),\n      );\n    }) ?? []\n  );\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAQY;IAAAA,cAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,cAAA;AAAAA,cAAA,GAAAE,CAAA;;;;;;AALZC,OAAA,CAAAC,8BAAA,GAAAA,8BAAA;AAHA,IAAAC,mBAAA;AAAA;AAAA,CAAAL,cAAA,GAAAE,CAAA,OAAAI,OAAA;AAGA,SAAgBF,8BAA8BA,CAACG,QAAuC;EAAA;EAAAP,cAAA,GAAAQ,CAAA;EAAA,IAAAC,UAAA;EACpF,IAAMC,KAAK;EAAA;EAAA,CAAAV,cAAA,GAAAE,CAAA,OAAGK,QAAQ,CAACG,KAAK;EAAA;EAAAV,cAAA,GAAAE,CAAA;EAC5B,QAAAO,UAAA,GACEC,KAAK;EAAA;EAAA,CAAAV,cAAA,GAAAW,CAAA;EAAA;EAAA,CAAAX,cAAA,GAAAW,CAAA,UAALD,KAAK,CAAEE,GAAG,CAAC,UAAAC,IAAI,EAAG;IAAA;IAAAb,cAAA,GAAAQ,CAAA;IAAA,IAAAM,qBAAA,EAAAC,sBAAA;IAChB,IAAMC,YAAY;IAAA;IAAA,CAAAhB,cAAA,GAAAE,CAAA,QAAAY,qBAAA,GAAGD,IAAI,CAACI,aAAa,CAACC,IAAI,CAAC,UAAAC,KAAK;MAAA;MAAAnB,cAAA,GAAAQ,CAAA;MAAAR,cAAA,GAAAE,CAAA;MAAA,OAAIiB,KAAK,CAACC,IAAI,KAAK,MAAM;IAAA,EAAC;IAAA;IAAA,CAAApB,cAAA,GAAAW,CAAA;IAAA;IAAA,CAAAX,cAAA,GAAAW,CAAA,UAAvDG,qBAAA,CAAyDO,SAAS;IACvF,IAAMC,cAAc;IAAA;IAAA,CAAAtB,cAAA,GAAAE,CAAA,QAAAa,sBAAA,GAAGF,IAAI,CAACI,aAAa,CAACC,IAAI,CAAC,UAAAC,KAAK;MAAA;MAAAnB,cAAA,GAAAQ,CAAA;MAAAR,cAAA,GAAAE,CAAA;MAAA,OAAIiB,KAAK,CAACC,IAAI,KAAK,IAAI;IAAA,EAAC;IAAA;IAAA,CAAApB,cAAA,GAAAW,CAAA;IAAA;IAAA,CAAAX,cAAA,GAAAW,CAAA,UAArDI,sBAAA,CAAuDM,SAAS;IACvF,IAAME,aAAa;IAAA;IAAA,CAAAvB,cAAA,GAAAE,CAAA,OAAGQ,KAAK;IAAA;IAAA,CAAAV,cAAA,GAAAW,CAAA;IAAA;IAAA,CAAAX,cAAA,GAAAW,CAAA,UAALD,KAAK,CAAEc,MAAM,CAAC,UAAAC,OAAO,EAAG;MAAA;MAAAzB,cAAA,GAAAQ,CAAA;MAAA,IAAAkB,qBAAA;MAC5C,IAAMC,eAAe;MAAA;MAAA,CAAA3B,cAAA,GAAAE,CAAA,SAAAwB,qBAAA,GAAGD,OAAO,CAACR,aAAa,CAACC,IAAI,CAAC,UAAAC,KAAK;QAAA;QAAAnB,cAAA,GAAAQ,CAAA;QAAAR,cAAA,GAAAE,CAAA;QAAA,OAAIiB,KAAK,CAACC,IAAI,KAAK,MAAM;MAAA,EAAC;MAAA;MAAA,CAAApB,cAAA,GAAAW,CAAA;MAAA;MAAA,CAAAX,cAAA,GAAAW,CAAA,UAA1De,qBAAA,CAA4DL,SAAS;MAAA;MAAArB,cAAA,GAAAE,CAAA;MAC7F,OAAO,2BAAAF,cAAA,GAAAW,CAAA,UAAAc,OAAO,CAACL,IAAI,CAACQ,QAAQ,CAAC,KAAK,CAAC;MAAA;MAAA,CAAA5B,cAAA,GAAAW,CAAA,UAAIK,YAAY;MAAA;MAAA,CAAAhB,cAAA,GAAAW,CAAA;MAAA;MAAA,CAAAX,cAAA,GAAAW,CAAA,UAAZK,YAAY,CAAEY,QAAQ;MAAC;MAAA,CAAA5B,cAAA,GAAAW,CAAA,UAAAgB,eAAe;MAAA;MAAA,CAAA3B,cAAA,GAAAW,CAAA,UAAI,EAAE,EAAC;IACtF,CAAC,CAAC;IAAA;IAAAX,cAAA,GAAAE,CAAA;IAEF,IAAIW,IAAI,CAACO,IAAI,CAACQ,QAAQ,CAAC,KAAK,CAAC,EAAE;MAAA;MAAA5B,cAAA,GAAAW,CAAA;MAAAX,cAAA,GAAAE,CAAA;MAC7B,OAAO,IAAIG,mBAAA,CAAAwB,aAAa;MACtB;MAAA,CAAA7B,cAAA,GAAAW,CAAA,WAAAK,YAAY;MAAA;MAAA,CAAAhB,cAAA,GAAAW,CAAA,WAAI,EAAE;MAClB;MAAA,CAAAX,cAAA,GAAAW,CAAA,WAAAE,IAAI,CAACiB,EAAE,CAACC,QAAQ,EAAE;MAAA;MAAA,CAAA/B,cAAA,GAAAW,CAAA,WAAI,EAAE;MACxB;MAAA,CAAAX,cAAA,GAAAW,CAAA,WAAAE,IAAI,CAACO,IAAI;MAAA;MAAA,CAAApB,cAAA,GAAAW,CAAA,WAAI,EAAE;MACf;MAAA,CAAAX,cAAA,GAAAW,CAAA,WAAAE,IAAI,CAACmB,IAAI;MAAA;MAAA,CAAAhC,cAAA,GAAAW,CAAA,WAAI,EAAE;MACf;MAAA,CAAAX,cAAA,GAAAW,CAAA,WAAAW,cAAc;MAAA;MAAA,CAAAtB,cAAA,GAAAW,CAAA,WAAI,EAAE;MACpB;MAAA,CAAAX,cAAA,GAAAW,CAAA,WAAAE,IAAI,CAACoB,MAAM,KAAK,QAAQ;MAAA;MAAA,CAAAjC,cAAA,GAAAW,CAAA,WAAI,KAAK,GACjCP,8BAA8B,CAAC;QAACM,KAAK,EAAE;MAAE,CAAC,CAAC,CAC5C;IACH;IAAA;IAAA;MAAAV,cAAA,GAAAW,CAAA;IAAA;IAAAX,cAAA,GAAAE,CAAA;IACA,OAAO,IAAIG,mBAAA,CAAAwB,aAAa;IACtB;IAAA,CAAA7B,cAAA,GAAAW,CAAA,WAAAK,YAAY;IAAA;IAAA,CAAAhB,cAAA,GAAAW,CAAA,WAAI,EAAE;IAClB;IAAA,CAAAX,cAAA,GAAAW,CAAA,WAAAE,IAAI,CAACiB,EAAE,CAACC,QAAQ,EAAE;IAAA;IAAA,CAAA/B,cAAA,GAAAW,CAAA,WAAI,EAAE;IACxB;IAAA,CAAAX,cAAA,GAAAW,CAAA,WAAAE,IAAI,CAACO,IAAI;IAAA;IAAA,CAAApB,cAAA,GAAAW,CAAA,WAAI,EAAE;IACf;IAAA,CAAAX,cAAA,GAAAW,CAAA,WAAAE,IAAI,CAACmB,IAAI;IAAA;IAAA,CAAAhC,cAAA,GAAAW,CAAA,WAAI,EAAE;IACf;IAAA,CAAAX,cAAA,GAAAW,CAAA,WAAAW,cAAc;IAAA;IAAA,CAAAtB,cAAA,GAAAW,CAAA,WAAI,EAAE;IACpB;IAAA,CAAAX,cAAA,GAAAW,CAAA,WAAAE,IAAI,CAACoB,MAAM,KAAK,QAAQ;IAAA;IAAA,CAAAjC,cAAA,GAAAW,CAAA,WAAI,KAAK,GACjCP,8BAA8B,CAAC;MAACM,KAAK,EAAEa;IAAa,CAAC,CAAC,CACvD;EACH,CAAC,CAAC;EAAA;EAAA,CAAAvB,cAAA,GAAAW,CAAA,UAAAF,UAAA;EAAA;EAAA,CAAAT,cAAA,GAAAW,CAAA,UAAI,EAAE;AAEZ", "ignoreList": []}