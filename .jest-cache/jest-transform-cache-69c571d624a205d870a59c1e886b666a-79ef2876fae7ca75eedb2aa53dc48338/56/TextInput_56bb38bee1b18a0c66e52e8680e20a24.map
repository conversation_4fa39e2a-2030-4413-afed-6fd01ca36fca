{"version": 3, "names": ["ReactNativeFeatureFlags", "_interopRequireWildcard", "require", "_usePressability2", "_interopRequireDefault", "_flattenStyle", "_StyleSheet", "_Text", "_TextAncestor", "_Platform", "_useMergeRefs", "_TextInputState", "_invariant", "_nullthrows", "_react", "React", "_jsxRuntime", "_excluded", "_excluded2", "_excluded3", "_getRequireWildcardCache", "e", "WeakMap", "r", "t", "__esModule", "default", "has", "get", "n", "__proto__", "a", "Object", "defineProperty", "getOwnPropertyDescriptor", "u", "hasOwnProperty", "call", "i", "set", "AndroidTextInput", "AndroidTextInputCommands", "RCTSinglelineTextInputView", "RCTSinglelineTextInputNativeCommands", "RCTMultilineTextInputView", "RCTMultilineTextInputNativeCommands", "Platform", "OS", "Commands", "emptyFunctionThatReturnsTrue", "useTextInputStateSynchronization_STATE", "_ref", "props", "mostRecentEventCount", "selection", "inputRef", "text", "viewCommands", "_useState", "useState", "value", "_useState2", "_slicedToArray2", "lastNativeText", "setLastNativeText", "_useState3", "start", "end", "_useState4", "lastNativeSelectionState", "setLastNativeSelection", "lastNativeSelection", "useLayoutEffect", "nativeUpdate", "keys", "length", "current", "_selection$start", "_selection$end", "setTextAndSelection", "defaultValue", "useTextInputStateSynchronization_REFS", "_ref2", "lastNativeTextRef", "useRef", "lastNativeSelectionRef", "_selection$start2", "_selection$end2", "InternalTextInput", "_propsSelection$end", "_props$multiline", "ariaBusy", "ariaChe<PERSON>", "ariaDisabled", "ariaExpanded", "ariaSelected", "accessibilityState", "id", "tabIndex", "propsSelection", "selectionColor", "selectionHandleColor", "cursorColor", "otherProps", "_objectWithoutProperties2", "undefined", "multiline", "_useState5", "_useState6", "setMostRecentEventCount", "useTextInputStateSynchronization", "useRefsForTextInputState", "_useTextInputStateSyn", "inputRefValue", "TextInputState", "registerInput", "unregisterInput", "currentlyFocusedInput", "nullthrows", "blur", "setLocalRef", "useCallback", "instance", "assign", "clear", "isFocused", "getNativeRef", "setSelection", "ref", "useMergeRefs", "forwardedRef", "_onChange", "event", "currentText", "nativeEvent", "onChange", "onChangeText", "eventCount", "_onSelectionChange", "onSelectionChange", "_onFocus", "focusInput", "onFocus", "_onBlur", "blurInput", "onBlur", "_onScroll", "onScroll", "textInput", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "blurOnSubmit", "accessible", "focusable", "editable", "hitSlop", "onPress", "onPressIn", "onPressOut", "rejectResponderTermination", "config", "useMemo", "focus", "cancelable", "caretHidden", "isTesting", "_usePressability", "usePressability", "eventHandlers", "_accessibilityState", "busy", "checked", "disabled", "expanded", "selected", "_style", "style", "flattenedStyle", "flattenStyle", "overrides", "fontWeight", "toString", "verticalAlign", "textAlignVertical", "verticalAlignToTextAlignVerticalMap", "RCTTextInputView", "useMultilineDefaultStyle", "padding", "paddingVertical", "paddingTop", "jsx", "dataDetectorTypes", "nativeID", "onContentSizeChange", "onSelectionChangeShouldSetResponder", "StyleSheet", "compose", "styles", "multilineDefault", "_props$ariaLabelledb", "_props$placeholder", "_props$rows", "autoCapitalize", "_accessibilityLabelledBy", "accessibilityLabelledBy", "placeholder", "children", "childCount", "Children", "count", "invariant", "colorProps", "disableFullscreenUI", "numberOfLines", "rows", "textBreakStrategy", "Provider", "enterKeyHintToReturnTypeMap", "enter", "done", "go", "next", "previous", "search", "send", "inputModeToKeyboardTypeMap", "none", "decimal", "numeric", "tel", "email", "url", "autoCompleteWebToAutoCompleteAndroidMap", "bday", "country", "name", "off", "sex", "username", "autoCompleteWebToTextContentTypeMap", "nickname", "organization", "ExportedForwardRef", "forwardRef", "TextInput", "_ref3", "_autoCompleteWebToAut", "_ref3$allowFontScalin", "allowFontScaling", "_ref3$rejectResponder", "_ref3$underlineColorA", "underlineColorAndroid", "autoComplete", "textContentType", "readOnly", "enterKeyHint", "returnKeyType", "inputMode", "showSoftInputOnFocus", "keyboardType", "restProps", "displayName", "State", "currentlyFocusedField", "focusTextInput", "blurTextInput", "create", "auto", "top", "bottom", "middle", "module", "exports"], "sources": ["TextInput.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @flow strict-local\n * @format\n */\n\nimport type {HostInstance} from '../../Renderer/shims/ReactNativeTypes';\nimport type {____TextStyle_Internal as TextStyleInternal} from '../../StyleSheet/StyleSheetTypes';\nimport type {\n  PressEvent,\n  ScrollEvent,\n  SyntheticEvent,\n} from '../../Types/CoreEventTypes';\nimport type {ViewProps} from '../View/ViewPropTypes';\nimport type {TextInputType} from './TextInput.flow';\n\nimport * as ReactNativeFeatureFlags from '../../../src/private/featureflags/ReactNativeFeatureFlags';\nimport usePressability from '../../Pressability/usePressability';\nimport flattenStyle from '../../StyleSheet/flattenStyle';\nimport StyleSheet, {\n  type ColorValue,\n  type TextStyleProp,\n  type ViewStyleProp,\n} from '../../StyleSheet/StyleSheet';\nimport Text from '../../Text/Text';\nimport TextAncestor from '../../Text/TextAncestor';\nimport Platform from '../../Utilities/Platform';\nimport useMergeRefs from '../../Utilities/useMergeRefs';\nimport TextInputState from './TextInputState';\nimport invariant from 'invariant';\nimport nullthrows from 'nullthrows';\nimport * as React from 'react';\nimport {useCallback, useLayoutEffect, useRef, useState} from 'react';\n\ntype ReactRefSetter<T> = {current: null | T, ...} | ((ref: null | T) => mixed);\ntype TextInputInstance = HostInstance & {\n  +clear: () => void,\n  +isFocused: () => boolean,\n  +getNativeRef: () => ?HostInstance,\n  +setSelection: (start: number, end: number) => void,\n};\n\nlet AndroidTextInput;\nlet AndroidTextInputCommands;\nlet RCTSinglelineTextInputView;\nlet RCTSinglelineTextInputNativeCommands;\nlet RCTMultilineTextInputView;\nlet RCTMultilineTextInputNativeCommands;\n\nif (Platform.OS === 'android') {\n  AndroidTextInput = require('./AndroidTextInputNativeComponent').default;\n  AndroidTextInputCommands =\n    require('./AndroidTextInputNativeComponent').Commands;\n} else if (Platform.OS === 'ios') {\n  RCTSinglelineTextInputView =\n    require('./RCTSingelineTextInputNativeComponent').default;\n  RCTSinglelineTextInputNativeCommands =\n    require('./RCTSingelineTextInputNativeComponent').Commands;\n  RCTMultilineTextInputView =\n    require('./RCTMultilineTextInputNativeComponent').default;\n  RCTMultilineTextInputNativeCommands =\n    require('./RCTMultilineTextInputNativeComponent').Commands;\n}\n\nexport type ChangeEvent = SyntheticEvent<\n  $ReadOnly<{|\n    eventCount: number,\n    target: number,\n    text: string,\n  |}>,\n>;\n\nexport type TextInputEvent = SyntheticEvent<\n  $ReadOnly<{|\n    eventCount: number,\n    previousText: string,\n    range: $ReadOnly<{|\n      start: number,\n      end: number,\n    |}>,\n    target: number,\n    text: string,\n  |}>,\n>;\n\nexport type ContentSizeChangeEvent = SyntheticEvent<\n  $ReadOnly<{|\n    target: number,\n    contentSize: $ReadOnly<{|\n      width: number,\n      height: number,\n    |}>,\n  |}>,\n>;\n\ntype TargetEvent = SyntheticEvent<\n  $ReadOnly<{|\n    target: number,\n  |}>,\n>;\n\nexport type BlurEvent = TargetEvent;\nexport type FocusEvent = TargetEvent;\n\ntype Selection = $ReadOnly<{|\n  start: number,\n  end: number,\n|}>;\n\nexport type SelectionChangeEvent = SyntheticEvent<\n  $ReadOnly<{|\n    selection: Selection,\n    target: number,\n  |}>,\n>;\n\nexport type KeyPressEvent = SyntheticEvent<\n  $ReadOnly<{|\n    key: string,\n    target?: ?number,\n    eventCount?: ?number,\n  |}>,\n>;\n\nexport type EditingEvent = SyntheticEvent<\n  $ReadOnly<{|\n    eventCount: number,\n    text: string,\n    target: number,\n  |}>,\n>;\n\ntype DataDetectorTypesType =\n  | 'phoneNumber'\n  | 'link'\n  | 'address'\n  | 'calendarEvent'\n  | 'trackingNumber'\n  | 'flightNumber'\n  | 'lookupSuggestion'\n  | 'none'\n  | 'all';\n\nexport type KeyboardType =\n  // Cross Platform\n  | 'default'\n  | 'email-address'\n  | 'numeric'\n  | 'phone-pad'\n  | 'number-pad'\n  | 'decimal-pad'\n  | 'url'\n  // iOS-only\n  | 'ascii-capable'\n  | 'numbers-and-punctuation'\n  | 'name-phone-pad'\n  | 'twitter'\n  | 'web-search'\n  // iOS 10+ only\n  | 'ascii-capable-number-pad'\n  // Android-only\n  | 'visible-password';\n\nexport type InputMode =\n  | 'none'\n  | 'text'\n  | 'decimal'\n  | 'numeric'\n  | 'tel'\n  | 'search'\n  | 'email'\n  | 'url';\n\nexport type ReturnKeyType =\n  // Cross Platform\n  | 'done'\n  | 'go'\n  | 'next'\n  | 'search'\n  | 'send'\n  // Android-only\n  | 'none'\n  | 'previous'\n  // iOS-only\n  | 'default'\n  | 'emergency-call'\n  | 'google'\n  | 'join'\n  | 'route'\n  | 'yahoo';\n\nexport type SubmitBehavior = 'submit' | 'blurAndSubmit' | 'newline';\n\nexport type AutoCapitalize = 'none' | 'sentences' | 'words' | 'characters';\n\nexport type TextContentType =\n  | 'none'\n  | 'URL'\n  | 'addressCity'\n  | 'addressCityAndState'\n  | 'addressState'\n  | 'countryName'\n  | 'creditCardNumber'\n  | 'creditCardExpiration'\n  | 'creditCardExpirationMonth'\n  | 'creditCardExpirationYear'\n  | 'creditCardSecurityCode'\n  | 'creditCardType'\n  | 'creditCardName'\n  | 'creditCardGivenName'\n  | 'creditCardMiddleName'\n  | 'creditCardFamilyName'\n  | 'emailAddress'\n  | 'familyName'\n  | 'fullStreetAddress'\n  | 'givenName'\n  | 'jobTitle'\n  | 'location'\n  | 'middleName'\n  | 'name'\n  | 'namePrefix'\n  | 'nameSuffix'\n  | 'nickname'\n  | 'organizationName'\n  | 'postalCode'\n  | 'streetAddressLine1'\n  | 'streetAddressLine2'\n  | 'sublocality'\n  | 'telephoneNumber'\n  | 'username'\n  | 'password'\n  | 'newPassword'\n  | 'oneTimeCode'\n  | 'birthdate'\n  | 'birthdateDay'\n  | 'birthdateMonth'\n  | 'birthdateYear'\n  | 'cellularEID'\n  | 'cellularIMEI'\n  | 'dateTime'\n  | 'flightNumber'\n  | 'shipmentTrackingNumber';\n\nexport type enterKeyHintType =\n  // Cross Platform\n  | 'done'\n  | 'go'\n  | 'next'\n  | 'search'\n  | 'send'\n  // Android-only\n  | 'previous'\n  // iOS-only\n  | 'enter';\n\ntype PasswordRules = string;\n\ntype IOSProps = $ReadOnly<{|\n  /**\n   * If true, the keyboard shortcuts (undo/redo and copy buttons) are disabled. The default value is false.\n   * @platform ios\n   */\n  disableKeyboardShortcuts?: ?boolean,\n\n  /**\n   * When the clear button should appear on the right side of the text view.\n   * This property is supported only for single-line TextInput component.\n   * @platform ios\n   */\n  clearButtonMode?: ?('never' | 'while-editing' | 'unless-editing' | 'always'),\n\n  /**\n   * If `true`, clears the text field automatically when editing begins.\n   * @platform ios\n   */\n  clearTextOnFocus?: ?boolean,\n\n  /**\n   * Determines the types of data converted to clickable URLs in the text input.\n   * Only valid if `multiline={true}` and `editable={false}`.\n   * By default no data types are detected.\n   *\n   * You can provide one type or an array of many types.\n   *\n   * Possible values for `dataDetectorTypes` are:\n   *\n   * - `'phoneNumber'`\n   * - `'link'`\n   * - `'address'`\n   * - `'calendarEvent'`\n   * - `'none'`\n   * - `'all'`\n   *\n   * @platform ios\n   */\n  dataDetectorTypes?:\n    | ?DataDetectorTypesType\n    | $ReadOnlyArray<DataDetectorTypesType>,\n\n  /**\n   * If `true`, the keyboard disables the return key when there is no text and\n   * automatically enables it when there is text. The default value is `false`.\n   * @platform ios\n   */\n  enablesReturnKeyAutomatically?: ?boolean,\n\n  /**\n   * An optional identifier which links a custom InputAccessoryView to\n   * this text input. The InputAccessoryView is rendered above the\n   * keyboard when this text input is focused.\n   * @platform ios\n   */\n  inputAccessoryViewID?: ?string,\n\n  /**\n   * An optional label that overrides the default input accessory view button label.\n   * @platform ios\n   */\n  inputAccessoryViewButtonLabel?: ?string,\n\n  /**\n   * Determines the color of the keyboard.\n   * @platform ios\n   */\n  keyboardAppearance?: ?('default' | 'light' | 'dark'),\n\n  /**\n   * Provide rules for your password.\n   * For example, say you want to require a password with at least eight characters consisting of a mix of uppercase and lowercase letters, at least one number, and at most two consecutive characters.\n   * \"required: upper; required: lower; required: digit; max-consecutive: 2; minlength: 8;\"\n   * @platform ios\n   */\n  passwordRules?: ?PasswordRules,\n\n  /*\n   * If `true`, allows TextInput to pass touch events to the parent component.\n   * This allows components to be swipeable from the TextInput on iOS,\n   * as is the case on Android by default.\n   * If `false`, TextInput always asks to handle the input (except when disabled).\n   * @platform ios\n   */\n  rejectResponderTermination?: ?boolean,\n\n  /**\n   * If `false`, scrolling of the text view will be disabled.\n   * The default value is `true`. Does only work with 'multiline={true}'.\n   * @platform ios\n   */\n  scrollEnabled?: ?boolean,\n\n  /**\n   * If `false`, disables spell-check style (i.e. red underlines).\n   * The default value is inherited from `autoCorrect`.\n   * @platform ios\n   */\n  spellCheck?: ?boolean,\n\n  /**\n   * Give the keyboard and the system information about the\n   * expected semantic meaning for the content that users enter.\n   * `autoComplete` property accomplishes same behavior and is recommended as its supported by both platforms.\n   * Avoid using both `autoComplete` and `textContentType`, you can use `Platform.select` for differing platform behaviors.\n   * For backwards compatibility, when both set, `textContentType` takes precedence on iOS.\n   * @platform ios\n   */\n  textContentType?: ?TextContentType,\n\n  /**\n   * Set line break strategy on iOS.\n   * @platform ios\n   */\n  lineBreakStrategyIOS?: ?('none' | 'standard' | 'hangul-word' | 'push-out'),\n\n  /**\n   * Set line break mode on iOS.\n   * @platform ios\n   */\n  lineBreakModeIOS?: ?(\n    | 'wordWrapping'\n    | 'char'\n    | 'clip'\n    | 'head'\n    | 'middle'\n    | 'tail'\n  ),\n\n  /**\n   * If `false`, the iOS system will not insert an extra space after a paste operation\n   * neither delete one or two spaces after a cut or delete operation.\n   *\n   * The default value is `true`.\n   *\n   * @platform ios\n   */\n  smartInsertDelete?: ?boolean,\n|}>;\n\ntype AndroidProps = $ReadOnly<{|\n  /**\n   * When provided it will set the color of the cursor (or \"caret\") in the component.\n   * Unlike the behavior of `selectionColor` the cursor color will be set independently\n   * from the color of the text selection box.\n   * @platform android\n   */\n  cursorColor?: ?ColorValue,\n\n  /**\n   * When `false`, if there is a small amount of space available around a text input\n   * (e.g. landscape orientation on a phone), the OS may choose to have the user edit\n   * the text inside of a full screen text input mode. When `true`, this feature is\n   * disabled and users will always edit the text directly inside of the text input.\n   * Defaults to `false`.\n   * @platform android\n   */\n  disableFullscreenUI?: ?boolean,\n\n  importantForAutofill?: ?(\n    | 'auto'\n    | 'no'\n    | 'noExcludeDescendants'\n    | 'yes'\n    | 'yesExcludeDescendants'\n  ),\n\n  /**\n   * If defined, the provided image resource will be rendered on the left.\n   * The image resource must be inside `/android/app/src/main/res/drawable` and referenced\n   * like\n   * ```\n   * <TextInput\n   *  inlineImageLeft='search_icon'\n   * />\n   * ```\n   * @platform android\n   */\n  inlineImageLeft?: ?string,\n\n  /**\n   * Padding between the inline image, if any, and the text input itself.\n   * @platform android\n   */\n  inlineImagePadding?: ?number,\n\n  /**\n   * Sets the number of lines for a `TextInput`. Use it with multiline set to\n   * `true` to be able to fill the lines.\n   * @platform android\n   */\n  numberOfLines?: ?number,\n\n  /**\n   * Sets the return key to the label. Use it instead of `returnKeyType`.\n   * @platform android\n   */\n  returnKeyLabel?: ?string,\n\n  /**\n   * Sets the number of rows for a `TextInput`. Use it with multiline set to\n   * `true` to be able to fill the lines.\n   * @platform android\n   */\n  rows?: ?number,\n\n  /**\n   * When `false`, it will prevent the soft keyboard from showing when the field is focused.\n   * Defaults to `true`.\n   */\n  showSoftInputOnFocus?: ?boolean,\n\n  /**\n   * Set text break strategy on Android API Level 23+, possible values are `simple`, `highQuality`, `balanced`\n   * The default value is `simple`.\n   * @platform android\n   */\n  textBreakStrategy?: ?('simple' | 'highQuality' | 'balanced'),\n\n  /**\n   * The color of the `TextInput` underline.\n   * @platform android\n   */\n  underlineColorAndroid?: ?ColorValue,\n|}>;\n\nexport type Props = $ReadOnly<{|\n  ...$Diff<ViewProps, $ReadOnly<{|style: ?ViewStyleProp|}>>,\n  ...IOSProps,\n  ...AndroidProps,\n\n  /**\n   * Can tell `TextInput` to automatically capitalize certain characters.\n   *\n   * - `characters`: all characters.\n   * - `words`: first letter of each word.\n   * - `sentences`: first letter of each sentence (*default*).\n   * - `none`: don't auto capitalize anything.\n   */\n  autoCapitalize?: ?AutoCapitalize,\n\n  /**\n   * Specifies autocomplete hints for the system, so it can provide autofill.\n   * On Android, the system will always attempt to offer autofill by using heuristics to identify the type of content.\n   * To disable autocomplete, set autoComplete to off.\n   *\n   * The following values work across platforms:\n   *\n   * - `additional-name`\n   * - `address-line1`\n   * - `address-line2`\n   * - `birthdate-day` (iOS 17+)\n   * - `birthdate-full` (iOS 17+)\n   * - `birthdate-month` (iOS 17+)\n   * - `birthdate-year` (iOS 17+)\n   * - `cc-number`\n   * - `cc-csc` (iOS 17+)\n   * - `cc-exp` (iOS 17+)\n   * - `cc-exp-day` (iOS 17+)\n   * - `cc-exp-month` (iOS 17+)\n   * - `cc-exp-year` (iOS 17+)\n   * - `country`\n   * - `current-password`\n   * - `email`\n   * - `family-name`\n   * - `given-name`\n   * - `honorific-prefix`\n   * - `honorific-suffix`\n   * - `name`\n   * - `new-password`\n   * - `off`\n   * - `one-time-code`\n   * - `postal-code`\n   * - `street-address`\n   * - `tel`\n   * - `username`\n   *\n   * The following values work on iOS only:\n   *\n   * - `cc-name` (iOS 17+)\n   * - `cc-given-name` (iOS 17+)\n   * - `cc-middle-name` (iOS 17+)\n   * - `cc-family-name` (iOS 17+)\n   * - `cc-type` (iOS 17+)\n   * - `nickname`\n   * - `organization`\n   * - `organization-title`\n   * - `url`\n   *\n   * The following values work on Android only:\n   *\n   * - `gender`\n   * - `name-family`\n   * - `name-given`\n   * - `name-middle`\n   * - `name-middle-initial`\n   * - `name-prefix`\n   * - `name-suffix`\n   * - `password`\n   * - `password-new`\n   * - `postal-address`\n   * - `postal-address-country`\n   * - `postal-address-extended`\n   * - `postal-address-extended-postal-code`\n   * - `postal-address-locality`\n   * - `postal-address-region`\n   * - `sms-otp`\n   * - `tel-country-code`\n   * - `tel-national`\n   * - `tel-device`\n   * - `username-new`\n   */\n  autoComplete?: ?(\n    | 'additional-name'\n    | 'address-line1'\n    | 'address-line2'\n    | 'birthdate-day'\n    | 'birthdate-full'\n    | 'birthdate-month'\n    | 'birthdate-year'\n    | 'cc-csc'\n    | 'cc-exp'\n    | 'cc-exp-day'\n    | 'cc-exp-month'\n    | 'cc-exp-year'\n    | 'cc-number'\n    | 'cc-name'\n    | 'cc-given-name'\n    | 'cc-middle-name'\n    | 'cc-family-name'\n    | 'cc-type'\n    | 'country'\n    | 'current-password'\n    | 'email'\n    | 'family-name'\n    | 'gender'\n    | 'given-name'\n    | 'honorific-prefix'\n    | 'honorific-suffix'\n    | 'name'\n    | 'name-family'\n    | 'name-given'\n    | 'name-middle'\n    | 'name-middle-initial'\n    | 'name-prefix'\n    | 'name-suffix'\n    | 'new-password'\n    | 'nickname'\n    | 'one-time-code'\n    | 'organization'\n    | 'organization-title'\n    | 'password'\n    | 'password-new'\n    | 'postal-address'\n    | 'postal-address-country'\n    | 'postal-address-extended'\n    | 'postal-address-extended-postal-code'\n    | 'postal-address-locality'\n    | 'postal-address-region'\n    | 'postal-code'\n    | 'street-address'\n    | 'sms-otp'\n    | 'tel'\n    | 'tel-country-code'\n    | 'tel-national'\n    | 'tel-device'\n    | 'url'\n    | 'username'\n    | 'username-new'\n    | 'off'\n  ),\n\n  /**\n   * If `false`, disables auto-correct. The default value is `true`.\n   */\n  autoCorrect?: ?boolean,\n\n  /**\n   * If `true`, focuses the input on `componentDidMount`.\n   * The default value is `false`.\n   */\n  autoFocus?: ?boolean,\n\n  /**\n   * Specifies whether fonts should scale to respect Text Size accessibility settings. The\n   * default is `true`.\n   */\n  allowFontScaling?: ?boolean,\n\n  /**\n   * If `true`, caret is hidden. The default value is `false`.\n   *\n   * On Android devices manufactured by Xiaomi with Android Q,\n   * when keyboardType equals 'email-address'this will be set\n   * in native to 'true' to prevent a system related crash. This\n   * will cause cursor to be disabled as a side-effect.\n   *\n   */\n  caretHidden?: ?boolean,\n\n  /*\n   * If `true`, contextMenuHidden is hidden. The default value is `false`.\n   */\n  contextMenuHidden?: ?boolean,\n\n  /**\n   * Provides an initial value that will change when the user starts typing.\n   * Useful for simple use-cases where you do not want to deal with listening\n   * to events and updating the value prop to keep the controlled state in sync.\n   */\n  defaultValue?: ?Stringish,\n\n  /**\n   * If `false`, text is not editable. The default value is `true`.\n   */\n  editable?: ?boolean,\n\n  forwardedRef?: ?ReactRefSetter<TextInputInstance>,\n\n  /**\n   * `enterKeyHint` defines what action label (or icon) to present for the enter key on virtual keyboards.\n   *\n   * The following values is supported:\n   *\n   * - `enter`\n   * - `done`\n   * - `go`\n   * - `next`\n   * - `previous`\n   * - `search`\n   * - `send`\n   */\n  enterKeyHint?: ?enterKeyHintType,\n\n  /**\n   * `inputMode` works like the `inputmode` attribute in HTML, it determines which\n   * keyboard to open, e.g.`numeric` and has precedence over keyboardType\n   *\n   * Support the following values:\n   *\n   * - `none`\n   * - `text`\n   * - `decimal`\n   * - `numeric`\n   * - `tel`\n   * - `search`\n   * - `email`\n   * - `url`\n   */\n  inputMode?: ?InputMode,\n\n  /**\n   * Determines which keyboard to open, e.g.`numeric`.\n   *\n   * The following values work across platforms:\n   *\n   * - `default`\n   * - `numeric`\n   * - `number-pad`\n   * - `decimal-pad`\n   * - `email-address`\n   * - `phone-pad`\n   * - `url`\n   *\n   * *iOS Only*\n   *\n   * The following values work on iOS only:\n   *\n   * - `ascii-capable`\n   * - `numbers-and-punctuation`\n   * - `name-phone-pad`\n   * - `twitter`\n   * - `web-search`\n   *\n   * *Android Only*\n   *\n   * The following values work on Android only:\n   *\n   * - `visible-password`\n   *\n   */\n  keyboardType?: ?KeyboardType,\n\n  /**\n   * Specifies largest possible scale a font can reach when `allowFontScaling` is enabled.\n   * Possible values:\n   * `null/undefined` (default): inherit from the parent node or the global default (0)\n   * `0`: no max, ignore parent/global default\n   * `>= 1`: sets the maxFontSizeMultiplier of this node to this value\n   */\n  maxFontSizeMultiplier?: ?number,\n\n  /**\n   * Limits the maximum number of characters that can be entered. Use this\n   * instead of implementing the logic in JS to avoid flicker.\n   */\n  maxLength?: ?number,\n\n  /**\n   * If `true`, the text input can be multiple lines.\n   * The default value is `false`.\n   */\n  multiline?: ?boolean,\n\n  /**\n   * Callback that is called when the text input is blurred.\n   */\n  onBlur?: ?(e: BlurEvent) => mixed,\n\n  /**\n   * Callback that is called when the text input's text changes.\n   */\n  onChange?: ?(e: ChangeEvent) => mixed,\n\n  /**\n   * Callback that is called when the text input's text changes.\n   * Changed text is passed as an argument to the callback handler.\n   */\n  onChangeText?: ?(text: string) => mixed,\n\n  /**\n   * Callback that is called when the text input's content size changes.\n   * This will be called with\n   * `{ nativeEvent: { contentSize: { width, height } } }`.\n   *\n   * Only called for multiline text inputs.\n   */\n  onContentSizeChange?: ?(e: ContentSizeChangeEvent) => mixed,\n\n  /**\n   * Callback that is called when text input ends.\n   */\n  onEndEditing?: ?(e: EditingEvent) => mixed,\n\n  /**\n   * Callback that is called when the text input is focused.\n   */\n  onFocus?: ?(e: FocusEvent) => mixed,\n\n  /**\n   * Callback that is called when a key is pressed.\n   * This will be called with `{ nativeEvent: { key: keyValue } }`\n   * where `keyValue` is `'Enter'` or `'Backspace'` for respective keys and\n   * the typed-in character otherwise including `' '` for space.\n   * Fires before `onChange` callbacks.\n   */\n  onKeyPress?: ?(e: KeyPressEvent) => mixed,\n\n  /**\n   * Called when a single tap gesture is detected.\n   */\n  onPress?: ?(event: PressEvent) => mixed,\n\n  /**\n   * Called when a touch is engaged.\n   */\n  onPressIn?: ?(event: PressEvent) => mixed,\n\n  /**\n   * Called when a touch is released.\n   */\n  onPressOut?: ?(event: PressEvent) => mixed,\n\n  /**\n   * Callback that is called when the text input selection is changed.\n   * This will be called with\n   * `{ nativeEvent: { selection: { start, end } } }`.\n   */\n  onSelectionChange?: ?(e: SelectionChangeEvent) => mixed,\n\n  /**\n   * Callback that is called when the text input's submit button is pressed.\n   * Invalid if `multiline={true}` is specified.\n   */\n  onSubmitEditing?: ?(e: EditingEvent) => mixed,\n\n  /**\n   * Invoked on content scroll with `{ nativeEvent: { contentOffset: { x, y } } }`.\n   * May also contain other properties from ScrollEvent but on Android contentSize\n   * is not provided for performance reasons.\n   */\n  onScroll?: ?(e: ScrollEvent) => mixed,\n\n  /**\n   * The string that will be rendered before text input has been entered.\n   */\n  placeholder?: ?Stringish,\n\n  /**\n   * The text color of the placeholder string.\n   */\n  placeholderTextColor?: ?ColorValue,\n\n  /** `readOnly` works like the `readonly` attribute in HTML.\n   *  If `true`, text is not editable. The default value is `false`.\n   *  See https://developer.mozilla.org/en-US/docs/Web/HTML/Attributes/readonly\n   *  for more details.\n   */\n  readOnly?: ?boolean,\n\n  /**\n   * Determines how the return key should look. On Android you can also use\n   * `returnKeyLabel`.\n   *\n   * *Cross platform*\n   *\n   * The following values work across platforms:\n   *\n   * - `done`\n   * - `go`\n   * - `next`\n   * - `search`\n   * - `send`\n   *\n   * *Android Only*\n   *\n   * The following values work on Android only:\n   *\n   * - `none`\n   * - `previous`\n   *\n   * *iOS Only*\n   *\n   * The following values work on iOS only:\n   *\n   * - `default`\n   * - `emergency-call`\n   * - `google`\n   * - `join`\n   * - `route`\n   * - `yahoo`\n   */\n  returnKeyType?: ?ReturnKeyType,\n\n  /**\n   * If `true`, the text input obscures the text entered so that sensitive text\n   * like passwords stay secure. The default value is `false`. Does not work with 'multiline={true}'.\n   */\n  secureTextEntry?: ?boolean,\n\n  /**\n   * The start and end of the text input's selection. Set start and end to\n   * the same value to position the cursor.\n   */\n  selection?: ?$ReadOnly<{|\n    start: number,\n    end?: ?number,\n  |}>,\n\n  /**\n   * The highlight and cursor color of the text input.\n   */\n  selectionColor?: ?ColorValue,\n\n  /**\n   * The text selection handle color.\n   * @platform android\n   */\n  selectionHandleColor?: ?ColorValue,\n\n  /**\n   * If `true`, all text will automatically be selected on focus.\n   */\n  selectTextOnFocus?: ?boolean,\n\n  /**\n   * If `true`, the text field will blur when submitted.\n   * The default value is true for single-line fields and false for\n   * multiline fields. Note that for multiline fields, setting `blurOnSubmit`\n   * to `true` means that pressing return will blur the field and trigger the\n   * `onSubmitEditing` event instead of inserting a newline into the field.\n   *\n   * @deprecated\n   * Note that `submitBehavior` now takes the place of `blurOnSubmit` and will\n   * override any behavior defined by `blurOnSubmit`.\n   * @see submitBehavior\n   */\n  blurOnSubmit?: ?boolean,\n\n  /**\n   * When the return key is pressed,\n   *\n   * For single line inputs:\n   *\n   * - `'newline`' defaults to `'blurAndSubmit'`\n   * - `undefined` defaults to `'blurAndSubmit'`\n   *\n   * For multiline inputs:\n   *\n   * - `'newline'` adds a newline\n   * - `undefined` defaults to `'newline'`\n   *\n   * For both single line and multiline inputs:\n   *\n   * - `'submit'` will only send a submit event and not blur the input\n   * - `'blurAndSubmit`' will both blur the input and send a submit event\n   */\n  submitBehavior?: ?SubmitBehavior,\n\n  /**\n   * Note that not all Text styles are supported, an incomplete list of what is not supported includes:\n   *\n   * - `borderLeftWidth`\n   * - `borderTopWidth`\n   * - `borderRightWidth`\n   * - `borderBottomWidth`\n   * - `borderTopLeftRadius`\n   * - `borderTopRightRadius`\n   * - `borderBottomRightRadius`\n   * - `borderBottomLeftRadius`\n   *\n   * see [Issue#7070](https://github.com/facebook/react-native/issues/7070)\n   * for more detail.\n   *\n   * [Styles](docs/style.html)\n   */\n  style?: ?TextStyleProp,\n\n  /**\n   * The value to show for the text input. `TextInput` is a controlled\n   * component, which means the native value will be forced to match this\n   * value prop if provided. For most uses, this works great, but in some\n   * cases this may cause flickering - one common cause is preventing edits\n   * by keeping value the same. In addition to simply setting the same value,\n   * either set `editable={false}`, or set/update `maxLength` to prevent\n   * unwanted edits without flicker.\n   */\n  value?: ?Stringish,\n|}>;\n\ntype ViewCommands = $NonMaybeType<\n  | typeof AndroidTextInputCommands\n  | typeof RCTMultilineTextInputNativeCommands\n  | typeof RCTSinglelineTextInputNativeCommands,\n>;\n\ntype LastNativeSelection = {|\n  selection: Selection,\n  mostRecentEventCount: number,\n|};\n\nconst emptyFunctionThatReturnsTrue = () => true;\n\n/**\n * This hook handles the synchronization between the state of the text input\n * in native and in JavaScript. This is necessary due to the asynchronous nature\n * of text input events.\n */\nfunction useTextInputStateSynchronization_STATE({\n  props,\n  mostRecentEventCount,\n  selection,\n  inputRef,\n  text,\n  viewCommands,\n}: {\n  props: Props,\n  mostRecentEventCount: number,\n  selection: ?Selection,\n  inputRef: React.RefObject<null | HostInstance>,\n  text?: string,\n  viewCommands: ViewCommands,\n}): {\n  setLastNativeText: string => void,\n  setLastNativeSelection: LastNativeSelection => void,\n} {\n  const [lastNativeText, setLastNativeText] = useState<?Stringish>(props.value);\n  const [lastNativeSelectionState, setLastNativeSelection] =\n    useState<LastNativeSelection>({\n      selection: {start: -1, end: -1},\n      mostRecentEventCount: mostRecentEventCount,\n    });\n\n  const lastNativeSelection = lastNativeSelectionState.selection;\n\n  // This is necessary in case native updates the text and JS decides\n  // that the update should be ignored and we should stick with the value\n  // that we have in JS.\n  useLayoutEffect(() => {\n    const nativeUpdate: {text?: string, selection?: Selection} = {};\n\n    if (lastNativeText !== props.value && typeof props.value === 'string') {\n      nativeUpdate.text = props.value;\n      setLastNativeText(props.value);\n    }\n\n    if (\n      selection &&\n      lastNativeSelection &&\n      (lastNativeSelection.start !== selection.start ||\n        lastNativeSelection.end !== selection.end)\n    ) {\n      nativeUpdate.selection = selection;\n      setLastNativeSelection({selection, mostRecentEventCount});\n    }\n\n    if (Object.keys(nativeUpdate).length === 0) {\n      return;\n    }\n\n    if (inputRef.current != null) {\n      viewCommands.setTextAndSelection(\n        inputRef.current,\n        mostRecentEventCount,\n        text,\n        selection?.start ?? -1,\n        selection?.end ?? -1,\n      );\n    }\n  }, [\n    mostRecentEventCount,\n    inputRef,\n    props.value,\n    props.defaultValue,\n    lastNativeText,\n    selection,\n    lastNativeSelection,\n    text,\n    viewCommands,\n  ]);\n\n  return {setLastNativeText, setLastNativeSelection};\n}\n\n/**\n * This hook handles the synchronization between the state of the text input\n * in native and in JavaScript. This is necessary due to the asynchronous nature\n * of text input events.\n */\nfunction useTextInputStateSynchronization_REFS({\n  props,\n  mostRecentEventCount,\n  selection,\n  inputRef,\n  text,\n  viewCommands,\n}: {\n  props: Props,\n  mostRecentEventCount: number,\n  selection: ?Selection,\n  inputRef: React.RefObject<null | HostInstance>,\n  text?: string,\n  viewCommands: ViewCommands,\n}): {\n  setLastNativeText: string => void,\n  setLastNativeSelection: LastNativeSelection => void,\n} {\n  const lastNativeTextRef = useRef<?Stringish>(props.value);\n  const lastNativeSelectionRef = useRef<LastNativeSelection>({\n    selection: {start: -1, end: -1},\n    mostRecentEventCount: mostRecentEventCount,\n  });\n\n  // This is necessary in case native updates the text and JS decides\n  // that the update should be ignored and we should stick with the value\n  // that we have in JS.\n  useLayoutEffect(() => {\n    const nativeUpdate: {text?: string, selection?: Selection} = {};\n\n    const lastNativeSelection = lastNativeSelectionRef.current.selection;\n\n    if (\n      lastNativeTextRef.current !== props.value &&\n      typeof props.value === 'string'\n    ) {\n      nativeUpdate.text = props.value;\n      lastNativeTextRef.current = props.value;\n    }\n\n    if (\n      selection &&\n      lastNativeSelection &&\n      (lastNativeSelection.start !== selection.start ||\n        lastNativeSelection.end !== selection.end)\n    ) {\n      nativeUpdate.selection = selection;\n      lastNativeSelectionRef.current = {selection, mostRecentEventCount};\n    }\n\n    if (Object.keys(nativeUpdate).length === 0) {\n      return;\n    }\n\n    if (inputRef.current != null) {\n      viewCommands.setTextAndSelection(\n        inputRef.current,\n        mostRecentEventCount,\n        text,\n        selection?.start ?? -1,\n        selection?.end ?? -1,\n      );\n    }\n  }, [\n    mostRecentEventCount,\n    inputRef,\n    props.value,\n    props.defaultValue,\n    selection,\n    text,\n    viewCommands,\n  ]);\n\n  return {\n    setLastNativeText: lastNativeText => {\n      lastNativeTextRef.current = lastNativeText;\n    },\n    setLastNativeSelection: lastNativeSelection => {\n      lastNativeSelectionRef.current = lastNativeSelection;\n    },\n  };\n}\n\n/**\n * A foundational component for inputting text into the app via a\n * keyboard. Props provide configurability for several features, such as\n * auto-correction, auto-capitalization, placeholder text, and different keyboard\n * types, such as a numeric keypad.\n *\n * The simplest use case is to plop down a `TextInput` and subscribe to the\n * `onChangeText` events to read the user input. There are also other events,\n * such as `onSubmitEditing` and `onFocus` that can be subscribed to. A simple\n * example:\n *\n * ```ReactNativeWebPlayer\n * import React, { Component } from 'react';\n * import { AppRegistry, TextInput } from 'react-native';\n *\n * export default class UselessTextInput extends Component {\n *   constructor(props) {\n *     super(props);\n *     this.state = { text: 'Useless Placeholder' };\n *   }\n *\n *   render() {\n *     return (\n *       <TextInput\n *         style={{height: 40, borderColor: 'gray', borderWidth: 1}}\n *         onChangeText={(text) => this.setState({text})}\n *         value={this.state.text}\n *       />\n *     );\n *   }\n * }\n *\n * // skip this line if using Create React Native App\n * AppRegistry.registerComponent('AwesomeProject', () => UselessTextInput);\n * ```\n *\n * Two methods exposed via the native element are .focus() and .blur() that\n * will focus or blur the TextInput programmatically.\n *\n * Note that some props are only available with `multiline={true/false}`.\n * Additionally, border styles that apply to only one side of the element\n * (e.g., `borderBottomColor`, `borderLeftWidth`, etc.) will not be applied if\n * `multiline=false`. To achieve the same effect, you can wrap your `TextInput`\n * in a `View`:\n *\n * ```ReactNativeWebPlayer\n * import React, { Component } from 'react';\n * import { AppRegistry, View, TextInput } from 'react-native';\n *\n * class UselessTextInput extends Component {\n *   render() {\n *     return (\n *       <TextInput\n *         {...this.props} // Inherit any props passed to it; e.g., multiline, numberOfLines below\n *         editable={true}\n *         maxLength={40}\n *       />\n *     );\n *   }\n * }\n *\n * export default class UselessTextInputMultiline extends Component {\n *   constructor(props) {\n *     super(props);\n *     this.state = {\n *       text: 'Useless Multiline Placeholder',\n *     };\n *   }\n *\n *   // If you type something in the text box that is a color, the background will change to that\n *   // color.\n *   render() {\n *     return (\n *      <View style={{\n *        backgroundColor: this.state.text,\n *        borderBottomColor: '#000000',\n *        borderBottomWidth: 1 }}\n *      >\n *        <UselessTextInput\n *          multiline={true}\n *          numberOfLines={4}\n *          onChangeText={(text) => this.setState({text})}\n *          value={this.state.text}\n *        />\n *      </View>\n *     );\n *   }\n * }\n *\n * // skip these lines if using Create React Native App\n * AppRegistry.registerComponent(\n *  'AwesomeProject',\n *  () => UselessTextInputMultiline\n * );\n * ```\n *\n * `TextInput` has by default a border at the bottom of its view. This border\n * has its padding set by the background image provided by the system, and it\n * cannot be changed. Solutions to avoid this is to either not set height\n * explicitly, case in which the system will take care of displaying the border\n * in the correct position, or to not display the border by setting\n * `underlineColorAndroid` to transparent.\n *\n * Note that on Android performing text selection in input can change\n * app's activity `windowSoftInputMode` param to `adjustResize`.\n * This may cause issues with components that have position: 'absolute'\n * while keyboard is active. To avoid this behavior either specify `windowSoftInputMode`\n * in AndroidManifest.xml ( https://developer.android.com/guide/topics/manifest/activity-element.html )\n * or control this param programmatically with native code.\n *\n */\nfunction InternalTextInput(props: Props): React.Node {\n  const {\n    'aria-busy': ariaBusy,\n    'aria-checked': ariaChecked,\n    'aria-disabled': ariaDisabled,\n    'aria-expanded': ariaExpanded,\n    'aria-selected': ariaSelected,\n    accessibilityState,\n    id,\n    tabIndex,\n    selection: propsSelection,\n    selectionColor,\n    selectionHandleColor,\n    cursorColor,\n    ...otherProps\n  } = props;\n\n  const inputRef = useRef<null | HostInstance>(null);\n\n  const selection: ?Selection =\n    propsSelection == null\n      ? null\n      : {\n          start: propsSelection.start,\n          end: propsSelection.end ?? propsSelection.start,\n        };\n\n  const text =\n    typeof props.value === 'string'\n      ? props.value\n      : typeof props.defaultValue === 'string'\n        ? props.defaultValue\n        : undefined;\n\n  const viewCommands =\n    AndroidTextInputCommands ||\n    (props.multiline === true\n      ? RCTMultilineTextInputNativeCommands\n      : RCTSinglelineTextInputNativeCommands);\n\n  const [mostRecentEventCount, setMostRecentEventCount] = useState<number>(0);\n  const useTextInputStateSynchronization =\n    ReactNativeFeatureFlags.useRefsForTextInputState()\n      ? useTextInputStateSynchronization_REFS\n      : useTextInputStateSynchronization_STATE;\n  const {setLastNativeText, setLastNativeSelection} =\n    useTextInputStateSynchronization({\n      props,\n      inputRef,\n      mostRecentEventCount,\n      selection,\n      text,\n      viewCommands,\n    });\n\n  useLayoutEffect(() => {\n    const inputRefValue = inputRef.current;\n\n    if (inputRefValue != null) {\n      TextInputState.registerInput(inputRefValue);\n\n      return () => {\n        TextInputState.unregisterInput(inputRefValue);\n\n        if (TextInputState.currentlyFocusedInput() === inputRefValue) {\n          nullthrows(inputRefValue).blur();\n        }\n      };\n    }\n  }, []);\n\n  const setLocalRef = useCallback(\n    (instance: TextInputInstance | null) => {\n      inputRef.current = instance;\n\n      /*\n      Hi reader from the future. I'm sorry for this.\n\n      This is a hack. Ideally we would forwardRef to the underlying\n      host component. However, since TextInput has it's own methods that can be\n      called as well, if we used the standard forwardRef then these\n      methods wouldn't be accessible and thus be a breaking change.\n\n      We have a couple of options of how to handle this:\n      - Return a new ref with everything we methods from both. This is problematic\n        because we need React to also know it is a host component which requires\n        internals of the class implementation of the ref.\n      - Break the API and have some other way to call one set of the methods or\n        the other. This is our long term approach as we want to eventually\n        get the methods on host components off the ref. So instead of calling\n        ref.measure() you might call ReactNative.measure(ref). This would hopefully\n        let the ref for TextInput then have the methods like `.clear`. Or we do it\n        the other way and make it TextInput.clear(textInputRef) which would be fine\n        too. Either way though is a breaking change that is longer term.\n      - Mutate this ref. :( Gross, but accomplishes what we need in the meantime\n        before we can get to the long term breaking change.\n      */\n      if (instance != null) {\n        // $FlowFixMe[incompatible-use] - See the explanation above.\n        Object.assign(instance, {\n          clear(): void {\n            if (inputRef.current != null) {\n              viewCommands.setTextAndSelection(\n                inputRef.current,\n                mostRecentEventCount,\n                '',\n                0,\n                0,\n              );\n            }\n          },\n          // TODO: Fix this returning true on null === null, when no input is focused\n          isFocused(): boolean {\n            return TextInputState.currentlyFocusedInput() === inputRef.current;\n          },\n          getNativeRef(): ?HostInstance {\n            return inputRef.current;\n          },\n          setSelection(start: number, end: number): void {\n            if (inputRef.current != null) {\n              viewCommands.setTextAndSelection(\n                inputRef.current,\n                mostRecentEventCount,\n                null,\n                start,\n                end,\n              );\n            }\n          },\n        });\n      }\n    },\n    [mostRecentEventCount, viewCommands],\n  );\n\n  const ref = useMergeRefs<TextInputInstance>(setLocalRef, props.forwardedRef);\n\n  const _onChange = (event: ChangeEvent) => {\n    const currentText = event.nativeEvent.text;\n    props.onChange && props.onChange(event);\n    props.onChangeText && props.onChangeText(currentText);\n\n    if (inputRef.current == null) {\n      // calling `props.onChange` or `props.onChangeText`\n      // may clean up the input itself. Exits here.\n      return;\n    }\n\n    setLastNativeText(currentText);\n    // This must happen last, after we call setLastNativeText.\n    // Different ordering can cause bugs when editing AndroidTextInputs\n    // with multiple Fragments.\n    // We must update this so that controlled input updates work.\n    setMostRecentEventCount(event.nativeEvent.eventCount);\n  };\n\n  const _onSelectionChange = (event: SelectionChangeEvent) => {\n    props.onSelectionChange && props.onSelectionChange(event);\n\n    if (inputRef.current == null) {\n      // calling `props.onSelectionChange`\n      // may clean up the input itself. Exits here.\n      return;\n    }\n\n    setLastNativeSelection({\n      selection: event.nativeEvent.selection,\n      mostRecentEventCount,\n    });\n  };\n\n  const _onFocus = (event: FocusEvent) => {\n    TextInputState.focusInput(inputRef.current);\n    if (props.onFocus) {\n      props.onFocus(event);\n    }\n  };\n\n  const _onBlur = (event: BlurEvent) => {\n    TextInputState.blurInput(inputRef.current);\n    if (props.onBlur) {\n      props.onBlur(event);\n    }\n  };\n\n  const _onScroll = (event: ScrollEvent) => {\n    props.onScroll && props.onScroll(event);\n  };\n\n  let textInput = null;\n\n  const multiline = props.multiline ?? false;\n\n  let submitBehavior: SubmitBehavior;\n  if (props.submitBehavior != null) {\n    // `submitBehavior` is set explicitly\n    if (!multiline && props.submitBehavior === 'newline') {\n      // For single line text inputs, `'newline'` is not a valid option\n      submitBehavior = 'blurAndSubmit';\n    } else {\n      submitBehavior = props.submitBehavior;\n    }\n  } else if (multiline) {\n    if (props.blurOnSubmit === true) {\n      submitBehavior = 'blurAndSubmit';\n    } else {\n      submitBehavior = 'newline';\n    }\n  } else {\n    // Single line\n    if (props.blurOnSubmit !== false) {\n      submitBehavior = 'blurAndSubmit';\n    } else {\n      submitBehavior = 'submit';\n    }\n  }\n\n  const accessible = props.accessible !== false;\n  const focusable = props.focusable !== false;\n\n  const {\n    editable,\n    hitSlop,\n    onPress,\n    onPressIn,\n    onPressOut,\n    rejectResponderTermination,\n  } = props;\n\n  const config = React.useMemo(\n    () => ({\n      hitSlop,\n      onPress: (event: PressEvent) => {\n        onPress?.(event);\n        if (editable !== false) {\n          if (inputRef.current != null) {\n            inputRef.current.focus();\n          }\n        }\n      },\n      onPressIn: onPressIn,\n      onPressOut: onPressOut,\n      cancelable: Platform.OS === 'ios' ? !rejectResponderTermination : null,\n    }),\n    [\n      editable,\n      hitSlop,\n      onPress,\n      onPressIn,\n      onPressOut,\n      rejectResponderTermination,\n    ],\n  );\n\n  // Hide caret during test runs due to a flashing caret\n  // makes screenshot tests flakey\n  let caretHidden = props.caretHidden;\n  if (Platform.isTesting) {\n    caretHidden = true;\n  }\n\n  // TextInput handles onBlur and onFocus events\n  // so omitting onBlur and onFocus pressability handlers here.\n  const {onBlur, onFocus, ...eventHandlers} = usePressability(config);\n\n  let _accessibilityState;\n  if (\n    accessibilityState != null ||\n    ariaBusy != null ||\n    ariaChecked != null ||\n    ariaDisabled != null ||\n    ariaExpanded != null ||\n    ariaSelected != null\n  ) {\n    _accessibilityState = {\n      busy: ariaBusy ?? accessibilityState?.busy,\n      checked: ariaChecked ?? accessibilityState?.checked,\n      disabled: ariaDisabled ?? accessibilityState?.disabled,\n      expanded: ariaExpanded ?? accessibilityState?.expanded,\n      selected: ariaSelected ?? accessibilityState?.selected,\n    };\n  }\n\n  // Keep the original (potentially nested) style when possible, as React can diff these more efficiently\n  let _style = props.style;\n  const flattenedStyle = flattenStyle<TextStyleProp>(props.style);\n  if (flattenedStyle != null) {\n    let overrides: ?{...TextStyleInternal} = null;\n    if (typeof flattenedStyle?.fontWeight === 'number') {\n      overrides = overrides || ({}: {...TextStyleInternal});\n      overrides.fontWeight =\n        // $FlowFixMe[incompatible-cast]\n        (flattenedStyle.fontWeight.toString(): TextStyleInternal['fontWeight']);\n    }\n\n    if (flattenedStyle.verticalAlign != null) {\n      overrides = overrides || ({}: {...TextStyleInternal});\n      overrides.textAlignVertical =\n        verticalAlignToTextAlignVerticalMap[flattenedStyle.verticalAlign];\n      overrides.verticalAlign = undefined;\n    }\n\n    if (overrides != null) {\n      // $FlowFixMe[incompatible-type]\n      _style = [_style, overrides];\n    }\n  }\n\n  if (Platform.OS === 'ios') {\n    const RCTTextInputView =\n      props.multiline === true\n        ? RCTMultilineTextInputView\n        : RCTSinglelineTextInputView;\n\n    const useMultilineDefaultStyle =\n      props.multiline === true &&\n      (flattenedStyle == null ||\n        (flattenedStyle.padding == null &&\n          flattenedStyle.paddingVertical == null &&\n          flattenedStyle.paddingTop == null));\n\n    textInput = (\n      <RCTTextInputView\n        // $FlowFixMe[incompatible-type] - Figure out imperative + forward refs.\n        ref={ref}\n        {...otherProps}\n        {...eventHandlers}\n        accessibilityState={_accessibilityState}\n        accessible={accessible}\n        submitBehavior={submitBehavior}\n        caretHidden={caretHidden}\n        dataDetectorTypes={props.dataDetectorTypes}\n        focusable={tabIndex !== undefined ? !tabIndex : focusable}\n        mostRecentEventCount={mostRecentEventCount}\n        nativeID={id ?? props.nativeID}\n        onBlur={_onBlur}\n        onChange={_onChange}\n        onContentSizeChange={props.onContentSizeChange}\n        onFocus={_onFocus}\n        onScroll={_onScroll}\n        onSelectionChange={_onSelectionChange}\n        onSelectionChangeShouldSetResponder={emptyFunctionThatReturnsTrue}\n        selection={selection}\n        selectionColor={selectionColor}\n        style={StyleSheet.compose(\n          useMultilineDefaultStyle ? styles.multilineDefault : null,\n          _style,\n        )}\n        text={text}\n      />\n    );\n  } else if (Platform.OS === 'android') {\n    const autoCapitalize = props.autoCapitalize || 'sentences';\n    const _accessibilityLabelledBy =\n      props?.['aria-labelledby'] ?? props?.accessibilityLabelledBy;\n    const placeholder = props.placeholder ?? '';\n    let children = props.children;\n    const childCount = React.Children.count(children);\n    invariant(\n      !(props.value != null && childCount),\n      'Cannot specify both value and children.',\n    );\n    if (childCount > 1) {\n      children = <Text>{children}</Text>;\n    }\n    // For consistency with iOS set cursor/selectionHandle color as selectionColor\n    const colorProps = {\n      selectionColor,\n      selectionHandleColor:\n        selectionHandleColor === undefined\n          ? selectionColor\n          : selectionHandleColor,\n      cursorColor: cursorColor === undefined ? selectionColor : cursorColor,\n    };\n    textInput = (\n      /* $FlowFixMe[prop-missing] the types for AndroidTextInput don't match up\n       * exactly with the props for TextInput. This will need to get fixed */\n      /* $FlowFixMe[incompatible-type] the types for AndroidTextInput don't\n       * match up exactly with the props for TextInput. This will need to get\n       * fixed */\n      /* $FlowFixMe[incompatible-type-arg] the types for AndroidTextInput don't\n       * match up exactly with the props for TextInput. This will need to get\n       * fixed */\n      <AndroidTextInput\n        // $FlowFixMe[incompatible-type] - Figure out imperative + forward refs.\n        ref={ref}\n        {...otherProps}\n        {...colorProps}\n        {...eventHandlers}\n        accessibilityState={_accessibilityState}\n        accessibilityLabelledBy={_accessibilityLabelledBy}\n        accessible={accessible}\n        autoCapitalize={autoCapitalize}\n        submitBehavior={submitBehavior}\n        caretHidden={caretHidden}\n        children={children}\n        disableFullscreenUI={props.disableFullscreenUI}\n        focusable={tabIndex !== undefined ? !tabIndex : focusable}\n        mostRecentEventCount={mostRecentEventCount}\n        nativeID={id ?? props.nativeID}\n        numberOfLines={props.rows ?? props.numberOfLines}\n        onBlur={_onBlur}\n        onChange={_onChange}\n        onFocus={_onFocus}\n        /* $FlowFixMe[prop-missing] the types for AndroidTextInput don't match\n         * up exactly with the props for TextInput. This will need to get fixed\n         */\n        /* $FlowFixMe[incompatible-type-arg] the types for AndroidTextInput\n         * don't match up exactly with the props for TextInput. This will need\n         * to get fixed */\n        onScroll={_onScroll}\n        onSelectionChange={_onSelectionChange}\n        placeholder={placeholder}\n        style={_style}\n        text={text}\n        textBreakStrategy={props.textBreakStrategy}\n      />\n    );\n  }\n  return (\n    <TextAncestor.Provider value={true}>{textInput}</TextAncestor.Provider>\n  );\n}\n\nconst enterKeyHintToReturnTypeMap = {\n  enter: 'default',\n  done: 'done',\n  go: 'go',\n  next: 'next',\n  previous: 'previous',\n  search: 'search',\n  send: 'send',\n};\n\nconst inputModeToKeyboardTypeMap = {\n  none: 'default',\n  text: 'default',\n  decimal: 'decimal-pad',\n  numeric: 'number-pad',\n  tel: 'phone-pad',\n  search: Platform.OS === 'ios' ? 'web-search' : 'default',\n  email: 'email-address',\n  url: 'url',\n};\n\n// Map HTML autocomplete values to Android autoComplete values\nconst autoCompleteWebToAutoCompleteAndroidMap = {\n  'address-line1': 'postal-address-region',\n  'address-line2': 'postal-address-locality',\n  bday: 'birthdate-full',\n  'bday-day': 'birthdate-day',\n  'bday-month': 'birthdate-month',\n  'bday-year': 'birthdate-year',\n  'cc-csc': 'cc-csc',\n  'cc-exp': 'cc-exp',\n  'cc-exp-month': 'cc-exp-month',\n  'cc-exp-year': 'cc-exp-year',\n  'cc-number': 'cc-number',\n  country: 'postal-address-country',\n  'current-password': 'password',\n  email: 'email',\n  'honorific-prefix': 'name-prefix',\n  'honorific-suffix': 'name-suffix',\n  name: 'name',\n  'additional-name': 'name-middle',\n  'family-name': 'name-family',\n  'given-name': 'name-given',\n  'new-password': 'password-new',\n  off: 'off',\n  'one-time-code': 'sms-otp',\n  'postal-code': 'postal-code',\n  sex: 'gender',\n  'street-address': 'street-address',\n  tel: 'tel',\n  'tel-country-code': 'tel-country-code',\n  'tel-national': 'tel-national',\n  username: 'username',\n};\n\n// Map HTML autocomplete values to iOS textContentType values\nconst autoCompleteWebToTextContentTypeMap = {\n  'address-line1': 'streetAddressLine1',\n  'address-line2': 'streetAddressLine2',\n  bday: 'birthdate',\n  'bday-day': 'birthdateDay',\n  'bday-month': 'birthdateMonth',\n  'bday-year': 'birthdateYear',\n  'cc-csc': 'creditCardSecurityCode',\n  'cc-exp-month': 'creditCardExpirationMonth',\n  'cc-exp-year': 'creditCardExpirationYear',\n  'cc-exp': 'creditCardExpiration',\n  'cc-given-name': 'creditCardGivenName',\n  'cc-additional-name': 'creditCardMiddleName',\n  'cc-family-name': 'creditCardFamilyName',\n  'cc-name': 'creditCardName',\n  'cc-number': 'creditCardNumber',\n  'cc-type': 'creditCardType',\n  'current-password': 'password',\n  country: 'countryName',\n  email: 'emailAddress',\n  name: 'name',\n  'additional-name': 'middleName',\n  'family-name': 'familyName',\n  'given-name': 'givenName',\n  nickname: 'nickname',\n  'honorific-prefix': 'namePrefix',\n  'honorific-suffix': 'nameSuffix',\n  'new-password': 'newPassword',\n  off: 'none',\n  'one-time-code': 'oneTimeCode',\n  organization: 'organizationName',\n  'organization-title': 'jobTitle',\n  'postal-code': 'postalCode',\n  'street-address': 'fullStreetAddress',\n  tel: 'telephoneNumber',\n  url: 'URL',\n  username: 'username',\n};\n\nconst ExportedForwardRef: component(\n  ref: React.RefSetter<TextInputInstance>,\n  ...props: React.ElementConfig<typeof InternalTextInput>\n  // $FlowFixMe[incompatible-call]\n) = React.forwardRef(function TextInput(\n  {\n    allowFontScaling = true,\n    rejectResponderTermination = true,\n    underlineColorAndroid = 'transparent',\n    autoComplete,\n    textContentType,\n    readOnly,\n    editable,\n    enterKeyHint,\n    returnKeyType,\n    inputMode,\n    showSoftInputOnFocus,\n    keyboardType,\n    ...restProps\n  },\n  forwardedRef: ReactRefSetter<TextInputInstance>,\n) {\n  return (\n    <InternalTextInput\n      allowFontScaling={allowFontScaling}\n      rejectResponderTermination={rejectResponderTermination}\n      underlineColorAndroid={underlineColorAndroid}\n      editable={readOnly !== undefined ? !readOnly : editable}\n      returnKeyType={\n        enterKeyHint ? enterKeyHintToReturnTypeMap[enterKeyHint] : returnKeyType\n      }\n      keyboardType={\n        inputMode ? inputModeToKeyboardTypeMap[inputMode] : keyboardType\n      }\n      showSoftInputOnFocus={\n        inputMode == null ? showSoftInputOnFocus : inputMode !== 'none'\n      }\n      autoComplete={\n        Platform.OS === 'android'\n          ? // $FlowFixMe[invalid-computed-prop]\n            // $FlowFixMe[prop-missing]\n            autoCompleteWebToAutoCompleteAndroidMap[autoComplete] ??\n            autoComplete\n          : undefined\n      }\n      textContentType={\n        textContentType != null\n          ? textContentType\n          : Platform.OS === 'ios' &&\n              autoComplete &&\n              autoComplete in autoCompleteWebToTextContentTypeMap\n            ? // $FlowFixMe[invalid-computed-prop]\n              // $FlowFixMe[prop-missing]\n              autoCompleteWebToTextContentTypeMap[autoComplete]\n            : textContentType\n      }\n      {...restProps}\n      forwardedRef={forwardedRef}\n    />\n  );\n});\n\nExportedForwardRef.displayName = 'TextInput';\n\n// $FlowFixMe[prop-missing]\nExportedForwardRef.State = {\n  currentlyFocusedInput: TextInputState.currentlyFocusedInput,\n\n  currentlyFocusedField: TextInputState.currentlyFocusedField,\n  focusTextInput: TextInputState.focusTextInput,\n  blurTextInput: TextInputState.blurTextInput,\n};\n\nexport type TextInputComponentStatics = $ReadOnly<{|\n  State: $ReadOnly<{|\n    currentlyFocusedInput: typeof TextInputState.currentlyFocusedInput,\n    currentlyFocusedField: typeof TextInputState.currentlyFocusedField,\n    focusTextInput: typeof TextInputState.focusTextInput,\n    blurTextInput: typeof TextInputState.blurTextInput,\n  |}>,\n|}>;\n\nconst styles = StyleSheet.create({\n  multilineDefault: {\n    // This default top inset makes RCTMultilineTextInputView seem as close as possible\n    // to single-line RCTSinglelineTextInputView defaults, using the system defaults\n    // of font size 17 and a height of 31 points.\n    paddingTop: 5,\n  },\n});\n\nconst verticalAlignToTextAlignVerticalMap = {\n  auto: 'auto',\n  top: 'top',\n  bottom: 'bottom',\n  middle: 'center',\n};\n\n// $FlowFixMe[unclear-type] Unclear type. Using `any` type is not safe.\nmodule.exports = ((ExportedForwardRef: any): TextInputType);\n"], "mappings": ";;;AAoBA,IAAAA,uBAAA,GAAAC,uBAAA,CAAAC,OAAA;AACA,IAAAC,iBAAA,GAAAC,sBAAA,CAAAF,OAAA;AACA,IAAAG,aAAA,GAAAD,sBAAA,CAAAF,OAAA;AACA,IAAAI,WAAA,GAAAF,sBAAA,CAAAF,OAAA;AAKA,IAAAK,KAAA,GAAAH,sBAAA,CAAAF,OAAA;AACA,IAAAM,aAAA,GAAAJ,sBAAA,CAAAF,OAAA;AACA,IAAAO,SAAA,GAAAL,sBAAA,CAAAF,OAAA;AACA,IAAAQ,aAAA,GAAAN,sBAAA,CAAAF,OAAA;AACA,IAAAS,eAAA,GAAAP,sBAAA,CAAAF,OAAA;AACA,IAAAU,UAAA,GAAAR,sBAAA,CAAAF,OAAA;AACA,IAAAW,WAAA,GAAAT,sBAAA,CAAAF,OAAA;AACA,IAAAY,MAAA,GAAAb,uBAAA,CAAAC,OAAA;AAA+B,IAAAa,KAAA,GAAAD,MAAA;AAAA,IAAAE,WAAA,GAAAd,OAAA;AAAA,IAAAe,SAAA;EAAAC,UAAA;EAAAC,UAAA;AAAA,SAAAC,yBAAAC,CAAA,6BAAAC,OAAA,mBAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,yBAAAC,CAAA,WAAAA,CAAA,GAAAG,CAAA,GAAAD,CAAA,KAAAF,CAAA;AAAA,SAAApB,wBAAAoB,CAAA,EAAAE,CAAA,SAAAA,CAAA,IAAAF,CAAA,IAAAA,CAAA,CAAAI,UAAA,SAAAJ,CAAA,eAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,WAAAK,OAAA,EAAAL,CAAA,QAAAG,CAAA,GAAAJ,wBAAA,CAAAG,CAAA,OAAAC,CAAA,IAAAA,CAAA,CAAAG,GAAA,CAAAN,CAAA,UAAAG,CAAA,CAAAI,GAAA,CAAAP,CAAA,OAAAQ,CAAA,KAAAC,SAAA,UAAAC,CAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,CAAA,IAAAd,CAAA,oBAAAc,CAAA,OAAAC,cAAA,CAAAC,IAAA,CAAAhB,CAAA,EAAAc,CAAA,SAAAG,CAAA,GAAAP,CAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAb,CAAA,EAAAc,CAAA,UAAAG,CAAA,KAAAA,CAAA,CAAAV,GAAA,IAAAU,CAAA,CAAAC,GAAA,IAAAP,MAAA,CAAAC,cAAA,CAAAJ,CAAA,EAAAM,CAAA,EAAAG,CAAA,IAAAT,CAAA,CAAAM,CAAA,IAAAd,CAAA,CAAAc,CAAA,YAAAN,CAAA,CAAAH,OAAA,GAAAL,CAAA,EAAAG,CAAA,IAAAA,CAAA,CAAAe,GAAA,CAAAlB,CAAA,EAAAQ,CAAA,GAAAA,CAAA;AAW/B,IAAIW,gBAAgB;AACpB,IAAIC,wBAAwB;AAC5B,IAAIC,0BAA0B;AAC9B,IAAIC,oCAAoC;AACxC,IAAIC,yBAAyB;AAC7B,IAAIC,mCAAmC;AAEvC,IAAIC,iBAAQ,CAACC,EAAE,KAAK,SAAS,EAAE;EAC7BP,gBAAgB,GAAGtC,OAAO,CAAC,mCAAmC,CAAC,CAACwB,OAAO;EACvEe,wBAAwB,GACtBvC,OAAO,CAAC,mCAAmC,CAAC,CAAC8C,QAAQ;AACzD,CAAC,MAAM,IAAIF,iBAAQ,CAACC,EAAE,KAAK,KAAK,EAAE;EAChCL,0BAA0B,GACxBxC,OAAO,CAAC,wCAAwC,CAAC,CAACwB,OAAO;EAC3DiB,oCAAoC,GAClCzC,OAAO,CAAC,wCAAwC,CAAC,CAAC8C,QAAQ;EAC5DJ,yBAAyB,GACvB1C,OAAO,CAAC,wCAAwC,CAAC,CAACwB,OAAO;EAC3DmB,mCAAmC,GACjC3C,OAAO,CAAC,wCAAwC,CAAC,CAAC8C,QAAQ;AAC9D;AAw6BA,IAAMC,4BAA4B,GAAG,SAA/BA,4BAA4BA,CAAA;EAAA,OAAS,IAAI;AAAA;AAO/C,SAASC,sCAAsCA,CAAAC,IAAA,EAiB7C;EAAA,IAhBAC,KAAK,GAAAD,IAAA,CAALC,KAAK;IACLC,oBAAoB,GAAAF,IAAA,CAApBE,oBAAoB;IACpBC,SAAS,GAAAH,IAAA,CAATG,SAAS;IACTC,QAAQ,GAAAJ,IAAA,CAARI,QAAQ;IACRC,IAAI,GAAAL,IAAA,CAAJK,IAAI;IACJC,YAAY,GAAAN,IAAA,CAAZM,YAAY;EAYZ,IAAAC,SAAA,GAA4C,IAAAC,eAAQ,EAAaP,KAAK,CAACQ,KAAK,CAAC;IAAAC,UAAA,OAAAC,eAAA,CAAApC,OAAA,EAAAgC,SAAA;IAAtEK,cAAc,GAAAF,UAAA;IAAEG,iBAAiB,GAAAH,UAAA;EACxC,IAAAI,UAAA,GACE,IAAAN,eAAQ,EAAsB;MAC5BL,SAAS,EAAE;QAACY,KAAK,EAAE,CAAC,CAAC;QAAEC,GAAG,EAAE,CAAC;MAAC,CAAC;MAC/Bd,oBAAoB,EAAEA;IACxB,CAAC,CAAC;IAAAe,UAAA,OAAAN,eAAA,CAAApC,OAAA,EAAAuC,UAAA;IAJGI,wBAAwB,GAAAD,UAAA;IAAEE,sBAAsB,GAAAF,UAAA;EAMvD,IAAMG,mBAAmB,GAAGF,wBAAwB,CAACf,SAAS;EAK9D,IAAAkB,sBAAe,EAAC,YAAM;IACpB,IAAMC,YAAoD,GAAG,CAAC,CAAC;IAE/D,IAAIV,cAAc,KAAKX,KAAK,CAACQ,KAAK,IAAI,OAAOR,KAAK,CAACQ,KAAK,KAAK,QAAQ,EAAE;MACrEa,YAAY,CAACjB,IAAI,GAAGJ,KAAK,CAACQ,KAAK;MAC/BI,iBAAiB,CAACZ,KAAK,CAACQ,KAAK,CAAC;IAChC;IAEA,IACEN,SAAS,IACTiB,mBAAmB,KAClBA,mBAAmB,CAACL,KAAK,KAAKZ,SAAS,CAACY,KAAK,IAC5CK,mBAAmB,CAACJ,GAAG,KAAKb,SAAS,CAACa,GAAG,CAAC,EAC5C;MACAM,YAAY,CAACnB,SAAS,GAAGA,SAAS;MAClCgB,sBAAsB,CAAC;QAAChB,SAAS,EAATA,SAAS;QAAED,oBAAoB,EAApBA;MAAoB,CAAC,CAAC;IAC3D;IAEA,IAAIrB,MAAM,CAAC0C,IAAI,CAACD,YAAY,CAAC,CAACE,MAAM,KAAK,CAAC,EAAE;MAC1C;IACF;IAEA,IAAIpB,QAAQ,CAACqB,OAAO,IAAI,IAAI,EAAE;MAAA,IAAAC,gBAAA,EAAAC,cAAA;MAC5BrB,YAAY,CAACsB,mBAAmB,CAC9BxB,QAAQ,CAACqB,OAAO,EAChBvB,oBAAoB,EACpBG,IAAI,GAAAqB,gBAAA,GACJvB,SAAS,oBAATA,SAAS,CAAEY,KAAK,YAAAW,gBAAA,GAAI,CAAC,CAAC,GAAAC,cAAA,GACtBxB,SAAS,oBAATA,SAAS,CAAEa,GAAG,YAAAW,cAAA,GAAI,CAAC,CACrB,CAAC;IACH;EACF,CAAC,EAAE,CACDzB,oBAAoB,EACpBE,QAAQ,EACRH,KAAK,CAACQ,KAAK,EACXR,KAAK,CAAC4B,YAAY,EAClBjB,cAAc,EACdT,SAAS,EACTiB,mBAAmB,EACnBf,IAAI,EACJC,YAAY,CACb,CAAC;EAEF,OAAO;IAACO,iBAAiB,EAAjBA,iBAAiB;IAAEM,sBAAsB,EAAtBA;EAAsB,CAAC;AACpD;AAOA,SAASW,qCAAqCA,CAAAC,KAAA,EAiB5C;EAAA,IAhBA9B,KAAK,GAAA8B,KAAA,CAAL9B,KAAK;IACLC,oBAAoB,GAAA6B,KAAA,CAApB7B,oBAAoB;IACpBC,SAAS,GAAA4B,KAAA,CAAT5B,SAAS;IACTC,QAAQ,GAAA2B,KAAA,CAAR3B,QAAQ;IACRC,IAAI,GAAA0B,KAAA,CAAJ1B,IAAI;IACJC,YAAY,GAAAyB,KAAA,CAAZzB,YAAY;EAYZ,IAAM0B,iBAAiB,GAAG,IAAAC,aAAM,EAAahC,KAAK,CAACQ,KAAK,CAAC;EACzD,IAAMyB,sBAAsB,GAAG,IAAAD,aAAM,EAAsB;IACzD9B,SAAS,EAAE;MAACY,KAAK,EAAE,CAAC,CAAC;MAAEC,GAAG,EAAE,CAAC;IAAC,CAAC;IAC/Bd,oBAAoB,EAAEA;EACxB,CAAC,CAAC;EAKF,IAAAmB,sBAAe,EAAC,YAAM;IACpB,IAAMC,YAAoD,GAAG,CAAC,CAAC;IAE/D,IAAMF,mBAAmB,GAAGc,sBAAsB,CAACT,OAAO,CAACtB,SAAS;IAEpE,IACE6B,iBAAiB,CAACP,OAAO,KAAKxB,KAAK,CAACQ,KAAK,IACzC,OAAOR,KAAK,CAACQ,KAAK,KAAK,QAAQ,EAC/B;MACAa,YAAY,CAACjB,IAAI,GAAGJ,KAAK,CAACQ,KAAK;MAC/BuB,iBAAiB,CAACP,OAAO,GAAGxB,KAAK,CAACQ,KAAK;IACzC;IAEA,IACEN,SAAS,IACTiB,mBAAmB,KAClBA,mBAAmB,CAACL,KAAK,KAAKZ,SAAS,CAACY,KAAK,IAC5CK,mBAAmB,CAACJ,GAAG,KAAKb,SAAS,CAACa,GAAG,CAAC,EAC5C;MACAM,YAAY,CAACnB,SAAS,GAAGA,SAAS;MAClC+B,sBAAsB,CAACT,OAAO,GAAG;QAACtB,SAAS,EAATA,SAAS;QAAED,oBAAoB,EAApBA;MAAoB,CAAC;IACpE;IAEA,IAAIrB,MAAM,CAAC0C,IAAI,CAACD,YAAY,CAAC,CAACE,MAAM,KAAK,CAAC,EAAE;MAC1C;IACF;IAEA,IAAIpB,QAAQ,CAACqB,OAAO,IAAI,IAAI,EAAE;MAAA,IAAAU,iBAAA,EAAAC,eAAA;MAC5B9B,YAAY,CAACsB,mBAAmB,CAC9BxB,QAAQ,CAACqB,OAAO,EAChBvB,oBAAoB,EACpBG,IAAI,GAAA8B,iBAAA,GACJhC,SAAS,oBAATA,SAAS,CAAEY,KAAK,YAAAoB,iBAAA,GAAI,CAAC,CAAC,GAAAC,eAAA,GACtBjC,SAAS,oBAATA,SAAS,CAAEa,GAAG,YAAAoB,eAAA,GAAI,CAAC,CACrB,CAAC;IACH;EACF,CAAC,EAAE,CACDlC,oBAAoB,EACpBE,QAAQ,EACRH,KAAK,CAACQ,KAAK,EACXR,KAAK,CAAC4B,YAAY,EAClB1B,SAAS,EACTE,IAAI,EACJC,YAAY,CACb,CAAC;EAEF,OAAO;IACLO,iBAAiB,EAAE,SAAnBA,iBAAiBA,CAAED,cAAc,EAAI;MACnCoB,iBAAiB,CAACP,OAAO,GAAGb,cAAc;IAC5C,CAAC;IACDO,sBAAsB,EAAE,SAAxBA,sBAAsBA,CAAEC,mBAAmB,EAAI;MAC7Cc,sBAAsB,CAACT,OAAO,GAAGL,mBAAmB;IACtD;EACF,CAAC;AACH;AAiHA,SAASiB,iBAAiBA,CAACpC,KAAY,EAAc;EAAA,IAAAqC,mBAAA,EAAAC,gBAAA;EACnD,IACeC,QAAQ,GAanBvC,KAAK,CAbP,WAAW;IACKwC,WAAW,GAYzBxC,KAAK,CAZP,cAAc;IACGyC,YAAY,GAW3BzC,KAAK,CAXP,eAAe;IACE0C,YAAY,GAU3B1C,KAAK,CAVP,eAAe;IACE2C,YAAY,GAS3B3C,KAAK,CATP,eAAe;IACf4C,kBAAkB,GAQhB5C,KAAK,CARP4C,kBAAkB;IAClBC,EAAE,GAOA7C,KAAK,CAPP6C,EAAE;IACFC,QAAQ,GAMN9C,KAAK,CANP8C,QAAQ;IACGC,cAAc,GAKvB/C,KAAK,CALPE,SAAS;IACT8C,cAAc,GAIZhD,KAAK,CAJPgD,cAAc;IACdC,oBAAoB,GAGlBjD,KAAK,CAHPiD,oBAAoB;IACpBC,WAAW,GAETlD,KAAK,CAFPkD,WAAW;IACRC,UAAU,OAAAC,yBAAA,CAAA9E,OAAA,EACX0B,KAAK,EAAAnC,SAAA;EAET,IAAMsC,QAAQ,GAAG,IAAA6B,aAAM,EAAsB,IAAI,CAAC;EAElD,IAAM9B,SAAqB,GACzB6C,cAAc,IAAI,IAAI,GAClB,IAAI,GACJ;IACEjC,KAAK,EAAEiC,cAAc,CAACjC,KAAK;IAC3BC,GAAG,GAAAsB,mBAAA,GAAEU,cAAc,CAAChC,GAAG,YAAAsB,mBAAA,GAAIU,cAAc,CAACjC;EAC5C,CAAC;EAEP,IAAMV,IAAI,GACR,OAAOJ,KAAK,CAACQ,KAAK,KAAK,QAAQ,GAC3BR,KAAK,CAACQ,KAAK,GACX,OAAOR,KAAK,CAAC4B,YAAY,KAAK,QAAQ,GACpC5B,KAAK,CAAC4B,YAAY,GAClByB,SAAS;EAEjB,IAAMhD,YAAY,GAChBhB,wBAAwB,KACvBW,KAAK,CAACsD,SAAS,KAAK,IAAI,GACrB7D,mCAAmC,GACnCF,oCAAoC,CAAC;EAE3C,IAAAgE,UAAA,GAAwD,IAAAhD,eAAQ,EAAS,CAAC,CAAC;IAAAiD,UAAA,OAAA9C,eAAA,CAAApC,OAAA,EAAAiF,UAAA;IAApEtD,oBAAoB,GAAAuD,UAAA;IAAEC,uBAAuB,GAAAD,UAAA;EACpD,IAAME,gCAAgC,GACpC9G,uBAAuB,CAAC+G,wBAAwB,CAAC,CAAC,GAC9C9B,qCAAqC,GACrC/B,sCAAsC;EAC5C,IAAA8D,qBAAA,GACEF,gCAAgC,CAAC;MAC/B1D,KAAK,EAALA,KAAK;MACLG,QAAQ,EAARA,QAAQ;MACRF,oBAAoB,EAApBA,oBAAoB;MACpBC,SAAS,EAATA,SAAS;MACTE,IAAI,EAAJA,IAAI;MACJC,YAAY,EAAZA;IACF,CAAC,CAAC;IARGO,iBAAiB,GAAAgD,qBAAA,CAAjBhD,iBAAiB;IAAEM,sBAAsB,GAAA0C,qBAAA,CAAtB1C,sBAAsB;EAUhD,IAAAE,sBAAe,EAAC,YAAM;IACpB,IAAMyC,aAAa,GAAG1D,QAAQ,CAACqB,OAAO;IAEtC,IAAIqC,aAAa,IAAI,IAAI,EAAE;MACzBC,uBAAc,CAACC,aAAa,CAACF,aAAa,CAAC;MAE3C,OAAO,YAAM;QACXC,uBAAc,CAACE,eAAe,CAACH,aAAa,CAAC;QAE7C,IAAIC,uBAAc,CAACG,qBAAqB,CAAC,CAAC,KAAKJ,aAAa,EAAE;UAC5D,IAAAK,mBAAU,EAACL,aAAa,CAAC,CAACM,IAAI,CAAC,CAAC;QAClC;MACF,CAAC;IACH;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,IAAMC,WAAW,GAAG,IAAAC,kBAAW,EAC7B,UAACC,QAAkC,EAAK;IACtCnE,QAAQ,CAACqB,OAAO,GAAG8C,QAAQ;IAwB3B,IAAIA,QAAQ,IAAI,IAAI,EAAE;MAEpB1F,MAAM,CAAC2F,MAAM,CAACD,QAAQ,EAAE;QACtBE,KAAK,WAALA,KAAKA,CAAA,EAAS;UACZ,IAAIrE,QAAQ,CAACqB,OAAO,IAAI,IAAI,EAAE;YAC5BnB,YAAY,CAACsB,mBAAmB,CAC9BxB,QAAQ,CAACqB,OAAO,EAChBvB,oBAAoB,EACpB,EAAE,EACF,CAAC,EACD,CACF,CAAC;UACH;QACF,CAAC;QAEDwE,SAAS,WAATA,SAASA,CAAA,EAAY;UACnB,OAAOX,uBAAc,CAACG,qBAAqB,CAAC,CAAC,KAAK9D,QAAQ,CAACqB,OAAO;QACpE,CAAC;QACDkD,YAAY,WAAZA,YAAYA,CAAA,EAAkB;UAC5B,OAAOvE,QAAQ,CAACqB,OAAO;QACzB,CAAC;QACDmD,YAAY,WAAZA,YAAYA,CAAC7D,KAAa,EAAEC,GAAW,EAAQ;UAC7C,IAAIZ,QAAQ,CAACqB,OAAO,IAAI,IAAI,EAAE;YAC5BnB,YAAY,CAACsB,mBAAmB,CAC9BxB,QAAQ,CAACqB,OAAO,EAChBvB,oBAAoB,EACpB,IAAI,EACJa,KAAK,EACLC,GACF,CAAC;UACH;QACF;MACF,CAAC,CAAC;IACJ;EACF,CAAC,EACD,CAACd,oBAAoB,EAAEI,YAAY,CACrC,CAAC;EAED,IAAMuE,GAAG,GAAG,IAAAC,qBAAY,EAAoBT,WAAW,EAAEpE,KAAK,CAAC8E,YAAY,CAAC;EAE5E,IAAMC,SAAS,GAAG,SAAZA,SAASA,CAAIC,KAAkB,EAAK;IACxC,IAAMC,WAAW,GAAGD,KAAK,CAACE,WAAW,CAAC9E,IAAI;IAC1CJ,KAAK,CAACmF,QAAQ,IAAInF,KAAK,CAACmF,QAAQ,CAACH,KAAK,CAAC;IACvChF,KAAK,CAACoF,YAAY,IAAIpF,KAAK,CAACoF,YAAY,CAACH,WAAW,CAAC;IAErD,IAAI9E,QAAQ,CAACqB,OAAO,IAAI,IAAI,EAAE;MAG5B;IACF;IAEAZ,iBAAiB,CAACqE,WAAW,CAAC;IAK9BxB,uBAAuB,CAACuB,KAAK,CAACE,WAAW,CAACG,UAAU,CAAC;EACvD,CAAC;EAED,IAAMC,kBAAkB,GAAG,SAArBA,kBAAkBA,CAAIN,KAA2B,EAAK;IAC1DhF,KAAK,CAACuF,iBAAiB,IAAIvF,KAAK,CAACuF,iBAAiB,CAACP,KAAK,CAAC;IAEzD,IAAI7E,QAAQ,CAACqB,OAAO,IAAI,IAAI,EAAE;MAG5B;IACF;IAEAN,sBAAsB,CAAC;MACrBhB,SAAS,EAAE8E,KAAK,CAACE,WAAW,CAAChF,SAAS;MACtCD,oBAAoB,EAApBA;IACF,CAAC,CAAC;EACJ,CAAC;EAED,IAAMuF,QAAQ,GAAG,SAAXA,QAAQA,CAAIR,KAAiB,EAAK;IACtClB,uBAAc,CAAC2B,UAAU,CAACtF,QAAQ,CAACqB,OAAO,CAAC;IAC3C,IAAIxB,KAAK,CAAC0F,OAAO,EAAE;MACjB1F,KAAK,CAAC0F,OAAO,CAACV,KAAK,CAAC;IACtB;EACF,CAAC;EAED,IAAMW,OAAO,GAAG,SAAVA,OAAOA,CAAIX,KAAgB,EAAK;IACpClB,uBAAc,CAAC8B,SAAS,CAACzF,QAAQ,CAACqB,OAAO,CAAC;IAC1C,IAAIxB,KAAK,CAAC6F,MAAM,EAAE;MAChB7F,KAAK,CAAC6F,MAAM,CAACb,KAAK,CAAC;IACrB;EACF,CAAC;EAED,IAAMc,SAAS,GAAG,SAAZA,SAASA,CAAId,KAAkB,EAAK;IACxChF,KAAK,CAAC+F,QAAQ,IAAI/F,KAAK,CAAC+F,QAAQ,CAACf,KAAK,CAAC;EACzC,CAAC;EAED,IAAIgB,SAAS,GAAG,IAAI;EAEpB,IAAM1C,SAAS,IAAAhB,gBAAA,GAAGtC,KAAK,CAACsD,SAAS,YAAAhB,gBAAA,GAAI,KAAK;EAE1C,IAAI2D,cAA8B;EAClC,IAAIjG,KAAK,CAACiG,cAAc,IAAI,IAAI,EAAE;IAEhC,IAAI,CAAC3C,SAAS,IAAItD,KAAK,CAACiG,cAAc,KAAK,SAAS,EAAE;MAEpDA,cAAc,GAAG,eAAe;IAClC,CAAC,MAAM;MACLA,cAAc,GAAGjG,KAAK,CAACiG,cAAc;IACvC;EACF,CAAC,MAAM,IAAI3C,SAAS,EAAE;IACpB,IAAItD,KAAK,CAACkG,YAAY,KAAK,IAAI,EAAE;MAC/BD,cAAc,GAAG,eAAe;IAClC,CAAC,MAAM;MACLA,cAAc,GAAG,SAAS;IAC5B;EACF,CAAC,MAAM;IAEL,IAAIjG,KAAK,CAACkG,YAAY,KAAK,KAAK,EAAE;MAChCD,cAAc,GAAG,eAAe;IAClC,CAAC,MAAM;MACLA,cAAc,GAAG,QAAQ;IAC3B;EACF;EAEA,IAAME,UAAU,GAAGnG,KAAK,CAACmG,UAAU,KAAK,KAAK;EAC7C,IAAMC,SAAS,GAAGpG,KAAK,CAACoG,SAAS,KAAK,KAAK;EAE3C,IACEC,QAAQ,GAMNrG,KAAK,CANPqG,QAAQ;IACRC,OAAO,GAKLtG,KAAK,CALPsG,OAAO;IACPC,QAAO,GAILvG,KAAK,CAJPuG,OAAO;IACPC,SAAS,GAGPxG,KAAK,CAHPwG,SAAS;IACTC,UAAU,GAERzG,KAAK,CAFPyG,UAAU;IACVC,0BAA0B,GACxB1G,KAAK,CADP0G,0BAA0B;EAG5B,IAAMC,MAAM,GAAGhJ,KAAK,CAACiJ,OAAO,CAC1B;IAAA,OAAO;MACLN,OAAO,EAAPA,OAAO;MACPC,OAAO,EAAE,SAATA,OAAOA,CAAGvB,KAAiB,EAAK;QAC9BuB,QAAO,YAAPA,QAAO,CAAGvB,KAAK,CAAC;QAChB,IAAIqB,QAAQ,KAAK,KAAK,EAAE;UACtB,IAAIlG,QAAQ,CAACqB,OAAO,IAAI,IAAI,EAAE;YAC5BrB,QAAQ,CAACqB,OAAO,CAACqF,KAAK,CAAC,CAAC;UAC1B;QACF;MACF,CAAC;MACDL,SAAS,EAAEA,SAAS;MACpBC,UAAU,EAAEA,UAAU;MACtBK,UAAU,EAAEpH,iBAAQ,CAACC,EAAE,KAAK,KAAK,GAAG,CAAC+G,0BAA0B,GAAG;IACpE,CAAC;EAAA,CAAC,EACF,CACEL,QAAQ,EACRC,OAAO,EACPC,QAAO,EACPC,SAAS,EACTC,UAAU,EACVC,0BAA0B,CAE9B,CAAC;EAID,IAAIK,WAAW,GAAG/G,KAAK,CAAC+G,WAAW;EACnC,IAAIrH,iBAAQ,CAACsH,SAAS,EAAE;IACtBD,WAAW,GAAG,IAAI;EACpB;EAIA,IAAAE,gBAAA,GAA4C,IAAAC,yBAAe,EAACP,MAAM,CAAC;IAA5Dd,MAAM,GAAAoB,gBAAA,CAANpB,MAAM;IAAEH,OAAO,GAAAuB,gBAAA,CAAPvB,OAAO;IAAKyB,aAAa,OAAA/D,yBAAA,CAAA9E,OAAA,EAAA2I,gBAAA,EAAAnJ,UAAA;EAExC,IAAIsJ,mBAAmB;EACvB,IACExE,kBAAkB,IAAI,IAAI,IAC1BL,QAAQ,IAAI,IAAI,IAChBC,WAAW,IAAI,IAAI,IACnBC,YAAY,IAAI,IAAI,IACpBC,YAAY,IAAI,IAAI,IACpBC,YAAY,IAAI,IAAI,EACpB;IACAyE,mBAAmB,GAAG;MACpBC,IAAI,EAAE9E,QAAQ,WAARA,QAAQ,GAAIK,kBAAkB,oBAAlBA,kBAAkB,CAAEyE,IAAI;MAC1CC,OAAO,EAAE9E,WAAW,WAAXA,WAAW,GAAII,kBAAkB,oBAAlBA,kBAAkB,CAAE0E,OAAO;MACnDC,QAAQ,EAAE9E,YAAY,WAAZA,YAAY,GAAIG,kBAAkB,oBAAlBA,kBAAkB,CAAE2E,QAAQ;MACtDC,QAAQ,EAAE9E,YAAY,WAAZA,YAAY,GAAIE,kBAAkB,oBAAlBA,kBAAkB,CAAE4E,QAAQ;MACtDC,QAAQ,EAAE9E,YAAY,WAAZA,YAAY,GAAIC,kBAAkB,oBAAlBA,kBAAkB,CAAE6E;IAChD,CAAC;EACH;EAGA,IAAIC,MAAM,GAAG1H,KAAK,CAAC2H,KAAK;EACxB,IAAMC,cAAc,GAAG,IAAAC,qBAAY,EAAgB7H,KAAK,CAAC2H,KAAK,CAAC;EAC/D,IAAIC,cAAc,IAAI,IAAI,EAAE;IAC1B,IAAIE,SAAkC,GAAG,IAAI;IAC7C,IAAI,QAAOF,cAAc,oBAAdA,cAAc,CAAEG,UAAU,MAAK,QAAQ,EAAE;MAClDD,SAAS,GAAGA,SAAS,IAAK,CAAC,CAA0B;MACrDA,SAAS,CAACC,UAAU,GAEjBH,cAAc,CAACG,UAAU,CAACC,QAAQ,CAAC,CAAmC;IAC3E;IAEA,IAAIJ,cAAc,CAACK,aAAa,IAAI,IAAI,EAAE;MACxCH,SAAS,GAAGA,SAAS,IAAK,CAAC,CAA0B;MACrDA,SAAS,CAACI,iBAAiB,GACzBC,mCAAmC,CAACP,cAAc,CAACK,aAAa,CAAC;MACnEH,SAAS,CAACG,aAAa,GAAG5E,SAAS;IACrC;IAEA,IAAIyE,SAAS,IAAI,IAAI,EAAE;MAErBJ,MAAM,GAAG,CAACA,MAAM,EAAEI,SAAS,CAAC;IAC9B;EACF;EAEA,IAAIpI,iBAAQ,CAACC,EAAE,KAAK,KAAK,EAAE;IACzB,IAAMyI,gBAAgB,GACpBpI,KAAK,CAACsD,SAAS,KAAK,IAAI,GACpB9D,yBAAyB,GACzBF,0BAA0B;IAEhC,IAAM+I,wBAAwB,GAC5BrI,KAAK,CAACsD,SAAS,KAAK,IAAI,KACvBsE,cAAc,IAAI,IAAI,IACpBA,cAAc,CAACU,OAAO,IAAI,IAAI,IAC7BV,cAAc,CAACW,eAAe,IAAI,IAAI,IACtCX,cAAc,CAACY,UAAU,IAAI,IAAK,CAAC;IAEzCxC,SAAS,GACP,IAAApI,WAAA,CAAA6K,GAAA,EAACL,gBAAgB,EAAAxJ,MAAA,CAAA2F,MAAA;MAEfK,GAAG,EAAEA;IAAI,GACLzB,UAAU,EACVgE,aAAa;MACjBvE,kBAAkB,EAAEwE,mBAAoB;MACxCjB,UAAU,EAAEA,UAAW;MACvBF,cAAc,EAAEA,cAAe;MAC/Bc,WAAW,EAAEA,WAAY;MACzB2B,iBAAiB,EAAE1I,KAAK,CAAC0I,iBAAkB;MAC3CtC,SAAS,EAAEtD,QAAQ,KAAKO,SAAS,GAAG,CAACP,QAAQ,GAAGsD,SAAU;MAC1DnG,oBAAoB,EAAEA,oBAAqB;MAC3C0I,QAAQ,EAAE9F,EAAE,WAAFA,EAAE,GAAI7C,KAAK,CAAC2I,QAAS;MAC/B9C,MAAM,EAAEF,OAAQ;MAChBR,QAAQ,EAAEJ,SAAU;MACpB6D,mBAAmB,EAAE5I,KAAK,CAAC4I,mBAAoB;MAC/ClD,OAAO,EAAEF,QAAS;MAClBO,QAAQ,EAAED,SAAU;MACpBP,iBAAiB,EAAED,kBAAmB;MACtCuD,mCAAmC,EAAEhJ,4BAA6B;MAClEK,SAAS,EAAEA,SAAU;MACrB8C,cAAc,EAAEA,cAAe;MAC/B2E,KAAK,EAAEmB,mBAAU,CAACC,OAAO,CACvBV,wBAAwB,GAAGW,MAAM,CAACC,gBAAgB,GAAG,IAAI,EACzDvB,MACF,CAAE;MACFtH,IAAI,EAAEA;IAAK,EACZ,CACF;EACH,CAAC,MAAM,IAAIV,iBAAQ,CAACC,EAAE,KAAK,SAAS,EAAE;IAAA,IAAAuJ,oBAAA,EAAAC,kBAAA,EAAAC,WAAA;IACpC,IAAMC,cAAc,GAAGrJ,KAAK,CAACqJ,cAAc,IAAI,WAAW;IAC1D,IAAMC,wBAAwB,IAAAJ,oBAAA,GAC5BlJ,KAAK,oBAALA,KAAK,CAAG,iBAAiB,CAAC,YAAAkJ,oBAAA,GAAIlJ,KAAK,oBAALA,KAAK,CAAEuJ,uBAAuB;IAC9D,IAAMC,WAAW,IAAAL,kBAAA,GAAGnJ,KAAK,CAACwJ,WAAW,YAAAL,kBAAA,GAAI,EAAE;IAC3C,IAAIM,QAAQ,GAAGzJ,KAAK,CAACyJ,QAAQ;IAC7B,IAAMC,UAAU,GAAG/L,KAAK,CAACgM,QAAQ,CAACC,KAAK,CAACH,QAAQ,CAAC;IACjD,IAAAI,kBAAS,EACP,EAAE7J,KAAK,CAACQ,KAAK,IAAI,IAAI,IAAIkJ,UAAU,CAAC,EACpC,yCACF,CAAC;IACD,IAAIA,UAAU,GAAG,CAAC,EAAE;MAClBD,QAAQ,GAAG,IAAA7L,WAAA,CAAA6K,GAAA,EAACtL,KAAA,CAAAmB,OAAI;QAAAmL,QAAA,EAAEA;MAAQ,CAAO,CAAC;IACpC;IAEA,IAAMK,UAAU,GAAG;MACjB9G,cAAc,EAAdA,cAAc;MACdC,oBAAoB,EAClBA,oBAAoB,KAAKI,SAAS,GAC9BL,cAAc,GACdC,oBAAoB;MAC1BC,WAAW,EAAEA,WAAW,KAAKG,SAAS,GAAGL,cAAc,GAAGE;IAC5D,CAAC;IACD8C,SAAS,GASP,IAAApI,WAAA,CAAA6K,GAAA,EAACrJ,gBAAgB,EAAAR,MAAA,CAAA2F,MAAA;MAEfK,GAAG,EAAEA;IAAI,GACLzB,UAAU,EACV2G,UAAU,EACV3C,aAAa;MACjBvE,kBAAkB,EAAEwE,mBAAoB;MACxCmC,uBAAuB,EAAED,wBAAyB;MAClDnD,UAAU,EAAEA,UAAW;MACvBkD,cAAc,EAAEA,cAAe;MAC/BpD,cAAc,EAAEA,cAAe;MAC/Bc,WAAW,EAAEA,WAAY;MACzB0C,QAAQ,EAAEA,QAAS;MACnBM,mBAAmB,EAAE/J,KAAK,CAAC+J,mBAAoB;MAC/C3D,SAAS,EAAEtD,QAAQ,KAAKO,SAAS,GAAG,CAACP,QAAQ,GAAGsD,SAAU;MAC1DnG,oBAAoB,EAAEA,oBAAqB;MAC3C0I,QAAQ,EAAE9F,EAAE,WAAFA,EAAE,GAAI7C,KAAK,CAAC2I,QAAS;MAC/BqB,aAAa,GAAAZ,WAAA,GAAEpJ,KAAK,CAACiK,IAAI,YAAAb,WAAA,GAAIpJ,KAAK,CAACgK,aAAc;MACjDnE,MAAM,EAAEF,OAAQ;MAChBR,QAAQ,EAAEJ,SAAU;MACpBW,OAAO,EAAEF,QAAS;MAOlBO,QAAQ,EAAED,SAAU;MACpBP,iBAAiB,EAAED,kBAAmB;MACtCkE,WAAW,EAAEA,WAAY;MACzB7B,KAAK,EAAED,MAAO;MACdtH,IAAI,EAAEA,IAAK;MACX8J,iBAAiB,EAAElK,KAAK,CAACkK;IAAkB,EAC5C,CACF;EACH;EACA,OACE,IAAAtM,WAAA,CAAA6K,GAAA,EAACrL,aAAA,CAAAkB,OAAY,CAAC6L,QAAQ;IAAC3J,KAAK,EAAE,IAAK;IAAAiJ,QAAA,EAAEzD;EAAS,CAAwB,CAAC;AAE3E;AAEA,IAAMoE,2BAA2B,GAAG;EAClCC,KAAK,EAAE,SAAS;EAChBC,IAAI,EAAE,MAAM;EACZC,EAAE,EAAE,IAAI;EACRC,IAAI,EAAE,MAAM;EACZC,QAAQ,EAAE,UAAU;EACpBC,MAAM,EAAE,QAAQ;EAChBC,IAAI,EAAE;AACR,CAAC;AAED,IAAMC,0BAA0B,GAAG;EACjCC,IAAI,EAAE,SAAS;EACfzK,IAAI,EAAE,SAAS;EACf0K,OAAO,EAAE,aAAa;EACtBC,OAAO,EAAE,YAAY;EACrBC,GAAG,EAAE,WAAW;EAChBN,MAAM,EAAEhL,iBAAQ,CAACC,EAAE,KAAK,KAAK,GAAG,YAAY,GAAG,SAAS;EACxDsL,KAAK,EAAE,eAAe;EACtBC,GAAG,EAAE;AACP,CAAC;AAGD,IAAMC,uCAAuC,GAAG;EAC9C,eAAe,EAAE,uBAAuB;EACxC,eAAe,EAAE,yBAAyB;EAC1CC,IAAI,EAAE,gBAAgB;EACtB,UAAU,EAAE,eAAe;EAC3B,YAAY,EAAE,iBAAiB;EAC/B,WAAW,EAAE,gBAAgB;EAC7B,QAAQ,EAAE,QAAQ;EAClB,QAAQ,EAAE,QAAQ;EAClB,cAAc,EAAE,cAAc;EAC9B,aAAa,EAAE,aAAa;EAC5B,WAAW,EAAE,WAAW;EACxBC,OAAO,EAAE,wBAAwB;EACjC,kBAAkB,EAAE,UAAU;EAC9BJ,KAAK,EAAE,OAAO;EACd,kBAAkB,EAAE,aAAa;EACjC,kBAAkB,EAAE,aAAa;EACjCK,IAAI,EAAE,MAAM;EACZ,iBAAiB,EAAE,aAAa;EAChC,aAAa,EAAE,aAAa;EAC5B,YAAY,EAAE,YAAY;EAC1B,cAAc,EAAE,cAAc;EAC9BC,GAAG,EAAE,KAAK;EACV,eAAe,EAAE,SAAS;EAC1B,aAAa,EAAE,aAAa;EAC5BC,GAAG,EAAE,QAAQ;EACb,gBAAgB,EAAE,gBAAgB;EAClCR,GAAG,EAAE,KAAK;EACV,kBAAkB,EAAE,kBAAkB;EACtC,cAAc,EAAE,cAAc;EAC9BS,QAAQ,EAAE;AACZ,CAAC;AAGD,IAAMC,mCAAmC,GAAG;EAC1C,eAAe,EAAE,oBAAoB;EACrC,eAAe,EAAE,oBAAoB;EACrCN,IAAI,EAAE,WAAW;EACjB,UAAU,EAAE,cAAc;EAC1B,YAAY,EAAE,gBAAgB;EAC9B,WAAW,EAAE,eAAe;EAC5B,QAAQ,EAAE,wBAAwB;EAClC,cAAc,EAAE,2BAA2B;EAC3C,aAAa,EAAE,0BAA0B;EACzC,QAAQ,EAAE,sBAAsB;EAChC,eAAe,EAAE,qBAAqB;EACtC,oBAAoB,EAAE,sBAAsB;EAC5C,gBAAgB,EAAE,sBAAsB;EACxC,SAAS,EAAE,gBAAgB;EAC3B,WAAW,EAAE,kBAAkB;EAC/B,SAAS,EAAE,gBAAgB;EAC3B,kBAAkB,EAAE,UAAU;EAC9BC,OAAO,EAAE,aAAa;EACtBJ,KAAK,EAAE,cAAc;EACrBK,IAAI,EAAE,MAAM;EACZ,iBAAiB,EAAE,YAAY;EAC/B,aAAa,EAAE,YAAY;EAC3B,YAAY,EAAE,WAAW;EACzBK,QAAQ,EAAE,UAAU;EACpB,kBAAkB,EAAE,YAAY;EAChC,kBAAkB,EAAE,YAAY;EAChC,cAAc,EAAE,aAAa;EAC7BJ,GAAG,EAAE,MAAM;EACX,eAAe,EAAE,aAAa;EAC9BK,YAAY,EAAE,kBAAkB;EAChC,oBAAoB,EAAE,UAAU;EAChC,aAAa,EAAE,YAAY;EAC3B,gBAAgB,EAAE,mBAAmB;EACrCZ,GAAG,EAAE,iBAAiB;EACtBE,GAAG,EAAE,KAAK;EACVO,QAAQ,EAAE;AACZ,CAAC;AAED,IAAMI,kBAIL,GAAGlO,KAAK,CAACmO,UAAU,CAAC,SAASC,SAASA,CAAAC,KAAA,EAgBrClH,YAA+C,EAC/C;EAAA,IAAAmH,qBAAA;EAAA,IAAAC,qBAAA,GAAAF,KAAA,CAfEG,gBAAgB;IAAhBA,gBAAgB,GAAAD,qBAAA,cAAG,IAAI,GAAAA,qBAAA;IAAAE,qBAAA,GAAAJ,KAAA,CACvBtF,0BAA0B;IAA1BA,0BAA0B,GAAA0F,qBAAA,cAAG,IAAI,GAAAA,qBAAA;IAAAC,qBAAA,GAAAL,KAAA,CACjCM,qBAAqB;IAArBA,qBAAqB,GAAAD,qBAAA,cAAG,aAAa,GAAAA,qBAAA;IACrCE,YAAY,GAAAP,KAAA,CAAZO,YAAY;IACZC,eAAe,GAAAR,KAAA,CAAfQ,eAAe;IACfC,QAAQ,GAAAT,KAAA,CAARS,QAAQ;IACRpG,QAAQ,GAAA2F,KAAA,CAAR3F,QAAQ;IACRqG,YAAY,GAAAV,KAAA,CAAZU,YAAY;IACZC,aAAa,GAAAX,KAAA,CAAbW,aAAa;IACbC,SAAS,GAAAZ,KAAA,CAATY,SAAS;IACTC,oBAAoB,GAAAb,KAAA,CAApBa,oBAAoB;IACpBC,YAAY,GAAAd,KAAA,CAAZc,YAAY;IACTC,SAAS,OAAA3J,yBAAA,CAAA9E,OAAA,EAAA0N,KAAA,EAAAjO,UAAA;EAId,OACE,IAAAH,WAAA,CAAA6K,GAAA,EAACrG,iBAAiB,EAAAxD,MAAA,CAAA2F,MAAA;IAChB4H,gBAAgB,EAAEA,gBAAiB;IACnCzF,0BAA0B,EAAEA,0BAA2B;IACvD4F,qBAAqB,EAAEA,qBAAsB;IAC7CjG,QAAQ,EAAEoG,QAAQ,KAAKpJ,SAAS,GAAG,CAACoJ,QAAQ,GAAGpG,QAAS;IACxDsG,aAAa,EACXD,YAAY,GAAGtC,2BAA2B,CAACsC,YAAY,CAAC,GAAGC,aAC5D;IACDG,YAAY,EACVF,SAAS,GAAGhC,0BAA0B,CAACgC,SAAS,CAAC,GAAGE,YACrD;IACDD,oBAAoB,EAClBD,SAAS,IAAI,IAAI,GAAGC,oBAAoB,GAAGD,SAAS,KAAK,MAC1D;IACDL,YAAY,EACV7M,iBAAQ,CAACC,EAAE,KAAK,SAAS,IAAAsM,qBAAA,GAGrBd,uCAAuC,CAACoB,YAAY,CAAC,YAAAN,qBAAA,GACrDM,YAAY,GACZlJ,SACL;IACDmJ,eAAe,EACbA,eAAe,IAAI,IAAI,GACnBA,eAAe,GACf9M,iBAAQ,CAACC,EAAE,KAAK,KAAK,IACnB4M,YAAY,IACZA,YAAY,IAAIb,mCAAmC,GAGnDA,mCAAmC,CAACa,YAAY,CAAC,GACjDC;EACP,GACGO,SAAS;IACbjI,YAAY,EAAEA;EAAa,EAC5B,CAAC;AAEN,CAAC,CAAC;AAEF+G,kBAAkB,CAACmB,WAAW,GAAG,WAAW;AAG5CnB,kBAAkB,CAACoB,KAAK,GAAG;EACzBhJ,qBAAqB,EAAEH,uBAAc,CAACG,qBAAqB;EAE3DiJ,qBAAqB,EAAEpJ,uBAAc,CAACoJ,qBAAqB;EAC3DC,cAAc,EAAErJ,uBAAc,CAACqJ,cAAc;EAC7CC,aAAa,EAAEtJ,uBAAc,CAACsJ;AAChC,CAAC;AAWD,IAAMpE,MAAM,GAAGF,mBAAU,CAACuE,MAAM,CAAC;EAC/BpE,gBAAgB,EAAE;IAIhBT,UAAU,EAAE;EACd;AACF,CAAC,CAAC;AAEF,IAAML,mCAAmC,GAAG;EAC1CmF,IAAI,EAAE,MAAM;EACZC,GAAG,EAAE,KAAK;EACVC,MAAM,EAAE,QAAQ;EAChBC,MAAM,EAAE;AACV,CAAC;AAGDC,MAAM,CAACC,OAAO,GAAK9B,kBAAwC", "ignoreList": []}