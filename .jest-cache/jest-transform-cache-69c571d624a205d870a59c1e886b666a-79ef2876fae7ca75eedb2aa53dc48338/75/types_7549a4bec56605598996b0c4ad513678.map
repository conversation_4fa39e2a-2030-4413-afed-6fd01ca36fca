{"version": 3, "names": [], "sources": ["/Users/<USER>/projects/react-native/payment-module/src/components/source-account/types.ts"], "sourcesContent": ["import {ViewStyle} from 'react-native';\nimport {SafeAny} from '../../commons/Constants';\nimport {SourceAccountModel} from '../../domain/entities/source-account-list/SourceAccountListModel';\n\nexport type SoureAccountProps = {\n  style?: ViewStyle;\n  title?: string;\n  account?: SourceAccountModel;\n  onSelectAccount?: () => SafeAny;\n  errorTitle?: string;\n};\n"], "mappings": "", "ignoreList": []}