74f756da403100aefb060f67cfe89992
"use strict";

/* istanbul ignore next */
function cov_19eablgxi3() {
  var path = "/Users/<USER>/projects/react-native/payment-module/src/components/source-account/types.ts";
  var hash = "ff5f89b485ad1bd34bd1a123efc6b9a955cca850";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/projects/react-native/payment-module/src/components/source-account/types.ts",
    statementMap: {
      "0": {
        start: {
          line: 3,
          column: 0
        },
        end: {
          line: 5,
          column: 3
        }
      }
    },
    fnMap: {},
    branchMap: {},
    s: {
      "0": 0
    },
    f: {},
    b: {},
    inputSourceMap: {
      version: 3,
      file: undefined,
      names: [],
      sourceRoot: undefined,
      sources: ["/Users/<USER>/projects/react-native/payment-module/src/components/source-account/types.ts"],
      sourcesContent: ["import {ViewStyle} from 'react-native';\nimport {SafeAny} from '../../commons/Constants';\nimport {SourceAccountModel} from '../../domain/entities/source-account-list/SourceAccountListModel';\n\nexport type SoureAccountProps = {\n  style?: ViewStyle;\n  title?: string;\n  account?: SourceAccountModel;\n  onSelectAccount?: () => SafeAny;\n  errorTitle?: string;\n};\n"],
      mappings: "",
      ignoreList: []
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "ff5f89b485ad1bd34bd1a123efc6b9a955cca850"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_19eablgxi3 = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_19eablgxi3();
cov_19eablgxi3().s[0]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6W10sInNvdXJjZXMiOlsiL1VzZXJzL3NvbnNhbXNldC9wcm9qZWN0cy9yZWFjdC1uYXRpdmUvcGF5bWVudC1tb2R1bGUvc3JjL2NvbXBvbmVudHMvc291cmNlLWFjY291bnQvdHlwZXMudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHtWaWV3U3R5bGV9IGZyb20gJ3JlYWN0LW5hdGl2ZSc7XG5pbXBvcnQge1NhZmVBbnl9IGZyb20gJy4uLy4uL2NvbW1vbnMvQ29uc3RhbnRzJztcbmltcG9ydCB7U291cmNlQWNjb3VudE1vZGVsfSBmcm9tICcuLi8uLi9kb21haW4vZW50aXRpZXMvc291cmNlLWFjY291bnQtbGlzdC9Tb3VyY2VBY2NvdW50TGlzdE1vZGVsJztcblxuZXhwb3J0IHR5cGUgU291cmVBY2NvdW50UHJvcHMgPSB7XG4gIHN0eWxlPzogVmlld1N0eWxlO1xuICB0aXRsZT86IHN0cmluZztcbiAgYWNjb3VudD86IFNvdXJjZUFjY291bnRNb2RlbDtcbiAgb25TZWxlY3RBY2NvdW50PzogKCkgPT4gU2FmZUFueTtcbiAgZXJyb3JUaXRsZT86IHN0cmluZztcbn07XG4iXSwibWFwcGluZ3MiOiIiLCJpZ25vcmVMaXN0IjpbXX0=