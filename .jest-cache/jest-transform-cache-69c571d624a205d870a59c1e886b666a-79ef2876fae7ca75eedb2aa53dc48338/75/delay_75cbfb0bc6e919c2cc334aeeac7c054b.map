{"version": 3, "names": ["Object", "defineProperty", "exports", "value", "<PERSON><PERSON><PERSON><PERSON>", "_util", "require", "delayMs", "_nextAnimation", "reduceMotion", "defineAnimation", "nextAnimation", "delay", "animation", "now", "startTime", "started", "previousAnimation", "current", "onStart", "finished", "onFrame", "undefined", "callback", "isHigherOrder", "getReduceMotionForAnimation"], "sources": ["../../../src/animation/delay.ts"], "sourcesContent": [null], "mappings": "AAAA,YAAY;;AAAAA,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,KAAA;AAAA;AAAAD,OAAA,CAAAE,SAAA;AACZ,IAAAC,KAAA,GAAAC,OAAA;AA8BO,IAAMF,SAAS,GAAAF,OAAA,CAAAE,SAAA,GAAG,SAAZA,SAASA,CACpBG,OAAe,EACfC,cAA6B,EAC7BC,YAA2B,EACA;EAC3B,SAAS;;EACT,OAAO,IAAAC,qBAAe,EACpBF,cAAc,EACd,YAAsB;IACpB,SAAS;;IACT,IAAMG,aAAa,GACjB,OAAOH,cAAc,KAAK,UAAU,GAChCA,cAAc,CAAC,CAAC,GAChBA,cAAc;IAEpB,SAASI,KAAKA,CAACC,SAAyB,EAAEC,GAAc,EAAW;MACjE,IAAQC,SAAS,GAAiCF,SAAS,CAAnDE,SAAS;QAAEC,OAAO,GAAwBH,SAAS,CAAxCG,OAAO;QAAEC,iBAAA,GAAsBJ,SAAS,CAA/BI,iBAAA;MAC5B,IAAMC,OAAwB,GAAGL,SAAS,CAACK,OAAO;MAClD,IAAIJ,GAAG,GAAGC,SAAS,IAAIR,OAAO,IAAIM,SAAS,CAACJ,YAAY,EAAE;QACxD,IAAI,CAACO,OAAO,EAAE;UACZL,aAAa,CAACQ,OAAO,CACnBR,aAAa,EACbO,OAAO,EACPJ,GAAG,EACHG,iBACF,CAAC;UACDJ,SAAS,CAACI,iBAAiB,GAAG,IAAI;UAClCJ,SAAS,CAACG,OAAO,GAAG,IAAI;QAC1B;QACA,IAAMI,QAAQ,GAAGT,aAAa,CAACU,OAAO,CAACV,aAAa,EAAEG,GAAG,CAAC;QAC1DD,SAAS,CAACK,OAAO,GAAGP,aAAa,CAACO,OAAQ;QAC1C,OAAOE,QAAQ;MACjB,CAAC,MAAM,IAAIH,iBAAiB,EAAE;QAC5B,IAAMG,SAAQ,GACZH,iBAAiB,CAACG,QAAQ,IAC1BH,iBAAiB,CAACI,OAAO,CAACJ,iBAAiB,EAAEH,GAAG,CAAC;QACnDD,SAAS,CAACK,OAAO,GAAGD,iBAAiB,CAACC,OAAO;QAC7C,IAAIE,SAAQ,EAAE;UACZP,SAAS,CAACI,iBAAiB,GAAG,IAAI;QACpC;MACF;MACA,OAAO,KAAK;IACd;IAEA,SAASE,OAAOA,CACdN,SAAyB,EACzBV,KAAsB,EACtBW,GAAc,EACdG,iBAAwC,EAClC;MACNJ,SAAS,CAACE,SAAS,GAAGD,GAAG;MACzBD,SAAS,CAACG,OAAO,GAAG,KAAK;MACzBH,SAAS,CAACK,OAAO,GAAGf,KAAK;MACzB,IAAIc,iBAAiB,KAAKJ,SAAS,EAAE;QACnCA,SAAS,CAACI,iBAAiB,GAAGA,iBAAiB,CAACA,iBAAiB;MACnE,CAAC,MAAM;QACLJ,SAAS,CAACI,iBAAiB,GAAGA,iBAAiB;MACjD;MAIA,IAAIN,aAAa,CAACF,YAAY,KAAKa,SAAS,EAAE;QAC5CX,aAAa,CAACF,YAAY,GAAGI,SAAS,CAACJ,YAAY;MACrD;IACF;IAEA,IAAMc,QAAQ,GAAI,SAAZA,QAAQA,CAAIH,QAAkB,EAAW;MAC7C,IAAIT,aAAa,CAACY,QAAQ,EAAE;QAC1BZ,aAAa,CAACY,QAAQ,CAACH,QAAQ,CAAC;MAClC;IACF,CAAC;IAED,OAAO;MACLI,aAAa,EAAE,IAAI;MACnBH,OAAO,EAAET,KAAK;MACdO,OAAO,EAAPA,OAAO;MACPD,OAAO,EAAEP,aAAa,CAACO,OAAQ;MAC/BK,QAAQ,EAARA,QAAQ;MACRN,iBAAiB,EAAE,IAAI;MACvBF,SAAS,EAAE,CAAC;MACZC,OAAO,EAAE,KAAK;MACdP,YAAY,EAAE,IAAAgB,iCAA2B,EAAChB,YAAY;IACxD,CAAC;EACH,CACF,CAAC;AACH,CAAkB", "ignoreList": []}