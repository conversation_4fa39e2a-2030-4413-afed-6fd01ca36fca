{"version": 3, "names": ["_interopRequireDefault", "require", "Object", "defineProperty", "exports", "value", "NativeEventsManager", "_classCallCheck2", "_createClass2", "_classPrivateFieldLooseBase2", "_classPrivateFieldLooseKey2", "_utils", "_WorkletEventHandler", "_findNodeHandle4", "_managedComponent", "default", "_componentOptions", "_eventViewTag", "component", "options", "writable", "getEventViewTag", "key", "attachEvents", "_this", "executeForEachEventHandler", "props", "handler", "registerForEvents", "detachEvents", "_this2", "_key", "unregisterFromEvents", "updateEvents", "prevProps", "_this3", "computedEventTag", "prev<PERSON><PERSON><PERSON>", "newProp", "isWorkletEventHandler", "workletEventHandler", "_classPrivateFieldLoo", "_findNodeHandle3", "componentUpdate", "arguments", "length", "undefined", "componentAnimatedRef", "_componentRef", "getScrollableNode", "_findNodeHandle", "scrollableNode", "findNodeHandle", "setNativeProps", "_findNodeHandle2", "getComponentViewTag", "__nativeTag", "_nativeTag", "_ref", "_componentAnimatedRef", "prop", "has", "WorkletEventHandler", "callback"], "sources": ["../../../src/createAnimatedComponent/NativeEventsManager.ts"], "sourcesContent": [null], "mappings": "AAAA,YAAY;;AAAA,IAAAA,sBAAA,GAAAC,OAAA;AAAAC,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,KAAA;AAAA;AAAAD,OAAA,CAAAE,mBAAA;AAAA,IAAAC,gBAAA,GAAAP,sBAAA,CAAAC,OAAA;AAAA,IAAAO,aAAA,GAAAR,sBAAA,CAAAC,OAAA;AAAA,IAAAQ,4BAAA,GAAAT,sBAAA,CAAAC,OAAA;AAAA,IAAAS,2BAAA,GAAAV,sBAAA,CAAAC,OAAA;AAQZ,IAAAU,MAAA,GAAAV,OAAA;AACA,IAAAW,oBAAA,GAAAX,OAAA;AACA,IAAAY,gBAAA,GAAAZ,OAAA;AAAoE,IAAAa,iBAAA,OAAAJ,2BAAA,CAAAK,OAAA;AAAA,IAAAC,iBAAA,OAAAN,2BAAA,CAAAK,OAAA;AAAA,IAAAE,aAAA,OAAAP,2BAAA,CAAAK,OAAA;AAAA,IAEvDT,mBAAmB,GAAAF,OAAA,CAAAE,mBAAA;EAK9B,SAAAA,oBAAYY,SAAmC,EAAEC,OAA0B,EAAE;IAAA,IAAAZ,gBAAA,CAAAQ,OAAA,QAAAT,mBAAA;IAAAJ,MAAA,CAAAC,cAAA,OAAAW,iBAAA;MAAAM,QAAA;MAAAf,KAAA;IAAA;IAAAH,MAAA,CAAAC,cAAA,OAAAa,iBAAA;MAAAI,QAAA;MAAAf,KAAA;IAAA;IAAAH,MAAA,CAAAC,cAAA,OAAAc,aAAA;MAAAG,QAAA;MAAAf,KAAA,EAF7D,CAAC;IAAC;IAGhB,IAAAI,4BAAA,CAAAM,OAAA,MAAI,EAAAD,iBAAA,EAAAA,iBAAA,IAAqBI,SAAS;IAClC,IAAAT,4BAAA,CAAAM,OAAA,MAAI,EAAAC,iBAAA,EAAAA,iBAAA,IAAqBG,OAAO;IAChC,IAAAV,4BAAA,CAAAM,OAAA,MAAI,EAAAE,aAAA,EAAAA,aAAA,IAAiB,IAAI,CAACI,eAAe,CAAC,CAAC;EAC7C;EAAA,WAAAb,aAAA,CAAAO,OAAA,EAAAT,mBAAA;IAAAgB,GAAA;IAAAjB,KAAA,EAEO,SAAAkB,YAAYA,CAAA,EAAG;MAAA,IAAAC,KAAA;MACpBC,0BAA0B,CAAC,IAAAhB,4BAAA,CAAAM,OAAA,MAAI,EAAAD,iBAAA,EAAAA,iBAAA,EAAmBY,KAAK,EAAE,UAACJ,GAAG,EAAEK,OAAO,EAAK;QACzEA,OAAO,CAACC,iBAAiB,KAAAnB,4BAAA,CAAAM,OAAA,EAACS,KAAI,EAAAP,aAAA,EAAAA,aAAA,GAAgBK,GAAG,CAAC;MACpD,CAAC,CAAC;IACJ;EAAA;IAAAA,GAAA;IAAAjB,KAAA,EAEO,SAAAwB,YAAYA,CAAA,EAAG;MAAA,IAAAC,MAAA;MACpBL,0BAA0B,CACxB,IAAAhB,4BAAA,CAAAM,OAAA,MAAI,EAAAD,iBAAA,EAAAA,iBAAA,EAAmBY,KAAK,EAC5B,UAACK,IAAI,EAAEJ,OAAO,EAAK;QACjBA,OAAO,CAACK,oBAAoB,KAAAvB,4BAAA,CAAAM,OAAA,EAACe,MAAI,EAAAb,aAAA,EAAAA,aAAA,CAAc,CAAC;MAClD,CACF,CAAC;IACH;EAAA;IAAAK,GAAA;IAAAjB,KAAA,EAEO,SAAA4B,YAAYA,CACjBC,SAAwD,EACxD;MAAA,IAAAC,MAAA;MACA,IAAMC,gBAAgB,GAAG,IAAI,CAACf,eAAe,CAAC,IAAI,CAAC;MAEnD,IAAI,IAAAZ,4BAAA,CAAAM,OAAA,MAAI,EAAAE,aAAA,EAAAA,aAAA,MAAmBmB,gBAAgB,EAAE;QAE3CX,0BAA0B,CAACS,SAAS,EAAE,UAACH,IAAI,EAAEJ,OAAO,EAAK;UACvDA,OAAO,CAACK,oBAAoB,KAAAvB,4BAAA,CAAAM,OAAA,EAACoB,MAAI,EAAAlB,aAAA,EAAAA,aAAA,CAAc,CAAC;QAClD,CAAC,CAAC;QAGF,IAAAR,4BAAA,CAAAM,OAAA,MAAI,EAAAE,aAAA,EAAAA,aAAA,IAAiBmB,gBAAgB;QAErC,IAAI,CAACb,YAAY,CAAC,CAAC;QACnB;MACF;MAEAE,0BAA0B,CAACS,SAAS,EAAE,UAACZ,GAAG,EAAEe,WAAW,EAAK;QAC1D,IAAMC,OAAO,GAAG,IAAA7B,4BAAA,CAAAM,OAAA,EAAAoB,MAAI,EAAArB,iBAAA,EAAAA,iBAAA,EAAmBY,KAAK,CAACJ,GAAG,CAAC;QACjD,IAAI,CAACgB,OAAO,EAAE;UAEZD,WAAW,CAACL,oBAAoB,KAAAvB,4BAAA,CAAAM,OAAA,EAACoB,MAAI,EAAAlB,aAAA,EAAAA,aAAA,CAAc,CAAC;QACtD,CAAC,MAAM,IACLsB,qBAAqB,CAACD,OAAO,CAAC,IAC9BA,OAAO,CAACE,mBAAmB,KAAKH,WAAW,EAC3C;UAEAA,WAAW,CAACL,oBAAoB,KAAAvB,4BAAA,CAAAM,OAAA,EAACoB,MAAI,EAAAlB,aAAA,EAAAA,aAAA,CAAc,CAAC;UACpDqB,OAAO,CAACE,mBAAmB,CAACZ,iBAAiB,KAAAnB,4BAAA,CAAAM,OAAA,EAACoB,MAAI,EAAAlB,aAAA,EAAAA,aAAA,CAAc,CAAC;QACnE;MACF,CAAC,CAAC;MAEFQ,0BAA0B,CAAC,IAAAhB,4BAAA,CAAAM,OAAA,MAAI,EAAAD,iBAAA,EAAAA,iBAAA,EAAmBY,KAAK,EAAE,UAACJ,GAAG,EAAEK,OAAO,EAAK;QACzE,IAAI,CAACO,SAAS,CAACZ,GAAG,CAAC,EAAE;UAEnBK,OAAO,CAACC,iBAAiB,KAAAnB,4BAAA,CAAAM,OAAA,EAACoB,MAAI,EAAAlB,aAAA,EAAAA,aAAA,CAAc,CAAC;QAC/C;MACF,CAAC,CAAC;IACJ;EAAA;IAAAK,GAAA;IAAAjB,KAAA,EAEQ,SAAAgB,eAAeA,CAAA,EAAmC;MAAA,IAAAoB,qBAAA,EAAAC,gBAAA;MAAA,IAAlCC,eAAwB,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,KAAK;MAEtD,IAAMG,oBAAoB,GAAG,IAAAtC,4BAAA,CAAAM,OAAA,MAAI,EAAAD,iBAAA,EAAAA,iBAAA,EAC9BkC,aAKF;MACD,IAAID,oBAAoB,CAACE,iBAAiB,EAAE;QAAA,IAAAC,eAAA;QAO1C,IAAMC,cAAc,GAAGJ,oBAAoB,CAACE,iBAAiB,CAAC,CAAC;QAC/D,IAAI,OAAOE,cAAc,KAAK,QAAQ,EAAE;UACtC,OAAOA,cAAc;QACvB;QACA,QAAAD,eAAA,GAAO,IAAAE,+BAAc,EAACD,cAAc,CAAC,YAAAD,eAAA,GAAI,CAAC,CAAC;MAC7C;MACA,KAAAT,qBAAA,OAAAhC,4BAAA,CAAAM,OAAA,EAAI,IAAI,EAAAC,iBAAA,EAAAA,iBAAA,cAAJyB,qBAAA,CAAwBY,cAAc,EAAE;QAAA,IAAAC,gBAAA;QAG1C,QAAAA,gBAAA,GAAO,IAAAF,+BAAc,MAAA3C,4BAAA,CAAAM,OAAA,EAAC,IAAI,EAAAD,iBAAA,EAAAA,iBAAA,CAAkB,CAAC,YAAAwC,gBAAA,GAAI,CAAC,CAAC;MACrD;MACA,IAAI,CAACX,eAAe,EAAE;QAEpB,OAAO,IAAAlC,4BAAA,CAAAM,OAAA,MAAI,EAAAD,iBAAA,EAAAA,iBAAA,EAAmByC,mBAAmB,CAAC,CAAC;MACrD;MACA,IAAIR,oBAAoB,CAACS,WAAW,IAAIT,oBAAoB,CAACU,UAAU,EAAE;QAAA,IAAAC,IAAA,EAAAC,qBAAA;QAMvE,QAAAD,IAAA,IAAAC,qBAAA,GACEZ,oBAAoB,CAACS,WAAW,YAAAG,qBAAA,GAChCZ,oBAAoB,CAACU,UAAU,YAAAC,IAAA,GAC/B,CAAC,CAAC;MAEN;MAKA,QAAAhB,gBAAA,GAAO,IAAAU,+BAAc,EAACL,oBAAoB,CAAC,YAAAL,gBAAA,GAAI,CAAC,CAAC;IACnD;EAAA;AAAA;AAGF,SAASH,qBAAqBA,CAC5BqB,IAAa,EACsB;EACnC,OACE,IAAAC,UAAG,EAAC,qBAAqB,EAAED,IAAI,CAAC,IAChCA,IAAI,CAACpB,mBAAmB,YAAYsB,wCAAmB;AAE3D;AAEA,SAASrC,0BAA0BA,CACjCC,KAAoD,EACpDqC,QAGS,EACT;EACA,KAAK,IAAMzC,GAAG,IAAII,KAAK,EAAE;IACvB,IAAMkC,IAAI,GAAGlC,KAAK,CAACJ,GAAG,CAAC;IACvB,IAAIiB,qBAAqB,CAACqB,IAAI,CAAC,EAAE;MAC/BG,QAAQ,CAACzC,GAAG,EAAEsC,IAAI,CAACpB,mBAAmB,CAAC;IACzC;EACF;AACF", "ignoreList": []}