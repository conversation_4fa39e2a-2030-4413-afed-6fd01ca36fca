949354215531a4e8aa15105df717834f
"use strict";

/* istanbul ignore next */
function cov_2qwd5kknd4() {
  var path = "/Users/<USER>/projects/react-native/payment-module/src/domain/usecases/bill-pay/BillValidateUseCase.ts";
  var hash = "417d2b4e8607b1231090cf942967a88f16ce47cb";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/projects/react-native/payment-module/src/domain/usecases/bill-pay/BillValidateUseCase.ts",
    statementMap: {
      "0": {
        start: {
          line: 3,
          column: 29
        },
        end: {
          line: 3,
          column: 84
        }
      },
      "1": {
        start: {
          line: 4,
          column: 25
        },
        end: {
          line: 4,
          column: 99
        }
      },
      "2": {
        start: {
          line: 5,
          column: 23
        },
        end: {
          line: 5,
          column: 95
        }
      },
      "3": {
        start: {
          line: 6,
          column: 20
        },
        end: {
          line: 6,
          column: 89
        }
      },
      "4": {
        start: {
          line: 7,
          column: 0
        },
        end: {
          line: 9,
          column: 3
        }
      },
      "5": {
        start: {
          line: 10,
          column: 0
        },
        end: {
          line: 10,
          column: 37
        }
      },
      "6": {
        start: {
          line: 11,
          column: 26
        },
        end: {
          line: 11,
          column: 69
        }
      },
      "7": {
        start: {
          line: 12,
          column: 26
        },
        end: {
          line: 32,
          column: 3
        }
      },
      "8": {
        start: {
          line: 14,
          column: 4
        },
        end: {
          line: 14,
          column: 61
        }
      },
      "9": {
        start: {
          line: 15,
          column: 4
        },
        end: {
          line: 15,
          column: 33
        }
      },
      "10": {
        start: {
          line: 17,
          column: 2
        },
        end: {
          line: 31,
          column: 6
        }
      },
      "11": {
        start: {
          line: 20,
          column: 21
        },
        end: {
          line: 25,
          column: 8
        }
      },
      "12": {
        start: {
          line: 21,
          column: 20
        },
        end: {
          line: 21,
          column: 24
        }
      },
      "13": {
        start: {
          line: 22,
          column: 8
        },
        end: {
          line: 24,
          column: 11
        }
      },
      "14": {
        start: {
          line: 23,
          column: 10
        },
        end: {
          line: 23,
          column: 56
        }
      },
      "15": {
        start: {
          line: 27,
          column: 8
        },
        end: {
          line: 27,
          column: 47
        }
      },
      "16": {
        start: {
          line: 29,
          column: 6
        },
        end: {
          line: 29,
          column: 21
        }
      },
      "17": {
        start: {
          line: 33,
          column: 0
        },
        end: {
          line: 33,
          column: 50
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 12,
            column: 26
          },
          end: {
            line: 12,
            column: 27
          }
        },
        loc: {
          start: {
            line: 12,
            column: 38
          },
          end: {
            line: 32,
            column: 1
          }
        },
        line: 12
      },
      "1": {
        name: "BillValidateUseCase",
        decl: {
          start: {
            line: 13,
            column: 11
          },
          end: {
            line: 13,
            column: 30
          }
        },
        loc: {
          start: {
            line: 13,
            column: 43
          },
          end: {
            line: 16,
            column: 3
          }
        },
        line: 13
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 19,
            column: 11
          },
          end: {
            line: 19,
            column: 12
          }
        },
        loc: {
          start: {
            line: 19,
            column: 23
          },
          end: {
            line: 30,
            column: 5
          }
        },
        line: 19
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 20,
            column: 53
          },
          end: {
            line: 20,
            column: 54
          }
        },
        loc: {
          start: {
            line: 20,
            column: 73
          },
          end: {
            line: 25,
            column: 7
          }
        },
        line: 20
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 22,
            column: 60
          },
          end: {
            line: 22,
            column: 61
          }
        },
        loc: {
          start: {
            line: 22,
            column: 72
          },
          end: {
            line: 24,
            column: 9
          }
        },
        line: 22
      },
      "5": {
        name: "execute",
        decl: {
          start: {
            line: 26,
            column: 15
          },
          end: {
            line: 26,
            column: 22
          }
        },
        loc: {
          start: {
            line: 26,
            column: 27
          },
          end: {
            line: 28,
            column: 7
          }
        },
        line: 26
      }
    },
    branchMap: {},
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0
    },
    b: {},
    inputSourceMap: {
      version: 3,
      file: undefined,
      names: ["ExcecutionHandler_1", "require", "BillValidateUseCase", "repository", "_classCallCheck2", "default", "_createClass2", "key", "value", "_execute", "_asyncToGenerator2", "request", "_this", "ExecutionHandler", "execute", "billValidate", "_x", "apply", "arguments", "exports"],
      sourceRoot: undefined,
      sources: ["/Users/<USER>/projects/react-native/payment-module/src/domain/usecases/bill-pay/BillValidateUseCase.ts"],
      sourcesContent: ["import {IBillPayRepository} from '../../repositories/IBillPayRepository';\nimport {BillValidateModel} from '../../entities/bill-validate/BillValidateModel';\nimport {ResultState} from '../../../core/ResultState';\nimport {ExecutionHandler} from '../../../utils/ExcecutionHandler';\nimport {BillValidateRequest} from '../../../data/models/bill-validate/BillValidateRequest';\nexport class BillValidateUseCase {\n  private repository: IBillPayRepository;\n\n  constructor(repository: IBillPayRepository) {\n    this.repository = repository;\n  }\n\n  public async execute(request: BillValidateRequest): Promise<ResultState<BillValidateModel>> {\n    // call this.repository.billValidate(...)\n    return ExecutionHandler.execute(() => this.repository.billValidate(request));\n  }\n}\n"],
      mappings: ";;;;;;;;;;AAGA,IAAAA,mBAAA,GAAAC,OAAA;AAAkE,IAErDC,mBAAmB;EAG9B,SAAAA,oBAAYC,UAA8B;IAAA,IAAAC,gBAAA,CAAAC,OAAA,QAAAH,mBAAA;IACxC,IAAI,CAACC,UAAU,GAAGA,UAAU;EAC9B;EAAC,WAAAG,aAAA,CAAAD,OAAA,EAAAH,mBAAA;IAAAK,GAAA;IAAAC,KAAA;MAAA,IAAAC,QAAA,OAAAC,kBAAA,CAAAL,OAAA,EAEM,WAAcM,OAA4B;QAAA,IAAAC,KAAA;QAE/C,OAAOZ,mBAAA,CAAAa,gBAAgB,CAACC,OAAO,CAAC;UAAA,OAAMF,KAAI,CAACT,UAAU,CAACY,YAAY,CAACJ,OAAO,CAAC;QAAA,EAAC;MAC9E,CAAC;MAAA,SAHYG,OAAOA,CAAAE,EAAA;QAAA,OAAAP,QAAA,CAAAQ,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAPJ,OAAO;IAAA;EAAA;AAAA;AAPtBK,OAAA,CAAAjB,mBAAA,GAAAA,mBAAA",
      ignoreList: []
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "417d2b4e8607b1231090cf942967a88f16ce47cb"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_2qwd5kknd4 = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_2qwd5kknd4();
var _interopRequireDefault =
/* istanbul ignore next */
(cov_2qwd5kknd4().s[0]++, require("@babel/runtime/helpers/interopRequireDefault"));
var _asyncToGenerator2 =
/* istanbul ignore next */
(cov_2qwd5kknd4().s[1]++, _interopRequireDefault(require("@babel/runtime/helpers/asyncToGenerator")));
var _classCallCheck2 =
/* istanbul ignore next */
(cov_2qwd5kknd4().s[2]++, _interopRequireDefault(require("@babel/runtime/helpers/classCallCheck")));
var _createClass2 =
/* istanbul ignore next */
(cov_2qwd5kknd4().s[3]++, _interopRequireDefault(require("@babel/runtime/helpers/createClass")));
/* istanbul ignore next */
cov_2qwd5kknd4().s[4]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_2qwd5kknd4().s[5]++;
exports.BillValidateUseCase = void 0;
var ExcecutionHandler_1 =
/* istanbul ignore next */
(cov_2qwd5kknd4().s[6]++, require("../../../utils/ExcecutionHandler"));
var BillValidateUseCase =
/* istanbul ignore next */
(cov_2qwd5kknd4().s[7]++, function () {
  /* istanbul ignore next */
  cov_2qwd5kknd4().f[0]++;
  function BillValidateUseCase(repository) {
    /* istanbul ignore next */
    cov_2qwd5kknd4().f[1]++;
    cov_2qwd5kknd4().s[8]++;
    (0, _classCallCheck2.default)(this, BillValidateUseCase);
    /* istanbul ignore next */
    cov_2qwd5kknd4().s[9]++;
    this.repository = repository;
  }
  /* istanbul ignore next */
  cov_2qwd5kknd4().s[10]++;
  return (0, _createClass2.default)(BillValidateUseCase, [{
    key: "execute",
    value: function () {
      /* istanbul ignore next */
      cov_2qwd5kknd4().f[2]++;
      var _execute =
      /* istanbul ignore next */
      (cov_2qwd5kknd4().s[11]++, (0, _asyncToGenerator2.default)(function* (request) {
        /* istanbul ignore next */
        cov_2qwd5kknd4().f[3]++;
        var _this =
        /* istanbul ignore next */
        (cov_2qwd5kknd4().s[12]++, this);
        /* istanbul ignore next */
        cov_2qwd5kknd4().s[13]++;
        return ExcecutionHandler_1.ExecutionHandler.execute(function () {
          /* istanbul ignore next */
          cov_2qwd5kknd4().f[4]++;
          cov_2qwd5kknd4().s[14]++;
          return _this.repository.billValidate(request);
        });
      }));
      function execute(_x) {
        /* istanbul ignore next */
        cov_2qwd5kknd4().f[5]++;
        cov_2qwd5kknd4().s[15]++;
        return _execute.apply(this, arguments);
      }
      /* istanbul ignore next */
      cov_2qwd5kknd4().s[16]++;
      return execute;
    }()
  }]);
}());
/* istanbul ignore next */
cov_2qwd5kknd4().s[17]++;
exports.BillValidateUseCase = BillValidateUseCase;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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