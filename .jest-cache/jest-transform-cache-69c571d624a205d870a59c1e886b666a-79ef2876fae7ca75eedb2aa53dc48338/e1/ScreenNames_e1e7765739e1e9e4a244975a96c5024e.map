{"version": 3, "names": ["cov_yr1ygpo45", "actualCoverage", "s", "ScreenNames", "PaymentHomePage", "BillDetailScreen", "EditBillScreen", "PaymentScreen", "PaymentBillScreen", "PaymentInfoScreen", "PaymentConfirmScreen", "PaymentResultScreen", "PaymentResultDetailScreen", "PaymentPhoneScreen", "SaveBillContactScreen", "EditBillContactScreen", "PrepaidMobileScreen", "PrepaidMobileInfoScreen", "PostpaidMobileScreen", "PostpaidMobileInfoScreen", "QRPaymentInfoScreen", "exports", "default"], "sources": ["/Users/<USER>/projects/react-native/payment-module/src/commons/ScreenNames.ts"], "sourcesContent": ["const ScreenNames = {\n  PaymentHomePage: 'PaymentHomePage',\n  BillDetailScreen: 'BillDetailScreen',\n  EditBillScreen: 'EditBillScreen',\n  PaymentScreen: 'PaymentScreen',\n  PaymentBillScreen: 'PaymentBillScreen',\n  PaymentInfoScreen: 'PaymentInfoScreen',\n  PaymentConfirmScreen: 'PaymentConfirmScreen',\n  PaymentResultScreen: 'PaymentResultScreen',\n  PaymentResultDetailScreen: 'PaymentResultDetailScreen',\n  PaymentPhoneScreen: 'PaymentPhoneScreen',\n  SaveBillContactScreen: 'SaveBillContactScreen',\n  EditBillContactScreen: 'EditBillContactScreen',\n  PrepaidMobileScreen: 'PrepaidMobileScreen',\n  PrepaidMobileInfoScreen: 'PrepaidMobileInfoScreen',\n  PostpaidMobileScreen: 'PostpaidMobileScreen',\n  PostpaidMobileInfoScreen: 'PostpaidMobileInfoScreen',\n  QRPaymentInfoScreen: 'QRPaymentInfoScreen',\n} as const;\n\nexport default ScreenNames;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAUE;IAAAA,aAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,aAAA;AAAAA,aAAA,GAAAE,CAAA;;;;AAVF,IAAMC,WAAW;AAAA;AAAA,CAAAH,aAAA,GAAAE,CAAA,OAAG;EAClBE,eAAe,EAAE,iBAAiB;EAClCC,gBAAgB,EAAE,kBAAkB;EACpCC,cAAc,EAAE,gBAAgB;EAChCC,aAAa,EAAE,eAAe;EAC9BC,iBAAiB,EAAE,mBAAmB;EACtCC,iBAAiB,EAAE,mBAAmB;EACtCC,oBAAoB,EAAE,sBAAsB;EAC5CC,mBAAmB,EAAE,qBAAqB;EAC1CC,yBAAyB,EAAE,2BAA2B;EACtDC,kBAAkB,EAAE,oBAAoB;EACxCC,qBAAqB,EAAE,uBAAuB;EAC9CC,qBAAqB,EAAE,uBAAuB;EAC9CC,mBAAmB,EAAE,qBAAqB;EAC1CC,uBAAuB,EAAE,yBAAyB;EAClDC,oBAAoB,EAAE,sBAAsB;EAC5CC,wBAAwB,EAAE,0BAA0B;EACpDC,mBAAmB,EAAE;CACb;AAAA;AAAApB,aAAA,GAAAE,CAAA;AAEVmB,OAAA,CAAAC,OAAA,GAAenB,WAAW", "ignoreList": []}