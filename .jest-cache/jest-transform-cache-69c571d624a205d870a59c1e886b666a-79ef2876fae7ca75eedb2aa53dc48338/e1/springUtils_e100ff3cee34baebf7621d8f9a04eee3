37824f26106fa98754267f4406cd6de7
'use strict';

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.bisectRoot = bisectRoot;
exports.calculateNewMassToMatchDuration = calculateNewMassToMatchDuration;
exports.checkIfConfigIsValid = checkIfConfigIsValid;
exports.criticallyDampedSpringCalculations = criticallyDampedSpringCalculations;
exports.initialCalculations = initialCalculations;
exports.isAnimationTerminatingCalculation = isAnimationTerminatingCalculation;
exports.scaleZetaToMatchClamps = scaleZetaToMatchClamps;
exports.underDampedSpringCalculations = underDampedSpringCalculations;
var _toConsumableArray2 = _interopRequireDefault(require("@babel/runtime/helpers/toConsumableArray"));
var _slicedToArray2 = _interopRequireDefault(require("@babel/runtime/helpers/slicedToArray"));
var _index = require("../logger/index.js");
function checkIfConfigIsValid(config) {
  'worklet';

  var _config$clamp, _config$clamp2;
  var errorMessage = '';
  ['stiffness', 'damping', 'dampingRatio', 'restDisplacementThreshold', 'restSpeedThreshold', 'mass'].forEach(function (prop) {
    var value = config[prop];
    if (value <= 0) {
      errorMessage += `, ${prop} must be grater than zero but got ${value}`;
    }
  });
  if (config.duration < 0) {
    errorMessage += `, duration can't be negative, got ${config.duration}`;
  }
  if ((_config$clamp = config.clamp) != null && _config$clamp.min && (_config$clamp2 = config.clamp) != null && _config$clamp2.max && config.clamp.min > config.clamp.max) {
    errorMessage += `, clamp.min should be lower than clamp.max, got clamp: {min: ${config.clamp.min}, max: ${config.clamp.max}} `;
  }
  if (errorMessage !== '') {
    _index.logger.warn('Invalid spring config' + errorMessage);
  }
  return errorMessage === '';
}
function bisectRoot(_ref) {
  'worklet';

  var min = _ref.min,
    max = _ref.max,
    func = _ref.func,
    _ref$maxIterations = _ref.maxIterations,
    maxIterations = _ref$maxIterations === void 0 ? 20 : _ref$maxIterations;
  var ACCURACY = 0.00005;
  var idx = maxIterations;
  var current = (max + min) / 2;
  while (Math.abs(func(current)) > ACCURACY && idx > 0) {
    idx -= 1;
    if (func(current) < 0) {
      min = current;
    } else {
      max = current;
    }
    current = (min + max) / 2;
  }
  return current;
}
function initialCalculations() {
  'worklet';

  var mass = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 0;
  var config = arguments.length > 1 ? arguments[1] : undefined;
  if (config.skipAnimation) {
    return {
      zeta: 0,
      omega0: 0,
      omega1: 0
    };
  }
  if (config.useDuration) {
    var k = config.stiffness,
      zeta = config.dampingRatio;
    var omega0 = Math.sqrt(k / mass);
    var omega1 = omega0 * Math.sqrt(1 - zeta ** 2);
    return {
      zeta: zeta,
      omega0: omega0,
      omega1: omega1
    };
  } else {
    var c = config.damping,
      m = config.mass,
      _k = config.stiffness;
    var _zeta = c / (2 * Math.sqrt(_k * m));
    var _omega = Math.sqrt(_k / m);
    var _omega2 = _omega * Math.sqrt(1 - _zeta ** 2);
    return {
      zeta: _zeta,
      omega0: _omega,
      omega1: _omega2
    };
  }
}
function scaleZetaToMatchClamps(animation, clamp) {
  'worklet';

  var zeta = animation.zeta,
    toValue = animation.toValue,
    startValue = animation.startValue;
  var toValueNum = Number(toValue);
  if (toValueNum === startValue) {
    return zeta;
  }
  var _ref2 = toValueNum - startValue > 0 ? [clamp.min, clamp.max] : [clamp.max, clamp.min],
    _ref3 = (0, _slicedToArray2.default)(_ref2, 2),
    firstBound = _ref3[0],
    secondBound = _ref3[1];
  var relativeExtremum1 = secondBound !== undefined ? Math.abs((secondBound - toValueNum) / (toValueNum - startValue)) : undefined;
  var relativeExtremum2 = firstBound !== undefined ? Math.abs((firstBound - toValueNum) / (toValueNum - startValue)) : undefined;
  var newZeta1 = relativeExtremum1 !== undefined ? Math.abs(Math.log(relativeExtremum1) / Math.PI) : undefined;
  var newZeta2 = relativeExtremum2 !== undefined ? Math.abs(Math.log(relativeExtremum2) / (2 * Math.PI)) : undefined;
  var zetaSatisfyingClamp = [newZeta1, newZeta2].filter(function (x) {
    return x !== undefined;
  });
  return Math.max.apply(Math, (0, _toConsumableArray2.default)(zetaSatisfyingClamp).concat([zeta]));
}
function calculateNewMassToMatchDuration(x0, config, v0) {
  'worklet';

  if (config.skipAnimation) {
    return 0;
  }
  var k = config.stiffness,
    zeta = config.dampingRatio,
    threshold = config.restSpeedThreshold,
    duration = config.duration;
  var durationForMass = function durationForMass(mass) {
    'worklet';

    var amplitude = (mass * v0 * v0 + k * x0 * x0) / (Math.exp(1 - 0.5 * zeta) * k);
    var c = zeta * 2 * Math.sqrt(k * mass);
    return 1000 * (-2 * mass / c) * Math.log(threshold * 0.01 / amplitude) - duration;
  };
  return bisectRoot({
    min: 0,
    max: 100,
    func: durationForMass
  });
}
function criticallyDampedSpringCalculations(animation, precalculatedValues) {
  'worklet';

  var toValue = animation.toValue;
  var v0 = precalculatedValues.v0,
    x0 = precalculatedValues.x0,
    omega0 = precalculatedValues.omega0,
    t = precalculatedValues.t;
  var criticallyDampedEnvelope = Math.exp(-omega0 * t);
  var criticallyDampedPosition = toValue - criticallyDampedEnvelope * (x0 + (v0 + omega0 * x0) * t);
  var criticallyDampedVelocity = criticallyDampedEnvelope * (v0 * (t * omega0 - 1) + t * x0 * omega0 * omega0);
  return {
    position: criticallyDampedPosition,
    velocity: criticallyDampedVelocity
  };
}
function underDampedSpringCalculations(animation, precalculatedValues) {
  'worklet';

  var toValue = animation.toValue,
    current = animation.current,
    velocity = animation.velocity;
  var zeta = precalculatedValues.zeta,
    t = precalculatedValues.t,
    omega0 = precalculatedValues.omega0,
    omega1 = precalculatedValues.omega1;
  var v0 = -velocity;
  var x0 = toValue - current;
  var sin1 = Math.sin(omega1 * t);
  var cos1 = Math.cos(omega1 * t);
  var underDampedEnvelope = Math.exp(-zeta * omega0 * t);
  var underDampedFrag1 = underDampedEnvelope * (sin1 * ((v0 + zeta * omega0 * x0) / omega1) + x0 * cos1);
  var underDampedPosition = toValue - underDampedFrag1;
  var underDampedVelocity = zeta * omega0 * underDampedFrag1 - underDampedEnvelope * (cos1 * (v0 + zeta * omega0 * x0) - omega1 * x0 * sin1);
  return {
    position: underDampedPosition,
    velocity: underDampedVelocity
  };
}
function isAnimationTerminatingCalculation(animation, config) {
  'worklet';

  var toValue = animation.toValue,
    velocity = animation.velocity,
    startValue = animation.startValue,
    current = animation.current;
  var isOvershooting = config.overshootClamping ? current > toValue && startValue < toValue || current < toValue && startValue > toValue : false;
  var isVelocity = Math.abs(velocity) < config.restSpeedThreshold;
  var isDisplacement = Math.abs(toValue - current) < config.restDisplacementThreshold;
  return {
    isOvershooting: isOvershooting,
    isVelocity: isVelocity,
    isDisplacement: isDisplacement
  };
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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