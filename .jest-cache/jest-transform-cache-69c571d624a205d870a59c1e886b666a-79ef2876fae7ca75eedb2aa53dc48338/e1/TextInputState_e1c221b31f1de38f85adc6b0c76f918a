00b657c0a1e6edfc14833dc3bbbb15a0
var _AndroidTextInputNativeComponent = require("../../Components/TextInput/AndroidTextInputNativeComponent");
var _RCTSingelineTextInputNativeComponent = require("../../Components/TextInput/RCTSingelineTextInputNativeComponent");
var _require = require('../../ReactNative/RendererProxy'),
  findNodeHandle = _require.findNodeHandle;
var Platform = require('../../Utilities/Platform');
var currentlyFocusedInputRef = null;
var inputs = new Set();
function currentlyFocusedInput() {
  return currentlyFocusedInputRef;
}
function currentlyFocusedField() {
  if (__DEV__) {
    console.error('currentlyFocusedField is deprecated and will be removed in a future release. Use currentlyFocusedInput');
  }
  return findNodeHandle(currentlyFocusedInputRef);
}
function focusInput(textField) {
  if (currentlyFocusedInputRef !== textField && textField != null) {
    currentlyFocusedInputRef = textField;
  }
}
function blurInput(textField) {
  if (currentlyFocusedInputRef === textField && textField != null) {
    currentlyFocusedInputRef = null;
  }
}
function focusField(textFieldID) {
  if (__DEV__) {
    console.error('focusField no longer works. Use focusInput');
  }
  return;
}
function blurField(textFieldID) {
  if (__DEV__) {
    console.error('blurField no longer works. Use blurInput');
  }
  return;
}
function focusTextInput(textField) {
  if (typeof textField === 'number') {
    if (__DEV__) {
      console.error('focusTextInput must be called with a host component. Passing a react tag is deprecated.');
    }
    return;
  }
  if (textField != null) {
    var _textField$currentPro;
    var fieldCanBeFocused = currentlyFocusedInputRef !== textField && ((_textField$currentPro = textField.currentProps) == null ? void 0 : _textField$currentPro.editable) !== false;
    if (!fieldCanBeFocused) {
      return;
    }
    focusInput(textField);
    if (Platform.OS === 'ios') {
      _RCTSingelineTextInputNativeComponent.Commands.focus(textField);
    } else if (Platform.OS === 'android') {
      _AndroidTextInputNativeComponent.Commands.focus(textField);
    }
  }
}
function blurTextInput(textField) {
  if (typeof textField === 'number') {
    if (__DEV__) {
      console.error('blurTextInput must be called with a host component. Passing a react tag is deprecated.');
    }
    return;
  }
  if (currentlyFocusedInputRef === textField && textField != null) {
    blurInput(textField);
    if (Platform.OS === 'ios') {
      _RCTSingelineTextInputNativeComponent.Commands.blur(textField);
    } else if (Platform.OS === 'android') {
      _AndroidTextInputNativeComponent.Commands.blur(textField);
    }
  }
}
function registerInput(textField) {
  if (typeof textField === 'number') {
    if (__DEV__) {
      console.error('registerInput must be called with a host component. Passing a react tag is deprecated.');
    }
    return;
  }
  inputs.add(textField);
}
function unregisterInput(textField) {
  if (typeof textField === 'number') {
    if (__DEV__) {
      console.error('unregisterInput must be called with a host component. Passing a react tag is deprecated.');
    }
    return;
  }
  inputs.delete(textField);
}
function isTextInput(textField) {
  if (typeof textField === 'number') {
    if (__DEV__) {
      console.error('isTextInput must be called with a host component. Passing a react tag is deprecated.');
    }
    return false;
  }
  return inputs.has(textField);
}
module.exports = {
  currentlyFocusedInput: currentlyFocusedInput,
  focusInput: focusInput,
  blurInput: blurInput,
  currentlyFocusedField: currentlyFocusedField,
  focusField: focusField,
  blurField: blurField,
  focusTextInput: focusTextInput,
  blurTextInput: blurTextInput,
  registerInput: registerInput,
  unregisterInput: unregisterInput,
  isTextInput: isTextInput
};
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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