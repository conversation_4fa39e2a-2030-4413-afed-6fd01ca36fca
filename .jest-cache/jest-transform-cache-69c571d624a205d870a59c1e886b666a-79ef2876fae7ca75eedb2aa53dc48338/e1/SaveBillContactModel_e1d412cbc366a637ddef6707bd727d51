bd2e66d6f66d9dab935a4f9f41bf5ccf
"use strict";

/* istanbul ignore next */
function cov_1ap4qbnsy3() {
  var path = "/Users/<USER>/projects/react-native/payment-module/src/domain/entities/save-bill-contact/SaveBillContactModel.ts";
  var hash = "b35eac858a3000d119c729375789812edb31da3b";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/projects/react-native/payment-module/src/domain/entities/save-bill-contact/SaveBillContactModel.ts",
    statementMap: {
      "0": {
        start: {
          line: 3,
          column: 29
        },
        end: {
          line: 3,
          column: 84
        }
      },
      "1": {
        start: {
          line: 4,
          column: 20
        },
        end: {
          line: 4,
          column: 89
        }
      },
      "2": {
        start: {
          line: 5,
          column: 23
        },
        end: {
          line: 5,
          column: 95
        }
      },
      "3": {
        start: {
          line: 6,
          column: 0
        },
        end: {
          line: 8,
          column: 3
        }
      },
      "4": {
        start: {
          line: 9,
          column: 0
        },
        end: {
          line: 9,
          column: 38
        }
      },
      "5": {
        start: {
          line: 10,
          column: 27
        },
        end: {
          line: 12,
          column: 2
        }
      },
      "6": {
        start: {
          line: 11,
          column: 2
        },
        end: {
          line: 11,
          column: 60
        }
      },
      "7": {
        start: {
          line: 13,
          column: 0
        },
        end: {
          line: 13,
          column: 52
        }
      }
    },
    fnMap: {
      "0": {
        name: "SaveBillContactModel",
        decl: {
          start: {
            line: 10,
            column: 63
          },
          end: {
            line: 10,
            column: 83
          }
        },
        loc: {
          start: {
            line: 10,
            column: 86
          },
          end: {
            line: 12,
            column: 1
          }
        },
        line: 10
      }
    },
    branchMap: {},
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0
    },
    f: {
      "0": 0
    },
    b: {},
    inputSourceMap: {
      version: 3,
      file: undefined,
      names: ["SaveBillContactModel", "_createClass2", "default", "_classCallCheck2", "exports"],
      sourceRoot: undefined,
      sources: ["/Users/<USER>/projects/react-native/payment-module/src/domain/entities/save-bill-contact/SaveBillContactModel.ts"],
      sourcesContent: ["export class SaveBillContactModel {\n  // TODO: define fields\n}\n"],
      mappings: ";;;;;;;;;IAAaA,oBAAoB,OAAAC,aAAA,CAAAC,OAAA,WAAAF,qBAAA;EAAA,IAAAG,gBAAA,CAAAD,OAAA,QAAAF,oBAAA;AAAA;AAAjCI,OAAA,CAAAJ,oBAAA,GAAAA,oBAAA",
      ignoreList: []
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "b35eac858a3000d119c729375789812edb31da3b"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_1ap4qbnsy3 = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_1ap4qbnsy3();
var _interopRequireDefault =
/* istanbul ignore next */
(cov_1ap4qbnsy3().s[0]++, require("@babel/runtime/helpers/interopRequireDefault"));
var _createClass2 =
/* istanbul ignore next */
(cov_1ap4qbnsy3().s[1]++, _interopRequireDefault(require("@babel/runtime/helpers/createClass")));
var _classCallCheck2 =
/* istanbul ignore next */
(cov_1ap4qbnsy3().s[2]++, _interopRequireDefault(require("@babel/runtime/helpers/classCallCheck")));
/* istanbul ignore next */
cov_1ap4qbnsy3().s[3]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_1ap4qbnsy3().s[4]++;
exports.SaveBillContactModel = void 0;
var SaveBillContactModel =
/* istanbul ignore next */
(cov_1ap4qbnsy3().s[5]++, (0, _createClass2.default)(function SaveBillContactModel() {
  /* istanbul ignore next */
  cov_1ap4qbnsy3().f[0]++;
  cov_1ap4qbnsy3().s[6]++;
  (0, _classCallCheck2.default)(this, SaveBillContactModel);
}));
/* istanbul ignore next */
cov_1ap4qbnsy3().s[7]++;
exports.SaveBillContactModel = SaveBillContactModel;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJTYXZlQmlsbENvbnRhY3RNb2RlbCIsImNvdl8xYXA0cWJuc3kzIiwicyIsIl9jcmVhdGVDbGFzczIiLCJkZWZhdWx0IiwiZiIsIl9jbGFzc0NhbGxDaGVjazIiLCJleHBvcnRzIl0sInNvdXJjZXMiOlsiL1VzZXJzL3NvbnNhbXNldC9wcm9qZWN0cy9yZWFjdC1uYXRpdmUvcGF5bWVudC1tb2R1bGUvc3JjL2RvbWFpbi9lbnRpdGllcy9zYXZlLWJpbGwtY29udGFjdC9TYXZlQmlsbENvbnRhY3RNb2RlbC50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgY2xhc3MgU2F2ZUJpbGxDb250YWN0TW9kZWwge1xuICAvLyBUT0RPOiBkZWZpbmUgZmllbGRzXG59XG4iXSwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztJQUFhQSxvQkFBb0I7QUFBQTtBQUFBLENBQUFDLGNBQUEsR0FBQUMsQ0FBQSxXQUFBQyxhQUFBLENBQUFDLE9BQUEsV0FBQUoscUJBQUE7RUFBQTtFQUFBQyxjQUFBLEdBQUFJLENBQUE7RUFBQUosY0FBQSxHQUFBQyxDQUFBO0VBQUEsSUFBQUksZ0JBQUEsQ0FBQUYsT0FBQSxRQUFBSixvQkFBQTtBQUFBO0FBQUE7QUFBQUMsY0FBQSxHQUFBQyxDQUFBO0FBQWpDSyxPQUFBLENBQUFQLG9CQUFBLEdBQUFBLG9CQUFBIiwiaWdub3JlTGlzdCI6W119