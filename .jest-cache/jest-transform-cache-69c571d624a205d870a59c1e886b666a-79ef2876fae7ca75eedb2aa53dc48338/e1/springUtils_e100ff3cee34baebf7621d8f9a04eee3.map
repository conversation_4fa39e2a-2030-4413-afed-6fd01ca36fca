{"version": 3, "names": ["_interopRequireDefault", "require", "Object", "defineProperty", "exports", "value", "bisectRoot", "calculateNewMassToMatchDuration", "checkIfConfigIsValid", "criticallyDampedSpringCalculations", "initialCalculations", "isAnimationTerminatingCalculation", "scaleZetaToMatchClamps", "underDampedSpringCalculations", "_toConsumableArray2", "_slicedToArray2", "_index", "config", "_config$clamp", "_config$clamp2", "errorMessage", "for<PERSON>ach", "prop", "duration", "clamp", "min", "max", "logger", "warn", "_ref", "func", "_ref$maxIterations", "maxIterations", "ACCURACY", "idx", "current", "Math", "abs", "mass", "arguments", "length", "undefined", "skipAnimation", "zeta", "omega0", "omega1", "useDuration", "k", "stiffness", "dampingRatio", "sqrt", "c", "damping", "m", "animation", "toValue", "startValue", "toValueNum", "Number", "_ref2", "_ref3", "default", "firstBound", "secondBound", "relativeExtremum1", "relativeExtremum2", "newZeta1", "log", "PI", "newZeta2", "zetaSatisfyingClamp", "filter", "x", "apply", "concat", "x0", "v0", "threshold", "restSpeedThreshold", "durationForMass", "amplitude", "exp", "precalculatedValues", "t", "criticallyDampedEnvelope", "criticallyDampedPosition", "criticallyDampedVelocity", "position", "velocity", "sin1", "sin", "cos1", "cos", "underDampedEnvelope", "underDampedFrag1", "underDampedPosition", "underDampedVelocity", "isOvershooting", "overshootClamping", "isVelocity", "isDisplacement", "restDisplacementThreshold"], "sources": ["../../../src/animation/springUtils.ts"], "sourcesContent": [null], "mappings": "AAAA,YAAY;;AAAA,IAAAA,sBAAA,GAAAC,OAAA;AAAAC,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,KAAA;AAAA;AAAAD,OAAA,CAAAE,UAAA,GAAAA,UAAA;AAAAF,OAAA,CAAAG,+BAAA,GAAAA,+BAAA;AAAAH,OAAA,CAAAI,oBAAA,GAAAA,oBAAA;AAAAJ,OAAA,CAAAK,kCAAA,GAAAA,kCAAA;AAAAL,OAAA,CAAAM,mBAAA,GAAAA,mBAAA;AAAAN,OAAA,CAAAO,iCAAA,GAAAA,iCAAA;AAAAP,OAAA,CAAAQ,sBAAA,GAAAA,sBAAA;AAAAR,OAAA,CAAAS,6BAAA,GAAAA,6BAAA;AAAA,IAAAC,mBAAA,GAAAd,sBAAA,CAAAC,OAAA;AAAA,IAAAc,eAAA,GAAAf,sBAAA,CAAAC,OAAA;AAOZ,IAAAe,MAAA,GAAAf,OAAA;AAmFO,SAASO,oBAAoBA,CAACS,MAA2B,EAAW;EACzE,SAAS;;EAAA,IAAAC,aAAA,EAAAC,cAAA;EACT,IAAIC,YAAY,GAAG,EAAE;EAEnB,CACE,WAAW,EACX,SAAS,EACT,cAAc,EACd,2BAA2B,EAC3B,oBAAoB,EACpB,MAAM,CACP,CACDC,OAAO,CAAE,UAAAC,IAAI,EAAK;IAClB,IAAMjB,KAAK,GAAGY,MAAM,CAACK,IAAI,CAAC;IAC1B,IAAIjB,KAAK,IAAI,CAAC,EAAE;MACde,YAAY,IAAI,KAAKE,IAAI,qCAAqCjB,KAAK,EAAE;IACvE;EACF,CAAC,CAAC;EAEF,IAAIY,MAAM,CAACM,QAAQ,GAAG,CAAC,EAAE;IACvBH,YAAY,IAAI,qCAAqCH,MAAM,CAACM,QAAQ,EAAE;EACxE;EAEA,IACE,CAAAL,aAAA,GAAAD,MAAM,CAACO,KAAK,aAAZN,aAAA,CAAcO,GAAG,KAAAN,cAAA,GACjBF,MAAM,CAACO,KAAK,aAAZL,cAAA,CAAcO,GAAG,IACjBT,MAAM,CAACO,KAAK,CAACC,GAAG,GAAGR,MAAM,CAACO,KAAK,CAACE,GAAG,EACnC;IACAN,YAAY,IAAI,gEAAgEH,MAAM,CAACO,KAAK,CAACC,GAAG,UAAUR,MAAM,CAACO,KAAK,CAACE,GAAG,IAAI;EAChI;EAEA,IAAIN,YAAY,KAAK,EAAE,EAAE;IACvBO,aAAM,CAACC,IAAI,CAAC,uBAAuB,GAAGR,YAAY,CAAC;EACrD;EAEA,OAAOA,YAAY,KAAK,EAAE;AAC5B;AAGO,SAASd,UAAUA,CAAAuB,IAAA,EAUvB;EACD,SAAS;;EAAA,IAVTJ,GAAG,GAAAI,IAAA,CAAHJ,GAAG;IACHC,GAAG,GAAAG,IAAA,CAAHH,GAAG;IACHI,IAAI,GAAAD,IAAA,CAAJC,IAAI;IAAAC,kBAAA,GAAAF,IAAA,CACJG,aAAa;IAAbA,aAAa,GAAAD,kBAAA,cAAG,KAAAA,kBAAA;EAQhB,IAAME,QAAQ,GAAG,OAAO;EACxB,IAAIC,GAAG,GAAGF,aAAa;EACvB,IAAIG,OAAO,GAAG,CAACT,GAAG,GAAGD,GAAG,IAAI,CAAC;EAC7B,OAAOW,IAAI,CAACC,GAAG,CAACP,IAAI,CAACK,OAAO,CAAC,CAAC,GAAGF,QAAQ,IAAIC,GAAG,GAAG,CAAC,EAAE;IACpDA,GAAG,IAAI,CAAC;IAER,IAAIJ,IAAI,CAACK,OAAO,CAAC,GAAG,CAAC,EAAE;MACrBV,GAAG,GAAGU,OAAO;IACf,CAAC,MAAM;MACLT,GAAG,GAAGS,OAAO;IACf;IACAA,OAAO,GAAG,CAACV,GAAG,GAAGC,GAAG,IAAI,CAAC;EAC3B;EACA,OAAOS,OAAO;AAChB;AAEO,SAASzB,mBAAmBA,CAAA,EAOjC;EACA,SAAS;;EAAA,IAPT4B,IAAI,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC;EAAA,IACRtB,MAA+C,GAAAsB,SAAA,CAAAC,MAAA,OAAAD,SAAA,MAAAE,SAAA;EAQ/C,IAAIxB,MAAM,CAACyB,aAAa,EAAE;IACxB,OAAO;MAAEC,IAAI,EAAE,CAAC;MAAEC,MAAM,EAAE,CAAC;MAAEC,MAAM,EAAE;IAAE,CAAC;EAC1C;EAEA,IAAI5B,MAAM,CAAC6B,WAAW,EAAE;IACtB,IAAmBC,CAAC,GAAyB9B,MAAM,CAA3C+B,SAAS;MAAmBL,IAAA,GAAS1B,MAAM,CAA7BgC,YAAY;IAOlC,IAAML,MAAM,GAAGR,IAAI,CAACc,IAAI,CAACH,CAAC,GAAGT,IAAI,CAAC;IAClC,IAAMO,MAAM,GAAGD,MAAM,GAAGR,IAAI,CAACc,IAAI,CAAC,CAAC,GAAGP,IAAI,IAAI,CAAC,CAAC;IAEhD,OAAO;MAAEA,IAAI,EAAJA,IAAI;MAAEC,MAAM,EAANA,MAAM;MAAEC,MAAA,EAAAA;IAAO,CAAC;EACjC,CAAC,MAAM;IACL,IAAiBM,CAAC,GAA4BlC,MAAM,CAA5CmC,OAAO;MAAWC,CAAC,GAAmBpC,MAAM,CAAhCqB,IAAI;MAAgBS,EAAA,GAAM9B,MAAM,CAAvB+B,SAAS;IAEtC,IAAML,KAAI,GAAGQ,CAAC,IAAI,CAAC,GAAGf,IAAI,CAACc,IAAI,CAACH,EAAC,GAAGM,CAAC,CAAC,CAAC;IACvC,IAAMT,MAAM,GAAGR,IAAI,CAACc,IAAI,CAACH,EAAC,GAAGM,CAAC,CAAC;IAC/B,IAAMR,OAAM,GAAGD,MAAM,GAAGR,IAAI,CAACc,IAAI,CAAC,CAAC,GAAGP,KAAI,IAAI,CAAC,CAAC;IAEhD,OAAO;MAAEA,IAAI,EAAJA,KAAI;MAAEC,MAAM,EAANA,MAAM;MAAEC,MAAA,EAAAA;IAAO,CAAC;EACjC;AACF;AAOO,SAASjC,sBAAsBA,CACpC0C,SAA0B,EAC1B9B,KAAqC,EAC7B;EACR,SAAS;;EACT,IAAQmB,IAAI,GAA0BW,SAAS,CAAvCX,IAAI;IAAEY,OAAO,GAAiBD,SAAS,CAAjCC,OAAO;IAAEC,UAAA,GAAeF,SAAS,CAAxBE,UAAA;EACvB,IAAMC,UAAU,GAAGC,MAAM,CAACH,OAAO,CAAC;EAElC,IAAIE,UAAU,KAAKD,UAAU,EAAE;IAC7B,OAAOb,IAAI;EACb;EAEA,IAAAgB,KAAA,GACEF,UAAU,GAAGD,UAAU,GAAG,CAAC,GACvB,CAAChC,KAAK,CAACC,GAAG,EAAED,KAAK,CAACE,GAAG,CAAC,GACtB,CAACF,KAAK,CAACE,GAAG,EAAEF,KAAK,CAACC,GAAG,CAAC;IAAAmC,KAAA,OAAA7C,eAAA,CAAA8C,OAAA,EAAAF,KAAA;IAHrBG,UAAU,GAAAF,KAAA;IAAEG,WAAW,GAAAH,KAAA;EAgB9B,IAAMI,iBAAiB,GACrBD,WAAW,KAAKtB,SAAS,GACrBL,IAAI,CAACC,GAAG,CAAC,CAAC0B,WAAW,GAAGN,UAAU,KAAKA,UAAU,GAAGD,UAAU,CAAC,CAAC,GAChEf,SAAS;EAEf,IAAMwB,iBAAiB,GACrBH,UAAU,KAAKrB,SAAS,GACpBL,IAAI,CAACC,GAAG,CAAC,CAACyB,UAAU,GAAGL,UAAU,KAAKA,UAAU,GAAGD,UAAU,CAAC,CAAC,GAC/Df,SAAS;EAYf,IAAMyB,QAAQ,GACZF,iBAAiB,KAAKvB,SAAS,GAC3BL,IAAI,CAACC,GAAG,CAACD,IAAI,CAAC+B,GAAG,CAACH,iBAAiB,CAAC,GAAG5B,IAAI,CAACgC,EAAE,CAAC,GAC/C3B,SAAS;EAEf,IAAM4B,QAAQ,GACZJ,iBAAiB,KAAKxB,SAAS,GAC3BL,IAAI,CAACC,GAAG,CAACD,IAAI,CAAC+B,GAAG,CAACF,iBAAiB,CAAC,IAAI,CAAC,GAAG7B,IAAI,CAACgC,EAAE,CAAC,CAAC,GACrD3B,SAAS;EAEf,IAAM6B,mBAAmB,GAAG,CAACJ,QAAQ,EAAEG,QAAQ,CAAC,CAACE,MAAM,CACpD,UAAAC,CAAqB;IAAA,OAAkBA,CAAC,KAAK/B,SAChD;EAAA,EAAC;EAGD,OAAOL,IAAI,CAACV,GAAG,CAAA+C,KAAA,CAARrC,IAAI,MAAAtB,mBAAA,CAAA+C,OAAA,EAAQS,mBAAmB,EAAAI,MAAA,EAAE/B,IAAI,GAAC;AAC/C;AAGO,SAASpC,+BAA+BA,CAC7CoE,EAAU,EACV1D,MAA+C,EAC/C2D,EAAU,EACV;EACA,SAAS;;EACT,IAAI3D,MAAM,CAACyB,aAAa,EAAE;IACxB,OAAO,CAAC;EACV;EAuBA,IACaK,CAAC,GAIV9B,MAAM,CAJR+B,SAAS;IACKL,IAAI,GAGhB1B,MAAM,CAHRgC,YAAY;IACQ4B,SAAS,GAE3B5D,MAAM,CAFR6D,kBAAkB;IAClBvD,QAAA,GACEN,MAAM,CADRM,QAAA;EAGF,IAAMwD,eAAe,GAAI,SAAnBA,eAAeA,CAAIzC,IAAY,EAAK;IACxC,SAAS;;IACT,IAAM0C,SAAS,GACb,CAAC1C,IAAI,GAAGsC,EAAE,GAAGA,EAAE,GAAG7B,CAAC,GAAG4B,EAAE,GAAGA,EAAE,KAAKvC,IAAI,CAAC6C,GAAG,CAAC,CAAC,GAAG,GAAG,GAAGtC,IAAI,CAAC,GAAGI,CAAC,CAAC;IACjE,IAAMI,CAAC,GAAGR,IAAI,GAAG,CAAC,GAAGP,IAAI,CAACc,IAAI,CAACH,CAAC,GAAGT,IAAI,CAAC;IACxC,OACE,IAAI,IAAK,CAAC,CAAC,GAAGA,IAAI,GAAIa,CAAC,CAAC,GAAGf,IAAI,CAAC+B,GAAG,CAAEU,SAAS,GAAG,IAAI,GAAIG,SAAS,CAAC,GACnEzD,QAAQ;EAEZ,CAAC;EAGD,OAAOjB,UAAU,CAAC;IAAEmB,GAAG,EAAE,CAAC;IAAEC,GAAG,EAAE,GAAG;IAAEI,IAAI,EAAEiD;EAAgB,CAAC,CAAC;AAChE;AAEO,SAAStE,kCAAkCA,CAChD6C,SAA+B,EAC/B4B,mBAKC,EACuC;EACxC,SAAS;;EACT,IAAQ3B,OAAA,GAAYD,SAAS,CAArBC,OAAA;EAER,IAAQqB,EAAE,GAAoBM,mBAAmB,CAAzCN,EAAE;IAAED,EAAE,GAAgBO,mBAAmB,CAArCP,EAAE;IAAE/B,MAAM,GAAQsC,mBAAmB,CAAjCtC,MAAM;IAAEuC,CAAA,GAAMD,mBAAmB,CAAzBC,CAAA;EAExB,IAAMC,wBAAwB,GAAGhD,IAAI,CAAC6C,GAAG,CAAC,CAACrC,MAAM,GAAGuC,CAAC,CAAC;EACtD,IAAME,wBAAwB,GAC5B9B,OAAO,GAAG6B,wBAAwB,IAAIT,EAAE,GAAG,CAACC,EAAE,GAAGhC,MAAM,GAAG+B,EAAE,IAAIQ,CAAC,CAAC;EAEpE,IAAMG,wBAAwB,GAC5BF,wBAAwB,IACvBR,EAAE,IAAIO,CAAC,GAAGvC,MAAM,GAAG,CAAC,CAAC,GAAGuC,CAAC,GAAGR,EAAE,GAAG/B,MAAM,GAAGA,MAAM,CAAC;EAEpD,OAAO;IACL2C,QAAQ,EAAEF,wBAAwB;IAClCG,QAAQ,EAAEF;EACZ,CAAC;AACH;AAEO,SAASzE,6BAA6BA,CAC3CyC,SAA+B,EAC/B4B,mBAOC,EACuC;EACxC,SAAS;;EACT,IAAQ3B,OAAO,GAAwBD,SAAS,CAAxCC,OAAO;IAAEpB,OAAO,GAAemB,SAAS,CAA/BnB,OAAO;IAAEqD,QAAA,GAAalC,SAAS,CAAtBkC,QAAA;EAE1B,IAAQ7C,IAAI,GAAwBuC,mBAAmB,CAA/CvC,IAAI;IAAEwC,CAAC,GAAqBD,mBAAmB,CAAzCC,CAAC;IAAEvC,MAAM,GAAasC,mBAAmB,CAAtCtC,MAAM;IAAEC,MAAA,GAAWqC,mBAAmB,CAA9BrC,MAAA;EAEzB,IAAM+B,EAAE,GAAG,CAACY,QAAQ;EACpB,IAAMb,EAAE,GAAGpB,OAAO,GAAGpB,OAAO;EAE5B,IAAMsD,IAAI,GAAGrD,IAAI,CAACsD,GAAG,CAAC7C,MAAM,GAAGsC,CAAC,CAAC;EACjC,IAAMQ,IAAI,GAAGvD,IAAI,CAACwD,GAAG,CAAC/C,MAAM,GAAGsC,CAAC,CAAC;EAGjC,IAAMU,mBAAmB,GAAGzD,IAAI,CAAC6C,GAAG,CAAC,CAACtC,IAAI,GAAGC,MAAM,GAAGuC,CAAC,CAAC;EACxD,IAAMW,gBAAgB,GACpBD,mBAAmB,IAClBJ,IAAI,IAAI,CAACb,EAAE,GAAGjC,IAAI,GAAGC,MAAM,GAAG+B,EAAE,IAAI9B,MAAM,CAAC,GAAG8B,EAAE,GAAGgB,IAAI,CAAC;EAE3D,IAAMI,mBAAmB,GAAGxC,OAAO,GAAGuC,gBAAgB;EAEtD,IAAME,mBAAmB,GACvBrD,IAAI,GAAGC,MAAM,GAAGkD,gBAAgB,GAChCD,mBAAmB,IAChBF,IAAI,IAAIf,EAAE,GAAGjC,IAAI,GAAGC,MAAM,GAAG+B,EAAE,CAAC,GAAG9B,MAAM,GAAG8B,EAAE,GAAGc,IAAI,CAAC;EAE3D,OAAO;IAAEF,QAAQ,EAAEQ,mBAAmB;IAAEP,QAAQ,EAAEQ;EAAoB,CAAC;AACzE;AAEO,SAASrF,iCAAiCA,CAC/C2C,SAA+B,EAC/BrC,MAA2B,EAK3B;EACA,SAAS;;EACT,IAAQsC,OAAO,GAAoCD,SAAS,CAApDC,OAAO;IAAEiC,QAAQ,GAA0BlC,SAAS,CAA3CkC,QAAQ;IAAEhC,UAAU,GAAcF,SAAS,CAAjCE,UAAU;IAAErB,OAAA,GAAYmB,SAAS,CAArBnB,OAAA;EAEvC,IAAM8D,cAAc,GAAGhF,MAAM,CAACiF,iBAAiB,GAC1C/D,OAAO,GAAGoB,OAAO,IAAIC,UAAU,GAAGD,OAAO,IACzCpB,OAAO,GAAGoB,OAAO,IAAIC,UAAU,GAAGD,OAAQ,GAC3C,KAAK;EAET,IAAM4C,UAAU,GAAG/D,IAAI,CAACC,GAAG,CAACmD,QAAQ,CAAC,GAAGvE,MAAM,CAAC6D,kBAAkB;EACjE,IAAMsB,cAAc,GAClBhE,IAAI,CAACC,GAAG,CAACkB,OAAO,GAAGpB,OAAO,CAAC,GAAGlB,MAAM,CAACoF,yBAAyB;EAEhE,OAAO;IAAEJ,cAAc,EAAdA,cAAc;IAAEE,UAAU,EAAVA,UAAU;IAAEC,cAAA,EAAAA;EAAe,CAAC;AACvD", "ignoreList": []}