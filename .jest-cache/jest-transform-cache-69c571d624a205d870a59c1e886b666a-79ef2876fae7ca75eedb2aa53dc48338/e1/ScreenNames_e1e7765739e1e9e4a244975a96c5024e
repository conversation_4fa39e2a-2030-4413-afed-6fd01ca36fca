2ba218a9430e1acbaadb27208e8a7e31
"use strict";

/* istanbul ignore next */
function cov_yr1ygpo45() {
  var path = "/Users/<USER>/projects/react-native/payment-module/src/commons/ScreenNames.ts";
  var hash = "7bfd0b5649167da4ebcafc5b4b611c6665893f9b";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/projects/react-native/payment-module/src/commons/ScreenNames.ts",
    statementMap: {
      "0": {
        start: {
          line: 3,
          column: 0
        },
        end: {
          line: 5,
          column: 3
        }
      },
      "1": {
        start: {
          line: 6,
          column: 18
        },
        end: {
          line: 24,
          column: 1
        }
      },
      "2": {
        start: {
          line: 25,
          column: 0
        },
        end: {
          line: 25,
          column: 30
        }
      }
    },
    fnMap: {},
    branchMap: {},
    s: {
      "0": 0,
      "1": 0,
      "2": 0
    },
    f: {},
    b: {},
    inputSourceMap: {
      version: 3,
      file: undefined,
      names: ["ScreenNames", "PaymentHomePage", "BillDetailScreen", "EditBillScreen", "PaymentScreen", "PaymentBillScreen", "PaymentInfoScreen", "PaymentConfirmScreen", "PaymentResultScreen", "PaymentResultDetailScreen", "PaymentPhoneScreen", "SaveBillContactScreen", "EditBillContactScreen", "PrepaidMobileScreen", "PrepaidMobileInfoScreen", "PostpaidMobileScreen", "PostpaidMobileInfoScreen", "QRPaymentInfoScreen", "exports", "default"],
      sourceRoot: undefined,
      sources: ["/Users/<USER>/projects/react-native/payment-module/src/commons/ScreenNames.ts"],
      sourcesContent: ["const ScreenNames = {\n  PaymentHomePage: 'PaymentHomePage',\n  BillDetailScreen: 'BillDetailScreen',\n  EditBillScreen: 'EditBillScreen',\n  PaymentScreen: 'PaymentScreen',\n  PaymentBillScreen: 'PaymentBillScreen',\n  PaymentInfoScreen: 'PaymentInfoScreen',\n  PaymentConfirmScreen: 'PaymentConfirmScreen',\n  PaymentResultScreen: 'PaymentResultScreen',\n  PaymentResultDetailScreen: 'PaymentResultDetailScreen',\n  PaymentPhoneScreen: 'PaymentPhoneScreen',\n  SaveBillContactScreen: 'SaveBillContactScreen',\n  EditBillContactScreen: 'EditBillContactScreen',\n  PrepaidMobileScreen: 'PrepaidMobileScreen',\n  PrepaidMobileInfoScreen: 'PrepaidMobileInfoScreen',\n  PostpaidMobileScreen: 'PostpaidMobileScreen',\n  PostpaidMobileInfoScreen: 'PostpaidMobileInfoScreen',\n  QRPaymentInfoScreen: 'QRPaymentInfoScreen',\n} as const;\n\nexport default ScreenNames;\n"],
      mappings: ";;;;;AAAA,IAAMA,WAAW,GAAG;EAClBC,eAAe,EAAE,iBAAiB;EAClCC,gBAAgB,EAAE,kBAAkB;EACpCC,cAAc,EAAE,gBAAgB;EAChCC,aAAa,EAAE,eAAe;EAC9BC,iBAAiB,EAAE,mBAAmB;EACtCC,iBAAiB,EAAE,mBAAmB;EACtCC,oBAAoB,EAAE,sBAAsB;EAC5CC,mBAAmB,EAAE,qBAAqB;EAC1CC,yBAAyB,EAAE,2BAA2B;EACtDC,kBAAkB,EAAE,oBAAoB;EACxCC,qBAAqB,EAAE,uBAAuB;EAC9CC,qBAAqB,EAAE,uBAAuB;EAC9CC,mBAAmB,EAAE,qBAAqB;EAC1CC,uBAAuB,EAAE,yBAAyB;EAClDC,oBAAoB,EAAE,sBAAsB;EAC5CC,wBAAwB,EAAE,0BAA0B;EACpDC,mBAAmB,EAAE;CACb;AAEVC,OAAA,CAAAC,OAAA,GAAenB,WAAW",
      ignoreList: []
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "7bfd0b5649167da4ebcafc5b4b611c6665893f9b"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_yr1ygpo45 = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_yr1ygpo45();
cov_yr1ygpo45().s[0]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
var ScreenNames =
/* istanbul ignore next */
(cov_yr1ygpo45().s[1]++, {
  PaymentHomePage: 'PaymentHomePage',
  BillDetailScreen: 'BillDetailScreen',
  EditBillScreen: 'EditBillScreen',
  PaymentScreen: 'PaymentScreen',
  PaymentBillScreen: 'PaymentBillScreen',
  PaymentInfoScreen: 'PaymentInfoScreen',
  PaymentConfirmScreen: 'PaymentConfirmScreen',
  PaymentResultScreen: 'PaymentResultScreen',
  PaymentResultDetailScreen: 'PaymentResultDetailScreen',
  PaymentPhoneScreen: 'PaymentPhoneScreen',
  SaveBillContactScreen: 'SaveBillContactScreen',
  EditBillContactScreen: 'EditBillContactScreen',
  PrepaidMobileScreen: 'PrepaidMobileScreen',
  PrepaidMobileInfoScreen: 'PrepaidMobileInfoScreen',
  PostpaidMobileScreen: 'PostpaidMobileScreen',
  PostpaidMobileInfoScreen: 'PostpaidMobileInfoScreen',
  QRPaymentInfoScreen: 'QRPaymentInfoScreen'
});
/* istanbul ignore next */
cov_yr1ygpo45().s[2]++;
exports.default = ScreenNames;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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