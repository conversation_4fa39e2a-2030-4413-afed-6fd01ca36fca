{"version": 3, "names": ["cov_dt7gl1oyk", "actualCoverage", "PathResolver_1", "s", "require", "ResponseHandler_1", "MSBCustomError_1", "CustomerRemoteDataSource", "f", "httpClient", "_classCallCheck2", "default", "_createClass2", "key", "value", "_getProfile", "_asyncToGenerator2", "url", "PathResolver", "customer", "getProfile", "response", "get", "handleResponse", "error", "CustomError", "b", "createError", "apply", "arguments", "exports"], "sources": ["/Users/<USER>/projects/react-native/payment-module/src/data/datasources/remote/CustomerRemoteDataSource.ts"], "sourcesContent": ["import {GetProfileResponse} from '../../models/get-profile/GetProfileResponse';\nimport {BaseResponse} from '../../../core/BaseResponse';\nimport {PathResolver} from '../../../utils/PathResolver';\nimport {IHttpClient} from 'msb-host-shared-module';\nimport {handleResponse} from '../../../utils/ResponseHandler';\nimport {ICustomerDataSource} from '../ICustomerDataSource';\nimport {createError, CustomError} from '../../../core/MSBCustomError';\n\nexport class CustomerRemoteDataSource implements ICustomerDataSource {\n  constructor(private httpClient: IHttpClient) {}\n\n  async getProfile(): Promise<BaseResponse<GetProfileResponse>> {\n    try {\n      const url = PathResolver.customer.getProfile();\n      const response = await this.httpClient.get(url);\n      return handleResponse(response);\n    } catch (error: any) {\n      if (error instanceof CustomError) {\n        throw error;\n      }\n      throw createError();\n    }\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAS6C;IAAAA,aAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,aAAA;;;;;;;;;;;;;;;;;;;;;AAP7C,IAAAE,cAAA;AAAA;AAAA,CAAAF,aAAA,GAAAG,CAAA,OAAAC,OAAA;AAEA,IAAAC,iBAAA;AAAA;AAAA,CAAAL,aAAA,GAAAG,CAAA,OAAAC,OAAA;AAEA,IAAAE,gBAAA;AAAA;AAAA,CAAAN,aAAA,GAAAG,CAAA,OAAAC,OAAA;AAAsE,IAEzDG,wBAAwB;AAAA;AAAA,CAAAP,aAAA,GAAAG,CAAA;EAAA;EAAAH,aAAA,GAAAQ,CAAA;EACnC,SAAAD,yBAAoBE,UAAuB;IAAA;IAAAT,aAAA,GAAAQ,CAAA;IAAAR,aAAA,GAAAG,CAAA;IAAA,IAAAO,gBAAA,CAAAC,OAAA,QAAAJ,wBAAA;IAAA;IAAAP,aAAA,GAAAG,CAAA;IAAvB,KAAAM,UAAU,GAAVA,UAAU;EAAgB;EAAA;EAAAT,aAAA,GAAAG,CAAA;EAAC,WAAAS,aAAA,CAAAD,OAAA,EAAAJ,wBAAA;IAAAM,GAAA;IAAAC,KAAA;MAAA;MAAAd,aAAA,GAAAQ,CAAA;MAAA,IAAAO,WAAA;MAAA;MAAA,CAAAf,aAAA,GAAAG,CAAA,YAAAa,kBAAA,CAAAL,OAAA,EAE/C,aAAgB;QAAA;QAAAX,aAAA,GAAAQ,CAAA;QAAAR,aAAA,GAAAG,CAAA;QACd,IAAI;UACF,IAAMc,GAAG;UAAA;UAAA,CAAAjB,aAAA,GAAAG,CAAA,QAAGD,cAAA,CAAAgB,YAAY,CAACC,QAAQ,CAACC,UAAU,EAAE;UAC9C,IAAMC,QAAQ;UAAA;UAAA,CAAArB,aAAA,GAAAG,CAAA,cAAS,IAAI,CAACM,UAAU,CAACa,GAAG,CAACL,GAAG,CAAC;UAAA;UAAAjB,aAAA,GAAAG,CAAA;UAC/C,OAAO,IAAAE,iBAAA,CAAAkB,cAAc,EAACF,QAAQ,CAAC;QACjC,CAAC,CAAC,OAAOG,KAAU,EAAE;UAAA;UAAAxB,aAAA,GAAAG,CAAA;UACnB,IAAIqB,KAAK,YAAYlB,gBAAA,CAAAmB,WAAW,EAAE;YAAA;YAAAzB,aAAA,GAAA0B,CAAA;YAAA1B,aAAA,GAAAG,CAAA;YAChC,MAAMqB,KAAK;UACb;UAAA;UAAA;YAAAxB,aAAA,GAAA0B,CAAA;UAAA;UAAA1B,aAAA,GAAAG,CAAA;UACA,MAAM,IAAAG,gBAAA,CAAAqB,WAAW,GAAE;QACrB;MACF,CAAC;MAAA,SAXKP,UAAUA,CAAA;QAAA;QAAApB,aAAA,GAAAQ,CAAA;QAAAR,aAAA,GAAAG,CAAA;QAAA,OAAAY,WAAA,CAAAa,KAAA,OAAAC,SAAA;MAAA;MAAA;MAAA7B,aAAA,GAAAG,CAAA;MAAA,OAAViB,UAAU;IAAA;EAAA;AAAA;AAAA;AAAApB,aAAA,GAAAG,CAAA;AAHlB2B,OAAA,CAAAvB,wBAAA,GAAAA,wBAAA", "ignoreList": []}