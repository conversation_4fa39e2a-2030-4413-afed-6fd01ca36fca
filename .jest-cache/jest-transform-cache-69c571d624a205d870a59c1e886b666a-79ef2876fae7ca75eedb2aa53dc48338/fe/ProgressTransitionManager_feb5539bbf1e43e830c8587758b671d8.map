{"version": 3, "names": ["_interopRequireDefault", "require", "Object", "defineProperty", "exports", "value", "ProgressTransitionManager", "_classCallCheck2", "_createClass2", "_threads", "_core", "_reactNative", "_PlatformChecker", "_errors", "IS_ANDROID", "Platform", "OS", "default", "_sharedElementCount", "_event<PERSON><PERSON><PERSON>", "isRegistered", "onTransitionProgress", "onAppear", "onDisappear", "onSwipeDismiss", "key", "addProgressAnimation", "viewTag", "progressAnimation", "runOnUIImmediately", "global", "ProgressTransitionRegister", "registerEventHandlers", "removeProgressAnimation", "isUnmounting", "arguments", "length", "undefined", "unregisterEventHandlers", "<PERSON><PERSON><PERSON><PERSON>", "eventPrefix", "lastProgressValue", "registerEventHandler", "event", "progress", "frame", "onTransitionEnd", "onAndroidFinishTransitioning", "unregisterEventHandler", "createProgressTransitionRegister", "progressAnimations", "Map", "snapshots", "currentTransitions", "Set", "toRemove", "skipCleaning", "isTransitionRestart", "progressTransitionManager", "size", "has", "set", "add", "delete", "onTransitionStart", "snapshot", "get", "removeViews", "clear", "_notifyAboutEnd", "shouldBeUseWeb", "maybeThrowError", "isJest", "ReanimatedError", "Proxy"], "sources": ["../../../../src/layoutReanimation/sharedTransitions/ProgressTransitionManager.ts"], "sourcesContent": [null], "mappings": "AAAA,YAAY;;AAAA,IAAAA,sBAAA,GAAAC,OAAA;AAAAC,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,KAAA;AAAA;AAAAD,OAAA,CAAAE,yBAAA;AAAA,IAAAC,gBAAA,GAAAP,sBAAA,CAAAC,OAAA;AAAA,IAAAO,aAAA,GAAAR,sBAAA,CAAAC,OAAA;AACZ,IAAAQ,QAAA,GAAAR,OAAA;AAKA,IAAAS,KAAA,GAAAT,OAAA;AACA,IAAAU,YAAA,GAAAV,OAAA;AACA,IAAAW,gBAAA,GAAAX,OAAA;AACA,IAAAY,OAAA,GAAAZ,OAAA;AAUA,IAAMa,UAAU,GAAGC,qBAAQ,CAACC,EAAE,KAAK,SAAS;AAAA,IAE/BV,yBAAyB,GAAAF,OAAA,CAAAE,yBAAA;EAAA,SAAAA,0BAAA;IAAA,IAAAC,gBAAA,CAAAU,OAAA,QAAAX,yBAAA;IAAA,KAC5BY,mBAAmB,GAAG,CAAC;IAAA,KACvBC,aAAa,GAAG;MACtBC,YAAY,EAAE,KAAK;MACnBC,oBAAoB,EAAE,CAAC,CAAC;MACxBC,QAAQ,EAAE,CAAC,CAAC;MACZC,WAAW,EAAE,CAAC,CAAC;MACfC,cAAc,EAAE,CAAC;IACnB,CAAC;EAAA;EAAA,WAAAhB,aAAA,CAAAS,OAAA,EAAAX,yBAAA;IAAAmB,GAAA;IAAApB,KAAA,EAEM,SAAAqB,oBAAoBA,CACzBC,OAAe,EACfC,iBAAoC,EACpC;MACA,IAAAC,2BAAkB,EAAC,YAAM;QACvB,SAAS;;QACTC,MAAM,CAACC,0BAA0B,CAACL,oBAAoB,CACpDC,OAAO,EACPC,iBACF,CAAC;MACH,CAAC,CAAC,CAAC,CAAC;MAEJ,IAAI,CAACI,qBAAqB,CAAC,CAAC;IAC9B;EAAA;IAAAP,GAAA;IAAApB,KAAA,EAEO,SAAA4B,uBAAuBA,CAACN,OAAe,EAAuB;MAAA,IAArBO,YAAY,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,IAAI;MACjE,IAAI,CAACG,uBAAuB,CAAC,CAAC;MAC9B,IAAAT,2BAAkB,EAAC,YAAM;QACvB,SAAS;;QACTC,MAAM,CAACC,0BAA0B,CAACE,uBAAuB,CACvDN,OAAO,EACPO,YACF,CAAC;MACH,CAAC,CAAC,CAAC,CAAC;IACN;EAAA;IAAAT,GAAA;IAAApB,KAAA,EAEQ,SAAA2B,qBAAqBA,CAAA,EAAG;MAC9B,IAAI,CAACd,mBAAmB,EAAE;MAC1B,IAAMqB,YAAY,GAAG,IAAI,CAACpB,aAAa;MACvC,IAAI,CAACoB,YAAY,CAACnB,YAAY,EAAE;QAC9BmB,YAAY,CAACnB,YAAY,GAAG,IAAI;QAChC,IAAMoB,WAAW,GAAG1B,UAAU,GAAG,IAAI,GAAG,KAAK;QAC7C,IAAI2B,iBAAiB,GAAG,CAAC,CAAC;QAC1BF,YAAY,CAAClB,oBAAoB,GAAG,IAAAqB,0BAAoB,EACrD,UAAAC,KAA8B,EAAK;UAClC,SAAS;;UACT,IAAMC,QAAQ,GAAGD,KAAK,CAACC,QAAQ;UAC/B,IAAIA,QAAQ,KAAKH,iBAAiB,EAAE;YAIlC;UACF;UACAA,iBAAiB,GAAGG,QAAQ;UAC5Bd,MAAM,CAACC,0BAA0B,CAACc,KAAK,CAACD,QAAQ,CAAC;QACnD,CAAC,EACDJ,WAAW,GAAG,oBAChB,CAAC;QACDD,YAAY,CAACjB,QAAQ,GAAG,IAAAoB,0BAAoB,EAAC,YAAM;UACjD,SAAS;;UACTZ,MAAM,CAACC,0BAA0B,CAACe,eAAe,CAAC,CAAC;QACrD,CAAC,EAAEN,WAAW,GAAG,QAAQ,CAAC;QAE1B,IAAI1B,UAAU,EAAE;UAGdyB,YAAY,CAAChB,WAAW,GAAG,IAAAmB,0BAAoB,EAAC,YAAM;YACpD,SAAS;;YACTZ,MAAM,CAACC,0BAA0B,CAACgB,4BAA4B,CAAC,CAAC;UAClE,CAAC,EAAE,uBAAuB,CAAC;QAC7B,CAAC,MAAM,IAAIhC,qBAAQ,CAACC,EAAE,KAAK,KAAK,EAAE;UAEhCuB,YAAY,CAAChB,WAAW,GAAG,IAAAmB,0BAAoB,EAAC,YAAM;YACpD,SAAS;;YACTZ,MAAM,CAACC,0BAA0B,CAACe,eAAe,CAAC,IAAI,CAAC;UACzD,CAAC,EAAE,cAAc,CAAC;UAClBP,YAAY,CAACf,cAAc,GAAG,IAAAkB,0BAAoB,EAAC,YAAM;YACvD,SAAS;;YACTZ,MAAM,CAACC,0BAA0B,CAACe,eAAe,CAAC,CAAC;UACrD,CAAC,EAAE,kBAAkB,CAAC;QACxB;MACF;IACF;EAAA;IAAArB,GAAA;IAAApB,KAAA,EAEQ,SAAAiC,uBAAuBA,CAAA,EAAS;MACtC,IAAI,CAACpB,mBAAmB,EAAE;MAC1B,IAAI,IAAI,CAACA,mBAAmB,KAAK,CAAC,EAAE;QAClC,IAAMqB,YAAY,GAAG,IAAI,CAACpB,aAAa;QACvCoB,YAAY,CAACnB,YAAY,GAAG,KAAK;QACjC,IAAImB,YAAY,CAAClB,oBAAoB,KAAK,CAAC,CAAC,EAAE;UAC5C,IAAA2B,4BAAsB,EAACT,YAAY,CAAClB,oBAAoB,CAAC;UACzDkB,YAAY,CAAClB,oBAAoB,GAAG,CAAC,CAAC;QACxC;QACA,IAAIkB,YAAY,CAACjB,QAAQ,KAAK,CAAC,CAAC,EAAE;UAChC,IAAA0B,4BAAsB,EAACT,YAAY,CAACjB,QAAQ,CAAC;UAC7CiB,YAAY,CAACjB,QAAQ,GAAG,CAAC,CAAC;QAC5B;QACA,IAAIiB,YAAY,CAAChB,WAAW,KAAK,CAAC,CAAC,EAAE;UACnC,IAAAyB,4BAAsB,EAACT,YAAY,CAAChB,WAAW,CAAC;UAChDgB,YAAY,CAAChB,WAAW,GAAG,CAAC,CAAC;QAC/B;QACA,IAAIgB,YAAY,CAACf,cAAc,KAAK,CAAC,CAAC,EAAE;UACtC,IAAAwB,4BAAsB,EAACT,YAAY,CAACf,cAAc,CAAC;UACnDe,YAAY,CAACf,cAAc,GAAG,CAAC,CAAC;QAClC;MACF;IACF;EAAA;AAAA;AAGF,SAASyB,gCAAgCA,CAAA,EAAG;EAC1C,SAAS;;EACT,IAAMC,kBAAkB,GAAG,IAAIC,GAAG,CAA4B,CAAC;EAC/D,IAAMC,SAAS,GAAG,IAAID,GAAG,CAGvB,CAAC;EACH,IAAME,kBAAkB,GAAG,IAAIC,GAAG,CAAS,CAAC;EAC5C,IAAMC,QAAQ,GAAG,IAAID,GAAG,CAAS,CAAC;EAElC,IAAIE,YAAY,GAAG,KAAK;EACxB,IAAIC,mBAAmB,GAAG,KAAK;EAE/B,IAAMC,yBAAyB,GAAG;IAChChC,oBAAoB,EAAE,SAAtBA,oBAAoBA,CAClBC,OAAe,EACfC,iBAAoC,EACjC;MACH,IAAIyB,kBAAkB,CAACM,IAAI,GAAG,CAAC,IAAI,CAACT,kBAAkB,CAACU,GAAG,CAACjC,OAAO,CAAC,EAAE;QAEnE8B,mBAAmB,GAAG,CAAC3C,UAAU;MACnC;MACAoC,kBAAkB,CAACW,GAAG,CAAClC,OAAO,EAAEC,iBAAiB,CAAC;IACpD,CAAC;IACDK,uBAAuB,EAAE,SAAzBA,uBAAuBA,CAAGN,OAAe,EAAEO,YAAqB,EAAK;MACnE,IAAImB,kBAAkB,CAACM,IAAI,GAAG,CAAC,EAAE;QAE/BF,mBAAmB,GAAG,CAAC3C,UAAU;MACnC;MACA,IAAIoB,YAAY,EAAE;QAEhBqB,QAAQ,CAACO,GAAG,CAACnC,OAAO,CAAC;MACvB,CAAC,MAAM;QAELuB,kBAAkB,CAACa,MAAM,CAACpC,OAAO,CAAC;MACpC;IACF,CAAC;IACDqC,iBAAiB,EAAE,SAAnBA,iBAAiBA,CACfrC,OAAe,EACfsC,QAAmD,EAChD;MACHT,YAAY,GAAGC,mBAAmB;MAClCL,SAAS,CAACS,GAAG,CAAClC,OAAO,EAAEsC,QAAQ,CAAC;MAChCZ,kBAAkB,CAACS,GAAG,CAACnC,OAAO,CAAC;MAE/B+B,yBAAyB,CAACb,KAAK,CAAC,CAAC,CAAC;IACpC,CAAC;IACDA,KAAK,EAAG,SAARA,KAAKA,CAAGD,QAAgB,EAAK;MAC3B,KAAK,IAAMjB,OAAO,IAAI0B,kBAAkB,EAAE;QACxC,IAAMzB,iBAAiB,GAAGsB,kBAAkB,CAACgB,GAAG,CAACvC,OAAO,CAAC;QACzD,IAAI,CAACC,iBAAiB,EAAE;UACtB;QACF;QACA,IAAMqC,QAAQ,GAAGb,SAAS,CAACc,GAAG,CAC5BvC,OACF,CAAsC;QACtCC,iBAAiB,CAACD,OAAO,EAAEsC,QAAQ,EAAErB,QAAQ,CAAC;MAChD;IACF,CAAC;IACDG,4BAA4B,EAAE,SAA9BA,4BAA4BA,CAAA,EAAQ;MAClC,IAAIQ,QAAQ,CAACI,IAAI,GAAG,CAAC,EAAE;QAErBD,yBAAyB,CAACZ,eAAe,CAAC,CAAC;MAC7C;IACF,CAAC;IACDA,eAAe,EAAE,SAAjBA,eAAeA,CAAA,EAA2B;MAAA,IAAxBqB,WAAW,GAAAhC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,KAAK;MACnC,IAAIkB,kBAAkB,CAACM,IAAI,KAAK,CAAC,EAAE;QACjCJ,QAAQ,CAACa,KAAK,CAAC,CAAC;QAChB;MACF;MACA,IAAIZ,YAAY,EAAE;QAChBA,YAAY,GAAG,KAAK;QACpBC,mBAAmB,GAAG,KAAK;QAC3B;MACF;MACA,KAAK,IAAM9B,OAAO,IAAI0B,kBAAkB,EAAE;QACxCvB,MAAM,CAACuC,eAAe,CAAC1C,OAAO,EAAEwC,WAAW,CAAC;MAC9C;MACAd,kBAAkB,CAACe,KAAK,CAAC,CAAC;MAC1B,IAAIX,mBAAmB,EAAE;QAGvB;MACF;MACAL,SAAS,CAACgB,KAAK,CAAC,CAAC;MACjB,IAAIb,QAAQ,CAACI,IAAI,GAAG,CAAC,EAAE;QACrB,KAAK,IAAMhC,QAAO,IAAI4B,QAAQ,EAAE;UAC9BL,kBAAkB,CAACa,MAAM,CAACpC,QAAO,CAAC;UAClCG,MAAM,CAACuC,eAAe,CAAC1C,QAAO,EAAEwC,WAAW,CAAC;QAC9C;QACAZ,QAAQ,CAACa,KAAK,CAAC,CAAC;MAClB;IACF;EACF,CAAC;EACD,OAAOV,yBAAyB;AAClC;AAEA,IAAI,IAAAY,+BAAc,EAAC,CAAC,EAAE;EACpB,IAAMC,eAAe,GAAG,SAAlBA,eAAeA,CAAA,EAAS;IAG5B,IAAI,CAAC,IAAAC,uBAAM,EAAC,CAAC,EAAE;MACb,MAAM,IAAIC,uBAAe,CACvB,uEACF,CAAC;IACH;EACF,CAAC;EACD3C,MAAM,CAACC,0BAA0B,GAAG,IAAI2C,KAAK,CAC3C,CAAC,CAAC,EACF;IACER,GAAG,EAAEK,eAAe;IACpBV,GAAG,EAAE,SAALA,GAAGA,CAAA,EAAQ;MACTU,eAAe,CAAC,CAAC;MACjB,OAAO,KAAK;IACd;EACF,CACF,CAAC;AACH,CAAC,MAAM;EACL,IAAA1C,2BAAkB,EAAC,YAAM;IACvB,SAAS;;IACTC,MAAM,CAACC,0BAA0B,GAAGkB,gCAAgC,CAAC,CAAC;EACxE,CAAC,CAAC,CAAC,CAAC;AACN", "ignoreList": []}