{"version": 3, "names": ["cov_1kh3uus08j", "actualCoverage", "s", "MSBErrorCode", "f", "b", "exports"], "sources": ["/Users/<USER>/projects/react-native/payment-module/src/core/MSBErrorCode.ts"], "sourcesContent": ["export enum MSBErrorCode {\n  UNKNOWN_ERROR = 'MSB-9999', // Unknown error\n  NOT_VALID_DATA_FORMAT = 'MSB-9997', // jsonparse error, data wrong format....\n  EMPTY_DATA = 'MSB-9998', // empty data\n\n  A05 = 'A05',\n  FTES0009 = 'FTES-0009', // <PERSON><PERSON><PERSON><PERSON> tờ tuỳ thân\n  FTES0008 = 'FTES-0008', // Sinh trắc học\n  FTES0001 = 'FTES-0001', // G<PERSON>i truy vấn\n  BMS009 = 'BMS-0009', // Napas gián đoạn\n  BMS010 = 'BMS-0010', //\n  BMS014 = 'BMS-0010', //\n  BMS011 = 'BMS-0011', //\n  BMS0017 = 'BMS-0017',\n  FTES0006 = 'FTES-0006', // Citad gián đoạn\n  BPE00001 = 'BPE-00001', // Không tìm thấy hợ<PERSON> đồng\n\n  PIS0100 = 'PIS-0100', // General error from ePayment\n  PIS0101 = 'PIS-0101', // Bill has no debt\n  PIS0102 = 'PIS-0102', // Bill not found or no debt\n  PIS0103 = 'PIS-0103', // Request format is invalid\n  PIS0104 = 'PIS-0104', // Request field is invalid\n  PIS0105 = 'PIS-0105', // Error calling partner\n  PIS0106 = 'PIS-0106', // Error connecting to partner\n  PIS0107 = 'PIS-0107', // ePayment timeout\n  CME0001 = 'CME-0001', // Internal Server Error\n  CME0002 = 'CME-0002', // Cif in token not found\n  CME0003 = 'CME-0003', // Username in token not found\n  CME0016 = 'CME-0016', // Category code not found\n  CME0017 = 'CME-0017', // Internal User not found\n  CME0018 = 'CME-0018', // Legal entity not found\n  CME0019 = 'CME-0019', // Service agreement not found\n  BPE0015 = 'BPE-0015', // Danh sách bill không khớp với hệ thống\n  BPE0010 = 'BPE-0010', // The entity is not valid\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IASE;IAAAA,cAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,cAAA;AAAAA,cAAA,GAAAE,CAAA;;;;;;;AATF,IAAYC,YAkCX;AAAA;AAAAH,cAAA,GAAAE,CAAA;AAlCD,WAAYC,YAAY;EAAA;EAAAH,cAAA,GAAAI,CAAA;EAAAJ,cAAA,GAAAE,CAAA;EACtBC,YAAA,8BAA0B;EAAA;EAAAH,cAAA,GAAAE,CAAA;EAC1BC,YAAA,sCAAkC;EAAA;EAAAH,cAAA,GAAAE,CAAA;EAClCC,YAAA,2BAAuB;EAAA;EAAAH,cAAA,GAAAE,CAAA;EAEvBC,YAAA,eAAW;EAAA;EAAAH,cAAA,GAAAE,CAAA;EACXC,YAAA,0BAAsB;EAAA;EAAAH,cAAA,GAAAE,CAAA;EACtBC,YAAA,0BAAsB;EAAA;EAAAH,cAAA,GAAAE,CAAA;EACtBC,YAAA,0BAAsB;EAAA;EAAAH,cAAA,GAAAE,CAAA;EACtBC,YAAA,uBAAmB;EAAA;EAAAH,cAAA,GAAAE,CAAA;EACnBC,YAAA,uBAAmB;EAAA;EAAAH,cAAA,GAAAE,CAAA;EACnBC,YAAA,uBAAmB;EAAA;EAAAH,cAAA,GAAAE,CAAA;EACnBC,YAAA,uBAAmB;EAAA;EAAAH,cAAA,GAAAE,CAAA;EACnBC,YAAA,wBAAoB;EAAA;EAAAH,cAAA,GAAAE,CAAA;EACpBC,YAAA,0BAAsB;EAAA;EAAAH,cAAA,GAAAE,CAAA;EACtBC,YAAA,0BAAsB;EAAA;EAAAH,cAAA,GAAAE,CAAA;EAEtBC,YAAA,wBAAoB;EAAA;EAAAH,cAAA,GAAAE,CAAA;EACpBC,YAAA,wBAAoB;EAAA;EAAAH,cAAA,GAAAE,CAAA;EACpBC,YAAA,wBAAoB;EAAA;EAAAH,cAAA,GAAAE,CAAA;EACpBC,YAAA,wBAAoB;EAAA;EAAAH,cAAA,GAAAE,CAAA;EACpBC,YAAA,wBAAoB;EAAA;EAAAH,cAAA,GAAAE,CAAA;EACpBC,YAAA,wBAAoB;EAAA;EAAAH,cAAA,GAAAE,CAAA;EACpBC,YAAA,wBAAoB;EAAA;EAAAH,cAAA,GAAAE,CAAA;EACpBC,YAAA,wBAAoB;EAAA;EAAAH,cAAA,GAAAE,CAAA;EACpBC,YAAA,wBAAoB;EAAA;EAAAH,cAAA,GAAAE,CAAA;EACpBC,YAAA,wBAAoB;EAAA;EAAAH,cAAA,GAAAE,CAAA;EACpBC,YAAA,wBAAoB;EAAA;EAAAH,cAAA,GAAAE,CAAA;EACpBC,YAAA,wBAAoB;EAAA;EAAAH,cAAA,GAAAE,CAAA;EACpBC,YAAA,wBAAoB;EAAA;EAAAH,cAAA,GAAAE,CAAA;EACpBC,YAAA,wBAAoB;EAAA;EAAAH,cAAA,GAAAE,CAAA;EACpBC,YAAA,wBAAoB;EAAA;EAAAH,cAAA,GAAAE,CAAA;EACpBC,YAAA,wBAAoB;EAAA;EAAAH,cAAA,GAAAE,CAAA;EACpBC,YAAA,wBAAoB;AACtB,CAAC;AAlCW;AAAA,CAAAH,cAAA,GAAAK,CAAA,UAAAF,YAAY;AAAA;AAAA,CAAAH,cAAA,GAAAK,CAAA,UAAAC,OAAA,CAAAH,YAAA,GAAZA,YAAY", "ignoreList": []}