1af95d51a423fe3344739a3b5b6023e1
"use strict";

/* istanbul ignore next */
function cov_2aeucap138() {
  var path = "/Users/<USER>/projects/react-native/payment-module/src/components/saved-tab/types.ts";
  var hash = "9fed0a2d3420920142e22cc5bea97bb73374a83c";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/projects/react-native/payment-module/src/components/saved-tab/types.ts",
    statementMap: {
      "0": {
        start: {
          line: 3,
          column: 0
        },
        end: {
          line: 5,
          column: 3
        }
      }
    },
    fnMap: {},
    branchMap: {},
    s: {
      "0": 0
    },
    f: {},
    b: {},
    inputSourceMap: {
      version: 3,
      file: undefined,
      names: [],
      sourceRoot: undefined,
      sources: ["/Users/<USER>/projects/react-native/payment-module/src/components/saved-tab/types.ts"],
      sourcesContent: ["import {ViewStyle} from 'react-native';\nimport {IBillContact} from '../../domain/entities/IBillContact';\n\nexport type SavedTabProps = {\n  style?: ViewStyle;\n  contactList: IBillContact[];\n  onSelect?: (contactInfo?: IBillContact) => void;\n};\n"],
      mappings: "",
      ignoreList: []
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "9fed0a2d3420920142e22cc5bea97bb73374a83c"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_2aeucap138 = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_2aeucap138();
cov_2aeucap138().s[0]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6W10sInNvdXJjZXMiOlsiL1VzZXJzL3NvbnNhbXNldC9wcm9qZWN0cy9yZWFjdC1uYXRpdmUvcGF5bWVudC1tb2R1bGUvc3JjL2NvbXBvbmVudHMvc2F2ZWQtdGFiL3R5cGVzLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7Vmlld1N0eWxlfSBmcm9tICdyZWFjdC1uYXRpdmUnO1xuaW1wb3J0IHtJQmlsbENvbnRhY3R9IGZyb20gJy4uLy4uL2RvbWFpbi9lbnRpdGllcy9JQmlsbENvbnRhY3QnO1xuXG5leHBvcnQgdHlwZSBTYXZlZFRhYlByb3BzID0ge1xuICBzdHlsZT86IFZpZXdTdHlsZTtcbiAgY29udGFjdExpc3Q6IElCaWxsQ29udGFjdFtdO1xuICBvblNlbGVjdD86IChjb250YWN0SW5mbz86IElCaWxsQ29udGFjdCkgPT4gdm9pZDtcbn07XG4iXSwibWFwcGluZ3MiOiIiLCJpZ25vcmVMaXN0IjpbXX0=