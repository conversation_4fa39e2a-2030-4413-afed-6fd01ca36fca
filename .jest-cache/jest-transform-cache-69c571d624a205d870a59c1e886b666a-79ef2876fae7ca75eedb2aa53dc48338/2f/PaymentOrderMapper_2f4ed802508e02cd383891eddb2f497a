c7a8a514f91b4e4f4376657f9cf7eb00
"use strict";

/* istanbul ignore next */
function cov_26nhs05b2k() {
  var path = "/Users/<USER>/projects/react-native/payment-module/src/data/mappers/payment-order/PaymentOrderMapper.ts";
  var hash = "d837cea7b00c91c5a20c575cf16e12e982d33777";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/projects/react-native/payment-module/src/data/mappers/payment-order/PaymentOrderMapper.ts",
    statementMap: {
      "0": {
        start: {
          line: 3,
          column: 0
        },
        end: {
          line: 5,
          column: 3
        }
      },
      "1": {
        start: {
          line: 6,
          column: 0
        },
        end: {
          line: 6,
          column: 72
        }
      },
      "2": {
        start: {
          line: 7,
          column: 26
        },
        end: {
          line: 7,
          column: 93
        }
      },
      "3": {
        start: {
          line: 9,
          column: 2
        },
        end: {
          line: 9,
          column: 83
        }
      }
    },
    fnMap: {
      "0": {
        name: "mapPaymentOrderResponseToModel",
        decl: {
          start: {
            line: 8,
            column: 9
          },
          end: {
            line: 8,
            column: 39
          }
        },
        loc: {
          start: {
            line: 8,
            column: 50
          },
          end: {
            line: 10,
            column: 1
          }
        },
        line: 8
      }
    },
    branchMap: {},
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0
    },
    f: {
      "0": 0
    },
    b: {},
    inputSourceMap: {
      version: 3,
      file: undefined,
      names: ["exports", "mapPaymentOrderResponseToModel", "PaymentOrderModel_1", "require", "response", "PaymentOrderModel", "status", "data"],
      sourceRoot: undefined,
      sources: ["/Users/<USER>/projects/react-native/payment-module/src/data/mappers/payment-order/PaymentOrderMapper.ts"],
      sourcesContent: ["import {PaymentOrderResponse} from '../../models/payment-order/PaymentOrderResponse';\nimport {PaymentOrderModel} from '../../../domain/entities/payment-order/PaymentOrderModel';\n\nexport function mapPaymentOrderResponseToModel(response: PaymentOrderResponse): PaymentOrderModel {\n  return new PaymentOrderModel(response.status, response.data);\n}\n"],
      mappings: ";;;;;AAGAA,OAAA,CAAAC,8BAAA,GAAAA,8BAAA;AAFA,IAAAC,mBAAA,GAAAC,OAAA;AAEA,SAAgBF,8BAA8BA,CAACG,QAA8B;EAC3E,OAAO,IAAIF,mBAAA,CAAAG,iBAAiB,CAACD,QAAQ,CAACE,MAAM,EAAEF,QAAQ,CAACG,IAAI,CAAC;AAC9D",
      ignoreList: []
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "d837cea7b00c91c5a20c575cf16e12e982d33777"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_26nhs05b2k = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_26nhs05b2k();
cov_26nhs05b2k().s[0]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_26nhs05b2k().s[1]++;
exports.mapPaymentOrderResponseToModel = mapPaymentOrderResponseToModel;
var PaymentOrderModel_1 =
/* istanbul ignore next */
(cov_26nhs05b2k().s[2]++, require("../../../domain/entities/payment-order/PaymentOrderModel"));
function mapPaymentOrderResponseToModel(response) {
  /* istanbul ignore next */
  cov_26nhs05b2k().f[0]++;
  cov_26nhs05b2k().s[3]++;
  return new PaymentOrderModel_1.PaymentOrderModel(response.status, response.data);
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJleHBvcnRzIiwibWFwUGF5bWVudE9yZGVyUmVzcG9uc2VUb01vZGVsIiwiUGF5bWVudE9yZGVyTW9kZWxfMSIsImNvdl8yNm5oczA1YjJrIiwicyIsInJlcXVpcmUiLCJyZXNwb25zZSIsImYiLCJQYXltZW50T3JkZXJNb2RlbCIsInN0YXR1cyIsImRhdGEiXSwic291cmNlcyI6WyIvVXNlcnMvc29uc2Ftc2V0L3Byb2plY3RzL3JlYWN0LW5hdGl2ZS9wYXltZW50LW1vZHVsZS9zcmMvZGF0YS9tYXBwZXJzL3BheW1lbnQtb3JkZXIvUGF5bWVudE9yZGVyTWFwcGVyLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7UGF5bWVudE9yZGVyUmVzcG9uc2V9IGZyb20gJy4uLy4uL21vZGVscy9wYXltZW50LW9yZGVyL1BheW1lbnRPcmRlclJlc3BvbnNlJztcbmltcG9ydCB7UGF5bWVudE9yZGVyTW9kZWx9IGZyb20gJy4uLy4uLy4uL2RvbWFpbi9lbnRpdGllcy9wYXltZW50LW9yZGVyL1BheW1lbnRPcmRlck1vZGVsJztcblxuZXhwb3J0IGZ1bmN0aW9uIG1hcFBheW1lbnRPcmRlclJlc3BvbnNlVG9Nb2RlbChyZXNwb25zZTogUGF5bWVudE9yZGVyUmVzcG9uc2UpOiBQYXltZW50T3JkZXJNb2RlbCB7XG4gIHJldHVybiBuZXcgUGF5bWVudE9yZGVyTW9kZWwocmVzcG9uc2Uuc3RhdHVzLCByZXNwb25zZS5kYXRhKTtcbn1cbiJdLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFHQUEsT0FBQSxDQUFBQyw4QkFBQSxHQUFBQSw4QkFBQTtBQUZBLElBQUFDLG1CQUFBO0FBQUE7QUFBQSxDQUFBQyxjQUFBLEdBQUFDLENBQUEsT0FBQUMsT0FBQTtBQUVBLFNBQWdCSiw4QkFBOEJBLENBQUNLLFFBQThCO0VBQUE7RUFBQUgsY0FBQSxHQUFBSSxDQUFBO0VBQUFKLGNBQUEsR0FBQUMsQ0FBQTtFQUMzRSxPQUFPLElBQUlGLG1CQUFBLENBQUFNLGlCQUFpQixDQUFDRixRQUFRLENBQUNHLE1BQU0sRUFBRUgsUUFBUSxDQUFDSSxJQUFJLENBQUM7QUFDOUQiLCJpZ25vcmVMaXN0IjpbXX0=