9dfe53f770d3b7c50cdc59bb61d5ebc1
"use strict";

/* istanbul ignore next */
function cov_15by71h1xl() {
  var path = "/Users/<USER>/projects/react-native/payment-module/src/domain/entities/source-account-list/SourceAccountListModel.ts";
  var hash = "e307fe993402f9ad9cd82cb61219d2c94210975d";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/projects/react-native/payment-module/src/domain/entities/source-account-list/SourceAccountListModel.ts",
    statementMap: {
      "0": {
        start: {
          line: 3,
          column: 0
        },
        end: {
          line: 5,
          column: 3
        }
      }
    },
    fnMap: {},
    branchMap: {},
    s: {
      "0": 0
    },
    f: {},
    b: {},
    inputSourceMap: {
      version: 3,
      file: undefined,
      names: [],
      sourceRoot: undefined,
      sources: ["/Users/<USER>/projects/react-native/payment-module/src/domain/entities/source-account-list/SourceAccountListModel.ts"],
      sourcesContent: ["export interface SourceAccountListModel {\n  totalCount: number;\n  data: SourceAccountModel[];\n}\n\nexport interface SourceAccountUserPreferencesModel {\n  arrangementId?: string;\n  alias?: string;\n  visible?: boolean | null;\n  favorite?: boolean;\n  additions?: any | null;\n}\n\nexport interface SourceAccountProductKindModel {\n  externalKindId: string;\n  kindName: string;\n  kindUri: string;\n  expectsChildren: boolean;\n  additions: any | null;\n}\n\nexport interface SourceAccountProductModel {\n  id: string;\n  translations: any[];\n  additions: any | null;\n  externalId: string;\n  externalTypeId: string | null;\n  typeName: string;\n  productKind: SourceAccountProductKindModel;\n}\n\nexport interface SourceAccountModel {\n  id: string;\n  productKindName: string;\n  legalEntityIds: string[];\n  productId: string;\n  productTypeName: string;\n  externalProductId: string;\n  externalArrangementId: string;\n  userPreferences?: SourceAccountUserPreferencesModel;\n  product: SourceAccountProductModel;\n  state?: any | null;\n  parentId?: string | null;\n  subscriptions?: any | null;\n  isDefault: string;\n  cifNo: string;\n  virtualAccountInfos?: any[];\n  additions?: any | null;\n  name: string;\n  bookedBalance?: number | null;\n  availableBalance?: number | null;\n  creditLimit?: number | null;\n  currency: string;\n  externalTransferAllowed?: boolean | null;\n  urgentTransferAllowed?: boolean | null;\n  accountOpeningDate?: string | null;\n  accountHolderNames?: string | null;\n  bankAlias?: string;\n  BBAN?: string;\n  IBAN?: string | null;\n  BIC?: string | null;\n  amountQR?: string | null;\n  contentQR?: string | null;\n}\n"],
      mappings: "",
      ignoreList: []
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "e307fe993402f9ad9cd82cb61219d2c94210975d"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_15by71h1xl = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_15by71h1xl();
cov_15by71h1xl().s[0]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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