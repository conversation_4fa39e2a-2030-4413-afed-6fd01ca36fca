{"version": 3, "names": [], "sources": ["/Users/<USER>/projects/react-native/payment-module/src/domain/entities/source-account-list/SourceAccountListModel.ts"], "sourcesContent": ["export interface SourceAccountListModel {\n  totalCount: number;\n  data: SourceAccountModel[];\n}\n\nexport interface SourceAccountUserPreferencesModel {\n  arrangementId?: string;\n  alias?: string;\n  visible?: boolean | null;\n  favorite?: boolean;\n  additions?: any | null;\n}\n\nexport interface SourceAccountProductKindModel {\n  externalKindId: string;\n  kindName: string;\n  kindUri: string;\n  expectsChildren: boolean;\n  additions: any | null;\n}\n\nexport interface SourceAccountProductModel {\n  id: string;\n  translations: any[];\n  additions: any | null;\n  externalId: string;\n  externalTypeId: string | null;\n  typeName: string;\n  productKind: SourceAccountProductKindModel;\n}\n\nexport interface SourceAccountModel {\n  id: string;\n  productKindName: string;\n  legalEntityIds: string[];\n  productId: string;\n  productTypeName: string;\n  externalProductId: string;\n  externalArrangementId: string;\n  userPreferences?: SourceAccountUserPreferencesModel;\n  product: SourceAccountProductModel;\n  state?: any | null;\n  parentId?: string | null;\n  subscriptions?: any | null;\n  isDefault: string;\n  cifNo: string;\n  virtualAccountInfos?: any[];\n  additions?: any | null;\n  name: string;\n  bookedBalance?: number | null;\n  availableBalance?: number | null;\n  creditLimit?: number | null;\n  currency: string;\n  externalTransferAllowed?: boolean | null;\n  urgentTransferAllowed?: boolean | null;\n  accountOpeningDate?: string | null;\n  accountHolderNames?: string | null;\n  bankAlias?: string;\n  BBAN?: string;\n  IBAN?: string | null;\n  BIC?: string | null;\n  amountQR?: string | null;\n  contentQR?: string | null;\n}\n"], "mappings": "", "ignoreList": []}