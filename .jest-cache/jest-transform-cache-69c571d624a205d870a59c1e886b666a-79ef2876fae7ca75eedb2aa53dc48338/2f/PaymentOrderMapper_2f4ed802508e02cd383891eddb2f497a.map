{"version": 3, "names": ["exports", "mapPaymentOrderResponseToModel", "PaymentOrderModel_1", "cov_26nhs05b2k", "s", "require", "response", "f", "PaymentOrderModel", "status", "data"], "sources": ["/Users/<USER>/projects/react-native/payment-module/src/data/mappers/payment-order/PaymentOrderMapper.ts"], "sourcesContent": ["import {PaymentOrderResponse} from '../../models/payment-order/PaymentOrderResponse';\nimport {PaymentOrderModel} from '../../../domain/entities/payment-order/PaymentOrderModel';\n\nexport function mapPaymentOrderResponseToModel(response: PaymentOrderResponse): PaymentOrderModel {\n  return new PaymentOrderModel(response.status, response.data);\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGAA,OAAA,CAAAC,8BAAA,GAAAA,8BAAA;AAFA,IAAAC,mBAAA;AAAA;AAAA,CAAAC,cAAA,GAAAC,CAAA,OAAAC,OAAA;AAEA,SAAgBJ,8BAA8BA,CAACK,QAA8B;EAAA;EAAAH,cAAA,GAAAI,CAAA;EAAAJ,cAAA,GAAAC,CAAA;EAC3E,OAAO,IAAIF,mBAAA,CAAAM,iBAAiB,CAACF,QAAQ,CAACG,MAAM,EAAEH,QAAQ,CAACI,IAAI,CAAC;AAC9D", "ignoreList": []}