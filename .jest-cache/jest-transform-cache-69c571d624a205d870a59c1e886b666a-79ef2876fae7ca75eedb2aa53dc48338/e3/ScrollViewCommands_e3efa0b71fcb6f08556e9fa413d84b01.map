{"version": 3, "names": ["_codegenNativeCommands", "_interopRequireDefault", "require", "React", "_interopRequireWildcard", "_getRequireWildcardCache", "e", "WeakMap", "r", "t", "__esModule", "default", "has", "get", "n", "__proto__", "a", "Object", "defineProperty", "getOwnPropertyDescriptor", "u", "hasOwnProperty", "call", "i", "set", "_default", "exports", "codegenNativeCommands", "supportedCommands"], "sources": ["ScrollViewCommands.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @format\n * @flow strict-local\n */\n\nimport type {HostComponent} from '../../Renderer/shims/ReactNativeTypes';\nimport type {Double} from '../../Types/CodegenTypes';\n\nimport codegenNativeCommands from '../../Utilities/codegenNativeCommands';\nimport * as React from 'react';\n\ntype ScrollViewNativeComponentType = HostComponent<{...}>;\ninterface NativeCommands {\n  +flashScrollIndicators: (\n    viewRef: React.ElementRef<ScrollViewNativeComponentType>,\n  ) => void;\n  +scrollTo: (\n    viewRef: React.ElementRef<ScrollViewNativeComponentType>,\n    x: Double,\n    y: Double,\n    animated: boolean,\n  ) => void;\n  +scrollToEnd: (\n    viewRef: React.ElementRef<ScrollViewNativeComponentType>,\n    animated: boolean,\n  ) => void;\n  +zoomToRect: (\n    viewRef: React.ElementRef<ScrollViewNativeComponentType>,\n    rect: {|\n      x: Double,\n      y: Double,\n      width: Double,\n      height: Double,\n      animated?: boolean,\n    |},\n    animated?: boolean,\n  ) => void;\n}\n\nexport default (codegenNativeCommands<NativeCommands>({\n  supportedCommands: [\n    'flashScrollIndicators',\n    'scrollTo',\n    'scrollToEnd',\n    'zoomToRect',\n  ],\n}): NativeCommands);\n"], "mappings": ";;;;;AAaA,IAAAA,sBAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,KAAA,GAAAC,uBAAA,CAAAF,OAAA;AAA+B,SAAAG,yBAAAC,CAAA,6BAAAC,OAAA,mBAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,yBAAAC,CAAA,WAAAA,CAAA,GAAAG,CAAA,GAAAD,CAAA,KAAAF,CAAA;AAAA,SAAAF,wBAAAE,CAAA,EAAAE,CAAA,SAAAA,CAAA,IAAAF,CAAA,IAAAA,CAAA,CAAAI,UAAA,SAAAJ,CAAA,eAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,WAAAK,OAAA,EAAAL,CAAA,QAAAG,CAAA,GAAAJ,wBAAA,CAAAG,CAAA,OAAAC,CAAA,IAAAA,CAAA,CAAAG,GAAA,CAAAN,CAAA,UAAAG,CAAA,CAAAI,GAAA,CAAAP,CAAA,OAAAQ,CAAA,KAAAC,SAAA,UAAAC,CAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,CAAA,IAAAd,CAAA,oBAAAc,CAAA,OAAAC,cAAA,CAAAC,IAAA,CAAAhB,CAAA,EAAAc,CAAA,SAAAG,CAAA,GAAAP,CAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAb,CAAA,EAAAc,CAAA,UAAAG,CAAA,KAAAA,CAAA,CAAAV,GAAA,IAAAU,CAAA,CAAAC,GAAA,IAAAP,MAAA,CAAAC,cAAA,CAAAJ,CAAA,EAAAM,CAAA,EAAAG,CAAA,IAAAT,CAAA,CAAAM,CAAA,IAAAd,CAAA,CAAAc,CAAA,YAAAN,CAAA,CAAAH,OAAA,GAAAL,CAAA,EAAAG,CAAA,IAAAA,CAAA,CAAAe,GAAA,CAAAlB,CAAA,EAAAQ,CAAA,GAAAA,CAAA;AAAA,IAAAW,QAAA,GAAAC,OAAA,CAAAf,OAAA,GA8Bf,IAAAgB,8BAAqB,EAAiB;EACpDC,iBAAiB,EAAE,CACjB,uBAAuB,EACvB,UAAU,EACV,aAAa,EACb,YAAY;AAEhB,CAAC,CAAC", "ignoreList": []}