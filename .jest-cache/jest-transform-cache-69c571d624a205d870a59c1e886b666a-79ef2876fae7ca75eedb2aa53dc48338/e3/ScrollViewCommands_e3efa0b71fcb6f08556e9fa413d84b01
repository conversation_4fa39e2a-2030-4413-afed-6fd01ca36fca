c696eb09127f016e85e1735a7095516e
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _codegenNativeCommands = _interopRequireDefault(require("../../Utilities/codegenNativeCommands"));
var React = _interopRequireWildcard(require("react"));
function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
var _default = exports.default = (0, _codegenNativeCommands.default)({
  supportedCommands: ['flashScrollIndicators', 'scrollTo', 'scrollToEnd', 'zoomToRect']
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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