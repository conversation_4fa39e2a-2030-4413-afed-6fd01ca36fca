60d2099e1744cbd10bf889c0b2a2b4eb
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
var _objectWithoutProperties2 = _interopRequireDefault(require("@babel/runtime/helpers/objectWithoutProperties"));
var _classCallCheck2 = _interopRequireDefault(require("@babel/runtime/helpers/classCallCheck"));
var _createClass2 = _interopRequireDefault(require("@babel/runtime/helpers/createClass"));
var _possibleConstructorReturn2 = _interopRequireDefault(require("@babel/runtime/helpers/possibleConstructorReturn"));
var _getPrototypeOf2 = _interopRequireDefault(require("@babel/runtime/helpers/getPrototypeOf"));
var _inherits2 = _interopRequireDefault(require("@babel/runtime/helpers/inherits"));
var ReactNativeFeatureFlags = _interopRequireWildcard(require("../../src/private/featureflags/ReactNativeFeatureFlags"));
var _virtualizedLists = require("@react-native/virtualized-lists");
var _memoizeOne = _interopRequireDefault(require("memoize-one"));
var _jsxRuntime = require("react/jsx-runtime");
var _excluded = ["numColumns", "columnWrapperStyle", "removeClippedSubviews", "strictMode"];
function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
function _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }
function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }
var View = require('../Components/View/View');
var StyleSheet = require('../StyleSheet/StyleSheet');
var deepDiffer = require('../Utilities/differ/deepDiffer');
var Platform = require('../Utilities/Platform');
var invariant = require('invariant');
var React = require('react');
function removeClippedSubviewsOrDefault(removeClippedSubviews) {
  if (ReactNativeFeatureFlags.shouldUseRemoveClippedSubviewsAsDefaultOnIOS()) {
    return removeClippedSubviews != null ? removeClippedSubviews : true;
  } else {
    return removeClippedSubviews != null ? removeClippedSubviews : Platform.OS === 'android';
  }
}
function numColumnsOrDefault(numColumns) {
  return numColumns != null ? numColumns : 1;
}
function isArrayLike(data) {
  return typeof Object(data).length === 'number';
}
var FlatList = function (_React$PureComponent) {
  function FlatList(_props) {
    var _this;
    (0, _classCallCheck2.default)(this, FlatList);
    _this = _callSuper(this, FlatList, [_props]);
    _this._virtualizedListPairs = [];
    _this._captureRef = function (ref) {
      _this._listRef = ref;
    };
    _this._getItem = function (data, index) {
      var numColumns = numColumnsOrDefault(_this.props.numColumns);
      if (numColumns > 1) {
        var ret = [];
        for (var kk = 0; kk < numColumns; kk++) {
          var itemIndex = index * numColumns + kk;
          if (itemIndex < data.length) {
            var _item = data[itemIndex];
            ret.push(_item);
          }
        }
        return ret;
      } else {
        return data[index];
      }
    };
    _this._getItemCount = function (data) {
      if (data != null && isArrayLike(data)) {
        var numColumns = numColumnsOrDefault(_this.props.numColumns);
        return numColumns > 1 ? Math.ceil(data.length / numColumns) : data.length;
      } else {
        return 0;
      }
    };
    _this._keyExtractor = function (items, index) {
      var _this$props$keyExtrac;
      var numColumns = numColumnsOrDefault(_this.props.numColumns);
      var keyExtractor = (_this$props$keyExtrac = _this.props.keyExtractor) != null ? _this$props$keyExtrac : _virtualizedLists.keyExtractor;
      if (numColumns > 1) {
        invariant(Array.isArray(items), 'FlatList: Encountered internal consistency error, expected each item to consist of an ' + 'array with 1-%s columns; instead, received a single item.', numColumns);
        return items.map(function (item, kk) {
          return keyExtractor(item, index * numColumns + kk);
        }).join(':');
      }
      return keyExtractor(items, index);
    };
    _this._renderer = function (ListItemComponent, renderItem, columnWrapperStyle, numColumns, extraData) {
      var cols = numColumnsOrDefault(numColumns);
      var render = function render(props) {
        if (ListItemComponent) {
          return (0, _jsxRuntime.jsx)(ListItemComponent, Object.assign({}, props));
        } else if (renderItem) {
          return renderItem(props);
        } else {
          return null;
        }
      };
      var renderProp = function renderProp(info) {
        if (cols > 1) {
          var _item2 = info.item,
            _index = info.index;
          invariant(Array.isArray(_item2), 'Expected array of items with numColumns > 1');
          return (0, _jsxRuntime.jsx)(View, {
            style: StyleSheet.compose(styles.row, columnWrapperStyle),
            children: _item2.map(function (it, kk) {
              var element = render({
                item: it,
                index: _index * cols + kk,
                separators: info.separators
              });
              return element != null ? (0, _jsxRuntime.jsx)(React.Fragment, {
                children: element
              }, kk) : null;
            })
          });
        } else {
          return render(info);
        }
      };
      return ListItemComponent ? {
        ListItemComponent: renderProp
      } : {
        renderItem: renderProp
      };
    };
    _this._memoizedRenderer = (0, _memoizeOne.default)(_this._renderer);
    _this._checkProps(_this.props);
    if (_this.props.viewabilityConfigCallbackPairs) {
      _this._virtualizedListPairs = _this.props.viewabilityConfigCallbackPairs.map(function (pair) {
        return {
          viewabilityConfig: pair.viewabilityConfig,
          onViewableItemsChanged: _this._createOnViewableItemsChanged(pair.onViewableItemsChanged)
        };
      });
    } else if (_this.props.onViewableItemsChanged) {
      _this._virtualizedListPairs.push({
        viewabilityConfig: _this.props.viewabilityConfig,
        onViewableItemsChanged: _this._createOnViewableItemsChanged(function () {
          var _this$props;
          invariant(_this.props.onViewableItemsChanged, 'Changing the nullability of onViewableItemsChanged is not supported. ' + 'Once a function or null is supplied that cannot be changed.');
          return (_this$props = _this.props).onViewableItemsChanged.apply(_this$props, arguments);
        })
      });
    }
    return _this;
  }
  (0, _inherits2.default)(FlatList, _React$PureComponent);
  return (0, _createClass2.default)(FlatList, [{
    key: "scrollToEnd",
    value: function scrollToEnd(params) {
      if (this._listRef) {
        this._listRef.scrollToEnd(params);
      }
    }
  }, {
    key: "scrollToIndex",
    value: function scrollToIndex(params) {
      if (this._listRef) {
        this._listRef.scrollToIndex(params);
      }
    }
  }, {
    key: "scrollToItem",
    value: function scrollToItem(params) {
      if (this._listRef) {
        this._listRef.scrollToItem(params);
      }
    }
  }, {
    key: "scrollToOffset",
    value: function scrollToOffset(params) {
      if (this._listRef) {
        this._listRef.scrollToOffset(params);
      }
    }
  }, {
    key: "recordInteraction",
    value: function recordInteraction() {
      if (this._listRef) {
        this._listRef.recordInteraction();
      }
    }
  }, {
    key: "flashScrollIndicators",
    value: function flashScrollIndicators() {
      if (this._listRef) {
        this._listRef.flashScrollIndicators();
      }
    }
  }, {
    key: "getScrollResponder",
    value: function getScrollResponder() {
      if (this._listRef) {
        return this._listRef.getScrollResponder();
      }
    }
  }, {
    key: "getNativeScrollRef",
    value: function getNativeScrollRef() {
      if (this._listRef) {
        return this._listRef.getScrollRef();
      }
    }
  }, {
    key: "getScrollableNode",
    value: function getScrollableNode() {
      if (this._listRef) {
        return this._listRef.getScrollableNode();
      }
    }
  }, {
    key: "setNativeProps",
    value: function setNativeProps(props) {
      if (this._listRef) {
        this._listRef.setNativeProps(props);
      }
    }
  }, {
    key: "componentDidUpdate",
    value: function componentDidUpdate(prevProps) {
      invariant(prevProps.numColumns === this.props.numColumns, 'Changing numColumns on the fly is not supported. Change the key prop on FlatList when ' + 'changing the number of columns to force a fresh render of the component.');
      invariant(prevProps.onViewableItemsChanged == null === (this.props.onViewableItemsChanged == null), 'Changing onViewableItemsChanged nullability on the fly is not supported');
      invariant(!deepDiffer(prevProps.viewabilityConfig, this.props.viewabilityConfig), 'Changing viewabilityConfig on the fly is not supported');
      invariant(prevProps.viewabilityConfigCallbackPairs === this.props.viewabilityConfigCallbackPairs, 'Changing viewabilityConfigCallbackPairs on the fly is not supported');
      this._checkProps(this.props);
    }
  }, {
    key: "_checkProps",
    value: function _checkProps(props) {
      var getItem = props.getItem,
        getItemCount = props.getItemCount,
        horizontal = props.horizontal,
        columnWrapperStyle = props.columnWrapperStyle,
        onViewableItemsChanged = props.onViewableItemsChanged,
        viewabilityConfigCallbackPairs = props.viewabilityConfigCallbackPairs;
      var numColumns = numColumnsOrDefault(this.props.numColumns);
      invariant(!getItem && !getItemCount, 'FlatList does not support custom data formats.');
      if (numColumns > 1) {
        invariant(!horizontal, 'numColumns does not support horizontal.');
      } else {
        invariant(!columnWrapperStyle, 'columnWrapperStyle not supported for single column lists');
      }
      invariant(!(onViewableItemsChanged && viewabilityConfigCallbackPairs), 'FlatList does not support setting both onViewableItemsChanged and ' + 'viewabilityConfigCallbackPairs.');
    }
  }, {
    key: "_pushMultiColumnViewable",
    value: function _pushMultiColumnViewable(arr, v) {
      var _this$props$keyExtrac2;
      var numColumns = numColumnsOrDefault(this.props.numColumns);
      var keyExtractor = (_this$props$keyExtrac2 = this.props.keyExtractor) != null ? _this$props$keyExtrac2 : _virtualizedLists.keyExtractor;
      v.item.forEach(function (item, ii) {
        invariant(v.index != null, 'Missing index!');
        var index = v.index * numColumns + ii;
        arr.push(Object.assign({}, v, {
          item: item,
          key: keyExtractor(item, index),
          index: index
        }));
      });
    }
  }, {
    key: "_createOnViewableItemsChanged",
    value: function _createOnViewableItemsChanged(onViewableItemsChanged) {
      var _this2 = this;
      return function (info) {
        var numColumns = numColumnsOrDefault(_this2.props.numColumns);
        if (onViewableItemsChanged) {
          if (numColumns > 1) {
            var changed = [];
            var viewableItems = [];
            info.viewableItems.forEach(function (v) {
              return _this2._pushMultiColumnViewable(viewableItems, v);
            });
            info.changed.forEach(function (v) {
              return _this2._pushMultiColumnViewable(changed, v);
            });
            onViewableItemsChanged({
              viewableItems: viewableItems,
              changed: changed
            });
          } else {
            onViewableItemsChanged(info);
          }
        }
      };
    }
  }, {
    key: "render",
    value: function render() {
      var _this$props2 = this.props,
        numColumns = _this$props2.numColumns,
        columnWrapperStyle = _this$props2.columnWrapperStyle,
        _removeClippedSubviews = _this$props2.removeClippedSubviews,
        _this$props2$strictMo = _this$props2.strictMode,
        strictMode = _this$props2$strictMo === void 0 ? false : _this$props2$strictMo,
        restProps = (0, _objectWithoutProperties2.default)(_this$props2, _excluded);
      var renderer = strictMode ? this._memoizedRenderer : this._renderer;
      return (0, _jsxRuntime.jsx)(_virtualizedLists.VirtualizedList, Object.assign({}, restProps, {
        getItem: this._getItem,
        getItemCount: this._getItemCount,
        keyExtractor: this._keyExtractor,
        ref: this._captureRef,
        viewabilityConfigCallbackPairs: this._virtualizedListPairs,
        removeClippedSubviews: removeClippedSubviewsOrDefault(_removeClippedSubviews)
      }, renderer(this.props.ListItemComponent, this.props.renderItem, columnWrapperStyle, numColumns, this.props.extraData)));
    }
  }]);
}(React.PureComponent);
var styles = StyleSheet.create({
  row: {
    flexDirection: 'row'
  }
});
module.exports = FlatList;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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