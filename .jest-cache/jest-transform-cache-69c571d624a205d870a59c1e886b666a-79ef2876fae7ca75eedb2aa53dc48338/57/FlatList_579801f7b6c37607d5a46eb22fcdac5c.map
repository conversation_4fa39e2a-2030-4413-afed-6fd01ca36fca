{"version": 3, "names": ["ReactNativeFeatureFlags", "_interopRequireWildcard", "require", "_virtualizedLists", "_memoizeOne", "_interopRequireDefault", "_jsxRuntime", "_excluded", "_getRequireWildcardCache", "e", "WeakMap", "r", "t", "__esModule", "default", "has", "get", "n", "__proto__", "a", "Object", "defineProperty", "getOwnPropertyDescriptor", "u", "hasOwnProperty", "call", "i", "set", "_callSuper", "o", "_getPrototypeOf2", "_possibleConstructorReturn2", "_isNativeReflectConstruct", "Reflect", "construct", "constructor", "apply", "Boolean", "prototype", "valueOf", "View", "StyleSheet", "<PERSON><PERSON><PERSON><PERSON>", "Platform", "invariant", "React", "removeClippedSubviewsOrDefault", "removeClippedSubviews", "shouldUseRemoveClippedSubviewsAsDefaultOnIOS", "OS", "numColumnsOrDefault", "numColumns", "isArrayLike", "data", "length", "FlatList", "_React$PureComponent", "props", "_this", "_classCallCheck2", "_virtualizedListPairs", "_captureRef", "ref", "_listRef", "_getItem", "index", "ret", "kk", "itemIndex", "item", "push", "_getItemCount", "Math", "ceil", "_keyExtractor", "items", "_this$props$keyExtrac", "keyExtractor", "defaultKeyExtractor", "Array", "isArray", "map", "join", "_renderer", "ListItemComponent", "renderItem", "columnWrapperStyle", "extraData", "cols", "render", "jsx", "assign", "renderProp", "info", "style", "compose", "styles", "row", "children", "it", "element", "separators", "Fragment", "_memoized<PERSON><PERSON><PERSON>", "memoizeOne", "_checkProps", "viewabilityConfigCallbackPairs", "pair", "viewabilityConfig", "onViewableItemsChanged", "_createOnViewableItemsChanged", "_this$props", "arguments", "_inherits2", "_createClass2", "key", "value", "scrollToEnd", "params", "scrollToIndex", "scrollToItem", "scrollToOffset", "recordInteraction", "flashScrollIndicators", "getScrollResponder", "getNativeScrollRef", "getScrollRef", "getScrollableNode", "setNativeProps", "componentDidUpdate", "prevProps", "getItem", "getItemCount", "horizontal", "_pushMultiColumnViewable", "arr", "v", "_this$props$keyExtrac2", "for<PERSON>ach", "ii", "_this2", "changed", "viewableItems", "_this$props2", "_removeClippedSubviews", "_this$props2$strictMo", "strictMode", "restProps", "_objectWithoutProperties2", "renderer", "VirtualizedList", "PureComponent", "create", "flexDirection", "module", "exports"], "sources": ["FlatList.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @flow\n * @format\n */\n\nimport typeof ScrollViewNativeComponent from '../Components/ScrollView/ScrollViewNativeComponent';\nimport type {ViewStyleProp} from '../StyleSheet/StyleSheet';\nimport type {\n  RenderItemProps,\n  RenderItemType,\n  ViewabilityConfigCallbackPair,\n  ViewToken,\n} from '@react-native/virtualized-lists';\n\nimport * as ReactNativeFeatureFlags from '../../src/private/featureflags/ReactNativeFeatureFlags';\nimport {type ScrollResponderType} from '../Components/ScrollView/ScrollView';\nimport {\n  VirtualizedList,\n  keyExtractor as defaultKeyExtractor,\n} from '@react-native/virtualized-lists';\nimport memoizeOne from 'memoize-one';\n\nconst View = require('../Components/View/View');\nconst StyleSheet = require('../StyleSheet/StyleSheet');\nconst deepDiffer = require('../Utilities/differ/deepDiffer');\nconst Platform = require('../Utilities/Platform');\nconst invariant = require('invariant');\nconst React = require('react');\n\ntype RequiredProps<ItemT> = {|\n  /**\n   * An array (or array-like list) of items to render. Other data types can be\n   * used by targeting VirtualizedList directly.\n   */\n  data: ?$ReadOnly<$ArrayLike<ItemT>>,\n|};\ntype OptionalProps<ItemT> = {|\n  /**\n   * Takes an item from `data` and renders it into the list. Example usage:\n   *\n   *     <FlatList\n   *       ItemSeparatorComponent={Platform.OS !== 'android' && ({highlighted}) => (\n   *         <View style={[style.separator, highlighted && {marginLeft: 0}]} />\n   *       )}\n   *       data={[{title: 'Title Text', key: 'item1'}]}\n   *       renderItem={({item, separators}) => (\n   *         <TouchableHighlight\n   *           onPress={() => this._onPress(item)}\n   *           onShowUnderlay={separators.highlight}\n   *           onHideUnderlay={separators.unhighlight}>\n   *           <View style={{backgroundColor: 'white'}}>\n   *             <Text>{item.title}</Text>\n   *           </View>\n   *         </TouchableHighlight>\n   *       )}\n   *     />\n   *\n   * Provides additional metadata like `index` if you need it, as well as a more generic\n   * `separators.updateProps` function which let's you set whatever props you want to change the\n   * rendering of either the leading separator or trailing separator in case the more common\n   * `highlight` and `unhighlight` (which set the `highlighted: boolean` prop) are insufficient for\n   * your use-case.\n   */\n  renderItem?: ?RenderItemType<ItemT>,\n\n  /**\n   * Optional custom style for multi-item rows generated when numColumns > 1.\n   */\n  columnWrapperStyle?: ViewStyleProp,\n  /**\n   * A marker property for telling the list to re-render (since it implements `PureComponent`). If\n   * any of your `renderItem`, Header, Footer, etc. functions depend on anything outside of the\n   * `data` prop, stick it here and treat it immutably.\n   */\n  extraData?: any,\n  /**\n   * `getItemLayout` is an optional optimizations that let us skip measurement of dynamic content if\n   * you know the height of items a priori. `getItemLayout` is the most efficient, and is easy to\n   * use if you have fixed height items, for example:\n   *\n   *     getItemLayout={(data, index) => (\n   *       {length: ITEM_HEIGHT, offset: ITEM_HEIGHT * index, index}\n   *     )}\n   *\n   * Adding `getItemLayout` can be a great performance boost for lists of several hundred items.\n   * Remember to include separator length (height or width) in your offset calculation if you\n   * specify `ItemSeparatorComponent`.\n   */\n  getItemLayout?: (\n    data: ?$ReadOnly<$ArrayLike<ItemT>>,\n    index: number,\n  ) => {\n    length: number,\n    offset: number,\n    index: number,\n    ...\n  },\n  /**\n   * If true, renders items next to each other horizontally instead of stacked vertically.\n   */\n  horizontal?: ?boolean,\n  /**\n   * How many items to render in the initial batch. This should be enough to fill the screen but not\n   * much more. Note these items will never be unmounted as part of the windowed rendering in order\n   * to improve perceived performance of scroll-to-top actions.\n   */\n  initialNumToRender?: ?number,\n  /**\n   * Instead of starting at the top with the first item, start at `initialScrollIndex`. This\n   * disables the \"scroll to top\" optimization that keeps the first `initialNumToRender` items\n   * always rendered and immediately renders the items starting at this initial index. Requires\n   * `getItemLayout` to be implemented.\n   */\n  initialScrollIndex?: ?number,\n  /**\n   * Reverses the direction of scroll. Uses scale transforms of -1.\n   */\n  inverted?: ?boolean,\n  /**\n   * Used to extract a unique key for a given item at the specified index. Key is used for caching\n   * and as the react key to track item re-ordering. The default extractor checks `item.key`, then\n   * falls back to using the index, like React does.\n   */\n  keyExtractor?: ?(item: ItemT, index: number) => string,\n  /**\n   * Multiple columns can only be rendered with `horizontal={false}` and will zig-zag like a\n   * `flexWrap` layout. Items should all be the same height - masonry layouts are not supported.\n   *\n   * The default value is 1.\n   */\n  numColumns?: number,\n  /**\n   * Note: may have bugs (missing content) in some circumstances - use at your own risk.\n   *\n   * This may improve scroll performance for large lists.\n   *\n   * The default value is true for Android.\n   */\n  removeClippedSubviews?: boolean,\n  /**\n   * See `ScrollView` for flow type and further documentation.\n   */\n  fadingEdgeLength?: ?number,\n  /**\n   * Enable an optimization to memoize the item renderer to prevent unnecessary rerenders.\n   */\n  strictMode?: boolean,\n|};\n\n/**\n * Default Props Helper Functions\n * Use the following helper functions for default values\n */\n\n// removeClippedSubviewsOrDefault(this.props.removeClippedSubviews)\nfunction removeClippedSubviewsOrDefault(removeClippedSubviews: ?boolean) {\n  if (ReactNativeFeatureFlags.shouldUseRemoveClippedSubviewsAsDefaultOnIOS()) {\n    return removeClippedSubviews ?? true;\n  } else {\n    return removeClippedSubviews ?? Platform.OS === 'android';\n  }\n}\n\n// numColumnsOrDefault(this.props.numColumns)\nfunction numColumnsOrDefault(numColumns: ?number) {\n  return numColumns ?? 1;\n}\n\nfunction isArrayLike(data: mixed): boolean {\n  // $FlowExpectedError[incompatible-use]\n  return typeof Object(data).length === 'number';\n}\n\ntype FlatListProps<ItemT> = {|\n  ...RequiredProps<ItemT>,\n  ...OptionalProps<ItemT>,\n|};\n\ntype VirtualizedListProps = React.ElementConfig<typeof VirtualizedList>;\n\nexport type Props<ItemT> = {\n  ...$Diff<\n    VirtualizedListProps,\n    {\n      getItem: $PropertyType<VirtualizedListProps, 'getItem'>,\n      getItemCount: $PropertyType<VirtualizedListProps, 'getItemCount'>,\n      getItemLayout: $PropertyType<VirtualizedListProps, 'getItemLayout'>,\n      renderItem: $PropertyType<VirtualizedListProps, 'renderItem'>,\n      keyExtractor: $PropertyType<VirtualizedListProps, 'keyExtractor'>,\n      ...\n    },\n  >,\n  ...FlatListProps<ItemT>,\n  ...\n};\n\n/**\n * A performant interface for rendering simple, flat lists, supporting the most handy features:\n *\n *  - Fully cross-platform.\n *  - Optional horizontal mode.\n *  - Configurable viewability callbacks.\n *  - Header support.\n *  - Footer support.\n *  - Separator support.\n *  - Pull to Refresh.\n *  - Scroll loading.\n *  - ScrollToIndex support.\n *\n * If you need section support, use [`<SectionList>`](docs/sectionlist.html).\n *\n * Minimal Example:\n *\n *     <FlatList\n *       data={[{key: 'a'}, {key: 'b'}]}\n *       renderItem={({item}) => <Text>{item.key}</Text>}\n *     />\n *\n * More complex, multi-select example demonstrating `PureComponent` usage for perf optimization and avoiding bugs.\n *\n * - By binding the `onPressItem` handler, the props will remain `===` and `PureComponent` will\n *   prevent wasteful re-renders unless the actual `id`, `selected`, or `title` props change, even\n *   if the components rendered in `MyListItem` did not have such optimizations.\n * - By passing `extraData={this.state}` to `FlatList` we make sure `FlatList` itself will re-render\n *   when the `state.selected` changes. Without setting this prop, `FlatList` would not know it\n *   needs to re-render any items because it is also a `PureComponent` and the prop comparison will\n *   not show any changes.\n * - `keyExtractor` tells the list to use the `id`s for the react keys instead of the default `key` property.\n *\n *\n *     class MyListItem extends React.PureComponent {\n *       _onPress = () => {\n *         this.props.onPressItem(this.props.id);\n *       };\n *\n *       render() {\n *         const textColor = this.props.selected ? \"red\" : \"black\";\n *         return (\n *           <TouchableOpacity onPress={this._onPress}>\n *             <View>\n *               <Text style={{ color: textColor }}>\n *                 {this.props.title}\n *               </Text>\n *             </View>\n *           </TouchableOpacity>\n *         );\n *       }\n *     }\n *\n *     class MultiSelectList extends React.PureComponent {\n *       state = {selected: (new Map(): Map<string, boolean>)};\n *\n *       _keyExtractor = (item, index) => item.id;\n *\n *       _onPressItem = (id: string) => {\n *         // updater functions are preferred for transactional updates\n *         this.setState((state) => {\n *           // copy the map rather than modifying state.\n *           const selected = new Map(state.selected);\n *           selected.set(id, !selected.get(id)); // toggle\n *           return {selected};\n *         });\n *       };\n *\n *       _renderItem = ({item}) => (\n *         <MyListItem\n *           id={item.id}\n *           onPressItem={this._onPressItem}\n *           selected={!!this.state.selected.get(item.id)}\n *           title={item.title}\n *         />\n *       );\n *\n *       render() {\n *         return (\n *           <FlatList\n *             data={this.props.data}\n *             extraData={this.state}\n *             keyExtractor={this._keyExtractor}\n *             renderItem={this._renderItem}\n *           />\n *         );\n *       }\n *     }\n *\n * This is a convenience wrapper around [`<VirtualizedList>`](docs/virtualizedlist.html),\n * and thus inherits its props (as well as those of `ScrollView`) that aren't explicitly listed\n * here, along with the following caveats:\n *\n * - Internal state is not preserved when content scrolls out of the render window. Make sure all\n *   your data is captured in the item data or external stores like Flux, Redux, or Relay.\n * - This is a `PureComponent` which means that it will not re-render if `props` remain shallow-\n *   equal. Make sure that everything your `renderItem` function depends on is passed as a prop\n *   (e.g. `extraData`) that is not `===` after updates, otherwise your UI may not update on\n *   changes. This includes the `data` prop and parent component state.\n * - In order to constrain memory and enable smooth scrolling, content is rendered asynchronously\n *   offscreen. This means it's possible to scroll faster than the fill rate ands momentarily see\n *   blank content. This is a tradeoff that can be adjusted to suit the needs of each application,\n *   and we are working on improving it behind the scenes.\n * - By default, the list looks for a `key` prop on each item and uses that for the React key.\n *   Alternatively, you can provide a custom `keyExtractor` prop.\n *\n * Also inherits [ScrollView Props](docs/scrollview.html#props), unless it is nested in another FlatList of same orientation.\n */\nclass FlatList<ItemT> extends React.PureComponent<Props<ItemT>, void> {\n  /**\n   * Scrolls to the end of the content. May be janky without `getItemLayout` prop.\n   */\n  scrollToEnd(params?: ?{animated?: ?boolean, ...}) {\n    if (this._listRef) {\n      this._listRef.scrollToEnd(params);\n    }\n  }\n\n  /**\n   * Scrolls to the item at the specified index such that it is positioned in the viewable area\n   * such that `viewPosition` 0 places it at the top, 1 at the bottom, and 0.5 centered in the\n   * middle. `viewOffset` is a fixed number of pixels to offset the final target position.\n   *\n   * Note: cannot scroll to locations outside the render window without specifying the\n   * `getItemLayout` prop.\n   */\n  scrollToIndex(params: {\n    animated?: ?boolean,\n    index: number,\n    viewOffset?: number,\n    viewPosition?: number,\n    ...\n  }) {\n    if (this._listRef) {\n      this._listRef.scrollToIndex(params);\n    }\n  }\n\n  /**\n   * Requires linear scan through data - use `scrollToIndex` instead if possible.\n   *\n   * Note: cannot scroll to locations outside the render window without specifying the\n   * `getItemLayout` prop.\n   */\n  scrollToItem(params: {\n    animated?: ?boolean,\n    item: ItemT,\n    viewOffset?: number,\n    viewPosition?: number,\n    ...\n  }) {\n    if (this._listRef) {\n      this._listRef.scrollToItem(params);\n    }\n  }\n\n  /**\n   * Scroll to a specific content pixel offset in the list.\n   *\n   * Check out [scrollToOffset](docs/virtualizedlist.html#scrolltooffset) of VirtualizedList\n   */\n  scrollToOffset(params: {animated?: ?boolean, offset: number, ...}) {\n    if (this._listRef) {\n      this._listRef.scrollToOffset(params);\n    }\n  }\n\n  /**\n   * Tells the list an interaction has occurred, which should trigger viewability calculations, e.g.\n   * if `waitForInteractions` is true and the user has not scrolled. This is typically called by\n   * taps on items or by navigation actions.\n   */\n  recordInteraction() {\n    if (this._listRef) {\n      this._listRef.recordInteraction();\n    }\n  }\n\n  /**\n   * Displays the scroll indicators momentarily.\n   *\n   * @platform ios\n   */\n  flashScrollIndicators() {\n    if (this._listRef) {\n      this._listRef.flashScrollIndicators();\n    }\n  }\n\n  /**\n   * Provides a handle to the underlying scroll responder.\n   */\n  getScrollResponder(): ?ScrollResponderType {\n    if (this._listRef) {\n      return this._listRef.getScrollResponder();\n    }\n  }\n\n  /**\n   * Provides a reference to the underlying host component\n   */\n  getNativeScrollRef():\n    | ?React.ElementRef<typeof View>\n    | ?React.ElementRef<ScrollViewNativeComponent> {\n    if (this._listRef) {\n      /* $FlowFixMe[incompatible-return] Suppresses errors found when fixing\n       * TextInput typing */\n      return this._listRef.getScrollRef();\n    }\n  }\n\n  getScrollableNode(): any {\n    if (this._listRef) {\n      return this._listRef.getScrollableNode();\n    }\n  }\n\n  setNativeProps(props: {[string]: mixed, ...}) {\n    if (this._listRef) {\n      this._listRef.setNativeProps(props);\n    }\n  }\n\n  constructor(props: Props<ItemT>) {\n    super(props);\n    this._checkProps(this.props);\n    if (this.props.viewabilityConfigCallbackPairs) {\n      this._virtualizedListPairs =\n        this.props.viewabilityConfigCallbackPairs.map(pair => ({\n          viewabilityConfig: pair.viewabilityConfig,\n          onViewableItemsChanged: this._createOnViewableItemsChanged(\n            pair.onViewableItemsChanged,\n          ),\n        }));\n    } else if (this.props.onViewableItemsChanged) {\n      this._virtualizedListPairs.push({\n        /* $FlowFixMe[incompatible-call] (>=0.63.0 site=react_native_fb) This\n         * comment suppresses an error found when Flow v0.63 was deployed. To\n         * see the error delete this comment and run Flow. */\n        viewabilityConfig: this.props.viewabilityConfig,\n        onViewableItemsChanged: this._createOnViewableItemsChanged(\n          // NOTE: we use a wrapper function to allow the actual callback to change\n          // while still keeping the function provided to native to be stable\n          (...args) => {\n            invariant(\n              this.props.onViewableItemsChanged,\n              'Changing the nullability of onViewableItemsChanged is not supported. ' +\n                'Once a function or null is supplied that cannot be changed.',\n            );\n            return this.props.onViewableItemsChanged(...args);\n          },\n        ),\n      });\n    }\n  }\n\n  // $FlowFixMe[missing-local-annot]\n  componentDidUpdate(prevProps: Props<ItemT>) {\n    invariant(\n      prevProps.numColumns === this.props.numColumns,\n      'Changing numColumns on the fly is not supported. Change the key prop on FlatList when ' +\n        'changing the number of columns to force a fresh render of the component.',\n    );\n    invariant(\n      (prevProps.onViewableItemsChanged == null) ===\n        (this.props.onViewableItemsChanged == null),\n      'Changing onViewableItemsChanged nullability on the fly is not supported',\n    );\n    invariant(\n      !deepDiffer(prevProps.viewabilityConfig, this.props.viewabilityConfig),\n      'Changing viewabilityConfig on the fly is not supported',\n    );\n    invariant(\n      prevProps.viewabilityConfigCallbackPairs ===\n        this.props.viewabilityConfigCallbackPairs,\n      'Changing viewabilityConfigCallbackPairs on the fly is not supported',\n    );\n\n    this._checkProps(this.props);\n  }\n\n  _listRef: ?VirtualizedList;\n  _virtualizedListPairs: Array<ViewabilityConfigCallbackPair> = [];\n\n  _captureRef = (ref: ?VirtualizedList) => {\n    this._listRef = ref;\n  };\n\n  // $FlowFixMe[missing-local-annot]\n  _checkProps(props: Props<ItemT>) {\n    const {\n      // $FlowFixMe[prop-missing] this prop doesn't exist, is only used for an invariant\n      getItem,\n      // $FlowFixMe[prop-missing] this prop doesn't exist, is only used for an invariant\n      getItemCount,\n      horizontal,\n      columnWrapperStyle,\n      onViewableItemsChanged,\n      viewabilityConfigCallbackPairs,\n    } = props;\n    const numColumns = numColumnsOrDefault(this.props.numColumns);\n    invariant(\n      !getItem && !getItemCount,\n      'FlatList does not support custom data formats.',\n    );\n    if (numColumns > 1) {\n      invariant(!horizontal, 'numColumns does not support horizontal.');\n    } else {\n      invariant(\n        !columnWrapperStyle,\n        'columnWrapperStyle not supported for single column lists',\n      );\n    }\n    invariant(\n      !(onViewableItemsChanged && viewabilityConfigCallbackPairs),\n      'FlatList does not support setting both onViewableItemsChanged and ' +\n        'viewabilityConfigCallbackPairs.',\n    );\n  }\n\n  _getItem = (\n    data: $ArrayLike<ItemT>,\n    index: number,\n  ): ?(ItemT | $ReadOnlyArray<ItemT>) => {\n    const numColumns = numColumnsOrDefault(this.props.numColumns);\n    if (numColumns > 1) {\n      const ret = [];\n      for (let kk = 0; kk < numColumns; kk++) {\n        const itemIndex = index * numColumns + kk;\n        if (itemIndex < data.length) {\n          const item = data[itemIndex];\n          ret.push(item);\n        }\n      }\n      return ret;\n    } else {\n      return data[index];\n    }\n  };\n\n  _getItemCount = (data: ?$ArrayLike<ItemT>): number => {\n    // Legacy behavior of FlatList was to forward \"undefined\" length if invalid\n    // data like a non-arraylike object is passed. VirtualizedList would then\n    // coerce this, and the math would work out to no-op. For compatibility, if\n    // invalid data is passed, we tell VirtualizedList there are zero items\n    // available to prevent it from trying to read from the invalid data\n    // (without propagating invalidly typed data).\n    if (data != null && isArrayLike(data)) {\n      const numColumns = numColumnsOrDefault(this.props.numColumns);\n      return numColumns > 1 ? Math.ceil(data.length / numColumns) : data.length;\n    } else {\n      return 0;\n    }\n  };\n\n  _keyExtractor = (items: ItemT | Array<ItemT>, index: number): string => {\n    const numColumns = numColumnsOrDefault(this.props.numColumns);\n    const keyExtractor = this.props.keyExtractor ?? defaultKeyExtractor;\n\n    if (numColumns > 1) {\n      invariant(\n        Array.isArray(items),\n        'FlatList: Encountered internal consistency error, expected each item to consist of an ' +\n          'array with 1-%s columns; instead, received a single item.',\n        numColumns,\n      );\n      return items\n        .map((item, kk) =>\n          keyExtractor(((item: $FlowFixMe): ItemT), index * numColumns + kk),\n        )\n        .join(':');\n    }\n\n    // $FlowFixMe[incompatible-call] Can't call keyExtractor with an array\n    return keyExtractor(items, index);\n  };\n\n  _pushMultiColumnViewable(arr: Array<ViewToken>, v: ViewToken): void {\n    const numColumns = numColumnsOrDefault(this.props.numColumns);\n    const keyExtractor = this.props.keyExtractor ?? defaultKeyExtractor;\n    v.item.forEach((item, ii) => {\n      invariant(v.index != null, 'Missing index!');\n      const index = v.index * numColumns + ii;\n      arr.push({...v, item, key: keyExtractor(item, index), index});\n    });\n  }\n\n  _createOnViewableItemsChanged(\n    onViewableItemsChanged: ?(info: {\n      viewableItems: Array<ViewToken>,\n      changed: Array<ViewToken>,\n      ...\n    }) => void,\n    // $FlowFixMe[missing-local-annot]\n  ) {\n    return (info: {\n      viewableItems: Array<ViewToken>,\n      changed: Array<ViewToken>,\n      ...\n    }) => {\n      const numColumns = numColumnsOrDefault(this.props.numColumns);\n      if (onViewableItemsChanged) {\n        if (numColumns > 1) {\n          const changed: Array<ViewToken> = [];\n          const viewableItems: Array<ViewToken> = [];\n          info.viewableItems.forEach(v =>\n            this._pushMultiColumnViewable(viewableItems, v),\n          );\n          info.changed.forEach(v => this._pushMultiColumnViewable(changed, v));\n          onViewableItemsChanged({viewableItems, changed});\n        } else {\n          onViewableItemsChanged(info);\n        }\n      }\n    };\n  }\n\n  _renderer = (\n    ListItemComponent: ?(React.ComponentType<any> | React.MixedElement),\n    renderItem: ?RenderItemType<ItemT>,\n    columnWrapperStyle: ?ViewStyleProp,\n    numColumns: ?number,\n    extraData: ?any,\n    // $FlowFixMe[missing-local-annot]\n  ) => {\n    const cols = numColumnsOrDefault(numColumns);\n\n    const render = (props: RenderItemProps<ItemT>): React.Node => {\n      if (ListItemComponent) {\n        // $FlowFixMe[not-a-component] Component isn't valid\n        // $FlowFixMe[incompatible-type-arg] Component isn't valid\n        // $FlowFixMe[incompatible-return] Component isn't valid\n        return <ListItemComponent {...props} />;\n      } else if (renderItem) {\n        // $FlowFixMe[incompatible-call]\n        return renderItem(props);\n      } else {\n        return null;\n      }\n    };\n\n    const renderProp = (info: RenderItemProps<ItemT>) => {\n      if (cols > 1) {\n        const {item, index} = info;\n        invariant(\n          Array.isArray(item),\n          'Expected array of items with numColumns > 1',\n        );\n        return (\n          <View style={StyleSheet.compose(styles.row, columnWrapperStyle)}>\n            {item.map((it, kk) => {\n              const element = render({\n                // $FlowFixMe[incompatible-call]\n                item: it,\n                index: index * cols + kk,\n                separators: info.separators,\n              });\n              return element != null ? (\n                <React.Fragment key={kk}>{element}</React.Fragment>\n              ) : null;\n            })}\n          </View>\n        );\n      } else {\n        return render(info);\n      }\n    };\n\n    return ListItemComponent\n      ? {ListItemComponent: renderProp}\n      : {renderItem: renderProp};\n  };\n\n  // $FlowFixMe[missing-local-annot]\n  _memoizedRenderer = memoizeOne(this._renderer);\n\n  render(): React.Node {\n    const {\n      numColumns,\n      columnWrapperStyle,\n      removeClippedSubviews: _removeClippedSubviews,\n      strictMode = false,\n      ...restProps\n    } = this.props;\n\n    const renderer = strictMode ? this._memoizedRenderer : this._renderer;\n\n    return (\n      // $FlowFixMe[incompatible-exact] - `restProps` (`Props`) is inexact.\n      <VirtualizedList\n        {...restProps}\n        getItem={this._getItem}\n        getItemCount={this._getItemCount}\n        keyExtractor={this._keyExtractor}\n        ref={this._captureRef}\n        viewabilityConfigCallbackPairs={this._virtualizedListPairs}\n        removeClippedSubviews={removeClippedSubviewsOrDefault(\n          _removeClippedSubviews,\n        )}\n        {...renderer(\n          this.props.ListItemComponent,\n          this.props.renderItem,\n          columnWrapperStyle,\n          numColumns,\n          this.props.extraData,\n        )}\n      />\n    );\n  }\n}\n\nconst styles = StyleSheet.create({\n  row: {flexDirection: 'row'},\n});\n\nmodule.exports = FlatList;\n"], "mappings": ";;;;;;;AAmBA,IAAAA,uBAAA,GAAAC,uBAAA,CAAAC,OAAA;AAEA,IAAAC,iBAAA,GAAAD,OAAA;AAIA,IAAAE,WAAA,GAAAC,sBAAA,CAAAH,OAAA;AAAqC,IAAAI,WAAA,GAAAJ,OAAA;AAAA,IAAAK,SAAA;AAAA,SAAAC,yBAAAC,CAAA,6BAAAC,OAAA,mBAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,yBAAAC,CAAA,WAAAA,CAAA,GAAAG,CAAA,GAAAD,CAAA,KAAAF,CAAA;AAAA,SAAAR,wBAAAQ,CAAA,EAAAE,CAAA,SAAAA,CAAA,IAAAF,CAAA,IAAAA,CAAA,CAAAI,UAAA,SAAAJ,CAAA,eAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,WAAAK,OAAA,EAAAL,CAAA,QAAAG,CAAA,GAAAJ,wBAAA,CAAAG,CAAA,OAAAC,CAAA,IAAAA,CAAA,CAAAG,GAAA,CAAAN,CAAA,UAAAG,CAAA,CAAAI,GAAA,CAAAP,CAAA,OAAAQ,CAAA,KAAAC,SAAA,UAAAC,CAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,CAAA,IAAAd,CAAA,oBAAAc,CAAA,OAAAC,cAAA,CAAAC,IAAA,CAAAhB,CAAA,EAAAc,CAAA,SAAAG,CAAA,GAAAP,CAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAb,CAAA,EAAAc,CAAA,UAAAG,CAAA,KAAAA,CAAA,CAAAV,GAAA,IAAAU,CAAA,CAAAC,GAAA,IAAAP,MAAA,CAAAC,cAAA,CAAAJ,CAAA,EAAAM,CAAA,EAAAG,CAAA,IAAAT,CAAA,CAAAM,CAAA,IAAAd,CAAA,CAAAc,CAAA,YAAAN,CAAA,CAAAH,OAAA,GAAAL,CAAA,EAAAG,CAAA,IAAAA,CAAA,CAAAe,GAAA,CAAAlB,CAAA,EAAAQ,CAAA,GAAAA,CAAA;AAAA,SAAAW,WAAAhB,CAAA,EAAAiB,CAAA,EAAApB,CAAA,WAAAoB,CAAA,OAAAC,gBAAA,CAAAhB,OAAA,EAAAe,CAAA,OAAAE,2BAAA,CAAAjB,OAAA,EAAAF,CAAA,EAAAoB,yBAAA,KAAAC,OAAA,CAAAC,SAAA,CAAAL,CAAA,EAAApB,CAAA,YAAAqB,gBAAA,CAAAhB,OAAA,EAAAF,CAAA,EAAAuB,WAAA,IAAAN,CAAA,CAAAO,KAAA,CAAAxB,CAAA,EAAAH,CAAA;AAAA,SAAAuB,0BAAA,cAAApB,CAAA,IAAAyB,OAAA,CAAAC,SAAA,CAAAC,OAAA,CAAAd,IAAA,CAAAQ,OAAA,CAAAC,SAAA,CAAAG,OAAA,iCAAAzB,CAAA,aAAAoB,yBAAA,YAAAA,0BAAA,aAAApB,CAAA;AAErC,IAAM4B,IAAI,GAAGtC,OAAO,CAAC,yBAAyB,CAAC;AAC/C,IAAMuC,UAAU,GAAGvC,OAAO,CAAC,0BAA0B,CAAC;AACtD,IAAMwC,UAAU,GAAGxC,OAAO,CAAC,gCAAgC,CAAC;AAC5D,IAAMyC,QAAQ,GAAGzC,OAAO,CAAC,uBAAuB,CAAC;AACjD,IAAM0C,SAAS,GAAG1C,OAAO,CAAC,WAAW,CAAC;AACtC,IAAM2C,KAAK,GAAG3C,OAAO,CAAC,OAAO,CAAC;AAgI9B,SAAS4C,8BAA8BA,CAACC,qBAA+B,EAAE;EACvE,IAAI/C,uBAAuB,CAACgD,4CAA4C,CAAC,CAAC,EAAE;IAC1E,OAAOD,qBAAqB,WAArBA,qBAAqB,GAAI,IAAI;EACtC,CAAC,MAAM;IACL,OAAOA,qBAAqB,WAArBA,qBAAqB,GAAIJ,QAAQ,CAACM,EAAE,KAAK,SAAS;EAC3D;AACF;AAGA,SAASC,mBAAmBA,CAACC,UAAmB,EAAE;EAChD,OAAOA,UAAU,WAAVA,UAAU,GAAI,CAAC;AACxB;AAEA,SAASC,WAAWA,CAACC,IAAW,EAAW;EAEzC,OAAO,OAAOjC,MAAM,CAACiC,IAAI,CAAC,CAACC,MAAM,KAAK,QAAQ;AAChD;AAAC,IAqIKC,QAAQ,aAAAC,oBAAA;EAmHZ,SAAAD,SAAYE,MAAmB,EAAE;IAAA,IAAAC,KAAA;IAAA,IAAAC,gBAAA,CAAA7C,OAAA,QAAAyC,QAAA;IAC/BG,KAAA,GAAA9B,UAAA,OAAA2B,QAAA,GAAME,MAAK;IAAEC,KAAA,CA0DfE,qBAAqB,GAAyC,EAAE;IAAAF,KAAA,CAEhEG,WAAW,GAAG,UAACC,GAAqB,EAAK;MACvCJ,KAAA,CAAKK,QAAQ,GAAGD,GAAG;IACrB,CAAC;IAAAJ,KAAA,CAkCDM,QAAQ,GAAG,UACTX,IAAuB,EACvBY,KAAa,EACwB;MACrC,IAAMd,UAAU,GAAGD,mBAAmB,CAACQ,KAAA,CAAKD,KAAK,CAACN,UAAU,CAAC;MAC7D,IAAIA,UAAU,GAAG,CAAC,EAAE;QAClB,IAAMe,GAAG,GAAG,EAAE;QACd,KAAK,IAAIC,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGhB,UAAU,EAAEgB,EAAE,EAAE,EAAE;UACtC,IAAMC,SAAS,GAAGH,KAAK,GAAGd,UAAU,GAAGgB,EAAE;UACzC,IAAIC,SAAS,GAAGf,IAAI,CAACC,MAAM,EAAE;YAC3B,IAAMe,KAAI,GAAGhB,IAAI,CAACe,SAAS,CAAC;YAC5BF,GAAG,CAACI,IAAI,CAACD,KAAI,CAAC;UAChB;QACF;QACA,OAAOH,GAAG;MACZ,CAAC,MAAM;QACL,OAAOb,IAAI,CAACY,KAAK,CAAC;MACpB;IACF,CAAC;IAAAP,KAAA,CAEDa,aAAa,GAAG,UAAClB,IAAwB,EAAa;MAOpD,IAAIA,IAAI,IAAI,IAAI,IAAID,WAAW,CAACC,IAAI,CAAC,EAAE;QACrC,IAAMF,UAAU,GAAGD,mBAAmB,CAACQ,KAAA,CAAKD,KAAK,CAACN,UAAU,CAAC;QAC7D,OAAOA,UAAU,GAAG,CAAC,GAAGqB,IAAI,CAACC,IAAI,CAACpB,IAAI,CAACC,MAAM,GAAGH,UAAU,CAAC,GAAGE,IAAI,CAACC,MAAM;MAC3E,CAAC,MAAM;QACL,OAAO,CAAC;MACV;IACF,CAAC;IAAAI,KAAA,CAEDgB,aAAa,GAAG,UAACC,KAA2B,EAAEV,KAAa,EAAa;MAAA,IAAAW,qBAAA;MACtE,IAAMzB,UAAU,GAAGD,mBAAmB,CAACQ,KAAA,CAAKD,KAAK,CAACN,UAAU,CAAC;MAC7D,IAAM0B,YAAY,IAAAD,qBAAA,GAAGlB,KAAA,CAAKD,KAAK,CAACoB,YAAY,YAAAD,qBAAA,GAAIE,8BAAmB;MAEnE,IAAI3B,UAAU,GAAG,CAAC,EAAE;QAClBP,SAAS,CACPmC,KAAK,CAACC,OAAO,CAACL,KAAK,CAAC,EACpB,wFAAwF,GACtF,2DAA2D,EAC7DxB,UACF,CAAC;QACD,OAAOwB,KAAK,CACTM,GAAG,CAAC,UAACZ,IAAI,EAAEF,EAAE;UAAA,OACZU,YAAY,CAAGR,IAAI,EAAuBJ,KAAK,GAAGd,UAAU,GAAGgB,EAAE,CAAC;QAAA,CACpE,CAAC,CACAe,IAAI,CAAC,GAAG,CAAC;MACd;MAGA,OAAOL,YAAY,CAACF,KAAK,EAAEV,KAAK,CAAC;IACnC,CAAC;IAAAP,KAAA,CA0CDyB,SAAS,GAAG,UACVC,iBAAmE,EACnEC,UAAkC,EAClCC,kBAAkC,EAClCnC,UAAmB,EACnBoC,SAAe,EAEZ;MACH,IAAMC,IAAI,GAAGtC,mBAAmB,CAACC,UAAU,CAAC;MAE5C,IAAMsC,MAAM,GAAG,SAATA,MAAMA,CAAIhC,KAA6B,EAAiB;QAC5D,IAAI2B,iBAAiB,EAAE;UAIrB,OAAO,IAAA9E,WAAA,CAAAoF,GAAA,EAACN,iBAAiB,EAAAhE,MAAA,CAAAuE,MAAA,KAAKlC,KAAK,CAAG,CAAC;QACzC,CAAC,MAAM,IAAI4B,UAAU,EAAE;UAErB,OAAOA,UAAU,CAAC5B,KAAK,CAAC;QAC1B,CAAC,MAAM;UACL,OAAO,IAAI;QACb;MACF,CAAC;MAED,IAAMmC,UAAU,GAAG,SAAbA,UAAUA,CAAIC,IAA4B,EAAK;QACnD,IAAIL,IAAI,GAAG,CAAC,EAAE;UACZ,IAAOnB,MAAI,GAAWwB,IAAI,CAAnBxB,IAAI;YAAEJ,MAAK,GAAI4B,IAAI,CAAb5B,KAAK;UAClBrB,SAAS,CACPmC,KAAK,CAACC,OAAO,CAACX,MAAI,CAAC,EACnB,6CACF,CAAC;UACD,OACE,IAAA/D,WAAA,CAAAoF,GAAA,EAAClD,IAAI;YAACsD,KAAK,EAAErD,UAAU,CAACsD,OAAO,CAACC,MAAM,CAACC,GAAG,EAAEX,kBAAkB,CAAE;YAAAY,QAAA,EAC7D7B,MAAI,CAACY,GAAG,CAAC,UAACkB,EAAE,EAAEhC,EAAE,EAAK;cACpB,IAAMiC,OAAO,GAAGX,MAAM,CAAC;gBAErBpB,IAAI,EAAE8B,EAAE;gBACRlC,KAAK,EAAEA,MAAK,GAAGuB,IAAI,GAAGrB,EAAE;gBACxBkC,UAAU,EAAER,IAAI,CAACQ;cACnB,CAAC,CAAC;cACF,OAAOD,OAAO,IAAI,IAAI,GACpB,IAAA9F,WAAA,CAAAoF,GAAA,EAAC7C,KAAK,CAACyD,QAAQ;gBAAAJ,QAAA,EAAWE;cAAO,GAAZjC,EAA6B,CAAC,GACjD,IAAI;YACV,CAAC;UAAC,CACE,CAAC;QAEX,CAAC,MAAM;UACL,OAAOsB,MAAM,CAACI,IAAI,CAAC;QACrB;MACF,CAAC;MAED,OAAOT,iBAAiB,GACpB;QAACA,iBAAiB,EAAEQ;MAAU,CAAC,GAC/B;QAACP,UAAU,EAAEO;MAAU,CAAC;IAC9B,CAAC;IAAAlC,KAAA,CAGD6C,iBAAiB,GAAG,IAAAC,mBAAU,EAAC9C,KAAA,CAAKyB,SAAS,CAAC;IAzP5CzB,KAAA,CAAK+C,WAAW,CAAC/C,KAAA,CAAKD,KAAK,CAAC;IAC5B,IAAIC,KAAA,CAAKD,KAAK,CAACiD,8BAA8B,EAAE;MAC7ChD,KAAA,CAAKE,qBAAqB,GACxBF,KAAA,CAAKD,KAAK,CAACiD,8BAA8B,CAACzB,GAAG,CAAC,UAAA0B,IAAI;QAAA,OAAK;UACrDC,iBAAiB,EAAED,IAAI,CAACC,iBAAiB;UACzCC,sBAAsB,EAAEnD,KAAA,CAAKoD,6BAA6B,CACxDH,IAAI,CAACE,sBACP;QACF,CAAC;MAAA,CAAC,CAAC;IACP,CAAC,MAAM,IAAInD,KAAA,CAAKD,KAAK,CAACoD,sBAAsB,EAAE;MAC5CnD,KAAA,CAAKE,qBAAqB,CAACU,IAAI,CAAC;QAI9BsC,iBAAiB,EAAElD,KAAA,CAAKD,KAAK,CAACmD,iBAAiB;QAC/CC,sBAAsB,EAAEnD,KAAA,CAAKoD,6BAA6B,CAGxD,YAAa;UAAA,IAAAC,WAAA;UACXnE,SAAS,CACPc,KAAA,CAAKD,KAAK,CAACoD,sBAAsB,EACjC,uEAAuE,GACrE,6DACJ,CAAC;UACD,OAAO,CAAAE,WAAA,GAAArD,KAAA,CAAKD,KAAK,EAACoD,sBAAsB,CAAAzE,KAAA,CAAA2E,WAAA,EAAAC,SAAQ,CAAC;QACnD,CACF;MACF,CAAC,CAAC;IACJ;IAAC,OAAAtD,KAAA;EACH;EAAC,IAAAuD,UAAA,CAAAnG,OAAA,EAAAyC,QAAA,EAAAC,oBAAA;EAAA,WAAA0D,aAAA,CAAApG,OAAA,EAAAyC,QAAA;IAAA4D,GAAA;IAAAC,KAAA,EA9ID,SAAAC,WAAWA,CAACC,MAAoC,EAAE;MAChD,IAAI,IAAI,CAACvD,QAAQ,EAAE;QACjB,IAAI,CAACA,QAAQ,CAACsD,WAAW,CAACC,MAAM,CAAC;MACnC;IACF;EAAC;IAAAH,GAAA;IAAAC,KAAA,EAUD,SAAAG,aAAaA,CAACD,MAMb,EAAE;MACD,IAAI,IAAI,CAACvD,QAAQ,EAAE;QACjB,IAAI,CAACA,QAAQ,CAACwD,aAAa,CAACD,MAAM,CAAC;MACrC;IACF;EAAC;IAAAH,GAAA;IAAAC,KAAA,EAQD,SAAAI,YAAYA,CAACF,MAMZ,EAAE;MACD,IAAI,IAAI,CAACvD,QAAQ,EAAE;QACjB,IAAI,CAACA,QAAQ,CAACyD,YAAY,CAACF,MAAM,CAAC;MACpC;IACF;EAAC;IAAAH,GAAA;IAAAC,KAAA,EAOD,SAAAK,cAAcA,CAACH,MAAkD,EAAE;MACjE,IAAI,IAAI,CAACvD,QAAQ,EAAE;QACjB,IAAI,CAACA,QAAQ,CAAC0D,cAAc,CAACH,MAAM,CAAC;MACtC;IACF;EAAC;IAAAH,GAAA;IAAAC,KAAA,EAOD,SAAAM,iBAAiBA,CAAA,EAAG;MAClB,IAAI,IAAI,CAAC3D,QAAQ,EAAE;QACjB,IAAI,CAACA,QAAQ,CAAC2D,iBAAiB,CAAC,CAAC;MACnC;IACF;EAAC;IAAAP,GAAA;IAAAC,KAAA,EAOD,SAAAO,qBAAqBA,CAAA,EAAG;MACtB,IAAI,IAAI,CAAC5D,QAAQ,EAAE;QACjB,IAAI,CAACA,QAAQ,CAAC4D,qBAAqB,CAAC,CAAC;MACvC;IACF;EAAC;IAAAR,GAAA;IAAAC,KAAA,EAKD,SAAAQ,kBAAkBA,CAAA,EAAyB;MACzC,IAAI,IAAI,CAAC7D,QAAQ,EAAE;QACjB,OAAO,IAAI,CAACA,QAAQ,CAAC6D,kBAAkB,CAAC,CAAC;MAC3C;IACF;EAAC;IAAAT,GAAA;IAAAC,KAAA,EAKD,SAAAS,kBAAkBA,CAAA,EAE+B;MAC/C,IAAI,IAAI,CAAC9D,QAAQ,EAAE;QAGjB,OAAO,IAAI,CAACA,QAAQ,CAAC+D,YAAY,CAAC,CAAC;MACrC;IACF;EAAC;IAAAX,GAAA;IAAAC,KAAA,EAED,SAAAW,iBAAiBA,CAAA,EAAQ;MACvB,IAAI,IAAI,CAAChE,QAAQ,EAAE;QACjB,OAAO,IAAI,CAACA,QAAQ,CAACgE,iBAAiB,CAAC,CAAC;MAC1C;IACF;EAAC;IAAAZ,GAAA;IAAAC,KAAA,EAED,SAAAY,cAAcA,CAACvE,KAA6B,EAAE;MAC5C,IAAI,IAAI,CAACM,QAAQ,EAAE;QACjB,IAAI,CAACA,QAAQ,CAACiE,cAAc,CAACvE,KAAK,CAAC;MACrC;IACF;EAAC;IAAA0D,GAAA;IAAAC,KAAA,EAoCD,SAAAa,kBAAkBA,CAACC,SAAuB,EAAE;MAC1CtF,SAAS,CACPsF,SAAS,CAAC/E,UAAU,KAAK,IAAI,CAACM,KAAK,CAACN,UAAU,EAC9C,wFAAwF,GACtF,0EACJ,CAAC;MACDP,SAAS,CACNsF,SAAS,CAACrB,sBAAsB,IAAI,IAAI,MACtC,IAAI,CAACpD,KAAK,CAACoD,sBAAsB,IAAI,IAAI,CAAC,EAC7C,yEACF,CAAC;MACDjE,SAAS,CACP,CAACF,UAAU,CAACwF,SAAS,CAACtB,iBAAiB,EAAE,IAAI,CAACnD,KAAK,CAACmD,iBAAiB,CAAC,EACtE,wDACF,CAAC;MACDhE,SAAS,CACPsF,SAAS,CAACxB,8BAA8B,KACtC,IAAI,CAACjD,KAAK,CAACiD,8BAA8B,EAC3C,qEACF,CAAC;MAED,IAAI,CAACD,WAAW,CAAC,IAAI,CAAChD,KAAK,CAAC;IAC9B;EAAC;IAAA0D,GAAA;IAAAC,KAAA,EAUD,SAAAX,WAAWA,CAAChD,KAAmB,EAAE;MAC/B,IAEE0E,OAAO,GAOL1E,KAAK,CAPP0E,OAAO;QAEPC,YAAY,GAKV3E,KAAK,CALP2E,YAAY;QACZC,UAAU,GAIR5E,KAAK,CAJP4E,UAAU;QACV/C,kBAAkB,GAGhB7B,KAAK,CAHP6B,kBAAkB;QAClBuB,sBAAsB,GAEpBpD,KAAK,CAFPoD,sBAAsB;QACtBH,8BAA8B,GAC5BjD,KAAK,CADPiD,8BAA8B;MAEhC,IAAMvD,UAAU,GAAGD,mBAAmB,CAAC,IAAI,CAACO,KAAK,CAACN,UAAU,CAAC;MAC7DP,SAAS,CACP,CAACuF,OAAO,IAAI,CAACC,YAAY,EACzB,gDACF,CAAC;MACD,IAAIjF,UAAU,GAAG,CAAC,EAAE;QAClBP,SAAS,CAAC,CAACyF,UAAU,EAAE,yCAAyC,CAAC;MACnE,CAAC,MAAM;QACLzF,SAAS,CACP,CAAC0C,kBAAkB,EACnB,0DACF,CAAC;MACH;MACA1C,SAAS,CACP,EAAEiE,sBAAsB,IAAIH,8BAA8B,CAAC,EAC3D,oEAAoE,GAClE,iCACJ,CAAC;IACH;EAAC;IAAAS,GAAA;IAAAC,KAAA,EA2DD,SAAAkB,wBAAwBA,CAACC,GAAqB,EAAEC,CAAY,EAAQ;MAAA,IAAAC,sBAAA;MAClE,IAAMtF,UAAU,GAAGD,mBAAmB,CAAC,IAAI,CAACO,KAAK,CAACN,UAAU,CAAC;MAC7D,IAAM0B,YAAY,IAAA4D,sBAAA,GAAG,IAAI,CAAChF,KAAK,CAACoB,YAAY,YAAA4D,sBAAA,GAAI3D,8BAAmB;MACnE0D,CAAC,CAACnE,IAAI,CAACqE,OAAO,CAAC,UAACrE,IAAI,EAAEsE,EAAE,EAAK;QAC3B/F,SAAS,CAAC4F,CAAC,CAACvE,KAAK,IAAI,IAAI,EAAE,gBAAgB,CAAC;QAC5C,IAAMA,KAAK,GAAGuE,CAAC,CAACvE,KAAK,GAAGd,UAAU,GAAGwF,EAAE;QACvCJ,GAAG,CAACjE,IAAI,CAAAlD,MAAA,CAAAuE,MAAA,KAAK6C,CAAC;UAAEnE,IAAI,EAAJA,IAAI;UAAE8C,GAAG,EAAEtC,YAAY,CAACR,IAAI,EAAEJ,KAAK,CAAC;UAAEA,KAAK,EAALA;QAAK,EAAC,CAAC;MAC/D,CAAC,CAAC;IACJ;EAAC;IAAAkD,GAAA;IAAAC,KAAA,EAED,SAAAN,6BAA6BA,CAC3BD,sBAIU,EAEV;MAAA,IAAA+B,MAAA;MACA,OAAO,UAAC/C,IAIP,EAAK;QACJ,IAAM1C,UAAU,GAAGD,mBAAmB,CAAC0F,MAAI,CAACnF,KAAK,CAACN,UAAU,CAAC;QAC7D,IAAI0D,sBAAsB,EAAE;UAC1B,IAAI1D,UAAU,GAAG,CAAC,EAAE;YAClB,IAAM0F,OAAyB,GAAG,EAAE;YACpC,IAAMC,aAA+B,GAAG,EAAE;YAC1CjD,IAAI,CAACiD,aAAa,CAACJ,OAAO,CAAC,UAAAF,CAAC;cAAA,OAC1BI,MAAI,CAACN,wBAAwB,CAACQ,aAAa,EAAEN,CAAC,CAAC;YAAA,CACjD,CAAC;YACD3C,IAAI,CAACgD,OAAO,CAACH,OAAO,CAAC,UAAAF,CAAC;cAAA,OAAII,MAAI,CAACN,wBAAwB,CAACO,OAAO,EAAEL,CAAC,CAAC;YAAA,EAAC;YACpE3B,sBAAsB,CAAC;cAACiC,aAAa,EAAbA,aAAa;cAAED,OAAO,EAAPA;YAAO,CAAC,CAAC;UAClD,CAAC,MAAM;YACLhC,sBAAsB,CAAChB,IAAI,CAAC;UAC9B;QACF;MACF,CAAC;IACH;EAAC;IAAAsB,GAAA;IAAAC,KAAA,EA6DD,SAAA3B,MAAMA,CAAA,EAAe;MACnB,IAAAsD,YAAA,GAMI,IAAI,CAACtF,KAAK;QALZN,UAAU,GAAA4F,YAAA,CAAV5F,UAAU;QACVmC,kBAAkB,GAAAyD,YAAA,CAAlBzD,kBAAkB;QACK0D,sBAAsB,GAAAD,YAAA,CAA7ChG,qBAAqB;QAAAkG,qBAAA,GAAAF,YAAA,CACrBG,UAAU;QAAVA,UAAU,GAAAD,qBAAA,cAAG,KAAK,GAAAA,qBAAA;QACfE,SAAS,OAAAC,yBAAA,CAAAtI,OAAA,EAAAiI,YAAA,EAAAxI,SAAA;MAGd,IAAM8I,QAAQ,GAAGH,UAAU,GAAG,IAAI,CAAC3C,iBAAiB,GAAG,IAAI,CAACpB,SAAS;MAErE,OAEE,IAAA7E,WAAA,CAAAoF,GAAA,EAACvF,iBAAA,CAAAmJ,eAAe,EAAAlI,MAAA,CAAAuE,MAAA,KACVwD,SAAS;QACbhB,OAAO,EAAE,IAAI,CAACnE,QAAS;QACvBoE,YAAY,EAAE,IAAI,CAAC7D,aAAc;QACjCM,YAAY,EAAE,IAAI,CAACH,aAAc;QACjCZ,GAAG,EAAE,IAAI,CAACD,WAAY;QACtB6C,8BAA8B,EAAE,IAAI,CAAC9C,qBAAsB;QAC3Db,qBAAqB,EAAED,8BAA8B,CACnDkG,sBACF;MAAE,GACEK,QAAQ,CACV,IAAI,CAAC5F,KAAK,CAAC2B,iBAAiB,EAC5B,IAAI,CAAC3B,KAAK,CAAC4B,UAAU,EACrBC,kBAAkB,EAClBnC,UAAU,EACV,IAAI,CAACM,KAAK,CAAC8B,SACb,CAAC,CACF,CAAC;IAEN;EAAC;AAAA,EAhZ2B1C,KAAK,CAAC0G,aAAa;AAmZjD,IAAMvD,MAAM,GAAGvD,UAAU,CAAC+G,MAAM,CAAC;EAC/BvD,GAAG,EAAE;IAACwD,aAAa,EAAE;EAAK;AAC5B,CAAC,CAAC;AAEFC,MAAM,CAACC,OAAO,GAAGpG,QAAQ", "ignoreList": []}