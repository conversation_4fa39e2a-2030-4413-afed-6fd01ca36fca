37de25517cd6901aaa2a65d57bdcc159
'use strict';

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.StretchOutData = exports.StretchOut = exports.StretchInData = exports.StretchIn = void 0;
var _animationParser = require("../animationParser.js");
var DEFAULT_STRETCH_TIME = 0.3;
var StretchInData = exports.StretchInData = {
  StretchInX: {
    name: 'StretchInX',
    style: {
      0: {
        transform: [{
          scaleX: 0
        }]
      },
      100: {
        transform: [{
          scaleX: 1
        }]
      }
    },
    duration: DEFAULT_STRETCH_TIME
  },
  StretchInY: {
    name: 'StretchInY',
    style: {
      0: {
        transform: [{
          scaleY: 0
        }]
      },
      100: {
        transform: [{
          scaleY: 1
        }]
      }
    },
    duration: DEFAULT_STRETCH_TIME
  }
};
var StretchOutData = exports.StretchOutData = {
  StretchOutX: {
    name: 'StretchOutX',
    style: {
      0: {
        transform: [{
          scaleX: 1
        }]
      },
      100: {
        transform: [{
          scaleX: 0
        }]
      }
    },
    duration: DEFAULT_STRETCH_TIME
  },
  StretchOutY: {
    name: 'StretchOutY',
    style: {
      0: {
        transform: [{
          scaleY: 1
        }]
      },
      100: {
        transform: [{
          scaleY: 0
        }]
      }
    },
    duration: DEFAULT_STRETCH_TIME
  }
};
var StretchIn = exports.StretchIn = {
  StretchInX: {
    style: (0, _animationParser.convertAnimationObjectToKeyframes)(StretchInData.StretchInX),
    duration: StretchInData.StretchInX.duration
  },
  StretchInY: {
    style: (0, _animationParser.convertAnimationObjectToKeyframes)(StretchInData.StretchInY),
    duration: StretchInData.StretchInY.duration
  }
};
var StretchOut = exports.StretchOut = {
  StretchOutX: {
    style: (0, _animationParser.convertAnimationObjectToKeyframes)(StretchOutData.StretchOutX),
    duration: StretchOutData.StretchOutX.duration
  },
  StretchOutY: {
    style: (0, _animationParser.convertAnimationObjectToKeyframes)(StretchOutData.StretchOutY),
    duration: StretchOutData.StretchOutY.duration
  }
};
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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