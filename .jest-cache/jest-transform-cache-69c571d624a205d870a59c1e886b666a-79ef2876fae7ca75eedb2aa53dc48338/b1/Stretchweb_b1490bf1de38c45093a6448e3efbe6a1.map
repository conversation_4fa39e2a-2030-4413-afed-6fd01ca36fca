{"version": 3, "names": ["Object", "defineProperty", "exports", "value", "StretchOutData", "StretchOut", "StretchInData", "StretchIn", "_animation<PERSON><PERSON>er", "require", "DEFAULT_STRETCH_TIME", "StretchInX", "name", "style", "transform", "scaleX", "duration", "StretchInY", "scaleY", "StretchOutX", "StretchOutY", "convertAnimationObjectToKeyframes"], "sources": ["../../../../../src/layoutReanimation/web/animation/Stretch.web.ts"], "sourcesContent": [null], "mappings": "AAAA,YAAY;;AAAAA,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,KAAA;AAAA;AAAAD,OAAA,CAAAE,cAAA,GAAAF,OAAA,CAAAG,UAAA,GAAAH,OAAA,CAAAI,aAAA,GAAAJ,OAAA,CAAAK,SAAA;AACZ,IAAAC,gBAAA,GAAAC,OAAA;AAEA,IAAMC,oBAAoB,GAAG,GAAG;AAEzB,IAAMJ,aAAa,GAAAJ,OAAA,CAAAI,aAAA,GAAG;EAC3BK,UAAU,EAAE;IACVC,IAAI,EAAE,YAAY;IAClBC,KAAK,EAAE;MACL,CAAC,EAAE;QAAEC,SAAS,EAAE,CAAC;UAAEC,MAAM,EAAE;QAAE,CAAC;MAAE,CAAC;MACjC,GAAG,EAAE;QAAED,SAAS,EAAE,CAAC;UAAEC,MAAM,EAAE;QAAE,CAAC;MAAE;IACpC,CAAC;IACDC,QAAQ,EAAEN;EACZ,CAAC;EAEDO,UAAU,EAAE;IACVL,IAAI,EAAE,YAAY;IAClBC,KAAK,EAAE;MACL,CAAC,EAAE;QAAEC,SAAS,EAAE,CAAC;UAAEI,MAAM,EAAE;QAAE,CAAC;MAAE,CAAC;MACjC,GAAG,EAAE;QAAEJ,SAAS,EAAE,CAAC;UAAEI,MAAM,EAAE;QAAE,CAAC;MAAE;IACpC,CAAC;IACDF,QAAQ,EAAEN;EACZ;AACF,CAAC;AAEM,IAAMN,cAAc,GAAAF,OAAA,CAAAE,cAAA,GAAG;EAC5Be,WAAW,EAAE;IACXP,IAAI,EAAE,aAAa;IACnBC,KAAK,EAAE;MACL,CAAC,EAAE;QAAEC,SAAS,EAAE,CAAC;UAAEC,MAAM,EAAE;QAAE,CAAC;MAAE,CAAC;MACjC,GAAG,EAAE;QAAED,SAAS,EAAE,CAAC;UAAEC,MAAM,EAAE;QAAE,CAAC;MAAE;IACpC,CAAC;IACDC,QAAQ,EAAEN;EACZ,CAAC;EAEDU,WAAW,EAAE;IACXR,IAAI,EAAE,aAAa;IACnBC,KAAK,EAAE;MACL,CAAC,EAAE;QAAEC,SAAS,EAAE,CAAC;UAAEI,MAAM,EAAE;QAAE,CAAC;MAAE,CAAC;MACjC,GAAG,EAAE;QAAEJ,SAAS,EAAE,CAAC;UAAEI,MAAM,EAAE;QAAE,CAAC;MAAE;IACpC,CAAC;IACDF,QAAQ,EAAEN;EACZ;AACF,CAAC;AAEM,IAAMH,SAAS,GAAAL,OAAA,CAAAK,SAAA,GAAG;EACvBI,UAAU,EAAE;IACVE,KAAK,EAAE,IAAAQ,kDAAiC,EAACf,aAAa,CAACK,UAAU,CAAC;IAClEK,QAAQ,EAAEV,aAAa,CAACK,UAAU,CAACK;EACrC,CAAC;EACDC,UAAU,EAAE;IACVJ,KAAK,EAAE,IAAAQ,kDAAiC,EAACf,aAAa,CAACW,UAAU,CAAC;IAClED,QAAQ,EAAEV,aAAa,CAACW,UAAU,CAACD;EACrC;AACF,CAAC;AAEM,IAAMX,UAAU,GAAAH,OAAA,CAAAG,UAAA,GAAG;EACxBc,WAAW,EAAE;IACXN,KAAK,EAAE,IAAAQ,kDAAiC,EAACjB,cAAc,CAACe,WAAW,CAAC;IACpEH,QAAQ,EAAEZ,cAAc,CAACe,WAAW,CAACH;EACvC,CAAC;EACDI,WAAW,EAAE;IACXP,KAAK,EAAE,IAAAQ,kDAAiC,EAACjB,cAAc,CAACgB,WAAW,CAAC;IACpEJ,QAAQ,EAAEZ,cAAc,CAACgB,WAAW,CAACJ;EACvC;AACF,CAAC", "ignoreList": []}