{"version": 3, "names": ["Object", "defineProperty", "exports", "value", "CurvedTransition", "prepareCurvedTransition", "_index", "require", "_EasingWeb", "resetStyle", "component", "style", "animationName", "position", "top", "left", "margin", "width", "height", "showChildren", "parent", "childrenDisplayProperty", "shouldShow", "i", "children", "length", "child", "display", "get", "set", "prepareParent", "element", "dummy", "animationConfig", "transitionData", "easing", "getEasingByName", "easingX", "Map", "originalBackgroundColor", "backgroundColor", "onFinalize", "contains", "<PERSON><PERSON><PERSON><PERSON>", "animationCancelCallback", "removeEventListener", "animationEndCallback", "addEventListener", "append<PERSON><PERSON><PERSON>", "prepareDummy", "dummyTransitionKeyframeName", "dummyAnimationConfig", "animationType", "LayoutAnimationType", "LAYOUT", "duration", "delay", "easingY", "callback", "reversed", "cloneNode", "_prepareDummy", "keyframeXName", "keyframeYName", "keyframeXObj", "name", "transform", "translateX", "scale", "scaleX", "scaleY", "keyframeYObj", "translateY", "firstKeyframeObj", "secondKeyframeObj"], "sources": ["../../../../../src/layoutReanimation/web/transition/Curved.web.ts"], "sourcesContent": [null], "mappings": "AAAA,YAAY;;AAAAA,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,KAAA;AAAA;AAAAD,OAAA,CAAAE,gBAAA,GAAAA,gBAAA;AAAAF,OAAA,CAAAG,uBAAA,GAAAA,uBAAA;AAEZ,IAAAC,MAAA,GAAAC,OAAA;AAEA,IAAAC,UAAA,GAAAD,OAAA;AAIA,SAASE,UAAUA,CAACC,SAAsB,EAAE;EAC1CA,SAAS,CAACC,KAAK,CAACC,aAAa,GAAG,EAAE;EAClCF,SAAS,CAACC,KAAK,CAACE,QAAQ,GAAG,UAAU;EACrCH,SAAS,CAACC,KAAK,CAACG,GAAG,GAAG,KAAK;EAC3BJ,SAAS,CAACC,KAAK,CAACI,IAAI,GAAG,KAAK;EAC5BL,SAAS,CAACC,KAAK,CAACK,MAAM,GAAG,KAAK;EAC9BN,SAAS,CAACC,KAAK,CAACM,KAAK,GAAG,MAAM;EAC9BP,SAAS,CAACC,KAAK,CAACO,MAAM,GAAG,MAAM;AACjC;AAEA,SAASC,YAAYA,CACnBC,MAAmB,EACnBC,uBAAiD,EACjDC,UAAmB,EACnB;EACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,MAAM,CAACI,QAAQ,CAACC,MAAM,EAAE,EAAEF,CAAC,EAAE;IAC/C,IAAMG,KAAK,GAAGN,MAAM,CAACI,QAAQ,CAACD,CAAC,CAAgB;IAE/C,IAAID,UAAU,EAAE;MACdI,KAAK,CAACf,KAAK,CAACgB,OAAO,GAAGN,uBAAuB,CAACO,GAAG,CAACF,KAAK,CAAE;IAC3D,CAAC,MAAM;MACLL,uBAAuB,CAACQ,GAAG,CAACH,KAAK,EAAEA,KAAK,CAACf,KAAK,CAACgB,OAAO,CAAC;MACvDD,KAAK,CAACf,KAAK,CAACgB,OAAO,GAAG,MAAM;IAC9B;EACF;AACF;AAEA,SAASG,aAAaA,CACpBC,OAA8B,EAC9BC,KAA4B,EAC5BC,eAAgC,EAChCC,cAA8B,EAC9B;EAEAD,eAAe,CAACE,MAAM,GAAG,IAAAC,0BAAe,EACtCF,cAAc,CAACG,OACjB,CAAC;EAED,IAAMhB,uBAAuB,GAAG,IAAIiB,GAAG,CAAsB,CAAC;EAC9DnB,YAAY,CAACY,OAAO,EAAEV,uBAAuB,EAAE,KAAK,CAAC;EAErD,IAAMkB,uBAAuB,GAAGR,OAAO,CAACpB,KAAK,CAAC6B,eAAe;EAC7DT,OAAO,CAACpB,KAAK,CAAC6B,eAAe,GAAG,aAAa;EAE7C,IAAMC,UAAU,GAAG,SAAbA,UAAUA,CAAA,EAAS;IACvB,IAAIV,OAAO,CAACW,QAAQ,CAACV,KAAK,CAAC,EAAE;MAC3BD,OAAO,CAACY,WAAW,CAACX,KAAK,CAAC;IAC5B;IAEAb,YAAY,CAACY,OAAO,EAAEV,uBAAuB,EAAE,IAAI,CAAC;IAEpDU,OAAO,CAACpB,KAAK,CAAC6B,eAAe,GAAGD,uBAAuB;EACzD,CAAC;EAED,IAAMK,wBAAuB,GAAG,SAA1BA,uBAAuBA,CAAA,EAAS;IACpCH,UAAU,CAAC,CAAC;IACZV,OAAO,CAACc,mBAAmB,CAAC,iBAAiB,EAAED,wBAAuB,CAAC;EACzE,CAAC;EAED,IAAME,qBAAoB,GAAG,SAAvBA,oBAAoBA,CAAA,EAAS;IACjCL,UAAU,CAAC,CAAC;IACZV,OAAO,CAACc,mBAAmB,CAAC,cAAc,EAAEC,qBAAoB,CAAC;EACnE,CAAC;EAEDf,OAAO,CAACgB,gBAAgB,CAAC,cAAc,EAAED,qBAAoB,CAAC;EAC9Df,OAAO,CAACgB,gBAAgB,CAAC,iBAAiB,EAAEH,wBAAuB,CAAC;EAEpEb,OAAO,CAACiB,WAAW,CAAChB,KAAK,CAAC;AAC5B;AAEA,SAASiB,YAAYA,CACnBlB,OAA8B,EAC9BE,eAAgC,EAChCC,cAA8B,EAC9BgB,2BAAmC,EACnC;EACA,IAAMC,oBAAqC,GAAG;IAC5CvC,aAAa,EAAEsC,2BAA2B;IAC1CE,aAAa,EAAEC,0BAAmB,CAACC,MAAM;IACzCC,QAAQ,EAAEtB,eAAe,CAACsB,QAAQ;IAClCC,KAAK,EAAEvB,eAAe,CAACuB,KAAK;IAC5BrB,MAAM,EAAE,IAAAC,0BAAe,EAACF,cAAc,CAACuB,OAA0B,CAAC;IAClEC,QAAQ,EAAE,IAAI;IACdC,QAAQ,EAAE;EACZ,CAAC;EAED,IAAM3B,KAAK,GAAGD,OAAO,CAAC6B,SAAS,CAAC,IAAI,CAA0B;EAC9DnD,UAAU,CAACuB,KAAK,CAAC;EAEjB,OAAO;IAAEA,KAAK,EAALA,KAAK;IAAEmB,oBAAA,EAAAA;EAAqB,CAAC;AACxC;AAEO,SAAS9C,uBAAuBA,CACrC0B,OAA8B,EAC9BE,eAAgC,EAChCC,cAA8B,EAC9BgB,2BAAmC,EACnC;EACA,IAAAW,aAAA,GAAwCZ,YAAY,CAClDlB,OAAO,EACPE,eAAe,EACfC,cAAc,EACdgB,2BACF,CAAC;IALOlB,KAAK,GAAA6B,aAAA,CAAL7B,KAAK;IAAEmB,oBAAA,GAAAU,aAAA,CAAAV,oBAAA;EAOfrB,aAAa,CAACC,OAAO,EAAEC,KAAK,EAAEC,eAAe,EAAEC,cAAc,CAAC;EAE9D,OAAO;IAAEF,KAAK,EAALA,KAAK;IAAEmB,oBAAA,EAAAA;EAAqB,CAAC;AACxC;AAEO,SAAS/C,gBAAgBA,CAC9B0D,aAAqB,EACrBC,aAAqB,EACrB7B,cAA8B,EAC9B;EACA,IAAM8B,YAAY,GAAG;IACnBC,IAAI,EAAEH,aAAa;IACnBnD,KAAK,EAAE;MACL,CAAC,EAAE;QACDuD,SAAS,EAAE,CACT;UACEC,UAAU,EAAE,GAAGjC,cAAc,CAACiC,UAAU,IAAI;UAC5CC,KAAK,EAAE,GAAGlC,cAAc,CAACmC,MAAM,IAAInC,cAAc,CAACoC,MAAM;QAC1D,CAAC;MAEL;IACF,CAAC;IACDf,QAAQ,EAAE;EACZ,CAAC;EAED,IAAMgB,YAAY,GAAG;IACnBN,IAAI,EAAEF,aAAa;IACnBpD,KAAK,EAAE;MACL,CAAC,EAAE;QACDuD,SAAS,EAAE,CACT;UACEM,UAAU,EAAE,GAAGtC,cAAc,CAACsC,UAAU,IAAI;UAC5CJ,KAAK,EAAE,GAAGlC,cAAc,CAACmC,MAAM,IAAInC,cAAc,CAACoC,MAAM;QAC1D,CAAC;MAEL;IACF,CAAC;IACDf,QAAQ,EAAE;EACZ,CAAC;EAED,OAAO;IACLkB,gBAAgB,EAAET,YAAY;IAC9BU,iBAAiB,EAAEH;EACrB,CAAC;AACH", "ignoreList": []}