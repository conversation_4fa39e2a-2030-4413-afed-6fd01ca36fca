{"version": 3, "names": ["Object", "defineProperty", "exports", "value", "areDependenciesEqual", "buildDependencies", "buildWorkletsHash", "isAnimated", "shallowEqual", "validateAnimatedStyles", "_errors", "require", "worklets", "values", "reduce", "acc", "worklet", "__workletHash", "toString", "dependencies", "handlers", "handlersList", "filter", "handler", "undefined", "map", "workletHash", "closure", "__closure", "push", "nextDependencies", "prevDependencies", "is", "x", "y", "Number", "isNaN", "objectIs", "areHookInputsEqual", "nextDeps", "prevDeps", "length", "i", "prop", "Array", "isArray", "some", "onFrame", "a", "b", "a<PERSON><PERSON><PERSON>", "keys", "b<PERSON><PERSON><PERSON>", "styles", "ReanimatedError"], "sources": ["../../../src/hook/utils.ts"], "sourcesContent": [null], "mappings": "AAAA,YAAY;;AAAAA,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,KAAA;AAAA;AAAAD,OAAA,CAAAE,oBAAA,GAAAA,oBAAA;AAAAF,OAAA,CAAAG,iBAAA,GAAAA,iBAAA;AAAAH,OAAA,CAAAI,iBAAA,GAAAA,iBAAA;AAAAJ,OAAA,CAAAK,UAAA,GAAAA,UAAA;AAAAL,OAAA,CAAAM,YAAA,GAAAA,YAAA;AAAAN,OAAA,CAAAO,sBAAA,GAAAA,sBAAA;AAEZ,IAAAC,OAAA,GAAAC,OAAA;AAIO,SAASL,iBAAiBA,CAC/BM,QAEwC,EACxC;EAEA,OAAOZ,MAAM,CAACa,MAAM,CAACD,QAAQ,CAAC,CAACE,MAAM,CACnC,UAACC,GAAG,EAAEC,OAA2C;IAAA,OAC/CD,GAAG,GAAGC,OAAO,CAACC,aAAa,CAACC,QAAQ,CAAC,CAAC;EAAA,GACxC,EACF,CAAC;AACH;AAGO,SAASb,iBAAiBA,CAC/Bc,YAA4B,EAC5BC,QAAqD,EACrD;EAEA,IAAMC,YAAY,GAAGrB,MAAM,CAACa,MAAM,CAACO,QAAQ,CAAC,CAACE,MAAM,CAChD,UAAAC,OAAO;IAAA,OAAKA,OAAO,KAAKC,SAC3B;EAAA,EAA2B;EAC3B,IAAI,CAACL,YAAY,EAAE;IACjBA,YAAY,GAAGE,YAAY,CAACI,GAAG,CAAE,UAAAF,OAAO,EAAK;MAC3C,OAAO;QACLG,WAAW,EAAEH,OAAO,CAACN,aAAa;QAClCU,OAAO,EAAEJ,OAAO,CAACK;MACnB,CAAC;IACH,CAAC,CAAC;EACJ,CAAC,MAAM;IACLT,YAAY,CAACU,IAAI,CAACvB,iBAAiB,CAACe,YAAY,CAAC,CAAC;EACpD;EAEA,OAAOF,YAAY;AACrB;AAGO,SAASf,oBAAoBA,CAClC0B,gBAAgC,EAChCC,gBAAgC,EAChC;EACA,SAASC,EAAEA,CAACC,CAAS,EAAEC,CAAS,EAAE;IAChC,OACGD,CAAC,KAAKC,CAAC,KAAKD,CAAC,KAAK,CAAC,IAAI,CAAC,GAAGA,CAAC,KAAK,CAAC,GAAGC,CAAC,CAAC,IACvCC,MAAM,CAACC,KAAK,CAACH,CAAC,CAAC,IAAIE,MAAM,CAACC,KAAK,CAACF,CAAC,CAAE;EAExC;EACA,IAAMG,QAA2D,GAC/D,OAAOrC,MAAM,CAACgC,EAAE,KAAK,UAAU,GAAGhC,MAAM,CAACgC,EAAE,GAAGA,EAAE;EAElD,SAASM,kBAAkBA,CACzBC,QAAwB,EACxBC,QAAwB,EACxB;IACA,IAAI,CAACD,QAAQ,IAAI,CAACC,QAAQ,IAAIA,QAAQ,CAACC,MAAM,KAAKF,QAAQ,CAACE,MAAM,EAAE;MACjE,OAAO,KAAK;IACd;IACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,QAAQ,CAACC,MAAM,EAAE,EAAEC,CAAC,EAAE;MACxC,IAAI,CAACL,QAAQ,CAACE,QAAQ,CAACG,CAAC,CAAC,EAAEF,QAAQ,CAACE,CAAC,CAAC,CAAC,EAAE;QACvC,OAAO,KAAK;MACd;IACF;IACA,OAAO,IAAI;EACb;EAEA,OAAOJ,kBAAkB,CAACR,gBAAgB,EAAEC,gBAAgB,CAAC;AAC/D;AAEO,SAASxB,UAAUA,CAACoC,IAAa,EAAE;EACxC,SAAS;;EACT,IAAIC,KAAK,CAACC,OAAO,CAACF,IAAI,CAAC,EAAE;IACvB,OAAOA,IAAI,CAACG,IAAI,CAACvC,UAAU,CAAC;EAC9B,CAAC,MAAM,IAAI,OAAOoC,IAAI,KAAK,QAAQ,IAAIA,IAAI,KAAK,IAAI,EAAE;IACpD,IAAKA,IAAI,CAA6BI,OAAO,KAAKvB,SAAS,EAAE;MAC3D,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAOxB,MAAM,CAACa,MAAM,CAAC8B,IAAI,CAAC,CAACG,IAAI,CAACvC,UAAU,CAAC;IAC7C;EACF;EACA,OAAO,KAAK;AACd;AAKO,SAASC,YAAYA,CAE1BwC,CAAI,EAAEC,CAAI,EAAE;EACZ,SAAS;;EACT,IAAMC,KAAK,GAAGlD,MAAM,CAACmD,IAAI,CAACH,CAAC,CAAC;EAC5B,IAAMI,KAAK,GAAGpD,MAAM,CAACmD,IAAI,CAACF,CAAC,CAAC;EAC5B,IAAIC,KAAK,CAACT,MAAM,KAAKW,KAAK,CAACX,MAAM,EAAE;IACjC,OAAO,KAAK;EACd;EACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGQ,KAAK,CAACT,MAAM,EAAEC,CAAC,EAAE,EAAE;IACrC,IAAIM,CAAC,CAACE,KAAK,CAACR,CAAC,CAAC,CAAC,KAAKO,CAAC,CAACC,KAAK,CAACR,CAAC,CAAC,CAAC,EAAE;MAC/B,OAAO,KAAK;IACd;EACF;EACA,OAAO,IAAI;AACb;AAEO,SAASjC,sBAAsBA,CAAC4C,MAA0B,EAAE;EACjE,SAAS;;EACT,IAAI,OAAOA,MAAM,KAAK,QAAQ,EAAE;IAC9B,MAAM,IAAIC,uBAAe,CACvB,uDAAuD,OAAOD,MAAM,WACtE,CAAC;EACH,CAAC,MAAM,IAAIT,KAAK,CAACC,OAAO,CAACQ,MAAM,CAAC,EAAE;IAChC,MAAM,IAAIC,uBAAe,CACvB,4JACF,CAAC;EACH;AACF", "ignoreList": []}