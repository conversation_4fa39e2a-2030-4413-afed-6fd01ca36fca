e78fe04f46db2f9406e05bbf33b46a87
'use strict';

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.areDependenciesEqual = areDependenciesEqual;
exports.buildDependencies = buildDependencies;
exports.buildWorkletsHash = buildWorkletsHash;
exports.isAnimated = isAnimated;
exports.shallowEqual = shallowEqual;
exports.validateAnimatedStyles = validateAnimatedStyles;
var _errors = require("../errors.js");
function buildWorkletsHash(worklets) {
  return Object.values(worklets).reduce(function (acc, worklet) {
    return acc + worklet.__workletHash.toString();
  }, '');
}
function buildDependencies(dependencies, handlers) {
  var handlersList = Object.values(handlers).filter(function (handler) {
    return handler !== undefined;
  });
  if (!dependencies) {
    dependencies = handlersList.map(function (handler) {
      return {
        workletHash: handler.__workletHash,
        closure: handler.__closure
      };
    });
  } else {
    dependencies.push(buildWorkletsHash(handlersList));
  }
  return dependencies;
}
function areDependenciesEqual(nextDependencies, prevDependencies) {
  function is(x, y) {
    return x === y && (x !== 0 || 1 / x === 1 / y) || Number.isNaN(x) && Number.isNaN(y);
  }
  var objectIs = typeof Object.is === 'function' ? Object.is : is;
  function areHookInputsEqual(nextDeps, prevDeps) {
    if (!nextDeps || !prevDeps || prevDeps.length !== nextDeps.length) {
      return false;
    }
    for (var i = 0; i < prevDeps.length; ++i) {
      if (!objectIs(nextDeps[i], prevDeps[i])) {
        return false;
      }
    }
    return true;
  }
  return areHookInputsEqual(nextDependencies, prevDependencies);
}
function isAnimated(prop) {
  'worklet';

  if (Array.isArray(prop)) {
    return prop.some(isAnimated);
  } else if (typeof prop === 'object' && prop !== null) {
    if (prop.onFrame !== undefined) {
      return true;
    } else {
      return Object.values(prop).some(isAnimated);
    }
  }
  return false;
}
function shallowEqual(a, b) {
  'worklet';

  var aKeys = Object.keys(a);
  var bKeys = Object.keys(b);
  if (aKeys.length !== bKeys.length) {
    return false;
  }
  for (var i = 0; i < aKeys.length; i++) {
    if (a[aKeys[i]] !== b[aKeys[i]]) {
      return false;
    }
  }
  return true;
}
function validateAnimatedStyles(styles) {
  'worklet';

  if (typeof styles !== 'object') {
    throw new _errors.ReanimatedError(`\`useAnimatedStyle\` has to return an object, found ${typeof styles} instead.`);
  } else if (Array.isArray(styles)) {
    throw new _errors.ReanimatedError('`useAnimatedStyle` has to return an object and cannot return static styles combined with dynamic ones. Please do merging where a component receives props.');
  }
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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