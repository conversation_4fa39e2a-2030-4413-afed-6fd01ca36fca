c43761704b379ad5638c47a4225a2a40
"use strict";

/* istanbul ignore next */
function cov_15mzbx1fq8() {
  var path = "/Users/<USER>/projects/react-native/payment-module/src/presentation/payment-result/components/transfer-result/constants.ts";
  var hash = "e939358f961fb841040a6e7530de467a6924bcc0";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/projects/react-native/payment-module/src/presentation/payment-result/components/transfer-result/constants.ts",
    statementMap: {
      "0": {
        start: {
          line: 3,
          column: 0
        },
        end: {
          line: 5,
          column: 3
        }
      },
      "1": {
        start: {
          line: 6,
          column: 0
        },
        end: {
          line: 6,
          column: 35
        }
      },
      "2": {
        start: {
          line: 7,
          column: 0
        },
        end: {
          line: 11,
          column: 2
        }
      }
    },
    fnMap: {},
    branchMap: {},
    s: {
      "0": 0,
      "1": 0,
      "2": 0
    },
    f: {},
    b: {},
    inputSourceMap: {
      version: 3,
      file: undefined,
      names: ["exports", "PaymentResultType", "SUCCESS", "ERROR", "PENDING"],
      sourceRoot: undefined,
      sources: ["/Users/<USER>/projects/react-native/payment-module/src/presentation/payment-result/components/transfer-result/constants.ts"],
      sourcesContent: ["export const PaymentResultType = {\n  SUCCESS: 'SUCCESS',\n  ERROR: 'ERROR',\n  PENDING: 'PENDING',\n};\n"],
      mappings: ";;;;;;AAAaA,OAAA,CAAAC,iBAAiB,GAAG;EAC/BC,OAAO,EAAE,SAAS;EAClBC,KAAK,EAAE,OAAO;EACdC,OAAO,EAAE;CACV",
      ignoreList: []
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "e939358f961fb841040a6e7530de467a6924bcc0"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_15mzbx1fq8 = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_15mzbx1fq8();
cov_15mzbx1fq8().s[0]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_15mzbx1fq8().s[1]++;
exports.PaymentResultType = void 0;
/* istanbul ignore next */
cov_15mzbx1fq8().s[2]++;
exports.PaymentResultType = {
  SUCCESS: 'SUCCESS',
  ERROR: 'ERROR',
  PENDING: 'PENDING'
};
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJleHBvcnRzIiwiUGF5bWVudFJlc3VsdFR5cGUiLCJTVUNDRVNTIiwiRVJST1IiLCJQRU5ESU5HIl0sInNvdXJjZXMiOlsiL1VzZXJzL3NvbnNhbXNldC9wcm9qZWN0cy9yZWFjdC1uYXRpdmUvcGF5bWVudC1tb2R1bGUvc3JjL3ByZXNlbnRhdGlvbi9wYXltZW50LXJlc3VsdC9jb21wb25lbnRzL3RyYW5zZmVyLXJlc3VsdC9jb25zdGFudHMudHMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGNvbnN0IFBheW1lbnRSZXN1bHRUeXBlID0ge1xuICBTVUNDRVNTOiAnU1VDQ0VTUycsXG4gIEVSUk9SOiAnRVJST1InLFxuICBQRU5ESU5HOiAnUEVORElORycsXG59O1xuIl0sIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBYUEsT0FBQSxDQUFBQyxpQkFBaUIsR0FBRztFQUMvQkMsT0FBTyxFQUFFLFNBQVM7RUFDbEJDLEtBQUssRUFBRSxPQUFPO0VBQ2RDLE9BQU8sRUFBRTtDQUNWIiwiaWdub3JlTGlzdCI6W119