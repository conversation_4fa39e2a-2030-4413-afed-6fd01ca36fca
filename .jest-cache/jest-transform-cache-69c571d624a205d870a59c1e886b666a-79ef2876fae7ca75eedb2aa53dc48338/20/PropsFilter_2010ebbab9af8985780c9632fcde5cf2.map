{"version": 3, "names": ["_interopRequireDefault", "require", "Object", "defineProperty", "exports", "value", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_classCallCheck2", "_createClass2", "_isSharedValue", "_PlatformChecker", "_WorkletEventHandler", "_index", "_InlinePropManager", "_utils", "_reactNative", "dummyListener", "default", "_initialStyle", "key", "filterNonAnimatedProps", "component", "_this", "inputProps", "props", "_loop", "styleProp", "style", "styles", "flattenArray", "processedStyle", "map", "viewDescriptors", "_isFirstRender", "assign", "initial", "initialUpdaterRun", "updater", "hasInlineStyles", "getInlineStyle", "StyleSheet", "flatten", "animatedProp", "animatedProps", "undefined", "keys", "for<PERSON>ach", "initialValueKey", "_animatedProp$initial", "has", "workletEventHandler", "WorkletEventHandler", "eventNames", "length", "eventName", "listeners", "isSharedValue", "isChromeDebugger"], "sources": ["../../../src/createAnimatedComponent/PropsFilter.tsx"], "sourcesContent": [null], "mappings": "AAAA,YAAY;;AAAA,IAAAA,sBAAA,GAAAC,OAAA;AAAAC,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,KAAA;AAAA;AAAAD,OAAA,CAAAE,WAAA;AAAA,IAAAC,gBAAA,GAAAP,sBAAA,CAAAC,OAAA;AAAA,IAAAO,aAAA,GAAAR,sBAAA,CAAAC,OAAA;AAGZ,IAAAQ,cAAA,GAAAR,OAAA;AACA,IAAAS,gBAAA,GAAAT,OAAA;AACA,IAAAU,oBAAA,GAAAV,OAAA;AACA,IAAAW,MAAA,GAAAX,OAAA;AACA,IAAAY,kBAAA,GAAAZ,OAAA;AAQA,IAAAa,MAAA,GAAAb,OAAA;AACA,IAAAc,YAAA,GAAAd,OAAA;AAEA,SAASe,aAAaA,CAAA,EAAG,CAEvB;AAAA,IAGWV,WAAW,GAAAF,OAAA,CAAAE,WAAA;EAAA,SAAAA,YAAA;IAAA,IAAAC,gBAAA,CAAAU,OAAA,QAAAX,WAAA;IAAA,KACdY,aAAa,GAAG,CAAC,CAAC;EAAA;EAAA,WAAAV,aAAA,CAAAS,OAAA,EAAAX,WAAA;IAAAa,GAAA;IAAAd,KAAA,EAEnB,SAAAe,sBAAsBA,CAC3BC,SAAyE,EAChD;MAAA,IAAAC,KAAA;MACzB,IAAMC,UAAU,GACdF,SAAS,CAACG,KAAsD;MAClE,IAAMA,KAA8B,GAAG,CAAC,CAAC;MAAA,IAAAC,KAAA,YAAAA,MAAA,EACX;QAC5B,IAAMpB,KAAK,GAAGkB,UAAU,CAACJ,GAAG,CAAC;QAC7B,IAAIA,GAAG,KAAK,OAAO,EAAE;UACnB,IAAMO,SAAS,GAAGH,UAAU,CAACI,KAAK;UAClC,IAAMC,MAAM,GAAG,IAAAC,mBAAY,EAAaH,SAAS,WAATA,SAAS,GAAI,EAAE,CAAC;UACxD,IAAMI,cAA4B,GAAGF,MAAM,CAACG,GAAG,CAAE,UAAAJ,KAAK,EAAK;YACzD,IAAIA,KAAK,IAAIA,KAAK,CAACK,eAAe,EAAE;cAElC,IAAIX,SAAS,CAACY,cAAc,EAAE;gBAC5BX,KAAI,CAACJ,aAAa,GAAAhB,MAAA,CAAAgC,MAAA,KACbP,KAAK,CAACQ,OAAO,CAAC9B,KAAK,EACnBiB,KAAI,CAACJ,aAAa,EAClB,IAAAkB,wBAAiB,EAAaT,KAAK,CAACQ,OAAO,CAACE,OAAO,EACvD;cACH;cACA,OAAOf,KAAI,CAACJ,aAAa;YAC3B,CAAC,MAAM,IAAI,IAAAoB,kCAAe,EAACX,KAAK,CAAC,EAAE;cACjC,OAAO,IAAAY,iCAAc,EAACZ,KAAK,EAAEN,SAAS,CAACY,cAAc,CAAC;YACxD,CAAC,MAAM;cACL,OAAON,KAAK;YACd;UACF,CAAC,CAAC;UACFH,KAAK,CAACL,GAAG,CAAC,GAAGqB,uBAAU,CAACC,OAAO,CAACX,cAAc,CAAC;QACjD,CAAC,MAAM,IAAIX,GAAG,KAAK,eAAe,EAAE;UAClC,IAAMuB,YAAY,GAAGnB,UAAU,CAACoB,aAE/B;UACD,IAAID,YAAY,CAACP,OAAO,KAAKS,SAAS,EAAE;YACtC1C,MAAM,CAAC2C,IAAI,CAACH,YAAY,CAACP,OAAO,CAAC9B,KAAK,CAAC,CAACyC,OAAO,CAAE,UAAAC,eAAe,EAAK;cAAA,IAAAC,qBAAA;cACnExB,KAAK,CAACuB,eAAe,CAAC,IAAAC,qBAAA,GACpBN,YAAY,CAACP,OAAO,qBAApBa,qBAAA,CAAsB3C,KAAK,CAAC0C,eAAe,CAAC;YAChD,CAAC,CAAC;UACJ;QACF,CAAC,MAAM,IACL,IAAAE,UAAG,EAAC,qBAAqB,EAAE5C,KAAK,CAAC,IACjCA,KAAK,CAAC6C,mBAAmB,YAAYC,wCAAmB,EACxD;UACA,IAAI9C,KAAK,CAAC6C,mBAAmB,CAACE,UAAU,CAACC,MAAM,GAAG,CAAC,EAAE;YACnDhD,KAAK,CAAC6C,mBAAmB,CAACE,UAAU,CAACN,OAAO,CAAE,UAAAQ,SAAS,EAAK;cAC1D9B,KAAK,CAAC8B,SAAS,CAAC,GAAG,IAAAL,UAAG,EAAC,WAAW,EAAE5C,KAAK,CAAC6C,mBAAmB,CAAC,GAExD7C,KAAK,CAAC6C,mBAAmB,CAACK,SAAS,CACnCD,SAAS,CAAC,GACZtC,aAAa;YACnB,CAAC,CAAC;UACJ,CAAC,MAAM;YACLQ,KAAK,CAACL,GAAG,CAAC,GAAGH,aAAa;UAC5B;QACF,CAAC,MAAM,IAAI,IAAAwC,4BAAa,EAACnD,KAAK,CAAC,EAAE;UAC/B,IAAIgB,SAAS,CAACY,cAAc,EAAE;YAC5BT,KAAK,CAACL,GAAG,CAAC,GAAGd,KAAK,CAACA,KAAK;UAC1B;QACF,CAAC,MAAM,IAAIc,GAAG,KAAK,6BAA6B,IAAI,CAAC,IAAAsC,iCAAgB,EAAC,CAAC,EAAE;UACvEjC,KAAK,CAACL,GAAG,CAAC,GAAGd,KAAK;QACpB;MACF;MAvDA,KAAK,IAAMc,GAAG,IAAII,UAAU;QAAAE,KAAA;MAAA;MAwD5B,OAAOD,KAAK;IACd;EAAA;AAAA", "ignoreList": []}