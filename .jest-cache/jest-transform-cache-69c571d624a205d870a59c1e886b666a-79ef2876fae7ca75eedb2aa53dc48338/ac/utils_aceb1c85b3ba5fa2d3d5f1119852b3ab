3ce338ef1232f01a15b3ac32724e96b2
'use strict';

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.VELOCITY_EPS = exports.SLOPE_FACTOR = void 0;
exports.isValidRubberBandConfig = isValidRubberBandConfig;
var _PlatformChecker = require("../../PlatformChecker.js");
var IS_WEB = (0, _PlatformChecker.isWeb)();
var VELOCITY_EPS = exports.VELOCITY_EPS = IS_WEB ? 1 / 20 : 1;
var SLOPE_FACTOR = exports.SLOPE_FACTOR = 0.1;
function isValidRubberBandConfig(config) {
  'worklet';

  return !!config.rubberBandEffect && Array.isArray(config.clamp) && config.clamp.length === 2;
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJPYmplY3QiLCJkZWZpbmVQcm9wZXJ0eSIsImV4cG9ydHMiLCJ2YWx1ZSIsIlZFTE9DSVRZX0VQUyIsIlNMT1BFX0ZBQ1RPUiIsImlzVmFsaWRSdWJiZXJCYW5kQ29uZmlnIiwiX1BsYXRmb3JtQ2hlY2tlciIsInJlcXVpcmUiLCJJU19XRUIiLCJpc1dlYiIsImNvbmZpZyIsInJ1YmJlckJhbmRFZmZlY3QiLCJBcnJheSIsImlzQXJyYXkiLCJjbGFtcCIsImxlbmd0aCJdLCJzb3VyY2VzIjpbIi4uLy4uLy4uLy4uL3NyYy9hbmltYXRpb24vZGVjYXkvdXRpbHMudHMiXSwic291cmNlc0NvbnRlbnQiOltudWxsXSwibWFwcGluZ3MiOiJBQUFBLFlBQVk7O0FBQUFBLE1BQUEsQ0FBQUMsY0FBQSxDQUFBQyxPQUFBO0VBQUFDLEtBQUE7QUFBQTtBQUFBRCxPQUFBLENBQUFFLFlBQUEsR0FBQUYsT0FBQSxDQUFBRyxZQUFBO0FBQUFILE9BQUEsQ0FBQUksdUJBQUEsR0FBQUEsdUJBQUE7QUFTWixJQUFBQyxnQkFBQSxHQUFBQyxPQUFBO0FBRUEsSUFBTUMsTUFBTSxHQUFHLElBQUFDLHNCQUFLLEVBQUMsQ0FBQztBQUNmLElBQU1OLFlBQVksR0FBQUYsT0FBQSxDQUFBRSxZQUFBLEdBQUdLLE1BQU0sR0FBRyxDQUFDLEdBQUcsRUFBRSxHQUFHLENBQUM7QUFDeEMsSUFBTUosWUFBWSxHQUFBSCxPQUFBLENBQUFHLFlBQUEsR0FBRyxHQUFHO0FBK0R4QixTQUFTQyx1QkFBdUJBLENBQ3JDSyxNQUEwQixFQUNPO0VBQ2pDLFNBQVM7O0VBQ1QsT0FDRSxDQUFDLENBQUNBLE1BQU0sQ0FBQ0MsZ0JBQWdCLElBQ3pCQyxLQUFLLENBQUNDLE9BQU8sQ0FBQ0gsTUFBTSxDQUFDSSxLQUFLLENBQUMsSUFDM0JKLE1BQU0sQ0FBQ0ksS0FBSyxDQUFDQyxNQUFNLEtBQUssQ0FBQztBQUU3QiIsImlnbm9yZUxpc3QiOltdfQ==