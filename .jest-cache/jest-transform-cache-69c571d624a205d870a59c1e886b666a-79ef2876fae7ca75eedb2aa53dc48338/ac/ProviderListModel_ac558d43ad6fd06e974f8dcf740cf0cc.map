{"version": 3, "names": ["cov_jo70a4xxm", "actualCoverage", "ProviderModel", "s", "f", "id", "serviceCode", "categoryCode", "subGroupId", "subgroupNameVn", "subgroupNameEn", "partnerCode", "partner<PERSON>ame", "autoBillSupport", "voucherSupport", "phoneRequired", "isRecommend", "partnerType", "payFee", "type", "paymentSupport", "description", "_classCallCheck2", "default", "_createClass2", "key", "value", "getName", "b", "getIconName", "_this$subGroupId$toSt", "_this$subGroupId", "toString", "isTopup", "setPartnerName", "name", "isViettelBill", "_this$subgroupNameVn", "toLocaleLowerCase", "includes", "exports"], "sources": ["/Users/<USER>/projects/react-native/payment-module/src/domain/entities/provider-list/ProviderListModel.ts"], "sourcesContent": ["// import Images from '../../../assets/images/Images';\n\nexport type ProviderListModel = ProviderModel[];\n\nexport class ProviderModel {\n  constructor(\n    public id?: number,\n    public serviceCode?: string,\n    public categoryCode?: string,\n    public subGroupId?: number,\n    public subgroupNameVn?: string,\n    public subgroupNameEn?: string,\n    public partnerCode?: string,\n    public partnerName?: string,\n    public autoBillSupport?: number,\n    public voucherSupport?: number,\n    public phoneRequired?: number,\n    public isRecommend?: number,\n    public partnerType?: number,\n    public payFee?: number,\n    public type?: number,\n    public paymentSupport?: number,\n    public description?: string,\n  ) {}\n\n  getName(): string {\n    return this.subgroupNameVn || this.description || '';\n  }\n\n  getIconName(): string {\n    return this.subGroupId?.toString() ?? '';\n  }\n\n  isTopup(): boolean {\n    return this.type === 2;\n  }\n\n  setPartnerName(name: string): void {\n    this.partnerName = name;\n  }\n\n  isViettelBill(): boolean {\n    return this.subgroupNameVn?.toLocaleLowerCase().includes('viettel') || false;\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAUW;IAAAA,aAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,aAAA;;;;;;;;;;;;;;;;;;IANEE,aAAa;AAAA;AAAA,CAAAF,aAAA,GAAAG,CAAA;EAAA;EAAAH,aAAA,GAAAI,CAAA;EACxB,SAAAF,cACSG,EAAW,EACXC,WAAoB,EACpBC,YAAqB,EACrBC,UAAmB,EACnBC,cAAuB,EACvBC,cAAuB,EACvBC,WAAoB,EACpBC,WAAoB,EACpBC,eAAwB,EACxBC,cAAuB,EACvBC,aAAsB,EACtBC,WAAoB,EACpBC,WAAoB,EACpBC,MAAe,EACfC,IAAa,EACbC,cAAuB,EACvBC,WAAoB;IAAA;IAAArB,aAAA,GAAAI,CAAA;IAAAJ,aAAA,GAAAG,CAAA;IAAA,IAAAmB,gBAAA,CAAAC,OAAA,QAAArB,aAAA;IAAA;IAAAF,aAAA,GAAAG,CAAA;IAhBpB,KAAAE,EAAE,GAAFA,EAAE;IAAA;IAAAL,aAAA,GAAAG,CAAA;IACF,KAAAG,WAAW,GAAXA,WAAW;IAAA;IAAAN,aAAA,GAAAG,CAAA;IACX,KAAAI,YAAY,GAAZA,YAAY;IAAA;IAAAP,aAAA,GAAAG,CAAA;IACZ,KAAAK,UAAU,GAAVA,UAAU;IAAA;IAAAR,aAAA,GAAAG,CAAA;IACV,KAAAM,cAAc,GAAdA,cAAc;IAAA;IAAAT,aAAA,GAAAG,CAAA;IACd,KAAAO,cAAc,GAAdA,cAAc;IAAA;IAAAV,aAAA,GAAAG,CAAA;IACd,KAAAQ,WAAW,GAAXA,WAAW;IAAA;IAAAX,aAAA,GAAAG,CAAA;IACX,KAAAS,WAAW,GAAXA,WAAW;IAAA;IAAAZ,aAAA,GAAAG,CAAA;IACX,KAAAU,eAAe,GAAfA,eAAe;IAAA;IAAAb,aAAA,GAAAG,CAAA;IACf,KAAAW,cAAc,GAAdA,cAAc;IAAA;IAAAd,aAAA,GAAAG,CAAA;IACd,KAAAY,aAAa,GAAbA,aAAa;IAAA;IAAAf,aAAA,GAAAG,CAAA;IACb,KAAAa,WAAW,GAAXA,WAAW;IAAA;IAAAhB,aAAA,GAAAG,CAAA;IACX,KAAAc,WAAW,GAAXA,WAAW;IAAA;IAAAjB,aAAA,GAAAG,CAAA;IACX,KAAAe,MAAM,GAANA,MAAM;IAAA;IAAAlB,aAAA,GAAAG,CAAA;IACN,KAAAgB,IAAI,GAAJA,IAAI;IAAA;IAAAnB,aAAA,GAAAG,CAAA;IACJ,KAAAiB,cAAc,GAAdA,cAAc;IAAA;IAAApB,aAAA,GAAAG,CAAA;IACd,KAAAkB,WAAW,GAAXA,WAAW;EACjB;EAAA;EAAArB,aAAA,GAAAG,CAAA;EAAC,WAAAqB,aAAA,CAAAD,OAAA,EAAArB,aAAA;IAAAuB,GAAA;IAAAC,KAAA,EAEJ,SAAAC,OAAOA,CAAA;MAAA;MAAA3B,aAAA,GAAAI,CAAA;MAAAJ,aAAA,GAAAG,CAAA;MACL,OAAO,2BAAAH,aAAA,GAAA4B,CAAA,cAAI,CAACnB,cAAc;MAAA;MAAA,CAAAT,aAAA,GAAA4B,CAAA,UAAI,IAAI,CAACP,WAAW;MAAA;MAAA,CAAArB,aAAA,GAAA4B,CAAA,UAAI,EAAE;IACtD;EAAC;IAAAH,GAAA;IAAAC,KAAA,EAED,SAAAG,WAAWA,CAAA;MAAA;MAAA7B,aAAA,GAAAI,CAAA;MAAA,IAAA0B,qBAAA,EAAAC,gBAAA;MAAA;MAAA/B,aAAA,GAAAG,CAAA;MACT,QAAA2B,qBAAA,IAAAC,gBAAA,GAAO,IAAI,CAACvB,UAAU;MAAA;MAAA,CAAAR,aAAA,GAAA4B,CAAA;MAAA;MAAA,CAAA5B,aAAA,GAAA4B,CAAA,UAAfG,gBAAA,CAAiBC,QAAQ,EAAE;MAAA;MAAA,CAAAhC,aAAA,GAAA4B,CAAA,UAAAE,qBAAA;MAAA;MAAA,CAAA9B,aAAA,GAAA4B,CAAA,UAAI,EAAE;IAC1C;EAAC;IAAAH,GAAA;IAAAC,KAAA,EAED,SAAAO,OAAOA,CAAA;MAAA;MAAAjC,aAAA,GAAAI,CAAA;MAAAJ,aAAA,GAAAG,CAAA;MACL,OAAO,IAAI,CAACgB,IAAI,KAAK,CAAC;IACxB;EAAC;IAAAM,GAAA;IAAAC,KAAA,EAED,SAAAQ,cAAcA,CAACC,IAAY;MAAA;MAAAnC,aAAA,GAAAI,CAAA;MAAAJ,aAAA,GAAAG,CAAA;MACzB,IAAI,CAACS,WAAW,GAAGuB,IAAI;IACzB;EAAC;IAAAV,GAAA;IAAAC,KAAA,EAED,SAAAU,aAAaA,CAAA;MAAA;MAAApC,aAAA,GAAAI,CAAA;MAAA,IAAAiC,oBAAA;MAAA;MAAArC,aAAA,GAAAG,CAAA;MACX,OAAO,2BAAAH,aAAA,GAAA4B,CAAA,WAAAS,oBAAA,OAAI,CAAC5B,cAAc;MAAA;MAAA,CAAAT,aAAA,GAAA4B,CAAA;MAAA;MAAA,CAAA5B,aAAA,GAAA4B,CAAA,UAAnBS,oBAAA,CAAqBC,iBAAiB,EAAE,CAACC,QAAQ,CAAC,SAAS,CAAC;MAAA;MAAA,CAAAvC,aAAA,GAAA4B,CAAA,UAAI,KAAK;IAC9E;EAAC;AAAA;AAAA;AAAA5B,aAAA,GAAAG,CAAA;AAvCHqC,OAAA,CAAAtC,aAAA,GAAAA,aAAA", "ignoreList": []}