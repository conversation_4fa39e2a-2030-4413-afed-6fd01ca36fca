{"version": 3, "names": [], "sources": ["/Users/<USER>/projects/react-native/payment-module/src/data/models/bill-validate/BillValidateResponse.ts"], "sourcesContent": ["export interface BillValidateResponse {\n  id: string;\n  originatorAccount: OriginatorAccountResponse;\n  instructionPriority: string;\n  requestedExecutionDate: string;\n  paymentMode: string;\n  paymentType: string;\n  transferTransactionInformation: TransferTransactionInformationResponse;\n  originator: OriginatorResponse;\n  totalAmount: CurrencyAmountResponse;\n  isIntraLegalEntityPaymentOrder: boolean;\n  canApprove: boolean;\n  finalApprover: boolean;\n}\n\nexport interface OriginatorAccountResponse {\n  arrangementId: string;\n  externalArrangementId: string;\n  identification: AccountIdentificationResponse;\n}\n\nexport interface AccountIdentificationResponse {\n  identification: string;\n  schemeName: string;\n}\n\nexport interface TransferTransactionInformationResponse {\n  counterparty: CounterpartyResponse;\n  counterpartyAccount: AccountIdentificationResponse;\n  instructedAmount: CurrencyAmountResponse;\n  additions: TransferAdditionsResponse;\n}\n\nexport interface CounterpartyResponse {\n  name: string;\n  role: string;\n}\n\nexport interface CurrencyAmountResponse {\n  amount: string;\n  currencyCode: string;\n}\n\nexport interface TransferAdditionsResponse {\n  bpQueryRef: string;\n  bpBillList: string; // Hoặc: BillItemResponse[] nếu parse JSON\n  bpSummary: string; // Hoặc: BpSummaryResponse nếu parse JSON\n  bpServiceCode: string;\n  cifNo: string;\n  bpCategory: string;\n  bpAccountingNumber: string;\n}\n\nexport interface OriginatorResponse {\n  name: string;\n  role: string;\n  postalAddress: Record<string, any>;\n}\n"], "mappings": "", "ignoreList": []}