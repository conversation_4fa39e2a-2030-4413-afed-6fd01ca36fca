240f94127f10f5145c54cd07cdb26be5
"use strict";

/* istanbul ignore next */
function cov_jo70a4xxm() {
  var path = "/Users/<USER>/projects/react-native/payment-module/src/domain/entities/provider-list/ProviderListModel.ts";
  var hash = "3fee43ec2c29a5466f27350d94ccd8db7d54db79";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/projects/react-native/payment-module/src/domain/entities/provider-list/ProviderListModel.ts",
    statementMap: {
      "0": {
        start: {
          line: 2,
          column: 29
        },
        end: {
          line: 2,
          column: 84
        }
      },
      "1": {
        start: {
          line: 3,
          column: 23
        },
        end: {
          line: 3,
          column: 95
        }
      },
      "2": {
        start: {
          line: 4,
          column: 20
        },
        end: {
          line: 4,
          column: 89
        }
      },
      "3": {
        start: {
          line: 5,
          column: 0
        },
        end: {
          line: 7,
          column: 3
        }
      },
      "4": {
        start: {
          line: 8,
          column: 0
        },
        end: {
          line: 8,
          column: 31
        }
      },
      "5": {
        start: {
          line: 9,
          column: 20
        },
        end: {
          line: 58,
          column: 3
        }
      },
      "6": {
        start: {
          line: 11,
          column: 4
        },
        end: {
          line: 11,
          column: 55
        }
      },
      "7": {
        start: {
          line: 12,
          column: 4
        },
        end: {
          line: 12,
          column: 17
        }
      },
      "8": {
        start: {
          line: 13,
          column: 4
        },
        end: {
          line: 13,
          column: 35
        }
      },
      "9": {
        start: {
          line: 14,
          column: 4
        },
        end: {
          line: 14,
          column: 37
        }
      },
      "10": {
        start: {
          line: 15,
          column: 4
        },
        end: {
          line: 15,
          column: 33
        }
      },
      "11": {
        start: {
          line: 16,
          column: 4
        },
        end: {
          line: 16,
          column: 41
        }
      },
      "12": {
        start: {
          line: 17,
          column: 4
        },
        end: {
          line: 17,
          column: 41
        }
      },
      "13": {
        start: {
          line: 18,
          column: 4
        },
        end: {
          line: 18,
          column: 35
        }
      },
      "14": {
        start: {
          line: 19,
          column: 4
        },
        end: {
          line: 19,
          column: 35
        }
      },
      "15": {
        start: {
          line: 20,
          column: 4
        },
        end: {
          line: 20,
          column: 43
        }
      },
      "16": {
        start: {
          line: 21,
          column: 4
        },
        end: {
          line: 21,
          column: 41
        }
      },
      "17": {
        start: {
          line: 22,
          column: 4
        },
        end: {
          line: 22,
          column: 39
        }
      },
      "18": {
        start: {
          line: 23,
          column: 4
        },
        end: {
          line: 23,
          column: 35
        }
      },
      "19": {
        start: {
          line: 24,
          column: 4
        },
        end: {
          line: 24,
          column: 35
        }
      },
      "20": {
        start: {
          line: 25,
          column: 4
        },
        end: {
          line: 25,
          column: 25
        }
      },
      "21": {
        start: {
          line: 26,
          column: 4
        },
        end: {
          line: 26,
          column: 21
        }
      },
      "22": {
        start: {
          line: 27,
          column: 4
        },
        end: {
          line: 27,
          column: 41
        }
      },
      "23": {
        start: {
          line: 28,
          column: 4
        },
        end: {
          line: 28,
          column: 35
        }
      },
      "24": {
        start: {
          line: 30,
          column: 2
        },
        end: {
          line: 57,
          column: 6
        }
      },
      "25": {
        start: {
          line: 33,
          column: 6
        },
        end: {
          line: 33,
          column: 59
        }
      },
      "26": {
        start: {
          line: 39,
          column: 6
        },
        end: {
          line: 39,
          column: 160
        }
      },
      "27": {
        start: {
          line: 44,
          column: 6
        },
        end: {
          line: 44,
          column: 29
        }
      },
      "28": {
        start: {
          line: 49,
          column: 6
        },
        end: {
          line: 49,
          column: 30
        }
      },
      "29": {
        start: {
          line: 55,
          column: 6
        },
        end: {
          line: 55,
          column: 149
        }
      },
      "30": {
        start: {
          line: 59,
          column: 0
        },
        end: {
          line: 59,
          column: 38
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 9,
            column: 20
          },
          end: {
            line: 9,
            column: 21
          }
        },
        loc: {
          start: {
            line: 9,
            column: 32
          },
          end: {
            line: 58,
            column: 1
          }
        },
        line: 9
      },
      "1": {
        name: "ProviderModel",
        decl: {
          start: {
            line: 10,
            column: 11
          },
          end: {
            line: 10,
            column: 24
          }
        },
        loc: {
          start: {
            line: 10,
            column: 243
          },
          end: {
            line: 29,
            column: 3
          }
        },
        line: 10
      },
      "2": {
        name: "getName",
        decl: {
          start: {
            line: 32,
            column: 20
          },
          end: {
            line: 32,
            column: 27
          }
        },
        loc: {
          start: {
            line: 32,
            column: 30
          },
          end: {
            line: 34,
            column: 5
          }
        },
        line: 32
      },
      "3": {
        name: "getIconName",
        decl: {
          start: {
            line: 37,
            column: 20
          },
          end: {
            line: 37,
            column: 31
          }
        },
        loc: {
          start: {
            line: 37,
            column: 34
          },
          end: {
            line: 40,
            column: 5
          }
        },
        line: 37
      },
      "4": {
        name: "isTopup",
        decl: {
          start: {
            line: 43,
            column: 20
          },
          end: {
            line: 43,
            column: 27
          }
        },
        loc: {
          start: {
            line: 43,
            column: 30
          },
          end: {
            line: 45,
            column: 5
          }
        },
        line: 43
      },
      "5": {
        name: "setPartnerName",
        decl: {
          start: {
            line: 48,
            column: 20
          },
          end: {
            line: 48,
            column: 34
          }
        },
        loc: {
          start: {
            line: 48,
            column: 41
          },
          end: {
            line: 50,
            column: 5
          }
        },
        line: 48
      },
      "6": {
        name: "isViettelBill",
        decl: {
          start: {
            line: 53,
            column: 20
          },
          end: {
            line: 53,
            column: 33
          }
        },
        loc: {
          start: {
            line: 53,
            column: 36
          },
          end: {
            line: 56,
            column: 5
          }
        },
        line: 53
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 33,
            column: 13
          },
          end: {
            line: 33,
            column: 58
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 33,
            column: 13
          },
          end: {
            line: 33,
            column: 32
          }
        }, {
          start: {
            line: 33,
            column: 36
          },
          end: {
            line: 33,
            column: 52
          }
        }, {
          start: {
            line: 33,
            column: 56
          },
          end: {
            line: 33,
            column: 58
          }
        }],
        line: 33
      },
      "1": {
        loc: {
          start: {
            line: 39,
            column: 13
          },
          end: {
            line: 39,
            column: 159
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 39,
            column: 133
          },
          end: {
            line: 39,
            column: 154
          }
        }, {
          start: {
            line: 39,
            column: 157
          },
          end: {
            line: 39,
            column: 159
          }
        }],
        line: 39
      },
      "2": {
        loc: {
          start: {
            line: 39,
            column: 38
          },
          end: {
            line: 39,
            column: 121
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 39,
            column: 85
          },
          end: {
            line: 39,
            column: 91
          }
        }, {
          start: {
            line: 39,
            column: 94
          },
          end: {
            line: 39,
            column: 121
          }
        }],
        line: 39
      },
      "3": {
        loc: {
          start: {
            line: 55,
            column: 13
          },
          end: {
            line: 55,
            column: 148
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 55,
            column: 14
          },
          end: {
            line: 55,
            column: 138
          }
        }, {
          start: {
            line: 55,
            column: 143
          },
          end: {
            line: 55,
            column: 148
          }
        }],
        line: 55
      },
      "4": {
        loc: {
          start: {
            line: 55,
            column: 14
          },
          end: {
            line: 55,
            column: 138
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 55,
            column: 69
          },
          end: {
            line: 55,
            column: 75
          }
        }, {
          start: {
            line: 55,
            column: 78
          },
          end: {
            line: 55,
            column: 138
          }
        }],
        line: 55
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0
    },
    b: {
      "0": [0, 0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0]
    },
    inputSourceMap: {
      version: 3,
      file: undefined,
      names: ["ProviderModel", "id", "serviceCode", "categoryCode", "subGroupId", "subgroupNameVn", "subgroupNameEn", "partnerCode", "partnerName", "autoBillSupport", "voucherSupport", "phoneRequired", "isRecommend", "partnerType", "payFee", "type", "paymentSupport", "description", "_classCallCheck2", "default", "_createClass2", "key", "value", "getName", "getIconName", "_this$subGroupId$toSt", "_this$subGroupId", "toString", "isTopup", "setPartnerName", "name", "isViettelBill", "_this$subgroupNameVn", "toLocaleLowerCase", "includes", "exports"],
      sourceRoot: undefined,
      sources: ["/Users/<USER>/projects/react-native/payment-module/src/domain/entities/provider-list/ProviderListModel.ts"],
      sourcesContent: ["// import Images from '../../../assets/images/Images';\n\nexport type ProviderListModel = ProviderModel[];\n\nexport class ProviderModel {\n  constructor(\n    public id?: number,\n    public serviceCode?: string,\n    public categoryCode?: string,\n    public subGroupId?: number,\n    public subgroupNameVn?: string,\n    public subgroupNameEn?: string,\n    public partnerCode?: string,\n    public partnerName?: string,\n    public autoBillSupport?: number,\n    public voucherSupport?: number,\n    public phoneRequired?: number,\n    public isRecommend?: number,\n    public partnerType?: number,\n    public payFee?: number,\n    public type?: number,\n    public paymentSupport?: number,\n    public description?: string,\n  ) {}\n\n  getName(): string {\n    return this.subgroupNameVn || this.description || '';\n  }\n\n  getIconName(): string {\n    return this.subGroupId?.toString() ?? '';\n  }\n\n  isTopup(): boolean {\n    return this.type === 2;\n  }\n\n  setPartnerName(name: string): void {\n    this.partnerName = name;\n  }\n\n  isViettelBill(): boolean {\n    return this.subgroupNameVn?.toLocaleLowerCase().includes('viettel') || false;\n  }\n}\n"],
      mappings: ";;;;;;;;IAIaA,aAAa;EACxB,SAAAA,cACSC,EAAW,EACXC,WAAoB,EACpBC,YAAqB,EACrBC,UAAmB,EACnBC,cAAuB,EACvBC,cAAuB,EACvBC,WAAoB,EACpBC,WAAoB,EACpBC,eAAwB,EACxBC,cAAuB,EACvBC,aAAsB,EACtBC,WAAoB,EACpBC,WAAoB,EACpBC,MAAe,EACfC,IAAa,EACbC,cAAuB,EACvBC,WAAoB;IAAA,IAAAC,gBAAA,CAAAC,OAAA,QAAAnB,aAAA;IAhBpB,KAAAC,EAAE,GAAFA,EAAE;IACF,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,UAAU,GAAVA,UAAU;IACV,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,IAAI,GAAJA,IAAI;IACJ,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,WAAW,GAAXA,WAAW;EACjB;EAAC,WAAAG,aAAA,CAAAD,OAAA,EAAAnB,aAAA;IAAAqB,GAAA;IAAAC,KAAA,EAEJ,SAAAC,OAAOA,CAAA;MACL,OAAO,IAAI,CAAClB,cAAc,IAAI,IAAI,CAACY,WAAW,IAAI,EAAE;IACtD;EAAC;IAAAI,GAAA;IAAAC,KAAA,EAED,SAAAE,WAAWA,CAAA;MAAA,IAAAC,qBAAA,EAAAC,gBAAA;MACT,QAAAD,qBAAA,IAAAC,gBAAA,GAAO,IAAI,CAACtB,UAAU,qBAAfsB,gBAAA,CAAiBC,QAAQ,EAAE,YAAAF,qBAAA,GAAI,EAAE;IAC1C;EAAC;IAAAJ,GAAA;IAAAC,KAAA,EAED,SAAAM,OAAOA,CAAA;MACL,OAAO,IAAI,CAACb,IAAI,KAAK,CAAC;IACxB;EAAC;IAAAM,GAAA;IAAAC,KAAA,EAED,SAAAO,cAAcA,CAACC,IAAY;MACzB,IAAI,CAACtB,WAAW,GAAGsB,IAAI;IACzB;EAAC;IAAAT,GAAA;IAAAC,KAAA,EAED,SAAAS,aAAaA,CAAA;MAAA,IAAAC,oBAAA;MACX,OAAO,EAAAA,oBAAA,OAAI,CAAC3B,cAAc,qBAAnB2B,oBAAA,CAAqBC,iBAAiB,EAAE,CAACC,QAAQ,CAAC,SAAS,CAAC,KAAI,KAAK;IAC9E;EAAC;AAAA;AAvCHC,OAAA,CAAAnC,aAAA,GAAAA,aAAA",
      ignoreList: []
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "3fee43ec2c29a5466f27350d94ccd8db7d54db79"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_jo70a4xxm = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_jo70a4xxm();
var _interopRequireDefault =
/* istanbul ignore next */
(cov_jo70a4xxm().s[0]++, require("@babel/runtime/helpers/interopRequireDefault"));
var _classCallCheck2 =
/* istanbul ignore next */
(cov_jo70a4xxm().s[1]++, _interopRequireDefault(require("@babel/runtime/helpers/classCallCheck")));
var _createClass2 =
/* istanbul ignore next */
(cov_jo70a4xxm().s[2]++, _interopRequireDefault(require("@babel/runtime/helpers/createClass")));
/* istanbul ignore next */
cov_jo70a4xxm().s[3]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_jo70a4xxm().s[4]++;
exports.ProviderModel = void 0;
var ProviderModel =
/* istanbul ignore next */
(cov_jo70a4xxm().s[5]++, function () {
  /* istanbul ignore next */
  cov_jo70a4xxm().f[0]++;
  function ProviderModel(id, serviceCode, categoryCode, subGroupId, subgroupNameVn, subgroupNameEn, partnerCode, partnerName, autoBillSupport, voucherSupport, phoneRequired, isRecommend, partnerType, payFee, type, paymentSupport, description) {
    /* istanbul ignore next */
    cov_jo70a4xxm().f[1]++;
    cov_jo70a4xxm().s[6]++;
    (0, _classCallCheck2.default)(this, ProviderModel);
    /* istanbul ignore next */
    cov_jo70a4xxm().s[7]++;
    this.id = id;
    /* istanbul ignore next */
    cov_jo70a4xxm().s[8]++;
    this.serviceCode = serviceCode;
    /* istanbul ignore next */
    cov_jo70a4xxm().s[9]++;
    this.categoryCode = categoryCode;
    /* istanbul ignore next */
    cov_jo70a4xxm().s[10]++;
    this.subGroupId = subGroupId;
    /* istanbul ignore next */
    cov_jo70a4xxm().s[11]++;
    this.subgroupNameVn = subgroupNameVn;
    /* istanbul ignore next */
    cov_jo70a4xxm().s[12]++;
    this.subgroupNameEn = subgroupNameEn;
    /* istanbul ignore next */
    cov_jo70a4xxm().s[13]++;
    this.partnerCode = partnerCode;
    /* istanbul ignore next */
    cov_jo70a4xxm().s[14]++;
    this.partnerName = partnerName;
    /* istanbul ignore next */
    cov_jo70a4xxm().s[15]++;
    this.autoBillSupport = autoBillSupport;
    /* istanbul ignore next */
    cov_jo70a4xxm().s[16]++;
    this.voucherSupport = voucherSupport;
    /* istanbul ignore next */
    cov_jo70a4xxm().s[17]++;
    this.phoneRequired = phoneRequired;
    /* istanbul ignore next */
    cov_jo70a4xxm().s[18]++;
    this.isRecommend = isRecommend;
    /* istanbul ignore next */
    cov_jo70a4xxm().s[19]++;
    this.partnerType = partnerType;
    /* istanbul ignore next */
    cov_jo70a4xxm().s[20]++;
    this.payFee = payFee;
    /* istanbul ignore next */
    cov_jo70a4xxm().s[21]++;
    this.type = type;
    /* istanbul ignore next */
    cov_jo70a4xxm().s[22]++;
    this.paymentSupport = paymentSupport;
    /* istanbul ignore next */
    cov_jo70a4xxm().s[23]++;
    this.description = description;
  }
  /* istanbul ignore next */
  cov_jo70a4xxm().s[24]++;
  return (0, _createClass2.default)(ProviderModel, [{
    key: "getName",
    value: function getName() {
      /* istanbul ignore next */
      cov_jo70a4xxm().f[2]++;
      cov_jo70a4xxm().s[25]++;
      return /* istanbul ignore next */(cov_jo70a4xxm().b[0][0]++, this.subgroupNameVn) ||
      /* istanbul ignore next */
      (cov_jo70a4xxm().b[0][1]++, this.description) ||
      /* istanbul ignore next */
      (cov_jo70a4xxm().b[0][2]++, '');
    }
  }, {
    key: "getIconName",
    value: function getIconName() {
      /* istanbul ignore next */
      cov_jo70a4xxm().f[3]++;
      var _this$subGroupId$toSt, _this$subGroupId;
      /* istanbul ignore next */
      cov_jo70a4xxm().s[26]++;
      return (_this$subGroupId$toSt = (_this$subGroupId = this.subGroupId) == null ?
      /* istanbul ignore next */
      (cov_jo70a4xxm().b[2][0]++, void 0) :
      /* istanbul ignore next */
      (cov_jo70a4xxm().b[2][1]++, _this$subGroupId.toString())) != null ?
      /* istanbul ignore next */
      (cov_jo70a4xxm().b[1][0]++, _this$subGroupId$toSt) :
      /* istanbul ignore next */
      (cov_jo70a4xxm().b[1][1]++, '');
    }
  }, {
    key: "isTopup",
    value: function isTopup() {
      /* istanbul ignore next */
      cov_jo70a4xxm().f[4]++;
      cov_jo70a4xxm().s[27]++;
      return this.type === 2;
    }
  }, {
    key: "setPartnerName",
    value: function setPartnerName(name) {
      /* istanbul ignore next */
      cov_jo70a4xxm().f[5]++;
      cov_jo70a4xxm().s[28]++;
      this.partnerName = name;
    }
  }, {
    key: "isViettelBill",
    value: function isViettelBill() {
      /* istanbul ignore next */
      cov_jo70a4xxm().f[6]++;
      var _this$subgroupNameVn;
      /* istanbul ignore next */
      cov_jo70a4xxm().s[29]++;
      return /* istanbul ignore next */(cov_jo70a4xxm().b[3][0]++, (_this$subgroupNameVn = this.subgroupNameVn) == null ?
      /* istanbul ignore next */
      (cov_jo70a4xxm().b[4][0]++, void 0) :
      /* istanbul ignore next */
      (cov_jo70a4xxm().b[4][1]++, _this$subgroupNameVn.toLocaleLowerCase().includes('viettel'))) ||
      /* istanbul ignore next */
      (cov_jo70a4xxm().b[3][1]++, false);
    }
  }]);
}());
/* istanbul ignore next */
cov_jo70a4xxm().s[30]++;
exports.ProviderModel = ProviderModel;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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