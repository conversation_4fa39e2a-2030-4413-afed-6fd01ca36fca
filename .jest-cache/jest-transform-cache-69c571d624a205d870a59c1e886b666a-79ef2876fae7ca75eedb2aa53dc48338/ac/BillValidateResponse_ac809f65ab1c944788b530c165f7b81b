c54615aee7ca24f06901d3d8f46e9114
"use strict";

/* istanbul ignore next */
function cov_2gvem2nx60() {
  var path = "/Users/<USER>/projects/react-native/payment-module/src/data/models/bill-validate/BillValidateResponse.ts";
  var hash = "660958af5bdcfa3125b9258403cb1429bbb5730e";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/projects/react-native/payment-module/src/data/models/bill-validate/BillValidateResponse.ts",
    statementMap: {
      "0": {
        start: {
          line: 3,
          column: 0
        },
        end: {
          line: 5,
          column: 3
        }
      }
    },
    fnMap: {},
    branchMap: {},
    s: {
      "0": 0
    },
    f: {},
    b: {},
    inputSourceMap: {
      version: 3,
      file: undefined,
      names: [],
      sourceRoot: undefined,
      sources: ["/Users/<USER>/projects/react-native/payment-module/src/data/models/bill-validate/BillValidateResponse.ts"],
      sourcesContent: ["export interface BillValidateResponse {\n  id: string;\n  originatorAccount: OriginatorAccountResponse;\n  instructionPriority: string;\n  requestedExecutionDate: string;\n  paymentMode: string;\n  paymentType: string;\n  transferTransactionInformation: TransferTransactionInformationResponse;\n  originator: OriginatorResponse;\n  totalAmount: CurrencyAmountResponse;\n  isIntraLegalEntityPaymentOrder: boolean;\n  canApprove: boolean;\n  finalApprover: boolean;\n}\n\nexport interface OriginatorAccountResponse {\n  arrangementId: string;\n  externalArrangementId: string;\n  identification: AccountIdentificationResponse;\n}\n\nexport interface AccountIdentificationResponse {\n  identification: string;\n  schemeName: string;\n}\n\nexport interface TransferTransactionInformationResponse {\n  counterparty: CounterpartyResponse;\n  counterpartyAccount: AccountIdentificationResponse;\n  instructedAmount: CurrencyAmountResponse;\n  additions: TransferAdditionsResponse;\n}\n\nexport interface CounterpartyResponse {\n  name: string;\n  role: string;\n}\n\nexport interface CurrencyAmountResponse {\n  amount: string;\n  currencyCode: string;\n}\n\nexport interface TransferAdditionsResponse {\n  bpQueryRef: string;\n  bpBillList: string; // Ho\u1EB7c: BillItemResponse[] n\u1EBFu parse JSON\n  bpSummary: string; // Ho\u1EB7c: BpSummaryResponse n\u1EBFu parse JSON\n  bpServiceCode: string;\n  cifNo: string;\n  bpCategory: string;\n  bpAccountingNumber: string;\n}\n\nexport interface OriginatorResponse {\n  name: string;\n  role: string;\n  postalAddress: Record<string, any>;\n}\n"],
      mappings: "",
      ignoreList: []
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "660958af5bdcfa3125b9258403cb1429bbb5730e"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_2gvem2nx60 = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_2gvem2nx60();
cov_2gvem2nx60().s[0]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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