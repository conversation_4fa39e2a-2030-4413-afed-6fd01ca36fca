{"version": 3, "names": ["_interopRequireDefault", "require", "Object", "defineProperty", "exports", "value", "default", "_classCallCheck2", "_createClass2", "_possibleConstructorReturn2", "_getPrototypeOf2", "_inherits2", "_callSuper", "t", "o", "e", "_isNativeReflectConstruct", "Reflect", "construct", "constructor", "apply", "Boolean", "prototype", "valueOf", "call", "React", "nativeTag", "_default", "viewName", "Component", "_React$Component", "_this", "_len", "arguments", "length", "args", "Array", "_key", "concat", "_nativeTag", "blur", "jest", "fn", "focus", "measure", "measureInWindow", "measureLayout", "setNativeProps", "key", "render", "createElement", "props", "children", "displayName"], "sources": ["mockNativeComponent.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @format\n */\n\n'use strict';\n\nconst React = require('react');\n\nlet nativeTag = 1;\n\nexport default viewName => {\n  const Component = class extends React.Component {\n    _nativeTag = nativeTag++;\n\n    render() {\n      return React.createElement(viewName, this.props, this.props.children);\n    }\n\n    // The methods that exist on host components\n    blur = jest.fn();\n    focus = jest.fn();\n    measure = jest.fn();\n    measureInWindow = jest.fn();\n    measureLayout = jest.fn();\n    setNativeProps = jest.fn();\n  };\n\n  if (viewName === 'RCTView') {\n    Component.displayName = 'View';\n  } else {\n    Component.displayName = viewName;\n  }\n\n  return Component;\n};\n"], "mappings": "AASA,YAAY;;AAAC,IAAAA,sBAAA,GAAAC,OAAA;AAAAC,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,KAAA;AAAA;AAAAD,OAAA,CAAAE,OAAA;AAAA,IAAAC,gBAAA,GAAAP,sBAAA,CAAAC,OAAA;AAAA,IAAAO,aAAA,GAAAR,sBAAA,CAAAC,OAAA;AAAA,IAAAQ,2BAAA,GAAAT,sBAAA,CAAAC,OAAA;AAAA,IAAAS,gBAAA,GAAAV,sBAAA,CAAAC,OAAA;AAAA,IAAAU,UAAA,GAAAX,sBAAA,CAAAC,OAAA;AAAA,SAAAW,WAAAC,CAAA,EAAAC,CAAA,EAAAC,CAAA,WAAAD,CAAA,OAAAJ,gBAAA,CAAAJ,OAAA,EAAAQ,CAAA,OAAAL,2BAAA,CAAAH,OAAA,EAAAO,CAAA,EAAAG,yBAAA,KAAAC,OAAA,CAAAC,SAAA,CAAAJ,CAAA,EAAAC,CAAA,YAAAL,gBAAA,CAAAJ,OAAA,EAAAO,CAAA,EAAAM,WAAA,IAAAL,CAAA,CAAAM,KAAA,CAAAP,CAAA,EAAAE,CAAA;AAAA,SAAAC,0BAAA,cAAAH,CAAA,IAAAQ,OAAA,CAAAC,SAAA,CAAAC,OAAA,CAAAC,IAAA,CAAAP,OAAA,CAAAC,SAAA,CAAAG,OAAA,iCAAAR,CAAA,aAAAG,yBAAA,YAAAA,0BAAA,aAAAH,CAAA;AAEb,IAAMY,KAAK,GAAGxB,OAAO,CAAC,OAAO,CAAC;AAE9B,IAAIyB,SAAS,GAAG,CAAC;AAAC,IAAAC,QAAA,GAAAvB,OAAA,CAAAE,OAAA,GAEH,SAAAqB,SAAAC,QAAQ,EAAI;EACzB,IAAMC,SAAS,aAAAC,gBAAA;IAAA,SAAAD,UAAA;MAAA,IAAAE,KAAA;MAAA,IAAAxB,gBAAA,CAAAD,OAAA,QAAAuB,SAAA;MAAA,SAAAG,IAAA,GAAAC,SAAA,CAAAC,MAAA,EAAAC,IAAA,OAAAC,KAAA,CAAAJ,IAAA,GAAAK,IAAA,MAAAA,IAAA,GAAAL,IAAA,EAAAK,IAAA;QAAAF,IAAA,CAAAE,IAAA,IAAAJ,SAAA,CAAAI,IAAA;MAAA;MAAAN,KAAA,GAAAnB,UAAA,OAAAiB,SAAA,KAAAS,MAAA,CAAAH,IAAA;MAAAJ,KAAA,CACbQ,UAAU,GAAGb,SAAS,EAAE;MAAAK,KAAA,CAOxBS,IAAI,GAAGC,IAAI,CAACC,EAAE,CAAC,CAAC;MAAAX,KAAA,CAChBY,KAAK,GAAGF,IAAI,CAACC,EAAE,CAAC,CAAC;MAAAX,KAAA,CACjBa,OAAO,GAAGH,IAAI,CAACC,EAAE,CAAC,CAAC;MAAAX,KAAA,CACnBc,eAAe,GAAGJ,IAAI,CAACC,EAAE,CAAC,CAAC;MAAAX,KAAA,CAC3Be,aAAa,GAAGL,IAAI,CAACC,EAAE,CAAC,CAAC;MAAAX,KAAA,CACzBgB,cAAc,GAAGN,IAAI,CAACC,EAAE,CAAC,CAAC;MAAA,OAAAX,KAAA;IAAA;IAAA,IAAApB,UAAA,CAAAL,OAAA,EAAAuB,SAAA,EAAAC,gBAAA;IAAA,WAAAtB,aAAA,CAAAF,OAAA,EAAAuB,SAAA;MAAAmB,GAAA;MAAA3C,KAAA,EAV1B,SAAA4C,MAAMA,CAAA,EAAG;QACP,OAAOxB,KAAK,CAACyB,aAAa,CAACtB,QAAQ,EAAE,IAAI,CAACuB,KAAK,EAAE,IAAI,CAACA,KAAK,CAACC,QAAQ,CAAC;MACvE;IAAC;EAAA,EAL6B3B,KAAK,CAACI,SAAS,CAc9C;EAED,IAAID,QAAQ,KAAK,SAAS,EAAE;IAC1BC,SAAS,CAACwB,WAAW,GAAG,MAAM;EAChC,CAAC,MAAM;IACLxB,SAAS,CAACwB,WAAW,GAAGzB,QAAQ;EAClC;EAEA,OAAOC,SAAS;AAClB,CAAC", "ignoreList": []}