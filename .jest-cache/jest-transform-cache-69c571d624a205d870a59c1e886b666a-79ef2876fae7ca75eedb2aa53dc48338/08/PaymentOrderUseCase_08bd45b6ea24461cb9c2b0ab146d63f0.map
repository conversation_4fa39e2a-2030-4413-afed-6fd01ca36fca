{"version": 3, "names": ["cov_22kz54lma4", "actualCoverage", "ExcecutionHandler_1", "s", "require", "PaymentOrderUseCase", "f", "repository", "_classCallCheck2", "default", "_createClass2", "key", "value", "_execute", "_asyncToGenerator2", "request", "_this", "ExecutionHandler", "execute", "paymentOrder", "_x", "apply", "arguments", "exports"], "sources": ["/Users/<USER>/projects/react-native/payment-module/src/domain/usecases/payment-order/PaymentOrderUseCase.ts"], "sourcesContent": ["import {IPaymentOrderRepository} from '../../repositories/IPaymentOrderRepository';\nimport {PaymentOrderModel} from '../../entities/payment-order/PaymentOrderModel';\nimport {ResultState} from '../../../core/ResultState';\nimport {ExecutionHandler} from '../../../utils/ExcecutionHandler';\nimport {PaymentOrderRequest} from '../../../data/models/payment-order/PaymentOrderRequest';\nexport class PaymentOrderUseCase {\n  private repository: IPaymentOrderRepository;\n\n  constructor(repository: IPaymentOrderRepository) {\n    this.repository = repository;\n  }\n\n  public async execute(request: PaymentOrderRequest): Promise<ResultState<PaymentOrderModel>> {\n    // call this.repository.paymentOrder(...)\n    return ExecutionHandler.execute(() => this.repository.paymentOrder(request));\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAUE;IAAAA,cAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,cAAA;;;;;;;;;;;;;;;;;;;;;AAPF,IAAAE,mBAAA;AAAA;AAAA,CAAAF,cAAA,GAAAG,CAAA,OAAAC,OAAA;AAAkE,IAErDC,mBAAmB;AAAA;AAAA,CAAAL,cAAA,GAAAG,CAAA;EAAA;EAAAH,cAAA,GAAAM,CAAA;EAG9B,SAAAD,oBAAYE,UAAmC;IAAA;IAAAP,cAAA,GAAAM,CAAA;IAAAN,cAAA,GAAAG,CAAA;IAAA,IAAAK,gBAAA,CAAAC,OAAA,QAAAJ,mBAAA;IAAA;IAAAL,cAAA,GAAAG,CAAA;IAC7C,IAAI,CAACI,UAAU,GAAGA,UAAU;EAC9B;EAAA;EAAAP,cAAA,GAAAG,CAAA;EAAC,WAAAO,aAAA,CAAAD,OAAA,EAAAJ,mBAAA;IAAAM,GAAA;IAAAC,KAAA;MAAA;MAAAZ,cAAA,GAAAM,CAAA;MAAA,IAAAO,QAAA;MAAA;MAAA,CAAAb,cAAA,GAAAG,CAAA,YAAAW,kBAAA,CAAAL,OAAA,EAEM,WAAcM,OAA4B;QAAA;QAAAf,cAAA,GAAAM,CAAA;QAAA,IAAAU,KAAA;QAAA;QAAA,CAAAhB,cAAA,GAAAG,CAAA;QAAA;QAAAH,cAAA,GAAAG,CAAA;QAE/C,OAAOD,mBAAA,CAAAe,gBAAgB,CAACC,OAAO,CAAC;UAAA;UAAAlB,cAAA,GAAAM,CAAA;UAAAN,cAAA,GAAAG,CAAA;UAAA,OAAMa,KAAI,CAACT,UAAU,CAACY,YAAY,CAACJ,OAAO,CAAC;QAAA,EAAC;MAC9E,CAAC;MAAA,SAHYG,OAAOA,CAAAE,EAAA;QAAA;QAAApB,cAAA,GAAAM,CAAA;QAAAN,cAAA,GAAAG,CAAA;QAAA,OAAAU,QAAA,CAAAQ,KAAA,OAAAC,SAAA;MAAA;MAAA;MAAAtB,cAAA,GAAAG,CAAA;MAAA,OAAPe,OAAO;IAAA;EAAA;AAAA;AAAA;AAAAlB,cAAA,GAAAG,CAAA;AAPtBoB,OAAA,CAAAlB,mBAAA,GAAAA,mBAAA", "ignoreList": []}