{"version": 3, "names": ["_useMergeRefs", "_interopRequireDefault", "require", "_react", "_interopRequireWildcard", "React", "_getRequireWildcardCache", "e", "WeakMap", "r", "t", "__esModule", "default", "has", "get", "n", "__proto__", "a", "Object", "defineProperty", "getOwnPropertyDescriptor", "u", "hasOwnProperty", "call", "i", "set", "injectedImageComponentDecorator", "unstable_setImageComponentDecorator", "imageComponentDecorator", "unstable_getImageComponentDecorator", "imageAttachedCallbacks", "Set", "unstable_registerImageAttachedCallback", "callback", "add", "unstable_unregisterImageAttachedCallback", "delete", "useWrapRefWithImageAttachedCallbacks", "forwardedRef", "pendingCleanupCallbacks", "useRef", "imageAttachedCallbacksRef", "current", "node", "length", "for<PERSON>ach", "cb", "imageAttachedCallback", "maybe<PERSON>leanup<PERSON>allback", "push", "useMergeRefs"], "sources": ["ImageInjection.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @format\n * @flow strict-local\n */\n\nimport type {\n  AbstractImageAndroid,\n  AbstractImageIOS,\n  Image as ImageComponent,\n} from './ImageTypes.flow';\n\nimport useMergeRefs from '../Utilities/useMergeRefs';\nimport * as React from 'react';\nimport {useRef} from 'react';\n\ntype ImageComponentDecorator = (AbstractImageAndroid => AbstractImageAndroid) &\n  (AbstractImageIOS => AbstractImageIOS);\n\nlet injectedImageComponentDecorator: ?ImageComponentDecorator;\n\nexport function unstable_setImageComponentDecorator(\n  imageComponentDecorator: ?ImageComponentDecorator,\n): void {\n  injectedImageComponentDecorator = imageComponentDecorator;\n}\n\nexport function unstable_getImageComponentDecorator(): ?ImageComponentDecorator {\n  return injectedImageComponentDecorator;\n}\n\ntype ImageInstance = React.ElementRef<ImageComponent>;\n\ntype ImageAttachedCallback = (\n  imageInstance: ImageInstance,\n) => void | (() => void);\n\nconst imageAttachedCallbacks = new Set<ImageAttachedCallback>();\n\nexport function unstable_registerImageAttachedCallback(\n  callback: ImageAttachedCallback,\n): void {\n  imageAttachedCallbacks.add(callback);\n}\n\nexport function unstable_unregisterImageAttachedCallback(\n  callback: ImageAttachedCallback,\n): void {\n  imageAttachedCallbacks.delete(callback);\n}\n\nexport function useWrapRefWithImageAttachedCallbacks(\n  forwardedRef: React.RefSetter<ImageInstance>,\n): React.RefSetter<ImageInstance> {\n  const pendingCleanupCallbacks = useRef<Array<() => void>>([]);\n\n  const imageAttachedCallbacksRef =\n    useRef<?(node: ImageInstance | null) => void>(null);\n\n  if (imageAttachedCallbacksRef.current == null) {\n    imageAttachedCallbacksRef.current = (node: ImageInstance | null): void => {\n      if (node == null) {\n        if (pendingCleanupCallbacks.current.length > 0) {\n          pendingCleanupCallbacks.current.forEach(cb => cb());\n          pendingCleanupCallbacks.current = [];\n        }\n      } else {\n        imageAttachedCallbacks.forEach(imageAttachedCallback => {\n          const maybeCleanupCallback = imageAttachedCallback(node);\n          if (maybeCleanupCallback != null) {\n            pendingCleanupCallbacks.current.push(maybeCleanupCallback);\n          }\n        });\n      }\n    };\n  }\n\n  // `useMergeRefs` returns a stable ref if its arguments don't change.\n  return useMergeRefs<ImageInstance>(\n    forwardedRef,\n    imageAttachedCallbacksRef.current,\n  );\n}\n"], "mappings": ";;;;;;;;;AAgBA,IAAAA,aAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,MAAA,GAAAC,uBAAA,CAAAF,OAAA;AAA+B,IAAAG,KAAA,GAAAF,MAAA;AAAA,SAAAG,yBAAAC,CAAA,6BAAAC,OAAA,mBAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,yBAAAC,CAAA,WAAAA,CAAA,GAAAG,CAAA,GAAAD,CAAA,KAAAF,CAAA;AAAA,SAAAH,wBAAAG,CAAA,EAAAE,CAAA,SAAAA,CAAA,IAAAF,CAAA,IAAAA,CAAA,CAAAI,UAAA,SAAAJ,CAAA,eAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,WAAAK,OAAA,EAAAL,CAAA,QAAAG,CAAA,GAAAJ,wBAAA,CAAAG,CAAA,OAAAC,CAAA,IAAAA,CAAA,CAAAG,GAAA,CAAAN,CAAA,UAAAG,CAAA,CAAAI,GAAA,CAAAP,CAAA,OAAAQ,CAAA,KAAAC,SAAA,UAAAC,CAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,CAAA,IAAAd,CAAA,oBAAAc,CAAA,OAAAC,cAAA,CAAAC,IAAA,CAAAhB,CAAA,EAAAc,CAAA,SAAAG,CAAA,GAAAP,CAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAb,CAAA,EAAAc,CAAA,UAAAG,CAAA,KAAAA,CAAA,CAAAV,GAAA,IAAAU,CAAA,CAAAC,GAAA,IAAAP,MAAA,CAAAC,cAAA,CAAAJ,CAAA,EAAAM,CAAA,EAAAG,CAAA,IAAAT,CAAA,CAAAM,CAAA,IAAAd,CAAA,CAAAc,CAAA,YAAAN,CAAA,CAAAH,OAAA,GAAAL,CAAA,EAAAG,CAAA,IAAAA,CAAA,CAAAe,GAAA,CAAAlB,CAAA,EAAAQ,CAAA,GAAAA,CAAA;AAM/B,IAAIW,+BAAyD;AAEtD,SAASC,mCAAmCA,CACjDC,uBAAiD,EAC3C;EACNF,+BAA+B,GAAGE,uBAAuB;AAC3D;AAEO,SAASC,mCAAmCA,CAAA,EAA6B;EAC9E,OAAOH,+BAA+B;AACxC;AAQA,IAAMI,sBAAsB,GAAG,IAAIC,GAAG,CAAwB,CAAC;AAExD,SAASC,sCAAsCA,CACpDC,QAA+B,EACzB;EACNH,sBAAsB,CAACI,GAAG,CAACD,QAAQ,CAAC;AACtC;AAEO,SAASE,wCAAwCA,CACtDF,QAA+B,EACzB;EACNH,sBAAsB,CAACM,MAAM,CAACH,QAAQ,CAAC;AACzC;AAEO,SAASI,oCAAoCA,CAClDC,YAA4C,EACZ;EAChC,IAAMC,uBAAuB,GAAG,IAAAC,aAAM,EAAoB,EAAE,CAAC;EAE7D,IAAMC,yBAAyB,GAC7B,IAAAD,aAAM,EAAwC,IAAI,CAAC;EAErD,IAAIC,yBAAyB,CAACC,OAAO,IAAI,IAAI,EAAE;IAC7CD,yBAAyB,CAACC,OAAO,GAAG,UAACC,IAA0B,EAAW;MACxE,IAAIA,IAAI,IAAI,IAAI,EAAE;QAChB,IAAIJ,uBAAuB,CAACG,OAAO,CAACE,MAAM,GAAG,CAAC,EAAE;UAC9CL,uBAAuB,CAACG,OAAO,CAACG,OAAO,CAAC,UAAAC,EAAE;YAAA,OAAIA,EAAE,CAAC,CAAC;UAAA,EAAC;UACnDP,uBAAuB,CAACG,OAAO,GAAG,EAAE;QACtC;MACF,CAAC,MAAM;QACLZ,sBAAsB,CAACe,OAAO,CAAC,UAAAE,qBAAqB,EAAI;UACtD,IAAMC,oBAAoB,GAAGD,qBAAqB,CAACJ,IAAI,CAAC;UACxD,IAAIK,oBAAoB,IAAI,IAAI,EAAE;YAChCT,uBAAuB,CAACG,OAAO,CAACO,IAAI,CAACD,oBAAoB,CAAC;UAC5D;QACF,CAAC,CAAC;MACJ;IACF,CAAC;EACH;EAGA,OAAO,IAAAE,qBAAY,EACjBZ,YAAY,EACZG,yBAAyB,CAACC,OAC5B,CAAC;AACH", "ignoreList": []}