33146d6cd863e5340cf36b2259244623
"use strict";

/* istanbul ignore next */
function cov_22kz54lma4() {
  var path = "/Users/<USER>/projects/react-native/payment-module/src/domain/usecases/payment-order/PaymentOrderUseCase.ts";
  var hash = "1aca75c2252ba9dc08a8e16086a5b7d5acf42c35";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/projects/react-native/payment-module/src/domain/usecases/payment-order/PaymentOrderUseCase.ts",
    statementMap: {
      "0": {
        start: {
          line: 3,
          column: 29
        },
        end: {
          line: 3,
          column: 84
        }
      },
      "1": {
        start: {
          line: 4,
          column: 25
        },
        end: {
          line: 4,
          column: 99
        }
      },
      "2": {
        start: {
          line: 5,
          column: 23
        },
        end: {
          line: 5,
          column: 95
        }
      },
      "3": {
        start: {
          line: 6,
          column: 20
        },
        end: {
          line: 6,
          column: 89
        }
      },
      "4": {
        start: {
          line: 7,
          column: 0
        },
        end: {
          line: 9,
          column: 3
        }
      },
      "5": {
        start: {
          line: 10,
          column: 0
        },
        end: {
          line: 10,
          column: 37
        }
      },
      "6": {
        start: {
          line: 11,
          column: 26
        },
        end: {
          line: 11,
          column: 69
        }
      },
      "7": {
        start: {
          line: 12,
          column: 26
        },
        end: {
          line: 32,
          column: 3
        }
      },
      "8": {
        start: {
          line: 14,
          column: 4
        },
        end: {
          line: 14,
          column: 61
        }
      },
      "9": {
        start: {
          line: 15,
          column: 4
        },
        end: {
          line: 15,
          column: 33
        }
      },
      "10": {
        start: {
          line: 17,
          column: 2
        },
        end: {
          line: 31,
          column: 6
        }
      },
      "11": {
        start: {
          line: 20,
          column: 21
        },
        end: {
          line: 25,
          column: 8
        }
      },
      "12": {
        start: {
          line: 21,
          column: 20
        },
        end: {
          line: 21,
          column: 24
        }
      },
      "13": {
        start: {
          line: 22,
          column: 8
        },
        end: {
          line: 24,
          column: 11
        }
      },
      "14": {
        start: {
          line: 23,
          column: 10
        },
        end: {
          line: 23,
          column: 56
        }
      },
      "15": {
        start: {
          line: 27,
          column: 8
        },
        end: {
          line: 27,
          column: 47
        }
      },
      "16": {
        start: {
          line: 29,
          column: 6
        },
        end: {
          line: 29,
          column: 21
        }
      },
      "17": {
        start: {
          line: 33,
          column: 0
        },
        end: {
          line: 33,
          column: 50
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 12,
            column: 26
          },
          end: {
            line: 12,
            column: 27
          }
        },
        loc: {
          start: {
            line: 12,
            column: 38
          },
          end: {
            line: 32,
            column: 1
          }
        },
        line: 12
      },
      "1": {
        name: "PaymentOrderUseCase",
        decl: {
          start: {
            line: 13,
            column: 11
          },
          end: {
            line: 13,
            column: 30
          }
        },
        loc: {
          start: {
            line: 13,
            column: 43
          },
          end: {
            line: 16,
            column: 3
          }
        },
        line: 13
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 19,
            column: 11
          },
          end: {
            line: 19,
            column: 12
          }
        },
        loc: {
          start: {
            line: 19,
            column: 23
          },
          end: {
            line: 30,
            column: 5
          }
        },
        line: 19
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 20,
            column: 53
          },
          end: {
            line: 20,
            column: 54
          }
        },
        loc: {
          start: {
            line: 20,
            column: 73
          },
          end: {
            line: 25,
            column: 7
          }
        },
        line: 20
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 22,
            column: 60
          },
          end: {
            line: 22,
            column: 61
          }
        },
        loc: {
          start: {
            line: 22,
            column: 72
          },
          end: {
            line: 24,
            column: 9
          }
        },
        line: 22
      },
      "5": {
        name: "execute",
        decl: {
          start: {
            line: 26,
            column: 15
          },
          end: {
            line: 26,
            column: 22
          }
        },
        loc: {
          start: {
            line: 26,
            column: 27
          },
          end: {
            line: 28,
            column: 7
          }
        },
        line: 26
      }
    },
    branchMap: {},
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0
    },
    b: {},
    inputSourceMap: {
      version: 3,
      file: undefined,
      names: ["ExcecutionHandler_1", "require", "PaymentOrderUseCase", "repository", "_classCallCheck2", "default", "_createClass2", "key", "value", "_execute", "_asyncToGenerator2", "request", "_this", "ExecutionHandler", "execute", "paymentOrder", "_x", "apply", "arguments", "exports"],
      sourceRoot: undefined,
      sources: ["/Users/<USER>/projects/react-native/payment-module/src/domain/usecases/payment-order/PaymentOrderUseCase.ts"],
      sourcesContent: ["import {IPaymentOrderRepository} from '../../repositories/IPaymentOrderRepository';\nimport {PaymentOrderModel} from '../../entities/payment-order/PaymentOrderModel';\nimport {ResultState} from '../../../core/ResultState';\nimport {ExecutionHandler} from '../../../utils/ExcecutionHandler';\nimport {PaymentOrderRequest} from '../../../data/models/payment-order/PaymentOrderRequest';\nexport class PaymentOrderUseCase {\n  private repository: IPaymentOrderRepository;\n\n  constructor(repository: IPaymentOrderRepository) {\n    this.repository = repository;\n  }\n\n  public async execute(request: PaymentOrderRequest): Promise<ResultState<PaymentOrderModel>> {\n    // call this.repository.paymentOrder(...)\n    return ExecutionHandler.execute(() => this.repository.paymentOrder(request));\n  }\n}\n"],
      mappings: ";;;;;;;;;;AAGA,IAAAA,mBAAA,GAAAC,OAAA;AAAkE,IAErDC,mBAAmB;EAG9B,SAAAA,oBAAYC,UAAmC;IAAA,IAAAC,gBAAA,CAAAC,OAAA,QAAAH,mBAAA;IAC7C,IAAI,CAACC,UAAU,GAAGA,UAAU;EAC9B;EAAC,WAAAG,aAAA,CAAAD,OAAA,EAAAH,mBAAA;IAAAK,GAAA;IAAAC,KAAA;MAAA,IAAAC,QAAA,OAAAC,kBAAA,CAAAL,OAAA,EAEM,WAAcM,OAA4B;QAAA,IAAAC,KAAA;QAE/C,OAAOZ,mBAAA,CAAAa,gBAAgB,CAACC,OAAO,CAAC;UAAA,OAAMF,KAAI,CAACT,UAAU,CAACY,YAAY,CAACJ,OAAO,CAAC;QAAA,EAAC;MAC9E,CAAC;MAAA,SAHYG,OAAOA,CAAAE,EAAA;QAAA,OAAAP,QAAA,CAAAQ,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAPJ,OAAO;IAAA;EAAA;AAAA;AAPtBK,OAAA,CAAAjB,mBAAA,GAAAA,mBAAA",
      ignoreList: []
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "1aca75c2252ba9dc08a8e16086a5b7d5acf42c35"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_22kz54lma4 = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_22kz54lma4();
var _interopRequireDefault =
/* istanbul ignore next */
(cov_22kz54lma4().s[0]++, require("@babel/runtime/helpers/interopRequireDefault"));
var _asyncToGenerator2 =
/* istanbul ignore next */
(cov_22kz54lma4().s[1]++, _interopRequireDefault(require("@babel/runtime/helpers/asyncToGenerator")));
var _classCallCheck2 =
/* istanbul ignore next */
(cov_22kz54lma4().s[2]++, _interopRequireDefault(require("@babel/runtime/helpers/classCallCheck")));
var _createClass2 =
/* istanbul ignore next */
(cov_22kz54lma4().s[3]++, _interopRequireDefault(require("@babel/runtime/helpers/createClass")));
/* istanbul ignore next */
cov_22kz54lma4().s[4]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_22kz54lma4().s[5]++;
exports.PaymentOrderUseCase = void 0;
var ExcecutionHandler_1 =
/* istanbul ignore next */
(cov_22kz54lma4().s[6]++, require("../../../utils/ExcecutionHandler"));
var PaymentOrderUseCase =
/* istanbul ignore next */
(cov_22kz54lma4().s[7]++, function () {
  /* istanbul ignore next */
  cov_22kz54lma4().f[0]++;
  function PaymentOrderUseCase(repository) {
    /* istanbul ignore next */
    cov_22kz54lma4().f[1]++;
    cov_22kz54lma4().s[8]++;
    (0, _classCallCheck2.default)(this, PaymentOrderUseCase);
    /* istanbul ignore next */
    cov_22kz54lma4().s[9]++;
    this.repository = repository;
  }
  /* istanbul ignore next */
  cov_22kz54lma4().s[10]++;
  return (0, _createClass2.default)(PaymentOrderUseCase, [{
    key: "execute",
    value: function () {
      /* istanbul ignore next */
      cov_22kz54lma4().f[2]++;
      var _execute =
      /* istanbul ignore next */
      (cov_22kz54lma4().s[11]++, (0, _asyncToGenerator2.default)(function* (request) {
        /* istanbul ignore next */
        cov_22kz54lma4().f[3]++;
        var _this =
        /* istanbul ignore next */
        (cov_22kz54lma4().s[12]++, this);
        /* istanbul ignore next */
        cov_22kz54lma4().s[13]++;
        return ExcecutionHandler_1.ExecutionHandler.execute(function () {
          /* istanbul ignore next */
          cov_22kz54lma4().f[4]++;
          cov_22kz54lma4().s[14]++;
          return _this.repository.paymentOrder(request);
        });
      }));
      function execute(_x) {
        /* istanbul ignore next */
        cov_22kz54lma4().f[5]++;
        cov_22kz54lma4().s[15]++;
        return _execute.apply(this, arguments);
      }
      /* istanbul ignore next */
      cov_22kz54lma4().s[16]++;
      return execute;
    }()
  }]);
}());
/* istanbul ignore next */
cov_22kz54lma4().s[17]++;
exports.PaymentOrderUseCase = PaymentOrderUseCase;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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