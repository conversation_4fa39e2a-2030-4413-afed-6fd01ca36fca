3d2eed75388c00b4bac6bfdf21483eeb
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.unstable_getImageComponentDecorator = unstable_getImageComponentDecorator;
exports.unstable_registerImageAttachedCallback = unstable_registerImageAttachedCallback;
exports.unstable_setImageComponentDecorator = unstable_setImageComponentDecorator;
exports.unstable_unregisterImageAttachedCallback = unstable_unregisterImageAttachedCallback;
exports.useWrapRefWithImageAttachedCallbacks = useWrapRefWithImageAttachedCallbacks;
var _useMergeRefs = _interopRequireDefault(require("../Utilities/useMergeRefs"));
var _react = _interopRequireWildcard(require("react"));
var React = _react;
function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
var injectedImageComponentDecorator;
function unstable_setImageComponentDecorator(imageComponentDecorator) {
  injectedImageComponentDecorator = imageComponentDecorator;
}
function unstable_getImageComponentDecorator() {
  return injectedImageComponentDecorator;
}
var imageAttachedCallbacks = new Set();
function unstable_registerImageAttachedCallback(callback) {
  imageAttachedCallbacks.add(callback);
}
function unstable_unregisterImageAttachedCallback(callback) {
  imageAttachedCallbacks.delete(callback);
}
function useWrapRefWithImageAttachedCallbacks(forwardedRef) {
  var pendingCleanupCallbacks = (0, _react.useRef)([]);
  var imageAttachedCallbacksRef = (0, _react.useRef)(null);
  if (imageAttachedCallbacksRef.current == null) {
    imageAttachedCallbacksRef.current = function (node) {
      if (node == null) {
        if (pendingCleanupCallbacks.current.length > 0) {
          pendingCleanupCallbacks.current.forEach(function (cb) {
            return cb();
          });
          pendingCleanupCallbacks.current = [];
        }
      } else {
        imageAttachedCallbacks.forEach(function (imageAttachedCallback) {
          var maybeCleanupCallback = imageAttachedCallback(node);
          if (maybeCleanupCallback != null) {
            pendingCleanupCallbacks.current.push(maybeCleanupCallback);
          }
        });
      }
    };
  }
  return (0, _useMergeRefs.default)(forwardedRef, imageAttachedCallbacksRef.current);
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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