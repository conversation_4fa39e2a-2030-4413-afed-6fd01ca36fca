fcf013947be2e69aa6f9df7656965c94
"use strict";

/* istanbul ignore next */
function cov_4diiqyp8a() {
  var path = "/Users/<USER>/projects/react-native/payment-module/src/domain/entities/IBillContact.ts";
  var hash = "9d4ebc9cf7e12bfd8391be65417babd17ea4d26a";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/projects/react-native/payment-module/src/domain/entities/IBillContact.ts",
    statementMap: {
      "0": {
        start: {
          line: 3,
          column: 0
        },
        end: {
          line: 5,
          column: 3
        }
      }
    },
    fnMap: {},
    branchMap: {},
    s: {
      "0": 0
    },
    f: {},
    b: {},
    inputSourceMap: {
      version: 3,
      file: undefined,
      names: [],
      sourceRoot: undefined,
      sources: ["/Users/<USER>/projects/react-native/payment-module/src/domain/entities/IBillContact.ts"],
      sourcesContent: ["export interface IBillContact {\n  getId: () => string;\n  getExternalId: () => string;\n  getPartnerCode: () => string;\n  getCustomerName: () => string;\n  getSubtitle: () => string;\n  getIcon: () => string;\n  isPair: () => boolean;\n  isTopup: () => boolean;\n  getType: () => string;\n\n  getSearchContent: () => string;\n  isEditable: () => boolean;\n  getBillCode?: () => string;\n  getCategoryCode?: () => string;\n  getPayableAmount: () => string;\n  getFavoriteStatus: () => 'ACTIVE' | 'INACTIVE';\n  getReminderStatus: () => 'ACTIVE' | 'INACTIVE';\n  getServiceCode: () => string;\n}\n"],
      mappings: "",
      ignoreList: []
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "9d4ebc9cf7e12bfd8391be65417babd17ea4d26a"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_4diiqyp8a = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_4diiqyp8a();
cov_4diiqyp8a().s[0]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6W10sInNvdXJjZXMiOlsiL1VzZXJzL3NvbnNhbXNldC9wcm9qZWN0cy9yZWFjdC1uYXRpdmUvcGF5bWVudC1tb2R1bGUvc3JjL2RvbWFpbi9lbnRpdGllcy9JQmlsbENvbnRhY3QudHMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGludGVyZmFjZSBJQmlsbENvbnRhY3Qge1xuICBnZXRJZDogKCkgPT4gc3RyaW5nO1xuICBnZXRFeHRlcm5hbElkOiAoKSA9PiBzdHJpbmc7XG4gIGdldFBhcnRuZXJDb2RlOiAoKSA9PiBzdHJpbmc7XG4gIGdldEN1c3RvbWVyTmFtZTogKCkgPT4gc3RyaW5nO1xuICBnZXRTdWJ0aXRsZTogKCkgPT4gc3RyaW5nO1xuICBnZXRJY29uOiAoKSA9PiBzdHJpbmc7XG4gIGlzUGFpcjogKCkgPT4gYm9vbGVhbjtcbiAgaXNUb3B1cDogKCkgPT4gYm9vbGVhbjtcbiAgZ2V0VHlwZTogKCkgPT4gc3RyaW5nO1xuXG4gIGdldFNlYXJjaENvbnRlbnQ6ICgpID0+IHN0cmluZztcbiAgaXNFZGl0YWJsZTogKCkgPT4gYm9vbGVhbjtcbiAgZ2V0QmlsbENvZGU/OiAoKSA9PiBzdHJpbmc7XG4gIGdldENhdGVnb3J5Q29kZT86ICgpID0+IHN0cmluZztcbiAgZ2V0UGF5YWJsZUFtb3VudDogKCkgPT4gc3RyaW5nO1xuICBnZXRGYXZvcml0ZVN0YXR1czogKCkgPT4gJ0FDVElWRScgfCAnSU5BQ1RJVkUnO1xuICBnZXRSZW1pbmRlclN0YXR1czogKCkgPT4gJ0FDVElWRScgfCAnSU5BQ1RJVkUnO1xuICBnZXRTZXJ2aWNlQ29kZTogKCkgPT4gc3RyaW5nO1xufVxuIl0sIm1hcHBpbmdzIjoiIiwiaWdub3JlTGlzdCI6W119