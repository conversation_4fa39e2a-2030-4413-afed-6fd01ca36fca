{"version": 3, "names": [], "sources": ["/Users/<USER>/projects/react-native/payment-module/src/domain/entities/IBillContact.ts"], "sourcesContent": ["export interface IBillContact {\n  getId: () => string;\n  getExternalId: () => string;\n  getPartnerCode: () => string;\n  getCustomerName: () => string;\n  getSubtitle: () => string;\n  getIcon: () => string;\n  isPair: () => boolean;\n  isTopup: () => boolean;\n  getType: () => string;\n\n  getSearchContent: () => string;\n  isEditable: () => boolean;\n  getBillCode?: () => string;\n  getCategoryCode?: () => string;\n  getPayableAmount: () => string;\n  getFavoriteStatus: () => 'ACTIVE' | 'INACTIVE';\n  getReminderStatus: () => 'ACTIVE' | 'INACTIVE';\n  getServiceCode: () => string;\n}\n"], "mappings": "", "ignoreList": []}