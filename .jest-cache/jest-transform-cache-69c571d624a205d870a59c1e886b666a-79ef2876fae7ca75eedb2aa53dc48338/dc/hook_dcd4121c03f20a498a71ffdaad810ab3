7784bc1b5d1cb6ae075aeda6be74e71c
"use strict";

/* istanbul ignore next */
function cov_18xbprop55() {
  var path = "/Users/<USER>/projects/react-native/payment-module/src/presentation/payment-result/hook.ts";
  var hash = "183000447dbda72064255dcafe0195815c835722";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/projects/react-native/payment-module/src/presentation/payment-result/hook.ts",
    statementMap: {
      "0": {
        start: {
          line: 3,
          column: 29
        },
        end: {
          line: 3,
          column: 84
        }
      },
      "1": {
        start: {
          line: 4,
          column: 25
        },
        end: {
          line: 4,
          column: 99
        }
      },
      "2": {
        start: {
          line: 5,
          column: 22
        },
        end: {
          line: 5,
          column: 93
        }
      },
      "3": {
        start: {
          line: 6,
          column: 22
        },
        end: {
          line: 10,
          column: 1
        }
      },
      "4": {
        start: {
          line: 7,
          column: 2
        },
        end: {
          line: 9,
          column: 4
        }
      },
      "5": {
        start: {
          line: 11,
          column: 0
        },
        end: {
          line: 13,
          column: 3
        }
      },
      "6": {
        start: {
          line: 14,
          column: 15
        },
        end: {
          line: 14,
          column: 50
        }
      },
      "7": {
        start: {
          line: 15,
          column: 14
        },
        end: {
          line: 15,
          column: 30
        }
      },
      "8": {
        start: {
          line: 16,
          column: 27
        },
        end: {
          line: 16,
          column: 73
        }
      },
      "9": {
        start: {
          line: 17,
          column: 31
        },
        end: {
          line: 17,
          column: 64
        }
      },
      "10": {
        start: {
          line: 18,
          column: 20
        },
        end: {
          line: 18,
          column: 73
        }
      },
      "11": {
        start: {
          line: 19,
          column: 18
        },
        end: {
          line: 19,
          column: 52
        }
      },
      "12": {
        start: {
          line: 20,
          column: 31
        },
        end: {
          line: 20,
          column: 64
        }
      },
      "13": {
        start: {
          line: 21,
          column: 23
        },
        end: {
          line: 21,
          column: 77
        }
      },
      "14": {
        start: {
          line: 22,
          column: 31
        },
        end: {
          line: 22,
          column: 107
        }
      },
      "15": {
        start: {
          line: 23,
          column: 23
        },
        end: {
          line: 148,
          column: 1
        }
      },
      "16": {
        start: {
          line: 25,
          column: 14
        },
        end: {
          line: 25,
          column: 38
        }
      },
      "17": {
        start: {
          line: 26,
          column: 19
        },
        end: {
          line: 26,
          column: 48
        }
      },
      "18": {
        start: {
          line: 27,
          column: 20
        },
        end: {
          line: 27,
          column: 44
        }
      },
      "19": {
        start: {
          line: 28,
          column: 2
        },
        end: {
          line: 28,
          column: 50
        }
      },
      "20": {
        start: {
          line: 29,
          column: 13
        },
        end: {
          line: 29,
          column: 78
        }
      },
      "21": {
        start: {
          line: 30,
          column: 12
        },
        end: {
          line: 30,
          column: 49
        }
      },
      "22": {
        start: {
          line: 31,
          column: 21
        },
        end: {
          line: 31,
          column: 29
        }
      },
      "23": {
        start: {
          line: 32,
          column: 24
        },
        end: {
          line: 32,
          column: 32
        }
      },
      "24": {
        start: {
          line: 33,
          column: 20
        },
        end: {
          line: 43,
          column: 19
        }
      },
      "25": {
        start: {
          line: 35,
          column: 4
        },
        end: {
          line: 38,
          column: 5
        }
      },
      "26": {
        start: {
          line: 37,
          column: 6
        },
        end: {
          line: 37,
          column: 226
        }
      },
      "27": {
        start: {
          line: 39,
          column: 23
        },
        end: {
          line: 41,
          column: 9
        }
      },
      "28": {
        start: {
          line: 40,
          column: 6
        },
        end: {
          line: 40,
          column: 38
        }
      },
      "29": {
        start: {
          line: 42,
          column: 4
        },
        end: {
          line: 42,
          column: 24
        }
      },
      "30": {
        start: {
          line: 44,
          column: 16
        },
        end: {
          line: 44,
          column: 41
        }
      },
      "31": {
        start: {
          line: 45,
          column: 16
        },
        end: {
          line: 45,
          column: 113
        }
      },
      "32": {
        start: {
          line: 46,
          column: 31
        },
        end: {
          line: 46,
          column: 67
        }
      },
      "33": {
        start: {
          line: 47,
          column: 23
        },
        end: {
          line: 68,
          column: 3
        }
      },
      "34": {
        start: {
          line: 48,
          column: 4
        },
        end: {
          line: 48,
          column: 40
        }
      },
      "35": {
        start: {
          line: 49,
          column: 4
        },
        end: {
          line: 67,
          column: 5
        }
      },
      "36": {
        start: {
          line: 51,
          column: 8
        },
        end: {
          line: 51,
          column: 24
        }
      },
      "37": {
        start: {
          line: 52,
          column: 8
        },
        end: {
          line: 52,
          column: 14
        }
      },
      "38": {
        start: {
          line: 54,
          column: 8
        },
        end: {
          line: 54,
          column: 31
        }
      },
      "39": {
        start: {
          line: 55,
          column: 8
        },
        end: {
          line: 55,
          column: 14
        }
      },
      "40": {
        start: {
          line: 57,
          column: 8
        },
        end: {
          line: 57,
          column: 34
        }
      },
      "41": {
        start: {
          line: 58,
          column: 8
        },
        end: {
          line: 58,
          column: 14
        }
      },
      "42": {
        start: {
          line: 60,
          column: 8
        },
        end: {
          line: 60,
          column: 26
        }
      },
      "43": {
        start: {
          line: 61,
          column: 8
        },
        end: {
          line: 61,
          column: 14
        }
      },
      "44": {
        start: {
          line: 63,
          column: 8
        },
        end: {
          line: 63,
          column: 26
        }
      },
      "45": {
        start: {
          line: 64,
          column: 8
        },
        end: {
          line: 64,
          column: 14
        }
      },
      "46": {
        start: {
          line: 66,
          column: 8
        },
        end: {
          line: 66,
          column: 14
        }
      },
      "47": {
        start: {
          line: 69,
          column: 22
        },
        end: {
          line: 90,
          column: 5
        }
      },
      "48": {
        start: {
          line: 70,
          column: 16
        },
        end: {
          line: 86,
          column: 6
        }
      },
      "49": {
        start: {
          line: 71,
          column: 6
        },
        end: {
          line: 85,
          column: 7
        }
      },
      "50": {
        start: {
          line: 72,
          column: 18
        },
        end: {
          line: 75,
          column: 10
        }
      },
      "51": {
        start: {
          line: 76,
          column: 22
        },
        end: {
          line: 81,
          column: 9
        }
      },
      "52": {
        start: {
          line: 82,
          column: 8
        },
        end: {
          line: 82,
          column: 57
        }
      },
      "53": {
        start: {
          line: 84,
          column: 8
        },
        end: {
          line: 84,
          column: 45
        }
      },
      "54": {
        start: {
          line: 87,
          column: 4
        },
        end: {
          line: 89,
          column: 6
        }
      },
      "55": {
        start: {
          line: 88,
          column: 6
        },
        end: {
          line: 88,
          column: 42
        }
      },
      "56": {
        start: {
          line: 91,
          column: 29
        },
        end: {
          line: 93,
          column: 3
        }
      },
      "57": {
        start: {
          line: 92,
          column: 4
        },
        end: {
          line: 92,
          column: 83
        }
      },
      "58": {
        start: {
          line: 94,
          column: 32
        },
        end: {
          line: 101,
          column: 3
        }
      },
      "59": {
        start: {
          line: 96,
          column: 18
        },
        end: {
          line: 96,
          column: 896
        }
      },
      "60": {
        start: {
          line: 97,
          column: 4
        },
        end: {
          line: 97,
          column: 49
        }
      },
      "61": {
        start: {
          line: 98,
          column: 4
        },
        end: {
          line: 100,
          column: 7
        }
      },
      "62": {
        start: {
          line: 102,
          column: 24
        },
        end: {
          line: 104,
          column: 3
        }
      },
      "63": {
        start: {
          line: 103,
          column: 4
        },
        end: {
          line: 103,
          column: 83
        }
      },
      "64": {
        start: {
          line: 105,
          column: 24
        },
        end: {
          line: 107,
          column: 3
        }
      },
      "65": {
        start: {
          line: 106,
          column: 4
        },
        end: {
          line: 106,
          column: 83
        }
      },
      "66": {
        start: {
          line: 108,
          column: 26
        },
        end: {
          line: 117,
          column: 3
        }
      },
      "67": {
        start: {
          line: 109,
          column: 4
        },
        end: {
          line: 116,
          column: 7
        }
      },
      "68": {
        start: {
          line: 118,
          column: 29
        },
        end: {
          line: 121,
          column: 3
        }
      },
      "69": {
        start: {
          line: 119,
          column: 17
        },
        end: {
          line: 119,
          column: 48
        }
      },
      "70": {
        start: {
          line: 120,
          column: 4
        },
        end: {
          line: 120,
          column: 30
        }
      },
      "71": {
        start: {
          line: 122,
          column: 15
        },
        end: {
          line: 129,
          column: 3
        }
      },
      "72": {
        start: {
          line: 123,
          column: 4
        },
        end: {
          line: 128,
          column: 7
        }
      },
      "73": {
        start: {
          line: 130,
          column: 30
        },
        end: {
          line: 134,
          column: 3
        }
      },
      "74": {
        start: {
          line: 131,
          column: 4
        },
        end: {
          line: 133,
          column: 7
        }
      },
      "75": {
        start: {
          line: 135,
          column: 2
        },
        end: {
          line: 147,
          column: 4
        }
      },
      "76": {
        start: {
          line: 149,
          column: 0
        },
        end: {
          line: 149,
          column: 35
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 6,
            column: 54
          },
          end: {
            line: 6,
            column: 55
          }
        },
        loc: {
          start: {
            line: 6,
            column: 69
          },
          end: {
            line: 10,
            column: 1
          }
        },
        line: 6
      },
      "1": {
        name: "usePaymentResult",
        decl: {
          start: {
            line: 23,
            column: 32
          },
          end: {
            line: 23,
            column: 48
          }
        },
        loc: {
          start: {
            line: 23,
            column: 51
          },
          end: {
            line: 148,
            column: 1
          }
        },
        line: 23
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 33,
            column: 41
          },
          end: {
            line: 33,
            column: 42
          }
        },
        loc: {
          start: {
            line: 33,
            column: 53
          },
          end: {
            line: 43,
            column: 3
          }
        },
        line: 33
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 39,
            column: 209
          },
          end: {
            line: 39,
            column: 210
          }
        },
        loc: {
          start: {
            line: 39,
            column: 230
          },
          end: {
            line: 41,
            column: 5
          }
        },
        line: 39
      },
      "4": {
        name: "goTransferResultDetail",
        decl: {
          start: {
            line: 46,
            column: 40
          },
          end: {
            line: 46,
            column: 62
          }
        },
        loc: {
          start: {
            line: 46,
            column: 65
          },
          end: {
            line: 46,
            column: 67
          }
        },
        line: 46
      },
      "5": {
        name: "transferAction",
        decl: {
          start: {
            line: 47,
            column: 32
          },
          end: {
            line: 47,
            column: 46
          }
        },
        loc: {
          start: {
            line: 47,
            column: 53
          },
          end: {
            line: 68,
            column: 3
          }
        },
        line: 47
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 69,
            column: 22
          },
          end: {
            line: 69,
            column: 23
          }
        },
        loc: {
          start: {
            line: 69,
            column: 34
          },
          end: {
            line: 90,
            column: 3
          }
        },
        line: 69
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 70,
            column: 48
          },
          end: {
            line: 70,
            column: 49
          }
        },
        loc: {
          start: {
            line: 70,
            column: 61
          },
          end: {
            line: 86,
            column: 5
          }
        },
        line: 70
      },
      "8": {
        name: "transferShare",
        decl: {
          start: {
            line: 87,
            column: 20
          },
          end: {
            line: 87,
            column: 33
          }
        },
        loc: {
          start: {
            line: 87,
            column: 36
          },
          end: {
            line: 89,
            column: 5
          }
        },
        line: 87
      },
      "9": {
        name: "transferSaveTemplate",
        decl: {
          start: {
            line: 91,
            column: 38
          },
          end: {
            line: 91,
            column: 58
          }
        },
        loc: {
          start: {
            line: 91,
            column: 61
          },
          end: {
            line: 93,
            column: 3
          }
        },
        line: 91
      },
      "10": {
        name: "transferSaveBeneficiary",
        decl: {
          start: {
            line: 94,
            column: 41
          },
          end: {
            line: 94,
            column: 64
          }
        },
        loc: {
          start: {
            line: 94,
            column: 67
          },
          end: {
            line: 101,
            column: 3
          }
        },
        line: 94
      },
      "11": {
        name: "transferManager",
        decl: {
          start: {
            line: 102,
            column: 33
          },
          end: {
            line: 102,
            column: 48
          }
        },
        loc: {
          start: {
            line: 102,
            column: 51
          },
          end: {
            line: 104,
            column: 3
          }
        },
        line: 102
      },
      "12": {
        name: "transferSupport",
        decl: {
          start: {
            line: 105,
            column: 33
          },
          end: {
            line: 105,
            column: 48
          }
        },
        loc: {
          start: {
            line: 105,
            column: 51
          },
          end: {
            line: 107,
            column: 3
          }
        },
        line: 105
      },
      "13": {
        name: "otherTransactions",
        decl: {
          start: {
            line: 108,
            column: 35
          },
          end: {
            line: 108,
            column: 52
          }
        },
        loc: {
          start: {
            line: 108,
            column: 55
          },
          end: {
            line: 117,
            column: 3
          }
        },
        line: 108
      },
      "14": {
        name: "handleLayoutViewshot",
        decl: {
          start: {
            line: 118,
            column: 38
          },
          end: {
            line: 118,
            column: 58
          }
        },
        loc: {
          start: {
            line: 118,
            column: 66
          },
          end: {
            line: 121,
            column: 3
          }
        },
        line: 118
      },
      "15": {
        name: "goHome",
        decl: {
          start: {
            line: 122,
            column: 24
          },
          end: {
            line: 122,
            column: 30
          }
        },
        loc: {
          start: {
            line: 122,
            column: 33
          },
          end: {
            line: 129,
            column: 3
          }
        },
        line: 122
      },
      "16": {
        name: "goPaymentResultDetail",
        decl: {
          start: {
            line: 130,
            column: 39
          },
          end: {
            line: 130,
            column: 60
          }
        },
        loc: {
          start: {
            line: 130,
            column: 63
          },
          end: {
            line: 134,
            column: 3
          }
        },
        line: 130
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 6,
            column: 22
          },
          end: {
            line: 10,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 6,
            column: 22
          },
          end: {
            line: 6,
            column: 26
          }
        }, {
          start: {
            line: 6,
            column: 30
          },
          end: {
            line: 6,
            column: 50
          }
        }, {
          start: {
            line: 6,
            column: 54
          },
          end: {
            line: 10,
            column: 1
          }
        }],
        line: 6
      },
      "1": {
        loc: {
          start: {
            line: 7,
            column: 9
          },
          end: {
            line: 9,
            column: 3
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 7,
            column: 33
          },
          end: {
            line: 7,
            column: 36
          }
        }, {
          start: {
            line: 7,
            column: 39
          },
          end: {
            line: 9,
            column: 3
          }
        }],
        line: 7
      },
      "2": {
        loc: {
          start: {
            line: 7,
            column: 9
          },
          end: {
            line: 7,
            column: 30
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 7,
            column: 9
          },
          end: {
            line: 7,
            column: 12
          }
        }, {
          start: {
            line: 7,
            column: 16
          },
          end: {
            line: 7,
            column: 30
          }
        }],
        line: 7
      },
      "3": {
        loc: {
          start: {
            line: 35,
            column: 4
          },
          end: {
            line: 38,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 35,
            column: 4
          },
          end: {
            line: 38,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 35
      },
      "4": {
        loc: {
          start: {
            line: 35,
            column: 8
          },
          end: {
            line: 35,
            column: 469
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 35,
            column: 8
          },
          end: {
            line: 35,
            column: 159
          }
        }, {
          start: {
            line: 35,
            column: 163
          },
          end: {
            line: 35,
            column: 315
          }
        }, {
          start: {
            line: 35,
            column: 319
          },
          end: {
            line: 35,
            column: 469
          }
        }],
        line: 35
      },
      "5": {
        loc: {
          start: {
            line: 35,
            column: 9
          },
          end: {
            line: 35,
            column: 115
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 35,
            column: 73
          },
          end: {
            line: 35,
            column: 79
          }
        }, {
          start: {
            line: 35,
            column: 82
          },
          end: {
            line: 35,
            column: 115
          }
        }],
        line: 35
      },
      "6": {
        loc: {
          start: {
            line: 35,
            column: 164
          },
          end: {
            line: 35,
            column: 272
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 35,
            column: 229
          },
          end: {
            line: 35,
            column: 235
          }
        }, {
          start: {
            line: 35,
            column: 238
          },
          end: {
            line: 35,
            column: 272
          }
        }],
        line: 35
      },
      "7": {
        loc: {
          start: {
            line: 35,
            column: 320
          },
          end: {
            line: 35,
            column: 428
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 35,
            column: 385
          },
          end: {
            line: 35,
            column: 391
          }
        }, {
          start: {
            line: 35,
            column: 394
          },
          end: {
            line: 35,
            column: 428
          }
        }],
        line: 35
      },
      "8": {
        loc: {
          start: {
            line: 37,
            column: 13
          },
          end: {
            line: 37,
            column: 225
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 37,
            column: 187
          },
          end: {
            line: 37,
            column: 193
          }
        }, {
          start: {
            line: 37,
            column: 196
          },
          end: {
            line: 37,
            column: 225
          }
        }],
        line: 37
      },
      "9": {
        loc: {
          start: {
            line: 37,
            column: 13
          },
          end: {
            line: 37,
            column: 184
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 37,
            column: 13
          },
          end: {
            line: 37,
            column: 106
          }
        }, {
          start: {
            line: 37,
            column: 110
          },
          end: {
            line: 37,
            column: 184
          }
        }],
        line: 37
      },
      "10": {
        loc: {
          start: {
            line: 39,
            column: 23
          },
          end: {
            line: 41,
            column: 9
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 39,
            column: 171
          },
          end: {
            line: 39,
            column: 177
          }
        }, {
          start: {
            line: 39,
            column: 180
          },
          end: {
            line: 41,
            column: 9
          }
        }],
        line: 39
      },
      "11": {
        loc: {
          start: {
            line: 39,
            column: 23
          },
          end: {
            line: 39,
            column: 168
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 39,
            column: 23
          },
          end: {
            line: 39,
            column: 42
          }
        }, {
          start: {
            line: 39,
            column: 46
          },
          end: {
            line: 39,
            column: 100
          }
        }, {
          start: {
            line: 39,
            column: 104
          },
          end: {
            line: 39,
            column: 168
          }
        }],
        line: 39
      },
      "12": {
        loc: {
          start: {
            line: 40,
            column: 20
          },
          end: {
            line: 40,
            column: 36
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 40,
            column: 20
          },
          end: {
            line: 40,
            column: 31
          }
        }, {
          start: {
            line: 40,
            column: 35
          },
          end: {
            line: 40,
            column: 36
          }
        }],
        line: 40
      },
      "13": {
        loc: {
          start: {
            line: 45,
            column: 16
          },
          end: {
            line: 45,
            column: 113
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 45,
            column: 73
          },
          end: {
            line: 45,
            column: 79
          }
        }, {
          start: {
            line: 45,
            column: 82
          },
          end: {
            line: 45,
            column: 113
          }
        }],
        line: 45
      },
      "14": {
        loc: {
          start: {
            line: 49,
            column: 4
          },
          end: {
            line: 67,
            column: 5
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 50,
            column: 6
          },
          end: {
            line: 52,
            column: 14
          }
        }, {
          start: {
            line: 53,
            column: 6
          },
          end: {
            line: 55,
            column: 14
          }
        }, {
          start: {
            line: 56,
            column: 6
          },
          end: {
            line: 58,
            column: 14
          }
        }, {
          start: {
            line: 59,
            column: 6
          },
          end: {
            line: 61,
            column: 14
          }
        }, {
          start: {
            line: 62,
            column: 6
          },
          end: {
            line: 64,
            column: 14
          }
        }, {
          start: {
            line: 65,
            column: 6
          },
          end: {
            line: 66,
            column: 14
          }
        }],
        line: 49
      },
      "15": {
        loc: {
          start: {
            line: 96,
            column: 70
          },
          end: {
            line: 96,
            column: 125
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 96,
            column: 92
          },
          end: {
            line: 96,
            column: 98
          }
        }, {
          start: {
            line: 96,
            column: 101
          },
          end: {
            line: 96,
            column: 125
          }
        }],
        line: 96
      },
      "16": {
        loc: {
          start: {
            line: 96,
            column: 131
          },
          end: {
            line: 96,
            column: 186
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 96,
            column: 153
          },
          end: {
            line: 96,
            column: 159
          }
        }, {
          start: {
            line: 96,
            column: 162
          },
          end: {
            line: 96,
            column: 186
          }
        }],
        line: 96
      },
      "17": {
        loc: {
          start: {
            line: 96,
            column: 239
          },
          end: {
            line: 96,
            column: 366
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 96,
            column: 320
          },
          end: {
            line: 96,
            column: 326
          }
        }, {
          start: {
            line: 96,
            column: 329
          },
          end: {
            line: 96,
            column: 366
          }
        }],
        line: 96
      },
      "18": {
        loc: {
          start: {
            line: 96,
            column: 239
          },
          end: {
            line: 96,
            column: 317
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 96,
            column: 239
          },
          end: {
            line: 96,
            column: 258
          }
        }, {
          start: {
            line: 96,
            column: 262
          },
          end: {
            line: 96,
            column: 317
          }
        }],
        line: 96
      },
      "19": {
        loc: {
          start: {
            line: 96,
            column: 368
          },
          end: {
            line: 96,
            column: 489
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 96,
            column: 449
          },
          end: {
            line: 96,
            column: 455
          }
        }, {
          start: {
            line: 96,
            column: 458
          },
          end: {
            line: 96,
            column: 489
          }
        }],
        line: 96
      },
      "20": {
        loc: {
          start: {
            line: 96,
            column: 368
          },
          end: {
            line: 96,
            column: 446
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 96,
            column: 368
          },
          end: {
            line: 96,
            column: 387
          }
        }, {
          start: {
            line: 96,
            column: 391
          },
          end: {
            line: 96,
            column: 446
          }
        }],
        line: 96
      },
      "21": {
        loc: {
          start: {
            line: 96,
            column: 491
          },
          end: {
            line: 96,
            column: 686
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 96,
            column: 644
          },
          end: {
            line: 96,
            column: 650
          }
        }, {
          start: {
            line: 96,
            column: 653
          },
          end: {
            line: 96,
            column: 686
          }
        }],
        line: 96
      },
      "22": {
        loc: {
          start: {
            line: 96,
            column: 491
          },
          end: {
            line: 96,
            column: 641
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 96,
            column: 491
          },
          end: {
            line: 96,
            column: 510
          }
        }, {
          start: {
            line: 96,
            column: 514
          },
          end: {
            line: 96,
            column: 569
          }
        }, {
          start: {
            line: 96,
            column: 573
          },
          end: {
            line: 96,
            column: 641
          }
        }],
        line: 96
      },
      "23": {
        loc: {
          start: {
            line: 96,
            column: 688
          },
          end: {
            line: 96,
            column: 790
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 96,
            column: 746
          },
          end: {
            line: 96,
            column: 752
          }
        }, {
          start: {
            line: 96,
            column: 755
          },
          end: {
            line: 96,
            column: 790
          }
        }],
        line: 96
      },
      "24": {
        loc: {
          start: {
            line: 96,
            column: 792
          },
          end: {
            line: 96,
            column: 893
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 96,
            column: 850
          },
          end: {
            line: 96,
            column: 856
          }
        }, {
          start: {
            line: 96,
            column: 859
          },
          end: {
            line: 96,
            column: 893
          }
        }],
        line: 96
      },
      "25": {
        loc: {
          start: {
            line: 123,
            column: 4
          },
          end: {
            line: 128,
            column: 6
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 123,
            column: 4
          },
          end: {
            line: 123,
            column: 22
          }
        }, {
          start: {
            line: 123,
            column: 26
          },
          end: {
            line: 128,
            column: 6
          }
        }],
        line: 123
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0
    },
    b: {
      "0": [0, 0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0, 0, 0, 0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0],
      "20": [0, 0],
      "21": [0, 0],
      "22": [0, 0, 0],
      "23": [0, 0],
      "24": [0, 0],
      "25": [0, 0]
    },
    inputSourceMap: {
      version: 3,
      file: undefined,
      names: ["native_1", "require", "react_1", "react_native_share_1", "__importDefault", "react_native_view_shot_1", "ScreenNames_1", "Constants_1", "msb_host_shared_module_1", "DimensionUtils_1", "MyBillContactListModel_1", "usePaymentResult", "_paymentInfo$provider", "route", "useRoute", "navigation", "useNavigation", "paymentInfo", "params", "console", "log", "_ref", "useState", "default", "getWindowHeight", "_ref2", "_slicedToArray2", "heightViewshot", "setHeightViewshot", "totalAmount", "useMemo", "_paymentInfo$paymentV", "_paymentInfo$paymentV2", "_paymentInfo$paymentV3", "_paymentInfo$billInfo", "paymentValidate", "paymentType", "PAYMENT_TYPE", "TOPUP_ACCOUNT", "TOPUP_CREDIT", "QR_PAYMENT", "_paymentInfo$paymentV4", "transferTransactionInformation", "instructedAmount", "amount", "_totalAmount", "billInfo", "billList", "reduce", "sum", "bill", "viewRef", "useRef", "isTopup", "provider", "goTransferResultDetail", "transferAction", "item", "type", "TRANSFER_RESULT_ACTION", "TRANSFER_SHARE", "transferShare", "TRANSER_SAVE_TEMPLATE", "transferSaveTemplate", "TRANSER_SAVE_BENEFICIARY", "transferSaveBeneficiary", "TRANSFER_MANAGER", "transferManager", "TRANSFER_SUPPORT", "transferSupport", "_ref3", "_asyncToGenerator2", "uri", "captureRef", "format", "quality", "options", "title", "message", "url", "open", "error", "apply", "arguments", "hostSharedModule", "d", "domainService", "undevelopedFeature", "_paymentInfo$provider2", "_paymentInfo$billInfo2", "_paymentInfo$provider3", "_paymentInfo$provider4", "_paymentInfo$provider5", "contact", "MyBillContactModel", "contractName", "categoryName", "AccountModel", "subgroupNameVn", "billCode", "subGroupId", "toString", "categoryCode", "serviceCode", "navigate", "EditBillContactScreen", "otherTransactions", "reset", "index", "routes", "name", "handleLayoutViewshot", "event", "height", "nativeEvent", "layout", "goHome", "goPaymentResultDetail", "PaymentResultDetailScreen", "exports"],
      sourceRoot: undefined,
      sources: ["/Users/<USER>/projects/react-native/payment-module/src/presentation/payment-result/hook.ts"],
      sourcesContent: ["import {RouteProp, useNavigation, useRoute, NavigationProp} from '@react-navigation/native';\nimport {useMemo, useRef, useState} from 'react';\nimport Share from 'react-native-share';\nimport {captureRef} from 'react-native-view-shot';\nimport {LayoutChangeEvent} from 'react-native';\n\nimport ScreenNames from '../../commons/ScreenNames';\nimport {ActionProps} from './components/transfer-result-action/types';\nimport {PAYMENT_TYPE, TRANSFER_RESULT_ACTION} from '../../commons/Constants';\nimport {PaymentStackParamList} from '../../navigation/PaymentStack';\nimport {hostSharedModule} from 'msb-host-shared-module';\nimport DimensionUtils from '../../utils/DimensionUtils';\nimport {AccountModel, MyBillContactModel} from '../../domain/entities/my-bill-contact-list/MyBillContactListModel';\n\nconst usePaymentResult = () => {\n  const route = useRoute<RouteProp<PaymentStackParamList, 'PaymentResultScreen'>>();\n  const navigation = useNavigation<NavigationProp<PaymentStackParamList, 'PaymentResultScreen'>>();\n\n  const {paymentInfo} = route.params;\n  console.log('PaymentResultScreen', paymentInfo);\n  const [heightViewshot, setHeightViewshot] = useState<number>(DimensionUtils.getWindowHeight());\n\n  const totalAmount = useMemo(() => {\n    if (\n      paymentInfo.paymentValidate?.paymentType === PAYMENT_TYPE.TOPUP_ACCOUNT ||\n      paymentInfo.paymentValidate?.paymentType === PAYMENT_TYPE.TOPUP_CREDIT ||\n      paymentInfo.paymentValidate?.paymentType === PAYMENT_TYPE.QR_PAYMENT\n    ) {\n      return paymentInfo.paymentValidate.transferTransactionInformation?.instructedAmount?.amount;\n    }\n    const _totalAmount = paymentInfo?.billInfo?.billList?.reduce((sum, bill) => sum + (bill.amount || 0), 0);\n    return _totalAmount;\n  }, [paymentInfo]);\n\n  const viewRef = useRef(null);\n\n  const isTopup = paymentInfo.provider?.isTopup();\n\n  const goTransferResultDetail = () => {};\n\n  const transferAction = (item: ActionProps) => {\n    console.log('transferAction', item);\n    switch (item.type) {\n      case TRANSFER_RESULT_ACTION.TRANSFER_SHARE:\n        transferShare();\n        break;\n      case TRANSFER_RESULT_ACTION.TRANSER_SAVE_TEMPLATE:\n        transferSaveTemplate();\n        break;\n      case TRANSFER_RESULT_ACTION.TRANSER_SAVE_BENEFICIARY:\n        transferSaveBeneficiary();\n        break;\n      case TRANSFER_RESULT_ACTION.TRANSFER_MANAGER:\n        transferManager();\n        break;\n      case TRANSFER_RESULT_ACTION.TRANSFER_SUPPORT:\n        transferSupport();\n        break;\n      default:\n        break;\n    }\n  };\n\n  const transferShare = async () => {\n    try {\n      const uri = await captureRef(viewRef, {\n        format: 'jpg',\n        quality: 0.8,\n      });\n\n      const options = {\n        title: 'Chia s\u1EBB giao d\u1ECBch',\n        message: '',\n        url: uri,\n        type: 'image/png',\n      };\n      await Share.open(options);\n    } catch (error) {\n      console.error('Share error:', error);\n    }\n  };\n\n  const transferSaveTemplate = () => {\n    hostSharedModule.d.domainService.undevelopedFeature();\n  };\n  const transferSaveBeneficiary = () => {\n    const contact = new MyBillContactModel('', paymentInfo?.contractName, '', paymentInfo?.categoryName, '', '', [\n      new AccountModel(\n        paymentInfo?.provider?.subgroupNameVn,\n        paymentInfo?.billInfo?.billCode,\n        paymentInfo?.provider?.subGroupId?.toString(),\n        paymentInfo.provider?.categoryCode,\n        paymentInfo.provider?.serviceCode,\n      ),\n    ]);\n    console.log('contact=====-----===', contact);\n    navigation.navigate(ScreenNames.EditBillContactScreen, {contact: contact});\n  };\n  const transferManager = () => {\n    hostSharedModule.d.domainService.undevelopedFeature();\n  };\n  const transferSupport = () => {\n    hostSharedModule.d.domainService.undevelopedFeature();\n  };\n\n  const otherTransactions = () => {\n    navigation.reset({\n      index: 2,\n      routes: [\n        {name: 'BottomTabs'},\n        {\n          name: 'PaymentStack',\n        },\n      ],\n    });\n  };\n\n  const handleLayoutViewshot = (event: LayoutChangeEvent) => {\n    const {height} = event.nativeEvent.layout;\n    setHeightViewshot(height);\n  };\n\n  const goHome = () => {\n    navigation?.reset({\n      index: 0,\n      routes: [\n        {\n          name: 'SegmentStack',\n        },\n      ],\n    });\n  };\n\n  const goPaymentResultDetail = () => {\n    navigation.navigate(ScreenNames.PaymentResultDetailScreen, {paymentInfo});\n  };\n\n  return {\n    goTransferResultDetail,\n    otherTransactions,\n    transferAction,\n    viewRef,\n    goHome,\n    paymentInfo,\n    goPaymentResultDetail,\n    totalAmount,\n    handleLayoutViewshot,\n    heightViewshot,\n    isTopup,\n  };\n};\n\nexport type Props = ReturnType<typeof usePaymentResult>;\n\nexport default usePaymentResult;\n"],
      mappings: ";;;;;;;;;;;;;AAAA,IAAAA,QAAA,GAAAC,OAAA;AACA,IAAAC,OAAA,GAAAD,OAAA;AACA,IAAAE,oBAAA,GAAAC,eAAA,CAAAH,OAAA;AACA,IAAAI,wBAAA,GAAAJ,OAAA;AAGA,IAAAK,aAAA,GAAAF,eAAA,CAAAH,OAAA;AAEA,IAAAM,WAAA,GAAAN,OAAA;AAEA,IAAAO,wBAAA,GAAAP,OAAA;AACA,IAAAQ,gBAAA,GAAAL,eAAA,CAAAH,OAAA;AACA,IAAAS,wBAAA,GAAAT,OAAA;AAEA,IAAMU,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAAA,EAAQ;EAAA,IAAAC,qBAAA;EAC5B,IAAMC,KAAK,GAAG,IAAAb,QAAA,CAAAc,QAAQ,GAA2D;EACjF,IAAMC,UAAU,GAAG,IAAAf,QAAA,CAAAgB,aAAa,GAAgE;EAEhG,IAAOC,WAAW,GAAIJ,KAAK,CAACK,MAAM,CAA3BD,WAAW;EAClBE,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEH,WAAW,CAAC;EAC/C,IAAAI,IAAA,GAA4C,IAAAnB,OAAA,CAAAoB,QAAQ,EAASb,gBAAA,CAAAc,OAAc,CAACC,eAAe,EAAE,CAAC;IAAAC,KAAA,OAAAC,eAAA,CAAAH,OAAA,EAAAF,IAAA;IAAvFM,cAAc,GAAAF,KAAA;IAAEG,iBAAiB,GAAAH,KAAA;EAExC,IAAMI,WAAW,GAAG,IAAA3B,OAAA,CAAA4B,OAAO,EAAC,YAAK;IAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,qBAAA;IAC/B,IACE,EAAAH,qBAAA,GAAAd,WAAW,CAACkB,eAAe,qBAA3BJ,qBAAA,CAA6BK,WAAW,MAAK7B,WAAA,CAAA8B,YAAY,CAACC,aAAa,IACvE,EAAAN,sBAAA,GAAAf,WAAW,CAACkB,eAAe,qBAA3BH,sBAAA,CAA6BI,WAAW,MAAK7B,WAAA,CAAA8B,YAAY,CAACE,YAAY,IACtE,EAAAN,sBAAA,GAAAhB,WAAW,CAACkB,eAAe,qBAA3BF,sBAAA,CAA6BG,WAAW,MAAK7B,WAAA,CAAA8B,YAAY,CAACG,UAAU,EACpE;MAAA,IAAAC,sBAAA;MACA,QAAAA,sBAAA,GAAOxB,WAAW,CAACkB,eAAe,CAACO,8BAA8B,cAAAD,sBAAA,GAA1DA,sBAAA,CAA4DE,gBAAgB,qBAA5EF,sBAAA,CAA8EG,MAAM;IAC7F;IACA,IAAMC,YAAY,GAAG5B,WAAW,aAAAiB,qBAAA,GAAXjB,WAAW,CAAE6B,QAAQ,cAAAZ,qBAAA,GAArBA,qBAAA,CAAuBa,QAAQ,qBAA/Bb,qBAAA,CAAiCc,MAAM,CAAC,UAACC,GAAG,EAAEC,IAAI;MAAA,OAAKD,GAAG,IAAIC,IAAI,CAACN,MAAM,IAAI,CAAC,CAAC;IAAA,GAAE,CAAC,CAAC;IACxG,OAAOC,YAAY;EACrB,CAAC,EAAE,CAAC5B,WAAW,CAAC,CAAC;EAEjB,IAAMkC,OAAO,GAAG,IAAAjD,OAAA,CAAAkD,MAAM,EAAC,IAAI,CAAC;EAE5B,IAAMC,OAAO,IAAAzC,qBAAA,GAAGK,WAAW,CAACqC,QAAQ,qBAApB1C,qBAAA,CAAsByC,OAAO,EAAE;EAE/C,IAAME,sBAAsB,GAAG,SAAzBA,sBAAsBA,CAAA,EAAQ,CAAE,CAAC;EAEvC,IAAMC,cAAc,GAAG,SAAjBA,cAAcA,CAAIC,IAAiB,EAAI;IAC3CtC,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEqC,IAAI,CAAC;IACnC,QAAQA,IAAI,CAACC,IAAI;MACf,KAAKnD,WAAA,CAAAoD,sBAAsB,CAACC,cAAc;QACxCC,aAAa,EAAE;QACf;MACF,KAAKtD,WAAA,CAAAoD,sBAAsB,CAACG,qBAAqB;QAC/CC,oBAAoB,EAAE;QACtB;MACF,KAAKxD,WAAA,CAAAoD,sBAAsB,CAACK,wBAAwB;QAClDC,uBAAuB,EAAE;QACzB;MACF,KAAK1D,WAAA,CAAAoD,sBAAsB,CAACO,gBAAgB;QAC1CC,eAAe,EAAE;QACjB;MACF,KAAK5D,WAAA,CAAAoD,sBAAsB,CAACS,gBAAgB;QAC1CC,eAAe,EAAE;QACjB;MACF;QACE;IACJ;EACF,CAAC;EAED,IAAMR,aAAa;IAAA,IAAAS,KAAA,OAAAC,kBAAA,CAAAhD,OAAA,EAAG,aAAW;MAC/B,IAAI;QACF,IAAMiD,GAAG,SAAS,IAAAnE,wBAAA,CAAAoE,UAAU,EAACtB,OAAO,EAAE;UACpCuB,MAAM,EAAE,KAAK;UACbC,OAAO,EAAE;SACV,CAAC;QAEF,IAAMC,OAAO,GAAG;UACdC,KAAK,EAAE,mBAAmB;UAC1BC,OAAO,EAAE,EAAE;UACXC,GAAG,EAAEP,GAAG;UACRd,IAAI,EAAE;SACP;QACD,MAAMvD,oBAAA,CAAAoB,OAAK,CAACyD,IAAI,CAACJ,OAAO,CAAC;MAC3B,CAAC,CAAC,OAAOK,KAAK,EAAE;QACd9D,OAAO,CAAC8D,KAAK,CAAC,cAAc,EAAEA,KAAK,CAAC;MACtC;IACF,CAAC;IAAA,gBAjBKpB,aAAaA,CAAA;MAAA,OAAAS,KAAA,CAAAY,KAAA,OAAAC,SAAA;IAAA;EAAA,GAiBlB;EAED,IAAMpB,oBAAoB,GAAG,SAAvBA,oBAAoBA,CAAA,EAAQ;IAChCvD,wBAAA,CAAA4E,gBAAgB,CAACC,CAAC,CAACC,aAAa,CAACC,kBAAkB,EAAE;EACvD,CAAC;EACD,IAAMtB,uBAAuB,GAAG,SAA1BA,uBAAuBA,CAAA,EAAQ;IAAA,IAAAuB,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;IACnC,IAAMC,OAAO,GAAG,IAAInF,wBAAA,CAAAoF,kBAAkB,CAAC,EAAE,EAAE7E,WAAW,oBAAXA,WAAW,CAAE8E,YAAY,EAAE,EAAE,EAAE9E,WAAW,oBAAXA,WAAW,CAAE+E,YAAY,EAAE,EAAE,EAAE,EAAE,EAAE,CAC3G,IAAItF,wBAAA,CAAAuF,YAAY,CACdhF,WAAW,aAAAuE,sBAAA,GAAXvE,WAAW,CAAEqC,QAAQ,qBAArBkC,sBAAA,CAAuBU,cAAc,EACrCjF,WAAW,aAAAwE,sBAAA,GAAXxE,WAAW,CAAE6B,QAAQ,qBAArB2C,sBAAA,CAAuBU,QAAQ,EAC/BlF,WAAW,aAAAyE,sBAAA,GAAXzE,WAAW,CAAEqC,QAAQ,cAAAoC,sBAAA,GAArBA,sBAAA,CAAuBU,UAAU,qBAAjCV,sBAAA,CAAmCW,QAAQ,EAAE,GAAAV,sBAAA,GAC7C1E,WAAW,CAACqC,QAAQ,qBAApBqC,sBAAA,CAAsBW,YAAY,GAAAV,sBAAA,GAClC3E,WAAW,CAACqC,QAAQ,qBAApBsC,sBAAA,CAAsBW,WAAW,CAClC,CACF,CAAC;IACFpF,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEyE,OAAO,CAAC;IAC5C9E,UAAU,CAACyF,QAAQ,CAAClG,aAAA,CAAAiB,OAAW,CAACkF,qBAAqB,EAAE;MAACZ,OAAO,EAAEA;IAAO,CAAC,CAAC;EAC5E,CAAC;EACD,IAAM1B,eAAe,GAAG,SAAlBA,eAAeA,CAAA,EAAQ;IAC3B3D,wBAAA,CAAA4E,gBAAgB,CAACC,CAAC,CAACC,aAAa,CAACC,kBAAkB,EAAE;EACvD,CAAC;EACD,IAAMlB,eAAe,GAAG,SAAlBA,eAAeA,CAAA,EAAQ;IAC3B7D,wBAAA,CAAA4E,gBAAgB,CAACC,CAAC,CAACC,aAAa,CAACC,kBAAkB,EAAE;EACvD,CAAC;EAED,IAAMmB,iBAAiB,GAAG,SAApBA,iBAAiBA,CAAA,EAAQ;IAC7B3F,UAAU,CAAC4F,KAAK,CAAC;MACfC,KAAK,EAAE,CAAC;MACRC,MAAM,EAAE,CACN;QAACC,IAAI,EAAE;MAAY,CAAC,EACpB;QACEA,IAAI,EAAE;OACP;KAEJ,CAAC;EACJ,CAAC;EAED,IAAMC,oBAAoB,GAAG,SAAvBA,oBAAoBA,CAAIC,KAAwB,EAAI;IACxD,IAAOC,MAAM,GAAID,KAAK,CAACE,WAAW,CAACC,MAAM,CAAlCF,MAAM;IACbrF,iBAAiB,CAACqF,MAAM,CAAC;EAC3B,CAAC;EAED,IAAMG,MAAM,GAAG,SAATA,MAAMA,CAAA,EAAQ;IAClBrG,UAAU,YAAVA,UAAU,CAAE4F,KAAK,CAAC;MAChBC,KAAK,EAAE,CAAC;MACRC,MAAM,EAAE,CACN;QACEC,IAAI,EAAE;OACP;KAEJ,CAAC;EACJ,CAAC;EAED,IAAMO,qBAAqB,GAAG,SAAxBA,qBAAqBA,CAAA,EAAQ;IACjCtG,UAAU,CAACyF,QAAQ,CAAClG,aAAA,CAAAiB,OAAW,CAAC+F,yBAAyB,EAAE;MAACrG,WAAW,EAAXA;IAAW,CAAC,CAAC;EAC3E,CAAC;EAED,OAAO;IACLsC,sBAAsB,EAAtBA,sBAAsB;IACtBmD,iBAAiB,EAAjBA,iBAAiB;IACjBlD,cAAc,EAAdA,cAAc;IACdL,OAAO,EAAPA,OAAO;IACPiE,MAAM,EAANA,MAAM;IACNnG,WAAW,EAAXA,WAAW;IACXoG,qBAAqB,EAArBA,qBAAqB;IACrBxF,WAAW,EAAXA,WAAW;IACXkF,oBAAoB,EAApBA,oBAAoB;IACpBpF,cAAc,EAAdA,cAAc;IACd0B,OAAO,EAAPA;GACD;AACH,CAAC;AAIDkE,OAAA,CAAAhG,OAAA,GAAeZ,gBAAgB",
      ignoreList: []
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "183000447dbda72064255dcafe0195815c835722"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_18xbprop55 = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_18xbprop55();
var _interopRequireDefault =
/* istanbul ignore next */
(cov_18xbprop55().s[0]++, require("@babel/runtime/helpers/interopRequireDefault"));
var _asyncToGenerator2 =
/* istanbul ignore next */
(cov_18xbprop55().s[1]++, _interopRequireDefault(require("@babel/runtime/helpers/asyncToGenerator")));
var _slicedToArray2 =
/* istanbul ignore next */
(cov_18xbprop55().s[2]++, _interopRequireDefault(require("@babel/runtime/helpers/slicedToArray")));
var __importDefault =
/* istanbul ignore next */
(cov_18xbprop55().s[3]++,
/* istanbul ignore next */
(cov_18xbprop55().b[0][0]++, this) &&
/* istanbul ignore next */
(cov_18xbprop55().b[0][1]++, this.__importDefault) ||
/* istanbul ignore next */
(cov_18xbprop55().b[0][2]++, function (mod) {
  /* istanbul ignore next */
  cov_18xbprop55().f[0]++;
  cov_18xbprop55().s[4]++;
  return /* istanbul ignore next */(cov_18xbprop55().b[2][0]++, mod) &&
  /* istanbul ignore next */
  (cov_18xbprop55().b[2][1]++, mod.__esModule) ?
  /* istanbul ignore next */
  (cov_18xbprop55().b[1][0]++, mod) :
  /* istanbul ignore next */
  (cov_18xbprop55().b[1][1]++, {
    "default": mod
  });
}));
/* istanbul ignore next */
cov_18xbprop55().s[5]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
var native_1 =
/* istanbul ignore next */
(cov_18xbprop55().s[6]++, require("@react-navigation/native"));
var react_1 =
/* istanbul ignore next */
(cov_18xbprop55().s[7]++, require("react"));
var react_native_share_1 =
/* istanbul ignore next */
(cov_18xbprop55().s[8]++, __importDefault(require("react-native-share")));
var react_native_view_shot_1 =
/* istanbul ignore next */
(cov_18xbprop55().s[9]++, require("react-native-view-shot"));
var ScreenNames_1 =
/* istanbul ignore next */
(cov_18xbprop55().s[10]++, __importDefault(require("../../commons/ScreenNames")));
var Constants_1 =
/* istanbul ignore next */
(cov_18xbprop55().s[11]++, require("../../commons/Constants"));
var msb_host_shared_module_1 =
/* istanbul ignore next */
(cov_18xbprop55().s[12]++, require("msb-host-shared-module"));
var DimensionUtils_1 =
/* istanbul ignore next */
(cov_18xbprop55().s[13]++, __importDefault(require("../../utils/DimensionUtils")));
var MyBillContactListModel_1 =
/* istanbul ignore next */
(cov_18xbprop55().s[14]++, require("../../domain/entities/my-bill-contact-list/MyBillContactListModel"));
/* istanbul ignore next */
cov_18xbprop55().s[15]++;
var usePaymentResult = function usePaymentResult() {
  /* istanbul ignore next */
  cov_18xbprop55().f[1]++;
  var _paymentInfo$provider;
  var route =
  /* istanbul ignore next */
  (cov_18xbprop55().s[16]++, (0, native_1.useRoute)());
  var navigation =
  /* istanbul ignore next */
  (cov_18xbprop55().s[17]++, (0, native_1.useNavigation)());
  var paymentInfo =
  /* istanbul ignore next */
  (cov_18xbprop55().s[18]++, route.params.paymentInfo);
  /* istanbul ignore next */
  cov_18xbprop55().s[19]++;
  console.log('PaymentResultScreen', paymentInfo);
  var _ref =
    /* istanbul ignore next */
    (cov_18xbprop55().s[20]++, (0, react_1.useState)(DimensionUtils_1.default.getWindowHeight())),
    _ref2 =
    /* istanbul ignore next */
    (cov_18xbprop55().s[21]++, (0, _slicedToArray2.default)(_ref, 2)),
    heightViewshot =
    /* istanbul ignore next */
    (cov_18xbprop55().s[22]++, _ref2[0]),
    setHeightViewshot =
    /* istanbul ignore next */
    (cov_18xbprop55().s[23]++, _ref2[1]);
  var totalAmount =
  /* istanbul ignore next */
  (cov_18xbprop55().s[24]++, (0, react_1.useMemo)(function () {
    /* istanbul ignore next */
    cov_18xbprop55().f[2]++;
    var _paymentInfo$paymentV, _paymentInfo$paymentV2, _paymentInfo$paymentV3, _paymentInfo$billInfo;
    /* istanbul ignore next */
    cov_18xbprop55().s[25]++;
    if (
    /* istanbul ignore next */
    (cov_18xbprop55().b[4][0]++, ((_paymentInfo$paymentV = paymentInfo.paymentValidate) == null ?
    /* istanbul ignore next */
    (cov_18xbprop55().b[5][0]++, void 0) :
    /* istanbul ignore next */
    (cov_18xbprop55().b[5][1]++, _paymentInfo$paymentV.paymentType)) === Constants_1.PAYMENT_TYPE.TOPUP_ACCOUNT) ||
    /* istanbul ignore next */
    (cov_18xbprop55().b[4][1]++, ((_paymentInfo$paymentV2 = paymentInfo.paymentValidate) == null ?
    /* istanbul ignore next */
    (cov_18xbprop55().b[6][0]++, void 0) :
    /* istanbul ignore next */
    (cov_18xbprop55().b[6][1]++, _paymentInfo$paymentV2.paymentType)) === Constants_1.PAYMENT_TYPE.TOPUP_CREDIT) ||
    /* istanbul ignore next */
    (cov_18xbprop55().b[4][2]++, ((_paymentInfo$paymentV3 = paymentInfo.paymentValidate) == null ?
    /* istanbul ignore next */
    (cov_18xbprop55().b[7][0]++, void 0) :
    /* istanbul ignore next */
    (cov_18xbprop55().b[7][1]++, _paymentInfo$paymentV3.paymentType)) === Constants_1.PAYMENT_TYPE.QR_PAYMENT)) {
      /* istanbul ignore next */
      cov_18xbprop55().b[3][0]++;
      var _paymentInfo$paymentV4;
      /* istanbul ignore next */
      cov_18xbprop55().s[26]++;
      return /* istanbul ignore next */(cov_18xbprop55().b[9][0]++, (_paymentInfo$paymentV4 = paymentInfo.paymentValidate.transferTransactionInformation) == null) ||
      /* istanbul ignore next */
      (cov_18xbprop55().b[9][1]++, (_paymentInfo$paymentV4 = _paymentInfo$paymentV4.instructedAmount) == null) ?
      /* istanbul ignore next */
      (cov_18xbprop55().b[8][0]++, void 0) :
      /* istanbul ignore next */
      (cov_18xbprop55().b[8][1]++, _paymentInfo$paymentV4.amount);
    } else
    /* istanbul ignore next */
    {
      cov_18xbprop55().b[3][1]++;
    }
    var _totalAmount =
    /* istanbul ignore next */
    (cov_18xbprop55().s[27]++,
    /* istanbul ignore next */
    (cov_18xbprop55().b[11][0]++, paymentInfo == null) ||
    /* istanbul ignore next */
    (cov_18xbprop55().b[11][1]++, (_paymentInfo$billInfo = paymentInfo.billInfo) == null) ||
    /* istanbul ignore next */
    (cov_18xbprop55().b[11][2]++, (_paymentInfo$billInfo = _paymentInfo$billInfo.billList) == null) ?
    /* istanbul ignore next */
    (cov_18xbprop55().b[10][0]++, void 0) :
    /* istanbul ignore next */
    (cov_18xbprop55().b[10][1]++, _paymentInfo$billInfo.reduce(function (sum, bill) {
      /* istanbul ignore next */
      cov_18xbprop55().f[3]++;
      cov_18xbprop55().s[28]++;
      return sum + (
      /* istanbul ignore next */
      (cov_18xbprop55().b[12][0]++, bill.amount) ||
      /* istanbul ignore next */
      (cov_18xbprop55().b[12][1]++, 0));
    }, 0)));
    /* istanbul ignore next */
    cov_18xbprop55().s[29]++;
    return _totalAmount;
  }, [paymentInfo]));
  var viewRef =
  /* istanbul ignore next */
  (cov_18xbprop55().s[30]++, (0, react_1.useRef)(null));
  var isTopup =
  /* istanbul ignore next */
  (cov_18xbprop55().s[31]++, (_paymentInfo$provider = paymentInfo.provider) == null ?
  /* istanbul ignore next */
  (cov_18xbprop55().b[13][0]++, void 0) :
  /* istanbul ignore next */
  (cov_18xbprop55().b[13][1]++, _paymentInfo$provider.isTopup()));
  /* istanbul ignore next */
  cov_18xbprop55().s[32]++;
  var goTransferResultDetail = function goTransferResultDetail() {
    /* istanbul ignore next */
    cov_18xbprop55().f[4]++;
  };
  /* istanbul ignore next */
  cov_18xbprop55().s[33]++;
  var transferAction = function transferAction(item) {
    /* istanbul ignore next */
    cov_18xbprop55().f[5]++;
    cov_18xbprop55().s[34]++;
    console.log('transferAction', item);
    /* istanbul ignore next */
    cov_18xbprop55().s[35]++;
    switch (item.type) {
      case Constants_1.TRANSFER_RESULT_ACTION.TRANSFER_SHARE:
        /* istanbul ignore next */
        cov_18xbprop55().b[14][0]++;
        cov_18xbprop55().s[36]++;
        transferShare();
        /* istanbul ignore next */
        cov_18xbprop55().s[37]++;
        break;
      case Constants_1.TRANSFER_RESULT_ACTION.TRANSER_SAVE_TEMPLATE:
        /* istanbul ignore next */
        cov_18xbprop55().b[14][1]++;
        cov_18xbprop55().s[38]++;
        transferSaveTemplate();
        /* istanbul ignore next */
        cov_18xbprop55().s[39]++;
        break;
      case Constants_1.TRANSFER_RESULT_ACTION.TRANSER_SAVE_BENEFICIARY:
        /* istanbul ignore next */
        cov_18xbprop55().b[14][2]++;
        cov_18xbprop55().s[40]++;
        transferSaveBeneficiary();
        /* istanbul ignore next */
        cov_18xbprop55().s[41]++;
        break;
      case Constants_1.TRANSFER_RESULT_ACTION.TRANSFER_MANAGER:
        /* istanbul ignore next */
        cov_18xbprop55().b[14][3]++;
        cov_18xbprop55().s[42]++;
        transferManager();
        /* istanbul ignore next */
        cov_18xbprop55().s[43]++;
        break;
      case Constants_1.TRANSFER_RESULT_ACTION.TRANSFER_SUPPORT:
        /* istanbul ignore next */
        cov_18xbprop55().b[14][4]++;
        cov_18xbprop55().s[44]++;
        transferSupport();
        /* istanbul ignore next */
        cov_18xbprop55().s[45]++;
        break;
      default:
        /* istanbul ignore next */
        cov_18xbprop55().b[14][5]++;
        cov_18xbprop55().s[46]++;
        break;
    }
  };
  var transferShare =
  /* istanbul ignore next */
  (cov_18xbprop55().s[47]++, function () {
    /* istanbul ignore next */
    cov_18xbprop55().f[6]++;
    var _ref3 =
    /* istanbul ignore next */
    (cov_18xbprop55().s[48]++, (0, _asyncToGenerator2.default)(function* () {
      /* istanbul ignore next */
      cov_18xbprop55().f[7]++;
      cov_18xbprop55().s[49]++;
      try {
        var uri =
        /* istanbul ignore next */
        (cov_18xbprop55().s[50]++, yield (0, react_native_view_shot_1.captureRef)(viewRef, {
          format: 'jpg',
          quality: 0.8
        }));
        var options =
        /* istanbul ignore next */
        (cov_18xbprop55().s[51]++, {
          title: 'Chia sẻ giao dịch',
          message: '',
          url: uri,
          type: 'image/png'
        });
        /* istanbul ignore next */
        cov_18xbprop55().s[52]++;
        yield react_native_share_1.default.open(options);
      } catch (error) {
        /* istanbul ignore next */
        cov_18xbprop55().s[53]++;
        console.error('Share error:', error);
      }
    }));
    /* istanbul ignore next */
    cov_18xbprop55().s[54]++;
    return function transferShare() {
      /* istanbul ignore next */
      cov_18xbprop55().f[8]++;
      cov_18xbprop55().s[55]++;
      return _ref3.apply(this, arguments);
    };
  }());
  /* istanbul ignore next */
  cov_18xbprop55().s[56]++;
  var transferSaveTemplate = function transferSaveTemplate() {
    /* istanbul ignore next */
    cov_18xbprop55().f[9]++;
    cov_18xbprop55().s[57]++;
    msb_host_shared_module_1.hostSharedModule.d.domainService.undevelopedFeature();
  };
  /* istanbul ignore next */
  cov_18xbprop55().s[58]++;
  var transferSaveBeneficiary = function transferSaveBeneficiary() {
    /* istanbul ignore next */
    cov_18xbprop55().f[10]++;
    var _paymentInfo$provider2, _paymentInfo$billInfo2, _paymentInfo$provider3, _paymentInfo$provider4, _paymentInfo$provider5;
    var contact =
    /* istanbul ignore next */
    (cov_18xbprop55().s[59]++, new MyBillContactListModel_1.MyBillContactModel('', paymentInfo == null ?
    /* istanbul ignore next */
    (cov_18xbprop55().b[15][0]++, void 0) :
    /* istanbul ignore next */
    (cov_18xbprop55().b[15][1]++, paymentInfo.contractName), '', paymentInfo == null ?
    /* istanbul ignore next */
    (cov_18xbprop55().b[16][0]++, void 0) :
    /* istanbul ignore next */
    (cov_18xbprop55().b[16][1]++, paymentInfo.categoryName), '', '', [new MyBillContactListModel_1.AccountModel(
    /* istanbul ignore next */
    (cov_18xbprop55().b[18][0]++, paymentInfo == null) ||
    /* istanbul ignore next */
    (cov_18xbprop55().b[18][1]++, (_paymentInfo$provider2 = paymentInfo.provider) == null) ?
    /* istanbul ignore next */
    (cov_18xbprop55().b[17][0]++, void 0) :
    /* istanbul ignore next */
    (cov_18xbprop55().b[17][1]++, _paymentInfo$provider2.subgroupNameVn),
    /* istanbul ignore next */
    (cov_18xbprop55().b[20][0]++, paymentInfo == null) ||
    /* istanbul ignore next */
    (cov_18xbprop55().b[20][1]++, (_paymentInfo$billInfo2 = paymentInfo.billInfo) == null) ?
    /* istanbul ignore next */
    (cov_18xbprop55().b[19][0]++, void 0) :
    /* istanbul ignore next */
    (cov_18xbprop55().b[19][1]++, _paymentInfo$billInfo2.billCode),
    /* istanbul ignore next */
    (cov_18xbprop55().b[22][0]++, paymentInfo == null) ||
    /* istanbul ignore next */
    (cov_18xbprop55().b[22][1]++, (_paymentInfo$provider3 = paymentInfo.provider) == null) ||
    /* istanbul ignore next */
    (cov_18xbprop55().b[22][2]++, (_paymentInfo$provider3 = _paymentInfo$provider3.subGroupId) == null) ?
    /* istanbul ignore next */
    (cov_18xbprop55().b[21][0]++, void 0) :
    /* istanbul ignore next */
    (cov_18xbprop55().b[21][1]++, _paymentInfo$provider3.toString()), (_paymentInfo$provider4 = paymentInfo.provider) == null ?
    /* istanbul ignore next */
    (cov_18xbprop55().b[23][0]++, void 0) :
    /* istanbul ignore next */
    (cov_18xbprop55().b[23][1]++, _paymentInfo$provider4.categoryCode), (_paymentInfo$provider5 = paymentInfo.provider) == null ?
    /* istanbul ignore next */
    (cov_18xbprop55().b[24][0]++, void 0) :
    /* istanbul ignore next */
    (cov_18xbprop55().b[24][1]++, _paymentInfo$provider5.serviceCode))]));
    /* istanbul ignore next */
    cov_18xbprop55().s[60]++;
    console.log('contact=====-----===', contact);
    /* istanbul ignore next */
    cov_18xbprop55().s[61]++;
    navigation.navigate(ScreenNames_1.default.EditBillContactScreen, {
      contact: contact
    });
  };
  /* istanbul ignore next */
  cov_18xbprop55().s[62]++;
  var transferManager = function transferManager() {
    /* istanbul ignore next */
    cov_18xbprop55().f[11]++;
    cov_18xbprop55().s[63]++;
    msb_host_shared_module_1.hostSharedModule.d.domainService.undevelopedFeature();
  };
  /* istanbul ignore next */
  cov_18xbprop55().s[64]++;
  var transferSupport = function transferSupport() {
    /* istanbul ignore next */
    cov_18xbprop55().f[12]++;
    cov_18xbprop55().s[65]++;
    msb_host_shared_module_1.hostSharedModule.d.domainService.undevelopedFeature();
  };
  /* istanbul ignore next */
  cov_18xbprop55().s[66]++;
  var otherTransactions = function otherTransactions() {
    /* istanbul ignore next */
    cov_18xbprop55().f[13]++;
    cov_18xbprop55().s[67]++;
    navigation.reset({
      index: 2,
      routes: [{
        name: 'BottomTabs'
      }, {
        name: 'PaymentStack'
      }]
    });
  };
  /* istanbul ignore next */
  cov_18xbprop55().s[68]++;
  var handleLayoutViewshot = function handleLayoutViewshot(event) {
    /* istanbul ignore next */
    cov_18xbprop55().f[14]++;
    var height =
    /* istanbul ignore next */
    (cov_18xbprop55().s[69]++, event.nativeEvent.layout.height);
    /* istanbul ignore next */
    cov_18xbprop55().s[70]++;
    setHeightViewshot(height);
  };
  /* istanbul ignore next */
  cov_18xbprop55().s[71]++;
  var goHome = function goHome() {
    /* istanbul ignore next */
    cov_18xbprop55().f[15]++;
    cov_18xbprop55().s[72]++;
    /* istanbul ignore next */
    (cov_18xbprop55().b[25][0]++, navigation == null) ||
    /* istanbul ignore next */
    (cov_18xbprop55().b[25][1]++, navigation.reset({
      index: 0,
      routes: [{
        name: 'SegmentStack'
      }]
    }));
  };
  /* istanbul ignore next */
  cov_18xbprop55().s[73]++;
  var goPaymentResultDetail = function goPaymentResultDetail() {
    /* istanbul ignore next */
    cov_18xbprop55().f[16]++;
    cov_18xbprop55().s[74]++;
    navigation.navigate(ScreenNames_1.default.PaymentResultDetailScreen, {
      paymentInfo: paymentInfo
    });
  };
  /* istanbul ignore next */
  cov_18xbprop55().s[75]++;
  return {
    goTransferResultDetail: goTransferResultDetail,
    otherTransactions: otherTransactions,
    transferAction: transferAction,
    viewRef: viewRef,
    goHome: goHome,
    paymentInfo: paymentInfo,
    goPaymentResultDetail: goPaymentResultDetail,
    totalAmount: totalAmount,
    handleLayoutViewshot: handleLayoutViewshot,
    heightViewshot: heightViewshot,
    isTopup: isTopup
  };
};
/* istanbul ignore next */
cov_18xbprop55().s[76]++;
exports.default = usePaymentResult;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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