{"version": 3, "names": ["cov_2tfl223td", "actualCoverage", "s", "msb_shared_component_1", "require", "exports", "makeStyle", "createMSBStyleSheet", "_ref", "f", "SizeGlobal", "ColorAlias", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Shadow", "button", "width", "margin", "paddingVertical", "justifyContent", "alignItems", "gap", "SizeButton", "SmallSpacingVertical", "buttonGroupData", "flex", "flexDirection", "Size200", "container", "Object", "assign", "align<PERSON><PERSON><PERSON>", "backgroundColor", "ColorGlobal", "White", "marginHorizontal", "SpacingSmall", "borderRadius", "Radius4", "borderWidth", "Size25", "borderColor", "BorderDefault", "minHeight", "marginBottom", "SpacingLarge", "center", "containerFeatureButton", "flexWrap", "containerGroupApp", "height", "getSize", "borderTopWidth", "containerGroupAppItem", "gridIconSize", "IconLarge", "functionIconSize", "IconMedium"], "sources": ["/Users/<USER>/projects/react-native/payment-module/src/presentation/payment-home/components/feature-section/style.ts"], "sourcesContent": ["// import {<PERSON>zeButton, SizeGlobal, ColorGlobal, SizeAlias, ColorAlias, IconButtonSize} from 'msb-shared-component';\n// import {StyleSheet} from 'react-native';\n// import StyleCommon from '../../../../commons/Styles.ts';\n\nimport {createMSBStyleSheet, SizeButton, ColorGlobal, getSize} from 'msb-shared-component';\n\nexport const makeStyle = createMSBStyleSheet(({SizeGlobal, ColorAlias, SizeAlias, Shadow}) => {\n  return {\n    button: {\n      // backgroundColor: ColorGlobal.Red500,\n      width: '30%',\n      margin: '1.5%',\n      // height: 68, // TODO: Designer\n      paddingVertical: 12,\n      justifyContent: 'center',\n      alignItems: 'center',\n      gap: SizeButton.SmallSpacingVertical,\n    },\n    buttonGroupData: {\n      alignItems: 'center',\n      flex: 1,\n      flexDirection: 'row',\n      gap: SizeGlobal.Size200,\n      justifyContent: 'center',\n    },\n    container: {\n      alignContent: 'center',\n      justifyContent: 'center',\n      alignItems: 'stretch',\n      backgroundColor: ColorGlobal.White,\n      marginHorizontal: SizeAlias.SpacingSmall,\n      borderRadius: SizeAlias.Radius4,\n      borderWidth: SizeGlobal.Size25,\n      borderColor: ColorAlias.BorderDefault,\n      // gap: SizeAlias.SpacingSmall,\n      minHeight: 232, // TODO: Designer\n      marginBottom: SizeAlias.SpacingLarge,\n      ...Shadow.center,\n    },\n    containerFeatureButton: {\n      flexDirection: 'row',\n      flexWrap: 'wrap', // Tự động xuống dòng\n      justifyContent: 'flex-start', // Căn trái\n    },\n    containerGroupApp: {\n      // backgroundColor: ColorGlobal.Red500,\n      height: getSize(52), // TODO: Designer\n      borderTopWidth: SizeGlobal.Size25,\n      borderColor: ColorAlias.BorderDefault,\n      flexDirection: 'row',\n    },\n    containerGroupAppItem: {\n      flex: 1,\n      justifyContent: 'center',\n      alignItems: 'center',\n    },\n    gridIconSize: {\n      height: SizeAlias.IconLarge,\n      width: SizeAlias.IconLarge,\n    },\n\n    functionIconSize: {\n      height: SizeAlias.IconMedium,\n      width: SizeAlias.IconMedium,\n    },\n  };\n});\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAaM;IAAAA,aAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,aAAA;AAAAA,aAAA,GAAAE,CAAA;;;;;;;AATN,IAAAC,sBAAA;AAAA;AAAA,CAAAH,aAAA,GAAAE,CAAA,OAAAE,OAAA;AAAA;AAAAJ,aAAA,GAAAE,CAAA;AAEaG,OAAA,CAAAC,SAAS,GAAG,IAAAH,sBAAA,CAAAI,mBAAmB,EAAC,UAAAC,IAAA,EAAgD;EAAA;EAAAR,aAAA,GAAAS,CAAA;EAAA,IAA9CC,UAAU;IAAA;IAAA,CAAAV,aAAA,GAAAE,CAAA,OAAAM,IAAA,CAAVE,UAAU;IAAEC,UAAU;IAAA;IAAA,CAAAX,aAAA,GAAAE,CAAA,OAAAM,IAAA,CAAVG,UAAU;IAAEC,SAAS;IAAA;IAAA,CAAAZ,aAAA,GAAAE,CAAA,OAAAM,IAAA,CAATI,SAAS;IAAEC,MAAM;IAAA;IAAA,CAAAb,aAAA,GAAAE,CAAA,OAAAM,IAAA,CAANK,MAAM;EAAA;EAAAb,aAAA,GAAAE,CAAA;EACtF,OAAO;IACLY,MAAM,EAAE;MAENC,KAAK,EAAE,KAAK;MACZC,MAAM,EAAE,MAAM;MAEdC,eAAe,EAAE,EAAE;MACnBC,cAAc,EAAE,QAAQ;MACxBC,UAAU,EAAE,QAAQ;MACpBC,GAAG,EAAEjB,sBAAA,CAAAkB,UAAU,CAACC;KACjB;IACDC,eAAe,EAAE;MACfJ,UAAU,EAAE,QAAQ;MACpBK,IAAI,EAAE,CAAC;MACPC,aAAa,EAAE,KAAK;MACpBL,GAAG,EAAEV,UAAU,CAACgB,OAAO;MACvBR,cAAc,EAAE;KACjB;IACDS,SAAS,EAAAC,MAAA,CAAAC,MAAA;MACPC,YAAY,EAAE,QAAQ;MACtBZ,cAAc,EAAE,QAAQ;MACxBC,UAAU,EAAE,SAAS;MACrBY,eAAe,EAAE5B,sBAAA,CAAA6B,WAAW,CAACC,KAAK;MAClCC,gBAAgB,EAAEtB,SAAS,CAACuB,YAAY;MACxCC,YAAY,EAAExB,SAAS,CAACyB,OAAO;MAC/BC,WAAW,EAAE5B,UAAU,CAAC6B,MAAM;MAC9BC,WAAW,EAAE7B,UAAU,CAAC8B,aAAa;MAErCC,SAAS,EAAE,GAAG;MACdC,YAAY,EAAE/B,SAAS,CAACgC;IAAY,GACjC/B,MAAM,CAACgC,MAAM,CACjB;IACDC,sBAAsB,EAAE;MACtBrB,aAAa,EAAE,KAAK;MACpBsB,QAAQ,EAAE,MAAM;MAChB7B,cAAc,EAAE;KACjB;IACD8B,iBAAiB,EAAE;MAEjBC,MAAM,EAAE,IAAA9C,sBAAA,CAAA+C,OAAO,EAAC,EAAE,CAAC;MACnBC,cAAc,EAAEzC,UAAU,CAAC6B,MAAM;MACjCC,WAAW,EAAE7B,UAAU,CAAC8B,aAAa;MACrChB,aAAa,EAAE;KAChB;IACD2B,qBAAqB,EAAE;MACrB5B,IAAI,EAAE,CAAC;MACPN,cAAc,EAAE,QAAQ;MACxBC,UAAU,EAAE;KACb;IACDkC,YAAY,EAAE;MACZJ,MAAM,EAAErC,SAAS,CAAC0C,SAAS;MAC3BvC,KAAK,EAAEH,SAAS,CAAC0C;KAClB;IAEDC,gBAAgB,EAAE;MAChBN,MAAM,EAAErC,SAAS,CAAC4C,UAAU;MAC5BzC,KAAK,EAAEH,SAAS,CAAC4C;;GAEpB;AACH,CAAC,CAAC", "ignoreList": []}