{"version": 3, "names": ["cov_21ls39lbat", "actualCoverage", "GetBillDetailMapper_1", "s", "require", "BillValidateMapper_1", "MyBillListMapper_1", "ProviderListMapper_1", "CategoryListMapper_1", "HandleData_1", "BillPayRepository", "f", "remoteDataSource", "_classCallCheck2", "default", "_createClass2", "key", "value", "_categoryList", "_asyncToGenerator2", "handleData", "categoryList", "mapCategoryListResponseToModel", "apply", "arguments", "_providerList", "request", "providerList", "mapProviderListResponseToModel", "_x", "_myBillList", "myBillList", "mapMyBillListResponseToModel", "_x2", "_getBillDetail", "getBillDetail", "mapGetBillDetailResponseToModel", "_x3", "_billValidate", "billValidate", "mapBillValidateResponseToModel", "_x4", "exports"], "sources": ["/Users/<USER>/projects/react-native/payment-module/src/data/repositories/BillPayRepository.ts"], "sourcesContent": ["import {mapGetBillDetailResponseToModel} from '../mappers/get-bill-detail/GetBillDetailMapper';\nimport {GetBillDetailModel} from '../../domain/entities/get-bill-detail/GetBillDetailModel';\nimport {GetBillDetailRequest} from '../models/get-bill-detail/GetBillDetailRequest';\nimport {mapBillValidateResponseToModel} from '../mappers/bill-validate/BillValidateMapper';\nimport {BillValidateModel} from '../../domain/entities/bill-validate/BillValidateModel';\nimport {mapMyBillListResponseToModel} from '../mappers/my-bill-list/MyBillListMapper';\nimport {MyBillListRequest} from '../models/my-bill-list/MyBillListRequest';\nimport {mapProviderListResponseToModel} from '../mappers/provider-list/ProviderListMapper';\nimport {ProviderListModel} from '../../domain/entities/provider-list/ProviderListModel';\nimport {ProviderListRequest} from '../models/provider-list/ProviderListRequest';\nimport {mapCategoryListResponseToModel} from '../mappers/category-list/CategoryListMapper';\nimport {handleData} from '../../utils/HandleData';\nimport {CategoryListModel} from '../../domain/entities/category-list/CategoryListModel';\nimport {IBillPayDataSource} from '../datasources/IBillPayDataSource';\nimport {BaseResponse} from '../../core/BaseResponse';\nimport {IBillPayRepository} from '../../domain/repositories/IBillPayRepository';\nimport {BillValidateRequest} from '../models/bill-validate/BillValidateRequest';\nimport {MyBillContactListModel} from '../../domain/entities/my-bill-contact-list/MyBillContactListModel';\n\nexport class BillPayRepository implements IBillPayRepository {\n  private remoteDataSource: IBillPayDataSource;\n\n  constructor(remoteDataSource: IBillPayDataSource) {\n    this.remoteDataSource = remoteDataSource;\n  }\n\n  async categoryList(): Promise<BaseResponse<CategoryListModel>> {\n    return handleData<CategoryListModel>(this.remoteDataSource.categoryList(), mapCategoryListResponseToModel);\n  }\n\n  async providerList(request: ProviderListRequest): Promise<BaseResponse<ProviderListModel>> {\n    return handleData<ProviderListModel>(this.remoteDataSource.providerList(request), mapProviderListResponseToModel);\n  }\n\n  async myBillList(request: MyBillListRequest): Promise<BaseResponse<MyBillContactListModel>> {\n    return handleData<MyBillContactListModel>(this.remoteDataSource.myBillList(request), mapMyBillListResponseToModel);\n  }\n\n  async getBillDetail(request: GetBillDetailRequest): Promise<BaseResponse<GetBillDetailModel>> {\n    return handleData<GetBillDetailModel>(\n      this.remoteDataSource.getBillDetail(request),\n      mapGetBillDetailResponseToModel,\n    );\n  }\n\n  async billValidate(request: BillValidateRequest): Promise<BaseResponse<BillValidateModel>> {\n    return handleData<BillValidateModel>(this.remoteDataSource.billValidate(request), mapBillValidateResponseToModel);\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAWA;IAAAA,cAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,cAAA;;;;;;;;;;;;;;;;;;;;;AAXA,IAAAE,qBAAA;AAAA;AAAA,CAAAF,cAAA,GAAAG,CAAA,OAAAC,OAAA;AAGA,IAAAC,oBAAA;AAAA;AAAA,CAAAL,cAAA,GAAAG,CAAA,OAAAC,OAAA;AAEA,IAAAE,kBAAA;AAAA;AAAA,CAAAN,cAAA,GAAAG,CAAA,OAAAC,OAAA;AAEA,IAAAG,oBAAA;AAAA;AAAA,CAAAP,cAAA,GAAAG,CAAA,OAAAC,OAAA;AAGA,IAAAI,oBAAA;AAAA;AAAA,CAAAR,cAAA,GAAAG,CAAA,QAAAC,OAAA;AACA,IAAAK,YAAA;AAAA;AAAA,CAAAT,cAAA,GAAAG,CAAA,QAAAC,OAAA;AAAkD,IAQrCM,iBAAiB;AAAA;AAAA,CAAAV,cAAA,GAAAG,CAAA;EAAA;EAAAH,cAAA,GAAAW,CAAA;EAG5B,SAAAD,kBAAYE,gBAAoC;IAAA;IAAAZ,cAAA,GAAAW,CAAA;IAAAX,cAAA,GAAAG,CAAA;IAAA,IAAAU,gBAAA,CAAAC,OAAA,QAAAJ,iBAAA;IAAA;IAAAV,cAAA,GAAAG,CAAA;IAC9C,IAAI,CAACS,gBAAgB,GAAGA,gBAAgB;EAC1C;EAAA;EAAAZ,cAAA,GAAAG,CAAA;EAAC,WAAAY,aAAA,CAAAD,OAAA,EAAAJ,iBAAA;IAAAM,GAAA;IAAAC,KAAA;MAAA;MAAAjB,cAAA,GAAAW,CAAA;MAAA,IAAAO,aAAA;MAAA;MAAA,CAAAlB,cAAA,GAAAG,CAAA,YAAAgB,kBAAA,CAAAL,OAAA,EAED,aAAkB;QAAA;QAAAd,cAAA,GAAAW,CAAA;QAAAX,cAAA,GAAAG,CAAA;QAChB,OAAO,IAAAM,YAAA,CAAAW,UAAU,EAAoB,IAAI,CAACR,gBAAgB,CAACS,YAAY,EAAE,EAAEb,oBAAA,CAAAc,8BAA8B,CAAC;MAC5G,CAAC;MAAA,SAFKD,YAAYA,CAAA;QAAA;QAAArB,cAAA,GAAAW,CAAA;QAAAX,cAAA,GAAAG,CAAA;QAAA,OAAAe,aAAA,CAAAK,KAAA,OAAAC,SAAA;MAAA;MAAA;MAAAxB,cAAA,GAAAG,CAAA;MAAA,OAAZkB,YAAY;IAAA;EAAA;IAAAL,GAAA;IAAAC,KAAA;MAAA;MAAAjB,cAAA,GAAAW,CAAA;MAAA,IAAAc,aAAA;MAAA;MAAA,CAAAzB,cAAA,GAAAG,CAAA,YAAAgB,kBAAA,CAAAL,OAAA,EAIlB,WAAmBY,OAA4B;QAAA;QAAA1B,cAAA,GAAAW,CAAA;QAAAX,cAAA,GAAAG,CAAA;QAC7C,OAAO,IAAAM,YAAA,CAAAW,UAAU,EAAoB,IAAI,CAACR,gBAAgB,CAACe,YAAY,CAACD,OAAO,CAAC,EAAEnB,oBAAA,CAAAqB,8BAA8B,CAAC;MACnH,CAAC;MAAA,SAFKD,YAAYA,CAAAE,EAAA;QAAA;QAAA7B,cAAA,GAAAW,CAAA;QAAAX,cAAA,GAAAG,CAAA;QAAA,OAAAsB,aAAA,CAAAF,KAAA,OAAAC,SAAA;MAAA;MAAA;MAAAxB,cAAA,GAAAG,CAAA;MAAA,OAAZwB,YAAY;IAAA;EAAA;IAAAX,GAAA;IAAAC,KAAA;MAAA;MAAAjB,cAAA,GAAAW,CAAA;MAAA,IAAAmB,WAAA;MAAA;MAAA,CAAA9B,cAAA,GAAAG,CAAA,YAAAgB,kBAAA,CAAAL,OAAA,EAIlB,WAAiBY,OAA0B;QAAA;QAAA1B,cAAA,GAAAW,CAAA;QAAAX,cAAA,GAAAG,CAAA;QACzC,OAAO,IAAAM,YAAA,CAAAW,UAAU,EAAyB,IAAI,CAACR,gBAAgB,CAACmB,UAAU,CAACL,OAAO,CAAC,EAAEpB,kBAAA,CAAA0B,4BAA4B,CAAC;MACpH,CAAC;MAAA,SAFKD,UAAUA,CAAAE,GAAA;QAAA;QAAAjC,cAAA,GAAAW,CAAA;QAAAX,cAAA,GAAAG,CAAA;QAAA,OAAA2B,WAAA,CAAAP,KAAA,OAAAC,SAAA;MAAA;MAAA;MAAAxB,cAAA,GAAAG,CAAA;MAAA,OAAV4B,UAAU;IAAA;EAAA;IAAAf,GAAA;IAAAC,KAAA;MAAA;MAAAjB,cAAA,GAAAW,CAAA;MAAA,IAAAuB,cAAA;MAAA;MAAA,CAAAlC,cAAA,GAAAG,CAAA,YAAAgB,kBAAA,CAAAL,OAAA,EAIhB,WAAoBY,OAA6B;QAAA;QAAA1B,cAAA,GAAAW,CAAA;QAAAX,cAAA,GAAAG,CAAA;QAC/C,OAAO,IAAAM,YAAA,CAAAW,UAAU,EACf,IAAI,CAACR,gBAAgB,CAACuB,aAAa,CAACT,OAAO,CAAC,EAC5CxB,qBAAA,CAAAkC,+BAA+B,CAChC;MACH,CAAC;MAAA,SALKD,aAAaA,CAAAE,GAAA;QAAA;QAAArC,cAAA,GAAAW,CAAA;QAAAX,cAAA,GAAAG,CAAA;QAAA,OAAA+B,cAAA,CAAAX,KAAA,OAAAC,SAAA;MAAA;MAAA;MAAAxB,cAAA,GAAAG,CAAA;MAAA,OAAbgC,aAAa;IAAA;EAAA;IAAAnB,GAAA;IAAAC,KAAA;MAAA;MAAAjB,cAAA,GAAAW,CAAA;MAAA,IAAA2B,aAAA;MAAA;MAAA,CAAAtC,cAAA,GAAAG,CAAA,YAAAgB,kBAAA,CAAAL,OAAA,EAOnB,WAAmBY,OAA4B;QAAA;QAAA1B,cAAA,GAAAW,CAAA;QAAAX,cAAA,GAAAG,CAAA;QAC7C,OAAO,IAAAM,YAAA,CAAAW,UAAU,EAAoB,IAAI,CAACR,gBAAgB,CAAC2B,YAAY,CAACb,OAAO,CAAC,EAAErB,oBAAA,CAAAmC,8BAA8B,CAAC;MACnH,CAAC;MAAA,SAFKD,YAAYA,CAAAE,GAAA;QAAA;QAAAzC,cAAA,GAAAW,CAAA;QAAAX,cAAA,GAAAG,CAAA;QAAA,OAAAmC,aAAA,CAAAf,KAAA,OAAAC,SAAA;MAAA;MAAA;MAAAxB,cAAA,GAAAG,CAAA;MAAA,OAAZoC,YAAY;IAAA;EAAA;AAAA;AAAA;AAAAvC,cAAA,GAAAG,CAAA;AA1BpBuC,OAAA,CAAAhC,iBAAA,GAAAA,iBAAA", "ignoreList": []}