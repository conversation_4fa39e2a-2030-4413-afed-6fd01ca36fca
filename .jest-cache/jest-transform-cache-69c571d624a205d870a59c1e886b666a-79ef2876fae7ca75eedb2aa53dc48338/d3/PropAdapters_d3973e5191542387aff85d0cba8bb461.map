{"version": 3, "names": ["Object", "defineProperty", "exports", "value", "createAnimatedPropAdapter", "_ConfigHelper", "require", "adapter", "nativeProps", "nativePropsToAdd", "for<PERSON>ach", "prop", "addWhitelistedNativeProps"], "sources": ["../../src/PropAdapters.ts"], "sourcesContent": [null], "mappings": "AAAA,YAAY;;AAAAA,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,KAAA;AAAA;AAAAD,OAAA,CAAAE,yBAAA,GAAAA,yBAAA;AACZ,IAAAC,aAAA,GAAAC,OAAA;AAYO,SAASF,yBAAyBA,CACvCG,OAAoC,EACpCC,WAAsB,EACO;EAC7B,IAAMC,gBAA4C,GAAG,CAAC,CAAC;EACvDD,WAAW,YAAXA,WAAW,CAAEE,OAAO,CAAE,UAAAC,IAAI,EAAK;IAC7BF,gBAAgB,CAACE,IAAI,CAAC,GAAG,IAAI;EAC/B,CAAC,CAAC;EACF,IAAAC,uCAAyB,EAACH,gBAAgB,CAAC;EAC3C,OAAOF,OAAO;AAChB", "ignoreList": []}