{"version": 3, "names": ["_interopRequireDefault", "require", "Object", "defineProperty", "exports", "value", "getImageSourcesFromImageProps", "_slicedToArray2", "_resolveAssetSource", "imageProps", "source", "resolveAssetSource", "sources", "crossOrigin", "referrerPolicy", "src", "srcSet", "width", "height", "headers", "sourceList", "srcSetList", "split", "shouldUseSrcForDefaultScale", "for<PERSON>ach", "imageSrc", "_imageSrc$split", "_imageSrc$split2", "default", "uri", "_imageSrc$split2$", "xScale", "endsWith", "console", "warn", "scale", "parseInt", "isNaN", "push", "length"], "sources": ["ImageSourceUtils.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @flow strict-local\n * @format\n */\n\n'use strict';\n\nimport type {ResolvedAssetSource} from './AssetSourceResolver';\nimport type {ImageProps} from './ImageProps';\n\nimport resolveAssetSource from './resolveAssetSource';\n\n/**\n * A function which returns the appropriate value for image source\n * by resolving the `source`, `src` and `srcSet` props.\n */\nexport function getImageSourcesFromImageProps(\n  imageProps: ImageProps,\n): ?ResolvedAssetSource | $ReadOnlyArray<{uri: string, ...}> {\n  let source = resolveAssetSource(imageProps.source);\n\n  let sources;\n\n  const {crossOrigin, referrerPolicy, src, srcSet, width, height} = imageProps;\n\n  const headers: {[string]: string} = {};\n  if (crossOrigin === 'use-credentials') {\n    headers['Access-Control-Allow-Credentials'] = 'true';\n  }\n  if (referrerPolicy != null) {\n    headers['Referrer-Policy'] = referrerPolicy;\n  }\n  if (srcSet != null) {\n    const sourceList = [];\n    const srcSetList = srcSet.split(', ');\n    // `src` prop should be used with default scale if `srcSet` does not have 1x scale.\n    let shouldUseSrcForDefaultScale = true;\n    srcSetList.forEach(imageSrc => {\n      const [uri, xScale = '1x'] = imageSrc.split(' ');\n      if (!xScale.endsWith('x')) {\n        console.warn(\n          'The provided format for scale is not supported yet. Please use scales like 1x, 2x, etc.',\n        );\n      } else {\n        const scale = parseInt(xScale.split('x')[0], 10);\n        if (!isNaN(scale)) {\n          // 1x scale is provided in `srcSet` prop so ignore the `src` prop if provided.\n          shouldUseSrcForDefaultScale =\n            scale === 1 ? false : shouldUseSrcForDefaultScale;\n          sourceList.push({headers: headers, scale, uri, width, height});\n        }\n      }\n    });\n\n    if (shouldUseSrcForDefaultScale && src != null) {\n      sourceList.push({\n        headers: headers,\n        scale: 1,\n        uri: src,\n        width,\n        height,\n      });\n    }\n    if (sourceList.length === 0) {\n      console.warn('The provided value for srcSet is not valid.');\n    }\n\n    sources = sourceList;\n  } else if (src != null) {\n    sources = [{uri: src, headers: headers, width, height}];\n  } else {\n    sources = source;\n  }\n  return sources;\n}\n"], "mappings": "AAUA,YAAY;;AAAC,IAAAA,sBAAA,GAAAC,OAAA;AAAAC,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,KAAA;AAAA;AAAAD,OAAA,CAAAE,6BAAA,GAAAA,6BAAA;AAAA,IAAAC,eAAA,GAAAP,sBAAA,CAAAC,OAAA;AAKb,IAAAO,mBAAA,GAAAR,sBAAA,CAAAC,OAAA;AAMO,SAASK,6BAA6BA,CAC3CG,UAAsB,EACqC;EAC3D,IAAIC,MAAM,GAAG,IAAAC,2BAAkB,EAACF,UAAU,CAACC,MAAM,CAAC;EAElD,IAAIE,OAAO;EAEX,IAAOC,WAAW,GAAgDJ,UAAU,CAArEI,WAAW;IAAEC,cAAc,GAAgCL,UAAU,CAAxDK,cAAc;IAAEC,GAAG,GAA2BN,UAAU,CAAxCM,GAAG;IAAEC,MAAM,GAAmBP,UAAU,CAAnCO,MAAM;IAAEC,KAAK,GAAYR,UAAU,CAA3BQ,KAAK;IAAEC,MAAM,GAAIT,UAAU,CAApBS,MAAM;EAE9D,IAAMC,OAA2B,GAAG,CAAC,CAAC;EACtC,IAAIN,WAAW,KAAK,iBAAiB,EAAE;IACrCM,OAAO,CAAC,kCAAkC,CAAC,GAAG,MAAM;EACtD;EACA,IAAIL,cAAc,IAAI,IAAI,EAAE;IAC1BK,OAAO,CAAC,iBAAiB,CAAC,GAAGL,cAAc;EAC7C;EACA,IAAIE,MAAM,IAAI,IAAI,EAAE;IAClB,IAAMI,UAAU,GAAG,EAAE;IACrB,IAAMC,UAAU,GAAGL,MAAM,CAACM,KAAK,CAAC,IAAI,CAAC;IAErC,IAAIC,2BAA2B,GAAG,IAAI;IACtCF,UAAU,CAACG,OAAO,CAAC,UAAAC,QAAQ,EAAI;MAC7B,IAAAC,eAAA,GAA6BD,QAAQ,CAACH,KAAK,CAAC,GAAG,CAAC;QAAAK,gBAAA,OAAApB,eAAA,CAAAqB,OAAA,EAAAF,eAAA;QAAzCG,GAAG,GAAAF,gBAAA;QAAAG,iBAAA,GAAAH,gBAAA;QAAEI,MAAM,GAAAD,iBAAA,cAAG,IAAI,GAAAA,iBAAA;MACzB,IAAI,CAACC,MAAM,CAACC,QAAQ,CAAC,GAAG,CAAC,EAAE;QACzBC,OAAO,CAACC,IAAI,CACV,yFACF,CAAC;MACH,CAAC,MAAM;QACL,IAAMC,KAAK,GAAGC,QAAQ,CAACL,MAAM,CAACT,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;QAChD,IAAI,CAACe,KAAK,CAACF,KAAK,CAAC,EAAE;UAEjBZ,2BAA2B,GACzBY,KAAK,KAAK,CAAC,GAAG,KAAK,GAAGZ,2BAA2B;UACnDH,UAAU,CAACkB,IAAI,CAAC;YAACnB,OAAO,EAAEA,OAAO;YAAEgB,KAAK,EAALA,KAAK;YAAEN,GAAG,EAAHA,GAAG;YAAEZ,KAAK,EAALA,KAAK;YAAEC,MAAM,EAANA;UAAM,CAAC,CAAC;QAChE;MACF;IACF,CAAC,CAAC;IAEF,IAAIK,2BAA2B,IAAIR,GAAG,IAAI,IAAI,EAAE;MAC9CK,UAAU,CAACkB,IAAI,CAAC;QACdnB,OAAO,EAAEA,OAAO;QAChBgB,KAAK,EAAE,CAAC;QACRN,GAAG,EAAEd,GAAG;QACRE,KAAK,EAALA,KAAK;QACLC,MAAM,EAANA;MACF,CAAC,CAAC;IACJ;IACA,IAAIE,UAAU,CAACmB,MAAM,KAAK,CAAC,EAAE;MAC3BN,OAAO,CAACC,IAAI,CAAC,6CAA6C,CAAC;IAC7D;IAEAtB,OAAO,GAAGQ,UAAU;EACtB,CAAC,MAAM,IAAIL,GAAG,IAAI,IAAI,EAAE;IACtBH,OAAO,GAAG,CAAC;MAACiB,GAAG,EAAEd,GAAG;MAAEI,OAAO,EAAEA,OAAO;MAAEF,KAAK,EAALA,KAAK;MAAEC,MAAM,EAANA;IAAM,CAAC,CAAC;EACzD,CAAC,MAAM;IACLN,OAAO,GAAGF,MAAM;EAClB;EACA,OAAOE,OAAO;AAChB", "ignoreList": []}