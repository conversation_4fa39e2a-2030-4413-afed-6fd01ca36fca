7a6af7be3a50af6456f0757b35ba781e
'use strict';

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.getImageSourcesFromImageProps = getImageSourcesFromImageProps;
var _slicedToArray2 = _interopRequireDefault(require("@babel/runtime/helpers/slicedToArray"));
var _resolveAssetSource = _interopRequireDefault(require("./resolveAssetSource"));
function getImageSourcesFromImageProps(imageProps) {
  var source = (0, _resolveAssetSource.default)(imageProps.source);
  var sources;
  var crossOrigin = imageProps.crossOrigin,
    referrerPolicy = imageProps.referrerPolicy,
    src = imageProps.src,
    srcSet = imageProps.srcSet,
    width = imageProps.width,
    height = imageProps.height;
  var headers = {};
  if (crossOrigin === 'use-credentials') {
    headers['Access-Control-Allow-Credentials'] = 'true';
  }
  if (referrerPolicy != null) {
    headers['Referrer-Policy'] = referrerPolicy;
  }
  if (srcSet != null) {
    var sourceList = [];
    var srcSetList = srcSet.split(', ');
    var shouldUseSrcForDefaultScale = true;
    srcSetList.forEach(function (imageSrc) {
      var _imageSrc$split = imageSrc.split(' '),
        _imageSrc$split2 = (0, _slicedToArray2.default)(_imageSrc$split, 2),
        uri = _imageSrc$split2[0],
        _imageSrc$split2$ = _imageSrc$split2[1],
        xScale = _imageSrc$split2$ === void 0 ? '1x' : _imageSrc$split2$;
      if (!xScale.endsWith('x')) {
        console.warn('The provided format for scale is not supported yet. Please use scales like 1x, 2x, etc.');
      } else {
        var scale = parseInt(xScale.split('x')[0], 10);
        if (!isNaN(scale)) {
          shouldUseSrcForDefaultScale = scale === 1 ? false : shouldUseSrcForDefaultScale;
          sourceList.push({
            headers: headers,
            scale: scale,
            uri: uri,
            width: width,
            height: height
          });
        }
      }
    });
    if (shouldUseSrcForDefaultScale && src != null) {
      sourceList.push({
        headers: headers,
        scale: 1,
        uri: src,
        width: width,
        height: height
      });
    }
    if (sourceList.length === 0) {
      console.warn('The provided value for srcSet is not valid.');
    }
    sources = sourceList;
  } else if (src != null) {
    sources = [{
      uri: src,
      headers: headers,
      width: width,
      height: height
    }];
  } else {
    sources = source;
  }
  return sources;
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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