b99109cb7e2ac0c94a5cc8d3f8a42ad1
'use strict';

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
var _composeStyles = _interopRequireDefault(require("../../src/private/styles/composeStyles"));
var ReactNativeStyleAttributes = require('../Components/View/ReactNativeStyleAttributes');
var PixelRatio = require('../Utilities/PixelRatio').default;
var flatten = require('./flattenStyle');
var hairlineWidth = PixelRatio.roundToNearestPixel(0.4);
if (hairlineWidth === 0) {
  hairlineWidth = 1 / PixelRatio.get();
}
var absoluteFill = {
  position: 'absolute',
  left: 0,
  right: 0,
  top: 0,
  bottom: 0
};
if (__DEV__) {
  Object.freeze(absoluteFill);
}
module.exports = {
  hairlineWidth: hairlineWidth,
  absoluteFill: absoluteFill,
  absoluteFillObject: absoluteFill,
  compose: _composeStyles.default,
  flatten: flatten,
  setStyleAttributePreprocessor: function setStyleAttributePreprocessor(property, process) {
    var _ReactNativeStyleAttr, _ReactNativeStyleAttr2;
    var value;
    if (ReactNativeStyleAttributes[property] === true) {
      value = {
        process: process
      };
    } else if (typeof ReactNativeStyleAttributes[property] === 'object') {
      value = Object.assign({}, ReactNativeStyleAttributes[property], {
        process: process
      });
    } else {
      console.error(`${property} is not a valid style attribute`);
      return;
    }
    if (__DEV__ && typeof value.process === 'function' && typeof ((_ReactNativeStyleAttr = ReactNativeStyleAttributes[property]) == null ? void 0 : _ReactNativeStyleAttr.process) === 'function' && value.process !== ((_ReactNativeStyleAttr2 = ReactNativeStyleAttributes[property]) == null ? void 0 : _ReactNativeStyleAttr2.process)) {
      console.warn(`Overwriting ${property} style attribute preprocessor`);
    }
    ReactNativeStyleAttributes[property] = value;
  },
  create: function create(obj) {
    if (__DEV__) {
      for (var _key in obj) {
        if (obj[_key]) {
          Object.freeze(obj[_key]);
        }
      }
    }
    return obj;
  }
};
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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