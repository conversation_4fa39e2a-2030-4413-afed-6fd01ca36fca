7a48e90aa71587a10ffa3817957f297d
"use strict";

/* istanbul ignore next */
function cov_9hnw53pqb() {
  var path = "/Users/<USER>/projects/react-native/payment-module/src/data/models/delete-bill-contact/DeleteBillContactResponse.ts";
  var hash = "832b0687b610fd0d77fa3ff09a07a39ef35f0062";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/projects/react-native/payment-module/src/data/models/delete-bill-contact/DeleteBillContactResponse.ts",
    statementMap: {
      "0": {
        start: {
          line: 3,
          column: 0
        },
        end: {
          line: 5,
          column: 3
        }
      }
    },
    fnMap: {},
    branchMap: {},
    s: {
      "0": 0
    },
    f: {},
    b: {},
    inputSourceMap: {
      version: 3,
      file: undefined,
      names: [],
      sourceRoot: undefined,
      sources: ["/Users/<USER>/projects/react-native/payment-module/src/data/models/delete-bill-contact/DeleteBillContactResponse.ts"],
      sourcesContent: ["export interface DeleteBillContactResponse {\n  // TODO: define fields\n}\n"],
      mappings: "",
      ignoreList: []
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "832b0687b610fd0d77fa3ff09a07a39ef35f0062"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_9hnw53pqb = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_9hnw53pqb();
cov_9hnw53pqb().s[0]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6W10sInNvdXJjZXMiOlsiL1VzZXJzL3NvbnNhbXNldC9wcm9qZWN0cy9yZWFjdC1uYXRpdmUvcGF5bWVudC1tb2R1bGUvc3JjL2RhdGEvbW9kZWxzL2RlbGV0ZS1iaWxsLWNvbnRhY3QvRGVsZXRlQmlsbENvbnRhY3RSZXNwb25zZS50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgaW50ZXJmYWNlIERlbGV0ZUJpbGxDb250YWN0UmVzcG9uc2Uge1xuICAvLyBUT0RPOiBkZWZpbmUgZmllbGRzXG59XG4iXSwibWFwcGluZ3MiOiIiLCJpZ25vcmVMaXN0IjpbXX0=