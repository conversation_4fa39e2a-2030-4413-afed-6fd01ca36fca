{"version": 3, "names": ["_interopRequireDefault", "require", "Object", "defineProperty", "exports", "value", "default", "_classCallCheck2", "_createClass2", "_NativeReanimated", "_commonTypes", "_mutables", "initSensorData", "sensorType", "SensorType", "ROTATION", "makeMutable", "qw", "qx", "qy", "qz", "yaw", "pitch", "roll", "interfaceOrientation", "x", "y", "z", "Sensor", "config", "listenersNumber", "sensorId", "data", "key", "register", "<PERSON><PERSON><PERSON><PERSON>", "NativeReanimatedModule", "registerSensor", "interval", "iosReferenceFrame", "isRunning", "isAvailable", "getSharedValue", "unregister", "unregisterSensor"], "sources": ["../../src/Sensor.ts"], "sourcesContent": [null], "mappings": "AAAA,YAAY;;AAAA,IAAAA,sBAAA,GAAAC,OAAA;AAAAC,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,KAAA;AAAA;AAAAD,OAAA,CAAAE,OAAA;AAAA,IAAAC,gBAAA,GAAAP,sBAAA,CAAAC,OAAA;AAAA,IAAAO,aAAA,GAAAR,sBAAA,CAAAC,OAAA;AACZ,IAAAQ,iBAAA,GAAAT,sBAAA,CAAAC,OAAA;AAQA,IAAAS,YAAA,GAAAT,OAAA;AACA,IAAAU,SAAA,GAAAV,OAAA;AAEA,SAASW,cAAcA,CACrBC,UAAsB,EACgB;EACtC,IAAIA,UAAU,KAAKC,uBAAU,CAACC,QAAQ,EAAE;IACtC,OAAO,IAAAC,qBAAW,EAA0B;MAC1CC,EAAE,EAAE,CAAC;MACLC,EAAE,EAAE,CAAC;MACLC,EAAE,EAAE,CAAC;MACLC,EAAE,EAAE,CAAC;MACLC,GAAG,EAAE,CAAC;MACNC,KAAK,EAAE,CAAC;MACRC,IAAI,EAAE,CAAC;MACPC,oBAAoB,EAAE;IACxB,CAAC,CAAC;EACJ,CAAC,MAAM;IACL,OAAO,IAAAR,qBAAW,EAA0B;MAC1CS,CAAC,EAAE,CAAC;MACJC,CAAC,EAAE,CAAC;MACJC,CAAC,EAAE,CAAC;MACJH,oBAAoB,EAAE;IACxB,CAAC,CAAC;EACJ;AACF;AAAA,IAEqBI,MAAM,GAAAxB,OAAA,CAAAE,OAAA;EAOzB,SAAAsB,OAAYf,UAAsB,EAAEgB,MAAoB,EAAE;IAAA,IAAAtB,gBAAA,CAAAD,OAAA,QAAAsB,MAAA;IAAA,KANnDE,eAAe,GAAG,CAAC;IAAA,KAClBC,QAAQ,GAAkB,IAAI;IAMpC,IAAI,CAAClB,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACgB,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACG,IAAI,GAAGpB,cAAc,CAACC,UAAU,CAAC;EACxC;EAAA,WAAAL,aAAA,CAAAF,OAAA,EAAAsB,MAAA;IAAAK,GAAA;IAAA5B,KAAA,EAEA,SAAA6B,QAAQA,CACNC,YAAmE,EACnE;MACA,IAAMN,MAAM,GAAG,IAAI,CAACA,MAAM;MAC1B,IAAMhB,UAAU,GAAG,IAAI,CAACA,UAAU;MAClC,IAAI,CAACkB,QAAQ,GAAGK,yBAAsB,CAACC,cAAc,CACnDxB,UAAU,EACVgB,MAAM,CAACS,QAAQ,KAAK,MAAM,GAAG,CAAC,CAAC,GAAGT,MAAM,CAACS,QAAQ,EACjDT,MAAM,CAACU,iBAAiB,EACxBJ,YACF,CAAC;MACD,OAAO,IAAI,CAACJ,QAAQ,KAAK,CAAC,CAAC;IAC7B;EAAA;IAAAE,GAAA;IAAA5B,KAAA,EAEA,SAAAmC,SAASA,CAAA,EAAG;MACV,OAAO,IAAI,CAACT,QAAQ,KAAK,CAAC,CAAC,IAAI,IAAI,CAACA,QAAQ,KAAK,IAAI;IACvD;EAAA;IAAAE,GAAA;IAAA5B,KAAA,EAEA,SAAAoC,WAAWA,CAAA,EAAG;MACZ,OAAO,IAAI,CAACV,QAAQ,KAAK,CAAC,CAAC;IAC7B;EAAA;IAAAE,GAAA;IAAA5B,KAAA,EAEA,SAAAqC,cAAcA,CAAA,EAAG;MACf,OAAO,IAAI,CAACV,IAAI;IAClB;EAAA;IAAAC,GAAA;IAAA5B,KAAA,EAEA,SAAAsC,UAAUA,CAAA,EAAG;MACX,IAAI,IAAI,CAACZ,QAAQ,KAAK,IAAI,IAAI,IAAI,CAACA,QAAQ,KAAK,CAAC,CAAC,EAAE;QAClDK,yBAAsB,CAACQ,gBAAgB,CAAC,IAAI,CAACb,QAAQ,CAAC;MACxD;MACA,IAAI,CAACA,QAAQ,GAAG,IAAI;IACtB;EAAA;AAAA", "ignoreList": []}