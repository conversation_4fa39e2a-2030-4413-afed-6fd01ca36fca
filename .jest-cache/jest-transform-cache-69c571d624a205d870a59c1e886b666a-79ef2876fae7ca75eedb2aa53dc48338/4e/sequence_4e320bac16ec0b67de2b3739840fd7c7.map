{"version": 3, "names": ["Object", "defineProperty", "exports", "value", "withSequence", "_util", "require", "_index", "_reduceMotionOrFirstAnimation", "_len", "arguments", "length", "_animations", "Array", "_key", "reduceMotion", "unshift", "logger", "warn", "defineAnimation", "onStart", "animation", "current", "onFrame", "animationIndex", "getReduceMotionForAnimation", "animations", "map", "a", "result", "finished", "findNextNonReducedMotionAnimationIndex", "index", "callback", "for<PERSON>ach", "sequence", "now", "currentAnim", "nextAnim", "previousAnimation", "anim", "undefined", "currentAnimation", "isHigherOrder"], "sources": ["../../../src/animation/sequence.ts"], "sourcesContent": [null], "mappings": "AAAA,YAAY;;AAAAA,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,KAAA;AAAA;AAAAD,OAAA,CAAAE,YAAA,GAAAA,YAAA;AACZ,IAAAC,KAAA,GAAAC,OAAA;AASA,IAAAC,MAAA,GAAAD,OAAA;AAqBO,SAASF,YAAYA,CAC1BI,6BAA6E,EAE/C;EAC9B,SAAS;;EAAA,SAAAC,IAAA,GAAAC,SAAA,CAAAC,MAAA,EAFNC,WAA6C,OAAAC,KAAA,CAAAJ,IAAA,OAAAA,IAAA,WAAAK,IAAA,MAAAA,IAAA,GAAAL,IAAA,EAAAK,IAAA;IAA7CF,WAA6C,CAAAE,IAAA,QAAAJ,SAAA,CAAAI,IAAA;EAAA;EAGhD,IAAIC,YAAsC;EAI1C,IAAIP,6BAA6B,EAAE;IACjC,IAAI,OAAOA,6BAA6B,KAAK,QAAQ,EAAE;MACrDO,YAAY,GAAGP,6BAA6C;IAC9D,CAAC,MAAM;MACLI,WAAW,CAACI,OAAO,CACjBR,6BACF,CAAC;IACH;EACF;EAEA,IAAII,WAAW,CAACD,MAAM,KAAK,CAAC,EAAE;IAC5BM,aAAM,CAACC,IAAI,CAAC,4CAA4C,CAAC;IAEzD,OAAO,IAAAC,qBAAe,EAAoB,CAAC,EAAE,YAAM;MACjD,SAAS;;MACT,OAAO;QACLC,OAAO,EAAE,SAATA,OAAOA,CAAGC,SAAS,EAAElB,KAAK;UAAA,OAAMkB,SAAS,CAACC,OAAO,GAAGnB,KAAM;QAAA;QAC1DoB,OAAO,EAAE,SAATA,OAAOA,CAAA;UAAA,OAAQ,IAAI;QAAA;QACnBD,OAAO,EAAE,CAAC;QACVE,cAAc,EAAE,CAAC;QACjBT,YAAY,EAAE,IAAAU,iCAA2B,EAACV,YAAY;MACxD,CAAC;IACH,CAAC,CAAC;EACJ;EAEA,OAAO,IAAAI,qBAAe,EACpBP,WAAW,CAAC,CAAC,CAAC,EACd,YAAM;IACJ,SAAS;;IAET,IAAMc,UAAU,GAAGd,WAAW,CAACe,GAAG,CAAE,UAAAC,CAAC,EAAK;MACxC,IAAMC,MAAM,GAAG,OAAOD,CAAC,KAAK,UAAU,GAAGA,CAAC,CAAC,CAAC,GAAGA,CAAC;MAChDC,MAAM,CAACC,QAAQ,GAAG,KAAK;MACvB,OAAOD,MAAM;IACf,CAAC,CAAC;IAEF,SAASE,sCAAsCA,CAACC,KAAa,EAAE;MAG7D,OACEA,KAAK,GAAGN,UAAU,CAACf,MAAM,GAAG,CAAC,IAC7Be,UAAU,CAACM,KAAK,CAAC,CAACjB,YAAY,EAC9B;QACAiB,KAAK,EAAE;MACT;MAEA,OAAOA,KAAK;IACd;IAEA,IAAMC,QAAQ,GAAI,SAAZA,QAAQA,CAAIH,QAAiB,EAAW;MAC5C,IAAIA,QAAQ,EAAE;QAGZ;MACF;MAEAJ,UAAU,CAACQ,OAAO,CAAE,UAAAb,SAAS,EAAK;QAChC,IAAI,OAAOA,SAAS,CAACY,QAAQ,KAAK,UAAU,IAAI,CAACZ,SAAS,CAACS,QAAQ,EAAE;UACnET,SAAS,CAACY,QAAQ,CAACH,QAAQ,CAAC;QAC9B;MACF,CAAC,CAAC;IACJ,CAAC;IAED,SAASK,QAAQA,CAACd,SAA4B,EAAEe,GAAc,EAAW;MACvE,IAAMC,WAAW,GAAGX,UAAU,CAACL,SAAS,CAACG,cAAc,CAAC;MACxD,IAAMM,QAAQ,GAAGO,WAAW,CAACd,OAAO,CAACc,WAAW,EAAED,GAAG,CAAC;MACtDf,SAAS,CAACC,OAAO,GAAGe,WAAW,CAACf,OAAO;MACvC,IAAIQ,QAAQ,EAAE;QAEZ,IAAIO,WAAW,CAACJ,QAAQ,EAAE;UACxBI,WAAW,CAACJ,QAAQ,CAAC,IAAmB,CAAC;QAC3C;QACAI,WAAW,CAACP,QAAQ,GAAG,IAAI;QAC3BT,SAAS,CAACG,cAAc,GAAGO,sCAAsC,CAC/DV,SAAS,CAACG,cAAc,GAAG,CAC7B,CAAC;QACD,IAAIH,SAAS,CAACG,cAAc,GAAGE,UAAU,CAACf,MAAM,EAAE;UAChD,IAAM2B,QAAQ,GAAGZ,UAAU,CAACL,SAAS,CAACG,cAAc,CAAC;UACrDc,QAAQ,CAAClB,OAAO,CAACkB,QAAQ,EAAED,WAAW,CAACf,OAAO,EAAEc,GAAG,EAAEC,WAAW,CAAC;UACjE,OAAO,KAAK;QACd;QACA,OAAO,IAAI;MACb;MACA,OAAO,KAAK;IACd;IAEA,SAASjB,OAAOA,CACdC,SAA4B,EAC5BlB,KAAsB,EACtBiC,GAAc,EACdG,iBAAoC,EAC9B;MAGNb,UAAU,CAACQ,OAAO,CAAE,UAAAM,IAAI,EAAK;QAC3B,IAAIA,IAAI,CAACzB,YAAY,KAAK0B,SAAS,EAAE;UACnCD,IAAI,CAACzB,YAAY,GAAGM,SAAS,CAACN,YAAY;QAC5C;MACF,CAAC,CAAC;MACFM,SAAS,CAACG,cAAc,GAAGO,sCAAsC,CAAC,CAAC,CAAC;MAEpE,IAAIQ,iBAAiB,KAAKE,SAAS,EAAE;QACnCF,iBAAiB,GAAGb,UAAU,CAC5BA,UAAU,CAACf,MAAM,GAAG,CAAC,CACD;MACxB;MAEA,IAAM+B,gBAAgB,GAAGhB,UAAU,CAACL,SAAS,CAACG,cAAc,CAAC;MAC7DkB,gBAAgB,CAACtB,OAAO,CACtBsB,gBAAgB,EAChBvC,KAAK,EACLiC,GAAG,EACHG,iBACF,CAAC;IACH;IAEA,OAAO;MACLI,aAAa,EAAE,IAAI;MACnBpB,OAAO,EAAEY,QAAQ;MACjBf,OAAO,EAAPA,OAAO;MACPI,cAAc,EAAE,CAAC;MACjBF,OAAO,EAAEI,UAAU,CAAC,CAAC,CAAC,CAACJ,OAAO;MAC9BW,QAAQ,EAARA,QAAQ;MACRlB,YAAY,EAAE,IAAAU,iCAA2B,EAACV,YAAY;IACxD,CAAC;EACH,CACF,CAAC;AACH", "ignoreList": []}