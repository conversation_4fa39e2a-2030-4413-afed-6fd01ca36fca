{"version": 3, "names": ["cov_26ipzzykgg", "actualCoverage", "CategoryModel", "s", "_createClass2", "default", "id", "order", "categoryCode", "categoryName", "description", "active", "children", "f", "_classCallCheck2", "exports"], "sources": ["/Users/<USER>/projects/react-native/payment-module/src/domain/entities/category-list/CategoryListModel.ts"], "sourcesContent": ["export type CategoryListModel = CategoryModel[];\n\nexport class CategoryModel {\n  constructor(\n    public id: string,\n    public order: string,\n    public categoryCode: string,\n    public categoryName: string,\n    public description: string,\n    public active: boolean,\n    public children: CategoryListModel,\n  ) {}\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAQW;IAAAA,cAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,cAAA;;;;;;;;;;;;;;;;;;IANEE,aAAa;AAAA;AAAA,CAAAF,cAAA,GAAAG,CAAA,WAAAC,aAAA,CAAAC,OAAA,EACxB,SAAAH,cACSI,EAAU,EACVC,KAAa,EACbC,YAAoB,EACpBC,YAAoB,EACpBC,WAAmB,EACnBC,MAAe,EACfC,QAA2B;EAAA;EAAAZ,cAAA,GAAAa,CAAA;EAAAb,cAAA,GAAAG,CAAA;EAAA,IAAAW,gBAAA,CAAAT,OAAA,QAAAH,aAAA;EAAA;EAAAF,cAAA,GAAAG,CAAA;EAN3B,KAAAG,EAAE,GAAFA,EAAE;EAAA;EAAAN,cAAA,GAAAG,CAAA;EACF,KAAAI,KAAK,GAALA,KAAK;EAAA;EAAAP,cAAA,GAAAG,CAAA;EACL,KAAAK,YAAY,GAAZA,YAAY;EAAA;EAAAR,cAAA,GAAAG,CAAA;EACZ,KAAAM,YAAY,GAAZA,YAAY;EAAA;EAAAT,cAAA,GAAAG,CAAA;EACZ,KAAAO,WAAW,GAAXA,WAAW;EAAA;EAAAV,cAAA,GAAAG,CAAA;EACX,KAAAQ,MAAM,GAANA,MAAM;EAAA;EAAAX,cAAA,GAAAG,CAAA;EACN,KAAAS,QAAQ,GAARA,QAAQ;AACd,CAAC;AAAA;AAAAZ,cAAA,GAAAG,CAAA;AATNY,OAAA,CAAAb,aAAA,GAAAA,aAAA", "ignoreList": []}