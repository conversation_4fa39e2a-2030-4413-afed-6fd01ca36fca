{"version": 3, "names": [], "sources": ["/Users/<USER>/projects/react-native/payment-module/src/data/datasources/IArrangementDataSource.ts"], "sourcesContent": ["import {BaseResponse} from '../../core/BaseResponse';\nimport {SourceAccountListResponse} from '../models/source-account-list/SourceAccountListResponse';\nimport {SourceAccountListRequest} from '../models/source-account-list/SourceAccountListRequest';\n\nexport interface IArrangementDataSource {\n  sourceAccountList(request: SourceAccountListRequest): Promise<BaseResponse<SourceAccountListResponse>>;\n}\n"], "mappings": "", "ignoreList": []}