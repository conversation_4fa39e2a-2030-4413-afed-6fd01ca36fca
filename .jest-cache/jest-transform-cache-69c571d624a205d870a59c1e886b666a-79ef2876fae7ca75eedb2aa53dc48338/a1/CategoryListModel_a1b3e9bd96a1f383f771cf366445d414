c347b8459106e60f57fafa54dffa223e
"use strict";

/* istanbul ignore next */
function cov_26ipzzykgg() {
  var path = "/Users/<USER>/projects/react-native/payment-module/src/domain/entities/category-list/CategoryListModel.ts";
  var hash = "d26286e0dd8859443fc17da93345c9199e12df58";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/projects/react-native/payment-module/src/domain/entities/category-list/CategoryListModel.ts",
    statementMap: {
      "0": {
        start: {
          line: 3,
          column: 29
        },
        end: {
          line: 3,
          column: 84
        }
      },
      "1": {
        start: {
          line: 4,
          column: 20
        },
        end: {
          line: 4,
          column: 89
        }
      },
      "2": {
        start: {
          line: 5,
          column: 23
        },
        end: {
          line: 5,
          column: 95
        }
      },
      "3": {
        start: {
          line: 6,
          column: 0
        },
        end: {
          line: 8,
          column: 3
        }
      },
      "4": {
        start: {
          line: 9,
          column: 0
        },
        end: {
          line: 9,
          column: 31
        }
      },
      "5": {
        start: {
          line: 10,
          column: 20
        },
        end: {
          line: 19,
          column: 2
        }
      },
      "6": {
        start: {
          line: 11,
          column: 2
        },
        end: {
          line: 11,
          column: 53
        }
      },
      "7": {
        start: {
          line: 12,
          column: 2
        },
        end: {
          line: 12,
          column: 15
        }
      },
      "8": {
        start: {
          line: 13,
          column: 2
        },
        end: {
          line: 13,
          column: 21
        }
      },
      "9": {
        start: {
          line: 14,
          column: 2
        },
        end: {
          line: 14,
          column: 35
        }
      },
      "10": {
        start: {
          line: 15,
          column: 2
        },
        end: {
          line: 15,
          column: 35
        }
      },
      "11": {
        start: {
          line: 16,
          column: 2
        },
        end: {
          line: 16,
          column: 33
        }
      },
      "12": {
        start: {
          line: 17,
          column: 2
        },
        end: {
          line: 17,
          column: 23
        }
      },
      "13": {
        start: {
          line: 18,
          column: 2
        },
        end: {
          line: 18,
          column: 27
        }
      },
      "14": {
        start: {
          line: 20,
          column: 0
        },
        end: {
          line: 20,
          column: 38
        }
      }
    },
    fnMap: {
      "0": {
        name: "CategoryModel",
        decl: {
          start: {
            line: 10,
            column: 56
          },
          end: {
            line: 10,
            column: 69
          }
        },
        loc: {
          start: {
            line: 10,
            column: 140
          },
          end: {
            line: 19,
            column: 1
          }
        },
        line: 10
      }
    },
    branchMap: {},
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0
    },
    f: {
      "0": 0
    },
    b: {},
    inputSourceMap: {
      version: 3,
      file: undefined,
      names: ["CategoryModel", "_createClass2", "default", "id", "order", "categoryCode", "categoryName", "description", "active", "children", "_classCallCheck2", "exports"],
      sourceRoot: undefined,
      sources: ["/Users/<USER>/projects/react-native/payment-module/src/domain/entities/category-list/CategoryListModel.ts"],
      sourcesContent: ["export type CategoryListModel = CategoryModel[];\n\nexport class CategoryModel {\n  constructor(\n    public id: string,\n    public order: string,\n    public categoryCode: string,\n    public categoryName: string,\n    public description: string,\n    public active: boolean,\n    public children: CategoryListModel,\n  ) {}\n}\n"],
      mappings: ";;;;;;;;;IAEaA,aAAa,OAAAC,aAAA,CAAAC,OAAA,EACxB,SAAAF,cACSG,EAAU,EACVC,KAAa,EACbC,YAAoB,EACpBC,YAAoB,EACpBC,WAAmB,EACnBC,MAAe,EACfC,QAA2B;EAAA,IAAAC,gBAAA,CAAAR,OAAA,QAAAF,aAAA;EAN3B,KAAAG,EAAE,GAAFA,EAAE;EACF,KAAAC,KAAK,GAALA,KAAK;EACL,KAAAC,YAAY,GAAZA,YAAY;EACZ,KAAAC,YAAY,GAAZA,YAAY;EACZ,KAAAC,WAAW,GAAXA,WAAW;EACX,KAAAC,MAAM,GAANA,MAAM;EACN,KAAAC,QAAQ,GAARA,QAAQ;AACd,CAAC;AATNE,OAAA,CAAAX,aAAA,GAAAA,aAAA",
      ignoreList: []
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "d26286e0dd8859443fc17da93345c9199e12df58"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_26ipzzykgg = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_26ipzzykgg();
var _interopRequireDefault =
/* istanbul ignore next */
(cov_26ipzzykgg().s[0]++, require("@babel/runtime/helpers/interopRequireDefault"));
var _createClass2 =
/* istanbul ignore next */
(cov_26ipzzykgg().s[1]++, _interopRequireDefault(require("@babel/runtime/helpers/createClass")));
var _classCallCheck2 =
/* istanbul ignore next */
(cov_26ipzzykgg().s[2]++, _interopRequireDefault(require("@babel/runtime/helpers/classCallCheck")));
/* istanbul ignore next */
cov_26ipzzykgg().s[3]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_26ipzzykgg().s[4]++;
exports.CategoryModel = void 0;
var CategoryModel =
/* istanbul ignore next */
(cov_26ipzzykgg().s[5]++, (0, _createClass2.default)(function CategoryModel(id, order, categoryCode, categoryName, description, active, children) {
  /* istanbul ignore next */
  cov_26ipzzykgg().f[0]++;
  cov_26ipzzykgg().s[6]++;
  (0, _classCallCheck2.default)(this, CategoryModel);
  /* istanbul ignore next */
  cov_26ipzzykgg().s[7]++;
  this.id = id;
  /* istanbul ignore next */
  cov_26ipzzykgg().s[8]++;
  this.order = order;
  /* istanbul ignore next */
  cov_26ipzzykgg().s[9]++;
  this.categoryCode = categoryCode;
  /* istanbul ignore next */
  cov_26ipzzykgg().s[10]++;
  this.categoryName = categoryName;
  /* istanbul ignore next */
  cov_26ipzzykgg().s[11]++;
  this.description = description;
  /* istanbul ignore next */
  cov_26ipzzykgg().s[12]++;
  this.active = active;
  /* istanbul ignore next */
  cov_26ipzzykgg().s[13]++;
  this.children = children;
}));
/* istanbul ignore next */
cov_26ipzzykgg().s[14]++;
exports.CategoryModel = CategoryModel;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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