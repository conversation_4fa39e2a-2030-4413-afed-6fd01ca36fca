8300784a4b20d50fbc89de76a2f274d5
"use strict";

/* istanbul ignore next */
function cov_18wuy6hwp3() {
  var path = "/Users/<USER>/projects/react-native/payment-module/src/data/datasources/IArrangementDataSource.ts";
  var hash = "848fe71d6e2099aff296442327959e9a9723ebda";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/projects/react-native/payment-module/src/data/datasources/IArrangementDataSource.ts",
    statementMap: {
      "0": {
        start: {
          line: 3,
          column: 0
        },
        end: {
          line: 5,
          column: 3
        }
      }
    },
    fnMap: {},
    branchMap: {},
    s: {
      "0": 0
    },
    f: {},
    b: {},
    inputSourceMap: {
      version: 3,
      file: undefined,
      names: [],
      sourceRoot: undefined,
      sources: ["/Users/<USER>/projects/react-native/payment-module/src/data/datasources/IArrangementDataSource.ts"],
      sourcesContent: ["import {BaseResponse} from '../../core/BaseResponse';\nimport {SourceAccountListResponse} from '../models/source-account-list/SourceAccountListResponse';\nimport {SourceAccountListRequest} from '../models/source-account-list/SourceAccountListRequest';\n\nexport interface IArrangementDataSource {\n  sourceAccountList(request: SourceAccountListRequest): Promise<BaseResponse<SourceAccountListResponse>>;\n}\n"],
      mappings: "",
      ignoreList: []
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "848fe71d6e2099aff296442327959e9a9723ebda"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_18wuy6hwp3 = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_18wuy6hwp3();
cov_18wuy6hwp3().s[0]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6W10sInNvdXJjZXMiOlsiL1VzZXJzL3NvbnNhbXNldC9wcm9qZWN0cy9yZWFjdC1uYXRpdmUvcGF5bWVudC1tb2R1bGUvc3JjL2RhdGEvZGF0YXNvdXJjZXMvSUFycmFuZ2VtZW50RGF0YVNvdXJjZS50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQge0Jhc2VSZXNwb25zZX0gZnJvbSAnLi4vLi4vY29yZS9CYXNlUmVzcG9uc2UnO1xuaW1wb3J0IHtTb3VyY2VBY2NvdW50TGlzdFJlc3BvbnNlfSBmcm9tICcuLi9tb2RlbHMvc291cmNlLWFjY291bnQtbGlzdC9Tb3VyY2VBY2NvdW50TGlzdFJlc3BvbnNlJztcbmltcG9ydCB7U291cmNlQWNjb3VudExpc3RSZXF1ZXN0fSBmcm9tICcuLi9tb2RlbHMvc291cmNlLWFjY291bnQtbGlzdC9Tb3VyY2VBY2NvdW50TGlzdFJlcXVlc3QnO1xuXG5leHBvcnQgaW50ZXJmYWNlIElBcnJhbmdlbWVudERhdGFTb3VyY2Uge1xuICBzb3VyY2VBY2NvdW50TGlzdChyZXF1ZXN0OiBTb3VyY2VBY2NvdW50TGlzdFJlcXVlc3QpOiBQcm9taXNlPEJhc2VSZXNwb25zZTxTb3VyY2VBY2NvdW50TGlzdFJlc3BvbnNlPj47XG59XG4iXSwibWFwcGluZ3MiOiIiLCJpZ25vcmVMaXN0IjpbXX0=