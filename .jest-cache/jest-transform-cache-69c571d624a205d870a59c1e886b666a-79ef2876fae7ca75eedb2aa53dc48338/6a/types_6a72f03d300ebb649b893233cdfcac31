0fb282b13ec1cedd6f65399a33ff2a2f
"use strict";

/* istanbul ignore next */
function cov_16s5i629ef() {
  var path = "/Users/<USER>/projects/react-native/payment-module/src/components/bill-item-info/types.ts";
  var hash = "370a7281ae0ba50cbb0f3decf163ab045e1b46ad";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/projects/react-native/payment-module/src/components/bill-item-info/types.ts",
    statementMap: {
      "0": {
        start: {
          line: 3,
          column: 0
        },
        end: {
          line: 5,
          column: 3
        }
      }
    },
    fnMap: {},
    branchMap: {},
    s: {
      "0": 0
    },
    f: {},
    b: {},
    inputSourceMap: {
      version: 3,
      file: undefined,
      names: [],
      sourceRoot: undefined,
      sources: ["/Users/<USER>/projects/react-native/payment-module/src/components/bill-item-info/types.ts"],
      sourcesContent: ["import {ViewStyle} from 'react-native';\n\nexport type BillItemProps = {\n  style?: ViewStyle;\n  title?: string;\n  value?: string;\n};\n"],
      mappings: "",
      ignoreList: []
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "370a7281ae0ba50cbb0f3decf163ab045e1b46ad"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_16s5i629ef = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_16s5i629ef();
cov_16s5i629ef().s[0]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6W10sInNvdXJjZXMiOlsiL1VzZXJzL3NvbnNhbXNldC9wcm9qZWN0cy9yZWFjdC1uYXRpdmUvcGF5bWVudC1tb2R1bGUvc3JjL2NvbXBvbmVudHMvYmlsbC1pdGVtLWluZm8vdHlwZXMudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHtWaWV3U3R5bGV9IGZyb20gJ3JlYWN0LW5hdGl2ZSc7XG5cbmV4cG9ydCB0eXBlIEJpbGxJdGVtUHJvcHMgPSB7XG4gIHN0eWxlPzogVmlld1N0eWxlO1xuICB0aXRsZT86IHN0cmluZztcbiAgdmFsdWU/OiBzdHJpbmc7XG59O1xuIl0sIm1hcHBpbmdzIjoiIiwiaWdub3JlTGlzdCI6W119