{"version": 3, "names": ["Object", "defineProperty", "exports", "value", "withTiming", "_Easing", "require", "_util", "toValue", "userConfig", "callback", "__DEV__", "easing", "assertEasingIsWorklet", "defineAnimation", "config", "duration", "Easing", "inOut", "quad", "keys", "for<PERSON>ach", "key", "timing", "animation", "now", "startTime", "startValue", "runtime", "current", "progress", "onStart", "previousAnimation", "type", "factory", "onFrame", "reduceMotion", "getReduceMotionForAnimation"], "sources": ["../../../src/animation/timing.ts"], "sourcesContent": [null], "mappings": "AAAA,YAAY;;AAAAA,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,KAAA;AAAA;AAAAD,OAAA,CAAAE,UAAA;AAEZ,IAAAC,OAAA,GAAAC,OAAA;AACA,IAAAC,KAAA,GAAAD,OAAA;AAqEO,IAAMF,UAAU,GAAAF,OAAA,CAAAE,UAAA,GAAG,SAAbA,UAAUA,CACrBI,OAAwB,EACxBC,UAAyB,EACzBC,QAA4B,EACA;EAC5B,SAAS;;EAET,IAAIC,OAAO,IAAIF,UAAU,YAAVA,UAAU,CAAEG,MAAM,EAAE;IACjC,IAAAC,2BAAqB,EAACJ,UAAU,CAACG,MAAM,CAAC;EAC1C;EAEA,OAAO,IAAAE,qBAAe,EAAkBN,OAAO,EAAE,YAAM;IACrD,SAAS;;IACT,IAAMO,MAAoD,GAAG;MAC3DC,QAAQ,EAAE,GAAG;MACbJ,MAAM,EAAEK,cAAM,CAACC,KAAK,CAACD,cAAM,CAACE,IAAI;IAClC,CAAC;IACD,IAAIV,UAAU,EAAE;MACdT,MAAM,CAACoB,IAAI,CAACX,UAAU,CAAC,CAACY,OAAO,CAC5B,UAAAC,GAAG;QAAA,OACAP,MAAM,CAASO,GAAG,CAAC,GAAGb,UAAU,CAACa,GAAG,CAC1C;MAAA,EAAC;IACH;IAEA,SAASC,MAAMA,CAACC,SAA+B,EAAEC,GAAc,EAAW;MAExE,IAAQjB,OAAO,GAA4BgB,SAAS,CAA5ChB,OAAO;QAAEkB,SAAS,GAAiBF,SAAS,CAAnCE,SAAS;QAAEC,UAAA,GAAeH,SAAS,CAAxBG,UAAA;MAC5B,IAAMC,OAAO,GAAGH,GAAG,GAAGC,SAAS;MAE/B,IAAIE,OAAO,IAAIb,MAAM,CAACC,QAAQ,EAAE;QAE9BQ,SAAS,CAACE,SAAS,GAAG,CAAC;QACvBF,SAAS,CAACK,OAAO,GAAGrB,OAAO;QAC3B,OAAO,IAAI;MACb;MACA,IAAMsB,QAAQ,GAAGN,SAAS,CAACZ,MAAM,CAACgB,OAAO,GAAGb,MAAM,CAACC,QAAQ,CAAC;MAC5DQ,SAAS,CAACK,OAAO,GACdF,UAAU,GAAc,CAACnB,OAAO,GAAImB,UAAqB,IAAIG,QAAQ;MACxE,OAAO,KAAK;IACd;IAEA,SAASC,OAAOA,CACdP,SAA0B,EAC1BrB,KAAa,EACbsB,GAAc,EACdO,iBAA6C,EACvC;MACN,IACEA,iBAAiB,IAChBA,iBAAiB,CAAqBC,IAAI,KAAK,QAAQ,IACvDD,iBAAiB,CAAqBxB,OAAO,KAAKA,OAAO,IACzDwB,iBAAiB,CAAqBN,SAAS,EAChD;QAIAF,SAAS,CAACE,SAAS,GAAIM,iBAAiB,CAAqBN,SAAS;QACtEF,SAAS,CAACG,UAAU,GAClBK,iBAAiB,CACjBL,UAAU;MACd,CAAC,MAAM;QACLH,SAAS,CAACE,SAAS,GAAGD,GAAG;QACzBD,SAAS,CAACG,UAAU,GAAGxB,KAAK;MAC9B;MACAqB,SAAS,CAACK,OAAO,GAAG1B,KAAK;MACzB,IAAI,OAAOY,MAAM,CAACH,MAAM,KAAK,QAAQ,EAAE;QACrCY,SAAS,CAACZ,MAAM,GAAGG,MAAM,CAACH,MAAM,CAACsB,OAAO,CAAC,CAAC;MAC5C,CAAC,MAAM;QACLV,SAAS,CAACZ,MAAM,GAAGG,MAAM,CAACH,MAAM;MAClC;IACF;IAEA,OAAO;MACLqB,IAAI,EAAE,QAAQ;MACdE,OAAO,EAAEZ,MAAM;MACfQ,OAAO,EAAEA,OAA+D;MACxED,QAAQ,EAAE,CAAC;MACXtB,OAAO,EAAPA,OAAO;MACPmB,UAAU,EAAE,CAAC;MACbD,SAAS,EAAE,CAAC;MACZd,MAAM,EAAE,SAARA,MAAMA,CAAA;QAAA,OAAQ,CAAC;MAAA;MACfiB,OAAO,EAAErB,OAAO;MAChBE,QAAQ,EAARA,QAAQ;MACR0B,YAAY,EAAE,IAAAC,iCAA2B,EAAC5B,UAAU,oBAAVA,UAAU,CAAE2B,YAAY;IACpE,CAAC;EACH,CAAC,CAAC;AACJ,CAAmB", "ignoreList": []}