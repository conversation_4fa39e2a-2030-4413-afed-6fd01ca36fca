a0df41520869c0bc54dd67dd9a4891e5
'use strict';

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.withTiming = void 0;
var _Easing = require("../Easing.js");
var _util = require("./util.js");
var withTiming = exports.withTiming = function withTiming(toValue, userConfig, callback) {
  'worklet';

  if (__DEV__ && userConfig != null && userConfig.easing) {
    (0, _util.assertEasingIsWorklet)(userConfig.easing);
  }
  return (0, _util.defineAnimation)(toValue, function () {
    'worklet';

    var config = {
      duration: 300,
      easing: _Easing.Easing.inOut(_Easing.Easing.quad)
    };
    if (userConfig) {
      Object.keys(userConfig).forEach(function (key) {
        return config[key] = userConfig[key];
      });
    }
    function timing(animation, now) {
      var toValue = animation.toValue,
        startTime = animation.startTime,
        startValue = animation.startValue;
      var runtime = now - startTime;
      if (runtime >= config.duration) {
        animation.startTime = 0;
        animation.current = toValue;
        return true;
      }
      var progress = animation.easing(runtime / config.duration);
      animation.current = startValue + (toValue - startValue) * progress;
      return false;
    }
    function onStart(animation, value, now, previousAnimation) {
      if (previousAnimation && previousAnimation.type === 'timing' && previousAnimation.toValue === toValue && previousAnimation.startTime) {
        animation.startTime = previousAnimation.startTime;
        animation.startValue = previousAnimation.startValue;
      } else {
        animation.startTime = now;
        animation.startValue = value;
      }
      animation.current = value;
      if (typeof config.easing === 'object') {
        animation.easing = config.easing.factory();
      } else {
        animation.easing = config.easing;
      }
    }
    return {
      type: 'timing',
      onFrame: timing,
      onStart: onStart,
      progress: 0,
      toValue: toValue,
      startValue: 0,
      startTime: 0,
      easing: function easing() {
        return 0;
      },
      current: toValue,
      callback: callback,
      reduceMotion: (0, _util.getReduceMotionForAnimation)(userConfig == null ? void 0 : userConfig.reduceMotion)
    };
  });
};
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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