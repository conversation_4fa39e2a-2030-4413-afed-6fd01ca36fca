256b416a045a6b7bcb8a6845ea6301b9
'use strict';

var dummyPoint = {
  x: undefined,
  y: undefined
};
var pointsDiffer = function pointsDiffer(one, two) {
  one = one || dummyPoint;
  two = two || dummyPoint;
  return one !== two && (one.x !== two.x || one.y !== two.y);
};
module.exports = pointsDiffer;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJkdW1teVBvaW50IiwieCIsInVuZGVmaW5lZCIsInkiLCJwb2ludHNEaWZmZXIiLCJvbmUiLCJ0d28iLCJtb2R1bGUiLCJleHBvcnRzIl0sInNvdXJjZXMiOlsicG9pbnRzRGlmZmVyLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQ29weXJpZ2h0IChjKSBNZXRhIFBsYXRmb3JtcywgSW5jLiBhbmQgYWZmaWxpYXRlcy5cbiAqXG4gKiBUaGlzIHNvdXJjZSBjb2RlIGlzIGxpY2Vuc2VkIHVuZGVyIHRoZSBNSVQgbGljZW5zZSBmb3VuZCBpbiB0aGVcbiAqIExJQ0VOU0UgZmlsZSBpbiB0aGUgcm9vdCBkaXJlY3Rvcnkgb2YgdGhpcyBzb3VyY2UgdHJlZS5cbiAqXG4gKiBAZm9ybWF0XG4gKiBAZmxvd1xuICovXG5cbid1c2Ugc3RyaWN0JztcblxudHlwZSBQb2ludCA9IHtcbiAgeDogP251bWJlcixcbiAgeTogP251bWJlcixcbiAgLi4uXG59O1xuXG5jb25zdCBkdW1teVBvaW50ID0ge3g6IHVuZGVmaW5lZCwgeTogdW5kZWZpbmVkfTtcblxuY29uc3QgcG9pbnRzRGlmZmVyID0gZnVuY3Rpb24gKG9uZTogP1BvaW50LCB0d286ID9Qb2ludCk6IGJvb2xlYW4ge1xuICBvbmUgPSBvbmUgfHwgZHVtbXlQb2ludDtcbiAgdHdvID0gdHdvIHx8IGR1bW15UG9pbnQ7XG4gIHJldHVybiBvbmUgIT09IHR3byAmJiAob25lLnggIT09IHR3by54IHx8IG9uZS55ICE9PSB0d28ueSk7XG59O1xuXG5tb2R1bGUuZXhwb3J0cyA9IHBvaW50c0RpZmZlcjtcbiJdLCJtYXBwaW5ncyI6IkFBVUEsWUFBWTs7QUFRWixJQUFNQSxVQUFVLEdBQUc7RUFBQ0MsQ0FBQyxFQUFFQyxTQUFTO0VBQUVDLENBQUMsRUFBRUQ7QUFBUyxDQUFDO0FBRS9DLElBQU1FLFlBQVksR0FBRyxTQUFmQSxZQUFZQSxDQUFhQyxHQUFXLEVBQUVDLEdBQVcsRUFBVztFQUNoRUQsR0FBRyxHQUFHQSxHQUFHLElBQUlMLFVBQVU7RUFDdkJNLEdBQUcsR0FBR0EsR0FBRyxJQUFJTixVQUFVO0VBQ3ZCLE9BQU9LLEdBQUcsS0FBS0MsR0FBRyxLQUFLRCxHQUFHLENBQUNKLENBQUMsS0FBS0ssR0FBRyxDQUFDTCxDQUFDLElBQUlJLEdBQUcsQ0FBQ0YsQ0FBQyxLQUFLRyxHQUFHLENBQUNILENBQUMsQ0FBQztBQUM1RCxDQUFDO0FBRURJLE1BQU0sQ0FBQ0MsT0FBTyxHQUFHSixZQUFZIiwiaWdub3JlTGlzdCI6W119