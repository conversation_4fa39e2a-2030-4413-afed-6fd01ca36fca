{"version": 3, "names": [], "sources": ["/Users/<USER>/projects/react-native/payment-module/src/data/models/get-bill-detail/GetBillDetailResponse.ts"], "sourcesContent": ["export interface GetBillDetailResponse {\n  billCode?: string;\n  service?: BillDetailServiceResponse;\n  queryRef?: string;\n  customerInfo?: BillDetailCustomerInfoResponse;\n  billList?: BillDetailBillResponse[];\n  partnerRespCode?: string;\n  tranSeqCount?: number;\n  partnerRespDesc?: string;\n  partnerTraceSeq?: string;\n  result?: string;\n  extendData?: any;\n  paymentRule?: number;\n}\n\nexport interface BillDetailServiceResponse {\n  code?: string;\n}\n\nexport interface BillDetailCustomerInfoResponse {\n  cif?: string;\n  phone?: any;\n  acct?: any;\n  name?: string;\n  address?: any;\n}\n\nexport interface BillDetailBillResponse {\n  id?: string;\n  no?: any;\n  amount?: number;\n  code?: string;\n  custCode?: any;\n  custName?: string;\n  period?: any;\n  fee?: any;\n  custAddress?: any;\n}\n"], "mappings": "", "ignoreList": []}