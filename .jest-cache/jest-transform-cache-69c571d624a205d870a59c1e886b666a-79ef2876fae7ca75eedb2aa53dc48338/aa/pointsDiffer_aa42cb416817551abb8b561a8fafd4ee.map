{"version": 3, "names": ["dummyPoint", "x", "undefined", "y", "points<PERSON><PERSON><PERSON>", "one", "two", "module", "exports"], "sources": ["pointsDiffer.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @format\n * @flow\n */\n\n'use strict';\n\ntype Point = {\n  x: ?number,\n  y: ?number,\n  ...\n};\n\nconst dummyPoint = {x: undefined, y: undefined};\n\nconst pointsDiffer = function (one: ?Point, two: ?Point): boolean {\n  one = one || dummyPoint;\n  two = two || dummyPoint;\n  return one !== two && (one.x !== two.x || one.y !== two.y);\n};\n\nmodule.exports = pointsDiffer;\n"], "mappings": "AAUA,YAAY;;AAQZ,IAAMA,UAAU,GAAG;EAACC,CAAC,EAAEC,SAAS;EAAEC,CAAC,EAAED;AAAS,CAAC;AAE/C,IAAME,YAAY,GAAG,SAAfA,YAAYA,CAAaC,GAAW,EAAEC,GAAW,EAAW;EAChED,GAAG,GAAGA,GAAG,IAAIL,UAAU;EACvBM,GAAG,GAAGA,GAAG,IAAIN,UAAU;EACvB,OAAOK,GAAG,KAAKC,GAAG,KAAKD,GAAG,CAACJ,CAAC,KAAKK,GAAG,CAACL,CAAC,IAAII,GAAG,CAACF,CAAC,KAAKG,GAAG,CAACH,CAAC,CAAC;AAC5D,CAAC;AAEDI,MAAM,CAACC,OAAO,GAAGJ,YAAY", "ignoreList": []}