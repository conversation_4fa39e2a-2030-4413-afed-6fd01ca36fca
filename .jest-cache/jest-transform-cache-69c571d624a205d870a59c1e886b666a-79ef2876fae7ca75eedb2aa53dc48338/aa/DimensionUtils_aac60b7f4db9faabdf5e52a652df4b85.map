{"version": 3, "names": ["cov_13loy85f5w", "actualCoverage", "s", "react_native_1", "require", "_react_native_1$Dimen", "Dimensions", "get", "height", "width", "iPhoneXModels", "models", "isIPhoneX", "f", "b", "Platform", "OS", "isPad", "isTV", "some", "model", "paddingTop", "select", "ios", "android", "StatusBar", "currentHeight", "default", "paddingBottom", "getPaddingTopByDevice", "getPaddingBottomByDevice", "getWindowWidth", "getWindowHeight", "exports"], "sources": ["/Users/<USER>/projects/react-native/payment-module/src/utils/DimensionUtils.ts"], "sourcesContent": ["// import {useSafeAreaInsets} from 'react-native-safe-area-context';\nimport {Dimensions, Platform, StatusBar} from 'react-native';\n\nconst {height, width} = Dimensions.get('screen'); // Use 'screen' for better accuracy\n\nconst iPhoneXModels = [\n  {width: 320, height: 480, models: ['iPhone 1', 'iPhone 3G', 'iPhone 3GS']},\n  {width: 320, height: 568, models: ['iPhone 5', 'iPhone 5S', 'iPhone 5C', 'iPhone SE (2016)']},\n  {\n    width: 375,\n    height: 667,\n    models: ['iPhone 6', 'iPhone 6S', 'iPhone 7', 'iPhone 8', 'iPhone SE (2020)', 'iPhone SE (2022)'],\n  },\n  {width: 414, height: 736, models: ['iPhone 6 Plus', 'iPhone 6S Plus', 'iPhone 7 Plus', 'iPhone 8 Plus']},\n  {width: 375, height: 812, models: ['iPhone X', 'iPhone XS', 'iPhone 11 Pro']},\n  {width: 414, height: 896, models: ['iPhone XR', 'iPhone XS Max', 'iPhone 11', 'iPhone 11 Pro Max']},\n  {width: 390, height: 844, models: ['iPhone 12', 'iPhone 12 Pro', 'iPhone 13', 'iPhone 13 Pro']},\n  {width: 428, height: 926, models: ['iPhone 12 Pro Max', 'iPhone 13 Pro Max']},\n  {width: 430, height: 932, models: ['iPhone 14 Pro Max', 'iPhone 15 Pro Max']},\n  {width: 393, height: 852, models: ['iPhone 14 Pro', 'iPhone 15 Pro']},\n  {width: 390, height: 844, models: ['iPhone 14', 'iPhone 15']},\n  {width: 430, height: 932, models: ['iPhone 16 Pro Max']},\n  {width: 402, height: 874, models: ['iPhone 16', 'iPhone 16 Pro']},\n  {width: 430, height: 960, models: ['iPhone 16 Plus']},\n];\n\nconst isIPhoneX = () =>\n  Platform.OS === 'ios' &&\n  !Platform.isPad &&\n  !Platform.isTV &&\n  iPhoneXModels.some(model => model.width === width && model.height === height);\n\nconst paddingTop = Platform.select({\n  ios: isIPhoneX() ? 44 : 20,\n  android: StatusBar.currentHeight || 24,\n  default: 0,\n});\n\nconst paddingBottom = Platform.select({\n  ios: isIPhoneX() ? 34 : 20,\n  android: 20,\n  default: 0,\n});\n\nconst getPaddingTopByDevice = () => {\n  // return useSafeAreaInsets().top;\n  return paddingTop;\n};\n\nconst getPaddingBottomByDevice = () => {\n  // return useSafeAreaInsets().bottom;\n  return paddingBottom;\n};\nconst getWindowWidth = () => {\n  return Dimensions.get('window').width;\n};\n\nconst getWindowHeight = () => {\n  return Dimensions.get('window').height;\n};\n\nexport default {\n  getPaddingTopByDevice,\n  getPaddingBottomByDevice,\n  getWindowHeight,\n  getWindowWidth,\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAOuB;IAAAA,cAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,cAAA;AAAAA,cAAA,GAAAE,CAAA;;;;AANvB,IAAAC,cAAA;AAAA;AAAA,CAAAH,cAAA,GAAAE,CAAA,OAAAE,OAAA;AAEA,IAAAC,qBAAA;EAAA;EAAA,CAAAL,cAAA,GAAAE,CAAA,OAAwBC,cAAA,CAAAG,UAAU,CAACC,GAAG,CAAC,QAAQ,CAAC;EAAzCC,MAAM;EAAA;EAAA,CAAAR,cAAA,GAAAE,CAAA,OAAAG,qBAAA,CAANG,MAAM;EAAEC,KAAK;EAAA;EAAA,CAAAT,cAAA,GAAAE,CAAA,OAAAG,qBAAA,CAALI,KAAK;AAEpB,IAAMC,aAAa;AAAA;AAAA,CAAAV,cAAA,GAAAE,CAAA,OAAG,CACpB;EAACO,KAAK,EAAE,GAAG;EAAED,MAAM,EAAE,GAAG;EAAEG,MAAM,EAAE,CAAC,UAAU,EAAE,WAAW,EAAE,YAAY;AAAC,CAAC,EAC1E;EAACF,KAAK,EAAE,GAAG;EAAED,MAAM,EAAE,GAAG;EAAEG,MAAM,EAAE,CAAC,UAAU,EAAE,WAAW,EAAE,WAAW,EAAE,kBAAkB;AAAC,CAAC,EAC7F;EACEF,KAAK,EAAE,GAAG;EACVD,MAAM,EAAE,GAAG;EACXG,MAAM,EAAE,CAAC,UAAU,EAAE,WAAW,EAAE,UAAU,EAAE,UAAU,EAAE,kBAAkB,EAAE,kBAAkB;CACjG,EACD;EAACF,KAAK,EAAE,GAAG;EAAED,MAAM,EAAE,GAAG;EAAEG,MAAM,EAAE,CAAC,eAAe,EAAE,gBAAgB,EAAE,eAAe,EAAE,eAAe;AAAC,CAAC,EACxG;EAACF,KAAK,EAAE,GAAG;EAAED,MAAM,EAAE,GAAG;EAAEG,MAAM,EAAE,CAAC,UAAU,EAAE,WAAW,EAAE,eAAe;AAAC,CAAC,EAC7E;EAACF,KAAK,EAAE,GAAG;EAAED,MAAM,EAAE,GAAG;EAAEG,MAAM,EAAE,CAAC,WAAW,EAAE,eAAe,EAAE,WAAW,EAAE,mBAAmB;AAAC,CAAC,EACnG;EAACF,KAAK,EAAE,GAAG;EAAED,MAAM,EAAE,GAAG;EAAEG,MAAM,EAAE,CAAC,WAAW,EAAE,eAAe,EAAE,WAAW,EAAE,eAAe;AAAC,CAAC,EAC/F;EAACF,KAAK,EAAE,GAAG;EAAED,MAAM,EAAE,GAAG;EAAEG,MAAM,EAAE,CAAC,mBAAmB,EAAE,mBAAmB;AAAC,CAAC,EAC7E;EAACF,KAAK,EAAE,GAAG;EAAED,MAAM,EAAE,GAAG;EAAEG,MAAM,EAAE,CAAC,mBAAmB,EAAE,mBAAmB;AAAC,CAAC,EAC7E;EAACF,KAAK,EAAE,GAAG;EAAED,MAAM,EAAE,GAAG;EAAEG,MAAM,EAAE,CAAC,eAAe,EAAE,eAAe;AAAC,CAAC,EACrE;EAACF,KAAK,EAAE,GAAG;EAAED,MAAM,EAAE,GAAG;EAAEG,MAAM,EAAE,CAAC,WAAW,EAAE,WAAW;AAAC,CAAC,EAC7D;EAACF,KAAK,EAAE,GAAG;EAAED,MAAM,EAAE,GAAG;EAAEG,MAAM,EAAE,CAAC,mBAAmB;AAAC,CAAC,EACxD;EAACF,KAAK,EAAE,GAAG;EAAED,MAAM,EAAE,GAAG;EAAEG,MAAM,EAAE,CAAC,WAAW,EAAE,eAAe;AAAC,CAAC,EACjE;EAACF,KAAK,EAAE,GAAG;EAAED,MAAM,EAAE,GAAG;EAAEG,MAAM,EAAE,CAAC,gBAAgB;AAAC,CAAC,CACtD;AAAA;AAAAX,cAAA,GAAAE,CAAA;AAED,IAAMU,SAAS,GAAG,SAAZA,SAASA,CAAA;EAAA;EAAAZ,cAAA,GAAAa,CAAA;EAAAb,cAAA,GAAAE,CAAA;EAAA,OACb,2BAAAF,cAAA,GAAAc,CAAA,UAAAX,cAAA,CAAAY,QAAQ,CAACC,EAAE,KAAK,KAAK;EAAA;EAAA,CAAAhB,cAAA,GAAAc,CAAA,UACrB,CAACX,cAAA,CAAAY,QAAQ,CAACE,KAAK;EAAA;EAAA,CAAAjB,cAAA,GAAAc,CAAA,UACf,CAACX,cAAA,CAAAY,QAAQ,CAACG,IAAI;EAAA;EAAA,CAAAlB,cAAA,GAAAc,CAAA,UACdJ,aAAa,CAACS,IAAI,CAAC,UAAAC,KAAK;IAAA;IAAApB,cAAA,GAAAa,CAAA;IAAAb,cAAA,GAAAE,CAAA;IAAA,OAAI,2BAAAF,cAAA,GAAAc,CAAA,UAAAM,KAAK,CAACX,KAAK,KAAKA,KAAK;IAAA;IAAA,CAAAT,cAAA,GAAAc,CAAA,UAAIM,KAAK,CAACZ,MAAM,KAAKA,MAAM;EAAA,EAAC;AAAA;AAE/E,IAAMa,UAAU;AAAA;AAAA,CAAArB,cAAA,GAAAE,CAAA,OAAGC,cAAA,CAAAY,QAAQ,CAACO,MAAM,CAAC;EACjCC,GAAG,EAAEX,SAAS,EAAE;EAAA;EAAA,CAAAZ,cAAA,GAAAc,CAAA,UAAG,EAAE;EAAA;EAAA,CAAAd,cAAA,GAAAc,CAAA,UAAG,EAAE;EAC1BU,OAAO;EAAE;EAAA,CAAAxB,cAAA,GAAAc,CAAA,UAAAX,cAAA,CAAAsB,SAAS,CAACC,aAAa;EAAA;EAAA,CAAA1B,cAAA,GAAAc,CAAA,UAAI,EAAE;EACtCa,OAAO,EAAE;CACV,CAAC;AAEF,IAAMC,aAAa;AAAA;AAAA,CAAA5B,cAAA,GAAAE,CAAA,QAAGC,cAAA,CAAAY,QAAQ,CAACO,MAAM,CAAC;EACpCC,GAAG,EAAEX,SAAS,EAAE;EAAA;EAAA,CAAAZ,cAAA,GAAAc,CAAA,UAAG,EAAE;EAAA;EAAA,CAAAd,cAAA,GAAAc,CAAA,UAAG,EAAE;EAC1BU,OAAO,EAAE,EAAE;EACXG,OAAO,EAAE;CACV,CAAC;AAAA;AAAA3B,cAAA,GAAAE,CAAA;AAEF,IAAM2B,qBAAqB,GAAG,SAAxBA,qBAAqBA,CAAA,EAAQ;EAAA;EAAA7B,cAAA,GAAAa,CAAA;EAAAb,cAAA,GAAAE,CAAA;EAEjC,OAAOmB,UAAU;AACnB,CAAC;AAAA;AAAArB,cAAA,GAAAE,CAAA;AAED,IAAM4B,wBAAwB,GAAG,SAA3BA,wBAAwBA,CAAA,EAAQ;EAAA;EAAA9B,cAAA,GAAAa,CAAA;EAAAb,cAAA,GAAAE,CAAA;EAEpC,OAAO0B,aAAa;AACtB,CAAC;AAAA;AAAA5B,cAAA,GAAAE,CAAA;AACD,IAAM6B,cAAc,GAAG,SAAjBA,cAAcA,CAAA,EAAQ;EAAA;EAAA/B,cAAA,GAAAa,CAAA;EAAAb,cAAA,GAAAE,CAAA;EAC1B,OAAOC,cAAA,CAAAG,UAAU,CAACC,GAAG,CAAC,QAAQ,CAAC,CAACE,KAAK;AACvC,CAAC;AAAA;AAAAT,cAAA,GAAAE,CAAA;AAED,IAAM8B,eAAe,GAAG,SAAlBA,eAAeA,CAAA,EAAQ;EAAA;EAAAhC,cAAA,GAAAa,CAAA;EAAAb,cAAA,GAAAE,CAAA;EAC3B,OAAOC,cAAA,CAAAG,UAAU,CAACC,GAAG,CAAC,QAAQ,CAAC,CAACC,MAAM;AACxC,CAAC;AAAA;AAAAR,cAAA,GAAAE,CAAA;AAED+B,OAAA,CAAAN,OAAA,GAAe;EACbE,qBAAqB,EAArBA,qBAAqB;EACrBC,wBAAwB,EAAxBA,wBAAwB;EACxBE,eAAe,EAAfA,eAAe;EACfD,cAAc,EAAdA;CACD", "ignoreList": []}