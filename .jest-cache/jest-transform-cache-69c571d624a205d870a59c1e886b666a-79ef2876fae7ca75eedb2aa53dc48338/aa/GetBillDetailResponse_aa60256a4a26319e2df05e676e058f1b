14c37ba2935a5f01a5f4d87733167353
"use strict";

/* istanbul ignore next */
function cov_1muhwxwxzt() {
  var path = "/Users/<USER>/projects/react-native/payment-module/src/data/models/get-bill-detail/GetBillDetailResponse.ts";
  var hash = "d7a3dd02bb9e5ecf2787888ef8448a5009d26472";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/projects/react-native/payment-module/src/data/models/get-bill-detail/GetBillDetailResponse.ts",
    statementMap: {
      "0": {
        start: {
          line: 3,
          column: 0
        },
        end: {
          line: 5,
          column: 3
        }
      }
    },
    fnMap: {},
    branchMap: {},
    s: {
      "0": 0
    },
    f: {},
    b: {},
    inputSourceMap: {
      version: 3,
      file: undefined,
      names: [],
      sourceRoot: undefined,
      sources: ["/Users/<USER>/projects/react-native/payment-module/src/data/models/get-bill-detail/GetBillDetailResponse.ts"],
      sourcesContent: ["export interface GetBillDetailResponse {\n  billCode?: string;\n  service?: BillDetailServiceResponse;\n  queryRef?: string;\n  customerInfo?: BillDetailCustomerInfoResponse;\n  billList?: BillDetailBillResponse[];\n  partnerRespCode?: string;\n  tranSeqCount?: number;\n  partnerRespDesc?: string;\n  partnerTraceSeq?: string;\n  result?: string;\n  extendData?: any;\n  paymentRule?: number;\n}\n\nexport interface BillDetailServiceResponse {\n  code?: string;\n}\n\nexport interface BillDetailCustomerInfoResponse {\n  cif?: string;\n  phone?: any;\n  acct?: any;\n  name?: string;\n  address?: any;\n}\n\nexport interface BillDetailBillResponse {\n  id?: string;\n  no?: any;\n  amount?: number;\n  code?: string;\n  custCode?: any;\n  custName?: string;\n  period?: any;\n  fee?: any;\n  custAddress?: any;\n}\n"],
      mappings: "",
      ignoreList: []
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "d7a3dd02bb9e5ecf2787888ef8448a5009d26472"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_1muhwxwxzt = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_1muhwxwxzt();
cov_1muhwxwxzt().s[0]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6W10sInNvdXJjZXMiOlsiL1VzZXJzL3NvbnNhbXNldC9wcm9qZWN0cy9yZWFjdC1uYXRpdmUvcGF5bWVudC1tb2R1bGUvc3JjL2RhdGEvbW9kZWxzL2dldC1iaWxsLWRldGFpbC9HZXRCaWxsRGV0YWlsUmVzcG9uc2UudHMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGludGVyZmFjZSBHZXRCaWxsRGV0YWlsUmVzcG9uc2Uge1xuICBiaWxsQ29kZT86IHN0cmluZztcbiAgc2VydmljZT86IEJpbGxEZXRhaWxTZXJ2aWNlUmVzcG9uc2U7XG4gIHF1ZXJ5UmVmPzogc3RyaW5nO1xuICBjdXN0b21lckluZm8/OiBCaWxsRGV0YWlsQ3VzdG9tZXJJbmZvUmVzcG9uc2U7XG4gIGJpbGxMaXN0PzogQmlsbERldGFpbEJpbGxSZXNwb25zZVtdO1xuICBwYXJ0bmVyUmVzcENvZGU/OiBzdHJpbmc7XG4gIHRyYW5TZXFDb3VudD86IG51bWJlcjtcbiAgcGFydG5lclJlc3BEZXNjPzogc3RyaW5nO1xuICBwYXJ0bmVyVHJhY2VTZXE/OiBzdHJpbmc7XG4gIHJlc3VsdD86IHN0cmluZztcbiAgZXh0ZW5kRGF0YT86IGFueTtcbiAgcGF5bWVudFJ1bGU/OiBudW1iZXI7XG59XG5cbmV4cG9ydCBpbnRlcmZhY2UgQmlsbERldGFpbFNlcnZpY2VSZXNwb25zZSB7XG4gIGNvZGU/OiBzdHJpbmc7XG59XG5cbmV4cG9ydCBpbnRlcmZhY2UgQmlsbERldGFpbEN1c3RvbWVySW5mb1Jlc3BvbnNlIHtcbiAgY2lmPzogc3RyaW5nO1xuICBwaG9uZT86IGFueTtcbiAgYWNjdD86IGFueTtcbiAgbmFtZT86IHN0cmluZztcbiAgYWRkcmVzcz86IGFueTtcbn1cblxuZXhwb3J0IGludGVyZmFjZSBCaWxsRGV0YWlsQmlsbFJlc3BvbnNlIHtcbiAgaWQ/OiBzdHJpbmc7XG4gIG5vPzogYW55O1xuICBhbW91bnQ/OiBudW1iZXI7XG4gIGNvZGU/OiBzdHJpbmc7XG4gIGN1c3RDb2RlPzogYW55O1xuICBjdXN0TmFtZT86IHN0cmluZztcbiAgcGVyaW9kPzogYW55O1xuICBmZWU/OiBhbnk7XG4gIGN1c3RBZGRyZXNzPzogYW55O1xufVxuIl0sIm1hcHBpbmdzIjoiIiwiaWdub3JlTGlzdCI6W119