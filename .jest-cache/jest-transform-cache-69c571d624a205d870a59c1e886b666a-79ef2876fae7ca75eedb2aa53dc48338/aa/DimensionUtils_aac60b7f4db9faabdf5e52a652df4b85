61b243ad6c1a9beda36208c473f0b6d2
"use strict";

/* istanbul ignore next */
function cov_13loy85f5w() {
  var path = "/Users/<USER>/projects/react-native/payment-module/src/utils/DimensionUtils.ts";
  var hash = "a8da74eb3a642d5fb1b0048c1308a130078022fd";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/projects/react-native/payment-module/src/utils/DimensionUtils.ts",
    statementMap: {
      "0": {
        start: {
          line: 3,
          column: 0
        },
        end: {
          line: 5,
          column: 3
        }
      },
      "1": {
        start: {
          line: 6,
          column: 21
        },
        end: {
          line: 6,
          column: 44
        }
      },
      "2": {
        start: {
          line: 7,
          column: 28
        },
        end: {
          line: 7,
          column: 67
        }
      },
      "3": {
        start: {
          line: 8,
          column: 11
        },
        end: {
          line: 8,
          column: 39
        }
      },
      "4": {
        start: {
          line: 9,
          column: 10
        },
        end: {
          line: 9,
          column: 37
        }
      },
      "5": {
        start: {
          line: 10,
          column: 20
        },
        end: {
          line: 66,
          column: 2
        }
      },
      "6": {
        start: {
          line: 67,
          column: 16
        },
        end: {
          line: 71,
          column: 1
        }
      },
      "7": {
        start: {
          line: 68,
          column: 2
        },
        end: {
          line: 70,
          column: 5
        }
      },
      "8": {
        start: {
          line: 69,
          column: 4
        },
        end: {
          line: 69,
          column: 60
        }
      },
      "9": {
        start: {
          line: 72,
          column: 17
        },
        end: {
          line: 76,
          column: 2
        }
      },
      "10": {
        start: {
          line: 77,
          column: 20
        },
        end: {
          line: 81,
          column: 2
        }
      },
      "11": {
        start: {
          line: 82,
          column: 28
        },
        end: {
          line: 84,
          column: 1
        }
      },
      "12": {
        start: {
          line: 83,
          column: 2
        },
        end: {
          line: 83,
          column: 20
        }
      },
      "13": {
        start: {
          line: 85,
          column: 31
        },
        end: {
          line: 87,
          column: 1
        }
      },
      "14": {
        start: {
          line: 86,
          column: 2
        },
        end: {
          line: 86,
          column: 23
        }
      },
      "15": {
        start: {
          line: 88,
          column: 21
        },
        end: {
          line: 90,
          column: 1
        }
      },
      "16": {
        start: {
          line: 89,
          column: 2
        },
        end: {
          line: 89,
          column: 55
        }
      },
      "17": {
        start: {
          line: 91,
          column: 22
        },
        end: {
          line: 93,
          column: 1
        }
      },
      "18": {
        start: {
          line: 92,
          column: 2
        },
        end: {
          line: 92,
          column: 56
        }
      },
      "19": {
        start: {
          line: 94,
          column: 0
        },
        end: {
          line: 99,
          column: 2
        }
      }
    },
    fnMap: {
      "0": {
        name: "isIPhoneX",
        decl: {
          start: {
            line: 67,
            column: 25
          },
          end: {
            line: 67,
            column: 34
          }
        },
        loc: {
          start: {
            line: 67,
            column: 37
          },
          end: {
            line: 71,
            column: 1
          }
        },
        line: 67
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 68,
            column: 135
          },
          end: {
            line: 68,
            column: 136
          }
        },
        loc: {
          start: {
            line: 68,
            column: 152
          },
          end: {
            line: 70,
            column: 3
          }
        },
        line: 68
      },
      "2": {
        name: "getPaddingTopByDevice",
        decl: {
          start: {
            line: 82,
            column: 37
          },
          end: {
            line: 82,
            column: 58
          }
        },
        loc: {
          start: {
            line: 82,
            column: 61
          },
          end: {
            line: 84,
            column: 1
          }
        },
        line: 82
      },
      "3": {
        name: "getPaddingBottomByDevice",
        decl: {
          start: {
            line: 85,
            column: 40
          },
          end: {
            line: 85,
            column: 64
          }
        },
        loc: {
          start: {
            line: 85,
            column: 67
          },
          end: {
            line: 87,
            column: 1
          }
        },
        line: 85
      },
      "4": {
        name: "getWindowWidth",
        decl: {
          start: {
            line: 88,
            column: 30
          },
          end: {
            line: 88,
            column: 44
          }
        },
        loc: {
          start: {
            line: 88,
            column: 47
          },
          end: {
            line: 90,
            column: 1
          }
        },
        line: 88
      },
      "5": {
        name: "getWindowHeight",
        decl: {
          start: {
            line: 91,
            column: 31
          },
          end: {
            line: 91,
            column: 46
          }
        },
        loc: {
          start: {
            line: 91,
            column: 49
          },
          end: {
            line: 93,
            column: 1
          }
        },
        line: 91
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 68,
            column: 9
          },
          end: {
            line: 70,
            column: 4
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 68,
            column: 9
          },
          end: {
            line: 68,
            column: 45
          }
        }, {
          start: {
            line: 68,
            column: 49
          },
          end: {
            line: 68,
            column: 79
          }
        }, {
          start: {
            line: 68,
            column: 83
          },
          end: {
            line: 68,
            column: 112
          }
        }, {
          start: {
            line: 68,
            column: 116
          },
          end: {
            line: 70,
            column: 4
          }
        }],
        line: 68
      },
      "1": {
        loc: {
          start: {
            line: 69,
            column: 11
          },
          end: {
            line: 69,
            column: 59
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 69,
            column: 11
          },
          end: {
            line: 69,
            column: 32
          }
        }, {
          start: {
            line: 69,
            column: 36
          },
          end: {
            line: 69,
            column: 59
          }
        }],
        line: 69
      },
      "2": {
        loc: {
          start: {
            line: 73,
            column: 7
          },
          end: {
            line: 73,
            column: 28
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 73,
            column: 21
          },
          end: {
            line: 73,
            column: 23
          }
        }, {
          start: {
            line: 73,
            column: 26
          },
          end: {
            line: 73,
            column: 28
          }
        }],
        line: 73
      },
      "3": {
        loc: {
          start: {
            line: 74,
            column: 11
          },
          end: {
            line: 74,
            column: 55
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 74,
            column: 11
          },
          end: {
            line: 74,
            column: 49
          }
        }, {
          start: {
            line: 74,
            column: 53
          },
          end: {
            line: 74,
            column: 55
          }
        }],
        line: 74
      },
      "4": {
        loc: {
          start: {
            line: 78,
            column: 7
          },
          end: {
            line: 78,
            column: 28
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 78,
            column: 21
          },
          end: {
            line: 78,
            column: 23
          }
        }, {
          start: {
            line: 78,
            column: 26
          },
          end: {
            line: 78,
            column: 28
          }
        }],
        line: 78
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0
    },
    b: {
      "0": [0, 0, 0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0]
    },
    inputSourceMap: {
      version: 3,
      file: undefined,
      names: ["react_native_1", "require", "_react_native_1$Dimen", "Dimensions", "get", "height", "width", "iPhoneXModels", "models", "isIPhoneX", "Platform", "OS", "isPad", "isTV", "some", "model", "paddingTop", "select", "ios", "android", "StatusBar", "currentHeight", "default", "paddingBottom", "getPaddingTopByDevice", "getPaddingBottomByDevice", "getWindowWidth", "getWindowHeight", "exports"],
      sourceRoot: undefined,
      sources: ["/Users/<USER>/projects/react-native/payment-module/src/utils/DimensionUtils.ts"],
      sourcesContent: ["// import {useSafeAreaInsets} from 'react-native-safe-area-context';\nimport {Dimensions, Platform, StatusBar} from 'react-native';\n\nconst {height, width} = Dimensions.get('screen'); // Use 'screen' for better accuracy\n\nconst iPhoneXModels = [\n  {width: 320, height: 480, models: ['iPhone 1', 'iPhone 3G', 'iPhone 3GS']},\n  {width: 320, height: 568, models: ['iPhone 5', 'iPhone 5S', 'iPhone 5C', 'iPhone SE (2016)']},\n  {\n    width: 375,\n    height: 667,\n    models: ['iPhone 6', 'iPhone 6S', 'iPhone 7', 'iPhone 8', 'iPhone SE (2020)', 'iPhone SE (2022)'],\n  },\n  {width: 414, height: 736, models: ['iPhone 6 Plus', 'iPhone 6S Plus', 'iPhone 7 Plus', 'iPhone 8 Plus']},\n  {width: 375, height: 812, models: ['iPhone X', 'iPhone XS', 'iPhone 11 Pro']},\n  {width: 414, height: 896, models: ['iPhone XR', 'iPhone XS Max', 'iPhone 11', 'iPhone 11 Pro Max']},\n  {width: 390, height: 844, models: ['iPhone 12', 'iPhone 12 Pro', 'iPhone 13', 'iPhone 13 Pro']},\n  {width: 428, height: 926, models: ['iPhone 12 Pro Max', 'iPhone 13 Pro Max']},\n  {width: 430, height: 932, models: ['iPhone 14 Pro Max', 'iPhone 15 Pro Max']},\n  {width: 393, height: 852, models: ['iPhone 14 Pro', 'iPhone 15 Pro']},\n  {width: 390, height: 844, models: ['iPhone 14', 'iPhone 15']},\n  {width: 430, height: 932, models: ['iPhone 16 Pro Max']},\n  {width: 402, height: 874, models: ['iPhone 16', 'iPhone 16 Pro']},\n  {width: 430, height: 960, models: ['iPhone 16 Plus']},\n];\n\nconst isIPhoneX = () =>\n  Platform.OS === 'ios' &&\n  !Platform.isPad &&\n  !Platform.isTV &&\n  iPhoneXModels.some(model => model.width === width && model.height === height);\n\nconst paddingTop = Platform.select({\n  ios: isIPhoneX() ? 44 : 20,\n  android: StatusBar.currentHeight || 24,\n  default: 0,\n});\n\nconst paddingBottom = Platform.select({\n  ios: isIPhoneX() ? 34 : 20,\n  android: 20,\n  default: 0,\n});\n\nconst getPaddingTopByDevice = () => {\n  // return useSafeAreaInsets().top;\n  return paddingTop;\n};\n\nconst getPaddingBottomByDevice = () => {\n  // return useSafeAreaInsets().bottom;\n  return paddingBottom;\n};\nconst getWindowWidth = () => {\n  return Dimensions.get('window').width;\n};\n\nconst getWindowHeight = () => {\n  return Dimensions.get('window').height;\n};\n\nexport default {\n  getPaddingTopByDevice,\n  getPaddingBottomByDevice,\n  getWindowHeight,\n  getWindowWidth,\n};\n"],
      mappings: ";;;;;AACA,IAAAA,cAAA,GAAAC,OAAA;AAEA,IAAAC,qBAAA,GAAwBF,cAAA,CAAAG,UAAU,CAACC,GAAG,CAAC,QAAQ,CAAC;EAAzCC,MAAM,GAAAH,qBAAA,CAANG,MAAM;EAAEC,KAAK,GAAAJ,qBAAA,CAALI,KAAK;AAEpB,IAAMC,aAAa,GAAG,CACpB;EAACD,KAAK,EAAE,GAAG;EAAED,MAAM,EAAE,GAAG;EAAEG,MAAM,EAAE,CAAC,UAAU,EAAE,WAAW,EAAE,YAAY;AAAC,CAAC,EAC1E;EAACF,KAAK,EAAE,GAAG;EAAED,MAAM,EAAE,GAAG;EAAEG,MAAM,EAAE,CAAC,UAAU,EAAE,WAAW,EAAE,WAAW,EAAE,kBAAkB;AAAC,CAAC,EAC7F;EACEF,KAAK,EAAE,GAAG;EACVD,MAAM,EAAE,GAAG;EACXG,MAAM,EAAE,CAAC,UAAU,EAAE,WAAW,EAAE,UAAU,EAAE,UAAU,EAAE,kBAAkB,EAAE,kBAAkB;CACjG,EACD;EAACF,KAAK,EAAE,GAAG;EAAED,MAAM,EAAE,GAAG;EAAEG,MAAM,EAAE,CAAC,eAAe,EAAE,gBAAgB,EAAE,eAAe,EAAE,eAAe;AAAC,CAAC,EACxG;EAACF,KAAK,EAAE,GAAG;EAAED,MAAM,EAAE,GAAG;EAAEG,MAAM,EAAE,CAAC,UAAU,EAAE,WAAW,EAAE,eAAe;AAAC,CAAC,EAC7E;EAACF,KAAK,EAAE,GAAG;EAAED,MAAM,EAAE,GAAG;EAAEG,MAAM,EAAE,CAAC,WAAW,EAAE,eAAe,EAAE,WAAW,EAAE,mBAAmB;AAAC,CAAC,EACnG;EAACF,KAAK,EAAE,GAAG;EAAED,MAAM,EAAE,GAAG;EAAEG,MAAM,EAAE,CAAC,WAAW,EAAE,eAAe,EAAE,WAAW,EAAE,eAAe;AAAC,CAAC,EAC/F;EAACF,KAAK,EAAE,GAAG;EAAED,MAAM,EAAE,GAAG;EAAEG,MAAM,EAAE,CAAC,mBAAmB,EAAE,mBAAmB;AAAC,CAAC,EAC7E;EAACF,KAAK,EAAE,GAAG;EAAED,MAAM,EAAE,GAAG;EAAEG,MAAM,EAAE,CAAC,mBAAmB,EAAE,mBAAmB;AAAC,CAAC,EAC7E;EAACF,KAAK,EAAE,GAAG;EAAED,MAAM,EAAE,GAAG;EAAEG,MAAM,EAAE,CAAC,eAAe,EAAE,eAAe;AAAC,CAAC,EACrE;EAACF,KAAK,EAAE,GAAG;EAAED,MAAM,EAAE,GAAG;EAAEG,MAAM,EAAE,CAAC,WAAW,EAAE,WAAW;AAAC,CAAC,EAC7D;EAACF,KAAK,EAAE,GAAG;EAAED,MAAM,EAAE,GAAG;EAAEG,MAAM,EAAE,CAAC,mBAAmB;AAAC,CAAC,EACxD;EAACF,KAAK,EAAE,GAAG;EAAED,MAAM,EAAE,GAAG;EAAEG,MAAM,EAAE,CAAC,WAAW,EAAE,eAAe;AAAC,CAAC,EACjE;EAACF,KAAK,EAAE,GAAG;EAAED,MAAM,EAAE,GAAG;EAAEG,MAAM,EAAE,CAAC,gBAAgB;AAAC,CAAC,CACtD;AAED,IAAMC,SAAS,GAAG,SAAZA,SAASA,CAAA;EAAA,OACbT,cAAA,CAAAU,QAAQ,CAACC,EAAE,KAAK,KAAK,IACrB,CAACX,cAAA,CAAAU,QAAQ,CAACE,KAAK,IACf,CAACZ,cAAA,CAAAU,QAAQ,CAACG,IAAI,IACdN,aAAa,CAACO,IAAI,CAAC,UAAAC,KAAK;IAAA,OAAIA,KAAK,CAACT,KAAK,KAAKA,KAAK,IAAIS,KAAK,CAACV,MAAM,KAAKA,MAAM;EAAA,EAAC;AAAA;AAE/E,IAAMW,UAAU,GAAGhB,cAAA,CAAAU,QAAQ,CAACO,MAAM,CAAC;EACjCC,GAAG,EAAET,SAAS,EAAE,GAAG,EAAE,GAAG,EAAE;EAC1BU,OAAO,EAAEnB,cAAA,CAAAoB,SAAS,CAACC,aAAa,IAAI,EAAE;EACtCC,OAAO,EAAE;CACV,CAAC;AAEF,IAAMC,aAAa,GAAGvB,cAAA,CAAAU,QAAQ,CAACO,MAAM,CAAC;EACpCC,GAAG,EAAET,SAAS,EAAE,GAAG,EAAE,GAAG,EAAE;EAC1BU,OAAO,EAAE,EAAE;EACXG,OAAO,EAAE;CACV,CAAC;AAEF,IAAME,qBAAqB,GAAG,SAAxBA,qBAAqBA,CAAA,EAAQ;EAEjC,OAAOR,UAAU;AACnB,CAAC;AAED,IAAMS,wBAAwB,GAAG,SAA3BA,wBAAwBA,CAAA,EAAQ;EAEpC,OAAOF,aAAa;AACtB,CAAC;AACD,IAAMG,cAAc,GAAG,SAAjBA,cAAcA,CAAA,EAAQ;EAC1B,OAAO1B,cAAA,CAAAG,UAAU,CAACC,GAAG,CAAC,QAAQ,CAAC,CAACE,KAAK;AACvC,CAAC;AAED,IAAMqB,eAAe,GAAG,SAAlBA,eAAeA,CAAA,EAAQ;EAC3B,OAAO3B,cAAA,CAAAG,UAAU,CAACC,GAAG,CAAC,QAAQ,CAAC,CAACC,MAAM;AACxC,CAAC;AAEDuB,OAAA,CAAAN,OAAA,GAAe;EACbE,qBAAqB,EAArBA,qBAAqB;EACrBC,wBAAwB,EAAxBA,wBAAwB;EACxBE,eAAe,EAAfA,eAAe;EACfD,cAAc,EAAdA;CACD",
      ignoreList: []
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "a8da74eb3a642d5fb1b0048c1308a130078022fd"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_13loy85f5w = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_13loy85f5w();
cov_13loy85f5w().s[0]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
var react_native_1 =
/* istanbul ignore next */
(cov_13loy85f5w().s[1]++, require("react-native"));
var _react_native_1$Dimen =
  /* istanbul ignore next */
  (cov_13loy85f5w().s[2]++, react_native_1.Dimensions.get('screen')),
  height =
  /* istanbul ignore next */
  (cov_13loy85f5w().s[3]++, _react_native_1$Dimen.height),
  width =
  /* istanbul ignore next */
  (cov_13loy85f5w().s[4]++, _react_native_1$Dimen.width);
var iPhoneXModels =
/* istanbul ignore next */
(cov_13loy85f5w().s[5]++, [{
  width: 320,
  height: 480,
  models: ['iPhone 1', 'iPhone 3G', 'iPhone 3GS']
}, {
  width: 320,
  height: 568,
  models: ['iPhone 5', 'iPhone 5S', 'iPhone 5C', 'iPhone SE (2016)']
}, {
  width: 375,
  height: 667,
  models: ['iPhone 6', 'iPhone 6S', 'iPhone 7', 'iPhone 8', 'iPhone SE (2020)', 'iPhone SE (2022)']
}, {
  width: 414,
  height: 736,
  models: ['iPhone 6 Plus', 'iPhone 6S Plus', 'iPhone 7 Plus', 'iPhone 8 Plus']
}, {
  width: 375,
  height: 812,
  models: ['iPhone X', 'iPhone XS', 'iPhone 11 Pro']
}, {
  width: 414,
  height: 896,
  models: ['iPhone XR', 'iPhone XS Max', 'iPhone 11', 'iPhone 11 Pro Max']
}, {
  width: 390,
  height: 844,
  models: ['iPhone 12', 'iPhone 12 Pro', 'iPhone 13', 'iPhone 13 Pro']
}, {
  width: 428,
  height: 926,
  models: ['iPhone 12 Pro Max', 'iPhone 13 Pro Max']
}, {
  width: 430,
  height: 932,
  models: ['iPhone 14 Pro Max', 'iPhone 15 Pro Max']
}, {
  width: 393,
  height: 852,
  models: ['iPhone 14 Pro', 'iPhone 15 Pro']
}, {
  width: 390,
  height: 844,
  models: ['iPhone 14', 'iPhone 15']
}, {
  width: 430,
  height: 932,
  models: ['iPhone 16 Pro Max']
}, {
  width: 402,
  height: 874,
  models: ['iPhone 16', 'iPhone 16 Pro']
}, {
  width: 430,
  height: 960,
  models: ['iPhone 16 Plus']
}]);
/* istanbul ignore next */
cov_13loy85f5w().s[6]++;
var isIPhoneX = function isIPhoneX() {
  /* istanbul ignore next */
  cov_13loy85f5w().f[0]++;
  cov_13loy85f5w().s[7]++;
  return /* istanbul ignore next */(cov_13loy85f5w().b[0][0]++, react_native_1.Platform.OS === 'ios') &&
  /* istanbul ignore next */
  (cov_13loy85f5w().b[0][1]++, !react_native_1.Platform.isPad) &&
  /* istanbul ignore next */
  (cov_13loy85f5w().b[0][2]++, !react_native_1.Platform.isTV) &&
  /* istanbul ignore next */
  (cov_13loy85f5w().b[0][3]++, iPhoneXModels.some(function (model) {
    /* istanbul ignore next */
    cov_13loy85f5w().f[1]++;
    cov_13loy85f5w().s[8]++;
    return /* istanbul ignore next */(cov_13loy85f5w().b[1][0]++, model.width === width) &&
    /* istanbul ignore next */
    (cov_13loy85f5w().b[1][1]++, model.height === height);
  }));
};
var paddingTop =
/* istanbul ignore next */
(cov_13loy85f5w().s[9]++, react_native_1.Platform.select({
  ios: isIPhoneX() ?
  /* istanbul ignore next */
  (cov_13loy85f5w().b[2][0]++, 44) :
  /* istanbul ignore next */
  (cov_13loy85f5w().b[2][1]++, 20),
  android:
  /* istanbul ignore next */
  (cov_13loy85f5w().b[3][0]++, react_native_1.StatusBar.currentHeight) ||
  /* istanbul ignore next */
  (cov_13loy85f5w().b[3][1]++, 24),
  default: 0
}));
var paddingBottom =
/* istanbul ignore next */
(cov_13loy85f5w().s[10]++, react_native_1.Platform.select({
  ios: isIPhoneX() ?
  /* istanbul ignore next */
  (cov_13loy85f5w().b[4][0]++, 34) :
  /* istanbul ignore next */
  (cov_13loy85f5w().b[4][1]++, 20),
  android: 20,
  default: 0
}));
/* istanbul ignore next */
cov_13loy85f5w().s[11]++;
var getPaddingTopByDevice = function getPaddingTopByDevice() {
  /* istanbul ignore next */
  cov_13loy85f5w().f[2]++;
  cov_13loy85f5w().s[12]++;
  return paddingTop;
};
/* istanbul ignore next */
cov_13loy85f5w().s[13]++;
var getPaddingBottomByDevice = function getPaddingBottomByDevice() {
  /* istanbul ignore next */
  cov_13loy85f5w().f[3]++;
  cov_13loy85f5w().s[14]++;
  return paddingBottom;
};
/* istanbul ignore next */
cov_13loy85f5w().s[15]++;
var getWindowWidth = function getWindowWidth() {
  /* istanbul ignore next */
  cov_13loy85f5w().f[4]++;
  cov_13loy85f5w().s[16]++;
  return react_native_1.Dimensions.get('window').width;
};
/* istanbul ignore next */
cov_13loy85f5w().s[17]++;
var getWindowHeight = function getWindowHeight() {
  /* istanbul ignore next */
  cov_13loy85f5w().f[5]++;
  cov_13loy85f5w().s[18]++;
  return react_native_1.Dimensions.get('window').height;
};
/* istanbul ignore next */
cov_13loy85f5w().s[19]++;
exports.default = {
  getPaddingTopByDevice: getPaddingTopByDevice,
  getPaddingBottomByDevice: getPaddingBottomByDevice,
  getWindowHeight: getWindowHeight,
  getWindowWidth: getWindowWidth
};
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJjb3ZfMTNsb3k4NWY1dyIsImFjdHVhbENvdmVyYWdlIiwicyIsInJlYWN0X25hdGl2ZV8xIiwicmVxdWlyZSIsIl9yZWFjdF9uYXRpdmVfMSREaW1lbiIsIkRpbWVuc2lvbnMiLCJnZXQiLCJoZWlnaHQiLCJ3aWR0aCIsImlQaG9uZVhNb2RlbHMiLCJtb2RlbHMiLCJpc0lQaG9uZVgiLCJmIiwiYiIsIlBsYXRmb3JtIiwiT1MiLCJpc1BhZCIsImlzVFYiLCJzb21lIiwibW9kZWwiLCJwYWRkaW5nVG9wIiwic2VsZWN0IiwiaW9zIiwiYW5kcm9pZCIsIlN0YXR1c0JhciIsImN1cnJlbnRIZWlnaHQiLCJkZWZhdWx0IiwicGFkZGluZ0JvdHRvbSIsImdldFBhZGRpbmdUb3BCeURldmljZSIsImdldFBhZGRpbmdCb3R0b21CeURldmljZSIsImdldFdpbmRvd1dpZHRoIiwiZ2V0V2luZG93SGVpZ2h0IiwiZXhwb3J0cyJdLCJzb3VyY2VzIjpbIi9Vc2Vycy9zb25zYW1zZXQvcHJvamVjdHMvcmVhY3QtbmF0aXZlL3BheW1lbnQtbW9kdWxlL3NyYy91dGlscy9EaW1lbnNpb25VdGlscy50cyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBpbXBvcnQge3VzZVNhZmVBcmVhSW5zZXRzfSBmcm9tICdyZWFjdC1uYXRpdmUtc2FmZS1hcmVhLWNvbnRleHQnO1xuaW1wb3J0IHtEaW1lbnNpb25zLCBQbGF0Zm9ybSwgU3RhdHVzQmFyfSBmcm9tICdyZWFjdC1uYXRpdmUnO1xuXG5jb25zdCB7aGVpZ2h0LCB3aWR0aH0gPSBEaW1lbnNpb25zLmdldCgnc2NyZWVuJyk7IC8vIFVzZSAnc2NyZWVuJyBmb3IgYmV0dGVyIGFjY3VyYWN5XG5cbmNvbnN0IGlQaG9uZVhNb2RlbHMgPSBbXG4gIHt3aWR0aDogMzIwLCBoZWlnaHQ6IDQ4MCwgbW9kZWxzOiBbJ2lQaG9uZSAxJywgJ2lQaG9uZSAzRycsICdpUGhvbmUgM0dTJ119LFxuICB7d2lkdGg6IDMyMCwgaGVpZ2h0OiA1NjgsIG1vZGVsczogWydpUGhvbmUgNScsICdpUGhvbmUgNVMnLCAnaVBob25lIDVDJywgJ2lQaG9uZSBTRSAoMjAxNiknXX0sXG4gIHtcbiAgICB3aWR0aDogMzc1LFxuICAgIGhlaWdodDogNjY3LFxuICAgIG1vZGVsczogWydpUGhvbmUgNicsICdpUGhvbmUgNlMnLCAnaVBob25lIDcnLCAnaVBob25lIDgnLCAnaVBob25lIFNFICgyMDIwKScsICdpUGhvbmUgU0UgKDIwMjIpJ10sXG4gIH0sXG4gIHt3aWR0aDogNDE0LCBoZWlnaHQ6IDczNiwgbW9kZWxzOiBbJ2lQaG9uZSA2IFBsdXMnLCAnaVBob25lIDZTIFBsdXMnLCAnaVBob25lIDcgUGx1cycsICdpUGhvbmUgOCBQbHVzJ119LFxuICB7d2lkdGg6IDM3NSwgaGVpZ2h0OiA4MTIsIG1vZGVsczogWydpUGhvbmUgWCcsICdpUGhvbmUgWFMnLCAnaVBob25lIDExIFBybyddfSxcbiAge3dpZHRoOiA0MTQsIGhlaWdodDogODk2LCBtb2RlbHM6IFsnaVBob25lIFhSJywgJ2lQaG9uZSBYUyBNYXgnLCAnaVBob25lIDExJywgJ2lQaG9uZSAxMSBQcm8gTWF4J119LFxuICB7d2lkdGg6IDM5MCwgaGVpZ2h0OiA4NDQsIG1vZGVsczogWydpUGhvbmUgMTInLCAnaVBob25lIDEyIFBybycsICdpUGhvbmUgMTMnLCAnaVBob25lIDEzIFBybyddfSxcbiAge3dpZHRoOiA0MjgsIGhlaWdodDogOTI2LCBtb2RlbHM6IFsnaVBob25lIDEyIFBybyBNYXgnLCAnaVBob25lIDEzIFBybyBNYXgnXX0sXG4gIHt3aWR0aDogNDMwLCBoZWlnaHQ6IDkzMiwgbW9kZWxzOiBbJ2lQaG9uZSAxNCBQcm8gTWF4JywgJ2lQaG9uZSAxNSBQcm8gTWF4J119LFxuICB7d2lkdGg6IDM5MywgaGVpZ2h0OiA4NTIsIG1vZGVsczogWydpUGhvbmUgMTQgUHJvJywgJ2lQaG9uZSAxNSBQcm8nXX0sXG4gIHt3aWR0aDogMzkwLCBoZWlnaHQ6IDg0NCwgbW9kZWxzOiBbJ2lQaG9uZSAxNCcsICdpUGhvbmUgMTUnXX0sXG4gIHt3aWR0aDogNDMwLCBoZWlnaHQ6IDkzMiwgbW9kZWxzOiBbJ2lQaG9uZSAxNiBQcm8gTWF4J119LFxuICB7d2lkdGg6IDQwMiwgaGVpZ2h0OiA4NzQsIG1vZGVsczogWydpUGhvbmUgMTYnLCAnaVBob25lIDE2IFBybyddfSxcbiAge3dpZHRoOiA0MzAsIGhlaWdodDogOTYwLCBtb2RlbHM6IFsnaVBob25lIDE2IFBsdXMnXX0sXG5dO1xuXG5jb25zdCBpc0lQaG9uZVggPSAoKSA9PlxuICBQbGF0Zm9ybS5PUyA9PT0gJ2lvcycgJiZcbiAgIVBsYXRmb3JtLmlzUGFkICYmXG4gICFQbGF0Zm9ybS5pc1RWICYmXG4gIGlQaG9uZVhNb2RlbHMuc29tZShtb2RlbCA9PiBtb2RlbC53aWR0aCA9PT0gd2lkdGggJiYgbW9kZWwuaGVpZ2h0ID09PSBoZWlnaHQpO1xuXG5jb25zdCBwYWRkaW5nVG9wID0gUGxhdGZvcm0uc2VsZWN0KHtcbiAgaW9zOiBpc0lQaG9uZVgoKSA/IDQ0IDogMjAsXG4gIGFuZHJvaWQ6IFN0YXR1c0Jhci5jdXJyZW50SGVpZ2h0IHx8IDI0LFxuICBkZWZhdWx0OiAwLFxufSk7XG5cbmNvbnN0IHBhZGRpbmdCb3R0b20gPSBQbGF0Zm9ybS5zZWxlY3Qoe1xuICBpb3M6IGlzSVBob25lWCgpID8gMzQgOiAyMCxcbiAgYW5kcm9pZDogMjAsXG4gIGRlZmF1bHQ6IDAsXG59KTtcblxuY29uc3QgZ2V0UGFkZGluZ1RvcEJ5RGV2aWNlID0gKCkgPT4ge1xuICAvLyByZXR1cm4gdXNlU2FmZUFyZWFJbnNldHMoKS50b3A7XG4gIHJldHVybiBwYWRkaW5nVG9wO1xufTtcblxuY29uc3QgZ2V0UGFkZGluZ0JvdHRvbUJ5RGV2aWNlID0gKCkgPT4ge1xuICAvLyByZXR1cm4gdXNlU2FmZUFyZWFJbnNldHMoKS5ib3R0b207XG4gIHJldHVybiBwYWRkaW5nQm90dG9tO1xufTtcbmNvbnN0IGdldFdpbmRvd1dpZHRoID0gKCkgPT4ge1xuICByZXR1cm4gRGltZW5zaW9ucy5nZXQoJ3dpbmRvdycpLndpZHRoO1xufTtcblxuY29uc3QgZ2V0V2luZG93SGVpZ2h0ID0gKCkgPT4ge1xuICByZXR1cm4gRGltZW5zaW9ucy5nZXQoJ3dpbmRvdycpLmhlaWdodDtcbn07XG5cbmV4cG9ydCBkZWZhdWx0IHtcbiAgZ2V0UGFkZGluZ1RvcEJ5RGV2aWNlLFxuICBnZXRQYWRkaW5nQm90dG9tQnlEZXZpY2UsXG4gIGdldFdpbmRvd0hlaWdodCxcbiAgZ2V0V2luZG93V2lkdGgsXG59O1xuIl0sIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztJQU91QjtJQUFBQSxjQUFBLFlBQUFBLENBQUE7TUFBQSxPQUFBQyxjQUFBO0lBQUE7RUFBQTtFQUFBLE9BQUFBLGNBQUE7QUFBQTtBQUFBRCxjQUFBO0FBQUFBLGNBQUEsR0FBQUUsQ0FBQTs7OztBQU52QixJQUFBQyxjQUFBO0FBQUE7QUFBQSxDQUFBSCxjQUFBLEdBQUFFLENBQUEsT0FBQUUsT0FBQTtBQUVBLElBQUFDLHFCQUFBO0VBQUE7RUFBQSxDQUFBTCxjQUFBLEdBQUFFLENBQUEsT0FBd0JDLGNBQUEsQ0FBQUcsVUFBVSxDQUFDQyxHQUFHLENBQUMsUUFBUSxDQUFDO0VBQXpDQyxNQUFNO0VBQUE7RUFBQSxDQUFBUixjQUFBLEdBQUFFLENBQUEsT0FBQUcscUJBQUEsQ0FBTkcsTUFBTTtFQUFFQyxLQUFLO0VBQUE7RUFBQSxDQUFBVCxjQUFBLEdBQUFFLENBQUEsT0FBQUcscUJBQUEsQ0FBTEksS0FBSztBQUVwQixJQUFNQyxhQUFhO0FBQUE7QUFBQSxDQUFBVixjQUFBLEdBQUFFLENBQUEsT0FBRyxDQUNwQjtFQUFDTyxLQUFLLEVBQUUsR0FBRztFQUFFRCxNQUFNLEVBQUUsR0FBRztFQUFFRyxNQUFNLEVBQUUsQ0FBQyxVQUFVLEVBQUUsV0FBVyxFQUFFLFlBQVk7QUFBQyxDQUFDLEVBQzFFO0VBQUNGLEtBQUssRUFBRSxHQUFHO0VBQUVELE1BQU0sRUFBRSxHQUFHO0VBQUVHLE1BQU0sRUFBRSxDQUFDLFVBQVUsRUFBRSxXQUFXLEVBQUUsV0FBVyxFQUFFLGtCQUFrQjtBQUFDLENBQUMsRUFDN0Y7RUFDRUYsS0FBSyxFQUFFLEdBQUc7RUFDVkQsTUFBTSxFQUFFLEdBQUc7RUFDWEcsTUFBTSxFQUFFLENBQUMsVUFBVSxFQUFFLFdBQVcsRUFBRSxVQUFVLEVBQUUsVUFBVSxFQUFFLGtCQUFrQixFQUFFLGtCQUFrQjtDQUNqRyxFQUNEO0VBQUNGLEtBQUssRUFBRSxHQUFHO0VBQUVELE1BQU0sRUFBRSxHQUFHO0VBQUVHLE1BQU0sRUFBRSxDQUFDLGVBQWUsRUFBRSxnQkFBZ0IsRUFBRSxlQUFlLEVBQUUsZUFBZTtBQUFDLENBQUMsRUFDeEc7RUFBQ0YsS0FBSyxFQUFFLEdBQUc7RUFBRUQsTUFBTSxFQUFFLEdBQUc7RUFBRUcsTUFBTSxFQUFFLENBQUMsVUFBVSxFQUFFLFdBQVcsRUFBRSxlQUFlO0FBQUMsQ0FBQyxFQUM3RTtFQUFDRixLQUFLLEVBQUUsR0FBRztFQUFFRCxNQUFNLEVBQUUsR0FBRztFQUFFRyxNQUFNLEVBQUUsQ0FBQyxXQUFXLEVBQUUsZUFBZSxFQUFFLFdBQVcsRUFBRSxtQkFBbUI7QUFBQyxDQUFDLEVBQ25HO0VBQUNGLEtBQUssRUFBRSxHQUFHO0VBQUVELE1BQU0sRUFBRSxHQUFHO0VBQUVHLE1BQU0sRUFBRSxDQUFDLFdBQVcsRUFBRSxlQUFlLEVBQUUsV0FBVyxFQUFFLGVBQWU7QUFBQyxDQUFDLEVBQy9GO0VBQUNGLEtBQUssRUFBRSxHQUFHO0VBQUVELE1BQU0sRUFBRSxHQUFHO0VBQUVHLE1BQU0sRUFBRSxDQUFDLG1CQUFtQixFQUFFLG1CQUFtQjtBQUFDLENBQUMsRUFDN0U7RUFBQ0YsS0FBSyxFQUFFLEdBQUc7RUFBRUQsTUFBTSxFQUFFLEdBQUc7RUFBRUcsTUFBTSxFQUFFLENBQUMsbUJBQW1CLEVBQUUsbUJBQW1CO0FBQUMsQ0FBQyxFQUM3RTtFQUFDRixLQUFLLEVBQUUsR0FBRztFQUFFRCxNQUFNLEVBQUUsR0FBRztFQUFFRyxNQUFNLEVBQUUsQ0FBQyxlQUFlLEVBQUUsZUFBZTtBQUFDLENBQUMsRUFDckU7RUFBQ0YsS0FBSyxFQUFFLEdBQUc7RUFBRUQsTUFBTSxFQUFFLEdBQUc7RUFBRUcsTUFBTSxFQUFFLENBQUMsV0FBVyxFQUFFLFdBQVc7QUFBQyxDQUFDLEVBQzdEO0VBQUNGLEtBQUssRUFBRSxHQUFHO0VBQUVELE1BQU0sRUFBRSxHQUFHO0VBQUVHLE1BQU0sRUFBRSxDQUFDLG1CQUFtQjtBQUFDLENBQUMsRUFDeEQ7RUFBQ0YsS0FBSyxFQUFFLEdBQUc7RUFBRUQsTUFBTSxFQUFFLEdBQUc7RUFBRUcsTUFBTSxFQUFFLENBQUMsV0FBVyxFQUFFLGVBQWU7QUFBQyxDQUFDLEVBQ2pFO0VBQUNGLEtBQUssRUFBRSxHQUFHO0VBQUVELE1BQU0sRUFBRSxHQUFHO0VBQUVHLE1BQU0sRUFBRSxDQUFDLGdCQUFnQjtBQUFDLENBQUMsQ0FDdEQ7QUFBQTtBQUFBWCxjQUFBLEdBQUFFLENBQUE7QUFFRCxJQUFNVSxTQUFTLEdBQUcsU0FBWkEsU0FBU0EsQ0FBQTtFQUFBO0VBQUFaLGNBQUEsR0FBQWEsQ0FBQTtFQUFBYixjQUFBLEdBQUFFLENBQUE7RUFBQSxPQUNiLDJCQUFBRixjQUFBLEdBQUFjLENBQUEsVUFBQVgsY0FBQSxDQUFBWSxRQUFRLENBQUNDLEVBQUUsS0FBSyxLQUFLO0VBQUE7RUFBQSxDQUFBaEIsY0FBQSxHQUFBYyxDQUFBLFVBQ3JCLENBQUNYLGNBQUEsQ0FBQVksUUFBUSxDQUFDRSxLQUFLO0VBQUE7RUFBQSxDQUFBakIsY0FBQSxHQUFBYyxDQUFBLFVBQ2YsQ0FBQ1gsY0FBQSxDQUFBWSxRQUFRLENBQUNHLElBQUk7RUFBQTtFQUFBLENBQUFsQixjQUFBLEdBQUFjLENBQUEsVUFDZEosYUFBYSxDQUFDUyxJQUFJLENBQUMsVUFBQUMsS0FBSztJQUFBO0lBQUFwQixjQUFBLEdBQUFhLENBQUE7SUFBQWIsY0FBQSxHQUFBRSxDQUFBO0lBQUEsT0FBSSwyQkFBQUYsY0FBQSxHQUFBYyxDQUFBLFVBQUFNLEtBQUssQ0FBQ1gsS0FBSyxLQUFLQSxLQUFLO0lBQUE7SUFBQSxDQUFBVCxjQUFBLEdBQUFjLENBQUEsVUFBSU0sS0FBSyxDQUFDWixNQUFNLEtBQUtBLE1BQU07RUFBQSxFQUFDO0FBQUE7QUFFL0UsSUFBTWEsVUFBVTtBQUFBO0FBQUEsQ0FBQXJCLGNBQUEsR0FBQUUsQ0FBQSxPQUFHQyxjQUFBLENBQUFZLFFBQVEsQ0FBQ08sTUFBTSxDQUFDO0VBQ2pDQyxHQUFHLEVBQUVYLFNBQVMsRUFBRTtFQUFBO0VBQUEsQ0FBQVosY0FBQSxHQUFBYyxDQUFBLFVBQUcsRUFBRTtFQUFBO0VBQUEsQ0FBQWQsY0FBQSxHQUFBYyxDQUFBLFVBQUcsRUFBRTtFQUMxQlUsT0FBTztFQUFFO0VBQUEsQ0FBQXhCLGNBQUEsR0FBQWMsQ0FBQSxVQUFBWCxjQUFBLENBQUFzQixTQUFTLENBQUNDLGFBQWE7RUFBQTtFQUFBLENBQUExQixjQUFBLEdBQUFjLENBQUEsVUFBSSxFQUFFO0VBQ3RDYSxPQUFPLEVBQUU7Q0FDVixDQUFDO0FBRUYsSUFBTUMsYUFBYTtBQUFBO0FBQUEsQ0FBQTVCLGNBQUEsR0FBQUUsQ0FBQSxRQUFHQyxjQUFBLENBQUFZLFFBQVEsQ0FBQ08sTUFBTSxDQUFDO0VBQ3BDQyxHQUFHLEVBQUVYLFNBQVMsRUFBRTtFQUFBO0VBQUEsQ0FBQVosY0FBQSxHQUFBYyxDQUFBLFVBQUcsRUFBRTtFQUFBO0VBQUEsQ0FBQWQsY0FBQSxHQUFBYyxDQUFBLFVBQUcsRUFBRTtFQUMxQlUsT0FBTyxFQUFFLEVBQUU7RUFDWEcsT0FBTyxFQUFFO0NBQ1YsQ0FBQztBQUFBO0FBQUEzQixjQUFBLEdBQUFFLENBQUE7QUFFRixJQUFNMkIscUJBQXFCLEdBQUcsU0FBeEJBLHFCQUFxQkEsQ0FBQSxFQUFRO0VBQUE7RUFBQTdCLGNBQUEsR0FBQWEsQ0FBQTtFQUFBYixjQUFBLEdBQUFFLENBQUE7RUFFakMsT0FBT21CLFVBQVU7QUFDbkIsQ0FBQztBQUFBO0FBQUFyQixjQUFBLEdBQUFFLENBQUE7QUFFRCxJQUFNNEIsd0JBQXdCLEdBQUcsU0FBM0JBLHdCQUF3QkEsQ0FBQSxFQUFRO0VBQUE7RUFBQTlCLGNBQUEsR0FBQWEsQ0FBQTtFQUFBYixjQUFBLEdBQUFFLENBQUE7RUFFcEMsT0FBTzBCLGFBQWE7QUFDdEIsQ0FBQztBQUFBO0FBQUE1QixjQUFBLEdBQUFFLENBQUE7QUFDRCxJQUFNNkIsY0FBYyxHQUFHLFNBQWpCQSxjQUFjQSxDQUFBLEVBQVE7RUFBQTtFQUFBL0IsY0FBQSxHQUFBYSxDQUFBO0VBQUFiLGNBQUEsR0FBQUUsQ0FBQTtFQUMxQixPQUFPQyxjQUFBLENBQUFHLFVBQVUsQ0FBQ0MsR0FBRyxDQUFDLFFBQVEsQ0FBQyxDQUFDRSxLQUFLO0FBQ3ZDLENBQUM7QUFBQTtBQUFBVCxjQUFBLEdBQUFFLENBQUE7QUFFRCxJQUFNOEIsZUFBZSxHQUFHLFNBQWxCQSxlQUFlQSxDQUFBLEVBQVE7RUFBQTtFQUFBaEMsY0FBQSxHQUFBYSxDQUFBO0VBQUFiLGNBQUEsR0FBQUUsQ0FBQTtFQUMzQixPQUFPQyxjQUFBLENBQUFHLFVBQVUsQ0FBQ0MsR0FBRyxDQUFDLFFBQVEsQ0FBQyxDQUFDQyxNQUFNO0FBQ3hDLENBQUM7QUFBQTtBQUFBUixjQUFBLEdBQUFFLENBQUE7QUFFRCtCLE9BQUEsQ0FBQU4sT0FBQSxHQUFlO0VBQ2JFLHFCQUFxQixFQUFyQkEscUJBQXFCO0VBQ3JCQyx3QkFBd0IsRUFBeEJBLHdCQUF3QjtFQUN4QkUsZUFBZSxFQUFmQSxlQUFlO0VBQ2ZELGNBQWMsRUFBZEE7Q0FDRCIsImlnbm9yZUxpc3QiOltdfQ==