{"version": 3, "names": ["NativeComponentRegistry", "_interopRequireWildcard", "require", "_ViewConfigIgnore", "_Platform", "_interopRequireDefault", "_getRequireWildcardCache", "e", "WeakMap", "r", "t", "__esModule", "default", "has", "get", "n", "__proto__", "a", "Object", "defineProperty", "getOwnPropertyDescriptor", "u", "hasOwnProperty", "call", "i", "set", "__INTERNAL_VIEW_CONFIG", "exports", "Platform", "OS", "uiViewClassName", "bubblingEventTypes", "directEventTypes", "topMomentumScrollBegin", "registrationName", "topMomentumScrollEnd", "topScroll", "topScrollBeginDrag", "topScrollEndDrag", "validAttributes", "contentOffset", "diff", "decelerationRate", "disableIntervalMomentum", "maintainVisibleContentPosition", "pagingEnabled", "scrollEnabled", "showsVerticalScrollIndicator", "snapToAlignment", "snapToEnd", "snapToInterval", "snapToOffsets", "snapToStart", "borderBottomLeftRadius", "borderBottomRightRadius", "sendMomentumEvents", "borderRadius", "nestedScrollEnabled", "scrollEventThrottle", "borderStyle", "borderRightColor", "process", "borderColor", "borderBottomColor", "persistentScrollbar", "horizontal", "endFillColor", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "overScrollMode", "borderTopLeftRadius", "scrollPerfTag", "borderTopColor", "removeClippedSubviews", "borderTopRightRadius", "borderLeftColor", "pointerEvents", "isInvertedVirtualizedList", "topScrollToTop", "assign", "alwaysBounceHorizontal", "alwaysBounceVertical", "automaticallyAdjustContentInsets", "automaticallyAdjustKeyboardInsets", "automaticallyAdjustsScrollIndicatorInsets", "bounces", "bouncesZoom", "canCancelContentTouches", "centerContent", "contentInset", "contentInsetAdjustmentBehavior", "endDraggingSensitivityMultiplier", "directionalLockEnabled", "indicatorStyle", "inverted", "keyboardDismissMode", "maximumZoomScale", "minimumZoomScale", "pinchGestureEnabled", "scrollIndicatorInsets", "scrollToOverflowEnabled", "scrollsToTop", "showsHorizontalScrollIndicator", "verticalScrollIndicatorInsets", "zoomScale", "ConditionallyIgnoredEventHandlers", "onScrollBeginDrag", "onMomentumScrollEnd", "onScrollEndDrag", "onMomentumScrollBegin", "onScrollToTop", "onScroll", "ScrollViewNativeComponent", "_default"], "sources": ["ScrollViewNativeComponent.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @flow strict-local\n * @format\n */\n\nimport type {\n  HostComponent,\n  PartialViewConfig,\n} from '../../Renderer/shims/ReactNativeTypes';\nimport type {ScrollViewNativeProps as Props} from './ScrollViewNativeComponentType';\n\nimport * as NativeComponentRegistry from '../../NativeComponent/NativeComponentRegistry';\nimport {ConditionallyIgnoredEventHandlers} from '../../NativeComponent/ViewConfigIgnore';\nimport Platform from '../../Utilities/Platform';\n\nexport const __INTERNAL_VIEW_CONFIG: PartialViewConfig =\n  Platform.OS === 'android'\n    ? {\n        uiViewClassName: 'RCTScrollView',\n        bubblingEventTypes: {},\n        directEventTypes: {\n          topMomentumScrollBegin: {\n            registrationName: 'onMomentumScrollBegin',\n          },\n          topMomentumScrollEnd: {\n            registrationName: 'onMomentumScrollEnd',\n          },\n          topScroll: {\n            registrationName: 'onScroll',\n          },\n          topScrollBeginDrag: {\n            registrationName: 'onScrollBeginDrag',\n          },\n          topScrollEndDrag: {\n            registrationName: 'onScrollEndDrag',\n          },\n        },\n        validAttributes: {\n          contentOffset: {\n            diff: require('../../Utilities/differ/pointsDiffer'),\n          },\n          decelerationRate: true,\n          disableIntervalMomentum: true,\n          maintainVisibleContentPosition: true,\n          pagingEnabled: true,\n          scrollEnabled: true,\n          showsVerticalScrollIndicator: true,\n          snapToAlignment: true,\n          snapToEnd: true,\n          snapToInterval: true,\n          snapToOffsets: true,\n          snapToStart: true,\n          borderBottomLeftRadius: true,\n          borderBottomRightRadius: true,\n          sendMomentumEvents: true,\n          borderRadius: true,\n          nestedScrollEnabled: true,\n          scrollEventThrottle: true,\n          borderStyle: true,\n          borderRightColor: {\n            process: require('../../StyleSheet/processColor').default,\n          },\n          borderColor: {\n            process: require('../../StyleSheet/processColor').default,\n          },\n          borderBottomColor: {\n            process: require('../../StyleSheet/processColor').default,\n          },\n          persistentScrollbar: true,\n          horizontal: true,\n          endFillColor: {\n            process: require('../../StyleSheet/processColor').default,\n          },\n          fadingEdgeLength: true,\n          overScrollMode: true,\n          borderTopLeftRadius: true,\n          scrollPerfTag: true,\n          borderTopColor: {\n            process: require('../../StyleSheet/processColor').default,\n          },\n          removeClippedSubviews: true,\n          borderTopRightRadius: true,\n          borderLeftColor: {\n            process: require('../../StyleSheet/processColor').default,\n          },\n          pointerEvents: true,\n          isInvertedVirtualizedList: true,\n        },\n      }\n    : {\n        uiViewClassName: 'RCTScrollView',\n        bubblingEventTypes: {},\n        directEventTypes: {\n          topMomentumScrollBegin: {\n            registrationName: 'onMomentumScrollBegin',\n          },\n          topMomentumScrollEnd: {\n            registrationName: 'onMomentumScrollEnd',\n          },\n          topScroll: {\n            registrationName: 'onScroll',\n          },\n          topScrollBeginDrag: {\n            registrationName: 'onScrollBeginDrag',\n          },\n          topScrollEndDrag: {\n            registrationName: 'onScrollEndDrag',\n          },\n          topScrollToTop: {\n            registrationName: 'onScrollToTop',\n          },\n        },\n        validAttributes: {\n          alwaysBounceHorizontal: true,\n          alwaysBounceVertical: true,\n          automaticallyAdjustContentInsets: true,\n          automaticallyAdjustKeyboardInsets: true,\n          automaticallyAdjustsScrollIndicatorInsets: true,\n          bounces: true,\n          bouncesZoom: true,\n          canCancelContentTouches: true,\n          centerContent: true,\n          contentInset: {\n            diff: require('../../Utilities/differ/insetsDiffer'),\n          },\n          contentOffset: {\n            diff: require('../../Utilities/differ/pointsDiffer'),\n          },\n          contentInsetAdjustmentBehavior: true,\n          decelerationRate: true,\n          endDraggingSensitivityMultiplier: true,\n          directionalLockEnabled: true,\n          disableIntervalMomentum: true,\n          indicatorStyle: true,\n          inverted: true,\n          keyboardDismissMode: true,\n          maintainVisibleContentPosition: true,\n          maximumZoomScale: true,\n          minimumZoomScale: true,\n          pagingEnabled: true,\n          pinchGestureEnabled: true,\n          scrollEnabled: true,\n          scrollEventThrottle: true,\n          scrollIndicatorInsets: {\n            diff: require('../../Utilities/differ/insetsDiffer'),\n          },\n          scrollToOverflowEnabled: true,\n          scrollsToTop: true,\n          showsHorizontalScrollIndicator: true,\n          showsVerticalScrollIndicator: true,\n          snapToAlignment: true,\n          snapToEnd: true,\n          snapToInterval: true,\n          snapToOffsets: true,\n          snapToStart: true,\n          verticalScrollIndicatorInsets: {\n            diff: require('../../Utilities/differ/insetsDiffer'),\n          },\n          zoomScale: true,\n          ...ConditionallyIgnoredEventHandlers({\n            onScrollBeginDrag: true,\n            onMomentumScrollEnd: true,\n            onScrollEndDrag: true,\n            onMomentumScrollBegin: true,\n            onScrollToTop: true,\n            onScroll: true,\n          }),\n        },\n      };\n\nconst ScrollViewNativeComponent: HostComponent<Props> =\n  NativeComponentRegistry.get<Props>(\n    'RCTScrollView',\n    () => __INTERNAL_VIEW_CONFIG,\n  );\n\nexport default ScrollViewNativeComponent;\n"], "mappings": ";;;;;AAgBA,IAAAA,uBAAA,GAAAC,uBAAA,CAAAC,OAAA;AACA,IAAAC,iBAAA,GAAAD,OAAA;AACA,IAAAE,SAAA,GAAAC,sBAAA,CAAAH,OAAA;AAAgD,SAAAI,yBAAAC,CAAA,6BAAAC,OAAA,mBAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,yBAAAC,CAAA,WAAAA,CAAA,GAAAG,CAAA,GAAAD,CAAA,KAAAF,CAAA;AAAA,SAAAN,wBAAAM,CAAA,EAAAE,CAAA,SAAAA,CAAA,IAAAF,CAAA,IAAAA,CAAA,CAAAI,UAAA,SAAAJ,CAAA,eAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,WAAAK,OAAA,EAAAL,CAAA,QAAAG,CAAA,GAAAJ,wBAAA,CAAAG,CAAA,OAAAC,CAAA,IAAAA,CAAA,CAAAG,GAAA,CAAAN,CAAA,UAAAG,CAAA,CAAAI,GAAA,CAAAP,CAAA,OAAAQ,CAAA,KAAAC,SAAA,UAAAC,CAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,CAAA,IAAAd,CAAA,oBAAAc,CAAA,OAAAC,cAAA,CAAAC,IAAA,CAAAhB,CAAA,EAAAc,CAAA,SAAAG,CAAA,GAAAP,CAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAb,CAAA,EAAAc,CAAA,UAAAG,CAAA,KAAAA,CAAA,CAAAV,GAAA,IAAAU,CAAA,CAAAC,GAAA,IAAAP,MAAA,CAAAC,cAAA,CAAAJ,CAAA,EAAAM,CAAA,EAAAG,CAAA,IAAAT,CAAA,CAAAM,CAAA,IAAAd,CAAA,CAAAc,CAAA,YAAAN,CAAA,CAAAH,OAAA,GAAAL,CAAA,EAAAG,CAAA,IAAAA,CAAA,CAAAe,GAAA,CAAAlB,CAAA,EAAAQ,CAAA,GAAAA,CAAA;AAEzC,IAAMW,sBAAyC,GAAAC,OAAA,CAAAD,sBAAA,GACpDE,iBAAQ,CAACC,EAAE,KAAK,SAAS,GACrB;EACEC,eAAe,EAAE,eAAe;EAChCC,kBAAkB,EAAE,CAAC,CAAC;EACtBC,gBAAgB,EAAE;IAChBC,sBAAsB,EAAE;MACtBC,gBAAgB,EAAE;IACpB,CAAC;IACDC,oBAAoB,EAAE;MACpBD,gBAAgB,EAAE;IACpB,CAAC;IACDE,SAAS,EAAE;MACTF,gBAAgB,EAAE;IACpB,CAAC;IACDG,kBAAkB,EAAE;MAClBH,gBAAgB,EAAE;IACpB,CAAC;IACDI,gBAAgB,EAAE;MAChBJ,gBAAgB,EAAE;IACpB;EACF,CAAC;EACDK,eAAe,EAAE;IACfC,aAAa,EAAE;MACbC,IAAI,EAAEvC,OAAO,CAAC,qCAAqC;IACrD,CAAC;IACDwC,gBAAgB,EAAE,IAAI;IACtBC,uBAAuB,EAAE,IAAI;IAC7BC,8BAA8B,EAAE,IAAI;IACpCC,aAAa,EAAE,IAAI;IACnBC,aAAa,EAAE,IAAI;IACnBC,4BAA4B,EAAE,IAAI;IAClCC,eAAe,EAAE,IAAI;IACrBC,SAAS,EAAE,IAAI;IACfC,cAAc,EAAE,IAAI;IACpBC,aAAa,EAAE,IAAI;IACnBC,WAAW,EAAE,IAAI;IACjBC,sBAAsB,EAAE,IAAI;IAC5BC,uBAAuB,EAAE,IAAI;IAC7BC,kBAAkB,EAAE,IAAI;IACxBC,YAAY,EAAE,IAAI;IAClBC,mBAAmB,EAAE,IAAI;IACzBC,mBAAmB,EAAE,IAAI;IACzBC,WAAW,EAAE,IAAI;IACjBC,gBAAgB,EAAE;MAChBC,OAAO,EAAE3D,OAAO,CAAC,+BAA+B,CAAC,CAACU;IACpD,CAAC;IACDkD,WAAW,EAAE;MACXD,OAAO,EAAE3D,OAAO,CAAC,+BAA+B,CAAC,CAACU;IACpD,CAAC;IACDmD,iBAAiB,EAAE;MACjBF,OAAO,EAAE3D,OAAO,CAAC,+BAA+B,CAAC,CAACU;IACpD,CAAC;IACDoD,mBAAmB,EAAE,IAAI;IACzBC,UAAU,EAAE,IAAI;IAChBC,YAAY,EAAE;MACZL,OAAO,EAAE3D,OAAO,CAAC,+BAA+B,CAAC,CAACU;IACpD,CAAC;IACDuD,gBAAgB,EAAE,IAAI;IACtBC,cAAc,EAAE,IAAI;IACpBC,mBAAmB,EAAE,IAAI;IACzBC,aAAa,EAAE,IAAI;IACnBC,cAAc,EAAE;MACdV,OAAO,EAAE3D,OAAO,CAAC,+BAA+B,CAAC,CAACU;IACpD,CAAC;IACD4D,qBAAqB,EAAE,IAAI;IAC3BC,oBAAoB,EAAE,IAAI;IAC1BC,eAAe,EAAE;MACfb,OAAO,EAAE3D,OAAO,CAAC,+BAA+B,CAAC,CAACU;IACpD,CAAC;IACD+D,aAAa,EAAE,IAAI;IACnBC,yBAAyB,EAAE;EAC7B;AACF,CAAC,GACD;EACE9C,eAAe,EAAE,eAAe;EAChCC,kBAAkB,EAAE,CAAC,CAAC;EACtBC,gBAAgB,EAAE;IAChBC,sBAAsB,EAAE;MACtBC,gBAAgB,EAAE;IACpB,CAAC;IACDC,oBAAoB,EAAE;MACpBD,gBAAgB,EAAE;IACpB,CAAC;IACDE,SAAS,EAAE;MACTF,gBAAgB,EAAE;IACpB,CAAC;IACDG,kBAAkB,EAAE;MAClBH,gBAAgB,EAAE;IACpB,CAAC;IACDI,gBAAgB,EAAE;MAChBJ,gBAAgB,EAAE;IACpB,CAAC;IACD2C,cAAc,EAAE;MACd3C,gBAAgB,EAAE;IACpB;EACF,CAAC;EACDK,eAAe,EAAArB,MAAA,CAAA4D,MAAA;IACbC,sBAAsB,EAAE,IAAI;IAC5BC,oBAAoB,EAAE,IAAI;IAC1BC,gCAAgC,EAAE,IAAI;IACtCC,iCAAiC,EAAE,IAAI;IACvCC,yCAAyC,EAAE,IAAI;IAC/CC,OAAO,EAAE,IAAI;IACbC,WAAW,EAAE,IAAI;IACjBC,uBAAuB,EAAE,IAAI;IAC7BC,aAAa,EAAE,IAAI;IACnBC,YAAY,EAAE;MACZ/C,IAAI,EAAEvC,OAAO,CAAC,qCAAqC;IACrD,CAAC;IACDsC,aAAa,EAAE;MACbC,IAAI,EAAEvC,OAAO,CAAC,qCAAqC;IACrD,CAAC;IACDuF,8BAA8B,EAAE,IAAI;IACpC/C,gBAAgB,EAAE,IAAI;IACtBgD,gCAAgC,EAAE,IAAI;IACtCC,sBAAsB,EAAE,IAAI;IAC5BhD,uBAAuB,EAAE,IAAI;IAC7BiD,cAAc,EAAE,IAAI;IACpBC,QAAQ,EAAE,IAAI;IACdC,mBAAmB,EAAE,IAAI;IACzBlD,8BAA8B,EAAE,IAAI;IACpCmD,gBAAgB,EAAE,IAAI;IACtBC,gBAAgB,EAAE,IAAI;IACtBnD,aAAa,EAAE,IAAI;IACnBoD,mBAAmB,EAAE,IAAI;IACzBnD,aAAa,EAAE,IAAI;IACnBY,mBAAmB,EAAE,IAAI;IACzBwC,qBAAqB,EAAE;MACrBzD,IAAI,EAAEvC,OAAO,CAAC,qCAAqC;IACrD,CAAC;IACDiG,uBAAuB,EAAE,IAAI;IAC7BC,YAAY,EAAE,IAAI;IAClBC,8BAA8B,EAAE,IAAI;IACpCtD,4BAA4B,EAAE,IAAI;IAClCC,eAAe,EAAE,IAAI;IACrBC,SAAS,EAAE,IAAI;IACfC,cAAc,EAAE,IAAI;IACpBC,aAAa,EAAE,IAAI;IACnBC,WAAW,EAAE,IAAI;IACjBkD,6BAA6B,EAAE;MAC7B7D,IAAI,EAAEvC,OAAO,CAAC,qCAAqC;IACrD,CAAC;IACDqG,SAAS,EAAE;EAAI,GACZ,IAAAC,mDAAiC,EAAC;IACnCC,iBAAiB,EAAE,IAAI;IACvBC,mBAAmB,EAAE,IAAI;IACzBC,eAAe,EAAE,IAAI;IACrBC,qBAAqB,EAAE,IAAI;IAC3BC,aAAa,EAAE,IAAI;IACnBC,QAAQ,EAAE;EACZ,CAAC,CAAC;AAEN,CAAC;AAEP,IAAMC,yBAA+C,GACnD/G,uBAAuB,CAACc,GAAG,CACzB,eAAe,EACf;EAAA,OAAMY,sBAAsB;AAAA,CAC9B,CAAC;AAAC,IAAAsF,QAAA,GAAArF,OAAA,CAAAf,OAAA,GAEWmG,yBAAyB", "ignoreList": []}