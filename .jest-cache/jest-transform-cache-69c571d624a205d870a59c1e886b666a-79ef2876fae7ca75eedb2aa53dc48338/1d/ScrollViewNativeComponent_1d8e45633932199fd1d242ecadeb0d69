ecf0a48c63c4c092f782fda541c1bec8
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = exports.__INTERNAL_VIEW_CONFIG = void 0;
var NativeComponentRegistry = _interopRequireWildcard(require("../../NativeComponent/NativeComponentRegistry"));
var _ViewConfigIgnore = require("../../NativeComponent/ViewConfigIgnore");
var _Platform = _interopRequireDefault(require("../../Utilities/Platform"));
function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
var __INTERNAL_VIEW_CONFIG = exports.__INTERNAL_VIEW_CONFIG = _Platform.default.OS === 'android' ? {
  uiViewClassName: 'RCTScrollView',
  bubblingEventTypes: {},
  directEventTypes: {
    topMomentumScrollBegin: {
      registrationName: 'onMomentumScrollBegin'
    },
    topMomentumScrollEnd: {
      registrationName: 'onMomentumScrollEnd'
    },
    topScroll: {
      registrationName: 'onScroll'
    },
    topScrollBeginDrag: {
      registrationName: 'onScrollBeginDrag'
    },
    topScrollEndDrag: {
      registrationName: 'onScrollEndDrag'
    }
  },
  validAttributes: {
    contentOffset: {
      diff: require('../../Utilities/differ/pointsDiffer')
    },
    decelerationRate: true,
    disableIntervalMomentum: true,
    maintainVisibleContentPosition: true,
    pagingEnabled: true,
    scrollEnabled: true,
    showsVerticalScrollIndicator: true,
    snapToAlignment: true,
    snapToEnd: true,
    snapToInterval: true,
    snapToOffsets: true,
    snapToStart: true,
    borderBottomLeftRadius: true,
    borderBottomRightRadius: true,
    sendMomentumEvents: true,
    borderRadius: true,
    nestedScrollEnabled: true,
    scrollEventThrottle: true,
    borderStyle: true,
    borderRightColor: {
      process: require('../../StyleSheet/processColor').default
    },
    borderColor: {
      process: require('../../StyleSheet/processColor').default
    },
    borderBottomColor: {
      process: require('../../StyleSheet/processColor').default
    },
    persistentScrollbar: true,
    horizontal: true,
    endFillColor: {
      process: require('../../StyleSheet/processColor').default
    },
    fadingEdgeLength: true,
    overScrollMode: true,
    borderTopLeftRadius: true,
    scrollPerfTag: true,
    borderTopColor: {
      process: require('../../StyleSheet/processColor').default
    },
    removeClippedSubviews: true,
    borderTopRightRadius: true,
    borderLeftColor: {
      process: require('../../StyleSheet/processColor').default
    },
    pointerEvents: true,
    isInvertedVirtualizedList: true
  }
} : {
  uiViewClassName: 'RCTScrollView',
  bubblingEventTypes: {},
  directEventTypes: {
    topMomentumScrollBegin: {
      registrationName: 'onMomentumScrollBegin'
    },
    topMomentumScrollEnd: {
      registrationName: 'onMomentumScrollEnd'
    },
    topScroll: {
      registrationName: 'onScroll'
    },
    topScrollBeginDrag: {
      registrationName: 'onScrollBeginDrag'
    },
    topScrollEndDrag: {
      registrationName: 'onScrollEndDrag'
    },
    topScrollToTop: {
      registrationName: 'onScrollToTop'
    }
  },
  validAttributes: Object.assign({
    alwaysBounceHorizontal: true,
    alwaysBounceVertical: true,
    automaticallyAdjustContentInsets: true,
    automaticallyAdjustKeyboardInsets: true,
    automaticallyAdjustsScrollIndicatorInsets: true,
    bounces: true,
    bouncesZoom: true,
    canCancelContentTouches: true,
    centerContent: true,
    contentInset: {
      diff: require('../../Utilities/differ/insetsDiffer')
    },
    contentOffset: {
      diff: require('../../Utilities/differ/pointsDiffer')
    },
    contentInsetAdjustmentBehavior: true,
    decelerationRate: true,
    endDraggingSensitivityMultiplier: true,
    directionalLockEnabled: true,
    disableIntervalMomentum: true,
    indicatorStyle: true,
    inverted: true,
    keyboardDismissMode: true,
    maintainVisibleContentPosition: true,
    maximumZoomScale: true,
    minimumZoomScale: true,
    pagingEnabled: true,
    pinchGestureEnabled: true,
    scrollEnabled: true,
    scrollEventThrottle: true,
    scrollIndicatorInsets: {
      diff: require('../../Utilities/differ/insetsDiffer')
    },
    scrollToOverflowEnabled: true,
    scrollsToTop: true,
    showsHorizontalScrollIndicator: true,
    showsVerticalScrollIndicator: true,
    snapToAlignment: true,
    snapToEnd: true,
    snapToInterval: true,
    snapToOffsets: true,
    snapToStart: true,
    verticalScrollIndicatorInsets: {
      diff: require('../../Utilities/differ/insetsDiffer')
    },
    zoomScale: true
  }, (0, _ViewConfigIgnore.ConditionallyIgnoredEventHandlers)({
    onScrollBeginDrag: true,
    onMomentumScrollEnd: true,
    onScrollEndDrag: true,
    onMomentumScrollBegin: true,
    onScrollToTop: true,
    onScroll: true
  }))
};
var ScrollViewNativeComponent = NativeComponentRegistry.get('RCTScrollView', function () {
  return __INTERNAL_VIEW_CONFIG;
});
var _default = exports.default = ScrollViewNativeComponent;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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