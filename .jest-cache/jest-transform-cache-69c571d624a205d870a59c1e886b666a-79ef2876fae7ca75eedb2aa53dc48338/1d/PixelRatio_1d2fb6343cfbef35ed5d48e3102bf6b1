5cb1e0ca6efbb80ca05aca8bc4f4e385
'use strict';

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _classCallCheck2 = _interopRequireDefault(require("@babel/runtime/helpers/classCallCheck"));
var _createClass2 = _interopRequireDefault(require("@babel/runtime/helpers/createClass"));
var Dimensions = require('./Dimensions').default;
var PixelRatio = function () {
  function PixelRatio() {
    (0, _classCallCheck2.default)(this, PixelRatio);
  }
  return (0, _createClass2.default)(PixelRatio, null, [{
    key: "get",
    value: function get() {
      return Dimensions.get('window').scale;
    }
  }, {
    key: "getFontScale",
    value: function getFontScale() {
      return Dimensions.get('window').fontScale || PixelRatio.get();
    }
  }, {
    key: "getPixelSizeForLayoutSize",
    value: function getPixelSizeForLayoutSize(layoutSize) {
      return Math.round(layoutSize * PixelRatio.get());
    }
  }, {
    key: "roundToNearestPixel",
    value: function roundToNearestPixel(layoutSize) {
      var ratio = PixelRatio.get();
      return Math.round(layoutSize * ratio) / ratio;
    }
  }, {
    key: "startDetecting",
    value: function startDetecting() {}
  }]);
}();
var _default = exports.default = PixelRatio;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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