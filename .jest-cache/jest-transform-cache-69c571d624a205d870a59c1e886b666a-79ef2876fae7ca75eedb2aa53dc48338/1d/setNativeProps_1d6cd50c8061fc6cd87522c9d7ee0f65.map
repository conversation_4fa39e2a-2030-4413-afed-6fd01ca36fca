{"version": 3, "names": ["Object", "defineProperty", "exports", "value", "setNativeProps", "_PlatformChecker", "require", "_Colors", "_index", "setNativePropsFabric", "animatedRef", "updates", "_WORKLET", "logger", "warn", "shadowNodeWrapper", "processColorsInProps", "global", "_updatePropsFabric", "setNativePropsPaper", "tag", "name", "viewName", "_updatePropsPaper", "setNativePropsJest", "setNativePropsChromeDebugger", "setNativePropsDefault", "shouldBeUseWeb", "isF<PERSON><PERSON>", "isJest", "isChromeDebugger"], "sources": ["../../../src/platformFunctions/setNativeProps.ts"], "sourcesContent": [null], "mappings": "AAAA,YAAY;;AAAAA,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,KAAA;AAAA;AAAAD,OAAA,CAAAE,cAAA;AAEZ,IAAAC,gBAAA,GAAAC,OAAA;AAYA,IAAAC,OAAA,GAAAD,OAAA;AACA,IAAAE,MAAA,GAAAF,OAAA;AAoBO,IAAIF,cAA8B;AAEzC,SAASK,oBAAoBA,CAC3BC,WAA8C,EAC9CC,OAAmB,EACnB;EACA,SAAS;;EACT,IAAI,CAACC,QAAQ,EAAE;IACbC,aAAM,CAACC,IAAI,CAAC,sDAAsD,CAAC;IACnE;EACF;EACA,IAAMC,iBAAiB,GAAGL,WAAW,CAAC,CAAsB;EAC5D,IAAAM,4BAAoB,EAACL,OAAO,CAAC;EAC7BM,MAAM,CAACC,kBAAkB,CAAE,CAAC;IAAEH,iBAAiB,EAAjBA,iBAAiB;IAAEJ,OAAA,EAAAA;EAAQ,CAAC,CAAC,CAAC;AAC9D;AAEA,SAASQ,mBAAmBA,CAC1BT,WAA8C,EAC9CC,OAAmB,EACnB;EACA,SAAS;;EACT,IAAI,CAACC,QAAQ,EAAE;IACbC,aAAM,CAACC,IAAI,CAAC,sDAAsD,CAAC;IACnE;EACF;EACA,IAAMM,GAAG,GAAGV,WAAW,CAAC,CAAW;EACnC,IAAMW,IAAI,GAAIX,WAAW,CAAqBY,QAAQ,CAACnB,KAAK;EAC5D,IAAAa,4BAAoB,EAACL,OAAO,CAAC;EAC7BM,MAAM,CAACM,iBAAiB,CAAE,CAAC;IAAEH,GAAG,EAAHA,GAAG;IAAEC,IAAI,EAAJA,IAAI;IAAEV,OAAA,EAAAA;EAAQ,CAAC,CAAC,CAAC;AACrD;AAEA,SAASa,kBAAkBA,CAAA,EAAG;EAC5BX,aAAM,CAACC,IAAI,CAAC,8CAA8C,CAAC;AAC7D;AAEA,SAASW,4BAA4BA,CAAA,EAAG;EACtCZ,aAAM,CAACC,IAAI,CAAC,yDAAyD,CAAC;AACxE;AAEA,SAASY,qBAAqBA,CAAA,EAAG;EAC/Bb,aAAM,CAACC,IAAI,CAAC,0DAA0D,CAAC;AACzE;AAEA,IAAI,CAAC,IAAAa,+BAAc,EAAC,CAAC,EAAE;EAIrB,IAAI,IAAAC,yBAAQ,EAAC,CAAC,EAAE;IACd1B,OAAA,CAAAE,cAAA,GAAAA,cAAc,GAAGK,oBAAiD;EACpE,CAAC,MAAM;IACLP,OAAA,CAAAE,cAAA,GAAAA,cAAc,GAAGe,mBAAgD;EACnE;AACF,CAAC,MAAM,IAAI,IAAAU,uBAAM,EAAC,CAAC,EAAE;EACnB3B,OAAA,CAAAE,cAAA,GAAAA,cAAc,GAAGoB,kBAAkB;AACrC,CAAC,MAAM,IAAI,IAAAM,iCAAgB,EAAC,CAAC,EAAE;EAC7B5B,OAAA,CAAAE,cAAA,GAAAA,cAAc,GAAGqB,4BAA4B;AAC/C,CAAC,MAAM;EACLvB,OAAA,CAAAE,cAAA,GAAAA,cAAc,GAAGsB,qBAAqB;AACxC", "ignoreList": []}