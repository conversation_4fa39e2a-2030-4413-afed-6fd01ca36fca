{"version": 3, "names": ["_interopRequireDefault", "require", "Object", "defineProperty", "exports", "value", "makeShareable", "makeShareableCloneOnUIRecursive", "makeShareableCloneRecursive", "_slicedToArray2", "_NativeReanimated", "_commonTypes", "_PlatformChecker", "_errors", "_jsVersion", "_shareableMappingCache", "_index", "SHOULD_BE_USE_WEB", "shouldBeUseWeb", "MAGIC_KEY", "isHostObject", "isPlainJSObject", "object", "getPrototypeOf", "prototype", "INACCESSIBLE_OBJECT", "__init", "Proxy", "get", "_", "prop", "ReanimatedError", "String", "set", "VALID_ARRAY_VIEWS_NAMES", "DETECT_CYCLIC_OBJECT_DEPTH_THRESHOLD", "processedObjectAtThresholdDepth", "shouldPersistRemote", "arguments", "length", "undefined", "depth", "type", "isTypeObject", "isTypeFunction", "cached", "shareableMappingCache", "shareableMappingFlag", "toAdapt", "Array", "isArray", "map", "element", "freezeObjectIfDev", "isWorkletFunction", "__workletContextObjectFactory", "workletContextObjectFactory", "handle", "__DEV__", "babelVersion", "__initData", "version", "jsVersion", "getWorkletCode", "registerWorkletStackDetails", "__workletHash", "__stackDetails", "_ref", "entries", "_ref2", "default", "key", "RegExp", "pattern", "source", "flags", "Error", "name", "message", "stack", "error", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "buffer", "typeName", "constructor", "includes", "global", "inaccessibleObject", "adapted", "NativeReanimatedModule", "makeShareableClone", "WORKLET_CODE_THRESHOLD", "_value$__initData", "code", "substring", "isRemoteFunction", "__remoteFunction", "for<PERSON>ach", "_ref3", "_ref4", "descriptor", "getOwnPropertyDescriptor", "configurable", "logger", "warn", "preventExtensions", "cloneRecursive", "_makeShareableClone", "_ref5", "_ref6", "makeShareableJS", "makeShareableNative"], "sources": ["../../src/shareables.ts"], "sourcesContent": [null], "mappings": "AAAA,YAAY;;AAAA,IAAAA,sBAAA,GAAAC,OAAA;AAAAC,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,KAAA;AAAA;AAAAD,OAAA,CAAAE,aAAA;AAAAF,OAAA,CAAAG,+BAAA,GAAAA,+BAAA;AAAAH,OAAA,CAAAI,2BAAA,GAAAA,2BAAA;AAAA,IAAAC,eAAA,GAAAT,sBAAA,CAAAC,OAAA;AACZ,IAAAS,iBAAA,GAAAV,sBAAA,CAAAC,OAAA;AACA,IAAAU,YAAA,GAAAV,OAAA;AAMA,IAAAW,gBAAA,GAAAX,OAAA;AACA,IAAAY,OAAA,GAAAZ,OAAA;AACA,IAAAa,UAAA,GAAAb,OAAA;AACA,IAAAc,sBAAA,GAAAd,OAAA;AAIA,IAAAe,MAAA,GAAAf,OAAA;AAMA,IAAMgB,iBAAiB,GAAG,IAAAC,+BAAc,EAAC,CAAC;AAE1C,IAAMC,SAAS,GAAG,sBAAsB;AAExC,SAASC,YAAYA,CAACf,KAA0B,EAAE;EAChD,SAAS;EAKT,OAAOc,SAAS,IAAId,KAAK;AAC3B;AAEA,SAASgB,eAAeA,CAACC,MAAc,EAAE;EACvC,OAAOpB,MAAM,CAACqB,cAAc,CAACD,MAAM,CAAC,KAAKpB,MAAM,CAACsB,SAAS;AAC3D;AASA,IAAMC,mBAAmB,GAAG;EAC1BC,MAAM,EAAE,SAARA,MAAMA,CAAA,EAAQ;IACZ,SAAS;;IACT,OAAO,IAAIC,KAAK,CACd,CAAC,CAAC,EACF;MACEC,GAAG,EAAE,SAALA,GAAGA,CAAGC,CAAU,EAAEC,IAAqB,EAAK;QAC1C,IACEA,IAAI,KAAK,0BAA0B,IACnCA,IAAI,KAAK,kBAAkB,EAC3B;UASA,OAAO,KAAK;QACd;QACA,MAAM,IAAIC,uBAAe,CACvB,+BAA+BC,MAAM,CACnCF,IACF,CAAC,yDACH,CAAC;MACH,CAAC;MACDG,GAAG,EAAE,SAALA,GAAGA,CAAA,EAAQ;QACT,MAAM,IAAIF,uBAAe,CACvB,sEACF,CAAC;MACH;IACF,CACF,CAAC;EACH;AACF,CAAC;AAED,IAAMG,uBAAuB,GAAG,CAC9B,WAAW,EACX,YAAY,EACZ,mBAAmB,EACnB,YAAY,EACZ,aAAa,EACb,YAAY,EACZ,aAAa,EACb,cAAc,EACd,cAAc,EACd,eAAe,EACf,gBAAgB,EAChB,UAAU,CACX;AAED,IAAMC,oCAAoC,GAAG,EAAE;AAG/C,IAAIC,+BAAwC;AAErC,SAAS5B,2BAA2BA,CACzCH,KAAU,EAGO;EAAA,IAFjBgC,mBAAmB,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,KAAK;EAAA,IAC3BG,KAAK,GAAAH,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC;EAET,IAAIrB,iBAAiB,EAAE;IACrB,OAAOZ,KAAK;EACd;EACA,IAAIoC,KAAK,IAAIN,oCAAoC,EAAE;IAMjD,IAAIM,KAAK,KAAKN,oCAAoC,EAAE;MAClDC,+BAA+B,GAAG/B,KAAK;IACzC,CAAC,MAAM,IAAIA,KAAK,KAAK+B,+BAA+B,EAAE;MACpD,MAAM,IAAIL,uBAAe,CACvB,0EACF,CAAC;IACH;EACF,CAAC,MAAM;IACLK,+BAA+B,GAAGI,SAAS;EAC7C;EAEA,IAAME,IAAI,GAAG,OAAOrC,KAAK;EACzB,IAAMsC,YAAY,GAAGD,IAAI,KAAK,QAAQ;EACtC,IAAME,cAAc,GAAGF,IAAI,KAAK,UAAU;EAC1C,IAAI,CAACC,YAAY,IAAIC,cAAc,KAAKvC,KAAK,KAAK,IAAI,EAAE;IACtD,IAAMwC,MAAM,GAAGC,4CAAqB,CAAClB,GAAG,CAACvB,KAAK,CAAC;IAC/C,IAAIwC,MAAM,KAAKE,2CAAoB,EAAE;MACnC,OAAO1C,KAAK;IACd,CAAC,MAAM,IAAIwC,MAAM,KAAKL,SAAS,EAAE;MAC/B,OAAOK,MAAM;IACf,CAAC,MAAM;MACL,IAAIG,OAAY;MAChB,IAAIC,KAAK,CAACC,OAAO,CAAC7C,KAAK,CAAC,EAAE;QACxB2C,OAAO,GAAG3C,KAAK,CAAC8C,GAAG,CAAE,UAAAC,OAAO;UAAA,OAC1B5C,2BAA2B,CAAC4C,OAAO,EAAEf,mBAAmB,EAAEI,KAAK,GAAG,CAAC,CACrE;QAAA,EAAC;QACDY,iBAAiB,CAAChD,KAAK,CAAC;MAC1B,CAAC,MAAM,IAAIuC,cAAc,IAAI,CAAC,IAAAU,8BAAiB,EAACjD,KAAK,CAAC,EAAE;QAEtD2C,OAAO,GAAG3C,KAAK;QACfgD,iBAAiB,CAAChD,KAAK,CAAC;MAC1B,CAAC,MAAM,IAAIe,YAAY,CAACf,KAAK,CAAC,EAAE;QAI9B2C,OAAO,GAAG3C,KAAK;MACjB,CAAC,MAAM,IACLgB,eAAe,CAAChB,KAAK,CAAC,IACtBA,KAAK,CAACkD,6BAA6B,EACnC;QACA,IAAMC,2BAA2B,GAAGnD,KAAK,CAACkD,6BAA6B;QACvE,IAAME,MAAM,GAAGjD,2BAA2B,CAAC;UACzCkB,MAAM,EAAE,SAARA,MAAMA,CAAA,EAAQ;YACZ,SAAS;;YACT,OAAO8B,2BAA2B,CAAC,CAAC;UACtC;QACF,CAAC,CAAC;QACFV,4CAAqB,CAACb,GAAG,CAAC5B,KAAK,EAAEoD,MAAM,CAAC;QACxC,OAAOA,MAAM;MACf,CAAC,MAAM,IAAIpC,eAAe,CAAChB,KAAK,CAAC,IAAIuC,cAAc,EAAE;QACnDI,OAAO,GAAG,CAAC,CAAC;QACZ,IAAI,IAAAM,8BAAiB,EAACjD,KAAK,CAAC,EAAE;UAC5B,IAAIqD,OAAO,EAAE;YACX,IAAMC,YAAY,GAAGtD,KAAK,CAACuD,UAAU,CAACC,OAAO;YAC7C,IAAIF,YAAY,KAAKnB,SAAS,IAAImB,YAAY,KAAKG,oBAAS,EAAE;cAC5D,MAAM,IAAI/B,uBAAe,CAAC,iFAAiF+B,oBAAS,QAAQH,YAAY;AACtJ;AACA,wBAAwBI,cAAc,CAAC1D,KAAK,CAAC,IAAI,CAAC;YACtC;YACA,IAAA2D,mCAA2B,EACzB3D,KAAK,CAAC4D,aAAa,EACnB5D,KAAK,CAAC6D,cACR,CAAC;UACH;UACA,IAAI7D,KAAK,CAAC6D,cAAc,EAAE;YAKxB,OAAO7D,KAAK,CAAC6D,cAAc;UAC7B;UAMAlB,OAAO,CAACY,UAAU,GAAGpD,2BAA2B,CAC9CH,KAAK,CAACuD,UAAU,EAChB,IAAI,EACJnB,KAAK,GAAG,CACV,CAAC;QACH;QAEA,SAAA0B,IAAA,IAA6BjE,MAAM,CAACkE,OAAO,CAAC/D,KAAK,CAAC,EAAE;UAAA,IAAAgE,KAAA,OAAA5D,eAAA,CAAA6D,OAAA,EAAAH,IAAA;UAAA,IAAxCI,GAAG,GAAAF,KAAA;UAAA,IAAEjB,OAAO,GAAAiB,KAAA;UACtB,IAAIE,GAAG,KAAK,YAAY,IAAIvB,OAAO,CAACY,UAAU,KAAKpB,SAAS,EAAE;YAC5D;UACF;UACAQ,OAAO,CAACuB,GAAG,CAAC,GAAG/D,2BAA2B,CACxC4C,OAAO,EACPf,mBAAmB,EACnBI,KAAK,GAAG,CACV,CAAC;QACH;QACAY,iBAAiB,CAAChD,KAAK,CAAC;MAC1B,CAAC,MAAM,IAAIA,KAAK,YAAYmE,MAAM,EAAE;QAClC,IAAMC,OAAO,GAAGpE,KAAK,CAACqE,MAAM;QAC5B,IAAMC,KAAK,GAAGtE,KAAK,CAACsE,KAAK;QACzB,IAAMlB,OAAM,GAAGjD,2BAA2B,CAAC;UACzCkB,MAAM,EAAE,SAARA,MAAMA,CAAA,EAAQ;YACZ,SAAS;;YACT,OAAO,IAAI8C,MAAM,CAACC,OAAO,EAAEE,KAAK,CAAC;UACnC;QACF,CAAC,CAAC;QACF7B,4CAAqB,CAACb,GAAG,CAAC5B,KAAK,EAAEoD,OAAM,CAAC;QACxC,OAAOA,OAAM;MACf,CAAC,MAAM,IAAIpD,KAAK,YAAYuE,KAAK,EAAE;QACjC,IAAQC,IAAI,GAAqBxE,KAAK,CAA9BwE,IAAI;UAAEC,OAAO,GAAYzE,KAAK,CAAxByE,OAAO;UAAEC,KAAA,GAAU1E,KAAK,CAAf0E,KAAA;QACvB,IAAMtB,QAAM,GAAGjD,2BAA2B,CAAC;UACzCkB,MAAM,EAAE,SAARA,MAAMA,CAAA,EAAQ;YACZ,SAAS;YAET,IAAMsD,KAAK,GAAG,IAAIJ,KAAK,CAAC,CAAC;YACzBI,KAAK,CAACH,IAAI,GAAGA,IAAI;YACjBG,KAAK,CAACF,OAAO,GAAGA,OAAO;YACvBE,KAAK,CAACD,KAAK,GAAGA,KAAK;YACnB,OAAOC,KAAK;UACd;QACF,CAAC,CAAC;QACFlC,4CAAqB,CAACb,GAAG,CAAC5B,KAAK,EAAEoD,QAAM,CAAC;QACxC,OAAOA,QAAM;MACf,CAAC,MAAM,IAAIpD,KAAK,YAAY4E,WAAW,EAAE;QACvCjC,OAAO,GAAG3C,KAAK;MACjB,CAAC,MAAM,IAAI4E,WAAW,CAACC,MAAM,CAAC7E,KAAK,CAAC,EAAE;QAEpC,IAAM8E,MAAM,GAAG9E,KAAK,CAAC8E,MAAM;QAC3B,IAAMC,QAAQ,GAAG/E,KAAK,CAACgF,WAAW,CAACR,IAAI;QACvC,IAAMpB,QAAM,GAAGjD,2BAA2B,CAAC;UACzCkB,MAAM,EAAE,SAARA,MAAMA,CAAA,EAAQ;YACZ,SAAS;;YACT,IAAI,CAACQ,uBAAuB,CAACoD,QAAQ,CAACF,QAAQ,CAAC,EAAE;cAC/C,MAAM,IAAIrD,uBAAe,CACvB,6BAA6BqD,QAAQ,KACvC,CAAC;YACH;YACA,IAAMC,WAAW,GAAGE,MAAM,CAACH,QAAQ,CAAwB;YAC3D,IAAIC,WAAW,KAAK7C,SAAS,EAAE;cAC7B,MAAM,IAAIT,uBAAe,CACvB,qBAAqBqD,QAAQ,eAC/B,CAAC;YACH;YACA,OAAO,IAAIC,WAAW,CAACF,MAAM,CAAC;UAChC;QACF,CAAC,CAAC;QACFrC,4CAAqB,CAACb,GAAG,CAAC5B,KAAK,EAAEoD,QAAM,CAAC;QACxC,OAAOA,QAAM;MACf,CAAC,MAAM;QASL,IAAM+B,kBAAkB,GACtBhF,2BAA2B,CAAIiB,mBAAmB,CAAC;QACrDqB,4CAAqB,CAACb,GAAG,CAAC5B,KAAK,EAAEmF,kBAAkB,CAAC;QACpD,OAAOA,kBAAkB;MAC3B;MACA,IAAMC,OAAO,GAAGC,yBAAsB,CAACC,kBAAkB,CACvD3C,OAAO,EACPX,mBAAmB,EACnBhC,KACF,CAAC;MACDyC,4CAAqB,CAACb,GAAG,CAAC5B,KAAK,EAAEoF,OAAO,CAAC;MACzC3C,4CAAqB,CAACb,GAAG,CAACwD,OAAO,CAAC;MAClC,OAAOA,OAAO;IAChB;EACF;EACA,OAAOC,yBAAsB,CAACC,kBAAkB,CAC9CtF,KAAK,EACLgC,mBAAmB,EACnBG,SACF,CAAC;AACH;AAEA,IAAMoD,sBAAsB,GAAG,GAAG;AAElC,SAAS7B,cAAcA,CAAC1D,KAAsB,EAAE;EAAA,IAAAwF,iBAAA;EAE9C,IAAMC,IAAI,GAAGzF,KAAK,aAAAwF,iBAAA,GAALxF,KAAK,CAAEuD,UAAU,qBAAjBiC,iBAAA,CAAmBC,IAAI;EACpC,IAAI,CAACA,IAAI,EAAE;IACT,OAAO,SAAS;EAClB;EACA,IAAIA,IAAI,CAACvD,MAAM,GAAGqD,sBAAsB,EAAE;IACxC,OAAO,GAAGE,IAAI,CAACC,SAAS,CAAC,CAAC,EAAEH,sBAAsB,CAAC,KAAK;EAC1D;EACA,OAAOE,IAAI;AACb;AAMA,SAASE,gBAAgBA,CAAI3F,KAE5B,EAA8B;EAC7B,SAAS;;EACT,OAAO,CAAC,CAACA,KAAK,CAAC4F,gBAAgB;AACjC;AAgBA,SAAS5C,iBAAiBA,CAAmBhD,KAAQ,EAAE;EACrD,IAAI,CAACqD,OAAO,EAAE;IACZ;EACF;EACAxD,MAAM,CAACkE,OAAO,CAAC/D,KAAK,CAAC,CAAC6F,OAAO,CAAC,UAAAC,KAAA,EAAoB;IAAA,IAAAC,KAAA,OAAA3F,eAAA,CAAA6D,OAAA,EAAA6B,KAAA;MAAlB5B,GAAG,GAAA6B,KAAA;MAAEhD,OAAO,GAAAgD,KAAA;IAC1C,IAAMC,UAAU,GAAGnG,MAAM,CAACoG,wBAAwB,CAACjG,KAAK,EAAEkE,GAAG,CAAE;IAC/D,IAAI,CAAC8B,UAAU,CAACE,YAAY,EAAE;MAC5B;IACF;IACArG,MAAM,CAACC,cAAc,CAACE,KAAK,EAAEkE,GAAG,EAAE;MAChC3C,GAAG,WAAHA,GAAGA,CAAA,EAAG;QACJ,OAAOwB,OAAO;MAChB,CAAC;MACDnB,GAAG,WAAHA,GAAGA,CAAA,EAAG;QACJuE,aAAM,CAACC,IAAI,CACT,yBAAyBlC,GAAG;AACtC;AACA,kBACQ,CAAC;MACH;IACF,CAAC,CAAC;EACJ,CAAC,CAAC;EACFrE,MAAM,CAACwG,iBAAiB,CAACrG,KAAK,CAAC;AACjC;AAEO,SAASE,+BAA+BA,CAC7CF,KAAQ,EACa;EACrB,SAAS;;EACT,IAAIY,iBAAiB,EAAE;IAGrB,OAAOZ,KAAK;EACd;EAEA,SAASsG,cAAcA,CAACtG,KAAQ,EAAuB;IACrD,IACG,OAAOA,KAAK,KAAK,QAAQ,IAAIA,KAAK,KAAK,IAAI,IAC5C,OAAOA,KAAK,KAAK,UAAU,EAC3B;MACA,IAAIe,YAAY,CAACf,KAAK,CAAC,EAAE;QAGvB,OAAOkF,MAAM,CAACqB,mBAAmB,CAC/BvG,KAAK,EACLmC,SACF,CAAC;MACH;MACA,IAAIwD,gBAAgB,CAAI3F,KAAK,CAAC,EAAE;QAI9B,OAAOA,KAAK,CAAC4F,gBAAgB;MAC/B;MACA,IAAIhD,KAAK,CAACC,OAAO,CAAC7C,KAAK,CAAC,EAAE;QACxB,OAAOkF,MAAM,CAACqB,mBAAmB,CAC/BvG,KAAK,CAAC8C,GAAG,CAACwD,cAAc,CAAC,EACzBnE,SACF,CAAC;MACH;MACA,IAAMQ,OAA4C,GAAG,CAAC,CAAC;MACvD,SAAA6D,KAAA,IAA6B3G,MAAM,CAACkE,OAAO,CAAC/D,KAAK,CAAC,EAAE;QAAA,IAAAyG,KAAA,OAAArG,eAAA,CAAA6D,OAAA,EAAAuC,KAAA;QAAA,IAAxCtC,GAAG,GAAAuC,KAAA;QAAA,IAAE1D,OAAO,GAAA0D,KAAA;QACtB9D,OAAO,CAACuB,GAAG,CAAC,GAAGoC,cAAc,CAACvD,OAAO,CAAC;MACxC;MACA,OAAOmC,MAAM,CAACqB,mBAAmB,CAAC5D,OAAO,EAAE3C,KAAK,CAAC;IACnD;IACA,OAAOkF,MAAM,CAACqB,mBAAmB,CAACvG,KAAK,EAAEmC,SAAS,CAAC;EACrD;EACA,OAAOmE,cAAc,CAACtG,KAAK,CAAC;AAC9B;AAEA,SAAS0G,eAAeA,CAAmB1G,KAAQ,EAAK;EACtD,OAAOA,KAAK;AACd;AAEA,SAAS2G,mBAAmBA,CAAmB3G,KAAQ,EAAK;EAC1D,IAAIyC,4CAAqB,CAAClB,GAAG,CAACvB,KAAK,CAAC,EAAE;IACpC,OAAOA,KAAK;EACd;EACA,IAAMoD,MAAM,GAAGjD,2BAA2B,CAAC;IACzCkB,MAAM,EAAE,SAARA,MAAMA,CAAA,EAAQ;MACZ,SAAS;;MACT,OAAOrB,KAAK;IACd;EACF,CAAC,CAAC;EACFyC,4CAAqB,CAACb,GAAG,CAAC5B,KAAK,EAAEoD,MAAM,CAAC;EACxC,OAAOpD,KAAK;AACd;AAOO,IAAMC,aAAa,GAAAF,OAAA,CAAAE,aAAA,GAAGW,iBAAiB,GAC1C8F,eAAe,GACfC,mBAAmB", "ignoreList": []}