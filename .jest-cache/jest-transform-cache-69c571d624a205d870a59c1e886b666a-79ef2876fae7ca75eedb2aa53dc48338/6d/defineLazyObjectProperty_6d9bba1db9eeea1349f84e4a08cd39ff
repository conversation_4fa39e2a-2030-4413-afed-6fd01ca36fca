5dd981003f8370cc214ebc0b81c956cc
'use strict';

function defineLazyObjectProperty(object, name, descriptor) {
  var get = descriptor.get;
  var enumerable = descriptor.enumerable !== false;
  var writable = descriptor.writable !== false;
  var value;
  var valueSet = false;
  function getValue() {
    if (!valueSet) {
      valueSet = true;
      setValue(get());
    }
    return value;
  }
  function setValue(newValue) {
    value = newValue;
    valueSet = true;
    Object.defineProperty(object, name, {
      value: newValue,
      configurable: true,
      enumerable: enumerable,
      writable: writable
    });
  }
  Object.defineProperty(object, name, {
    get: getValue,
    set: setValue,
    configurable: true,
    enumerable: enumerable
  });
}
module.exports = defineLazyObjectProperty;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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