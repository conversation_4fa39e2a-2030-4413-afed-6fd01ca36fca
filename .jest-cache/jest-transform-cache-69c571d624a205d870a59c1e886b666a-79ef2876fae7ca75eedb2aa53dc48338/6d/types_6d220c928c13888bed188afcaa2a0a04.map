{"version": 3, "names": [], "sources": ["/Users/<USER>/projects/react-native/payment-module/src/components/provider-selection/types.ts"], "sourcesContent": ["import {StyleProp, ViewStyle} from 'react-native';\nimport {ProviderModel} from '../../domain/entities/provider-list/ProviderListModel';\n\nexport type ProviderSelectionState = {\n  providerSelected?: ProviderModel;\n};\n\nexport type ProviderSelectionProps = {\n  rootStyle?: StyleProp<ViewStyle>;\n  code: string;\n  disabled?: boolean;\n  defaultValue?: ProviderSelectionState;\n  onSelected: (value: ProviderModel) => void;\n};\n\nexport type ProviderSelectionRef = {\n  resetSelected: () => void;\n  isBottomSheetOpen: boolean;\n};\n\nexport type ComponentState<T> = {\n  isLoading: boolean;\n  data: T | any;\n  error: any;\n};\n"], "mappings": "", "ignoreList": []}