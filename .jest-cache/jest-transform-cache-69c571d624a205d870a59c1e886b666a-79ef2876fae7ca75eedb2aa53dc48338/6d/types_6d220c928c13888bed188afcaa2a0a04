e877c6bacffab828a5a3437ae6807f7f
"use strict";

/* istanbul ignore next */
function cov_20ugeh62fj() {
  var path = "/Users/<USER>/projects/react-native/payment-module/src/components/provider-selection/types.ts";
  var hash = "063b950f9c93d86c1ca94c9f5d63a25e15f4b276";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/projects/react-native/payment-module/src/components/provider-selection/types.ts",
    statementMap: {
      "0": {
        start: {
          line: 3,
          column: 0
        },
        end: {
          line: 5,
          column: 3
        }
      }
    },
    fnMap: {},
    branchMap: {},
    s: {
      "0": 0
    },
    f: {},
    b: {},
    inputSourceMap: {
      version: 3,
      file: undefined,
      names: [],
      sourceRoot: undefined,
      sources: ["/Users/<USER>/projects/react-native/payment-module/src/components/provider-selection/types.ts"],
      sourcesContent: ["import {StyleProp, ViewStyle} from 'react-native';\nimport {ProviderModel} from '../../domain/entities/provider-list/ProviderListModel';\n\nexport type ProviderSelectionState = {\n  providerSelected?: ProviderModel;\n};\n\nexport type ProviderSelectionProps = {\n  rootStyle?: StyleProp<ViewStyle>;\n  code: string;\n  disabled?: boolean;\n  defaultValue?: ProviderSelectionState;\n  onSelected: (value: ProviderModel) => void;\n};\n\nexport type ProviderSelectionRef = {\n  resetSelected: () => void;\n  isBottomSheetOpen: boolean;\n};\n\nexport type ComponentState<T> = {\n  isLoading: boolean;\n  data: T | any;\n  error: any;\n};\n"],
      mappings: "",
      ignoreList: []
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "063b950f9c93d86c1ca94c9f5d63a25e15f4b276"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_20ugeh62fj = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_20ugeh62fj();
cov_20ugeh62fj().s[0]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6W10sInNvdXJjZXMiOlsiL1VzZXJzL3NvbnNhbXNldC9wcm9qZWN0cy9yZWFjdC1uYXRpdmUvcGF5bWVudC1tb2R1bGUvc3JjL2NvbXBvbmVudHMvcHJvdmlkZXItc2VsZWN0aW9uL3R5cGVzLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7U3R5bGVQcm9wLCBWaWV3U3R5bGV9IGZyb20gJ3JlYWN0LW5hdGl2ZSc7XG5pbXBvcnQge1Byb3ZpZGVyTW9kZWx9IGZyb20gJy4uLy4uL2RvbWFpbi9lbnRpdGllcy9wcm92aWRlci1saXN0L1Byb3ZpZGVyTGlzdE1vZGVsJztcblxuZXhwb3J0IHR5cGUgUHJvdmlkZXJTZWxlY3Rpb25TdGF0ZSA9IHtcbiAgcHJvdmlkZXJTZWxlY3RlZD86IFByb3ZpZGVyTW9kZWw7XG59O1xuXG5leHBvcnQgdHlwZSBQcm92aWRlclNlbGVjdGlvblByb3BzID0ge1xuICByb290U3R5bGU/OiBTdHlsZVByb3A8Vmlld1N0eWxlPjtcbiAgY29kZTogc3RyaW5nO1xuICBkaXNhYmxlZD86IGJvb2xlYW47XG4gIGRlZmF1bHRWYWx1ZT86IFByb3ZpZGVyU2VsZWN0aW9uU3RhdGU7XG4gIG9uU2VsZWN0ZWQ6ICh2YWx1ZTogUHJvdmlkZXJNb2RlbCkgPT4gdm9pZDtcbn07XG5cbmV4cG9ydCB0eXBlIFByb3ZpZGVyU2VsZWN0aW9uUmVmID0ge1xuICByZXNldFNlbGVjdGVkOiAoKSA9PiB2b2lkO1xuICBpc0JvdHRvbVNoZWV0T3BlbjogYm9vbGVhbjtcbn07XG5cbmV4cG9ydCB0eXBlIENvbXBvbmVudFN0YXRlPFQ+ID0ge1xuICBpc0xvYWRpbmc6IGJvb2xlYW47XG4gIGRhdGE6IFQgfCBhbnk7XG4gIGVycm9yOiBhbnk7XG59O1xuIl0sIm1hcHBpbmdzIjoiIiwiaWdub3JlTGlzdCI6W119