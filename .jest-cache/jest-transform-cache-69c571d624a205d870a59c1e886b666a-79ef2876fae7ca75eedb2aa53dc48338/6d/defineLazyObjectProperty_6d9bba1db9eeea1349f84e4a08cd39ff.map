{"version": 3, "names": ["defineLazyObjectProperty", "object", "name", "descriptor", "get", "enumerable", "writable", "value", "valueSet", "getValue", "setValue", "newValue", "Object", "defineProperty", "configurable", "set", "module", "exports"], "sources": ["defineLazyObjectProperty.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @format\n * @flow strict\n */\n\n'use strict';\n\n/**\n * Defines a lazily evaluated property on the supplied `object`.\n */\nfunction defineLazyObjectProperty<T>(\n  object: interface {},\n  name: string,\n  descriptor: {\n    get: () => T,\n    enumerable?: boolean,\n    writable?: boolean,\n    ...\n  },\n): void {\n  const {get} = descriptor;\n  const enumerable = descriptor.enumerable !== false;\n  const writable = descriptor.writable !== false;\n\n  let value;\n  let valueSet = false;\n  function getValue(): T {\n    // WORKAROUND: A weird infinite loop occurs where calling `getValue` calls\n    // `setValue` which calls `Object.defineProperty` which somehow triggers\n    // `getValue` again. Adding `valueSet` breaks this loop.\n    if (!valueSet) {\n      // Calling `get()` here can trigger an infinite loop if it fails to\n      // remove the getter on the property, which can happen when executing\n      // JS in a V8 context.  `valueSet = true` will break this loop, and\n      // sets the value of the property to undefined, until the code in `get()`\n      // finishes, at which point the property is set to the correct value.\n      valueSet = true;\n      setValue(get());\n    }\n    return value;\n  }\n  function setValue(newValue: T): void {\n    value = newValue;\n    valueSet = true;\n    Object.defineProperty(object, name, {\n      value: newValue,\n      configurable: true,\n      enumerable,\n      writable,\n    });\n  }\n\n  Object.defineProperty(object, name, {\n    get: getValue,\n    set: setValue,\n    configurable: true,\n    enumerable,\n  });\n}\n\nmodule.exports = defineLazyObjectProperty;\n"], "mappings": "AAUA,YAAY;;AAKZ,SAASA,wBAAwBA,CAC/BC,MAAoB,EACpBC,IAAY,EACZC,UAKC,EACK;EACN,IAAOC,GAAG,GAAID,UAAU,CAAjBC,GAAG;EACV,IAAMC,UAAU,GAAGF,UAAU,CAACE,UAAU,KAAK,KAAK;EAClD,IAAMC,QAAQ,GAAGH,UAAU,CAACG,QAAQ,KAAK,KAAK;EAE9C,IAAIC,KAAK;EACT,IAAIC,QAAQ,GAAG,KAAK;EACpB,SAASC,QAAQA,CAAA,EAAM;IAIrB,IAAI,CAACD,QAAQ,EAAE;MAMbA,QAAQ,GAAG,IAAI;MACfE,QAAQ,CAACN,GAAG,CAAC,CAAC,CAAC;IACjB;IACA,OAAOG,KAAK;EACd;EACA,SAASG,QAAQA,CAACC,QAAW,EAAQ;IACnCJ,KAAK,GAAGI,QAAQ;IAChBH,QAAQ,GAAG,IAAI;IACfI,MAAM,CAACC,cAAc,CAACZ,MAAM,EAAEC,IAAI,EAAE;MAClCK,KAAK,EAAEI,QAAQ;MACfG,YAAY,EAAE,IAAI;MAClBT,UAAU,EAAVA,UAAU;MACVC,QAAQ,EAARA;IACF,CAAC,CAAC;EACJ;EAEAM,MAAM,CAACC,cAAc,CAACZ,MAAM,EAAEC,IAAI,EAAE;IAClCE,GAAG,EAAEK,QAAQ;IACbM,GAAG,EAAEL,QAAQ;IACbI,YAAY,EAAE,IAAI;IAClBT,UAAU,EAAVA;EACF,CAAC,CAAC;AACJ;AAEAW,MAAM,CAACC,OAAO,GAAGjB,wBAAwB", "ignoreList": []}