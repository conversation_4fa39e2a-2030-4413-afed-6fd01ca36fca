1dc7aa5d7a09ca4accfd15eb47761716
'use strict';

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.TransitionType = exports.AnimationsData = exports.Animations = void 0;
var _BounceWeb = require("./animation/Bounce.web.js");
var _FadeWeb = require("./animation/Fade.web.js");
var _FlipWeb = require("./animation/Flip.web.js");
var _LightspeedWeb = require("./animation/Lightspeed.web.js");
var _PinwheelWeb = require("./animation/Pinwheel.web.js");
var _RollWeb = require("./animation/Roll.web.js");
var _RotateWeb = require("./animation/Rotate.web.js");
var _SlideWeb = require("./animation/Slide.web.js");
var _StretchWeb = require("./animation/Stretch.web.js");
var _ZoomWeb = require("./animation/Zoom.web.js");
var TransitionType = exports.TransitionType = function (TransitionType) {
  TransitionType[TransitionType["LINEAR"] = 0] = "LINEAR";
  TransitionType[TransitionType["SEQUENCED"] = 1] = "SEQUENCED";
  TransitionType[TransitionType["FADING"] = 2] = "FADING";
  TransitionType[TransitionType["JUMPING"] = 3] = "JUMPING";
  TransitionType[TransitionType["CURVED"] = 4] = "CURVED";
  TransitionType[TransitionType["ENTRY_EXIT"] = 5] = "ENTRY_EXIT";
  return TransitionType;
}({});
var AnimationsData = exports.AnimationsData = Object.assign({}, _FadeWeb.FadeInData, _FadeWeb.FadeOutData, _BounceWeb.BounceInData, _BounceWeb.BounceOutData, _FlipWeb.FlipInData, _FlipWeb.FlipOutData, _StretchWeb.StretchInData, _StretchWeb.StretchOutData, _ZoomWeb.ZoomInData, _ZoomWeb.ZoomOutData, _SlideWeb.SlideInData, _SlideWeb.SlideOutData, _LightspeedWeb.LightSpeedInData, _LightspeedWeb.LightSpeedOutData, _PinwheelWeb.PinwheelData, _RotateWeb.RotateInData, _RotateWeb.RotateOutData, _RollWeb.RollInData, _RollWeb.RollOutData);
var Animations = exports.Animations = Object.assign({}, _FadeWeb.FadeIn, _FadeWeb.FadeOut, _BounceWeb.BounceIn, _BounceWeb.BounceOut, _FlipWeb.FlipIn, _FlipWeb.FlipOut, _StretchWeb.StretchIn, _StretchWeb.StretchOut, _ZoomWeb.ZoomIn, _ZoomWeb.ZoomOut, _SlideWeb.SlideIn, _SlideWeb.SlideOut, _LightspeedWeb.LightSpeedIn, _LightspeedWeb.LightSpeedOut, _PinwheelWeb.Pinwheel, _RotateWeb.RotateIn, _RotateWeb.RotateOut, _RollWeb.RollIn, _RollWeb.RollOut);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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