ef8165fd3e92270fb35f3f9e809e3162
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = exports.__INTERNAL_VIEW_CONFIG = void 0;
var _codegenNativeComponent = _interopRequireDefault(require("../../../../Libraries/Utilities/codegenNativeComponent"));
var NativeComponentRegistry = require('react-native/Libraries/NativeComponent/NativeComponentRegistry');
var nativeComponentName = 'AndroidHorizontalScrollContentView';
var __INTERNAL_VIEW_CONFIG = exports.__INTERNAL_VIEW_CONFIG = {
  uiViewClassName: 'AndroidHorizontalScrollContentView',
  validAttributes: {
    removeClippedSubviews: true
  }
};
var _default = exports.default = NativeComponentRegistry.get(nativeComponentName, function () {
  return __INTERNAL_VIEW_CONFIG;
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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