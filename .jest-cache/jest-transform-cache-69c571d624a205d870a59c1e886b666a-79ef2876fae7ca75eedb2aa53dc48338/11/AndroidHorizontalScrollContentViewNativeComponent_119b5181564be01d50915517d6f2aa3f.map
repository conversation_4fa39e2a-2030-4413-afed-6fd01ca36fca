{"version": 3, "names": ["_codegenNativeComponent", "_interopRequireDefault", "require", "NativeComponentRegistry", "nativeComponentName", "__INTERNAL_VIEW_CONFIG", "exports", "uiViewClassName", "validAttributes", "removeClippedSubviews", "_default", "default", "get"], "sources": ["AndroidHorizontalScrollContentViewNativeComponent.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @format\n * @flow strict-local\n */\n\nimport type {ViewProps} from '../../../../Libraries/Components/View/ViewPropTypes';\nimport type {HostComponent} from '../../../../Libraries/Renderer/shims/ReactNativeTypes';\n\nimport codegenNativeComponent from '../../../../Libraries/Utilities/codegenNativeComponent';\n\ntype NativeProps = $ReadOnly<{|\n  ...ViewProps,\n\n  removeClippedSubviews?: ?boolean,\n|}>;\n\ntype NativeType = HostComponent<NativeProps>;\n\nexport default (codegenNativeComponent<NativeProps>(\n  'AndroidHorizontalScrollContentView',\n  {interfaceOnly: true},\n): NativeType);\n"], "mappings": ";;;;;AAaA,IAAAA,uBAAA,GAAAC,sBAAA,CAAAC,OAAA;AAUA,IAAAC,uBAGe,GAHfD,OAGe,CAHf,gEAGc,CAAC;AAHf,IAAAE,mBAGe,GAHf,oCAGe;AAHf,IAAAC,sBAGe,GAAAC,OAAA,CAAAD,sBAAA,GAHf;EAAAE,eAGe,EAHf,oCAGe;EAHfC,eAGe,EAHf;IAAAC,qBAGe,EAHf;EAGc;AAAA,CAAC;AAAA,IAAAC,QAAA,GAAAJ,OAAA,CAAAK,OAAA,GAHfR,uBAGe,CAHfS,GAGe,CAHfR,mBAGe,EAHf;EAAA,OAAAC,sBAGe;AAAA,CAAD,CAAC", "ignoreList": []}