4c12da4266476810d8c87a67c24bb5ea
"use strict";

/* istanbul ignore next */
function cov_yr9cs0go4() {
  var path = "/Users/<USER>/projects/react-native/payment-module/src/domain/repositories/IPaymentOrderRepository.ts";
  var hash = "6f2cd65ad0ddf9395a04cd7b17494b5f7b9c75df";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/projects/react-native/payment-module/src/domain/repositories/IPaymentOrderRepository.ts",
    statementMap: {
      "0": {
        start: {
          line: 3,
          column: 0
        },
        end: {
          line: 5,
          column: 3
        }
      }
    },
    fnMap: {},
    branchMap: {},
    s: {
      "0": 0
    },
    f: {},
    b: {},
    inputSourceMap: {
      version: 3,
      file: undefined,
      names: [],
      sourceRoot: undefined,
      sources: ["/Users/<USER>/projects/react-native/payment-module/src/domain/repositories/IPaymentOrderRepository.ts"],
      sourcesContent: ["import {PaymentOrderStatusModel} from '../entities/payment-order-status/PaymentOrderStatusModel';\nimport {PaymentOrderModel} from '../entities/payment-order/PaymentOrderModel';\nimport {BaseResponse} from '../../core/BaseResponse';\nimport {PaymentOrderRequest} from '../../data/models/payment-order/PaymentOrderRequest';\nimport {PaymentOrderStatusRequest} from '../../data/models/payment-order-status/PaymentOrderStatusRequest';\n\nexport interface IPaymentOrderRepository {\n  paymentOrder(request: PaymentOrderRequest): Promise<BaseResponse<PaymentOrderModel>>;\n  paymentOrderStatus(request: PaymentOrderStatusRequest): Promise<BaseResponse<PaymentOrderStatusModel>>;\n}\n"],
      mappings: "",
      ignoreList: []
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "6f2cd65ad0ddf9395a04cd7b17494b5f7b9c75df"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_yr9cs0go4 = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_yr9cs0go4();
cov_yr9cs0go4().s[0]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6W10sInNvdXJjZXMiOlsiL1VzZXJzL3NvbnNhbXNldC9wcm9qZWN0cy9yZWFjdC1uYXRpdmUvcGF5bWVudC1tb2R1bGUvc3JjL2RvbWFpbi9yZXBvc2l0b3JpZXMvSVBheW1lbnRPcmRlclJlcG9zaXRvcnkudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHtQYXltZW50T3JkZXJTdGF0dXNNb2RlbH0gZnJvbSAnLi4vZW50aXRpZXMvcGF5bWVudC1vcmRlci1zdGF0dXMvUGF5bWVudE9yZGVyU3RhdHVzTW9kZWwnO1xuaW1wb3J0IHtQYXltZW50T3JkZXJNb2RlbH0gZnJvbSAnLi4vZW50aXRpZXMvcGF5bWVudC1vcmRlci9QYXltZW50T3JkZXJNb2RlbCc7XG5pbXBvcnQge0Jhc2VSZXNwb25zZX0gZnJvbSAnLi4vLi4vY29yZS9CYXNlUmVzcG9uc2UnO1xuaW1wb3J0IHtQYXltZW50T3JkZXJSZXF1ZXN0fSBmcm9tICcuLi8uLi9kYXRhL21vZGVscy9wYXltZW50LW9yZGVyL1BheW1lbnRPcmRlclJlcXVlc3QnO1xuaW1wb3J0IHtQYXltZW50T3JkZXJTdGF0dXNSZXF1ZXN0fSBmcm9tICcuLi8uLi9kYXRhL21vZGVscy9wYXltZW50LW9yZGVyLXN0YXR1cy9QYXltZW50T3JkZXJTdGF0dXNSZXF1ZXN0JztcblxuZXhwb3J0IGludGVyZmFjZSBJUGF5bWVudE9yZGVyUmVwb3NpdG9yeSB7XG4gIHBheW1lbnRPcmRlcihyZXF1ZXN0OiBQYXltZW50T3JkZXJSZXF1ZXN0KTogUHJvbWlzZTxCYXNlUmVzcG9uc2U8UGF5bWVudE9yZGVyTW9kZWw+PjtcbiAgcGF5bWVudE9yZGVyU3RhdHVzKHJlcXVlc3Q6IFBheW1lbnRPcmRlclN0YXR1c1JlcXVlc3QpOiBQcm9taXNlPEJhc2VSZXNwb25zZTxQYXltZW50T3JkZXJTdGF0dXNNb2RlbD4+O1xufVxuIl0sIm1hcHBpbmdzIjoiIiwiaWdub3JlTGlzdCI6W119