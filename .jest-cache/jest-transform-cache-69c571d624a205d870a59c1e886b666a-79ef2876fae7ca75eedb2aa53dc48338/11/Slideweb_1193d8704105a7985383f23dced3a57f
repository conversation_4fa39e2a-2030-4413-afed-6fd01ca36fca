827cd749c0428836842acb211b23fdc2
'use strict';

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.SlideOutData = exports.SlideOut = exports.SlideInData = exports.SlideIn = void 0;
var _animationParser = require("../animationParser.js");
var DEFAULT_SLIDE_TIME = 0.3;
var SlideInData = exports.SlideInData = {
  SlideInRight: {
    name: 'SlideInRight',
    style: {
      0: {
        transform: [{
          translateX: '100vw'
        }]
      },
      100: {
        transform: [{
          translateX: '0%'
        }]
      }
    },
    duration: DEFAULT_SLIDE_TIME
  },
  SlideInLeft: {
    name: 'SlideInLeft',
    style: {
      0: {
        transform: [{
          translateX: '-100vw'
        }]
      },
      100: {
        transform: [{
          translateX: '0%'
        }]
      }
    },
    duration: DEFAULT_SLIDE_TIME
  },
  SlideInUp: {
    name: 'SlideInUp',
    style: {
      0: {
        transform: [{
          translateY: '-100vh'
        }]
      },
      100: {
        transform: [{
          translateY: '0%'
        }]
      }
    },
    duration: DEFAULT_SLIDE_TIME
  },
  SlideInDown: {
    name: 'SlideInDown',
    style: {
      0: {
        transform: [{
          translateY: '100vh'
        }]
      },
      100: {
        transform: [{
          translateY: '0%'
        }]
      }
    },
    duration: DEFAULT_SLIDE_TIME
  }
};
var SlideOutData = exports.SlideOutData = {
  SlideOutRight: {
    name: 'SlideOutRight',
    style: {
      0: {
        transform: [{
          translateX: '0%'
        }]
      },
      100: {
        transform: [{
          translateX: '100vw'
        }]
      }
    },
    duration: DEFAULT_SLIDE_TIME
  },
  SlideOutLeft: {
    name: 'SlideOutLeft',
    style: {
      0: {
        transform: [{
          translateX: '0%'
        }]
      },
      100: {
        transform: [{
          translateX: '-100vw'
        }]
      }
    },
    duration: DEFAULT_SLIDE_TIME
  },
  SlideOutUp: {
    name: 'SlideOutUp',
    style: {
      0: {
        transform: [{
          translateY: '0%'
        }]
      },
      100: {
        transform: [{
          translateY: '-100vh'
        }]
      }
    },
    duration: DEFAULT_SLIDE_TIME
  },
  SlideOutDown: {
    name: 'SlideOutDown',
    style: {
      0: {
        transform: [{
          translateY: '0%'
        }]
      },
      100: {
        transform: [{
          translateY: '100vh'
        }]
      }
    },
    duration: DEFAULT_SLIDE_TIME
  }
};
var SlideIn = exports.SlideIn = {
  SlideInRight: {
    style: (0, _animationParser.convertAnimationObjectToKeyframes)(SlideInData.SlideInRight),
    duration: SlideInData.SlideInRight.duration
  },
  SlideInLeft: {
    style: (0, _animationParser.convertAnimationObjectToKeyframes)(SlideInData.SlideInLeft),
    duration: SlideInData.SlideInLeft.duration
  },
  SlideInUp: {
    style: (0, _animationParser.convertAnimationObjectToKeyframes)(SlideInData.SlideInUp),
    duration: SlideInData.SlideInUp.duration
  },
  SlideInDown: {
    style: (0, _animationParser.convertAnimationObjectToKeyframes)(SlideInData.SlideInDown),
    duration: SlideInData.SlideInDown.duration
  }
};
var SlideOut = exports.SlideOut = {
  SlideOutRight: {
    style: (0, _animationParser.convertAnimationObjectToKeyframes)(SlideOutData.SlideOutRight),
    duration: SlideOutData.SlideOutRight.duration
  },
  SlideOutLeft: {
    style: (0, _animationParser.convertAnimationObjectToKeyframes)(SlideOutData.SlideOutLeft),
    duration: SlideOutData.SlideOutLeft.duration
  },
  SlideOutUp: {
    style: (0, _animationParser.convertAnimationObjectToKeyframes)(SlideOutData.SlideOutUp),
    duration: SlideOutData.SlideOutUp.duration
  },
  SlideOutDown: {
    style: (0, _animationParser.convertAnimationObjectToKeyframes)(SlideOutData.SlideOutDown),
    duration: SlideOutData.SlideOutDown.duration
  }
};
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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