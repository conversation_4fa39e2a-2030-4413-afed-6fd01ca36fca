{"version": 3, "names": ["Object", "defineProperty", "exports", "value", "SlideOutData", "SlideOut", "SlideInData", "SlideIn", "_animation<PERSON><PERSON>er", "require", "DEFAULT_SLIDE_TIME", "SlideInRight", "name", "style", "transform", "translateX", "duration", "SlideInLeft", "SlideInUp", "translateY", "SlideInDown", "SlideOutRight", "SlideOutLeft", "SlideOutUp", "SlideOutDown", "convertAnimationObjectToKeyframes"], "sources": ["../../../../../src/layoutReanimation/web/animation/Slide.web.ts"], "sourcesContent": [null], "mappings": "AAAA,YAAY;;AAAAA,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,KAAA;AAAA;AAAAD,OAAA,CAAAE,YAAA,GAAAF,OAAA,CAAAG,QAAA,GAAAH,OAAA,CAAAI,WAAA,GAAAJ,OAAA,CAAAK,OAAA;AACZ,IAAAC,gBAAA,GAAAC,OAAA;AAEA,IAAMC,kBAAkB,GAAG,GAAG;AAEvB,IAAMJ,WAAW,GAAAJ,OAAA,CAAAI,WAAA,GAAG;EACzBK,YAAY,EAAE;IACZC,IAAI,EAAE,cAAc;IACpBC,KAAK,EAAE;MACL,CAAC,EAAE;QAAEC,SAAS,EAAE,CAAC;UAAEC,UAAU,EAAE;QAAQ,CAAC;MAAE,CAAC;MAC3C,GAAG,EAAE;QAAED,SAAS,EAAE,CAAC;UAAEC,UAAU,EAAE;QAAK,CAAC;MAAE;IAC3C,CAAC;IACDC,QAAQ,EAAEN;EACZ,CAAC;EAEDO,WAAW,EAAE;IACXL,IAAI,EAAE,aAAa;IACnBC,KAAK,EAAE;MACL,CAAC,EAAE;QAAEC,SAAS,EAAE,CAAC;UAAEC,UAAU,EAAE;QAAS,CAAC;MAAE,CAAC;MAC5C,GAAG,EAAE;QAAED,SAAS,EAAE,CAAC;UAAEC,UAAU,EAAE;QAAK,CAAC;MAAE;IAC3C,CAAC;IACDC,QAAQ,EAAEN;EACZ,CAAC;EAEDQ,SAAS,EAAE;IACTN,IAAI,EAAE,WAAW;IACjBC,KAAK,EAAE;MACL,CAAC,EAAE;QAAEC,SAAS,EAAE,CAAC;UAAEK,UAAU,EAAE;QAAS,CAAC;MAAE,CAAC;MAC5C,GAAG,EAAE;QAAEL,SAAS,EAAE,CAAC;UAAEK,UAAU,EAAE;QAAK,CAAC;MAAE;IAC3C,CAAC;IACDH,QAAQ,EAAEN;EACZ,CAAC;EAEDU,WAAW,EAAE;IACXR,IAAI,EAAE,aAAa;IACnBC,KAAK,EAAE;MACL,CAAC,EAAE;QAAEC,SAAS,EAAE,CAAC;UAAEK,UAAU,EAAE;QAAQ,CAAC;MAAE,CAAC;MAC3C,GAAG,EAAE;QAAEL,SAAS,EAAE,CAAC;UAAEK,UAAU,EAAE;QAAK,CAAC;MAAE;IAC3C,CAAC;IACDH,QAAQ,EAAEN;EACZ;AACF,CAAC;AAEM,IAAMN,YAAY,GAAAF,OAAA,CAAAE,YAAA,GAAG;EAC1BiB,aAAa,EAAE;IACbT,IAAI,EAAE,eAAe;IACrBC,KAAK,EAAE;MACL,CAAC,EAAE;QAAEC,SAAS,EAAE,CAAC;UAAEC,UAAU,EAAE;QAAK,CAAC;MAAE,CAAC;MACxC,GAAG,EAAE;QAAED,SAAS,EAAE,CAAC;UAAEC,UAAU,EAAE;QAAQ,CAAC;MAAE;IAC9C,CAAC;IACDC,QAAQ,EAAEN;EACZ,CAAC;EAEDY,YAAY,EAAE;IACZV,IAAI,EAAE,cAAc;IACpBC,KAAK,EAAE;MACL,CAAC,EAAE;QAAEC,SAAS,EAAE,CAAC;UAAEC,UAAU,EAAE;QAAK,CAAC;MAAE,CAAC;MACxC,GAAG,EAAE;QAAED,SAAS,EAAE,CAAC;UAAEC,UAAU,EAAE;QAAS,CAAC;MAAE;IAC/C,CAAC;IACDC,QAAQ,EAAEN;EACZ,CAAC;EAEDa,UAAU,EAAE;IACVX,IAAI,EAAE,YAAY;IAClBC,KAAK,EAAE;MACL,CAAC,EAAE;QAAEC,SAAS,EAAE,CAAC;UAAEK,UAAU,EAAE;QAAK,CAAC;MAAE,CAAC;MACxC,GAAG,EAAE;QAAEL,SAAS,EAAE,CAAC;UAAEK,UAAU,EAAE;QAAS,CAAC;MAAE;IAC/C,CAAC;IACDH,QAAQ,EAAEN;EACZ,CAAC;EAEDc,YAAY,EAAE;IACZZ,IAAI,EAAE,cAAc;IACpBC,KAAK,EAAE;MACL,CAAC,EAAE;QAAEC,SAAS,EAAE,CAAC;UAAEK,UAAU,EAAE;QAAK,CAAC;MAAE,CAAC;MACxC,GAAG,EAAE;QAAEL,SAAS,EAAE,CAAC;UAAEK,UAAU,EAAE;QAAQ,CAAC;MAAE;IAC9C,CAAC;IACDH,QAAQ,EAAEN;EACZ;AACF,CAAC;AAEM,IAAMH,OAAO,GAAAL,OAAA,CAAAK,OAAA,GAAG;EACrBI,YAAY,EAAE;IACZE,KAAK,EAAE,IAAAY,kDAAiC,EAACnB,WAAW,CAACK,YAAY,CAAC;IAClEK,QAAQ,EAAEV,WAAW,CAACK,YAAY,CAACK;EACrC,CAAC;EACDC,WAAW,EAAE;IACXJ,KAAK,EAAE,IAAAY,kDAAiC,EAACnB,WAAW,CAACW,WAAW,CAAC;IACjED,QAAQ,EAAEV,WAAW,CAACW,WAAW,CAACD;EACpC,CAAC;EACDE,SAAS,EAAE;IACTL,KAAK,EAAE,IAAAY,kDAAiC,EAACnB,WAAW,CAACY,SAAS,CAAC;IAC/DF,QAAQ,EAAEV,WAAW,CAACY,SAAS,CAACF;EAClC,CAAC;EACDI,WAAW,EAAE;IACXP,KAAK,EAAE,IAAAY,kDAAiC,EAACnB,WAAW,CAACc,WAAW,CAAC;IACjEJ,QAAQ,EAAEV,WAAW,CAACc,WAAW,CAACJ;EACpC;AACF,CAAC;AAEM,IAAMX,QAAQ,GAAAH,OAAA,CAAAG,QAAA,GAAG;EACtBgB,aAAa,EAAE;IACbR,KAAK,EAAE,IAAAY,kDAAiC,EAACrB,YAAY,CAACiB,aAAa,CAAC;IACpEL,QAAQ,EAAEZ,YAAY,CAACiB,aAAa,CAACL;EACvC,CAAC;EACDM,YAAY,EAAE;IACZT,KAAK,EAAE,IAAAY,kDAAiC,EAACrB,YAAY,CAACkB,YAAY,CAAC;IACnEN,QAAQ,EAAEZ,YAAY,CAACkB,YAAY,CAACN;EACtC,CAAC;EACDO,UAAU,EAAE;IACVV,KAAK,EAAE,IAAAY,kDAAiC,EAACrB,YAAY,CAACmB,UAAU,CAAC;IACjEP,QAAQ,EAAEZ,YAAY,CAACmB,UAAU,CAACP;EACpC,CAAC;EACDQ,YAAY,EAAE;IACZX,KAAK,EAAE,IAAAY,kDAAiC,EAACrB,YAAY,CAACoB,YAAY,CAAC;IACnER,QAAQ,EAAEZ,YAAY,CAACoB,YAAY,CAACR;EACtC;AACF,CAAC", "ignoreList": []}