{"version": 3, "names": [], "sources": ["/Users/<USER>/projects/react-native/payment-module/src/domain/repositories/IPaymentOrderRepository.ts"], "sourcesContent": ["import {PaymentOrderStatusModel} from '../entities/payment-order-status/PaymentOrderStatusModel';\nimport {PaymentOrderModel} from '../entities/payment-order/PaymentOrderModel';\nimport {BaseResponse} from '../../core/BaseResponse';\nimport {PaymentOrderRequest} from '../../data/models/payment-order/PaymentOrderRequest';\nimport {PaymentOrderStatusRequest} from '../../data/models/payment-order-status/PaymentOrderStatusRequest';\n\nexport interface IPaymentOrderRepository {\n  paymentOrder(request: PaymentOrderRequest): Promise<BaseResponse<PaymentOrderModel>>;\n  paymentOrderStatus(request: PaymentOrderStatusRequest): Promise<BaseResponse<PaymentOrderStatusModel>>;\n}\n"], "mappings": "", "ignoreList": []}