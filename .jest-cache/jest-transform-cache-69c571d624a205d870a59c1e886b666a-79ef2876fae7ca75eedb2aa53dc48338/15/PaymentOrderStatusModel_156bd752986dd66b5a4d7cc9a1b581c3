cbe550fbfa7188a5a0c1e9fd97bc5389
"use strict";

/* istanbul ignore next */
function cov_rz7xz294c() {
  var path = "/Users/<USER>/projects/react-native/payment-module/src/domain/entities/payment-order-status/PaymentOrderStatusModel.ts";
  var hash = "29a669d21e8628af4df7696947e65e0af3bb9abf";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/projects/react-native/payment-module/src/domain/entities/payment-order-status/PaymentOrderStatusModel.ts",
    statementMap: {
      "0": {
        start: {
          line: 3,
          column: 29
        },
        end: {
          line: 3,
          column: 84
        }
      },
      "1": {
        start: {
          line: 4,
          column: 20
        },
        end: {
          line: 4,
          column: 89
        }
      },
      "2": {
        start: {
          line: 5,
          column: 23
        },
        end: {
          line: 5,
          column: 95
        }
      },
      "3": {
        start: {
          line: 6,
          column: 0
        },
        end: {
          line: 8,
          column: 3
        }
      },
      "4": {
        start: {
          line: 9,
          column: 0
        },
        end: {
          line: 9,
          column: 85
        }
      },
      "5": {
        start: {
          line: 10,
          column: 30
        },
        end: {
          line: 14,
          column: 2
        }
      },
      "6": {
        start: {
          line: 11,
          column: 2
        },
        end: {
          line: 11,
          column: 63
        }
      },
      "7": {
        start: {
          line: 12,
          column: 2
        },
        end: {
          line: 12,
          column: 31
        }
      },
      "8": {
        start: {
          line: 13,
          column: 2
        },
        end: {
          line: 13,
          column: 29
        }
      },
      "9": {
        start: {
          line: 15,
          column: 0
        },
        end: {
          line: 15,
          column: 58
        }
      },
      "10": {
        start: {
          line: 16,
          column: 40
        },
        end: {
          line: 19,
          column: 2
        }
      },
      "11": {
        start: {
          line: 17,
          column: 2
        },
        end: {
          line: 17,
          column: 73
        }
      },
      "12": {
        start: {
          line: 18,
          column: 2
        },
        end: {
          line: 18,
          column: 35
        }
      },
      "13": {
        start: {
          line: 20,
          column: 0
        },
        end: {
          line: 20,
          column: 78
        }
      }
    },
    fnMap: {
      "0": {
        name: "PaymentOrderStatusModel",
        decl: {
          start: {
            line: 10,
            column: 66
          },
          end: {
            line: 10,
            column: 89
          }
        },
        loc: {
          start: {
            line: 10,
            column: 113
          },
          end: {
            line: 14,
            column: 1
          }
        },
        line: 10
      },
      "1": {
        name: "PaymentOrderStatusAdditionalModel",
        decl: {
          start: {
            line: 16,
            column: 76
          },
          end: {
            line: 16,
            column: 109
          }
        },
        loc: {
          start: {
            line: 16,
            column: 124
          },
          end: {
            line: 19,
            column: 1
          }
        },
        line: 16
      }
    },
    branchMap: {},
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0
    },
    f: {
      "0": 0,
      "1": 0
    },
    b: {},
    inputSourceMap: {
      version: 3,
      file: undefined,
      names: ["PaymentOrderStatusModel", "_createClass2", "default", "bankStatus", "additions", "_classCallCheck2", "exports", "PaymentOrderStatusAdditionalModel", "t24TraceCode"],
      sourceRoot: undefined,
      sources: ["/Users/<USER>/projects/react-native/payment-module/src/domain/entities/payment-order-status/PaymentOrderStatusModel.ts"],
      sourcesContent: ["export class PaymentOrderStatusModel {\n  constructor(public bankStatus: string, public additions: PaymentOrderStatusAdditionalModel) {}\n}\n\nexport class PaymentOrderStatusAdditionalModel {\n  constructor(public t24TraceCode: string) {}\n}\n"],
      mappings: ";;;;;;;;;IAAaA,uBAAuB,OAAAC,aAAA,CAAAC,OAAA,EAClC,SAAAF,wBAAmBG,UAAkB,EAASC,SAA4C;EAAA,IAAAC,gBAAA,CAAAH,OAAA,QAAAF,uBAAA;EAAvE,KAAAG,UAAU,GAAVA,UAAU;EAAiB,KAAAC,SAAS,GAATA,SAAS;AAAsC,CAAC;AADhGE,OAAA,CAAAN,uBAAA,GAAAA,uBAAA;AAEC,IAEYO,iCAAiC,OAAAN,aAAA,CAAAC,OAAA,EAC5C,SAAAK,kCAAmBC,YAAoB;EAAA,IAAAH,gBAAA,CAAAH,OAAA,QAAAK,iCAAA;EAApB,KAAAC,YAAY,GAAZA,YAAY;AAAW,CAAC;AAD7CF,OAAA,CAAAC,iCAAA,GAAAA,iCAAA",
      ignoreList: []
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "29a669d21e8628af4df7696947e65e0af3bb9abf"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_rz7xz294c = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_rz7xz294c();
var _interopRequireDefault =
/* istanbul ignore next */
(cov_rz7xz294c().s[0]++, require("@babel/runtime/helpers/interopRequireDefault"));
var _createClass2 =
/* istanbul ignore next */
(cov_rz7xz294c().s[1]++, _interopRequireDefault(require("@babel/runtime/helpers/createClass")));
var _classCallCheck2 =
/* istanbul ignore next */
(cov_rz7xz294c().s[2]++, _interopRequireDefault(require("@babel/runtime/helpers/classCallCheck")));
/* istanbul ignore next */
cov_rz7xz294c().s[3]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_rz7xz294c().s[4]++;
exports.PaymentOrderStatusAdditionalModel = exports.PaymentOrderStatusModel = void 0;
var PaymentOrderStatusModel =
/* istanbul ignore next */
(cov_rz7xz294c().s[5]++, (0, _createClass2.default)(function PaymentOrderStatusModel(bankStatus, additions) {
  /* istanbul ignore next */
  cov_rz7xz294c().f[0]++;
  cov_rz7xz294c().s[6]++;
  (0, _classCallCheck2.default)(this, PaymentOrderStatusModel);
  /* istanbul ignore next */
  cov_rz7xz294c().s[7]++;
  this.bankStatus = bankStatus;
  /* istanbul ignore next */
  cov_rz7xz294c().s[8]++;
  this.additions = additions;
}));
/* istanbul ignore next */
cov_rz7xz294c().s[9]++;
exports.PaymentOrderStatusModel = PaymentOrderStatusModel;
var PaymentOrderStatusAdditionalModel =
/* istanbul ignore next */
(cov_rz7xz294c().s[10]++, (0, _createClass2.default)(function PaymentOrderStatusAdditionalModel(t24TraceCode) {
  /* istanbul ignore next */
  cov_rz7xz294c().f[1]++;
  cov_rz7xz294c().s[11]++;
  (0, _classCallCheck2.default)(this, PaymentOrderStatusAdditionalModel);
  /* istanbul ignore next */
  cov_rz7xz294c().s[12]++;
  this.t24TraceCode = t24TraceCode;
}));
/* istanbul ignore next */
cov_rz7xz294c().s[13]++;
exports.PaymentOrderStatusAdditionalModel = PaymentOrderStatusAdditionalModel;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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