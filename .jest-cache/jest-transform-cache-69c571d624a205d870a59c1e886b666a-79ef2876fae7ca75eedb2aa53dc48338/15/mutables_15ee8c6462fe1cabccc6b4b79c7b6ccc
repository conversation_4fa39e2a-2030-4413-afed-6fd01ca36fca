85ddf9c655b62824c8e699219c343e52
'use strict';

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.makeMutable = void 0;
exports.makeMutableUI = makeMutableUI;
var _PlatformChecker = require("./PlatformChecker.js");
var _errors = require("./errors.js");
var _index = require("./logger/index.js");
var _reactUtils = require("./reactUtils.js");
var _shareableMappingCache = require("./shareableMappingCache.js");
var _shareables = require("./shareables.js");
var _threads = require("./threads.js");
var _valueSetter = require("./valueSetter.js");
var SHOULD_BE_USE_WEB = (0, _PlatformChecker.shouldBeUseWeb)();
function shouldWarnAboutAccessDuringRender() {
  return __DEV__ && (0, _reactUtils.isReactRendering)() && !(0, _reactUtils.isFirstReactRender)();
}
function checkInvalidReadDuringRender() {
  if (shouldWarnAboutAccessDuringRender()) {
    _index.logger.warn('Reading from `value` during component render. Please ensure that you do not access the `value` property or use `get` method of a shared value while React is rendering a component.', {
      strict: true
    });
  }
}
function checkInvalidWriteDuringRender() {
  if (shouldWarnAboutAccessDuringRender()) {
    _index.logger.warn('Writing to `value` during component render. Please ensure that you do not access the `value` property or use `set` method of a shared value while React is rendering a component.', {
      strict: true
    });
  }
}
function addCompilerSafeGetAndSet(mutable) {
  'worklet';

  Object.defineProperties(mutable, {
    get: {
      value: function value() {
        return mutable.value;
      },
      configurable: false,
      enumerable: false
    },
    set: {
      value: function value(newValue) {
        if (typeof newValue === 'function' && !newValue.__isAnimationDefinition) {
          mutable.value = newValue(mutable.value);
        } else {
          mutable.value = newValue;
        }
      },
      configurable: false,
      enumerable: false
    }
  });
}
function hideInternalValueProp(mutable) {
  'worklet';

  Object.defineProperty(mutable, '_value', {
    configurable: false,
    enumerable: false
  });
}
function makeMutableUI(initial) {
  'worklet';

  var listeners = new Map();
  var value = initial;
  var mutable = {
    get value() {
      return value;
    },
    set value(newValue) {
      (0, _valueSetter.valueSetter)(mutable, newValue);
    },
    get _value() {
      return value;
    },
    set _value(newValue) {
      value = newValue;
      listeners.forEach(function (listener) {
        listener(newValue);
      });
    },
    modify: function modify(modifier) {
      var forceUpdate = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : true;
      (0, _valueSetter.valueSetter)(mutable, modifier !== undefined ? modifier(value) : value, forceUpdate);
    },
    addListener: function addListener(id, listener) {
      listeners.set(id, listener);
    },
    removeListener: function removeListener(id) {
      listeners.delete(id);
    },
    _animation: null,
    _isReanimatedSharedValue: true
  };
  hideInternalValueProp(mutable);
  addCompilerSafeGetAndSet(mutable);
  return mutable;
}
function makeMutableNative(initial) {
  var handle = (0, _shareables.makeShareableCloneRecursive)({
    __init: function __init() {
      'worklet';

      return makeMutableUI(initial);
    }
  });
  var mutable = {
    get value() {
      checkInvalidReadDuringRender();
      var uiValueGetter = (0, _threads.executeOnUIRuntimeSync)(function (sv) {
        return sv.value;
      });
      return uiValueGetter(mutable);
    },
    set value(newValue) {
      checkInvalidWriteDuringRender();
      (0, _threads.runOnUI)(function () {
        mutable.value = newValue;
      })();
    },
    get _value() {
      throw new _errors.ReanimatedError('Reading from `_value` directly is only possible on the UI runtime. Perhaps you passed an Animated Style to a non-animated component?');
    },
    set _value(_newValue) {
      throw new _errors.ReanimatedError('Setting `_value` directly is only possible on the UI runtime. Perhaps you want to assign to `value` instead?');
    },
    modify: function modify(modifier) {
      var forceUpdate = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : true;
      (0, _threads.runOnUI)(function () {
        mutable.modify(modifier, forceUpdate);
      })();
    },
    addListener: function addListener() {
      throw new _errors.ReanimatedError('Adding listeners is only possible on the UI runtime.');
    },
    removeListener: function removeListener() {
      throw new _errors.ReanimatedError('Removing listeners is only possible on the UI runtime.');
    },
    _isReanimatedSharedValue: true
  };
  hideInternalValueProp(mutable);
  addCompilerSafeGetAndSet(mutable);
  _shareableMappingCache.shareableMappingCache.set(mutable, handle);
  return mutable;
}
function makeMutableWeb(initial) {
  var value = initial;
  var listeners = new Map();
  var mutable = {
    get value() {
      checkInvalidReadDuringRender();
      return value;
    },
    set value(newValue) {
      checkInvalidWriteDuringRender();
      (0, _valueSetter.valueSetter)(mutable, newValue);
    },
    get _value() {
      return value;
    },
    set _value(newValue) {
      value = newValue;
      listeners.forEach(function (listener) {
        listener(newValue);
      });
    },
    modify: function modify(modifier) {
      var forceUpdate = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : true;
      (0, _valueSetter.valueSetter)(mutable, modifier !== undefined ? modifier(mutable.value) : mutable.value, forceUpdate);
    },
    addListener: function addListener(id, listener) {
      listeners.set(id, listener);
    },
    removeListener: function removeListener(id) {
      listeners.delete(id);
    },
    _isReanimatedSharedValue: true
  };
  hideInternalValueProp(mutable);
  addCompilerSafeGetAndSet(mutable);
  return mutable;
}
var makeMutable = exports.makeMutable = SHOULD_BE_USE_WEB ? makeMutableWeb : makeMutableNative;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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