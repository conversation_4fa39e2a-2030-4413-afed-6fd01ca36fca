{"version": 3, "names": ["Object", "defineProperty", "exports", "value", "makeMutable", "makeMutableUI", "_PlatformChecker", "require", "_errors", "_index", "_reactUtils", "_shareableMappingCache", "_shareables", "_threads", "_valueSetter", "SHOULD_BE_USE_WEB", "shouldBeUseWeb", "shouldWarnAboutAccessDuringRender", "__DEV__", "isReactRendering", "isFirstReactRender", "checkInvalidReadDuringRender", "logger", "warn", "strict", "checkInvalidWriteDuringRender", "addCompilerSafeGetAndSet", "mutable", "defineProperties", "get", "configurable", "enumerable", "set", "newValue", "__isAnimationDefinition", "hideInternalValueProp", "initial", "listeners", "Map", "valueSetter", "_value", "for<PERSON>ach", "listener", "modify", "modifier", "forceUpdate", "arguments", "length", "undefined", "addListener", "id", "removeListener", "delete", "_animation", "_isReanimatedSharedValue", "makeMutableNative", "handle", "makeShareableCloneRecursive", "__init", "uiValueGetter", "executeOnUIRuntimeSync", "sv", "runOnUI", "ReanimatedError", "_newValue", "shareableMappingCache", "makeMutableWeb"], "sources": ["../../src/mutables.ts"], "sourcesContent": [null], "mappings": "AAAA,YAAY;;AAAAA,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,KAAA;AAAA;AAAAD,OAAA,CAAAE,WAAA;AAAAF,OAAA,CAAAG,aAAA,GAAAA,aAAA;AACZ,IAAAC,gBAAA,GAAAC,OAAA;AAEA,IAAAC,OAAA,GAAAD,OAAA;AACA,IAAAE,MAAA,GAAAF,OAAA;AACA,IAAAG,WAAA,GAAAH,OAAA;AACA,IAAAI,sBAAA,GAAAJ,OAAA;AACA,IAAAK,WAAA,GAAAL,OAAA;AACA,IAAAM,QAAA,GAAAN,OAAA;AACA,IAAAO,YAAA,GAAAP,OAAA;AAEA,IAAMQ,iBAAiB,GAAG,IAAAC,+BAAc,EAAC,CAAC;AAE1C,SAASC,iCAAiCA,CAAA,EAAG;EAC3C,OAAOC,OAAO,IAAI,IAAAC,4BAAgB,EAAC,CAAC,IAAI,CAAC,IAAAC,8BAAkB,EAAC,CAAC;AAC/D;AAEA,SAASC,4BAA4BA,CAAA,EAAG;EACtC,IAAIJ,iCAAiC,CAAC,CAAC,EAAE;IACvCK,aAAM,CAACC,IAAI,CACT,qLAAqL,EACrL;MAAEC,MAAM,EAAE;IAAK,CACjB,CAAC;EACH;AACF;AAEA,SAASC,6BAA6BA,CAAA,EAAG;EACvC,IAAIR,iCAAiC,CAAC,CAAC,EAAE;IACvCK,aAAM,CAACC,IAAI,CACT,mLAAmL,EACnL;MAAEC,MAAM,EAAE;IAAK,CACjB,CAAC;EACH;AACF;AAeA,SAASE,wBAAwBA,CAAQC,OAA8B,EAAQ;EAC7E,SAAS;;EACT3B,MAAM,CAAC4B,gBAAgB,CAACD,OAAO,EAAE;IAC/BE,GAAG,EAAE;MACH1B,KAAK,WAALA,KAAKA,CAAA,EAAG;QACN,OAAOwB,OAAO,CAACxB,KAAK;MACtB,CAAC;MACD2B,YAAY,EAAE,KAAK;MACnBC,UAAU,EAAE;IACd,CAAC;IACDC,GAAG,EAAE;MACH7B,KAAK,WAALA,KAAKA,CAAC8B,QAA2C,EAAE;QACjD,IACE,OAAOA,QAAQ,KAAK,UAAU,IAE9B,CAAEA,QAAQ,CAA6BC,uBAAuB,EAC9D;UACAP,OAAO,CAACxB,KAAK,GAAI8B,QAAQ,CAA6BN,OAAO,CAACxB,KAAK,CAAC;QACtE,CAAC,MAAM;UACLwB,OAAO,CAACxB,KAAK,GAAG8B,QAAiB;QACnC;MACF,CAAC;MACDH,YAAY,EAAE,KAAK;MACnBC,UAAU,EAAE;IACd;EACF,CAAC,CAAC;AACJ;AAcA,SAASI,qBAAqBA,CAAQR,OAA8B,EAAE;EACpE,SAAS;;EACT3B,MAAM,CAACC,cAAc,CAAC0B,OAAO,EAAE,QAAQ,EAAE;IACvCG,YAAY,EAAE,KAAK;IACnBC,UAAU,EAAE;EACd,CAAC,CAAC;AACJ;AAEO,SAAS1B,aAAaA,CAAQ+B,OAAc,EAAkB;EACnE,SAAS;;EACT,IAAMC,SAAS,GAAG,IAAIC,GAAG,CAA0B,CAAC;EACpD,IAAInC,KAAK,GAAGiC,OAAO;EAEnB,IAAMT,OAA8B,GAAG;IACrC,IAAIxB,KAAKA,CAAA,EAAG;MACV,OAAOA,KAAK;IACd,CAAC;IACD,IAAIA,KAAKA,CAAC8B,QAAQ,EAAE;MAClB,IAAAM,wBAAW,EAACZ,OAAO,EAAoBM,QAAQ,CAAC;IAClD,CAAC;IACD,IAAIO,MAAMA,CAAA,EAAU;MAClB,OAAOrC,KAAK;IACd,CAAC;IACD,IAAIqC,MAAMA,CAACP,QAAe,EAAE;MAC1B9B,KAAK,GAAG8B,QAAQ;MAChBI,SAAS,CAACI,OAAO,CAAE,UAAAC,QAAQ,EAAK;QAC9BA,QAAQ,CAACT,QAAQ,CAAC;MACpB,CAAC,CAAC;IACJ,CAAC;IACDU,MAAM,EAAE,SAARA,MAAMA,CAAGC,QAAQ,EAAyB;MAAA,IAAvBC,WAAW,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,IAAI;MACnC,IAAAP,wBAAW,EACTZ,OAAO,EACPiB,QAAQ,KAAKI,SAAS,GAAGJ,QAAQ,CAACzC,KAAK,CAAC,GAAGA,KAAK,EAChD0C,WACF,CAAC;IACH,CAAC;IACDI,WAAW,EAAE,SAAbA,WAAWA,CAAGC,EAAU,EAAER,QAAyB,EAAK;MACtDL,SAAS,CAACL,GAAG,CAACkB,EAAE,EAAER,QAAQ,CAAC;IAC7B,CAAC;IACDS,cAAc,EAAG,SAAjBA,cAAcA,CAAGD,EAAU,EAAK;MAC9Bb,SAAS,CAACe,MAAM,CAACF,EAAE,CAAC;IACtB,CAAC;IAEDG,UAAU,EAAE,IAAI;IAChBC,wBAAwB,EAAE;EAC5B,CAAC;EAEDnB,qBAAqB,CAACR,OAAO,CAAC;EAC9BD,wBAAwB,CAACC,OAAO,CAAC;EAEjC,OAAOA,OAAO;AAChB;AAEA,SAAS4B,iBAAiBA,CAAQnB,OAAc,EAAkB;EAChE,IAAMoB,MAAM,GAAG,IAAAC,uCAA2B,EAAC;IACzCC,MAAM,EAAE,SAARA,MAAMA,CAAA,EAAQ;MACZ,SAAS;;MACT,OAAOrD,aAAa,CAAC+B,OAAO,CAAC;IAC/B;EACF,CAAC,CAAC;EAEF,IAAMT,OAA8B,GAAG;IACrC,IAAIxB,KAAKA,CAAA,EAAU;MACjBkB,4BAA4B,CAAC,CAAC;MAC9B,IAAMsC,aAAa,GAAG,IAAAC,+BAAsB,EAAE,UAAAC,EAAkB,EAAK;QACnE,OAAOA,EAAE,CAAC1D,KAAK;MACjB,CAAC,CAAC;MACF,OAAOwD,aAAa,CAAChC,OAAyB,CAAC;IACjD,CAAC;IACD,IAAIxB,KAAKA,CAAC8B,QAAQ,EAAE;MAClBR,6BAA6B,CAAC,CAAC;MAC/B,IAAAqC,gBAAO,EAAC,YAAM;QACZnC,OAAO,CAACxB,KAAK,GAAG8B,QAAQ;MAC1B,CAAC,CAAC,CAAC,CAAC;IACN,CAAC;IAED,IAAIO,MAAMA,CAAA,EAAU;MAClB,MAAM,IAAIuB,uBAAe,CACvB,sIACF,CAAC;IACH,CAAC;IACD,IAAIvB,MAAMA,CAACwB,SAAgB,EAAE;MAC3B,MAAM,IAAID,uBAAe,CACvB,8GACF,CAAC;IACH,CAAC;IAEDpB,MAAM,EAAE,SAARA,MAAMA,CAAGC,QAAQ,EAAyB;MAAA,IAAvBC,WAAW,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,IAAI;MACnC,IAAAgB,gBAAO,EAAC,YAAM;QACZnC,OAAO,CAACgB,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC;MACvC,CAAC,CAAC,CAAC,CAAC;IACN,CAAC;IACDI,WAAW,EAAE,SAAbA,WAAWA,CAAA,EAAQ;MACjB,MAAM,IAAIc,uBAAe,CACvB,sDACF,CAAC;IACH,CAAC;IACDZ,cAAc,EAAE,SAAhBA,cAAcA,CAAA,EAAQ;MACpB,MAAM,IAAIY,uBAAe,CACvB,wDACF,CAAC;IACH,CAAC;IAEDT,wBAAwB,EAAE;EAC5B,CAAC;EAEDnB,qBAAqB,CAACR,OAAO,CAAC;EAC9BD,wBAAwB,CAACC,OAAO,CAAC;EAEjCsC,4CAAqB,CAACjC,GAAG,CAACL,OAAO,EAAE6B,MAAM,CAAC;EAC1C,OAAO7B,OAAO;AAChB;AAEA,SAASuC,cAAcA,CAAQ9B,OAAc,EAAkB;EAC7D,IAAIjC,KAAY,GAAGiC,OAAO;EAC1B,IAAMC,SAAS,GAAG,IAAIC,GAAG,CAA0B,CAAC;EAEpD,IAAMX,OAA8B,GAAG;IACrC,IAAIxB,KAAKA,CAAA,EAAU;MACjBkB,4BAA4B,CAAC,CAAC;MAC9B,OAAOlB,KAAK;IACd,CAAC;IACD,IAAIA,KAAKA,CAAC8B,QAAQ,EAAE;MAClBR,6BAA6B,CAAC,CAAC;MAC/B,IAAAc,wBAAW,EAACZ,OAAO,EAAoBM,QAAQ,CAAC;IAClD,CAAC;IAED,IAAIO,MAAMA,CAAA,EAAU;MAClB,OAAOrC,KAAK;IACd,CAAC;IACD,IAAIqC,MAAMA,CAACP,QAAe,EAAE;MAC1B9B,KAAK,GAAG8B,QAAQ;MAChBI,SAAS,CAACI,OAAO,CAAE,UAAAC,QAAQ,EAAK;QAC9BA,QAAQ,CAACT,QAAQ,CAAC;MACpB,CAAC,CAAC;IACJ,CAAC;IAEDU,MAAM,EAAE,SAARA,MAAMA,CAAGC,QAAQ,EAAyB;MAAA,IAAvBC,WAAW,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,IAAI;MACnC,IAAAP,wBAAW,EACTZ,OAAO,EACPiB,QAAQ,KAAKI,SAAS,GAAGJ,QAAQ,CAACjB,OAAO,CAACxB,KAAK,CAAC,GAAGwB,OAAO,CAACxB,KAAK,EAChE0C,WACF,CAAC;IACH,CAAC;IACDI,WAAW,EAAE,SAAbA,WAAWA,CAAGC,EAAU,EAAER,QAAyB,EAAK;MACtDL,SAAS,CAACL,GAAG,CAACkB,EAAE,EAAER,QAAQ,CAAC;IAC7B,CAAC;IACDS,cAAc,EAAG,SAAjBA,cAAcA,CAAGD,EAAU,EAAK;MAC9Bb,SAAS,CAACe,MAAM,CAACF,EAAE,CAAC;IACtB,CAAC;IAEDI,wBAAwB,EAAE;EAC5B,CAAC;EAEDnB,qBAAqB,CAACR,OAAO,CAAC;EAC9BD,wBAAwB,CAACC,OAAO,CAAC;EAEjC,OAAOA,OAAO;AAChB;AAEO,IAAMvB,WAAW,GAAAF,OAAA,CAAAE,WAAA,GAAGW,iBAAiB,GACxCmD,cAAc,GACdX,iBAAiB", "ignoreList": []}