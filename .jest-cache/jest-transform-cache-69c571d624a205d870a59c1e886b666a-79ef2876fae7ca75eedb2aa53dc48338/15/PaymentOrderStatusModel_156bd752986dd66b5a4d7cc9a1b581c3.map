{"version": 3, "names": ["cov_rz7xz294c", "actualCoverage", "PaymentOrderStatusModel", "s", "_createClass2", "default", "bankStatus", "additions", "f", "_classCallCheck2", "exports", "PaymentOrderStatusAdditionalModel", "t24TraceCode"], "sources": ["/Users/<USER>/projects/react-native/payment-module/src/domain/entities/payment-order-status/PaymentOrderStatusModel.ts"], "sourcesContent": ["export class PaymentOrderStatusModel {\n  constructor(public bankStatus: string, public additions: PaymentOrderStatusAdditionalModel) {}\n}\n\nexport class PaymentOrderStatusAdditionalModel {\n  constructor(public t24TraceCode: string) {}\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAIa;IAAAA,aAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,aAAA;;;;;;;;;;;;;;;;;;IAJAE,uBAAuB;AAAA;AAAA,CAAAF,aAAA,GAAAG,CAAA,WAAAC,aAAA,CAAAC,OAAA,EAClC,SAAAH,wBAAmBI,UAAkB,EAASC,SAA4C;EAAA;EAAAP,aAAA,GAAAQ,CAAA;EAAAR,aAAA,GAAAG,CAAA;EAAA,IAAAM,gBAAA,CAAAJ,OAAA,QAAAH,uBAAA;EAAA;EAAAF,aAAA,GAAAG,CAAA;EAAvE,KAAAG,UAAU,GAAVA,UAAU;EAAA;EAAAN,aAAA,GAAAG,CAAA;EAAiB,KAAAI,SAAS,GAATA,SAAS;AAAsC,CAAC;AAAA;AAAAP,aAAA,GAAAG,CAAA;AADhGO,OAAA,CAAAR,uBAAA,GAAAA,uBAAA;AAEC,IAEYS,iCAAiC;AAAA;AAAA,CAAAX,aAAA,GAAAG,CAAA,YAAAC,aAAA,CAAAC,OAAA,EAC5C,SAAAM,kCAAmBC,YAAoB;EAAA;EAAAZ,aAAA,GAAAQ,CAAA;EAAAR,aAAA,GAAAG,CAAA;EAAA,IAAAM,gBAAA,CAAAJ,OAAA,QAAAM,iCAAA;EAAA;EAAAX,aAAA,GAAAG,CAAA;EAApB,KAAAS,YAAY,GAAZA,YAAY;AAAW,CAAC;AAAA;AAAAZ,aAAA,GAAAG,CAAA;AAD7CO,OAAA,CAAAC,iCAAA,GAAAA,iCAAA", "ignoreList": []}