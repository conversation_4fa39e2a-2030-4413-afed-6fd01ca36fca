{"version": 3, "names": ["cov_1srfhct84d", "actualCoverage", "s", "exports", "mapMyBillContactListResponseToModel", "MyBillContactListModel_1", "require", "response", "f", "map", "bill", "mapBillContactResponseToModel", "_response$accounts", "_response$additions", "_response$additions2", "_response$additions3", "data", "MyBillContactModel", "id", "name", "alias", "category", "activeStatus", "accessContextScope", "accounts", "b", "account", "mapAccountResponseToModel", "additions", "payableAmount", "favoriteStatus", "reminderStatus", "console", "log", "AccountModel", "bankName", "accountNumber", "bankCode", "accountType", "externalId", "bankPostCode"], "sources": ["/Users/<USER>/projects/react-native/payment-module/src/data/mappers/my-bill-contact-list/MyBillContactListMapper.ts"], "sourcesContent": ["import {\n  MyBillContactListModel,\n  MyBillContactModel,\n  AccountModel,\n} from '../../../domain/entities/my-bill-contact-list/MyBillContactListModel';\nimport {\n  MyBillContactListResponse,\n  MyBillContactResponse,\n  AccountResponse,\n} from '../../models/my-bill-contact-list/MyBillContactListResponse';\n\nexport function mapMyBillContactListResponseToModel(response: MyBillContactListResponse): MyBillContactListModel {\n  return response.map(bill => mapBillContactResponseToModel(bill));\n}\n\nfunction mapBillContactResponseToModel(response: MyBillContactResponse): MyBillContactModel {\n  const data = new MyBillContactModel(\n    response.id,\n    response.name,\n    response.alias,\n    response.category,\n    response.activeStatus,\n    response.accessContextScope,\n    response.accounts?.map(account => mapAccountResponseToModel(account)),\n    response.additions?.payableAmount,\n    response.additions?.favoriteStatus as 'ACTIVE' | 'INACTIVE',\n    response.additions?.reminderStatus as 'ACTIVE' | 'INACTIVE',\n  );\n\n  console.log('LOG CONTACT', data);\n  return data;\n}\n\nfunction mapAccountResponseToModel(account: AccountResponse): AccountModel {\n  return new AccountModel(\n    account.bankName,\n    account.accountNumber,\n    account.bankCode,\n    account.accountType,\n    account.externalId,\n    account.bankPostCode,\n  );\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAuBsC;IAAAA,cAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,cAAA;AAAAA,cAAA,GAAAE,CAAA;;;;;;AAZtCC,OAAA,CAAAC,mCAAA,GAAAA,mCAAA;AAXA,IAAAC,wBAAA;AAAA;AAAA,CAAAL,cAAA,GAAAE,CAAA,OAAAI,OAAA;AAWA,SAAgBF,mCAAmCA,CAACG,QAAmC;EAAA;EAAAP,cAAA,GAAAQ,CAAA;EAAAR,cAAA,GAAAE,CAAA;EACrF,OAAOK,QAAQ,CAACE,GAAG,CAAC,UAAAC,IAAI;IAAA;IAAAV,cAAA,GAAAQ,CAAA;IAAAR,cAAA,GAAAE,CAAA;IAAA,OAAIS,6BAA6B,CAACD,IAAI,CAAC;EAAA,EAAC;AAClE;AAEA,SAASC,6BAA6BA,CAACJ,QAA+B;EAAA;EAAAP,cAAA,GAAAQ,CAAA;EAAA,IAAAI,kBAAA,EAAAC,mBAAA,EAAAC,oBAAA,EAAAC,oBAAA;EACpE,IAAMC,IAAI;EAAA;EAAA,CAAAhB,cAAA,GAAAE,CAAA,OAAG,IAAIG,wBAAA,CAAAY,kBAAkB,CACjCV,QAAQ,CAACW,EAAE,EACXX,QAAQ,CAACY,IAAI,EACbZ,QAAQ,CAACa,KAAK,EACdb,QAAQ,CAACc,QAAQ,EACjBd,QAAQ,CAACe,YAAY,EACrBf,QAAQ,CAACgB,kBAAkB,GAAAX,kBAAA,GAC3BL,QAAQ,CAACiB,QAAQ;EAAA;EAAA,CAAAxB,cAAA,GAAAyB,CAAA;EAAA;EAAA,CAAAzB,cAAA,GAAAyB,CAAA,UAAjBb,kBAAA,CAAmBH,GAAG,CAAC,UAAAiB,OAAO;IAAA;IAAA1B,cAAA,GAAAQ,CAAA;IAAAR,cAAA,GAAAE,CAAA;IAAA,OAAIyB,yBAAyB,CAACD,OAAO,CAAC;EAAA,EAAC,IAAAb,mBAAA,GACrEN,QAAQ,CAACqB,SAAS;EAAA;EAAA,CAAA5B,cAAA,GAAAyB,CAAA;EAAA;EAAA,CAAAzB,cAAA,GAAAyB,CAAA,UAAlBZ,mBAAA,CAAoBgB,aAAa,IAAAf,oBAAA,GACjCP,QAAQ,CAACqB,SAAS;EAAA;EAAA,CAAA5B,cAAA,GAAAyB,CAAA;EAAA;EAAA,CAAAzB,cAAA,GAAAyB,CAAA,UAAlBX,oBAAA,CAAoBgB,cAAuC,IAAAf,oBAAA,GAC3DR,QAAQ,CAACqB,SAAS;EAAA;EAAA,CAAA5B,cAAA,GAAAyB,CAAA;EAAA;EAAA,CAAAzB,cAAA,GAAAyB,CAAA,UAAlBV,oBAAA,CAAoBgB,cAAuC,EAC5D;EAAA;EAAA/B,cAAA,GAAAE,CAAA;EAED8B,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEjB,IAAI,CAAC;EAAA;EAAAhB,cAAA,GAAAE,CAAA;EAChC,OAAOc,IAAI;AACb;AAEA,SAASW,yBAAyBA,CAACD,OAAwB;EAAA;EAAA1B,cAAA,GAAAQ,CAAA;EAAAR,cAAA,GAAAE,CAAA;EACzD,OAAO,IAAIG,wBAAA,CAAA6B,YAAY,CACrBR,OAAO,CAACS,QAAQ,EAChBT,OAAO,CAACU,aAAa,EACrBV,OAAO,CAACW,QAAQ,EAChBX,OAAO,CAACY,WAAW,EACnBZ,OAAO,CAACa,UAAU,EAClBb,OAAO,CAACc,YAAY,CACrB;AACH", "ignoreList": []}