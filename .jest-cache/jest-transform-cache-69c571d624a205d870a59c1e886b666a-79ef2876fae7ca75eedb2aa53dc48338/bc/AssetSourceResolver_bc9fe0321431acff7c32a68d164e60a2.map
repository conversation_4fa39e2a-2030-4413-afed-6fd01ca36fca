{"version": 3, "names": ["_interopRequireDefault", "require", "_classCallCheck2", "_createClass2", "PixelRatio", "default", "Platform", "_require", "pickScale", "_require2", "getAndroidResourceFolderName", "getAndroidResourceIdentifier", "get<PERSON><PERSON><PERSON><PERSON>", "invariant", "getScaledAssetPath", "asset", "scale", "scales", "get", "scaleSuffix", "assetDir", "name", "type", "getAssetPathInDrawableFolder", "drawableFolder", "fileName", "assetSupportsNetworkLoads", "OS", "AssetSourceResolver", "serverUrl", "jsbundleUrl", "key", "value", "isLoadedFromServer", "isLoadedFromFileSystem", "_this$jsbundleUrl", "startsWith", "defaultAsset", "assetServerURL", "resolver", "getAssetUsingResolver", "drawableFolderInBundle", "resourceIdentifierWithoutScale", "scaledAssetURLNearBundle", "Error", "JSON", "stringify", "fromSource", "hash", "scaledAssetPath", "_this$jsbundleUrl2", "path", "replace", "_this$jsbundleUrl3", "source", "__packager_asset", "width", "height", "uri", "module", "exports"], "sources": ["AssetSourceResolver.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @flow strict-local\n * @format\n */\n\n'use strict';\n\nexport type ResolvedAssetSource = {|\n  +__packager_asset: boolean,\n  +width: ?number,\n  +height: ?number,\n  +uri: string,\n  +scale: number,\n|};\n\nimport type {\n  AssetDestPathResolver,\n  PackagerAsset,\n} from '@react-native/assets-registry/registry';\n\nconst PixelRatio = require('../Utilities/PixelRatio').default;\nconst Platform = require('../Utilities/Platform');\nconst {pickScale} = require('./AssetUtils');\nconst {\n  getAndroidResourceFolderName,\n  getAndroidResourceIdentifier,\n  getBasePath,\n} = require('@react-native/assets-registry/path-support');\nconst invariant = require('invariant');\n\n/**\n * Returns a path like 'assets/AwesomeModule/<EMAIL>'\n */\nfunction getScaledAssetPath(asset: PackagerAsset): string {\n  const scale = pickScale(asset.scales, PixelRatio.get());\n  const scaleSuffix = scale === 1 ? '' : '@' + scale + 'x';\n  const assetDir = getBasePath(asset);\n  return assetDir + '/' + asset.name + scaleSuffix + '.' + asset.type;\n}\n\n/**\n * Returns a path like 'drawable-mdpi/icon.png'\n */\nfunction getAssetPathInDrawableFolder(asset: PackagerAsset): string {\n  const scale = pickScale(asset.scales, PixelRatio.get());\n  const drawableFolder = getAndroidResourceFolderName(asset, scale);\n  const fileName = getAndroidResourceIdentifier(asset);\n  return drawableFolder + '/' + fileName + '.' + asset.type;\n}\n\n/**\n * Returns true if the asset can be loaded over the network.\n *\n * This prevents an issue loading XML assets on Android. XML asset types like\n * vector drawables can only be loaded from precompiled source. Android does\n * not support loading these over the network, and AAPT precompiles data by\n * breaking path data and resource information apart into multiple files,\n * stuffing it all into the resource table. As a result, we should only attempt\n * to load resources as we would in release builds: by the resource name.\n *\n * For more information, see:\n * https://issuetracker.google.com/issues/62435069\n * https://issuetracker.google.com/issues/68293189\n */\nfunction assetSupportsNetworkLoads(asset: PackagerAsset): boolean {\n  return !(asset.type === 'xml' && Platform.OS === 'android');\n}\n\nclass AssetSourceResolver {\n  serverUrl: ?string;\n  // where the jsbundle is being run from\n  jsbundleUrl: ?string;\n  // the asset to resolve\n  asset: PackagerAsset;\n\n  constructor(serverUrl: ?string, jsbundleUrl: ?string, asset: PackagerAsset) {\n    this.serverUrl = serverUrl;\n    this.jsbundleUrl = jsbundleUrl;\n    this.asset = asset;\n  }\n\n  isLoadedFromServer(): boolean {\n    return (\n      this.serverUrl != null &&\n      this.serverUrl !== '' &&\n      assetSupportsNetworkLoads(this.asset)\n    );\n  }\n\n  isLoadedFromFileSystem(): boolean {\n    return this.jsbundleUrl != null && this.jsbundleUrl?.startsWith('file://');\n  }\n\n  defaultAsset(): ResolvedAssetSource {\n    if (this.isLoadedFromServer()) {\n      return this.assetServerURL();\n    }\n\n    if (this.asset.resolver != null) {\n      return this.getAssetUsingResolver(this.asset.resolver);\n    }\n\n    if (Platform.OS === 'android') {\n      return this.isLoadedFromFileSystem()\n        ? this.drawableFolderInBundle()\n        : this.resourceIdentifierWithoutScale();\n    } else {\n      return this.scaledAssetURLNearBundle();\n    }\n  }\n\n  getAssetUsingResolver(resolver: AssetDestPathResolver): ResolvedAssetSource {\n    switch (resolver) {\n      case 'android':\n        return this.isLoadedFromFileSystem()\n          ? this.drawableFolderInBundle()\n          : this.resourceIdentifierWithoutScale();\n      case 'generic':\n        return this.scaledAssetURLNearBundle();\n      default:\n        throw new Error(\n          \"Don't know how to get asset via provided resolver: \" +\n            resolver +\n            '\\nAsset: ' +\n            JSON.stringify(this.asset, null, '\\t') +\n            '\\nPossible resolvers are:' +\n            JSON.stringify(['android', 'generic'], null, '\\t'),\n        );\n    }\n  }\n\n  /**\n   * Returns an absolute URL which can be used to fetch the asset\n   * from the devserver\n   */\n  assetServerURL(): ResolvedAssetSource {\n    invariant(this.serverUrl != null, 'need server to load from');\n    return this.fromSource(\n      this.serverUrl +\n        getScaledAssetPath(this.asset) +\n        '?platform=' +\n        Platform.OS +\n        '&hash=' +\n        this.asset.hash,\n    );\n  }\n\n  /**\n   * Resolves to just the scaled asset filename\n   * E.g. 'assets/AwesomeModule/<EMAIL>'\n   */\n  scaledAssetPath(): ResolvedAssetSource {\n    return this.fromSource(getScaledAssetPath(this.asset));\n  }\n\n  /**\n   * Resolves to where the bundle is running from, with a scaled asset filename\n   * E.g. 'file:///sdcard/bundle/assets/AwesomeModule/<EMAIL>'\n   */\n  scaledAssetURLNearBundle(): ResolvedAssetSource {\n    const path = this.jsbundleUrl ?? 'file://';\n    return this.fromSource(\n      // Assets can have relative paths outside of the project root.\n      // When bundling them we replace `../` with `_` to make sure they\n      // don't end up outside of the expected assets directory.\n      path + getScaledAssetPath(this.asset).replace(/\\.\\.\\//g, '_'),\n    );\n  }\n\n  /**\n   * The default location of assets bundled with the app, located by\n   * resource identifier\n   * The Android resource system picks the correct scale.\n   * E.g. 'assets_awesomemodule_icon'\n   */\n  resourceIdentifierWithoutScale(): ResolvedAssetSource {\n    invariant(\n      Platform.OS === 'android',\n      'resource identifiers work on Android',\n    );\n    return this.fromSource(getAndroidResourceIdentifier(this.asset));\n  }\n\n  /**\n   * If the jsbundle is running from a sideload location, this resolves assets\n   * relative to its location\n   * E.g. 'file:///sdcard/AwesomeModule/drawable-mdpi/icon.png'\n   */\n  drawableFolderInBundle(): ResolvedAssetSource {\n    const path = this.jsbundleUrl ?? 'file://';\n    return this.fromSource(path + getAssetPathInDrawableFolder(this.asset));\n  }\n\n  fromSource(source: string): ResolvedAssetSource {\n    return {\n      __packager_asset: true,\n      width: this.asset.width,\n      height: this.asset.height,\n      uri: source,\n      scale: pickScale(this.asset.scales, PixelRatio.get()),\n    };\n  }\n\n  static pickScale: (scales: Array<number>, deviceScale?: number) => number =\n    pickScale;\n}\n\nmodule.exports = AssetSourceResolver;\n"], "mappings": "AAUA,YAAY;;AAAC,IAAAA,sBAAA,GAAAC,OAAA;AAAA,IAAAC,gBAAA,GAAAF,sBAAA,CAAAC,OAAA;AAAA,IAAAE,aAAA,GAAAH,sBAAA,CAAAC,OAAA;AAeb,IAAMG,UAAU,GAAGH,OAAO,CAAC,yBAAyB,CAAC,CAACI,OAAO;AAC7D,IAAMC,QAAQ,GAAGL,OAAO,CAAC,uBAAuB,CAAC;AACjD,IAAAM,QAAA,GAAoBN,OAAO,CAAC,cAAc,CAAC;EAApCO,SAAS,GAAAD,QAAA,CAATC,SAAS;AAChB,IAAAC,SAAA,GAIIR,OAAO,CAAC,4CAA4C,CAAC;EAHvDS,4BAA4B,GAAAD,SAAA,CAA5BC,4BAA4B;EAC5BC,4BAA4B,GAAAF,SAAA,CAA5BE,4BAA4B;EAC5BC,WAAW,GAAAH,SAAA,CAAXG,WAAW;AAEb,IAAMC,SAAS,GAAGZ,OAAO,CAAC,WAAW,CAAC;AAKtC,SAASa,kBAAkBA,CAACC,KAAoB,EAAU;EACxD,IAAMC,KAAK,GAAGR,SAAS,CAACO,KAAK,CAACE,MAAM,EAAEb,UAAU,CAACc,GAAG,CAAC,CAAC,CAAC;EACvD,IAAMC,WAAW,GAAGH,KAAK,KAAK,CAAC,GAAG,EAAE,GAAG,GAAG,GAAGA,KAAK,GAAG,GAAG;EACxD,IAAMI,QAAQ,GAAGR,WAAW,CAACG,KAAK,CAAC;EACnC,OAAOK,QAAQ,GAAG,GAAG,GAAGL,KAAK,CAACM,IAAI,GAAGF,WAAW,GAAG,GAAG,GAAGJ,KAAK,CAACO,IAAI;AACrE;AAKA,SAASC,4BAA4BA,CAACR,KAAoB,EAAU;EAClE,IAAMC,KAAK,GAAGR,SAAS,CAACO,KAAK,CAACE,MAAM,EAAEb,UAAU,CAACc,GAAG,CAAC,CAAC,CAAC;EACvD,IAAMM,cAAc,GAAGd,4BAA4B,CAACK,KAAK,EAAEC,KAAK,CAAC;EACjE,IAAMS,QAAQ,GAAGd,4BAA4B,CAACI,KAAK,CAAC;EACpD,OAAOS,cAAc,GAAG,GAAG,GAAGC,QAAQ,GAAG,GAAG,GAAGV,KAAK,CAACO,IAAI;AAC3D;AAgBA,SAASI,yBAAyBA,CAACX,KAAoB,EAAW;EAChE,OAAO,EAAEA,KAAK,CAACO,IAAI,KAAK,KAAK,IAAIhB,QAAQ,CAACqB,EAAE,KAAK,SAAS,CAAC;AAC7D;AAAC,IAEKC,mBAAmB;EAOvB,SAAAA,oBAAYC,SAAkB,EAAEC,WAAoB,EAAEf,KAAoB,EAAE;IAAA,IAAAb,gBAAA,CAAAG,OAAA,QAAAuB,mBAAA;IAC1E,IAAI,CAACC,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACf,KAAK,GAAGA,KAAK;EACpB;EAAC,WAAAZ,aAAA,CAAAE,OAAA,EAAAuB,mBAAA;IAAAG,GAAA;IAAAC,KAAA,EAED,SAAAC,kBAAkBA,CAAA,EAAY;MAC5B,OACE,IAAI,CAACJ,SAAS,IAAI,IAAI,IACtB,IAAI,CAACA,SAAS,KAAK,EAAE,IACrBH,yBAAyB,CAAC,IAAI,CAACX,KAAK,CAAC;IAEzC;EAAC;IAAAgB,GAAA;IAAAC,KAAA,EAED,SAAAE,sBAAsBA,CAAA,EAAY;MAAA,IAAAC,iBAAA;MAChC,OAAO,IAAI,CAACL,WAAW,IAAI,IAAI,MAAAK,iBAAA,GAAI,IAAI,CAACL,WAAW,qBAAhBK,iBAAA,CAAkBC,UAAU,CAAC,SAAS,CAAC;IAC5E;EAAC;IAAAL,GAAA;IAAAC,KAAA,EAED,SAAAK,YAAYA,CAAA,EAAwB;MAClC,IAAI,IAAI,CAACJ,kBAAkB,CAAC,CAAC,EAAE;QAC7B,OAAO,IAAI,CAACK,cAAc,CAAC,CAAC;MAC9B;MAEA,IAAI,IAAI,CAACvB,KAAK,CAACwB,QAAQ,IAAI,IAAI,EAAE;QAC/B,OAAO,IAAI,CAACC,qBAAqB,CAAC,IAAI,CAACzB,KAAK,CAACwB,QAAQ,CAAC;MACxD;MAEA,IAAIjC,QAAQ,CAACqB,EAAE,KAAK,SAAS,EAAE;QAC7B,OAAO,IAAI,CAACO,sBAAsB,CAAC,CAAC,GAChC,IAAI,CAACO,sBAAsB,CAAC,CAAC,GAC7B,IAAI,CAACC,8BAA8B,CAAC,CAAC;MAC3C,CAAC,MAAM;QACL,OAAO,IAAI,CAACC,wBAAwB,CAAC,CAAC;MACxC;IACF;EAAC;IAAAZ,GAAA;IAAAC,KAAA,EAED,SAAAQ,qBAAqBA,CAACD,QAA+B,EAAuB;MAC1E,QAAQA,QAAQ;QACd,KAAK,SAAS;UACZ,OAAO,IAAI,CAACL,sBAAsB,CAAC,CAAC,GAChC,IAAI,CAACO,sBAAsB,CAAC,CAAC,GAC7B,IAAI,CAACC,8BAA8B,CAAC,CAAC;QAC3C,KAAK,SAAS;UACZ,OAAO,IAAI,CAACC,wBAAwB,CAAC,CAAC;QACxC;UACE,MAAM,IAAIC,KAAK,CACb,qDAAqD,GACnDL,QAAQ,GACR,WAAW,GACXM,IAAI,CAACC,SAAS,CAAC,IAAI,CAAC/B,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC,GACtC,2BAA2B,GAC3B8B,IAAI,CAACC,SAAS,CAAC,CAAC,SAAS,EAAE,SAAS,CAAC,EAAE,IAAI,EAAE,IAAI,CACrD,CAAC;MACL;IACF;EAAC;IAAAf,GAAA;IAAAC,KAAA,EAMD,SAAAM,cAAcA,CAAA,EAAwB;MACpCzB,SAAS,CAAC,IAAI,CAACgB,SAAS,IAAI,IAAI,EAAE,0BAA0B,CAAC;MAC7D,OAAO,IAAI,CAACkB,UAAU,CACpB,IAAI,CAAClB,SAAS,GACZf,kBAAkB,CAAC,IAAI,CAACC,KAAK,CAAC,GAC9B,YAAY,GACZT,QAAQ,CAACqB,EAAE,GACX,QAAQ,GACR,IAAI,CAACZ,KAAK,CAACiC,IACf,CAAC;IACH;EAAC;IAAAjB,GAAA;IAAAC,KAAA,EAMD,SAAAiB,eAAeA,CAAA,EAAwB;MACrC,OAAO,IAAI,CAACF,UAAU,CAACjC,kBAAkB,CAAC,IAAI,CAACC,KAAK,CAAC,CAAC;IACxD;EAAC;IAAAgB,GAAA;IAAAC,KAAA,EAMD,SAAAW,wBAAwBA,CAAA,EAAwB;MAAA,IAAAO,kBAAA;MAC9C,IAAMC,IAAI,IAAAD,kBAAA,GAAG,IAAI,CAACpB,WAAW,YAAAoB,kBAAA,GAAI,SAAS;MAC1C,OAAO,IAAI,CAACH,UAAU,CAIpBI,IAAI,GAAGrC,kBAAkB,CAAC,IAAI,CAACC,KAAK,CAAC,CAACqC,OAAO,CAAC,SAAS,EAAE,GAAG,CAC9D,CAAC;IACH;EAAC;IAAArB,GAAA;IAAAC,KAAA,EAQD,SAAAU,8BAA8BA,CAAA,EAAwB;MACpD7B,SAAS,CACPP,QAAQ,CAACqB,EAAE,KAAK,SAAS,EACzB,sCACF,CAAC;MACD,OAAO,IAAI,CAACoB,UAAU,CAACpC,4BAA4B,CAAC,IAAI,CAACI,KAAK,CAAC,CAAC;IAClE;EAAC;IAAAgB,GAAA;IAAAC,KAAA,EAOD,SAAAS,sBAAsBA,CAAA,EAAwB;MAAA,IAAAY,kBAAA;MAC5C,IAAMF,IAAI,IAAAE,kBAAA,GAAG,IAAI,CAACvB,WAAW,YAAAuB,kBAAA,GAAI,SAAS;MAC1C,OAAO,IAAI,CAACN,UAAU,CAACI,IAAI,GAAG5B,4BAA4B,CAAC,IAAI,CAACR,KAAK,CAAC,CAAC;IACzE;EAAC;IAAAgB,GAAA;IAAAC,KAAA,EAED,SAAAe,UAAUA,CAACO,MAAc,EAAuB;MAC9C,OAAO;QACLC,gBAAgB,EAAE,IAAI;QACtBC,KAAK,EAAE,IAAI,CAACzC,KAAK,CAACyC,KAAK;QACvBC,MAAM,EAAE,IAAI,CAAC1C,KAAK,CAAC0C,MAAM;QACzBC,GAAG,EAAEJ,MAAM;QACXtC,KAAK,EAAER,SAAS,CAAC,IAAI,CAACO,KAAK,CAACE,MAAM,EAAEb,UAAU,CAACc,GAAG,CAAC,CAAC;MACtD,CAAC;IACH;EAAC;AAAA;AArIGU,mBAAmB,CAuIhBpB,SAAS,GACdA,SAAS;AAGbmD,MAAM,CAACC,OAAO,GAAGhC,mBAAmB", "ignoreList": []}