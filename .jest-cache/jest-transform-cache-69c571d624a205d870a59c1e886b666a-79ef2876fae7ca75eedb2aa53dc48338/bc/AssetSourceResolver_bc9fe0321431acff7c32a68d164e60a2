39b3814dfb3e882f7611644e7bbe3d7e
'use strict';

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
var _classCallCheck2 = _interopRequireDefault(require("@babel/runtime/helpers/classCallCheck"));
var _createClass2 = _interopRequireDefault(require("@babel/runtime/helpers/createClass"));
var PixelRatio = require('../Utilities/PixelRatio').default;
var Platform = require('../Utilities/Platform');
var _require = require('./AssetUtils'),
  pickScale = _require.pickScale;
var _require2 = require('@react-native/assets-registry/path-support'),
  getAndroidResourceFolderName = _require2.getAndroidResourceFolderName,
  getAndroidResourceIdentifier = _require2.getAndroidResourceIdentifier,
  getBasePath = _require2.getBasePath;
var invariant = require('invariant');
function getScaledAssetPath(asset) {
  var scale = pickScale(asset.scales, PixelRatio.get());
  var scaleSuffix = scale === 1 ? '' : '@' + scale + 'x';
  var assetDir = getBasePath(asset);
  return assetDir + '/' + asset.name + scaleSuffix + '.' + asset.type;
}
function getAssetPathInDrawableFolder(asset) {
  var scale = pickScale(asset.scales, PixelRatio.get());
  var drawableFolder = getAndroidResourceFolderName(asset, scale);
  var fileName = getAndroidResourceIdentifier(asset);
  return drawableFolder + '/' + fileName + '.' + asset.type;
}
function assetSupportsNetworkLoads(asset) {
  return !(asset.type === 'xml' && Platform.OS === 'android');
}
var AssetSourceResolver = function () {
  function AssetSourceResolver(serverUrl, jsbundleUrl, asset) {
    (0, _classCallCheck2.default)(this, AssetSourceResolver);
    this.serverUrl = serverUrl;
    this.jsbundleUrl = jsbundleUrl;
    this.asset = asset;
  }
  return (0, _createClass2.default)(AssetSourceResolver, [{
    key: "isLoadedFromServer",
    value: function isLoadedFromServer() {
      return this.serverUrl != null && this.serverUrl !== '' && assetSupportsNetworkLoads(this.asset);
    }
  }, {
    key: "isLoadedFromFileSystem",
    value: function isLoadedFromFileSystem() {
      var _this$jsbundleUrl;
      return this.jsbundleUrl != null && ((_this$jsbundleUrl = this.jsbundleUrl) == null ? void 0 : _this$jsbundleUrl.startsWith('file://'));
    }
  }, {
    key: "defaultAsset",
    value: function defaultAsset() {
      if (this.isLoadedFromServer()) {
        return this.assetServerURL();
      }
      if (this.asset.resolver != null) {
        return this.getAssetUsingResolver(this.asset.resolver);
      }
      if (Platform.OS === 'android') {
        return this.isLoadedFromFileSystem() ? this.drawableFolderInBundle() : this.resourceIdentifierWithoutScale();
      } else {
        return this.scaledAssetURLNearBundle();
      }
    }
  }, {
    key: "getAssetUsingResolver",
    value: function getAssetUsingResolver(resolver) {
      switch (resolver) {
        case 'android':
          return this.isLoadedFromFileSystem() ? this.drawableFolderInBundle() : this.resourceIdentifierWithoutScale();
        case 'generic':
          return this.scaledAssetURLNearBundle();
        default:
          throw new Error("Don't know how to get asset via provided resolver: " + resolver + '\nAsset: ' + JSON.stringify(this.asset, null, '\t') + '\nPossible resolvers are:' + JSON.stringify(['android', 'generic'], null, '\t'));
      }
    }
  }, {
    key: "assetServerURL",
    value: function assetServerURL() {
      invariant(this.serverUrl != null, 'need server to load from');
      return this.fromSource(this.serverUrl + getScaledAssetPath(this.asset) + '?platform=' + Platform.OS + '&hash=' + this.asset.hash);
    }
  }, {
    key: "scaledAssetPath",
    value: function scaledAssetPath() {
      return this.fromSource(getScaledAssetPath(this.asset));
    }
  }, {
    key: "scaledAssetURLNearBundle",
    value: function scaledAssetURLNearBundle() {
      var _this$jsbundleUrl2;
      var path = (_this$jsbundleUrl2 = this.jsbundleUrl) != null ? _this$jsbundleUrl2 : 'file://';
      return this.fromSource(path + getScaledAssetPath(this.asset).replace(/\.\.\//g, '_'));
    }
  }, {
    key: "resourceIdentifierWithoutScale",
    value: function resourceIdentifierWithoutScale() {
      invariant(Platform.OS === 'android', 'resource identifiers work on Android');
      return this.fromSource(getAndroidResourceIdentifier(this.asset));
    }
  }, {
    key: "drawableFolderInBundle",
    value: function drawableFolderInBundle() {
      var _this$jsbundleUrl3;
      var path = (_this$jsbundleUrl3 = this.jsbundleUrl) != null ? _this$jsbundleUrl3 : 'file://';
      return this.fromSource(path + getAssetPathInDrawableFolder(this.asset));
    }
  }, {
    key: "fromSource",
    value: function fromSource(source) {
      return {
        __packager_asset: true,
        width: this.asset.width,
        height: this.asset.height,
        uri: source,
        scale: pickScale(this.asset.scales, PixelRatio.get())
      };
    }
  }]);
}();
AssetSourceResolver.pickScale = pickScale;
module.exports = AssetSourceResolver;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************