41a8d4519e22e2a11f378a26fbe3c53c
'use strict';

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.RollOutRight = exports.RollOutLeft = exports.RollInRight = exports.RollInLeft = void 0;
var _slicedToArray2 = _interopRequireDefault(require("@babel/runtime/helpers/slicedToArray"));
var _classCallCheck2 = _interopRequireDefault(require("@babel/runtime/helpers/classCallCheck"));
var _createClass2 = _interopRequireDefault(require("@babel/runtime/helpers/createClass"));
var _possibleConstructorReturn2 = _interopRequireDefault(require("@babel/runtime/helpers/possibleConstructorReturn"));
var _getPrototypeOf2 = _interopRequireDefault(require("@babel/runtime/helpers/getPrototypeOf"));
var _inherits2 = _interopRequireDefault(require("@babel/runtime/helpers/inherits"));
var _index = require("../animationBuilder/index.js");
function _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }
function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }
var RollInLeft = exports.RollInLeft = function (_ComplexAnimationBuil) {
  function RollInLeft() {
    var _this;
    (0, _classCallCheck2.default)(this, RollInLeft);
    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {
      args[_key] = arguments[_key];
    }
    _this = _callSuper(this, RollInLeft, [].concat(args));
    _this.build = function () {
      var delayFunction = _this.getDelayFunction();
      var _this$getAnimationAnd = _this.getAnimationAndConfig(),
        _this$getAnimationAnd2 = (0, _slicedToArray2.default)(_this$getAnimationAnd, 2),
        animation = _this$getAnimationAnd2[0],
        config = _this$getAnimationAnd2[1];
      var delay = _this.getDelay();
      var callback = _this.callbackV;
      var initialValues = _this.initialValues;
      return function (values) {
        'worklet';

        return {
          animations: {
            transform: [{
              translateX: delayFunction(delay, animation(0, config))
            }, {
              rotate: delayFunction(delay, animation('0deg', config))
            }]
          },
          initialValues: Object.assign({
            transform: [{
              translateX: -values.windowWidth
            }, {
              rotate: '-180deg'
            }]
          }, initialValues),
          callback: callback
        };
      };
    };
    return _this;
  }
  (0, _inherits2.default)(RollInLeft, _ComplexAnimationBuil);
  return (0, _createClass2.default)(RollInLeft, null, [{
    key: "createInstance",
    value: function createInstance() {
      return new RollInLeft();
    }
  }]);
}(_index.ComplexAnimationBuilder);
RollInLeft.presetName = 'RollInLeft';
var RollInRight = exports.RollInRight = function (_ComplexAnimationBuil2) {
  function RollInRight() {
    var _this2;
    (0, _classCallCheck2.default)(this, RollInRight);
    for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {
      args[_key2] = arguments[_key2];
    }
    _this2 = _callSuper(this, RollInRight, [].concat(args));
    _this2.build = function () {
      var delayFunction = _this2.getDelayFunction();
      var _this2$getAnimationAn = _this2.getAnimationAndConfig(),
        _this2$getAnimationAn2 = (0, _slicedToArray2.default)(_this2$getAnimationAn, 2),
        animation = _this2$getAnimationAn2[0],
        config = _this2$getAnimationAn2[1];
      var delay = _this2.getDelay();
      var callback = _this2.callbackV;
      var initialValues = _this2.initialValues;
      return function (values) {
        'worklet';

        return {
          animations: {
            transform: [{
              translateX: delayFunction(delay, animation(0, config))
            }, {
              rotate: delayFunction(delay, animation('0deg', config))
            }]
          },
          initialValues: Object.assign({
            transform: [{
              translateX: values.windowWidth
            }, {
              rotate: '180deg'
            }]
          }, initialValues),
          callback: callback
        };
      };
    };
    return _this2;
  }
  (0, _inherits2.default)(RollInRight, _ComplexAnimationBuil2);
  return (0, _createClass2.default)(RollInRight, null, [{
    key: "createInstance",
    value: function createInstance() {
      return new RollInRight();
    }
  }]);
}(_index.ComplexAnimationBuilder);
RollInRight.presetName = 'RollInRight';
var RollOutLeft = exports.RollOutLeft = function (_ComplexAnimationBuil3) {
  function RollOutLeft() {
    var _this3;
    (0, _classCallCheck2.default)(this, RollOutLeft);
    for (var _len3 = arguments.length, args = new Array(_len3), _key3 = 0; _key3 < _len3; _key3++) {
      args[_key3] = arguments[_key3];
    }
    _this3 = _callSuper(this, RollOutLeft, [].concat(args));
    _this3.build = function () {
      var delayFunction = _this3.getDelayFunction();
      var _this3$getAnimationAn = _this3.getAnimationAndConfig(),
        _this3$getAnimationAn2 = (0, _slicedToArray2.default)(_this3$getAnimationAn, 2),
        animation = _this3$getAnimationAn2[0],
        config = _this3$getAnimationAn2[1];
      var delay = _this3.getDelay();
      var callback = _this3.callbackV;
      var initialValues = _this3.initialValues;
      return function (values) {
        'worklet';

        return {
          animations: {
            transform: [{
              translateX: delayFunction(delay, animation(-values.windowWidth, config))
            }, {
              rotate: delayFunction(delay, animation('-180deg', config))
            }]
          },
          initialValues: Object.assign({
            transform: [{
              translateX: 0
            }, {
              rotate: '0deg'
            }]
          }, initialValues),
          callback: callback
        };
      };
    };
    return _this3;
  }
  (0, _inherits2.default)(RollOutLeft, _ComplexAnimationBuil3);
  return (0, _createClass2.default)(RollOutLeft, null, [{
    key: "createInstance",
    value: function createInstance() {
      return new RollOutLeft();
    }
  }]);
}(_index.ComplexAnimationBuilder);
RollOutLeft.presetName = 'RollOutLeft';
var RollOutRight = exports.RollOutRight = function (_ComplexAnimationBuil4) {
  function RollOutRight() {
    var _this4;
    (0, _classCallCheck2.default)(this, RollOutRight);
    for (var _len4 = arguments.length, args = new Array(_len4), _key4 = 0; _key4 < _len4; _key4++) {
      args[_key4] = arguments[_key4];
    }
    _this4 = _callSuper(this, RollOutRight, [].concat(args));
    _this4.build = function () {
      var delayFunction = _this4.getDelayFunction();
      var _this4$getAnimationAn = _this4.getAnimationAndConfig(),
        _this4$getAnimationAn2 = (0, _slicedToArray2.default)(_this4$getAnimationAn, 2),
        animation = _this4$getAnimationAn2[0],
        config = _this4$getAnimationAn2[1];
      var delay = _this4.getDelay();
      var callback = _this4.callbackV;
      var initialValues = _this4.initialValues;
      return function (values) {
        'worklet';

        return {
          animations: {
            transform: [{
              translateX: delayFunction(delay, animation(values.windowWidth, config))
            }, {
              rotate: delayFunction(delay, animation('180deg', config))
            }]
          },
          initialValues: Object.assign({
            transform: [{
              translateX: 0
            }, {
              rotate: '0deg'
            }]
          }, initialValues),
          callback: callback
        };
      };
    };
    return _this4;
  }
  (0, _inherits2.default)(RollOutRight, _ComplexAnimationBuil4);
  return (0, _createClass2.default)(RollOutRight, null, [{
    key: "createInstance",
    value: function createInstance() {
      return new RollOutRight();
    }
  }]);
}(_index.ComplexAnimationBuilder);
RollOutRight.presetName = 'RollOutRight';
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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