{"version": 3, "names": ["_interopRequireDefault", "require", "Object", "defineProperty", "exports", "value", "RollOutRight", "RollOutLeft", "RollInRight", "RollInLeft", "_slicedToArray2", "_classCallCheck2", "_createClass2", "_possibleConstructorReturn2", "_getPrototypeOf2", "_inherits2", "_index", "_callSuper", "t", "o", "e", "default", "_isNativeReflectConstruct", "Reflect", "construct", "constructor", "apply", "Boolean", "prototype", "valueOf", "call", "_ComplexAnimationBuil", "_this", "_len", "arguments", "length", "args", "Array", "_key", "concat", "build", "delayFunction", "getDelayFunction", "_this$getAnimationAnd", "getAnimationAndConfig", "_this$getAnimationAnd2", "animation", "config", "delay", "get<PERSON>elay", "callback", "callbackV", "initialValues", "values", "animations", "transform", "translateX", "rotate", "assign", "windowWidth", "key", "createInstance", "ComplexAnimationBuilder", "presetName", "_ComplexAnimationBuil2", "_this2", "_len2", "_key2", "_this2$getAnimationAn", "_this2$getAnimationAn2", "_ComplexAnimationBuil3", "_this3", "_len3", "_key3", "_this3$getAnimationAn", "_this3$getAnimationAn2", "_ComplexAnimationBuil4", "_this4", "_len4", "_key4", "_this4$getAnimationAn", "_this4$getAnimationAn2"], "sources": ["../../../../src/layoutReanimation/defaultAnimations/Roll.ts"], "sourcesContent": [null], "mappings": "AAAA,YAAY;;AAAA,IAAAA,sBAAA,GAAAC,OAAA;AAAAC,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,KAAA;AAAA;AAAAD,OAAA,CAAAE,YAAA,GAAAF,OAAA,CAAAG,WAAA,GAAAH,OAAA,CAAAI,WAAA,GAAAJ,OAAA,CAAAK,UAAA;AAAA,IAAAC,eAAA,GAAAV,sBAAA,CAAAC,OAAA;AAAA,IAAAU,gBAAA,GAAAX,sBAAA,CAAAC,OAAA;AAAA,IAAAW,aAAA,GAAAZ,sBAAA,CAAAC,OAAA;AAAA,IAAAY,2BAAA,GAAAb,sBAAA,CAAAC,OAAA;AAAA,IAAAa,gBAAA,GAAAd,sBAAA,CAAAC,OAAA;AAAA,IAAAc,UAAA,GAAAf,sBAAA,CAAAC,OAAA;AAEZ,IAAAe,MAAA,GAAAf,OAAA;AAA6D,SAAAgB,WAAAC,CAAA,EAAAC,CAAA,EAAAC,CAAA,WAAAD,CAAA,OAAAL,gBAAA,CAAAO,OAAA,EAAAF,CAAA,OAAAN,2BAAA,CAAAQ,OAAA,EAAAH,CAAA,EAAAI,yBAAA,KAAAC,OAAA,CAAAC,SAAA,CAAAL,CAAA,EAAAC,CAAA,YAAAN,gBAAA,CAAAO,OAAA,EAAAH,CAAA,EAAAO,WAAA,IAAAN,CAAA,CAAAO,KAAA,CAAAR,CAAA,EAAAE,CAAA;AAAA,SAAAE,0BAAA,cAAAJ,CAAA,IAAAS,OAAA,CAAAC,SAAA,CAAAC,OAAA,CAAAC,IAAA,CAAAP,OAAA,CAAAC,SAAA,CAAAG,OAAA,iCAAAT,CAAA,aAAAI,yBAAA,YAAAA,0BAAA,aAAAJ,CAAA;AAAA,IAgBhDT,UAAU,GAAAL,OAAA,CAAAK,UAAA,aAAAsB,qBAAA;EAAA,SAAAtB,WAAA;IAAA,IAAAuB,KAAA;IAAA,IAAArB,gBAAA,CAAAU,OAAA,QAAAZ,UAAA;IAAA,SAAAwB,IAAA,GAAAC,SAAA,CAAAC,MAAA,EAAAC,IAAA,OAAAC,KAAA,CAAAJ,IAAA,GAAAK,IAAA,MAAAA,IAAA,GAAAL,IAAA,EAAAK,IAAA;MAAAF,IAAA,CAAAE,IAAA,IAAAJ,SAAA,CAAAI,IAAA;IAAA;IAAAN,KAAA,GAAAf,UAAA,OAAAR,UAAA,KAAA8B,MAAA,CAAAH,IAAA;IAAAJ,KAAA,CAYrBQ,KAAK,GAAG,YAAkC;MACxC,IAAMC,aAAa,GAAGT,KAAA,CAAKU,gBAAgB,CAAC,CAAC;MAC7C,IAAAC,qBAAA,GAA4BX,KAAA,CAAKY,qBAAqB,CAAC,CAAC;QAAAC,sBAAA,OAAAnC,eAAA,CAAAW,OAAA,EAAAsB,qBAAA;QAAjDG,SAAS,GAAAD,sBAAA;QAAEE,MAAM,GAAAF,sBAAA;MACxB,IAAMG,KAAK,GAAGhB,KAAA,CAAKiB,QAAQ,CAAC,CAAC;MAC7B,IAAMC,QAAQ,GAAGlB,KAAA,CAAKmB,SAAS;MAC/B,IAAMC,aAAa,GAAGpB,KAAA,CAAKoB,aAAa;MAExC,OAAQ,UAAAC,MAAiC,EAAK;QAC5C,SAAS;;QACT,OAAO;UACLC,UAAU,EAAE;YACVC,SAAS,EAAE,CACT;cAAEC,UAAU,EAAEf,aAAa,CAACO,KAAK,EAAEF,SAAS,CAAC,CAAC,EAAEC,MAAM,CAAC;YAAE,CAAC,EAC1D;cAAEU,MAAM,EAAEhB,aAAa,CAACO,KAAK,EAAEF,SAAS,CAAC,MAAM,EAAEC,MAAM,CAAC;YAAE,CAAC;UAE/D,CAAC;UACDK,aAAa,EAAAlD,MAAA,CAAAwD,MAAA;YACXH,SAAS,EAAE,CACT;cAAEC,UAAU,EAAE,CAACH,MAAM,CAACM;YAAY,CAAC,EACnC;cAAEF,MAAM,EAAE;YAAU,CAAC;UACtB,GACEL,aAAA,CACJ;UACDF,QAAA,EAAAA;QACF,CAAC;MACH,CAAC;IACH,CAAC;IAAA,OAAAlB,KAAA;EAAA;EAAA,IAAAjB,UAAA,CAAAM,OAAA,EAAAZ,UAAA,EAAAsB,qBAAA;EAAA,WAAAnB,aAAA,CAAAS,OAAA,EAAAZ,UAAA;IAAAmD,GAAA;IAAAvD,KAAA,EAhCD,SAAOwD,cAAcA,CAAA,EAEF;MACjB,OAAO,IAAIpD,UAAU,CAAC,CAAC;IACzB;EAAA;AAAA,EATQqD,8BAAuB;AADpBrD,UAAU,CAIdsD,UAAU,GAAG,YAAY;AAAA,IA8CrBvD,WAAW,GAAAJ,OAAA,CAAAI,WAAA,aAAAwD,sBAAA;EAAA,SAAAxD,YAAA;IAAA,IAAAyD,MAAA;IAAA,IAAAtD,gBAAA,CAAAU,OAAA,QAAAb,WAAA;IAAA,SAAA0D,KAAA,GAAAhC,SAAA,CAAAC,MAAA,EAAAC,IAAA,OAAAC,KAAA,CAAA6B,KAAA,GAAAC,KAAA,MAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA;MAAA/B,IAAA,CAAA+B,KAAA,IAAAjC,SAAA,CAAAiC,KAAA;IAAA;IAAAF,MAAA,GAAAhD,UAAA,OAAAT,WAAA,KAAA+B,MAAA,CAAAH,IAAA;IAAA6B,MAAA,CAYtBzB,KAAK,GAAG,YAAkC;MACxC,IAAMC,aAAa,GAAGwB,MAAA,CAAKvB,gBAAgB,CAAC,CAAC;MAC7C,IAAA0B,qBAAA,GAA4BH,MAAA,CAAKrB,qBAAqB,CAAC,CAAC;QAAAyB,sBAAA,OAAA3D,eAAA,CAAAW,OAAA,EAAA+C,qBAAA;QAAjDtB,SAAS,GAAAuB,sBAAA;QAAEtB,MAAM,GAAAsB,sBAAA;MACxB,IAAMrB,KAAK,GAAGiB,MAAA,CAAKhB,QAAQ,CAAC,CAAC;MAC7B,IAAMC,QAAQ,GAAGe,MAAA,CAAKd,SAAS;MAC/B,IAAMC,aAAa,GAAGa,MAAA,CAAKb,aAAa;MAExC,OAAQ,UAAAC,MAAiC,EAAK;QAC5C,SAAS;;QACT,OAAO;UACLC,UAAU,EAAE;YACVC,SAAS,EAAE,CACT;cAAEC,UAAU,EAAEf,aAAa,CAACO,KAAK,EAAEF,SAAS,CAAC,CAAC,EAAEC,MAAM,CAAC;YAAE,CAAC,EAC1D;cAAEU,MAAM,EAAEhB,aAAa,CAACO,KAAK,EAAEF,SAAS,CAAC,MAAM,EAAEC,MAAM,CAAC;YAAE,CAAC;UAE/D,CAAC;UACDK,aAAa,EAAAlD,MAAA,CAAAwD,MAAA;YACXH,SAAS,EAAE,CAAC;cAAEC,UAAU,EAAEH,MAAM,CAACM;YAAY,CAAC,EAAE;cAAEF,MAAM,EAAE;YAAS,CAAC;UAAC,GAClEL,aAAA,CACJ;UACDF,QAAA,EAAAA;QACF,CAAC;MACH,CAAC;IACH,CAAC;IAAA,OAAAe,MAAA;EAAA;EAAA,IAAAlD,UAAA,CAAAM,OAAA,EAAAb,WAAA,EAAAwD,sBAAA;EAAA,WAAApD,aAAA,CAAAS,OAAA,EAAAb,WAAA;IAAAoD,GAAA;IAAAvD,KAAA,EA7BD,SAAOwD,cAAcA,CAAA,EAEF;MACjB,OAAO,IAAIrD,WAAW,CAAC,CAAC;IAC1B;EAAA;AAAA,EATQsD,8BAAuB;AADpBtD,WAAW,CAIfuD,UAAU,GAAG,aAAa;AAAA,IA2CtBxD,WAAW,GAAAH,OAAA,CAAAG,WAAA,aAAA+D,sBAAA;EAAA,SAAA/D,YAAA;IAAA,IAAAgE,MAAA;IAAA,IAAA5D,gBAAA,CAAAU,OAAA,QAAAd,WAAA;IAAA,SAAAiE,KAAA,GAAAtC,SAAA,CAAAC,MAAA,EAAAC,IAAA,OAAAC,KAAA,CAAAmC,KAAA,GAAAC,KAAA,MAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA;MAAArC,IAAA,CAAAqC,KAAA,IAAAvC,SAAA,CAAAuC,KAAA;IAAA;IAAAF,MAAA,GAAAtD,UAAA,OAAAV,WAAA,KAAAgC,MAAA,CAAAH,IAAA;IAAAmC,MAAA,CAYtB/B,KAAK,GAAG,YAAkC;MACxC,IAAMC,aAAa,GAAG8B,MAAA,CAAK7B,gBAAgB,CAAC,CAAC;MAC7C,IAAAgC,qBAAA,GAA4BH,MAAA,CAAK3B,qBAAqB,CAAC,CAAC;QAAA+B,sBAAA,OAAAjE,eAAA,CAAAW,OAAA,EAAAqD,qBAAA;QAAjD5B,SAAS,GAAA6B,sBAAA;QAAE5B,MAAM,GAAA4B,sBAAA;MACxB,IAAM3B,KAAK,GAAGuB,MAAA,CAAKtB,QAAQ,CAAC,CAAC;MAC7B,IAAMC,QAAQ,GAAGqB,MAAA,CAAKpB,SAAS;MAC/B,IAAMC,aAAa,GAAGmB,MAAA,CAAKnB,aAAa;MAExC,OAAQ,UAAAC,MAAiC,EAAK;QAC5C,SAAS;;QACT,OAAO;UACLC,UAAU,EAAE;YACVC,SAAS,EAAE,CACT;cACEC,UAAU,EAAEf,aAAa,CACvBO,KAAK,EACLF,SAAS,CAAC,CAACO,MAAM,CAACM,WAAW,EAAEZ,MAAM,CACvC;YACF,CAAC,EACD;cAAEU,MAAM,EAAEhB,aAAa,CAACO,KAAK,EAAEF,SAAS,CAAC,SAAS,EAAEC,MAAM,CAAC;YAAE,CAAC;UAElE,CAAC;UACDK,aAAa,EAAAlD,MAAA,CAAAwD,MAAA;YACXH,SAAS,EAAE,CAAC;cAAEC,UAAU,EAAE;YAAE,CAAC,EAAE;cAAEC,MAAM,EAAE;YAAO,CAAC;UAAC,GAC/CL,aAAA,CACJ;UACDF,QAAA,EAAAA;QACF,CAAC;MACH,CAAC;IACH,CAAC;IAAA,OAAAqB,MAAA;EAAA;EAAA,IAAAxD,UAAA,CAAAM,OAAA,EAAAd,WAAA,EAAA+D,sBAAA;EAAA,WAAA1D,aAAA,CAAAS,OAAA,EAAAd,WAAA;IAAAqD,GAAA;IAAAvD,KAAA,EAlCD,SAAOwD,cAAcA,CAAA,EAEF;MACjB,OAAO,IAAItD,WAAW,CAAC,CAAC;IAC1B;EAAA;AAAA,EATQuD,8BAAuB;AADpBvD,WAAW,CAIfwD,UAAU,GAAG,aAAa;AAAA,IAgDtBzD,YAAY,GAAAF,OAAA,CAAAE,YAAA,aAAAsE,sBAAA;EAAA,SAAAtE,aAAA;IAAA,IAAAuE,MAAA;IAAA,IAAAlE,gBAAA,CAAAU,OAAA,QAAAf,YAAA;IAAA,SAAAwE,KAAA,GAAA5C,SAAA,CAAAC,MAAA,EAAAC,IAAA,OAAAC,KAAA,CAAAyC,KAAA,GAAAC,KAAA,MAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA;MAAA3C,IAAA,CAAA2C,KAAA,IAAA7C,SAAA,CAAA6C,KAAA;IAAA;IAAAF,MAAA,GAAA5D,UAAA,OAAAX,YAAA,KAAAiC,MAAA,CAAAH,IAAA;IAAAyC,MAAA,CAYvBrC,KAAK,GAAG,YAAkC;MACxC,IAAMC,aAAa,GAAGoC,MAAA,CAAKnC,gBAAgB,CAAC,CAAC;MAC7C,IAAAsC,qBAAA,GAA4BH,MAAA,CAAKjC,qBAAqB,CAAC,CAAC;QAAAqC,sBAAA,OAAAvE,eAAA,CAAAW,OAAA,EAAA2D,qBAAA;QAAjDlC,SAAS,GAAAmC,sBAAA;QAAElC,MAAM,GAAAkC,sBAAA;MACxB,IAAMjC,KAAK,GAAG6B,MAAA,CAAK5B,QAAQ,CAAC,CAAC;MAC7B,IAAMC,QAAQ,GAAG2B,MAAA,CAAK1B,SAAS;MAC/B,IAAMC,aAAa,GAAGyB,MAAA,CAAKzB,aAAa;MAExC,OAAQ,UAAAC,MAAiC,EAAK;QAC5C,SAAS;;QACT,OAAO;UACLC,UAAU,EAAE;YACVC,SAAS,EAAE,CACT;cACEC,UAAU,EAAEf,aAAa,CACvBO,KAAK,EACLF,SAAS,CAACO,MAAM,CAACM,WAAW,EAAEZ,MAAM,CACtC;YACF,CAAC,EACD;cAAEU,MAAM,EAAEhB,aAAa,CAACO,KAAK,EAAEF,SAAS,CAAC,QAAQ,EAAEC,MAAM,CAAC;YAAE,CAAC;UAEjE,CAAC;UACDK,aAAa,EAAAlD,MAAA,CAAAwD,MAAA;YACXH,SAAS,EAAE,CAAC;cAAEC,UAAU,EAAE;YAAE,CAAC,EAAE;cAAEC,MAAM,EAAE;YAAO,CAAC;UAAC,GAC/CL,aAAA,CACJ;UACDF,QAAA,EAAAA;QACF,CAAC;MACH,CAAC;IACH,CAAC;IAAA,OAAA2B,MAAA;EAAA;EAAA,IAAA9D,UAAA,CAAAM,OAAA,EAAAf,YAAA,EAAAsE,sBAAA;EAAA,WAAAhE,aAAA,CAAAS,OAAA,EAAAf,YAAA;IAAAsD,GAAA;IAAAvD,KAAA,EAlCD,SAAOwD,cAAcA,CAAA,EAEF;MACjB,OAAO,IAAIvD,YAAY,CAAC,CAAC;IAC3B;EAAA;AAAA,EATQwD,8BAAuB;AADpBxD,YAAY,CAIhByD,UAAU,GAAG,cAAc", "ignoreList": []}