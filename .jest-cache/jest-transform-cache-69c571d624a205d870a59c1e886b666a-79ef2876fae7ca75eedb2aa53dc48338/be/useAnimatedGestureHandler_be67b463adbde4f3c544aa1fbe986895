2533e0b880fac81354542b069ebcae37
'use strict';

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.useAnimatedGestureHandler = useAnimatedGestureHandler;
var _useHandler2 = require("./useHandler.js");
var _useEvent = require("./useEvent.js");
var EVENT_TYPE = {
  UNDETERMINED: 0,
  FAILED: 1,
  BEGAN: 2,
  CANCELLED: 3,
  ACTIVE: 4,
  END: 5
};
function useAnimatedGestureHandler(handlers, dependencies) {
  var _useHandler = (0, _useHandler2.useHandler)(handlers, dependencies),
    context = _useHandler.context,
    doDependenciesDiffer = _useHandler.doDependenciesDiffer,
    useWeb = _useHandler.useWeb;
  var handler = function handler(e) {
    'worklet';

    var event = useWeb ? e.nativeEvent : e;
    if (event.state === EVENT_TYPE.BEGAN && handlers.onStart) {
      handlers.onStart(event, context);
    }
    if (event.state === EVENT_TYPE.ACTIVE && handlers.onActive) {
      handlers.onActive(event, context);
    }
    if (event.oldState === EVENT_TYPE.ACTIVE && event.state === EVENT_TYPE.END && handlers.onEnd) {
      handlers.onEnd(event, context);
    }
    if (event.oldState === EVENT_TYPE.BEGAN && event.state === EVENT_TYPE.FAILED && handlers.onFail) {
      handlers.onFail(event, context);
    }
    if (event.oldState === EVENT_TYPE.ACTIVE && event.state === EVENT_TYPE.CANCELLED && handlers.onCancel) {
      handlers.onCancel(event, context);
    }
    if ((event.oldState === EVENT_TYPE.BEGAN || event.oldState === EVENT_TYPE.ACTIVE) && event.state !== EVENT_TYPE.BEGAN && event.state !== EVENT_TYPE.ACTIVE && handlers.onFinish) {
      handlers.onFinish(event, context, event.state === EVENT_TYPE.CANCELLED || event.state === EVENT_TYPE.FAILED);
    }
  };
  if (useWeb) {
    return handler;
  }
  return (0, _useEvent.useEvent)(handler, ['onGestureHandlerStateChange', 'onGestureHandlerEvent'], doDependenciesDiffer);
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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