a79fa99d62b257ececda639fc5a5426a
'use strict';

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.isFirstReactRender = isFirstReactRender;
exports.isReactRendering = isReactRendering;
var _react = _interopRequireDefault(require("react"));
function getCurrentReactOwner() {
  var _ReactSharedInternals;
  var ReactSharedInternals = _react.default.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED || _react.default.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;
  return ReactSharedInternals == null || (_ReactSharedInternals = ReactSharedInternals.ReactCurrentOwner) == null ? void 0 : _ReactSharedInternals.current;
}
function isReactRendering() {
  return !!getCurrentReactOwner();
}
function isFirstReactRender() {
  var currentOwner = getCurrentReactOwner();
  return currentOwner && !(currentOwner != null && currentOwner.alternate);
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJfaW50ZXJvcFJlcXVpcmVEZWZhdWx0IiwicmVxdWlyZSIsIk9iamVjdCIsImRlZmluZVByb3BlcnR5IiwiZXhwb3J0cyIsInZhbHVlIiwiaXNGaXJzdFJlYWN0UmVuZGVyIiwiaXNSZWFjdFJlbmRlcmluZyIsIl9yZWFjdCIsImdldEN1cnJlbnRSZWFjdE93bmVyIiwiX1JlYWN0U2hhcmVkSW50ZXJuYWxzIiwiUmVhY3RTaGFyZWRJbnRlcm5hbHMiLCJSZWFjdCIsIl9fU0VDUkVUX0lOVEVSTkFMU19ET19OT1RfVVNFX09SX1lPVV9XSUxMX0JFX0ZJUkVEIiwiX19DTElFTlRfSU5URVJOQUxTX0RPX05PVF9VU0VfT1JfV0FSTl9VU0VSU19USEVZX0NBTk5PVF9VUEdSQURFIiwiUmVhY3RDdXJyZW50T3duZXIiLCJjdXJyZW50IiwiY3VycmVudE93bmVyIiwiYWx0ZXJuYXRlIl0sInNvdXJjZXMiOlsiLi4vLi4vc3JjL3JlYWN0VXRpbHMudHMiXSwic291cmNlc0NvbnRlbnQiOltudWxsXSwibWFwcGluZ3MiOiJBQUFBLFlBQVk7O0FBQUEsSUFBQUEsc0JBQUEsR0FBQUMsT0FBQTtBQUFBQyxNQUFBLENBQUFDLGNBQUEsQ0FBQUMsT0FBQTtFQUFBQyxLQUFBO0FBQUE7QUFBQUQsT0FBQSxDQUFBRSxrQkFBQSxHQUFBQSxrQkFBQTtBQUFBRixPQUFBLENBQUFHLGdCQUFBLEdBQUFBLGdCQUFBO0FBQ1osSUFBQUMsTUFBQSxHQUFBUixzQkFBQSxDQUFBQyxPQUFBO0FBRUEsU0FBU1Esb0JBQW9CQSxDQUFBLEVBQUc7RUFBQSxJQUFBQyxxQkFBQTtFQUM5QixJQUFNQyxvQkFBb0IsR0FFeEJDLGNBQUssQ0FBQ0Msa0RBQWtELElBRXhERCxjQUFLLENBQUNFLCtEQUErRDtFQUN2RSxPQUFPSCxvQkFBb0IsYUFBQUQscUJBQUEsR0FBcEJDLG9CQUFvQixDQUFFSSxpQkFBaUIscUJBQXZDTCxxQkFBQSxDQUF5Q00sT0FBTztBQUN6RDtBQUVPLFNBQVNULGdCQUFnQkEsQ0FBQSxFQUFHO0VBQ2pDLE9BQU8sQ0FBQyxDQUFDRSxvQkFBb0IsQ0FBQyxDQUFDO0FBQ2pDO0FBRU8sU0FBU0gsa0JBQWtCQSxDQUFBLEVBQUc7RUFDbkMsSUFBTVcsWUFBWSxHQUFHUixvQkFBb0IsQ0FBQyxDQUFDO0VBRzNDLE9BQU9RLFlBQVksSUFBSSxFQUFDQSxZQUFZLFlBQVpBLFlBQVksQ0FBRUMsU0FBUztBQUNqRCIsImlnbm9yZUxpc3QiOltdfQ==