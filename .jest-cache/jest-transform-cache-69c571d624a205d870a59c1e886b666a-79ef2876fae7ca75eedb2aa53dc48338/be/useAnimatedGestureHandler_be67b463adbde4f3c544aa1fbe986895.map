{"version": 3, "names": ["Object", "defineProperty", "exports", "value", "useAnimatedGestureHandler", "_useHandler2", "require", "_useEvent", "EVENT_TYPE", "UNDETERMINED", "FAILED", "BEGAN", "CANCELLED", "ACTIVE", "END", "handlers", "dependencies", "_use<PERSON><PERSON>ler", "useHandler", "context", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "useWeb", "handler", "e", "event", "nativeEvent", "state", "onStart", "onActive", "oldState", "onEnd", "onFail", "onCancel", "onFinish", "useEvent"], "sources": ["../../../src/hook/useAnimatedGestureHandler.ts"], "sourcesContent": [null], "mappings": "AAAA,YAAY;;AAAAA,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,KAAA;AAAA;AAAAD,OAAA,CAAAE,yBAAA,GAAAA,yBAAA;AAMZ,IAAAC,YAAA,GAAAC,OAAA;AACA,IAAAC,SAAA,GAAAD,OAAA;AAEA,IAAME,UAAU,GAAG;EACjBC,YAAY,EAAE,CAAC;EACfC,MAAM,EAAE,CAAC;EACTC,KAAK,EAAE,CAAC;EACRC,SAAS,EAAE,CAAC;EACZC,MAAM,EAAE,CAAC;EACTC,GAAG,EAAE;AACP,CAAU;AA+DH,SAASV,yBAAyBA,CAIvCW,QAAyC,EAAEC,YAA6B,EAAE;EAG1E,IAAAC,WAAA,GAAkD,IAAAC,uBAAU,EAC1DH,QAAQ,EACRC,YACF,CAAC;IAHOG,OAAO,GAAAF,WAAA,CAAPE,OAAO;IAAEC,oBAAoB,GAAAH,WAAA,CAApBG,oBAAoB;IAAEC,MAAA,GAAAJ,WAAA,CAAAI,MAAA;EAIvC,IAAMC,OAAO,GAAI,SAAXA,OAAOA,CAAIC,CAAmB,EAAK;IACvC,SAAS;;IACT,IAAMC,KAAK,GAAGH,MAAM,GAIdE,CAAC,CAAWE,WAAW,GACxBF,CAA4B;IAEjC,IAAIC,KAAK,CAACE,KAAK,KAAKlB,UAAU,CAACG,KAAK,IAAII,QAAQ,CAACY,OAAO,EAAE;MACxDZ,QAAQ,CAACY,OAAO,CAACH,KAAK,EAAEL,OAAO,CAAC;IAClC;IACA,IAAIK,KAAK,CAACE,KAAK,KAAKlB,UAAU,CAACK,MAAM,IAAIE,QAAQ,CAACa,QAAQ,EAAE;MAC1Db,QAAQ,CAACa,QAAQ,CAACJ,KAAK,EAAEL,OAAO,CAAC;IACnC;IACA,IACEK,KAAK,CAACK,QAAQ,KAAKrB,UAAU,CAACK,MAAM,IACpCW,KAAK,CAACE,KAAK,KAAKlB,UAAU,CAACM,GAAG,IAC9BC,QAAQ,CAACe,KAAK,EACd;MACAf,QAAQ,CAACe,KAAK,CAACN,KAAK,EAAEL,OAAO,CAAC;IAChC;IACA,IACEK,KAAK,CAACK,QAAQ,KAAKrB,UAAU,CAACG,KAAK,IACnCa,KAAK,CAACE,KAAK,KAAKlB,UAAU,CAACE,MAAM,IACjCK,QAAQ,CAACgB,MAAM,EACf;MACAhB,QAAQ,CAACgB,MAAM,CAACP,KAAK,EAAEL,OAAO,CAAC;IACjC;IACA,IACEK,KAAK,CAACK,QAAQ,KAAKrB,UAAU,CAACK,MAAM,IACpCW,KAAK,CAACE,KAAK,KAAKlB,UAAU,CAACI,SAAS,IACpCG,QAAQ,CAACiB,QAAQ,EACjB;MACAjB,QAAQ,CAACiB,QAAQ,CAACR,KAAK,EAAEL,OAAO,CAAC;IACnC;IACA,IACE,CAACK,KAAK,CAACK,QAAQ,KAAKrB,UAAU,CAACG,KAAK,IAClCa,KAAK,CAACK,QAAQ,KAAKrB,UAAU,CAACK,MAAM,KACtCW,KAAK,CAACE,KAAK,KAAKlB,UAAU,CAACG,KAAK,IAChCa,KAAK,CAACE,KAAK,KAAKlB,UAAU,CAACK,MAAM,IACjCE,QAAQ,CAACkB,QAAQ,EACjB;MACAlB,QAAQ,CAACkB,QAAQ,CACfT,KAAK,EACLL,OAAO,EACPK,KAAK,CAACE,KAAK,KAAKlB,UAAU,CAACI,SAAS,IAClCY,KAAK,CAACE,KAAK,KAAKlB,UAAU,CAACE,MAC/B,CAAC;IACH;EACF,CAAC;EAED,IAAIW,MAAM,EAAE;IACV,OAAOC,OAAO;EAChB;EAGA,OAAO,IAAAY,kBAAQ,EACbZ,OAAO,EACP,CAAC,6BAA6B,EAAE,uBAAuB,CAAC,EACxDF,oBAEF,CAAC;AACH", "ignoreList": []}