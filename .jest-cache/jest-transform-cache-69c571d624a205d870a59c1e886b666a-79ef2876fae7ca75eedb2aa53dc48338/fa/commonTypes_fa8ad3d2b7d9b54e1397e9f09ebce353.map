{"version": 3, "names": ["Object", "defineProperty", "exports", "value", "SharedTransitionType", "LayoutAnimationType"], "sources": ["../../../../src/layoutReanimation/animationBuilder/commonTypes.ts"], "sourcesContent": [null], "mappings": "AAAA,YAAY;AAAAA,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,KAAA;AAAA;AAAAD,OAAA,CAAAE,oBAAA,GAAAF,OAAA,CAAAG,mBAAA;AA4FZ,IAAYA,mBAAmB,GAAAH,OAAA,CAAAG,mBAAA,aAAnBA,mBAAmB;EAAnBA,mBAAmB,CAAnBA,mBAAmB;EAAnBA,mBAAmB,CAAnBA,mBAAmB;EAAnBA,mBAAmB,CAAnBA,mBAAmB;EAAnBA,mBAAmB,CAAnBA,mBAAmB;EAAnBA,mBAAmB,CAAnBA,mBAAmB;EAAA,OAAnBA,mBAAmB;AAAA;AA8E/B,IAAYD,oBAAoB,GAAAF,OAAA,CAAAE,oBAAA,aAApBA,oBAAoB;EAApBA,oBAAoB;EAApBA,oBAAoB;EAAA,OAApBA,oBAAoB;AAAA", "ignoreList": []}