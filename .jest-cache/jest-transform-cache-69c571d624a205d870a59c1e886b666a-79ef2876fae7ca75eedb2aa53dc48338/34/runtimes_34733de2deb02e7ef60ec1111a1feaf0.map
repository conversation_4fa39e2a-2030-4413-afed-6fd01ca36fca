{"version": 3, "names": ["_interopRequireDefault", "require", "Object", "defineProperty", "exports", "value", "createWorkletRuntime", "runOnRuntime", "_commonTypes", "_errors", "_initializers", "_index", "_NativeReanimated", "_PlatformChecker", "_shareables", "SHOULD_BE_USE_WEB", "shouldBeUseWeb", "name", "initializer", "config", "__reanimatedLoggerConfig", "NativeReanimatedModule", "makeShareableCloneRecursive", "registerReanimatedError", "registerLoggerConfig", "setupCallGuard", "setupConsole", "workletRuntime", "worklet", "__DEV__", "isWorkletFunction", "ReanimatedError", "_WORKLET", "_len", "arguments", "length", "args", "Array", "_key", "global", "_scheduleOnRuntime", "makeShareableCloneOnUIRecursive", "apply", "_len2", "_key2", "scheduleOnRuntime"], "sources": ["../../src/runtimes.ts"], "sourcesContent": [null], "mappings": "AAAA,YAAY;;AAAA,IAAAA,sBAAA,GAAAC,OAAA;AAAAC,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,KAAA;AAAA;AAAAD,OAAA,CAAAE,oBAAA,GAAAA,oBAAA;AAAAF,OAAA,CAAAG,YAAA,GAAAA,YAAA;AACZ,IAAAC,YAAA,GAAAP,OAAA;AAEA,IAAAQ,OAAA,GAAAR,OAAA;AACA,IAAAS,aAAA,GAAAT,OAAA;AACA,IAAAU,MAAA,GAAAV,OAAA;AACA,IAAAW,iBAAA,GAAAZ,sBAAA,CAAAC,OAAA;AACA,IAAAY,gBAAA,GAAAZ,OAAA;AACA,IAAAa,WAAA,GAAAb,OAAA;AAKA,IAAMc,iBAAiB,GAAG,IAAAC,+BAAc,EAAC,CAAC;AAyBnC,SAASV,oBAAoBA,CAClCW,IAAY,EACZC,WAAuC,EACvB;EAGhB,IAAMC,MAAM,GAAGC,wBAAwB;EACvC,OAAOC,yBAAsB,CAACf,oBAAoB,CAChDW,IAAI,EACJ,IAAAK,uCAA2B,EAAC,YAAM;IAChC,SAAS;;IACT,IAAAC,+BAAuB,EAAC,CAAC;IACzB,IAAAC,2BAAoB,EAACL,MAAM,CAAC;IAC5B,IAAAM,4BAAc,EAAC,CAAC;IAChB,IAAAC,0BAAY,EAAC,CAAC;IACdR,WAAW,YAAXA,WAAW,CAAG,CAAC;EACjB,CAAC,CACH,CAAC;AACH;AAQO,SAASX,YAAYA,CAC1BoB,cAA8B,EAC9BC,OAA2C,EAClB;EACzB,SAAS;;EACT,IAAIC,OAAO,IAAI,CAACd,iBAAiB,IAAI,CAAC,IAAAe,8BAAiB,EAACF,OAAO,CAAC,EAAE;IAChE,MAAM,IAAIG,uBAAe,CACvB,yDAAyD,IACtDC,QAAQ,GACL,8FAA8F,GAC9F,EAAE,CACV,CAAC;EACH;EACA,IAAIA,QAAQ,EAAE;IACZ,OAAO;MAAA,SAAAC,IAAA,GAAAC,SAAA,CAAAC,MAAA,EAAIC,IAAI,OAAAC,KAAA,CAAAJ,IAAA,GAAAK,IAAA,MAAAA,IAAA,GAAAL,IAAA,EAAAK,IAAA;QAAJF,IAAI,CAAAE,IAAA,IAAAJ,SAAA,CAAAI,IAAA;MAAA;MAAA,OACbC,MAAM,CAACC,kBAAkB,CACvBb,cAAc,EACd,IAAAc,2CAA+B,EAAC,YAAM;QACpC,SAAS;;QACTb,OAAO,CAAAc,KAAA,SAAIN,IAAI,CAAC;MAClB,CAAC,CACH,CAAC;IAAA;EACL;EACA,OAAO;IAAA,SAAAO,KAAA,GAAAT,SAAA,CAAAC,MAAA,EAAIC,IAAI,OAAAC,KAAA,CAAAM,KAAA,GAAAC,KAAA,MAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA;MAAJR,IAAI,CAAAQ,KAAA,IAAAV,SAAA,CAAAU,KAAA;IAAA;IAAA,OACbvB,yBAAsB,CAACwB,iBAAiB,CACtClB,cAAc,EACd,IAAAL,uCAA2B,EAAC,YAAM;MAChC,SAAS;;MACTM,OAAO,CAAAc,KAAA,SAAIN,IAAI,CAAC;IAClB,CAAC,CACH,CAAC;EAAA;AACL", "ignoreList": []}