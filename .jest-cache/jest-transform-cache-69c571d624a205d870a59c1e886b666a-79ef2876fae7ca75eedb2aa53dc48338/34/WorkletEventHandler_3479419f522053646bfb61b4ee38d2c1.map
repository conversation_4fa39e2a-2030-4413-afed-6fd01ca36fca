{"version": 3, "names": ["_interopRequireDefault", "require", "Object", "defineProperty", "exports", "value", "WorkletEventHandler", "_classCallCheck2", "_createClass2", "_classPrivateFieldLooseBase2", "_classPrivateFieldLooseKey2", "_core", "_PlatformChecker", "SHOULD_BE_USE_WEB", "shouldBeUseWeb", "jsListener", "eventName", "handler", "evt", "assign", "nativeEvent", "_viewTags", "default", "_registrations", "WorkletEventHandlerNative", "worklet", "eventNames", "writable", "Set", "Map", "key", "updateEventHandler", "newWorklet", "newEvents", "_this", "for<PERSON>ach", "registrationIDs", "id", "unregisterEventHandler", "Array", "from", "tag", "newRegistrations", "map", "registerEventHandler", "set", "registerForEvents", "viewTag", "fallbackEventName", "_this2", "add", "length", "newRegistration", "unregisterFromEvents", "_classPrivateFieldLoo", "delete", "get", "WorkletEventHandlerWeb", "arguments", "undefined", "listeners", "setupWebListeners", "_this3", "_viewTag", "_fallbackEventName"], "sources": ["../../src/WorkletEventHandler.ts"], "sourcesContent": [null], "mappings": "AAAA,YAAY;;AAAA,IAAAA,sBAAA,GAAAC,OAAA;AAAAC,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,KAAA;AAAA;AAAAD,OAAA,CAAAE,mBAAA;AAAA,IAAAC,gBAAA,GAAAP,sBAAA,CAAAC,OAAA;AAAA,IAAAO,aAAA,GAAAR,sBAAA,CAAAC,OAAA;AAAA,IAAAQ,4BAAA,GAAAT,sBAAA,CAAAC,OAAA;AAAA,IAAAS,2BAAA,GAAAV,sBAAA,CAAAC,OAAA;AAEZ,IAAAU,KAAA,GAAAV,OAAA;AAMA,IAAAW,gBAAA,GAAAX,OAAA;AAEA,IAAMY,iBAAiB,GAAG,IAAAC,+BAAc,EAAC,CAAC;AAO1C,SAASC,UAAUA,CACjBC,SAAiB,EACjBC,OAAgD,EAChD;EACA,OAAQ,UAAAC,GAAmB,EAAK;IAC9BD,OAAO,CAAAf,MAAA,CAAAiB,MAAA,KAAMD,GAAG,CAACE,WAAW;MAAEJ,SAAA,EAAAA;IAAA,EAAqC,CAAC;EACtE,CAAC;AACH;AAAA,IAAAK,SAAA,OAAAX,2BAAA,CAAAY,OAAA;AAAA,IAAAC,cAAA,OAAAb,2BAAA,CAAAY,OAAA;AAAA,IAEME,yBAAyB;EAO7B,SAAAA,0BACEC,OAAgD,EAChDC,UAAoB,EACpB;IAAA,IAAAnB,gBAAA,CAAAe,OAAA,QAAAE,yBAAA;IAAAtB,MAAA,CAAAC,cAAA,OAAAkB,SAAA;MAAAM,QAAA;MAAAtB,KAAA;IAAA;IAAAH,MAAA,CAAAC,cAAA,OAAAoB,cAAA;MAAAI,QAAA;MAAAtB,KAAA;IAAA;IACA,IAAI,CAACoB,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,UAAU,GAAGA,UAAU;IAC5B,IAAAjB,4BAAA,CAAAa,OAAA,MAAI,EAAAD,SAAA,EAAAA,SAAA,IAAa,IAAIO,GAAG,CAAS,CAAC;IAClC,IAAAnB,4BAAA,CAAAa,OAAA,MAAI,EAAAC,cAAA,EAAAA,cAAA,IAAkB,IAAIM,GAAG,CAAmB,CAAC;EACnD;EAAA,WAAArB,aAAA,CAAAc,OAAA,EAAAE,yBAAA;IAAAM,GAAA;IAAAzB,KAAA,EAEA,SAAA0B,kBAAkBA,CAChBC,UAAmD,EACnDC,SAAmB,EACb;MAAA,IAAAC,KAAA;MAEN,IAAI,CAACT,OAAO,GAAGO,UAAU;MACzB,IAAI,CAACN,UAAU,GAAGO,SAAS;MAG3B,IAAAxB,4BAAA,CAAAa,OAAA,MAAI,EAAAC,cAAA,EAAAA,cAAA,EAAgBY,OAAO,CAAE,UAAAC,eAAe,EAAK;QAC/CA,eAAe,CAACD,OAAO,CAAE,UAAAE,EAAE;UAAA,OAAK,IAAAC,4BAAsB,EAACD,EAAE,CAAC;QAAA,EAAC;MAE7D,CAAC,CAAC;MAGFE,KAAK,CAACC,IAAI,KAAA/B,4BAAA,CAAAa,OAAA,EAAC,IAAI,EAAAD,SAAA,EAAAA,SAAA,CAAU,CAAC,CAACc,OAAO,CAAE,UAAAM,GAAG,EAAK;QAC1C,IAAMC,gBAAgB,GAAGR,KAAI,CAACR,UAAU,CAACiB,GAAG,CAAE,UAAA3B,SAAS;UAAA,OACrD,IAAA4B,0BAAoB,EAACV,KAAI,CAACT,OAAO,EAAET,SAAS,EAAEyB,GAAG,CACnD;QAAA,EAAC;QACD,IAAAhC,4BAAA,CAAAa,OAAA,EAAAY,KAAI,EAAAX,cAAA,EAAAA,cAAA,EAAgBsB,GAAG,CAACJ,GAAG,EAAEC,gBAAgB,CAAC;MAChD,CAAC,CAAC;IACJ;EAAA;IAAAZ,GAAA;IAAAzB,KAAA,EAEA,SAAAyC,iBAAiBA,CAACC,OAAe,EAAEC,iBAA0B,EAAQ;MAAA,IAAAC,MAAA;MACnE,IAAAxC,4BAAA,CAAAa,OAAA,MAAI,EAAAD,SAAA,EAAAA,SAAA,EAAW6B,GAAG,CAACH,OAAO,CAAC;MAE3B,IAAML,gBAAgB,GAAG,IAAI,CAAChB,UAAU,CAACiB,GAAG,CAAE,UAAA3B,SAAS;QAAA,OACrD,IAAA4B,0BAAoB,EAACK,MAAI,CAACxB,OAAO,EAAET,SAAS,EAAE+B,OAAO,CACvD;MAAA,EAAC;MACD,IAAAtC,4BAAA,CAAAa,OAAA,MAAI,EAAAC,cAAA,EAAAA,cAAA,EAAgBsB,GAAG,CAACE,OAAO,EAAEL,gBAAgB,CAAC;MAElD,IAAI,IAAI,CAAChB,UAAU,CAACyB,MAAM,KAAK,CAAC,IAAIH,iBAAiB,EAAE;QACrD,IAAMI,eAAe,GAAG,IAAAR,0BAAoB,EAC1C,IAAI,CAACnB,OAAO,EACZuB,iBAAiB,EACjBD,OACF,CAAC;QACD,IAAAtC,4BAAA,CAAAa,OAAA,MAAI,EAAAC,cAAA,EAAAA,cAAA,EAAgBsB,GAAG,CAACE,OAAO,EAAE,CAACK,eAAe,CAAC,CAAC;MACrD;IACF;EAAA;IAAAtB,GAAA;IAAAzB,KAAA,EAEA,SAAAgD,oBAAoBA,CAACN,OAAe,EAAQ;MAAA,IAAAO,qBAAA;MAC1C,IAAA7C,4BAAA,CAAAa,OAAA,MAAI,EAAAD,SAAA,EAAAA,SAAA,EAAWkC,MAAM,CAACR,OAAO,CAAC;MAC9B,CAAAO,qBAAA,OAAA7C,4BAAA,CAAAa,OAAA,MAAI,EAAAC,cAAA,EAAAA,cAAA,EAAgBiC,GAAG,CAACT,OAAO,CAAC,aAAhCO,qBAAA,CAAkCnB,OAAO,CAAE,UAAAE,EAAE,EAAK;QAChD,IAAAC,4BAAsB,EAACD,EAAE,CAAC;MAC5B,CAAC,CAAC;MACF,IAAA5B,4BAAA,CAAAa,OAAA,MAAI,EAAAC,cAAA,EAAAA,cAAA,EAAgBgC,MAAM,CAACR,OAAO,CAAC;IACrC;EAAA;AAAA;AAAA,IAGIU,sBAAsB;EAU1B,SAAAA,uBACEhC,OAAgD,EAEhD;IAAA,IADAC,UAAoB,GAAAgC,SAAA,CAAAP,MAAA,QAAAO,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,EAAE;IAAA,IAAAnD,gBAAA,CAAAe,OAAA,QAAAmC,sBAAA;IAEzB,IAAI,CAAChC,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACkC,SAAS,GAAG,CAAC,CAAC;IACnB,IAAI,CAACC,iBAAiB,CAAC,CAAC;EAC1B;EAAA,WAAArD,aAAA,CAAAc,OAAA,EAAAmC,sBAAA;IAAA3B,GAAA;IAAAzB,KAAA,EAEA,SAAAwD,iBAAiBA,CAAA,EAAG;MAAA,IAAAC,MAAA;MAClB,IAAI,CAACF,SAAS,GAAG,CAAC,CAAC;MACnB,IAAI,CAAClC,UAAU,CAACS,OAAO,CAAE,UAAAnB,SAAS,EAAK;QACrC8C,MAAI,CAACF,SAAS,CAAC5C,SAAS,CAAC,GAAGD,UAAU,CAACC,SAAS,EAAE8C,MAAI,CAACrC,OAAO,CAAC;MACjE,CAAC,CAAC;IACJ;EAAA;IAAAK,GAAA;IAAAzB,KAAA,EAEA,SAAA0B,kBAAkBA,CAChBC,UAAmD,EACnDC,SAAmB,EACb;MAEN,IAAI,CAACR,OAAO,GAAGO,UAAU;MACzB,IAAI,CAACN,UAAU,GAAGO,SAAS;MAC3B,IAAI,CAAC4B,iBAAiB,CAAC,CAAC;IAC1B;EAAA;IAAA/B,GAAA;IAAAzB,KAAA,EAEA,SAAAyC,iBAAiBA,CAACiB,QAAgB,EAAEC,kBAA2B,EAAQ,CACrE;EAAA;IAAAlC,GAAA;IAAAzB,KAAA,EAGF,SAAAgD,oBAAoBA,CAACU,QAAgB,EAAQ,CAC3C;EAAA;AAAA;AAIG,IAAMzD,mBAAmB,GAAAF,OAAA,CAAAE,mBAAA,GAAGO,iBAAiB,GAChD4C,sBAAsB,GACtBjC,yBAAyB", "ignoreList": []}