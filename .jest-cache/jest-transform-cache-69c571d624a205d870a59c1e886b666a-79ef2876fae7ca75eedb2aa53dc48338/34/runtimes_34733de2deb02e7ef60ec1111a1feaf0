aca3963fe7a028b80dc2ff12e87348a1
'use strict';

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.createWorkletRuntime = createWorkletRuntime;
exports.runOnRuntime = runOnRuntime;
var _commonTypes = require("./commonTypes.js");
var _errors = require("./errors.js");
var _initializers = require("./initializers.js");
var _index = require("./logger/index.js");
var _NativeReanimated = _interopRequireDefault(require("./NativeReanimated"));
var _PlatformChecker = require("./PlatformChecker.js");
var _shareables = require("./shareables.js");
var SHOULD_BE_USE_WEB = (0, _PlatformChecker.shouldBeUseWeb)();
function createWorkletRuntime(name, initializer) {
  var config = __reanimatedLoggerConfig;
  return _NativeReanimated.default.createWorkletRuntime(name, (0, _shareables.makeShareableCloneRecursive)(function () {
    'worklet';

    (0, _errors.registerReanimatedError)();
    (0, _index.registerLoggerConfig)(config);
    (0, _initializers.setupCallGuard)();
    (0, _initializers.setupConsole)();
    initializer == null || initializer();
  }));
}
function runOnRuntime(workletRuntime, worklet) {
  'worklet';

  if (__DEV__ && !SHOULD_BE_USE_WEB && !(0, _commonTypes.isWorkletFunction)(worklet)) {
    throw new _errors.ReanimatedError('The function passed to `runOnRuntime` is not a worklet.' + (_WORKLET ? ' Please make sure that `processNestedWorklets` option in Reanimated Babel plugin is enabled.' : ''));
  }
  if (_WORKLET) {
    return function () {
      for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {
        args[_key] = arguments[_key];
      }
      return global._scheduleOnRuntime(workletRuntime, (0, _shareables.makeShareableCloneOnUIRecursive)(function () {
        'worklet';

        worklet.apply(void 0, args);
      }));
    };
  }
  return function () {
    for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {
      args[_key2] = arguments[_key2];
    }
    return _NativeReanimated.default.scheduleOnRuntime(workletRuntime, (0, _shareables.makeShareableCloneRecursive)(function () {
      'worklet';

      worklet.apply(void 0, args);
    }));
  };
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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