573f656df807cb9fcf9f921e626a094b
'use strict';

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.PinwheelOut = exports.PinwheelIn = void 0;
var _slicedToArray2 = _interopRequireDefault(require("@babel/runtime/helpers/slicedToArray"));
var _classCallCheck2 = _interopRequireDefault(require("@babel/runtime/helpers/classCallCheck"));
var _createClass2 = _interopRequireDefault(require("@babel/runtime/helpers/createClass"));
var _possibleConstructorReturn2 = _interopRequireDefault(require("@babel/runtime/helpers/possibleConstructorReturn"));
var _getPrototypeOf2 = _interopRequireDefault(require("@babel/runtime/helpers/getPrototypeOf"));
var _inherits2 = _interopRequireDefault(require("@babel/runtime/helpers/inherits"));
var _index = require("../animationBuilder/index.js");
function _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }
function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }
var PinwheelIn = exports.PinwheelIn = function (_ComplexAnimationBuil) {
  function PinwheelIn() {
    var _this;
    (0, _classCallCheck2.default)(this, PinwheelIn);
    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {
      args[_key] = arguments[_key];
    }
    _this = _callSuper(this, PinwheelIn, [].concat(args));
    _this.build = function () {
      var delayFunction = _this.getDelayFunction();
      var _this$getAnimationAnd = _this.getAnimationAndConfig(),
        _this$getAnimationAnd2 = (0, _slicedToArray2.default)(_this$getAnimationAnd, 2),
        animation = _this$getAnimationAnd2[0],
        config = _this$getAnimationAnd2[1];
      var delay = _this.getDelay();
      var callback = _this.callbackV;
      var initialValues = _this.initialValues;
      return function () {
        'worklet';

        return {
          animations: {
            opacity: delayFunction(delay, animation(1, config)),
            transform: [{
              scale: delayFunction(delay, animation(1, config))
            }, {
              rotate: delayFunction(delay, animation('0', config))
            }]
          },
          initialValues: Object.assign({
            opacity: 0,
            transform: [{
              scale: 0
            }, {
              rotate: '5'
            }]
          }, initialValues),
          callback: callback
        };
      };
    };
    return _this;
  }
  (0, _inherits2.default)(PinwheelIn, _ComplexAnimationBuil);
  return (0, _createClass2.default)(PinwheelIn, null, [{
    key: "createInstance",
    value: function createInstance() {
      return new PinwheelIn();
    }
  }]);
}(_index.ComplexAnimationBuilder);
PinwheelIn.presetName = 'PinwheelIn';
var PinwheelOut = exports.PinwheelOut = function (_ComplexAnimationBuil2) {
  function PinwheelOut() {
    var _this2;
    (0, _classCallCheck2.default)(this, PinwheelOut);
    for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {
      args[_key2] = arguments[_key2];
    }
    _this2 = _callSuper(this, PinwheelOut, [].concat(args));
    _this2.build = function () {
      var delayFunction = _this2.getDelayFunction();
      var _this2$getAnimationAn = _this2.getAnimationAndConfig(),
        _this2$getAnimationAn2 = (0, _slicedToArray2.default)(_this2$getAnimationAn, 2),
        animation = _this2$getAnimationAn2[0],
        config = _this2$getAnimationAn2[1];
      var delay = _this2.getDelay();
      var callback = _this2.callbackV;
      var initialValues = _this2.initialValues;
      return function () {
        'worklet';

        return {
          animations: {
            opacity: delayFunction(delay, animation(0, config)),
            transform: [{
              scale: delayFunction(delay, animation(0, config))
            }, {
              rotate: delayFunction(delay, animation('5', config))
            }]
          },
          initialValues: Object.assign({
            opacity: 1,
            transform: [{
              scale: 1
            }, {
              rotate: '0'
            }]
          }, initialValues),
          callback: callback
        };
      };
    };
    return _this2;
  }
  (0, _inherits2.default)(PinwheelOut, _ComplexAnimationBuil2);
  return (0, _createClass2.default)(PinwheelOut, null, [{
    key: "createInstance",
    value: function createInstance() {
      return new PinwheelOut();
    }
  }]);
}(_index.ComplexAnimationBuilder);
PinwheelOut.presetName = 'PinwheelOut';
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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