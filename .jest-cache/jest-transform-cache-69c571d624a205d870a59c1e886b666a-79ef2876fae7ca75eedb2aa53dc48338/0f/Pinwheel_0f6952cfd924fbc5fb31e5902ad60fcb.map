{"version": 3, "names": ["_interopRequireDefault", "require", "Object", "defineProperty", "exports", "value", "PinwheelOut", "PinwheelIn", "_slicedToArray2", "_classCallCheck2", "_createClass2", "_possibleConstructorReturn2", "_getPrototypeOf2", "_inherits2", "_index", "_callSuper", "t", "o", "e", "default", "_isNativeReflectConstruct", "Reflect", "construct", "constructor", "apply", "Boolean", "prototype", "valueOf", "call", "_ComplexAnimationBuil", "_this", "_len", "arguments", "length", "args", "Array", "_key", "concat", "build", "delayFunction", "getDelayFunction", "_this$getAnimationAnd", "getAnimationAndConfig", "_this$getAnimationAnd2", "animation", "config", "delay", "get<PERSON>elay", "callback", "callbackV", "initialValues", "animations", "opacity", "transform", "scale", "rotate", "assign", "key", "createInstance", "ComplexAnimationBuilder", "presetName", "_ComplexAnimationBuil2", "_this2", "_len2", "_key2", "_this2$getAnimationAn", "_this2$getAnimationAn2"], "sources": ["../../../../src/layoutReanimation/defaultAnimations/Pinwheel.ts"], "sourcesContent": [null], "mappings": "AAAA,YAAY;;AAAA,IAAAA,sBAAA,GAAAC,OAAA;AAAAC,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,KAAA;AAAA;AAAAD,OAAA,CAAAE,WAAA,GAAAF,OAAA,CAAAG,UAAA;AAAA,IAAAC,eAAA,GAAAR,sBAAA,CAAAC,OAAA;AAAA,IAAAQ,gBAAA,GAAAT,sBAAA,CAAAC,OAAA;AAAA,IAAAS,aAAA,GAAAV,sBAAA,CAAAC,OAAA;AAAA,IAAAU,2BAAA,GAAAX,sBAAA,CAAAC,OAAA;AAAA,IAAAW,gBAAA,GAAAZ,sBAAA,CAAAC,OAAA;AAAA,IAAAY,UAAA,GAAAb,sBAAA,CAAAC,OAAA;AAEZ,IAAAa,MAAA,GAAAb,OAAA;AAA6D,SAAAc,WAAAC,CAAA,EAAAC,CAAA,EAAAC,CAAA,WAAAD,CAAA,OAAAL,gBAAA,CAAAO,OAAA,EAAAF,CAAA,OAAAN,2BAAA,CAAAQ,OAAA,EAAAH,CAAA,EAAAI,yBAAA,KAAAC,OAAA,CAAAC,SAAA,CAAAL,CAAA,EAAAC,CAAA,YAAAN,gBAAA,CAAAO,OAAA,EAAAH,CAAA,EAAAO,WAAA,IAAAN,CAAA,CAAAO,KAAA,CAAAR,CAAA,EAAAE,CAAA;AAAA,SAAAE,0BAAA,cAAAJ,CAAA,IAAAS,OAAA,CAAAC,SAAA,CAAAC,OAAA,CAAAC,IAAA,CAAAP,OAAA,CAAAC,SAAA,CAAAG,OAAA,iCAAAT,CAAA,aAAAI,yBAAA,YAAAA,0BAAA,aAAAJ,CAAA;AAAA,IAehDT,UAAU,GAAAH,OAAA,CAAAG,UAAA,aAAAsB,qBAAA;EAAA,SAAAtB,WAAA;IAAA,IAAAuB,KAAA;IAAA,IAAArB,gBAAA,CAAAU,OAAA,QAAAZ,UAAA;IAAA,SAAAwB,IAAA,GAAAC,SAAA,CAAAC,MAAA,EAAAC,IAAA,OAAAC,KAAA,CAAAJ,IAAA,GAAAK,IAAA,MAAAA,IAAA,GAAAL,IAAA,EAAAK,IAAA;MAAAF,IAAA,CAAAE,IAAA,IAAAJ,SAAA,CAAAI,IAAA;IAAA;IAAAN,KAAA,GAAAf,UAAA,OAAAR,UAAA,KAAA8B,MAAA,CAAAH,IAAA;IAAAJ,KAAA,CAYrBQ,KAAK,GAAG,YAAkC;MACxC,IAAMC,aAAa,GAAGT,KAAA,CAAKU,gBAAgB,CAAC,CAAC;MAC7C,IAAAC,qBAAA,GAA4BX,KAAA,CAAKY,qBAAqB,CAAC,CAAC;QAAAC,sBAAA,OAAAnC,eAAA,CAAAW,OAAA,EAAAsB,qBAAA;QAAjDG,SAAS,GAAAD,sBAAA;QAAEE,MAAM,GAAAF,sBAAA;MACxB,IAAMG,KAAK,GAAGhB,KAAA,CAAKiB,QAAQ,CAAC,CAAC;MAC7B,IAAMC,QAAQ,GAAGlB,KAAA,CAAKmB,SAAS;MAC/B,IAAMC,aAAa,GAAGpB,KAAA,CAAKoB,aAAa;MAExC,OAAO,YAAM;QACX,SAAS;;QACT,OAAO;UACLC,UAAU,EAAE;YACVC,OAAO,EAAEb,aAAa,CAACO,KAAK,EAAEF,SAAS,CAAC,CAAC,EAAEC,MAAM,CAAC,CAAC;YACnDQ,SAAS,EAAE,CACT;cACEC,KAAK,EAAEf,aAAa,CAACO,KAAK,EAAEF,SAAS,CAAC,CAAC,EAAEC,MAAM,CAAC;YAClD,CAAC,EACD;cACEU,MAAM,EAAEhB,aAAa,CAACO,KAAK,EAAEF,SAAS,CAAC,GAAG,EAAEC,MAAM,CAAC;YACrD,CAAC;UAEL,CAAC;UACDK,aAAa,EAAAhD,MAAA,CAAAsD,MAAA;YACXJ,OAAO,EAAE,CAAC;YACVC,SAAS,EAAE,CACT;cACEC,KAAK,EAAE;YACT,CAAC,EACD;cACEC,MAAM,EAAE;YACV,CAAC;UACF,GACEL,aAAA,CACJ;UACDF,QAAA,EAAAA;QACF,CAAC;MACH,CAAC;IACH,CAAC;IAAA,OAAAlB,KAAA;EAAA;EAAA,IAAAjB,UAAA,CAAAM,OAAA,EAAAZ,UAAA,EAAAsB,qBAAA;EAAA,WAAAnB,aAAA,CAAAS,OAAA,EAAAZ,UAAA;IAAAkD,GAAA;IAAApD,KAAA,EA1CD,SAAOqD,cAAcA,CAAA,EAEF;MACjB,OAAO,IAAInD,UAAU,CAAC,CAAC;IACzB;EAAA;AAAA,EATQoD,8BAAuB;AADpBpD,UAAU,CAIdqD,UAAU,GAAG,YAAY;AAAA,IAwDrBtD,WAAW,GAAAF,OAAA,CAAAE,WAAA,aAAAuD,sBAAA;EAAA,SAAAvD,YAAA;IAAA,IAAAwD,MAAA;IAAA,IAAArD,gBAAA,CAAAU,OAAA,QAAAb,WAAA;IAAA,SAAAyD,KAAA,GAAA/B,SAAA,CAAAC,MAAA,EAAAC,IAAA,OAAAC,KAAA,CAAA4B,KAAA,GAAAC,KAAA,MAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA;MAAA9B,IAAA,CAAA8B,KAAA,IAAAhC,SAAA,CAAAgC,KAAA;IAAA;IAAAF,MAAA,GAAA/C,UAAA,OAAAT,WAAA,KAAA+B,MAAA,CAAAH,IAAA;IAAA4B,MAAA,CAYtBxB,KAAK,GAAG,YAAkC;MACxC,IAAMC,aAAa,GAAGuB,MAAA,CAAKtB,gBAAgB,CAAC,CAAC;MAC7C,IAAAyB,qBAAA,GAA4BH,MAAA,CAAKpB,qBAAqB,CAAC,CAAC;QAAAwB,sBAAA,OAAA1D,eAAA,CAAAW,OAAA,EAAA8C,qBAAA;QAAjDrB,SAAS,GAAAsB,sBAAA;QAAErB,MAAM,GAAAqB,sBAAA;MACxB,IAAMpB,KAAK,GAAGgB,MAAA,CAAKf,QAAQ,CAAC,CAAC;MAC7B,IAAMC,QAAQ,GAAGc,MAAA,CAAKb,SAAS;MAC/B,IAAMC,aAAa,GAAGY,MAAA,CAAKZ,aAAa;MAExC,OAAO,YAAM;QACX,SAAS;;QACT,OAAO;UACLC,UAAU,EAAE;YACVC,OAAO,EAAEb,aAAa,CAACO,KAAK,EAAEF,SAAS,CAAC,CAAC,EAAEC,MAAM,CAAC,CAAC;YACnDQ,SAAS,EAAE,CACT;cACEC,KAAK,EAAEf,aAAa,CAACO,KAAK,EAAEF,SAAS,CAAC,CAAC,EAAEC,MAAM,CAAC;YAClD,CAAC,EACD;cACEU,MAAM,EAAEhB,aAAa,CAACO,KAAK,EAAEF,SAAS,CAAC,GAAG,EAAEC,MAAM,CAAC;YACrD,CAAC;UAEL,CAAC;UACDK,aAAa,EAAAhD,MAAA,CAAAsD,MAAA;YACXJ,OAAO,EAAE,CAAC;YACVC,SAAS,EAAE,CACT;cACEC,KAAK,EAAE;YACT,CAAC,EACD;cACEC,MAAM,EAAE;YACV,CAAC;UACF,GACEL,aAAA,CACJ;UACDF,QAAA,EAAAA;QACF,CAAC;MACH,CAAC;IACH,CAAC;IAAA,OAAAc,MAAA;EAAA;EAAA,IAAAjD,UAAA,CAAAM,OAAA,EAAAb,WAAA,EAAAuD,sBAAA;EAAA,WAAAnD,aAAA,CAAAS,OAAA,EAAAb,WAAA;IAAAmD,GAAA;IAAApD,KAAA,EA1CD,SAAOqD,cAAcA,CAAA,EAEF;MACjB,OAAO,IAAIpD,WAAW,CAAC,CAAC;IAC1B;EAAA;AAAA,EATQqD,8BAAuB;AADpBrD,WAAW,CAIfsD,UAAU,GAAG,aAAa", "ignoreList": []}