{"version": 3, "names": ["objectFitMap", "contain", "cover", "fill", "none", "convertObjectFitToResizeMode", "objectFit", "undefined"], "sources": ["ImageUtils.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @flow strict\n * @format\n */\n\nimport type {ImageResizeMode} from './ImageResizeMode';\n\nconst objectFitMap: {[string]: ImageResizeMode} = {\n  contain: 'contain',\n  cover: 'cover',\n  fill: 'stretch',\n  'scale-down': 'contain',\n  none: 'none',\n};\n\nexport function convertObjectFitToResizeMode(\n  objectFit: ?string,\n): ?ImageResizeMode {\n  return objectFit != null ? objectFitMap[objectFit] : undefined;\n}\n"], "mappings": ";;;;AAYA,IAAMA,YAAyC,GAAG;EAChDC,OAAO,EAAE,SAAS;EAClBC,KAAK,EAAE,OAAO;EACdC,IAAI,EAAE,SAAS;EACf,YAAY,EAAE,SAAS;EACvBC,IAAI,EAAE;AACR,CAAC;AAEM,SAASC,4BAA4BA,CAC1CC,SAAkB,EACA;EAClB,OAAOA,SAAS,IAAI,IAAI,GAAGN,YAAY,CAACM,SAAS,CAAC,GAAGC,SAAS;AAChE", "ignoreList": []}