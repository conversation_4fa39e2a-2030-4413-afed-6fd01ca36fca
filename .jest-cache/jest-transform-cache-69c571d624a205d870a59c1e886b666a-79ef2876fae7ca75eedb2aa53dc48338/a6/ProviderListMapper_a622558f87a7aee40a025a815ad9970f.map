{"version": 3, "names": ["exports", "mapProviderListResponseToModel", "ProviderListModel_1", "cov_m0lc78nip", "s", "require", "response", "f", "map", "provider", "ProviderModel", "subgroupId", "serviceCode", "categoryCode", "subgroupNameVn", "subgroupNameEn", "partnerCode", "partner<PERSON>ame", "autoBillSupport", "voucherSupport", "phoneRequired", "isRecommend", "partnerType", "payFee", "type", "paymentSupport", "description"], "sources": ["/Users/<USER>/projects/react-native/payment-module/src/data/mappers/provider-list/ProviderListMapper.ts"], "sourcesContent": ["import {ProviderListResponse} from '../../models/provider-list/ProviderListResponse';\nimport {ProviderListModel, ProviderModel} from '../../../domain/entities/provider-list/ProviderListModel';\n\nexport function mapProviderListResponseToModel(response: ProviderListResponse): ProviderListModel {\n  return response.map(\n    provider =>\n      new ProviderModel(\n        provider.subgroupId,\n        provider.serviceCode,\n        provider.categoryCode,\n        provider.subgroupId,\n        provider.subgroupNameVn,\n        provider.subgroupNameEn,\n        provider.partnerCode,\n        provider.partnerName,\n        provider.autoBillSupport,\n        provider.voucherSupport,\n        provider.phoneRequired,\n        provider.isRecommend,\n        provider.partnerType,\n        provider.payFee,\n        provider.type,\n        provider.paymentSupport,\n        provider.description,\n      ),\n  );\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGAA,OAAA,CAAAC,8BAAA,GAAAA,8BAAA;AAFA,IAAAC,mBAAA;AAAA;AAAA,CAAAC,aAAA,GAAAC,CAAA,OAAAC,OAAA;AAEA,SAAgBJ,8BAA8BA,CAACK,QAA8B;EAAA;EAAAH,aAAA,GAAAI,CAAA;EAAAJ,aAAA,GAAAC,CAAA;EAC3E,OAAOE,QAAQ,CAACE,GAAG,CACjB,UAAAC,QAAQ;IAAA;IAAAN,aAAA,GAAAI,CAAA;IAAAJ,aAAA,GAAAC,CAAA;IAAA,OACN,IAAIF,mBAAA,CAAAQ,aAAa,CACfD,QAAQ,CAACE,UAAU,EACnBF,QAAQ,CAACG,WAAW,EACpBH,QAAQ,CAACI,YAAY,EACrBJ,QAAQ,CAACE,UAAU,EACnBF,QAAQ,CAACK,cAAc,EACvBL,QAAQ,CAACM,cAAc,EACvBN,QAAQ,CAACO,WAAW,EACpBP,QAAQ,CAACQ,WAAW,EACpBR,QAAQ,CAACS,eAAe,EACxBT,QAAQ,CAACU,cAAc,EACvBV,QAAQ,CAACW,aAAa,EACtBX,QAAQ,CAACY,WAAW,EACpBZ,QAAQ,CAACa,WAAW,EACpBb,QAAQ,CAACc,MAAM,EACfd,QAAQ,CAACe,IAAI,EACbf,QAAQ,CAACgB,cAAc,EACvBhB,QAAQ,CAACiB,WAAW,CACrB;EAAA,EACJ;AACH", "ignoreList": []}