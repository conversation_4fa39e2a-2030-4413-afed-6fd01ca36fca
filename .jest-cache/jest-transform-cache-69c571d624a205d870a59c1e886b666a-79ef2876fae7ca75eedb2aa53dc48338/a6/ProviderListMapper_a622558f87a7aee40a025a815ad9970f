4355a3c2a68599ab64214c94e10e5e7b
"use strict";

/* istanbul ignore next */
function cov_m0lc78nip() {
  var path = "/Users/<USER>/projects/react-native/payment-module/src/data/mappers/provider-list/ProviderListMapper.ts";
  var hash = "1b9426672df8c81967290bafdd6412d2cabf2abc";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/projects/react-native/payment-module/src/data/mappers/provider-list/ProviderListMapper.ts",
    statementMap: {
      "0": {
        start: {
          line: 3,
          column: 0
        },
        end: {
          line: 5,
          column: 3
        }
      },
      "1": {
        start: {
          line: 6,
          column: 0
        },
        end: {
          line: 6,
          column: 72
        }
      },
      "2": {
        start: {
          line: 7,
          column: 26
        },
        end: {
          line: 7,
          column: 93
        }
      },
      "3": {
        start: {
          line: 9,
          column: 2
        },
        end: {
          line: 11,
          column: 5
        }
      },
      "4": {
        start: {
          line: 10,
          column: 4
        },
        end: {
          line: 10,
          column: 428
        }
      }
    },
    fnMap: {
      "0": {
        name: "mapProviderListResponseToModel",
        decl: {
          start: {
            line: 8,
            column: 9
          },
          end: {
            line: 8,
            column: 39
          }
        },
        loc: {
          start: {
            line: 8,
            column: 50
          },
          end: {
            line: 12,
            column: 1
          }
        },
        line: 8
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 9,
            column: 22
          },
          end: {
            line: 9,
            column: 23
          }
        },
        loc: {
          start: {
            line: 9,
            column: 42
          },
          end: {
            line: 11,
            column: 3
          }
        },
        line: 9
      }
    },
    branchMap: {},
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0
    },
    f: {
      "0": 0,
      "1": 0
    },
    b: {},
    inputSourceMap: {
      version: 3,
      file: undefined,
      names: ["exports", "mapProviderListResponseToModel", "ProviderListModel_1", "require", "response", "map", "provider", "ProviderModel", "subgroupId", "serviceCode", "categoryCode", "subgroupNameVn", "subgroupNameEn", "partnerCode", "partnerName", "autoBillSupport", "voucherSupport", "phoneRequired", "isRecommend", "partnerType", "payFee", "type", "paymentSupport", "description"],
      sourceRoot: undefined,
      sources: ["/Users/<USER>/projects/react-native/payment-module/src/data/mappers/provider-list/ProviderListMapper.ts"],
      sourcesContent: ["import {ProviderListResponse} from '../../models/provider-list/ProviderListResponse';\nimport {ProviderListModel, ProviderModel} from '../../../domain/entities/provider-list/ProviderListModel';\n\nexport function mapProviderListResponseToModel(response: ProviderListResponse): ProviderListModel {\n  return response.map(\n    provider =>\n      new ProviderModel(\n        provider.subgroupId,\n        provider.serviceCode,\n        provider.categoryCode,\n        provider.subgroupId,\n        provider.subgroupNameVn,\n        provider.subgroupNameEn,\n        provider.partnerCode,\n        provider.partnerName,\n        provider.autoBillSupport,\n        provider.voucherSupport,\n        provider.phoneRequired,\n        provider.isRecommend,\n        provider.partnerType,\n        provider.payFee,\n        provider.type,\n        provider.paymentSupport,\n        provider.description,\n      ),\n  );\n}\n"],
      mappings: ";;;;;AAGAA,OAAA,CAAAC,8BAAA,GAAAA,8BAAA;AAFA,IAAAC,mBAAA,GAAAC,OAAA;AAEA,SAAgBF,8BAA8BA,CAACG,QAA8B;EAC3E,OAAOA,QAAQ,CAACC,GAAG,CACjB,UAAAC,QAAQ;IAAA,OACN,IAAIJ,mBAAA,CAAAK,aAAa,CACfD,QAAQ,CAACE,UAAU,EACnBF,QAAQ,CAACG,WAAW,EACpBH,QAAQ,CAACI,YAAY,EACrBJ,QAAQ,CAACE,UAAU,EACnBF,QAAQ,CAACK,cAAc,EACvBL,QAAQ,CAACM,cAAc,EACvBN,QAAQ,CAACO,WAAW,EACpBP,QAAQ,CAACQ,WAAW,EACpBR,QAAQ,CAACS,eAAe,EACxBT,QAAQ,CAACU,cAAc,EACvBV,QAAQ,CAACW,aAAa,EACtBX,QAAQ,CAACY,WAAW,EACpBZ,QAAQ,CAACa,WAAW,EACpBb,QAAQ,CAACc,MAAM,EACfd,QAAQ,CAACe,IAAI,EACbf,QAAQ,CAACgB,cAAc,EACvBhB,QAAQ,CAACiB,WAAW,CACrB;EAAA,EACJ;AACH",
      ignoreList: []
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "1b9426672df8c81967290bafdd6412d2cabf2abc"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_m0lc78nip = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_m0lc78nip();
cov_m0lc78nip().s[0]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_m0lc78nip().s[1]++;
exports.mapProviderListResponseToModel = mapProviderListResponseToModel;
var ProviderListModel_1 =
/* istanbul ignore next */
(cov_m0lc78nip().s[2]++, require("../../../domain/entities/provider-list/ProviderListModel"));
function mapProviderListResponseToModel(response) {
  /* istanbul ignore next */
  cov_m0lc78nip().f[0]++;
  cov_m0lc78nip().s[3]++;
  return response.map(function (provider) {
    /* istanbul ignore next */
    cov_m0lc78nip().f[1]++;
    cov_m0lc78nip().s[4]++;
    return new ProviderListModel_1.ProviderModel(provider.subgroupId, provider.serviceCode, provider.categoryCode, provider.subgroupId, provider.subgroupNameVn, provider.subgroupNameEn, provider.partnerCode, provider.partnerName, provider.autoBillSupport, provider.voucherSupport, provider.phoneRequired, provider.isRecommend, provider.partnerType, provider.payFee, provider.type, provider.paymentSupport, provider.description);
  });
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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