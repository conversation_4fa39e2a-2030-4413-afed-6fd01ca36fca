{"version": 3, "names": ["_interopRequireDefault", "require", "Object", "defineProperty", "exports", "value", "JumpingTransition", "_classCallCheck2", "_createClass2", "_possibleConstructorReturn2", "_getPrototypeOf2", "_inherits2", "_index", "_Easing", "_index2", "_callSuper", "t", "o", "e", "default", "_isNativeReflectConstruct", "Reflect", "construct", "constructor", "apply", "Boolean", "prototype", "valueOf", "call", "_BaseAnimationBuilder", "_this", "_len", "arguments", "length", "args", "Array", "_key", "concat", "build", "_this$durationV", "delayFunction", "getDelayFunction", "callback", "callbackV", "delay", "get<PERSON>elay", "duration", "durationV", "halfDuration", "config", "values", "d", "Math", "max", "abs", "targetOriginX", "currentOriginX", "targetOriginY", "currentOriginY", "initialValues", "originX", "originY", "width", "currentWidth", "height", "currentHeight", "animations", "withTiming", "withSequence", "min", "easing", "Easing", "out", "exp", "assign", "bounce", "targetWidth", "targetHeight", "key", "createInstance", "BaseAnimationBuilder", "presetName"], "sources": ["../../../../src/layoutReanimation/defaultTransitions/JumpingTransition.ts"], "sourcesContent": [null], "mappings": "AAAA,YAAY;;AAAA,IAAAA,sBAAA,GAAAC,OAAA;AAAAC,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,KAAA;AAAA;AAAAD,OAAA,CAAAE,iBAAA;AAAA,IAAAC,gBAAA,GAAAP,sBAAA,CAAAC,OAAA;AAAA,IAAAO,aAAA,GAAAR,sBAAA,CAAAC,OAAA;AAAA,IAAAQ,2BAAA,GAAAT,sBAAA,CAAAC,OAAA;AAAA,IAAAS,gBAAA,GAAAV,sBAAA,CAAAC,OAAA;AAAA,IAAAU,UAAA,GAAAX,sBAAA,CAAAC,OAAA;AAKZ,IAAAW,MAAA,GAAAX,OAAA;AACA,IAAAY,OAAA,GAAAZ,OAAA;AACA,IAAAa,OAAA,GAAAb,OAAA;AAA0D,SAAAc,WAAAC,CAAA,EAAAC,CAAA,EAAAC,CAAA,WAAAD,CAAA,OAAAP,gBAAA,CAAAS,OAAA,EAAAF,CAAA,OAAAR,2BAAA,CAAAU,OAAA,EAAAH,CAAA,EAAAI,yBAAA,KAAAC,OAAA,CAAAC,SAAA,CAAAL,CAAA,EAAAC,CAAA,YAAAR,gBAAA,CAAAS,OAAA,EAAAH,CAAA,EAAAO,WAAA,IAAAN,CAAA,CAAAO,KAAA,CAAAR,CAAA,EAAAE,CAAA;AAAA,SAAAE,0BAAA,cAAAJ,CAAA,IAAAS,OAAA,CAAAC,SAAA,CAAAC,OAAA,CAAAC,IAAA,CAAAP,OAAA,CAAAC,SAAA,CAAAG,OAAA,iCAAAT,CAAA,aAAAI,yBAAA,YAAAA,0BAAA,aAAAJ,CAAA;AAAA,IAW7CV,iBAAiB,GAAAF,OAAA,CAAAE,iBAAA,aAAAuB,qBAAA;EAAA,SAAAvB,kBAAA;IAAA,IAAAwB,KAAA;IAAA,IAAAvB,gBAAA,CAAAY,OAAA,QAAAb,iBAAA;IAAA,SAAAyB,IAAA,GAAAC,SAAA,CAAAC,MAAA,EAAAC,IAAA,OAAAC,KAAA,CAAAJ,IAAA,GAAAK,IAAA,MAAAA,IAAA,GAAAL,IAAA,EAAAK,IAAA;MAAAF,IAAA,CAAAE,IAAA,IAAAJ,SAAA,CAAAI,IAAA;IAAA;IAAAN,KAAA,GAAAf,UAAA,OAAAT,iBAAA,KAAA+B,MAAA,CAAAH,IAAA;IAAAJ,KAAA,CAY5BQ,KAAK,GAAG,YAA+B;MAAA,IAAAC,eAAA;MACrC,IAAMC,aAAa,GAAGV,KAAA,CAAKW,gBAAgB,CAAC,CAAC;MAC7C,IAAMC,QAAQ,GAAGZ,KAAA,CAAKa,SAAS;MAC/B,IAAMC,KAAK,GAAGd,KAAA,CAAKe,QAAQ,CAAC,CAAC;MAC7B,IAAMC,QAAQ,IAAAP,eAAA,GAAGT,KAAA,CAAKiB,SAAS,YAAAR,eAAA,GAAI,GAAG;MACtC,IAAMS,YAAY,GAAGF,QAAQ,GAAG,CAAC;MACjC,IAAMG,MAAM,GAAG;QAAEH,QAAA,EAAAA;MAAS,CAAC;MAE3B,OAAQ,UAAAI,MAAM,EAAK;QACjB,SAAS;;QACT,IAAMC,CAAC,GAAGC,IAAI,CAACC,GAAG,CAChBD,IAAI,CAACE,GAAG,CAACJ,MAAM,CAACK,aAAa,GAAGL,MAAM,CAACM,cAAc,CAAC,EACtDJ,IAAI,CAACE,GAAG,CAACJ,MAAM,CAACO,aAAa,GAAGP,MAAM,CAACQ,cAAc,CACvD,CAAC;QACD,OAAO;UACLC,aAAa,EAAE;YACbC,OAAO,EAAEV,MAAM,CAACM,cAAc;YAC9BK,OAAO,EAAEX,MAAM,CAACQ,cAAc;YAC9BI,KAAK,EAAEZ,MAAM,CAACa,YAAY;YAC1BC,MAAM,EAAEd,MAAM,CAACe;UACjB,CAAC;UACDC,UAAU,EAAE;YACVN,OAAO,EAAEpB,aAAa,CACpBI,KAAK,EACL,IAAAuB,iBAAU,EAACjB,MAAM,CAACK,aAAa,EAAEN,MAAM,CACzC,CAAC;YACDY,OAAO,EAAErB,aAAa,CACpBI,KAAK,EACL,IAAAwB,mBAAY,EACV,IAAAD,iBAAU,EACRf,IAAI,CAACiB,GAAG,CAACnB,MAAM,CAACO,aAAa,EAAEP,MAAM,CAACQ,cAAc,CAAC,GAAGP,CAAC,EACzD;cACEL,QAAQ,EAAEE,YAAY;cACtBsB,MAAM,EAAEC,cAAM,CAACC,GAAG,CAACD,cAAM,CAACE,GAAG;YAC/B,CACF,CAAC,EACD,IAAAN,iBAAU,EAACjB,MAAM,CAACO,aAAa,EAAAvD,MAAA,CAAAwE,MAAA,KAC1BzB,MAAM;cACTH,QAAQ,EAAEE,YAAY;cACtBsB,MAAM,EAAEC,cAAM,CAACI;YAAA,EAChB,CACH,CACF,CAAC;YACDb,KAAK,EAAEtB,aAAa,CAACI,KAAK,EAAE,IAAAuB,iBAAU,EAACjB,MAAM,CAAC0B,WAAW,EAAE3B,MAAM,CAAC,CAAC;YACnEe,MAAM,EAAExB,aAAa,CAACI,KAAK,EAAE,IAAAuB,iBAAU,EAACjB,MAAM,CAAC2B,YAAY,EAAE5B,MAAM,CAAC;UACtE,CAAC;UACDP,QAAA,EAAAA;QACF,CAAC;MACH,CAAC;IACH,CAAC;IAAA,OAAAZ,KAAA;EAAA;EAAA,IAAAnB,UAAA,CAAAQ,OAAA,EAAAb,iBAAA,EAAAuB,qBAAA;EAAA,WAAArB,aAAA,CAAAW,OAAA,EAAAb,iBAAA;IAAAwE,GAAA;IAAAzE,KAAA,EAvDD,SAAO0E,cAAcA,CAAA,EAEF;MACjB,OAAO,IAAIzE,iBAAiB,CAAC,CAAC;IAChC;EAAA;AAAA,EATQ0E,4BAAoB;AADjB1E,iBAAiB,CAIrB2E,UAAU,GAAG,mBAAmB", "ignoreList": []}