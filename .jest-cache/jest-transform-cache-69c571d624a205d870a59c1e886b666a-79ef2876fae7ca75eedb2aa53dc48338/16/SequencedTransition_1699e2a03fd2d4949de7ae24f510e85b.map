{"version": 3, "names": ["_interopRequireDefault", "require", "Object", "defineProperty", "exports", "value", "SequencedTransition", "_classCallCheck2", "_createClass2", "_possibleConstructorReturn2", "_getPrototypeOf2", "_inherits2", "_index", "_index2", "_callSuper", "t", "o", "e", "default", "_isNativeReflectConstruct", "Reflect", "construct", "constructor", "apply", "Boolean", "prototype", "valueOf", "call", "_BaseAnimationBuilder", "_this", "_len", "arguments", "length", "args", "Array", "_key", "concat", "reversed", "build", "_this$durationV", "delayFunction", "getDelayFunction", "callback", "callbackV", "delay", "get<PERSON>elay", "halfDuration", "durationV", "config", "duration", "reverse", "values", "initialValues", "originX", "currentOriginX", "originY", "currentOriginY", "width", "currentWidth", "height", "currentHeight", "animations", "withSequence", "withTiming", "targetOriginX", "targetOriginY", "targetWidth", "targetHeight", "key", "createInstance", "instance", "BaseAnimationBuilder", "presetName"], "sources": ["../../../../src/layoutReanimation/defaultTransitions/SequencedTransition.ts"], "sourcesContent": [null], "mappings": "AAAA,YAAY;;AAAA,IAAAA,sBAAA,GAAAC,OAAA;AAAAC,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,KAAA;AAAA;AAAAD,OAAA,CAAAE,mBAAA;AAAA,IAAAC,gBAAA,GAAAP,sBAAA,CAAAC,OAAA;AAAA,IAAAO,aAAA,GAAAR,sBAAA,CAAAC,OAAA;AAAA,IAAAQ,2BAAA,GAAAT,sBAAA,CAAAC,OAAA;AAAA,IAAAS,gBAAA,GAAAV,sBAAA,CAAAC,OAAA;AAAA,IAAAU,UAAA,GAAAX,sBAAA,CAAAC,OAAA;AACZ,IAAAW,MAAA,GAAAX,OAAA;AAKA,IAAAY,OAAA,GAAAZ,OAAA;AAA0D,SAAAa,WAAAC,CAAA,EAAAC,CAAA,EAAAC,CAAA,WAAAD,CAAA,OAAAN,gBAAA,CAAAQ,OAAA,EAAAF,CAAA,OAAAP,2BAAA,CAAAS,OAAA,EAAAH,CAAA,EAAAI,yBAAA,KAAAC,OAAA,CAAAC,SAAA,CAAAL,CAAA,EAAAC,CAAA,YAAAP,gBAAA,CAAAQ,OAAA,EAAAH,CAAA,EAAAO,WAAA,IAAAN,CAAA,CAAAO,KAAA,CAAAR,CAAA,EAAAE,CAAA;AAAA,SAAAE,0BAAA,cAAAJ,CAAA,IAAAS,OAAA,CAAAC,SAAA,CAAAC,OAAA,CAAAC,IAAA,CAAAP,OAAA,CAAAC,SAAA,CAAAG,OAAA,iCAAAT,CAAA,aAAAI,yBAAA,YAAAA,0BAAA,aAAAJ,CAAA;AAAA,IAY7CT,mBAAmB,GAAAF,OAAA,CAAAE,mBAAA,aAAAsB,qBAAA;EAAA,SAAAtB,oBAAA;IAAA,IAAAuB,KAAA;IAAA,IAAAtB,gBAAA,CAAAW,OAAA,QAAAZ,mBAAA;IAAA,SAAAwB,IAAA,GAAAC,SAAA,CAAAC,MAAA,EAAAC,IAAA,OAAAC,KAAA,CAAAJ,IAAA,GAAAK,IAAA,MAAAA,IAAA,GAAAL,IAAA,EAAAK,IAAA;MAAAF,IAAA,CAAAE,IAAA,IAAAJ,SAAA,CAAAI,IAAA;IAAA;IAAAN,KAAA,GAAAf,UAAA,OAAAR,mBAAA,KAAA8B,MAAA,CAAAH,IAAA;IAAAJ,KAAA,CAM9BQ,QAAQ,GAAG,KAAK;IAAAR,KAAA,CAkBhBS,KAAK,GAAG,YAA+B;MAAA,IAAAC,eAAA;MACrC,IAAMC,aAAa,GAAGX,KAAA,CAAKY,gBAAgB,CAAC,CAAC;MAC7C,IAAMC,QAAQ,GAAGb,KAAA,CAAKc,SAAS;MAC/B,IAAMC,KAAK,GAAGf,KAAA,CAAKgB,QAAQ,CAAC,CAAC;MAC7B,IAAMC,YAAY,GAAG,EAAAP,eAAA,GAACV,KAAA,CAAKkB,SAAS,YAAAR,eAAA,GAAI,GAAG,IAAI,CAAC;MAChD,IAAMS,MAAM,GAAG;QAAEC,QAAQ,EAAEH;MAAa,CAAC;MACzC,IAAMI,OAAO,GAAGrB,KAAA,CAAKQ,QAAQ;MAE7B,OAAQ,UAAAc,MAAM,EAAK;QACjB,SAAS;;QACT,OAAO;UACLC,aAAa,EAAE;YACbC,OAAO,EAAEF,MAAM,CAACG,cAAc;YAC9BC,OAAO,EAAEJ,MAAM,CAACK,cAAc;YAC9BC,KAAK,EAAEN,MAAM,CAACO,YAAY;YAC1BC,MAAM,EAAER,MAAM,CAACS;UACjB,CAAC;UACDC,UAAU,EAAE;YACVR,OAAO,EAAEb,aAAa,CACpBI,KAAK,EACL,IAAAkB,mBAAY,EACV,IAAAC,iBAAU,EACRb,OAAO,GAAGC,MAAM,CAACG,cAAc,GAAGH,MAAM,CAACa,aAAa,EACtDhB,MACF,CAAC,EACD,IAAAe,iBAAU,EAACZ,MAAM,CAACa,aAAa,EAAEhB,MAAM,CACzC,CACF,CAAC;YACDO,OAAO,EAAEf,aAAa,CACpBI,KAAK,EACL,IAAAkB,mBAAY,EACV,IAAAC,iBAAU,EACRb,OAAO,GAAGC,MAAM,CAACc,aAAa,GAAGd,MAAM,CAACK,cAAc,EACtDR,MACF,CAAC,EACD,IAAAe,iBAAU,EAACZ,MAAM,CAACc,aAAa,EAAEjB,MAAM,CACzC,CACF,CAAC;YACDS,KAAK,EAAEjB,aAAa,CAClBI,KAAK,EACL,IAAAkB,mBAAY,EACV,IAAAC,iBAAU,EACRb,OAAO,GAAGC,MAAM,CAACO,YAAY,GAAGP,MAAM,CAACe,WAAW,EAClDlB,MACF,CAAC,EACD,IAAAe,iBAAU,EAACZ,MAAM,CAACe,WAAW,EAAElB,MAAM,CACvC,CACF,CAAC;YACDW,MAAM,EAAEnB,aAAa,CACnBI,KAAK,EACL,IAAAkB,mBAAY,EACV,IAAAC,iBAAU,EACRb,OAAO,GAAGC,MAAM,CAACgB,YAAY,GAAGhB,MAAM,CAACS,aAAa,EACpDZ,MACF,CAAC,EACD,IAAAe,iBAAU,EAACZ,MAAM,CAACgB,YAAY,EAAEnB,MAAM,CACxC,CACF;UACF,CAAC;UACDN,QAAA,EAAAA;QACF,CAAC;MACH,CAAC;IACH,CAAC;IAAA,OAAAb,KAAA;EAAA;EAAA,IAAAlB,UAAA,CAAAO,OAAA,EAAAZ,mBAAA,EAAAsB,qBAAA;EAAA,WAAApB,aAAA,CAAAU,OAAA,EAAAZ,mBAAA;IAAA8D,GAAA;IAAA/D,KAAA,EAnED,SAAA6C,OAAOA,CAAA,EAAwB;MAC7B,IAAI,CAACb,QAAQ,GAAG,CAAC,IAAI,CAACA,QAAQ;MAC9B,OAAO,IAAI;IACb;EAAA;IAAA+B,GAAA;IAAA/D,KAAA,EAdA,SAAOgE,cAAcA,CAAA,EAEF;MACjB,OAAO,IAAI/D,mBAAmB,CAAC,CAAC;IAClC;EAAA;IAAA8D,GAAA;IAAA/D,KAAA,EAEA,SAAO6C,OAAOA,CAAA,EAAwB;MACpC,IAAMoB,QAAQ,GAAGhE,mBAAmB,CAAC+D,cAAc,CAAC,CAAC;MACrD,OAAOC,QAAQ,CAACpB,OAAO,CAAC,CAAC;IAC3B;EAAA;AAAA,EAhBQqB,4BAAoB;AADjBjE,mBAAmB,CAIvBkE,UAAU,GAAG,qBAAqB", "ignoreList": []}