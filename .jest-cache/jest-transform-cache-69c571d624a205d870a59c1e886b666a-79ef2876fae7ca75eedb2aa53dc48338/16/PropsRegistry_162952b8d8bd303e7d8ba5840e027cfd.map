{"version": 3, "names": ["Object", "defineProperty", "exports", "value", "removeFromPropsRegistry", "_errors", "require", "_PlatformChecker", "_threads", "VIEW_TAGS", "viewTag", "push", "length", "queueMicrotask", "flush", "__DEV__", "isF<PERSON><PERSON>", "ReanimatedError", "runOnUI", "removeFromPropsRegistryOnUI", "viewTags", "global", "_removeFromPropsRegistry"], "sources": ["../../src/PropsRegistry.ts"], "sourcesContent": [null], "mappings": "AAAA,YAAY;;AAAAA,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,KAAA;AAAA;AAAAD,OAAA,CAAAE,uBAAA,GAAAA,uBAAA;AACZ,IAAAC,OAAA,GAAAC,OAAA;AACA,IAAAC,gBAAA,GAAAD,OAAA;AACA,IAAAE,QAAA,GAAAF,OAAA;AAEA,IAAIG,SAAmB,GAAG,EAAE;AAErB,SAASL,uBAAuBA,CAACM,OAAe,EAAE;EACvDD,SAAS,CAACE,IAAI,CAACD,OAAO,CAAC;EACvB,IAAID,SAAS,CAACG,MAAM,KAAK,CAAC,EAAE;IAC1BC,cAAc,CAACC,KAAK,CAAC;EACvB;AACF;AAEA,SAASA,KAAKA,CAAA,EAAG;EACf,IAAIC,OAAO,IAAI,CAAC,IAAAC,yBAAQ,EAAC,CAAC,EAAE;IAC1B,MAAM,IAAIC,uBAAe,CAAC,4CAA4C,CAAC;EACzE;EACA,IAAAC,gBAAO,EAACC,2BAA2B,CAAC,CAACV,SAAS,CAAC;EAC/CA,SAAS,GAAG,EAAE;AAChB;AAEA,SAASU,2BAA2BA,CAACC,QAAkB,EAAE;EACvD,SAAS;;EACTC,MAAM,CAACC,wBAAwB,CAACF,QAAQ,CAAC;AAC3C", "ignoreList": []}