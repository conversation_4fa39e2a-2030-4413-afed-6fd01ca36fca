{"version": 3, "names": ["_Platform", "_interopRequireDefault", "require", "processDecelerationRate", "decelerationRate", "Platform", "select", "ios", "android", "module", "exports"], "sources": ["processDecelerationRate.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @format\n * @flow strict-local\n */\n\nimport Platform from '../../Utilities/Platform';\n\nfunction processDecelerationRate(\n  decelerationRate: number | 'normal' | 'fast',\n): number {\n  if (decelerationRate === 'normal') {\n    return Platform.select({\n      ios: 0.998,\n      android: 0.985,\n    });\n  } else if (decelerationRate === 'fast') {\n    return Platform.select({\n      ios: 0.99,\n      android: 0.9,\n    });\n  }\n  return decelerationRate;\n}\n\nmodule.exports = processDecelerationRate;\n"], "mappings": ";AAUA,IAAAA,SAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA,SAASC,uBAAuBA,CAC9BC,gBAA4C,EACpC;EACR,IAAIA,gBAAgB,KAAK,QAAQ,EAAE;IACjC,OAAOC,iBAAQ,CAACC,MAAM,CAAC;MACrBC,GAAG,EAAE,KAAK;MACVC,OAAO,EAAE;IACX,CAAC,CAAC;EACJ,CAAC,MAAM,IAAIJ,gBAAgB,KAAK,MAAM,EAAE;IACtC,OAAOC,iBAAQ,CAACC,MAAM,CAAC;MACrBC,GAAG,EAAE,IAAI;MACTC,OAAO,EAAE;IACX,CAAC,CAAC;EACJ;EACA,OAAOJ,gBAAgB;AACzB;AAEAK,MAAM,CAACC,OAAO,GAAGP,uBAAuB", "ignoreList": []}