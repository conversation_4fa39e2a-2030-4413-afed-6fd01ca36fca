{"version": 3, "names": ["Object", "defineProperty", "exports", "value", "useHandler", "_react", "require", "_PlatformChecker", "_utils", "_shareables", "handlers", "dependencies", "initRef", "useRef", "current", "context", "makeShareable", "savedDependencies", "useEffect", "_initRef$current", "buildDependencies", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "areDependenciesEqual", "useWeb", "isWeb", "isJest"], "sources": ["../../../src/hook/useHandler.ts"], "sourcesContent": [null], "mappings": "AAAA,YAAY;;AAAAA,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,KAAA;AAAA;AAAAD,OAAA,CAAAE,UAAA,GAAAA,UAAA;AACZ,IAAAC,MAAA,GAAAC,OAAA;AAEA,IAAAC,gBAAA,GAAAD,OAAA;AAEA,IAAAE,MAAA,GAAAF,OAAA;AACA,IAAAG,WAAA,GAAAH,OAAA;AAsDO,SAASF,UAAUA,CAIxBM,QAAgD,EAChDC,YAA6B,EACD;EAC5B,IAAMC,OAAO,GAAG,IAAAC,aAAM,EAA0C,IAAI,CAAC;EACrE,IAAID,OAAO,CAACE,OAAO,KAAK,IAAI,EAAE;IAC5B,IAAMC,QAAO,GAAG,IAAAC,yBAAa,EAAC,CAAC,CAAY,CAAC;IAC5CJ,OAAO,CAACE,OAAO,GAAG;MAChBC,OAAO,EAAPA,QAAO;MACPE,iBAAiB,EAAE;IACrB,CAAC;EACH;EAEA,IAAAC,gBAAS,EAAC,YAAM;IACd,OAAO,YAAM;MACXN,OAAO,CAACE,OAAO,GAAG,IAAI;IACxB,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,IAAAK,gBAAA,GAAuCP,OAAO,CAACE,OAAO;IAA9CC,OAAO,GAAAI,gBAAA,CAAPJ,OAAO;IAAEE,iBAAA,GAAAE,gBAAA,CAAAF,iBAAA;EAEjBN,YAAY,GAAG,IAAAS,wBAAiB,EAC9BT,YAAY,EACZD,QACF,CAAC;EAED,IAAMW,oBAAoB,GAAG,CAAC,IAAAC,2BAAoB,EAChDX,YAAY,EACZM,iBACF,CAAC;EACDL,OAAO,CAACE,OAAO,CAACG,iBAAiB,GAAGN,YAAY;EAChD,IAAMY,MAAM,GAAG,IAAAC,sBAAK,EAAC,CAAC,IAAI,IAAAC,uBAAM,EAAC,CAAC;EAElC,OAAO;IAAEV,OAAO,EAAPA,OAAO;IAAEM,oBAAoB,EAApBA,oBAAoB;IAAEE,MAAA,EAAAA;EAAO,CAAC;AAClD", "ignoreList": []}