{"version": 3, "names": ["Object", "defineProperty", "exports", "value", "startWebLayoutAnimation", "tryActivateLayoutTransition", "_config", "require", "_commonTypes", "_createAnimation", "_componentUtils", "_domUtils", "_index", "_componentStyle", "_Easing", "_index2", "chooseConfig", "animationType", "props", "config", "LayoutAnimationType", "ENTERING", "entering", "EXITING", "exiting", "LAYOUT", "layout", "checkUndefinedAnimationFail", "initialAnimationName", "needsCustomization", "Animations", "logger", "warn", "maybeReportOverwrittenProperties", "keyframe", "styles", "propertyRegex", "animationProperties", "Set", "match", "matchAll", "add", "commonProperties", "Array", "from", "filter", "style", "has", "length", "join", "chooseAction", "animationConfig", "element", "transitionData", "setElementAnimation", "reversed", "handleLayoutTransition", "handleExitingAnimation", "tryGetAnimationConfig", "isLayoutTransition", "isCustomKeyframe", "Keyframe", "hasInitialValues", "initialValues", "undefined", "animationName", "createCustomKeyFrameAnimation", "definitions", "presetName", "constructor", "createAnimationWithInitialValues", "shouldFail", "keyframeTimestamps", "keys", "includes", "getProcessedConfig", "maybeModifyStyleForKeyframe", "makeElementVisible", "snapshot", "_props$layout$enterin", "_props$layout$exiting", "_props$layout$easingX", "_props$layout$easingX2", "_props$layout$easingY", "_props$layout$easingY2", "rect", "getBoundingClientRect", "areDOMRectsEqual", "enteringAnimation", "enteringV", "exitingAnimation", "exitingV", "translateX", "x", "translateY", "y", "scaleX", "width", "scaleY", "height", "easingX", "easingXV", "EasingNameSymbol", "easingY", "easingYV"], "sources": ["../../../../src/layoutReanimation/web/animationsManager.ts"], "sourcesContent": [null], "mappings": "AAAA,YAAY;;AAAAA,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,KAAA;AAAA;AAAAD,OAAA,CAAAE,uBAAA,GAAAA,uBAAA;AAAAF,OAAA,CAAAG,2BAAA,GAAAA,2BAAA;AASZ,IAAAC,OAAA,GAAAC,OAAA;AAKA,IAAAC,YAAA,GAAAD,OAAA;AACA,IAAAE,gBAAA,GAAAF,OAAA;AAIA,IAAAG,eAAA,GAAAH,OAAA;AAOA,IAAAI,SAAA,GAAAJ,OAAA;AAEA,IAAAK,MAAA,GAAAL,OAAA;AACA,IAAAM,eAAA,GAAAN,OAAA;AACA,IAAAO,OAAA,GAAAP,OAAA;AAEA,IAAAQ,OAAA,GAAAR,OAAA;AAEA,SAASS,YAAYA,CACnBC,aAAkC,EAClCC,KAAuD,EACvD;EACA,IAAMC,MAAM,GACVF,aAAa,KAAKG,gCAAmB,CAACC,QAAQ,GAC1CH,KAAK,CAACI,QAAQ,GACdL,aAAa,KAAKG,gCAAmB,CAACG,OAAO,GAC3CL,KAAK,CAACM,OAAO,GACbP,aAAa,KAAKG,gCAAmB,CAACK,MAAM,GAC1CP,KAAK,CAACQ,MAAM,GACZ,IAAI;EAEd,OAAOP,MAAM;AACf;AAEA,SAASQ,2BAA2BA,CAClCC,oBAA4B,EAC5BC,kBAA2B,EAC3B;EAGA,IAAID,oBAAoB,IAAIE,kBAAU,IAAID,kBAAkB,EAAE;IAC5D,OAAO,KAAK;EACd;EAEAE,cAAM,CAACC,IAAI,CACT,qLACF,CAAC;EAED,OAAO,IAAI;AACb;AAEA,SAASC,gCAAgCA,CACvCC,QAAgB,EAChBC,MAA2B,EAC3B;EACA,IAAMC,aAAa,GAAG,oBAAoB;EAC1C,IAAMC,mBAAmB,GAAG,IAAIC,GAAG,CAAC,CAAC;EAErC,KAAK,IAAMC,KAAK,IAAIL,QAAQ,CAACM,QAAQ,CAACJ,aAAa,CAAC,EAAE;IACpDC,mBAAmB,CAACI,GAAG,CAACF,KAAK,CAAC,CAAC,CAAC,CAAC;EACnC;EAEA,IAAMG,gBAAgB,GAAGC,KAAK,CAACC,IAAI,CAACT,MAAM,CAAC,CAACU,MAAM,CAAE,UAAAC,KAAK;IAAA,OACvDT,mBAAmB,CAACU,GAAG,CAACD,KAAK,CAC/B;EAAA,EAAC;EAED,IAAIJ,gBAAgB,CAACM,MAAM,KAAK,CAAC,EAAE;IACjC;EACF;EAEAjB,cAAM,CAACC,IAAI,CACT,GACEU,gBAAgB,CAACM,MAAM,KAAK,CAAC,GAAG,UAAU,GAAG,YAAY,KACtDN,gBAAgB,CAACO,IAAI,CACxB,IACF,CAAC,6IACH,CAAC;AACH;AAEA,SAASC,YAAYA,CACnBjC,aAAkC,EAClCkC,eAAgC,EAChCC,OAA8B,EAC9BC,cAA8B,EAC9B;EACA,QAAQpC,aAAa;IACnB,KAAKG,gCAAmB,CAACC,QAAQ;MAC/B,IAAAiC,mCAAmB,EAACF,OAAO,EAAED,eAAe,EAAE,IAAI,CAAC;MACnD;IACF,KAAK/B,gCAAmB,CAACK,MAAM;MAC7B4B,cAAc,CAACE,QAAQ,GAAGJ,eAAe,CAACI,QAAQ;MAClD,IAAAC,sCAAsB,EAACJ,OAAO,EAAED,eAAe,EAAEE,cAAc,CAAC;MAChE;IACF,KAAKjC,gCAAmB,CAACG,OAAO;MAC9B,IAAAkC,sCAAsB,EAACL,OAAO,EAAED,eAAe,CAAC;MAChD;EACJ;AACF;AAEA,SAASO,qBAAqBA,CAC5BxC,KAAuD,EACvDD,aAAkC,EAClC;EACA,IAAME,MAAM,GAAGH,YAAY,CAACC,aAAa,EAAEC,KAAK,CAAC;EACjD,IAAI,CAACC,MAAM,EAAE;IACX,OAAO,IAAI;EACb;EAKA,IAAMwC,kBAAkB,GAAG1C,aAAa,KAAKG,gCAAmB,CAACK,MAAM;EACvE,IAAMmC,gBAAgB,GAAGzC,MAAM,YAAY0C,eAAQ;EACnD,IAAMC,gBAAgB,GAAI3C,MAAM,CAAkB4C,aAAa,KAAKC,SAAS;EAE7E,IAAIC,aAAa;EAEjB,IAAIL,gBAAgB,EAAE;IACpBK,aAAa,GAAG,IAAAC,8CAA6B,EAC1C/C,MAAM,CAAkBgD,WAC3B,CAAC;EACH,CAAC,MAAM,IAAI,OAAOhD,MAAM,KAAK,UAAU,EAAE;IACvC8C,aAAa,GAAG9C,MAAM,CAACiD,UAAU;EACnC,CAAC,MAAM;IACLH,aAAa,GAAI9C,MAAM,CAACkD,WAAW,CAChCD,UAAU;EACf;EAEA,IAAIN,gBAAgB,EAAE;IACpBG,aAAa,GAAG,IAAAK,iDAAgC,EAC9CL,aAAa,EACZ9C,MAAM,CAAkB4C,aAC3B,CAAC;EACH;EAEA,IAAMQ,UAAU,GAAG5C,2BAA2B,CAC5CsC,aAAa,EACbN,kBAAkB,IAAIC,gBAAgB,IAAIE,gBAC5C,CAAC;EAED,IAAIS,UAAU,EAAE;IACd,OAAO,IAAI;EACb;EAEA,IAAIX,gBAAgB,EAAE;IACpB,IAAMY,kBAAkB,GAAGxE,MAAM,CAACyE,IAAI,CACnCtD,MAAM,CAAkBgD,WAC3B,CAAC;IAED,IACE,EAAEK,kBAAkB,CAACE,QAAQ,CAAC,KAAK,CAAC,IAAIF,kBAAkB,CAACE,QAAQ,CAAC,IAAI,CAAC,CAAC,EAC1E;MACA3C,cAAM,CAACC,IAAI,CACT,+MACF,CAAC;IACH;EACF;EAEA,IAAMmB,eAAe,GAAG,IAAAwB,kCAAkB,EACxCV,aAAa,EACbhD,aAAa,EACbE,MACF,CAAC;EAED,OAAOgC,eAAe;AACxB;AAEO,SAAS/C,uBAAuBA,CAGrCc,KAAuD,EACvDkC,OAA8B,EAC9BnC,aAAkC,EAClCoC,cAA+B,EAC/B;EACA,IAAMF,eAAe,GAAGO,qBAAqB,CAACxC,KAAK,EAAED,aAAa,CAAC;EAEnE,IAAA2D,2CAA2B,EAACxB,OAAO,EAAElC,KAAK,CAACI,QAAwB,CAAC;EAEpE,IAAK,CAAA6B,eAAe,oBAAfA,eAAe,CAAEc,aAAa,KAAuBnC,kBAAU,EAAE;IACpEG,gCAAgC,CAC9BH,kBAAU,CAACqB,eAAe,oBAAfA,eAAe,CAAEc,aAAa,CAAmB,CAACnB,KAAK,EAClEM,OAAO,CAACN,KACV,CAAC;EACH;EAEA,IAAIK,eAAe,EAAE;IACnBD,YAAY,CACVjC,aAAa,EACbkC,eAAe,EACfC,OAAO,EACPC,cACF,CAAC;EACH,CAAC,MAAM;IACL,IAAAwB,kCAAkB,EAACzB,OAAO,EAAE,CAAC,CAAC;EAChC;AACF;AAEO,SAAS/C,2BAA2BA,CAGzCa,KAAuD,EACvDkC,OAA8B,EAC9B0B,QAAiB,EACjB;EAAA,IAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,qBAAA,EAAAC,sBAAA;EACA,IAAI,CAAClE,KAAK,CAACQ,MAAM,EAAE;IACjB;EACF;EAEA,IAAM2D,IAAI,GAAGjC,OAAO,CAACkC,qBAAqB,CAAC,CAAC;EAE5C,IAAI,IAAAC,0BAAgB,EAACF,IAAI,EAAEP,QAAQ,CAAC,EAAE;IACpC;EACF;EAEA,IAAMU,iBAAiB,IAAAT,qBAAA,GAAI7D,KAAK,CAACQ,MAAM,CAAkB+D,SAAS,qBAAvCV,qBAAA,CACvBX,UAAU;EACd,IAAMsB,gBAAgB,IAAAV,qBAAA,GAAI9D,KAAK,CAACQ,MAAM,CAAkBiE,QAAQ,qBAAtCX,qBAAA,CAAwCZ,UAAU;EAE5E,IAAMf,cAA8B,GAAG;IACrCuC,UAAU,EAAEd,QAAQ,CAACe,CAAC,GAAGR,IAAI,CAACQ,CAAC;IAC/BC,UAAU,EAAEhB,QAAQ,CAACiB,CAAC,GAAGV,IAAI,CAACU,CAAC;IAC/BC,MAAM,EAAElB,QAAQ,CAACmB,KAAK,GAAGZ,IAAI,CAACY,KAAK;IACnCC,MAAM,EAAEpB,QAAQ,CAACqB,MAAM,GAAGd,IAAI,CAACc,MAAM;IACrC5C,QAAQ,EAAE,KAAK;IACf6C,OAAO,GAAAnB,qBAAA,IAAAC,sBAAA,GACJhE,KAAK,CAACQ,MAAM,CAAkB2E,QAAQ,qBAAtCnB,sBAAA,CAAyCoB,wBAAgB,CAAC,YAAArB,qBAAA,GAAI,MAAM;IACvEsB,OAAO,GAAApB,qBAAA,IAAAC,sBAAA,GACJlE,KAAK,CAACQ,MAAM,CAAkB8E,QAAQ,qBAAtCpB,sBAAA,CAAyCkB,wBAAgB,CAAC,YAAAnB,qBAAA,GAAI,MAAM;IACvE7D,QAAQ,EAAEkE,iBAAiB;IAC3BhE,OAAO,EAAEkE;EACX,CAAC;EAEDtF,uBAAuB,CACrBc,KAAK,EACLkC,OAAO,EACPhC,gCAAmB,CAACK,MAAM,EAC1B4B,cACF,CAAC;AACH", "ignoreList": []}