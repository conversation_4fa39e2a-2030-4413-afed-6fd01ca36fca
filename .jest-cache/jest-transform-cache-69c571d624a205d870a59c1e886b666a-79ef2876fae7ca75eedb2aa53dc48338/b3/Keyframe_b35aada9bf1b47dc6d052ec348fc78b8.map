{"version": 3, "names": ["_interopRequireDefault", "require", "Object", "defineProperty", "exports", "value", "Keyframe", "_defineProperty2", "_toConsumableArray2", "_classCallCheck2", "_createClass2", "_Easing", "_index", "_commonTypes", "_util", "_errors", "InnerKeyframe", "definitions", "_this", "default", "reduceMotionV", "ReduceMotion", "System", "build", "delay", "delayV", "delayFunction", "getDelayFunction", "_this$parseDefinition", "parseDefinitions", "keyframes", "initialValues", "callback", "callbackV", "animations", "addAnimation", "key", "keyframePoints", "length", "animation", "withTiming", "duration", "easing", "Easing", "linear", "withSequence", "apply", "map", "keyframePoint", "includes", "transform", "push", "split", "keys", "for<PERSON>ach", "transformProp", "index", "transformPropKey", "makeKey<PERSON>ey", "_this2", "parsedKeyframes", "from", "ReanimatedError", "to", "styleProp", "Array", "isArray", "transformStyle", "durationV", "animationKeyPoints", "Number", "getAnimationDuration", "currentKeyPoint", "maxDuration", "currentDuration", "reduce", "acc", "addKeyPoint", "_ref", "__DEV__", "assertEasingIsWorklet", "filter", "sort", "a", "b", "keyPoint", "keyframe", "addKeyPointWith", "durationMs", "delayMs", "<PERSON><PERSON><PERSON><PERSON>", "reduceMotion", "<PERSON><PERSON><PERSON><PERSON>", "_", "getReduceMotionFromConfig"], "sources": ["../../../../src/layoutReanimation/animationBuilder/Keyframe.ts"], "sourcesContent": [null], "mappings": "AAAA,YAAY;;AAAA,IAAAA,sBAAA,GAAAC,OAAA;AAAAC,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,KAAA;AAAA;AAAAD,OAAA,CAAAE,QAAA;AAAA,IAAAC,gBAAA,GAAAP,sBAAA,CAAAC,OAAA;AAAA,IAAAO,mBAAA,GAAAR,sBAAA,CAAAC,OAAA;AAAA,IAAAQ,gBAAA,GAAAT,sBAAA,CAAAC,OAAA;AAAA,IAAAS,aAAA,GAAAV,sBAAA,CAAAC,OAAA;AACZ,IAAAU,OAAA,GAAAV,OAAA;AACA,IAAAW,MAAA,GAAAX,OAAA;AAeA,IAAAY,YAAA,GAAAZ,OAAA;AACA,IAAAa,KAAA,GAAAb,OAAA;AAIA,IAAAc,OAAA,GAAAd,OAAA;AAA8C,IAYxCe,aAAa;EAWjB,SAAAA,cAAYC,WAA+B,EAAE;IAAA,IAAAC,KAAA;IAAA,IAAAT,gBAAA,CAAAU,OAAA,QAAAH,aAAA;IAAA,KAR7CI,aAAa,GAAiBC,yBAAY,CAACC,MAAM;IAAA,KAiMjDC,KAAK,GAAG,YAAkC;MACxC,IAAMC,KAAK,GAAGN,KAAI,CAACO,MAAM;MACzB,IAAMC,aAAa,GAAGR,KAAI,CAACS,gBAAgB,CAAC,CAAC;MAC7C,IAAAC,qBAAA,GAAqCV,KAAI,CAACW,gBAAgB,CAAC,CAAC;QAApDC,SAAS,GAAAF,qBAAA,CAATE,SAAS;QAAEC,aAAA,GAAAH,qBAAA,CAAAG,aAAA;MACnB,IAAMC,QAAQ,GAAGd,KAAI,CAACe,SAAS;MAE/B,OAAO,YAAM;QACX,SAAS;;QACT,IAAMC,UAAwC,GAAG,CAAC,CAAC;QAMnD,IAAMC,YAAY,GAAI,SAAhBA,YAAYA,CAAIC,GAAW,EAAK;UACpC,IAAMC,cAAc,GAAGP,SAAS,CAACM,GAAG,CAAC;UAErC,IAAIC,cAAc,CAACC,MAAM,KAAK,CAAC,EAAE;YAC/B;UACF;UACA,IAAMC,SAAS,GAAGb,aAAa,CAC7BF,KAAK,EACLa,cAAc,CAACC,MAAM,KAAK,CAAC,GACvB,IAAAE,iBAAU,EAACH,cAAc,CAAC,CAAC,CAAC,CAAChC,KAAK,EAAE;YAClCoC,QAAQ,EAAEJ,cAAc,CAAC,CAAC,CAAC,CAACI,QAAQ;YACpCC,MAAM,EAAEL,cAAc,CAAC,CAAC,CAAC,CAACK,MAAM,GAC5BL,cAAc,CAAC,CAAC,CAAC,CAACK,MAAM,GACxBC,cAAM,CAACC;UACb,CAAC,CAAC,GACFC,mBAAY,CAAAC,KAAA,aAAAtC,mBAAA,CAAAW,OAAA,EACPkB,cAAc,CAACU,GAAG,CAAE,UAAAC,aAA4B;YAAA,OACjD,IAAAR,iBAAU,EAACQ,aAAa,CAAC3C,KAAK,EAAE;cAC9BoC,QAAQ,EAAEO,aAAa,CAACP,QAAQ;cAChCC,MAAM,EAAEM,aAAa,CAACN,MAAM,GACxBM,aAAa,CAACN,MAAM,GACpBC,cAAM,CAACC;YACb,CAAC,CACH;UAAA,EACF,EACN,CAAC;UACD,IAAIR,GAAG,CAACa,QAAQ,CAAC,WAAW,CAAC,EAAE;YAC7B,IAAI,EAAE,WAAW,IAAIf,UAAU,CAAC,EAAE;cAChCA,UAAU,CAACgB,SAAS,GAAG,EAAE;YAC3B;YACAhB,UAAU,CAACgB,SAAS,CAAEC,IAAI,KAAA5C,gBAAA,CAAAY,OAAA,MACvBiB,GAAG,CAACgB,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAGb,SAAA,CACtB,CAAC;UACJ,CAAC,MAAM;YACLL,UAAU,CAACE,GAAG,CAAC,GAAGG,SAAS;UAC7B;QACF,CAAC;QACDrC,MAAM,CAACmD,IAAI,CAACtB,aAAa,CAAC,CAACuB,OAAO,CAAE,UAAAlB,GAAW,EAAK;UAClD,IAAIA,GAAG,CAACa,QAAQ,CAAC,WAAW,CAAC,EAAE;YAC7BlB,aAAa,CAACK,GAAG,CAAC,CAACkB,OAAO,CACxB,UAACC,aAA8C,EAAEC,KAAa,EAAK;cACjEtD,MAAM,CAACmD,IAAI,CAACE,aAAa,CAAC,CAACD,OAAO,CAAE,UAAAG,gBAAwB,EAAK;gBAC/DtB,YAAY,CAACuB,eAAe,CAACF,KAAK,EAAEC,gBAAgB,CAAC,CAAC;cACxD,CAAC,CAAC;YACJ,CACF,CAAC;UACH,CAAC,MAAM;YACLtB,YAAY,CAACC,GAAG,CAAC;UACnB;QACF,CAAC,CAAC;QACF,OAAO;UACLF,UAAU,EAAVA,UAAU;UACVH,aAAa,EAAbA,aAAa;UACbC,QAAA,EAAAA;QACF,CAAC;MACH,CAAC;IACH,CAAC;IA9PC,IAAI,CAACf,WAAW,GAAGA,WAAwC;EAC7D;EAAA,WAAAP,aAAA,CAAAS,OAAA,EAAAH,aAAA;IAAAoB,GAAA;IAAA/B,KAAA,EAEQ,SAAAwB,gBAAgBA,CAAA,EAA8B;MAAA,IAAA8B,MAAA;MAKpD,IAAMC,eAAgD,GAAG,CAAC,CAAC;MAI3D,IAAI,IAAI,CAAC3C,WAAW,CAAC4C,IAAI,EAAE;QACzB,IAAI,IAAI,CAAC5C,WAAW,CAAC,GAAG,CAAC,EAAE;UACzB,MAAM,IAAI6C,uBAAe,CACvB,sFACF,CAAC;QACH;QACA,IAAI,CAAC7C,WAAW,CAAC,GAAG,CAAC,GAAG,IAAI,CAACA,WAAW,CAAC4C,IAAI;QAC7C,OAAO,IAAI,CAAC5C,WAAW,CAAC4C,IAAI;MAC9B;MACA,IAAI,IAAI,CAAC5C,WAAW,CAAC8C,EAAE,EAAE;QACvB,IAAI,IAAI,CAAC9C,WAAW,CAAC,KAAK,CAAC,EAAE;UAC3B,MAAM,IAAI6C,uBAAe,CACvB,0GACF,CAAC;QACH;QACA,IAAI,CAAC7C,WAAW,CAAC,KAAK,CAAC,GAAG,IAAI,CAACA,WAAW,CAAC8C,EAAE;QAC7C,OAAO,IAAI,CAAC9C,WAAW,CAAC8C,EAAE;MAC5B;MAKA,IAAI,CAAC,IAAI,CAAC9C,WAAW,CAAC,GAAG,CAAC,EAAE;QAC1B,MAAM,IAAI6C,uBAAe,CACvB,wEACF,CAAC;MACH;MACA,IAAM/B,aAAyB,GAAG,IAAI,CAACd,WAAW,CAAC,GAAG,CAAe;MAIrEf,MAAM,CAACmD,IAAI,CAACtB,aAAa,CAAC,CAACuB,OAAO,CAAE,UAAAU,SAAiB,EAAK;QACxD,IAAIA,SAAS,KAAK,WAAW,EAAE;UAC7B,IAAI,CAACC,KAAK,CAACC,OAAO,CAACnC,aAAa,CAACmB,SAAS,CAAC,EAAE;YAC3C;UACF;UACAnB,aAAa,CAACmB,SAAS,CAACI,OAAO,CAAC,UAACa,cAAc,EAAEX,KAAK,EAAK;YACzDtD,MAAM,CAACmD,IAAI,CAACc,cAAc,CAAC,CAACb,OAAO,CAAE,UAAAC,aAAqB,EAAK;cAC7DK,eAAe,CAACF,eAAe,CAACF,KAAK,EAAED,aAAa,CAAC,CAAC,GAAG,EAAE;YAC7D,CAAC,CAAC;UACJ,CAAC,CAAC;QACJ,CAAC,MAAM;UACLK,eAAe,CAACI,SAAS,CAAC,GAAG,EAAE;QACjC;MACF,CAAC,CAAC;MAEF,IAAMvB,QAAgB,GAAG,IAAI,CAAC2B,SAAS,GAAG,IAAI,CAACA,SAAS,GAAG,GAAG;MAC9D,IAAMC,kBAAiC,GAAGJ,KAAK,CAACJ,IAAI,CAClD3D,MAAM,CAACmD,IAAI,CAAC,IAAI,CAACpC,WAAW,CAC9B,CAAC,CAAC8B,GAAG,CAACuB,MAAM,CAAC;MAEb,IAAMC,oBAAoB,GAAG,SAAvBA,oBAAoBA,CACxBnC,GAAW,EACXoC,eAAuB,EACZ;QACX,IAAMC,WAAW,GAAID,eAAe,GAAG,GAAG,GAAI/B,QAAQ;QACtD,IAAMiC,eAAe,GAAGd,eAAe,CAACxB,GAAG,CAAC,CAACuC,MAAM,CACjD,UAACC,GAAW,EAAEvE,KAAoB;UAAA,OAAKuE,GAAG,GAAGvE,KAAK,CAACoC,QAAQ;QAAA,GAC3D,CACF,CAAC;QACD,OAAOgC,WAAW,GAAGC,eAAe;MACtC,CAAC;MAKD,IAAMG,WAAW,GAAG,SAAdA,WAAWA,CAAAC,IAAA,EAUL;QAAA,IATV1C,GAAG,GAAA0C,IAAA,CAAH1C,GAAG;UACH/B,KAAK,GAAAyE,IAAA,CAALzE,KAAK;UACLmE,eAAe,GAAAM,IAAA,CAAfN,eAAe;UACf9B,MAAA,GAAAoC,IAAA,CAAApC,MAAA;QAOA,IAAI,EAAEN,GAAG,IAAIwB,eAAe,CAAC,EAAE;UAC7B,MAAM,IAAIE,uBAAe,CACvB,+GACF,CAAC;QACH;QAEA,IAAIiB,OAAO,IAAIrC,MAAM,EAAE;UACrB,IAAAsC,2BAAqB,EAACtC,MAAM,CAAC;QAC/B;QAEAkB,eAAe,CAACxB,GAAG,CAAC,CAACe,IAAI,CAAC;UACxBV,QAAQ,EAAE8B,oBAAoB,CAACnC,GAAG,EAAEoC,eAAe,CAAC;UACpDnE,KAAK,EAALA,KAAK;UACLqC,MAAA,EAAAA;QACF,CAAC,CAAC;MACJ,CAAC;MACD2B,kBAAkB,CACfY,MAAM,CAAE,UAAA5E,KAAa;QAAA,OAAKA,KAAK,KAAK,CAAC;MAAA,EAAC,CACtC6E,IAAI,CAAC,UAACC,CAAS,EAAEC,CAAS;QAAA,OAAKD,CAAC,GAAGC,CAAC;MAAA,EAAC,CACrC9B,OAAO,CAAE,UAAA+B,QAAgB,EAAK;QAC7B,IAAIA,QAAQ,GAAG,CAAC,IAAIA,QAAQ,GAAG,GAAG,EAAE;UAClC,MAAM,IAAIvB,uBAAe,CACvB,8CACF,CAAC;QACH;QACA,IAAMwB,QAAuB,GAAG3B,MAAI,CAAC1C,WAAW,CAACoE,QAAQ,CAAC;QAC1D,IAAM3C,MAAM,GAAG4C,QAAQ,CAAC5C,MAAM;QAC9B,OAAO4C,QAAQ,CAAC5C,MAAM;QACtB,IAAM6C,eAAe,GAAG,SAAlBA,eAAeA,CAAInD,GAAW,EAAE/B,KAAsB;UAAA,OAC1DwE,WAAW,CAAC;YACVzC,GAAG,EAAHA,GAAG;YACH/B,KAAK,EAALA,KAAK;YACLmE,eAAe,EAAEa,QAAQ;YACzB3C,MAAA,EAAAA;UACF,CAAC,CAAC;QAAA;QACJxC,MAAM,CAACmD,IAAI,CAACiC,QAAQ,CAAC,CAAChC,OAAO,CAAE,UAAAlB,GAAW,EAAK;UAC7C,IAAIA,GAAG,KAAK,WAAW,EAAE;YACvB,IAAI,CAAC6B,KAAK,CAACC,OAAO,CAACoB,QAAQ,CAACpC,SAAS,CAAC,EAAE;cACtC;YACF;YACAoC,QAAQ,CAACpC,SAAS,CAACI,OAAO,CAAC,UAACa,cAAc,EAAEX,KAAK,EAAK;cACpDtD,MAAM,CAACmD,IAAI,CAACc,cAAc,CAAC,CAACb,OAAO,CAAE,UAAAC,aAAqB,EAAK;gBAC7DgC,eAAe,CACb7B,eAAe,CAACF,KAAK,EAAED,aAAa,CAAC,EACrCY,cAAc,CACZZ,aAAa,CAGjB,CAAC;cACH,CAAC,CAAC;YACJ,CAAC,CAAC;UACJ,CAAC,MAAM;YACLgC,eAAe,CAACnD,GAAG,EAAEkD,QAAQ,CAAClD,GAAG,CAAC,CAAC;UACrC;QACF,CAAC,CAAC;MACJ,CAAC,CAAC;MACJ,OAAO;QAAEL,aAAa,EAAbA,aAAa;QAAED,SAAS,EAAE8B;MAAgB,CAAC;IACtD;EAAA;IAAAxB,GAAA;IAAA/B,KAAA,EAEA,SAAAoC,QAAQA,CAAC+C,UAAkB,EAAiB;MAC1C,IAAI,CAACpB,SAAS,GAAGoB,UAAU;MAC3B,OAAO,IAAI;IACb;EAAA;IAAApD,GAAA;IAAA/B,KAAA,EAEA,SAAAmB,KAAKA,CAACiE,OAAe,EAAiB;MACpC,IAAI,CAAChE,MAAM,GAAGgE,OAAO;MACrB,OAAO,IAAI;IACb;EAAA;IAAArD,GAAA;IAAA/B,KAAA,EAEA,SAAAqF,YAAYA,CAAC1D,QAAqC,EAAiB;MACjE,IAAI,CAACC,SAAS,GAAGD,QAAQ;MACzB,OAAO,IAAI;IACb;EAAA;IAAAI,GAAA;IAAA/B,KAAA,EAEA,SAAAsF,YAAYA,CAACvE,aAA2B,EAAQ;MAC9C,IAAI,CAACA,aAAa,GAAGA,aAAa;MAClC,OAAO,IAAI;IACb;EAAA;IAAAgB,GAAA;IAAA/B,KAAA,EAEQ,SAAAsB,gBAAgBA,CAAA,EAAsB;MAC5C,IAAMH,KAAK,GAAG,IAAI,CAACC,MAAM;MACzB,IAAMkE,YAAY,GAAG,IAAI,CAACvE,aAAa;MACvC,OAAOI,KAAK,GAER,UAACA,KAAK,EAAEe,SAAS,EAAK;QACpB,SAAS;;QACT,OAAO,IAAAqD,gBAAS,EAACpE,KAAK,EAAEe,SAAS,EAAEoD,YAAY,CAAC;MAClD,CAAC,GACD,UAACE,CAAC,EAAEtD,SAAS,EAAK;QAChB,SAAS;;QACTA,SAAS,CAACoD,YAAY,GAAG,IAAAG,+BAAyB,EAACH,YAAY,CAAC;QAChE,OAAOpD,SAAS;MAClB,CAAC;IACP;EAAA;AAAA;AA2EF,SAASmB,eAAeA,CAACF,KAAa,EAAED,aAAqB,EAAE;EAC7D,SAAS;;EACT,OAAO,GAAGC,KAAK,cAAcD,aAAa,EAAE;AAC9C;AAUO,IAAMjD,QAAQ,GAAAF,OAAA,CAAAE,QAAA,GAAGU,aAA0C", "ignoreList": []}