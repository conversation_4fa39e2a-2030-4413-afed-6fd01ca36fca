2888487d105fee75c10a37c629283946
'use strict';

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.Keyframe = void 0;
var _defineProperty2 = _interopRequireDefault(require("@babel/runtime/helpers/defineProperty"));
var _toConsumableArray2 = _interopRequireDefault(require("@babel/runtime/helpers/toConsumableArray"));
var _classCallCheck2 = _interopRequireDefault(require("@babel/runtime/helpers/classCallCheck"));
var _createClass2 = _interopRequireDefault(require("@babel/runtime/helpers/createClass"));
var _Easing = require("../../Easing.js");
var _index = require("../../animation/index.js");
var _commonTypes = require("../../commonTypes.js");
var _util = require("../../animation/util.js");
var _errors = require("../../errors.js");
var InnerKeyframe = function () {
  function InnerKeyframe(definitions) {
    var _this = this;
    (0, _classCallCheck2.default)(this, InnerKeyframe);
    this.reduceMotionV = _commonTypes.ReduceMotion.System;
    this.build = function () {
      var delay = _this.delayV;
      var delayFunction = _this.getDelayFunction();
      var _this$parseDefinition = _this.parseDefinitions(),
        keyframes = _this$parseDefinition.keyframes,
        initialValues = _this$parseDefinition.initialValues;
      var callback = _this.callbackV;
      return function () {
        'worklet';

        var animations = {};
        var addAnimation = function addAnimation(key) {
          var keyframePoints = keyframes[key];
          if (keyframePoints.length === 0) {
            return;
          }
          var animation = delayFunction(delay, keyframePoints.length === 1 ? (0, _index.withTiming)(keyframePoints[0].value, {
            duration: keyframePoints[0].duration,
            easing: keyframePoints[0].easing ? keyframePoints[0].easing : _Easing.Easing.linear
          }) : _index.withSequence.apply(void 0, (0, _toConsumableArray2.default)(keyframePoints.map(function (keyframePoint) {
            return (0, _index.withTiming)(keyframePoint.value, {
              duration: keyframePoint.duration,
              easing: keyframePoint.easing ? keyframePoint.easing : _Easing.Easing.linear
            });
          }))));
          if (key.includes('transform')) {
            if (!('transform' in animations)) {
              animations.transform = [];
            }
            animations.transform.push((0, _defineProperty2.default)({}, key.split(':')[1], animation));
          } else {
            animations[key] = animation;
          }
        };
        Object.keys(initialValues).forEach(function (key) {
          if (key.includes('transform')) {
            initialValues[key].forEach(function (transformProp, index) {
              Object.keys(transformProp).forEach(function (transformPropKey) {
                addAnimation(makeKeyframeKey(index, transformPropKey));
              });
            });
          } else {
            addAnimation(key);
          }
        });
        return {
          animations: animations,
          initialValues: initialValues,
          callback: callback
        };
      };
    };
    this.definitions = definitions;
  }
  return (0, _createClass2.default)(InnerKeyframe, [{
    key: "parseDefinitions",
    value: function parseDefinitions() {
      var _this2 = this;
      var parsedKeyframes = {};
      if (this.definitions.from) {
        if (this.definitions['0']) {
          throw new _errors.ReanimatedError("You cannot provide both keyframe 0 and 'from' as they both specified initial values.");
        }
        this.definitions['0'] = this.definitions.from;
        delete this.definitions.from;
      }
      if (this.definitions.to) {
        if (this.definitions['100']) {
          throw new _errors.ReanimatedError("You cannot provide both keyframe 100 and 'to' as they both specified values at the end of the animation.");
        }
        this.definitions['100'] = this.definitions.to;
        delete this.definitions.to;
      }
      if (!this.definitions['0']) {
        throw new _errors.ReanimatedError("Please provide 0 or 'from' keyframe with initial state of your object.");
      }
      var initialValues = this.definitions['0'];
      Object.keys(initialValues).forEach(function (styleProp) {
        if (styleProp === 'transform') {
          if (!Array.isArray(initialValues.transform)) {
            return;
          }
          initialValues.transform.forEach(function (transformStyle, index) {
            Object.keys(transformStyle).forEach(function (transformProp) {
              parsedKeyframes[makeKeyframeKey(index, transformProp)] = [];
            });
          });
        } else {
          parsedKeyframes[styleProp] = [];
        }
      });
      var duration = this.durationV ? this.durationV : 500;
      var animationKeyPoints = Array.from(Object.keys(this.definitions)).map(Number);
      var getAnimationDuration = function getAnimationDuration(key, currentKeyPoint) {
        var maxDuration = currentKeyPoint / 100 * duration;
        var currentDuration = parsedKeyframes[key].reduce(function (acc, value) {
          return acc + value.duration;
        }, 0);
        return maxDuration - currentDuration;
      };
      var addKeyPoint = function addKeyPoint(_ref) {
        var key = _ref.key,
          value = _ref.value,
          currentKeyPoint = _ref.currentKeyPoint,
          easing = _ref.easing;
        if (!(key in parsedKeyframes)) {
          throw new _errors.ReanimatedError("Keyframe can contain only that set of properties that were provide with initial values (keyframe 0 or 'from')");
        }
        if (__DEV__ && easing) {
          (0, _util.assertEasingIsWorklet)(easing);
        }
        parsedKeyframes[key].push({
          duration: getAnimationDuration(key, currentKeyPoint),
          value: value,
          easing: easing
        });
      };
      animationKeyPoints.filter(function (value) {
        return value !== 0;
      }).sort(function (a, b) {
        return a - b;
      }).forEach(function (keyPoint) {
        if (keyPoint < 0 || keyPoint > 100) {
          throw new _errors.ReanimatedError('Keyframe should be in between range 0 - 100.');
        }
        var keyframe = _this2.definitions[keyPoint];
        var easing = keyframe.easing;
        delete keyframe.easing;
        var addKeyPointWith = function addKeyPointWith(key, value) {
          return addKeyPoint({
            key: key,
            value: value,
            currentKeyPoint: keyPoint,
            easing: easing
          });
        };
        Object.keys(keyframe).forEach(function (key) {
          if (key === 'transform') {
            if (!Array.isArray(keyframe.transform)) {
              return;
            }
            keyframe.transform.forEach(function (transformStyle, index) {
              Object.keys(transformStyle).forEach(function (transformProp) {
                addKeyPointWith(makeKeyframeKey(index, transformProp), transformStyle[transformProp]);
              });
            });
          } else {
            addKeyPointWith(key, keyframe[key]);
          }
        });
      });
      return {
        initialValues: initialValues,
        keyframes: parsedKeyframes
      };
    }
  }, {
    key: "duration",
    value: function duration(durationMs) {
      this.durationV = durationMs;
      return this;
    }
  }, {
    key: "delay",
    value: function delay(delayMs) {
      this.delayV = delayMs;
      return this;
    }
  }, {
    key: "withCallback",
    value: function withCallback(callback) {
      this.callbackV = callback;
      return this;
    }
  }, {
    key: "reduceMotion",
    value: function reduceMotion(reduceMotionV) {
      this.reduceMotionV = reduceMotionV;
      return this;
    }
  }, {
    key: "getDelayFunction",
    value: function getDelayFunction() {
      var delay = this.delayV;
      var reduceMotion = this.reduceMotionV;
      return delay ? function (delay, animation) {
        'worklet';

        return (0, _index.withDelay)(delay, animation, reduceMotion);
      } : function (_, animation) {
        'worklet';

        animation.reduceMotion = (0, _util.getReduceMotionFromConfig)(reduceMotion);
        return animation;
      };
    }
  }]);
}();
function makeKeyframeKey(index, transformProp) {
  'worklet';

  return `${index}_transform:${transformProp}`;
}
var Keyframe = exports.Keyframe = InnerKeyframe;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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