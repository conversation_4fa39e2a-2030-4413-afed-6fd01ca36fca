{"version": 3, "names": ["Object", "defineProperty", "exports", "value", "withClamp", "_util", "require", "_index", "config", "_animationToClamp", "defineAnimation", "animationToClamp", "strippedMin", "min", "undefined", "recognizePrefixSuffix", "strippedValue", "strippedMax", "max", "clampOnFrame", "animation", "now", "finished", "onFrame", "current", "logger", "warn", "_recognizePrefixSuffi", "prefix", "suffix", "newValue", "onStart", "previousAnimation", "animationBeforeClamped", "callback", "isHigherOrder", "reduceMotion", "getReduceMotionForAnimation"], "sources": ["../../../src/animation/clamp.ts"], "sourcesContent": [null], "mappings": "AAAA,YAAY;;AAAAA,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,KAAA;AAAA;AAAAD,OAAA,CAAAE,SAAA;AACZ,IAAAC,KAAA,GAAAC,OAAA;AAaA,IAAAC,MAAA,GAAAD,OAAA;AAUO,IAAMF,SAAS,GAAAF,OAAA,CAAAE,SAAA,GAAG,SAAZA,SAASA,CACpBI,MAAyD,EACzDC,iBAAkE,EACvC;EAC3B,SAAS;;EACT,OAAO,IAAAC,qBAAe,EACpBD,iBAAiB,EACjB,YAAsB;IACpB,SAAS;;IACT,IAAME,gBAAgB,GACpB,OAAOF,iBAAiB,KAAK,UAAU,GACnCA,iBAAiB,CAAC,CAAC,GACnBA,iBAAiB;IAEvB,IAAMG,WAAW,GACfJ,MAAM,CAACK,GAAG,KAAKC,SAAS,GACpBA,SAAS,GACT,IAAAC,2BAAqB,EAACP,MAAM,CAACK,GAAG,CAAC,CAACG,aAAa;IAErD,IAAMC,WAAW,GACfT,MAAM,CAACU,GAAG,KAAKJ,SAAS,GACpBA,SAAS,GACT,IAAAC,2BAAqB,EAACP,MAAM,CAACU,GAAG,CAAC,CAACF,aAAa;IAErD,SAASG,YAAYA,CACnBC,SAAyB,EACzBC,GAAc,EACL;MACT,IAAMC,QAAQ,GAAGX,gBAAgB,CAACY,OAAO,CAACZ,gBAAgB,EAAEU,GAAG,CAAC;MAEhE,IAAIV,gBAAgB,CAACa,OAAO,KAAKV,SAAS,EAAE;QAC1CW,aAAM,CAACC,IAAI,CACT,mFACF,CAAC;QACD,OAAO,IAAI;MACb,CAAC,MAAM;QACL,IAAAC,qBAAA,GAA0C,IAAAZ,2BAAqB,EAC7DJ,gBAAgB,CAACa,OACnB,CAAC;UAFOI,MAAM,GAAAD,qBAAA,CAANC,MAAM;UAAEZ,aAAa,GAAAW,qBAAA,CAAbX,aAAa;UAAEa,MAAA,GAAAF,qBAAA,CAAAE,MAAA;QAI/B,IAAIC,QAAQ;QAEZ,IAAIb,WAAW,KAAKH,SAAS,IAAIG,WAAW,GAAGD,aAAa,EAAE;UAC5Dc,QAAQ,GAAGb,WAAW;QACxB,CAAC,MAAM,IAAIL,WAAW,KAAKE,SAAS,IAAIF,WAAW,GAAGI,aAAa,EAAE;UACnEc,QAAQ,GAAGlB,WAAW;QACxB,CAAC,MAAM;UACLkB,QAAQ,GAAGd,aAAa;QAC1B;QAEAI,SAAS,CAACI,OAAO,GACf,OAAOb,gBAAgB,CAACa,OAAO,KAAK,QAAQ,GACxCM,QAAQ,GACR,GAAGF,MAAM,KAAKd,SAAS,GAAG,EAAE,GAAGc,MAAM,GAAGE,QAAQ,GAC9CD,MAAM,KAAKf,SAAS,GAAG,EAAE,GAAGe,MAAM,EAClC;MACV;MAEA,OAAOP,QAAQ;IACjB;IAEA,SAASS,OAAOA,CACdX,SAAyB,EACzBjB,KAAsB,EACtBkB,GAAc,EACdW,iBAAwC,EAClC;MACNZ,SAAS,CAACI,OAAO,GAAGrB,KAAK;MACzBiB,SAAS,CAACY,iBAAiB,GAAGrB,gBAAgB;MAC9C,IAAMsB,sBAAsB,GAAGD,iBAAiB,oBAAjBA,iBAAiB,CAAEA,iBAAiB;MACnE,IACExB,MAAM,CAACU,GAAG,KAAKJ,SAAS,IACxBN,MAAM,CAACK,GAAG,KAAKC,SAAS,IACxBN,MAAM,CAACU,GAAG,GAAGV,MAAM,CAACK,GAAG,EACvB;QACAY,aAAM,CAACC,IAAI,CACT,sEACF,CAAC;MACH;MAEAf,gBAAgB,CAACoB,OAAO,CACtBpB,gBAAgB,EAKhB,CAAAsB,sBAAsB,oBAAtBA,sBAAsB,CAAET,OAAO,KAAIrB,KAAK,EACxCkB,GAAG,EACHY,sBACF,CAAC;IACH;IAEA,IAAMC,QAAQ,GAAI,SAAZA,QAAQA,CAAIZ,QAAkB,EAAW;MAC7C,IAAIX,gBAAgB,CAACuB,QAAQ,EAAE;QAC7BvB,gBAAgB,CAACuB,QAAQ,CAACZ,QAAQ,CAAC;MACrC;IACF,CAAC;IAED,OAAO;MACLa,aAAa,EAAE,IAAI;MACnBZ,OAAO,EAAEJ,YAAY;MACrBY,OAAO,EAAPA,OAAO;MACPP,OAAO,EAAEb,gBAAgB,CAACa,OAAQ;MAClCU,QAAQ,EAARA,QAAQ;MACRF,iBAAiB,EAAE,IAAI;MACvBI,YAAY,EAAE,IAAAC,iCAA2B,EAAC7B,MAAM,CAAC4B,YAAY;IAC/D,CAAC;EACH,CACF,CAAC;AACH,CAAkB", "ignoreList": []}