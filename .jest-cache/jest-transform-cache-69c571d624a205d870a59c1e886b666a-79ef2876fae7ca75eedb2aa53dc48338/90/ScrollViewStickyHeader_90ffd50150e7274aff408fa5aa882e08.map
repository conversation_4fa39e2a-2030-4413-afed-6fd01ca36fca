{"version": 3, "names": ["_Animated", "_interopRequireDefault", "require", "_ReactFabricPublicInstanceUtils", "_StyleSheet", "_Platform", "_useMergeRefs", "_react", "_interopRequireWildcard", "React", "_jsxRuntime", "_getRequireWildcardCache", "e", "WeakMap", "r", "t", "__esModule", "default", "has", "get", "n", "__proto__", "a", "Object", "defineProperty", "getOwnPropertyDescriptor", "u", "hasOwnProperty", "call", "i", "set", "ScrollViewStickyHeaderWithForwardedRef", "forwardRef", "ScrollViewStickyHeader", "props", "forwardedRef", "inverted", "scrollViewHeight", "hiddenOnScroll", "scrollAnimatedValue", "_nextHeaderLayoutY", "nextHeaderLayoutY", "_useState", "useState", "_useState2", "_slicedToArray2", "measured", "setMeasured", "_useState3", "_useState4", "layoutY", "setLayoutY", "_useState5", "_useState6", "layoutHeight", "setLayoutHeight", "_useState7", "_useState8", "translateY", "setTranslateY", "_useState9", "_useState10", "setNextHeaderLayoutY", "_useState11", "_useState12", "isF<PERSON><PERSON>", "setIsFabric", "callback<PERSON><PERSON>", "useCallback", "ref", "setNextHeaderY", "isFabricPublicInstance", "useMergeRefs", "offset", "useMemo", "Animated", "diffClamp", "interpolate", "extrapolateLeft", "inputRange", "outputRange", "_useState13", "initialTranslateY", "add", "_useState14", "animatedTranslateY", "setAnimatedTranslateY", "haveReceivedInitialZeroTranslateY", "useRef", "translateYDebounceTimer", "useEffect", "current", "animatedValueListener", "_ref", "value", "debounceTimeout", "Platform", "OS", "clearTimeout", "setTimeout", "stickStartPoint", "push", "collisionPoint", "newAnimatedTranslateY", "animatedListenerId", "addListener", "removeListener", "_onLayout", "event", "nativeEvent", "layout", "y", "height", "onLayout", "child", "Children", "only", "children", "passthroughAnimatedPropExplicitValues", "style", "transform", "jsx", "View", "collapsable", "nativeID", "styles", "header", "cloneElement", "fill", "undefined", "StyleSheet", "create", "zIndex", "flex", "_default", "exports"], "sources": ["ScrollViewStickyHeader.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @flow strict-local\n * @format\n */\n\nimport type {LayoutEvent} from '../../Types/CoreEventTypes';\n\nimport Animated from '../../Animated/Animated';\nimport {isPublicInstance as isFabricPublicInstance} from '../../ReactNative/ReactFabricPublicInstance/ReactFabricPublicInstanceUtils';\nimport StyleSheet from '../../StyleSheet/StyleSheet';\nimport Platform from '../../Utilities/Platform';\nimport useMergeRefs from '../../Utilities/useMergeRefs';\nimport * as React from 'react';\nimport {useCallback, useEffect, useMemo, useRef, useState} from 'react';\n\nexport type Props = $ReadOnly<{\n  children?: ExactReactElement_DEPRECATED<$FlowFixMe>,\n  nextHeaderLayoutY: ?number,\n  onLayout: (event: LayoutEvent) => void,\n  scrollAnimatedValue: Animated.Value,\n  // Will cause sticky headers to stick at the bottom of the ScrollView instead\n  // of the top.\n  inverted: ?boolean,\n  // The height of the parent ScrollView. Currently only set when inverted.\n  scrollViewHeight: ?number,\n  nativeID?: ?string,\n  hiddenOnScroll?: ?boolean,\n}>;\n\ntype Instance = {\n  ...React.ElementRef<typeof Animated.View>,\n  setNextHeaderY: number => void,\n  ...\n};\n\nconst ScrollViewStickyHeaderWithForwardedRef: component(\n  ref: React.RefSetter<Instance>,\n  ...props: Props\n) = React.forwardRef(function ScrollViewStickyHeader(props, forwardedRef) {\n  const {\n    inverted,\n    scrollViewHeight,\n    hiddenOnScroll,\n    scrollAnimatedValue,\n    nextHeaderLayoutY: _nextHeaderLayoutY,\n  } = props;\n\n  const [measured, setMeasured] = useState<boolean>(false);\n  const [layoutY, setLayoutY] = useState<number>(0);\n  const [layoutHeight, setLayoutHeight] = useState<number>(0);\n  const [translateY, setTranslateY] = useState<?number>(null);\n  const [nextHeaderLayoutY, setNextHeaderLayoutY] =\n    useState<?number>(_nextHeaderLayoutY);\n  const [isFabric, setIsFabric] = useState<boolean>(false);\n\n  const callbackRef = useCallback((ref: Instance | null): void => {\n    if (ref == null) {\n      return;\n    }\n    ref.setNextHeaderY = setNextHeaderLayoutY;\n    setIsFabric(isFabricPublicInstance(ref));\n  }, []);\n  const ref: React.RefSetter<React.ElementRef<typeof Animated.View>> =\n    // $FlowFixMe[prop-missing] - Instance is mutated to have `setNextHeaderY`.\n    useMergeRefs<Instance>(callbackRef, forwardedRef);\n\n  const offset = useMemo(\n    () =>\n      hiddenOnScroll === true\n        ? Animated.diffClamp(\n            scrollAnimatedValue\n              .interpolate({\n                extrapolateLeft: 'clamp',\n                inputRange: [layoutY, layoutY + 1],\n                outputRange: ([0, 1]: Array<number>),\n              })\n              .interpolate({\n                inputRange: [0, 1],\n                outputRange: ([0, -1]: Array<number>),\n              }),\n            -layoutHeight,\n            0,\n          )\n        : null,\n    [scrollAnimatedValue, layoutHeight, layoutY, hiddenOnScroll],\n  );\n\n  const [animatedTranslateY, setAnimatedTranslateY] = useState<Animated.Node>(\n    () => {\n      const inputRange: Array<number> = [-1, 0];\n      const outputRange: Array<number> = [0, 0];\n      const initialTranslateY = scrollAnimatedValue.interpolate({\n        inputRange,\n        outputRange,\n      });\n\n      if (offset != null) {\n        return Animated.add(initialTranslateY, offset);\n      }\n      return initialTranslateY;\n    },\n  );\n\n  const haveReceivedInitialZeroTranslateY = useRef<boolean>(true);\n  const translateYDebounceTimer = useRef<?TimeoutID>(null);\n\n  useEffect(() => {\n    if (translateY !== 0 && translateY != null) {\n      haveReceivedInitialZeroTranslateY.current = false;\n    }\n  }, [translateY]);\n\n  // This is called whenever the (Interpolated) Animated Value\n  // updates, which is several times per frame during scrolling.\n  // To ensure that the Fabric ShadowTree has the most recent\n  // translate style of this node, we debounce the value and then\n  // pass it through to the underlying node during render.\n  // This is:\n  // 1. Only an issue in Fabric.\n  // 2. Worse in Android than iOS. In Android, but not iOS, you\n  //    can touch and move your finger slightly and still trigger\n  //    a \"tap\" event. In iOS, moving will cancel the tap in\n  //    both Fabric and non-Fabric. On Android when you move\n  //    your finger, the hit-detection moves from the Android\n  //    platform to JS, so we need the ShadowTree to have knowledge\n  //    of the current position.\n  const animatedValueListener = useCallback(({value}: $FlowFixMe) => {\n    const debounceTimeout: number = Platform.OS === 'android' ? 15 : 64;\n    // When the AnimatedInterpolation is recreated, it always initializes\n    // to a value of zero and emits a value change of 0 to its listeners.\n    if (value === 0 && !haveReceivedInitialZeroTranslateY.current) {\n      haveReceivedInitialZeroTranslateY.current = true;\n      return;\n    }\n    if (translateYDebounceTimer.current != null) {\n      clearTimeout(translateYDebounceTimer.current);\n    }\n    translateYDebounceTimer.current = setTimeout(\n      () => setTranslateY(value),\n      debounceTimeout,\n    );\n  }, []);\n\n  useEffect(() => {\n    const inputRange: Array<number> = [-1, 0];\n    const outputRange: Array<number> = [0, 0];\n\n    if (measured) {\n      if (inverted === true) {\n        // The interpolation looks like:\n        // - Negative scroll: no translation\n        // - `stickStartPoint` is the point at which the header will start sticking.\n        //   It is calculated using the ScrollView viewport height so it is a the bottom.\n        // - Headers that are in the initial viewport will never stick, `stickStartPoint`\n        //   will be negative.\n        // - From 0 to `stickStartPoint` no translation. This will cause the header\n        //   to scroll normally until it reaches the top of the scroll view.\n        // - From `stickStartPoint` to when the next header y hits the bottom edge of the header: translate\n        //   equally to scroll. This will cause the header to stay at the top of the scroll view.\n        // - Past the collision with the next header y: no more translation. This will cause the\n        //   header to continue scrolling up and make room for the next sticky header.\n        //   In the case that there is no next header just translate equally to\n        //   scroll indefinitely.\n        if (scrollViewHeight != null) {\n          const stickStartPoint = layoutY + layoutHeight - scrollViewHeight;\n          if (stickStartPoint > 0) {\n            inputRange.push(stickStartPoint);\n            outputRange.push(0);\n            inputRange.push(stickStartPoint + 1);\n            outputRange.push(1);\n            // If the next sticky header has not loaded yet (probably windowing) or is the last\n            // we can just keep it sticked forever.\n            const collisionPoint =\n              (nextHeaderLayoutY || 0) - layoutHeight - scrollViewHeight;\n            if (collisionPoint > stickStartPoint) {\n              inputRange.push(collisionPoint, collisionPoint + 1);\n              outputRange.push(\n                collisionPoint - stickStartPoint,\n                collisionPoint - stickStartPoint,\n              );\n            }\n          }\n        }\n      } else {\n        // The interpolation looks like:\n        // - Negative scroll: no translation\n        // - From 0 to the y of the header: no translation. This will cause the header\n        //   to scroll normally until it reaches the top of the scroll view.\n        // - From header y to when the next header y hits the bottom edge of the header: translate\n        //   equally to scroll. This will cause the header to stay at the top of the scroll view.\n        // - Past the collision with the next header y: no more translation. This will cause the\n        //   header to continue scrolling up and make room for the next sticky header.\n        //   In the case that there is no next header just translate equally to\n        //   scroll indefinitely.\n        inputRange.push(layoutY);\n        outputRange.push(0);\n        // If the next sticky header has not loaded yet (probably windowing) or is the last\n        // we can just keep it sticked forever.\n        const collisionPoint = (nextHeaderLayoutY || 0) - layoutHeight;\n        if (collisionPoint >= layoutY) {\n          inputRange.push(collisionPoint, collisionPoint + 1);\n          outputRange.push(collisionPoint - layoutY, collisionPoint - layoutY);\n        } else {\n          inputRange.push(layoutY + 1);\n          outputRange.push(1);\n        }\n      }\n    }\n\n    let newAnimatedTranslateY: Animated.Node = scrollAnimatedValue.interpolate({\n      inputRange,\n      outputRange,\n    });\n\n    if (offset != null) {\n      newAnimatedTranslateY = Animated.add(newAnimatedTranslateY, offset);\n    }\n\n    // add the event listener\n    let animatedListenerId;\n    if (isFabric) {\n      animatedListenerId = newAnimatedTranslateY.addListener(\n        animatedValueListener,\n      );\n    }\n\n    setAnimatedTranslateY(newAnimatedTranslateY);\n\n    // clean up the event listener and timer\n    return () => {\n      if (animatedListenerId) {\n        newAnimatedTranslateY.removeListener(animatedListenerId);\n      }\n      if (translateYDebounceTimer.current != null) {\n        clearTimeout(translateYDebounceTimer.current);\n      }\n    };\n  }, [\n    nextHeaderLayoutY,\n    measured,\n    layoutHeight,\n    layoutY,\n    scrollViewHeight,\n    scrollAnimatedValue,\n    inverted,\n    offset,\n    animatedValueListener,\n    isFabric,\n  ]);\n\n  const _onLayout = (event: LayoutEvent) => {\n    setLayoutY(event.nativeEvent.layout.y);\n    setLayoutHeight(event.nativeEvent.layout.height);\n    setMeasured(true);\n\n    props.onLayout(event);\n    const child = React.Children.only<$FlowFixMe>(props.children);\n    if (child.props.onLayout) {\n      child.props.onLayout(event);\n    }\n  };\n\n  const child = React.Children.only<$FlowFixMe>(props.children);\n\n  const passthroughAnimatedPropExplicitValues =\n    isFabric && translateY != null\n      ? {\n          style: {transform: [{translateY: translateY}]},\n        }\n      : null;\n\n  return (\n    <Animated.View\n      collapsable={false}\n      nativeID={props.nativeID}\n      onLayout={_onLayout}\n      /* $FlowFixMe[prop-missing] passthroughAnimatedPropExplicitValues isn't properly\n         included in the Animated.View flow type. */\n      ref={ref}\n      style={[\n        child.props.style,\n        styles.header,\n        {transform: [{translateY: animatedTranslateY}]},\n      ]}\n      passthroughAnimatedPropExplicitValues={\n        passthroughAnimatedPropExplicitValues\n      }>\n      {React.cloneElement(child, {\n        style: styles.fill, // We transfer the child style to the wrapper.\n        onLayout: undefined, // we call this manually through our this._onLayout\n      })}\n    </Animated.View>\n  );\n});\n\nconst styles = StyleSheet.create({\n  header: {\n    zIndex: 10,\n  },\n  fill: {\n    flex: 1,\n  },\n});\n\nexport default ScrollViewStickyHeaderWithForwardedRef;\n"], "mappings": ";;;;;;AAYA,IAAAA,SAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,+BAAA,GAAAD,OAAA;AACA,IAAAE,WAAA,GAAAH,sBAAA,CAAAC,OAAA;AACA,IAAAG,SAAA,GAAAJ,sBAAA,CAAAC,OAAA;AACA,IAAAI,aAAA,GAAAL,sBAAA,CAAAC,OAAA;AACA,IAAAK,MAAA,GAAAC,uBAAA,CAAAN,OAAA;AAA+B,IAAAO,KAAA,GAAAF,MAAA;AAAA,IAAAG,WAAA,GAAAR,OAAA;AAAA,SAAAS,yBAAAC,CAAA,6BAAAC,OAAA,mBAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,yBAAAC,CAAA,WAAAA,CAAA,GAAAG,CAAA,GAAAD,CAAA,KAAAF,CAAA;AAAA,SAAAJ,wBAAAI,CAAA,EAAAE,CAAA,SAAAA,CAAA,IAAAF,CAAA,IAAAA,CAAA,CAAAI,UAAA,SAAAJ,CAAA,eAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,WAAAK,OAAA,EAAAL,CAAA,QAAAG,CAAA,GAAAJ,wBAAA,CAAAG,CAAA,OAAAC,CAAA,IAAAA,CAAA,CAAAG,GAAA,CAAAN,CAAA,UAAAG,CAAA,CAAAI,GAAA,CAAAP,CAAA,OAAAQ,CAAA,KAAAC,SAAA,UAAAC,CAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,CAAA,IAAAd,CAAA,oBAAAc,CAAA,OAAAC,cAAA,CAAAC,IAAA,CAAAhB,CAAA,EAAAc,CAAA,SAAAG,CAAA,GAAAP,CAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAb,CAAA,EAAAc,CAAA,UAAAG,CAAA,KAAAA,CAAA,CAAAV,GAAA,IAAAU,CAAA,CAAAC,GAAA,IAAAP,MAAA,CAAAC,cAAA,CAAAJ,CAAA,EAAAM,CAAA,EAAAG,CAAA,IAAAT,CAAA,CAAAM,CAAA,IAAAd,CAAA,CAAAc,CAAA,YAAAN,CAAA,CAAAH,OAAA,GAAAL,CAAA,EAAAG,CAAA,IAAAA,CAAA,CAAAe,GAAA,CAAAlB,CAAA,EAAAQ,CAAA,GAAAA,CAAA;AAuB/B,IAAMW,sCAGL,GAAGtB,KAAK,CAACuB,UAAU,CAAC,SAASC,sBAAsBA,CAACC,KAAK,EAAEC,YAAY,EAAE;EACxE,IACEC,QAAQ,GAKNF,KAAK,CALPE,QAAQ;IACRC,gBAAgB,GAIdH,KAAK,CAJPG,gBAAgB;IAChBC,cAAc,GAGZJ,KAAK,CAHPI,cAAc;IACdC,mBAAmB,GAEjBL,KAAK,CAFPK,mBAAmB;IACAC,kBAAkB,GACnCN,KAAK,CADPO,iBAAiB;EAGnB,IAAAC,SAAA,GAAgC,IAAAC,eAAQ,EAAU,KAAK,CAAC;IAAAC,UAAA,OAAAC,eAAA,CAAA5B,OAAA,EAAAyB,SAAA;IAAjDI,QAAQ,GAAAF,UAAA;IAAEG,WAAW,GAAAH,UAAA;EAC5B,IAAAI,UAAA,GAA8B,IAAAL,eAAQ,EAAS,CAAC,CAAC;IAAAM,UAAA,OAAAJ,eAAA,CAAA5B,OAAA,EAAA+B,UAAA;IAA1CE,OAAO,GAAAD,UAAA;IAAEE,UAAU,GAAAF,UAAA;EAC1B,IAAAG,UAAA,GAAwC,IAAAT,eAAQ,EAAS,CAAC,CAAC;IAAAU,UAAA,OAAAR,eAAA,CAAA5B,OAAA,EAAAmC,UAAA;IAApDE,YAAY,GAAAD,UAAA;IAAEE,eAAe,GAAAF,UAAA;EACpC,IAAAG,UAAA,GAAoC,IAAAb,eAAQ,EAAU,IAAI,CAAC;IAAAc,UAAA,OAAAZ,eAAA,CAAA5B,OAAA,EAAAuC,UAAA;IAApDE,UAAU,GAAAD,UAAA;IAAEE,aAAa,GAAAF,UAAA;EAChC,IAAAG,UAAA,GACE,IAAAjB,eAAQ,EAAUH,kBAAkB,CAAC;IAAAqB,WAAA,OAAAhB,eAAA,CAAA5B,OAAA,EAAA2C,UAAA;IADhCnB,iBAAiB,GAAAoB,WAAA;IAAEC,oBAAoB,GAAAD,WAAA;EAE9C,IAAAE,WAAA,GAAgC,IAAApB,eAAQ,EAAU,KAAK,CAAC;IAAAqB,WAAA,OAAAnB,eAAA,CAAA5B,OAAA,EAAA8C,WAAA;IAAjDE,QAAQ,GAAAD,WAAA;IAAEE,WAAW,GAAAF,WAAA;EAE5B,IAAMG,WAAW,GAAG,IAAAC,kBAAW,EAAC,UAACC,GAAoB,EAAW;IAC9D,IAAIA,GAAG,IAAI,IAAI,EAAE;MACf;IACF;IACAA,GAAG,CAACC,cAAc,GAAGR,oBAAoB;IACzCI,WAAW,CAAC,IAAAK,gDAAsB,EAACF,GAAG,CAAC,CAAC;EAC1C,CAAC,EAAE,EAAE,CAAC;EACN,IAAMA,GAA4D,GAEhE,IAAAG,qBAAY,EAAWL,WAAW,EAAEhC,YAAY,CAAC;EAEnD,IAAMsC,MAAM,GAAG,IAAAC,cAAO,EACpB;IAAA,OACEpC,cAAc,KAAK,IAAI,GACnBqC,iBAAQ,CAACC,SAAS,CAChBrC,mBAAmB,CAChBsC,WAAW,CAAC;MACXC,eAAe,EAAE,OAAO;MACxBC,UAAU,EAAE,CAAC7B,OAAO,EAAEA,OAAO,GAAG,CAAC,CAAC;MAClC8B,WAAW,EAAG,CAAC,CAAC,EAAE,CAAC;IACrB,CAAC,CAAC,CACDH,WAAW,CAAC;MACXE,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;MAClBC,WAAW,EAAG,CAAC,CAAC,EAAE,CAAC,CAAC;IACtB,CAAC,CAAC,EACJ,CAAC1B,YAAY,EACb,CACF,CAAC,GACD,IAAI;EAAA,GACV,CAACf,mBAAmB,EAAEe,YAAY,EAAEJ,OAAO,EAAEZ,cAAc,CAC7D,CAAC;EAED,IAAA2C,WAAA,GAAoD,IAAAtC,eAAQ,EAC1D,YAAM;MACJ,IAAMoC,UAAyB,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACzC,IAAMC,WAA0B,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;MACzC,IAAME,iBAAiB,GAAG3C,mBAAmB,CAACsC,WAAW,CAAC;QACxDE,UAAU,EAAVA,UAAU;QACVC,WAAW,EAAXA;MACF,CAAC,CAAC;MAEF,IAAIP,MAAM,IAAI,IAAI,EAAE;QAClB,OAAOE,iBAAQ,CAACQ,GAAG,CAACD,iBAAiB,EAAET,MAAM,CAAC;MAChD;MACA,OAAOS,iBAAiB;IAC1B,CACF,CAAC;IAAAE,WAAA,OAAAvC,eAAA,CAAA5B,OAAA,EAAAgE,WAAA;IAdMI,kBAAkB,GAAAD,WAAA;IAAEE,qBAAqB,GAAAF,WAAA;EAgBhD,IAAMG,iCAAiC,GAAG,IAAAC,aAAM,EAAU,IAAI,CAAC;EAC/D,IAAMC,uBAAuB,GAAG,IAAAD,aAAM,EAAa,IAAI,CAAC;EAExD,IAAAE,gBAAS,EAAC,YAAM;IACd,IAAIhC,UAAU,KAAK,CAAC,IAAIA,UAAU,IAAI,IAAI,EAAE;MAC1C6B,iCAAiC,CAACI,OAAO,GAAG,KAAK;IACnD;EACF,CAAC,EAAE,CAACjC,UAAU,CAAC,CAAC;EAgBhB,IAAMkC,qBAAqB,GAAG,IAAAxB,kBAAW,EAAC,UAAAyB,IAAA,EAAyB;IAAA,IAAvBC,KAAK,GAAAD,IAAA,CAALC,KAAK;IAC/C,IAAMC,eAAuB,GAAGC,iBAAQ,CAACC,EAAE,KAAK,SAAS,GAAG,EAAE,GAAG,EAAE;IAGnE,IAAIH,KAAK,KAAK,CAAC,IAAI,CAACP,iCAAiC,CAACI,OAAO,EAAE;MAC7DJ,iCAAiC,CAACI,OAAO,GAAG,IAAI;MAChD;IACF;IACA,IAAIF,uBAAuB,CAACE,OAAO,IAAI,IAAI,EAAE;MAC3CO,YAAY,CAACT,uBAAuB,CAACE,OAAO,CAAC;IAC/C;IACAF,uBAAuB,CAACE,OAAO,GAAGQ,UAAU,CAC1C;MAAA,OAAMxC,aAAa,CAACmC,KAAK,CAAC;IAAA,GAC1BC,eACF,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,IAAAL,gBAAS,EAAC,YAAM;IACd,IAAMX,UAAyB,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IACzC,IAAMC,WAA0B,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;IAEzC,IAAIlC,QAAQ,EAAE;MACZ,IAAIV,QAAQ,KAAK,IAAI,EAAE;QAerB,IAAIC,gBAAgB,IAAI,IAAI,EAAE;UAC5B,IAAM+D,eAAe,GAAGlD,OAAO,GAAGI,YAAY,GAAGjB,gBAAgB;UACjE,IAAI+D,eAAe,GAAG,CAAC,EAAE;YACvBrB,UAAU,CAACsB,IAAI,CAACD,eAAe,CAAC;YAChCpB,WAAW,CAACqB,IAAI,CAAC,CAAC,CAAC;YACnBtB,UAAU,CAACsB,IAAI,CAACD,eAAe,GAAG,CAAC,CAAC;YACpCpB,WAAW,CAACqB,IAAI,CAAC,CAAC,CAAC;YAGnB,IAAMC,cAAc,GAClB,CAAC7D,iBAAiB,IAAI,CAAC,IAAIa,YAAY,GAAGjB,gBAAgB;YAC5D,IAAIiE,cAAc,GAAGF,eAAe,EAAE;cACpCrB,UAAU,CAACsB,IAAI,CAACC,cAAc,EAAEA,cAAc,GAAG,CAAC,CAAC;cACnDtB,WAAW,CAACqB,IAAI,CACdC,cAAc,GAAGF,eAAe,EAChCE,cAAc,GAAGF,eACnB,CAAC;YACH;UACF;QACF;MACF,CAAC,MAAM;QAWLrB,UAAU,CAACsB,IAAI,CAACnD,OAAO,CAAC;QACxB8B,WAAW,CAACqB,IAAI,CAAC,CAAC,CAAC;QAGnB,IAAMC,eAAc,GAAG,CAAC7D,iBAAiB,IAAI,CAAC,IAAIa,YAAY;QAC9D,IAAIgD,eAAc,IAAIpD,OAAO,EAAE;UAC7B6B,UAAU,CAACsB,IAAI,CAACC,eAAc,EAAEA,eAAc,GAAG,CAAC,CAAC;UACnDtB,WAAW,CAACqB,IAAI,CAACC,eAAc,GAAGpD,OAAO,EAAEoD,eAAc,GAAGpD,OAAO,CAAC;QACtE,CAAC,MAAM;UACL6B,UAAU,CAACsB,IAAI,CAACnD,OAAO,GAAG,CAAC,CAAC;UAC5B8B,WAAW,CAACqB,IAAI,CAAC,CAAC,CAAC;QACrB;MACF;IACF;IAEA,IAAIE,qBAAoC,GAAGhE,mBAAmB,CAACsC,WAAW,CAAC;MACzEE,UAAU,EAAVA,UAAU;MACVC,WAAW,EAAXA;IACF,CAAC,CAAC;IAEF,IAAIP,MAAM,IAAI,IAAI,EAAE;MAClB8B,qBAAqB,GAAG5B,iBAAQ,CAACQ,GAAG,CAACoB,qBAAqB,EAAE9B,MAAM,CAAC;IACrE;IAGA,IAAI+B,kBAAkB;IACtB,IAAIvC,QAAQ,EAAE;MACZuC,kBAAkB,GAAGD,qBAAqB,CAACE,WAAW,CACpDb,qBACF,CAAC;IACH;IAEAN,qBAAqB,CAACiB,qBAAqB,CAAC;IAG5C,OAAO,YAAM;MACX,IAAIC,kBAAkB,EAAE;QACtBD,qBAAqB,CAACG,cAAc,CAACF,kBAAkB,CAAC;MAC1D;MACA,IAAIf,uBAAuB,CAACE,OAAO,IAAI,IAAI,EAAE;QAC3CO,YAAY,CAACT,uBAAuB,CAACE,OAAO,CAAC;MAC/C;IACF,CAAC;EACH,CAAC,EAAE,CACDlD,iBAAiB,EACjBK,QAAQ,EACRQ,YAAY,EACZJ,OAAO,EACPb,gBAAgB,EAChBE,mBAAmB,EACnBH,QAAQ,EACRqC,MAAM,EACNmB,qBAAqB,EACrB3B,QAAQ,CACT,CAAC;EAEF,IAAM0C,SAAS,GAAG,SAAZA,SAASA,CAAIC,KAAkB,EAAK;IACxCzD,UAAU,CAACyD,KAAK,CAACC,WAAW,CAACC,MAAM,CAACC,CAAC,CAAC;IACtCxD,eAAe,CAACqD,KAAK,CAACC,WAAW,CAACC,MAAM,CAACE,MAAM,CAAC;IAChDjE,WAAW,CAAC,IAAI,CAAC;IAEjBb,KAAK,CAAC+E,QAAQ,CAACL,KAAK,CAAC;IACrB,IAAMM,KAAK,GAAGzG,KAAK,CAAC0G,QAAQ,CAACC,IAAI,CAAalF,KAAK,CAACmF,QAAQ,CAAC;IAC7D,IAAIH,KAAK,CAAChF,KAAK,CAAC+E,QAAQ,EAAE;MACxBC,KAAK,CAAChF,KAAK,CAAC+E,QAAQ,CAACL,KAAK,CAAC;IAC7B;EACF,CAAC;EAED,IAAMM,KAAK,GAAGzG,KAAK,CAAC0G,QAAQ,CAACC,IAAI,CAAalF,KAAK,CAACmF,QAAQ,CAAC;EAE7D,IAAMC,qCAAqC,GACzCrD,QAAQ,IAAIP,UAAU,IAAI,IAAI,GAC1B;IACE6D,KAAK,EAAE;MAACC,SAAS,EAAE,CAAC;QAAC9D,UAAU,EAAEA;MAAU,CAAC;IAAC;EAC/C,CAAC,GACD,IAAI;EAEV,OACE,IAAAhD,WAAA,CAAA+G,GAAA,EAACzH,SAAA,CAAAiB,OAAQ,CAACyG,IAAI;IACZC,WAAW,EAAE,KAAM;IACnBC,QAAQ,EAAE1F,KAAK,CAAC0F,QAAS;IACzBX,QAAQ,EAAEN,SAAU;IAGpBtC,GAAG,EAAEA,GAAI;IACTkD,KAAK,EAAE,CACLL,KAAK,CAAChF,KAAK,CAACqF,KAAK,EACjBM,MAAM,CAACC,MAAM,EACb;MAACN,SAAS,EAAE,CAAC;QAAC9D,UAAU,EAAE2B;MAAkB,CAAC;IAAC,CAAC,CAC/C;IACFiC,qCAAqC,EACnCA,qCACD;IAAAD,QAAA,EACA5G,KAAK,CAACsH,YAAY,CAACb,KAAK,EAAE;MACzBK,KAAK,EAAEM,MAAM,CAACG,IAAI;MAClBf,QAAQ,EAAEgB;IACZ,CAAC;EAAC,CACW,CAAC;AAEpB,CAAC,CAAC;AAEF,IAAMJ,MAAM,GAAGK,mBAAU,CAACC,MAAM,CAAC;EAC/BL,MAAM,EAAE;IACNM,MAAM,EAAE;EACV,CAAC;EACDJ,IAAI,EAAE;IACJK,IAAI,EAAE;EACR;AACF,CAAC,CAAC;AAAC,IAAAC,QAAA,GAAAC,OAAA,CAAAtH,OAAA,GAEYc,sCAAsC", "ignoreList": []}