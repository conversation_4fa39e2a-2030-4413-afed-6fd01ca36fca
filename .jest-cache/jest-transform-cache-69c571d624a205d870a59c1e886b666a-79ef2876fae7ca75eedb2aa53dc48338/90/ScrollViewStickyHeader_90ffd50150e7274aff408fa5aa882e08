083c727948c3871711153b9b74489b89
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _slicedToArray2 = _interopRequireDefault(require("@babel/runtime/helpers/slicedToArray"));
var _Animated = _interopRequireDefault(require("../../Animated/Animated"));
var _ReactFabricPublicInstanceUtils = require("../../ReactNative/ReactFabricPublicInstance/ReactFabricPublicInstanceUtils");
var _StyleSheet = _interopRequireDefault(require("../../StyleSheet/StyleSheet"));
var _Platform = _interopRequireDefault(require("../../Utilities/Platform"));
var _useMergeRefs = _interopRequireDefault(require("../../Utilities/useMergeRefs"));
var _react = _interopRequireWildcard(require("react"));
var React = _react;
var _jsxRuntime = require("react/jsx-runtime");
function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
var ScrollViewStickyHeaderWithForwardedRef = React.forwardRef(function ScrollViewStickyHeader(props, forwardedRef) {
  var inverted = props.inverted,
    scrollViewHeight = props.scrollViewHeight,
    hiddenOnScroll = props.hiddenOnScroll,
    scrollAnimatedValue = props.scrollAnimatedValue,
    _nextHeaderLayoutY = props.nextHeaderLayoutY;
  var _useState = (0, _react.useState)(false),
    _useState2 = (0, _slicedToArray2.default)(_useState, 2),
    measured = _useState2[0],
    setMeasured = _useState2[1];
  var _useState3 = (0, _react.useState)(0),
    _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
    layoutY = _useState4[0],
    setLayoutY = _useState4[1];
  var _useState5 = (0, _react.useState)(0),
    _useState6 = (0, _slicedToArray2.default)(_useState5, 2),
    layoutHeight = _useState6[0],
    setLayoutHeight = _useState6[1];
  var _useState7 = (0, _react.useState)(null),
    _useState8 = (0, _slicedToArray2.default)(_useState7, 2),
    translateY = _useState8[0],
    setTranslateY = _useState8[1];
  var _useState9 = (0, _react.useState)(_nextHeaderLayoutY),
    _useState10 = (0, _slicedToArray2.default)(_useState9, 2),
    nextHeaderLayoutY = _useState10[0],
    setNextHeaderLayoutY = _useState10[1];
  var _useState11 = (0, _react.useState)(false),
    _useState12 = (0, _slicedToArray2.default)(_useState11, 2),
    isFabric = _useState12[0],
    setIsFabric = _useState12[1];
  var callbackRef = (0, _react.useCallback)(function (ref) {
    if (ref == null) {
      return;
    }
    ref.setNextHeaderY = setNextHeaderLayoutY;
    setIsFabric((0, _ReactFabricPublicInstanceUtils.isPublicInstance)(ref));
  }, []);
  var ref = (0, _useMergeRefs.default)(callbackRef, forwardedRef);
  var offset = (0, _react.useMemo)(function () {
    return hiddenOnScroll === true ? _Animated.default.diffClamp(scrollAnimatedValue.interpolate({
      extrapolateLeft: 'clamp',
      inputRange: [layoutY, layoutY + 1],
      outputRange: [0, 1]
    }).interpolate({
      inputRange: [0, 1],
      outputRange: [0, -1]
    }), -layoutHeight, 0) : null;
  }, [scrollAnimatedValue, layoutHeight, layoutY, hiddenOnScroll]);
  var _useState13 = (0, _react.useState)(function () {
      var inputRange = [-1, 0];
      var outputRange = [0, 0];
      var initialTranslateY = scrollAnimatedValue.interpolate({
        inputRange: inputRange,
        outputRange: outputRange
      });
      if (offset != null) {
        return _Animated.default.add(initialTranslateY, offset);
      }
      return initialTranslateY;
    }),
    _useState14 = (0, _slicedToArray2.default)(_useState13, 2),
    animatedTranslateY = _useState14[0],
    setAnimatedTranslateY = _useState14[1];
  var haveReceivedInitialZeroTranslateY = (0, _react.useRef)(true);
  var translateYDebounceTimer = (0, _react.useRef)(null);
  (0, _react.useEffect)(function () {
    if (translateY !== 0 && translateY != null) {
      haveReceivedInitialZeroTranslateY.current = false;
    }
  }, [translateY]);
  var animatedValueListener = (0, _react.useCallback)(function (_ref) {
    var value = _ref.value;
    var debounceTimeout = _Platform.default.OS === 'android' ? 15 : 64;
    if (value === 0 && !haveReceivedInitialZeroTranslateY.current) {
      haveReceivedInitialZeroTranslateY.current = true;
      return;
    }
    if (translateYDebounceTimer.current != null) {
      clearTimeout(translateYDebounceTimer.current);
    }
    translateYDebounceTimer.current = setTimeout(function () {
      return setTranslateY(value);
    }, debounceTimeout);
  }, []);
  (0, _react.useEffect)(function () {
    var inputRange = [-1, 0];
    var outputRange = [0, 0];
    if (measured) {
      if (inverted === true) {
        if (scrollViewHeight != null) {
          var stickStartPoint = layoutY + layoutHeight - scrollViewHeight;
          if (stickStartPoint > 0) {
            inputRange.push(stickStartPoint);
            outputRange.push(0);
            inputRange.push(stickStartPoint + 1);
            outputRange.push(1);
            var collisionPoint = (nextHeaderLayoutY || 0) - layoutHeight - scrollViewHeight;
            if (collisionPoint > stickStartPoint) {
              inputRange.push(collisionPoint, collisionPoint + 1);
              outputRange.push(collisionPoint - stickStartPoint, collisionPoint - stickStartPoint);
            }
          }
        }
      } else {
        inputRange.push(layoutY);
        outputRange.push(0);
        var _collisionPoint = (nextHeaderLayoutY || 0) - layoutHeight;
        if (_collisionPoint >= layoutY) {
          inputRange.push(_collisionPoint, _collisionPoint + 1);
          outputRange.push(_collisionPoint - layoutY, _collisionPoint - layoutY);
        } else {
          inputRange.push(layoutY + 1);
          outputRange.push(1);
        }
      }
    }
    var newAnimatedTranslateY = scrollAnimatedValue.interpolate({
      inputRange: inputRange,
      outputRange: outputRange
    });
    if (offset != null) {
      newAnimatedTranslateY = _Animated.default.add(newAnimatedTranslateY, offset);
    }
    var animatedListenerId;
    if (isFabric) {
      animatedListenerId = newAnimatedTranslateY.addListener(animatedValueListener);
    }
    setAnimatedTranslateY(newAnimatedTranslateY);
    return function () {
      if (animatedListenerId) {
        newAnimatedTranslateY.removeListener(animatedListenerId);
      }
      if (translateYDebounceTimer.current != null) {
        clearTimeout(translateYDebounceTimer.current);
      }
    };
  }, [nextHeaderLayoutY, measured, layoutHeight, layoutY, scrollViewHeight, scrollAnimatedValue, inverted, offset, animatedValueListener, isFabric]);
  var _onLayout = function _onLayout(event) {
    setLayoutY(event.nativeEvent.layout.y);
    setLayoutHeight(event.nativeEvent.layout.height);
    setMeasured(true);
    props.onLayout(event);
    var child = React.Children.only(props.children);
    if (child.props.onLayout) {
      child.props.onLayout(event);
    }
  };
  var child = React.Children.only(props.children);
  var passthroughAnimatedPropExplicitValues = isFabric && translateY != null ? {
    style: {
      transform: [{
        translateY: translateY
      }]
    }
  } : null;
  return (0, _jsxRuntime.jsx)(_Animated.default.View, {
    collapsable: false,
    nativeID: props.nativeID,
    onLayout: _onLayout,
    ref: ref,
    style: [child.props.style, styles.header, {
      transform: [{
        translateY: animatedTranslateY
      }]
    }],
    passthroughAnimatedPropExplicitValues: passthroughAnimatedPropExplicitValues,
    children: React.cloneElement(child, {
      style: styles.fill,
      onLayout: undefined
    })
  });
});
var styles = _StyleSheet.default.create({
  header: {
    zIndex: 10
  },
  fill: {
    flex: 1
  }
});
var _default = exports.default = ScrollViewStickyHeaderWithForwardedRef;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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