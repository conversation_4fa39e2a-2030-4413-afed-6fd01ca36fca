38e5abd55bbdaf536c0390bec4ca4f01
"use strict";

/* istanbul ignore next */
function cov_vocix0klq() {
  var path = "/Users/<USER>/projects/react-native/payment-module/src/presentation/payment-home/components/bill-list/BillTab.tsx";
  var hash = "d37c5e41be059e03889880a2ca90d5a64b97f97a";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/projects/react-native/payment-module/src/presentation/payment-home/components/bill-list/BillTab.tsx",
    statementMap: {
      "0": {
        start: {
          line: 3,
          column: 29
        },
        end: {
          line: 3,
          column: 84
        }
      },
      "1": {
        start: {
          line: 4,
          column: 22
        },
        end: {
          line: 4,
          column: 93
        }
      },
      "2": {
        start: {
          line: 5,
          column: 22
        },
        end: {
          line: 20,
          column: 2
        }
      },
      "3": {
        start: {
          line: 6,
          column: 2
        },
        end: {
          line: 6,
          column: 31
        }
      },
      "4": {
        start: {
          line: 6,
          column: 24
        },
        end: {
          line: 6,
          column: 31
        }
      },
      "5": {
        start: {
          line: 7,
          column: 13
        },
        end: {
          line: 7,
          column: 50
        }
      },
      "6": {
        start: {
          line: 8,
          column: 2
        },
        end: {
          line: 15,
          column: 3
        }
      },
      "7": {
        start: {
          line: 9,
          column: 4
        },
        end: {
          line: 14,
          column: 6
        }
      },
      "8": {
        start: {
          line: 12,
          column: 8
        },
        end: {
          line: 12,
          column: 20
        }
      },
      "9": {
        start: {
          line: 16,
          column: 2
        },
        end: {
          line: 16,
          column: 37
        }
      },
      "10": {
        start: {
          line: 18,
          column: 2
        },
        end: {
          line: 18,
          column: 31
        }
      },
      "11": {
        start: {
          line: 18,
          column: 24
        },
        end: {
          line: 18,
          column: 31
        }
      },
      "12": {
        start: {
          line: 19,
          column: 2
        },
        end: {
          line: 19,
          column: 15
        }
      },
      "13": {
        start: {
          line: 21,
          column: 25
        },
        end: {
          line: 28,
          column: 2
        }
      },
      "14": {
        start: {
          line: 22,
          column: 2
        },
        end: {
          line: 25,
          column: 5
        }
      },
      "15": {
        start: {
          line: 27,
          column: 2
        },
        end: {
          line: 27,
          column: 19
        }
      },
      "16": {
        start: {
          line: 29,
          column: 19
        },
        end: {
          line: 45,
          column: 3
        }
      },
      "17": {
        start: {
          line: 30,
          column: 17
        },
        end: {
          line: 37,
          column: 3
        }
      },
      "18": {
        start: {
          line: 31,
          column: 4
        },
        end: {
          line: 35,
          column: 6
        }
      },
      "19": {
        start: {
          line: 32,
          column: 15
        },
        end: {
          line: 32,
          column: 17
        }
      },
      "20": {
        start: {
          line: 33,
          column: 6
        },
        end: {
          line: 33,
          column: 89
        }
      },
      "21": {
        start: {
          line: 33,
          column: 23
        },
        end: {
          line: 33,
          column: 89
        }
      },
      "22": {
        start: {
          line: 33,
          column: 71
        },
        end: {
          line: 33,
          column: 89
        }
      },
      "23": {
        start: {
          line: 34,
          column: 6
        },
        end: {
          line: 34,
          column: 16
        }
      },
      "24": {
        start: {
          line: 36,
          column: 4
        },
        end: {
          line: 36,
          column: 23
        }
      },
      "25": {
        start: {
          line: 38,
          column: 2
        },
        end: {
          line: 44,
          column: 4
        }
      },
      "26": {
        start: {
          line: 39,
          column: 4
        },
        end: {
          line: 39,
          column: 42
        }
      },
      "27": {
        start: {
          line: 39,
          column: 31
        },
        end: {
          line: 39,
          column: 42
        }
      },
      "28": {
        start: {
          line: 40,
          column: 17
        },
        end: {
          line: 40,
          column: 19
        }
      },
      "29": {
        start: {
          line: 41,
          column: 4
        },
        end: {
          line: 41,
          column: 134
        }
      },
      "30": {
        start: {
          line: 41,
          column: 21
        },
        end: {
          line: 41,
          column: 134
        }
      },
      "31": {
        start: {
          line: 41,
          column: 34
        },
        end: {
          line: 41,
          column: 47
        }
      },
      "32": {
        start: {
          line: 41,
          column: 53
        },
        end: {
          line: 41,
          column: 54
        }
      },
      "33": {
        start: {
          line: 41,
          column: 75
        },
        end: {
          line: 41,
          column: 134
        }
      },
      "34": {
        start: {
          line: 41,
          column: 99
        },
        end: {
          line: 41,
          column: 134
        }
      },
      "35": {
        start: {
          line: 42,
          column: 4
        },
        end: {
          line: 42,
          column: 36
        }
      },
      "36": {
        start: {
          line: 43,
          column: 4
        },
        end: {
          line: 43,
          column: 18
        }
      },
      "37": {
        start: {
          line: 46,
          column: 0
        },
        end: {
          line: 48,
          column: 3
        }
      },
      "38": {
        start: {
          line: 49,
          column: 0
        },
        end: {
          line: 49,
          column: 24
        }
      },
      "39": {
        start: {
          line: 50,
          column: 21
        },
        end: {
          line: 50,
          column: 44
        }
      },
      "40": {
        start: {
          line: 51,
          column: 14
        },
        end: {
          line: 51,
          column: 44
        }
      },
      "41": {
        start: {
          line: 52,
          column: 21
        },
        end: {
          line: 52,
          column: 46
        }
      },
      "42": {
        start: {
          line: 53,
          column: 23
        },
        end: {
          line: 53,
          column: 50
        }
      },
      "43": {
        start: {
          line: 54,
          column: 16
        },
        end: {
          line: 54,
          column: 36
        }
      },
      "44": {
        start: {
          line: 55,
          column: 14
        },
        end: {
          line: 89,
          column: 1
        }
      },
      "45": {
        start: {
          line: 57,
          column: 13
        },
        end: {
          line: 57,
          column: 88
        }
      },
      "46": {
        start: {
          line: 58,
          column: 13
        },
        end: {
          line: 58,
          column: 38
        }
      },
      "47": {
        start: {
          line: 59,
          column: 12
        },
        end: {
          line: 59,
          column: 49
        }
      },
      "48": {
        start: {
          line: 60,
          column: 17
        },
        end: {
          line: 60,
          column: 25
        }
      },
      "49": {
        start: {
          line: 61,
          column: 20
        },
        end: {
          line: 61,
          column: 28
        }
      },
      "50": {
        start: {
          line: 62,
          column: 2
        },
        end: {
          line: 88,
          column: 6
        }
      },
      "51": {
        start: {
          line: 66,
          column: 6
        },
        end: {
          line: 66,
          column: 27
        }
      },
      "52": {
        start: {
          line: 76,
          column: 6
        },
        end: {
          line: 76,
          column: 34
        }
      },
      "53": {
        start: {
          line: 79,
          column: 6
        },
        end: {
          line: 79,
          column: 32
        }
      },
      "54": {
        start: {
          line: 82,
          column: 6
        },
        end: {
          line: 82,
          column: 35
        }
      },
      "55": {
        start: {
          line: 85,
          column: 6
        },
        end: {
          line: 85,
          column: 42
        }
      },
      "56": {
        start: {
          line: 86,
          column: 6
        },
        end: {
          line: 86,
          column: 35
        }
      },
      "57": {
        start: {
          line: 90,
          column: 0
        },
        end: {
          line: 97,
          column: 3
        }
      },
      "58": {
        start: {
          line: 98,
          column: 0
        },
        end: {
          line: 98,
          column: 26
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 5,
            column: 71
          },
          end: {
            line: 5,
            column: 72
          }
        },
        loc: {
          start: {
            line: 5,
            column: 94
          },
          end: {
            line: 17,
            column: 1
          }
        },
        line: 5
      },
      "1": {
        name: "get",
        decl: {
          start: {
            line: 11,
            column: 20
          },
          end: {
            line: 11,
            column: 23
          }
        },
        loc: {
          start: {
            line: 11,
            column: 26
          },
          end: {
            line: 13,
            column: 7
          }
        },
        line: 11
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 17,
            column: 4
          },
          end: {
            line: 17,
            column: 5
          }
        },
        loc: {
          start: {
            line: 17,
            column: 27
          },
          end: {
            line: 20,
            column: 1
          }
        },
        line: 17
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 21,
            column: 77
          },
          end: {
            line: 21,
            column: 78
          }
        },
        loc: {
          start: {
            line: 21,
            column: 93
          },
          end: {
            line: 26,
            column: 1
          }
        },
        line: 21
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 26,
            column: 4
          },
          end: {
            line: 26,
            column: 5
          }
        },
        loc: {
          start: {
            line: 26,
            column: 20
          },
          end: {
            line: 28,
            column: 1
          }
        },
        line: 26
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 29,
            column: 48
          },
          end: {
            line: 29,
            column: 49
          }
        },
        loc: {
          start: {
            line: 29,
            column: 60
          },
          end: {
            line: 45,
            column: 1
          }
        },
        line: 29
      },
      "6": {
        name: "ownKeys",
        decl: {
          start: {
            line: 30,
            column: 26
          },
          end: {
            line: 30,
            column: 33
          }
        },
        loc: {
          start: {
            line: 30,
            column: 37
          },
          end: {
            line: 37,
            column: 3
          }
        },
        line: 30
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 31,
            column: 45
          },
          end: {
            line: 31,
            column: 46
          }
        },
        loc: {
          start: {
            line: 31,
            column: 58
          },
          end: {
            line: 35,
            column: 5
          }
        },
        line: 31
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 38,
            column: 9
          },
          end: {
            line: 38,
            column: 10
          }
        },
        loc: {
          start: {
            line: 38,
            column: 24
          },
          end: {
            line: 44,
            column: 3
          }
        },
        line: 38
      },
      "9": {
        name: "BillTab",
        decl: {
          start: {
            line: 55,
            column: 23
          },
          end: {
            line: 55,
            column: 30
          }
        },
        loc: {
          start: {
            line: 55,
            column: 38
          },
          end: {
            line: 89,
            column: 1
          }
        },
        line: 55
      },
      "10": {
        name: "onSearch",
        decl: {
          start: {
            line: 65,
            column: 23
          },
          end: {
            line: 65,
            column: 31
          }
        },
        loc: {
          start: {
            line: 65,
            column: 39
          },
          end: {
            line: 67,
            column: 5
          }
        },
        line: 65
      },
      "11": {
        name: "onClick",
        decl: {
          start: {
            line: 75,
            column: 22
          },
          end: {
            line: 75,
            column: 29
          }
        },
        loc: {
          start: {
            line: 75,
            column: 36
          },
          end: {
            line: 77,
            column: 5
          }
        },
        line: 75
      },
      "12": {
        name: "onEdit",
        decl: {
          start: {
            line: 78,
            column: 21
          },
          end: {
            line: 78,
            column: 27
          }
        },
        loc: {
          start: {
            line: 78,
            column: 34
          },
          end: {
            line: 80,
            column: 5
          }
        },
        line: 78
      },
      "13": {
        name: "onDelete",
        decl: {
          start: {
            line: 81,
            column: 23
          },
          end: {
            line: 81,
            column: 31
          }
        },
        loc: {
          start: {
            line: 81,
            column: 38
          },
          end: {
            line: 83,
            column: 5
          }
        },
        line: 81
      },
      "14": {
        name: "onPayment",
        decl: {
          start: {
            line: 84,
            column: 24
          },
          end: {
            line: 84,
            column: 33
          }
        },
        loc: {
          start: {
            line: 84,
            column: 40
          },
          end: {
            line: 87,
            column: 5
          }
        },
        line: 84
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 5,
            column: 22
          },
          end: {
            line: 20,
            column: 2
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 5,
            column: 22
          },
          end: {
            line: 5,
            column: 26
          }
        }, {
          start: {
            line: 5,
            column: 30
          },
          end: {
            line: 5,
            column: 50
          }
        }, {
          start: {
            line: 5,
            column: 55
          },
          end: {
            line: 20,
            column: 1
          }
        }],
        line: 5
      },
      "1": {
        loc: {
          start: {
            line: 5,
            column: 55
          },
          end: {
            line: 20,
            column: 1
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 5,
            column: 71
          },
          end: {
            line: 17,
            column: 1
          }
        }, {
          start: {
            line: 17,
            column: 4
          },
          end: {
            line: 20,
            column: 1
          }
        }],
        line: 5
      },
      "2": {
        loc: {
          start: {
            line: 6,
            column: 2
          },
          end: {
            line: 6,
            column: 31
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 6,
            column: 2
          },
          end: {
            line: 6,
            column: 31
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 6
      },
      "3": {
        loc: {
          start: {
            line: 8,
            column: 2
          },
          end: {
            line: 15,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 8,
            column: 2
          },
          end: {
            line: 15,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 8
      },
      "4": {
        loc: {
          start: {
            line: 8,
            column: 6
          },
          end: {
            line: 8,
            column: 83
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 8,
            column: 6
          },
          end: {
            line: 8,
            column: 11
          }
        }, {
          start: {
            line: 8,
            column: 16
          },
          end: {
            line: 8,
            column: 82
          }
        }],
        line: 8
      },
      "5": {
        loc: {
          start: {
            line: 8,
            column: 16
          },
          end: {
            line: 8,
            column: 82
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 8,
            column: 32
          },
          end: {
            line: 8,
            column: 45
          }
        }, {
          start: {
            line: 8,
            column: 48
          },
          end: {
            line: 8,
            column: 82
          }
        }],
        line: 8
      },
      "6": {
        loc: {
          start: {
            line: 8,
            column: 48
          },
          end: {
            line: 8,
            column: 82
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 8,
            column: 48
          },
          end: {
            line: 8,
            column: 61
          }
        }, {
          start: {
            line: 8,
            column: 65
          },
          end: {
            line: 8,
            column: 82
          }
        }],
        line: 8
      },
      "7": {
        loc: {
          start: {
            line: 18,
            column: 2
          },
          end: {
            line: 18,
            column: 31
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 18,
            column: 2
          },
          end: {
            line: 18,
            column: 31
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 18
      },
      "8": {
        loc: {
          start: {
            line: 21,
            column: 25
          },
          end: {
            line: 28,
            column: 2
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 21,
            column: 25
          },
          end: {
            line: 21,
            column: 29
          }
        }, {
          start: {
            line: 21,
            column: 33
          },
          end: {
            line: 21,
            column: 56
          }
        }, {
          start: {
            line: 21,
            column: 61
          },
          end: {
            line: 28,
            column: 1
          }
        }],
        line: 21
      },
      "9": {
        loc: {
          start: {
            line: 21,
            column: 61
          },
          end: {
            line: 28,
            column: 1
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 21,
            column: 77
          },
          end: {
            line: 26,
            column: 1
          }
        }, {
          start: {
            line: 26,
            column: 4
          },
          end: {
            line: 28,
            column: 1
          }
        }],
        line: 21
      },
      "10": {
        loc: {
          start: {
            line: 29,
            column: 19
          },
          end: {
            line: 45,
            column: 3
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 29,
            column: 19
          },
          end: {
            line: 29,
            column: 23
          }
        }, {
          start: {
            line: 29,
            column: 27
          },
          end: {
            line: 29,
            column: 44
          }
        }, {
          start: {
            line: 29,
            column: 48
          },
          end: {
            line: 45,
            column: 3
          }
        }],
        line: 29
      },
      "11": {
        loc: {
          start: {
            line: 31,
            column: 15
          },
          end: {
            line: 35,
            column: 5
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 31,
            column: 15
          },
          end: {
            line: 31,
            column: 41
          }
        }, {
          start: {
            line: 31,
            column: 45
          },
          end: {
            line: 35,
            column: 5
          }
        }],
        line: 31
      },
      "12": {
        loc: {
          start: {
            line: 33,
            column: 23
          },
          end: {
            line: 33,
            column: 89
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 33,
            column: 23
          },
          end: {
            line: 33,
            column: 89
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 33
      },
      "13": {
        loc: {
          start: {
            line: 39,
            column: 4
          },
          end: {
            line: 39,
            column: 42
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 39,
            column: 4
          },
          end: {
            line: 39,
            column: 42
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 39
      },
      "14": {
        loc: {
          start: {
            line: 39,
            column: 8
          },
          end: {
            line: 39,
            column: 29
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 39,
            column: 8
          },
          end: {
            line: 39,
            column: 11
          }
        }, {
          start: {
            line: 39,
            column: 15
          },
          end: {
            line: 39,
            column: 29
          }
        }],
        line: 39
      },
      "15": {
        loc: {
          start: {
            line: 41,
            column: 4
          },
          end: {
            line: 41,
            column: 134
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 41,
            column: 4
          },
          end: {
            line: 41,
            column: 134
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 41
      },
      "16": {
        loc: {
          start: {
            line: 41,
            column: 75
          },
          end: {
            line: 41,
            column: 134
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 41,
            column: 75
          },
          end: {
            line: 41,
            column: 134
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 41
      },
      "17": {
        loc: {
          start: {
            line: 71,
            column: 15
          },
          end: {
            line: 71,
            column: 86
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 71,
            column: 62
          },
          end: {
            line: 71,
            column: 78
          }
        }, {
          start: {
            line: 71,
            column: 81
          },
          end: {
            line: 71,
            column: 86
          }
        }],
        line: 71
      },
      "18": {
        loc: {
          start: {
            line: 73,
            column: 16
          },
          end: {
            line: 73,
            column: 104
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 73,
            column: 75
          },
          end: {
            line: 73,
            column: 96
          }
        }, {
          start: {
            line: 73,
            column: 99
          },
          end: {
            line: 73,
            column: 104
          }
        }],
        line: 73
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0
    },
    b: {
      "0": [0, 0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0, 0],
      "9": [0, 0],
      "10": [0, 0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0],
      "18": [0, 0]
    },
    inputSourceMap: {
      version: 3,
      file: undefined,
      names: ["react_native_1", "require", "react_1", "__importStar", "BillList_tsx_1", "SearchBill_tsx_1", "hook_ts_1", "BillTab", "props", "_props$isBlocked", "_props$isShowAddConta", "hook", "useContacts", "isShowAddContact", "contacts", "_ref", "useState", "_ref2", "_slicedToArray2", "default", "searchText", "setSearchText", "createElement", "View", "style", "exports", "styles", "container", "SearchBill", "onSearch", "value", "onAddNewContact", "BillList", "isBlocked", "isEditable", "bills", "onClick", "item", "gotoDetailScreen", "onEdit", "gotoEditScreen", "onDelete", "showConfirmDialog", "onPayment", "console", "log", "gotoPaymentScreen", "StyleSheet", "create", "flexDirection", "flex", "justifyContent", "width"],
      sourceRoot: undefined,
      sources: ["/Users/<USER>/projects/react-native/payment-module/src/presentation/payment-home/components/bill-list/BillTab.tsx"],
      sourcesContent: ["import {StyleSheet, View} from 'react-native';\nimport React, {useState} from 'react';\n\nimport {BillList} from './BillList.tsx';\nimport {SearchBill} from './SearchBill.tsx';\nimport {useContacts} from './hook.ts';\nimport {IBillContact} from '../../../../domain/entities/IBillContact.ts';\n\ninterface BillTabProps {\n  isBlocked?: boolean;\n  isShowAddContact?: boolean;\n  contacts?: IBillContact[];\n  onAddNewContact?: () => void;\n}\n\nconst BillTab = (props: BillTabProps) => {\n  const hook = useContacts(props.isShowAddContact === true, props.contacts);\n\n  const [searchText, setSearchText] = useState('');\n\n  return (\n    <View style={styles.container}>\n      <SearchBill\n        onSearch={value => {\n          setSearchText(value);\n        }}\n        isShowAddContact={props.isShowAddContact}\n        onAddNewContact={props.onAddNewContact}\n      />\n      <BillList\n        isBlocked={props.isBlocked ?? false}\n        searchText={searchText}\n        isEditable={props.isShowAddContact ?? false}\n        bills={hook.contacts}\n        onClick={item => {\n          hook.gotoDetailScreen(item);\n        }}\n        onEdit={item => {\n          hook.gotoEditScreen(item);\n        }}\n        onDelete={item => {\n          hook.showConfirmDialog(item);\n        }}\n        onPayment={item => {\n          console.log('onPayment ==>>', item);\n          hook.gotoPaymentScreen(item);\n        }}\n      />\n    </View>\n  );\n};\n\nexport const styles = StyleSheet.create({\n  container: {\n    flexDirection: 'column',\n    flex: 1,\n    justifyContent: 'center',\n    width: '100%',\n  },\n});\n\nexport default BillTab;\n"],
      mappings: ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAAA,cAAA,GAAAC,OAAA;AACA,IAAAC,OAAA,GAAAC,YAAA,CAAAF,OAAA;AAEA,IAAAG,cAAA,GAAAH,OAAA;AACA,IAAAI,gBAAA,GAAAJ,OAAA;AACA,IAAAK,SAAA,GAAAL,OAAA;AAUA,IAAMM,OAAO,GAAG,SAAVA,OAAOA,CAAIC,KAAmB,EAAI;EAAA,IAAAC,gBAAA,EAAAC,qBAAA;EACtC,IAAMC,IAAI,GAAG,IAAAL,SAAA,CAAAM,WAAW,EAACJ,KAAK,CAACK,gBAAgB,KAAK,IAAI,EAAEL,KAAK,CAACM,QAAQ,CAAC;EAEzE,IAAAC,IAAA,GAAoC,IAAAb,OAAA,CAAAc,QAAQ,EAAC,EAAE,CAAC;IAAAC,KAAA,OAAAC,eAAA,CAAAC,OAAA,EAAAJ,IAAA;IAAzCK,UAAU,GAAAH,KAAA;IAAEI,aAAa,GAAAJ,KAAA;EAEhC,OACEf,OAAA,CAAAiB,OAAA,CAAAG,aAAA,CAACtB,cAAA,CAAAuB,IAAI;IAACC,KAAK,EAAEC,OAAA,CAAAC,MAAM,CAACC;EAAS,GAC3BzB,OAAA,CAAAiB,OAAA,CAAAG,aAAA,CAACjB,gBAAA,CAAAuB,UAAU;IACTC,QAAQ,EAAE,SAAVA,QAAQA,CAAEC,KAAK,EAAG;MAChBT,aAAa,CAACS,KAAK,CAAC;IACtB,CAAC;IACDjB,gBAAgB,EAAEL,KAAK,CAACK,gBAAgB;IACxCkB,eAAe,EAAEvB,KAAK,CAACuB;EAAe,EACtC,EACF7B,OAAA,CAAAiB,OAAA,CAAAG,aAAA,CAAClB,cAAA,CAAA4B,QAAQ;IACPC,SAAS,GAAAxB,gBAAA,GAAED,KAAK,CAACyB,SAAS,YAAAxB,gBAAA,GAAI,KAAK;IACnCW,UAAU,EAAEA,UAAU;IACtBc,UAAU,GAAAxB,qBAAA,GAAEF,KAAK,CAACK,gBAAgB,YAAAH,qBAAA,GAAI,KAAK;IAC3CyB,KAAK,EAAExB,IAAI,CAACG,QAAQ;IACpBsB,OAAO,EAAE,SAATA,OAAOA,CAAEC,IAAI,EAAG;MACd1B,IAAI,CAAC2B,gBAAgB,CAACD,IAAI,CAAC;IAC7B,CAAC;IACDE,MAAM,EAAE,SAARA,MAAMA,CAAEF,IAAI,EAAG;MACb1B,IAAI,CAAC6B,cAAc,CAACH,IAAI,CAAC;IAC3B,CAAC;IACDI,QAAQ,EAAE,SAAVA,QAAQA,CAAEJ,IAAI,EAAG;MACf1B,IAAI,CAAC+B,iBAAiB,CAACL,IAAI,CAAC;IAC9B,CAAC;IACDM,SAAS,EAAE,SAAXA,SAASA,CAAEN,IAAI,EAAG;MAChBO,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAER,IAAI,CAAC;MACnC1B,IAAI,CAACmC,iBAAiB,CAACT,IAAI,CAAC;IAC9B;EAAC,EACD,CACG;AAEX,CAAC;AAEYZ,OAAA,CAAAC,MAAM,GAAG1B,cAAA,CAAA+C,UAAU,CAACC,MAAM,CAAC;EACtCrB,SAAS,EAAE;IACTsB,aAAa,EAAE,QAAQ;IACvBC,IAAI,EAAE,CAAC;IACPC,cAAc,EAAE,QAAQ;IACxBC,KAAK,EAAE;;CAEV,CAAC;AAEF3B,OAAA,CAAAN,OAAA,GAAeZ,OAAO",
      ignoreList: []
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "d37c5e41be059e03889880a2ca90d5a64b97f97a"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_vocix0klq = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_vocix0klq();
var _interopRequireDefault =
/* istanbul ignore next */
(cov_vocix0klq().s[0]++, require("@babel/runtime/helpers/interopRequireDefault"));
var _slicedToArray2 =
/* istanbul ignore next */
(cov_vocix0klq().s[1]++, _interopRequireDefault(require("@babel/runtime/helpers/slicedToArray")));
var __createBinding =
/* istanbul ignore next */
(cov_vocix0klq().s[2]++,
/* istanbul ignore next */
(cov_vocix0klq().b[0][0]++, this) &&
/* istanbul ignore next */
(cov_vocix0klq().b[0][1]++, this.__createBinding) ||
/* istanbul ignore next */
(cov_vocix0klq().b[0][2]++, Object.create ?
/* istanbul ignore next */
(cov_vocix0klq().b[1][0]++, function (o, m, k, k2) {
  /* istanbul ignore next */
  cov_vocix0klq().f[0]++;
  cov_vocix0klq().s[3]++;
  if (k2 === undefined) {
    /* istanbul ignore next */
    cov_vocix0klq().b[2][0]++;
    cov_vocix0klq().s[4]++;
    k2 = k;
  } else
  /* istanbul ignore next */
  {
    cov_vocix0klq().b[2][1]++;
  }
  var desc =
  /* istanbul ignore next */
  (cov_vocix0klq().s[5]++, Object.getOwnPropertyDescriptor(m, k));
  /* istanbul ignore next */
  cov_vocix0klq().s[6]++;
  if (
  /* istanbul ignore next */
  (cov_vocix0klq().b[4][0]++, !desc) ||
  /* istanbul ignore next */
  (cov_vocix0klq().b[4][1]++, "get" in desc ?
  /* istanbul ignore next */
  (cov_vocix0klq().b[5][0]++, !m.__esModule) :
  /* istanbul ignore next */
  (cov_vocix0klq().b[5][1]++,
  /* istanbul ignore next */
  (cov_vocix0klq().b[6][0]++, desc.writable) ||
  /* istanbul ignore next */
  (cov_vocix0klq().b[6][1]++, desc.configurable)))) {
    /* istanbul ignore next */
    cov_vocix0klq().b[3][0]++;
    cov_vocix0klq().s[7]++;
    desc = {
      enumerable: true,
      get: function get() {
        /* istanbul ignore next */
        cov_vocix0klq().f[1]++;
        cov_vocix0klq().s[8]++;
        return m[k];
      }
    };
  } else
  /* istanbul ignore next */
  {
    cov_vocix0klq().b[3][1]++;
  }
  cov_vocix0klq().s[9]++;
  Object.defineProperty(o, k2, desc);
}) :
/* istanbul ignore next */
(cov_vocix0klq().b[1][1]++, function (o, m, k, k2) {
  /* istanbul ignore next */
  cov_vocix0klq().f[2]++;
  cov_vocix0klq().s[10]++;
  if (k2 === undefined) {
    /* istanbul ignore next */
    cov_vocix0klq().b[7][0]++;
    cov_vocix0klq().s[11]++;
    k2 = k;
  } else
  /* istanbul ignore next */
  {
    cov_vocix0klq().b[7][1]++;
  }
  cov_vocix0klq().s[12]++;
  o[k2] = m[k];
})));
var __setModuleDefault =
/* istanbul ignore next */
(cov_vocix0klq().s[13]++,
/* istanbul ignore next */
(cov_vocix0klq().b[8][0]++, this) &&
/* istanbul ignore next */
(cov_vocix0klq().b[8][1]++, this.__setModuleDefault) ||
/* istanbul ignore next */
(cov_vocix0klq().b[8][2]++, Object.create ?
/* istanbul ignore next */
(cov_vocix0klq().b[9][0]++, function (o, v) {
  /* istanbul ignore next */
  cov_vocix0klq().f[3]++;
  cov_vocix0klq().s[14]++;
  Object.defineProperty(o, "default", {
    enumerable: true,
    value: v
  });
}) :
/* istanbul ignore next */
(cov_vocix0klq().b[9][1]++, function (o, v) {
  /* istanbul ignore next */
  cov_vocix0klq().f[4]++;
  cov_vocix0klq().s[15]++;
  o["default"] = v;
})));
var __importStar =
/* istanbul ignore next */
(cov_vocix0klq().s[16]++,
/* istanbul ignore next */
(cov_vocix0klq().b[10][0]++, this) &&
/* istanbul ignore next */
(cov_vocix0klq().b[10][1]++, this.__importStar) ||
/* istanbul ignore next */
(cov_vocix0klq().b[10][2]++, function () {
  /* istanbul ignore next */
  cov_vocix0klq().f[5]++;
  cov_vocix0klq().s[17]++;
  var _ownKeys = function ownKeys(o) {
    /* istanbul ignore next */
    cov_vocix0klq().f[6]++;
    cov_vocix0klq().s[18]++;
    _ownKeys =
    /* istanbul ignore next */
    (cov_vocix0klq().b[11][0]++, Object.getOwnPropertyNames) ||
    /* istanbul ignore next */
    (cov_vocix0klq().b[11][1]++, function (o) {
      /* istanbul ignore next */
      cov_vocix0klq().f[7]++;
      var ar =
      /* istanbul ignore next */
      (cov_vocix0klq().s[19]++, []);
      /* istanbul ignore next */
      cov_vocix0klq().s[20]++;
      for (var k in o) {
        /* istanbul ignore next */
        cov_vocix0klq().s[21]++;
        if (Object.prototype.hasOwnProperty.call(o, k)) {
          /* istanbul ignore next */
          cov_vocix0klq().b[12][0]++;
          cov_vocix0klq().s[22]++;
          ar[ar.length] = k;
        } else
        /* istanbul ignore next */
        {
          cov_vocix0klq().b[12][1]++;
        }
      }
      /* istanbul ignore next */
      cov_vocix0klq().s[23]++;
      return ar;
    });
    /* istanbul ignore next */
    cov_vocix0klq().s[24]++;
    return _ownKeys(o);
  };
  /* istanbul ignore next */
  cov_vocix0klq().s[25]++;
  return function (mod) {
    /* istanbul ignore next */
    cov_vocix0klq().f[8]++;
    cov_vocix0klq().s[26]++;
    if (
    /* istanbul ignore next */
    (cov_vocix0klq().b[14][0]++, mod) &&
    /* istanbul ignore next */
    (cov_vocix0klq().b[14][1]++, mod.__esModule)) {
      /* istanbul ignore next */
      cov_vocix0klq().b[13][0]++;
      cov_vocix0klq().s[27]++;
      return mod;
    } else
    /* istanbul ignore next */
    {
      cov_vocix0klq().b[13][1]++;
    }
    var result =
    /* istanbul ignore next */
    (cov_vocix0klq().s[28]++, {});
    /* istanbul ignore next */
    cov_vocix0klq().s[29]++;
    if (mod != null) {
      /* istanbul ignore next */
      cov_vocix0klq().b[15][0]++;
      cov_vocix0klq().s[30]++;
      for (var k =
        /* istanbul ignore next */
        (cov_vocix0klq().s[31]++, _ownKeys(mod)), i =
        /* istanbul ignore next */
        (cov_vocix0klq().s[32]++, 0); i < k.length; i++) {
        /* istanbul ignore next */
        cov_vocix0klq().s[33]++;
        if (k[i] !== "default") {
          /* istanbul ignore next */
          cov_vocix0klq().b[16][0]++;
          cov_vocix0klq().s[34]++;
          __createBinding(result, mod, k[i]);
        } else
        /* istanbul ignore next */
        {
          cov_vocix0klq().b[16][1]++;
        }
      }
    } else
    /* istanbul ignore next */
    {
      cov_vocix0klq().b[15][1]++;
    }
    cov_vocix0klq().s[35]++;
    __setModuleDefault(result, mod);
    /* istanbul ignore next */
    cov_vocix0klq().s[36]++;
    return result;
  };
}()));
/* istanbul ignore next */
cov_vocix0klq().s[37]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_vocix0klq().s[38]++;
exports.styles = void 0;
var react_native_1 =
/* istanbul ignore next */
(cov_vocix0klq().s[39]++, require("react-native"));
var react_1 =
/* istanbul ignore next */
(cov_vocix0klq().s[40]++, __importStar(require("react")));
var BillList_tsx_1 =
/* istanbul ignore next */
(cov_vocix0klq().s[41]++, require("./BillList.tsx"));
var SearchBill_tsx_1 =
/* istanbul ignore next */
(cov_vocix0klq().s[42]++, require("./SearchBill.tsx"));
var hook_ts_1 =
/* istanbul ignore next */
(cov_vocix0klq().s[43]++, require("./hook.ts"));
/* istanbul ignore next */
cov_vocix0klq().s[44]++;
var BillTab = function BillTab(props) {
  /* istanbul ignore next */
  cov_vocix0klq().f[9]++;
  var _props$isBlocked, _props$isShowAddConta;
  var hook =
  /* istanbul ignore next */
  (cov_vocix0klq().s[45]++, (0, hook_ts_1.useContacts)(props.isShowAddContact === true, props.contacts));
  var _ref =
    /* istanbul ignore next */
    (cov_vocix0klq().s[46]++, (0, react_1.useState)('')),
    _ref2 =
    /* istanbul ignore next */
    (cov_vocix0klq().s[47]++, (0, _slicedToArray2.default)(_ref, 2)),
    searchText =
    /* istanbul ignore next */
    (cov_vocix0klq().s[48]++, _ref2[0]),
    setSearchText =
    /* istanbul ignore next */
    (cov_vocix0klq().s[49]++, _ref2[1]);
  /* istanbul ignore next */
  cov_vocix0klq().s[50]++;
  return react_1.default.createElement(react_native_1.View, {
    style: exports.styles.container
  }, react_1.default.createElement(SearchBill_tsx_1.SearchBill, {
    onSearch: function onSearch(value) {
      /* istanbul ignore next */
      cov_vocix0klq().f[10]++;
      cov_vocix0klq().s[51]++;
      setSearchText(value);
    },
    isShowAddContact: props.isShowAddContact,
    onAddNewContact: props.onAddNewContact
  }), react_1.default.createElement(BillList_tsx_1.BillList, {
    isBlocked: (_props$isBlocked = props.isBlocked) != null ?
    /* istanbul ignore next */
    (cov_vocix0klq().b[17][0]++, _props$isBlocked) :
    /* istanbul ignore next */
    (cov_vocix0klq().b[17][1]++, false),
    searchText: searchText,
    isEditable: (_props$isShowAddConta = props.isShowAddContact) != null ?
    /* istanbul ignore next */
    (cov_vocix0klq().b[18][0]++, _props$isShowAddConta) :
    /* istanbul ignore next */
    (cov_vocix0klq().b[18][1]++, false),
    bills: hook.contacts,
    onClick: function onClick(item) {
      /* istanbul ignore next */
      cov_vocix0klq().f[11]++;
      cov_vocix0klq().s[52]++;
      hook.gotoDetailScreen(item);
    },
    onEdit: function onEdit(item) {
      /* istanbul ignore next */
      cov_vocix0klq().f[12]++;
      cov_vocix0klq().s[53]++;
      hook.gotoEditScreen(item);
    },
    onDelete: function onDelete(item) {
      /* istanbul ignore next */
      cov_vocix0klq().f[13]++;
      cov_vocix0klq().s[54]++;
      hook.showConfirmDialog(item);
    },
    onPayment: function onPayment(item) {
      /* istanbul ignore next */
      cov_vocix0klq().f[14]++;
      cov_vocix0klq().s[55]++;
      console.log('onPayment ==>>', item);
      /* istanbul ignore next */
      cov_vocix0klq().s[56]++;
      hook.gotoPaymentScreen(item);
    }
  }));
};
/* istanbul ignore next */
cov_vocix0klq().s[57]++;
exports.styles = react_native_1.StyleSheet.create({
  container: {
    flexDirection: 'column',
    flex: 1,
    justifyContent: 'center',
    width: '100%'
  }
});
/* istanbul ignore next */
cov_vocix0klq().s[58]++;
exports.default = BillTab;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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