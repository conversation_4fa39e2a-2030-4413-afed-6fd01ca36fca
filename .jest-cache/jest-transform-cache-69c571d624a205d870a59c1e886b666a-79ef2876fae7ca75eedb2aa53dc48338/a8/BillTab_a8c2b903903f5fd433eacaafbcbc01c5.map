{"version": 3, "names": ["react_native_1", "cov_vocix0klq", "s", "require", "react_1", "__importStar", "BillList_tsx_1", "SearchBill_tsx_1", "hook_ts_1", "<PERSON><PERSON><PERSON>", "props", "f", "_props$isBlocked", "_props$isShowAddConta", "hook", "useContacts", "isShowAddContact", "contacts", "_ref", "useState", "_ref2", "_slicedToArray2", "default", "searchText", "setSearchText", "createElement", "View", "style", "exports", "styles", "container", "SearchBill", "onSearch", "value", "onAddNewContact", "BillList", "isBlocked", "b", "isEditable", "bills", "onClick", "item", "gotoDetailScreen", "onEdit", "gotoEditScreen", "onDelete", "showConfirmDialog", "onPayment", "console", "log", "gotoPaymentScreen", "StyleSheet", "create", "flexDirection", "flex", "justifyContent", "width"], "sources": ["/Users/<USER>/projects/react-native/payment-module/src/presentation/payment-home/components/bill-list/BillTab.tsx"], "sourcesContent": ["import {StyleSheet, View} from 'react-native';\nimport React, {useState} from 'react';\n\nimport {BillList} from './BillList.tsx';\nimport {SearchBill} from './SearchBill.tsx';\nimport {useContacts} from './hook.ts';\nimport {IBillContact} from '../../../../domain/entities/IBillContact.ts';\n\ninterface BillTabProps {\n  isBlocked?: boolean;\n  isShowAddContact?: boolean;\n  contacts?: IBillContact[];\n  onAddNewContact?: () => void;\n}\n\nconst BillTab = (props: BillTabProps) => {\n  const hook = useContacts(props.isShowAddContact === true, props.contacts);\n\n  const [searchText, setSearchText] = useState('');\n\n  return (\n    <View style={styles.container}>\n      <SearchBill\n        onSearch={value => {\n          setSearchText(value);\n        }}\n        isShowAddContact={props.isShowAddContact}\n        onAddNewContact={props.onAddNewContact}\n      />\n      <BillList\n        isBlocked={props.isBlocked ?? false}\n        searchText={searchText}\n        isEditable={props.isShowAddContact ?? false}\n        bills={hook.contacts}\n        onClick={item => {\n          hook.gotoDetailScreen(item);\n        }}\n        onEdit={item => {\n          hook.gotoEditScreen(item);\n        }}\n        onDelete={item => {\n          hook.showConfirmDialog(item);\n        }}\n        onPayment={item => {\n          console.log('onPayment ==>>', item);\n          hook.gotoPaymentScreen(item);\n        }}\n      />\n    </View>\n  );\n};\n\nexport const styles = StyleSheet.create({\n  container: {\n    flexDirection: 'column',\n    flex: 1,\n    justifyContent: 'center',\n    width: '100%',\n  },\n});\n\nexport default BillTab;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAAA,cAAA;AAAA;AAAA,CAAAC,aAAA,GAAAC,CAAA,QAAAC,OAAA;AACA,IAAAC,OAAA;AAAA;AAAA,CAAAH,aAAA,GAAAC,CAAA,QAAAG,YAAA,CAAAF,OAAA;AAEA,IAAAG,cAAA;AAAA;AAAA,CAAAL,aAAA,GAAAC,CAAA,QAAAC,OAAA;AACA,IAAAI,gBAAA;AAAA;AAAA,CAAAN,aAAA,GAAAC,CAAA,QAAAC,OAAA;AACA,IAAAK,SAAA;AAAA;AAAA,CAAAP,aAAA,GAAAC,CAAA,QAAAC,OAAA;AAAA;AAAAF,aAAA,GAAAC,CAAA;AAUA,IAAMO,OAAO,GAAG,SAAVA,OAAOA,CAAIC,KAAmB,EAAI;EAAA;EAAAT,aAAA,GAAAU,CAAA;EAAA,IAAAC,gBAAA,EAAAC,qBAAA;EACtC,IAAMC,IAAI;EAAA;EAAA,CAAAb,aAAA,GAAAC,CAAA,QAAG,IAAAM,SAAA,CAAAO,WAAW,EAACL,KAAK,CAACM,gBAAgB,KAAK,IAAI,EAAEN,KAAK,CAACO,QAAQ,CAAC;EAEzE,IAAAC,IAAA;IAAA;IAAA,CAAAjB,aAAA,GAAAC,CAAA,QAAoC,IAAAE,OAAA,CAAAe,QAAQ,EAAC,EAAE,CAAC;IAAAC,KAAA;IAAA;IAAA,CAAAnB,aAAA,GAAAC,CAAA,YAAAmB,eAAA,CAAAC,OAAA,EAAAJ,IAAA;IAAzCK,UAAU;IAAA;IAAA,CAAAtB,aAAA,GAAAC,CAAA,QAAAkB,KAAA;IAAEI,aAAa;IAAA;IAAA,CAAAvB,aAAA,GAAAC,CAAA,QAAAkB,KAAA;EAAA;EAAAnB,aAAA,GAAAC,CAAA;EAEhC,OACEE,OAAA,CAAAkB,OAAA,CAAAG,aAAA,CAACzB,cAAA,CAAA0B,IAAI;IAACC,KAAK,EAAEC,OAAA,CAAAC,MAAM,CAACC;EAAS,GAC3B1B,OAAA,CAAAkB,OAAA,CAAAG,aAAA,CAAClB,gBAAA,CAAAwB,UAAU;IACTC,QAAQ,EAAE,SAAVA,QAAQA,CAAEC,KAAK,EAAG;MAAA;MAAAhC,aAAA,GAAAU,CAAA;MAAAV,aAAA,GAAAC,CAAA;MAChBsB,aAAa,CAACS,KAAK,CAAC;IACtB,CAAC;IACDjB,gBAAgB,EAAEN,KAAK,CAACM,gBAAgB;IACxCkB,eAAe,EAAExB,KAAK,CAACwB;EAAe,EACtC,EACF9B,OAAA,CAAAkB,OAAA,CAAAG,aAAA,CAACnB,cAAA,CAAA6B,QAAQ;IACPC,SAAS,GAAAxB,gBAAA,GAAEF,KAAK,CAAC0B,SAAS;IAAA;IAAA,CAAAnC,aAAA,GAAAoC,CAAA,WAAAzB,gBAAA;IAAA;IAAA,CAAAX,aAAA,GAAAoC,CAAA,WAAI,KAAK;IACnCd,UAAU,EAAEA,UAAU;IACtBe,UAAU,GAAAzB,qBAAA,GAAEH,KAAK,CAACM,gBAAgB;IAAA;IAAA,CAAAf,aAAA,GAAAoC,CAAA,WAAAxB,qBAAA;IAAA;IAAA,CAAAZ,aAAA,GAAAoC,CAAA,WAAI,KAAK;IAC3CE,KAAK,EAAEzB,IAAI,CAACG,QAAQ;IACpBuB,OAAO,EAAE,SAATA,OAAOA,CAAEC,IAAI,EAAG;MAAA;MAAAxC,aAAA,GAAAU,CAAA;MAAAV,aAAA,GAAAC,CAAA;MACdY,IAAI,CAAC4B,gBAAgB,CAACD,IAAI,CAAC;IAC7B,CAAC;IACDE,MAAM,EAAE,SAARA,MAAMA,CAAEF,IAAI,EAAG;MAAA;MAAAxC,aAAA,GAAAU,CAAA;MAAAV,aAAA,GAAAC,CAAA;MACbY,IAAI,CAAC8B,cAAc,CAACH,IAAI,CAAC;IAC3B,CAAC;IACDI,QAAQ,EAAE,SAAVA,QAAQA,CAAEJ,IAAI,EAAG;MAAA;MAAAxC,aAAA,GAAAU,CAAA;MAAAV,aAAA,GAAAC,CAAA;MACfY,IAAI,CAACgC,iBAAiB,CAACL,IAAI,CAAC;IAC9B,CAAC;IACDM,SAAS,EAAE,SAAXA,SAASA,CAAEN,IAAI,EAAG;MAAA;MAAAxC,aAAA,GAAAU,CAAA;MAAAV,aAAA,GAAAC,CAAA;MAChB8C,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAER,IAAI,CAAC;MAAA;MAAAxC,aAAA,GAAAC,CAAA;MACnCY,IAAI,CAACoC,iBAAiB,CAACT,IAAI,CAAC;IAC9B;EAAC,EACD,CACG;AAEX,CAAC;AAAA;AAAAxC,aAAA,GAAAC,CAAA;AAEY0B,OAAA,CAAAC,MAAM,GAAG7B,cAAA,CAAAmD,UAAU,CAACC,MAAM,CAAC;EACtCtB,SAAS,EAAE;IACTuB,aAAa,EAAE,QAAQ;IACvBC,IAAI,EAAE,CAAC;IACPC,cAAc,EAAE,QAAQ;IACxBC,KAAK,EAAE;;CAEV,CAAC;AAAA;AAAAvD,aAAA,GAAAC,CAAA;AAEF0B,OAAA,CAAAN,OAAA,GAAeb,OAAO", "ignoreList": []}