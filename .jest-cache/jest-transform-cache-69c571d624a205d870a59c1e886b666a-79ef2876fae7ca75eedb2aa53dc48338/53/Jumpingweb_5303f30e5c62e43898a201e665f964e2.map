{"version": 3, "names": ["Object", "defineProperty", "exports", "value", "JumpingTransition", "_Easing", "require", "name", "transitionData", "translateX", "translateY", "scaleX", "scaleY", "d", "Math", "max", "abs", "peakTranslateY", "jumpingTransition", "style", "transform", "scale", "easing", "Easing", "exp", "duration"], "sources": ["../../../../../src/layoutReanimation/web/transition/Jumping.web.ts"], "sourcesContent": [null], "mappings": "AAAA,YAAY;;AAAAA,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,KAAA;AAAA;AAAAD,OAAA,CAAAE,iBAAA,GAAAA,iBAAA;AAEZ,IAAAC,OAAA,GAAAC,OAAA;AAEO,SAASF,iBAAiBA,CAC/BG,IAAY,EACZC,cAA8B,EAC9B;EACA,IAAQC,UAAU,GAAiCD,cAAc,CAAzDC,UAAU;IAAEC,UAAU,GAAqBF,cAAc,CAA7CE,UAAU;IAAEC,MAAM,GAAaH,cAAc,CAAjCG,MAAM;IAAEC,MAAA,GAAWJ,cAAc,CAAzBI,MAAA;EAExC,IAAMC,CAAC,GAAGC,IAAI,CAACC,GAAG,CAACD,IAAI,CAACE,GAAG,CAACP,UAAU,CAAC,EAAEK,IAAI,CAACE,GAAG,CAACN,UAAU,CAAC,CAAC,GAAG,CAAC;EAClE,IAAMO,cAAc,GAAGP,UAAU,IAAI,CAAC,GAAGA,UAAU,GAAGG,CAAC,GAAG,CAACH,UAAU,GAAGG,CAAC;EAEzE,IAAMK,iBAAiB,GAAG;IACxBX,IAAI,EAAJA,IAAI;IACJY,KAAK,EAAE;MACL,CAAC,EAAE;QACDC,SAAS,EAAE,CACT;UACEX,UAAU,EAAE,GAAGA,UAAU,IAAI;UAC7BC,UAAU,EAAE,GAAGA,UAAU,IAAI;UAC7BW,KAAK,EAAE,GAAGV,MAAM,IAAIC,MAAM;QAC5B,CAAC,CACF;QACDU,MAAM,EAAEC,cAAM,CAACC;MACjB,CAAC;MACD,EAAE,EAAE;QACFJ,SAAS,EAAE,CACT;UACEX,UAAU,EAAE,GAAGA,UAAU,GAAG,CAAC,IAAI;UACjCC,UAAU,EAAE,GAAGO,cAAc,IAAI;UACjCI,KAAK,EAAE,GAAGV,MAAM,IAAIC,MAAM;QAC5B,CAAC;MAEL,CAAC;MACD,GAAG,EAAE;QACHQ,SAAS,EAAE,CAAC;UAAEX,UAAU,EAAE,KAAK;UAAEC,UAAU,EAAE,KAAK;UAAEW,KAAK,EAAE;QAAM,CAAC;MACpE;IACF,CAAC;IACDI,QAAQ,EAAE;EACZ,CAAC;EAED,OAAOP,iBAAiB;AAC1B", "ignoreList": []}