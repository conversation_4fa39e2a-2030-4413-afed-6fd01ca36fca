{"version": 3, "names": ["Object", "defineProperty", "exports", "value", "Extrapolation", "clamp", "interpolate", "_errors", "require", "getVal", "type", "coef", "val", "leftEdgeOutput", "rightEdgeOutput", "x", "IDENTITY", "CLAMP", "EXTEND", "isExtrapolate", "validateType", "extrapolationConfig", "extrapolateLeft", "extrapolateRight", "ReanimatedError", "assign", "internalInterpolate", "narrowedInput", "leftEdgeInput", "rightEdgeInput", "progress", "inputRange", "outputRange", "length", "i", "min", "max", "Math"], "sources": ["../../src/interpolation.ts"], "sourcesContent": [null], "mappings": "AAAA,YAAY;;AAAAA,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,KAAA;AAAA;AAAAD,OAAA,CAAAE,aAAA;AAAAF,OAAA,CAAAG,KAAA,GAAAA,KAAA;AAAAH,OAAA,CAAAI,WAAA,GAAAA,WAAA;AAEZ,IAAAC,OAAA,GAAAC,OAAA;AASA,IAAYJ,aAAa,GAAAF,OAAA,CAAAE,aAAA,aAAbA,aAAa;EAAbA,aAAa;EAAbA,aAAa;EAAbA,aAAa;EAAA,OAAbA,aAAa;AAAA;AAkCzB,SAASK,MAAMA,CACbC,IAAmB,EACnBC,IAAY,EACZC,GAAW,EACXC,cAAsB,EACtBC,eAAuB,EACvBC,CAAS,EACD;EACR,SAAS;;EAET,QAAQL,IAAI;IACV,KAAKN,aAAa,CAACY,QAAQ;MACzB,OAAOD,CAAC;IACV,KAAKX,aAAa,CAACa,KAAK;MACtB,IAAIN,IAAI,GAAGC,GAAG,GAAGD,IAAI,GAAGE,cAAc,EAAE;QACtC,OAAOA,cAAc;MACvB;MACA,OAAOC,eAAe;IACxB,KAAKV,aAAa,CAACc,MAAM;IACzB;MACE,OAAON,GAAG;EACd;AACF;AAEA,SAASO,aAAaA,CAAChB,KAAa,EAA0B;EAC5D,SAAS;;EAET,OAEEA,KAAK,KAAKC,aAAa,CAACc,MAAM,IAC9Bf,KAAK,KAAKC,aAAa,CAACa,KAAK,IAC7Bd,KAAK,KAAKC,aAAa,CAACY,QAAA;AAG5B;AAIA,SAASI,YAAYA,CAACV,IAAuB,EAA+B;EAC1E,SAAS;EAET,IAAMW,mBAAgD,GAAG;IACvDC,eAAe,EAAElB,aAAa,CAACc,MAAM;IACrCK,gBAAgB,EAAEnB,aAAa,CAACc;EAClC,CAAC;EAED,IAAI,CAACR,IAAI,EAAE;IACT,OAAOW,mBAAmB;EAC5B;EAEA,IAAI,OAAOX,IAAI,KAAK,QAAQ,EAAE;IAC5B,IAAI,CAACS,aAAa,CAACT,IAAI,CAAC,EAAE;MACxB,MAAM,IAAIc,uBAAe,CACvB;AACR,iEACM,CAAC;IACH;IACAH,mBAAmB,CAACC,eAAe,GAAGZ,IAAI;IAC1CW,mBAAmB,CAACE,gBAAgB,GAAGb,IAAI;IAC3C,OAAOW,mBAAmB;EAC5B;EAGA,IACGX,IAAI,CAACY,eAAe,IAAI,CAACH,aAAa,CAACT,IAAI,CAACY,eAAe,CAAC,IAC5DZ,IAAI,CAACa,gBAAgB,IAAI,CAACJ,aAAa,CAACT,IAAI,CAACa,gBAAgB,CAAE,EAChE;IACA,MAAM,IAAIC,uBAAe,CACvB;AACN;AACA;AACA;AACA,UACI,CAAC;EACH;EAEAxB,MAAM,CAACyB,MAAM,CAACJ,mBAAmB,EAAEX,IAAI,CAAC;EACxC,OAAOW,mBAAmB;AAC5B;AAEA,SAASK,mBAAmBA,CAC1BX,CAAS,EACTY,aAAyC,EACzCN,mBAAgD,EAChD;EACA,SAAS;;EACT,IAAQO,aAAa,GACnBD,aAAa,CADPC,aAAa;IAAEC,cAAc,GACnCF,aAAa,CADQE,cAAc;IAAEhB,cAAc,GACnDc,aAAa,CADwBd,cAAc;IAAEC,eAAA,GACrDa,aAAa,CADwCb,eAAA;EAEvD,IAAIe,cAAc,GAAGD,aAAa,KAAK,CAAC,EAAE;IACxC,OAAOf,cAAc;EACvB;EACA,IAAMiB,QAAQ,GAAG,CAACf,CAAC,GAAGa,aAAa,KAAKC,cAAc,GAAGD,aAAa,CAAC;EACvE,IAAMhB,GAAG,GAAGC,cAAc,GAAGiB,QAAQ,IAAIhB,eAAe,GAAGD,cAAc,CAAC;EAC1E,IAAMF,IAAI,GAAGG,eAAe,IAAID,cAAc,GAAG,CAAC,GAAG,CAAC,CAAC;EAEvD,IAAIF,IAAI,GAAGC,GAAG,GAAGD,IAAI,GAAGE,cAAc,EAAE;IACtC,OAAOJ,MAAM,CACXY,mBAAmB,CAACC,eAAe,EACnCX,IAAI,EACJC,GAAG,EACHC,cAAc,EACdC,eAAe,EACfC,CACF,CAAC;EACH,CAAC,MAAM,IAAIJ,IAAI,GAAGC,GAAG,GAAGD,IAAI,GAAGG,eAAe,EAAE;IAC9C,OAAOL,MAAM,CACXY,mBAAmB,CAACE,gBAAgB,EACpCZ,IAAI,EACJC,GAAG,EACHC,cAAc,EACdC,eAAe,EACfC,CACF,CAAC;EACH;EAEA,OAAOH,GAAG;AACZ;AAiBO,SAASN,WAAWA,CACzBS,CAAS,EACTgB,UAA6B,EAC7BC,WAA8B,EAC9BtB,IAAwB,EAChB;EACR,SAAS;;EACT,IAAIqB,UAAU,CAACE,MAAM,GAAG,CAAC,IAAID,WAAW,CAACC,MAAM,GAAG,CAAC,EAAE;IACnD,MAAM,IAAIT,uBAAe,CACvB,2EACF,CAAC;EACH;EAEA,IAAMH,mBAAmB,GAAGD,YAAY,CAACV,IAAI,CAAC;EAC9C,IAAMuB,MAAM,GAAGF,UAAU,CAACE,MAAM;EAChC,IAAMN,aAAyC,GAAG;IAChDC,aAAa,EAAEG,UAAU,CAAC,CAAC,CAAC;IAC5BF,cAAc,EAAEE,UAAU,CAAC,CAAC,CAAC;IAC7BlB,cAAc,EAAEmB,WAAW,CAAC,CAAC,CAAC;IAC9BlB,eAAe,EAAEkB,WAAW,CAAC,CAAC;EAChC,CAAC;EACD,IAAIC,MAAM,GAAG,CAAC,EAAE;IACd,IAAIlB,CAAC,GAAGgB,UAAU,CAACE,MAAM,GAAG,CAAC,CAAC,EAAE;MAC9BN,aAAa,CAACC,aAAa,GAAGG,UAAU,CAACE,MAAM,GAAG,CAAC,CAAC;MACpDN,aAAa,CAACE,cAAc,GAAGE,UAAU,CAACE,MAAM,GAAG,CAAC,CAAC;MACrDN,aAAa,CAACd,cAAc,GAAGmB,WAAW,CAACC,MAAM,GAAG,CAAC,CAAC;MACtDN,aAAa,CAACb,eAAe,GAAGkB,WAAW,CAACC,MAAM,GAAG,CAAC,CAAC;IACzD,CAAC,MAAM;MACL,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,MAAM,EAAE,EAAEC,CAAC,EAAE;QAC/B,IAAInB,CAAC,IAAIgB,UAAU,CAACG,CAAC,CAAC,EAAE;UACtBP,aAAa,CAACC,aAAa,GAAGG,UAAU,CAACG,CAAC,GAAG,CAAC,CAAC;UAC/CP,aAAa,CAACE,cAAc,GAAGE,UAAU,CAACG,CAAC,CAAC;UAC5CP,aAAa,CAACd,cAAc,GAAGmB,WAAW,CAACE,CAAC,GAAG,CAAC,CAAC;UACjDP,aAAa,CAACb,eAAe,GAAGkB,WAAW,CAACE,CAAC,CAAC;UAC9C;QACF;MACF;IACF;EACF;EAEA,OAAOR,mBAAmB,CAACX,CAAC,EAAEY,aAAa,EAAEN,mBAAmB,CAAC;AACnE;AAcO,SAAShB,KAAKA,CAACF,KAAa,EAAEgC,GAAW,EAAEC,GAAW,EAAE;EAC7D,SAAS;;EACT,OAAOC,IAAI,CAACF,GAAG,CAACE,IAAI,CAACD,GAAG,CAACjC,KAAK,EAAEgC,GAAG,CAAC,EAAEC,GAAG,CAAC;AAC5C", "ignoreList": []}