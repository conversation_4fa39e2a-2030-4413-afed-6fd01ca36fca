62c73bc7496682f9205f8e7009373c7b
"use strict";

/* istanbul ignore next */
function cov_1m7n0schb7() {
  var path = "/Users/<USER>/projects/react-native/payment-module/src/components/contact-item/types.ts";
  var hash = "ad4ab593a1b437b736bf5600fa268ec81cd5fa23";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/projects/react-native/payment-module/src/components/contact-item/types.ts",
    statementMap: {
      "0": {
        start: {
          line: 3,
          column: 0
        },
        end: {
          line: 5,
          column: 3
        }
      }
    },
    fnMap: {},
    branchMap: {},
    s: {
      "0": 0
    },
    f: {},
    b: {},
    inputSourceMap: {
      version: 3,
      file: undefined,
      names: [],
      sourceRoot: undefined,
      sources: ["/Users/<USER>/projects/react-native/payment-module/src/components/contact-item/types.ts"],
      sourcesContent: ["import {TextStyle, ViewStyle} from 'react-native';\nimport {MSBIcons} from 'msb-shared-component';\n\nimport {SafeAny} from '../../commons/Constants';\n\nexport type ContactItemProps = {\n  style?: ViewStyle;\n  styleName?: TextStyle;\n  styleBankName?: TextStyle;\n  styleAccountNo?: TextStyle;\n  onPress?: (tag?: string) => SafeAny;\n  title?: string;\n  icon?: MSBIcons;\n  name?: string;\n  bankName?: string;\n  isTopup?: boolean;\n  bankAlias?: string;\n  isNotShowBankName?: boolean;\n  searchText?: string;\n};\n"],
      mappings: "",
      ignoreList: []
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "ad4ab593a1b437b736bf5600fa268ec81cd5fa23"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_1m7n0schb7 = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_1m7n0schb7();
cov_1m7n0schb7().s[0]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6W10sInNvdXJjZXMiOlsiL1VzZXJzL3NvbnNhbXNldC9wcm9qZWN0cy9yZWFjdC1uYXRpdmUvcGF5bWVudC1tb2R1bGUvc3JjL2NvbXBvbmVudHMvY29udGFjdC1pdGVtL3R5cGVzLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7VGV4dFN0eWxlLCBWaWV3U3R5bGV9IGZyb20gJ3JlYWN0LW5hdGl2ZSc7XG5pbXBvcnQge01TQkljb25zfSBmcm9tICdtc2Itc2hhcmVkLWNvbXBvbmVudCc7XG5cbmltcG9ydCB7U2FmZUFueX0gZnJvbSAnLi4vLi4vY29tbW9ucy9Db25zdGFudHMnO1xuXG5leHBvcnQgdHlwZSBDb250YWN0SXRlbVByb3BzID0ge1xuICBzdHlsZT86IFZpZXdTdHlsZTtcbiAgc3R5bGVOYW1lPzogVGV4dFN0eWxlO1xuICBzdHlsZUJhbmtOYW1lPzogVGV4dFN0eWxlO1xuICBzdHlsZUFjY291bnRObz86IFRleHRTdHlsZTtcbiAgb25QcmVzcz86ICh0YWc/OiBzdHJpbmcpID0+IFNhZmVBbnk7XG4gIHRpdGxlPzogc3RyaW5nO1xuICBpY29uPzogTVNCSWNvbnM7XG4gIG5hbWU/OiBzdHJpbmc7XG4gIGJhbmtOYW1lPzogc3RyaW5nO1xuICBpc1RvcHVwPzogYm9vbGVhbjtcbiAgYmFua0FsaWFzPzogc3RyaW5nO1xuICBpc05vdFNob3dCYW5rTmFtZT86IGJvb2xlYW47XG4gIHNlYXJjaFRleHQ/OiBzdHJpbmc7XG59O1xuIl0sIm1hcHBpbmdzIjoiIiwiaWdub3JlTGlzdCI6W119