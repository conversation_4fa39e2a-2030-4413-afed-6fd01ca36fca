{"version": 3, "names": ["Object", "defineProperty", "exports", "value", "measure", "_PlatformChecker", "require", "_index", "measureFabric", "animatedRef", "_WORKLET", "viewTag", "logger", "warn", "measured", "global", "_measureFabric", "x", "isNaN", "measurePaper", "_measurePaper", "measureJest", "measureChromeDebugger", "measureDefault", "shouldBeUseWeb", "isF<PERSON><PERSON>", "isJest", "isChromeDebugger"], "sources": ["../../../src/platformFunctions/measure.ts"], "sourcesContent": [null], "mappings": "AAAA,YAAY;;AAAAA,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,KAAA;AAAA;AAAAD,OAAA,CAAAE,OAAA;AAEZ,IAAAC,gBAAA,GAAAC,OAAA;AAYA,IAAAC,MAAA,GAAAD,OAAA;AAiBO,IAAIF,OAAgB;AAE3B,SAASI,aAAaA,CAACC,WAA8C,EAAE;EACrE,SAAS;;EACT,IAAI,CAACC,QAAQ,EAAE;IACb,OAAO,IAAI;EACb;EAEA,IAAMC,OAAO,GAAGF,WAAW,CAAC,CAAC;EAC7B,IAAIE,OAAO,KAAK,CAAC,CAAC,EAAE;IAClBC,aAAM,CAACC,IAAI,CACT,qBAAqBF,OAAO,4JAC9B,CAAC;IACD,OAAO,IAAI;EACb;EAEA,IAAMG,QAAQ,GAAGC,MAAM,CAACC,cAAc,CAAEL,OAA4B,CAAC;EACrE,IAAIG,QAAQ,KAAK,IAAI,EAAE;IACrBF,aAAM,CAACC,IAAI,CACT,kNACF,CAAC;IACD,OAAO,IAAI;EACb,CAAC,MAAM,IAAIC,QAAQ,CAACG,CAAC,KAAK,CAAC,OAAO,EAAE;IAClCL,aAAM,CAACC,IAAI,CACT,qGACF,CAAC;IACD,OAAO,IAAI;EACb,CAAC,MAAM,IAAIK,KAAK,CAACJ,QAAQ,CAACG,CAAC,CAAC,EAAE;IAC5BL,aAAM,CAACC,IAAI,CACT,qHACF,CAAC;IACD,OAAO,IAAI;EACb,CAAC,MAAM;IACL,OAAOC,QAAQ;EACjB;AACF;AAEA,SAASK,YAAYA,CAACV,WAA8C,EAAE;EACpE,SAAS;;EACT,IAAI,CAACC,QAAQ,EAAE;IACb,OAAO,IAAI;EACb;EAEA,IAAMC,OAAO,GAAGF,WAAW,CAAC,CAAC;EAC7B,IAAIE,OAAO,KAAK,CAAC,CAAC,EAAE;IAClBC,aAAM,CAACC,IAAI,CACT,qBAAqBF,OAAO,4JAC9B,CAAC;IACD,OAAO,IAAI;EACb;EAEA,IAAMG,QAAQ,GAAGC,MAAM,CAACK,aAAa,CAAET,OAAiB,CAAC;EACzD,IAAIG,QAAQ,KAAK,IAAI,EAAE;IACrBF,aAAM,CAACC,IAAI,CACT,qBACEF,OAAO,0MAEX,CAAC;IACD,OAAO,IAAI;EACb,CAAC,MAAM,IAAIG,QAAQ,CAACG,CAAC,KAAK,CAAC,OAAO,EAAE;IAClCL,aAAM,CAACC,IAAI,CACT,qBACEF,OAAO,6FAEX,CAAC;IACD,OAAO,IAAI;EACb,CAAC,MAAM,IAAIO,KAAK,CAACJ,QAAQ,CAACG,CAAC,CAAC,EAAE;IAC5BL,aAAM,CAACC,IAAI,CACT,qBACEF,OAAO,6GAEX,CAAC;IACD,OAAO,IAAI;EACb,CAAC,MAAM;IACL,OAAOG,QAAQ;EACjB;AACF;AAEA,SAASO,WAAWA,CAAA,EAAG;EACrBT,aAAM,CAACC,IAAI,CAAC,qCAAqC,CAAC;EAClD,OAAO,IAAI;AACb;AAEA,SAASS,qBAAqBA,CAAA,EAAG;EAC/BV,aAAM,CAACC,IAAI,CAAC,gDAAgD,CAAC;EAC7D,OAAO,IAAI;AACb;AAEA,SAASU,cAAcA,CAAA,EAAG;EACxBX,aAAM,CAACC,IAAI,CAAC,mDAAmD,CAAC;EAChE,OAAO,IAAI;AACb;AAEA,IAAI,CAAC,IAAAW,+BAAc,EAAC,CAAC,EAAE;EAIrB,IAAI,IAAAC,yBAAQ,EAAC,CAAC,EAAE;IACdvB,OAAA,CAAAE,OAAA,GAAAA,OAAO,GAAGI,aAAmC;EAC/C,CAAC,MAAM;IACLN,OAAA,CAAAE,OAAA,GAAAA,OAAO,GAAGe,YAAkC;EAC9C;AACF,CAAC,MAAM,IAAI,IAAAO,uBAAM,EAAC,CAAC,EAAE;EACnBxB,OAAA,CAAAE,OAAA,GAAAA,OAAO,GAAGiB,WAAW;AACvB,CAAC,MAAM,IAAI,IAAAM,iCAAgB,EAAC,CAAC,EAAE;EAC7BzB,OAAA,CAAAE,OAAA,GAAAA,OAAO,GAAGkB,qBAAqB;AACjC,CAAC,MAAM;EACLpB,OAAA,CAAAE,OAAA,GAAAA,OAAO,GAAGmB,cAAc;AAC1B", "ignoreList": []}