{"version": 3, "names": [], "sources": ["/Users/<USER>/projects/react-native/payment-module/src/components/contact-item/types.ts"], "sourcesContent": ["import {TextStyle, ViewStyle} from 'react-native';\nimport {MSBIcons} from 'msb-shared-component';\n\nimport {SafeAny} from '../../commons/Constants';\n\nexport type ContactItemProps = {\n  style?: ViewStyle;\n  styleName?: TextStyle;\n  styleBankName?: TextStyle;\n  styleAccountNo?: TextStyle;\n  onPress?: (tag?: string) => SafeAny;\n  title?: string;\n  icon?: MSBIcons;\n  name?: string;\n  bankName?: string;\n  isTopup?: boolean;\n  bankAlias?: string;\n  isNotShowBankName?: boolean;\n  searchText?: string;\n};\n"], "mappings": "", "ignoreList": []}