721c1e6319efe3d8ede39fab7d3bbf7d
"use strict";

/* istanbul ignore next */
function cov_fxod3nvp2() {
  var path = "/Users/<USER>/projects/react-native/payment-module/src/domain/usecases/bill-pay/GetBillDetailUseCase.ts";
  var hash = "e0d72dccb0699cc0c71daed4445674ecb12a67ca";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/projects/react-native/payment-module/src/domain/usecases/bill-pay/GetBillDetailUseCase.ts",
    statementMap: {
      "0": {
        start: {
          line: 3,
          column: 29
        },
        end: {
          line: 3,
          column: 84
        }
      },
      "1": {
        start: {
          line: 4,
          column: 25
        },
        end: {
          line: 4,
          column: 99
        }
      },
      "2": {
        start: {
          line: 5,
          column: 23
        },
        end: {
          line: 5,
          column: 95
        }
      },
      "3": {
        start: {
          line: 6,
          column: 20
        },
        end: {
          line: 6,
          column: 89
        }
      },
      "4": {
        start: {
          line: 7,
          column: 0
        },
        end: {
          line: 9,
          column: 3
        }
      },
      "5": {
        start: {
          line: 10,
          column: 0
        },
        end: {
          line: 10,
          column: 38
        }
      },
      "6": {
        start: {
          line: 11,
          column: 26
        },
        end: {
          line: 11,
          column: 69
        }
      },
      "7": {
        start: {
          line: 12,
          column: 27
        },
        end: {
          line: 32,
          column: 3
        }
      },
      "8": {
        start: {
          line: 14,
          column: 4
        },
        end: {
          line: 14,
          column: 62
        }
      },
      "9": {
        start: {
          line: 15,
          column: 4
        },
        end: {
          line: 15,
          column: 33
        }
      },
      "10": {
        start: {
          line: 17,
          column: 2
        },
        end: {
          line: 31,
          column: 6
        }
      },
      "11": {
        start: {
          line: 20,
          column: 21
        },
        end: {
          line: 25,
          column: 8
        }
      },
      "12": {
        start: {
          line: 21,
          column: 20
        },
        end: {
          line: 21,
          column: 24
        }
      },
      "13": {
        start: {
          line: 22,
          column: 8
        },
        end: {
          line: 24,
          column: 17
        }
      },
      "14": {
        start: {
          line: 23,
          column: 10
        },
        end: {
          line: 23,
          column: 57
        }
      },
      "15": {
        start: {
          line: 27,
          column: 8
        },
        end: {
          line: 27,
          column: 47
        }
      },
      "16": {
        start: {
          line: 29,
          column: 6
        },
        end: {
          line: 29,
          column: 21
        }
      },
      "17": {
        start: {
          line: 33,
          column: 0
        },
        end: {
          line: 33,
          column: 52
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 12,
            column: 27
          },
          end: {
            line: 12,
            column: 28
          }
        },
        loc: {
          start: {
            line: 12,
            column: 39
          },
          end: {
            line: 32,
            column: 1
          }
        },
        line: 12
      },
      "1": {
        name: "GetBillDetailUseCase",
        decl: {
          start: {
            line: 13,
            column: 11
          },
          end: {
            line: 13,
            column: 31
          }
        },
        loc: {
          start: {
            line: 13,
            column: 44
          },
          end: {
            line: 16,
            column: 3
          }
        },
        line: 13
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 19,
            column: 11
          },
          end: {
            line: 19,
            column: 12
          }
        },
        loc: {
          start: {
            line: 19,
            column: 23
          },
          end: {
            line: 30,
            column: 5
          }
        },
        line: 19
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 20,
            column: 53
          },
          end: {
            line: 20,
            column: 54
          }
        },
        loc: {
          start: {
            line: 20,
            column: 73
          },
          end: {
            line: 25,
            column: 7
          }
        },
        line: 20
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 22,
            column: 60
          },
          end: {
            line: 22,
            column: 61
          }
        },
        loc: {
          start: {
            line: 22,
            column: 72
          },
          end: {
            line: 24,
            column: 9
          }
        },
        line: 22
      },
      "5": {
        name: "execute",
        decl: {
          start: {
            line: 26,
            column: 15
          },
          end: {
            line: 26,
            column: 22
          }
        },
        loc: {
          start: {
            line: 26,
            column: 27
          },
          end: {
            line: 28,
            column: 7
          }
        },
        line: 26
      }
    },
    branchMap: {},
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0
    },
    b: {},
    inputSourceMap: {
      version: 3,
      file: undefined,
      names: ["ExcecutionHandler_1", "require", "GetBillDetailUseCase", "repository", "_classCallCheck2", "default", "_createClass2", "key", "value", "_execute", "_asyncToGenerator2", "request", "_this", "ExecutionHandler", "execute", "getBillDetail", "_x", "apply", "arguments", "exports"],
      sourceRoot: undefined,
      sources: ["/Users/<USER>/projects/react-native/payment-module/src/domain/usecases/bill-pay/GetBillDetailUseCase.ts"],
      sourcesContent: ["import {IBillPayRepository} from '../../repositories/IBillPayRepository';\nimport {GetBillDetailModel} from '../../entities/get-bill-detail/GetBillDetailModel';\nimport {ResultState} from '../../../core/ResultState';\nimport {ExecutionHandler} from '../../../utils/ExcecutionHandler';\nimport {GetBillDetailRequest} from '../../../data/models/get-bill-detail/GetBillDetailRequest';\nexport class GetBillDetailUseCase {\n  private repository: IBillPayRepository;\n\n  constructor(repository: IBillPayRepository) {\n    this.repository = repository;\n  }\n\n  public async execute(request: GetBillDetailRequest): Promise<ResultState<GetBillDetailModel>> {\n    return ExecutionHandler.execute(() => this.repository.getBillDetail(request), true);\n  }\n}\n"],
      mappings: ";;;;;;;;;;AAGA,IAAAA,mBAAA,GAAAC,OAAA;AAAkE,IAErDC,oBAAoB;EAG/B,SAAAA,qBAAYC,UAA8B;IAAA,IAAAC,gBAAA,CAAAC,OAAA,QAAAH,oBAAA;IACxC,IAAI,CAACC,UAAU,GAAGA,UAAU;EAC9B;EAAC,WAAAG,aAAA,CAAAD,OAAA,EAAAH,oBAAA;IAAAK,GAAA;IAAAC,KAAA;MAAA,IAAAC,QAAA,OAAAC,kBAAA,CAAAL,OAAA,EAEM,WAAcM,OAA6B;QAAA,IAAAC,KAAA;QAChD,OAAOZ,mBAAA,CAAAa,gBAAgB,CAACC,OAAO,CAAC;UAAA,OAAMF,KAAI,CAACT,UAAU,CAACY,aAAa,CAACJ,OAAO,CAAC;QAAA,GAAE,IAAI,CAAC;MACrF,CAAC;MAAA,SAFYG,OAAOA,CAAAE,EAAA;QAAA,OAAAP,QAAA,CAAAQ,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAPJ,OAAO;IAAA;EAAA;AAAA;AAPtBK,OAAA,CAAAjB,oBAAA,GAAAA,oBAAA",
      ignoreList: []
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "e0d72dccb0699cc0c71daed4445674ecb12a67ca"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_fxod3nvp2 = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_fxod3nvp2();
var _interopRequireDefault =
/* istanbul ignore next */
(cov_fxod3nvp2().s[0]++, require("@babel/runtime/helpers/interopRequireDefault"));
var _asyncToGenerator2 =
/* istanbul ignore next */
(cov_fxod3nvp2().s[1]++, _interopRequireDefault(require("@babel/runtime/helpers/asyncToGenerator")));
var _classCallCheck2 =
/* istanbul ignore next */
(cov_fxod3nvp2().s[2]++, _interopRequireDefault(require("@babel/runtime/helpers/classCallCheck")));
var _createClass2 =
/* istanbul ignore next */
(cov_fxod3nvp2().s[3]++, _interopRequireDefault(require("@babel/runtime/helpers/createClass")));
/* istanbul ignore next */
cov_fxod3nvp2().s[4]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_fxod3nvp2().s[5]++;
exports.GetBillDetailUseCase = void 0;
var ExcecutionHandler_1 =
/* istanbul ignore next */
(cov_fxod3nvp2().s[6]++, require("../../../utils/ExcecutionHandler"));
var GetBillDetailUseCase =
/* istanbul ignore next */
(cov_fxod3nvp2().s[7]++, function () {
  /* istanbul ignore next */
  cov_fxod3nvp2().f[0]++;
  function GetBillDetailUseCase(repository) {
    /* istanbul ignore next */
    cov_fxod3nvp2().f[1]++;
    cov_fxod3nvp2().s[8]++;
    (0, _classCallCheck2.default)(this, GetBillDetailUseCase);
    /* istanbul ignore next */
    cov_fxod3nvp2().s[9]++;
    this.repository = repository;
  }
  /* istanbul ignore next */
  cov_fxod3nvp2().s[10]++;
  return (0, _createClass2.default)(GetBillDetailUseCase, [{
    key: "execute",
    value: function () {
      /* istanbul ignore next */
      cov_fxod3nvp2().f[2]++;
      var _execute =
      /* istanbul ignore next */
      (cov_fxod3nvp2().s[11]++, (0, _asyncToGenerator2.default)(function* (request) {
        /* istanbul ignore next */
        cov_fxod3nvp2().f[3]++;
        var _this =
        /* istanbul ignore next */
        (cov_fxod3nvp2().s[12]++, this);
        /* istanbul ignore next */
        cov_fxod3nvp2().s[13]++;
        return ExcecutionHandler_1.ExecutionHandler.execute(function () {
          /* istanbul ignore next */
          cov_fxod3nvp2().f[4]++;
          cov_fxod3nvp2().s[14]++;
          return _this.repository.getBillDetail(request);
        }, true);
      }));
      function execute(_x) {
        /* istanbul ignore next */
        cov_fxod3nvp2().f[5]++;
        cov_fxod3nvp2().s[15]++;
        return _execute.apply(this, arguments);
      }
      /* istanbul ignore next */
      cov_fxod3nvp2().s[16]++;
      return execute;
    }()
  }]);
}());
/* istanbul ignore next */
cov_fxod3nvp2().s[17]++;
exports.GetBillDetailUseCase = GetBillDetailUseCase;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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