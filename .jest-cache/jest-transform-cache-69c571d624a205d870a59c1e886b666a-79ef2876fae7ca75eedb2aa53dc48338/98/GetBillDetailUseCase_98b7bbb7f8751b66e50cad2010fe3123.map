{"version": 3, "names": ["cov_fxod3nvp2", "actualCoverage", "ExcecutionHandler_1", "s", "require", "GetBillDetailUseCase", "f", "repository", "_classCallCheck2", "default", "_createClass2", "key", "value", "_execute", "_asyncToGenerator2", "request", "_this", "ExecutionHandler", "execute", "getBillDetail", "_x", "apply", "arguments", "exports"], "sources": ["/Users/<USER>/projects/react-native/payment-module/src/domain/usecases/bill-pay/GetBillDetailUseCase.ts"], "sourcesContent": ["import {IBillPayRepository} from '../../repositories/IBillPayRepository';\nimport {GetBillDetailModel} from '../../entities/get-bill-detail/GetBillDetailModel';\nimport {ResultState} from '../../../core/ResultState';\nimport {ExecutionHandler} from '../../../utils/ExcecutionHandler';\nimport {GetBillDetailRequest} from '../../../data/models/get-bill-detail/GetBillDetailRequest';\nexport class GetBillDetailUseCase {\n  private repository: IBillPayRepository;\n\n  constructor(repository: IBillPayRepository) {\n    this.repository = repository;\n  }\n\n  public async execute(request: GetBillDetailRequest): Promise<ResultState<GetBillDetailModel>> {\n    return ExecutionHandler.execute(() => this.repository.getBillDetail(request), true);\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAUE;IAAAA,aAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,aAAA;;;;;;;;;;;;;;;;;;;;;AAPF,IAAAE,mBAAA;AAAA;AAAA,CAAAF,aAAA,GAAAG,CAAA,OAAAC,OAAA;AAAkE,IAErDC,oBAAoB;AAAA;AAAA,CAAAL,aAAA,GAAAG,CAAA;EAAA;EAAAH,aAAA,GAAAM,CAAA;EAG/B,SAAAD,qBAAYE,UAA8B;IAAA;IAAAP,aAAA,GAAAM,CAAA;IAAAN,aAAA,GAAAG,CAAA;IAAA,IAAAK,gBAAA,CAAAC,OAAA,QAAAJ,oBAAA;IAAA;IAAAL,aAAA,GAAAG,CAAA;IACxC,IAAI,CAACI,UAAU,GAAGA,UAAU;EAC9B;EAAA;EAAAP,aAAA,GAAAG,CAAA;EAAC,WAAAO,aAAA,CAAAD,OAAA,EAAAJ,oBAAA;IAAAM,GAAA;IAAAC,KAAA;MAAA;MAAAZ,aAAA,GAAAM,CAAA;MAAA,IAAAO,QAAA;MAAA;MAAA,CAAAb,aAAA,GAAAG,CAAA,YAAAW,kBAAA,CAAAL,OAAA,EAEM,WAAcM,OAA6B;QAAA;QAAAf,aAAA,GAAAM,CAAA;QAAA,IAAAU,KAAA;QAAA;QAAA,CAAAhB,aAAA,GAAAG,CAAA;QAAA;QAAAH,aAAA,GAAAG,CAAA;QAChD,OAAOD,mBAAA,CAAAe,gBAAgB,CAACC,OAAO,CAAC;UAAA;UAAAlB,aAAA,GAAAM,CAAA;UAAAN,aAAA,GAAAG,CAAA;UAAA,OAAMa,KAAI,CAACT,UAAU,CAACY,aAAa,CAACJ,OAAO,CAAC;QAAA,GAAE,IAAI,CAAC;MACrF,CAAC;MAAA,SAFYG,OAAOA,CAAAE,EAAA;QAAA;QAAApB,aAAA,GAAAM,CAAA;QAAAN,aAAA,GAAAG,CAAA;QAAA,OAAAU,QAAA,CAAAQ,KAAA,OAAAC,SAAA;MAAA;MAAA;MAAAtB,aAAA,GAAAG,CAAA;MAAA,OAAPe,OAAO;IAAA;EAAA;AAAA;AAAA;AAAAlB,aAAA,GAAAG,CAAA;AAPtBoB,OAAA,CAAAlB,oBAAA,GAAAA,oBAAA", "ignoreList": []}