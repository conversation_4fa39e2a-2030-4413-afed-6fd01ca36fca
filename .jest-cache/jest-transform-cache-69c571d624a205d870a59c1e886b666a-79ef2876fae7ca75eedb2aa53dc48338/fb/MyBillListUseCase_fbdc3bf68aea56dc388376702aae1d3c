e89174030270606949a53d6c062dc683
"use strict";

/* istanbul ignore next */
function cov_1t9rdizv5j() {
  var path = "/Users/<USER>/projects/react-native/payment-module/src/domain/usecases/bill-pay/MyBillListUseCase.ts";
  var hash = "04bc8d39e232153c8414350ec25e919509a568e0";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/projects/react-native/payment-module/src/domain/usecases/bill-pay/MyBillListUseCase.ts",
    statementMap: {
      "0": {
        start: {
          line: 3,
          column: 29
        },
        end: {
          line: 3,
          column: 84
        }
      },
      "1": {
        start: {
          line: 4,
          column: 25
        },
        end: {
          line: 4,
          column: 99
        }
      },
      "2": {
        start: {
          line: 5,
          column: 23
        },
        end: {
          line: 5,
          column: 95
        }
      },
      "3": {
        start: {
          line: 6,
          column: 20
        },
        end: {
          line: 6,
          column: 89
        }
      },
      "4": {
        start: {
          line: 7,
          column: 0
        },
        end: {
          line: 9,
          column: 3
        }
      },
      "5": {
        start: {
          line: 10,
          column: 0
        },
        end: {
          line: 10,
          column: 35
        }
      },
      "6": {
        start: {
          line: 11,
          column: 26
        },
        end: {
          line: 11,
          column: 69
        }
      },
      "7": {
        start: {
          line: 12,
          column: 24
        },
        end: {
          line: 32,
          column: 3
        }
      },
      "8": {
        start: {
          line: 14,
          column: 4
        },
        end: {
          line: 14,
          column: 59
        }
      },
      "9": {
        start: {
          line: 15,
          column: 4
        },
        end: {
          line: 15,
          column: 33
        }
      },
      "10": {
        start: {
          line: 17,
          column: 2
        },
        end: {
          line: 31,
          column: 6
        }
      },
      "11": {
        start: {
          line: 20,
          column: 21
        },
        end: {
          line: 25,
          column: 8
        }
      },
      "12": {
        start: {
          line: 21,
          column: 20
        },
        end: {
          line: 21,
          column: 24
        }
      },
      "13": {
        start: {
          line: 22,
          column: 8
        },
        end: {
          line: 24,
          column: 11
        }
      },
      "14": {
        start: {
          line: 23,
          column: 10
        },
        end: {
          line: 23,
          column: 54
        }
      },
      "15": {
        start: {
          line: 27,
          column: 8
        },
        end: {
          line: 27,
          column: 47
        }
      },
      "16": {
        start: {
          line: 29,
          column: 6
        },
        end: {
          line: 29,
          column: 21
        }
      },
      "17": {
        start: {
          line: 33,
          column: 0
        },
        end: {
          line: 33,
          column: 46
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 12,
            column: 24
          },
          end: {
            line: 12,
            column: 25
          }
        },
        loc: {
          start: {
            line: 12,
            column: 36
          },
          end: {
            line: 32,
            column: 1
          }
        },
        line: 12
      },
      "1": {
        name: "MyBillListUseCase",
        decl: {
          start: {
            line: 13,
            column: 11
          },
          end: {
            line: 13,
            column: 28
          }
        },
        loc: {
          start: {
            line: 13,
            column: 41
          },
          end: {
            line: 16,
            column: 3
          }
        },
        line: 13
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 19,
            column: 11
          },
          end: {
            line: 19,
            column: 12
          }
        },
        loc: {
          start: {
            line: 19,
            column: 23
          },
          end: {
            line: 30,
            column: 5
          }
        },
        line: 19
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 20,
            column: 53
          },
          end: {
            line: 20,
            column: 54
          }
        },
        loc: {
          start: {
            line: 20,
            column: 73
          },
          end: {
            line: 25,
            column: 7
          }
        },
        line: 20
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 22,
            column: 60
          },
          end: {
            line: 22,
            column: 61
          }
        },
        loc: {
          start: {
            line: 22,
            column: 72
          },
          end: {
            line: 24,
            column: 9
          }
        },
        line: 22
      },
      "5": {
        name: "execute",
        decl: {
          start: {
            line: 26,
            column: 15
          },
          end: {
            line: 26,
            column: 22
          }
        },
        loc: {
          start: {
            line: 26,
            column: 27
          },
          end: {
            line: 28,
            column: 7
          }
        },
        line: 26
      }
    },
    branchMap: {},
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0
    },
    b: {},
    inputSourceMap: {
      version: 3,
      file: undefined,
      names: ["ExcecutionHandler_1", "require", "MyBillListUseCase", "repository", "_classCallCheck2", "default", "_createClass2", "key", "value", "_execute", "_asyncToGenerator2", "request", "_this", "ExecutionHandler", "execute", "myBillList", "_x", "apply", "arguments", "exports"],
      sourceRoot: undefined,
      sources: ["/Users/<USER>/projects/react-native/payment-module/src/domain/usecases/bill-pay/MyBillListUseCase.ts"],
      sourcesContent: ["import {IBillPayRepository} from '../../repositories/IBillPayRepository';\nimport {ResultState} from '../../../core/ResultState';\nimport {ExecutionHandler} from '../../../utils/ExcecutionHandler';\nimport {MyBillListRequest} from '../../../data/models/my-bill-list/MyBillListRequest';\nimport {MyBillContactListModel} from '../../entities/my-bill-contact-list/MyBillContactListModel';\nexport class MyBillListUseCase {\n  private repository: IBillPayRepository;\n\n  constructor(repository: IBillPayRepository) {\n    this.repository = repository;\n  }\n\n  public async execute(request: MyBillListRequest): Promise<ResultState<MyBillContactListModel>> {\n    // call this.repository.myBillList(...)\n    return ExecutionHandler.execute(() => this.repository.myBillList(request));\n  }\n}\n"],
      mappings: ";;;;;;;;;;AAEA,IAAAA,mBAAA,GAAAC,OAAA;AAAkE,IAGrDC,iBAAiB;EAG5B,SAAAA,kBAAYC,UAA8B;IAAA,IAAAC,gBAAA,CAAAC,OAAA,QAAAH,iBAAA;IACxC,IAAI,CAACC,UAAU,GAAGA,UAAU;EAC9B;EAAC,WAAAG,aAAA,CAAAD,OAAA,EAAAH,iBAAA;IAAAK,GAAA;IAAAC,KAAA;MAAA,IAAAC,QAAA,OAAAC,kBAAA,CAAAL,OAAA,EAEM,WAAcM,OAA0B;QAAA,IAAAC,KAAA;QAE7C,OAAOZ,mBAAA,CAAAa,gBAAgB,CAACC,OAAO,CAAC;UAAA,OAAMF,KAAI,CAACT,UAAU,CAACY,UAAU,CAACJ,OAAO,CAAC;QAAA,EAAC;MAC5E,CAAC;MAAA,SAHYG,OAAOA,CAAAE,EAAA;QAAA,OAAAP,QAAA,CAAAQ,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAPJ,OAAO;IAAA;EAAA;AAAA;AAPtBK,OAAA,CAAAjB,iBAAA,GAAAA,iBAAA",
      ignoreList: []
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "04bc8d39e232153c8414350ec25e919509a568e0"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_1t9rdizv5j = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_1t9rdizv5j();
var _interopRequireDefault =
/* istanbul ignore next */
(cov_1t9rdizv5j().s[0]++, require("@babel/runtime/helpers/interopRequireDefault"));
var _asyncToGenerator2 =
/* istanbul ignore next */
(cov_1t9rdizv5j().s[1]++, _interopRequireDefault(require("@babel/runtime/helpers/asyncToGenerator")));
var _classCallCheck2 =
/* istanbul ignore next */
(cov_1t9rdizv5j().s[2]++, _interopRequireDefault(require("@babel/runtime/helpers/classCallCheck")));
var _createClass2 =
/* istanbul ignore next */
(cov_1t9rdizv5j().s[3]++, _interopRequireDefault(require("@babel/runtime/helpers/createClass")));
/* istanbul ignore next */
cov_1t9rdizv5j().s[4]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_1t9rdizv5j().s[5]++;
exports.MyBillListUseCase = void 0;
var ExcecutionHandler_1 =
/* istanbul ignore next */
(cov_1t9rdizv5j().s[6]++, require("../../../utils/ExcecutionHandler"));
var MyBillListUseCase =
/* istanbul ignore next */
(cov_1t9rdizv5j().s[7]++, function () {
  /* istanbul ignore next */
  cov_1t9rdizv5j().f[0]++;
  function MyBillListUseCase(repository) {
    /* istanbul ignore next */
    cov_1t9rdizv5j().f[1]++;
    cov_1t9rdizv5j().s[8]++;
    (0, _classCallCheck2.default)(this, MyBillListUseCase);
    /* istanbul ignore next */
    cov_1t9rdizv5j().s[9]++;
    this.repository = repository;
  }
  /* istanbul ignore next */
  cov_1t9rdizv5j().s[10]++;
  return (0, _createClass2.default)(MyBillListUseCase, [{
    key: "execute",
    value: function () {
      /* istanbul ignore next */
      cov_1t9rdizv5j().f[2]++;
      var _execute =
      /* istanbul ignore next */
      (cov_1t9rdizv5j().s[11]++, (0, _asyncToGenerator2.default)(function* (request) {
        /* istanbul ignore next */
        cov_1t9rdizv5j().f[3]++;
        var _this =
        /* istanbul ignore next */
        (cov_1t9rdizv5j().s[12]++, this);
        /* istanbul ignore next */
        cov_1t9rdizv5j().s[13]++;
        return ExcecutionHandler_1.ExecutionHandler.execute(function () {
          /* istanbul ignore next */
          cov_1t9rdizv5j().f[4]++;
          cov_1t9rdizv5j().s[14]++;
          return _this.repository.myBillList(request);
        });
      }));
      function execute(_x) {
        /* istanbul ignore next */
        cov_1t9rdizv5j().f[5]++;
        cov_1t9rdizv5j().s[15]++;
        return _execute.apply(this, arguments);
      }
      /* istanbul ignore next */
      cov_1t9rdizv5j().s[16]++;
      return execute;
    }()
  }]);
}());
/* istanbul ignore next */
cov_1t9rdizv5j().s[17]++;
exports.MyBillListUseCase = MyBillListUseCase;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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