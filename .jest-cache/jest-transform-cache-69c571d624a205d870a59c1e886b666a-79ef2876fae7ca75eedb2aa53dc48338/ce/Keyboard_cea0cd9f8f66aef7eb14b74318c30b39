bb827fa7df911b8a75ef94de2661ff39
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
var _classCallCheck2 = _interopRequireDefault(require("@babel/runtime/helpers/classCallCheck"));
var _createClass2 = _interopRequireDefault(require("@babel/runtime/helpers/createClass"));
var _NativeEventEmitter = _interopRequireDefault(require("../../EventEmitter/NativeEventEmitter"));
var _LayoutAnimation = _interopRequireDefault(require("../../LayoutAnimation/LayoutAnimation"));
var _dismissKeyboard = _interopRequireDefault(require("../../Utilities/dismissKeyboard"));
var _Platform = _interopRequireDefault(require("../../Utilities/Platform"));
var _NativeKeyboardObserver = _interopRequireDefault(require("./NativeKeyboardObserver"));
var Keyboard = function () {
  function Keyboard() {
    var _this = this;
    (0, _classCallCheck2.default)(this, Keyboard);
    this._emitter = new _NativeEventEmitter.default(_Platform.default.OS !== 'ios' ? null : _NativeKeyboardObserver.default);
    this.addListener('keyboardDidShow', function (ev) {
      _this._currentlyShowing = ev;
    });
    this.addListener('keyboardDidHide', function (_ev) {
      _this._currentlyShowing = null;
    });
  }
  return (0, _createClass2.default)(Keyboard, [{
    key: "addListener",
    value: function addListener(eventType, listener, context) {
      return this._emitter.addListener(eventType, listener);
    }
  }, {
    key: "removeAllListeners",
    value: function removeAllListeners(eventType) {
      this._emitter.removeAllListeners(eventType);
    }
  }, {
    key: "dismiss",
    value: function dismiss() {
      (0, _dismissKeyboard.default)();
    }
  }, {
    key: "isVisible",
    value: function isVisible() {
      return !!this._currentlyShowing;
    }
  }, {
    key: "metrics",
    value: function metrics() {
      var _this$_currentlyShowi;
      return (_this$_currentlyShowi = this._currentlyShowing) == null ? void 0 : _this$_currentlyShowi.endCoordinates;
    }
  }, {
    key: "scheduleLayoutAnimation",
    value: function scheduleLayoutAnimation(event) {
      var duration = event.duration,
        easing = event.easing;
      if (duration != null && duration !== 0) {
        _LayoutAnimation.default.configureNext({
          duration: duration,
          update: {
            duration: duration,
            type: easing != null && _LayoutAnimation.default.Types[easing] || 'keyboard'
          }
        });
      }
    }
  }]);
}();
module.exports = new Keyboard();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJfTmF0aXZlRXZlbnRFbWl0dGVyIiwiX2ludGVyb3BSZXF1aXJlRGVmYXVsdCIsInJlcXVpcmUiLCJfTGF5b3V0QW5pbWF0aW9uIiwiX2Rpc21pc3NLZXlib2FyZCIsIl9QbGF0Zm9ybSIsIl9OYXRpdmVLZXlib2FyZE9ic2VydmVyIiwiS2V5Ym9hcmQiLCJfdGhpcyIsIl9jbGFzc0NhbGxDaGVjazIiLCJkZWZhdWx0IiwiX2VtaXR0ZXIiLCJOYXRpdmVFdmVudEVtaXR0ZXIiLCJQbGF0Zm9ybSIsIk9TIiwiTmF0aXZlS2V5Ym9hcmRPYnNlcnZlciIsImFkZExpc3RlbmVyIiwiZXYiLCJfY3VycmVudGx5U2hvd2luZyIsIl9ldiIsIl9jcmVhdGVDbGFzczIiLCJrZXkiLCJ2YWx1ZSIsImV2ZW50VHlwZSIsImxpc3RlbmVyIiwiY29udGV4dCIsInJlbW92ZUFsbExpc3RlbmVycyIsImRpc21pc3MiLCJkaXNtaXNzS2V5Ym9hcmQiLCJpc1Zpc2libGUiLCJtZXRyaWNzIiwiX3RoaXMkX2N1cnJlbnRseVNob3dpIiwiZW5kQ29vcmRpbmF0ZXMiLCJzY2hlZHVsZUxheW91dEFuaW1hdGlvbiIsImV2ZW50IiwiZHVyYXRpb24iLCJlYXNpbmciLCJMYXlvdXRBbmltYXRpb24iLCJjb25maWd1cmVOZXh0IiwidXBkYXRlIiwidHlwZSIsIlR5cGVzIiwibW9kdWxlIiwiZXhwb3J0cyJdLCJzb3VyY2VzIjpbIktleWJvYXJkLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQ29weXJpZ2h0IChjKSBNZXRhIFBsYXRmb3JtcywgSW5jLiBhbmQgYWZmaWxpYXRlcy5cbiAqXG4gKiBUaGlzIHNvdXJjZSBjb2RlIGlzIGxpY2Vuc2VkIHVuZGVyIHRoZSBNSVQgbGljZW5zZSBmb3VuZCBpbiB0aGVcbiAqIExJQ0VOU0UgZmlsZSBpbiB0aGUgcm9vdCBkaXJlY3Rvcnkgb2YgdGhpcyBzb3VyY2UgdHJlZS5cbiAqXG4gKiBAZm9ybWF0XG4gKiBAZmxvdyBzdHJpY3QtbG9jYWxcbiAqL1xuXG5pbXBvcnQgdHlwZSB7RXZlbnRTdWJzY3JpcHRpb259IGZyb20gJy4uLy4uL3ZlbmRvci9lbWl0dGVyL0V2ZW50RW1pdHRlcic7XG5cbmltcG9ydCBOYXRpdmVFdmVudEVtaXR0ZXIgZnJvbSAnLi4vLi4vRXZlbnRFbWl0dGVyL05hdGl2ZUV2ZW50RW1pdHRlcic7XG5pbXBvcnQgTGF5b3V0QW5pbWF0aW9uIGZyb20gJy4uLy4uL0xheW91dEFuaW1hdGlvbi9MYXlvdXRBbmltYXRpb24nO1xuaW1wb3J0IGRpc21pc3NLZXlib2FyZCBmcm9tICcuLi8uLi9VdGlsaXRpZXMvZGlzbWlzc0tleWJvYXJkJztcbmltcG9ydCBQbGF0Zm9ybSBmcm9tICcuLi8uLi9VdGlsaXRpZXMvUGxhdGZvcm0nO1xuaW1wb3J0IE5hdGl2ZUtleWJvYXJkT2JzZXJ2ZXIgZnJvbSAnLi9OYXRpdmVLZXlib2FyZE9ic2VydmVyJztcblxuZXhwb3J0IHR5cGUgS2V5Ym9hcmRFdmVudE5hbWUgPSAkS2V5czxLZXlib2FyZEV2ZW50RGVmaW5pdGlvbnM+O1xuXG5leHBvcnQgdHlwZSBLZXlib2FyZEV2ZW50RWFzaW5nID1cbiAgfCAnZWFzZUluJ1xuICB8ICdlYXNlSW5FYXNlT3V0J1xuICB8ICdlYXNlT3V0J1xuICB8ICdsaW5lYXInXG4gIHwgJ2tleWJvYXJkJztcblxuZXhwb3J0IHR5cGUgS2V5Ym9hcmRNZXRyaWNzID0gJFJlYWRPbmx5PHt8XG4gIHNjcmVlblg6IG51bWJlcixcbiAgc2NyZWVuWTogbnVtYmVyLFxuICB3aWR0aDogbnVtYmVyLFxuICBoZWlnaHQ6IG51bWJlcixcbnx9PjtcblxuZXhwb3J0IHR5cGUgS2V5Ym9hcmRFdmVudCA9IEFuZHJvaWRLZXlib2FyZEV2ZW50IHwgSU9TS2V5Ym9hcmRFdmVudDtcblxudHlwZSBCYXNlS2V5Ym9hcmRFdmVudCA9IHt8XG4gIGR1cmF0aW9uOiBudW1iZXIsXG4gIGVhc2luZzogS2V5Ym9hcmRFdmVudEVhc2luZyxcbiAgZW5kQ29vcmRpbmF0ZXM6IEtleWJvYXJkTWV0cmljcyxcbnx9O1xuXG5leHBvcnQgdHlwZSBBbmRyb2lkS2V5Ym9hcmRFdmVudCA9ICRSZWFkT25seTx7fFxuICAuLi5CYXNlS2V5Ym9hcmRFdmVudCxcbiAgZHVyYXRpb246IDAsXG4gIGVhc2luZzogJ2tleWJvYXJkJyxcbnx9PjtcblxuZXhwb3J0IHR5cGUgSU9TS2V5Ym9hcmRFdmVudCA9ICRSZWFkT25seTx7fFxuICAuLi5CYXNlS2V5Ym9hcmRFdmVudCxcbiAgc3RhcnRDb29yZGluYXRlczogS2V5Ym9hcmRNZXRyaWNzLFxuICBpc0V2ZW50RnJvbVRoaXNBcHA6IGJvb2xlYW4sXG58fT47XG5cbnR5cGUgS2V5Ym9hcmRFdmVudERlZmluaXRpb25zID0ge1xuICBrZXlib2FyZFdpbGxTaG93OiBbS2V5Ym9hcmRFdmVudF0sXG4gIGtleWJvYXJkRGlkU2hvdzogW0tleWJvYXJkRXZlbnRdLFxuICBrZXlib2FyZFdpbGxIaWRlOiBbS2V5Ym9hcmRFdmVudF0sXG4gIGtleWJvYXJkRGlkSGlkZTogW0tleWJvYXJkRXZlbnRdLFxuICBrZXlib2FyZFdpbGxDaGFuZ2VGcmFtZTogW0tleWJvYXJkRXZlbnRdLFxuICBrZXlib2FyZERpZENoYW5nZUZyYW1lOiBbS2V5Ym9hcmRFdmVudF0sXG59O1xuXG4vKipcbiAqIGBLZXlib2FyZGAgbW9kdWxlIHRvIGNvbnRyb2wga2V5Ym9hcmQgZXZlbnRzLlxuICpcbiAqICMjIyBVc2FnZVxuICpcbiAqIFRoZSBLZXlib2FyZCBtb2R1bGUgYWxsb3dzIHlvdSB0byBsaXN0ZW4gZm9yIG5hdGl2ZSBldmVudHMgYW5kIHJlYWN0IHRvIHRoZW0sIGFzXG4gKiB3ZWxsIGFzIG1ha2UgY2hhbmdlcyB0byB0aGUga2V5Ym9hcmQsIGxpa2UgZGlzbWlzc2luZyBpdC5cbiAqXG4gKmBgYFxuICogaW1wb3J0IFJlYWN0LCB7IENvbXBvbmVudCB9IGZyb20gJ3JlYWN0JztcbiAqIGltcG9ydCB7IEtleWJvYXJkLCBUZXh0SW5wdXQgfSBmcm9tICdyZWFjdC1uYXRpdmUnO1xuICpcbiAqIGNsYXNzIEV4YW1wbGUgZXh0ZW5kcyBDb21wb25lbnQge1xuICogICBjb21wb25lbnRXaWxsTW91bnQgKCkge1xuICogICAgIHRoaXMua2V5Ym9hcmREaWRTaG93TGlzdGVuZXIgPSBLZXlib2FyZC5hZGRMaXN0ZW5lcigna2V5Ym9hcmREaWRTaG93JywgdGhpcy5fa2V5Ym9hcmREaWRTaG93KTtcbiAqICAgICB0aGlzLmtleWJvYXJkRGlkSGlkZUxpc3RlbmVyID0gS2V5Ym9hcmQuYWRkTGlzdGVuZXIoJ2tleWJvYXJkRGlkSGlkZScsIHRoaXMuX2tleWJvYXJkRGlkSGlkZSk7XG4gKiAgIH1cbiAqXG4gKiAgIGNvbXBvbmVudFdpbGxVbm1vdW50ICgpIHtcbiAqICAgICB0aGlzLmtleWJvYXJkRGlkU2hvd0xpc3RlbmVyLnJlbW92ZSgpO1xuICogICAgIHRoaXMua2V5Ym9hcmREaWRIaWRlTGlzdGVuZXIucmVtb3ZlKCk7XG4gKiAgIH1cbiAqXG4gKiAgIF9rZXlib2FyZERpZFNob3cgKCkge1xuICogICAgIGFsZXJ0KCdLZXlib2FyZCBTaG93bicpO1xuICogICB9XG4gKlxuICogICBfa2V5Ym9hcmREaWRIaWRlICgpIHtcbiAqICAgICBhbGVydCgnS2V5Ym9hcmQgSGlkZGVuJyk7XG4gKiAgIH1cbiAqXG4gKiAgIHJlbmRlcigpIHtcbiAqICAgICByZXR1cm4gKFxuICogICAgICAgPFRleHRJbnB1dFxuICogICAgICAgICBvblN1Ym1pdEVkaXRpbmc9e0tleWJvYXJkLmRpc21pc3N9XG4gKiAgICAgICAvPlxuICogICAgICk7XG4gKiAgIH1cbiAqIH1cbiAqYGBgXG4gKi9cblxuY2xhc3MgS2V5Ym9hcmQge1xuICBfY3VycmVudGx5U2hvd2luZzogP0tleWJvYXJkRXZlbnQ7XG5cbiAgX2VtaXR0ZXI6IE5hdGl2ZUV2ZW50RW1pdHRlcjxLZXlib2FyZEV2ZW50RGVmaW5pdGlvbnM+ID1cbiAgICBuZXcgTmF0aXZlRXZlbnRFbWl0dGVyKFxuICAgICAgLy8gVDg4NzE1MDYzOiBOYXRpdmVFdmVudEVtaXR0ZXIgb25seSB1c2VkIHRoaXMgcGFyYW1ldGVyIG9uIGlPUy4gTm93IGl0IHVzZXMgaXQgb24gYWxsIHBsYXRmb3Jtcywgc28gdGhpcyBjb2RlIHdhcyBtb2RpZmllZCBhdXRvbWF0aWNhbGx5IHRvIHByZXNlcnZlIGl0cyBiZWhhdmlvclxuICAgICAgLy8gSWYgeW91IHdhbnQgdG8gdXNlIHRoZSBuYXRpdmUgbW9kdWxlIG9uIG90aGVyIHBsYXRmb3JtcywgcGxlYXNlIHJlbW92ZSB0aGlzIGNvbmRpdGlvbiBhbmQgdGVzdCBpdHMgYmVoYXZpb3JcbiAgICAgIFBsYXRmb3JtLk9TICE9PSAnaW9zJyA/IG51bGwgOiBOYXRpdmVLZXlib2FyZE9ic2VydmVyLFxuICAgICk7XG5cbiAgY29uc3RydWN0b3IoKSB7XG4gICAgdGhpcy5hZGRMaXN0ZW5lcigna2V5Ym9hcmREaWRTaG93JywgZXYgPT4ge1xuICAgICAgdGhpcy5fY3VycmVudGx5U2hvd2luZyA9IGV2O1xuICAgIH0pO1xuICAgIHRoaXMuYWRkTGlzdGVuZXIoJ2tleWJvYXJkRGlkSGlkZScsIF9ldiA9PiB7XG4gICAgICB0aGlzLl9jdXJyZW50bHlTaG93aW5nID0gbnVsbDtcbiAgICB9KTtcbiAgfVxuXG4gIC8qKlxuICAgKiBUaGUgYGFkZExpc3RlbmVyYCBmdW5jdGlvbiBjb25uZWN0cyBhIEphdmFTY3JpcHQgZnVuY3Rpb24gdG8gYW4gaWRlbnRpZmllZCBuYXRpdmVcbiAgICoga2V5Ym9hcmQgbm90aWZpY2F0aW9uIGV2ZW50LlxuICAgKlxuICAgKiBUaGlzIGZ1bmN0aW9uIHRoZW4gcmV0dXJucyB0aGUgcmVmZXJlbmNlIHRvIHRoZSBsaXN0ZW5lci5cbiAgICpcbiAgICogQHBhcmFtIHtzdHJpbmd9IGV2ZW50TmFtZSBUaGUgYG5hdGl2ZUV2ZW50YCBpcyB0aGUgc3RyaW5nIHRoYXQgaWRlbnRpZmllcyB0aGUgZXZlbnQgeW91J3JlIGxpc3RlbmluZyBmb3IuICBUaGlzXG4gICAqY2FuIGJlIGFueSBvZiB0aGUgZm9sbG93aW5nOlxuICAgKlxuICAgKiAtIGBrZXlib2FyZFdpbGxTaG93YFxuICAgKiAtIGBrZXlib2FyZERpZFNob3dgXG4gICAqIC0gYGtleWJvYXJkV2lsbEhpZGVgXG4gICAqIC0gYGtleWJvYXJkRGlkSGlkZWBcbiAgICogLSBga2V5Ym9hcmRXaWxsQ2hhbmdlRnJhbWVgXG4gICAqIC0gYGtleWJvYXJkRGlkQ2hhbmdlRnJhbWVgXG4gICAqXG4gICAqIEFuZHJvaWQgdmVyc2lvbnMgcHJpb3IgdG8gQVBJIDMwIHJlbHkgb24gb2JzZXJ2aW5nIGxheW91dCBjaGFuZ2VzIHdoZW5cbiAgICogYGFuZHJvaWQ6d2luZG93U29mdElucHV0TW9kZWAgaXMgc2V0IHRvIGBhZGp1c3RSZXNpemVgIG9yIGBhZGp1c3RQYW5gLlxuICAgKlxuICAgKiBga2V5Ym9hcmRXaWxsU2hvd2AgYXMgd2VsbCBhcyBga2V5Ym9hcmRXaWxsSGlkZWAgYXJlIG5vdCBhdmFpbGFibGUgb24gQW5kcm9pZCBzaW5jZSB0aGVyZSBpc1xuICAgKiBubyBuYXRpdmUgY29ycmVzcG9uZGluZyBldmVudC5cbiAgICpcbiAgICogQHBhcmFtIHtmdW5jdGlvbn0gY2FsbGJhY2sgZnVuY3Rpb24gdG8gYmUgY2FsbGVkIHdoZW4gdGhlIGV2ZW50IGZpcmVzLlxuICAgKi9cbiAgYWRkTGlzdGVuZXI8SzogJEtleXM8S2V5Ym9hcmRFdmVudERlZmluaXRpb25zPj4oXG4gICAgZXZlbnRUeXBlOiBLLFxuICAgIGxpc3RlbmVyOiAoLi4uJEVsZW1lbnRUeXBlPEtleWJvYXJkRXZlbnREZWZpbml0aW9ucywgSz4pID0+IG1peGVkLFxuICAgIGNvbnRleHQ/OiBtaXhlZCxcbiAgKTogRXZlbnRTdWJzY3JpcHRpb24ge1xuICAgIHJldHVybiB0aGlzLl9lbWl0dGVyLmFkZExpc3RlbmVyKGV2ZW50VHlwZSwgbGlzdGVuZXIpO1xuICB9XG5cbiAgLyoqXG4gICAqIFJlbW92ZXMgYWxsIGxpc3RlbmVycyBmb3IgYSBzcGVjaWZpYyBldmVudCB0eXBlLlxuICAgKlxuICAgKiBAcGFyYW0ge3N0cmluZ30gZXZlbnRUeXBlIFRoZSBuYXRpdmUgZXZlbnQgc3RyaW5nIGxpc3RlbmVycyBhcmUgd2F0Y2hpbmcgd2hpY2ggd2lsbCBiZSByZW1vdmVkLlxuICAgKi9cbiAgcmVtb3ZlQWxsTGlzdGVuZXJzPEs6ICRLZXlzPEtleWJvYXJkRXZlbnREZWZpbml0aW9ucz4+KGV2ZW50VHlwZTogP0spOiB2b2lkIHtcbiAgICB0aGlzLl9lbWl0dGVyLnJlbW92ZUFsbExpc3RlbmVycyhldmVudFR5cGUpO1xuICB9XG5cbiAgLyoqXG4gICAqIERpc21pc3NlcyB0aGUgYWN0aXZlIGtleWJvYXJkIGFuZCByZW1vdmVzIGZvY3VzLlxuICAgKi9cbiAgZGlzbWlzcygpOiB2b2lkIHtcbiAgICBkaXNtaXNzS2V5Ym9hcmQoKTtcbiAgfVxuXG4gIC8qKlxuICAgKiBXaGV0aGVyIHRoZSBrZXlib2FyZCBpcyBsYXN0IGtub3duIHRvIGJlIHZpc2libGUuXG4gICAqL1xuICBpc1Zpc2libGUoKTogYm9vbGVhbiB7XG4gICAgcmV0dXJuICEhdGhpcy5fY3VycmVudGx5U2hvd2luZztcbiAgfVxuXG4gIC8qKlxuICAgKiBSZXR1cm4gdGhlIG1ldHJpY3Mgb2YgdGhlIHNvZnQta2V5Ym9hcmQgaWYgdmlzaWJsZS5cbiAgICovXG4gIG1ldHJpY3MoKTogP0tleWJvYXJkTWV0cmljcyB7XG4gICAgcmV0dXJuIHRoaXMuX2N1cnJlbnRseVNob3dpbmc/LmVuZENvb3JkaW5hdGVzO1xuICB9XG5cbiAgLyoqXG4gICAqIFVzZWZ1bCBmb3Igc3luY2luZyBUZXh0SW5wdXQgKG9yIG90aGVyIGtleWJvYXJkIGFjY2Vzc29yeSB2aWV3KSBzaXplIG9mXG4gICAqIHBvc2l0aW9uIGNoYW5nZXMgd2l0aCBrZXlib2FyZCBtb3ZlbWVudHMuXG4gICAqL1xuICBzY2hlZHVsZUxheW91dEFuaW1hdGlvbihldmVudDogS2V5Ym9hcmRFdmVudCk6IHZvaWQge1xuICAgIGNvbnN0IHtkdXJhdGlvbiwgZWFzaW5nfSA9IGV2ZW50O1xuICAgIGlmIChkdXJhdGlvbiAhPSBudWxsICYmIGR1cmF0aW9uICE9PSAwKSB7XG4gICAgICBMYXlvdXRBbmltYXRpb24uY29uZmlndXJlTmV4dCh7XG4gICAgICAgIGR1cmF0aW9uOiBkdXJhdGlvbixcbiAgICAgICAgdXBkYXRlOiB7XG4gICAgICAgICAgZHVyYXRpb246IGR1cmF0aW9uLFxuICAgICAgICAgIHR5cGU6IChlYXNpbmcgIT0gbnVsbCAmJiBMYXlvdXRBbmltYXRpb24uVHlwZXNbZWFzaW5nXSkgfHwgJ2tleWJvYXJkJyxcbiAgICAgICAgfSxcbiAgICAgIH0pO1xuICAgIH1cbiAgfVxufVxuXG5tb2R1bGUuZXhwb3J0cyA9IChuZXcgS2V5Ym9hcmQoKTogS2V5Ym9hcmQpO1xuIl0sIm1hcHBpbmdzIjoiOzs7QUFZQSxJQUFBQSxtQkFBQSxHQUFBQyxzQkFBQSxDQUFBQyxPQUFBO0FBQ0EsSUFBQUMsZ0JBQUEsR0FBQUYsc0JBQUEsQ0FBQUMsT0FBQTtBQUNBLElBQUFFLGdCQUFBLEdBQUFILHNCQUFBLENBQUFDLE9BQUE7QUFDQSxJQUFBRyxTQUFBLEdBQUFKLHNCQUFBLENBQUFDLE9BQUE7QUFDQSxJQUFBSSx1QkFBQSxHQUFBTCxzQkFBQSxDQUFBQyxPQUFBO0FBQThELElBeUZ4REssUUFBUTtFQVVaLFNBQUFBLFNBQUEsRUFBYztJQUFBLElBQUFDLEtBQUE7SUFBQSxJQUFBQyxnQkFBQSxDQUFBQyxPQUFBLFFBQUFILFFBQUE7SUFBQSxLQVBkSSxRQUFRLEdBQ04sSUFBSUMsMkJBQWtCLENBR3BCQyxpQkFBUSxDQUFDQyxFQUFFLEtBQUssS0FBSyxHQUFHLElBQUksR0FBR0MsK0JBQ2pDLENBQUM7SUFHRCxJQUFJLENBQUNDLFdBQVcsQ0FBQyxpQkFBaUIsRUFBRSxVQUFBQyxFQUFFLEVBQUk7TUFDeENULEtBQUksQ0FBQ1UsaUJBQWlCLEdBQUdELEVBQUU7SUFDN0IsQ0FBQyxDQUFDO0lBQ0YsSUFBSSxDQUFDRCxXQUFXLENBQUMsaUJBQWlCLEVBQUUsVUFBQUcsR0FBRyxFQUFJO01BQ3pDWCxLQUFJLENBQUNVLGlCQUFpQixHQUFHLElBQUk7SUFDL0IsQ0FBQyxDQUFDO0VBQ0o7RUFBQyxXQUFBRSxhQUFBLENBQUFWLE9BQUEsRUFBQUgsUUFBQTtJQUFBYyxHQUFBO0lBQUFDLEtBQUEsRUEwQkQsU0FBQU4sV0FBV0EsQ0FDVE8sU0FBWSxFQUNaQyxRQUFpRSxFQUNqRUMsT0FBZSxFQUNJO01BQ25CLE9BQU8sSUFBSSxDQUFDZCxRQUFRLENBQUNLLFdBQVcsQ0FBQ08sU0FBUyxFQUFFQyxRQUFRLENBQUM7SUFDdkQ7RUFBQztJQUFBSCxHQUFBO0lBQUFDLEtBQUEsRUFPRCxTQUFBSSxrQkFBa0JBLENBQXFDSCxTQUFhLEVBQVE7TUFDMUUsSUFBSSxDQUFDWixRQUFRLENBQUNlLGtCQUFrQixDQUFDSCxTQUFTLENBQUM7SUFDN0M7RUFBQztJQUFBRixHQUFBO0lBQUFDLEtBQUEsRUFLRCxTQUFBSyxPQUFPQSxDQUFBLEVBQVM7TUFDZCxJQUFBQyx3QkFBZSxFQUFDLENBQUM7SUFDbkI7RUFBQztJQUFBUCxHQUFBO0lBQUFDLEtBQUEsRUFLRCxTQUFBTyxTQUFTQSxDQUFBLEVBQVk7TUFDbkIsT0FBTyxDQUFDLENBQUMsSUFBSSxDQUFDWCxpQkFBaUI7SUFDakM7RUFBQztJQUFBRyxHQUFBO0lBQUFDLEtBQUEsRUFLRCxTQUFBUSxPQUFPQSxDQUFBLEVBQXFCO01BQUEsSUFBQUMscUJBQUE7TUFDMUIsUUFBQUEscUJBQUEsR0FBTyxJQUFJLENBQUNiLGlCQUFpQixxQkFBdEJhLHFCQUFBLENBQXdCQyxjQUFjO0lBQy9DO0VBQUM7SUFBQVgsR0FBQTtJQUFBQyxLQUFBLEVBTUQsU0FBQVcsdUJBQXVCQSxDQUFDQyxLQUFvQixFQUFRO01BQ2xELElBQU9DLFFBQVEsR0FBWUQsS0FBSyxDQUF6QkMsUUFBUTtRQUFFQyxNQUFNLEdBQUlGLEtBQUssQ0FBZkUsTUFBTTtNQUN2QixJQUFJRCxRQUFRLElBQUksSUFBSSxJQUFJQSxRQUFRLEtBQUssQ0FBQyxFQUFFO1FBQ3RDRSx3QkFBZSxDQUFDQyxhQUFhLENBQUM7VUFDNUJILFFBQVEsRUFBRUEsUUFBUTtVQUNsQkksTUFBTSxFQUFFO1lBQ05KLFFBQVEsRUFBRUEsUUFBUTtZQUNsQkssSUFBSSxFQUFHSixNQUFNLElBQUksSUFBSSxJQUFJQyx3QkFBZSxDQUFDSSxLQUFLLENBQUNMLE1BQU0sQ0FBQyxJQUFLO1VBQzdEO1FBQ0YsQ0FBQyxDQUFDO01BQ0o7SUFDRjtFQUFDO0FBQUE7QUFHSE0sTUFBTSxDQUFDQyxPQUFPLEdBQUksSUFBSXBDLFFBQVEsQ0FBQyxDQUFZIiwiaWdub3JlTGlzdCI6W119