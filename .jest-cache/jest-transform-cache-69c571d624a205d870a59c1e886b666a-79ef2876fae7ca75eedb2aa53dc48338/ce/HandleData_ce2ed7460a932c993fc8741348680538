e51183b3ff20ccc14a2f9e1263eb5a78
"use strict";

/* istanbul ignore next */
function cov_18gz7zfii8() {
  var path = "/Users/<USER>/projects/react-native/payment-module/src/utils/HandleData.ts";
  var hash = "40b161eb501d476fcf99eac45cfff49dc85bf734";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/projects/react-native/payment-module/src/utils/HandleData.ts",
    statementMap: {
      "0": {
        start: {
          line: 3,
          column: 29
        },
        end: {
          line: 3,
          column: 84
        }
      },
      "1": {
        start: {
          line: 4,
          column: 25
        },
        end: {
          line: 4,
          column: 99
        }
      },
      "2": {
        start: {
          line: 5,
          column: 0
        },
        end: {
          line: 7,
          column: 3
        }
      },
      "3": {
        start: {
          line: 8,
          column: 0
        },
        end: {
          line: 8,
          column: 28
        }
      },
      "4": {
        start: {
          line: 9,
          column: 23
        },
        end: {
          line: 9,
          column: 56
        }
      },
      "5": {
        start: {
          line: 10,
          column: 21
        },
        end: {
          line: 10,
          column: 52
        }
      },
      "6": {
        start: {
          line: 11,
          column: 17
        },
        end: {
          line: 34,
          column: 3
        }
      },
      "7": {
        start: {
          line: 12,
          column: 13
        },
        end: {
          line: 30,
          column: 4
        }
      },
      "8": {
        start: {
          line: 13,
          column: 4
        },
        end: {
          line: 29,
          column: 5
        }
      },
      "9": {
        start: {
          line: 14,
          column: 21
        },
        end: {
          line: 14,
          column: 34
        }
      },
      "10": {
        start: {
          line: 15,
          column: 6
        },
        end: {
          line: 15,
          column: 37
        }
      },
      "11": {
        start: {
          line: 16,
          column: 6
        },
        end: {
          line: 20,
          column: 7
        }
      },
      "12": {
        start: {
          line: 18,
          column: 8
        },
        end: {
          line: 18,
          column: 87
        }
      },
      "13": {
        start: {
          line: 19,
          column: 8
        },
        end: {
          line: 19,
          column: 198
        }
      },
      "14": {
        start: {
          line: 21,
          column: 6
        },
        end: {
          line: 21,
          column: 72
        }
      },
      "15": {
        start: {
          line: 22,
          column: 6
        },
        end: {
          line: 22,
          column: 30
        }
      },
      "16": {
        start: {
          line: 24,
          column: 6
        },
        end: {
          line: 24,
          column: 85
        }
      },
      "17": {
        start: {
          line: 25,
          column: 6
        },
        end: {
          line: 27,
          column: 7
        }
      },
      "18": {
        start: {
          line: 26,
          column: 8
        },
        end: {
          line: 26,
          column: 20
        }
      },
      "19": {
        start: {
          line: 28,
          column: 6
        },
        end: {
          line: 28,
          column: 97
        }
      },
      "20": {
        start: {
          line: 31,
          column: 2
        },
        end: {
          line: 33,
          column: 4
        }
      },
      "21": {
        start: {
          line: 32,
          column: 4
        },
        end: {
          line: 32,
          column: 39
        }
      },
      "22": {
        start: {
          line: 35,
          column: 0
        },
        end: {
          line: 35,
          column: 32
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 11,
            column: 17
          },
          end: {
            line: 11,
            column: 18
          }
        },
        loc: {
          start: {
            line: 11,
            column: 29
          },
          end: {
            line: 34,
            column: 1
          }
        },
        line: 11
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 12,
            column: 45
          },
          end: {
            line: 12,
            column: 46
          }
        },
        loc: {
          start: {
            line: 12,
            column: 73
          },
          end: {
            line: 30,
            column: 3
          }
        },
        line: 12
      },
      "2": {
        name: "handleData",
        decl: {
          start: {
            line: 31,
            column: 18
          },
          end: {
            line: 31,
            column: 28
          }
        },
        loc: {
          start: {
            line: 31,
            column: 38
          },
          end: {
            line: 33,
            column: 3
          }
        },
        line: 31
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 16,
            column: 6
          },
          end: {
            line: 20,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 16,
            column: 6
          },
          end: {
            line: 20,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 16
      },
      "1": {
        loc: {
          start: {
            line: 16,
            column: 10
          },
          end: {
            line: 16,
            column: 58
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 16,
            column: 10
          },
          end: {
            line: 16,
            column: 19
          }
        }, {
          start: {
            line: 16,
            column: 23
          },
          end: {
            line: 16,
            column: 39
          }
        }, {
          start: {
            line: 16,
            column: 43
          },
          end: {
            line: 16,
            column: 58
          }
        }],
        line: 16
      },
      "2": {
        loc: {
          start: {
            line: 19,
            column: 48
          },
          end: {
            line: 19,
            column: 196
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 19,
            column: 167
          },
          end: {
            line: 19,
            column: 173
          }
        }, {
          start: {
            line: 19,
            column: 176
          },
          end: {
            line: 19,
            column: 196
          }
        }],
        line: 19
      },
      "3": {
        loc: {
          start: {
            line: 19,
            column: 48
          },
          end: {
            line: 19,
            column: 164
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 19,
            column: 48
          },
          end: {
            line: 19,
            column: 64
          }
        }, {
          start: {
            line: 19,
            column: 68
          },
          end: {
            line: 19,
            column: 112
          }
        }, {
          start: {
            line: 19,
            column: 116
          },
          end: {
            line: 19,
            column: 164
          }
        }],
        line: 19
      },
      "4": {
        loc: {
          start: {
            line: 25,
            column: 6
          },
          end: {
            line: 27,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 25,
            column: 6
          },
          end: {
            line: 27,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 25
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0
    },
    b: {
      "0": [0, 0],
      "1": [0, 0, 0],
      "2": [0, 0],
      "3": [0, 0, 0],
      "4": [0, 0]
    },
    inputSourceMap: {
      version: 3,
      file: undefined,
      names: ["MSBCustomError_1", "require", "MSBErrorCode_1", "handleData", "_ref", "_asyncToGenerator2", "default", "request", "mapper", "response", "console", "log", "errors", "_response$errors", "createError", "key", "error", "CustomError", "MSBErrorCode", "NOT_VALID_DATA_FORMAT", "_x", "_x2", "apply", "arguments", "exports"],
      sourceRoot: undefined,
      sources: ["/Users/<USER>/projects/react-native/payment-module/src/utils/HandleData.ts"],
      sourcesContent: ["import {createError, CustomError} from '../core/MSBCustomError';\nimport {MSBErrorCode} from '../core/MSBErrorCode';\n\nexport const handleData = async <T>(request: Promise<any>, mapper: (response: any) => T): Promise<T> => {\n  try {\n    const response = await request;\n    console.log('DATA:', response);\n    if (!response || response?.errors) {\n      console.log('\u274C\u274C\u274C\u274C\u274C\u274C\u274C\u274C\u274C\u274CRepository handleData Null data or response', response);\n      throw createError(response?.errors?.[0]?.key);\n    }\n    console.log('\u2705\u2705\u2705\u2705\u2705\u2705\u2705\u2705\u2705\u2705\u2705Repository handleData Success', response);\n    return mapper(response);\n  } catch (error) {\n    console.log('\u274C\u274C\u274C\u274C\u274C\u274C\u274C\u274C\u274C\u274CRepository handleData mapper data to DTO Error', error);\n    if (error instanceof CustomError) {\n      throw error;\n    }\n    throw createError(MSBErrorCode.NOT_VALID_DATA_FORMAT);\n  }\n};\n"],
      mappings: ";;;;;;;;AAAA,IAAAA,gBAAA,GAAAC,OAAA;AACA,IAAAC,cAAA,GAAAD,OAAA;AAEO,IAAME,UAAU;EAAA,IAAAC,IAAA,OAAAC,kBAAA,CAAAC,OAAA,EAAG,WAAUC,OAAqB,EAAEC,MAA4B,EAAgB;IACrG,IAAI;MACF,IAAMC,QAAQ,SAASF,OAAO;MAC9BG,OAAO,CAACC,GAAG,CAAC,OAAO,EAAEF,QAAQ,CAAC;MAC9B,IAAI,CAACA,QAAQ,IAAIA,QAAQ,YAARA,QAAQ,CAAEG,MAAM,EAAE;QAAA,IAAAC,gBAAA;QACjCH,OAAO,CAACC,GAAG,CAAC,uDAAuD,EAAEF,QAAQ,CAAC;QAC9E,MAAM,IAAAT,gBAAA,CAAAc,WAAW,EAACL,QAAQ,aAAAI,gBAAA,GAARJ,QAAQ,CAAEG,MAAM,cAAAC,gBAAA,GAAhBA,gBAAA,CAAmB,CAAC,CAAC,qBAArBA,gBAAA,CAAuBE,GAAG,CAAC;MAC/C;MACAL,OAAO,CAACC,GAAG,CAAC,0CAA0C,EAAEF,QAAQ,CAAC;MACjE,OAAOD,MAAM,CAACC,QAAQ,CAAC;IACzB,CAAC,CAAC,OAAOO,KAAK,EAAE;MACdN,OAAO,CAACC,GAAG,CAAC,0DAA0D,EAAEK,KAAK,CAAC;MAC9E,IAAIA,KAAK,YAAYhB,gBAAA,CAAAiB,WAAW,EAAE;QAChC,MAAMD,KAAK;MACb;MACA,MAAM,IAAAhB,gBAAA,CAAAc,WAAW,EAACZ,cAAA,CAAAgB,YAAY,CAACC,qBAAqB,CAAC;IACvD;EACF,CAAC;EAAA,gBAjBYhB,UAAUA,CAAAiB,EAAA,EAAAC,GAAA;IAAA,OAAAjB,IAAA,CAAAkB,KAAA,OAAAC,SAAA;EAAA;AAAA,GAiBtB;AAjBYC,OAAA,CAAArB,UAAU,GAAAA,UAAA",
      ignoreList: []
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "40b161eb501d476fcf99eac45cfff49dc85bf734"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_18gz7zfii8 = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_18gz7zfii8();
var _interopRequireDefault =
/* istanbul ignore next */
(cov_18gz7zfii8().s[0]++, require("@babel/runtime/helpers/interopRequireDefault"));
var _asyncToGenerator2 =
/* istanbul ignore next */
(cov_18gz7zfii8().s[1]++, _interopRequireDefault(require("@babel/runtime/helpers/asyncToGenerator")));
/* istanbul ignore next */
cov_18gz7zfii8().s[2]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_18gz7zfii8().s[3]++;
exports.handleData = void 0;
var MSBCustomError_1 =
/* istanbul ignore next */
(cov_18gz7zfii8().s[4]++, require("../core/MSBCustomError"));
var MSBErrorCode_1 =
/* istanbul ignore next */
(cov_18gz7zfii8().s[5]++, require("../core/MSBErrorCode"));
var handleData =
/* istanbul ignore next */
(cov_18gz7zfii8().s[6]++, function () {
  /* istanbul ignore next */
  cov_18gz7zfii8().f[0]++;
  var _ref =
  /* istanbul ignore next */
  (cov_18gz7zfii8().s[7]++, (0, _asyncToGenerator2.default)(function* (request, mapper) {
    /* istanbul ignore next */
    cov_18gz7zfii8().f[1]++;
    cov_18gz7zfii8().s[8]++;
    try {
      var response =
      /* istanbul ignore next */
      (cov_18gz7zfii8().s[9]++, yield request);
      /* istanbul ignore next */
      cov_18gz7zfii8().s[10]++;
      console.log('DATA:', response);
      /* istanbul ignore next */
      cov_18gz7zfii8().s[11]++;
      if (
      /* istanbul ignore next */
      (cov_18gz7zfii8().b[1][0]++, !response) ||
      /* istanbul ignore next */
      (cov_18gz7zfii8().b[1][1]++, response != null) &&
      /* istanbul ignore next */
      (cov_18gz7zfii8().b[1][2]++, response.errors)) {
        /* istanbul ignore next */
        cov_18gz7zfii8().b[0][0]++;
        var _response$errors;
        /* istanbul ignore next */
        cov_18gz7zfii8().s[12]++;
        console.log('❌❌❌❌❌❌❌❌❌❌Repository handleData Null data or response', response);
        /* istanbul ignore next */
        cov_18gz7zfii8().s[13]++;
        throw (0, MSBCustomError_1.createError)(
        /* istanbul ignore next */
        (cov_18gz7zfii8().b[3][0]++, response == null) ||
        /* istanbul ignore next */
        (cov_18gz7zfii8().b[3][1]++, (_response$errors = response.errors) == null) ||
        /* istanbul ignore next */
        (cov_18gz7zfii8().b[3][2]++, (_response$errors = _response$errors[0]) == null) ?
        /* istanbul ignore next */
        (cov_18gz7zfii8().b[2][0]++, void 0) :
        /* istanbul ignore next */
        (cov_18gz7zfii8().b[2][1]++, _response$errors.key));
      } else
      /* istanbul ignore next */
      {
        cov_18gz7zfii8().b[0][1]++;
      }
      cov_18gz7zfii8().s[14]++;
      console.log('✅✅✅✅✅✅✅✅✅✅✅Repository handleData Success', response);
      /* istanbul ignore next */
      cov_18gz7zfii8().s[15]++;
      return mapper(response);
    } catch (error) {
      /* istanbul ignore next */
      cov_18gz7zfii8().s[16]++;
      console.log('❌❌❌❌❌❌❌❌❌❌Repository handleData mapper data to DTO Error', error);
      /* istanbul ignore next */
      cov_18gz7zfii8().s[17]++;
      if (error instanceof MSBCustomError_1.CustomError) {
        /* istanbul ignore next */
        cov_18gz7zfii8().b[4][0]++;
        cov_18gz7zfii8().s[18]++;
        throw error;
      } else
      /* istanbul ignore next */
      {
        cov_18gz7zfii8().b[4][1]++;
      }
      cov_18gz7zfii8().s[19]++;
      throw (0, MSBCustomError_1.createError)(MSBErrorCode_1.MSBErrorCode.NOT_VALID_DATA_FORMAT);
    }
  }));
  /* istanbul ignore next */
  cov_18gz7zfii8().s[20]++;
  return function handleData(_x, _x2) {
    /* istanbul ignore next */
    cov_18gz7zfii8().f[2]++;
    cov_18gz7zfii8().s[21]++;
    return _ref.apply(this, arguments);
  };
}());
/* istanbul ignore next */
cov_18gz7zfii8().s[22]++;
exports.handleData = handleData;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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