{"version": 3, "names": ["cov_18gz7zfii8", "actualCoverage", "MSBCustomError_1", "s", "require", "MSBErrorCode_1", "handleData", "f", "_ref", "_asyncToGenerator2", "default", "request", "mapper", "response", "console", "log", "b", "errors", "_response$errors", "createError", "key", "error", "CustomError", "MSBErrorCode", "NOT_VALID_DATA_FORMAT", "_x", "_x2", "apply", "arguments", "exports"], "sources": ["/Users/<USER>/projects/react-native/payment-module/src/utils/HandleData.ts"], "sourcesContent": ["import {createError, CustomError} from '../core/MSBCustomError';\nimport {MSBErrorCode} from '../core/MSBErrorCode';\n\nexport const handleData = async <T>(request: Promise<any>, mapper: (response: any) => T): Promise<T> => {\n  try {\n    const response = await request;\n    console.log('DATA:', response);\n    if (!response || response?.errors) {\n      console.log('❌❌❌❌❌❌❌❌❌❌Repository handleData Null data or response', response);\n      throw createError(response?.errors?.[0]?.key);\n    }\n    console.log('✅✅✅✅✅✅✅✅✅✅✅Repository handleData Success', response);\n    return mapper(response);\n  } catch (error) {\n    console.log('❌❌❌❌❌❌❌❌❌❌Repository handleData mapper data to DTO Error', error);\n    if (error instanceof CustomError) {\n      throw error;\n    }\n    throw createError(MSBErrorCode.NOT_VALID_DATA_FORMAT);\n  }\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAOS;IAAAA,cAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,cAAA;;;;;;;;;;;;;;;AAPT,IAAAE,gBAAA;AAAA;AAAA,CAAAF,cAAA,GAAAG,CAAA,OAAAC,OAAA;AACA,IAAAC,cAAA;AAAA;AAAA,CAAAL,cAAA,GAAAG,CAAA,OAAAC,OAAA;AAEO,IAAME,UAAU;AAAA;AAAA,CAAAN,cAAA,GAAAG,CAAA;EAAA;EAAAH,cAAA,GAAAO,CAAA;EAAA,IAAAC,IAAA;EAAA;EAAA,CAAAR,cAAA,GAAAG,CAAA,WAAAM,kBAAA,CAAAC,OAAA,EAAG,WAAUC,OAAqB,EAAEC,MAA4B,EAAgB;IAAA;IAAAZ,cAAA,GAAAO,CAAA;IAAAP,cAAA,GAAAG,CAAA;IACrG,IAAI;MACF,IAAMU,QAAQ;MAAA;MAAA,CAAAb,cAAA,GAAAG,CAAA,aAASQ,OAAO;MAAA;MAAAX,cAAA,GAAAG,CAAA;MAC9BW,OAAO,CAACC,GAAG,CAAC,OAAO,EAAEF,QAAQ,CAAC;MAAA;MAAAb,cAAA,GAAAG,CAAA;MAC9B;MAAI;MAAA,CAAAH,cAAA,GAAAgB,CAAA,WAACH,QAAQ;MAAI;MAAA,CAAAb,cAAA,GAAAgB,CAAA,UAAAH,QAAQ;MAAA;MAAA,CAAAb,cAAA,GAAAgB,CAAA,UAARH,QAAQ,CAAEI,MAAM,GAAE;QAAA;QAAAjB,cAAA,GAAAgB,CAAA;QAAA,IAAAE,gBAAA;QAAA;QAAAlB,cAAA,GAAAG,CAAA;QACjCW,OAAO,CAACC,GAAG,CAAC,uDAAuD,EAAEF,QAAQ,CAAC;QAAA;QAAAb,cAAA,GAAAG,CAAA;QAC9E,MAAM,IAAAD,gBAAA,CAAAiB,WAAW;QAAC;QAAA,CAAAnB,cAAA,GAAAgB,CAAA,UAAAH,QAAQ;QAAA;QAAA,CAAAb,cAAA,GAAAgB,CAAA,WAAAE,gBAAA,GAARL,QAAQ,CAAEI,MAAM;QAAA;QAAA,CAAAjB,cAAA,GAAAgB,CAAA,WAAAE,gBAAA,GAAhBA,gBAAA,CAAmB,CAAC,CAAC;QAAA;QAAA,CAAAlB,cAAA,GAAAgB,CAAA;QAAA;QAAA,CAAAhB,cAAA,GAAAgB,CAAA,UAArBE,gBAAA,CAAuBE,GAAG,EAAC;MAC/C;MAAA;MAAA;QAAApB,cAAA,GAAAgB,CAAA;MAAA;MAAAhB,cAAA,GAAAG,CAAA;MACAW,OAAO,CAACC,GAAG,CAAC,0CAA0C,EAAEF,QAAQ,CAAC;MAAA;MAAAb,cAAA,GAAAG,CAAA;MACjE,OAAOS,MAAM,CAACC,QAAQ,CAAC;IACzB,CAAC,CAAC,OAAOQ,KAAK,EAAE;MAAA;MAAArB,cAAA,GAAAG,CAAA;MACdW,OAAO,CAACC,GAAG,CAAC,0DAA0D,EAAEM,KAAK,CAAC;MAAA;MAAArB,cAAA,GAAAG,CAAA;MAC9E,IAAIkB,KAAK,YAAYnB,gBAAA,CAAAoB,WAAW,EAAE;QAAA;QAAAtB,cAAA,GAAAgB,CAAA;QAAAhB,cAAA,GAAAG,CAAA;QAChC,MAAMkB,KAAK;MACb;MAAA;MAAA;QAAArB,cAAA,GAAAgB,CAAA;MAAA;MAAAhB,cAAA,GAAAG,CAAA;MACA,MAAM,IAAAD,gBAAA,CAAAiB,WAAW,EAACd,cAAA,CAAAkB,YAAY,CAACC,qBAAqB,CAAC;IACvD;EACF,CAAC;EAAA;EAAAxB,cAAA,GAAAG,CAAA;EAAA,gBAjBYG,UAAUA,CAAAmB,EAAA,EAAAC,GAAA;IAAA;IAAA1B,cAAA,GAAAO,CAAA;IAAAP,cAAA,GAAAG,CAAA;IAAA,OAAAK,IAAA,CAAAmB,KAAA,OAAAC,SAAA;EAAA;AAAA,GAiBtB;AAAA;AAAA5B,cAAA,GAAAG,CAAA;AAjBY0B,OAAA,CAAAvB,UAAU,GAAAA,UAAA", "ignoreList": []}