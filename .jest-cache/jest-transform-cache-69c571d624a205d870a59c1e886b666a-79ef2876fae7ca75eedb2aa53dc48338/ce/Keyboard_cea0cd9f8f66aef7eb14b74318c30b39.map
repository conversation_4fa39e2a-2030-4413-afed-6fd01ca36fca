{"version": 3, "names": ["_NativeEventEmitter", "_interopRequireDefault", "require", "_LayoutAnimation", "_dismissKeyboard", "_Platform", "_NativeKeyboardObserver", "Keyboard", "_this", "_classCallCheck2", "default", "_emitter", "NativeEventEmitter", "Platform", "OS", "NativeKeyboardObserver", "addListener", "ev", "_currentlyShowing", "_ev", "_createClass2", "key", "value", "eventType", "listener", "context", "removeAllListeners", "dismiss", "dismissKeyboard", "isVisible", "metrics", "_this$_currentlyShowi", "endCoordinates", "scheduleLayoutAnimation", "event", "duration", "easing", "LayoutAnimation", "configureNext", "update", "type", "Types", "module", "exports"], "sources": ["Keyboard.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @format\n * @flow strict-local\n */\n\nimport type {EventSubscription} from '../../vendor/emitter/EventEmitter';\n\nimport NativeEventEmitter from '../../EventEmitter/NativeEventEmitter';\nimport LayoutAnimation from '../../LayoutAnimation/LayoutAnimation';\nimport dismissKeyboard from '../../Utilities/dismissKeyboard';\nimport Platform from '../../Utilities/Platform';\nimport NativeKeyboardObserver from './NativeKeyboardObserver';\n\nexport type KeyboardEventName = $Keys<KeyboardEventDefinitions>;\n\nexport type KeyboardEventEasing =\n  | 'easeIn'\n  | 'easeInEaseOut'\n  | 'easeOut'\n  | 'linear'\n  | 'keyboard';\n\nexport type KeyboardMetrics = $ReadOnly<{|\n  screenX: number,\n  screenY: number,\n  width: number,\n  height: number,\n|}>;\n\nexport type KeyboardEvent = AndroidKeyboardEvent | IOSKeyboardEvent;\n\ntype BaseKeyboardEvent = {|\n  duration: number,\n  easing: KeyboardEventEasing,\n  endCoordinates: KeyboardMetrics,\n|};\n\nexport type AndroidKeyboardEvent = $ReadOnly<{|\n  ...BaseKeyboardEvent,\n  duration: 0,\n  easing: 'keyboard',\n|}>;\n\nexport type IOSKeyboardEvent = $ReadOnly<{|\n  ...BaseKeyboardEvent,\n  startCoordinates: KeyboardMetrics,\n  isEventFromThisApp: boolean,\n|}>;\n\ntype KeyboardEventDefinitions = {\n  keyboardWillShow: [KeyboardEvent],\n  keyboardDidShow: [KeyboardEvent],\n  keyboardWillHide: [KeyboardEvent],\n  keyboardDidHide: [KeyboardEvent],\n  keyboardWillChangeFrame: [KeyboardEvent],\n  keyboardDidChangeFrame: [KeyboardEvent],\n};\n\n/**\n * `Keyboard` module to control keyboard events.\n *\n * ### Usage\n *\n * The Keyboard module allows you to listen for native events and react to them, as\n * well as make changes to the keyboard, like dismissing it.\n *\n *```\n * import React, { Component } from 'react';\n * import { Keyboard, TextInput } from 'react-native';\n *\n * class Example extends Component {\n *   componentWillMount () {\n *     this.keyboardDidShowListener = Keyboard.addListener('keyboardDidShow', this._keyboardDidShow);\n *     this.keyboardDidHideListener = Keyboard.addListener('keyboardDidHide', this._keyboardDidHide);\n *   }\n *\n *   componentWillUnmount () {\n *     this.keyboardDidShowListener.remove();\n *     this.keyboardDidHideListener.remove();\n *   }\n *\n *   _keyboardDidShow () {\n *     alert('Keyboard Shown');\n *   }\n *\n *   _keyboardDidHide () {\n *     alert('Keyboard Hidden');\n *   }\n *\n *   render() {\n *     return (\n *       <TextInput\n *         onSubmitEditing={Keyboard.dismiss}\n *       />\n *     );\n *   }\n * }\n *```\n */\n\nclass Keyboard {\n  _currentlyShowing: ?KeyboardEvent;\n\n  _emitter: NativeEventEmitter<KeyboardEventDefinitions> =\n    new NativeEventEmitter(\n      // *********: NativeEventEmitter only used this parameter on iOS. Now it uses it on all platforms, so this code was modified automatically to preserve its behavior\n      // If you want to use the native module on other platforms, please remove this condition and test its behavior\n      Platform.OS !== 'ios' ? null : NativeKeyboardObserver,\n    );\n\n  constructor() {\n    this.addListener('keyboardDidShow', ev => {\n      this._currentlyShowing = ev;\n    });\n    this.addListener('keyboardDidHide', _ev => {\n      this._currentlyShowing = null;\n    });\n  }\n\n  /**\n   * The `addListener` function connects a JavaScript function to an identified native\n   * keyboard notification event.\n   *\n   * This function then returns the reference to the listener.\n   *\n   * @param {string} eventName The `nativeEvent` is the string that identifies the event you're listening for.  This\n   *can be any of the following:\n   *\n   * - `keyboardWillShow`\n   * - `keyboardDidShow`\n   * - `keyboardWillHide`\n   * - `keyboardDidHide`\n   * - `keyboardWillChangeFrame`\n   * - `keyboardDidChangeFrame`\n   *\n   * Android versions prior to API 30 rely on observing layout changes when\n   * `android:windowSoftInputMode` is set to `adjustResize` or `adjustPan`.\n   *\n   * `keyboardWillShow` as well as `keyboardWillHide` are not available on Android since there is\n   * no native corresponding event.\n   *\n   * @param {function} callback function to be called when the event fires.\n   */\n  addListener<K: $Keys<KeyboardEventDefinitions>>(\n    eventType: K,\n    listener: (...$ElementType<KeyboardEventDefinitions, K>) => mixed,\n    context?: mixed,\n  ): EventSubscription {\n    return this._emitter.addListener(eventType, listener);\n  }\n\n  /**\n   * Removes all listeners for a specific event type.\n   *\n   * @param {string} eventType The native event string listeners are watching which will be removed.\n   */\n  removeAllListeners<K: $Keys<KeyboardEventDefinitions>>(eventType: ?K): void {\n    this._emitter.removeAllListeners(eventType);\n  }\n\n  /**\n   * Dismisses the active keyboard and removes focus.\n   */\n  dismiss(): void {\n    dismissKeyboard();\n  }\n\n  /**\n   * Whether the keyboard is last known to be visible.\n   */\n  isVisible(): boolean {\n    return !!this._currentlyShowing;\n  }\n\n  /**\n   * Return the metrics of the soft-keyboard if visible.\n   */\n  metrics(): ?KeyboardMetrics {\n    return this._currentlyShowing?.endCoordinates;\n  }\n\n  /**\n   * Useful for syncing TextInput (or other keyboard accessory view) size of\n   * position changes with keyboard movements.\n   */\n  scheduleLayoutAnimation(event: KeyboardEvent): void {\n    const {duration, easing} = event;\n    if (duration != null && duration !== 0) {\n      LayoutAnimation.configureNext({\n        duration: duration,\n        update: {\n          duration: duration,\n          type: (easing != null && LayoutAnimation.Types[easing]) || 'keyboard',\n        },\n      });\n    }\n  }\n}\n\nmodule.exports = (new Keyboard(): Keyboard);\n"], "mappings": ";;;AAYA,IAAAA,mBAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,gBAAA,GAAAF,sBAAA,CAAAC,OAAA;AACA,IAAAE,gBAAA,GAAAH,sBAAA,CAAAC,OAAA;AACA,IAAAG,SAAA,GAAAJ,sBAAA,CAAAC,OAAA;AACA,IAAAI,uBAAA,GAAAL,sBAAA,CAAAC,OAAA;AAA8D,IAyFxDK,QAAQ;EAUZ,SAAAA,SAAA,EAAc;IAAA,IAAAC,KAAA;IAAA,IAAAC,gBAAA,CAAAC,OAAA,QAAAH,QAAA;IAAA,KAPdI,QAAQ,GACN,IAAIC,2BAAkB,CAGpBC,iBAAQ,CAACC,EAAE,KAAK,KAAK,GAAG,IAAI,GAAGC,+BACjC,CAAC;IAGD,IAAI,CAACC,WAAW,CAAC,iBAAiB,EAAE,UAAAC,EAAE,EAAI;MACxCT,KAAI,CAACU,iBAAiB,GAAGD,EAAE;IAC7B,CAAC,CAAC;IACF,IAAI,CAACD,WAAW,CAAC,iBAAiB,EAAE,UAAAG,GAAG,EAAI;MACzCX,KAAI,CAACU,iBAAiB,GAAG,IAAI;IAC/B,CAAC,CAAC;EACJ;EAAC,WAAAE,aAAA,CAAAV,OAAA,EAAAH,QAAA;IAAAc,GAAA;IAAAC,KAAA,EA0BD,SAAAN,WAAWA,CACTO,SAAY,EACZC,QAAiE,EACjEC,OAAe,EACI;MACnB,OAAO,IAAI,CAACd,QAAQ,CAACK,WAAW,CAACO,SAAS,EAAEC,QAAQ,CAAC;IACvD;EAAC;IAAAH,GAAA;IAAAC,KAAA,EAOD,SAAAI,kBAAkBA,CAAqCH,SAAa,EAAQ;MAC1E,IAAI,CAACZ,QAAQ,CAACe,kBAAkB,CAACH,SAAS,CAAC;IAC7C;EAAC;IAAAF,GAAA;IAAAC,KAAA,EAKD,SAAAK,OAAOA,CAAA,EAAS;MACd,IAAAC,wBAAe,EAAC,CAAC;IACnB;EAAC;IAAAP,GAAA;IAAAC,KAAA,EAKD,SAAAO,SAASA,CAAA,EAAY;MACnB,OAAO,CAAC,CAAC,IAAI,CAACX,iBAAiB;IACjC;EAAC;IAAAG,GAAA;IAAAC,KAAA,EAKD,SAAAQ,OAAOA,CAAA,EAAqB;MAAA,IAAAC,qBAAA;MAC1B,QAAAA,qBAAA,GAAO,IAAI,CAACb,iBAAiB,qBAAtBa,qBAAA,CAAwBC,cAAc;IAC/C;EAAC;IAAAX,GAAA;IAAAC,KAAA,EAMD,SAAAW,uBAAuBA,CAACC,KAAoB,EAAQ;MAClD,IAAOC,QAAQ,GAAYD,KAAK,CAAzBC,QAAQ;QAAEC,MAAM,GAAIF,KAAK,CAAfE,MAAM;MACvB,IAAID,QAAQ,IAAI,IAAI,IAAIA,QAAQ,KAAK,CAAC,EAAE;QACtCE,wBAAe,CAACC,aAAa,CAAC;UAC5BH,QAAQ,EAAEA,QAAQ;UAClBI,MAAM,EAAE;YACNJ,QAAQ,EAAEA,QAAQ;YAClBK,IAAI,EAAGJ,MAAM,IAAI,IAAI,IAAIC,wBAAe,CAACI,KAAK,CAACL,MAAM,CAAC,IAAK;UAC7D;QACF,CAAC,CAAC;MACJ;IACF;EAAC;AAAA;AAGHM,MAAM,CAACC,OAAO,GAAI,IAAIpC,QAAQ,CAAC,CAAY", "ignoreList": []}