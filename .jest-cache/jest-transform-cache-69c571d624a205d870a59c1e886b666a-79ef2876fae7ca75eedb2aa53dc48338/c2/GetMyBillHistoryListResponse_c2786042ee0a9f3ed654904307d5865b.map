{"version": 3, "names": [], "sources": ["/Users/<USER>/projects/react-native/payment-module/src/data/models/get-my-bill-history-list/GetMyBillHistoryListResponse.ts"], "sourcesContent": ["import {BillHistoryData} from '../../../domain/entities/get-my-bill-history-list/GetMyBillHistoryListModel.ts';\n\nexport type GetMyBillHistoryListResponse = BillHistoryData[];\n\nexport interface GetMyBillHistoryList {\n  id: string;\n  billCode: string;\n  category: string;\n  subGroupId?: string;\n  customerName?: string;\n  totalAmount: number;\n  period?: string;\n  paymentDate: string;\n  accountNumber: string;\n  coreRef?: string;\n  content?: string;\n  serviceCode: string;\n  arrangementId: string;\n  paymentOrderId: string;\n  cifNo: string;\n  creditDebitIndicator?: 'CRDT' | 'DBIT';\n  transactionAmountCurrency?: {\n    amount: string;\n    currencyCode: string;\n  };\n  creationTime: string;\n  counterPartyName?: string;\n  description?: string;\n}\n"], "mappings": "", "ignoreList": []}