c39cba4ea84402104a68e5f518b10370
"use strict";

/* istanbul ignore next */
function cov_23e5yrlne4() {
  var path = "/Users/<USER>/projects/react-native/payment-module/src/data/models/get-my-bill-history-list/GetMyBillHistoryListResponse.ts";
  var hash = "fb977154e04d2a321f02dad08decfa20869f1edb";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/projects/react-native/payment-module/src/data/models/get-my-bill-history-list/GetMyBillHistoryListResponse.ts",
    statementMap: {
      "0": {
        start: {
          line: 3,
          column: 0
        },
        end: {
          line: 5,
          column: 3
        }
      }
    },
    fnMap: {},
    branchMap: {},
    s: {
      "0": 0
    },
    f: {},
    b: {},
    inputSourceMap: {
      version: 3,
      file: undefined,
      names: [],
      sourceRoot: undefined,
      sources: ["/Users/<USER>/projects/react-native/payment-module/src/data/models/get-my-bill-history-list/GetMyBillHistoryListResponse.ts"],
      sourcesContent: ["import {BillHistoryData} from '../../../domain/entities/get-my-bill-history-list/GetMyBillHistoryListModel.ts';\n\nexport type GetMyBillHistoryListResponse = BillHistoryData[];\n\nexport interface GetMyBillHistoryList {\n  id: string;\n  billCode: string;\n  category: string;\n  subGroupId?: string;\n  customerName?: string;\n  totalAmount: number;\n  period?: string;\n  paymentDate: string;\n  accountNumber: string;\n  coreRef?: string;\n  content?: string;\n  serviceCode: string;\n  arrangementId: string;\n  paymentOrderId: string;\n  cifNo: string;\n  creditDebitIndicator?: 'CRDT' | 'DBIT';\n  transactionAmountCurrency?: {\n    amount: string;\n    currencyCode: string;\n  };\n  creationTime: string;\n  counterPartyName?: string;\n  description?: string;\n}\n"],
      mappings: "",
      ignoreList: []
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "fb977154e04d2a321f02dad08decfa20869f1edb"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_23e5yrlne4 = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_23e5yrlne4();
cov_23e5yrlne4().s[0]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6W10sInNvdXJjZXMiOlsiL1VzZXJzL3NvbnNhbXNldC9wcm9qZWN0cy9yZWFjdC1uYXRpdmUvcGF5bWVudC1tb2R1bGUvc3JjL2RhdGEvbW9kZWxzL2dldC1teS1iaWxsLWhpc3RvcnktbGlzdC9HZXRNeUJpbGxIaXN0b3J5TGlzdFJlc3BvbnNlLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7QmlsbEhpc3RvcnlEYXRhfSBmcm9tICcuLi8uLi8uLi9kb21haW4vZW50aXRpZXMvZ2V0LW15LWJpbGwtaGlzdG9yeS1saXN0L0dldE15QmlsbEhpc3RvcnlMaXN0TW9kZWwudHMnO1xuXG5leHBvcnQgdHlwZSBHZXRNeUJpbGxIaXN0b3J5TGlzdFJlc3BvbnNlID0gQmlsbEhpc3RvcnlEYXRhW107XG5cbmV4cG9ydCBpbnRlcmZhY2UgR2V0TXlCaWxsSGlzdG9yeUxpc3Qge1xuICBpZDogc3RyaW5nO1xuICBiaWxsQ29kZTogc3RyaW5nO1xuICBjYXRlZ29yeTogc3RyaW5nO1xuICBzdWJHcm91cElkPzogc3RyaW5nO1xuICBjdXN0b21lck5hbWU/OiBzdHJpbmc7XG4gIHRvdGFsQW1vdW50OiBudW1iZXI7XG4gIHBlcmlvZD86IHN0cmluZztcbiAgcGF5bWVudERhdGU6IHN0cmluZztcbiAgYWNjb3VudE51bWJlcjogc3RyaW5nO1xuICBjb3JlUmVmPzogc3RyaW5nO1xuICBjb250ZW50Pzogc3RyaW5nO1xuICBzZXJ2aWNlQ29kZTogc3RyaW5nO1xuICBhcnJhbmdlbWVudElkOiBzdHJpbmc7XG4gIHBheW1lbnRPcmRlcklkOiBzdHJpbmc7XG4gIGNpZk5vOiBzdHJpbmc7XG4gIGNyZWRpdERlYml0SW5kaWNhdG9yPzogJ0NSRFQnIHwgJ0RCSVQnO1xuICB0cmFuc2FjdGlvbkFtb3VudEN1cnJlbmN5Pzoge1xuICAgIGFtb3VudDogc3RyaW5nO1xuICAgIGN1cnJlbmN5Q29kZTogc3RyaW5nO1xuICB9O1xuICBjcmVhdGlvblRpbWU6IHN0cmluZztcbiAgY291bnRlclBhcnR5TmFtZT86IHN0cmluZztcbiAgZGVzY3JpcHRpb24/OiBzdHJpbmc7XG59XG4iXSwibWFwcGluZ3MiOiIiLCJpZ25vcmVMaXN0IjpbXX0=