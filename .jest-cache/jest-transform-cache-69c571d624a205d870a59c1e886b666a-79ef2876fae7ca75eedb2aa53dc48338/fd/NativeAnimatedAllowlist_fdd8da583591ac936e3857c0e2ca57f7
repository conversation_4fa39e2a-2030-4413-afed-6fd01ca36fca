c2935860445434422ba9b25c01a43025
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.allowInterpolationParam = allowInterpolationParam;
exports.allowStyleProp = allowStyleProp;
exports.allowTransformProp = allowTransformProp;
exports.default = void 0;
exports.isSupportedColorStyleProp = isSupportedColorStyleProp;
exports.isSupportedInterpolationParam = isSupportedInterpolationParam;
exports.isSupportedStyleProp = isSupportedStyleProp;
exports.isSupportedTransformProp = isSupportedTransformProp;
var ReactNativeFeatureFlags = _interopRequireWildcard(require("../../src/private/featureflags/ReactNativeFeatureFlags"));
function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
var SUPPORTED_COLOR_STYLES = {
  backgroundColor: true,
  borderBottomColor: true,
  borderColor: true,
  borderEndColor: true,
  borderLeftColor: true,
  borderRightColor: true,
  borderStartColor: true,
  borderTopColor: true,
  color: true,
  tintColor: true
};
var SUPPORTED_STYLES = Object.assign({}, SUPPORTED_COLOR_STYLES, {
  borderBottomEndRadius: true,
  borderBottomLeftRadius: true,
  borderBottomRightRadius: true,
  borderBottomStartRadius: true,
  borderEndEndRadius: true,
  borderEndStartRadius: true,
  borderRadius: true,
  borderTopEndRadius: true,
  borderTopLeftRadius: true,
  borderTopRightRadius: true,
  borderTopStartRadius: true,
  borderStartEndRadius: true,
  borderStartStartRadius: true,
  elevation: true,
  opacity: true,
  transform: true,
  zIndex: true,
  shadowOpacity: true,
  shadowRadius: true,
  scaleX: true,
  scaleY: true,
  translateX: true,
  translateY: true
});
var SUPPORTED_TRANSFORMS = Object.assign({
  translateX: true,
  translateY: true,
  scale: true,
  scaleX: true,
  scaleY: true,
  rotate: true,
  rotateX: true,
  rotateY: true,
  rotateZ: true,
  perspective: true,
  skewX: true,
  skewY: true
}, ReactNativeFeatureFlags.shouldUseAnimatedObjectForTransform() ? {
  matrix: true
} : {});
var SUPPORTED_INTERPOLATION_PARAMS = {
  inputRange: true,
  outputRange: true,
  extrapolate: true,
  extrapolateRight: true,
  extrapolateLeft: true
};
var _default = exports.default = {
  style: SUPPORTED_STYLES
};
function allowInterpolationParam(param) {
  SUPPORTED_INTERPOLATION_PARAMS[param] = true;
}
function allowStyleProp(prop) {
  SUPPORTED_STYLES[prop] = true;
}
function allowTransformProp(prop) {
  SUPPORTED_TRANSFORMS[prop] = true;
}
function isSupportedColorStyleProp(prop) {
  return SUPPORTED_COLOR_STYLES.hasOwnProperty(prop);
}
function isSupportedInterpolationParam(param) {
  return SUPPORTED_INTERPOLATION_PARAMS.hasOwnProperty(param);
}
function isSupportedStyleProp(prop) {
  return SUPPORTED_STYLES.hasOwnProperty(prop);
}
function isSupportedTransformProp(prop) {
  return SUPPORTED_TRANSFORMS.hasOwnProperty(prop);
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJSZWFjdE5hdGl2ZUZlYXR1cmVGbGFncyIsIl9pbnRlcm9wUmVxdWlyZVdpbGRjYXJkIiwicmVxdWlyZSIsIl9nZXRSZXF1aXJlV2lsZGNhcmRDYWNoZSIsImUiLCJXZWFrTWFwIiwiciIsInQiLCJfX2VzTW9kdWxlIiwiZGVmYXVsdCIsImhhcyIsImdldCIsIm4iLCJfX3Byb3RvX18iLCJhIiwiT2JqZWN0IiwiZGVmaW5lUHJvcGVydHkiLCJnZXRPd25Qcm9wZXJ0eURlc2NyaXB0b3IiLCJ1IiwiaGFzT3duUHJvcGVydHkiLCJjYWxsIiwiaSIsInNldCIsIlNVUFBPUlRFRF9DT0xPUl9TVFlMRVMiLCJiYWNrZ3JvdW5kQ29sb3IiLCJib3JkZXJCb3R0b21Db2xvciIsImJvcmRlckNvbG9yIiwiYm9yZGVyRW5kQ29sb3IiLCJib3JkZXJMZWZ0Q29sb3IiLCJib3JkZXJSaWdodENvbG9yIiwiYm9yZGVyU3RhcnRDb2xvciIsImJvcmRlclRvcENvbG9yIiwiY29sb3IiLCJ0aW50Q29sb3IiLCJTVVBQT1JURURfU1RZTEVTIiwiYXNzaWduIiwiYm9yZGVyQm90dG9tRW5kUmFkaXVzIiwiYm9yZGVyQm90dG9tTGVmdFJhZGl1cyIsImJvcmRlckJvdHRvbVJpZ2h0UmFkaXVzIiwiYm9yZGVyQm90dG9tU3RhcnRSYWRpdXMiLCJib3JkZXJFbmRFbmRSYWRpdXMiLCJib3JkZXJFbmRTdGFydFJhZGl1cyIsImJvcmRlclJhZGl1cyIsImJvcmRlclRvcEVuZFJhZGl1cyIsImJvcmRlclRvcExlZnRSYWRpdXMiLCJib3JkZXJUb3BSaWdodFJhZGl1cyIsImJvcmRlclRvcFN0YXJ0UmFkaXVzIiwiYm9yZGVyU3RhcnRFbmRSYWRpdXMiLCJib3JkZXJTdGFydFN0YXJ0UmFkaXVzIiwiZWxldmF0aW9uIiwib3BhY2l0eSIsInRyYW5zZm9ybSIsInpJbmRleCIsInNoYWRvd09wYWNpdHkiLCJzaGFkb3dSYWRpdXMiLCJzY2FsZVgiLCJzY2FsZVkiLCJ0cmFuc2xhdGVYIiwidHJhbnNsYXRlWSIsIlNVUFBPUlRFRF9UUkFOU0ZPUk1TIiwic2NhbGUiLCJyb3RhdGUiLCJyb3RhdGVYIiwicm90YXRlWSIsInJvdGF0ZVoiLCJwZXJzcGVjdGl2ZSIsInNrZXdYIiwic2tld1kiLCJzaG91bGRVc2VBbmltYXRlZE9iamVjdEZvclRyYW5zZm9ybSIsIm1hdHJpeCIsIlNVUFBPUlRFRF9JTlRFUlBPTEFUSU9OX1BBUkFNUyIsImlucHV0UmFuZ2UiLCJvdXRwdXRSYW5nZSIsImV4dHJhcG9sYXRlIiwiZXh0cmFwb2xhdGVSaWdodCIsImV4dHJhcG9sYXRlTGVmdCIsIl9kZWZhdWx0IiwiZXhwb3J0cyIsInN0eWxlIiwiYWxsb3dJbnRlcnBvbGF0aW9uUGFyYW0iLCJwYXJhbSIsImFsbG93U3R5bGVQcm9wIiwicHJvcCIsImFsbG93VHJhbnNmb3JtUHJvcCIsImlzU3VwcG9ydGVkQ29sb3JTdHlsZVByb3AiLCJpc1N1cHBvcnRlZEludGVycG9sYXRpb25QYXJhbSIsImlzU3VwcG9ydGVkU3R5bGVQcm9wIiwiaXNTdXBwb3J0ZWRUcmFuc2Zvcm1Qcm9wIl0sInNvdXJjZXMiOlsiTmF0aXZlQW5pbWF0ZWRBbGxvd2xpc3QuanMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBDb3B5cmlnaHQgKGMpIE1ldGEgUGxhdGZvcm1zLCBJbmMuIGFuZCBhZmZpbGlhdGVzLlxuICpcbiAqIFRoaXMgc291cmNlIGNvZGUgaXMgbGljZW5zZWQgdW5kZXIgdGhlIE1JVCBsaWNlbnNlIGZvdW5kIGluIHRoZVxuICogTElDRU5TRSBmaWxlIGluIHRoZSByb290IGRpcmVjdG9yeSBvZiB0aGlzIHNvdXJjZSB0cmVlLlxuICpcbiAqIEBmbG93IHN0cmljdC1sb2NhbFxuICogQGZvcm1hdFxuICovXG5cbmltcG9ydCB0eXBlIHtBbmltYXRlZFByb3BzQWxsb3dsaXN0fSBmcm9tICcuL25vZGVzL0FuaW1hdGVkUHJvcHMnO1xuXG5pbXBvcnQgKiBhcyBSZWFjdE5hdGl2ZUZlYXR1cmVGbGFncyBmcm9tICcuLi8uLi9zcmMvcHJpdmF0ZS9mZWF0dXJlZmxhZ3MvUmVhY3ROYXRpdmVGZWF0dXJlRmxhZ3MnO1xuXG4vKipcbiAqIFN0eWxlcyBhbGxvd2VkIGJ5IHRoZSBuYXRpdmUgYW5pbWF0ZWQgaW1wbGVtZW50YXRpb24uXG4gKlxuICogSW4gZ2VuZXJhbCBuYXRpdmUgYW5pbWF0ZWQgaW1wbGVtZW50YXRpb24gc2hvdWxkIHN1cHBvcnQgYW55IG51bWVyaWMgb3IgY29sb3IgcHJvcGVydHkgdGhhdFxuICogZG9lc24ndCBuZWVkIHRvIGJlIHVwZGF0ZWQgdGhyb3VnaCB0aGUgc2hhZG93IHZpZXcgaGllcmFyY2h5IChhbGwgbm9uLWxheW91dCBwcm9wZXJ0aWVzKS5cbiAqL1xuY29uc3QgU1VQUE9SVEVEX0NPTE9SX1NUWUxFUzoge1tzdHJpbmddOiB0cnVlfSA9IHtcbiAgYmFja2dyb3VuZENvbG9yOiB0cnVlLFxuICBib3JkZXJCb3R0b21Db2xvcjogdHJ1ZSxcbiAgYm9yZGVyQ29sb3I6IHRydWUsXG4gIGJvcmRlckVuZENvbG9yOiB0cnVlLFxuICBib3JkZXJMZWZ0Q29sb3I6IHRydWUsXG4gIGJvcmRlclJpZ2h0Q29sb3I6IHRydWUsXG4gIGJvcmRlclN0YXJ0Q29sb3I6IHRydWUsXG4gIGJvcmRlclRvcENvbG9yOiB0cnVlLFxuICBjb2xvcjogdHJ1ZSxcbiAgdGludENvbG9yOiB0cnVlLFxufTtcblxuY29uc3QgU1VQUE9SVEVEX1NUWUxFUzoge1tzdHJpbmddOiB0cnVlfSA9IHtcbiAgLi4uU1VQUE9SVEVEX0NPTE9SX1NUWUxFUyxcbiAgYm9yZGVyQm90dG9tRW5kUmFkaXVzOiB0cnVlLFxuICBib3JkZXJCb3R0b21MZWZ0UmFkaXVzOiB0cnVlLFxuICBib3JkZXJCb3R0b21SaWdodFJhZGl1czogdHJ1ZSxcbiAgYm9yZGVyQm90dG9tU3RhcnRSYWRpdXM6IHRydWUsXG4gIGJvcmRlckVuZEVuZFJhZGl1czogdHJ1ZSxcbiAgYm9yZGVyRW5kU3RhcnRSYWRpdXM6IHRydWUsXG4gIGJvcmRlclJhZGl1czogdHJ1ZSxcbiAgYm9yZGVyVG9wRW5kUmFkaXVzOiB0cnVlLFxuICBib3JkZXJUb3BMZWZ0UmFkaXVzOiB0cnVlLFxuICBib3JkZXJUb3BSaWdodFJhZGl1czogdHJ1ZSxcbiAgYm9yZGVyVG9wU3RhcnRSYWRpdXM6IHRydWUsXG4gIGJvcmRlclN0YXJ0RW5kUmFkaXVzOiB0cnVlLFxuICBib3JkZXJTdGFydFN0YXJ0UmFkaXVzOiB0cnVlLFxuICBlbGV2YXRpb246IHRydWUsXG4gIG9wYWNpdHk6IHRydWUsXG4gIHRyYW5zZm9ybTogdHJ1ZSxcbiAgekluZGV4OiB0cnVlLFxuICAvKiBpb3Mgc3R5bGVzICovXG4gIHNoYWRvd09wYWNpdHk6IHRydWUsXG4gIHNoYWRvd1JhZGl1czogdHJ1ZSxcbiAgLyogbGVnYWN5IGFuZHJvaWQgdHJhbnNmb3JtIHByb3BlcnRpZXMgKi9cbiAgc2NhbGVYOiB0cnVlLFxuICBzY2FsZVk6IHRydWUsXG4gIHRyYW5zbGF0ZVg6IHRydWUsXG4gIHRyYW5zbGF0ZVk6IHRydWUsXG59O1xuXG5jb25zdCBTVVBQT1JURURfVFJBTlNGT1JNUzoge1tzdHJpbmddOiB0cnVlfSA9IHtcbiAgdHJhbnNsYXRlWDogdHJ1ZSxcbiAgdHJhbnNsYXRlWTogdHJ1ZSxcbiAgc2NhbGU6IHRydWUsXG4gIHNjYWxlWDogdHJ1ZSxcbiAgc2NhbGVZOiB0cnVlLFxuICByb3RhdGU6IHRydWUsXG4gIHJvdGF0ZVg6IHRydWUsXG4gIHJvdGF0ZVk6IHRydWUsXG4gIHJvdGF0ZVo6IHRydWUsXG4gIHBlcnNwZWN0aXZlOiB0cnVlLFxuICBza2V3WDogdHJ1ZSxcbiAgc2tld1k6IHRydWUsXG4gIC4uLihSZWFjdE5hdGl2ZUZlYXR1cmVGbGFncy5zaG91bGRVc2VBbmltYXRlZE9iamVjdEZvclRyYW5zZm9ybSgpXG4gICAgPyB7bWF0cml4OiB0cnVlfVxuICAgIDoge30pLFxufTtcblxuY29uc3QgU1VQUE9SVEVEX0lOVEVSUE9MQVRJT05fUEFSQU1TOiB7W3N0cmluZ106IHRydWV9ID0ge1xuICBpbnB1dFJhbmdlOiB0cnVlLFxuICBvdXRwdXRSYW5nZTogdHJ1ZSxcbiAgZXh0cmFwb2xhdGU6IHRydWUsXG4gIGV4dHJhcG9sYXRlUmlnaHQ6IHRydWUsXG4gIGV4dHJhcG9sYXRlTGVmdDogdHJ1ZSxcbn07XG5cbi8qKlxuICogRGVmYXVsdCBhbGxvd2xpc3QgZm9yIGNvbXBvbmVudCBwcm9wcyB0aGF0IHN1cHBvcnQgbmF0aXZlIGFuaW1hdGVkIHZhbHVlcy5cbiAqL1xuZXhwb3J0IGRlZmF1bHQge1xuICBzdHlsZTogU1VQUE9SVEVEX1NUWUxFUyxcbn0gYXMgQW5pbWF0ZWRQcm9wc0FsbG93bGlzdDtcblxuZXhwb3J0IGZ1bmN0aW9uIGFsbG93SW50ZXJwb2xhdGlvblBhcmFtKHBhcmFtOiBzdHJpbmcpOiB2b2lkIHtcbiAgU1VQUE9SVEVEX0lOVEVSUE9MQVRJT05fUEFSQU1TW3BhcmFtXSA9IHRydWU7XG59XG5cbmV4cG9ydCBmdW5jdGlvbiBhbGxvd1N0eWxlUHJvcChwcm9wOiBzdHJpbmcpOiB2b2lkIHtcbiAgU1VQUE9SVEVEX1NUWUxFU1twcm9wXSA9IHRydWU7XG59XG5cbmV4cG9ydCBmdW5jdGlvbiBhbGxvd1RyYW5zZm9ybVByb3AocHJvcDogc3RyaW5nKTogdm9pZCB7XG4gIFNVUFBPUlRFRF9UUkFOU0ZPUk1TW3Byb3BdID0gdHJ1ZTtcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIGlzU3VwcG9ydGVkQ29sb3JTdHlsZVByb3AocHJvcDogc3RyaW5nKTogYm9vbGVhbiB7XG4gIHJldHVybiBTVVBQT1JURURfQ09MT1JfU1RZTEVTLmhhc093blByb3BlcnR5KHByb3ApO1xufVxuXG5leHBvcnQgZnVuY3Rpb24gaXNTdXBwb3J0ZWRJbnRlcnBvbGF0aW9uUGFyYW0ocGFyYW06IHN0cmluZyk6IGJvb2xlYW4ge1xuICByZXR1cm4gU1VQUE9SVEVEX0lOVEVSUE9MQVRJT05fUEFSQU1TLmhhc093blByb3BlcnR5KHBhcmFtKTtcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIGlzU3VwcG9ydGVkU3R5bGVQcm9wKHByb3A6IHN0cmluZyk6IGJvb2xlYW4ge1xuICByZXR1cm4gU1VQUE9SVEVEX1NUWUxFUy5oYXNPd25Qcm9wZXJ0eShwcm9wKTtcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIGlzU3VwcG9ydGVkVHJhbnNmb3JtUHJvcChwcm9wOiBzdHJpbmcpOiBib29sZWFuIHtcbiAgcmV0dXJuIFNVUFBPUlRFRF9UUkFOU0ZPUk1TLmhhc093blByb3BlcnR5KHByb3ApO1xufVxuIl0sIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQVlBLElBQUFBLHVCQUFBLEdBQUFDLHVCQUFBLENBQUFDLE9BQUE7QUFBa0csU0FBQUMseUJBQUFDLENBQUEsNkJBQUFDLE9BQUEsbUJBQUFDLENBQUEsT0FBQUQsT0FBQSxJQUFBRSxDQUFBLE9BQUFGLE9BQUEsWUFBQUYsd0JBQUEsWUFBQUEseUJBQUFDLENBQUEsV0FBQUEsQ0FBQSxHQUFBRyxDQUFBLEdBQUFELENBQUEsS0FBQUYsQ0FBQTtBQUFBLFNBQUFILHdCQUFBRyxDQUFBLEVBQUFFLENBQUEsU0FBQUEsQ0FBQSxJQUFBRixDQUFBLElBQUFBLENBQUEsQ0FBQUksVUFBQSxTQUFBSixDQUFBLGVBQUFBLENBQUEsdUJBQUFBLENBQUEseUJBQUFBLENBQUEsV0FBQUssT0FBQSxFQUFBTCxDQUFBLFFBQUFHLENBQUEsR0FBQUosd0JBQUEsQ0FBQUcsQ0FBQSxPQUFBQyxDQUFBLElBQUFBLENBQUEsQ0FBQUcsR0FBQSxDQUFBTixDQUFBLFVBQUFHLENBQUEsQ0FBQUksR0FBQSxDQUFBUCxDQUFBLE9BQUFRLENBQUEsS0FBQUMsU0FBQSxVQUFBQyxDQUFBLEdBQUFDLE1BQUEsQ0FBQUMsY0FBQSxJQUFBRCxNQUFBLENBQUFFLHdCQUFBLFdBQUFDLENBQUEsSUFBQWQsQ0FBQSxvQkFBQWMsQ0FBQSxPQUFBQyxjQUFBLENBQUFDLElBQUEsQ0FBQWhCLENBQUEsRUFBQWMsQ0FBQSxTQUFBRyxDQUFBLEdBQUFQLENBQUEsR0FBQUMsTUFBQSxDQUFBRSx3QkFBQSxDQUFBYixDQUFBLEVBQUFjLENBQUEsVUFBQUcsQ0FBQSxLQUFBQSxDQUFBLENBQUFWLEdBQUEsSUFBQVUsQ0FBQSxDQUFBQyxHQUFBLElBQUFQLE1BQUEsQ0FBQUMsY0FBQSxDQUFBSixDQUFBLEVBQUFNLENBQUEsRUFBQUcsQ0FBQSxJQUFBVCxDQUFBLENBQUFNLENBQUEsSUFBQWQsQ0FBQSxDQUFBYyxDQUFBLFlBQUFOLENBQUEsQ0FBQUgsT0FBQSxHQUFBTCxDQUFBLEVBQUFHLENBQUEsSUFBQUEsQ0FBQSxDQUFBZSxHQUFBLENBQUFsQixDQUFBLEVBQUFRLENBQUEsR0FBQUEsQ0FBQTtBQVFsRyxJQUFNVyxzQkFBd0MsR0FBRztFQUMvQ0MsZUFBZSxFQUFFLElBQUk7RUFDckJDLGlCQUFpQixFQUFFLElBQUk7RUFDdkJDLFdBQVcsRUFBRSxJQUFJO0VBQ2pCQyxjQUFjLEVBQUUsSUFBSTtFQUNwQkMsZUFBZSxFQUFFLElBQUk7RUFDckJDLGdCQUFnQixFQUFFLElBQUk7RUFDdEJDLGdCQUFnQixFQUFFLElBQUk7RUFDdEJDLGNBQWMsRUFBRSxJQUFJO0VBQ3BCQyxLQUFLLEVBQUUsSUFBSTtFQUNYQyxTQUFTLEVBQUU7QUFDYixDQUFDO0FBRUQsSUFBTUMsZ0JBQWtDLEdBQUFuQixNQUFBLENBQUFvQixNQUFBLEtBQ25DWixzQkFBc0I7RUFDekJhLHFCQUFxQixFQUFFLElBQUk7RUFDM0JDLHNCQUFzQixFQUFFLElBQUk7RUFDNUJDLHVCQUF1QixFQUFFLElBQUk7RUFDN0JDLHVCQUF1QixFQUFFLElBQUk7RUFDN0JDLGtCQUFrQixFQUFFLElBQUk7RUFDeEJDLG9CQUFvQixFQUFFLElBQUk7RUFDMUJDLFlBQVksRUFBRSxJQUFJO0VBQ2xCQyxrQkFBa0IsRUFBRSxJQUFJO0VBQ3hCQyxtQkFBbUIsRUFBRSxJQUFJO0VBQ3pCQyxvQkFBb0IsRUFBRSxJQUFJO0VBQzFCQyxvQkFBb0IsRUFBRSxJQUFJO0VBQzFCQyxvQkFBb0IsRUFBRSxJQUFJO0VBQzFCQyxzQkFBc0IsRUFBRSxJQUFJO0VBQzVCQyxTQUFTLEVBQUUsSUFBSTtFQUNmQyxPQUFPLEVBQUUsSUFBSTtFQUNiQyxTQUFTLEVBQUUsSUFBSTtFQUNmQyxNQUFNLEVBQUUsSUFBSTtFQUVaQyxhQUFhLEVBQUUsSUFBSTtFQUNuQkMsWUFBWSxFQUFFLElBQUk7RUFFbEJDLE1BQU0sRUFBRSxJQUFJO0VBQ1pDLE1BQU0sRUFBRSxJQUFJO0VBQ1pDLFVBQVUsRUFBRSxJQUFJO0VBQ2hCQyxVQUFVLEVBQUU7QUFBSSxFQUNqQjtBQUVELElBQU1DLG9CQUFzQyxHQUFBNUMsTUFBQSxDQUFBb0IsTUFBQTtFQUMxQ3NCLFVBQVUsRUFBRSxJQUFJO0VBQ2hCQyxVQUFVLEVBQUUsSUFBSTtFQUNoQkUsS0FBSyxFQUFFLElBQUk7RUFDWEwsTUFBTSxFQUFFLElBQUk7RUFDWkMsTUFBTSxFQUFFLElBQUk7RUFDWkssTUFBTSxFQUFFLElBQUk7RUFDWkMsT0FBTyxFQUFFLElBQUk7RUFDYkMsT0FBTyxFQUFFLElBQUk7RUFDYkMsT0FBTyxFQUFFLElBQUk7RUFDYkMsV0FBVyxFQUFFLElBQUk7RUFDakJDLEtBQUssRUFBRSxJQUFJO0VBQ1hDLEtBQUssRUFBRTtBQUFJLEdBQ1BuRSx1QkFBdUIsQ0FBQ29FLG1DQUFtQyxDQUFDLENBQUMsR0FDN0Q7RUFBQ0MsTUFBTSxFQUFFO0FBQUksQ0FBQyxHQUNkLENBQUMsQ0FBQyxDQUNQO0FBRUQsSUFBTUMsOEJBQWdELEdBQUc7RUFDdkRDLFVBQVUsRUFBRSxJQUFJO0VBQ2hCQyxXQUFXLEVBQUUsSUFBSTtFQUNqQkMsV0FBVyxFQUFFLElBQUk7RUFDakJDLGdCQUFnQixFQUFFLElBQUk7RUFDdEJDLGVBQWUsRUFBRTtBQUNuQixDQUFDO0FBQUMsSUFBQUMsUUFBQSxHQUFBQyxPQUFBLENBQUFwRSxPQUFBLEdBS2E7RUFDYnFFLEtBQUssRUFBRTVDO0FBQ1QsQ0FBQztBQUVNLFNBQVM2Qyx1QkFBdUJBLENBQUNDLEtBQWEsRUFBUTtFQUMzRFYsOEJBQThCLENBQUNVLEtBQUssQ0FBQyxHQUFHLElBQUk7QUFDOUM7QUFFTyxTQUFTQyxjQUFjQSxDQUFDQyxJQUFZLEVBQVE7RUFDakRoRCxnQkFBZ0IsQ0FBQ2dELElBQUksQ0FBQyxHQUFHLElBQUk7QUFDL0I7QUFFTyxTQUFTQyxrQkFBa0JBLENBQUNELElBQVksRUFBUTtFQUNyRHZCLG9CQUFvQixDQUFDdUIsSUFBSSxDQUFDLEdBQUcsSUFBSTtBQUNuQztBQUVPLFNBQVNFLHlCQUF5QkEsQ0FBQ0YsSUFBWSxFQUFXO0VBQy9ELE9BQU8zRCxzQkFBc0IsQ0FBQ0osY0FBYyxDQUFDK0QsSUFBSSxDQUFDO0FBQ3BEO0FBRU8sU0FBU0csNkJBQTZCQSxDQUFDTCxLQUFhLEVBQVc7RUFDcEUsT0FBT1YsOEJBQThCLENBQUNuRCxjQUFjLENBQUM2RCxLQUFLLENBQUM7QUFDN0Q7QUFFTyxTQUFTTSxvQkFBb0JBLENBQUNKLElBQVksRUFBVztFQUMxRCxPQUFPaEQsZ0JBQWdCLENBQUNmLGNBQWMsQ0FBQytELElBQUksQ0FBQztBQUM5QztBQUVPLFNBQVNLLHdCQUF3QkEsQ0FBQ0wsSUFBWSxFQUFXO0VBQzlELE9BQU92QixvQkFBb0IsQ0FBQ3hDLGNBQWMsQ0FBQytELElBQUksQ0FBQztBQUNsRCIsImlnbm9yZUxpc3QiOltdfQ==