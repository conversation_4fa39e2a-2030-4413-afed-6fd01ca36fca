{"version": 3, "names": ["cov_1e8259jsib", "actualCoverage", "s", "exports", "mapGetBillDetailResponseToModel", "GetBillDetailModel_1", "require", "response", "f", "GetBillDetailModel", "billCode", "service", "b", "mapBillDetailServiceResponseToModel", "undefined", "queryRef", "customerInfo", "mapBillDetailCustomerInfoResponseToModel", "billList", "map", "mapBillDetailBillResponseToModel", "partnerRespCode", "tranSeqCount", "partnerRespDesc", "partnerTraceSeq", "result", "extendData", "paymentRule", "BillDetailBillModel", "id", "no", "amount", "code", "custCode", "custName", "period", "fee", "custAddress", "BillDetailCustomerInfoModel", "cif", "phone", "acct", "name", "address", "BillDetailServiceModel"], "sources": ["/Users/<USER>/projects/react-native/payment-module/src/data/mappers/get-bill-detail/GetBillDetailMapper.ts"], "sourcesContent": ["import {\n  BillDetailBillResponse,\n  BillDetailCustomerInfoResponse,\n  BillDetailServiceResponse,\n  GetBillDetailResponse,\n} from '../../models/get-bill-detail/GetBillDetailResponse';\nimport {\n  BillDetailBillModel,\n  BillDetailCustomerInfoModel,\n  BillDetailServiceModel,\n  GetBillDetailModel,\n} from '../../../domain/entities/get-bill-detail/GetBillDetailModel';\n\nexport function mapGetBillDetailResponseToModel(response: GetBillDetailResponse): GetBillDetailModel {\n  return new GetBillDetailModel(\n    response.billCode,\n    response.service ? mapBillDetailServiceResponseToModel(response.service) : undefined,\n    response.queryRef,\n    response.customerInfo ? mapBillDetailCustomerInfoResponseToModel(response.customerInfo) : undefined,\n    response.billList ? response.billList.map(mapBillDetailBillResponseToModel) : undefined,\n    response.partnerRespCode,\n    response.tranSeqCount,\n    response.partnerRespDesc,\n    response.partnerTraceSeq,\n    response.result,\n    response.extendData,\n    response.paymentRule,\n  );\n}\n\nfunction mapBillDetailBillResponseToModel(response: BillDetailBillResponse): BillDetailBillModel {\n  return new BillDetailBillModel(\n    response.id,\n    response.no,\n    response.amount,\n    response.code,\n    response.custCode,\n    response.custName,\n    response.period,\n    response.fee,\n    response.custAddress,\n  );\n}\n\nfunction mapBillDetailCustomerInfoResponseToModel(\n  response: BillDetailCustomerInfoResponse,\n): BillDetailCustomerInfoModel {\n  return new BillDetailCustomerInfoModel(response.cif, response.phone, response.acct, response.name, response.address);\n}\n\nfunction mapBillDetailServiceResponseToModel(response: BillDetailServiceResponse): BillDetailServiceModel {\n  return new BillDetailServiceModel(response.code);\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAgDA;IAAAA,cAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,cAAA;AAAAA,cAAA,GAAAE,CAAA;;;;;;AAnCAC,OAAA,CAAAC,+BAAA,GAAAA,+BAAA;AAPA,IAAAC,oBAAA;AAAA;AAAA,CAAAL,cAAA,GAAAE,CAAA,OAAAI,OAAA;AAOA,SAAgBF,+BAA+BA,CAACG,QAA+B;EAAA;EAAAP,cAAA,GAAAQ,CAAA;EAAAR,cAAA,GAAAE,CAAA;EAC7E,OAAO,IAAIG,oBAAA,CAAAI,kBAAkB,CAC3BF,QAAQ,CAACG,QAAQ,EACjBH,QAAQ,CAACI,OAAO;EAAA;EAAA,CAAAX,cAAA,GAAAY,CAAA,UAAGC,mCAAmC,CAACN,QAAQ,CAACI,OAAO,CAAC;EAAA;EAAA,CAAAX,cAAA,GAAAY,CAAA,UAAGE,SAAS,GACpFP,QAAQ,CAACQ,QAAQ,EACjBR,QAAQ,CAACS,YAAY;EAAA;EAAA,CAAAhB,cAAA,GAAAY,CAAA,UAAGK,wCAAwC,CAACV,QAAQ,CAACS,YAAY,CAAC;EAAA;EAAA,CAAAhB,cAAA,GAAAY,CAAA,UAAGE,SAAS,GACnGP,QAAQ,CAACW,QAAQ;EAAA;EAAA,CAAAlB,cAAA,GAAAY,CAAA,UAAGL,QAAQ,CAACW,QAAQ,CAACC,GAAG,CAACC,gCAAgC,CAAC;EAAA;EAAA,CAAApB,cAAA,GAAAY,CAAA,UAAGE,SAAS,GACvFP,QAAQ,CAACc,eAAe,EACxBd,QAAQ,CAACe,YAAY,EACrBf,QAAQ,CAACgB,eAAe,EACxBhB,QAAQ,CAACiB,eAAe,EACxBjB,QAAQ,CAACkB,MAAM,EACflB,QAAQ,CAACmB,UAAU,EACnBnB,QAAQ,CAACoB,WAAW,CACrB;AACH;AAEA,SAASP,gCAAgCA,CAACb,QAAgC;EAAA;EAAAP,cAAA,GAAAQ,CAAA;EAAAR,cAAA,GAAAE,CAAA;EACxE,OAAO,IAAIG,oBAAA,CAAAuB,mBAAmB,CAC5BrB,QAAQ,CAACsB,EAAE,EACXtB,QAAQ,CAACuB,EAAE,EACXvB,QAAQ,CAACwB,MAAM,EACfxB,QAAQ,CAACyB,IAAI,EACbzB,QAAQ,CAAC0B,QAAQ,EACjB1B,QAAQ,CAAC2B,QAAQ,EACjB3B,QAAQ,CAAC4B,MAAM,EACf5B,QAAQ,CAAC6B,GAAG,EACZ7B,QAAQ,CAAC8B,WAAW,CACrB;AACH;AAEA,SAASpB,wCAAwCA,CAC/CV,QAAwC;EAAA;EAAAP,cAAA,GAAAQ,CAAA;EAAAR,cAAA,GAAAE,CAAA;EAExC,OAAO,IAAIG,oBAAA,CAAAiC,2BAA2B,CAAC/B,QAAQ,CAACgC,GAAG,EAAEhC,QAAQ,CAACiC,KAAK,EAAEjC,QAAQ,CAACkC,IAAI,EAAElC,QAAQ,CAACmC,IAAI,EAAEnC,QAAQ,CAACoC,OAAO,CAAC;AACtH;AAEA,SAAS9B,mCAAmCA,CAACN,QAAmC;EAAA;EAAAP,cAAA,GAAAQ,CAAA;EAAAR,cAAA,GAAAE,CAAA;EAC9E,OAAO,IAAIG,oBAAA,CAAAuC,sBAAsB,CAACrC,QAAQ,CAACyB,IAAI,CAAC;AAClD", "ignoreList": []}