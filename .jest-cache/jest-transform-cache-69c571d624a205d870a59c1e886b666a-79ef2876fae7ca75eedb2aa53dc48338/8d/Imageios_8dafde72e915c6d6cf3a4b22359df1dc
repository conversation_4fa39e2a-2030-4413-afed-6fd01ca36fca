0005a55913aba849620d4e7aa9d14f55
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
var _objectWithoutProperties2 = _interopRequireDefault(require("@babel/runtime/helpers/objectWithoutProperties"));
var _asyncToGenerator2 = _interopRequireDefault(require("@babel/runtime/helpers/asyncToGenerator"));
var _slicedToArray2 = _interopRequireDefault(require("@babel/runtime/helpers/slicedToArray"));
var _RootTag = require("../ReactNative/RootTag");
var _flattenStyle = _interopRequireDefault(require("../StyleSheet/flattenStyle"));
var _StyleSheet = _interopRequireDefault(require("../StyleSheet/StyleSheet"));
var _ImageAnalyticsTagContext = _interopRequireDefault(require("./ImageAnalyticsTagContext"));
var _ImageInjection = require("./ImageInjection");
var _ImageSourceUtils = require("./ImageSourceUtils");
var _ImageUtils = require("./ImageUtils");
var _ImageViewNativeComponent = _interopRequireDefault(require("./ImageViewNativeComponent"));
var _NativeImageLoaderIOS = _interopRequireDefault(require("./NativeImageLoaderIOS"));
var _resolveAssetSource = _interopRequireDefault(require("./resolveAssetSource"));
var React = _interopRequireWildcard(require("react"));
var _jsxRuntime = require("react/jsx-runtime");
var _excluded = ["aria-busy", "aria-checked", "aria-disabled", "aria-expanded", "aria-selected", "src"];
function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
function getSize(uri, success, failure) {
  var promise = _NativeImageLoaderIOS.default.getSize(uri).then(function (_ref) {
    var _ref2 = (0, _slicedToArray2.default)(_ref, 2),
      width = _ref2[0],
      height = _ref2[1];
    return {
      width: width,
      height: height
    };
  });
  if (typeof success !== 'function') {
    return promise;
  }
  promise.then(function (sizes) {
    return success(sizes.width, sizes.height);
  }).catch(failure || function () {
    console.warn('Failed to get size for image: ' + uri);
  });
}
function getSizeWithHeaders(uri, headers, success, failure) {
  var promise = _NativeImageLoaderIOS.default.getSizeWithHeaders(uri, headers);
  if (typeof success !== 'function') {
    return promise;
  }
  promise.then(function (sizes) {
    return success(sizes.width, sizes.height);
  }).catch(failure || function () {
    console.warn('Failed to get size for image: ' + uri);
  });
}
function prefetchWithMetadata(url, queryRootName, rootTag) {
  if (_NativeImageLoaderIOS.default.prefetchImageWithMetadata) {
    return _NativeImageLoaderIOS.default.prefetchImageWithMetadata(url, queryRootName, rootTag != null ? rootTag : (0, _RootTag.createRootTag)(0));
  } else {
    return _NativeImageLoaderIOS.default.prefetchImage(url);
  }
}
function prefetch(url) {
  return _NativeImageLoaderIOS.default.prefetchImage(url);
}
function queryCache(_x) {
  return _queryCache.apply(this, arguments);
}
function _queryCache() {
  _queryCache = (0, _asyncToGenerator2.default)(function* (urls) {
    return _NativeImageLoaderIOS.default.queryCache(urls);
  });
  return _queryCache.apply(this, arguments);
}
var BaseImage = React.forwardRef(function (props, forwardedRef) {
  var _props$tintColor, _props$accessibilityS, _props$accessibilityS2, _props$accessibilityS3, _props$accessibilityS4, _props$accessibilityS5, _props$ariaLabel;
  var source = (0, _ImageSourceUtils.getImageSourcesFromImageProps)(props) || {
    uri: undefined,
    width: undefined,
    height: undefined
  };
  var style;
  var sources;
  if (Array.isArray(source)) {
    style = [styles.base, props.style];
    sources = source;
  } else {
    var _source$width, _source$height;
    var uri = source.uri;
    if (uri === '') {
      console.warn('source.uri should not be an empty string');
    }
    var _width = (_source$width = source.width) != null ? _source$width : props.width;
    var _height = (_source$height = source.height) != null ? _source$height : props.height;
    style = [{
      width: _width,
      height: _height
    }, styles.base, props.style];
    sources = [source];
  }
  var flattenedStyle = (0, _flattenStyle.default)(style);
  var objectFit = (0, _ImageUtils.convertObjectFitToResizeMode)(flattenedStyle == null ? void 0 : flattenedStyle.objectFit);
  var resizeMode = objectFit || props.resizeMode || (flattenedStyle == null ? void 0 : flattenedStyle.resizeMode) || 'cover';
  var tintColor = (_props$tintColor = props.tintColor) != null ? _props$tintColor : flattenedStyle == null ? void 0 : flattenedStyle.tintColor;
  if (props.children != null) {
    throw new Error('The <Image> component cannot contain children. If you want to render content on top of the image, consider using the <ImageBackground> component or absolute positioning.');
  }
  var ariaBusy = props['aria-busy'],
    ariaChecked = props['aria-checked'],
    ariaDisabled = props['aria-disabled'],
    ariaExpanded = props['aria-expanded'],
    ariaSelected = props['aria-selected'],
    src = props.src,
    restProps = (0, _objectWithoutProperties2.default)(props, _excluded);
  var _accessibilityState = {
    busy: ariaBusy != null ? ariaBusy : (_props$accessibilityS = props.accessibilityState) == null ? void 0 : _props$accessibilityS.busy,
    checked: ariaChecked != null ? ariaChecked : (_props$accessibilityS2 = props.accessibilityState) == null ? void 0 : _props$accessibilityS2.checked,
    disabled: ariaDisabled != null ? ariaDisabled : (_props$accessibilityS3 = props.accessibilityState) == null ? void 0 : _props$accessibilityS3.disabled,
    expanded: ariaExpanded != null ? ariaExpanded : (_props$accessibilityS4 = props.accessibilityState) == null ? void 0 : _props$accessibilityS4.expanded,
    selected: ariaSelected != null ? ariaSelected : (_props$accessibilityS5 = props.accessibilityState) == null ? void 0 : _props$accessibilityS5.selected
  };
  var accessibilityLabel = (_props$ariaLabel = props['aria-label']) != null ? _props$ariaLabel : props.accessibilityLabel;
  var actualRef = (0, _ImageInjection.useWrapRefWithImageAttachedCallbacks)(forwardedRef);
  return (0, _jsxRuntime.jsx)(_ImageAnalyticsTagContext.default.Consumer, {
    children: function children(analyticTag) {
      return (0, _jsxRuntime.jsx)(_ImageViewNativeComponent.default, Object.assign({
        accessibilityState: _accessibilityState
      }, restProps, {
        accessible: props.alt !== undefined ? true : props.accessible,
        accessibilityLabel: accessibilityLabel != null ? accessibilityLabel : props.alt,
        ref: actualRef,
        style: style,
        resizeMode: resizeMode,
        tintColor: tintColor,
        source: sources,
        internal_analyticTag: analyticTag
      }));
    }
  });
});
var imageComponentDecorator = (0, _ImageInjection.unstable_getImageComponentDecorator)();
if (imageComponentDecorator != null) {
  BaseImage = imageComponentDecorator(BaseImage);
}
var Image = BaseImage;
Image.displayName = 'Image';
Image.getSize = getSize;
Image.getSizeWithHeaders = getSizeWithHeaders;
Image.prefetch = prefetch;
Image.prefetchWithMetadata = prefetchWithMetadata;
Image.queryCache = queryCache;
Image.resolveAssetSource = _resolveAssetSource.default;
var styles = _StyleSheet.default.create({
  base: {
    overflow: 'hidden'
  }
});
module.exports = Image;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJfUm9vdFRhZyIsInJlcXVpcmUiLCJfZmxhdHRlblN0eWxlIiwiX2ludGVyb3BSZXF1aXJlRGVmYXVsdCIsIl9TdHlsZVNoZWV0IiwiX0ltYWdlQW5hbHl0aWNzVGFnQ29udGV4dCIsIl9JbWFnZUluamVjdGlvbiIsIl9JbWFnZVNvdXJjZVV0aWxzIiwiX0ltYWdlVXRpbHMiLCJfSW1hZ2VWaWV3TmF0aXZlQ29tcG9uZW50IiwiX05hdGl2ZUltYWdlTG9hZGVySU9TIiwiX3Jlc29sdmVBc3NldFNvdXJjZSIsIlJlYWN0IiwiX2ludGVyb3BSZXF1aXJlV2lsZGNhcmQiLCJfanN4UnVudGltZSIsIl9leGNsdWRlZCIsIl9nZXRSZXF1aXJlV2lsZGNhcmRDYWNoZSIsImUiLCJXZWFrTWFwIiwiciIsInQiLCJfX2VzTW9kdWxlIiwiZGVmYXVsdCIsImhhcyIsImdldCIsIm4iLCJfX3Byb3RvX18iLCJhIiwiT2JqZWN0IiwiZGVmaW5lUHJvcGVydHkiLCJnZXRPd25Qcm9wZXJ0eURlc2NyaXB0b3IiLCJ1IiwiaGFzT3duUHJvcGVydHkiLCJjYWxsIiwiaSIsInNldCIsImdldFNpemUiLCJ1cmkiLCJzdWNjZXNzIiwiZmFpbHVyZSIsInByb21pc2UiLCJOYXRpdmVJbWFnZUxvYWRlcklPUyIsInRoZW4iLCJfcmVmIiwiX3JlZjIiLCJfc2xpY2VkVG9BcnJheTIiLCJ3aWR0aCIsImhlaWdodCIsInNpemVzIiwiY2F0Y2giLCJjb25zb2xlIiwid2FybiIsImdldFNpemVXaXRoSGVhZGVycyIsImhlYWRlcnMiLCJwcmVmZXRjaFdpdGhNZXRhZGF0YSIsInVybCIsInF1ZXJ5Um9vdE5hbWUiLCJyb290VGFnIiwicHJlZmV0Y2hJbWFnZVdpdGhNZXRhZGF0YSIsImNyZWF0ZVJvb3RUYWciLCJwcmVmZXRjaEltYWdlIiwicHJlZmV0Y2giLCJxdWVyeUNhY2hlIiwiX3giLCJfcXVlcnlDYWNoZSIsImFwcGx5IiwiYXJndW1lbnRzIiwiX2FzeW5jVG9HZW5lcmF0b3IyIiwidXJscyIsIkJhc2VJbWFnZSIsImZvcndhcmRSZWYiLCJwcm9wcyIsImZvcndhcmRlZFJlZiIsIl9wcm9wcyR0aW50Q29sb3IiLCJfcHJvcHMkYWNjZXNzaWJpbGl0eVMiLCJfcHJvcHMkYWNjZXNzaWJpbGl0eVMyIiwiX3Byb3BzJGFjY2Vzc2liaWxpdHlTMyIsIl9wcm9wcyRhY2Nlc3NpYmlsaXR5UzQiLCJfcHJvcHMkYWNjZXNzaWJpbGl0eVM1IiwiX3Byb3BzJGFyaWFMYWJlbCIsInNvdXJjZSIsImdldEltYWdlU291cmNlc0Zyb21JbWFnZVByb3BzIiwidW5kZWZpbmVkIiwic3R5bGUiLCJzb3VyY2VzIiwiQXJyYXkiLCJpc0FycmF5Iiwic3R5bGVzIiwiYmFzZSIsIl9zb3VyY2Ukd2lkdGgiLCJfc291cmNlJGhlaWdodCIsImZsYXR0ZW5lZFN0eWxlIiwiZmxhdHRlblN0eWxlIiwib2JqZWN0Rml0IiwiY29udmVydE9iamVjdEZpdFRvUmVzaXplTW9kZSIsInJlc2l6ZU1vZGUiLCJ0aW50Q29sb3IiLCJjaGlsZHJlbiIsIkVycm9yIiwiYXJpYUJ1c3kiLCJhcmlhQ2hlY2tlZCIsImFyaWFEaXNhYmxlZCIsImFyaWFFeHBhbmRlZCIsImFyaWFTZWxlY3RlZCIsInNyYyIsInJlc3RQcm9wcyIsIl9vYmplY3RXaXRob3V0UHJvcGVydGllczIiLCJfYWNjZXNzaWJpbGl0eVN0YXRlIiwiYnVzeSIsImFjY2Vzc2liaWxpdHlTdGF0ZSIsImNoZWNrZWQiLCJkaXNhYmxlZCIsImV4cGFuZGVkIiwic2VsZWN0ZWQiLCJhY2Nlc3NpYmlsaXR5TGFiZWwiLCJhY3R1YWxSZWYiLCJ1c2VXcmFwUmVmV2l0aEltYWdlQXR0YWNoZWRDYWxsYmFja3MiLCJqc3giLCJDb25zdW1lciIsImFuYWx5dGljVGFnIiwiYXNzaWduIiwiYWNjZXNzaWJsZSIsImFsdCIsInJlZiIsImludGVybmFsX2FuYWx5dGljVGFnIiwiaW1hZ2VDb21wb25lbnREZWNvcmF0b3IiLCJ1bnN0YWJsZV9nZXRJbWFnZUNvbXBvbmVudERlY29yYXRvciIsIkltYWdlIiwiZGlzcGxheU5hbWUiLCJyZXNvbHZlQXNzZXRTb3VyY2UiLCJTdHlsZVNoZWV0IiwiY3JlYXRlIiwib3ZlcmZsb3ciLCJtb2R1bGUiLCJleHBvcnRzIl0sInNvdXJjZXMiOlsiSW1hZ2UuaW9zLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQ29weXJpZ2h0IChjKSBNZXRhIFBsYXRmb3JtcywgSW5jLiBhbmQgYWZmaWxpYXRlcy5cbiAqXG4gKiBUaGlzIHNvdXJjZSBjb2RlIGlzIGxpY2Vuc2VkIHVuZGVyIHRoZSBNSVQgbGljZW5zZSBmb3VuZCBpbiB0aGVcbiAqIExJQ0VOU0UgZmlsZSBpbiB0aGUgcm9vdCBkaXJlY3Rvcnkgb2YgdGhpcyBzb3VyY2UgdHJlZS5cbiAqXG4gKiBAZmxvdyBzdHJpY3QtbG9jYWxcbiAqIEBmb3JtYXRcbiAqL1xuXG5pbXBvcnQgdHlwZSB7SW1hZ2VTdHlsZVByb3B9IGZyb20gJy4uL1N0eWxlU2hlZXQvU3R5bGVTaGVldCc7XG5pbXBvcnQgdHlwZSB7Um9vdFRhZ30gZnJvbSAnLi4vVHlwZXMvUm9vdFRhZ1R5cGVzJztcbmltcG9ydCB0eXBlIHtBYnN0cmFjdEltYWdlSU9TLCBJbWFnZUlPU30gZnJvbSAnLi9JbWFnZVR5cGVzLmZsb3cnO1xuaW1wb3J0IHR5cGUge0ltYWdlU2l6ZX0gZnJvbSAnLi9OYXRpdmVJbWFnZUxvYWRlckFuZHJvaWQnO1xuXG5pbXBvcnQge2NyZWF0ZVJvb3RUYWd9IGZyb20gJy4uL1JlYWN0TmF0aXZlL1Jvb3RUYWcnO1xuaW1wb3J0IGZsYXR0ZW5TdHlsZSBmcm9tICcuLi9TdHlsZVNoZWV0L2ZsYXR0ZW5TdHlsZSc7XG5pbXBvcnQgU3R5bGVTaGVldCBmcm9tICcuLi9TdHlsZVNoZWV0L1N0eWxlU2hlZXQnO1xuaW1wb3J0IEltYWdlQW5hbHl0aWNzVGFnQ29udGV4dCBmcm9tICcuL0ltYWdlQW5hbHl0aWNzVGFnQ29udGV4dCc7XG5pbXBvcnQge1xuICB1bnN0YWJsZV9nZXRJbWFnZUNvbXBvbmVudERlY29yYXRvcixcbiAgdXNlV3JhcFJlZldpdGhJbWFnZUF0dGFjaGVkQ2FsbGJhY2tzLFxufSBmcm9tICcuL0ltYWdlSW5qZWN0aW9uJztcbmltcG9ydCB7Z2V0SW1hZ2VTb3VyY2VzRnJvbUltYWdlUHJvcHN9IGZyb20gJy4vSW1hZ2VTb3VyY2VVdGlscyc7XG5pbXBvcnQge2NvbnZlcnRPYmplY3RGaXRUb1Jlc2l6ZU1vZGV9IGZyb20gJy4vSW1hZ2VVdGlscyc7XG5pbXBvcnQgSW1hZ2VWaWV3TmF0aXZlQ29tcG9uZW50IGZyb20gJy4vSW1hZ2VWaWV3TmF0aXZlQ29tcG9uZW50JztcbmltcG9ydCBOYXRpdmVJbWFnZUxvYWRlcklPUyBmcm9tICcuL05hdGl2ZUltYWdlTG9hZGVySU9TJztcbmltcG9ydCByZXNvbHZlQXNzZXRTb3VyY2UgZnJvbSAnLi9yZXNvbHZlQXNzZXRTb3VyY2UnO1xuaW1wb3J0ICogYXMgUmVhY3QgZnJvbSAncmVhY3QnO1xuXG5mdW5jdGlvbiBnZXRTaXplKFxuICB1cmk6IHN0cmluZyxcbiAgc3VjY2Vzcz86ICh3aWR0aDogbnVtYmVyLCBoZWlnaHQ6IG51bWJlcikgPT4gdm9pZCxcbiAgZmFpbHVyZT86IChlcnJvcjogbWl4ZWQpID0+IHZvaWQsXG4pOiB2b2lkIHwgUHJvbWlzZTxJbWFnZVNpemU+IHtcbiAgY29uc3QgcHJvbWlzZSA9IE5hdGl2ZUltYWdlTG9hZGVySU9TLmdldFNpemUodXJpKS50aGVuKChbd2lkdGgsIGhlaWdodF0pID0+ICh7XG4gICAgd2lkdGgsXG4gICAgaGVpZ2h0LFxuICB9KSk7XG4gIGlmICh0eXBlb2Ygc3VjY2VzcyAhPT0gJ2Z1bmN0aW9uJykge1xuICAgIHJldHVybiBwcm9taXNlO1xuICB9XG4gIHByb21pc2VcbiAgICAudGhlbihzaXplcyA9PiBzdWNjZXNzKHNpemVzLndpZHRoLCBzaXplcy5oZWlnaHQpKVxuICAgIC5jYXRjaChcbiAgICAgIGZhaWx1cmUgfHxcbiAgICAgICAgZnVuY3Rpb24gKCkge1xuICAgICAgICAgIGNvbnNvbGUud2FybignRmFpbGVkIHRvIGdldCBzaXplIGZvciBpbWFnZTogJyArIHVyaSk7XG4gICAgICAgIH0sXG4gICAgKTtcbn1cblxuZnVuY3Rpb24gZ2V0U2l6ZVdpdGhIZWFkZXJzKFxuICB1cmk6IHN0cmluZyxcbiAgaGVhZGVyczoge1tzdHJpbmddOiBzdHJpbmcsIC4uLn0sXG4gIHN1Y2Nlc3M/OiAod2lkdGg6IG51bWJlciwgaGVpZ2h0OiBudW1iZXIpID0+IHZvaWQsXG4gIGZhaWx1cmU/OiAoZXJyb3I6IG1peGVkKSA9PiB2b2lkLFxuKTogdm9pZCB8IFByb21pc2U8SW1hZ2VTaXplPiB7XG4gIGNvbnN0IHByb21pc2UgPSBOYXRpdmVJbWFnZUxvYWRlcklPUy5nZXRTaXplV2l0aEhlYWRlcnModXJpLCBoZWFkZXJzKTtcbiAgaWYgKHR5cGVvZiBzdWNjZXNzICE9PSAnZnVuY3Rpb24nKSB7XG4gICAgcmV0dXJuIHByb21pc2U7XG4gIH1cbiAgcHJvbWlzZVxuICAgIC50aGVuKHNpemVzID0+IHN1Y2Nlc3Moc2l6ZXMud2lkdGgsIHNpemVzLmhlaWdodCkpXG4gICAgLmNhdGNoKFxuICAgICAgZmFpbHVyZSB8fFxuICAgICAgICBmdW5jdGlvbiAoKSB7XG4gICAgICAgICAgY29uc29sZS53YXJuKCdGYWlsZWQgdG8gZ2V0IHNpemUgZm9yIGltYWdlOiAnICsgdXJpKTtcbiAgICAgICAgfSxcbiAgICApO1xufVxuXG5mdW5jdGlvbiBwcmVmZXRjaFdpdGhNZXRhZGF0YShcbiAgdXJsOiBzdHJpbmcsXG4gIHF1ZXJ5Um9vdE5hbWU6IHN0cmluZyxcbiAgcm9vdFRhZz86ID9Sb290VGFnLFxuKTogUHJvbWlzZTxib29sZWFuPiB7XG4gIGlmIChOYXRpdmVJbWFnZUxvYWRlcklPUy5wcmVmZXRjaEltYWdlV2l0aE1ldGFkYXRhKSB7XG4gICAgLy8gbnVtYmVyIHBhcmFtcyBsaWtlIHJvb3RUYWcgY2Fubm90IGJlIG51bGxhYmxlIGJlZm9yZSBUdXJib01vZHVsZXMgaXMgYXZhaWxhYmxlXG4gICAgcmV0dXJuIE5hdGl2ZUltYWdlTG9hZGVySU9TLnByZWZldGNoSW1hZ2VXaXRoTWV0YWRhdGEoXG4gICAgICB1cmwsXG4gICAgICBxdWVyeVJvb3ROYW1lLFxuICAgICAgLy8gTk9URTogUm9vdFRhZyB0eXBlXG4gICAgICByb290VGFnICE9IG51bGwgPyByb290VGFnIDogY3JlYXRlUm9vdFRhZygwKSxcbiAgICApO1xuICB9IGVsc2Uge1xuICAgIHJldHVybiBOYXRpdmVJbWFnZUxvYWRlcklPUy5wcmVmZXRjaEltYWdlKHVybCk7XG4gIH1cbn1cblxuZnVuY3Rpb24gcHJlZmV0Y2godXJsOiBzdHJpbmcpOiBQcm9taXNlPGJvb2xlYW4+IHtcbiAgcmV0dXJuIE5hdGl2ZUltYWdlTG9hZGVySU9TLnByZWZldGNoSW1hZ2UodXJsKTtcbn1cblxuYXN5bmMgZnVuY3Rpb24gcXVlcnlDYWNoZShcbiAgdXJsczogQXJyYXk8c3RyaW5nPixcbik6IFByb21pc2U8e1tzdHJpbmddOiAnbWVtb3J5JyB8ICdkaXNrJyB8ICdkaXNrL21lbW9yeScsIC4uLn0+IHtcbiAgcmV0dXJuIE5hdGl2ZUltYWdlTG9hZGVySU9TLnF1ZXJ5Q2FjaGUodXJscyk7XG59XG5cbi8qKlxuICogQSBSZWFjdCBjb21wb25lbnQgZm9yIGRpc3BsYXlpbmcgZGlmZmVyZW50IHR5cGVzIG9mIGltYWdlcyxcbiAqIGluY2x1ZGluZyBuZXR3b3JrIGltYWdlcywgc3RhdGljIHJlc291cmNlcywgdGVtcG9yYXJ5IGxvY2FsIGltYWdlcywgYW5kXG4gKiBpbWFnZXMgZnJvbSBsb2NhbCBkaXNrLCBzdWNoIGFzIHRoZSBjYW1lcmEgcm9sbC5cbiAqXG4gKiBTZWUgaHR0cHM6Ly9yZWFjdG5hdGl2ZS5kZXYvZG9jcy9pbWFnZVxuICovXG5sZXQgQmFzZUltYWdlOiBBYnN0cmFjdEltYWdlSU9TID0gUmVhY3QuZm9yd2FyZFJlZigocHJvcHMsIGZvcndhcmRlZFJlZikgPT4ge1xuICBjb25zdCBzb3VyY2UgPSBnZXRJbWFnZVNvdXJjZXNGcm9tSW1hZ2VQcm9wcyhwcm9wcykgfHwge1xuICAgIHVyaTogdW5kZWZpbmVkLFxuICAgIHdpZHRoOiB1bmRlZmluZWQsXG4gICAgaGVpZ2h0OiB1bmRlZmluZWQsXG4gIH07XG5cbiAgbGV0IHN0eWxlOiBJbWFnZVN0eWxlUHJvcDtcbiAgbGV0IHNvdXJjZXM7XG4gIGlmIChBcnJheS5pc0FycmF5KHNvdXJjZSkpIHtcbiAgICBzdHlsZSA9IFtzdHlsZXMuYmFzZSwgcHJvcHMuc3R5bGVdO1xuICAgIHNvdXJjZXMgPSBzb3VyY2U7XG4gIH0gZWxzZSB7XG4gICAgY29uc3Qge3VyaX0gPSBzb3VyY2U7XG4gICAgaWYgKHVyaSA9PT0gJycpIHtcbiAgICAgIGNvbnNvbGUud2Fybignc291cmNlLnVyaSBzaG91bGQgbm90IGJlIGFuIGVtcHR5IHN0cmluZycpO1xuICAgIH1cbiAgICBjb25zdCB3aWR0aCA9IHNvdXJjZS53aWR0aCA/PyBwcm9wcy53aWR0aDtcbiAgICBjb25zdCBoZWlnaHQgPSBzb3VyY2UuaGVpZ2h0ID8/IHByb3BzLmhlaWdodDtcbiAgICBzdHlsZSA9IFt7d2lkdGgsIGhlaWdodH0sIHN0eWxlcy5iYXNlLCBwcm9wcy5zdHlsZV07XG4gICAgc291cmNlcyA9IFtzb3VyY2VdO1xuICB9XG5cbiAgY29uc3QgZmxhdHRlbmVkU3R5bGUgPSBmbGF0dGVuU3R5bGU8SW1hZ2VTdHlsZVByb3A+KHN0eWxlKTtcbiAgY29uc3Qgb2JqZWN0Rml0ID0gY29udmVydE9iamVjdEZpdFRvUmVzaXplTW9kZShmbGF0dGVuZWRTdHlsZT8ub2JqZWN0Rml0KTtcbiAgY29uc3QgcmVzaXplTW9kZSA9XG4gICAgb2JqZWN0Rml0IHx8IHByb3BzLnJlc2l6ZU1vZGUgfHwgZmxhdHRlbmVkU3R5bGU/LnJlc2l6ZU1vZGUgfHwgJ2NvdmVyJztcbiAgY29uc3QgdGludENvbG9yID0gcHJvcHMudGludENvbG9yID8/IGZsYXR0ZW5lZFN0eWxlPy50aW50Q29sb3I7XG5cbiAgaWYgKHByb3BzLmNoaWxkcmVuICE9IG51bGwpIHtcbiAgICB0aHJvdyBuZXcgRXJyb3IoXG4gICAgICAnVGhlIDxJbWFnZT4gY29tcG9uZW50IGNhbm5vdCBjb250YWluIGNoaWxkcmVuLiBJZiB5b3Ugd2FudCB0byByZW5kZXIgY29udGVudCBvbiB0b3Agb2YgdGhlIGltYWdlLCBjb25zaWRlciB1c2luZyB0aGUgPEltYWdlQmFja2dyb3VuZD4gY29tcG9uZW50IG9yIGFic29sdXRlIHBvc2l0aW9uaW5nLicsXG4gICAgKTtcbiAgfVxuICBjb25zdCB7XG4gICAgJ2FyaWEtYnVzeSc6IGFyaWFCdXN5LFxuICAgICdhcmlhLWNoZWNrZWQnOiBhcmlhQ2hlY2tlZCxcbiAgICAnYXJpYS1kaXNhYmxlZCc6IGFyaWFEaXNhYmxlZCxcbiAgICAnYXJpYS1leHBhbmRlZCc6IGFyaWFFeHBhbmRlZCxcbiAgICAnYXJpYS1zZWxlY3RlZCc6IGFyaWFTZWxlY3RlZCxcbiAgICBzcmMsXG4gICAgLi4ucmVzdFByb3BzXG4gIH0gPSBwcm9wcztcblxuICBjb25zdCBfYWNjZXNzaWJpbGl0eVN0YXRlID0ge1xuICAgIGJ1c3k6IGFyaWFCdXN5ID8/IHByb3BzLmFjY2Vzc2liaWxpdHlTdGF0ZT8uYnVzeSxcbiAgICBjaGVja2VkOiBhcmlhQ2hlY2tlZCA/PyBwcm9wcy5hY2Nlc3NpYmlsaXR5U3RhdGU/LmNoZWNrZWQsXG4gICAgZGlzYWJsZWQ6IGFyaWFEaXNhYmxlZCA/PyBwcm9wcy5hY2Nlc3NpYmlsaXR5U3RhdGU/LmRpc2FibGVkLFxuICAgIGV4cGFuZGVkOiBhcmlhRXhwYW5kZWQgPz8gcHJvcHMuYWNjZXNzaWJpbGl0eVN0YXRlPy5leHBhbmRlZCxcbiAgICBzZWxlY3RlZDogYXJpYVNlbGVjdGVkID8/IHByb3BzLmFjY2Vzc2liaWxpdHlTdGF0ZT8uc2VsZWN0ZWQsXG4gIH07XG4gIGNvbnN0IGFjY2Vzc2liaWxpdHlMYWJlbCA9IHByb3BzWydhcmlhLWxhYmVsJ10gPz8gcHJvcHMuYWNjZXNzaWJpbGl0eUxhYmVsO1xuXG4gIGNvbnN0IGFjdHVhbFJlZiA9IHVzZVdyYXBSZWZXaXRoSW1hZ2VBdHRhY2hlZENhbGxiYWNrcyhmb3J3YXJkZWRSZWYpO1xuXG4gIHJldHVybiAoXG4gICAgPEltYWdlQW5hbHl0aWNzVGFnQ29udGV4dC5Db25zdW1lcj5cbiAgICAgIHthbmFseXRpY1RhZyA9PiB7XG4gICAgICAgIHJldHVybiAoXG4gICAgICAgICAgPEltYWdlVmlld05hdGl2ZUNvbXBvbmVudFxuICAgICAgICAgICAgYWNjZXNzaWJpbGl0eVN0YXRlPXtfYWNjZXNzaWJpbGl0eVN0YXRlfVxuICAgICAgICAgICAgey4uLnJlc3RQcm9wc31cbiAgICAgICAgICAgIGFjY2Vzc2libGU9e3Byb3BzLmFsdCAhPT0gdW5kZWZpbmVkID8gdHJ1ZSA6IHByb3BzLmFjY2Vzc2libGV9XG4gICAgICAgICAgICBhY2Nlc3NpYmlsaXR5TGFiZWw9e2FjY2Vzc2liaWxpdHlMYWJlbCA/PyBwcm9wcy5hbHR9XG4gICAgICAgICAgICByZWY9e2FjdHVhbFJlZn1cbiAgICAgICAgICAgIHN0eWxlPXtzdHlsZX1cbiAgICAgICAgICAgIHJlc2l6ZU1vZGU9e3Jlc2l6ZU1vZGV9XG4gICAgICAgICAgICB0aW50Q29sb3I9e3RpbnRDb2xvcn1cbiAgICAgICAgICAgIHNvdXJjZT17c291cmNlc31cbiAgICAgICAgICAgIGludGVybmFsX2FuYWx5dGljVGFnPXthbmFseXRpY1RhZ31cbiAgICAgICAgICAvPlxuICAgICAgICApO1xuICAgICAgfX1cbiAgICA8L0ltYWdlQW5hbHl0aWNzVGFnQ29udGV4dC5Db25zdW1lcj5cbiAgKTtcbn0pO1xuXG5jb25zdCBpbWFnZUNvbXBvbmVudERlY29yYXRvciA9IHVuc3RhYmxlX2dldEltYWdlQ29tcG9uZW50RGVjb3JhdG9yKCk7XG5pZiAoaW1hZ2VDb21wb25lbnREZWNvcmF0b3IgIT0gbnVsbCkge1xuICBCYXNlSW1hZ2UgPSBpbWFnZUNvbXBvbmVudERlY29yYXRvcihCYXNlSW1hZ2UpO1xufVxuXG4vLyAkRmxvd0V4cGVjdGVkRXJyb3JbaW5jb21wYXRpYmxlLXR5cGVdIEV2ZW50dWFsbHkgd2UgbmVlZCB0byBtb3ZlIHRoZXNlIGZ1bmN0aW9ucyBmcm9tIHN0YXRpY3Mgb2YgdGhlIGNvbXBvbmVudCB0byBleHBvcnRzIGluIHRoZSBtb2R1bGUuXG5jb25zdCBJbWFnZTogSW1hZ2VJT1MgPSBCYXNlSW1hZ2U7XG5cbkltYWdlLmRpc3BsYXlOYW1lID0gJ0ltYWdlJztcblxuLyoqXG4gKiBSZXRyaWV2ZSB0aGUgd2lkdGggYW5kIGhlaWdodCAoaW4gcGl4ZWxzKSBvZiBhbiBpbWFnZSBwcmlvciB0byBkaXNwbGF5aW5nIGl0LlxuICpcbiAqIFNlZSBodHRwczovL3JlYWN0bmF0aXZlLmRldi9kb2NzL2ltYWdlI2dldHNpemVcbiAqL1xuLy8gJEZsb3dGaXhNZVtpbmNvbXBhdGlibGUtdXNlXSBUaGlzIHByb3BlcnR5IGlzbid0IHdyaXRhYmxlIGJ1dCB3ZSdyZSBhY3R1YWxseSBkZWZpbmluZyBpdCBoZXJlIGZvciB0aGUgZmlyc3QgdGltZS5cbkltYWdlLmdldFNpemUgPSBnZXRTaXplO1xuXG4vKipcbiAqIFJldHJpZXZlIHRoZSB3aWR0aCBhbmQgaGVpZ2h0IChpbiBwaXhlbHMpIG9mIGFuIGltYWdlIHByaW9yIHRvIGRpc3BsYXlpbmcgaXRcbiAqIHdpdGggdGhlIGFiaWxpdHkgdG8gcHJvdmlkZSB0aGUgaGVhZGVycyBmb3IgdGhlIHJlcXVlc3QuXG4gKlxuICogU2VlIGh0dHBzOi8vcmVhY3RuYXRpdmUuZGV2L2RvY3MvaW1hZ2UjZ2V0c2l6ZXdpdGhoZWFkZXJzXG4gKi9cbi8vICRGbG93Rml4TWVbaW5jb21wYXRpYmxlLXVzZV0gVGhpcyBwcm9wZXJ0eSBpc24ndCB3cml0YWJsZSBidXQgd2UncmUgYWN0dWFsbHkgZGVmaW5pbmcgaXQgaGVyZSBmb3IgdGhlIGZpcnN0IHRpbWUuXG5JbWFnZS5nZXRTaXplV2l0aEhlYWRlcnMgPSBnZXRTaXplV2l0aEhlYWRlcnM7XG5cbi8qKlxuICogUHJlZmV0Y2hlcyBhIHJlbW90ZSBpbWFnZSBmb3IgbGF0ZXIgdXNlIGJ5IGRvd25sb2FkaW5nIGl0IHRvIHRoZSBkaXNrXG4gKiBjYWNoZS5cbiAqXG4gKiBTZWUgaHR0cHM6Ly9yZWFjdG5hdGl2ZS5kZXYvZG9jcy9pbWFnZSNwcmVmZXRjaFxuICovXG4vLyAkRmxvd0ZpeE1lW2luY29tcGF0aWJsZS11c2VdIFRoaXMgcHJvcGVydHkgaXNuJ3Qgd3JpdGFibGUgYnV0IHdlJ3JlIGFjdHVhbGx5IGRlZmluaW5nIGl0IGhlcmUgZm9yIHRoZSBmaXJzdCB0aW1lLlxuSW1hZ2UucHJlZmV0Y2ggPSBwcmVmZXRjaDtcblxuLyoqXG4gKiBQcmVmZXRjaGVzIGEgcmVtb3RlIGltYWdlIGZvciBsYXRlciB1c2UgYnkgZG93bmxvYWRpbmcgaXQgdG8gdGhlIGRpc2tcbiAqIGNhY2hlLCBhbmQgYWRkcyBtZXRhZGF0YSBmb3IgcXVlcnlSb290TmFtZSBhbmQgcm9vdFRhZy5cbiAqXG4gKiBTZWUgaHR0cHM6Ly9yZWFjdG5hdGl2ZS5kZXYvZG9jcy9pbWFnZSNwcmVmZXRjaFxuICovXG4vLyAkRmxvd0ZpeE1lW2luY29tcGF0aWJsZS11c2VdIFRoaXMgcHJvcGVydHkgaXNuJ3Qgd3JpdGFibGUgYnV0IHdlJ3JlIGFjdHVhbGx5IGRlZmluaW5nIGl0IGhlcmUgZm9yIHRoZSBmaXJzdCB0aW1lLlxuSW1hZ2UucHJlZmV0Y2hXaXRoTWV0YWRhdGEgPSBwcmVmZXRjaFdpdGhNZXRhZGF0YTtcblxuLyoqXG4gKiBQZXJmb3JtcyBjYWNoZSBpbnRlcnJvZ2F0aW9uLlxuICpcbiAqICBTZWUgaHR0cHM6Ly9yZWFjdG5hdGl2ZS5kZXYvZG9jcy9pbWFnZSNxdWVyeWNhY2hlXG4gKi9cbi8vICRGbG93Rml4TWVbaW5jb21wYXRpYmxlLXVzZV0gVGhpcyBwcm9wZXJ0eSBpc24ndCB3cml0YWJsZSBidXQgd2UncmUgYWN0dWFsbHkgZGVmaW5pbmcgaXQgaGVyZSBmb3IgdGhlIGZpcnN0IHRpbWUuXG5JbWFnZS5xdWVyeUNhY2hlID0gcXVlcnlDYWNoZTtcblxuLyoqXG4gKiBSZXNvbHZlcyBhbiBhc3NldCByZWZlcmVuY2UgaW50byBhbiBvYmplY3QuXG4gKlxuICogU2VlIGh0dHBzOi8vcmVhY3RuYXRpdmUuZGV2L2RvY3MvaW1hZ2UjcmVzb2x2ZWFzc2V0c291cmNlXG4gKi9cbi8vICRGbG93Rml4TWVbaW5jb21wYXRpYmxlLXVzZV0gVGhpcyBwcm9wZXJ0eSBpc24ndCB3cml0YWJsZSBidXQgd2UncmUgYWN0dWFsbHkgZGVmaW5pbmcgaXQgaGVyZSBmb3IgdGhlIGZpcnN0IHRpbWUuXG5JbWFnZS5yZXNvbHZlQXNzZXRTb3VyY2UgPSByZXNvbHZlQXNzZXRTb3VyY2U7XG5cbmNvbnN0IHN0eWxlcyA9IFN0eWxlU2hlZXQuY3JlYXRlKHtcbiAgYmFzZToge1xuICAgIG92ZXJmbG93OiAnaGlkZGVuJyxcbiAgfSxcbn0pO1xuXG5tb2R1bGUuZXhwb3J0cyA9IEltYWdlO1xuIl0sIm1hcHBpbmdzIjoiOzs7O0FBZUEsSUFBQUEsUUFBQSxHQUFBQyxPQUFBO0FBQ0EsSUFBQUMsYUFBQSxHQUFBQyxzQkFBQSxDQUFBRixPQUFBO0FBQ0EsSUFBQUcsV0FBQSxHQUFBRCxzQkFBQSxDQUFBRixPQUFBO0FBQ0EsSUFBQUkseUJBQUEsR0FBQUYsc0JBQUEsQ0FBQUYsT0FBQTtBQUNBLElBQUFLLGVBQUEsR0FBQUwsT0FBQTtBQUlBLElBQUFNLGlCQUFBLEdBQUFOLE9BQUE7QUFDQSxJQUFBTyxXQUFBLEdBQUFQLE9BQUE7QUFDQSxJQUFBUSx5QkFBQSxHQUFBTixzQkFBQSxDQUFBRixPQUFBO0FBQ0EsSUFBQVMscUJBQUEsR0FBQVAsc0JBQUEsQ0FBQUYsT0FBQTtBQUNBLElBQUFVLG1CQUFBLEdBQUFSLHNCQUFBLENBQUFGLE9BQUE7QUFDQSxJQUFBVyxLQUFBLEdBQUFDLHVCQUFBLENBQUFaLE9BQUE7QUFBK0IsSUFBQWEsV0FBQSxHQUFBYixPQUFBO0FBQUEsSUFBQWMsU0FBQTtBQUFBLFNBQUFDLHlCQUFBQyxDQUFBLDZCQUFBQyxPQUFBLG1CQUFBQyxDQUFBLE9BQUFELE9BQUEsSUFBQUUsQ0FBQSxPQUFBRixPQUFBLFlBQUFGLHdCQUFBLFlBQUFBLHlCQUFBQyxDQUFBLFdBQUFBLENBQUEsR0FBQUcsQ0FBQSxHQUFBRCxDQUFBLEtBQUFGLENBQUE7QUFBQSxTQUFBSix3QkFBQUksQ0FBQSxFQUFBRSxDQUFBLFNBQUFBLENBQUEsSUFBQUYsQ0FBQSxJQUFBQSxDQUFBLENBQUFJLFVBQUEsU0FBQUosQ0FBQSxlQUFBQSxDQUFBLHVCQUFBQSxDQUFBLHlCQUFBQSxDQUFBLFdBQUFLLE9BQUEsRUFBQUwsQ0FBQSxRQUFBRyxDQUFBLEdBQUFKLHdCQUFBLENBQUFHLENBQUEsT0FBQUMsQ0FBQSxJQUFBQSxDQUFBLENBQUFHLEdBQUEsQ0FBQU4sQ0FBQSxVQUFBRyxDQUFBLENBQUFJLEdBQUEsQ0FBQVAsQ0FBQSxPQUFBUSxDQUFBLEtBQUFDLFNBQUEsVUFBQUMsQ0FBQSxHQUFBQyxNQUFBLENBQUFDLGNBQUEsSUFBQUQsTUFBQSxDQUFBRSx3QkFBQSxXQUFBQyxDQUFBLElBQUFkLENBQUEsb0JBQUFjLENBQUEsT0FBQUMsY0FBQSxDQUFBQyxJQUFBLENBQUFoQixDQUFBLEVBQUFjLENBQUEsU0FBQUcsQ0FBQSxHQUFBUCxDQUFBLEdBQUFDLE1BQUEsQ0FBQUUsd0JBQUEsQ0FBQWIsQ0FBQSxFQUFBYyxDQUFBLFVBQUFHLENBQUEsS0FBQUEsQ0FBQSxDQUFBVixHQUFBLElBQUFVLENBQUEsQ0FBQUMsR0FBQSxJQUFBUCxNQUFBLENBQUFDLGNBQUEsQ0FBQUosQ0FBQSxFQUFBTSxDQUFBLEVBQUFHLENBQUEsSUFBQVQsQ0FBQSxDQUFBTSxDQUFBLElBQUFkLENBQUEsQ0FBQWMsQ0FBQSxZQUFBTixDQUFBLENBQUFILE9BQUEsR0FBQUwsQ0FBQSxFQUFBRyxDQUFBLElBQUFBLENBQUEsQ0FBQWUsR0FBQSxDQUFBbEIsQ0FBQSxFQUFBUSxDQUFBLEdBQUFBLENBQUE7QUFFL0IsU0FBU1csT0FBT0EsQ0FDZEMsR0FBVyxFQUNYQyxPQUFpRCxFQUNqREMsT0FBZ0MsRUFDTDtFQUMzQixJQUFNQyxPQUFPLEdBQUdDLDZCQUFvQixDQUFDTCxPQUFPLENBQUNDLEdBQUcsQ0FBQyxDQUFDSyxJQUFJLENBQUMsVUFBQUMsSUFBQTtJQUFBLElBQUFDLEtBQUEsT0FBQUMsZUFBQSxDQUFBdkIsT0FBQSxFQUFBcUIsSUFBQTtNQUFFRyxLQUFLLEdBQUFGLEtBQUE7TUFBRUcsTUFBTSxHQUFBSCxLQUFBO0lBQUEsT0FBTztNQUMzRUUsS0FBSyxFQUFMQSxLQUFLO01BQ0xDLE1BQU0sRUFBTkE7SUFDRixDQUFDO0VBQUEsQ0FBQyxDQUFDO0VBQ0gsSUFBSSxPQUFPVCxPQUFPLEtBQUssVUFBVSxFQUFFO0lBQ2pDLE9BQU9FLE9BQU87RUFDaEI7RUFDQUEsT0FBTyxDQUNKRSxJQUFJLENBQUMsVUFBQU0sS0FBSztJQUFBLE9BQUlWLE9BQU8sQ0FBQ1UsS0FBSyxDQUFDRixLQUFLLEVBQUVFLEtBQUssQ0FBQ0QsTUFBTSxDQUFDO0VBQUEsRUFBQyxDQUNqREUsS0FBSyxDQUNKVixPQUFPLElBQ0wsWUFBWTtJQUNWVyxPQUFPLENBQUNDLElBQUksQ0FBQyxnQ0FBZ0MsR0FBR2QsR0FBRyxDQUFDO0VBQ3RELENBQ0osQ0FBQztBQUNMO0FBRUEsU0FBU2Usa0JBQWtCQSxDQUN6QmYsR0FBVyxFQUNYZ0IsT0FBZ0MsRUFDaENmLE9BQWlELEVBQ2pEQyxPQUFnQyxFQUNMO0VBQzNCLElBQU1DLE9BQU8sR0FBR0MsNkJBQW9CLENBQUNXLGtCQUFrQixDQUFDZixHQUFHLEVBQUVnQixPQUFPLENBQUM7RUFDckUsSUFBSSxPQUFPZixPQUFPLEtBQUssVUFBVSxFQUFFO0lBQ2pDLE9BQU9FLE9BQU87RUFDaEI7RUFDQUEsT0FBTyxDQUNKRSxJQUFJLENBQUMsVUFBQU0sS0FBSztJQUFBLE9BQUlWLE9BQU8sQ0FBQ1UsS0FBSyxDQUFDRixLQUFLLEVBQUVFLEtBQUssQ0FBQ0QsTUFBTSxDQUFDO0VBQUEsRUFBQyxDQUNqREUsS0FBSyxDQUNKVixPQUFPLElBQ0wsWUFBWTtJQUNWVyxPQUFPLENBQUNDLElBQUksQ0FBQyxnQ0FBZ0MsR0FBR2QsR0FBRyxDQUFDO0VBQ3RELENBQ0osQ0FBQztBQUNMO0FBRUEsU0FBU2lCLG9CQUFvQkEsQ0FDM0JDLEdBQVcsRUFDWEMsYUFBcUIsRUFDckJDLE9BQWtCLEVBQ0E7RUFDbEIsSUFBSWhCLDZCQUFvQixDQUFDaUIseUJBQXlCLEVBQUU7SUFFbEQsT0FBT2pCLDZCQUFvQixDQUFDaUIseUJBQXlCLENBQ25ESCxHQUFHLEVBQ0hDLGFBQWEsRUFFYkMsT0FBTyxJQUFJLElBQUksR0FBR0EsT0FBTyxHQUFHLElBQUFFLHNCQUFhLEVBQUMsQ0FBQyxDQUM3QyxDQUFDO0VBQ0gsQ0FBQyxNQUFNO0lBQ0wsT0FBT2xCLDZCQUFvQixDQUFDbUIsYUFBYSxDQUFDTCxHQUFHLENBQUM7RUFDaEQ7QUFDRjtBQUVBLFNBQVNNLFFBQVFBLENBQUNOLEdBQVcsRUFBb0I7RUFDL0MsT0FBT2QsNkJBQW9CLENBQUNtQixhQUFhLENBQUNMLEdBQUcsQ0FBQztBQUNoRDtBQUFDLFNBRWNPLFVBQVVBLENBQUFDLEVBQUE7RUFBQSxPQUFBQyxXQUFBLENBQUFDLEtBQUEsT0FBQUMsU0FBQTtBQUFBO0FBQUEsU0FBQUYsWUFBQTtFQUFBQSxXQUFBLE9BQUFHLGtCQUFBLENBQUE3QyxPQUFBLEVBQXpCLFdBQ0U4QyxJQUFtQixFQUMwQztJQUM3RCxPQUFPM0IsNkJBQW9CLENBQUNxQixVQUFVLENBQUNNLElBQUksQ0FBQztFQUM5QyxDQUFDO0VBQUEsT0FBQUosV0FBQSxDQUFBQyxLQUFBLE9BQUFDLFNBQUE7QUFBQTtBQVNELElBQUlHLFNBQTJCLEdBQUd6RCxLQUFLLENBQUMwRCxVQUFVLENBQUMsVUFBQ0MsS0FBSyxFQUFFQyxZQUFZLEVBQUs7RUFBQSxJQUFBQyxnQkFBQSxFQUFBQyxxQkFBQSxFQUFBQyxzQkFBQSxFQUFBQyxzQkFBQSxFQUFBQyxzQkFBQSxFQUFBQyxzQkFBQSxFQUFBQyxnQkFBQTtFQUMxRSxJQUFNQyxNQUFNLEdBQUcsSUFBQUMsK0NBQTZCLEVBQUNWLEtBQUssQ0FBQyxJQUFJO0lBQ3JEbEMsR0FBRyxFQUFFNkMsU0FBUztJQUNkcEMsS0FBSyxFQUFFb0MsU0FBUztJQUNoQm5DLE1BQU0sRUFBRW1DO0VBQ1YsQ0FBQztFQUVELElBQUlDLEtBQXFCO0VBQ3pCLElBQUlDLE9BQU87RUFDWCxJQUFJQyxLQUFLLENBQUNDLE9BQU8sQ0FBQ04sTUFBTSxDQUFDLEVBQUU7SUFDekJHLEtBQUssR0FBRyxDQUFDSSxNQUFNLENBQUNDLElBQUksRUFBRWpCLEtBQUssQ0FBQ1ksS0FBSyxDQUFDO0lBQ2xDQyxPQUFPLEdBQUdKLE1BQU07RUFDbEIsQ0FBQyxNQUFNO0lBQUEsSUFBQVMsYUFBQSxFQUFBQyxjQUFBO0lBQ0wsSUFBT3JELEdBQUcsR0FBSTJDLE1BQU0sQ0FBYjNDLEdBQUc7SUFDVixJQUFJQSxHQUFHLEtBQUssRUFBRSxFQUFFO01BQ2RhLE9BQU8sQ0FBQ0MsSUFBSSxDQUFDLDBDQUEwQyxDQUFDO0lBQzFEO0lBQ0EsSUFBTUwsTUFBSyxJQUFBMkMsYUFBQSxHQUFHVCxNQUFNLENBQUNsQyxLQUFLLFlBQUEyQyxhQUFBLEdBQUlsQixLQUFLLENBQUN6QixLQUFLO0lBQ3pDLElBQU1DLE9BQU0sSUFBQTJDLGNBQUEsR0FBR1YsTUFBTSxDQUFDakMsTUFBTSxZQUFBMkMsY0FBQSxHQUFJbkIsS0FBSyxDQUFDeEIsTUFBTTtJQUM1Q29DLEtBQUssR0FBRyxDQUFDO01BQUNyQyxLQUFLLEVBQUxBLE1BQUs7TUFBRUMsTUFBTSxFQUFOQTtJQUFNLENBQUMsRUFBRXdDLE1BQU0sQ0FBQ0MsSUFBSSxFQUFFakIsS0FBSyxDQUFDWSxLQUFLLENBQUM7SUFDbkRDLE9BQU8sR0FBRyxDQUFDSixNQUFNLENBQUM7RUFDcEI7RUFFQSxJQUFNVyxjQUFjLEdBQUcsSUFBQUMscUJBQVksRUFBaUJULEtBQUssQ0FBQztFQUMxRCxJQUFNVSxTQUFTLEdBQUcsSUFBQUMsd0NBQTRCLEVBQUNILGNBQWMsb0JBQWRBLGNBQWMsQ0FBRUUsU0FBUyxDQUFDO0VBQ3pFLElBQU1FLFVBQVUsR0FDZEYsU0FBUyxJQUFJdEIsS0FBSyxDQUFDd0IsVUFBVSxLQUFJSixjQUFjLG9CQUFkQSxjQUFjLENBQUVJLFVBQVUsS0FBSSxPQUFPO0VBQ3hFLElBQU1DLFNBQVMsSUFBQXZCLGdCQUFBLEdBQUdGLEtBQUssQ0FBQ3lCLFNBQVMsWUFBQXZCLGdCQUFBLEdBQUlrQixjQUFjLG9CQUFkQSxjQUFjLENBQUVLLFNBQVM7RUFFOUQsSUFBSXpCLEtBQUssQ0FBQzBCLFFBQVEsSUFBSSxJQUFJLEVBQUU7SUFDMUIsTUFBTSxJQUFJQyxLQUFLLENBQ2IsMktBQ0YsQ0FBQztFQUNIO0VBQ0EsSUFDZUMsUUFBUSxHQU9uQjVCLEtBQUssQ0FQUCxXQUFXO0lBQ0s2QixXQUFXLEdBTXpCN0IsS0FBSyxDQU5QLGNBQWM7SUFDRzhCLFlBQVksR0FLM0I5QixLQUFLLENBTFAsZUFBZTtJQUNFK0IsWUFBWSxHQUkzQi9CLEtBQUssQ0FKUCxlQUFlO0lBQ0VnQyxZQUFZLEdBRzNCaEMsS0FBSyxDQUhQLGVBQWU7SUFDZmlDLEdBQUcsR0FFRGpDLEtBQUssQ0FGUGlDLEdBQUc7SUFDQUMsU0FBUyxPQUFBQyx5QkFBQSxDQUFBcEYsT0FBQSxFQUNWaUQsS0FBSyxFQUFBeEQsU0FBQTtFQUVULElBQU00RixtQkFBbUIsR0FBRztJQUMxQkMsSUFBSSxFQUFFVCxRQUFRLFdBQVJBLFFBQVEsSUFBQXpCLHFCQUFBLEdBQUlILEtBQUssQ0FBQ3NDLGtCQUFrQixxQkFBeEJuQyxxQkFBQSxDQUEwQmtDLElBQUk7SUFDaERFLE9BQU8sRUFBRVYsV0FBVyxXQUFYQSxXQUFXLElBQUF6QixzQkFBQSxHQUFJSixLQUFLLENBQUNzQyxrQkFBa0IscUJBQXhCbEMsc0JBQUEsQ0FBMEJtQyxPQUFPO0lBQ3pEQyxRQUFRLEVBQUVWLFlBQVksV0FBWkEsWUFBWSxJQUFBekIsc0JBQUEsR0FBSUwsS0FBSyxDQUFDc0Msa0JBQWtCLHFCQUF4QmpDLHNCQUFBLENBQTBCbUMsUUFBUTtJQUM1REMsUUFBUSxFQUFFVixZQUFZLFdBQVpBLFlBQVksSUFBQXpCLHNCQUFBLEdBQUlOLEtBQUssQ0FBQ3NDLGtCQUFrQixxQkFBeEJoQyxzQkFBQSxDQUEwQm1DLFFBQVE7SUFDNURDLFFBQVEsRUFBRVYsWUFBWSxXQUFaQSxZQUFZLElBQUF6QixzQkFBQSxHQUFJUCxLQUFLLENBQUNzQyxrQkFBa0IscUJBQXhCL0Isc0JBQUEsQ0FBMEJtQztFQUN0RCxDQUFDO0VBQ0QsSUFBTUMsa0JBQWtCLElBQUFuQyxnQkFBQSxHQUFHUixLQUFLLENBQUMsWUFBWSxDQUFDLFlBQUFRLGdCQUFBLEdBQUlSLEtBQUssQ0FBQzJDLGtCQUFrQjtFQUUxRSxJQUFNQyxTQUFTLEdBQUcsSUFBQUMsb0RBQW9DLEVBQUM1QyxZQUFZLENBQUM7RUFFcEUsT0FDRSxJQUFBMUQsV0FBQSxDQUFBdUcsR0FBQSxFQUFDaEgseUJBQUEsQ0FBQWlCLE9BQXdCLENBQUNnRyxRQUFRO0lBQUFyQixRQUFBLEVBQy9CLFNBQUFBLFNBQUFzQixXQUFXLEVBQUk7TUFDZCxPQUNFLElBQUF6RyxXQUFBLENBQUF1RyxHQUFBLEVBQUM1Ryx5QkFBQSxDQUFBYSxPQUF3QixFQUFBTSxNQUFBLENBQUE0RixNQUFBO1FBQ3ZCWCxrQkFBa0IsRUFBRUY7TUFBb0IsR0FDcENGLFNBQVM7UUFDYmdCLFVBQVUsRUFBRWxELEtBQUssQ0FBQ21ELEdBQUcsS0FBS3hDLFNBQVMsR0FBRyxJQUFJLEdBQUdYLEtBQUssQ0FBQ2tELFVBQVc7UUFDOURQLGtCQUFrQixFQUFFQSxrQkFBa0IsV0FBbEJBLGtCQUFrQixHQUFJM0MsS0FBSyxDQUFDbUQsR0FBSTtRQUNwREMsR0FBRyxFQUFFUixTQUFVO1FBQ2ZoQyxLQUFLLEVBQUVBLEtBQU07UUFDYlksVUFBVSxFQUFFQSxVQUFXO1FBQ3ZCQyxTQUFTLEVBQUVBLFNBQVU7UUFDckJoQixNQUFNLEVBQUVJLE9BQVE7UUFDaEJ3QyxvQkFBb0IsRUFBRUw7TUFBWSxFQUNuQyxDQUFDO0lBRU47RUFBQyxDQUNnQyxDQUFDO0FBRXhDLENBQUMsQ0FBQztBQUVGLElBQU1NLHVCQUF1QixHQUFHLElBQUFDLG1EQUFtQyxFQUFDLENBQUM7QUFDckUsSUFBSUQsdUJBQXVCLElBQUksSUFBSSxFQUFFO0VBQ25DeEQsU0FBUyxHQUFHd0QsdUJBQXVCLENBQUN4RCxTQUFTLENBQUM7QUFDaEQ7QUFHQSxJQUFNMEQsS0FBZSxHQUFHMUQsU0FBUztBQUVqQzBELEtBQUssQ0FBQ0MsV0FBVyxHQUFHLE9BQU87QUFRM0JELEtBQUssQ0FBQzNGLE9BQU8sR0FBR0EsT0FBTztBQVN2QjJGLEtBQUssQ0FBQzNFLGtCQUFrQixHQUFHQSxrQkFBa0I7QUFTN0MyRSxLQUFLLENBQUNsRSxRQUFRLEdBQUdBLFFBQVE7QUFTekJrRSxLQUFLLENBQUN6RSxvQkFBb0IsR0FBR0Esb0JBQW9CO0FBUWpEeUUsS0FBSyxDQUFDakUsVUFBVSxHQUFHQSxVQUFVO0FBUTdCaUUsS0FBSyxDQUFDRSxrQkFBa0IsR0FBR0EsMkJBQWtCO0FBRTdDLElBQU0xQyxNQUFNLEdBQUcyQyxtQkFBVSxDQUFDQyxNQUFNLENBQUM7RUFDL0IzQyxJQUFJLEVBQUU7SUFDSjRDLFFBQVEsRUFBRTtFQUNaO0FBQ0YsQ0FBQyxDQUFDO0FBRUZDLE1BQU0sQ0FBQ0MsT0FBTyxHQUFHUCxLQUFLIiwiaWdub3JlTGlzdCI6W119