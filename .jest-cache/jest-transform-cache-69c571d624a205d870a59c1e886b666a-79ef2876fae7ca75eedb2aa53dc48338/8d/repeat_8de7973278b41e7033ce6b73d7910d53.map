{"version": 3, "names": ["Object", "defineProperty", "exports", "value", "withRepeat", "_util", "require", "_nextAnimation", "numberOfReps", "arguments", "length", "undefined", "reverse", "callback", "reduceMotion", "defineAnimation", "nextAnimation", "repeat", "animation", "now", "finished", "onFrame", "current", "reps", "startValue", "toValue", "onStart", "previousAnimation", "rep<PERSON><PERSON><PERSON>", "isHigherOrder", "getReduceMotionForAnimation"], "sources": ["../../../src/animation/repeat.ts"], "sourcesContent": [null], "mappings": "AAAA,YAAY;;AAAAA,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,KAAA;AAAA;AAAAD,OAAA,CAAAE,UAAA;AACZ,IAAAC,KAAA,GAAAC,OAAA;AAqCO,IAAMF,UAAU,GAAAF,OAAA,CAAAE,UAAA,GAAG,SAAbA,UAAUA,CACrBG,cAA6B,EAKD;EAC5B,SAAS;;EAAA,IALTC,YAAY,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC;EAAA,IAChBG,OAAO,GAAAH,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,KAAK;EAAA,IACfI,QAA4B,GAAAJ,SAAA,CAAAC,MAAA,OAAAD,SAAA,MAAAE,SAAA;EAAA,IAC5BG,YAA2B,GAAAL,SAAA,CAAAC,MAAA,OAAAD,SAAA,MAAAE,SAAA;EAI3B,OAAO,IAAAI,qBAAe,EACpBR,cAAc,EACd,YAAuB;IACrB,SAAS;;IAET,IAAMS,aAAa,GACjB,OAAOT,cAAc,KAAK,UAAU,GAChCA,cAAc,CAAC,CAAC,GAChBA,cAAc;IAEpB,SAASU,MAAMA,CAACC,SAA0B,EAAEC,GAAc,EAAW;MACnE,IAAMC,QAAQ,GAAGJ,aAAa,CAACK,OAAO,CAACL,aAAa,EAAEG,GAAG,CAAC;MAC1DD,SAAS,CAACI,OAAO,GAAGN,aAAa,CAACM,OAAO;MACzC,IAAIF,QAAQ,EAAE;QACZF,SAAS,CAACK,IAAI,IAAI,CAAC;QAGnB,IAAIP,aAAa,CAACH,QAAQ,EAAE;UAC1BG,aAAa,CAACH,QAAQ,CAAC,IAAI,EAAiBK,SAAS,CAACI,OAAO,CAAC;QAChE;QACA,IACEJ,SAAS,CAACJ,YAAY,IACrBN,YAAY,GAAG,CAAC,IAAIU,SAAS,CAACK,IAAI,IAAIf,YAAa,EACpD;UACA,OAAO,IAAI;QACb;QAEA,IAAMgB,UAAU,GAAGZ,OAAO,GACrBI,aAAa,CAACM,OAAO,GACtBJ,SAAS,CAACM,UAAU;QACxB,IAAIZ,OAAO,EAAE;UACXI,aAAa,CAACS,OAAO,GAAGP,SAAS,CAACM,UAAU;UAC5CN,SAAS,CAACM,UAAU,GAAGA,UAAU;QACnC;QACAR,aAAa,CAACU,OAAO,CACnBV,aAAa,EACbQ,UAAU,EACVL,GAAG,EACHH,aAAa,CAACW,iBAChB,CAAC;QACD,OAAO,KAAK;MACd;MACA,OAAO,KAAK;IACd;IAEA,IAAMC,WAAW,GAAI,SAAfA,WAAWA,CAAIR,QAAkB,EAAW;MAChD,IAAIP,QAAQ,EAAE;QACZA,QAAQ,CAACO,QAAQ,CAAC;MACpB;MAEA,IAAI,CAACA,QAAQ,IAAIJ,aAAa,CAACH,QAAQ,EAAE;QACvCG,aAAa,CAACH,QAAQ,CAAC,KAAoB,CAAC;MAC9C;IACF,CAAC;IAED,SAASa,OAAOA,CACdR,SAA0B,EAC1Bf,KAAsB,EACtBgB,GAAc,EACdQ,iBAAwC,EAClC;MACNT,SAAS,CAACM,UAAU,GAAGrB,KAAK;MAC5Be,SAAS,CAACK,IAAI,GAAG,CAAC;MAIlB,IAAIP,aAAa,CAACF,YAAY,KAAKH,SAAS,EAAE;QAC5CK,aAAa,CAACF,YAAY,GAAGI,SAAS,CAACJ,YAAY;MACrD;MAIA,IACEI,SAAS,CAACJ,YAAY,IACtBF,OAAO,KACNJ,YAAY,IAAI,CAAC,IAAIA,YAAY,GAAG,CAAC,KAAK,CAAC,CAAC,EAC7C;QACAU,SAAS,CAACI,OAAO,GAAGJ,SAAS,CAACM,UAAU;QACxCN,SAAS,CAACG,OAAO,GAAG;UAAA,OAAM,IAAI;QAAA;MAChC,CAAC,MAAM;QACLL,aAAa,CAACU,OAAO,CAACV,aAAa,EAAEb,KAAK,EAAEgB,GAAG,EAAEQ,iBAAiB,CAAC;MACrE;IACF;IAEA,OAAO;MACLE,aAAa,EAAE,IAAI;MACnBR,OAAO,EAAEJ,MAAM;MACfS,OAAO,EAAPA,OAAO;MACPH,IAAI,EAAE,CAAC;MACPD,OAAO,EAAEN,aAAa,CAACM,OAAO;MAC9BT,QAAQ,EAAEe,WAAW;MACrBJ,UAAU,EAAE,CAAC;MACbV,YAAY,EAAE,IAAAgB,iCAA2B,EAAChB,YAAY;IACxD,CAAC;EACH,CACF,CAAC;AACH,CAAmB", "ignoreList": []}