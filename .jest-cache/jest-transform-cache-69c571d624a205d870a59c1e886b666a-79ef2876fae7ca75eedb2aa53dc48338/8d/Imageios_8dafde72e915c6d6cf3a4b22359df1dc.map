{"version": 3, "names": ["_RootTag", "require", "_flattenStyle", "_interopRequireDefault", "_StyleSheet", "_ImageAnalyticsTagContext", "_ImageInjection", "_ImageSourceUtils", "_ImageUtils", "_ImageViewNativeComponent", "_NativeImageLoaderIOS", "_resolveAssetSource", "React", "_interopRequireWildcard", "_jsxRuntime", "_excluded", "_getRequireWildcardCache", "e", "WeakMap", "r", "t", "__esModule", "default", "has", "get", "n", "__proto__", "a", "Object", "defineProperty", "getOwnPropertyDescriptor", "u", "hasOwnProperty", "call", "i", "set", "getSize", "uri", "success", "failure", "promise", "NativeImageLoaderIOS", "then", "_ref", "_ref2", "_slicedToArray2", "width", "height", "sizes", "catch", "console", "warn", "getSizeWithHeaders", "headers", "prefetchWithMetadata", "url", "queryRootName", "rootTag", "prefetchImageWithMetadata", "createRootTag", "prefetchImage", "prefetch", "queryCache", "_x", "_queryCache", "apply", "arguments", "_asyncToGenerator2", "urls", "BaseImage", "forwardRef", "props", "forwardedRef", "_props$tintColor", "_props$accessibilityS", "_props$accessibilityS2", "_props$accessibilityS3", "_props$accessibilityS4", "_props$accessibilityS5", "_props$ariaLabel", "source", "getImageSourcesFromImageProps", "undefined", "style", "sources", "Array", "isArray", "styles", "base", "_source$width", "_source$height", "flattenedStyle", "flattenStyle", "objectFit", "convertObjectFitToResizeMode", "resizeMode", "tintColor", "children", "Error", "ariaBusy", "ariaChe<PERSON>", "ariaDisabled", "ariaExpanded", "ariaSelected", "src", "restProps", "_objectWithoutProperties2", "_accessibilityState", "busy", "accessibilityState", "checked", "disabled", "expanded", "selected", "accessibilityLabel", "actualRef", "useWrapRefWithImageAttachedCallbacks", "jsx", "Consumer", "analyticTag", "assign", "accessible", "alt", "ref", "internal_analyticTag", "imageComponentDecorator", "unstable_getImageComponentDecorator", "Image", "displayName", "resolveAssetSource", "StyleSheet", "create", "overflow", "module", "exports"], "sources": ["Image.ios.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @flow strict-local\n * @format\n */\n\nimport type {ImageStyleProp} from '../StyleSheet/StyleSheet';\nimport type {RootTag} from '../Types/RootTagTypes';\nimport type {AbstractImageIOS, ImageIOS} from './ImageTypes.flow';\nimport type {ImageSize} from './NativeImageLoaderAndroid';\n\nimport {createRootTag} from '../ReactNative/RootTag';\nimport flattenStyle from '../StyleSheet/flattenStyle';\nimport StyleSheet from '../StyleSheet/StyleSheet';\nimport ImageAnalyticsTagContext from './ImageAnalyticsTagContext';\nimport {\n  unstable_getImageComponentDecorator,\n  useWrapRefWithImageAttachedCallbacks,\n} from './ImageInjection';\nimport {getImageSourcesFromImageProps} from './ImageSourceUtils';\nimport {convertObjectFitToResizeMode} from './ImageUtils';\nimport ImageViewNativeComponent from './ImageViewNativeComponent';\nimport NativeImageLoaderIOS from './NativeImageLoaderIOS';\nimport resolveAssetSource from './resolveAssetSource';\nimport * as React from 'react';\n\nfunction getSize(\n  uri: string,\n  success?: (width: number, height: number) => void,\n  failure?: (error: mixed) => void,\n): void | Promise<ImageSize> {\n  const promise = NativeImageLoaderIOS.getSize(uri).then(([width, height]) => ({\n    width,\n    height,\n  }));\n  if (typeof success !== 'function') {\n    return promise;\n  }\n  promise\n    .then(sizes => success(sizes.width, sizes.height))\n    .catch(\n      failure ||\n        function () {\n          console.warn('Failed to get size for image: ' + uri);\n        },\n    );\n}\n\nfunction getSizeWithHeaders(\n  uri: string,\n  headers: {[string]: string, ...},\n  success?: (width: number, height: number) => void,\n  failure?: (error: mixed) => void,\n): void | Promise<ImageSize> {\n  const promise = NativeImageLoaderIOS.getSizeWithHeaders(uri, headers);\n  if (typeof success !== 'function') {\n    return promise;\n  }\n  promise\n    .then(sizes => success(sizes.width, sizes.height))\n    .catch(\n      failure ||\n        function () {\n          console.warn('Failed to get size for image: ' + uri);\n        },\n    );\n}\n\nfunction prefetchWithMetadata(\n  url: string,\n  queryRootName: string,\n  rootTag?: ?RootTag,\n): Promise<boolean> {\n  if (NativeImageLoaderIOS.prefetchImageWithMetadata) {\n    // number params like rootTag cannot be nullable before TurboModules is available\n    return NativeImageLoaderIOS.prefetchImageWithMetadata(\n      url,\n      queryRootName,\n      // NOTE: RootTag type\n      rootTag != null ? rootTag : createRootTag(0),\n    );\n  } else {\n    return NativeImageLoaderIOS.prefetchImage(url);\n  }\n}\n\nfunction prefetch(url: string): Promise<boolean> {\n  return NativeImageLoaderIOS.prefetchImage(url);\n}\n\nasync function queryCache(\n  urls: Array<string>,\n): Promise<{[string]: 'memory' | 'disk' | 'disk/memory', ...}> {\n  return NativeImageLoaderIOS.queryCache(urls);\n}\n\n/**\n * A React component for displaying different types of images,\n * including network images, static resources, temporary local images, and\n * images from local disk, such as the camera roll.\n *\n * See https://reactnative.dev/docs/image\n */\nlet BaseImage: AbstractImageIOS = React.forwardRef((props, forwardedRef) => {\n  const source = getImageSourcesFromImageProps(props) || {\n    uri: undefined,\n    width: undefined,\n    height: undefined,\n  };\n\n  let style: ImageStyleProp;\n  let sources;\n  if (Array.isArray(source)) {\n    style = [styles.base, props.style];\n    sources = source;\n  } else {\n    const {uri} = source;\n    if (uri === '') {\n      console.warn('source.uri should not be an empty string');\n    }\n    const width = source.width ?? props.width;\n    const height = source.height ?? props.height;\n    style = [{width, height}, styles.base, props.style];\n    sources = [source];\n  }\n\n  const flattenedStyle = flattenStyle<ImageStyleProp>(style);\n  const objectFit = convertObjectFitToResizeMode(flattenedStyle?.objectFit);\n  const resizeMode =\n    objectFit || props.resizeMode || flattenedStyle?.resizeMode || 'cover';\n  const tintColor = props.tintColor ?? flattenedStyle?.tintColor;\n\n  if (props.children != null) {\n    throw new Error(\n      'The <Image> component cannot contain children. If you want to render content on top of the image, consider using the <ImageBackground> component or absolute positioning.',\n    );\n  }\n  const {\n    'aria-busy': ariaBusy,\n    'aria-checked': ariaChecked,\n    'aria-disabled': ariaDisabled,\n    'aria-expanded': ariaExpanded,\n    'aria-selected': ariaSelected,\n    src,\n    ...restProps\n  } = props;\n\n  const _accessibilityState = {\n    busy: ariaBusy ?? props.accessibilityState?.busy,\n    checked: ariaChecked ?? props.accessibilityState?.checked,\n    disabled: ariaDisabled ?? props.accessibilityState?.disabled,\n    expanded: ariaExpanded ?? props.accessibilityState?.expanded,\n    selected: ariaSelected ?? props.accessibilityState?.selected,\n  };\n  const accessibilityLabel = props['aria-label'] ?? props.accessibilityLabel;\n\n  const actualRef = useWrapRefWithImageAttachedCallbacks(forwardedRef);\n\n  return (\n    <ImageAnalyticsTagContext.Consumer>\n      {analyticTag => {\n        return (\n          <ImageViewNativeComponent\n            accessibilityState={_accessibilityState}\n            {...restProps}\n            accessible={props.alt !== undefined ? true : props.accessible}\n            accessibilityLabel={accessibilityLabel ?? props.alt}\n            ref={actualRef}\n            style={style}\n            resizeMode={resizeMode}\n            tintColor={tintColor}\n            source={sources}\n            internal_analyticTag={analyticTag}\n          />\n        );\n      }}\n    </ImageAnalyticsTagContext.Consumer>\n  );\n});\n\nconst imageComponentDecorator = unstable_getImageComponentDecorator();\nif (imageComponentDecorator != null) {\n  BaseImage = imageComponentDecorator(BaseImage);\n}\n\n// $FlowExpectedError[incompatible-type] Eventually we need to move these functions from statics of the component to exports in the module.\nconst Image: ImageIOS = BaseImage;\n\nImage.displayName = 'Image';\n\n/**\n * Retrieve the width and height (in pixels) of an image prior to displaying it.\n *\n * See https://reactnative.dev/docs/image#getsize\n */\n// $FlowFixMe[incompatible-use] This property isn't writable but we're actually defining it here for the first time.\nImage.getSize = getSize;\n\n/**\n * Retrieve the width and height (in pixels) of an image prior to displaying it\n * with the ability to provide the headers for the request.\n *\n * See https://reactnative.dev/docs/image#getsizewithheaders\n */\n// $FlowFixMe[incompatible-use] This property isn't writable but we're actually defining it here for the first time.\nImage.getSizeWithHeaders = getSizeWithHeaders;\n\n/**\n * Prefetches a remote image for later use by downloading it to the disk\n * cache.\n *\n * See https://reactnative.dev/docs/image#prefetch\n */\n// $FlowFixMe[incompatible-use] This property isn't writable but we're actually defining it here for the first time.\nImage.prefetch = prefetch;\n\n/**\n * Prefetches a remote image for later use by downloading it to the disk\n * cache, and adds metadata for queryRootName and rootTag.\n *\n * See https://reactnative.dev/docs/image#prefetch\n */\n// $FlowFixMe[incompatible-use] This property isn't writable but we're actually defining it here for the first time.\nImage.prefetchWithMetadata = prefetchWithMetadata;\n\n/**\n * Performs cache interrogation.\n *\n *  See https://reactnative.dev/docs/image#querycache\n */\n// $FlowFixMe[incompatible-use] This property isn't writable but we're actually defining it here for the first time.\nImage.queryCache = queryCache;\n\n/**\n * Resolves an asset reference into an object.\n *\n * See https://reactnative.dev/docs/image#resolveassetsource\n */\n// $FlowFixMe[incompatible-use] This property isn't writable but we're actually defining it here for the first time.\nImage.resolveAssetSource = resolveAssetSource;\n\nconst styles = StyleSheet.create({\n  base: {\n    overflow: 'hidden',\n  },\n});\n\nmodule.exports = Image;\n"], "mappings": ";;;;AAeA,IAAAA,QAAA,GAAAC,OAAA;AACA,IAAAC,aAAA,GAAAC,sBAAA,CAAAF,OAAA;AACA,IAAAG,WAAA,GAAAD,sBAAA,CAAAF,OAAA;AACA,IAAAI,yBAAA,GAAAF,sBAAA,CAAAF,OAAA;AACA,IAAAK,eAAA,GAAAL,OAAA;AAIA,IAAAM,iBAAA,GAAAN,OAAA;AACA,IAAAO,WAAA,GAAAP,OAAA;AACA,IAAAQ,yBAAA,GAAAN,sBAAA,CAAAF,OAAA;AACA,IAAAS,qBAAA,GAAAP,sBAAA,CAAAF,OAAA;AACA,IAAAU,mBAAA,GAAAR,sBAAA,CAAAF,OAAA;AACA,IAAAW,KAAA,GAAAC,uBAAA,CAAAZ,OAAA;AAA+B,IAAAa,WAAA,GAAAb,OAAA;AAAA,IAAAc,SAAA;AAAA,SAAAC,yBAAAC,CAAA,6BAAAC,OAAA,mBAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,yBAAAC,CAAA,WAAAA,CAAA,GAAAG,CAAA,GAAAD,CAAA,KAAAF,CAAA;AAAA,SAAAJ,wBAAAI,CAAA,EAAAE,CAAA,SAAAA,CAAA,IAAAF,CAAA,IAAAA,CAAA,CAAAI,UAAA,SAAAJ,CAAA,eAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,WAAAK,OAAA,EAAAL,CAAA,QAAAG,CAAA,GAAAJ,wBAAA,CAAAG,CAAA,OAAAC,CAAA,IAAAA,CAAA,CAAAG,GAAA,CAAAN,CAAA,UAAAG,CAAA,CAAAI,GAAA,CAAAP,CAAA,OAAAQ,CAAA,KAAAC,SAAA,UAAAC,CAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,CAAA,IAAAd,CAAA,oBAAAc,CAAA,OAAAC,cAAA,CAAAC,IAAA,CAAAhB,CAAA,EAAAc,CAAA,SAAAG,CAAA,GAAAP,CAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAb,CAAA,EAAAc,CAAA,UAAAG,CAAA,KAAAA,CAAA,CAAAV,GAAA,IAAAU,CAAA,CAAAC,GAAA,IAAAP,MAAA,CAAAC,cAAA,CAAAJ,CAAA,EAAAM,CAAA,EAAAG,CAAA,IAAAT,CAAA,CAAAM,CAAA,IAAAd,CAAA,CAAAc,CAAA,YAAAN,CAAA,CAAAH,OAAA,GAAAL,CAAA,EAAAG,CAAA,IAAAA,CAAA,CAAAe,GAAA,CAAAlB,CAAA,EAAAQ,CAAA,GAAAA,CAAA;AAE/B,SAASW,OAAOA,CACdC,GAAW,EACXC,OAAiD,EACjDC,OAAgC,EACL;EAC3B,IAAMC,OAAO,GAAGC,6BAAoB,CAACL,OAAO,CAACC,GAAG,CAAC,CAACK,IAAI,CAAC,UAAAC,IAAA;IAAA,IAAAC,KAAA,OAAAC,eAAA,CAAAvB,OAAA,EAAAqB,IAAA;MAAEG,KAAK,GAAAF,KAAA;MAAEG,MAAM,GAAAH,KAAA;IAAA,OAAO;MAC3EE,KAAK,EAALA,KAAK;MACLC,MAAM,EAANA;IACF,CAAC;EAAA,CAAC,CAAC;EACH,IAAI,OAAOT,OAAO,KAAK,UAAU,EAAE;IACjC,OAAOE,OAAO;EAChB;EACAA,OAAO,CACJE,IAAI,CAAC,UAAAM,KAAK;IAAA,OAAIV,OAAO,CAACU,KAAK,CAACF,KAAK,EAAEE,KAAK,CAACD,MAAM,CAAC;EAAA,EAAC,CACjDE,KAAK,CACJV,OAAO,IACL,YAAY;IACVW,OAAO,CAACC,IAAI,CAAC,gCAAgC,GAAGd,GAAG,CAAC;EACtD,CACJ,CAAC;AACL;AAEA,SAASe,kBAAkBA,CACzBf,GAAW,EACXgB,OAAgC,EAChCf,OAAiD,EACjDC,OAAgC,EACL;EAC3B,IAAMC,OAAO,GAAGC,6BAAoB,CAACW,kBAAkB,CAACf,GAAG,EAAEgB,OAAO,CAAC;EACrE,IAAI,OAAOf,OAAO,KAAK,UAAU,EAAE;IACjC,OAAOE,OAAO;EAChB;EACAA,OAAO,CACJE,IAAI,CAAC,UAAAM,KAAK;IAAA,OAAIV,OAAO,CAACU,KAAK,CAACF,KAAK,EAAEE,KAAK,CAACD,MAAM,CAAC;EAAA,EAAC,CACjDE,KAAK,CACJV,OAAO,IACL,YAAY;IACVW,OAAO,CAACC,IAAI,CAAC,gCAAgC,GAAGd,GAAG,CAAC;EACtD,CACJ,CAAC;AACL;AAEA,SAASiB,oBAAoBA,CAC3BC,GAAW,EACXC,aAAqB,EACrBC,OAAkB,EACA;EAClB,IAAIhB,6BAAoB,CAACiB,yBAAyB,EAAE;IAElD,OAAOjB,6BAAoB,CAACiB,yBAAyB,CACnDH,GAAG,EACHC,aAAa,EAEbC,OAAO,IAAI,IAAI,GAAGA,OAAO,GAAG,IAAAE,sBAAa,EAAC,CAAC,CAC7C,CAAC;EACH,CAAC,MAAM;IACL,OAAOlB,6BAAoB,CAACmB,aAAa,CAACL,GAAG,CAAC;EAChD;AACF;AAEA,SAASM,QAAQA,CAACN,GAAW,EAAoB;EAC/C,OAAOd,6BAAoB,CAACmB,aAAa,CAACL,GAAG,CAAC;AAChD;AAAC,SAEcO,UAAUA,CAAAC,EAAA;EAAA,OAAAC,WAAA,CAAAC,KAAA,OAAAC,SAAA;AAAA;AAAA,SAAAF,YAAA;EAAAA,WAAA,OAAAG,kBAAA,CAAA7C,OAAA,EAAzB,WACE8C,IAAmB,EAC0C;IAC7D,OAAO3B,6BAAoB,CAACqB,UAAU,CAACM,IAAI,CAAC;EAC9C,CAAC;EAAA,OAAAJ,WAAA,CAAAC,KAAA,OAAAC,SAAA;AAAA;AASD,IAAIG,SAA2B,GAAGzD,KAAK,CAAC0D,UAAU,CAAC,UAACC,KAAK,EAAEC,YAAY,EAAK;EAAA,IAAAC,gBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,gBAAA;EAC1E,IAAMC,MAAM,GAAG,IAAAC,+CAA6B,EAACV,KAAK,CAAC,IAAI;IACrDlC,GAAG,EAAE6C,SAAS;IACdpC,KAAK,EAAEoC,SAAS;IAChBnC,MAAM,EAAEmC;EACV,CAAC;EAED,IAAIC,KAAqB;EACzB,IAAIC,OAAO;EACX,IAAIC,KAAK,CAACC,OAAO,CAACN,MAAM,CAAC,EAAE;IACzBG,KAAK,GAAG,CAACI,MAAM,CAACC,IAAI,EAAEjB,KAAK,CAACY,KAAK,CAAC;IAClCC,OAAO,GAAGJ,MAAM;EAClB,CAAC,MAAM;IAAA,IAAAS,aAAA,EAAAC,cAAA;IACL,IAAOrD,GAAG,GAAI2C,MAAM,CAAb3C,GAAG;IACV,IAAIA,GAAG,KAAK,EAAE,EAAE;MACda,OAAO,CAACC,IAAI,CAAC,0CAA0C,CAAC;IAC1D;IACA,IAAML,MAAK,IAAA2C,aAAA,GAAGT,MAAM,CAAClC,KAAK,YAAA2C,aAAA,GAAIlB,KAAK,CAACzB,KAAK;IACzC,IAAMC,OAAM,IAAA2C,cAAA,GAAGV,MAAM,CAACjC,MAAM,YAAA2C,cAAA,GAAInB,KAAK,CAACxB,MAAM;IAC5CoC,KAAK,GAAG,CAAC;MAACrC,KAAK,EAALA,MAAK;MAAEC,MAAM,EAANA;IAAM,CAAC,EAAEwC,MAAM,CAACC,IAAI,EAAEjB,KAAK,CAACY,KAAK,CAAC;IACnDC,OAAO,GAAG,CAACJ,MAAM,CAAC;EACpB;EAEA,IAAMW,cAAc,GAAG,IAAAC,qBAAY,EAAiBT,KAAK,CAAC;EAC1D,IAAMU,SAAS,GAAG,IAAAC,wCAA4B,EAACH,cAAc,oBAAdA,cAAc,CAAEE,SAAS,CAAC;EACzE,IAAME,UAAU,GACdF,SAAS,IAAItB,KAAK,CAACwB,UAAU,KAAIJ,cAAc,oBAAdA,cAAc,CAAEI,UAAU,KAAI,OAAO;EACxE,IAAMC,SAAS,IAAAvB,gBAAA,GAAGF,KAAK,CAACyB,SAAS,YAAAvB,gBAAA,GAAIkB,cAAc,oBAAdA,cAAc,CAAEK,SAAS;EAE9D,IAAIzB,KAAK,CAAC0B,QAAQ,IAAI,IAAI,EAAE;IAC1B,MAAM,IAAIC,KAAK,CACb,2KACF,CAAC;EACH;EACA,IACeC,QAAQ,GAOnB5B,KAAK,CAPP,WAAW;IACK6B,WAAW,GAMzB7B,KAAK,CANP,cAAc;IACG8B,YAAY,GAK3B9B,KAAK,CALP,eAAe;IACE+B,YAAY,GAI3B/B,KAAK,CAJP,eAAe;IACEgC,YAAY,GAG3BhC,KAAK,CAHP,eAAe;IACfiC,GAAG,GAEDjC,KAAK,CAFPiC,GAAG;IACAC,SAAS,OAAAC,yBAAA,CAAApF,OAAA,EACViD,KAAK,EAAAxD,SAAA;EAET,IAAM4F,mBAAmB,GAAG;IAC1BC,IAAI,EAAET,QAAQ,WAARA,QAAQ,IAAAzB,qBAAA,GAAIH,KAAK,CAACsC,kBAAkB,qBAAxBnC,qBAAA,CAA0BkC,IAAI;IAChDE,OAAO,EAAEV,WAAW,WAAXA,WAAW,IAAAzB,sBAAA,GAAIJ,KAAK,CAACsC,kBAAkB,qBAAxBlC,sBAAA,CAA0BmC,OAAO;IACzDC,QAAQ,EAAEV,YAAY,WAAZA,YAAY,IAAAzB,sBAAA,GAAIL,KAAK,CAACsC,kBAAkB,qBAAxBjC,sBAAA,CAA0BmC,QAAQ;IAC5DC,QAAQ,EAAEV,YAAY,WAAZA,YAAY,IAAAzB,sBAAA,GAAIN,KAAK,CAACsC,kBAAkB,qBAAxBhC,sBAAA,CAA0BmC,QAAQ;IAC5DC,QAAQ,EAAEV,YAAY,WAAZA,YAAY,IAAAzB,sBAAA,GAAIP,KAAK,CAACsC,kBAAkB,qBAAxB/B,sBAAA,CAA0BmC;EACtD,CAAC;EACD,IAAMC,kBAAkB,IAAAnC,gBAAA,GAAGR,KAAK,CAAC,YAAY,CAAC,YAAAQ,gBAAA,GAAIR,KAAK,CAAC2C,kBAAkB;EAE1E,IAAMC,SAAS,GAAG,IAAAC,oDAAoC,EAAC5C,YAAY,CAAC;EAEpE,OACE,IAAA1D,WAAA,CAAAuG,GAAA,EAAChH,yBAAA,CAAAiB,OAAwB,CAACgG,QAAQ;IAAArB,QAAA,EAC/B,SAAAA,SAAAsB,WAAW,EAAI;MACd,OACE,IAAAzG,WAAA,CAAAuG,GAAA,EAAC5G,yBAAA,CAAAa,OAAwB,EAAAM,MAAA,CAAA4F,MAAA;QACvBX,kBAAkB,EAAEF;MAAoB,GACpCF,SAAS;QACbgB,UAAU,EAAElD,KAAK,CAACmD,GAAG,KAAKxC,SAAS,GAAG,IAAI,GAAGX,KAAK,CAACkD,UAAW;QAC9DP,kBAAkB,EAAEA,kBAAkB,WAAlBA,kBAAkB,GAAI3C,KAAK,CAACmD,GAAI;QACpDC,GAAG,EAAER,SAAU;QACfhC,KAAK,EAAEA,KAAM;QACbY,UAAU,EAAEA,UAAW;QACvBC,SAAS,EAAEA,SAAU;QACrBhB,MAAM,EAAEI,OAAQ;QAChBwC,oBAAoB,EAAEL;MAAY,EACnC,CAAC;IAEN;EAAC,CACgC,CAAC;AAExC,CAAC,CAAC;AAEF,IAAMM,uBAAuB,GAAG,IAAAC,mDAAmC,EAAC,CAAC;AACrE,IAAID,uBAAuB,IAAI,IAAI,EAAE;EACnCxD,SAAS,GAAGwD,uBAAuB,CAACxD,SAAS,CAAC;AAChD;AAGA,IAAM0D,KAAe,GAAG1D,SAAS;AAEjC0D,KAAK,CAACC,WAAW,GAAG,OAAO;AAQ3BD,KAAK,CAAC3F,OAAO,GAAGA,OAAO;AASvB2F,KAAK,CAAC3E,kBAAkB,GAAGA,kBAAkB;AAS7C2E,KAAK,CAAClE,QAAQ,GAAGA,QAAQ;AASzBkE,KAAK,CAACzE,oBAAoB,GAAGA,oBAAoB;AAQjDyE,KAAK,CAACjE,UAAU,GAAGA,UAAU;AAQ7BiE,KAAK,CAACE,kBAAkB,GAAGA,2BAAkB;AAE7C,IAAM1C,MAAM,GAAG2C,mBAAU,CAACC,MAAM,CAAC;EAC/B3C,IAAI,EAAE;IACJ4C,QAAQ,EAAE;EACZ;AACF,CAAC,CAAC;AAEFC,MAAM,CAACC,OAAO,GAAGP,KAAK", "ignoreList": []}