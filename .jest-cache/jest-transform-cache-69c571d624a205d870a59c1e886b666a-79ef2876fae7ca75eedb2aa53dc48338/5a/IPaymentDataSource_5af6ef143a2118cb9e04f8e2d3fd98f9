d2d6a4b0a6a62a6a632f5825ff72d58a
"use strict";

/* istanbul ignore next */
function cov_2e29zc8hqa() {
  var path = "/Users/<USER>/projects/react-native/payment-module/src/data/datasources/IPaymentDataSource.ts";
  var hash = "15b9237925269ed72c5335d07b5113a17dabce41";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/projects/react-native/payment-module/src/data/datasources/IPaymentDataSource.ts",
    statementMap: {
      "0": {
        start: {
          line: 3,
          column: 0
        },
        end: {
          line: 5,
          column: 3
        }
      }
    },
    fnMap: {},
    branchMap: {},
    s: {
      "0": 0
    },
    f: {},
    b: {},
    inputSourceMap: {
      version: 3,
      file: undefined,
      names: [],
      sourceRoot: undefined,
      sources: ["/Users/<USER>/projects/react-native/payment-module/src/data/datasources/IPaymentDataSource.ts"],
      sourcesContent: ["import {BaseResponse} from '../../core/BaseResponse';\nimport {ValidateResponse} from '../models/validate/ValidateResponse';\nimport {ValidateRequest} from '../models/validate/ValidateRequest';\n\nexport interface IPaymentDataSource {\n  validate(request: ValidateRequest): Promise<BaseResponse<ValidateResponse>>;\n}\n"],
      mappings: "",
      ignoreList: []
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "15b9237925269ed72c5335d07b5113a17dabce41"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_2e29zc8hqa = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_2e29zc8hqa();
cov_2e29zc8hqa().s[0]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6W10sInNvdXJjZXMiOlsiL1VzZXJzL3NvbnNhbXNldC9wcm9qZWN0cy9yZWFjdC1uYXRpdmUvcGF5bWVudC1tb2R1bGUvc3JjL2RhdGEvZGF0YXNvdXJjZXMvSVBheW1lbnREYXRhU291cmNlLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7QmFzZVJlc3BvbnNlfSBmcm9tICcuLi8uLi9jb3JlL0Jhc2VSZXNwb25zZSc7XG5pbXBvcnQge1ZhbGlkYXRlUmVzcG9uc2V9IGZyb20gJy4uL21vZGVscy92YWxpZGF0ZS9WYWxpZGF0ZVJlc3BvbnNlJztcbmltcG9ydCB7VmFsaWRhdGVSZXF1ZXN0fSBmcm9tICcuLi9tb2RlbHMvdmFsaWRhdGUvVmFsaWRhdGVSZXF1ZXN0JztcblxuZXhwb3J0IGludGVyZmFjZSBJUGF5bWVudERhdGFTb3VyY2Uge1xuICB2YWxpZGF0ZShyZXF1ZXN0OiBWYWxpZGF0ZVJlcXVlc3QpOiBQcm9taXNlPEJhc2VSZXNwb25zZTxWYWxpZGF0ZVJlc3BvbnNlPj47XG59XG4iXSwibWFwcGluZ3MiOiIiLCJpZ25vcmVMaXN0IjpbXX0=