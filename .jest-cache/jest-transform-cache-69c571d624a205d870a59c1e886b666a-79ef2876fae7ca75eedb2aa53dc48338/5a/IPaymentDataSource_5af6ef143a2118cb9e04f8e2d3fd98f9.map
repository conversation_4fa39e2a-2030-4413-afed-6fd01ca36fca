{"version": 3, "names": [], "sources": ["/Users/<USER>/projects/react-native/payment-module/src/data/datasources/IPaymentDataSource.ts"], "sourcesContent": ["import {BaseResponse} from '../../core/BaseResponse';\nimport {ValidateResponse} from '../models/validate/ValidateResponse';\nimport {ValidateRequest} from '../models/validate/ValidateRequest';\n\nexport interface IPaymentDataSource {\n  validate(request: ValidateRequest): Promise<BaseResponse<ValidateResponse>>;\n}\n"], "mappings": "", "ignoreList": []}