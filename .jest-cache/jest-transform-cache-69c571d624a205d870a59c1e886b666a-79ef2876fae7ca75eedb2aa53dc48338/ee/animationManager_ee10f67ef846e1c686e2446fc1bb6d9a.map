{"version": 3, "names": ["Object", "defineProperty", "exports", "value", "finishScreenTransition", "startScreenTransition", "_ConfigHelper", "require", "_styleUpdater", "_swipeSimulator", "configureProps", "screenTransitionConfig", "stackTag", "sharedEvent", "addListener", "applyStyle", "getLockAxis", "goBackGesture", "includes", "undefined", "removeListener", "lockAxis", "step", "getSwipeSimulator"], "sources": ["../../../src/screenTransition/animationManager.ts"], "sourcesContent": [null], "mappings": "AAAA,YAAY;;AAAAA,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,KAAA;AAAA;AAAAD,OAAA,CAAAE,sBAAA,GAAAA,sBAAA;AAAAF,OAAA,CAAAG,qBAAA,GAAAA,qBAAA;AAGZ,IAAAC,aAAA,GAAAC,OAAA;AACA,IAAAC,aAAA,GAAAD,OAAA;AACA,IAAAE,eAAA,GAAAF,OAAA;AAEA,IAAAG,4BAAc,EAAC,CAAC;AAET,SAASL,qBAAqBA,CACnCM,sBAA8C,EAC9C;EACA,SAAS;;EACT,IAAQC,QAAQ,GAAkBD,sBAAsB,CAAhDC,QAAQ;IAAEC,WAAA,GAAgBF,sBAAsB,CAAtCE,WAAA;EAClBA,WAAW,CAACC,WAAW,CAACF,QAAQ,EAAE,YAAM;IACtC,IAAAG,wBAAU,EAACJ,sBAAsB,EAAEE,WAAW,CAACV,KAAK,CAAC;EACvD,CAAC,CAAC;AACJ;AAEA,SAASa,WAAWA,CAACC,aAAqB,EAAY;EACpD,SAAS;;EACT,IAAI,CAAC,YAAY,EAAE,WAAW,EAAE,iBAAiB,CAAC,CAACC,QAAQ,CAACD,aAAa,CAAC,EAAE;IAC1E,OAAO,GAAG;EACZ,CAAC,MAAM,IACL,CAAC,SAAS,EAAE,WAAW,EAAE,eAAe,CAAC,CAACC,QAAQ,CAACD,aAAa,CAAC,EACjE;IACA,OAAO,GAAG;EACZ;EACA,OAAOE,SAAS;AAClB;AAEO,SAASf,sBAAsBA,CACpCO,sBAA8C,EAC9C;EACA,SAAS;;EACT,IAAQC,QAAQ,GAAiCD,sBAAsB,CAA/DC,QAAQ;IAAEC,WAAW,GAAoBF,sBAAsB,CAArDE,WAAW;IAAEI,aAAA,GAAkBN,sBAAsB,CAAxCM,aAAA;EAC/BJ,WAAW,CAACO,cAAc,CAACR,QAAQ,CAAC;EACpC,IAAMS,QAAQ,GAAGL,WAAW,CAACC,aAAa,CAAC;EAC3C,IAAMK,IAAI,GAAG,IAAAC,iCAAiB,EAC5BV,WAAW,CAACV,KAAK,EACjBQ,sBAAsB,EACtBU,QACF,CAAC;EACDC,IAAI,CAAC,CAAC;AACR", "ignoreList": []}