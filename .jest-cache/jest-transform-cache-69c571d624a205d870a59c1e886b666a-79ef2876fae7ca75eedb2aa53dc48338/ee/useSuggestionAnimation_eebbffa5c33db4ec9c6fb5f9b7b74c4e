4be22d9bf571a5a45f247bdbd5793124
"use strict";

/* istanbul ignore next */
function cov_2g5zc0i9hx() {
  var path = "/Users/<USER>/projects/react-native/payment-module/src/presentation/payment-bill/hooks/useSuggestionAnimation.ts";
  var hash = "2ea9514d9326d1d1ccd2cd82c151839d918f8fad";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/projects/react-native/payment-module/src/presentation/payment-bill/hooks/useSuggestionAnimation.ts",
    statementMap: {
      "0": {
        start: {
          line: 3,
          column: 0
        },
        end: {
          line: 5,
          column: 3
        }
      },
      "1": {
        start: {
          line: 6,
          column: 0
        },
        end: {
          line: 6,
          column: 40
        }
      },
      "2": {
        start: {
          line: 7,
          column: 32
        },
        end: {
          line: 7,
          column: 66
        }
      },
      "3": {
        start: {
          line: 8,
          column: 29
        },
        end: {
          line: 31,
          column: 1
        }
      },
      "4": {
        start: {
          line: 9,
          column: 29
        },
        end: {
          line: 9,
          column: 77
        }
      },
      "5": {
        start: {
          line: 10,
          column: 36
        },
        end: {
          line: 10,
          column: 84
        }
      },
      "6": {
        start: {
          line: 11,
          column: 35
        },
        end: {
          line: 19,
          column: 3
        }
      },
      "7": {
        start: {
          line: 12,
          column: 17
        },
        end: {
          line: 12,
          column: 73
        }
      },
      "8": {
        start: {
          line: 13,
          column: 4
        },
        end: {
          line: 15,
          column: 7
        }
      },
      "9": {
        start: {
          line: 16,
          column: 4
        },
        end: {
          line: 18,
          column: 7
        }
      },
      "10": {
        start: {
          line: 20,
          column: 22
        },
        end: {
          line: 25,
          column: 4
        }
      },
      "11": {
        start: {
          line: 21,
          column: 4
        },
        end: {
          line: 24,
          column: 6
        }
      },
      "12": {
        start: {
          line: 26,
          column: 2
        },
        end: {
          line: 30,
          column: 4
        }
      },
      "13": {
        start: {
          line: 32,
          column: 0
        },
        end: {
          line: 32,
          column: 56
        }
      }
    },
    fnMap: {
      "0": {
        name: "useSuggestionAnimation",
        decl: {
          start: {
            line: 8,
            column: 38
          },
          end: {
            line: 8,
            column: 60
          }
        },
        loc: {
          start: {
            line: 8,
            column: 73
          },
          end: {
            line: 31,
            column: 1
          }
        },
        line: 8
      },
      "1": {
        name: "updateSuggestionListHeight",
        decl: {
          start: {
            line: 11,
            column: 44
          },
          end: {
            line: 11,
            column: 70
          }
        },
        loc: {
          start: {
            line: 11,
            column: 78
          },
          end: {
            line: 19,
            column: 3
          }
        },
        line: 11
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 20,
            column: 70
          },
          end: {
            line: 20,
            column: 71
          }
        },
        loc: {
          start: {
            line: 20,
            column: 82
          },
          end: {
            line: 25,
            column: 3
          }
        },
        line: 20
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 12,
            column: 17
          },
          end: {
            line: 12,
            column: 73
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 12,
            column: 29
          },
          end: {
            line: 12,
            column: 69
          }
        }, {
          start: {
            line: 12,
            column: 72
          },
          end: {
            line: 12,
            column: 73
          }
        }],
        line: 12
      },
      "1": {
        loc: {
          start: {
            line: 23,
            column: 15
          },
          end: {
            line: 23,
            column: 53
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 23,
            column: 48
          },
          end: {
            line: 23,
            column: 49
          }
        }, {
          start: {
            line: 23,
            column: 52
          },
          end: {
            line: 23,
            column: 53
          }
        }],
        line: 23
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0
    },
    b: {
      "0": [0, 0],
      "1": [0, 0]
    },
    inputSourceMap: {
      version: 3,
      file: undefined,
      names: ["react_native_reanimated_1", "require", "useSuggestionAnimation", "itemHeight", "suggestionListHeight", "useSharedValue", "paddingBottomSuggestionList", "updateSuggestionListHeight", "count", "height", "Math", "min", "value", "withTiming", "duration", "animatedStyle", "useAnimatedStyle", "opacity", "exports"],
      sourceRoot: undefined,
      sources: ["/Users/<USER>/projects/react-native/payment-module/src/presentation/payment-bill/hooks/useSuggestionAnimation.ts"],
      sourcesContent: ["import {useSharedValue, withTiming, useAnimatedStyle} from 'react-native-reanimated';\n\nexport const useSuggestionAnimation = (itemHeight: number) => {\n  const suggestionListHeight = useSharedValue(0);\n  const paddingBottomSuggestionList = useSharedValue(0);\n\n  const updateSuggestionListHeight = (count: number) => {\n    const height = count > 0 ? Math.min(count * (itemHeight + 10), 300) : 0;\n    suggestionListHeight.value = withTiming(height, {duration: 300});\n    paddingBottomSuggestionList.value = withTiming(height, {duration: 300});\n  };\n\n  const animatedStyle = useAnimatedStyle(() => ({\n    height: suggestionListHeight.value,\n    opacity: suggestionListHeight.value > 0 ? 1 : 0,\n  }));\n\n  return {\n    animatedStyle, // <<< Gi\u1EEF t\xEAn n\xE0y\n    updateSuggestionListHeight,\n    paddingBottomSuggestionList,\n  };\n};\n"],
      mappings: ";;;;;;AAAA,IAAAA,yBAAA,GAAAC,OAAA;AAEO,IAAMC,sBAAsB,GAAG,SAAzBA,sBAAsBA,CAAIC,UAAkB,EAAI;EAC3D,IAAMC,oBAAoB,GAAG,IAAAJ,yBAAA,CAAAK,cAAc,EAAC,CAAC,CAAC;EAC9C,IAAMC,2BAA2B,GAAG,IAAAN,yBAAA,CAAAK,cAAc,EAAC,CAAC,CAAC;EAErD,IAAME,0BAA0B,GAAG,SAA7BA,0BAA0BA,CAAIC,KAAa,EAAI;IACnD,IAAMC,MAAM,GAAGD,KAAK,GAAG,CAAC,GAAGE,IAAI,CAACC,GAAG,CAACH,KAAK,IAAIL,UAAU,GAAG,EAAE,CAAC,EAAE,GAAG,CAAC,GAAG,CAAC;IACvEC,oBAAoB,CAACQ,KAAK,GAAG,IAAAZ,yBAAA,CAAAa,UAAU,EAACJ,MAAM,EAAE;MAACK,QAAQ,EAAE;IAAG,CAAC,CAAC;IAChER,2BAA2B,CAACM,KAAK,GAAG,IAAAZ,yBAAA,CAAAa,UAAU,EAACJ,MAAM,EAAE;MAACK,QAAQ,EAAE;IAAG,CAAC,CAAC;EACzE,CAAC;EAED,IAAMC,aAAa,GAAG,IAAAf,yBAAA,CAAAgB,gBAAgB,EAAC;IAAA,OAAO;MAC5CP,MAAM,EAAEL,oBAAoB,CAACQ,KAAK;MAClCK,OAAO,EAAEb,oBAAoB,CAACQ,KAAK,GAAG,CAAC,GAAG,CAAC,GAAG;KAC/C;EAAA,CAAC,CAAC;EAEH,OAAO;IACLG,aAAa,EAAbA,aAAa;IACbR,0BAA0B,EAA1BA,0BAA0B;IAC1BD,2BAA2B,EAA3BA;GACD;AACH,CAAC;AApBYY,OAAA,CAAAhB,sBAAsB,GAAAA,sBAAA",
      ignoreList: []
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "2ea9514d9326d1d1ccd2cd82c151839d918f8fad"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_2g5zc0i9hx = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_2g5zc0i9hx();
cov_2g5zc0i9hx().s[0]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_2g5zc0i9hx().s[1]++;
exports.useSuggestionAnimation = void 0;
var react_native_reanimated_1 =
/* istanbul ignore next */
(cov_2g5zc0i9hx().s[2]++, require("react-native-reanimated"));
/* istanbul ignore next */
cov_2g5zc0i9hx().s[3]++;
var useSuggestionAnimation = function useSuggestionAnimation(itemHeight) {
  /* istanbul ignore next */
  cov_2g5zc0i9hx().f[0]++;
  var suggestionListHeight =
  /* istanbul ignore next */
  (cov_2g5zc0i9hx().s[4]++, (0, react_native_reanimated_1.useSharedValue)(0));
  var paddingBottomSuggestionList =
  /* istanbul ignore next */
  (cov_2g5zc0i9hx().s[5]++, (0, react_native_reanimated_1.useSharedValue)(0));
  /* istanbul ignore next */
  cov_2g5zc0i9hx().s[6]++;
  var updateSuggestionListHeight = function updateSuggestionListHeight(count) {
    /* istanbul ignore next */
    cov_2g5zc0i9hx().f[1]++;
    var height =
    /* istanbul ignore next */
    (cov_2g5zc0i9hx().s[7]++, count > 0 ?
    /* istanbul ignore next */
    (cov_2g5zc0i9hx().b[0][0]++, Math.min(count * (itemHeight + 10), 300)) :
    /* istanbul ignore next */
    (cov_2g5zc0i9hx().b[0][1]++, 0));
    /* istanbul ignore next */
    cov_2g5zc0i9hx().s[8]++;
    suggestionListHeight.value = (0, react_native_reanimated_1.withTiming)(height, {
      duration: 300
    });
    /* istanbul ignore next */
    cov_2g5zc0i9hx().s[9]++;
    paddingBottomSuggestionList.value = (0, react_native_reanimated_1.withTiming)(height, {
      duration: 300
    });
  };
  var animatedStyle =
  /* istanbul ignore next */
  (cov_2g5zc0i9hx().s[10]++, (0, react_native_reanimated_1.useAnimatedStyle)(function () {
    /* istanbul ignore next */
    cov_2g5zc0i9hx().f[2]++;
    cov_2g5zc0i9hx().s[11]++;
    return {
      height: suggestionListHeight.value,
      opacity: suggestionListHeight.value > 0 ?
      /* istanbul ignore next */
      (cov_2g5zc0i9hx().b[1][0]++, 1) :
      /* istanbul ignore next */
      (cov_2g5zc0i9hx().b[1][1]++, 0)
    };
  }));
  /* istanbul ignore next */
  cov_2g5zc0i9hx().s[12]++;
  return {
    animatedStyle: animatedStyle,
    updateSuggestionListHeight: updateSuggestionListHeight,
    paddingBottomSuggestionList: paddingBottomSuggestionList
  };
};
/* istanbul ignore next */
cov_2g5zc0i9hx().s[13]++;
exports.useSuggestionAnimation = useSuggestionAnimation;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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