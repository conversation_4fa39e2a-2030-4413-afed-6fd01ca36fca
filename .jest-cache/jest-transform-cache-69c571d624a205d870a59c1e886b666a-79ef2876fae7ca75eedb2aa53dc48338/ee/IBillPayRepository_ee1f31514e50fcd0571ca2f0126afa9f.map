{"version": 3, "names": [], "sources": ["/Users/<USER>/projects/react-native/payment-module/src/domain/repositories/IBillPayRepository.ts"], "sourcesContent": ["import {GetBillDetailModel} from '../entities/get-bill-detail/GetBillDetailModel';\nimport {GetBillDetailRequest} from '../../data/models/get-bill-detail/GetBillDetailRequest';\nimport {BillValidateModel} from '../entities/bill-validate/BillValidateModel';\nimport {MyBillListRequest} from '../../data/models/my-bill-list/MyBillListRequest';\nimport {ProviderListModel} from '../entities/provider-list/ProviderListModel';\nimport {ProviderListRequest} from '../../data/models/provider-list/ProviderListRequest';\nimport {CategoryListModel} from '../entities/category-list/CategoryListModel';\nimport {BaseResponse} from '../../core/BaseResponse';\nimport {BillValidateRequest} from '../../data/models/bill-validate/BillValidateRequest';\nimport {MyBillContactListModel} from '../entities/my-bill-contact-list/MyBillContactListModel';\n\nexport interface IBillPayRepository {\n  categoryList(): Promise<BaseResponse<CategoryListModel>>;\n  providerList(request: ProviderListRequest): Promise<BaseResponse<ProviderListModel>>;\n  myBillList(request: MyBillListRequest): Promise<BaseResponse<MyBillContactListModel>>;\n  getBillDetail(request: GetBillDetailRequest): Promise<BaseResponse<GetBillDetailModel>>;\n  billValidate(request: BillValidateRequest): Promise<BaseResponse<BillValidateModel>>;\n}\n"], "mappings": "", "ignoreList": []}