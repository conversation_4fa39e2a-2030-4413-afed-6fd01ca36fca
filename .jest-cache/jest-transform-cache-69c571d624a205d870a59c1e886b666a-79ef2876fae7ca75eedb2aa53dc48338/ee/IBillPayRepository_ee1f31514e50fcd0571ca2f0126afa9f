6e0eed02f1b03cc55638474609a90875
"use strict";

/* istanbul ignore next */
function cov_1fk3cid08l() {
  var path = "/Users/<USER>/projects/react-native/payment-module/src/domain/repositories/IBillPayRepository.ts";
  var hash = "be1cb1ab05cfa8b3ec8d2ccb33b0a525afb35be7";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/projects/react-native/payment-module/src/domain/repositories/IBillPayRepository.ts",
    statementMap: {
      "0": {
        start: {
          line: 3,
          column: 0
        },
        end: {
          line: 5,
          column: 3
        }
      }
    },
    fnMap: {},
    branchMap: {},
    s: {
      "0": 0
    },
    f: {},
    b: {},
    inputSourceMap: {
      version: 3,
      file: undefined,
      names: [],
      sourceRoot: undefined,
      sources: ["/Users/<USER>/projects/react-native/payment-module/src/domain/repositories/IBillPayRepository.ts"],
      sourcesContent: ["import {GetBillDetailModel} from '../entities/get-bill-detail/GetBillDetailModel';\nimport {GetBillDetailRequest} from '../../data/models/get-bill-detail/GetBillDetailRequest';\nimport {BillValidateModel} from '../entities/bill-validate/BillValidateModel';\nimport {MyBillListRequest} from '../../data/models/my-bill-list/MyBillListRequest';\nimport {ProviderListModel} from '../entities/provider-list/ProviderListModel';\nimport {ProviderListRequest} from '../../data/models/provider-list/ProviderListRequest';\nimport {CategoryListModel} from '../entities/category-list/CategoryListModel';\nimport {BaseResponse} from '../../core/BaseResponse';\nimport {BillValidateRequest} from '../../data/models/bill-validate/BillValidateRequest';\nimport {MyBillContactListModel} from '../entities/my-bill-contact-list/MyBillContactListModel';\n\nexport interface IBillPayRepository {\n  categoryList(): Promise<BaseResponse<CategoryListModel>>;\n  providerList(request: ProviderListRequest): Promise<BaseResponse<ProviderListModel>>;\n  myBillList(request: MyBillListRequest): Promise<BaseResponse<MyBillContactListModel>>;\n  getBillDetail(request: GetBillDetailRequest): Promise<BaseResponse<GetBillDetailModel>>;\n  billValidate(request: BillValidateRequest): Promise<BaseResponse<BillValidateModel>>;\n}\n"],
      mappings: "",
      ignoreList: []
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "be1cb1ab05cfa8b3ec8d2ccb33b0a525afb35be7"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_1fk3cid08l = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_1fk3cid08l();
cov_1fk3cid08l().s[0]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6W10sInNvdXJjZXMiOlsiL1VzZXJzL3NvbnNhbXNldC9wcm9qZWN0cy9yZWFjdC1uYXRpdmUvcGF5bWVudC1tb2R1bGUvc3JjL2RvbWFpbi9yZXBvc2l0b3JpZXMvSUJpbGxQYXlSZXBvc2l0b3J5LnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7R2V0QmlsbERldGFpbE1vZGVsfSBmcm9tICcuLi9lbnRpdGllcy9nZXQtYmlsbC1kZXRhaWwvR2V0QmlsbERldGFpbE1vZGVsJztcbmltcG9ydCB7R2V0QmlsbERldGFpbFJlcXVlc3R9IGZyb20gJy4uLy4uL2RhdGEvbW9kZWxzL2dldC1iaWxsLWRldGFpbC9HZXRCaWxsRGV0YWlsUmVxdWVzdCc7XG5pbXBvcnQge0JpbGxWYWxpZGF0ZU1vZGVsfSBmcm9tICcuLi9lbnRpdGllcy9iaWxsLXZhbGlkYXRlL0JpbGxWYWxpZGF0ZU1vZGVsJztcbmltcG9ydCB7TXlCaWxsTGlzdFJlcXVlc3R9IGZyb20gJy4uLy4uL2RhdGEvbW9kZWxzL215LWJpbGwtbGlzdC9NeUJpbGxMaXN0UmVxdWVzdCc7XG5pbXBvcnQge1Byb3ZpZGVyTGlzdE1vZGVsfSBmcm9tICcuLi9lbnRpdGllcy9wcm92aWRlci1saXN0L1Byb3ZpZGVyTGlzdE1vZGVsJztcbmltcG9ydCB7UHJvdmlkZXJMaXN0UmVxdWVzdH0gZnJvbSAnLi4vLi4vZGF0YS9tb2RlbHMvcHJvdmlkZXItbGlzdC9Qcm92aWRlckxpc3RSZXF1ZXN0JztcbmltcG9ydCB7Q2F0ZWdvcnlMaXN0TW9kZWx9IGZyb20gJy4uL2VudGl0aWVzL2NhdGVnb3J5LWxpc3QvQ2F0ZWdvcnlMaXN0TW9kZWwnO1xuaW1wb3J0IHtCYXNlUmVzcG9uc2V9IGZyb20gJy4uLy4uL2NvcmUvQmFzZVJlc3BvbnNlJztcbmltcG9ydCB7QmlsbFZhbGlkYXRlUmVxdWVzdH0gZnJvbSAnLi4vLi4vZGF0YS9tb2RlbHMvYmlsbC12YWxpZGF0ZS9CaWxsVmFsaWRhdGVSZXF1ZXN0JztcbmltcG9ydCB7TXlCaWxsQ29udGFjdExpc3RNb2RlbH0gZnJvbSAnLi4vZW50aXRpZXMvbXktYmlsbC1jb250YWN0LWxpc3QvTXlCaWxsQ29udGFjdExpc3RNb2RlbCc7XG5cbmV4cG9ydCBpbnRlcmZhY2UgSUJpbGxQYXlSZXBvc2l0b3J5IHtcbiAgY2F0ZWdvcnlMaXN0KCk6IFByb21pc2U8QmFzZVJlc3BvbnNlPENhdGVnb3J5TGlzdE1vZGVsPj47XG4gIHByb3ZpZGVyTGlzdChyZXF1ZXN0OiBQcm92aWRlckxpc3RSZXF1ZXN0KTogUHJvbWlzZTxCYXNlUmVzcG9uc2U8UHJvdmlkZXJMaXN0TW9kZWw+PjtcbiAgbXlCaWxsTGlzdChyZXF1ZXN0OiBNeUJpbGxMaXN0UmVxdWVzdCk6IFByb21pc2U8QmFzZVJlc3BvbnNlPE15QmlsbENvbnRhY3RMaXN0TW9kZWw+PjtcbiAgZ2V0QmlsbERldGFpbChyZXF1ZXN0OiBHZXRCaWxsRGV0YWlsUmVxdWVzdCk6IFByb21pc2U8QmFzZVJlc3BvbnNlPEdldEJpbGxEZXRhaWxNb2RlbD4+O1xuICBiaWxsVmFsaWRhdGUocmVxdWVzdDogQmlsbFZhbGlkYXRlUmVxdWVzdCk6IFByb21pc2U8QmFzZVJlc3BvbnNlPEJpbGxWYWxpZGF0ZU1vZGVsPj47XG59XG4iXSwibWFwcGluZ3MiOiIiLCJpZ25vcmVMaXN0IjpbXX0=