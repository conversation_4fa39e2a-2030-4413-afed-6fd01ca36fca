{"version": 3, "names": ["cov_2g5zc0i9hx", "actualCoverage", "s", "react_native_reanimated_1", "require", "useSuggestionAnimation", "itemHeight", "f", "suggestionListHeight", "useSharedValue", "paddingBottomSuggestionList", "updateSuggestionListHeight", "count", "height", "b", "Math", "min", "value", "withTiming", "duration", "animatedStyle", "useAnimatedStyle", "opacity", "exports"], "sources": ["/Users/<USER>/projects/react-native/payment-module/src/presentation/payment-bill/hooks/useSuggestionAnimation.ts"], "sourcesContent": ["import {useSharedValue, withTiming, useAnimatedStyle} from 'react-native-reanimated';\n\nexport const useSuggestionAnimation = (itemHeight: number) => {\n  const suggestionListHeight = useSharedValue(0);\n  const paddingBottomSuggestionList = useSharedValue(0);\n\n  const updateSuggestionListHeight = (count: number) => {\n    const height = count > 0 ? Math.min(count * (itemHeight + 10), 300) : 0;\n    suggestionListHeight.value = withTiming(height, {duration: 300});\n    paddingBottomSuggestionList.value = withTiming(height, {duration: 300});\n  };\n\n  const animatedStyle = useAnimatedStyle(() => ({\n    height: suggestionListHeight.value,\n    opacity: suggestionListHeight.value > 0 ? 1 : 0,\n  }));\n\n  return {\n    animatedStyle, // <<< Giữ tên này\n    updateSuggestionListHeight,\n    paddingBottomSuggestionList,\n  };\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IASI;IAAAA,cAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,cAAA;AAAAA,cAAA,GAAAE,CAAA;;;;;;;AATJ,IAAAC,yBAAA;AAAA;AAAA,CAAAH,cAAA,GAAAE,CAAA,OAAAE,OAAA;AAAA;AAAAJ,cAAA,GAAAE,CAAA;AAEO,IAAMG,sBAAsB,GAAG,SAAzBA,sBAAsBA,CAAIC,UAAkB,EAAI;EAAA;EAAAN,cAAA,GAAAO,CAAA;EAC3D,IAAMC,oBAAoB;EAAA;EAAA,CAAAR,cAAA,GAAAE,CAAA,OAAG,IAAAC,yBAAA,CAAAM,cAAc,EAAC,CAAC,CAAC;EAC9C,IAAMC,2BAA2B;EAAA;EAAA,CAAAV,cAAA,GAAAE,CAAA,OAAG,IAAAC,yBAAA,CAAAM,cAAc,EAAC,CAAC,CAAC;EAAA;EAAAT,cAAA,GAAAE,CAAA;EAErD,IAAMS,0BAA0B,GAAG,SAA7BA,0BAA0BA,CAAIC,KAAa,EAAI;IAAA;IAAAZ,cAAA,GAAAO,CAAA;IACnD,IAAMM,MAAM;IAAA;IAAA,CAAAb,cAAA,GAAAE,CAAA,OAAGU,KAAK,GAAG,CAAC;IAAA;IAAA,CAAAZ,cAAA,GAAAc,CAAA,UAAGC,IAAI,CAACC,GAAG,CAACJ,KAAK,IAAIN,UAAU,GAAG,EAAE,CAAC,EAAE,GAAG,CAAC;IAAA;IAAA,CAAAN,cAAA,GAAAc,CAAA,UAAG,CAAC;IAAA;IAAAd,cAAA,GAAAE,CAAA;IACvEM,oBAAoB,CAACS,KAAK,GAAG,IAAAd,yBAAA,CAAAe,UAAU,EAACL,MAAM,EAAE;MAACM,QAAQ,EAAE;IAAG,CAAC,CAAC;IAAA;IAAAnB,cAAA,GAAAE,CAAA;IAChEQ,2BAA2B,CAACO,KAAK,GAAG,IAAAd,yBAAA,CAAAe,UAAU,EAACL,MAAM,EAAE;MAACM,QAAQ,EAAE;IAAG,CAAC,CAAC;EACzE,CAAC;EAED,IAAMC,aAAa;EAAA;EAAA,CAAApB,cAAA,GAAAE,CAAA,QAAG,IAAAC,yBAAA,CAAAkB,gBAAgB,EAAC;IAAA;IAAArB,cAAA,GAAAO,CAAA;IAAAP,cAAA,GAAAE,CAAA;IAAA,OAAO;MAC5CW,MAAM,EAAEL,oBAAoB,CAACS,KAAK;MAClCK,OAAO,EAAEd,oBAAoB,CAACS,KAAK,GAAG,CAAC;MAAA;MAAA,CAAAjB,cAAA,GAAAc,CAAA,UAAG,CAAC;MAAA;MAAA,CAAAd,cAAA,GAAAc,CAAA,UAAG;KAC/C;EAAA,CAAC,CAAC;EAAA;EAAAd,cAAA,GAAAE,CAAA;EAEH,OAAO;IACLkB,aAAa,EAAbA,aAAa;IACbT,0BAA0B,EAA1BA,0BAA0B;IAC1BD,2BAA2B,EAA3BA;GACD;AACH,CAAC;AAAA;AAAAV,cAAA,GAAAE,CAAA;AApBYqB,OAAA,CAAAlB,sBAAsB,GAAAA,sBAAA", "ignoreList": []}