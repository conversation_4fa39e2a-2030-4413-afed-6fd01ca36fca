0d3e283bc1af96e038e3089dcba3a35e
"use strict";

/* istanbul ignore next */
function cov_25altp5hc8() {
  var path = "/Users/<USER>/projects/react-native/payment-module/src/data/mappers/get-my-bill-history-list/GetMyBillHistoryListMapper.ts";
  var hash = "9e9d75b262ad7cc7db8e82d5241fcb609ff02b67";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/projects/react-native/payment-module/src/data/mappers/get-my-bill-history-list/GetMyBillHistoryListMapper.ts",
    statementMap: {
      "0": {
        start: {
          line: 3,
          column: 29
        },
        end: {
          line: 3,
          column: 84
        }
      },
      "1": {
        start: {
          line: 4,
          column: 22
        },
        end: {
          line: 4,
          column: 93
        }
      },
      "2": {
        start: {
          line: 5,
          column: 22
        },
        end: {
          line: 9,
          column: 1
        }
      },
      "3": {
        start: {
          line: 6,
          column: 2
        },
        end: {
          line: 8,
          column: 4
        }
      },
      "4": {
        start: {
          line: 10,
          column: 0
        },
        end: {
          line: 12,
          column: 3
        }
      },
      "5": {
        start: {
          line: 13,
          column: 0
        },
        end: {
          line: 13,
          column: 88
        }
      },
      "6": {
        start: {
          line: 14,
          column: 15
        },
        end: {
          line: 14,
          column: 56
        }
      },
      "7": {
        start: {
          line: 15,
          column: 18
        },
        end: {
          line: 24,
          column: 1
        }
      },
      "8": {
        start: {
          line: 16,
          column: 2
        },
        end: {
          line: 18,
          column: 3
        }
      },
      "9": {
        start: {
          line: 17,
          column: 4
        },
        end: {
          line: 17,
          column: 14
        }
      },
      "10": {
        start: {
          line: 19,
          column: 12
        },
        end: {
          line: 19,
          column: 64
        }
      },
      "11": {
        start: {
          line: 20,
          column: 2
        },
        end: {
          line: 22,
          column: 3
        }
      },
      "12": {
        start: {
          line: 21,
          column: 4
        },
        end: {
          line: 21,
          column: 14
        }
      },
      "13": {
        start: {
          line: 23,
          column: 2
        },
        end: {
          line: 23,
          column: 57
        }
      },
      "14": {
        start: {
          line: 25,
          column: 17
        },
        end: {
          line: 27,
          column: 1
        }
      },
      "15": {
        start: {
          line: 26,
          column: 2
        },
        end: {
          line: 26,
          column: 72
        }
      },
      "16": {
        start: {
          line: 30,
          column: 16
        },
        end: {
          line: 45,
          column: 41
        }
      },
      "17": {
        start: {
          line: 32,
          column: 18
        },
        end: {
          line: 32,
          column: 207
        }
      },
      "18": {
        start: {
          line: 33,
          column: 4
        },
        end: {
          line: 35,
          column: 5
        }
      },
      "19": {
        start: {
          line: 34,
          column: 6
        },
        end: {
          line: 34,
          column: 24
        }
      },
      "20": {
        start: {
          line: 36,
          column: 4
        },
        end: {
          line: 43,
          column: 7
        }
      },
      "21": {
        start: {
          line: 44,
          column: 4
        },
        end: {
          line: 44,
          column: 15
        }
      },
      "22": {
        start: {
          line: 46,
          column: 20
        },
        end: {
          line: 54,
          column: 4
        }
      },
      "23": {
        start: {
          line: 47,
          column: 16
        },
        end: {
          line: 47,
          column: 53
        }
      },
      "24": {
        start: {
          line: 48,
          column: 13
        },
        end: {
          line: 48,
          column: 21
        }
      },
      "25": {
        start: {
          line: 49,
          column: 14
        },
        end: {
          line: 49,
          column: 22
        }
      },
      "26": {
        start: {
          line: 50,
          column: 4
        },
        end: {
          line: 53,
          column: 6
        }
      },
      "27": {
        start: {
          line: 55,
          column: 2
        },
        end: {
          line: 58,
          column: 4
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 5,
            column: 54
          },
          end: {
            line: 5,
            column: 55
          }
        },
        loc: {
          start: {
            line: 5,
            column: 69
          },
          end: {
            line: 9,
            column: 1
          }
        },
        line: 5
      },
      "1": {
        name: "formatPrice",
        decl: {
          start: {
            line: 15,
            column: 27
          },
          end: {
            line: 15,
            column: 38
          }
        },
        loc: {
          start: {
            line: 15,
            column: 47
          },
          end: {
            line: 24,
            column: 1
          }
        },
        line: 15
      },
      "2": {
        name: "formatDate",
        decl: {
          start: {
            line: 25,
            column: 26
          },
          end: {
            line: 25,
            column: 36
          }
        },
        loc: {
          start: {
            line: 25,
            column: 43
          },
          end: {
            line: 27,
            column: 1
          }
        },
        line: 25
      },
      "3": {
        name: "mapGetMyBillHistoryListResponseToModel",
        decl: {
          start: {
            line: 28,
            column: 9
          },
          end: {
            line: 28,
            column: 47
          }
        },
        loc: {
          start: {
            line: 28,
            column: 58
          },
          end: {
            line: 59,
            column: 1
          }
        },
        line: 28
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 30,
            column: 80
          },
          end: {
            line: 30,
            column: 81
          }
        },
        loc: {
          start: {
            line: 30,
            column: 101
          },
          end: {
            line: 45,
            column: 3
          }
        },
        line: 30
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 46,
            column: 48
          },
          end: {
            line: 46,
            column: 49
          }
        },
        loc: {
          start: {
            line: 46,
            column: 64
          },
          end: {
            line: 54,
            column: 3
          }
        },
        line: 46
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 5,
            column: 22
          },
          end: {
            line: 9,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 5,
            column: 22
          },
          end: {
            line: 5,
            column: 26
          }
        }, {
          start: {
            line: 5,
            column: 30
          },
          end: {
            line: 5,
            column: 50
          }
        }, {
          start: {
            line: 5,
            column: 54
          },
          end: {
            line: 9,
            column: 1
          }
        }],
        line: 5
      },
      "1": {
        loc: {
          start: {
            line: 6,
            column: 9
          },
          end: {
            line: 8,
            column: 3
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 6,
            column: 33
          },
          end: {
            line: 6,
            column: 36
          }
        }, {
          start: {
            line: 6,
            column: 39
          },
          end: {
            line: 8,
            column: 3
          }
        }],
        line: 6
      },
      "2": {
        loc: {
          start: {
            line: 6,
            column: 9
          },
          end: {
            line: 6,
            column: 30
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 6,
            column: 9
          },
          end: {
            line: 6,
            column: 12
          }
        }, {
          start: {
            line: 6,
            column: 16
          },
          end: {
            line: 6,
            column: 30
          }
        }],
        line: 6
      },
      "3": {
        loc: {
          start: {
            line: 16,
            column: 2
          },
          end: {
            line: 18,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 16,
            column: 2
          },
          end: {
            line: 18,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 16
      },
      "4": {
        loc: {
          start: {
            line: 16,
            column: 6
          },
          end: {
            line: 16,
            column: 43
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 16,
            column: 6
          },
          end: {
            line: 16,
            column: 20
          }
        }, {
          start: {
            line: 16,
            column: 24
          },
          end: {
            line: 16,
            column: 43
          }
        }],
        line: 16
      },
      "5": {
        loc: {
          start: {
            line: 19,
            column: 12
          },
          end: {
            line: 19,
            column: 64
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 19,
            column: 41
          },
          end: {
            line: 19,
            column: 55
          }
        }, {
          start: {
            line: 19,
            column: 58
          },
          end: {
            line: 19,
            column: 64
          }
        }],
        line: 19
      },
      "6": {
        loc: {
          start: {
            line: 20,
            column: 2
          },
          end: {
            line: 22,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 20,
            column: 2
          },
          end: {
            line: 22,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 20
      },
      "7": {
        loc: {
          start: {
            line: 30,
            column: 16
          },
          end: {
            line: 45,
            column: 41
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 45,
            column: 20
          },
          end: {
            line: 45,
            column: 36
          }
        }, {
          start: {
            line: 45,
            column: 39
          },
          end: {
            line: 45,
            column: 41
          }
        }],
        line: 30
      },
      "8": {
        loc: {
          start: {
            line: 30,
            column: 36
          },
          end: {
            line: 45,
            column: 8
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 30,
            column: 55
          },
          end: {
            line: 30,
            column: 61
          }
        }, {
          start: {
            line: 30,
            column: 64
          },
          end: {
            line: 45,
            column: 8
          }
        }],
        line: 30
      },
      "9": {
        loc: {
          start: {
            line: 32,
            column: 18
          },
          end: {
            line: 32,
            column: 207
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 32,
            column: 19
          },
          end: {
            line: 32,
            column: 108
          }
        }, {
          start: {
            line: 32,
            column: 114
          },
          end: {
            line: 32,
            column: 206
          }
        }],
        line: 32
      },
      "10": {
        loc: {
          start: {
            line: 32,
            column: 19
          },
          end: {
            line: 32,
            column: 108
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 32,
            column: 68
          },
          end: {
            line: 32,
            column: 74
          }
        }, {
          start: {
            line: 32,
            column: 77
          },
          end: {
            line: 32,
            column: 108
          }
        }],
        line: 32
      },
      "11": {
        loc: {
          start: {
            line: 32,
            column: 114
          },
          end: {
            line: 32,
            column: 206
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 32,
            column: 165
          },
          end: {
            line: 32,
            column: 171
          }
        }, {
          start: {
            line: 32,
            column: 174
          },
          end: {
            line: 32,
            column: 206
          }
        }],
        line: 32
      },
      "12": {
        loc: {
          start: {
            line: 33,
            column: 4
          },
          end: {
            line: 35,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 33,
            column: 4
          },
          end: {
            line: 35,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 33
      },
      "13": {
        loc: {
          start: {
            line: 38,
            column: 17
          },
          end: {
            line: 38,
            column: 76
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 38,
            column: 17
          },
          end: {
            line: 38,
            column: 38
          }
        }, {
          start: {
            line: 38,
            column: 42
          },
          end: {
            line: 38,
            column: 59
          }
        }, {
          start: {
            line: 38,
            column: 63
          },
          end: {
            line: 38,
            column: 76
          }
        }],
        line: 38
      },
      "14": {
        loc: {
          start: {
            line: 39,
            column: 15
          },
          end: {
            line: 39,
            column: 87
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 39,
            column: 15
          },
          end: {
            line: 39,
            column: 31
          }
        }, {
          start: {
            line: 39,
            column: 35
          },
          end: {
            line: 39,
            column: 47
          }
        }, {
          start: {
            line: 39,
            column: 51
          },
          end: {
            line: 39,
            column: 87
          }
        }],
        line: 39
      },
      "15": {
        loc: {
          start: {
            line: 40,
            column: 14
          },
          end: {
            line: 40,
            column: 364
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 40,
            column: 53
          },
          end: {
            line: 40,
            column: 206
          }
        }, {
          start: {
            line: 40,
            column: 209
          },
          end: {
            line: 40,
            column: 364
          }
        }],
        line: 40
      },
      "16": {
        loc: {
          start: {
            line: 40,
            column: 71
          },
          end: {
            line: 40,
            column: 202
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 40,
            column: 72
          },
          end: {
            line: 40,
            column: 176
          }
        }, {
          start: {
            line: 40,
            column: 181
          },
          end: {
            line: 40,
            column: 197
          }
        }, {
          start: {
            line: 40,
            column: 201
          },
          end: {
            line: 40,
            column: 202
          }
        }],
        line: 40
      },
      "17": {
        loc: {
          start: {
            line: 40,
            column: 72
          },
          end: {
            line: 40,
            column: 176
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 40,
            column: 139
          },
          end: {
            line: 40,
            column: 145
          }
        }, {
          start: {
            line: 40,
            column: 148
          },
          end: {
            line: 40,
            column: 176
          }
        }],
        line: 40
      },
      "18": {
        loc: {
          start: {
            line: 40,
            column: 227
          },
          end: {
            line: 40,
            column: 360
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 40,
            column: 228
          },
          end: {
            line: 40,
            column: 334
          }
        }, {
          start: {
            line: 40,
            column: 339
          },
          end: {
            line: 40,
            column: 355
          }
        }, {
          start: {
            line: 40,
            column: 359
          },
          end: {
            line: 40,
            column: 360
          }
        }],
        line: 40
      },
      "19": {
        loc: {
          start: {
            line: 40,
            column: 228
          },
          end: {
            line: 40,
            column: 334
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 40,
            column: 296
          },
          end: {
            line: 40,
            column: 302
          }
        }, {
          start: {
            line: 40,
            column: 305
          },
          end: {
            line: 40,
            column: 334
          }
        }],
        line: 40
      },
      "20": {
        loc: {
          start: {
            line: 41,
            column: 17
          },
          end: {
            line: 41,
            column: 197
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 41,
            column: 37
          },
          end: {
            line: 41,
            column: 104
          }
        }, {
          start: {
            line: 41,
            column: 107
          },
          end: {
            line: 41,
            column: 197
          }
        }],
        line: 41
      },
      "21": {
        loc: {
          start: {
            line: 41,
            column: 107
          },
          end: {
            line: 41,
            column: 197
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 41,
            column: 126
          },
          end: {
            line: 41,
            column: 192
          }
        }, {
          start: {
            line: 41,
            column: 195
          },
          end: {
            line: 41,
            column: 197
          }
        }],
        line: 41
      },
      "22": {
        loc: {
          start: {
            line: 42,
            column: 20
          },
          end: {
            line: 42,
            column: 57
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 42,
            column: 20
          },
          end: {
            line: 42,
            column: 37
          }
        }, {
          start: {
            line: 42,
            column: 41
          },
          end: {
            line: 42,
            column: 57
          }
        }],
        line: 42
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0
    },
    b: {
      "0": [0, 0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0, 0],
      "14": [0, 0, 0],
      "15": [0, 0],
      "16": [0, 0, 0],
      "17": [0, 0],
      "18": [0, 0, 0],
      "19": [0, 0],
      "20": [0, 0],
      "21": [0, 0],
      "22": [0, 0]
    },
    inputSourceMap: {
      version: 3,
      file: undefined,
      names: ["exports", "mapGetMyBillHistoryListResponseToModel", "moment_1", "__importDefault", "require", "formatPrice", "amount", "undefined", "num", "Number", "isNaN", "toLocaleString", "replace", "formatDate", "date", "default", "format", "response", "_response$reduce", "grouped", "reduce", "acc", "bill", "_bill$paymentDate", "_bill$creationTime", "_bill$transactionAmou", "_bill$transactionAmou2", "dateKey", "paymentDate", "split", "creationTime", "push", "id", "transName", "counterPartyName", "customerName", "billCode", "content", "description", "serviceCode", "creditDebitIndicator", "transactionAmountCurrency", "totalAmount", "transDate", "toString", "creationDate", "billHistory", "Object", "entries", "map", "_ref", "_ref2", "_slicedToArray2", "bills", "title", "data", "billHistoryDTO"],
      sourceRoot: undefined,
      sources: ["/Users/<USER>/projects/react-native/payment-module/src/data/mappers/get-my-bill-history-list/GetMyBillHistoryListMapper.ts"],
      sourcesContent: ["import {\n  BillData,\n  BillHistoryDTO,\n  BillHistoryModel,\n} from '../../../domain/entities/get-my-bill-history-list/GetMyBillHistoryListModel.ts';\nimport {GetMyBillHistoryListResponse} from '../../models/get-my-bill-history-list/GetMyBillHistoryListResponse.ts';\nimport moment from 'moment/moment';\n\n// Utility functions copied from account module\nconst formatPrice = (amount: number | string | null | undefined) => {\n  if (amount == null || amount == undefined) {\n    return '';\n  }\n  const num = typeof amount === 'string' ? Number(amount) : amount;\n  if (isNaN(num)) {\n    return '';\n  }\n  return num.toLocaleString('vi-VN').replace(/\\./g, ',');\n};\n\nconst formatDate = (date: string): string => {\n  return moment(date, 'YYYY-MM-DD').format('DD/MM/YYYY');\n};\n\nexport function mapGetMyBillHistoryListResponseToModel(response: GetMyBillHistoryListResponse): BillHistoryModel {\n  // Group bills by paymentDate\n  const grouped =\n    response?.reduce((acc, bill) => {\n      const dateKey = bill.paymentDate?.split('T')[0] || bill.creationTime?.split('T')[0]; // Extract date part\n      if (!acc[dateKey]) {\n        acc[dateKey] = [];\n      }\n      acc[dateKey].push({\n        id: bill.id,\n        transName: bill.counterPartyName || bill.customerName || bill.billCode,\n        content: bill.description || bill.content || `Bill Payment - ${bill.serviceCode}`,\n        amount:\n          bill.creditDebitIndicator === 'CRDT'\n            ? `+${formatPrice(+(bill.transactionAmountCurrency?.amount || bill.totalAmount || 0))}`\n            : `-${formatPrice(+(bill.transactionAmountCurrency?.amount || bill.totalAmount || 0))}`,\n        transDate: bill.creationTime\n          ? moment(bill.creationTime).format('HH:mm').toString()\n          : bill.paymentDate\n          ? moment(bill.paymentDate).format('HH:mm').toString()\n          : '',\n        creationDate: bill.creationTime || bill.paymentDate,\n      });\n      return acc;\n    }, {} as Record<string, BillData[]>) ?? [];\n\n  // Convert grouped object to DTO structure\n  const billHistory: BillHistoryDTO[] = Object.entries(grouped).map(([date, bills]) => ({\n    title: formatDate(date),\n    data: bills,\n  }));\n\n  return {\n    billHistoryDTO: billHistory,\n    billHistory: response,\n  };\n}\n"],
      mappings: ";;;;;;;;;;;;AAwBAA,OAAA,CAAAC,sCAAA,GAAAA,sCAAA;AAlBA,IAAAC,QAAA,GAAAC,eAAA,CAAAC,OAAA;AAGA,IAAMC,WAAW,GAAG,SAAdA,WAAWA,CAAIC,MAA0C,EAAI;EACjE,IAAIA,MAAM,IAAI,IAAI,IAAIA,MAAM,IAAIC,SAAS,EAAE;IACzC,OAAO,EAAE;EACX;EACA,IAAMC,GAAG,GAAG,OAAOF,MAAM,KAAK,QAAQ,GAAGG,MAAM,CAACH,MAAM,CAAC,GAAGA,MAAM;EAChE,IAAII,KAAK,CAACF,GAAG,CAAC,EAAE;IACd,OAAO,EAAE;EACX;EACA,OAAOA,GAAG,CAACG,cAAc,CAAC,OAAO,CAAC,CAACC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC;AACxD,CAAC;AAED,IAAMC,UAAU,GAAG,SAAbA,UAAUA,CAAIC,IAAY,EAAY;EAC1C,OAAO,IAAAZ,QAAA,CAAAa,OAAM,EAACD,IAAI,EAAE,YAAY,CAAC,CAACE,MAAM,CAAC,YAAY,CAAC;AACxD,CAAC;AAED,SAAgBf,sCAAsCA,CAACgB,QAAsC;EAAA,IAAAC,gBAAA;EAE3F,IAAMC,OAAO,IAAAD,gBAAA,GACXD,QAAQ,oBAARA,QAAQ,CAAEG,MAAM,CAAC,UAACC,GAAG,EAAEC,IAAI,EAAI;IAAA,IAAAC,iBAAA,EAAAC,kBAAA,EAAAC,qBAAA,EAAAC,sBAAA;IAC7B,IAAMC,OAAO,GAAG,EAAAJ,iBAAA,GAAAD,IAAI,CAACM,WAAW,qBAAhBL,iBAAA,CAAkBM,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,OAAAL,kBAAA,GAAIF,IAAI,CAACQ,YAAY,qBAAjBN,kBAAA,CAAmBK,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IACnF,IAAI,CAACR,GAAG,CAACM,OAAO,CAAC,EAAE;MACjBN,GAAG,CAACM,OAAO,CAAC,GAAG,EAAE;IACnB;IACAN,GAAG,CAACM,OAAO,CAAC,CAACI,IAAI,CAAC;MAChBC,EAAE,EAAEV,IAAI,CAACU,EAAE;MACXC,SAAS,EAAEX,IAAI,CAACY,gBAAgB,IAAIZ,IAAI,CAACa,YAAY,IAAIb,IAAI,CAACc,QAAQ;MACtEC,OAAO,EAAEf,IAAI,CAACgB,WAAW,IAAIhB,IAAI,CAACe,OAAO,IAAI,kBAAkBf,IAAI,CAACiB,WAAW,EAAE;MACjFjC,MAAM,EACJgB,IAAI,CAACkB,oBAAoB,KAAK,MAAM,GAChC,IAAInC,WAAW,CAAC,EAAE,EAAAoB,qBAAA,GAAAH,IAAI,CAACmB,yBAAyB,qBAA9BhB,qBAAA,CAAgCnB,MAAM,KAAIgB,IAAI,CAACoB,WAAW,IAAI,CAAC,CAAC,CAAC,EAAE,GACrF,IAAIrC,WAAW,CAAC,EAAE,EAAAqB,sBAAA,GAAAJ,IAAI,CAACmB,yBAAyB,qBAA9Bf,sBAAA,CAAgCpB,MAAM,KAAIgB,IAAI,CAACoB,WAAW,IAAI,CAAC,CAAC,CAAC,EAAE;MAC3FC,SAAS,EAAErB,IAAI,CAACQ,YAAY,GACxB,IAAA5B,QAAA,CAAAa,OAAM,EAACO,IAAI,CAACQ,YAAY,CAAC,CAACd,MAAM,CAAC,OAAO,CAAC,CAAC4B,QAAQ,EAAE,GACpDtB,IAAI,CAACM,WAAW,GAChB,IAAA1B,QAAA,CAAAa,OAAM,EAACO,IAAI,CAACM,WAAW,CAAC,CAACZ,MAAM,CAAC,OAAO,CAAC,CAAC4B,QAAQ,EAAE,GACnD,EAAE;MACNC,YAAY,EAAEvB,IAAI,CAACQ,YAAY,IAAIR,IAAI,CAACM;KACzC,CAAC;IACF,OAAOP,GAAG;EACZ,CAAC,EAAE,EAAgC,CAAC,YAAAH,gBAAA,GAAI,EAAE;EAG5C,IAAM4B,WAAW,GAAqBC,MAAM,CAACC,OAAO,CAAC7B,OAAO,CAAC,CAAC8B,GAAG,CAAC,UAAAC,IAAA;IAAA,IAAAC,KAAA,OAAAC,eAAA,CAAArC,OAAA,EAAAmC,IAAA;MAAEpC,IAAI,GAAAqC,KAAA;MAAEE,KAAK,GAAAF,KAAA;IAAA,OAAO;MACpFG,KAAK,EAAEzC,UAAU,CAACC,IAAI,CAAC;MACvByC,IAAI,EAAEF;KACP;EAAA,CAAC,CAAC;EAEH,OAAO;IACLG,cAAc,EAAEV,WAAW;IAC3BA,WAAW,EAAE7B;GACd;AACH",
      ignoreList: []
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "9e9d75b262ad7cc7db8e82d5241fcb609ff02b67"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_25altp5hc8 = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_25altp5hc8();
var _interopRequireDefault =
/* istanbul ignore next */
(cov_25altp5hc8().s[0]++, require("@babel/runtime/helpers/interopRequireDefault"));
var _slicedToArray2 =
/* istanbul ignore next */
(cov_25altp5hc8().s[1]++, _interopRequireDefault(require("@babel/runtime/helpers/slicedToArray")));
var __importDefault =
/* istanbul ignore next */
(cov_25altp5hc8().s[2]++,
/* istanbul ignore next */
(cov_25altp5hc8().b[0][0]++, this) &&
/* istanbul ignore next */
(cov_25altp5hc8().b[0][1]++, this.__importDefault) ||
/* istanbul ignore next */
(cov_25altp5hc8().b[0][2]++, function (mod) {
  /* istanbul ignore next */
  cov_25altp5hc8().f[0]++;
  cov_25altp5hc8().s[3]++;
  return /* istanbul ignore next */(cov_25altp5hc8().b[2][0]++, mod) &&
  /* istanbul ignore next */
  (cov_25altp5hc8().b[2][1]++, mod.__esModule) ?
  /* istanbul ignore next */
  (cov_25altp5hc8().b[1][0]++, mod) :
  /* istanbul ignore next */
  (cov_25altp5hc8().b[1][1]++, {
    "default": mod
  });
}));
/* istanbul ignore next */
cov_25altp5hc8().s[4]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_25altp5hc8().s[5]++;
exports.mapGetMyBillHistoryListResponseToModel = mapGetMyBillHistoryListResponseToModel;
var moment_1 =
/* istanbul ignore next */
(cov_25altp5hc8().s[6]++, __importDefault(require("moment/moment")));
/* istanbul ignore next */
cov_25altp5hc8().s[7]++;
var formatPrice = function formatPrice(amount) {
  /* istanbul ignore next */
  cov_25altp5hc8().f[1]++;
  cov_25altp5hc8().s[8]++;
  if (
  /* istanbul ignore next */
  (cov_25altp5hc8().b[4][0]++, amount == null) ||
  /* istanbul ignore next */
  (cov_25altp5hc8().b[4][1]++, amount == undefined)) {
    /* istanbul ignore next */
    cov_25altp5hc8().b[3][0]++;
    cov_25altp5hc8().s[9]++;
    return '';
  } else
  /* istanbul ignore next */
  {
    cov_25altp5hc8().b[3][1]++;
  }
  var num =
  /* istanbul ignore next */
  (cov_25altp5hc8().s[10]++, typeof amount === 'string' ?
  /* istanbul ignore next */
  (cov_25altp5hc8().b[5][0]++, Number(amount)) :
  /* istanbul ignore next */
  (cov_25altp5hc8().b[5][1]++, amount));
  /* istanbul ignore next */
  cov_25altp5hc8().s[11]++;
  if (isNaN(num)) {
    /* istanbul ignore next */
    cov_25altp5hc8().b[6][0]++;
    cov_25altp5hc8().s[12]++;
    return '';
  } else
  /* istanbul ignore next */
  {
    cov_25altp5hc8().b[6][1]++;
  }
  cov_25altp5hc8().s[13]++;
  return num.toLocaleString('vi-VN').replace(/\./g, ',');
};
/* istanbul ignore next */
cov_25altp5hc8().s[14]++;
var formatDate = function formatDate(date) {
  /* istanbul ignore next */
  cov_25altp5hc8().f[2]++;
  cov_25altp5hc8().s[15]++;
  return (0, moment_1.default)(date, 'YYYY-MM-DD').format('DD/MM/YYYY');
};
function mapGetMyBillHistoryListResponseToModel(response) {
  /* istanbul ignore next */
  cov_25altp5hc8().f[3]++;
  var _response$reduce;
  var grouped =
  /* istanbul ignore next */
  (cov_25altp5hc8().s[16]++, (_response$reduce = response == null ?
  /* istanbul ignore next */
  (cov_25altp5hc8().b[8][0]++, void 0) :
  /* istanbul ignore next */
  (cov_25altp5hc8().b[8][1]++, response.reduce(function (acc, bill) {
    /* istanbul ignore next */
    cov_25altp5hc8().f[4]++;
    var _bill$paymentDate, _bill$creationTime, _bill$transactionAmou, _bill$transactionAmou2;
    var dateKey =
    /* istanbul ignore next */
    (cov_25altp5hc8().s[17]++,
    /* istanbul ignore next */
    (cov_25altp5hc8().b[9][0]++, (_bill$paymentDate = bill.paymentDate) == null ?
    /* istanbul ignore next */
    (cov_25altp5hc8().b[10][0]++, void 0) :
    /* istanbul ignore next */
    (cov_25altp5hc8().b[10][1]++, _bill$paymentDate.split('T')[0])) ||
    /* istanbul ignore next */
    (cov_25altp5hc8().b[9][1]++, (_bill$creationTime = bill.creationTime) == null ?
    /* istanbul ignore next */
    (cov_25altp5hc8().b[11][0]++, void 0) :
    /* istanbul ignore next */
    (cov_25altp5hc8().b[11][1]++, _bill$creationTime.split('T')[0])));
    /* istanbul ignore next */
    cov_25altp5hc8().s[18]++;
    if (!acc[dateKey]) {
      /* istanbul ignore next */
      cov_25altp5hc8().b[12][0]++;
      cov_25altp5hc8().s[19]++;
      acc[dateKey] = [];
    } else
    /* istanbul ignore next */
    {
      cov_25altp5hc8().b[12][1]++;
    }
    cov_25altp5hc8().s[20]++;
    acc[dateKey].push({
      id: bill.id,
      transName:
      /* istanbul ignore next */
      (cov_25altp5hc8().b[13][0]++, bill.counterPartyName) ||
      /* istanbul ignore next */
      (cov_25altp5hc8().b[13][1]++, bill.customerName) ||
      /* istanbul ignore next */
      (cov_25altp5hc8().b[13][2]++, bill.billCode),
      content:
      /* istanbul ignore next */
      (cov_25altp5hc8().b[14][0]++, bill.description) ||
      /* istanbul ignore next */
      (cov_25altp5hc8().b[14][1]++, bill.content) ||
      /* istanbul ignore next */
      (cov_25altp5hc8().b[14][2]++, `Bill Payment - ${bill.serviceCode}`),
      amount: bill.creditDebitIndicator === 'CRDT' ?
      /* istanbul ignore next */
      (cov_25altp5hc8().b[15][0]++, `+${formatPrice(+(
      /* istanbul ignore next */
      (cov_25altp5hc8().b[16][0]++, (_bill$transactionAmou = bill.transactionAmountCurrency) == null ?
      /* istanbul ignore next */
      (cov_25altp5hc8().b[17][0]++, void 0) :
      /* istanbul ignore next */
      (cov_25altp5hc8().b[17][1]++, _bill$transactionAmou.amount)) ||
      /* istanbul ignore next */
      (cov_25altp5hc8().b[16][1]++, bill.totalAmount) ||
      /* istanbul ignore next */
      (cov_25altp5hc8().b[16][2]++, 0)))}`) :
      /* istanbul ignore next */
      (cov_25altp5hc8().b[15][1]++, `-${formatPrice(+(
      /* istanbul ignore next */
      (cov_25altp5hc8().b[18][0]++, (_bill$transactionAmou2 = bill.transactionAmountCurrency) == null ?
      /* istanbul ignore next */
      (cov_25altp5hc8().b[19][0]++, void 0) :
      /* istanbul ignore next */
      (cov_25altp5hc8().b[19][1]++, _bill$transactionAmou2.amount)) ||
      /* istanbul ignore next */
      (cov_25altp5hc8().b[18][1]++, bill.totalAmount) ||
      /* istanbul ignore next */
      (cov_25altp5hc8().b[18][2]++, 0)))}`),
      transDate: bill.creationTime ?
      /* istanbul ignore next */
      (cov_25altp5hc8().b[20][0]++, (0, moment_1.default)(bill.creationTime).format('HH:mm').toString()) :
      /* istanbul ignore next */
      (cov_25altp5hc8().b[20][1]++, bill.paymentDate ?
      /* istanbul ignore next */
      (cov_25altp5hc8().b[21][0]++, (0, moment_1.default)(bill.paymentDate).format('HH:mm').toString()) :
      /* istanbul ignore next */
      (cov_25altp5hc8().b[21][1]++, '')),
      creationDate:
      /* istanbul ignore next */
      (cov_25altp5hc8().b[22][0]++, bill.creationTime) ||
      /* istanbul ignore next */
      (cov_25altp5hc8().b[22][1]++, bill.paymentDate)
    });
    /* istanbul ignore next */
    cov_25altp5hc8().s[21]++;
    return acc;
  }, {}))) != null ?
  /* istanbul ignore next */
  (cov_25altp5hc8().b[7][0]++, _response$reduce) :
  /* istanbul ignore next */
  (cov_25altp5hc8().b[7][1]++, []));
  var billHistory =
  /* istanbul ignore next */
  (cov_25altp5hc8().s[22]++, Object.entries(grouped).map(function (_ref) {
    /* istanbul ignore next */
    cov_25altp5hc8().f[5]++;
    var _ref2 =
      /* istanbul ignore next */
      (cov_25altp5hc8().s[23]++, (0, _slicedToArray2.default)(_ref, 2)),
      date =
      /* istanbul ignore next */
      (cov_25altp5hc8().s[24]++, _ref2[0]),
      bills =
      /* istanbul ignore next */
      (cov_25altp5hc8().s[25]++, _ref2[1]);
    /* istanbul ignore next */
    cov_25altp5hc8().s[26]++;
    return {
      title: formatDate(date),
      data: bills
    };
  }));
  /* istanbul ignore next */
  cov_25altp5hc8().s[27]++;
  return {
    billHistoryDTO: billHistory,
    billHistory: response
  };
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJjb3ZfMjVhbHRwNWhjOCIsImFjdHVhbENvdmVyYWdlIiwiZXhwb3J0cyIsIm1hcEdldE15QmlsbEhpc3RvcnlMaXN0UmVzcG9uc2VUb01vZGVsIiwibW9tZW50XzEiLCJzIiwiX19pbXBvcnREZWZhdWx0IiwicmVxdWlyZSIsImZvcm1hdFByaWNlIiwiYW1vdW50IiwiZiIsImIiLCJ1bmRlZmluZWQiLCJudW0iLCJOdW1iZXIiLCJpc05hTiIsInRvTG9jYWxlU3RyaW5nIiwicmVwbGFjZSIsImZvcm1hdERhdGUiLCJkYXRlIiwiZGVmYXVsdCIsImZvcm1hdCIsInJlc3BvbnNlIiwiX3Jlc3BvbnNlJHJlZHVjZSIsImdyb3VwZWQiLCJyZWR1Y2UiLCJhY2MiLCJiaWxsIiwiX2JpbGwkcGF5bWVudERhdGUiLCJfYmlsbCRjcmVhdGlvblRpbWUiLCJfYmlsbCR0cmFuc2FjdGlvbkFtb3UiLCJfYmlsbCR0cmFuc2FjdGlvbkFtb3UyIiwiZGF0ZUtleSIsInBheW1lbnREYXRlIiwic3BsaXQiLCJjcmVhdGlvblRpbWUiLCJwdXNoIiwiaWQiLCJ0cmFuc05hbWUiLCJjb3VudGVyUGFydHlOYW1lIiwiY3VzdG9tZXJOYW1lIiwiYmlsbENvZGUiLCJjb250ZW50IiwiZGVzY3JpcHRpb24iLCJzZXJ2aWNlQ29kZSIsImNyZWRpdERlYml0SW5kaWNhdG9yIiwidHJhbnNhY3Rpb25BbW91bnRDdXJyZW5jeSIsInRvdGFsQW1vdW50IiwidHJhbnNEYXRlIiwidG9TdHJpbmciLCJjcmVhdGlvbkRhdGUiLCJiaWxsSGlzdG9yeSIsIk9iamVjdCIsImVudHJpZXMiLCJtYXAiLCJfcmVmIiwiX3JlZjIiLCJfc2xpY2VkVG9BcnJheTIiLCJiaWxscyIsInRpdGxlIiwiZGF0YSIsImJpbGxIaXN0b3J5RFRPIl0sInNvdXJjZXMiOlsiL1VzZXJzL3NvbnNhbXNldC9wcm9qZWN0cy9yZWFjdC1uYXRpdmUvcGF5bWVudC1tb2R1bGUvc3JjL2RhdGEvbWFwcGVycy9nZXQtbXktYmlsbC1oaXN0b3J5LWxpc3QvR2V0TXlCaWxsSGlzdG9yeUxpc3RNYXBwZXIudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHtcbiAgQmlsbERhdGEsXG4gIEJpbGxIaXN0b3J5RFRPLFxuICBCaWxsSGlzdG9yeU1vZGVsLFxufSBmcm9tICcuLi8uLi8uLi9kb21haW4vZW50aXRpZXMvZ2V0LW15LWJpbGwtaGlzdG9yeS1saXN0L0dldE15QmlsbEhpc3RvcnlMaXN0TW9kZWwudHMnO1xuaW1wb3J0IHtHZXRNeUJpbGxIaXN0b3J5TGlzdFJlc3BvbnNlfSBmcm9tICcuLi8uLi9tb2RlbHMvZ2V0LW15LWJpbGwtaGlzdG9yeS1saXN0L0dldE15QmlsbEhpc3RvcnlMaXN0UmVzcG9uc2UudHMnO1xuaW1wb3J0IG1vbWVudCBmcm9tICdtb21lbnQvbW9tZW50JztcblxuLy8gVXRpbGl0eSBmdW5jdGlvbnMgY29waWVkIGZyb20gYWNjb3VudCBtb2R1bGVcbmNvbnN0IGZvcm1hdFByaWNlID0gKGFtb3VudDogbnVtYmVyIHwgc3RyaW5nIHwgbnVsbCB8IHVuZGVmaW5lZCkgPT4ge1xuICBpZiAoYW1vdW50ID09IG51bGwgfHwgYW1vdW50ID09IHVuZGVmaW5lZCkge1xuICAgIHJldHVybiAnJztcbiAgfVxuICBjb25zdCBudW0gPSB0eXBlb2YgYW1vdW50ID09PSAnc3RyaW5nJyA/IE51bWJlcihhbW91bnQpIDogYW1vdW50O1xuICBpZiAoaXNOYU4obnVtKSkge1xuICAgIHJldHVybiAnJztcbiAgfVxuICByZXR1cm4gbnVtLnRvTG9jYWxlU3RyaW5nKCd2aS1WTicpLnJlcGxhY2UoL1xcLi9nLCAnLCcpO1xufTtcblxuY29uc3QgZm9ybWF0RGF0ZSA9IChkYXRlOiBzdHJpbmcpOiBzdHJpbmcgPT4ge1xuICByZXR1cm4gbW9tZW50KGRhdGUsICdZWVlZLU1NLUREJykuZm9ybWF0KCdERC9NTS9ZWVlZJyk7XG59O1xuXG5leHBvcnQgZnVuY3Rpb24gbWFwR2V0TXlCaWxsSGlzdG9yeUxpc3RSZXNwb25zZVRvTW9kZWwocmVzcG9uc2U6IEdldE15QmlsbEhpc3RvcnlMaXN0UmVzcG9uc2UpOiBCaWxsSGlzdG9yeU1vZGVsIHtcbiAgLy8gR3JvdXAgYmlsbHMgYnkgcGF5bWVudERhdGVcbiAgY29uc3QgZ3JvdXBlZCA9XG4gICAgcmVzcG9uc2U/LnJlZHVjZSgoYWNjLCBiaWxsKSA9PiB7XG4gICAgICBjb25zdCBkYXRlS2V5ID0gYmlsbC5wYXltZW50RGF0ZT8uc3BsaXQoJ1QnKVswXSB8fCBiaWxsLmNyZWF0aW9uVGltZT8uc3BsaXQoJ1QnKVswXTsgLy8gRXh0cmFjdCBkYXRlIHBhcnRcbiAgICAgIGlmICghYWNjW2RhdGVLZXldKSB7XG4gICAgICAgIGFjY1tkYXRlS2V5XSA9IFtdO1xuICAgICAgfVxuICAgICAgYWNjW2RhdGVLZXldLnB1c2goe1xuICAgICAgICBpZDogYmlsbC5pZCxcbiAgICAgICAgdHJhbnNOYW1lOiBiaWxsLmNvdW50ZXJQYXJ0eU5hbWUgfHwgYmlsbC5jdXN0b21lck5hbWUgfHwgYmlsbC5iaWxsQ29kZSxcbiAgICAgICAgY29udGVudDogYmlsbC5kZXNjcmlwdGlvbiB8fCBiaWxsLmNvbnRlbnQgfHwgYEJpbGwgUGF5bWVudCAtICR7YmlsbC5zZXJ2aWNlQ29kZX1gLFxuICAgICAgICBhbW91bnQ6XG4gICAgICAgICAgYmlsbC5jcmVkaXREZWJpdEluZGljYXRvciA9PT0gJ0NSRFQnXG4gICAgICAgICAgICA/IGArJHtmb3JtYXRQcmljZSgrKGJpbGwudHJhbnNhY3Rpb25BbW91bnRDdXJyZW5jeT8uYW1vdW50IHx8IGJpbGwudG90YWxBbW91bnQgfHwgMCkpfWBcbiAgICAgICAgICAgIDogYC0ke2Zvcm1hdFByaWNlKCsoYmlsbC50cmFuc2FjdGlvbkFtb3VudEN1cnJlbmN5Py5hbW91bnQgfHwgYmlsbC50b3RhbEFtb3VudCB8fCAwKSl9YCxcbiAgICAgICAgdHJhbnNEYXRlOiBiaWxsLmNyZWF0aW9uVGltZVxuICAgICAgICAgID8gbW9tZW50KGJpbGwuY3JlYXRpb25UaW1lKS5mb3JtYXQoJ0hIOm1tJykudG9TdHJpbmcoKVxuICAgICAgICAgIDogYmlsbC5wYXltZW50RGF0ZVxuICAgICAgICAgID8gbW9tZW50KGJpbGwucGF5bWVudERhdGUpLmZvcm1hdCgnSEg6bW0nKS50b1N0cmluZygpXG4gICAgICAgICAgOiAnJyxcbiAgICAgICAgY3JlYXRpb25EYXRlOiBiaWxsLmNyZWF0aW9uVGltZSB8fCBiaWxsLnBheW1lbnREYXRlLFxuICAgICAgfSk7XG4gICAgICByZXR1cm4gYWNjO1xuICAgIH0sIHt9IGFzIFJlY29yZDxzdHJpbmcsIEJpbGxEYXRhW10+KSA/PyBbXTtcblxuICAvLyBDb252ZXJ0IGdyb3VwZWQgb2JqZWN0IHRvIERUTyBzdHJ1Y3R1cmVcbiAgY29uc3QgYmlsbEhpc3Rvcnk6IEJpbGxIaXN0b3J5RFRPW10gPSBPYmplY3QuZW50cmllcyhncm91cGVkKS5tYXAoKFtkYXRlLCBiaWxsc10pID0+ICh7XG4gICAgdGl0bGU6IGZvcm1hdERhdGUoZGF0ZSksXG4gICAgZGF0YTogYmlsbHMsXG4gIH0pKTtcblxuICByZXR1cm4ge1xuICAgIGJpbGxIaXN0b3J5RFRPOiBiaWxsSGlzdG9yeSxcbiAgICBiaWxsSGlzdG9yeTogcmVzcG9uc2UsXG4gIH07XG59XG4iXSwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztJQVVZO0lBQUFBLGNBQUEsWUFBQUEsQ0FBQTtNQUFBLE9BQUFDLGNBQUE7SUFBQTtFQUFBO0VBQUEsT0FBQUEsY0FBQTtBQUFBO0FBQUFELGNBQUE7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQWNaRSxPQUFBLENBQUFDLHNDQUFBLEdBQUFBLHNDQUFBO0FBbEJBLElBQUFDLFFBQUE7QUFBQTtBQUFBLENBQUFKLGNBQUEsR0FBQUssQ0FBQSxPQUFBQyxlQUFBLENBQUFDLE9BQUE7QUFBQTtBQUFBUCxjQUFBLEdBQUFLLENBQUE7QUFHQSxJQUFNRyxXQUFXLEdBQUcsU0FBZEEsV0FBV0EsQ0FBSUMsTUFBMEMsRUFBSTtFQUFBO0VBQUFULGNBQUEsR0FBQVUsQ0FBQTtFQUFBVixjQUFBLEdBQUFLLENBQUE7RUFDakU7RUFBSTtFQUFBLENBQUFMLGNBQUEsR0FBQVcsQ0FBQSxVQUFBRixNQUFNLElBQUksSUFBSTtFQUFBO0VBQUEsQ0FBQVQsY0FBQSxHQUFBVyxDQUFBLFVBQUlGLE1BQU0sSUFBSUcsU0FBUyxHQUFFO0lBQUE7SUFBQVosY0FBQSxHQUFBVyxDQUFBO0lBQUFYLGNBQUEsR0FBQUssQ0FBQTtJQUN6QyxPQUFPLEVBQUU7RUFDWDtFQUFBO0VBQUE7SUFBQUwsY0FBQSxHQUFBVyxDQUFBO0VBQUE7RUFDQSxJQUFNRSxHQUFHO0VBQUE7RUFBQSxDQUFBYixjQUFBLEdBQUFLLENBQUEsUUFBRyxPQUFPSSxNQUFNLEtBQUssUUFBUTtFQUFBO0VBQUEsQ0FBQVQsY0FBQSxHQUFBVyxDQUFBLFVBQUdHLE1BQU0sQ0FBQ0wsTUFBTSxDQUFDO0VBQUE7RUFBQSxDQUFBVCxjQUFBLEdBQUFXLENBQUEsVUFBR0YsTUFBTTtFQUFBO0VBQUFULGNBQUEsR0FBQUssQ0FBQTtFQUNoRSxJQUFJVSxLQUFLLENBQUNGLEdBQUcsQ0FBQyxFQUFFO0lBQUE7SUFBQWIsY0FBQSxHQUFBVyxDQUFBO0lBQUFYLGNBQUEsR0FBQUssQ0FBQTtJQUNkLE9BQU8sRUFBRTtFQUNYO0VBQUE7RUFBQTtJQUFBTCxjQUFBLEdBQUFXLENBQUE7RUFBQTtFQUFBWCxjQUFBLEdBQUFLLENBQUE7RUFDQSxPQUFPUSxHQUFHLENBQUNHLGNBQWMsQ0FBQyxPQUFPLENBQUMsQ0FBQ0MsT0FBTyxDQUFDLEtBQUssRUFBRSxHQUFHLENBQUM7QUFDeEQsQ0FBQztBQUFBO0FBQUFqQixjQUFBLEdBQUFLLENBQUE7QUFFRCxJQUFNYSxVQUFVLEdBQUcsU0FBYkEsVUFBVUEsQ0FBSUMsSUFBWSxFQUFZO0VBQUE7RUFBQW5CLGNBQUEsR0FBQVUsQ0FBQTtFQUFBVixjQUFBLEdBQUFLLENBQUE7RUFDMUMsT0FBTyxJQUFBRCxRQUFBLENBQUFnQixPQUFNLEVBQUNELElBQUksRUFBRSxZQUFZLENBQUMsQ0FBQ0UsTUFBTSxDQUFDLFlBQVksQ0FBQztBQUN4RCxDQUFDO0FBRUQsU0FBZ0JsQixzQ0FBc0NBLENBQUNtQixRQUFzQztFQUFBO0VBQUF0QixjQUFBLEdBQUFVLENBQUE7RUFBQSxJQUFBYSxnQkFBQTtFQUUzRixJQUFNQyxPQUFPO0VBQUE7RUFBQSxDQUFBeEIsY0FBQSxHQUFBSyxDQUFBLFNBQUFrQixnQkFBQSxHQUNYRCxRQUFRO0VBQUE7RUFBQSxDQUFBdEIsY0FBQSxHQUFBVyxDQUFBO0VBQUE7RUFBQSxDQUFBWCxjQUFBLEdBQUFXLENBQUEsVUFBUlcsUUFBUSxDQUFFRyxNQUFNLENBQUMsVUFBQ0MsR0FBRyxFQUFFQyxJQUFJLEVBQUk7SUFBQTtJQUFBM0IsY0FBQSxHQUFBVSxDQUFBO0lBQUEsSUFBQWtCLGlCQUFBLEVBQUFDLGtCQUFBLEVBQUFDLHFCQUFBLEVBQUFDLHNCQUFBO0lBQzdCLElBQU1DLE9BQU87SUFBQTtJQUFBLENBQUFoQyxjQUFBLEdBQUFLLENBQUE7SUFBRztJQUFBLENBQUFMLGNBQUEsR0FBQVcsQ0FBQSxXQUFBaUIsaUJBQUEsR0FBQUQsSUFBSSxDQUFDTSxXQUFXO0lBQUE7SUFBQSxDQUFBakMsY0FBQSxHQUFBVyxDQUFBO0lBQUE7SUFBQSxDQUFBWCxjQUFBLEdBQUFXLENBQUEsV0FBaEJpQixpQkFBQSxDQUFrQk0sS0FBSyxDQUFDLEdBQUcsQ0FBQyxDQUFDLENBQUMsQ0FBQztJQUFBO0lBQUEsQ0FBQWxDLGNBQUEsR0FBQVcsQ0FBQSxXQUFBa0Isa0JBQUEsR0FBSUYsSUFBSSxDQUFDUSxZQUFZO0lBQUE7SUFBQSxDQUFBbkMsY0FBQSxHQUFBVyxDQUFBO0lBQUE7SUFBQSxDQUFBWCxjQUFBLEdBQUFXLENBQUEsV0FBakJrQixrQkFBQSxDQUFtQkssS0FBSyxDQUFDLEdBQUcsQ0FBQyxDQUFDLENBQUMsQ0FBQztJQUFBO0lBQUFsQyxjQUFBLEdBQUFLLENBQUE7SUFDbkYsSUFBSSxDQUFDcUIsR0FBRyxDQUFDTSxPQUFPLENBQUMsRUFBRTtNQUFBO01BQUFoQyxjQUFBLEdBQUFXLENBQUE7TUFBQVgsY0FBQSxHQUFBSyxDQUFBO01BQ2pCcUIsR0FBRyxDQUFDTSxPQUFPLENBQUMsR0FBRyxFQUFFO0lBQ25CO0lBQUE7SUFBQTtNQUFBaEMsY0FBQSxHQUFBVyxDQUFBO0lBQUE7SUFBQVgsY0FBQSxHQUFBSyxDQUFBO0lBQ0FxQixHQUFHLENBQUNNLE9BQU8sQ0FBQyxDQUFDSSxJQUFJLENBQUM7TUFDaEJDLEVBQUUsRUFBRVYsSUFBSSxDQUFDVSxFQUFFO01BQ1hDLFNBQVM7TUFBRTtNQUFBLENBQUF0QyxjQUFBLEdBQUFXLENBQUEsV0FBQWdCLElBQUksQ0FBQ1ksZ0JBQWdCO01BQUE7TUFBQSxDQUFBdkMsY0FBQSxHQUFBVyxDQUFBLFdBQUlnQixJQUFJLENBQUNhLFlBQVk7TUFBQTtNQUFBLENBQUF4QyxjQUFBLEdBQUFXLENBQUEsV0FBSWdCLElBQUksQ0FBQ2MsUUFBUTtNQUN0RUMsT0FBTztNQUFFO01BQUEsQ0FBQTFDLGNBQUEsR0FBQVcsQ0FBQSxXQUFBZ0IsSUFBSSxDQUFDZ0IsV0FBVztNQUFBO01BQUEsQ0FBQTNDLGNBQUEsR0FBQVcsQ0FBQSxXQUFJZ0IsSUFBSSxDQUFDZSxPQUFPO01BQUE7TUFBQSxDQUFBMUMsY0FBQSxHQUFBVyxDQUFBLFdBQUksa0JBQWtCZ0IsSUFBSSxDQUFDaUIsV0FBVyxFQUFFO01BQ2pGbkMsTUFBTSxFQUNKa0IsSUFBSSxDQUFDa0Isb0JBQW9CLEtBQUssTUFBTTtNQUFBO01BQUEsQ0FBQTdDLGNBQUEsR0FBQVcsQ0FBQSxXQUNoQyxJQUFJSCxXQUFXLENBQUM7TUFBRTtNQUFBLENBQUFSLGNBQUEsR0FBQVcsQ0FBQSxZQUFBbUIscUJBQUEsR0FBQUgsSUFBSSxDQUFDbUIseUJBQXlCO01BQUE7TUFBQSxDQUFBOUMsY0FBQSxHQUFBVyxDQUFBO01BQUE7TUFBQSxDQUFBWCxjQUFBLEdBQUFXLENBQUEsV0FBOUJtQixxQkFBQSxDQUFnQ3JCLE1BQU07TUFBQTtNQUFBLENBQUFULGNBQUEsR0FBQVcsQ0FBQSxXQUFJZ0IsSUFBSSxDQUFDb0IsV0FBVztNQUFBO01BQUEsQ0FBQS9DLGNBQUEsR0FBQVcsQ0FBQSxXQUFJLENBQUMsRUFBQyxDQUFDLEVBQUU7TUFBQTtNQUFBLENBQUFYLGNBQUEsR0FBQVcsQ0FBQSxXQUNyRixJQUFJSCxXQUFXLENBQUM7TUFBRTtNQUFBLENBQUFSLGNBQUEsR0FBQVcsQ0FBQSxZQUFBb0Isc0JBQUEsR0FBQUosSUFBSSxDQUFDbUIseUJBQXlCO01BQUE7TUFBQSxDQUFBOUMsY0FBQSxHQUFBVyxDQUFBO01BQUE7TUFBQSxDQUFBWCxjQUFBLEdBQUFXLENBQUEsV0FBOUJvQixzQkFBQSxDQUFnQ3RCLE1BQU07TUFBQTtNQUFBLENBQUFULGNBQUEsR0FBQVcsQ0FBQSxXQUFJZ0IsSUFBSSxDQUFDb0IsV0FBVztNQUFBO01BQUEsQ0FBQS9DLGNBQUEsR0FBQVcsQ0FBQSxXQUFJLENBQUMsRUFBQyxDQUFDLEVBQUU7TUFDM0ZxQyxTQUFTLEVBQUVyQixJQUFJLENBQUNRLFlBQVk7TUFBQTtNQUFBLENBQUFuQyxjQUFBLEdBQUFXLENBQUEsV0FDeEIsSUFBQVAsUUFBQSxDQUFBZ0IsT0FBTSxFQUFDTyxJQUFJLENBQUNRLFlBQVksQ0FBQyxDQUFDZCxNQUFNLENBQUMsT0FBTyxDQUFDLENBQUM0QixRQUFRLEVBQUU7TUFBQTtNQUFBLENBQUFqRCxjQUFBLEdBQUFXLENBQUEsV0FDcERnQixJQUFJLENBQUNNLFdBQVc7TUFBQTtNQUFBLENBQUFqQyxjQUFBLEdBQUFXLENBQUEsV0FDaEIsSUFBQVAsUUFBQSxDQUFBZ0IsT0FBTSxFQUFDTyxJQUFJLENBQUNNLFdBQVcsQ0FBQyxDQUFDWixNQUFNLENBQUMsT0FBTyxDQUFDLENBQUM0QixRQUFRLEVBQUU7TUFBQTtNQUFBLENBQUFqRCxjQUFBLEdBQUFXLENBQUEsV0FDbkQsRUFBRTtNQUNOdUMsWUFBWTtNQUFFO01BQUEsQ0FBQWxELGNBQUEsR0FBQVcsQ0FBQSxXQUFBZ0IsSUFBSSxDQUFDUSxZQUFZO01BQUE7TUFBQSxDQUFBbkMsY0FBQSxHQUFBVyxDQUFBLFdBQUlnQixJQUFJLENBQUNNLFdBQUE7S0FDekMsQ0FBQztJQUFBO0lBQUFqQyxjQUFBLEdBQUFLLENBQUE7SUFDRixPQUFPcUIsR0FBRztFQUNaLENBQUMsRUFBRSxFQUFnQyxDQUFDO0VBQUE7RUFBQSxDQUFBMUIsY0FBQSxHQUFBVyxDQUFBLFVBQUFZLGdCQUFBO0VBQUE7RUFBQSxDQUFBdkIsY0FBQSxHQUFBVyxDQUFBLFVBQUksRUFBRTtFQUc1QyxJQUFNd0MsV0FBVztFQUFBO0VBQUEsQ0FBQW5ELGNBQUEsR0FBQUssQ0FBQSxRQUFxQitDLE1BQU0sQ0FBQ0MsT0FBTyxDQUFDN0IsT0FBTyxDQUFDLENBQUM4QixHQUFHLENBQUMsVUFBQUMsSUFBQTtJQUFBO0lBQUF2RCxjQUFBLEdBQUFVLENBQUE7SUFBQSxJQUFBOEMsS0FBQTtNQUFBO01BQUEsQ0FBQXhELGNBQUEsR0FBQUssQ0FBQSxZQUFBb0QsZUFBQSxDQUFBckMsT0FBQSxFQUFBbUMsSUFBQTtNQUFFcEMsSUFBSTtNQUFBO01BQUEsQ0FBQW5CLGNBQUEsR0FBQUssQ0FBQSxRQUFBbUQsS0FBQTtNQUFFRSxLQUFLO01BQUE7TUFBQSxDQUFBMUQsY0FBQSxHQUFBSyxDQUFBLFFBQUFtRCxLQUFBO0lBQUE7SUFBQXhELGNBQUEsR0FBQUssQ0FBQTtJQUFBLE9BQU87TUFDcEZzRCxLQUFLLEVBQUV6QyxVQUFVLENBQUNDLElBQUksQ0FBQztNQUN2QnlDLElBQUksRUFBRUY7S0FDUDtFQUFBLENBQUMsQ0FBQztFQUFBO0VBQUExRCxjQUFBLEdBQUFLLENBQUE7RUFFSCxPQUFPO0lBQ0x3RCxjQUFjLEVBQUVWLFdBQVc7SUFDM0JBLFdBQVcsRUFBRTdCO0dBQ2Q7QUFDSCIsImlnbm9yZUxpc3QiOltdfQ==