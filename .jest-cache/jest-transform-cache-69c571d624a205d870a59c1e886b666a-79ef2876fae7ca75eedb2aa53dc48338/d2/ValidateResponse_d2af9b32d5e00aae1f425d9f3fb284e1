8987dc755a29c4ff9a0eb473855c4168
"use strict";

/* istanbul ignore next */
function cov_2ld2wgldwd() {
  var path = "/Users/<USER>/projects/react-native/payment-module/src/data/models/validate/ValidateResponse.ts";
  var hash = "82463afb651e07069c16665660001dfe270b72cd";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/projects/react-native/payment-module/src/data/models/validate/ValidateResponse.ts",
    statementMap: {
      "0": {
        start: {
          line: 3,
          column: 0
        },
        end: {
          line: 5,
          column: 3
        }
      }
    },
    fnMap: {},
    branchMap: {},
    s: {
      "0": 0
    },
    f: {},
    b: {},
    inputSourceMap: {
      version: 3,
      file: undefined,
      names: [],
      sourceRoot: undefined,
      sources: ["/Users/<USER>/projects/react-native/payment-module/src/data/models/validate/ValidateResponse.ts"],
      sourcesContent: ["export interface ValidateResponse {\n  // TODO: define fields\n}\n"],
      mappings: "",
      ignoreList: []
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "82463afb651e07069c16665660001dfe270b72cd"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_2ld2wgldwd = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_2ld2wgldwd();
cov_2ld2wgldwd().s[0]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6W10sInNvdXJjZXMiOlsiL1VzZXJzL3NvbnNhbXNldC9wcm9qZWN0cy9yZWFjdC1uYXRpdmUvcGF5bWVudC1tb2R1bGUvc3JjL2RhdGEvbW9kZWxzL3ZhbGlkYXRlL1ZhbGlkYXRlUmVzcG9uc2UudHMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGludGVyZmFjZSBWYWxpZGF0ZVJlc3BvbnNlIHtcbiAgLy8gVE9ETzogZGVmaW5lIGZpZWxkc1xufVxuIl0sIm1hcHBpbmdzIjoiIiwiaWdub3JlTGlzdCI6W119