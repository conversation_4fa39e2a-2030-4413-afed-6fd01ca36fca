{"version": 3, "names": ["cov_25altp5hc8", "actualCoverage", "exports", "mapGetMyBillHistoryListResponseToModel", "moment_1", "s", "__importDefault", "require", "formatPrice", "amount", "f", "b", "undefined", "num", "Number", "isNaN", "toLocaleString", "replace", "formatDate", "date", "default", "format", "response", "_response$reduce", "grouped", "reduce", "acc", "bill", "_bill$paymentDate", "_bill$creationTime", "_bill$transactionAmou", "_bill$transactionAmou2", "<PERSON><PERSON><PERSON>", "paymentDate", "split", "creationTime", "push", "id", "transName", "counterPartyName", "customerName", "billCode", "content", "description", "serviceCode", "creditDebitIndicator", "transactionAmountCurrency", "totalAmount", "transDate", "toString", "creationDate", "bill<PERSON><PERSON><PERSON>", "Object", "entries", "map", "_ref", "_ref2", "_slicedToArray2", "bills", "title", "data", "billHistoryDTO"], "sources": ["/Users/<USER>/projects/react-native/payment-module/src/data/mappers/get-my-bill-history-list/GetMyBillHistoryListMapper.ts"], "sourcesContent": ["import {\n  BillData,\n  BillHistoryDTO,\n  BillHistoryModel,\n} from '../../../domain/entities/get-my-bill-history-list/GetMyBillHistoryListModel.ts';\nimport {GetMyBillHistoryListResponse} from '../../models/get-my-bill-history-list/GetMyBillHistoryListResponse.ts';\nimport moment from 'moment/moment';\n\n// Utility functions copied from account module\nconst formatPrice = (amount: number | string | null | undefined) => {\n  if (amount == null || amount == undefined) {\n    return '';\n  }\n  const num = typeof amount === 'string' ? Number(amount) : amount;\n  if (isNaN(num)) {\n    return '';\n  }\n  return num.toLocaleString('vi-VN').replace(/\\./g, ',');\n};\n\nconst formatDate = (date: string): string => {\n  return moment(date, 'YYYY-MM-DD').format('DD/MM/YYYY');\n};\n\nexport function mapGetMyBillHistoryListResponseToModel(response: GetMyBillHistoryListResponse): BillHistoryModel {\n  // Group bills by paymentDate\n  const grouped =\n    response?.reduce((acc, bill) => {\n      const dateKey = bill.paymentDate?.split('T')[0] || bill.creationTime?.split('T')[0]; // Extract date part\n      if (!acc[dateKey]) {\n        acc[dateKey] = [];\n      }\n      acc[dateKey].push({\n        id: bill.id,\n        transName: bill.counterPartyName || bill.customerName || bill.billCode,\n        content: bill.description || bill.content || `Bill Payment - ${bill.serviceCode}`,\n        amount:\n          bill.creditDebitIndicator === 'CRDT'\n            ? `+${formatPrice(+(bill.transactionAmountCurrency?.amount || bill.totalAmount || 0))}`\n            : `-${formatPrice(+(bill.transactionAmountCurrency?.amount || bill.totalAmount || 0))}`,\n        transDate: bill.creationTime\n          ? moment(bill.creationTime).format('HH:mm').toString()\n          : bill.paymentDate\n          ? moment(bill.paymentDate).format('HH:mm').toString()\n          : '',\n        creationDate: bill.creationTime || bill.paymentDate,\n      });\n      return acc;\n    }, {} as Record<string, BillData[]>) ?? [];\n\n  // Convert grouped object to DTO structure\n  const billHistory: BillHistoryDTO[] = Object.entries(grouped).map(([date, bills]) => ({\n    title: formatDate(date),\n    data: bills,\n  }));\n\n  return {\n    billHistoryDTO: billHistory,\n    billHistory: response,\n  };\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAUY;IAAAA,cAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,cAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAcZE,OAAA,CAAAC,sCAAA,GAAAA,sCAAA;AAlBA,IAAAC,QAAA;AAAA;AAAA,CAAAJ,cAAA,GAAAK,CAAA,OAAAC,eAAA,CAAAC,OAAA;AAAA;AAAAP,cAAA,GAAAK,CAAA;AAGA,IAAMG,WAAW,GAAG,SAAdA,WAAWA,CAAIC,MAA0C,EAAI;EAAA;EAAAT,cAAA,GAAAU,CAAA;EAAAV,cAAA,GAAAK,CAAA;EACjE;EAAI;EAAA,CAAAL,cAAA,GAAAW,CAAA,UAAAF,MAAM,IAAI,IAAI;EAAA;EAAA,CAAAT,cAAA,GAAAW,CAAA,UAAIF,MAAM,IAAIG,SAAS,GAAE;IAAA;IAAAZ,cAAA,GAAAW,CAAA;IAAAX,cAAA,GAAAK,CAAA;IACzC,OAAO,EAAE;EACX;EAAA;EAAA;IAAAL,cAAA,GAAAW,CAAA;EAAA;EACA,IAAME,GAAG;EAAA;EAAA,CAAAb,cAAA,GAAAK,CAAA,QAAG,OAAOI,MAAM,KAAK,QAAQ;EAAA;EAAA,CAAAT,cAAA,GAAAW,CAAA,UAAGG,MAAM,CAACL,MAAM,CAAC;EAAA;EAAA,CAAAT,cAAA,GAAAW,CAAA,UAAGF,MAAM;EAAA;EAAAT,cAAA,GAAAK,CAAA;EAChE,IAAIU,KAAK,CAACF,GAAG,CAAC,EAAE;IAAA;IAAAb,cAAA,GAAAW,CAAA;IAAAX,cAAA,GAAAK,CAAA;IACd,OAAO,EAAE;EACX;EAAA;EAAA;IAAAL,cAAA,GAAAW,CAAA;EAAA;EAAAX,cAAA,GAAAK,CAAA;EACA,OAAOQ,GAAG,CAACG,cAAc,CAAC,OAAO,CAAC,CAACC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC;AACxD,CAAC;AAAA;AAAAjB,cAAA,GAAAK,CAAA;AAED,IAAMa,UAAU,GAAG,SAAbA,UAAUA,CAAIC,IAAY,EAAY;EAAA;EAAAnB,cAAA,GAAAU,CAAA;EAAAV,cAAA,GAAAK,CAAA;EAC1C,OAAO,IAAAD,QAAA,CAAAgB,OAAM,EAACD,IAAI,EAAE,YAAY,CAAC,CAACE,MAAM,CAAC,YAAY,CAAC;AACxD,CAAC;AAED,SAAgBlB,sCAAsCA,CAACmB,QAAsC;EAAA;EAAAtB,cAAA,GAAAU,CAAA;EAAA,IAAAa,gBAAA;EAE3F,IAAMC,OAAO;EAAA;EAAA,CAAAxB,cAAA,GAAAK,CAAA,SAAAkB,gBAAA,GACXD,QAAQ;EAAA;EAAA,CAAAtB,cAAA,GAAAW,CAAA;EAAA;EAAA,CAAAX,cAAA,GAAAW,CAAA,UAARW,QAAQ,CAAEG,MAAM,CAAC,UAACC,GAAG,EAAEC,IAAI,EAAI;IAAA;IAAA3B,cAAA,GAAAU,CAAA;IAAA,IAAAkB,iBAAA,EAAAC,kBAAA,EAAAC,qBAAA,EAAAC,sBAAA;IAC7B,IAAMC,OAAO;IAAA;IAAA,CAAAhC,cAAA,GAAAK,CAAA;IAAG;IAAA,CAAAL,cAAA,GAAAW,CAAA,WAAAiB,iBAAA,GAAAD,IAAI,CAACM,WAAW;IAAA;IAAA,CAAAjC,cAAA,GAAAW,CAAA;IAAA;IAAA,CAAAX,cAAA,GAAAW,CAAA,WAAhBiB,iBAAA,CAAkBM,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAAA;IAAA,CAAAlC,cAAA,GAAAW,CAAA,WAAAkB,kBAAA,GAAIF,IAAI,CAACQ,YAAY;IAAA;IAAA,CAAAnC,cAAA,GAAAW,CAAA;IAAA;IAAA,CAAAX,cAAA,GAAAW,CAAA,WAAjBkB,kBAAA,CAAmBK,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAAA;IAAAlC,cAAA,GAAAK,CAAA;IACnF,IAAI,CAACqB,GAAG,CAACM,OAAO,CAAC,EAAE;MAAA;MAAAhC,cAAA,GAAAW,CAAA;MAAAX,cAAA,GAAAK,CAAA;MACjBqB,GAAG,CAACM,OAAO,CAAC,GAAG,EAAE;IACnB;IAAA;IAAA;MAAAhC,cAAA,GAAAW,CAAA;IAAA;IAAAX,cAAA,GAAAK,CAAA;IACAqB,GAAG,CAACM,OAAO,CAAC,CAACI,IAAI,CAAC;MAChBC,EAAE,EAAEV,IAAI,CAACU,EAAE;MACXC,SAAS;MAAE;MAAA,CAAAtC,cAAA,GAAAW,CAAA,WAAAgB,IAAI,CAACY,gBAAgB;MAAA;MAAA,CAAAvC,cAAA,GAAAW,CAAA,WAAIgB,IAAI,CAACa,YAAY;MAAA;MAAA,CAAAxC,cAAA,GAAAW,CAAA,WAAIgB,IAAI,CAACc,QAAQ;MACtEC,OAAO;MAAE;MAAA,CAAA1C,cAAA,GAAAW,CAAA,WAAAgB,IAAI,CAACgB,WAAW;MAAA;MAAA,CAAA3C,cAAA,GAAAW,CAAA,WAAIgB,IAAI,CAACe,OAAO;MAAA;MAAA,CAAA1C,cAAA,GAAAW,CAAA,WAAI,kBAAkBgB,IAAI,CAACiB,WAAW,EAAE;MACjFnC,MAAM,EACJkB,IAAI,CAACkB,oBAAoB,KAAK,MAAM;MAAA;MAAA,CAAA7C,cAAA,GAAAW,CAAA,WAChC,IAAIH,WAAW,CAAC;MAAE;MAAA,CAAAR,cAAA,GAAAW,CAAA,YAAAmB,qBAAA,GAAAH,IAAI,CAACmB,yBAAyB;MAAA;MAAA,CAAA9C,cAAA,GAAAW,CAAA;MAAA;MAAA,CAAAX,cAAA,GAAAW,CAAA,WAA9BmB,qBAAA,CAAgCrB,MAAM;MAAA;MAAA,CAAAT,cAAA,GAAAW,CAAA,WAAIgB,IAAI,CAACoB,WAAW;MAAA;MAAA,CAAA/C,cAAA,GAAAW,CAAA,WAAI,CAAC,EAAC,CAAC,EAAE;MAAA;MAAA,CAAAX,cAAA,GAAAW,CAAA,WACrF,IAAIH,WAAW,CAAC;MAAE;MAAA,CAAAR,cAAA,GAAAW,CAAA,YAAAoB,sBAAA,GAAAJ,IAAI,CAACmB,yBAAyB;MAAA;MAAA,CAAA9C,cAAA,GAAAW,CAAA;MAAA;MAAA,CAAAX,cAAA,GAAAW,CAAA,WAA9BoB,sBAAA,CAAgCtB,MAAM;MAAA;MAAA,CAAAT,cAAA,GAAAW,CAAA,WAAIgB,IAAI,CAACoB,WAAW;MAAA;MAAA,CAAA/C,cAAA,GAAAW,CAAA,WAAI,CAAC,EAAC,CAAC,EAAE;MAC3FqC,SAAS,EAAErB,IAAI,CAACQ,YAAY;MAAA;MAAA,CAAAnC,cAAA,GAAAW,CAAA,WACxB,IAAAP,QAAA,CAAAgB,OAAM,EAACO,IAAI,CAACQ,YAAY,CAAC,CAACd,MAAM,CAAC,OAAO,CAAC,CAAC4B,QAAQ,EAAE;MAAA;MAAA,CAAAjD,cAAA,GAAAW,CAAA,WACpDgB,IAAI,CAACM,WAAW;MAAA;MAAA,CAAAjC,cAAA,GAAAW,CAAA,WAChB,IAAAP,QAAA,CAAAgB,OAAM,EAACO,IAAI,CAACM,WAAW,CAAC,CAACZ,MAAM,CAAC,OAAO,CAAC,CAAC4B,QAAQ,EAAE;MAAA;MAAA,CAAAjD,cAAA,GAAAW,CAAA,WACnD,EAAE;MACNuC,YAAY;MAAE;MAAA,CAAAlD,cAAA,GAAAW,CAAA,WAAAgB,IAAI,CAACQ,YAAY;MAAA;MAAA,CAAAnC,cAAA,GAAAW,CAAA,WAAIgB,IAAI,CAACM,WAAA;KACzC,CAAC;IAAA;IAAAjC,cAAA,GAAAK,CAAA;IACF,OAAOqB,GAAG;EACZ,CAAC,EAAE,EAAgC,CAAC;EAAA;EAAA,CAAA1B,cAAA,GAAAW,CAAA,UAAAY,gBAAA;EAAA;EAAA,CAAAvB,cAAA,GAAAW,CAAA,UAAI,EAAE;EAG5C,IAAMwC,WAAW;EAAA;EAAA,CAAAnD,cAAA,GAAAK,CAAA,QAAqB+C,MAAM,CAACC,OAAO,CAAC7B,OAAO,CAAC,CAAC8B,GAAG,CAAC,UAAAC,IAAA;IAAA;IAAAvD,cAAA,GAAAU,CAAA;IAAA,IAAA8C,KAAA;MAAA;MAAA,CAAAxD,cAAA,GAAAK,CAAA,YAAAoD,eAAA,CAAArC,OAAA,EAAAmC,IAAA;MAAEpC,IAAI;MAAA;MAAA,CAAAnB,cAAA,GAAAK,CAAA,QAAAmD,KAAA;MAAEE,KAAK;MAAA;MAAA,CAAA1D,cAAA,GAAAK,CAAA,QAAAmD,KAAA;IAAA;IAAAxD,cAAA,GAAAK,CAAA;IAAA,OAAO;MACpFsD,KAAK,EAAEzC,UAAU,CAACC,IAAI,CAAC;MACvByC,IAAI,EAAEF;KACP;EAAA,CAAC,CAAC;EAAA;EAAA1D,cAAA,GAAAK,CAAA;EAEH,OAAO;IACLwD,cAAc,EAAEV,WAAW;IAC3BA,WAAW,EAAE7B;GACd;AACH", "ignoreList": []}