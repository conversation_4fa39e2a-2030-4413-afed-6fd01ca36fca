{"version": 3, "names": [], "sources": ["/Users/<USER>/projects/react-native/payment-module/src/data/models/provider-list/ProviderListResponse.ts"], "sourcesContent": ["export type ProviderListResponse = ProviderResponse[];\n\nexport interface ProviderResponse {\n  subgroupId?: number;\n  serviceCode?: string;\n  subgroupNameVn?: string;\n  subgroupNameEn?: string;\n  categoryCode?: string;\n  partnerCode?: string;\n  partnerName?: string;\n  autoBillSupport?: number;\n  voucherSupport?: number;\n  phoneRequired?: number;\n  isRecommend?: number;\n  partnerType?: number;\n  payFee?: number;\n  type?: number;\n  paymentSupport?: number;\n  description?: string;\n}\n"], "mappings": "", "ignoreList": []}