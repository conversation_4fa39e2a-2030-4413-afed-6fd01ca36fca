{"version": 3, "names": ["Object", "defineProperty", "exports", "value", "updateLayoutAnimations", "_PlatformChecker", "require", "_core", "createUpdateManager", "animations", "deferredAnimations", "update", "batchItem", "isUnmounting", "push", "length", "isF<PERSON><PERSON>", "flush", "setImmediate", "configureLayoutAnimationBatch", "concat", "shouldBeUseWeb", "updateLayoutAnimationsManager", "viewTag", "type", "config", "sharedTransitionTag", "makeShareableCloneRecursive", "undefined"], "sources": ["../../src/UpdateLayoutAnimations.ts"], "sourcesContent": [null], "mappings": "AAAA,YAAY;;AAAAA,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,KAAA;AAAA;AAAAD,OAAA,CAAAE,sBAAA;AACZ,IAAAC,gBAAA,GAAAC,OAAA;AACA,IAAAC,KAAA,GAAAD,OAAA;AAcA,SAASE,mBAAmBA,CAAA,EAAG;EAC7B,IAAMC,UAAsC,GAAG,EAAE;EAIjD,IAAMC,kBAA8C,GAAG,EAAE;EAEzD,OAAO;IACLC,MAAM,WAANA,MAAMA,CAACC,SAAmC,EAAEC,YAAsB,EAAE;MAClE,IAAIA,YAAY,EAAE;QAChBH,kBAAkB,CAACI,IAAI,CAACF,SAAS,CAAC;MACpC,CAAC,MAAM;QACLH,UAAU,CAACK,IAAI,CAACF,SAAS,CAAC;MAC5B;MACA,IAAIH,UAAU,CAACM,MAAM,GAAGL,kBAAkB,CAACK,MAAM,KAAK,CAAC,EAAE;QACvD,IAAAC,yBAAQ,EAAC,CAAC,GAAG,IAAI,CAACC,KAAK,CAAC,CAAC,GAAGC,YAAY,CAAC,IAAI,CAACD,KAAK,CAAC;MACtD;IACF,CAAC;IACDA,KAAK,WAALA,KAAKA,CAAA,EAAa;MAChB,IAAAE,mCAA6B,EAACV,UAAU,CAACW,MAAM,CAACV,kBAAkB,CAAC,CAAC;MACpED,UAAU,CAACM,MAAM,GAAG,CAAC;MACrBL,kBAAkB,CAACK,MAAM,GAAG,CAAC;IAC/B;EACF,CAAC;AACH;AAqBO,IAAIX,sBAUF;AAET,IAAI,IAAAiB,+BAAc,EAAC,CAAC,EAAE;EACpBnB,OAAA,CAAAE,sBAAA,GAAAA,sBAAsB,GAAG,SAAzBA,sBAAsBA,CAAA,EAAS,CAC7B,CACD;AACH,CAAC,MAAM;EACL,IAAMkB,6BAA6B,GAAGd,mBAAmB,CAAC,CAAC;EAC3DN,OAAA,CAAAE,sBAAA,GAAAA,sBAAsB,GAAG,SAAzBA,sBAAsBA,CACpBmB,OAAO,EACPC,IAAI,EACJC,MAAM,EACNC,mBAAmB,EACnBb,YAAY;IAAA,OAEZS,6BAA6B,CAACX,MAAM,CAClC;MACEY,OAAO,EAAPA,OAAO;MACPC,IAAI,EAAJA,IAAI;MACJC,MAAM,EAAEA,MAAM,GAAG,IAAAE,iCAA2B,EAACF,MAAM,CAAC,GAAGG,SAAS;MAChEF,mBAAA,EAAAA;IACF,CAAC,EACDb,YACF,CAAC;EAAA;AACL", "ignoreList": []}