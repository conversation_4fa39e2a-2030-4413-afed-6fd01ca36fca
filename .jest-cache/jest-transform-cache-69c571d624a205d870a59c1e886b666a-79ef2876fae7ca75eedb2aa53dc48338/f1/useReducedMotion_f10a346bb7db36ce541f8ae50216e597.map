{"version": 3, "names": ["Object", "defineProperty", "exports", "value", "useReducedMotion", "_ReducedMotion", "require", "IS_REDUCED_MOTION_ENABLED_IN_SYSTEM", "isReducedMotionEnabledInSystem"], "sources": ["../../../src/hook/useReducedMotion.ts"], "sourcesContent": [null], "mappings": "AAAA,YAAY;;AAAAA,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,KAAA;AAAA;AAAAD,OAAA,CAAAE,gBAAA,GAAAA,gBAAA;AACZ,IAAAC,cAAA,GAAAC,OAAA;AAEA,IAAMC,mCAAmC,GAAG,IAAAC,6CAA8B,EAAC,CAAC;AAYrE,SAASJ,gBAAgBA,CAAA,EAAG;EACjC,OAAOG,mCAAmC;AAC5C", "ignoreList": []}