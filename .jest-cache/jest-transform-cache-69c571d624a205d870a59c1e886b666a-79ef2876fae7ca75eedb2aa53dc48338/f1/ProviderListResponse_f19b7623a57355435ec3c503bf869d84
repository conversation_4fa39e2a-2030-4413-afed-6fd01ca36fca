f7d1eee0c764a5c7e57a33ccd0ca7335
"use strict";

/* istanbul ignore next */
function cov_nsngflx73() {
  var path = "/Users/<USER>/projects/react-native/payment-module/src/data/models/provider-list/ProviderListResponse.ts";
  var hash = "4a3a2cfa46329deeea6ce2cf29f3234d1a0a1e56";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/projects/react-native/payment-module/src/data/models/provider-list/ProviderListResponse.ts",
    statementMap: {
      "0": {
        start: {
          line: 3,
          column: 0
        },
        end: {
          line: 5,
          column: 3
        }
      }
    },
    fnMap: {},
    branchMap: {},
    s: {
      "0": 0
    },
    f: {},
    b: {},
    inputSourceMap: {
      version: 3,
      file: undefined,
      names: [],
      sourceRoot: undefined,
      sources: ["/Users/<USER>/projects/react-native/payment-module/src/data/models/provider-list/ProviderListResponse.ts"],
      sourcesContent: ["export type ProviderListResponse = ProviderResponse[];\n\nexport interface ProviderResponse {\n  subgroupId?: number;\n  serviceCode?: string;\n  subgroupNameVn?: string;\n  subgroupNameEn?: string;\n  categoryCode?: string;\n  partnerCode?: string;\n  partnerName?: string;\n  autoBillSupport?: number;\n  voucherSupport?: number;\n  phoneRequired?: number;\n  isRecommend?: number;\n  partnerType?: number;\n  payFee?: number;\n  type?: number;\n  paymentSupport?: number;\n  description?: string;\n}\n"],
      mappings: "",
      ignoreList: []
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "4a3a2cfa46329deeea6ce2cf29f3234d1a0a1e56"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_nsngflx73 = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_nsngflx73();
cov_nsngflx73().s[0]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6W10sInNvdXJjZXMiOlsiL1VzZXJzL3NvbnNhbXNldC9wcm9qZWN0cy9yZWFjdC1uYXRpdmUvcGF5bWVudC1tb2R1bGUvc3JjL2RhdGEvbW9kZWxzL3Byb3ZpZGVyLWxpc3QvUHJvdmlkZXJMaXN0UmVzcG9uc2UudHMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IHR5cGUgUHJvdmlkZXJMaXN0UmVzcG9uc2UgPSBQcm92aWRlclJlc3BvbnNlW107XG5cbmV4cG9ydCBpbnRlcmZhY2UgUHJvdmlkZXJSZXNwb25zZSB7XG4gIHN1Ymdyb3VwSWQ/OiBudW1iZXI7XG4gIHNlcnZpY2VDb2RlPzogc3RyaW5nO1xuICBzdWJncm91cE5hbWVWbj86IHN0cmluZztcbiAgc3ViZ3JvdXBOYW1lRW4/OiBzdHJpbmc7XG4gIGNhdGVnb3J5Q29kZT86IHN0cmluZztcbiAgcGFydG5lckNvZGU/OiBzdHJpbmc7XG4gIHBhcnRuZXJOYW1lPzogc3RyaW5nO1xuICBhdXRvQmlsbFN1cHBvcnQ/OiBudW1iZXI7XG4gIHZvdWNoZXJTdXBwb3J0PzogbnVtYmVyO1xuICBwaG9uZVJlcXVpcmVkPzogbnVtYmVyO1xuICBpc1JlY29tbWVuZD86IG51bWJlcjtcbiAgcGFydG5lclR5cGU/OiBudW1iZXI7XG4gIHBheUZlZT86IG51bWJlcjtcbiAgdHlwZT86IG51bWJlcjtcbiAgcGF5bWVudFN1cHBvcnQ/OiBudW1iZXI7XG4gIGRlc2NyaXB0aW9uPzogc3RyaW5nO1xufVxuIl0sIm1hcHBpbmdzIjoiIiwiaWdub3JlTGlzdCI6W119