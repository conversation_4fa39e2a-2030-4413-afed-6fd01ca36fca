{"version": 3, "names": ["Object", "defineProperty", "exports", "value", "dispatchCommand", "_PlatformChecker", "require", "_index", "dispatchCommandFabric", "animatedRef", "commandName", "args", "arguments", "length", "undefined", "_WORKLET", "shadowNodeWrapper", "global", "_dispatchCommandFabric", "dispatchCommandPaper", "viewTag", "_dispatchCommandPaper", "dispatchCommandJest", "logger", "warn", "dispatchCommandChromeDebugger", "dispatchCommandDefault", "shouldBeUseWeb", "isF<PERSON><PERSON>", "isJest", "isChromeDebugger"], "sources": ["../../../src/platformFunctions/dispatchCommand.ts"], "sourcesContent": [null], "mappings": "AAAA,YAAY;;AAAAA,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,KAAA;AAAA;AAAAD,OAAA,CAAAE,eAAA;AAEZ,IAAAC,gBAAA,GAAAC,OAAA;AAYA,IAAAC,MAAA,GAAAD,OAAA;AAmBO,IAAIF,eAAgC;AAE3C,SAASI,qBAAqBA,CAC5BC,WAA8C,EAC9CC,WAAmB,EAEnB;EACA,SAAS;;EAAA,IAFTC,IAAoB,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,EAAE;EAGzB,IAAI,CAACG,QAAQ,EAAE;IACb;EACF;EAEA,IAAMC,iBAAiB,GAAGP,WAAW,CAAC,CAAsB;EAC5DQ,MAAM,CAACC,sBAAsB,CAAEF,iBAAiB,EAAEN,WAAW,EAAEC,IAAI,CAAC;AACtE;AAEA,SAASQ,oBAAoBA,CAC3BV,WAA8C,EAC9CC,WAAmB,EAEnB;EACA,SAAS;;EAAA,IAFTC,IAAoB,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,EAAE;EAGzB,IAAI,CAACG,QAAQ,EAAE;IACb;EACF;EAEA,IAAMK,OAAO,GAAGX,WAAW,CAAC,CAAW;EACvCQ,MAAM,CAACI,qBAAqB,CAAED,OAAO,EAAEV,WAAW,EAAEC,IAAI,CAAC;AAC3D;AAEA,SAASW,mBAAmBA,CAAA,EAAG;EAC7BC,aAAM,CAACC,IAAI,CAAC,+CAA+C,CAAC;AAC9D;AAEA,SAASC,6BAA6BA,CAAA,EAAG;EACvCF,aAAM,CAACC,IAAI,CAAC,0DAA0D,CAAC;AACzE;AAEA,SAASE,sBAAsBA,CAAA,EAAG;EAChCH,aAAM,CAACC,IAAI,CAAC,2DAA2D,CAAC;AAC1E;AAEA,IAAI,CAAC,IAAAG,+BAAc,EAAC,CAAC,EAAE;EAIrB,IAAI,IAAAC,yBAAQ,EAAC,CAAC,EAAE;IACd1B,OAAA,CAAAE,eAAA,GAAAA,eAAe,GAAGI,qBAAmD;EACvE,CAAC,MAAM;IACLN,OAAA,CAAAE,eAAA,GAAAA,eAAe,GAAGe,oBAAkD;EACtE;AACF,CAAC,MAAM,IAAI,IAAAU,uBAAM,EAAC,CAAC,EAAE;EACnB3B,OAAA,CAAAE,eAAA,GAAAA,eAAe,GAAGkB,mBAAmB;AACvC,CAAC,MAAM,IAAI,IAAAQ,iCAAgB,EAAC,CAAC,EAAE;EAC7B5B,OAAA,CAAAE,eAAA,GAAAA,eAAe,GAAGqB,6BAA6B;AACjD,CAAC,MAAM;EACLvB,OAAA,CAAAE,eAAA,GAAAA,eAAe,GAAGsB,sBAAsB;AAC1C", "ignoreList": []}