de4a1b4b3a625900833dd0575e4c7cd7
"use strict";

/* istanbul ignore next */
function cov_sg3z69fyg() {
  var path = "/Users/<USER>/projects/react-native/payment-module/src/core/ResultState.ts";
  var hash = "f3de68b93f752139e2d971d3be7a168027b281e3";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/projects/react-native/payment-module/src/core/ResultState.ts",
    statementMap: {
      "0": {
        start: {
          line: 3,
          column: 0
        },
        end: {
          line: 5,
          column: 3
        }
      }
    },
    fnMap: {},
    branchMap: {},
    s: {
      "0": 0
    },
    f: {},
    b: {},
    inputSourceMap: {
      version: 3,
      file: undefined,
      names: [],
      sourceRoot: undefined,
      sources: ["/Users/<USER>/projects/react-native/payment-module/src/core/ResultState.ts"],
      sourcesContent: ["import {CustomError} from './MSBCustomError';\n\nexport type ResultState<T> = {status: 'SUCCESS'; data: T | undefined} | {status: 'ERROR'; error: CustomError};\n"],
      mappings: "",
      ignoreList: []
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "f3de68b93f752139e2d971d3be7a168027b281e3"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_sg3z69fyg = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_sg3z69fyg();
cov_sg3z69fyg().s[0]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6W10sInNvdXJjZXMiOlsiL1VzZXJzL3NvbnNhbXNldC9wcm9qZWN0cy9yZWFjdC1uYXRpdmUvcGF5bWVudC1tb2R1bGUvc3JjL2NvcmUvUmVzdWx0U3RhdGUudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHtDdXN0b21FcnJvcn0gZnJvbSAnLi9NU0JDdXN0b21FcnJvcic7XG5cbmV4cG9ydCB0eXBlIFJlc3VsdFN0YXRlPFQ+ID0ge3N0YXR1czogJ1NVQ0NFU1MnOyBkYXRhOiBUIHwgdW5kZWZpbmVkfSB8IHtzdGF0dXM6ICdFUlJPUic7IGVycm9yOiBDdXN0b21FcnJvcn07XG4iXSwibWFwcGluZ3MiOiIiLCJpZ25vcmVMaXN0IjpbXX0=