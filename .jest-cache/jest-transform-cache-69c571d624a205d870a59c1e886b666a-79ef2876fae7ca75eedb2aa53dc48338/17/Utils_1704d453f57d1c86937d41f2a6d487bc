0cad40f486e96bfe380e19228b896a2b
"use strict";

/* istanbul ignore next */
function cov_1zwhcw8kem() {
  var path = "/Users/<USER>/projects/react-native/payment-module/src/utils/Utils.ts";
  var hash = "1d9516092919e8486811c68db07112bb953ddcdc";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/projects/react-native/payment-module/src/utils/Utils.ts",
    statementMap: {
      "0": {
        start: {
          line: 3,
          column: 0
        },
        end: {
          line: 5,
          column: 3
        }
      },
      "1": {
        start: {
          line: 6,
          column: 31
        },
        end: {
          line: 6,
          column: 64
        }
      },
      "2": {
        start: {
          line: 7,
          column: 13
        },
        end: {
          line: 7,
          column: 39
        }
      },
      "3": {
        start: {
          line: 8,
          column: 14
        },
        end: {
          line: 25,
          column: 1
        }
      },
      "4": {
        start: {
          line: 9,
          column: 2
        },
        end: {
          line: 11,
          column: 3
        }
      },
      "5": {
        start: {
          line: 10,
          column: 4
        },
        end: {
          line: 10,
          column: 16
        }
      },
      "6": {
        start: {
          line: 12,
          column: 2
        },
        end: {
          line: 14,
          column: 3
        }
      },
      "7": {
        start: {
          line: 13,
          column: 4
        },
        end: {
          line: 13,
          column: 16
        }
      },
      "8": {
        start: {
          line: 15,
          column: 2
        },
        end: {
          line: 17,
          column: 3
        }
      },
      "9": {
        start: {
          line: 16,
          column: 4
        },
        end: {
          line: 16,
          column: 16
        }
      },
      "10": {
        start: {
          line: 18,
          column: 2
        },
        end: {
          line: 20,
          column: 3
        }
      },
      "11": {
        start: {
          line: 19,
          column: 4
        },
        end: {
          line: 19,
          column: 16
        }
      },
      "12": {
        start: {
          line: 21,
          column: 2
        },
        end: {
          line: 23,
          column: 3
        }
      },
      "13": {
        start: {
          line: 22,
          column: 4
        },
        end: {
          line: 22,
          column: 16
        }
      },
      "14": {
        start: {
          line: 24,
          column: 2
        },
        end: {
          line: 24,
          column: 15
        }
      },
      "15": {
        start: {
          line: 26,
          column: 21
        },
        end: {
          line: 34,
          column: 1
        }
      },
      "16": {
        start: {
          line: 27,
          column: 2
        },
        end: {
          line: 33,
          column: 9
        }
      },
      "17": {
        start: {
          line: 29,
          column: 4
        },
        end: {
          line: 32,
          column: 7
        }
      },
      "18": {
        start: {
          line: 35,
          column: 23
        },
        end: {
          line: 43,
          column: 1
        }
      },
      "19": {
        start: {
          line: 36,
          column: 2
        },
        end: {
          line: 42,
          column: 9
        }
      },
      "20": {
        start: {
          line: 38,
          column: 4
        },
        end: {
          line: 41,
          column: 7
        }
      },
      "21": {
        start: {
          line: 44,
          column: 18
        },
        end: {
          line: 47,
          column: 1
        }
      },
      "22": {
        start: {
          line: 46,
          column: 2
        },
        end: {
          line: 46,
          column: 263
        }
      },
      "23": {
        start: {
          line: 48,
          column: 18
        },
        end: {
          line: 51,
          column: 1
        }
      },
      "24": {
        start: {
          line: 50,
          column: 2
        },
        end: {
          line: 50,
          column: 265
        }
      },
      "25": {
        start: {
          line: 53,
          column: 2
        },
        end: {
          line: 53,
          column: 46
        }
      },
      "26": {
        start: {
          line: 55,
          column: 23
        },
        end: {
          line: 57,
          column: 1
        }
      },
      "27": {
        start: {
          line: 56,
          column: 2
        },
        end: {
          line: 56,
          column: 62
        }
      },
      "28": {
        start: {
          line: 58,
          column: 39
        },
        end: {
          line: 63,
          column: 1
        }
      },
      "29": {
        start: {
          line: 59,
          column: 2
        },
        end: {
          line: 61,
          column: 3
        }
      },
      "30": {
        start: {
          line: 60,
          column: 4
        },
        end: {
          line: 60,
          column: 14
        }
      },
      "31": {
        start: {
          line: 62,
          column: 2
        },
        end: {
          line: 62,
          column: 101
        }
      },
      "32": {
        start: {
          line: 64,
          column: 33
        },
        end: {
          line: 66,
          column: 1
        }
      },
      "33": {
        start: {
          line: 65,
          column: 2
        },
        end: {
          line: 65,
          column: 43
        }
      },
      "34": {
        start: {
          line: 67,
          column: 24
        },
        end: {
          line: 69,
          column: 1
        }
      },
      "35": {
        start: {
          line: 68,
          column: 2
        },
        end: {
          line: 68,
          column: 42
        }
      },
      "36": {
        start: {
          line: 70,
          column: 18
        },
        end: {
          line: 72,
          column: 1
        }
      },
      "37": {
        start: {
          line: 71,
          column: 2
        },
        end: {
          line: 71,
          column: 98
        }
      },
      "38": {
        start: {
          line: 73,
          column: 20
        },
        end: {
          line: 75,
          column: 1
        }
      },
      "39": {
        start: {
          line: 74,
          column: 2
        },
        end: {
          line: 74,
          column: 48
        }
      },
      "40": {
        start: {
          line: 76,
          column: 30
        },
        end: {
          line: 78,
          column: 1
        }
      },
      "41": {
        start: {
          line: 77,
          column: 2
        },
        end: {
          line: 77,
          column: 49
        }
      },
      "42": {
        start: {
          line: 79,
          column: 22
        },
        end: {
          line: 81,
          column: 1
        }
      },
      "43": {
        start: {
          line: 80,
          column: 2
        },
        end: {
          line: 80,
          column: 42
        }
      },
      "44": {
        start: {
          line: 82,
          column: 27
        },
        end: {
          line: 84,
          column: 1
        }
      },
      "45": {
        start: {
          line: 83,
          column: 2
        },
        end: {
          line: 83,
          column: 87
        }
      },
      "46": {
        start: {
          line: 85,
          column: 22
        },
        end: {
          line: 87,
          column: 1
        }
      },
      "47": {
        start: {
          line: 86,
          column: 2
        },
        end: {
          line: 86,
          column: 96
        }
      },
      "48": {
        start: {
          line: 88,
          column: 16
        },
        end: {
          line: 91,
          column: 1
        }
      },
      "49": {
        start: {
          line: 90,
          column: 2
        },
        end: {
          line: 90,
          column: 259
        }
      },
      "50": {
        start: {
          line: 92,
          column: 0
        },
        end: {
          line: 110,
          column: 2
        }
      }
    },
    fnMap: {
      "0": {
        name: "isEmpty",
        decl: {
          start: {
            line: 8,
            column: 23
          },
          end: {
            line: 8,
            column: 30
          }
        },
        loc: {
          start: {
            line: 8,
            column: 38
          },
          end: {
            line: 25,
            column: 1
          }
        },
        line: 8
      },
      "1": {
        name: "showToastError",
        decl: {
          start: {
            line: 26,
            column: 30
          },
          end: {
            line: 26,
            column: 44
          }
        },
        loc: {
          start: {
            line: 26,
            column: 54
          },
          end: {
            line: 34,
            column: 1
          }
        },
        line: 26
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 27,
            column: 13
          },
          end: {
            line: 27,
            column: 14
          }
        },
        loc: {
          start: {
            line: 27,
            column: 25
          },
          end: {
            line: 33,
            column: 3
          }
        },
        line: 27
      },
      "3": {
        name: "showToastSuccess",
        decl: {
          start: {
            line: 35,
            column: 32
          },
          end: {
            line: 35,
            column: 48
          }
        },
        loc: {
          start: {
            line: 35,
            column: 58
          },
          end: {
            line: 43,
            column: 1
          }
        },
        line: 35
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 36,
            column: 13
          },
          end: {
            line: 36,
            column: 14
          }
        },
        loc: {
          start: {
            line: 36,
            column: 25
          },
          end: {
            line: 42,
            column: 3
          }
        },
        line: 36
      },
      "5": {
        name: "showLoading",
        decl: {
          start: {
            line: 44,
            column: 27
          },
          end: {
            line: 44,
            column: 38
          }
        },
        loc: {
          start: {
            line: 44,
            column: 41
          },
          end: {
            line: 47,
            column: 1
          }
        },
        line: 44
      },
      "6": {
        name: "hideLoading",
        decl: {
          start: {
            line: 48,
            column: 27
          },
          end: {
            line: 48,
            column: 38
          }
        },
        loc: {
          start: {
            line: 48,
            column: 41
          },
          end: {
            line: 51,
            column: 1
          }
        },
        line: 48
      },
      "7": {
        name: "isArrayValid",
        decl: {
          start: {
            line: 52,
            column: 9
          },
          end: {
            line: 52,
            column: 21
          }
        },
        loc: {
          start: {
            line: 52,
            column: 27
          },
          end: {
            line: 54,
            column: 1
          }
        },
        line: 52
      },
      "8": {
        name: "removeDiacritics",
        decl: {
          start: {
            line: 55,
            column: 32
          },
          end: {
            line: 55,
            column: 48
          }
        },
        loc: {
          start: {
            line: 55,
            column: 54
          },
          end: {
            line: 57,
            column: 1
          }
        },
        line: 55
      },
      "9": {
        name: "regexTransformToEnglishCharacter",
        decl: {
          start: {
            line: 58,
            column: 48
          },
          end: {
            line: 58,
            column: 80
          }
        },
        loc: {
          start: {
            line: 58,
            column: 87
          },
          end: {
            line: 63,
            column: 1
          }
        },
        line: 58
      },
      "10": {
        name: "normalizeNumberAndAlphabet",
        decl: {
          start: {
            line: 64,
            column: 42
          },
          end: {
            line: 64,
            column: 68
          }
        },
        loc: {
          start: {
            line: 64,
            column: 75
          },
          end: {
            line: 66,
            column: 1
          }
        },
        line: 64
      },
      "11": {
        name: "regexTransferName",
        decl: {
          start: {
            line: 67,
            column: 33
          },
          end: {
            line: 67,
            column: 50
          }
        },
        loc: {
          start: {
            line: 67,
            column: 57
          },
          end: {
            line: 69,
            column: 1
          }
        },
        line: 67
      },
      "12": {
        name: "removeEmoji",
        decl: {
          start: {
            line: 70,
            column: 27
          },
          end: {
            line: 70,
            column: 38
          }
        },
        loc: {
          start: {
            line: 70,
            column: 44
          },
          end: {
            line: 72,
            column: 1
          }
        },
        line: 70
      },
      "13": {
        name: "regexNickName",
        decl: {
          start: {
            line: 73,
            column: 29
          },
          end: {
            line: 73,
            column: 42
          }
        },
        loc: {
          start: {
            line: 73,
            column: 49
          },
          end: {
            line: 75,
            column: 1
          }
        },
        line: 73
      },
      "14": {
        name: "regexAccountNumberInput",
        decl: {
          start: {
            line: 76,
            column: 39
          },
          end: {
            line: 76,
            column: 62
          }
        },
        loc: {
          start: {
            line: 76,
            column: 69
          },
          end: {
            line: 78,
            column: 1
          }
        },
        line: 76
      },
      "15": {
        name: "normalizeSpaces",
        decl: {
          start: {
            line: 79,
            column: 31
          },
          end: {
            line: 79,
            column: 46
          }
        },
        loc: {
          start: {
            line: 79,
            column: 53
          },
          end: {
            line: 81,
            column: 1
          }
        },
        line: 79
      },
      "16": {
        name: "regexTransferContent",
        decl: {
          start: {
            line: 82,
            column: 36
          },
          end: {
            line: 82,
            column: 56
          }
        },
        loc: {
          start: {
            line: 82,
            column: 63
          },
          end: {
            line: 84,
            column: 1
          }
        },
        line: 82
      },
      "17": {
        name: "transferContent",
        decl: {
          start: {
            line: 85,
            column: 31
          },
          end: {
            line: 85,
            column: 46
          }
        },
        loc: {
          start: {
            line: 85,
            column: 57
          },
          end: {
            line: 87,
            column: 1
          }
        },
        line: 85
      },
      "18": {
        name: "showPopup",
        decl: {
          start: {
            line: 88,
            column: 25
          },
          end: {
            line: 88,
            column: 34
          }
        },
        loc: {
          start: {
            line: 88,
            column: 41
          },
          end: {
            line: 91,
            column: 1
          }
        },
        line: 88
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 9,
            column: 2
          },
          end: {
            line: 11,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 9,
            column: 2
          },
          end: {
            line: 11,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 9
      },
      "1": {
        loc: {
          start: {
            line: 9,
            column: 6
          },
          end: {
            line: 9,
            column: 43
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 9,
            column: 6
          },
          end: {
            line: 9,
            column: 20
          }
        }, {
          start: {
            line: 9,
            column: 24
          },
          end: {
            line: 9,
            column: 43
          }
        }],
        line: 9
      },
      "2": {
        loc: {
          start: {
            line: 12,
            column: 2
          },
          end: {
            line: 14,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 12,
            column: 2
          },
          end: {
            line: 14,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 12
      },
      "3": {
        loc: {
          start: {
            line: 12,
            column: 6
          },
          end: {
            line: 12,
            column: 54
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 12,
            column: 6
          },
          end: {
            line: 12,
            column: 31
          }
        }, {
          start: {
            line: 12,
            column: 35
          },
          end: {
            line: 12,
            column: 54
          }
        }],
        line: 12
      },
      "4": {
        loc: {
          start: {
            line: 15,
            column: 2
          },
          end: {
            line: 17,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 15,
            column: 2
          },
          end: {
            line: 17,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 15
      },
      "5": {
        loc: {
          start: {
            line: 15,
            column: 6
          },
          end: {
            line: 15,
            column: 47
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 15,
            column: 6
          },
          end: {
            line: 15,
            column: 31
          }
        }, {
          start: {
            line: 15,
            column: 35
          },
          end: {
            line: 15,
            column: 47
          }
        }],
        line: 15
      },
      "6": {
        loc: {
          start: {
            line: 18,
            column: 2
          },
          end: {
            line: 20,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 18,
            column: 2
          },
          end: {
            line: 20,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 18
      },
      "7": {
        loc: {
          start: {
            line: 18,
            column: 6
          },
          end: {
            line: 18,
            column: 48
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 18,
            column: 6
          },
          end: {
            line: 18,
            column: 26
          }
        }, {
          start: {
            line: 18,
            column: 30
          },
          end: {
            line: 18,
            column: 48
          }
        }],
        line: 18
      },
      "8": {
        loc: {
          start: {
            line: 21,
            column: 2
          },
          end: {
            line: 23,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 21,
            column: 2
          },
          end: {
            line: 23,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 21
      },
      "9": {
        loc: {
          start: {
            line: 21,
            column: 6
          },
          end: {
            line: 21,
            column: 66
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 21,
            column: 6
          },
          end: {
            line: 21,
            column: 31
          }
        }, {
          start: {
            line: 21,
            column: 35
          },
          end: {
            line: 21,
            column: 66
          }
        }],
        line: 21
      },
      "10": {
        loc: {
          start: {
            line: 29,
            column: 4
          },
          end: {
            line: 32,
            column: 6
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 29,
            column: 4
          },
          end: {
            line: 29,
            column: 95
          }
        }, {
          start: {
            line: 29,
            column: 99
          },
          end: {
            line: 32,
            column: 6
          }
        }],
        line: 29
      },
      "11": {
        loc: {
          start: {
            line: 38,
            column: 4
          },
          end: {
            line: 41,
            column: 6
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 38,
            column: 4
          },
          end: {
            line: 38,
            column: 96
          }
        }, {
          start: {
            line: 38,
            column: 100
          },
          end: {
            line: 41,
            column: 6
          }
        }],
        line: 38
      },
      "12": {
        loc: {
          start: {
            line: 46,
            column: 2
          },
          end: {
            line: 46,
            column: 262
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 46,
            column: 2
          },
          end: {
            line: 46,
            column: 78
          }
        }, {
          start: {
            line: 46,
            column: 82
          },
          end: {
            line: 46,
            column: 141
          }
        }, {
          start: {
            line: 46,
            column: 145
          },
          end: {
            line: 46,
            column: 216
          }
        }, {
          start: {
            line: 46,
            column: 220
          },
          end: {
            line: 46,
            column: 262
          }
        }],
        line: 46
      },
      "13": {
        loc: {
          start: {
            line: 50,
            column: 2
          },
          end: {
            line: 50,
            column: 264
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 50,
            column: 2
          },
          end: {
            line: 50,
            column: 78
          }
        }, {
          start: {
            line: 50,
            column: 82
          },
          end: {
            line: 50,
            column: 141
          }
        }, {
          start: {
            line: 50,
            column: 145
          },
          end: {
            line: 50,
            column: 216
          }
        }, {
          start: {
            line: 50,
            column: 220
          },
          end: {
            line: 50,
            column: 264
          }
        }],
        line: 50
      },
      "14": {
        loc: {
          start: {
            line: 53,
            column: 9
          },
          end: {
            line: 53,
            column: 45
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 53,
            column: 9
          },
          end: {
            line: 53,
            column: 27
          }
        }, {
          start: {
            line: 53,
            column: 31
          },
          end: {
            line: 53,
            column: 45
          }
        }],
        line: 53
      },
      "15": {
        loc: {
          start: {
            line: 59,
            column: 2
          },
          end: {
            line: 61,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 59,
            column: 2
          },
          end: {
            line: 61,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 59
      },
      "16": {
        loc: {
          start: {
            line: 90,
            column: 2
          },
          end: {
            line: 90,
            column: 258
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 90,
            column: 2
          },
          end: {
            line: 90,
            column: 78
          }
        }, {
          start: {
            line: 90,
            column: 82
          },
          end: {
            line: 90,
            column: 141
          }
        }, {
          start: {
            line: 90,
            column: 145
          },
          end: {
            line: 90,
            column: 216
          }
        }, {
          start: {
            line: 90,
            column: 220
          },
          end: {
            line: 90,
            column: 258
          }
        }],
        line: 90
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0
    },
    b: {
      "0": [0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0, 0, 0],
      "13": [0, 0, 0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0, 0, 0]
    },
    inputSourceMap: {
      version: 3,
      file: undefined,
      names: ["msb_host_shared_module_1", "require", "i18n_1", "isEmpty", "value", "undefined", "trim", "isNaN", "Array", "isArray", "length", "Object", "keys", "showToastError", "message", "setTimeout", "_msb_host_shared_modu", "hostSharedModule", "d", "domainService", "showToast", "type", "ToastType", "ERROR", "showToastSuccess", "_msb_host_shared_modu2", "SUCCESS", "showLoading", "_msb_host_shared_modu3", "addSpinnerRequest", "hideLoading", "_msb_host_shared_modu4", "addSpinnerCompleted", "isArrayValid", "arr", "removeDiacritics", "str", "normalize", "replace", "regexTransformToEnglishCharacter", "text", "normalizeNumberAndAlphabet", "regexTransferName", "removeEmoji", "regexNickName", "regexAccountNumberInput", "normalizeSpaces", "regexTransferContent", "transferContent", "fullname", "translate", "showPopup", "data", "_msb_host_shared_modu5", "exports", "default"],
      sourceRoot: undefined,
      sources: ["/Users/<USER>/projects/react-native/payment-module/src/utils/Utils.ts"],
      sourcesContent: ["import {hostSharedModule, ToastType} from 'msb-host-shared-module';\n// import * as ProviderLogos from '../assets/images/remote/providers-icon';\n// import Images from '../assets/images/Images';\nimport {SafeAny} from '../commons/Constants';\nimport {translate} from '../locales/i18n';\nimport {CustomIDomainService} from '../../@types/msb-host-shared-module';\n\nconst isEmpty = (value: SafeAny): boolean => {\n  if (value === null || value === undefined) {\n    return true;\n  }\n  if (typeof value === 'string' && value.trim() === '') {\n    return true;\n  }\n  if (typeof value === 'number' && isNaN(value)) {\n    return true;\n  }\n  if (Array.isArray(value) && value.length === 0) {\n    return true;\n  }\n  if (typeof value === 'object' && Object.keys(value).length === 0) {\n    return true;\n  }\n\n  return false;\n};\n\nconst showToastError = (message: string) => {\n  setTimeout(() => {\n    hostSharedModule.d.domainService?.showToast({\n      message,\n      type: ToastType.ERROR, // TODO for toast failed type\n    });\n  }, 50);\n};\n\nconst showToastSuccess = (message: string) => {\n  setTimeout(() => {\n    hostSharedModule.d.domainService?.showToast({\n      message,\n      type: ToastType.SUCCESS,\n    });\n  }, 50);\n};\n\nconst showLoading = () => {\n  hostSharedModule?.d?.domainService?.addSpinnerRequest();\n};\n\nconst hideLoading = () => {\n  hostSharedModule?.d?.domainService?.addSpinnerCompleted();\n};\n\nfunction isArrayValid<T>(arr: T[] | null | undefined): boolean {\n  return Array.isArray(arr) && arr.length > 0;\n}\n\nconst removeDiacritics = (str: string) => {\n  return str.normalize('NFD').replace(/[\\u0300-\\u036f]/g, '');\n};\n\nconst regexTransformToEnglishCharacter = (text: string): string => {\n  if (!text) {\n    return '';\n  }\n  return text\n    .normalize('NFD')\n    .replace(/[\\u0300-\\u036f]/g, '')\n    .replace(/\u0111/g, 'd')\n    .replace(/\u0110/g, 'D');\n};\n\nconst normalizeNumberAndAlphabet = (text: string): string => {\n  // Ch\u1EC9 cho ph\xE9p s\u1ED1 v\xE0 ch\u1EEF c\xE1i (lo\u1EA1i b\u1ECF k\xFD t\u1EF1 \u0111\u1EB7c bi\u1EC7t)\n  return text.replace(/[^a-zA-Z0-9]/g, '');\n};\n\nconst regexTransferName = (text: string): string => {\n  // Ch\u1EC9 cho ph\xE9p s\u1ED1 v\xE0 ch\u1EEF c\xE1i, d\u1EA5u c\xE1ch (lo\u1EA1i b\u1ECF k\xFD t\u1EF1 \u0111\u1EB7c bi\u1EC7t)\n  return text.replace(/[^a-zA-Z\\s]/g, '');\n};\n\nconst removeEmoji = (str: string) => {\n  return str.replace(/[\\uD83C-\\uDBFF\\uDC00-\\uDFFF]+/gu, '');\n};\n\nconst regexNickName = (text: string): string => {\n  // Ch\u1EC9 cho ph\xE9p s\u1ED1 v\xE0 ch\u1EEF c\xE1i c\xF3 d\u1EA5u, d\u1EA5u c\xE1ch (lo\u1EA1i b\u1ECF k\xFD t\u1EF1 \u0111\u1EB7c bi\u1EC7t)\n  return text.replace(/[^a-zA-Z\xC0-\u1EF4\xE0-\u1EF5\\s]/g, '');\n};\n\nconst regexAccountNumberInput = (text: string): string => {\n  return text.replace(/[^0-9a-zA-Z\xC0-\u1EF4\xE0-\u1EF5]/g, '');\n};\n\n// Lo\u1EA1i b\u1ECF kho\u1EA3ng tr\u1EAFng d\u01B0 th\u1EEBa gi\u1EEFa c\xE1c t\u1EEB, gi\u1EEF l\u1EA1i 1 kho\u1EA3ng tr\u1EAFng\nconst normalizeSpaces = (text: string): string => {\n  return text.replace(/\\s+/g, ' ').trim();\n};\n\nconst regexTransferContent = (text: string): string => {\n  // Ch\u1EC9 cho ph\xE9p s\u1ED1 v\xE0 ch\u1EEF c\xE1i ti\u1EBFng anh, ti\u1EBFng vi\u1EC7t (lo\u1EA1i b\u1ECF k\xFD t\u1EF1 \u0111\u1EB7c bi\u1EC7t ngo\u1EA1i tr\u1EEB -/().+_,)\n  return regexTransformToEnglishCharacter(text).replace(/[^0-9a-zA-Z\\s().,!+|=]/g, '');\n};\n\n// n\u1ED9i dung chuy\u1EC3n ti\u1EC1n m\u1EB7c \u0111\u1ECBnh\nconst transferContent = (fullname: string): string => {\n  return `${regexTransferContent(fullname)} ${translate('common.transfer')}`.trim();\n};\n\nconst showPopup: CustomIDomainService['showPopup'] = data => {\n  hostSharedModule?.d?.domainService?.showPopup(data);\n};\n\nexport default {\n  isEmpty,\n  removeDiacritics,\n  showToastError,\n  isArrayValid,\n  showToastSuccess,\n  hideLoading,\n  showLoading,\n  normalizeNumberAndAlphabet,\n  regexTransferName,\n  regexTransformToEnglishCharacter,\n  removeEmoji,\n  regexNickName,\n  regexAccountNumberInput,\n  normalizeSpaces,\n  regexTransferContent,\n  transferContent,\n  showPopup,\n};\n"],
      mappings: ";;;;;AAAA,IAAAA,wBAAA,GAAAC,OAAA;AAIA,IAAAC,MAAA,GAAAD,OAAA;AAGA,IAAME,OAAO,GAAG,SAAVA,OAAOA,CAAIC,KAAc,EAAa;EAC1C,IAAIA,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAKC,SAAS,EAAE;IACzC,OAAO,IAAI;EACb;EACA,IAAI,OAAOD,KAAK,KAAK,QAAQ,IAAIA,KAAK,CAACE,IAAI,EAAE,KAAK,EAAE,EAAE;IACpD,OAAO,IAAI;EACb;EACA,IAAI,OAAOF,KAAK,KAAK,QAAQ,IAAIG,KAAK,CAACH,KAAK,CAAC,EAAE;IAC7C,OAAO,IAAI;EACb;EACA,IAAII,KAAK,CAACC,OAAO,CAACL,KAAK,CAAC,IAAIA,KAAK,CAACM,MAAM,KAAK,CAAC,EAAE;IAC9C,OAAO,IAAI;EACb;EACA,IAAI,OAAON,KAAK,KAAK,QAAQ,IAAIO,MAAM,CAACC,IAAI,CAACR,KAAK,CAAC,CAACM,MAAM,KAAK,CAAC,EAAE;IAChE,OAAO,IAAI;EACb;EAEA,OAAO,KAAK;AACd,CAAC;AAED,IAAMG,cAAc,GAAG,SAAjBA,cAAcA,CAAIC,OAAe,EAAI;EACzCC,UAAU,CAAC,YAAK;IAAA,IAAAC,qBAAA;IACd,CAAAA,qBAAA,GAAAhB,wBAAA,CAAAiB,gBAAgB,CAACC,CAAC,CAACC,aAAa,aAAhCH,qBAAA,CAAkCI,SAAS,CAAC;MAC1CN,OAAO,EAAPA,OAAO;MACPO,IAAI,EAAErB,wBAAA,CAAAsB,SAAS,CAACC;KACjB,CAAC;EACJ,CAAC,EAAE,EAAE,CAAC;AACR,CAAC;AAED,IAAMC,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAAIV,OAAe,EAAI;EAC3CC,UAAU,CAAC,YAAK;IAAA,IAAAU,sBAAA;IACd,CAAAA,sBAAA,GAAAzB,wBAAA,CAAAiB,gBAAgB,CAACC,CAAC,CAACC,aAAa,aAAhCM,sBAAA,CAAkCL,SAAS,CAAC;MAC1CN,OAAO,EAAPA,OAAO;MACPO,IAAI,EAAErB,wBAAA,CAAAsB,SAAS,CAACI;KACjB,CAAC;EACJ,CAAC,EAAE,EAAE,CAAC;AACR,CAAC;AAED,IAAMC,WAAW,GAAG,SAAdA,WAAWA,CAAA,EAAQ;EAAA,IAAAC,sBAAA;EACvB,CAAAA,sBAAA,GAAA5B,wBAAA,CAAAiB,gBAAgB,cAAAW,sBAAA,GAAhBA,sBAAA,CAAkBV,CAAC,cAAAU,sBAAA,GAAnBA,sBAAA,CAAqBT,aAAa,aAAlCS,sBAAA,CAAoCC,iBAAiB,EAAE;AACzD,CAAC;AAED,IAAMC,WAAW,GAAG,SAAdA,WAAWA,CAAA,EAAQ;EAAA,IAAAC,sBAAA;EACvB,CAAAA,sBAAA,GAAA/B,wBAAA,CAAAiB,gBAAgB,cAAAc,sBAAA,GAAhBA,sBAAA,CAAkBb,CAAC,cAAAa,sBAAA,GAAnBA,sBAAA,CAAqBZ,aAAa,aAAlCY,sBAAA,CAAoCC,mBAAmB,EAAE;AAC3D,CAAC;AAED,SAASC,YAAYA,CAAIC,GAA2B;EAClD,OAAO1B,KAAK,CAACC,OAAO,CAACyB,GAAG,CAAC,IAAIA,GAAG,CAACxB,MAAM,GAAG,CAAC;AAC7C;AAEA,IAAMyB,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAAIC,GAAW,EAAI;EACvC,OAAOA,GAAG,CAACC,SAAS,CAAC,KAAK,CAAC,CAACC,OAAO,CAAC,kBAAkB,EAAE,EAAE,CAAC;AAC7D,CAAC;AAED,IAAMC,gCAAgC,GAAG,SAAnCA,gCAAgCA,CAAIC,IAAY,EAAY;EAChE,IAAI,CAACA,IAAI,EAAE;IACT,OAAO,EAAE;EACX;EACA,OAAOA,IAAI,CACRH,SAAS,CAAC,KAAK,CAAC,CAChBC,OAAO,CAAC,kBAAkB,EAAE,EAAE,CAAC,CAC/BA,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAClBA,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC;AACvB,CAAC;AAED,IAAMG,0BAA0B,GAAG,SAA7BA,0BAA0BA,CAAID,IAAY,EAAY;EAE1D,OAAOA,IAAI,CAACF,OAAO,CAAC,eAAe,EAAE,EAAE,CAAC;AAC1C,CAAC;AAED,IAAMI,iBAAiB,GAAG,SAApBA,iBAAiBA,CAAIF,IAAY,EAAY;EAEjD,OAAOA,IAAI,CAACF,OAAO,CAAC,cAAc,EAAE,EAAE,CAAC;AACzC,CAAC;AAED,IAAMK,WAAW,GAAG,SAAdA,WAAWA,CAAIP,GAAW,EAAI;EAClC,OAAOA,GAAG,CAACE,OAAO,CAAC,uEAAiC,EAAE,EAAE,CAAC;AAC3D,CAAC;AAED,IAAMM,aAAa,GAAG,SAAhBA,aAAaA,CAAIJ,IAAY,EAAY;EAE7C,OAAOA,IAAI,CAACF,OAAO,CAAC,oBAAoB,EAAE,EAAE,CAAC;AAC/C,CAAC;AAED,IAAMO,uBAAuB,GAAG,SAA1BA,uBAAuBA,CAAIL,IAAY,EAAY;EACvD,OAAOA,IAAI,CAACF,OAAO,CAAC,qBAAqB,EAAE,EAAE,CAAC;AAChD,CAAC;AAGD,IAAMQ,eAAe,GAAG,SAAlBA,eAAeA,CAAIN,IAAY,EAAY;EAC/C,OAAOA,IAAI,CAACF,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAAChC,IAAI,EAAE;AACzC,CAAC;AAED,IAAMyC,oBAAoB,GAAG,SAAvBA,oBAAoBA,CAAIP,IAAY,EAAY;EAEpD,OAAOD,gCAAgC,CAACC,IAAI,CAAC,CAACF,OAAO,CAAC,yBAAyB,EAAE,EAAE,CAAC;AACtF,CAAC;AAGD,IAAMU,eAAe,GAAG,SAAlBA,eAAeA,CAAIC,QAAgB,EAAY;EACnD,OAAO,GAAGF,oBAAoB,CAACE,QAAQ,CAAC,IAAI,IAAA/C,MAAA,CAAAgD,SAAS,EAAC,iBAAiB,CAAC,EAAE,CAAC5C,IAAI,EAAE;AACnF,CAAC;AAED,IAAM6C,SAAS,GAAsC,SAA/CA,SAASA,CAAsCC,IAAI,EAAG;EAAA,IAAAC,sBAAA;EAC1D,CAAAA,sBAAA,GAAArD,wBAAA,CAAAiB,gBAAgB,cAAAoC,sBAAA,GAAhBA,sBAAA,CAAkBnC,CAAC,cAAAmC,sBAAA,GAAnBA,sBAAA,CAAqBlC,aAAa,aAAlCkC,sBAAA,CAAoCF,SAAS,CAACC,IAAI,CAAC;AACrD,CAAC;AAEDE,OAAA,CAAAC,OAAA,GAAe;EACbpD,OAAO,EAAPA,OAAO;EACPgC,gBAAgB,EAAhBA,gBAAgB;EAChBtB,cAAc,EAAdA,cAAc;EACdoB,YAAY,EAAZA,YAAY;EACZT,gBAAgB,EAAhBA,gBAAgB;EAChBM,WAAW,EAAXA,WAAW;EACXH,WAAW,EAAXA,WAAW;EACXc,0BAA0B,EAA1BA,0BAA0B;EAC1BC,iBAAiB,EAAjBA,iBAAiB;EACjBH,gCAAgC,EAAhCA,gCAAgC;EAChCI,WAAW,EAAXA,WAAW;EACXC,aAAa,EAAbA,aAAa;EACbC,uBAAuB,EAAvBA,uBAAuB;EACvBC,eAAe,EAAfA,eAAe;EACfC,oBAAoB,EAApBA,oBAAoB;EACpBC,eAAe,EAAfA,eAAe;EACfG,SAAS,EAATA;CACD",
      ignoreList: []
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "1d9516092919e8486811c68db07112bb953ddcdc"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_1zwhcw8kem = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_1zwhcw8kem();
cov_1zwhcw8kem().s[0]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
var msb_host_shared_module_1 =
/* istanbul ignore next */
(cov_1zwhcw8kem().s[1]++, require("msb-host-shared-module"));
var i18n_1 =
/* istanbul ignore next */
(cov_1zwhcw8kem().s[2]++, require("../locales/i18n"));
/* istanbul ignore next */
cov_1zwhcw8kem().s[3]++;
var isEmpty = function isEmpty(value) {
  /* istanbul ignore next */
  cov_1zwhcw8kem().f[0]++;
  cov_1zwhcw8kem().s[4]++;
  if (
  /* istanbul ignore next */
  (cov_1zwhcw8kem().b[1][0]++, value === null) ||
  /* istanbul ignore next */
  (cov_1zwhcw8kem().b[1][1]++, value === undefined)) {
    /* istanbul ignore next */
    cov_1zwhcw8kem().b[0][0]++;
    cov_1zwhcw8kem().s[5]++;
    return true;
  } else
  /* istanbul ignore next */
  {
    cov_1zwhcw8kem().b[0][1]++;
  }
  cov_1zwhcw8kem().s[6]++;
  if (
  /* istanbul ignore next */
  (cov_1zwhcw8kem().b[3][0]++, typeof value === 'string') &&
  /* istanbul ignore next */
  (cov_1zwhcw8kem().b[3][1]++, value.trim() === '')) {
    /* istanbul ignore next */
    cov_1zwhcw8kem().b[2][0]++;
    cov_1zwhcw8kem().s[7]++;
    return true;
  } else
  /* istanbul ignore next */
  {
    cov_1zwhcw8kem().b[2][1]++;
  }
  cov_1zwhcw8kem().s[8]++;
  if (
  /* istanbul ignore next */
  (cov_1zwhcw8kem().b[5][0]++, typeof value === 'number') &&
  /* istanbul ignore next */
  (cov_1zwhcw8kem().b[5][1]++, isNaN(value))) {
    /* istanbul ignore next */
    cov_1zwhcw8kem().b[4][0]++;
    cov_1zwhcw8kem().s[9]++;
    return true;
  } else
  /* istanbul ignore next */
  {
    cov_1zwhcw8kem().b[4][1]++;
  }
  cov_1zwhcw8kem().s[10]++;
  if (
  /* istanbul ignore next */
  (cov_1zwhcw8kem().b[7][0]++, Array.isArray(value)) &&
  /* istanbul ignore next */
  (cov_1zwhcw8kem().b[7][1]++, value.length === 0)) {
    /* istanbul ignore next */
    cov_1zwhcw8kem().b[6][0]++;
    cov_1zwhcw8kem().s[11]++;
    return true;
  } else
  /* istanbul ignore next */
  {
    cov_1zwhcw8kem().b[6][1]++;
  }
  cov_1zwhcw8kem().s[12]++;
  if (
  /* istanbul ignore next */
  (cov_1zwhcw8kem().b[9][0]++, typeof value === 'object') &&
  /* istanbul ignore next */
  (cov_1zwhcw8kem().b[9][1]++, Object.keys(value).length === 0)) {
    /* istanbul ignore next */
    cov_1zwhcw8kem().b[8][0]++;
    cov_1zwhcw8kem().s[13]++;
    return true;
  } else
  /* istanbul ignore next */
  {
    cov_1zwhcw8kem().b[8][1]++;
  }
  cov_1zwhcw8kem().s[14]++;
  return false;
};
/* istanbul ignore next */
cov_1zwhcw8kem().s[15]++;
var showToastError = function showToastError(message) {
  /* istanbul ignore next */
  cov_1zwhcw8kem().f[1]++;
  cov_1zwhcw8kem().s[16]++;
  setTimeout(function () {
    /* istanbul ignore next */
    cov_1zwhcw8kem().f[2]++;
    var _msb_host_shared_modu;
    /* istanbul ignore next */
    cov_1zwhcw8kem().s[17]++;
    /* istanbul ignore next */
    (cov_1zwhcw8kem().b[10][0]++, (_msb_host_shared_modu = msb_host_shared_module_1.hostSharedModule.d.domainService) == null) ||
    /* istanbul ignore next */
    (cov_1zwhcw8kem().b[10][1]++, _msb_host_shared_modu.showToast({
      message: message,
      type: msb_host_shared_module_1.ToastType.ERROR
    }));
  }, 50);
};
/* istanbul ignore next */
cov_1zwhcw8kem().s[18]++;
var showToastSuccess = function showToastSuccess(message) {
  /* istanbul ignore next */
  cov_1zwhcw8kem().f[3]++;
  cov_1zwhcw8kem().s[19]++;
  setTimeout(function () {
    /* istanbul ignore next */
    cov_1zwhcw8kem().f[4]++;
    var _msb_host_shared_modu2;
    /* istanbul ignore next */
    cov_1zwhcw8kem().s[20]++;
    /* istanbul ignore next */
    (cov_1zwhcw8kem().b[11][0]++, (_msb_host_shared_modu2 = msb_host_shared_module_1.hostSharedModule.d.domainService) == null) ||
    /* istanbul ignore next */
    (cov_1zwhcw8kem().b[11][1]++, _msb_host_shared_modu2.showToast({
      message: message,
      type: msb_host_shared_module_1.ToastType.SUCCESS
    }));
  }, 50);
};
/* istanbul ignore next */
cov_1zwhcw8kem().s[21]++;
var showLoading = function showLoading() {
  /* istanbul ignore next */
  cov_1zwhcw8kem().f[5]++;
  var _msb_host_shared_modu3;
  /* istanbul ignore next */
  cov_1zwhcw8kem().s[22]++;
  /* istanbul ignore next */
  (cov_1zwhcw8kem().b[12][0]++, (_msb_host_shared_modu3 = msb_host_shared_module_1.hostSharedModule) == null) ||
  /* istanbul ignore next */
  (cov_1zwhcw8kem().b[12][1]++, (_msb_host_shared_modu3 = _msb_host_shared_modu3.d) == null) ||
  /* istanbul ignore next */
  (cov_1zwhcw8kem().b[12][2]++, (_msb_host_shared_modu3 = _msb_host_shared_modu3.domainService) == null) ||
  /* istanbul ignore next */
  (cov_1zwhcw8kem().b[12][3]++, _msb_host_shared_modu3.addSpinnerRequest());
};
/* istanbul ignore next */
cov_1zwhcw8kem().s[23]++;
var hideLoading = function hideLoading() {
  /* istanbul ignore next */
  cov_1zwhcw8kem().f[6]++;
  var _msb_host_shared_modu4;
  /* istanbul ignore next */
  cov_1zwhcw8kem().s[24]++;
  /* istanbul ignore next */
  (cov_1zwhcw8kem().b[13][0]++, (_msb_host_shared_modu4 = msb_host_shared_module_1.hostSharedModule) == null) ||
  /* istanbul ignore next */
  (cov_1zwhcw8kem().b[13][1]++, (_msb_host_shared_modu4 = _msb_host_shared_modu4.d) == null) ||
  /* istanbul ignore next */
  (cov_1zwhcw8kem().b[13][2]++, (_msb_host_shared_modu4 = _msb_host_shared_modu4.domainService) == null) ||
  /* istanbul ignore next */
  (cov_1zwhcw8kem().b[13][3]++, _msb_host_shared_modu4.addSpinnerCompleted());
};
function isArrayValid(arr) {
  /* istanbul ignore next */
  cov_1zwhcw8kem().f[7]++;
  cov_1zwhcw8kem().s[25]++;
  return /* istanbul ignore next */(cov_1zwhcw8kem().b[14][0]++, Array.isArray(arr)) &&
  /* istanbul ignore next */
  (cov_1zwhcw8kem().b[14][1]++, arr.length > 0);
}
/* istanbul ignore next */
cov_1zwhcw8kem().s[26]++;
var removeDiacritics = function removeDiacritics(str) {
  /* istanbul ignore next */
  cov_1zwhcw8kem().f[8]++;
  cov_1zwhcw8kem().s[27]++;
  return str.normalize('NFD').replace(/[\u0300-\u036f]/g, '');
};
/* istanbul ignore next */
cov_1zwhcw8kem().s[28]++;
var regexTransformToEnglishCharacter = function regexTransformToEnglishCharacter(text) {
  /* istanbul ignore next */
  cov_1zwhcw8kem().f[9]++;
  cov_1zwhcw8kem().s[29]++;
  if (!text) {
    /* istanbul ignore next */
    cov_1zwhcw8kem().b[15][0]++;
    cov_1zwhcw8kem().s[30]++;
    return '';
  } else
  /* istanbul ignore next */
  {
    cov_1zwhcw8kem().b[15][1]++;
  }
  cov_1zwhcw8kem().s[31]++;
  return text.normalize('NFD').replace(/[\u0300-\u036f]/g, '').replace(/đ/g, 'd').replace(/Đ/g, 'D');
};
/* istanbul ignore next */
cov_1zwhcw8kem().s[32]++;
var normalizeNumberAndAlphabet = function normalizeNumberAndAlphabet(text) {
  /* istanbul ignore next */
  cov_1zwhcw8kem().f[10]++;
  cov_1zwhcw8kem().s[33]++;
  return text.replace(/[^a-zA-Z0-9]/g, '');
};
/* istanbul ignore next */
cov_1zwhcw8kem().s[34]++;
var regexTransferName = function regexTransferName(text) {
  /* istanbul ignore next */
  cov_1zwhcw8kem().f[11]++;
  cov_1zwhcw8kem().s[35]++;
  return text.replace(/[^a-zA-Z\s]/g, '');
};
/* istanbul ignore next */
cov_1zwhcw8kem().s[36]++;
var removeEmoji = function removeEmoji(str) {
  /* istanbul ignore next */
  cov_1zwhcw8kem().f[12]++;
  cov_1zwhcw8kem().s[37]++;
  return str.replace(/(?:[\x2D\uD83C-\uFFFF]|[\uD800-\uDBFE][\uDC00-\uDFFF]|\uDBFF\uDC00)+/g, '');
};
/* istanbul ignore next */
cov_1zwhcw8kem().s[38]++;
var regexNickName = function regexNickName(text) {
  /* istanbul ignore next */
  cov_1zwhcw8kem().f[13]++;
  cov_1zwhcw8kem().s[39]++;
  return text.replace(/[^a-zA-ZÀ-Ỵà-ỵ\s]/g, '');
};
/* istanbul ignore next */
cov_1zwhcw8kem().s[40]++;
var regexAccountNumberInput = function regexAccountNumberInput(text) {
  /* istanbul ignore next */
  cov_1zwhcw8kem().f[14]++;
  cov_1zwhcw8kem().s[41]++;
  return text.replace(/[^0-9a-zA-ZÀ-Ỵà-ỵ]/g, '');
};
/* istanbul ignore next */
cov_1zwhcw8kem().s[42]++;
var normalizeSpaces = function normalizeSpaces(text) {
  /* istanbul ignore next */
  cov_1zwhcw8kem().f[15]++;
  cov_1zwhcw8kem().s[43]++;
  return text.replace(/\s+/g, ' ').trim();
};
/* istanbul ignore next */
cov_1zwhcw8kem().s[44]++;
var regexTransferContent = function regexTransferContent(text) {
  /* istanbul ignore next */
  cov_1zwhcw8kem().f[16]++;
  cov_1zwhcw8kem().s[45]++;
  return regexTransformToEnglishCharacter(text).replace(/[^0-9a-zA-Z\s().,!+|=]/g, '');
};
/* istanbul ignore next */
cov_1zwhcw8kem().s[46]++;
var transferContent = function transferContent(fullname) {
  /* istanbul ignore next */
  cov_1zwhcw8kem().f[17]++;
  cov_1zwhcw8kem().s[47]++;
  return `${regexTransferContent(fullname)} ${(0, i18n_1.translate)('common.transfer')}`.trim();
};
/* istanbul ignore next */
cov_1zwhcw8kem().s[48]++;
var showPopup = function showPopup(data) {
  /* istanbul ignore next */
  cov_1zwhcw8kem().f[18]++;
  var _msb_host_shared_modu5;
  /* istanbul ignore next */
  cov_1zwhcw8kem().s[49]++;
  /* istanbul ignore next */
  (cov_1zwhcw8kem().b[16][0]++, (_msb_host_shared_modu5 = msb_host_shared_module_1.hostSharedModule) == null) ||
  /* istanbul ignore next */
  (cov_1zwhcw8kem().b[16][1]++, (_msb_host_shared_modu5 = _msb_host_shared_modu5.d) == null) ||
  /* istanbul ignore next */
  (cov_1zwhcw8kem().b[16][2]++, (_msb_host_shared_modu5 = _msb_host_shared_modu5.domainService) == null) ||
  /* istanbul ignore next */
  (cov_1zwhcw8kem().b[16][3]++, _msb_host_shared_modu5.showPopup(data));
};
/* istanbul ignore next */
cov_1zwhcw8kem().s[50]++;
exports.default = {
  isEmpty: isEmpty,
  removeDiacritics: removeDiacritics,
  showToastError: showToastError,
  isArrayValid: isArrayValid,
  showToastSuccess: showToastSuccess,
  hideLoading: hideLoading,
  showLoading: showLoading,
  normalizeNumberAndAlphabet: normalizeNumberAndAlphabet,
  regexTransferName: regexTransferName,
  regexTransformToEnglishCharacter: regexTransformToEnglishCharacter,
  removeEmoji: removeEmoji,
  regexNickName: regexNickName,
  regexAccountNumberInput: regexAccountNumberInput,
  normalizeSpaces: normalizeSpaces,
  regexTransferContent: regexTransferContent,
  transferContent: transferContent,
  showPopup: showPopup
};
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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