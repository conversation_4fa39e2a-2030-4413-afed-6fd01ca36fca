e8070fe3420ef9c004a91417836ee9ed
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = exports.__INTERNAL_VIEW_CONFIG = exports.Commands = void 0;
var NativeComponentRegistry = _interopRequireWildcard(require("../../NativeComponent/NativeComponentRegistry"));
var _codegenNativeCommands = _interopRequireDefault(require("../../Utilities/codegenNativeCommands"));
var _RCTTextInputViewConfig = _interopRequireDefault(require("./RCTTextInputViewConfig"));
function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
var Commands = exports.Commands = (0, _codegenNativeCommands.default)({
  supportedCommands: ['focus', 'blur', 'setTextAndSelection']
});
var __INTERNAL_VIEW_CONFIG = exports.__INTERNAL_VIEW_CONFIG = Object.assign({
  uiViewClassName: 'RCTMultilineTextInputView'
}, _RCTTextInputViewConfig.default, {
  validAttributes: Object.assign({}, _RCTTextInputViewConfig.default.validAttributes, {
    dataDetectorTypes: true
  })
});
var MultilineTextInputNativeComponent = NativeComponentRegistry.get('RCTMultilineTextInputView', function () {
  return __INTERNAL_VIEW_CONFIG;
});
var _default = exports.default = MultilineTextInputNativeComponent;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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