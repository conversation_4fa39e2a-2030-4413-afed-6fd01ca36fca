{"version": 3, "names": ["NativeComponentRegistry", "_interopRequireWildcard", "require", "_codegenNativeCommands", "_interopRequireDefault", "_RCTTextInputViewConfig", "_getRequireWildcardCache", "e", "WeakMap", "r", "t", "__esModule", "default", "has", "get", "n", "__proto__", "a", "Object", "defineProperty", "getOwnPropertyDescriptor", "u", "hasOwnProperty", "call", "i", "set", "Commands", "exports", "codegenNativeCommands", "supportedCommands", "__INTERNAL_VIEW_CONFIG", "assign", "uiViewClassName", "RCTTextInputViewConfig", "validAttributes", "dataDetectorTypes", "MultilineTextInputNativeComponent", "_default"], "sources": ["RCTMultilineTextInputNativeComponent.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @flow strict-local\n * @format\n */\n\nimport type {\n  HostComponent,\n  PartialViewConfig,\n} from '../../Renderer/shims/ReactNativeTypes';\nimport type {TextInputNativeCommands} from './TextInputNativeCommands';\n\nimport * as NativeComponentRegistry from '../../NativeComponent/NativeComponentRegistry';\nimport codegenNativeCommands from '../../Utilities/codegenNativeCommands';\nimport RCTTextInputViewConfig from './RCTTextInputViewConfig';\n\ntype NativeType = HostComponent<{...}>;\n\ntype NativeCommands = TextInputNativeCommands<NativeType>;\n\nexport const Commands: NativeCommands = codegenNativeCommands<NativeCommands>({\n  supportedCommands: ['focus', 'blur', 'setTextAndSelection'],\n});\n\nexport const __INTERNAL_VIEW_CONFIG: PartialViewConfig = {\n  uiViewClassName: 'RCTMultilineTextInputView',\n  ...RCTTextInputViewConfig,\n  validAttributes: {\n    ...RCTTextInputViewConfig.validAttributes,\n    dataDetectorTypes: true,\n  },\n};\n\nconst MultilineTextInputNativeComponent: HostComponent<{...}> =\n  NativeComponentRegistry.get<{...}>(\n    'RCTMultilineTextInputView',\n    () => __INTERNAL_VIEW_CONFIG,\n  );\n\n// flowlint-next-line unclear-type:off\nexport default ((MultilineTextInputNativeComponent: any): HostComponent<{...}>);\n"], "mappings": ";;;;;AAgBA,IAAAA,uBAAA,GAAAC,uBAAA,CAAAC,OAAA;AACA,IAAAC,sBAAA,GAAAC,sBAAA,CAAAF,OAAA;AACA,IAAAG,uBAAA,GAAAD,sBAAA,CAAAF,OAAA;AAA8D,SAAAI,yBAAAC,CAAA,6BAAAC,OAAA,mBAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,yBAAAC,CAAA,WAAAA,CAAA,GAAAG,CAAA,GAAAD,CAAA,KAAAF,CAAA;AAAA,SAAAN,wBAAAM,CAAA,EAAAE,CAAA,SAAAA,CAAA,IAAAF,CAAA,IAAAA,CAAA,CAAAI,UAAA,SAAAJ,CAAA,eAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,WAAAK,OAAA,EAAAL,CAAA,QAAAG,CAAA,GAAAJ,wBAAA,CAAAG,CAAA,OAAAC,CAAA,IAAAA,CAAA,CAAAG,GAAA,CAAAN,CAAA,UAAAG,CAAA,CAAAI,GAAA,CAAAP,CAAA,OAAAQ,CAAA,KAAAC,SAAA,UAAAC,CAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,CAAA,IAAAd,CAAA,oBAAAc,CAAA,OAAAC,cAAA,CAAAC,IAAA,CAAAhB,CAAA,EAAAc,CAAA,SAAAG,CAAA,GAAAP,CAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAb,CAAA,EAAAc,CAAA,UAAAG,CAAA,KAAAA,CAAA,CAAAV,GAAA,IAAAU,CAAA,CAAAC,GAAA,IAAAP,MAAA,CAAAC,cAAA,CAAAJ,CAAA,EAAAM,CAAA,EAAAG,CAAA,IAAAT,CAAA,CAAAM,CAAA,IAAAd,CAAA,CAAAc,CAAA,YAAAN,CAAA,CAAAH,OAAA,GAAAL,CAAA,EAAAG,CAAA,IAAAA,CAAA,CAAAe,GAAA,CAAAlB,CAAA,EAAAQ,CAAA,GAAAA,CAAA;AAMvD,IAAMW,QAAwB,GAAAC,OAAA,CAAAD,QAAA,GAAG,IAAAE,8BAAqB,EAAiB;EAC5EC,iBAAiB,EAAE,CAAC,OAAO,EAAE,MAAM,EAAE,qBAAqB;AAC5D,CAAC,CAAC;AAEK,IAAMC,sBAAyC,GAAAH,OAAA,CAAAG,sBAAA,GAAAZ,MAAA,CAAAa,MAAA;EACpDC,eAAe,EAAE;AAA2B,GACzCC,+BAAsB;EACzBC,eAAe,EAAAhB,MAAA,CAAAa,MAAA,KACVE,+BAAsB,CAACC,eAAe;IACzCC,iBAAiB,EAAE;EAAI;AACxB,EACF;AAED,IAAMC,iCAAuD,GAC3DpC,uBAAuB,CAACc,GAAG,CACzB,2BAA2B,EAC3B;EAAA,OAAMgB,sBAAsB;AAAA,CAC9B,CAAC;AAAC,IAAAO,QAAA,GAAAV,OAAA,CAAAf,OAAA,GAGawB,iCAAiC", "ignoreList": []}