{"version": 3, "names": ["Object", "defineProperty", "exports", "value", "valueSetter", "mutable", "forceUpdate", "arguments", "length", "undefined", "previousAnimation", "_animation", "cancelled", "onFrame", "animation", "_value", "current", "isHigherOrder", "callback", "initializeAnimation", "timestamp", "onStart", "currentTimestamp", "global", "__frameTimestamp", "_getAnimationTimestamp", "step", "newTimestamp", "finished", "requestAnimationFrame"], "sources": ["../../src/valueSetter.ts"], "sourcesContent": [null], "mappings": "AAAA,YAAY;;AAAAA,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,KAAA;AAAA;AAAAD,OAAA,CAAAE,WAAA,GAAAA,WAAA;AAGL,SAASA,WAAWA,CACzBC,OAAuB,EACvBF,KAAY,EAEN;EACN,SAAS;;EAAA,IAFTG,WAAW,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,KAAK;EAGnB,IAAMG,iBAAiB,GAAGL,OAAO,CAACM,UAAU;EAC5C,IAAID,iBAAiB,EAAE;IACrBA,iBAAiB,CAACE,SAAS,GAAG,IAAI;IAClCP,OAAO,CAACM,UAAU,GAAG,IAAI;EAC3B;EACA,IACE,OAAOR,KAAK,KAAK,UAAU,IAC1BA,KAAK,KAAK,IAAI,IACb,OAAOA,KAAK,KAAK,QAAQ,IAExBA,KAAK,CAAgCU,OAAO,KAAKJ,SAAU,EAC9D;IACA,IAAMK,SAAiC,GACrC,OAAOX,KAAK,KAAK,UAAU,GAEtBA,KAAK,CAAkC,CAAC,GAExCA,KAA2C;IAKlD,IACEE,OAAO,CAACU,MAAM,KAAKD,SAAS,CAACE,OAAO,IACpC,CAACF,SAAS,CAACG,aAAa,IACxB,CAACX,WAAW,EACZ;MACAQ,SAAS,CAACI,QAAQ,IAAIJ,SAAS,CAACI,QAAQ,CAAC,IAAI,CAAC;MAC9C;IACF;IAEA,IAAMC,mBAAmB,GAAI,SAAvBA,mBAAmBA,CAAIC,SAAiB,EAAK;MACjDN,SAAS,CAACO,OAAO,CAACP,SAAS,EAAET,OAAO,CAACF,KAAK,EAAEiB,SAAS,EAAEV,iBAAiB,CAAC;IAC3E,CAAC;IACD,IAAMY,gBAAgB,GACpBC,MAAM,CAACC,gBAAgB,IAAID,MAAM,CAACE,sBAAsB,CAAC,CAAC;IAC5DN,mBAAmB,CAACG,gBAAgB,CAAC;IAErC,IAAMI,KAAI,GAAI,SAARA,IAAIA,CAAIC,YAAoB,EAAK;MAKrC,IAAMP,SAAS,GACbO,YAAY,IAAIb,SAAS,CAACM,SAAS,IAAI,CAAC,CAAC,GACrCN,SAAS,CAACM,SAAS,GACnBO,YAAY;MAElB,IAAIb,SAAS,CAACF,SAAS,EAAE;QACvBE,SAAS,CAACI,QAAQ,IAAIJ,SAAS,CAACI,QAAQ,CAAC,KAAoB,CAAC;QAC9D;MACF;MACA,IAAMU,QAAQ,GAAGd,SAAS,CAACD,OAAO,CAACC,SAAS,EAAEM,SAAS,CAAC;MACxDN,SAAS,CAACc,QAAQ,GAAG,IAAI;MACzBd,SAAS,CAACM,SAAS,GAAGA,SAAS;MAI/Bf,OAAO,CAACU,MAAM,GAAGD,SAAS,CAACE,OAAQ;MACnC,IAAIY,QAAQ,EAAE;QACZd,SAAS,CAACI,QAAQ,IAAIJ,SAAS,CAACI,QAAQ,CAAC,IAAmB,CAAC;MAC/D,CAAC,MAAM;QACLW,qBAAqB,CAACH,KAAI,CAAC;MAC7B;IACF,CAAC;IAEDrB,OAAO,CAACM,UAAU,GAAGG,SAAS;IAE9BY,KAAI,CAACJ,gBAAgB,CAAC;EACxB,CAAC,MAAM;IAGL,IAAIjB,OAAO,CAACU,MAAM,KAAKZ,KAAK,IAAI,CAACG,WAAW,EAAE;MAC5C;IACF;IACAD,OAAO,CAACU,MAAM,GAAGZ,KAAK;EACxB;AACF", "ignoreList": []}