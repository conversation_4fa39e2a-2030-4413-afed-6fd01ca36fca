d0a8da68ebde37d1a2d4e858fc869ad2
Object.defineProperty(exports, "__esModule", {
  value: true
});
var _exportNames = {};
exports.default = void 0;
var _NativeDeviceInfo = _interopRequireWildcard(require("../../src/private/specs/modules/NativeDeviceInfo"));
Object.keys(_NativeDeviceInfo).forEach(function (key) {
  if (key === "default" || key === "__esModule") return;
  if (Object.prototype.hasOwnProperty.call(_exportNames, key)) return;
  if (key in exports && exports[key] === _NativeDeviceInfo[key]) return;
  Object.defineProperty(exports, key, {
    enumerable: true,
    get: function get() {
      return _NativeDeviceInfo[key];
    }
  });
});
function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
var _default = exports.default = _NativeDeviceInfo.default;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJfTmF0aXZlRGV2aWNlSW5mbyIsIl9pbnRlcm9wUmVxdWlyZVdpbGRjYXJkIiwicmVxdWlyZSIsIk9iamVjdCIsImtleXMiLCJmb3JFYWNoIiwia2V5IiwicHJvdG90eXBlIiwiaGFzT3duUHJvcGVydHkiLCJjYWxsIiwiX2V4cG9ydE5hbWVzIiwiZXhwb3J0cyIsImRlZmluZVByb3BlcnR5IiwiZW51bWVyYWJsZSIsImdldCIsIl9nZXRSZXF1aXJlV2lsZGNhcmRDYWNoZSIsImUiLCJXZWFrTWFwIiwiciIsInQiLCJfX2VzTW9kdWxlIiwiZGVmYXVsdCIsImhhcyIsIm4iLCJfX3Byb3RvX18iLCJhIiwiZ2V0T3duUHJvcGVydHlEZXNjcmlwdG9yIiwidSIsImkiLCJzZXQiLCJfZGVmYXVsdCIsIk5hdGl2ZURldmljZUluZm8iXSwic291cmNlcyI6WyJOYXRpdmVEZXZpY2VJbmZvLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQ29weXJpZ2h0IChjKSBNZXRhIFBsYXRmb3JtcywgSW5jLiBhbmQgYWZmaWxpYXRlcy5cbiAqXG4gKiBUaGlzIHNvdXJjZSBjb2RlIGlzIGxpY2Vuc2VkIHVuZGVyIHRoZSBNSVQgbGljZW5zZSBmb3VuZCBpbiB0aGVcbiAqIExJQ0VOU0UgZmlsZSBpbiB0aGUgcm9vdCBkaXJlY3Rvcnkgb2YgdGhpcyBzb3VyY2UgdHJlZS5cbiAqXG4gKiBAZmxvdyBzdHJpY3RcbiAqIEBmb3JtYXRcbiAqL1xuXG5leHBvcnQgKiBmcm9tICcuLi8uLi9zcmMvcHJpdmF0ZS9zcGVjcy9tb2R1bGVzL05hdGl2ZURldmljZUluZm8nO1xuaW1wb3J0IE5hdGl2ZURldmljZUluZm8gZnJvbSAnLi4vLi4vc3JjL3ByaXZhdGUvc3BlY3MvbW9kdWxlcy9OYXRpdmVEZXZpY2VJbmZvJztcbmV4cG9ydCBkZWZhdWx0IE5hdGl2ZURldmljZUluZm87XG4iXSwibWFwcGluZ3MiOiI7Ozs7O0FBVUEsSUFBQUEsaUJBQUEsR0FBQUMsdUJBQUEsQ0FBQUMsT0FBQTtBQUFBQyxNQUFBLENBQUFDLElBQUEsQ0FBQUosaUJBQUEsRUFBQUssT0FBQSxXQUFBQyxHQUFBO0VBQUEsSUFBQUEsR0FBQSxrQkFBQUEsR0FBQTtFQUFBLElBQUFILE1BQUEsQ0FBQUksU0FBQSxDQUFBQyxjQUFBLENBQUFDLElBQUEsQ0FBQUMsWUFBQSxFQUFBSixHQUFBO0VBQUEsSUFBQUEsR0FBQSxJQUFBSyxPQUFBLElBQUFBLE9BQUEsQ0FBQUwsR0FBQSxNQUFBTixpQkFBQSxDQUFBTSxHQUFBO0VBQUFILE1BQUEsQ0FBQVMsY0FBQSxDQUFBRCxPQUFBLEVBQUFMLEdBQUE7SUFBQU8sVUFBQTtJQUFBQyxHQUFBLFdBQUFBLElBQUE7TUFBQSxPQUFBZCxpQkFBQSxDQUFBTSxHQUFBO0lBQUE7RUFBQTtBQUFBO0FBQWlFLFNBQUFTLHlCQUFBQyxDQUFBLDZCQUFBQyxPQUFBLG1CQUFBQyxDQUFBLE9BQUFELE9BQUEsSUFBQUUsQ0FBQSxPQUFBRixPQUFBLFlBQUFGLHdCQUFBLFlBQUFBLHlCQUFBQyxDQUFBLFdBQUFBLENBQUEsR0FBQUcsQ0FBQSxHQUFBRCxDQUFBLEtBQUFGLENBQUE7QUFBQSxTQUFBZix3QkFBQWUsQ0FBQSxFQUFBRSxDQUFBLFNBQUFBLENBQUEsSUFBQUYsQ0FBQSxJQUFBQSxDQUFBLENBQUFJLFVBQUEsU0FBQUosQ0FBQSxlQUFBQSxDQUFBLHVCQUFBQSxDQUFBLHlCQUFBQSxDQUFBLFdBQUFLLE9BQUEsRUFBQUwsQ0FBQSxRQUFBRyxDQUFBLEdBQUFKLHdCQUFBLENBQUFHLENBQUEsT0FBQUMsQ0FBQSxJQUFBQSxDQUFBLENBQUFHLEdBQUEsQ0FBQU4sQ0FBQSxVQUFBRyxDQUFBLENBQUFMLEdBQUEsQ0FBQUUsQ0FBQSxPQUFBTyxDQUFBLEtBQUFDLFNBQUEsVUFBQUMsQ0FBQSxHQUFBdEIsTUFBQSxDQUFBUyxjQUFBLElBQUFULE1BQUEsQ0FBQXVCLHdCQUFBLFdBQUFDLENBQUEsSUFBQVgsQ0FBQSxvQkFBQVcsQ0FBQSxPQUFBbkIsY0FBQSxDQUFBQyxJQUFBLENBQUFPLENBQUEsRUFBQVcsQ0FBQSxTQUFBQyxDQUFBLEdBQUFILENBQUEsR0FBQXRCLE1BQUEsQ0FBQXVCLHdCQUFBLENBQUFWLENBQUEsRUFBQVcsQ0FBQSxVQUFBQyxDQUFBLEtBQUFBLENBQUEsQ0FBQWQsR0FBQSxJQUFBYyxDQUFBLENBQUFDLEdBQUEsSUFBQTFCLE1BQUEsQ0FBQVMsY0FBQSxDQUFBVyxDQUFBLEVBQUFJLENBQUEsRUFBQUMsQ0FBQSxJQUFBTCxDQUFBLENBQUFJLENBQUEsSUFBQVgsQ0FBQSxDQUFBVyxDQUFBLFlBQUFKLENBQUEsQ0FBQUYsT0FBQSxHQUFBTCxDQUFBLEVBQUFHLENBQUEsSUFBQUEsQ0FBQSxDQUFBVSxHQUFBLENBQUFiLENBQUEsRUFBQU8sQ0FBQSxHQUFBQSxDQUFBO0FBQUEsSUFBQU8sUUFBQSxHQUFBbkIsT0FBQSxDQUFBVSxPQUFBLEdBRWxEVSx5QkFBZ0IiLCJpZ25vcmVMaXN0IjpbXX0=