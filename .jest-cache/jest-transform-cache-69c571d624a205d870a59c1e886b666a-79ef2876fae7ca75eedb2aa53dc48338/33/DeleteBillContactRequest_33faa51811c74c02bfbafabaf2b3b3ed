4babafdd10dfecb113ebe028e138831c
"use strict";

/* istanbul ignore next */
function cov_fhgtl32hg() {
  var path = "/Users/<USER>/projects/react-native/payment-module/src/data/models/delete-bill-contact/DeleteBillContactRequest.ts";
  var hash = "4447085ee27a04c0c88b053395a0813249fbd67d";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/projects/react-native/payment-module/src/data/models/delete-bill-contact/DeleteBillContactRequest.ts",
    statementMap: {
      "0": {
        start: {
          line: 3,
          column: 0
        },
        end: {
          line: 5,
          column: 3
        }
      }
    },
    fnMap: {},
    branchMap: {},
    s: {
      "0": 0
    },
    f: {},
    b: {},
    inputSourceMap: {
      version: 3,
      file: undefined,
      names: [],
      sourceRoot: undefined,
      sources: ["/Users/<USER>/projects/react-native/payment-module/src/data/models/delete-bill-contact/DeleteBillContactRequest.ts"],
      sourcesContent: ["export interface DeleteBillContactRequest {\n  id: string;\n}\n"],
      mappings: "",
      ignoreList: []
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "4447085ee27a04c0c88b053395a0813249fbd67d"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_fhgtl32hg = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_fhgtl32hg();
cov_fhgtl32hg().s[0]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6W10sInNvdXJjZXMiOlsiL1VzZXJzL3NvbnNhbXNldC9wcm9qZWN0cy9yZWFjdC1uYXRpdmUvcGF5bWVudC1tb2R1bGUvc3JjL2RhdGEvbW9kZWxzL2RlbGV0ZS1iaWxsLWNvbnRhY3QvRGVsZXRlQmlsbENvbnRhY3RSZXF1ZXN0LnRzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBpbnRlcmZhY2UgRGVsZXRlQmlsbENvbnRhY3RSZXF1ZXN0IHtcbiAgaWQ6IHN0cmluZztcbn1cbiJdLCJtYXBwaW5ncyI6IiIsImlnbm9yZUxpc3QiOltdfQ==