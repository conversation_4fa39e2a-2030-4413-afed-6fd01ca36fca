{"version": 3, "names": ["exports", "mapEditBillContactResponseToModel", "EditBillContactModel_1", "cov_mcp131wru", "s", "require", "response", "f", "EditBillContactModel"], "sources": ["/Users/<USER>/projects/react-native/payment-module/src/data/mappers/edit-bill-contact/EditBillContactMapper.ts"], "sourcesContent": ["import {EditBillContactResponse} from '../../models/edit-bill-contact/EditBillContactResponse';\nimport {EditBillContactModel} from '../../../domain/entities/edit-bill-contact/EditBillContactModel';\n\nexport function mapEditBillContactResponseToModel(response: EditBillContactResponse): EditBillContactModel {\n  return new EditBillContactModel();\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGAA,OAAA,CAAAC,iCAAA,GAAAA,iCAAA;AAFA,IAAAC,sBAAA;AAAA;AAAA,CAAAC,aAAA,GAAAC,CAAA,OAAAC,OAAA;AAEA,SAAgBJ,iCAAiCA,CAACK,QAAiC;EAAA;EAAAH,aAAA,GAAAI,CAAA;EAAAJ,aAAA,GAAAC,CAAA;EACjF,OAAO,IAAIF,sBAAA,CAAAM,oBAAoB,EAAE;AACnC", "ignoreList": []}