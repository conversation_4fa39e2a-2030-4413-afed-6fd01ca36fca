9ec73afa62c59d4f43d9d9d2052370da
"use strict";

/* istanbul ignore next */
function cov_mcp131wru() {
  var path = "/Users/<USER>/projects/react-native/payment-module/src/data/mappers/edit-bill-contact/EditBillContactMapper.ts";
  var hash = "94fc47ec8d209ba5699312016992926ada631153";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/projects/react-native/payment-module/src/data/mappers/edit-bill-contact/EditBillContactMapper.ts",
    statementMap: {
      "0": {
        start: {
          line: 3,
          column: 0
        },
        end: {
          line: 5,
          column: 3
        }
      },
      "1": {
        start: {
          line: 6,
          column: 0
        },
        end: {
          line: 6,
          column: 78
        }
      },
      "2": {
        start: {
          line: 7,
          column: 29
        },
        end: {
          line: 7,
          column: 103
        }
      },
      "3": {
        start: {
          line: 9,
          column: 2
        },
        end: {
          line: 9,
          column: 59
        }
      }
    },
    fnMap: {
      "0": {
        name: "mapEditBillContactResponseToModel",
        decl: {
          start: {
            line: 8,
            column: 9
          },
          end: {
            line: 8,
            column: 42
          }
        },
        loc: {
          start: {
            line: 8,
            column: 53
          },
          end: {
            line: 10,
            column: 1
          }
        },
        line: 8
      }
    },
    branchMap: {},
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0
    },
    f: {
      "0": 0
    },
    b: {},
    inputSourceMap: {
      version: 3,
      file: undefined,
      names: ["exports", "mapEditBillContactResponseToModel", "EditBillContactModel_1", "require", "response", "EditBillContactModel"],
      sourceRoot: undefined,
      sources: ["/Users/<USER>/projects/react-native/payment-module/src/data/mappers/edit-bill-contact/EditBillContactMapper.ts"],
      sourcesContent: ["import {EditBillContactResponse} from '../../models/edit-bill-contact/EditBillContactResponse';\nimport {EditBillContactModel} from '../../../domain/entities/edit-bill-contact/EditBillContactModel';\n\nexport function mapEditBillContactResponseToModel(response: EditBillContactResponse): EditBillContactModel {\n  return new EditBillContactModel();\n}\n"],
      mappings: ";;;;;AAGAA,OAAA,CAAAC,iCAAA,GAAAA,iCAAA;AAFA,IAAAC,sBAAA,GAAAC,OAAA;AAEA,SAAgBF,iCAAiCA,CAACG,QAAiC;EACjF,OAAO,IAAIF,sBAAA,CAAAG,oBAAoB,EAAE;AACnC",
      ignoreList: []
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "94fc47ec8d209ba5699312016992926ada631153"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_mcp131wru = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_mcp131wru();
cov_mcp131wru().s[0]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_mcp131wru().s[1]++;
exports.mapEditBillContactResponseToModel = mapEditBillContactResponseToModel;
var EditBillContactModel_1 =
/* istanbul ignore next */
(cov_mcp131wru().s[2]++, require("../../../domain/entities/edit-bill-contact/EditBillContactModel"));
function mapEditBillContactResponseToModel(response) {
  /* istanbul ignore next */
  cov_mcp131wru().f[0]++;
  cov_mcp131wru().s[3]++;
  return new EditBillContactModel_1.EditBillContactModel();
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJleHBvcnRzIiwibWFwRWRpdEJpbGxDb250YWN0UmVzcG9uc2VUb01vZGVsIiwiRWRpdEJpbGxDb250YWN0TW9kZWxfMSIsImNvdl9tY3AxMzF3cnUiLCJzIiwicmVxdWlyZSIsInJlc3BvbnNlIiwiZiIsIkVkaXRCaWxsQ29udGFjdE1vZGVsIl0sInNvdXJjZXMiOlsiL1VzZXJzL3NvbnNhbXNldC9wcm9qZWN0cy9yZWFjdC1uYXRpdmUvcGF5bWVudC1tb2R1bGUvc3JjL2RhdGEvbWFwcGVycy9lZGl0LWJpbGwtY29udGFjdC9FZGl0QmlsbENvbnRhY3RNYXBwZXIudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHtFZGl0QmlsbENvbnRhY3RSZXNwb25zZX0gZnJvbSAnLi4vLi4vbW9kZWxzL2VkaXQtYmlsbC1jb250YWN0L0VkaXRCaWxsQ29udGFjdFJlc3BvbnNlJztcbmltcG9ydCB7RWRpdEJpbGxDb250YWN0TW9kZWx9IGZyb20gJy4uLy4uLy4uL2RvbWFpbi9lbnRpdGllcy9lZGl0LWJpbGwtY29udGFjdC9FZGl0QmlsbENvbnRhY3RNb2RlbCc7XG5cbmV4cG9ydCBmdW5jdGlvbiBtYXBFZGl0QmlsbENvbnRhY3RSZXNwb25zZVRvTW9kZWwocmVzcG9uc2U6IEVkaXRCaWxsQ29udGFjdFJlc3BvbnNlKTogRWRpdEJpbGxDb250YWN0TW9kZWwge1xuICByZXR1cm4gbmV3IEVkaXRCaWxsQ29udGFjdE1vZGVsKCk7XG59XG4iXSwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBR0FBLE9BQUEsQ0FBQUMsaUNBQUEsR0FBQUEsaUNBQUE7QUFGQSxJQUFBQyxzQkFBQTtBQUFBO0FBQUEsQ0FBQUMsYUFBQSxHQUFBQyxDQUFBLE9BQUFDLE9BQUE7QUFFQSxTQUFnQkosaUNBQWlDQSxDQUFDSyxRQUFpQztFQUFBO0VBQUFILGFBQUEsR0FBQUksQ0FBQTtFQUFBSixhQUFBLEdBQUFDLENBQUE7RUFDakYsT0FBTyxJQUFJRixzQkFBQSxDQUFBTSxvQkFBb0IsRUFBRTtBQUNuQyIsImlnbm9yZUxpc3QiOltdfQ==