{"version": 3, "names": ["Object", "defineProperty", "exports", "value", "PerformanceMonitor", "_react", "_interopRequireWildcard", "require", "_reactNative", "_index", "_index2", "_ConfigHelper", "_getRequireWildcardCache", "e", "WeakMap", "r", "t", "__esModule", "default", "has", "get", "n", "__proto__", "a", "getOwnPropertyDescriptor", "u", "hasOwnProperty", "call", "i", "set", "createCircularDoublesBuffer", "size", "next", "buffer", "Float32Array", "count", "push", "oldValue", "oldCount", "Math", "min", "front", "notEmpty", "current", "index", "back", "DEFAULT_BUFFER_SIZE", "addWhitelistedNativeProps", "text", "AnimatedTextInput", "createAnimatedComponent", "TextInput", "loopAnimationFrame", "fn", "lastTime", "loop", "requestAnimationFrame", "time", "getFps", "renderTimeInMs", "completeBufferRoutine", "timestamp", "_buffer$push", "round", "droppedTimestamp", "measuredRangeDuration", "JsPerformance", "_ref", "smoothingFrames", "jsFps", "useSharedValue", "totalRenderTime", "circular<PERSON>uffer", "useRef", "useEffect", "_", "currentFps", "toFixed", "animatedProps", "useAnimatedProps", "_jsFps$value", "defaultValue", "React", "createElement", "View", "style", "styles", "container", "editable", "UiPerformance", "_ref2", "uiFps", "useFrameCallback", "_ref3", "_uiFps$value", "_ref4", "_ref4$smoothingFrames", "monitor", "StyleSheet", "create", "flexDirection", "position", "backgroundColor", "zIndex", "header", "fontSize", "color", "paddingHorizontal", "fontFamily", "alignItems", "justifyContent", "flexWrap"], "sources": ["../../../src/component/PerformanceMonitor.tsx"], "sourcesContent": [null], "mappings": "AAAA,YAAY;;AAAAA,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,KAAA;AAAA;AAAAD,OAAA,CAAAE,kBAAA,GAAAA,kBAAA;AAEZ,IAAAC,MAAA,GAAAC,uBAAA,CAAAC,OAAA;AACA,IAAAC,YAAA,GAAAD,OAAA;AAGA,IAAAE,MAAA,GAAAF,OAAA;AACA,IAAAG,OAAA,GAAAH,OAAA;AACA,IAAAI,aAAA,GAAAJ,OAAA;AAA2D,SAAAK,yBAAAC,CAAA,6BAAAC,OAAA,mBAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,yBAAAC,CAAA,WAAAA,CAAA,GAAAG,CAAA,GAAAD,CAAA,KAAAF,CAAA;AAAA,SAAAP,wBAAAO,CAAA,EAAAE,CAAA,SAAAA,CAAA,IAAAF,CAAA,IAAAA,CAAA,CAAAI,UAAA,SAAAJ,CAAA,eAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,WAAAK,OAAA,EAAAL,CAAA,QAAAG,CAAA,GAAAJ,wBAAA,CAAAG,CAAA,OAAAC,CAAA,IAAAA,CAAA,CAAAG,GAAA,CAAAN,CAAA,UAAAG,CAAA,CAAAI,GAAA,CAAAP,CAAA,OAAAQ,CAAA,KAAAC,SAAA,UAAAC,CAAA,GAAAvB,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAwB,wBAAA,WAAAC,CAAA,IAAAZ,CAAA,oBAAAY,CAAA,OAAAC,cAAA,CAAAC,IAAA,CAAAd,CAAA,EAAAY,CAAA,SAAAG,CAAA,GAAAL,CAAA,GAAAvB,MAAA,CAAAwB,wBAAA,CAAAX,CAAA,EAAAY,CAAA,UAAAG,CAAA,KAAAA,CAAA,CAAAR,GAAA,IAAAQ,CAAA,CAAAC,GAAA,IAAA7B,MAAA,CAAAC,cAAA,CAAAoB,CAAA,EAAAI,CAAA,EAAAG,CAAA,IAAAP,CAAA,CAAAI,CAAA,IAAAZ,CAAA,CAAAY,CAAA,YAAAJ,CAAA,CAAAH,OAAA,GAAAL,CAAA,EAAAG,CAAA,IAAAA,CAAA,CAAAa,GAAA,CAAAhB,CAAA,EAAAQ,CAAA,GAAAA,CAAA;AAG3D,SAASS,2BAA2BA,CAACC,IAAY,EAAE;EACjD,SAAS;;EAET,OAAO;IACLC,IAAI,EAAE,CAAW;IACjBC,MAAM,EAAE,IAAIC,YAAY,CAACH,IAAI,CAAC;IAC9BA,IAAI,EAAJA,IAAI;IACJI,KAAK,EAAE,CAAW;IAElBC,IAAI,WAAJA,IAAIA,CAACjC,KAAa,EAAiB;MACjC,IAAMkC,QAAQ,GAAG,IAAI,CAACJ,MAAM,CAAC,IAAI,CAACD,IAAI,CAAC;MACvC,IAAMM,QAAQ,GAAG,IAAI,CAACH,KAAK;MAC3B,IAAI,CAACF,MAAM,CAAC,IAAI,CAACD,IAAI,CAAC,GAAG7B,KAAK;MAE9B,IAAI,CAAC6B,IAAI,GAAG,CAAC,IAAI,CAACA,IAAI,GAAG,CAAC,IAAI,IAAI,CAACD,IAAI;MACvC,IAAI,CAACI,KAAK,GAAGI,IAAI,CAACC,GAAG,CAAC,IAAI,CAACT,IAAI,EAAE,IAAI,CAACI,KAAK,GAAG,CAAC,CAAC;MAChD,OAAOG,QAAQ,KAAK,IAAI,CAACP,IAAI,GAAGM,QAAQ,GAAG,IAAI;IACjD,CAAC;IAEDI,KAAK,WAALA,KAAKA,CAAA,EAAkB;MACrB,IAAMC,QAAQ,GAAG,IAAI,CAACP,KAAK,GAAG,CAAC;MAC/B,IAAIO,QAAQ,EAAE;QACZ,IAAMC,OAAO,GAAG,IAAI,CAACX,IAAI,GAAG,CAAC;QAC7B,IAAMY,KAAK,GAAGD,OAAO,GAAG,CAAC,GAAG,IAAI,CAACZ,IAAI,GAAG,CAAC,GAAGY,OAAO;QACnD,OAAO,IAAI,CAACV,MAAM,CAACW,KAAK,CAAC;MAC3B;MACA,OAAO,IAAI;IACb,CAAC;IAEDC,IAAI,WAAJA,IAAIA,CAAA,EAAkB;MACpB,IAAMH,QAAQ,GAAG,IAAI,CAACP,KAAK,GAAG,CAAC;MAC/B,OAAOO,QAAQ,GAAG,IAAI,CAACT,MAAM,CAAC,IAAI,CAACD,IAAI,CAAC,GAAG,IAAI;IACjD;EACF,CAAC;AACH;AAEA,IAAMc,mBAAmB,GAAG,EAAE;AAC9B,IAAAC,uCAAyB,EAAC;EAAEC,IAAI,EAAE;AAAK,CAAC,CAAC;AACzC,IAAMC,iBAAiB,GAAG,IAAAC,+BAAuB,EAACC,sBAAS,CAAC;AAE5D,SAASC,kBAAkBA,CAACC,EAA4C,EAAE;EACxE,IAAIC,QAAQ,GAAG,CAAC;EAEhB,SAASC,IAAIA,CAAA,EAAG;IACdC,qBAAqB,CAAE,UAAAC,IAAI,EAAK;MAC9B,IAAIH,QAAQ,GAAG,CAAC,EAAE;QAChBD,EAAE,CAACC,QAAQ,EAAEG,IAAI,CAAC;MACpB;MACAH,QAAQ,GAAGG,IAAI;MACfD,qBAAqB,CAACD,IAAI,CAAC;IAC7B,CAAC,CAAC;EACJ;EAEAA,IAAI,CAAC,CAAC;AACR;AAEA,SAASG,MAAMA,CAACC,cAAsB,EAAU;EAC9C,SAAS;;EACT,OAAO,IAAI,GAAGA,cAAc;AAC9B;AAEA,SAASC,qBAAqBA,CAC5B3B,MAAsB,EACtB4B,SAAiB,EACT;EACR,SAAS;;EAAA,IAAAC,YAAA;EACTD,SAAS,GAAGtB,IAAI,CAACwB,KAAK,CAACF,SAAS,CAAC;EAEjC,IAAMG,gBAAgB,IAAAF,YAAA,GAAG7B,MAAM,CAACG,IAAI,CAACyB,SAAS,CAAC,YAAAC,YAAA,GAAID,SAAS;EAE5D,IAAMI,qBAAqB,GAAGJ,SAAS,GAAGG,gBAAgB;EAE1D,OAAON,MAAM,CAACO,qBAAqB,GAAGhC,MAAM,CAACE,KAAK,CAAC;AACrD;AAEA,SAAS+B,aAAaA,CAAAC,IAAA,EAAmD;EAAA,IAAhDC,eAAA,GAAAD,IAAA,CAAAC,eAAA;EACvB,IAAMC,KAAK,GAAG,IAAAC,qBAAc,EAAgB,IAAI,CAAC;EACjD,IAAMC,eAAe,GAAG,IAAAD,qBAAc,EAAC,CAAC,CAAC;EACzC,IAAME,cAAc,GAAG,IAAAC,aAAM,EAC3B3C,2BAA2B,CAACsC,eAAe,CAC7C,CAAC;EAED,IAAAM,gBAAS,EAAC,YAAM;IACdtB,kBAAkB,CAAC,UAACuB,CAAC,EAAEd,SAAS,EAAK;MACnCA,SAAS,GAAGtB,IAAI,CAACwB,KAAK,CAACF,SAAS,CAAC;MAEjC,IAAMe,UAAU,GAAGhB,qBAAqB,CACtCY,cAAc,CAAC7B,OAAO,EACtBkB,SACF,CAAC;MAIDQ,KAAK,CAAClE,KAAK,GAAG,CAACyE,UAAU,GAAG,CAAC,EAAEC,OAAO,CAAC,CAAC,CAAC;IAC3C,CAAC,CAAC;EACJ,CAAC,EAAE,CAACR,KAAK,EAAEE,eAAe,CAAC,CAAC;EAE5B,IAAMO,aAAa,GAAG,IAAAC,uBAAgB,EAAC,YAAM;IAAA,IAAAC,YAAA;IAC3C,IAAMhC,IAAI,GAAG,MAAM,KAAAgC,YAAA,GAAIX,KAAK,CAAClE,KAAK,YAAA6E,YAAA,GAAI,KAAK,CAAC,GAAG,GAAG;IAClD,OAAO;MAAEhC,IAAI,EAAJA,IAAI;MAAEiC,YAAY,EAAEjC;IAAK,CAAC;EACrC,CAAC,CAAC;EAEF,OACEkC,cAAA,CAAAC,aAAA,CAACC,iBAAI;IAACC,KAAK,EAAEC,MAAM,CAACC;EAAU,GAC5BL,cAAA,CAAAC,aAAA,CAAClC,iBAAiB;IAChBoC,KAAK,EAAEC,MAAM,CAACtC,IAAK;IACnB8B,aAAa,EAAEA,aAAc;IAC7BU,QAAQ,EAAE;EAAM,CACjB,CACG,CAAC;AAEX;AAEA,SAASC,aAAaA,CAAAC,KAAA,EAAmD;EAAA,IAAhDtB,eAAA,GAAAsB,KAAA,CAAAtB,eAAA;EACvB,IAAMuB,KAAK,GAAG,IAAArB,qBAAc,EAAgB,IAAI,CAAC;EACjD,IAAME,cAAc,GAAG,IAAAF,qBAAc,EAAwB,IAAI,CAAC;EAElE,IAAAsB,uBAAgB,EAAC,UAAAC,KAAA,EAA8B;IAAA,IAA3BhC,SAAA,GAAAgC,KAAA,CAAAhC,SAAA;IAClB,IAAIW,cAAc,CAACrE,KAAK,KAAK,IAAI,EAAE;MACjCqE,cAAc,CAACrE,KAAK,GAAG2B,2BAA2B,CAACsC,eAAe,CAAC;IACrE;IAEAP,SAAS,GAAGtB,IAAI,CAACwB,KAAK,CAACF,SAAS,CAAC;IAEjC,IAAMe,UAAU,GAAGhB,qBAAqB,CAACY,cAAc,CAACrE,KAAK,EAAE0D,SAAS,CAAC;IAEzE8B,KAAK,CAACxF,KAAK,GAAGyE,UAAU,CAACC,OAAO,CAAC,CAAC,CAAC;EACrC,CAAC,CAAC;EAEF,IAAMC,aAAa,GAAG,IAAAC,uBAAgB,EAAC,YAAM;IAAA,IAAAe,YAAA;IAC3C,IAAM9C,IAAI,GAAG,MAAM,KAAA8C,YAAA,GAAIH,KAAK,CAACxF,KAAK,YAAA2F,YAAA,GAAI,KAAK,CAAC,GAAG,GAAG;IAClD,OAAO;MAAE9C,IAAI,EAAJA,IAAI;MAAEiC,YAAY,EAAEjC;IAAK,CAAC;EACrC,CAAC,CAAC;EAEF,OACEkC,cAAA,CAAAC,aAAA,CAACC,iBAAI;IAACC,KAAK,EAAEC,MAAM,CAACC;EAAU,GAC5BL,cAAA,CAAAC,aAAA,CAAClC,iBAAiB;IAChBoC,KAAK,EAAEC,MAAM,CAACtC,IAAK;IACnB8B,aAAa,EAAEA,aAAc;IAC7BU,QAAQ,EAAE;EAAM,CACjB,CACG,CAAC;AAEX;AAoBO,SAASpF,kBAAkBA,CAAA2F,KAAA,EAEN;EAAA,IAAAC,qBAAA,GAAAD,KAAA,CAD1B3B,eAAe;IAAfA,eAAe,GAAA4B,qBAAA,cAAGlD,mBAAA,GAAAkD,qBAAA;EAElB,OACEd,cAAA,CAAAC,aAAA,CAACC,iBAAI;IAACC,KAAK,EAAEC,MAAM,CAACW;EAAQ,GAC1Bf,cAAA,CAAAC,aAAA,CAACjB,aAAa;IAACE,eAAe,EAAEA;EAAgB,CAAE,CAAC,EACnDc,cAAA,CAAAC,aAAA,CAACM,aAAa;IAACrB,eAAe,EAAEA;EAAgB,CAAE,CAC9C,CAAC;AAEX;AAEA,IAAMkB,MAAM,GAAGY,uBAAU,CAACC,MAAM,CAAC;EAC/BF,OAAO,EAAE;IACPG,aAAa,EAAE,KAAK;IACpBC,QAAQ,EAAE,UAAU;IACpBC,eAAe,EAAE,OAAO;IACxBC,MAAM,EAAE;EACV,CAAC;EACDC,MAAM,EAAE;IACNC,QAAQ,EAAE,EAAE;IACZC,KAAK,EAAE,OAAO;IACdC,iBAAiB,EAAE;EACrB,CAAC;EACD3D,IAAI,EAAE;IACJyD,QAAQ,EAAE,EAAE;IACZC,KAAK,EAAE,OAAO;IACdE,UAAU,EAAE,WAAW;IACvBD,iBAAiB,EAAE;EACrB,CAAC;EACDpB,SAAS,EAAE;IACTsB,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE,QAAQ;IACxBV,aAAa,EAAE,KAAK;IACpBW,QAAQ,EAAE;EACZ;AACF,CAAC,CAAC", "ignoreList": []}