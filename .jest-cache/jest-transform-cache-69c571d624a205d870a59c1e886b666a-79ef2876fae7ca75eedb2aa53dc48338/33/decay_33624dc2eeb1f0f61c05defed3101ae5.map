{"version": 3, "names": ["Object", "defineProperty", "exports", "value", "<PERSON><PERSON><PERSON><PERSON>", "_util", "require", "_rubberBandDecay", "_utils", "_rigidDecay", "_errors", "validateConfig", "config", "clamp", "Array", "isArray", "ReanimatedError", "length", "velocityFactor", "rubberBandEffect", "userConfig", "callback", "defineAnimation", "_config$velocity", "deceleration", "velocity", "rubberBandFactor", "keys", "for<PERSON>ach", "key", "decay", "isValidRubberBandConfig", "animation", "now", "rubberBandDecay", "rigidDecay", "onStart", "current", "lastTimestamp", "startTimestamp", "initialVelocity", "reduceMotion", "onFrame", "getReduceMotionForAnimation"], "sources": ["../../../../src/animation/decay/decay.ts"], "sourcesContent": [null], "mappings": "AAAA,YAAY;;AAAAA,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,KAAA;AAAA;AAAAD,OAAA,CAAAE,SAAA;AACZ,IAAAC,KAAA,GAAAC,OAAA;AAMA,IAAAC,gBAAA,GAAAD,OAAA;AACA,IAAAE,MAAA,GAAAF,OAAA;AAOA,IAAAG,WAAA,GAAAH,OAAA;AACA,IAAAI,OAAA,GAAAJ,OAAA;AAUA,SAASK,cAAcA,CAACC,MAA0B,EAAQ;EACxD,SAAS;;EACT,IAAIA,MAAM,CAACC,KAAK,EAAE;IAChB,IAAI,CAACC,KAAK,CAACC,OAAO,CAACH,MAAM,CAACC,KAAK,CAAC,EAAE;MAChC,MAAM,IAAIG,uBAAe,CACvB,4CAA4C,OAAOJ,MAAM,CAACC,KAAK,GACjE,CAAC;IACH;IACA,IAAID,MAAM,CAACC,KAAK,CAACI,MAAM,KAAK,CAAC,EAAE;MAC7B,MAAM,IAAID,uBAAe,CACvB,qDACEJ,MAAM,CAACC,KAAK,CAACI,MAAM,GAEvB,CAAC;IACH;EACF;EACA,IAAIL,MAAM,CAACM,cAAc,IAAI,CAAC,EAAE;IAC9B,MAAM,IAAIF,uBAAe,CACvB,2DAA2DJ,MAAM,CAACM,cAAc,GAClF,CAAC;EACH;EACA,IAAIN,MAAM,CAACO,gBAAgB,IAAI,CAACP,MAAM,CAACC,KAAK,EAAE;IAC5C,MAAM,IAAIG,uBAAe,CACvB,iEACF,CAAC;EACH;AACF;AAaO,IAAMZ,SAAS,GAAAF,OAAA,CAAAE,SAAA,GAAG,SAAZA,SAASA,CACpBgB,UAAuB,EACvBC,QAA4B,EACD;EAC3B,SAAS;;EAET,OAAO,IAAAC,qBAAe,EAAiB,CAAC,EAAE,YAAM;IAC9C,SAAS;;IAAA,IAAAC,gBAAA;IACT,IAAMX,MAA0B,GAAG;MACjCY,YAAY,EAAE,KAAK;MACnBN,cAAc,EAAE,CAAC;MACjBO,QAAQ,EAAE,CAAC;MACXC,gBAAgB,EAAE;IACpB,CAAC;IACD,IAAIN,UAAU,EAAE;MACdpB,MAAM,CAAC2B,IAAI,CAACP,UAAU,CAAC,CAACQ,OAAO,CAC5B,UAAAC,GAAG;QAAA,OACAjB,MAAM,CAASiB,GAAG,CAAC,GAAGT,UAAU,CAACS,GAAG,CAC1C;MAAA,EAAC;IACH;IAEA,IAAMC,KAA+D,GACnE,IAAAC,8BAAuB,EAACnB,MAAM,CAAC,GAC3B,UAACoB,SAAS,EAAEC,GAAG;MAAA,OAAK,IAAAC,gCAAe,EAACF,SAAS,EAAEC,GAAG,EAAErB,MAAM,CAAC;IAAA,IAC3D,UAACoB,SAAS,EAAEC,GAAG;MAAA,OAAK,IAAAE,sBAAU,EAACH,SAAS,EAAEC,GAAG,EAAErB,MAAM,CAAC;IAAA;IAE5D,SAASwB,OAAOA,CACdJ,SAAyB,EACzB7B,KAAa,EACb8B,GAAc,EACR;MACND,SAAS,CAACK,OAAO,GAAGlC,KAAK;MACzB6B,SAAS,CAACM,aAAa,GAAGL,GAAG;MAC7BD,SAAS,CAACO,cAAc,GAAGN,GAAG;MAC9BD,SAAS,CAACQ,eAAe,GAAG5B,MAAM,CAACa,QAAQ;MAC3Cd,cAAc,CAACC,MAAM,CAAC;MAEtB,IAAIoB,SAAS,CAACS,YAAY,IAAI7B,MAAM,CAACC,KAAK,EAAE;QAC1C,IAAIV,KAAK,GAAGS,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC,EAAE;UAC3BmB,SAAS,CAACK,OAAO,GAAGzB,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;QACrC,CAAC,MAAM,IAAIV,KAAK,GAAGS,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC,EAAE;UAClCmB,SAAS,CAACK,OAAO,GAAGzB,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;QACrC;MACF;IACF;IAEA,OAAO;MACL6B,OAAO,EAAEZ,KAAK;MACdM,OAAO,EAAPA,OAAO;MACPf,QAAQ,EAARA,QAAQ;MACRI,QAAQ,GAAAF,gBAAA,GAAEX,MAAM,CAACa,QAAQ,YAAAF,gBAAA,GAAI,CAAC;MAC9BiB,eAAe,EAAE,CAAC;MAClBH,OAAO,EAAE,CAAC;MACVC,aAAa,EAAE,CAAC;MAChBC,cAAc,EAAE,CAAC;MACjBE,YAAY,EAAE,IAAAE,iCAA2B,EAAC/B,MAAM,CAAC6B,YAAY;IAC/D,CAAC;EACH,CAAC,CAAC;AACJ,CAA6B", "ignoreList": []}