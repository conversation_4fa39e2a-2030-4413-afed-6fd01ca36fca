6d15d32d8204439c612e62c5977d53aa
'use strict';

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.PerformanceMonitor = PerformanceMonitor;
var _react = _interopRequireWildcard(require("react"));
var _reactNative = require("react-native");
var _index = require("../hook/index.js");
var _index2 = require("../createAnimatedComponent/index.js");
var _ConfigHelper = require("../ConfigHelper.js");
function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
function createCircularDoublesBuffer(size) {
  'worklet';

  return {
    next: 0,
    buffer: new Float32Array(size),
    size: size,
    count: 0,
    push: function push(value) {
      var oldValue = this.buffer[this.next];
      var oldCount = this.count;
      this.buffer[this.next] = value;
      this.next = (this.next + 1) % this.size;
      this.count = Math.min(this.size, this.count + 1);
      return oldCount === this.size ? oldValue : null;
    },
    front: function front() {
      var notEmpty = this.count > 0;
      if (notEmpty) {
        var current = this.next - 1;
        var index = current < 0 ? this.size - 1 : current;
        return this.buffer[index];
      }
      return null;
    },
    back: function back() {
      var notEmpty = this.count > 0;
      return notEmpty ? this.buffer[this.next] : null;
    }
  };
}
var DEFAULT_BUFFER_SIZE = 20;
(0, _ConfigHelper.addWhitelistedNativeProps)({
  text: true
});
var AnimatedTextInput = (0, _index2.createAnimatedComponent)(_reactNative.TextInput);
function loopAnimationFrame(fn) {
  var lastTime = 0;
  function loop() {
    requestAnimationFrame(function (time) {
      if (lastTime > 0) {
        fn(lastTime, time);
      }
      lastTime = time;
      requestAnimationFrame(loop);
    });
  }
  loop();
}
function getFps(renderTimeInMs) {
  'worklet';

  return 1000 / renderTimeInMs;
}
function completeBufferRoutine(buffer, timestamp) {
  'worklet';

  var _buffer$push;
  timestamp = Math.round(timestamp);
  var droppedTimestamp = (_buffer$push = buffer.push(timestamp)) != null ? _buffer$push : timestamp;
  var measuredRangeDuration = timestamp - droppedTimestamp;
  return getFps(measuredRangeDuration / buffer.count);
}
function JsPerformance(_ref) {
  var smoothingFrames = _ref.smoothingFrames;
  var jsFps = (0, _index.useSharedValue)(null);
  var totalRenderTime = (0, _index.useSharedValue)(0);
  var circularBuffer = (0, _react.useRef)(createCircularDoublesBuffer(smoothingFrames));
  (0, _react.useEffect)(function () {
    loopAnimationFrame(function (_, timestamp) {
      timestamp = Math.round(timestamp);
      var currentFps = completeBufferRoutine(circularBuffer.current, timestamp);
      jsFps.value = (currentFps * 2).toFixed(0);
    });
  }, [jsFps, totalRenderTime]);
  var animatedProps = (0, _index.useAnimatedProps)(function () {
    var _jsFps$value;
    var text = 'JS: ' + ((_jsFps$value = jsFps.value) != null ? _jsFps$value : 'N/A') + ' ';
    return {
      text: text,
      defaultValue: text
    };
  });
  return _react.default.createElement(_reactNative.View, {
    style: styles.container
  }, _react.default.createElement(AnimatedTextInput, {
    style: styles.text,
    animatedProps: animatedProps,
    editable: false
  }));
}
function UiPerformance(_ref2) {
  var smoothingFrames = _ref2.smoothingFrames;
  var uiFps = (0, _index.useSharedValue)(null);
  var circularBuffer = (0, _index.useSharedValue)(null);
  (0, _index.useFrameCallback)(function (_ref3) {
    var timestamp = _ref3.timestamp;
    if (circularBuffer.value === null) {
      circularBuffer.value = createCircularDoublesBuffer(smoothingFrames);
    }
    timestamp = Math.round(timestamp);
    var currentFps = completeBufferRoutine(circularBuffer.value, timestamp);
    uiFps.value = currentFps.toFixed(0);
  });
  var animatedProps = (0, _index.useAnimatedProps)(function () {
    var _uiFps$value;
    var text = 'UI: ' + ((_uiFps$value = uiFps.value) != null ? _uiFps$value : 'N/A') + ' ';
    return {
      text: text,
      defaultValue: text
    };
  });
  return _react.default.createElement(_reactNative.View, {
    style: styles.container
  }, _react.default.createElement(AnimatedTextInput, {
    style: styles.text,
    animatedProps: animatedProps,
    editable: false
  }));
}
function PerformanceMonitor(_ref4) {
  var _ref4$smoothingFrames = _ref4.smoothingFrames,
    smoothingFrames = _ref4$smoothingFrames === void 0 ? DEFAULT_BUFFER_SIZE : _ref4$smoothingFrames;
  return _react.default.createElement(_reactNative.View, {
    style: styles.monitor
  }, _react.default.createElement(JsPerformance, {
    smoothingFrames: smoothingFrames
  }), _react.default.createElement(UiPerformance, {
    smoothingFrames: smoothingFrames
  }));
}
var styles = _reactNative.StyleSheet.create({
  monitor: {
    flexDirection: 'row',
    position: 'absolute',
    backgroundColor: '#0006',
    zIndex: 1000
  },
  header: {
    fontSize: 14,
    color: '#ffff',
    paddingHorizontal: 5
  },
  text: {
    fontSize: 13,
    color: '#ffff',
    fontFamily: 'monospace',
    paddingHorizontal: 3
  },
  container: {
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row',
    flexWrap: 'wrap'
  }
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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