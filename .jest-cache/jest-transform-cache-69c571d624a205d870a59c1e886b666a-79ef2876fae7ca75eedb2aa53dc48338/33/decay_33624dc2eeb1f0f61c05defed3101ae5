e35d76497bfecc98d1873ded102ce400
'use strict';

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.withDecay = void 0;
var _util = require("../util.js");
var _rubberBandDecay = require("./rubberBandDecay.js");
var _utils = require("./utils.js");
var _rigidDecay = require("./rigidDecay.js");
var _errors = require("../../errors.js");
function validateConfig(config) {
  'worklet';

  if (config.clamp) {
    if (!Array.isArray(config.clamp)) {
      throw new _errors.ReanimatedError(`\`config.clamp\` must be an array but is ${typeof config.clamp}.`);
    }
    if (config.clamp.length !== 2) {
      throw new _errors.ReanimatedError(`\`clamp array\` must contain 2 items but is given ${config.clamp.length}.`);
    }
  }
  if (config.velocityFactor <= 0) {
    throw new _errors.ReanimatedError(`\`config.velocityFactor\` must be greater then 0 but is ${config.velocityFactor}.`);
  }
  if (config.rubberBandEffect && !config.clamp) {
    throw new _errors.ReanimatedError('You need to set `clamp` property when using `rubberBandEffect`.');
  }
}
var withDecay = exports.withDecay = function withDecay(userConfig, callback) {
  'worklet';

  return (0, _util.defineAnimation)(0, function () {
    'worklet';

    var _config$velocity;
    var config = {
      deceleration: 0.998,
      velocityFactor: 1,
      velocity: 0,
      rubberBandFactor: 0.6
    };
    if (userConfig) {
      Object.keys(userConfig).forEach(function (key) {
        return config[key] = userConfig[key];
      });
    }
    var decay = (0, _utils.isValidRubberBandConfig)(config) ? function (animation, now) {
      return (0, _rubberBandDecay.rubberBandDecay)(animation, now, config);
    } : function (animation, now) {
      return (0, _rigidDecay.rigidDecay)(animation, now, config);
    };
    function onStart(animation, value, now) {
      animation.current = value;
      animation.lastTimestamp = now;
      animation.startTimestamp = now;
      animation.initialVelocity = config.velocity;
      validateConfig(config);
      if (animation.reduceMotion && config.clamp) {
        if (value < config.clamp[0]) {
          animation.current = config.clamp[0];
        } else if (value > config.clamp[1]) {
          animation.current = config.clamp[1];
        }
      }
    }
    return {
      onFrame: decay,
      onStart: onStart,
      callback: callback,
      velocity: (_config$velocity = config.velocity) != null ? _config$velocity : 0,
      initialVelocity: 0,
      current: 0,
      lastTimestamp: 0,
      startTimestamp: 0,
      reduceMotion: (0, _util.getReduceMotionForAnimation)(config.reduceMotion)
    };
  });
};
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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