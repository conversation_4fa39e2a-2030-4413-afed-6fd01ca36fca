7e52000a767ff46fe5e0edbd8d936ede
'use strict';

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.useAnimatedKeyboard = useAnimatedKeyboard;
var _react = require("react");
var _core = require("../core.js");
var _commonTypes = require("../commonTypes.js");
function useAnimatedKeyboard() {
  var options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {
    isStatusBarTranslucentAndroid: false,
    isNavigationBarTranslucentAndroid: false
  };
  var ref = (0, _react.useRef)(null);
  var listenerId = (0, _react.useRef)(-1);
  var isSubscribed = (0, _react.useRef)(false);
  if (ref.current === null) {
    var keyboardEventData = {
      state: (0, _core.makeMutable)(_commonTypes.KeyboardState.UNKNOWN),
      height: (0, _core.makeMutable)(0)
    };
    listenerId.current = (0, _core.subscribeForKeyboardEvents)(function (state, height) {
      'worklet';

      keyboardEventData.state.value = state;
      keyboardEventData.height.value = height;
    }, options);
    ref.current = keyboardEventData;
    isSubscribed.current = true;
  }
  (0, _react.useEffect)(function () {
    if (isSubscribed.current === false && ref.current !== null) {
      var _keyboardEventData = ref.current;
      listenerId.current = (0, _core.subscribeForKeyboardEvents)(function (state, height) {
        'worklet';

        _keyboardEventData.state.value = state;
        _keyboardEventData.height.value = height;
      }, options);
      isSubscribed.current = true;
    }
    return function () {
      (0, _core.unsubscribeFromKeyboardEvents)(listenerId.current);
      isSubscribed.current = false;
    };
  }, []);
  return ref.current;
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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