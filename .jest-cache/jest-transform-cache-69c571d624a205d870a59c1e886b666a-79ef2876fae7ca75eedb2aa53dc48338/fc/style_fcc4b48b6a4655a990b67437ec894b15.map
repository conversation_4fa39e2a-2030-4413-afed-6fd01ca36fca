{"version": 3, "names": ["cov_5m42bmd96", "actualCoverage", "s", "msb_shared_component_1", "require", "exports", "makeStyle", "createMSBStyleSheet", "_ref", "f", "ColorGlobal", "SizeGlobal", "ColorField", "ColorAlias", "ColorDataView", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Shadow", "Typography", "beneficiaryInfo", "alignItems", "flexDirection", "bottomSpace", "marginHorizontal", "SpacingSmall", "container", "flex", "bottomSheetContentContainer", "height", "loadingContainer", "justifyContent", "position", "backgroundColor", "opacity", "width", "contentContainer", "Object", "assign", "NeutralWhite", "borderRadius", "Radius3", "marginTop", "SpacingLarge", "padding", "center", "iconContact", "Size600", "logo", "Size800", "marginRight", "resizeMode", "marginTop20", "SpacingMedium", "scrollViewContentContainer", "paddingBottom", "Spacing3xSmall", "paddingHorizontal", "space", "SpacingXSmall", "suggestionList", "Size300", "left", "overflow", "right", "top", "getSize", "zIndex", "zheight"], "sources": ["/Users/<USER>/projects/react-native/payment-module/src/presentation/payment-bill/style.ts"], "sourcesContent": ["import {createMSBStyleSheet, getSize} from 'msb-shared-component';\nimport DimensionUtils from '../../utils/DimensionUtils';\n\nexport const makeStyle = createMSBStyleSheet(\n  ({ColorGlobal, SizeGlobal, ColorField, ColorAlias, ColorDataView, SizeAlias, Shadow, Typography}) => {\n    return {\n      beneficiaryInfo: {\n        alignItems: 'center',\n        flexDirection: 'row',\n        // height: 32,\n      },\n      bottomSpace: {\n        // marginBottom: DimensionUtils.getPaddingBottomByDevice(),\n        marginHorizontal: SizeAlias.SpacingSmall,\n      },\n      container: {\n        flex: 1,\n      },\n      bottomSheetContentContainer: {flexDirection: 'column', flex: 1, height: 400},\n      loadingContainer: {\n        justifyContent: 'center',\n        alignItems: 'center',\n        position: 'absolute',\n        backgroundColor: 'black',\n        opacity: 0.5,\n        width: '100%',\n        height: '100%',\n      },\n      contentContainer: {\n        backgroundColor: ColorGlobal.NeutralWhite,\n        borderRadius: SizeAlias.Radius3,\n        marginTop: SizeAlias.SpacingLarge,\n        padding: SizeAlias.SpacingSmall,\n        ...Shadow.center,\n      },\n      iconContact: {\n        height: SizeGlobal.Size600,\n        width: SizeGlobal.Size600,\n      },\n\n      logo: {\n        height: SizeGlobal.Size800,\n        marginRight: SizeAlias.SpacingSmall,\n        resizeMode: 'stretch',\n        width: SizeGlobal.Size800,\n      },\n      marginTop20: {\n        marginTop: SizeAlias.SpacingMedium,\n      },\n      scrollViewContentContainer: {\n        paddingBottom: SizeAlias.Spacing3xSmall,\n        paddingHorizontal: SizeAlias.SpacingSmall,\n      },\n      space: {\n        height: SizeAlias.SpacingXSmall,\n      },\n      suggestionList: {\n        backgroundColor: ColorGlobal.NeutralWhite,\n        borderRadius: SizeGlobal.Size300,\n        left: 0,\n        overflow: 'visible',\n        position: 'absolute',\n        right: 0,\n        top: getSize(94),\n        zIndex: 999,\n        ...Shadow.center,\n      },\n\n      zheight: {\n        zIndex: 999,\n      },\n    };\n  },\n);\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAIuF;IAAAA,aAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,aAAA;AAAAA,aAAA,GAAAE,CAAA;;;;;;;AAJvF,IAAAC,sBAAA;AAAA;AAAA,CAAAH,aAAA,GAAAE,CAAA,OAAAE,OAAA;AAAA;AAAAJ,aAAA,GAAAE,CAAA;AAGaG,OAAA,CAAAC,SAAS,GAAG,IAAAH,sBAAA,CAAAI,mBAAmB,EAC1C,UAAAC,IAAA,EAAoG;EAAA;EAAAR,aAAA,GAAAS,CAAA;EAAA,IAAlGC,WAAW;IAAA;IAAA,CAAAV,aAAA,GAAAE,CAAA,OAAAM,IAAA,CAAXE,WAAW;IAAEC,UAAU;IAAA;IAAA,CAAAX,aAAA,GAAAE,CAAA,OAAAM,IAAA,CAAVG,UAAU;IAAEC,UAAU;IAAA;IAAA,CAAAZ,aAAA,GAAAE,CAAA,OAAAM,IAAA,CAAVI,UAAU;IAAEC,UAAU;IAAA;IAAA,CAAAb,aAAA,GAAAE,CAAA,OAAAM,IAAA,CAAVK,UAAU;IAAEC,aAAa;IAAA;IAAA,CAAAd,aAAA,GAAAE,CAAA,OAAAM,IAAA,CAAbM,aAAa;IAAEC,SAAS;IAAA;IAAA,CAAAf,aAAA,GAAAE,CAAA,OAAAM,IAAA,CAATO,SAAS;IAAEC,MAAM;IAAA;IAAA,CAAAhB,aAAA,GAAAE,CAAA,QAAAM,IAAA,CAANQ,MAAM;IAAEC,UAAU;IAAA;IAAA,CAAAjB,aAAA,GAAAE,CAAA,QAAAM,IAAA,CAAVS,UAAU;EAAA;EAAAjB,aAAA,GAAAE,CAAA;EAC7F,OAAO;IACLgB,eAAe,EAAE;MACfC,UAAU,EAAE,QAAQ;MACpBC,aAAa,EAAE;KAEhB;IACDC,WAAW,EAAE;MAEXC,gBAAgB,EAAEP,SAAS,CAACQ;KAC7B;IACDC,SAAS,EAAE;MACTC,IAAI,EAAE;KACP;IACDC,2BAA2B,EAAE;MAACN,aAAa,EAAE,QAAQ;MAAEK,IAAI,EAAE,CAAC;MAAEE,MAAM,EAAE;IAAG,CAAC;IAC5EC,gBAAgB,EAAE;MAChBC,cAAc,EAAE,QAAQ;MACxBV,UAAU,EAAE,QAAQ;MACpBW,QAAQ,EAAE,UAAU;MACpBC,eAAe,EAAE,OAAO;MACxBC,OAAO,EAAE,GAAG;MACZC,KAAK,EAAE,MAAM;MACbN,MAAM,EAAE;KACT;IACDO,gBAAgB,EAAAC,MAAA,CAAAC,MAAA;MACdL,eAAe,EAAErB,WAAW,CAAC2B,YAAY;MACzCC,YAAY,EAAEvB,SAAS,CAACwB,OAAO;MAC/BC,SAAS,EAAEzB,SAAS,CAAC0B,YAAY;MACjCC,OAAO,EAAE3B,SAAS,CAACQ;IAAY,GAC5BP,MAAM,CAAC2B,MAAM,CACjB;IACDC,WAAW,EAAE;MACXjB,MAAM,EAAEhB,UAAU,CAACkC,OAAO;MAC1BZ,KAAK,EAAEtB,UAAU,CAACkC;KACnB;IAEDC,IAAI,EAAE;MACJnB,MAAM,EAAEhB,UAAU,CAACoC,OAAO;MAC1BC,WAAW,EAAEjC,SAAS,CAACQ,YAAY;MACnC0B,UAAU,EAAE,SAAS;MACrBhB,KAAK,EAAEtB,UAAU,CAACoC;KACnB;IACDG,WAAW,EAAE;MACXV,SAAS,EAAEzB,SAAS,CAACoC;KACtB;IACDC,0BAA0B,EAAE;MAC1BC,aAAa,EAAEtC,SAAS,CAACuC,cAAc;MACvCC,iBAAiB,EAAExC,SAAS,CAACQ;KAC9B;IACDiC,KAAK,EAAE;MACL7B,MAAM,EAAEZ,SAAS,CAAC0C;KACnB;IACDC,cAAc,EAAAvB,MAAA,CAAAC,MAAA;MACZL,eAAe,EAAErB,WAAW,CAAC2B,YAAY;MACzCC,YAAY,EAAE3B,UAAU,CAACgD,OAAO;MAChCC,IAAI,EAAE,CAAC;MACPC,QAAQ,EAAE,SAAS;MACnB/B,QAAQ,EAAE,UAAU;MACpBgC,KAAK,EAAE,CAAC;MACRC,GAAG,EAAE,IAAA5D,sBAAA,CAAA6D,OAAO,EAAC,EAAE,CAAC;MAChBC,MAAM,EAAE;IAAG,GACRjD,MAAM,CAAC2B,MAAM,CACjB;IAEDuB,OAAO,EAAE;MACPD,MAAM,EAAE;;GAEX;AACH,CAAC,CACF", "ignoreList": []}