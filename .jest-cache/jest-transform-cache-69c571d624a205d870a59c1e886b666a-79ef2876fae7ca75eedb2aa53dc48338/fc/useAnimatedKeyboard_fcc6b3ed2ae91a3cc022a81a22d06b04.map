{"version": 3, "names": ["Object", "defineProperty", "exports", "value", "useAnimatedKeyboard", "_react", "require", "_core", "_commonTypes", "options", "arguments", "length", "undefined", "isStatusBarTranslucentAndroid", "isNavigationBarTranslucentAndroid", "ref", "useRef", "listenerId", "isSubscribed", "current", "keyboardEventData", "state", "makeMutable", "KeyboardState", "UNKNOWN", "height", "subscribeForKeyboardEvents", "useEffect", "unsubscribeFromKeyboardEvents"], "sources": ["../../../src/hook/useAnimatedKeyboard.ts"], "sourcesContent": [null], "mappings": "AAAA,YAAY;;AAAAA,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,KAAA;AAAA;AAAAD,OAAA,CAAAE,mBAAA,GAAAA,mBAAA;AACZ,IAAAC,MAAA,GAAAC,OAAA;AACA,IAAAC,KAAA,GAAAD,OAAA;AASA,IAAAE,YAAA,GAAAF,OAAA;AAUO,SAASF,mBAAmBA,CAAA,EAKX;EAAA,IAJtBK,OAAgC,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG;IACjCG,6BAA6B,EAAE,KAAK;IACpCC,iCAAiC,EAAE;EACrC,CAAC;EAED,IAAMC,GAAG,GAAG,IAAAC,aAAM,EAA8B,IAAI,CAAC;EACrD,IAAMC,UAAU,GAAG,IAAAD,aAAM,EAAS,CAAC,CAAC,CAAC;EACrC,IAAME,YAAY,GAAG,IAAAF,aAAM,EAAU,KAAK,CAAC;EAE3C,IAAID,GAAG,CAACI,OAAO,KAAK,IAAI,EAAE;IACxB,IAAMC,iBAAuC,GAAG;MAC9CC,KAAK,EAAE,IAAAC,iBAAW,EAAgBC,0BAAa,CAACC,OAAO,CAAC;MACxDC,MAAM,EAAE,IAAAH,iBAAW,EAAC,CAAC;IACvB,CAAC;IACDL,UAAU,CAACE,OAAO,GAAG,IAAAO,gCAA0B,EAAC,UAACL,KAAK,EAAEI,MAAM,EAAK;MACjE,SAAS;;MACTL,iBAAiB,CAACC,KAAK,CAAClB,KAAK,GAAGkB,KAAK;MACrCD,iBAAiB,CAACK,MAAM,CAACtB,KAAK,GAAGsB,MAAM;IACzC,CAAC,EAAEhB,OAAO,CAAC;IACXM,GAAG,CAACI,OAAO,GAAGC,iBAAiB;IAC/BF,YAAY,CAACC,OAAO,GAAG,IAAI;EAC7B;EACA,IAAAQ,gBAAS,EAAC,YAAM;IACd,IAAIT,YAAY,CAACC,OAAO,KAAK,KAAK,IAAIJ,GAAG,CAACI,OAAO,KAAK,IAAI,EAAE;MAC1D,IAAMC,kBAAiB,GAAGL,GAAG,CAACI,OAAO;MAErCF,UAAU,CAACE,OAAO,GAAG,IAAAO,gCAA0B,EAAC,UAACL,KAAK,EAAEI,MAAM,EAAK;QACjE,SAAS;;QACTL,kBAAiB,CAACC,KAAK,CAAClB,KAAK,GAAGkB,KAAK;QACrCD,kBAAiB,CAACK,MAAM,CAACtB,KAAK,GAAGsB,MAAM;MACzC,CAAC,EAAEhB,OAAO,CAAC;MACXS,YAAY,CAACC,OAAO,GAAG,IAAI;IAC7B;IACA,OAAO,YAAM;MACX,IAAAS,mCAA6B,EAACX,UAAU,CAACE,OAAO,CAAC;MACjDD,YAAY,CAACC,OAAO,GAAG,KAAK;IAC9B,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EACN,OAAOJ,GAAG,CAACI,OAAO;AACpB", "ignoreList": []}