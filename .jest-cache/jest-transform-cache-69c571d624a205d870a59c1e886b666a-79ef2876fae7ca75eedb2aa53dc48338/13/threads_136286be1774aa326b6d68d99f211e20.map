{"version": 3, "names": ["_interopRequireDefault", "require", "Object", "defineProperty", "exports", "value", "callMicrotasks", "executeOnUIRuntimeSync", "runOnJS", "runOnUI", "runOnUIImmediately", "setupMicrotasks", "_toConsumableArray2", "_slicedToArray2", "_NativeReanimated", "_PlatformChecker", "_shareables", "_commonTypes", "_errors", "IS_JEST", "isJest", "SHOULD_BE_USE_WEB", "shouldBeUseWeb", "_runOnUIQueue", "microtasksQueue", "isExecutingMicrotasksQueue", "global", "queueMicrotask", "callback", "push", "__callMicrotasks", "index", "length", "_maybeFlushUIUpdatesQueue", "callMicrotasksOnUIThread", "worklet", "__DEV__", "_WORKLET", "ReanimatedError", "isWorkletFunction", "_len", "arguments", "args", "Array", "_key", "NativeReanimatedModule", "scheduleOnUI", "makeShareableCloneRecursive", "apply", "queue", "for<PERSON>ach", "_ref", "_ref2", "default", "_len2", "_key2", "result", "makeShareableCloneOnUIRecursive", "_len3", "_key3", "runWorkletOnJS", "_len4", "_key4", "fun", "_len5", "_key5", "_len6", "_key6", "concat", "__remoteFunction", "scheduleOnJS", "_scheduleHostFunctionOnJS", "_scheduleRemoteFunctionOnJS", "_len7", "_key7", "undefined"], "sources": ["../../src/threads.ts"], "sourcesContent": [null], "mappings": "AAAA,YAAY;;AAAA,IAAAA,sBAAA,GAAAC,OAAA;AAAAC,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,KAAA;AAAA;AAAAD,OAAA,CAAAE,cAAA;AAAAF,OAAA,CAAAG,sBAAA,GAAAA,sBAAA;AAAAH,OAAA,CAAAI,OAAA,GAAAA,OAAA;AAAAJ,OAAA,CAAAK,OAAA,GAAAA,OAAA;AAAAL,OAAA,CAAAM,kBAAA,GAAAA,kBAAA;AAAAN,OAAA,CAAAO,eAAA,GAAAA,eAAA;AAAA,IAAAC,mBAAA,GAAAZ,sBAAA,CAAAC,OAAA;AAAA,IAAAY,eAAA,GAAAb,sBAAA,CAAAC,OAAA;AACZ,IAAAa,iBAAA,GAAAd,sBAAA,CAAAC,OAAA;AACA,IAAAc,gBAAA,GAAAd,OAAA;AAEA,IAAAe,WAAA,GAAAf,OAAA;AAIA,IAAAgB,YAAA,GAAAhB,OAAA;AACA,IAAAiB,OAAA,GAAAjB,OAAA;AAEA,IAAMkB,OAAO,GAAG,IAAAC,uBAAM,EAAC,CAAC;AACxB,IAAMC,iBAAiB,GAAG,IAAAC,+BAAc,EAAC,CAAC;AAG1C,IAAIC,aAAsE,GAAG,EAAE;AAExE,SAASZ,eAAeA,CAAA,EAAG;EAChC,SAAS;;EAET,IAAIa,eAAkC,GAAG,EAAE;EAC3C,IAAIC,0BAA0B,GAAG,KAAK;EACtCC,MAAM,CAACC,cAAc,GAAI,UAAAC,QAAoB,EAAK;IAChDJ,eAAe,CAACK,IAAI,CAACD,QAAQ,CAAC;EAChC,CAAC;EAEDF,MAAM,CAACI,gBAAgB,GAAG,YAAM;IAC9B,IAAIL,0BAA0B,EAAE;MAC9B;IACF;IACA,IAAI;MACFA,0BAA0B,GAAG,IAAI;MACjC,KAAK,IAAIM,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGP,eAAe,CAACQ,MAAM,EAAED,KAAK,IAAI,CAAC,EAAE;QAE9DP,eAAe,CAACO,KAAK,CAAC,CAAC,CAAC;MAC1B;MACAP,eAAe,GAAG,EAAE;MACpBE,MAAM,CAACO,yBAAyB,CAAC,CAAC;IACpC,CAAC,SAAS;MACRR,0BAA0B,GAAG,KAAK;IACpC;EACF,CAAC;AACH;AAEA,SAASS,wBAAwBA,CAAA,EAAG;EAClC,SAAS;;EACTR,MAAM,CAACI,gBAAgB,CAAC,CAAC;AAC3B;AAEO,IAAMxB,cAAc,GAAAF,OAAA,CAAAE,cAAA,GAAGe,iBAAiB,GAC3C,YAAM,CACJ,CACD,GACDa,wBAAwB;AA2BrB,SAASzB,OAAOA,CACrB0B,OAA2C,EAClB;EACzB,SAAS;;EACT,IAAIC,OAAO,IAAI,CAACf,iBAAiB,IAAIgB,QAAQ,EAAE;IAC7C,MAAM,IAAIC,uBAAe,CACvB,kJACF,CAAC;EACH;EACA,IAAIF,OAAO,IAAI,CAACf,iBAAiB,IAAI,CAAC,IAAAkB,8BAAiB,EAACJ,OAAO,CAAC,EAAE;IAChE,MAAM,IAAIG,uBAAe,CAAC,2CAA2C,CAAC;EACxE;EACA,OAAO,YAAa;IAAA,SAAAE,IAAA,GAAAC,SAAA,CAAAT,MAAA,EAATU,IAAI,OAAAC,KAAA,CAAAH,IAAA,GAAAI,IAAA,MAAAA,IAAA,GAAAJ,IAAA,EAAAI,IAAA;MAAJF,IAAI,CAAAE,IAAA,IAAAH,SAAA,CAAAG,IAAA;IAAA;IACb,IAAIzB,OAAO,EAAE;MAUX0B,yBAAsB,CAACC,YAAY,CACjC,IAAAC,uCAA2B,EAAC,YAAM;QAChC,SAAS;;QACTZ,OAAO,CAAAa,KAAA,SAAIN,IAAI,CAAC;MAClB,CAAC,CACH,CAAC;MACD;IACF;IACA,IAAIN,OAAO,EAAE;MAMX,IAAAW,uCAA2B,EAACZ,OAAO,CAAC;MACpC,IAAAY,uCAA2B,EAACL,IAAI,CAAC;IACnC;IACAnB,aAAa,CAACM,IAAI,CAAC,CAACM,OAAO,EAAqBO,IAAI,CAAC,CAAC;IACtD,IAAInB,aAAa,CAACS,MAAM,KAAK,CAAC,EAAE;MAC9BL,cAAc,CAAC,YAAM;QACnB,IAAMsB,KAAK,GAAG1B,aAAa;QAC3BA,aAAa,GAAG,EAAE;QAClBsB,yBAAsB,CAACC,YAAY,CACjC,IAAAC,uCAA2B,EAAC,YAAM;UAChC,SAAS;UAETE,KAAK,CAACC,OAAO,CAAC,UAAAC,IAAA,EAAqB;YAAA,IAAAC,KAAA,OAAAvC,eAAA,CAAAwC,OAAA,EAAAF,IAAA;cAAnBhB,OAAO,GAAAiB,KAAA;cAAEV,IAAI,GAAAU,KAAA;YAC3BjB,OAAO,CAAAa,KAAA,aAAApC,mBAAA,CAAAyC,OAAA,EAAIX,IAAI,EAAC;UAClB,CAAC,CAAC;UACFpC,cAAc,CAAC,CAAC;QAClB,CAAC,CACH,CAAC;MACH,CAAC,CAAC;IACJ;EACF,CAAC;AACH;AAOO,SAASC,sBAAsBA,CACpC4B,OAA2C,EACX;EAChC,OAAO,YAAa;IAAA,SAAAmB,KAAA,GAAAb,SAAA,CAAAT,MAAA,EAATU,IAAI,OAAAC,KAAA,CAAAW,KAAA,GAAAC,KAAA,MAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA;MAAJb,IAAI,CAAAa,KAAA,IAAAd,SAAA,CAAAc,KAAA;IAAA;IACb,OAAOV,yBAAsB,CAACtC,sBAAsB,CAClD,IAAAwC,uCAA2B,EAAC,YAAM;MAChC,SAAS;;MACT,IAAMS,MAAM,GAAGrB,OAAO,CAAAa,KAAA,SAAIN,IAAI,CAAC;MAC/B,OAAO,IAAAe,2CAA+B,EAACD,MAAM,CAAC;IAChD,CAAC,CACH,CAAC;EACH,CAAC;AACH;AAOO,SAAS9C,kBAAkBA,CAChCyB,OAA2C,EAClB;EACzB,SAAS;;EACT,IAAIC,OAAO,IAAI,CAACf,iBAAiB,IAAIgB,QAAQ,EAAE;IAC7C,MAAM,IAAIC,uBAAe,CACvB,6JACF,CAAC;EACH;EACA,IAAIF,OAAO,IAAI,CAACf,iBAAiB,IAAI,CAAC,IAAAkB,8BAAiB,EAACJ,OAAO,CAAC,EAAE;IAChE,MAAM,IAAIG,uBAAe,CACvB,sDACF,CAAC;EACH;EACA,OAAO,YAAa;IAAA,SAAAoB,KAAA,GAAAjB,SAAA,CAAAT,MAAA,EAATU,IAAI,OAAAC,KAAA,CAAAe,KAAA,GAAAC,KAAA,MAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA;MAAJjB,IAAI,CAAAiB,KAAA,IAAAlB,SAAA,CAAAkB,KAAA;IAAA;IACbd,yBAAsB,CAACC,YAAY,CACjC,IAAAC,uCAA2B,EAAC,YAAM;MAChC,SAAS;;MACTZ,OAAO,CAAAa,KAAA,SAAIN,IAAI,CAAC;IAClB,CAAC,CACH,CAAC;EACH,CAAC;AACH;AAcA,SAASkB,cAAcA,CACrBzB,OAA2C,EAErC;EAAA,SAAA0B,KAAA,GAAApB,SAAA,CAAAT,MAAA,EADHU,IAAU,OAAAC,KAAA,CAAAkB,KAAA,OAAAA,KAAA,WAAAC,KAAA,MAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA;IAAVpB,IAAU,CAAAoB,KAAA,QAAArB,SAAA,CAAAqB,KAAA;EAAA;EAGb3B,OAAO,CAAAa,KAAA,SAAIN,IAAI,CAAC;AAClB;AAgBO,SAASlC,OAAOA,CACrBuD,GAGsC,EACb;EACzB,SAAS;;EAET,IAAI1C,iBAAiB,IAAI,CAACgB,QAAQ,EAAE;IAElC,OAAO;MAAA,SAAA2B,KAAA,GAAAvB,SAAA,CAAAT,MAAA,EAAIU,IAAI,OAAAC,KAAA,CAAAqB,KAAA,GAAAC,KAAA,MAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA;QAAJvB,IAAI,CAAAuB,KAAA,IAAAxB,SAAA,CAAAwB,KAAA;MAAA;MAAA,OACbtC,cAAc,CACZe,IAAI,CAACV,MAAM,GACP;QAAA,OAAO+B,GAAG,CAAAf,KAAA,SAAuCN,IAAI,CAAC;MAAA,IACrDqB,GACP,CAAC;IAAA;EACL;EACA,IAAI,IAAAxB,8BAAiB,EAAoBwB,GAAG,CAAC,EAAE;IAI7C,OAAO;MAAA,SAAAG,KAAA,GAAAzB,SAAA,CAAAT,MAAA,EAAIU,IAAI,OAAAC,KAAA,CAAAuB,KAAA,GAAAC,KAAA,MAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA;QAAJzB,IAAI,CAAAyB,KAAA,IAAA1B,SAAA,CAAA0B,KAAA;MAAA;MAAA,OACb3D,OAAO,CAACoD,cAAiC,CAAC,CAAAZ,KAAA,UACxCe,GAAG,EAAAK,MAAA,CACA1B,IACL,EAAC;IAAA;EACL;EACA,IAAKqB,GAAG,CAAkBM,gBAAgB,EAAE;IAK1CN,GAAG,GAAIA,GAAG,CAAkBM,gBAAgB;EAC9C;EAEA,IAAMC,YAAY,GAChB,OAAOP,GAAG,KAAK,UAAU,GACrBrC,MAAM,CAAC6C,yBAAyB,GAChC7C,MAAM,CAAC8C,2BAA2B;EAExC,OAAO,YAAa;IAAA,SAAAC,KAAA,GAAAhC,SAAA,CAAAT,MAAA,EAATU,IAAI,OAAAC,KAAA,CAAA8B,KAAA,GAAAC,KAAA,MAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA;MAAJhC,IAAI,CAAAgC,KAAA,IAAAjC,SAAA,CAAAiC,KAAA;IAAA;IACbJ,YAAY,CACVP,GAAG,EAGHrB,IAAI,CAACV,MAAM,GAAG,CAAC,GAEV,IAAAyB,2CAA+B,EAACf,IAAI,CAAC,GACtCiC,SACN,CAAC;EACH,CAAC;AACH", "ignoreList": []}