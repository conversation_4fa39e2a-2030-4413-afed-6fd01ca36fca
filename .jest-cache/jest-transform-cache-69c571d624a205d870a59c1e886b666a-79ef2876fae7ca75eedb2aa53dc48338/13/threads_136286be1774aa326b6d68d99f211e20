503b1ae991eadbc846b32c5dd7dd579d
'use strict';

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.callMicrotasks = void 0;
exports.executeOnUIRuntimeSync = executeOnUIRuntimeSync;
exports.runOnJS = runOnJS;
exports.runOnUI = runOnUI;
exports.runOnUIImmediately = runOnUIImmediately;
exports.setupMicrotasks = setupMicrotasks;
var _toConsumableArray2 = _interopRequireDefault(require("@babel/runtime/helpers/toConsumableArray"));
var _slicedToArray2 = _interopRequireDefault(require("@babel/runtime/helpers/slicedToArray"));
var _NativeReanimated = _interopRequireDefault(require("./NativeReanimated"));
var _PlatformChecker = require("./PlatformChecker.js");
var _shareables = require("./shareables.js");
var _commonTypes = require("./commonTypes.js");
var _errors = require("./errors.js");
var IS_JEST = (0, _PlatformChecker.isJest)();
var SHOULD_BE_USE_WEB = (0, _PlatformChecker.shouldBeUseWeb)();
var _runOnUIQueue = [];
function setupMicrotasks() {
  'worklet';

  var microtasksQueue = [];
  var isExecutingMicrotasksQueue = false;
  global.queueMicrotask = function (callback) {
    microtasksQueue.push(callback);
  };
  global.__callMicrotasks = function () {
    if (isExecutingMicrotasksQueue) {
      return;
    }
    try {
      isExecutingMicrotasksQueue = true;
      for (var index = 0; index < microtasksQueue.length; index += 1) {
        microtasksQueue[index]();
      }
      microtasksQueue = [];
      global._maybeFlushUIUpdatesQueue();
    } finally {
      isExecutingMicrotasksQueue = false;
    }
  };
}
function callMicrotasksOnUIThread() {
  'worklet';

  global.__callMicrotasks();
}
var callMicrotasks = exports.callMicrotasks = SHOULD_BE_USE_WEB ? function () {} : callMicrotasksOnUIThread;
function runOnUI(worklet) {
  'worklet';

  if (__DEV__ && !SHOULD_BE_USE_WEB && _WORKLET) {
    throw new _errors.ReanimatedError('`runOnUI` cannot be called on the UI runtime. Please call the function synchronously or use `queueMicrotask` or `requestAnimationFrame` instead.');
  }
  if (__DEV__ && !SHOULD_BE_USE_WEB && !(0, _commonTypes.isWorkletFunction)(worklet)) {
    throw new _errors.ReanimatedError('`runOnUI` can only be used with worklets.');
  }
  return function () {
    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {
      args[_key] = arguments[_key];
    }
    if (IS_JEST) {
      _NativeReanimated.default.scheduleOnUI((0, _shareables.makeShareableCloneRecursive)(function () {
        'worklet';

        worklet.apply(void 0, args);
      }));
      return;
    }
    if (__DEV__) {
      (0, _shareables.makeShareableCloneRecursive)(worklet);
      (0, _shareables.makeShareableCloneRecursive)(args);
    }
    _runOnUIQueue.push([worklet, args]);
    if (_runOnUIQueue.length === 1) {
      queueMicrotask(function () {
        var queue = _runOnUIQueue;
        _runOnUIQueue = [];
        _NativeReanimated.default.scheduleOnUI((0, _shareables.makeShareableCloneRecursive)(function () {
          'worklet';
          queue.forEach(function (_ref) {
            var _ref2 = (0, _slicedToArray2.default)(_ref, 2),
              worklet = _ref2[0],
              args = _ref2[1];
            worklet.apply(void 0, (0, _toConsumableArray2.default)(args));
          });
          callMicrotasks();
        }));
      });
    }
  };
}
function executeOnUIRuntimeSync(worklet) {
  return function () {
    for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {
      args[_key2] = arguments[_key2];
    }
    return _NativeReanimated.default.executeOnUIRuntimeSync((0, _shareables.makeShareableCloneRecursive)(function () {
      'worklet';

      var result = worklet.apply(void 0, args);
      return (0, _shareables.makeShareableCloneOnUIRecursive)(result);
    }));
  };
}
function runOnUIImmediately(worklet) {
  'worklet';

  if (__DEV__ && !SHOULD_BE_USE_WEB && _WORKLET) {
    throw new _errors.ReanimatedError('`runOnUIImmediately` cannot be called on the UI runtime. Please call the function synchronously or use `queueMicrotask` or `requestAnimationFrame` instead.');
  }
  if (__DEV__ && !SHOULD_BE_USE_WEB && !(0, _commonTypes.isWorkletFunction)(worklet)) {
    throw new _errors.ReanimatedError('`runOnUIImmediately` can only be used with worklets.');
  }
  return function () {
    for (var _len3 = arguments.length, args = new Array(_len3), _key3 = 0; _key3 < _len3; _key3++) {
      args[_key3] = arguments[_key3];
    }
    _NativeReanimated.default.scheduleOnUI((0, _shareables.makeShareableCloneRecursive)(function () {
      'worklet';

      worklet.apply(void 0, args);
    }));
  };
}
function runWorkletOnJS(worklet) {
  for (var _len4 = arguments.length, args = new Array(_len4 > 1 ? _len4 - 1 : 0), _key4 = 1; _key4 < _len4; _key4++) {
    args[_key4 - 1] = arguments[_key4];
  }
  worklet.apply(void 0, args);
}
function runOnJS(fun) {
  'worklet';

  if (SHOULD_BE_USE_WEB || !_WORKLET) {
    return function () {
      for (var _len5 = arguments.length, args = new Array(_len5), _key5 = 0; _key5 < _len5; _key5++) {
        args[_key5] = arguments[_key5];
      }
      return queueMicrotask(args.length ? function () {
        return fun.apply(void 0, args);
      } : fun);
    };
  }
  if ((0, _commonTypes.isWorkletFunction)(fun)) {
    return function () {
      for (var _len6 = arguments.length, args = new Array(_len6), _key6 = 0; _key6 < _len6; _key6++) {
        args[_key6] = arguments[_key6];
      }
      return runOnJS(runWorkletOnJS).apply(void 0, [fun].concat(args));
    };
  }
  if (fun.__remoteFunction) {
    fun = fun.__remoteFunction;
  }
  var scheduleOnJS = typeof fun === 'function' ? global._scheduleHostFunctionOnJS : global._scheduleRemoteFunctionOnJS;
  return function () {
    for (var _len7 = arguments.length, args = new Array(_len7), _key7 = 0; _key7 < _len7; _key7++) {
      args[_key7] = arguments[_key7];
    }
    scheduleOnJS(fun, args.length > 0 ? (0, _shareables.makeShareableCloneOnUIRecursive)(args) : undefined);
  };
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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