{"version": 3, "names": ["_interopRequireDefault", "require", "Object", "defineProperty", "exports", "value", "LightSpeedOutRight", "LightSpeedOutLeft", "LightSpeedInRight", "LightSpeedInLeft", "_slicedToArray2", "_classCallCheck2", "_createClass2", "_possibleConstructorReturn2", "_getPrototypeOf2", "_inherits2", "_index", "_index2", "_callSuper", "t", "o", "e", "default", "_isNativeReflectConstruct", "Reflect", "construct", "constructor", "apply", "Boolean", "prototype", "valueOf", "call", "_ComplexAnimationBuil", "_this", "_len", "arguments", "length", "args", "Array", "_key", "concat", "build", "delayFunction", "getDelayFunction", "_this$getAnimationAnd", "getAnimationAndConfig", "_this$getAnimationAnd2", "animation", "config", "delay", "get<PERSON>elay", "duration", "getDuration", "callback", "callbackV", "initialValues", "values", "animations", "opacity", "withTiming", "transform", "translateX", "assign", "skewX", "withSequence", "windowWidth", "key", "createInstance", "ComplexAnimationBuilder", "presetName", "_ComplexAnimationBuil2", "_this2", "_len2", "_key2", "_this2$getAnimationAn", "_this2$getAnimationAn2", "_ComplexAnimationBuil3", "_this3", "_len3", "_key3", "_this3$getAnimationAn", "_this3$getAnimationAn2", "_ComplexAnimationBuil4", "_this4", "_len4", "_key4", "_this4$getAnimationAn", "_this4$getAnimationAn2"], "sources": ["../../../../src/layoutReanimation/defaultAnimations/Lightspeed.ts"], "sourcesContent": [null], "mappings": "AAAA,YAAY;;AAAA,IAAAA,sBAAA,GAAAC,OAAA;AAAAC,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,KAAA;AAAA;AAAAD,OAAA,CAAAE,kBAAA,GAAAF,OAAA,CAAAG,iBAAA,GAAAH,OAAA,CAAAI,iBAAA,GAAAJ,OAAA,CAAAK,gBAAA;AAAA,IAAAC,eAAA,GAAAV,sBAAA,CAAAC,OAAA;AAAA,IAAAU,gBAAA,GAAAX,sBAAA,CAAAC,OAAA;AAAA,IAAAW,aAAA,GAAAZ,sBAAA,CAAAC,OAAA;AAAA,IAAAY,2BAAA,GAAAb,sBAAA,CAAAC,OAAA;AAAA,IAAAa,gBAAA,GAAAd,sBAAA,CAAAC,OAAA;AAAA,IAAAc,UAAA,GAAAf,sBAAA,CAAAC,OAAA;AACZ,IAAAe,MAAA,GAAAf,OAAA;AAEA,IAAAgB,OAAA,GAAAhB,OAAA;AAA6D,SAAAiB,WAAAC,CAAA,EAAAC,CAAA,EAAAC,CAAA,WAAAD,CAAA,OAAAN,gBAAA,CAAAQ,OAAA,EAAAF,CAAA,OAAAP,2BAAA,CAAAS,OAAA,EAAAH,CAAA,EAAAI,yBAAA,KAAAC,OAAA,CAAAC,SAAA,CAAAL,CAAA,EAAAC,CAAA,YAAAP,gBAAA,CAAAQ,OAAA,EAAAH,CAAA,EAAAO,WAAA,IAAAN,CAAA,CAAAO,KAAA,CAAAR,CAAA,EAAAE,CAAA;AAAA,SAAAE,0BAAA,cAAAJ,CAAA,IAAAS,OAAA,CAAAC,SAAA,CAAAC,OAAA,CAAAC,IAAA,CAAAP,OAAA,CAAAC,SAAA,CAAAG,OAAA,iCAAAT,CAAA,aAAAI,yBAAA,YAAAA,0BAAA,aAAAJ,CAAA;AAAA,IAgBhDX,iBAAiB,GAAAJ,OAAA,CAAAI,iBAAA,aAAAwB,qBAAA;EAAA,SAAAxB,kBAAA;IAAA,IAAAyB,KAAA;IAAA,IAAAtB,gBAAA,CAAAW,OAAA,QAAAd,iBAAA;IAAA,SAAA0B,IAAA,GAAAC,SAAA,CAAAC,MAAA,EAAAC,IAAA,OAAAC,KAAA,CAAAJ,IAAA,GAAAK,IAAA,MAAAA,IAAA,GAAAL,IAAA,EAAAK,IAAA;MAAAF,IAAA,CAAAE,IAAA,IAAAJ,SAAA,CAAAI,IAAA;IAAA;IAAAN,KAAA,GAAAf,UAAA,OAAAV,iBAAA,KAAAgC,MAAA,CAAAH,IAAA;IAAAJ,KAAA,CAY5BQ,KAAK,GAAG,YAAkC;MACxC,IAAMC,aAAa,GAAGT,KAAA,CAAKU,gBAAgB,CAAC,CAAC;MAC7C,IAAAC,qBAAA,GAA4BX,KAAA,CAAKY,qBAAqB,CAAC,CAAC;QAAAC,sBAAA,OAAApC,eAAA,CAAAY,OAAA,EAAAsB,qBAAA;QAAjDG,SAAS,GAAAD,sBAAA;QAAEE,MAAM,GAAAF,sBAAA;MACxB,IAAMG,KAAK,GAAGhB,KAAA,CAAKiB,QAAQ,CAAC,CAAC;MAC7B,IAAMC,QAAQ,GAAGlB,KAAA,CAAKmB,WAAW,CAAC,CAAC;MACnC,IAAMC,QAAQ,GAAGpB,KAAA,CAAKqB,SAAS;MAC/B,IAAMC,aAAa,GAAGtB,KAAA,CAAKsB,aAAa;MAExC,OAAQ,UAAAC,MAAiC,EAAK;QAC5C,SAAS;;QACT,OAAO;UACLC,UAAU,EAAE;YACVC,OAAO,EAAEhB,aAAa,CAACO,KAAK,EAAE,IAAAU,iBAAU,EAAC,CAAC,EAAE;cAAER,QAAA,EAAAA;YAAS,CAAC,CAAC,CAAC;YAC1DS,SAAS,EAAE,CACT;cACEC,UAAU,EAAEnB,aAAa,CACvBO,KAAK,EACLF,SAAS,CAAC,CAAC,EAAA7C,MAAA,CAAA4D,MAAA,KAAOd,MAAM;gBAAEG,QAAQ,EAAEA,QAAQ,GAAG;cAAA,EAAK,CACtD;YACF,CAAC,EACD;cACEY,KAAK,EAAErB,aAAa,CAClBO,KAAK,EACL,IAAAe,mBAAY,EACV,IAAAL,iBAAU,EAAC,OAAO,EAAE;gBAAER,QAAQ,EAAEA,QAAQ,GAAG;cAAI,CAAC,CAAC,EACjD,IAAAQ,iBAAU,EAAC,OAAO,EAAE;gBAAER,QAAQ,EAAEA,QAAQ,GAAG;cAAK,CAAC,CAAC,EAClD,IAAAQ,iBAAU,EAAC,MAAM,EAAE;gBAAER,QAAQ,EAAEA,QAAQ,GAAG;cAAK,CAAC,CAClD,CACF;YACF,CAAC;UAEL,CAAC;UACDI,aAAa,EAAArD,MAAA,CAAA4D,MAAA;YACXJ,OAAO,EAAE,CAAC;YACVE,SAAS,EAAE,CAAC;cAAEC,UAAU,EAAEL,MAAM,CAACS;YAAY,CAAC,EAAE;cAAEF,KAAK,EAAE;YAAS,CAAC;UAAC,GACjER,aAAA,CACJ;UACDF,QAAA,EAAAA;QACF,CAAC;MACH,CAAC;IACH,CAAC;IAAA,OAAApB,KAAA;EAAA;EAAA,IAAAlB,UAAA,CAAAO,OAAA,EAAAd,iBAAA,EAAAwB,qBAAA;EAAA,WAAApB,aAAA,CAAAU,OAAA,EAAAd,iBAAA;IAAA0D,GAAA;IAAA7D,KAAA,EA9CD,SAAO8D,cAAcA,CAAA,EAEF;MACjB,OAAO,IAAI3D,iBAAiB,CAAC,CAAC;IAChC;EAAA;AAAA,EATQ4D,+BAAuB;AADpB5D,iBAAiB,CAIrB6D,UAAU,GAAG,mBAAmB;AAAA,IA4D5B5D,gBAAgB,GAAAL,OAAA,CAAAK,gBAAA,aAAA6D,sBAAA;EAAA,SAAA7D,iBAAA;IAAA,IAAA8D,MAAA;IAAA,IAAA5D,gBAAA,CAAAW,OAAA,QAAAb,gBAAA;IAAA,SAAA+D,KAAA,GAAArC,SAAA,CAAAC,MAAA,EAAAC,IAAA,OAAAC,KAAA,CAAAkC,KAAA,GAAAC,KAAA,MAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA;MAAApC,IAAA,CAAAoC,KAAA,IAAAtC,SAAA,CAAAsC,KAAA;IAAA;IAAAF,MAAA,GAAArD,UAAA,OAAAT,gBAAA,KAAA+B,MAAA,CAAAH,IAAA;IAAAkC,MAAA,CAY3B9B,KAAK,GAAG,YAAkC;MACxC,IAAMC,aAAa,GAAG6B,MAAA,CAAK5B,gBAAgB,CAAC,CAAC;MAC7C,IAAA+B,qBAAA,GAA4BH,MAAA,CAAK1B,qBAAqB,CAAC,CAAC;QAAA8B,sBAAA,OAAAjE,eAAA,CAAAY,OAAA,EAAAoD,qBAAA;QAAjD3B,SAAS,GAAA4B,sBAAA;QAAE3B,MAAM,GAAA2B,sBAAA;MACxB,IAAM1B,KAAK,GAAGsB,MAAA,CAAKrB,QAAQ,CAAC,CAAC;MAC7B,IAAMC,QAAQ,GAAGoB,MAAA,CAAKnB,WAAW,CAAC,CAAC;MACnC,IAAMC,QAAQ,GAAGkB,MAAA,CAAKjB,SAAS;MAC/B,IAAMC,aAAa,GAAGgB,MAAA,CAAKhB,aAAa;MAExC,OAAQ,UAAAC,MAAiC,EAAK;QAC5C,SAAS;;QACT,OAAO;UACLC,UAAU,EAAE;YACVC,OAAO,EAAEhB,aAAa,CAACO,KAAK,EAAE,IAAAU,iBAAU,EAAC,CAAC,EAAE;cAAER,QAAA,EAAAA;YAAS,CAAC,CAAC,CAAC;YAC1DS,SAAS,EAAE,CACT;cACEC,UAAU,EAAEnB,aAAa,CACvBO,KAAK,EACLF,SAAS,CAAC,CAAC,EAAA7C,MAAA,CAAA4D,MAAA,KAAOd,MAAM;gBAAEG,QAAQ,EAAEA,QAAQ,GAAG;cAAA,EAAK,CACtD;YACF,CAAC,EACD;cACEY,KAAK,EAAErB,aAAa,CAClBO,KAAK,EACL,IAAAe,mBAAY,EACV,IAAAL,iBAAU,EAAC,QAAQ,EAAE;gBAAER,QAAQ,EAAEA,QAAQ,GAAG;cAAI,CAAC,CAAC,EAClD,IAAAQ,iBAAU,EAAC,MAAM,EAAE;gBAAER,QAAQ,EAAEA,QAAQ,GAAG;cAAK,CAAC,CAAC,EACjD,IAAAQ,iBAAU,EAAC,MAAM,EAAE;gBAAER,QAAQ,EAAEA,QAAQ,GAAG;cAAK,CAAC,CAClD,CACF;YACF,CAAC;UAEL,CAAC;UACDI,aAAa,EAAArD,MAAA,CAAA4D,MAAA;YACXJ,OAAO,EAAE,CAAC;YACVE,SAAS,EAAE,CAAC;cAAEC,UAAU,EAAE,CAACL,MAAM,CAACS;YAAY,CAAC,EAAE;cAAEF,KAAK,EAAE;YAAQ,CAAC;UAAC,GACjER,aAAA,CACJ;UACDF,QAAA,EAAAA;QACF,CAAC;MACH,CAAC;IACH,CAAC;IAAA,OAAAkB,MAAA;EAAA;EAAA,IAAAxD,UAAA,CAAAO,OAAA,EAAAb,gBAAA,EAAA6D,sBAAA;EAAA,WAAA1D,aAAA,CAAAU,OAAA,EAAAb,gBAAA;IAAAyD,GAAA;IAAA7D,KAAA,EA9CD,SAAO8D,cAAcA,CAAA,EAEF;MACjB,OAAO,IAAI1D,gBAAgB,CAAC,CAAC;IAC/B;EAAA;AAAA,EATQ2D,+BAAuB;AADpB3D,gBAAgB,CAIpB4D,UAAU,GAAG,kBAAkB;AAAA,IA4D3B/D,kBAAkB,GAAAF,OAAA,CAAAE,kBAAA,aAAAsE,sBAAA;EAAA,SAAAtE,mBAAA;IAAA,IAAAuE,MAAA;IAAA,IAAAlE,gBAAA,CAAAW,OAAA,QAAAhB,kBAAA;IAAA,SAAAwE,KAAA,GAAA3C,SAAA,CAAAC,MAAA,EAAAC,IAAA,OAAAC,KAAA,CAAAwC,KAAA,GAAAC,KAAA,MAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA;MAAA1C,IAAA,CAAA0C,KAAA,IAAA5C,SAAA,CAAA4C,KAAA;IAAA;IAAAF,MAAA,GAAA3D,UAAA,OAAAZ,kBAAA,KAAAkC,MAAA,CAAAH,IAAA;IAAAwC,MAAA,CAY7BpC,KAAK,GAAG,YAAkC;MACxC,IAAMC,aAAa,GAAGmC,MAAA,CAAKlC,gBAAgB,CAAC,CAAC;MAC7C,IAAAqC,qBAAA,GAA4BH,MAAA,CAAKhC,qBAAqB,CAAC,CAAC;QAAAoC,sBAAA,OAAAvE,eAAA,CAAAY,OAAA,EAAA0D,qBAAA;QAAjDjC,SAAS,GAAAkC,sBAAA;QAAEjC,MAAM,GAAAiC,sBAAA;MACxB,IAAMhC,KAAK,GAAG4B,MAAA,CAAK3B,QAAQ,CAAC,CAAC;MAC7B,IAAMG,QAAQ,GAAGwB,MAAA,CAAKvB,SAAS;MAC/B,IAAMC,aAAa,GAAGsB,MAAA,CAAKtB,aAAa;MAExC,OAAQ,UAAAC,MAAiC,EAAK;QAC5C,SAAS;;QACT,OAAO;UACLC,UAAU,EAAE;YACVC,OAAO,EAAEhB,aAAa,CAACO,KAAK,EAAEF,SAAS,CAAC,CAAC,EAAEC,MAAM,CAAC,CAAC;YACnDY,SAAS,EAAE,CACT;cACEC,UAAU,EAAEnB,aAAa,CACvBO,KAAK,EACLF,SAAS,CAACS,MAAM,CAACS,WAAW,EAAEjB,MAAM,CACtC;YACF,CAAC,EACD;cACEe,KAAK,EAAErB,aAAa,CAACO,KAAK,EAAEF,SAAS,CAAC,QAAQ,EAAEC,MAAM,CAAC;YACzD,CAAC;UAEL,CAAC;UACDO,aAAa,EAAArD,MAAA,CAAA4D,MAAA;YACXJ,OAAO,EAAE,CAAC;YACVE,SAAS,EAAE,CAAC;cAAEC,UAAU,EAAE;YAAE,CAAC,EAAE;cAAEE,KAAK,EAAE;YAAO,CAAC;UAAC,GAC9CR,aAAA,CACJ;UACDF,QAAA,EAAAA;QACF,CAAC;MACH,CAAC;IACH,CAAC;IAAA,OAAAwB,MAAA;EAAA;EAAA,IAAA9D,UAAA,CAAAO,OAAA,EAAAhB,kBAAA,EAAAsE,sBAAA;EAAA,WAAAhE,aAAA,CAAAU,OAAA,EAAAhB,kBAAA;IAAA4D,GAAA;IAAA7D,KAAA,EAtCD,SAAO8D,cAAcA,CAAA,EAEF;MACjB,OAAO,IAAI7D,kBAAkB,CAAC,CAAC;IACjC;EAAA;AAAA,EATQ8D,+BAAuB;AADpB9D,kBAAkB,CAItB+D,UAAU,GAAG,oBAAoB;AAAA,IAoD7B9D,iBAAiB,GAAAH,OAAA,CAAAG,iBAAA,aAAA2E,sBAAA;EAAA,SAAA3E,kBAAA;IAAA,IAAA4E,MAAA;IAAA,IAAAxE,gBAAA,CAAAW,OAAA,QAAAf,iBAAA;IAAA,SAAA6E,KAAA,GAAAjD,SAAA,CAAAC,MAAA,EAAAC,IAAA,OAAAC,KAAA,CAAA8C,KAAA,GAAAC,KAAA,MAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA;MAAAhD,IAAA,CAAAgD,KAAA,IAAAlD,SAAA,CAAAkD,KAAA;IAAA;IAAAF,MAAA,GAAAjE,UAAA,OAAAX,iBAAA,KAAAiC,MAAA,CAAAH,IAAA;IAAA8C,MAAA,CAY5B1C,KAAK,GAAG,YAAkC;MACxC,IAAMC,aAAa,GAAGyC,MAAA,CAAKxC,gBAAgB,CAAC,CAAC;MAC7C,IAAA2C,qBAAA,GAA4BH,MAAA,CAAKtC,qBAAqB,CAAC,CAAC;QAAA0C,sBAAA,OAAA7E,eAAA,CAAAY,OAAA,EAAAgE,qBAAA;QAAjDvC,SAAS,GAAAwC,sBAAA;QAAEvC,MAAM,GAAAuC,sBAAA;MACxB,IAAMtC,KAAK,GAAGkC,MAAA,CAAKjC,QAAQ,CAAC,CAAC;MAC7B,IAAMG,QAAQ,GAAG8B,MAAA,CAAK7B,SAAS;MAC/B,IAAMC,aAAa,GAAG4B,MAAA,CAAK5B,aAAa;MAExC,OAAQ,UAAAC,MAAiC,EAAK;QAC5C,SAAS;;QACT,OAAO;UACLC,UAAU,EAAE;YACVC,OAAO,EAAEhB,aAAa,CAACO,KAAK,EAAEF,SAAS,CAAC,CAAC,EAAEC,MAAM,CAAC,CAAC;YACnDY,SAAS,EAAE,CACT;cACEC,UAAU,EAAEnB,aAAa,CACvBO,KAAK,EACLF,SAAS,CAAC,CAACS,MAAM,CAACS,WAAW,EAAEjB,MAAM,CACvC;YACF,CAAC,EACD;cACEe,KAAK,EAAErB,aAAa,CAACO,KAAK,EAAEF,SAAS,CAAC,OAAO,EAAEC,MAAM,CAAC;YACxD,CAAC;UAEL,CAAC;UACDO,aAAa,EAAArD,MAAA,CAAA4D,MAAA;YACXJ,OAAO,EAAE,CAAC;YACVE,SAAS,EAAE,CAAC;cAAEC,UAAU,EAAE;YAAE,CAAC,EAAE;cAAEE,KAAK,EAAE;YAAO,CAAC;UAAC,GAC9CR,aAAA,CACJ;UACDF,QAAA,EAAAA;QACF,CAAC;MACH,CAAC;IACH,CAAC;IAAA,OAAA8B,MAAA;EAAA;EAAA,IAAApE,UAAA,CAAAO,OAAA,EAAAf,iBAAA,EAAA2E,sBAAA;EAAA,WAAAtE,aAAA,CAAAU,OAAA,EAAAf,iBAAA;IAAA2D,GAAA;IAAA7D,KAAA,EAtCD,SAAO8D,cAAcA,CAAA,EAEF;MACjB,OAAO,IAAI5D,iBAAiB,CAAC,CAAC;IAChC;EAAA;AAAA,EATQ6D,+BAAuB;AADpB7D,iBAAiB,CAIrB8D,UAAU,GAAG,mBAAmB", "ignoreList": []}