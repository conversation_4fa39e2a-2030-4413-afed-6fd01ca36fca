e0c389dee6c7949e70b2069ea7612068
'use strict';

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.PinwheelData = exports.Pinwheel = void 0;
var _animationParser = require("../animationParser.js");
var DEFAULT_PINWHEEL_TIME = 0.3;
var PinwheelData = exports.PinwheelData = {
  PinwheelIn: {
    name: 'PinwheelIn',
    style: {
      0: {
        transform: [{
          rotate: '5rad',
          scale: 0
        }],
        opacity: 0
      },
      100: {
        transform: [{
          rotate: '0deg',
          scale: 1
        }],
        opacity: 1
      }
    },
    duration: DEFAULT_PINWHEEL_TIME
  },
  PinwheelOut: {
    name: 'PinwheelOut',
    style: {
      0: {
        transform: [{
          rotate: '0rad',
          scale: 1
        }],
        opacity: 1
      },
      100: {
        transform: [{
          rotate: '5rad',
          scale: 0
        }],
        opacity: 0
      }
    },
    duration: DEFAULT_PINWHEEL_TIME
  }
};
var Pinwheel = exports.Pinwheel = {
  PinwheelIn: {
    style: (0, _animationParser.convertAnimationObjectToKeyframes)(PinwheelData.PinwheelIn),
    duration: PinwheelData.PinwheelIn.duration
  },
  PinwheelOut: {
    style: (0, _animationParser.convertAnimationObjectToKeyframes)(PinwheelData.PinwheelOut),
    duration: PinwheelData.PinwheelOut.duration
  }
};
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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