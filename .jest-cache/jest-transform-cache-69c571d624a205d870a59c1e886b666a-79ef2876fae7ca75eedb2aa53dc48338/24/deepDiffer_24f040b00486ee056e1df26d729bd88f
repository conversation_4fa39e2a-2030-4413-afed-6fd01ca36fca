ed00ede5e18e0b119add7ac6d65d03ac
'use strict';

var logListeners;
function unstable_setLogListeners(listeners) {
  logListeners = listeners;
}
var _deepDiffer = function deepDiffer(one, two) {
  var maxDepthOrOptions = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : -1;
  var maybeOptions = arguments.length > 3 ? arguments[3] : undefined;
  var options = typeof maxDepthOrOptions === 'number' ? maybeOptions : maxDepthOrOptions;
  var maxDepth = typeof maxDepthOrOptions === 'number' ? maxDepthOrOptions : -1;
  if (maxDepth === 0) {
    return true;
  }
  if (one === two) {
    return false;
  }
  if (typeof one === 'function' && typeof two === 'function') {
    var unsafelyIgnoreFunctions = options == null ? void 0 : options.unsafelyIgnoreFunctions;
    if (unsafelyIgnoreFunctions == null) {
      if (logListeners && logListeners.onDifferentFunctionsIgnored && (!options || !('unsafelyIgnoreFunctions' in options))) {
        logListeners.onDifferentFunctionsIgnored(one.name, two.name);
      }
      unsafelyIgnoreFunctions = true;
    }
    return !unsafelyIgnoreFunctions;
  }
  if (typeof one !== 'object' || one === null) {
    return one !== two;
  }
  if (typeof two !== 'object' || two === null) {
    return true;
  }
  if (one.constructor !== two.constructor) {
    return true;
  }
  if (Array.isArray(one)) {
    var len = one.length;
    if (two.length !== len) {
      return true;
    }
    for (var ii = 0; ii < len; ii++) {
      if (_deepDiffer(one[ii], two[ii], maxDepth - 1, options)) {
        return true;
      }
    }
  } else {
    for (var key in one) {
      if (_deepDiffer(one[key], two[key], maxDepth - 1, options)) {
        return true;
      }
    }
    for (var twoKey in two) {
      if (one[twoKey] === undefined && two[twoKey] !== undefined) {
        return true;
      }
    }
  }
  return false;
};
_deepDiffer.unstable_setLogListeners = unstable_setLogListeners;
module.exports = _deepDiffer;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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