{"version": 3, "names": ["logListeners", "unstable_setLogListeners", "listeners", "<PERSON><PERSON><PERSON><PERSON>", "one", "two", "maxDepthOrOptions", "arguments", "length", "undefined", "maybeOptions", "options", "max<PERSON><PERSON><PERSON>", "unsafelyIgnoreFunctions", "onDifferentFunctionsIgnored", "name", "constructor", "Array", "isArray", "len", "ii", "key", "twoKey", "module", "exports"], "sources": ["deepDiffer.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @format\n * @flow\n */\n\n'use strict';\n\nlet logListeners;\n\ntype LogListeners = {|\n  +onDifferentFunctionsIgnored: (nameOne: ?string, nameTwo: ?string) => void,\n|};\n\ntype Options = {|+unsafelyIgnoreFunctions?: boolean|};\n\nfunction unstable_setLogListeners(listeners: ?LogListeners) {\n  logListeners = listeners;\n}\n\n/*\n * @returns {bool} true if different, false if equal\n */\nconst deepDiffer = function (\n  one: any,\n  two: any,\n  maxDepthOrOptions: Options | number = -1,\n  maybeOptions?: Options,\n): boolean {\n  const options =\n    typeof maxDepthOrOptions === 'number' ? maybeOptions : maxDepthOrOptions;\n  const maxDepth =\n    typeof maxDepthOrOptions === 'number' ? maxDepthOrOptions : -1;\n  if (maxDepth === 0) {\n    return true;\n  }\n  if (one === two) {\n    // Short circuit on identical object references instead of traversing them.\n    return false;\n  }\n  if (typeof one === 'function' && typeof two === 'function') {\n    // We consider all functions equal unless explicitly configured otherwise\n    let unsafelyIgnoreFunctions = options?.unsafelyIgnoreFunctions;\n    if (unsafelyIgnoreFunctions == null) {\n      if (\n        logListeners &&\n        logListeners.onDifferentFunctionsIgnored &&\n        (!options || !('unsafelyIgnoreFunctions' in options))\n      ) {\n        logListeners.onDifferentFunctionsIgnored(one.name, two.name);\n      }\n      unsafelyIgnoreFunctions = true;\n    }\n    return !unsafelyIgnoreFunctions;\n  }\n  if (typeof one !== 'object' || one === null) {\n    // Primitives can be directly compared\n    return one !== two;\n  }\n  if (typeof two !== 'object' || two === null) {\n    // We know they are different because the previous case would have triggered\n    // otherwise.\n    return true;\n  }\n  if (one.constructor !== two.constructor) {\n    return true;\n  }\n  if (Array.isArray(one)) {\n    // We know two is also an array because the constructors are equal\n    const len = one.length;\n    if (two.length !== len) {\n      return true;\n    }\n    for (let ii = 0; ii < len; ii++) {\n      if (deepDiffer(one[ii], two[ii], maxDepth - 1, options)) {\n        return true;\n      }\n    }\n  } else {\n    for (const key in one) {\n      if (deepDiffer(one[key], two[key], maxDepth - 1, options)) {\n        return true;\n      }\n    }\n    for (const twoKey in two) {\n      // The only case we haven't checked yet is keys that are in two but aren't\n      // in one, which means they are different.\n      if (one[twoKey] === undefined && two[twoKey] !== undefined) {\n        return true;\n      }\n    }\n  }\n  return false;\n};\n\ndeepDiffer.unstable_setLogListeners = unstable_setLogListeners;\nmodule.exports = deepDiffer;\n"], "mappings": "AAUA,YAAY;;AAEZ,IAAIA,YAAY;AAQhB,SAASC,wBAAwBA,CAACC,SAAwB,EAAE;EAC1DF,YAAY,GAAGE,SAAS;AAC1B;AAKA,IAAMC,WAAU,GAAG,SAAbA,UAAUA,CACdC,GAAQ,EACRC,GAAQ,EAGC;EAAA,IAFTC,iBAAmC,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;EAAA,IACxCG,YAAsB,GAAAH,SAAA,CAAAC,MAAA,OAAAD,SAAA,MAAAE,SAAA;EAEtB,IAAME,OAAO,GACX,OAAOL,iBAAiB,KAAK,QAAQ,GAAGI,YAAY,GAAGJ,iBAAiB;EAC1E,IAAMM,QAAQ,GACZ,OAAON,iBAAiB,KAAK,QAAQ,GAAGA,iBAAiB,GAAG,CAAC,CAAC;EAChE,IAAIM,QAAQ,KAAK,CAAC,EAAE;IAClB,OAAO,IAAI;EACb;EACA,IAAIR,GAAG,KAAKC,GAAG,EAAE;IAEf,OAAO,KAAK;EACd;EACA,IAAI,OAAOD,GAAG,KAAK,UAAU,IAAI,OAAOC,GAAG,KAAK,UAAU,EAAE;IAE1D,IAAIQ,uBAAuB,GAAGF,OAAO,oBAAPA,OAAO,CAAEE,uBAAuB;IAC9D,IAAIA,uBAAuB,IAAI,IAAI,EAAE;MACnC,IACEb,YAAY,IACZA,YAAY,CAACc,2BAA2B,KACvC,CAACH,OAAO,IAAI,EAAE,yBAAyB,IAAIA,OAAO,CAAC,CAAC,EACrD;QACAX,YAAY,CAACc,2BAA2B,CAACV,GAAG,CAACW,IAAI,EAAEV,GAAG,CAACU,IAAI,CAAC;MAC9D;MACAF,uBAAuB,GAAG,IAAI;IAChC;IACA,OAAO,CAACA,uBAAuB;EACjC;EACA,IAAI,OAAOT,GAAG,KAAK,QAAQ,IAAIA,GAAG,KAAK,IAAI,EAAE;IAE3C,OAAOA,GAAG,KAAKC,GAAG;EACpB;EACA,IAAI,OAAOA,GAAG,KAAK,QAAQ,IAAIA,GAAG,KAAK,IAAI,EAAE;IAG3C,OAAO,IAAI;EACb;EACA,IAAID,GAAG,CAACY,WAAW,KAAKX,GAAG,CAACW,WAAW,EAAE;IACvC,OAAO,IAAI;EACb;EACA,IAAIC,KAAK,CAACC,OAAO,CAACd,GAAG,CAAC,EAAE;IAEtB,IAAMe,GAAG,GAAGf,GAAG,CAACI,MAAM;IACtB,IAAIH,GAAG,CAACG,MAAM,KAAKW,GAAG,EAAE;MACtB,OAAO,IAAI;IACb;IACA,KAAK,IAAIC,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGD,GAAG,EAAEC,EAAE,EAAE,EAAE;MAC/B,IAAIjB,WAAU,CAACC,GAAG,CAACgB,EAAE,CAAC,EAAEf,GAAG,CAACe,EAAE,CAAC,EAAER,QAAQ,GAAG,CAAC,EAAED,OAAO,CAAC,EAAE;QACvD,OAAO,IAAI;MACb;IACF;EACF,CAAC,MAAM;IACL,KAAK,IAAMU,GAAG,IAAIjB,GAAG,EAAE;MACrB,IAAID,WAAU,CAACC,GAAG,CAACiB,GAAG,CAAC,EAAEhB,GAAG,CAACgB,GAAG,CAAC,EAAET,QAAQ,GAAG,CAAC,EAAED,OAAO,CAAC,EAAE;QACzD,OAAO,IAAI;MACb;IACF;IACA,KAAK,IAAMW,MAAM,IAAIjB,GAAG,EAAE;MAGxB,IAAID,GAAG,CAACkB,MAAM,CAAC,KAAKb,SAAS,IAAIJ,GAAG,CAACiB,MAAM,CAAC,KAAKb,SAAS,EAAE;QAC1D,OAAO,IAAI;MACb;IACF;EACF;EACA,OAAO,KAAK;AACd,CAAC;AAEDN,WAAU,CAACF,wBAAwB,GAAGA,wBAAwB;AAC9DsB,MAAM,CAACC,OAAO,GAAGrB,WAAU", "ignoreList": []}