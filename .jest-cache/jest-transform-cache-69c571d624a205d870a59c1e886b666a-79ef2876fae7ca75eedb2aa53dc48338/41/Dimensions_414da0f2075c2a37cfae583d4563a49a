8102c80cfdba5b12153169b0101ecd6a
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _classCallCheck2 = _interopRequireDefault(require("@babel/runtime/helpers/classCallCheck"));
var _createClass2 = _interopRequireDefault(require("@babel/runtime/helpers/createClass"));
var _RCTDeviceEventEmitter = _interopRequireDefault(require("../EventEmitter/RCTDeviceEventEmitter"));
var _EventEmitter = _interopRequireDefault(require("../vendor/emitter/EventEmitter"));
var _NativeDeviceInfo = _interopRequireDefault(require("./NativeDeviceInfo"));
var _invariant = _interopRequireDefault(require("invariant"));
var eventEmitter = new _EventEmitter.default();
var dimensionsInitialized = false;
var dimensions;
var Dimensions = function () {
  function Dimensions() {
    (0, _classCallCheck2.default)(this, Dimensions);
  }
  return (0, _createClass2.default)(Dimensions, null, [{
    key: "get",
    value: function get(dim) {
      (0, _invariant.default)(dimensions[dim], 'No dimension set for key ' + dim);
      return dimensions[dim];
    }
  }, {
    key: "set",
    value: function set(dims) {
      var screen = dims.screen,
        window = dims.window;
      var windowPhysicalPixels = dims.windowPhysicalPixels;
      if (windowPhysicalPixels) {
        window = {
          width: windowPhysicalPixels.width / windowPhysicalPixels.scale,
          height: windowPhysicalPixels.height / windowPhysicalPixels.scale,
          scale: windowPhysicalPixels.scale,
          fontScale: windowPhysicalPixels.fontScale
        };
      }
      var screenPhysicalPixels = dims.screenPhysicalPixels;
      if (screenPhysicalPixels) {
        screen = {
          width: screenPhysicalPixels.width / screenPhysicalPixels.scale,
          height: screenPhysicalPixels.height / screenPhysicalPixels.scale,
          scale: screenPhysicalPixels.scale,
          fontScale: screenPhysicalPixels.fontScale
        };
      } else if (screen == null) {
        screen = window;
      }
      dimensions = {
        window: window,
        screen: screen
      };
      if (dimensionsInitialized) {
        eventEmitter.emit('change', dimensions);
      } else {
        dimensionsInitialized = true;
      }
    }
  }, {
    key: "addEventListener",
    value: function addEventListener(type, handler) {
      (0, _invariant.default)(type === 'change', 'Trying to subscribe to unknown event: "%s"', type);
      return eventEmitter.addListener(type, handler);
    }
  }]);
}();
_RCTDeviceEventEmitter.default.addListener('didUpdateDimensions', function (update) {
  Dimensions.set(update);
});
Dimensions.set(_NativeDeviceInfo.default.getConstants().Dimensions);
var _default = exports.default = Dimensions;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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