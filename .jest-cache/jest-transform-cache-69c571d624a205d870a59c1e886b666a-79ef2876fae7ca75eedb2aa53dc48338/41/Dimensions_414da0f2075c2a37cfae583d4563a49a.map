{"version": 3, "names": ["_RCTDeviceEventEmitter", "_interopRequireDefault", "require", "_EventEmitter", "_NativeDeviceInfo", "_invariant", "eventEmitter", "EventEmitter", "dimensionsInitialized", "dimensions", "Dimensions", "_classCallCheck2", "default", "_createClass2", "key", "value", "get", "dim", "invariant", "set", "dims", "screen", "window", "windowPhysicalPixels", "width", "scale", "height", "fontScale", "screenPhysicalPixels", "emit", "addEventListener", "type", "handler", "addListener", "RCTDeviceEventEmitter", "update", "NativeDeviceInfo", "getConstants", "_default", "exports"], "sources": ["Dimensions.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @format\n * @flow\n */\n\nimport RCTDeviceEventEmitter from '../EventEmitter/RCTDeviceEventEmitter';\nimport EventEmitter, {\n  type EventSubscription,\n} from '../vendor/emitter/EventEmitter';\nimport NativeDeviceInfo, {\n  type DimensionsPayload,\n  type DisplayMetrics,\n  type DisplayMetricsAndroid,\n} from './NativeDeviceInfo';\nimport invariant from 'invariant';\n\nconst eventEmitter = new EventEmitter<{\n  change: [DimensionsPayload],\n}>();\nlet dimensionsInitialized = false;\nlet dimensions: DimensionsPayload;\n\nclass Dimensions {\n  /**\n   * NOTE: `useWindowDimensions` is the preferred API for React components.\n   *\n   * Initial dimensions are set before `runApplication` is called so they should\n   * be available before any other require's are run, but may be updated later.\n   *\n   * Note: Although dimensions are available immediately, they may change (e.g\n   * due to device rotation) so any rendering logic or styles that depend on\n   * these constants should try to call this function on every render, rather\n   * than caching the value (for example, using inline styles rather than\n   * setting a value in a `StyleSheet`).\n   *\n   * Example: `const {height, width} = Dimensions.get('window');`\n   *\n   * @param {string} dim Name of dimension as defined when calling `set`.\n   * @returns {DisplayMetrics? | DisplayMetricsAndroid?} Value for the dimension.\n   */\n  static get(dim: string): DisplayMetrics | DisplayMetricsAndroid {\n    // $FlowFixMe[invalid-computed-prop]\n    invariant(dimensions[dim], 'No dimension set for key ' + dim);\n    return dimensions[dim];\n  }\n\n  /**\n   * This should only be called from native code by sending the\n   * didUpdateDimensions event.\n   *\n   * @param {DimensionsPayload} dims Simple string-keyed object of dimensions to set\n   */\n  static set(dims: $ReadOnly<DimensionsPayload>): void {\n    // We calculate the window dimensions in JS so that we don't encounter loss of\n    // precision in transferring the dimensions (which could be non-integers) over\n    // the bridge.\n    let {screen, window} = dims;\n    const {windowPhysicalPixels} = dims;\n    if (windowPhysicalPixels) {\n      window = {\n        width: windowPhysicalPixels.width / windowPhysicalPixels.scale,\n        height: windowPhysicalPixels.height / windowPhysicalPixels.scale,\n        scale: windowPhysicalPixels.scale,\n        fontScale: windowPhysicalPixels.fontScale,\n      };\n    }\n    const {screenPhysicalPixels} = dims;\n    if (screenPhysicalPixels) {\n      screen = {\n        width: screenPhysicalPixels.width / screenPhysicalPixels.scale,\n        height: screenPhysicalPixels.height / screenPhysicalPixels.scale,\n        scale: screenPhysicalPixels.scale,\n        fontScale: screenPhysicalPixels.fontScale,\n      };\n    } else if (screen == null) {\n      screen = window;\n    }\n\n    dimensions = {window, screen};\n    if (dimensionsInitialized) {\n      // Don't fire 'change' the first time the dimensions are set.\n      eventEmitter.emit('change', dimensions);\n    } else {\n      dimensionsInitialized = true;\n    }\n  }\n\n  /**\n   * Add an event handler. Supported events:\n   *\n   * - `change`: Fires when a property within the `Dimensions` object changes. The argument\n   *   to the event handler is an object with `window` and `screen` properties whose values\n   *   are the same as the return values of `Dimensions.get('window')` and\n   *   `Dimensions.get('screen')`, respectively.\n   */\n  static addEventListener(\n    type: 'change',\n    handler: Function,\n  ): EventSubscription {\n    invariant(\n      type === 'change',\n      'Trying to subscribe to unknown event: \"%s\"',\n      type,\n    );\n    return eventEmitter.addListener(type, handler);\n  }\n}\n\n// Subscribe before calling getConstants to make sure we don't miss any updates in between.\nRCTDeviceEventEmitter.addListener(\n  'didUpdateDimensions',\n  (update: DimensionsPayload) => {\n    Dimensions.set(update);\n  },\n);\nDimensions.set(NativeDeviceInfo.getConstants().Dimensions);\n\nexport default Dimensions;\n"], "mappings": ";;;;;;;AAUA,IAAAA,sBAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,aAAA,GAAAF,sBAAA,CAAAC,OAAA;AAGA,IAAAE,iBAAA,GAAAH,sBAAA,CAAAC,OAAA;AAKA,IAAAG,UAAA,GAAAJ,sBAAA,CAAAC,OAAA;AAEA,IAAMI,YAAY,GAAG,IAAIC,qBAAY,CAElC,CAAC;AACJ,IAAIC,qBAAqB,GAAG,KAAK;AACjC,IAAIC,UAA6B;AAAC,IAE5BC,UAAU;EAAA,SAAAA,WAAA;IAAA,IAAAC,gBAAA,CAAAC,OAAA,QAAAF,UAAA;EAAA;EAAA,WAAAG,aAAA,CAAAD,OAAA,EAAAF,UAAA;IAAAI,GAAA;IAAAC,KAAA,EAkBd,SAAOC,GAAGA,CAACC,GAAW,EAA0C;MAE9D,IAAAC,kBAAS,EAACT,UAAU,CAACQ,GAAG,CAAC,EAAE,2BAA2B,GAAGA,GAAG,CAAC;MAC7D,OAAOR,UAAU,CAACQ,GAAG,CAAC;IACxB;EAAC;IAAAH,GAAA;IAAAC,KAAA,EAQD,SAAOI,GAAGA,CAACC,IAAkC,EAAQ;MAInD,IAAKC,MAAM,GAAYD,IAAI,CAAtBC,MAAM;QAAEC,MAAM,GAAIF,IAAI,CAAdE,MAAM;MACnB,IAAOC,oBAAoB,GAAIH,IAAI,CAA5BG,oBAAoB;MAC3B,IAAIA,oBAAoB,EAAE;QACxBD,MAAM,GAAG;UACPE,KAAK,EAAED,oBAAoB,CAACC,KAAK,GAAGD,oBAAoB,CAACE,KAAK;UAC9DC,MAAM,EAAEH,oBAAoB,CAACG,MAAM,GAAGH,oBAAoB,CAACE,KAAK;UAChEA,KAAK,EAAEF,oBAAoB,CAACE,KAAK;UACjCE,SAAS,EAAEJ,oBAAoB,CAACI;QAClC,CAAC;MACH;MACA,IAAOC,oBAAoB,GAAIR,IAAI,CAA5BQ,oBAAoB;MAC3B,IAAIA,oBAAoB,EAAE;QACxBP,MAAM,GAAG;UACPG,KAAK,EAAEI,oBAAoB,CAACJ,KAAK,GAAGI,oBAAoB,CAACH,KAAK;UAC9DC,MAAM,EAAEE,oBAAoB,CAACF,MAAM,GAAGE,oBAAoB,CAACH,KAAK;UAChEA,KAAK,EAAEG,oBAAoB,CAACH,KAAK;UACjCE,SAAS,EAAEC,oBAAoB,CAACD;QAClC,CAAC;MACH,CAAC,MAAM,IAAIN,MAAM,IAAI,IAAI,EAAE;QACzBA,MAAM,GAAGC,MAAM;MACjB;MAEAb,UAAU,GAAG;QAACa,MAAM,EAANA,MAAM;QAAED,MAAM,EAANA;MAAM,CAAC;MAC7B,IAAIb,qBAAqB,EAAE;QAEzBF,YAAY,CAACuB,IAAI,CAAC,QAAQ,EAAEpB,UAAU,CAAC;MACzC,CAAC,MAAM;QACLD,qBAAqB,GAAG,IAAI;MAC9B;IACF;EAAC;IAAAM,GAAA;IAAAC,KAAA,EAUD,SAAOe,gBAAgBA,CACrBC,IAAc,EACdC,OAAiB,EACE;MACnB,IAAAd,kBAAS,EACPa,IAAI,KAAK,QAAQ,EACjB,4CAA4C,EAC5CA,IACF,CAAC;MACD,OAAOzB,YAAY,CAAC2B,WAAW,CAACF,IAAI,EAAEC,OAAO,CAAC;IAChD;EAAC;AAAA;AAIHE,8BAAqB,CAACD,WAAW,CAC/B,qBAAqB,EACrB,UAACE,MAAyB,EAAK;EAC7BzB,UAAU,CAACS,GAAG,CAACgB,MAAM,CAAC;AACxB,CACF,CAAC;AACDzB,UAAU,CAACS,GAAG,CAACiB,yBAAgB,CAACC,YAAY,CAAC,CAAC,CAAC3B,UAAU,CAAC;AAAC,IAAA4B,QAAA,GAAAC,OAAA,CAAA3B,OAAA,GAE5CF,UAAU", "ignoreList": []}