d004ecfcb52a470e8e53a4c148fd85e9
"use strict";

/* istanbul ignore next */
function cov_1moxq7795z() {
  var path = "/Users/<USER>/projects/react-native/payment-module/src/components/transfer-account-number-input/types.ts";
  var hash = "bf17af683859590990444f47520106c104a4ddfd";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/projects/react-native/payment-module/src/components/transfer-account-number-input/types.ts",
    statementMap: {
      "0": {
        start: {
          line: 3,
          column: 0
        },
        end: {
          line: 5,
          column: 3
        }
      }
    },
    fnMap: {},
    branchMap: {},
    s: {
      "0": 0
    },
    f: {},
    b: {},
    inputSourceMap: {
      version: 3,
      file: undefined,
      names: [],
      sourceRoot: undefined,
      sources: ["/Users/<USER>/projects/react-native/payment-module/src/components/transfer-account-number-input/types.ts"],
      sourcesContent: ["import {MSBInputBaseProps} from 'msb-shared-component';\n\nexport type TransferAccountNumberInputProps = MSBInputBaseProps & {};\n"],
      mappings: "",
      ignoreList: []
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "bf17af683859590990444f47520106c104a4ddfd"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_1moxq7795z = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_1moxq7795z();
cov_1moxq7795z().s[0]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6W10sInNvdXJjZXMiOlsiL1VzZXJzL3NvbnNhbXNldC9wcm9qZWN0cy9yZWFjdC1uYXRpdmUvcGF5bWVudC1tb2R1bGUvc3JjL2NvbXBvbmVudHMvdHJhbnNmZXItYWNjb3VudC1udW1iZXItaW5wdXQvdHlwZXMudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHtNU0JJbnB1dEJhc2VQcm9wc30gZnJvbSAnbXNiLXNoYXJlZC1jb21wb25lbnQnO1xuXG5leHBvcnQgdHlwZSBUcmFuc2ZlckFjY291bnROdW1iZXJJbnB1dFByb3BzID0gTVNCSW5wdXRCYXNlUHJvcHMgJiB7fTtcbiJdLCJtYXBwaW5ncyI6IiIsImlnbm9yZUxpc3QiOltdfQ==