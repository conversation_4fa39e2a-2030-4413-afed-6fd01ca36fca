{"version": 3, "names": ["cov_2g6ppgnef0", "actualCoverage", "ResponseHandler_1", "s", "require", "PathResolver_1", "msb_host_shared_module_1", "MSBCustomError_1", "PaymentOrderRemoteDataSource", "f", "httpClient", "_classCallCheck2", "default", "_createClass2", "key", "value", "_paymentOrder", "_asyncToGenerator2", "request", "_msb_host_shared_modu", "url", "PathResolver", "paymentOrder", "console", "log", "response", "hostSharedModule", "d", "domainService", "b", "onTransfer", "method", "body", "JSON", "stringify", "error", "CustomError", "createError", "_x", "apply", "arguments", "_paymentOrderStatus", "Object", "keys", "includes", "_request$id", "_url", "paymentOrderStatus", "id", "_response", "get", "handleResponse", "queryParams", "URLSearchParams", "toString", "fullUrl", "_x2", "exports"], "sources": ["/Users/<USER>/projects/react-native/payment-module/src/data/datasources/remote/PaymentOrderRemoteDataSource.ts"], "sourcesContent": ["import {PaymentOrderStatusResponse} from '../../models/payment-order-status/PaymentOrderStatusResponse';\nimport {handleResponse} from '../../../utils/ResponseHandler';\nimport {PaymentOrderResponse} from '../../models/payment-order/PaymentOrderResponse';\nimport {BaseResponse} from '../../../core/BaseResponse';\nimport {PathResolver} from '../../../utils/PathResolver';\nimport {hostSharedModule, IHttpClient} from 'msb-host-shared-module';\nimport {IPaymentOrderDataSource} from '../IPaymentOrderDataSource';\nimport {PaymentOrderRequest} from '../../models/payment-order/PaymentOrderRequest';\nimport {PaymentOrderStatusRequest} from '../../models/payment-order-status/PaymentOrderStatusRequest';\nimport {createError, CustomError} from '../../../core/MSBCustomError';\n\nexport class PaymentOrderRemoteDataSource implements IPaymentOrderDataSource {\n  constructor(private httpClient: IHttpClient) {}\n\n  async paymentOrder(request: PaymentOrderRequest): Promise<BaseResponse<PaymentOrderResponse>> {\n    try {\n      const url = PathResolver.paymentOrder.paymentOrder();\n      console.log('url', url);\n      console.log('request', request);\n      const response = await hostSharedModule.d.domainService?.onTransfer({\n        url: url,\n        method: 'POST',\n        body: JSON.stringify(request),\n      });\n      return response as BaseResponse<PaymentOrderResponse>;\n    } catch (error: any) {\n      if (error instanceof CustomError) {\n        throw error;\n      }\n      throw createError();\n    }\n  }\n\n  async paymentOrderStatus(request: PaymentOrderStatusRequest): Promise<BaseResponse<PaymentOrderStatusResponse>> {\n    try {\n      if (Object.keys(request).includes('id')) {\n        const url = PathResolver.paymentOrder.paymentOrderStatus(request.id ?? '');\n        const response = await this.httpClient.get(url);\n        return handleResponse(response);\n      }\n      const url = PathResolver.paymentOrder.paymentOrder();\n      const queryParams = new URLSearchParams(request as any).toString();\n      const fullUrl = `${url}?${queryParams}`;\n      const response = await this.httpClient.get(fullUrl);\n      return handleResponse(response);\n    } catch (error: any) {\n      if (error instanceof CustomError) {\n        throw error;\n      }\n      throw createError();\n    }\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAYE;IAAAA,cAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,cAAA;;;;;;;;;;;;;;;;;;;;;AAXF,IAAAE,iBAAA;AAAA;AAAA,CAAAF,cAAA,GAAAG,CAAA,OAAAC,OAAA;AAGA,IAAAC,cAAA;AAAA;AAAA,CAAAL,cAAA,GAAAG,CAAA,OAAAC,OAAA;AACA,IAAAE,wBAAA;AAAA;AAAA,CAAAN,cAAA,GAAAG,CAAA,OAAAC,OAAA;AAIA,IAAAG,gBAAA;AAAA;AAAA,CAAAP,cAAA,GAAAG,CAAA,OAAAC,OAAA;AAAsE,IAEzDI,4BAA4B;AAAA;AAAA,CAAAR,cAAA,GAAAG,CAAA;EAAA;EAAAH,cAAA,GAAAS,CAAA;EACvC,SAAAD,6BAAoBE,UAAuB;IAAA;IAAAV,cAAA,GAAAS,CAAA;IAAAT,cAAA,GAAAG,CAAA;IAAA,IAAAQ,gBAAA,CAAAC,OAAA,QAAAJ,4BAAA;IAAA;IAAAR,cAAA,GAAAG,CAAA;IAAvB,KAAAO,UAAU,GAAVA,UAAU;EAAgB;EAAA;EAAAV,cAAA,GAAAG,CAAA;EAAC,WAAAU,aAAA,CAAAD,OAAA,EAAAJ,4BAAA;IAAAM,GAAA;IAAAC,KAAA;MAAA;MAAAf,cAAA,GAAAS,CAAA;MAAA,IAAAO,aAAA;MAAA;MAAA,CAAAhB,cAAA,GAAAG,CAAA,YAAAc,kBAAA,CAAAL,OAAA,EAE/C,WAAmBM,OAA4B;QAAA;QAAAlB,cAAA,GAAAS,CAAA;QAAAT,cAAA,GAAAG,CAAA;QAC7C,IAAI;UAAA,IAAAgB,qBAAA;UACF,IAAMC,GAAG;UAAA;UAAA,CAAApB,cAAA,GAAAG,CAAA,QAAGE,cAAA,CAAAgB,YAAY,CAACC,YAAY,CAACA,YAAY,EAAE;UAAA;UAAAtB,cAAA,GAAAG,CAAA;UACpDoB,OAAO,CAACC,GAAG,CAAC,KAAK,EAAEJ,GAAG,CAAC;UAAA;UAAApB,cAAA,GAAAG,CAAA;UACvBoB,OAAO,CAACC,GAAG,CAAC,SAAS,EAAEN,OAAO,CAAC;UAC/B,IAAMO,QAAQ;UAAA;UAAA,CAAAzB,cAAA,GAAAG,CAAA,eAAAgB,qBAAA,GAASb,wBAAA,CAAAoB,gBAAgB,CAACC,CAAC,CAACC,aAAa;UAAA;UAAA,CAAA5B,cAAA,GAAA6B,CAAA;UAAA;UAAA,CAAA7B,cAAA,GAAA6B,CAAA,UAAhCV,qBAAA,CAAkCW,UAAU,CAAC;YAClEV,GAAG,EAAEA,GAAG;YACRW,MAAM,EAAE,MAAM;YACdC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAChB,OAAO;WAC7B,CAAC;UAAA;UAAAlB,cAAA,GAAAG,CAAA;UACF,OAAOsB,QAA8C;QACvD,CAAC,CAAC,OAAOU,KAAU,EAAE;UAAA;UAAAnC,cAAA,GAAAG,CAAA;UACnB,IAAIgC,KAAK,YAAY5B,gBAAA,CAAA6B,WAAW,EAAE;YAAA;YAAApC,cAAA,GAAA6B,CAAA;YAAA7B,cAAA,GAAAG,CAAA;YAChC,MAAMgC,KAAK;UACb;UAAA;UAAA;YAAAnC,cAAA,GAAA6B,CAAA;UAAA;UAAA7B,cAAA,GAAAG,CAAA;UACA,MAAM,IAAAI,gBAAA,CAAA8B,WAAW,GAAE;QACrB;MACF,CAAC;MAAA,SAjBKf,YAAYA,CAAAgB,EAAA;QAAA;QAAAtC,cAAA,GAAAS,CAAA;QAAAT,cAAA,GAAAG,CAAA;QAAA,OAAAa,aAAA,CAAAuB,KAAA,OAAAC,SAAA;MAAA;MAAA;MAAAxC,cAAA,GAAAG,CAAA;MAAA,OAAZmB,YAAY;IAAA;EAAA;IAAAR,GAAA;IAAAC,KAAA;MAAA;MAAAf,cAAA,GAAAS,CAAA;MAAA,IAAAgC,mBAAA;MAAA;MAAA,CAAAzC,cAAA,GAAAG,CAAA,YAAAc,kBAAA,CAAAL,OAAA,EAmBlB,WAAyBM,OAAkC;QAAA;QAAAlB,cAAA,GAAAS,CAAA;QAAAT,cAAA,GAAAG,CAAA;QACzD,IAAI;UAAA;UAAAH,cAAA,GAAAG,CAAA;UACF,IAAIuC,MAAM,CAACC,IAAI,CAACzB,OAAO,CAAC,CAAC0B,QAAQ,CAAC,IAAI,CAAC,EAAE;YAAA;YAAA5C,cAAA,GAAA6B,CAAA;YAAA,IAAAgB,WAAA;YACvC,IAAMC,IAAG;YAAA;YAAA,CAAA9C,cAAA,GAAAG,CAAA,QAAGE,cAAA,CAAAgB,YAAY,CAACC,YAAY,CAACyB,kBAAkB,EAAAF,WAAA,GAAC3B,OAAO,CAAC8B,EAAE;YAAA;YAAA,CAAAhD,cAAA,GAAA6B,CAAA,UAAAgB,WAAA;YAAA;YAAA,CAAA7C,cAAA,GAAA6B,CAAA,UAAI,EAAE,EAAC;YAC1E,IAAMoB,SAAQ;YAAA;YAAA,CAAAjD,cAAA,GAAAG,CAAA,cAAS,IAAI,CAACO,UAAU,CAACwC,GAAG,CAACJ,IAAG,CAAC;YAAA;YAAA9C,cAAA,GAAAG,CAAA;YAC/C,OAAO,IAAAD,iBAAA,CAAAiD,cAAc,EAACF,SAAQ,CAAC;UACjC;UAAA;UAAA;YAAAjD,cAAA,GAAA6B,CAAA;UAAA;UACA,IAAMT,GAAG;UAAA;UAAA,CAAApB,cAAA,GAAAG,CAAA,QAAGE,cAAA,CAAAgB,YAAY,CAACC,YAAY,CAACA,YAAY,EAAE;UACpD,IAAM8B,WAAW;UAAA;UAAA,CAAApD,cAAA,GAAAG,CAAA,QAAG,IAAIkD,eAAe,CAACnC,OAAc,CAAC,CAACoC,QAAQ,EAAE;UAClE,IAAMC,OAAO;UAAA;UAAA,CAAAvD,cAAA,GAAAG,CAAA,QAAG,GAAGiB,GAAG,IAAIgC,WAAW,EAAE;UACvC,IAAM3B,QAAQ;UAAA;UAAA,CAAAzB,cAAA,GAAAG,CAAA,cAAS,IAAI,CAACO,UAAU,CAACwC,GAAG,CAACK,OAAO,CAAC;UAAA;UAAAvD,cAAA,GAAAG,CAAA;UACnD,OAAO,IAAAD,iBAAA,CAAAiD,cAAc,EAAC1B,QAAQ,CAAC;QACjC,CAAC,CAAC,OAAOU,KAAU,EAAE;UAAA;UAAAnC,cAAA,GAAAG,CAAA;UACnB,IAAIgC,KAAK,YAAY5B,gBAAA,CAAA6B,WAAW,EAAE;YAAA;YAAApC,cAAA,GAAA6B,CAAA;YAAA7B,cAAA,GAAAG,CAAA;YAChC,MAAMgC,KAAK;UACb;UAAA;UAAA;YAAAnC,cAAA,GAAA6B,CAAA;UAAA;UAAA7B,cAAA,GAAAG,CAAA;UACA,MAAM,IAAAI,gBAAA,CAAA8B,WAAW,GAAE;QACrB;MACF,CAAC;MAAA,SAlBKU,kBAAkBA,CAAAS,GAAA;QAAA;QAAAxD,cAAA,GAAAS,CAAA;QAAAT,cAAA,GAAAG,CAAA;QAAA,OAAAsC,mBAAA,CAAAF,KAAA,OAAAC,SAAA;MAAA;MAAA;MAAAxC,cAAA,GAAAG,CAAA;MAAA,OAAlB4C,kBAAkB;IAAA;EAAA;AAAA;AAAA;AAAA/C,cAAA,GAAAG,CAAA;AAtB1BsD,OAAA,CAAAjD,4BAAA,GAAAA,4BAAA", "ignoreList": []}