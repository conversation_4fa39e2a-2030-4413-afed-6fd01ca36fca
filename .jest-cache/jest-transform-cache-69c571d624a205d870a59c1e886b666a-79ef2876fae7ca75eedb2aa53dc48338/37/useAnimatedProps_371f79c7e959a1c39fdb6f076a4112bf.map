{"version": 3, "names": ["_NativeAnimatedHelper", "_interopRequireDefault", "require", "_useAnimatedPropsMemo", "ReactNativeFeatureFlags", "_interopRequireWildcard", "_ReactFabricPublicInstanceUtils", "_useRefEffect", "_AnimatedEvent", "_AnimatedProps", "_react", "_getRequireWildcardCache", "e", "WeakMap", "r", "t", "__esModule", "default", "has", "get", "n", "__proto__", "a", "Object", "defineProperty", "getOwnPropertyDescriptor", "u", "hasOwnProperty", "call", "i", "set", "useMemoOrAnimatedPropsMemo", "enableAnimatedPropsMemo", "useAnimatedPropsMemo", "useMemo", "useAnimatedProps", "props", "allowlist", "_useReducer", "useReducer", "count", "_useReducer2", "_slicedToArray2", "scheduleUpdate", "onUpdateRef", "useRef", "timerRef", "allowlistIfEnabled", "enableAnimatedAllowlist", "node", "AnimatedProps", "current", "useNativePropsInFabric", "shouldUseSetNativePropsInFabric", "useAnimatedPropsLifecycle", "useInsertionEffectsForAnimations", "useAnimatedPropsLifecycle_insertionEffects", "useAnimatedPropsLifecycle_layoutEffects", "refEffect", "useCallback", "instance", "setNativeView", "process", "env", "NODE_ENV", "isFabricNode", "isFabricInstance", "__isNative", "setNativeProps", "__getAnimatedValue", "clearTimeout", "setTimeout", "target", "getEventTarget", "events", "propName", "propValue", "AnimatedEvent", "__attach", "push", "_ref", "_ref2", "__detach", "callback<PERSON><PERSON>", "useRefEffect", "reduceAnimatedProps", "assign", "__getValueWithStaticProps", "__getValue", "collapsable", "prevNodeRef", "isUnmountingRef", "useEffect", "NativeAnimatedHelper", "API", "flushQueue", "drivenAnimationEndedListener", "nativeEventEmitter", "addListener", "data", "update", "_drivenAnimationEnded", "remove", "useLayoutEffect", "prevNode", "__restore<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_drivenAnimationEnded2", "useInsertionEffect", "getScrollableNode", "_instance$getScrollRe", "isFabricPublicInstance", "getNativeScrollRef", "getScrollResponder"], "sources": ["useAnimatedProps.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @flow strict-local\n * @format\n */\n\nimport type {EventSubscription} from '../EventEmitter/NativeEventEmitter';\nimport type {AnimatedPropsAllowlist} from './nodes/AnimatedProps';\n\nimport NativeAnimatedHelper from '../../src/private/animated/NativeAnimatedHelper';\nimport {useAnimatedPropsMemo} from '../../src/private/animated/useAnimatedPropsMemo';\nimport * as ReactNativeFeatureFlags from '../../src/private/featureflags/ReactNativeFeatureFlags';\nimport {isPublicInstance as isFabricPublicInstance} from '../ReactNative/ReactFabricPublicInstance/ReactFabricPublicInstanceUtils';\nimport useRefEffect from '../Utilities/useRefEffect';\nimport {AnimatedEvent} from './AnimatedEvent';\nimport AnimatedProps from './nodes/AnimatedProps';\nimport {\n  useCallback,\n  useEffect,\n  useInsertionEffect,\n  useLayoutEffect,\n  useMemo,\n  useReducer,\n  useRef,\n} from 'react';\n\ntype ReducedProps<TProps> = {\n  ...TProps,\n  collapsable: boolean,\n  ...\n};\ntype CallbackRef<T> = T => mixed;\n\ntype UpdateCallback = () => void;\n\nconst useMemoOrAnimatedPropsMemo =\n  ReactNativeFeatureFlags.enableAnimatedPropsMemo()\n    ? useAnimatedPropsMemo\n    : useMemo;\n\nexport default function useAnimatedProps<TProps: {...}, TInstance>(\n  props: TProps,\n  allowlist?: ?AnimatedPropsAllowlist,\n): [ReducedProps<TProps>, CallbackRef<TInstance | null>] {\n  const [, scheduleUpdate] = useReducer<number, void>(count => count + 1, 0);\n  const onUpdateRef = useRef<UpdateCallback | null>(null);\n  const timerRef = useRef<TimeoutID | null>(null);\n\n  const allowlistIfEnabled = ReactNativeFeatureFlags.enableAnimatedAllowlist()\n    ? allowlist\n    : null;\n\n  const node = useMemoOrAnimatedPropsMemo(\n    () =>\n      new AnimatedProps(\n        props,\n        () => onUpdateRef.current?.(),\n        allowlistIfEnabled,\n      ),\n    [allowlistIfEnabled, props],\n  );\n\n  const useNativePropsInFabric =\n    ReactNativeFeatureFlags.shouldUseSetNativePropsInFabric();\n\n  const useAnimatedPropsLifecycle =\n    ReactNativeFeatureFlags.useInsertionEffectsForAnimations()\n      ? useAnimatedPropsLifecycle_insertionEffects\n      : useAnimatedPropsLifecycle_layoutEffects;\n\n  useAnimatedPropsLifecycle(node);\n\n  // TODO: This \"effect\" does three things:\n  //\n  //   1) Call `setNativeView`.\n  //   2) Update `onUpdateRef`.\n  //   3) Update listeners for `AnimatedEvent` props.\n  //\n  // Ideally, each of these would be separate \"effects\" so that they are not\n  // unnecessarily re-run when irrelevant dependencies change. For example, we\n  // should be able to hoist all `AnimatedEvent` props and only do #3 if either\n  // the `AnimatedEvent` props change or `instance` changes.\n  //\n  // But there is no way to transparently compose three separate callback refs,\n  // so we just combine them all into one for now.\n  const refEffect = useCallback(\n    (instance: TInstance) => {\n      // NOTE: This may be called more often than necessary (e.g. when `props`\n      // changes), but `setNativeView` already optimizes for that.\n      node.setNativeView(instance);\n\n      // NOTE: When using the JS animation driver, this callback is called on\n      // every animation frame. When using the native driver, this callback is\n      // called when the animation completes.\n      onUpdateRef.current = () => {\n        if (process.env.NODE_ENV === 'test') {\n          // Check 1: this is a test.\n          // call `scheduleUpdate` to bypass use of setNativeProps.\n          return scheduleUpdate();\n        }\n\n        const isFabricNode = isFabricInstance(instance);\n        if (node.__isNative) {\n          // Check 2: this is an animation driven by native.\n          // In native driven animations, this callback is only called once the animation completes.\n          if (isFabricNode) {\n            // Call `scheduleUpdate` to synchronise Fiber and Shadow tree.\n            // Must not be called in Paper.\n            scheduleUpdate();\n          }\n          return;\n        }\n\n        if (\n          typeof instance !== 'object' ||\n          typeof instance?.setNativeProps !== 'function'\n        ) {\n          // Check 3: the instance does not support setNativeProps. Call `scheduleUpdate`.\n          return scheduleUpdate();\n        }\n\n        if (!isFabricNode) {\n          // Check 4: this is a paper instance, call setNativeProps.\n          // $FlowIgnore[not-a-function] - Assume it's still a function.\n          // $FlowFixMe[incompatible-use]\n          return instance.setNativeProps(node.__getAnimatedValue());\n        }\n\n        if (!useNativePropsInFabric) {\n          // Check 5: setNativeProps are disabled.\n          return scheduleUpdate();\n        }\n\n        // This is a Fabric instance and setNativeProps is supported.\n\n        // $FlowIgnore[not-a-function] - Assume it's still a function.\n        // $FlowFixMe[incompatible-use]\n        instance.setNativeProps(node.__getAnimatedValue());\n\n        // Keeping state of Fiber tree and Shadow tree in sync.\n        //\n        // This is done by calling `scheduleUpdate` which will trigger a commit.\n        // However, React commit is not fast enough to drive animations.\n        // This is where setNativeProps comes in handy but the state between\n        // Fiber tree and Shadow tree needs to be kept in sync.\n        // The goal is to call `scheduleUpdate` as little as possible to maintain\n        // performance but frequently enough to keep state in sync.\n        // Debounce is set to 48ms, which is 3 * the duration of a frame.\n        // 3 frames was the highest value where flickering state was not observed.\n        if (timerRef.current != null) {\n          clearTimeout(timerRef.current);\n        }\n        timerRef.current = setTimeout(() => {\n          timerRef.current = null;\n          scheduleUpdate();\n        }, 48);\n      };\n\n      const target = getEventTarget(instance);\n      const events = [];\n\n      for (const propName in props) {\n        // $FlowFixMe[invalid-computed-prop]\n        const propValue = props[propName];\n        if (propValue instanceof AnimatedEvent && propValue.__isNative) {\n          propValue.__attach(target, propName);\n          events.push([propName, propValue]);\n        }\n      }\n\n      return () => {\n        onUpdateRef.current = null;\n\n        for (const [propName, propValue] of events) {\n          propValue.__detach(target, propName);\n        }\n      };\n    },\n    [node, useNativePropsInFabric, props],\n  );\n  const callbackRef = useRefEffect<TInstance>(refEffect);\n\n  return [reduceAnimatedProps<TProps>(node, props), callbackRef];\n}\n\nfunction reduceAnimatedProps<TProps>(\n  node: AnimatedProps,\n  props: TProps,\n): ReducedProps<TProps> {\n  // Force `collapsable` to be false so that the native view is not flattened.\n  // Flattened views cannot be accurately referenced by the native driver.\n  return {\n    ...(ReactNativeFeatureFlags.enableAnimatedPropsMemo()\n      ? node.__getValueWithStaticProps(props)\n      : node.__getValue()),\n    collapsable: false,\n  };\n}\n\n/**\n * Manages the lifecycle of the supplied `AnimatedProps` by invoking `__attach`\n * and `__detach`. However, this is more complicated because `AnimatedProps`\n * uses reference counting to determine when to recursively detach its children\n * nodes. So in order to optimize this, we avoid detaching until the next attach\n * unless we are unmounting.\n */\nfunction useAnimatedPropsLifecycle_layoutEffects(node: AnimatedProps): void {\n  const prevNodeRef = useRef<?AnimatedProps>(null);\n  const isUnmountingRef = useRef<boolean>(false);\n\n  useEffect(() => {\n    // It is ok for multiple components to call `flushQueue` because it noops\n    // if the queue is empty. When multiple animated components are mounted at\n    // the same time. Only first component flushes the queue and the others will noop.\n    NativeAnimatedHelper.API.flushQueue();\n    let drivenAnimationEndedListener: ?EventSubscription = null;\n    if (node.__isNative) {\n      drivenAnimationEndedListener =\n        NativeAnimatedHelper.nativeEventEmitter.addListener(\n          'onUserDrivenAnimationEnded',\n          data => {\n            node.update();\n          },\n        );\n    }\n\n    return () => {\n      drivenAnimationEndedListener?.remove();\n    };\n  });\n\n  useLayoutEffect(() => {\n    isUnmountingRef.current = false;\n    return () => {\n      isUnmountingRef.current = true;\n    };\n  }, []);\n\n  useLayoutEffect(() => {\n    node.__attach();\n    if (prevNodeRef.current != null) {\n      const prevNode = prevNodeRef.current;\n      // TODO: Stop restoring default values (unless `reset` is called).\n      prevNode.__restoreDefaultValues();\n      prevNode.__detach();\n      prevNodeRef.current = null;\n    }\n    return () => {\n      if (isUnmountingRef.current) {\n        // NOTE: Do not restore default values on unmount, see *********.\n        node.__detach();\n      } else {\n        prevNodeRef.current = node;\n      }\n    };\n  }, [node]);\n}\n\n/**\n * Manages the lifecycle of the supplied `AnimatedProps` by invoking `__attach`\n * and `__detach`. However, this is more complicated because `AnimatedProps`\n * uses reference counting to determine when to recursively detach its children\n * nodes. So in order to optimize this, we avoid detaching until the next attach\n * unless we are unmounting.\n */\nfunction useAnimatedPropsLifecycle_insertionEffects(node: AnimatedProps): void {\n  const prevNodeRef = useRef<?AnimatedProps>(null);\n  const isUnmountingRef = useRef<boolean>(false);\n\n  useEffect(() => {\n    // It is ok for multiple components to call `flushQueue` because it noops\n    // if the queue is empty. When multiple animated components are mounted at\n    // the same time. Only first component flushes the queue and the others will noop.\n    NativeAnimatedHelper.API.flushQueue();\n    let drivenAnimationEndedListener: ?EventSubscription = null;\n    if (node.__isNative) {\n      drivenAnimationEndedListener =\n        NativeAnimatedHelper.nativeEventEmitter.addListener(\n          'onUserDrivenAnimationEnded',\n          data => {\n            node.update();\n          },\n        );\n    }\n\n    return () => {\n      drivenAnimationEndedListener?.remove();\n    };\n  });\n\n  useInsertionEffect(() => {\n    isUnmountingRef.current = false;\n    return () => {\n      isUnmountingRef.current = true;\n    };\n  }, []);\n\n  useInsertionEffect(() => {\n    node.__attach();\n    if (prevNodeRef.current != null) {\n      const prevNode = prevNodeRef.current;\n      // TODO: Stop restoring default values (unless `reset` is called).\n      prevNode.__restoreDefaultValues();\n      prevNode.__detach();\n      prevNodeRef.current = null;\n    }\n    return () => {\n      if (isUnmountingRef.current) {\n        // NOTE: Do not restore default values on unmount, see *********.\n        node.__detach();\n      } else {\n        prevNodeRef.current = node;\n      }\n    };\n  }, [node]);\n}\n\nfunction getEventTarget<TInstance>(instance: TInstance): TInstance {\n  return typeof instance === 'object' &&\n    typeof instance?.getScrollableNode === 'function'\n    ? // $FlowFixMe[incompatible-use] - Legacy instance assumptions.\n      instance.getScrollableNode()\n    : instance;\n}\n\n// $FlowFixMe[unclear-type] - Legacy instance assumptions.\nfunction isFabricInstance(instance: any): boolean {\n  return (\n    isFabricPublicInstance(instance) ||\n    // Some components have a setNativeProps function but aren't a host component\n    // such as lists like FlatList and SectionList. These should also use\n    // forceUpdate in Fabric since setNativeProps doesn't exist on the underlying\n    // host component. This crazy hack is essentially special casing those lists and\n    // ScrollView itself to use forceUpdate in Fabric.\n    // If these components end up using forwardRef then these hacks can go away\n    // as instance would actually be the underlying host component and the above check\n    // would be sufficient.\n    isFabricPublicInstance(instance?.getNativeScrollRef?.()) ||\n    isFabricPublicInstance(\n      instance?.getScrollResponder?.()?.getNativeScrollRef?.(),\n    )\n  );\n}\n"], "mappings": ";;;;;;AAaA,IAAAA,qBAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,qBAAA,GAAAD,OAAA;AACA,IAAAE,uBAAA,GAAAC,uBAAA,CAAAH,OAAA;AACA,IAAAI,+BAAA,GAAAJ,OAAA;AACA,IAAAK,aAAA,GAAAN,sBAAA,CAAAC,OAAA;AACA,IAAAM,cAAA,GAAAN,OAAA;AACA,IAAAO,cAAA,GAAAR,sBAAA,CAAAC,OAAA;AACA,IAAAQ,MAAA,GAAAR,OAAA;AAQe,SAAAS,yBAAAC,CAAA,6BAAAC,OAAA,mBAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,yBAAAC,CAAA,WAAAA,CAAA,GAAAG,CAAA,GAAAD,CAAA,KAAAF,CAAA;AAAA,SAAAP,wBAAAO,CAAA,EAAAE,CAAA,SAAAA,CAAA,IAAAF,CAAA,IAAAA,CAAA,CAAAI,UAAA,SAAAJ,CAAA,eAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,WAAAK,OAAA,EAAAL,CAAA,QAAAG,CAAA,GAAAJ,wBAAA,CAAAG,CAAA,OAAAC,CAAA,IAAAA,CAAA,CAAAG,GAAA,CAAAN,CAAA,UAAAG,CAAA,CAAAI,GAAA,CAAAP,CAAA,OAAAQ,CAAA,KAAAC,SAAA,UAAAC,CAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,CAAA,IAAAd,CAAA,oBAAAc,CAAA,OAAAC,cAAA,CAAAC,IAAA,CAAAhB,CAAA,EAAAc,CAAA,SAAAG,CAAA,GAAAP,CAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAb,CAAA,EAAAc,CAAA,UAAAG,CAAA,KAAAA,CAAA,CAAAV,GAAA,IAAAU,CAAA,CAAAC,GAAA,IAAAP,MAAA,CAAAC,cAAA,CAAAJ,CAAA,EAAAM,CAAA,EAAAG,CAAA,IAAAT,CAAA,CAAAM,CAAA,IAAAd,CAAA,CAAAc,CAAA,YAAAN,CAAA,CAAAH,OAAA,GAAAL,CAAA,EAAAG,CAAA,IAAAA,CAAA,CAAAe,GAAA,CAAAlB,CAAA,EAAAQ,CAAA,GAAAA,CAAA;AAWf,IAAMW,0BAA0B,GAC9B3B,uBAAuB,CAAC4B,uBAAuB,CAAC,CAAC,GAC7CC,0CAAoB,GACpBC,cAAO;AAEE,SAASC,gBAAgBA,CACtCC,KAAa,EACbC,SAAmC,EACoB;EACvD,IAAAC,WAAA,GAA2B,IAAAC,iBAAU,EAAe,UAAAC,KAAK;MAAA,OAAIA,KAAK,GAAG,CAAC;IAAA,GAAE,CAAC,CAAC;IAAAC,YAAA,OAAAC,eAAA,CAAAzB,OAAA,EAAAqB,WAAA;IAAjEK,cAAc,GAAAF,YAAA;EACvB,IAAMG,WAAW,GAAG,IAAAC,aAAM,EAAwB,IAAI,CAAC;EACvD,IAAMC,QAAQ,GAAG,IAAAD,aAAM,EAAmB,IAAI,CAAC;EAE/C,IAAME,kBAAkB,GAAG3C,uBAAuB,CAAC4C,uBAAuB,CAAC,CAAC,GACxEX,SAAS,GACT,IAAI;EAER,IAAMY,IAAI,GAAGlB,0BAA0B,CACrC;IAAA,OACE,IAAImB,sBAAa,CACfd,KAAK,EACL;MAAA,OAAMQ,WAAW,CAACO,OAAO,oBAAnBP,WAAW,CAACO,OAAO,CAAG,CAAC;IAAA,GAC7BJ,kBACF,CAAC;EAAA,GACH,CAACA,kBAAkB,EAAEX,KAAK,CAC5B,CAAC;EAED,IAAMgB,sBAAsB,GAC1BhD,uBAAuB,CAACiD,+BAA+B,CAAC,CAAC;EAE3D,IAAMC,yBAAyB,GAC7BlD,uBAAuB,CAACmD,gCAAgC,CAAC,CAAC,GACtDC,0CAA0C,GAC1CC,uCAAuC;EAE7CH,yBAAyB,CAACL,IAAI,CAAC;EAe/B,IAAMS,SAAS,GAAG,IAAAC,kBAAW,EAC3B,UAACC,QAAmB,EAAK;IAGvBX,IAAI,CAACY,aAAa,CAACD,QAAQ,CAAC;IAK5BhB,WAAW,CAACO,OAAO,GAAG,YAAM;MAC1B,IAAIW,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,MAAM,EAAE;QAGnC,OAAOrB,cAAc,CAAC,CAAC;MACzB;MAEA,IAAMsB,YAAY,GAAGC,gBAAgB,CAACN,QAAQ,CAAC;MAC/C,IAAIX,IAAI,CAACkB,UAAU,EAAE;QAGnB,IAAIF,YAAY,EAAE;UAGhBtB,cAAc,CAAC,CAAC;QAClB;QACA;MACF;MAEA,IACE,OAAOiB,QAAQ,KAAK,QAAQ,IAC5B,QAAOA,QAAQ,oBAARA,QAAQ,CAAEQ,cAAc,MAAK,UAAU,EAC9C;QAEA,OAAOzB,cAAc,CAAC,CAAC;MACzB;MAEA,IAAI,CAACsB,YAAY,EAAE;QAIjB,OAAOL,QAAQ,CAACQ,cAAc,CAACnB,IAAI,CAACoB,kBAAkB,CAAC,CAAC,CAAC;MAC3D;MAEA,IAAI,CAACjB,sBAAsB,EAAE;QAE3B,OAAOT,cAAc,CAAC,CAAC;MACzB;MAMAiB,QAAQ,CAACQ,cAAc,CAACnB,IAAI,CAACoB,kBAAkB,CAAC,CAAC,CAAC;MAYlD,IAAIvB,QAAQ,CAACK,OAAO,IAAI,IAAI,EAAE;QAC5BmB,YAAY,CAACxB,QAAQ,CAACK,OAAO,CAAC;MAChC;MACAL,QAAQ,CAACK,OAAO,GAAGoB,UAAU,CAAC,YAAM;QAClCzB,QAAQ,CAACK,OAAO,GAAG,IAAI;QACvBR,cAAc,CAAC,CAAC;MAClB,CAAC,EAAE,EAAE,CAAC;IACR,CAAC;IAED,IAAM6B,MAAM,GAAGC,cAAc,CAACb,QAAQ,CAAC;IACvC,IAAMc,MAAM,GAAG,EAAE;IAEjB,KAAK,IAAMC,QAAQ,IAAIvC,KAAK,EAAE;MAE5B,IAAMwC,SAAS,GAAGxC,KAAK,CAACuC,QAAQ,CAAC;MACjC,IAAIC,SAAS,YAAYC,4BAAa,IAAID,SAAS,CAACT,UAAU,EAAE;QAC9DS,SAAS,CAACE,QAAQ,CAACN,MAAM,EAAEG,QAAQ,CAAC;QACpCD,MAAM,CAACK,IAAI,CAAC,CAACJ,QAAQ,EAAEC,SAAS,CAAC,CAAC;MACpC;IACF;IAEA,OAAO,YAAM;MACXhC,WAAW,CAACO,OAAO,GAAG,IAAI;MAE1B,SAAA6B,IAAA,IAAoCN,MAAM,EAAE;QAAA,IAAAO,KAAA,OAAAvC,eAAA,CAAAzB,OAAA,EAAA+D,IAAA;QAAA,IAAhCL,SAAQ,GAAAM,KAAA;QAAA,IAAEL,UAAS,GAAAK,KAAA;QAC7BL,UAAS,CAACM,QAAQ,CAACV,MAAM,EAAEG,SAAQ,CAAC;MACtC;IACF,CAAC;EACH,CAAC,EACD,CAAC1B,IAAI,EAAEG,sBAAsB,EAAEhB,KAAK,CACtC,CAAC;EACD,IAAM+C,WAAW,GAAG,IAAAC,qBAAY,EAAY1B,SAAS,CAAC;EAEtD,OAAO,CAAC2B,mBAAmB,CAASpC,IAAI,EAAEb,KAAK,CAAC,EAAE+C,WAAW,CAAC;AAChE;AAEA,SAASE,mBAAmBA,CAC1BpC,IAAmB,EACnBb,KAAa,EACS;EAGtB,OAAAb,MAAA,CAAA+D,MAAA,KACMlF,uBAAuB,CAAC4B,uBAAuB,CAAC,CAAC,GACjDiB,IAAI,CAACsC,yBAAyB,CAACnD,KAAK,CAAC,GACrCa,IAAI,CAACuC,UAAU,CAAC,CAAC;IACrBC,WAAW,EAAE;EAAK;AAEtB;AASA,SAAShC,uCAAuCA,CAACR,IAAmB,EAAQ;EAC1E,IAAMyC,WAAW,GAAG,IAAA7C,aAAM,EAAiB,IAAI,CAAC;EAChD,IAAM8C,eAAe,GAAG,IAAA9C,aAAM,EAAU,KAAK,CAAC;EAE9C,IAAA+C,gBAAS,EAAC,YAAM;IAIdC,6BAAoB,CAACC,GAAG,CAACC,UAAU,CAAC,CAAC;IACrC,IAAIC,4BAAgD,GAAG,IAAI;IAC3D,IAAI/C,IAAI,CAACkB,UAAU,EAAE;MACnB6B,4BAA4B,GAC1BH,6BAAoB,CAACI,kBAAkB,CAACC,WAAW,CACjD,4BAA4B,EAC5B,UAAAC,IAAI,EAAI;QACNlD,IAAI,CAACmD,MAAM,CAAC,CAAC;MACf,CACF,CAAC;IACL;IAEA,OAAO,YAAM;MAAA,IAAAC,qBAAA;MACX,CAAAA,qBAAA,GAAAL,4BAA4B,aAA5BK,qBAAA,CAA8BC,MAAM,CAAC,CAAC;IACxC,CAAC;EACH,CAAC,CAAC;EAEF,IAAAC,sBAAe,EAAC,YAAM;IACpBZ,eAAe,CAACxC,OAAO,GAAG,KAAK;IAC/B,OAAO,YAAM;MACXwC,eAAe,CAACxC,OAAO,GAAG,IAAI;IAChC,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,IAAAoD,sBAAe,EAAC,YAAM;IACpBtD,IAAI,CAAC6B,QAAQ,CAAC,CAAC;IACf,IAAIY,WAAW,CAACvC,OAAO,IAAI,IAAI,EAAE;MAC/B,IAAMqD,QAAQ,GAAGd,WAAW,CAACvC,OAAO;MAEpCqD,QAAQ,CAACC,sBAAsB,CAAC,CAAC;MACjCD,QAAQ,CAACtB,QAAQ,CAAC,CAAC;MACnBQ,WAAW,CAACvC,OAAO,GAAG,IAAI;IAC5B;IACA,OAAO,YAAM;MACX,IAAIwC,eAAe,CAACxC,OAAO,EAAE;QAE3BF,IAAI,CAACiC,QAAQ,CAAC,CAAC;MACjB,CAAC,MAAM;QACLQ,WAAW,CAACvC,OAAO,GAAGF,IAAI;MAC5B;IACF,CAAC;EACH,CAAC,EAAE,CAACA,IAAI,CAAC,CAAC;AACZ;AASA,SAASO,0CAA0CA,CAACP,IAAmB,EAAQ;EAC7E,IAAMyC,WAAW,GAAG,IAAA7C,aAAM,EAAiB,IAAI,CAAC;EAChD,IAAM8C,eAAe,GAAG,IAAA9C,aAAM,EAAU,KAAK,CAAC;EAE9C,IAAA+C,gBAAS,EAAC,YAAM;IAIdC,6BAAoB,CAACC,GAAG,CAACC,UAAU,CAAC,CAAC;IACrC,IAAIC,4BAAgD,GAAG,IAAI;IAC3D,IAAI/C,IAAI,CAACkB,UAAU,EAAE;MACnB6B,4BAA4B,GAC1BH,6BAAoB,CAACI,kBAAkB,CAACC,WAAW,CACjD,4BAA4B,EAC5B,UAAAC,IAAI,EAAI;QACNlD,IAAI,CAACmD,MAAM,CAAC,CAAC;MACf,CACF,CAAC;IACL;IAEA,OAAO,YAAM;MAAA,IAAAM,sBAAA;MACX,CAAAA,sBAAA,GAAAV,4BAA4B,aAA5BU,sBAAA,CAA8BJ,MAAM,CAAC,CAAC;IACxC,CAAC;EACH,CAAC,CAAC;EAEF,IAAAK,yBAAkB,EAAC,YAAM;IACvBhB,eAAe,CAACxC,OAAO,GAAG,KAAK;IAC/B,OAAO,YAAM;MACXwC,eAAe,CAACxC,OAAO,GAAG,IAAI;IAChC,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,IAAAwD,yBAAkB,EAAC,YAAM;IACvB1D,IAAI,CAAC6B,QAAQ,CAAC,CAAC;IACf,IAAIY,WAAW,CAACvC,OAAO,IAAI,IAAI,EAAE;MAC/B,IAAMqD,QAAQ,GAAGd,WAAW,CAACvC,OAAO;MAEpCqD,QAAQ,CAACC,sBAAsB,CAAC,CAAC;MACjCD,QAAQ,CAACtB,QAAQ,CAAC,CAAC;MACnBQ,WAAW,CAACvC,OAAO,GAAG,IAAI;IAC5B;IACA,OAAO,YAAM;MACX,IAAIwC,eAAe,CAACxC,OAAO,EAAE;QAE3BF,IAAI,CAACiC,QAAQ,CAAC,CAAC;MACjB,CAAC,MAAM;QACLQ,WAAW,CAACvC,OAAO,GAAGF,IAAI;MAC5B;IACF,CAAC;EACH,CAAC,EAAE,CAACA,IAAI,CAAC,CAAC;AACZ;AAEA,SAASwB,cAAcA,CAAYb,QAAmB,EAAa;EACjE,OAAO,OAAOA,QAAQ,KAAK,QAAQ,IACjC,QAAOA,QAAQ,oBAARA,QAAQ,CAAEgD,iBAAiB,MAAK,UAAU,GAE/ChD,QAAQ,CAACgD,iBAAiB,CAAC,CAAC,GAC5BhD,QAAQ;AACd;AAGA,SAASM,gBAAgBA,CAACN,QAAa,EAAW;EAAA,IAAAiD,qBAAA;EAChD,OACE,IAAAC,gDAAsB,EAAClD,QAAQ,CAAC,IAShC,IAAAkD,gDAAsB,EAAClD,QAAQ,YAARA,QAAQ,CAAEmD,kBAAkB,oBAA5BnD,QAAQ,CAAEmD,kBAAkB,CAAG,CAAC,CAAC,IACxD,IAAAD,gDAAsB,EACpBlD,QAAQ,YAARA,QAAQ,CAAEoD,kBAAkB,aAAAH,qBAAA,GAA5BjD,QAAQ,CAAEoD,kBAAkB,CAAG,CAAC,aAAhCH,qBAAA,CAAkCE,kBAAkB,oBAApDF,qBAAA,CAAkCE,kBAAkB,CAAG,CACzD,CAAC;AAEL", "ignoreList": []}