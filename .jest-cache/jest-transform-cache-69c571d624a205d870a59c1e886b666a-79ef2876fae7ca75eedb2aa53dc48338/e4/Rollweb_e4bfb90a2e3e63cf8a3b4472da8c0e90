fd2707d3967966d159af4ee0db991119
'use strict';

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.RollOutData = exports.RollOut = exports.RollInData = exports.RollIn = void 0;
var _animationParser = require("../animationParser.js");
var DEFAULT_ROLL_TIME = 0.3;
var RollInData = exports.RollInData = {
  RollInLeft: {
    name: 'RollInLeft',
    style: {
      0: {
        transform: [{
          translateX: '-100vw',
          rotate: '-180deg'
        }]
      },
      100: {
        transform: [{
          translateX: '0vw',
          rotate: '0deg'
        }]
      }
    },
    duration: DEFAULT_ROLL_TIME
  },
  RollInRight: {
    name: 'RollInRight',
    style: {
      0: {
        transform: [{
          translateX: '100vw',
          rotate: '180deg'
        }]
      },
      100: {
        transform: [{
          translateX: '0vw',
          rotate: '0deg'
        }]
      }
    },
    duration: DEFAULT_ROLL_TIME
  }
};
var RollOutData = exports.RollOutData = {
  RollOutLeft: {
    name: 'RollOutLeft',
    style: {
      0: {
        transform: [{
          translateX: '0vw',
          rotate: '0deg'
        }]
      },
      100: {
        transform: [{
          translateX: '-100vw',
          rotate: '-180deg'
        }]
      }
    },
    duration: DEFAULT_ROLL_TIME
  },
  RollOutRight: {
    name: 'RollOutRight',
    style: {
      0: {
        transform: [{
          translateX: '0vw',
          rotate: '0deg'
        }]
      },
      100: {
        transform: [{
          translateX: '100vw',
          rotate: '180deg'
        }]
      }
    },
    duration: DEFAULT_ROLL_TIME
  }
};
var RollIn = exports.RollIn = {
  RollInLeft: {
    style: (0, _animationParser.convertAnimationObjectToKeyframes)(RollInData.RollInLeft),
    duration: RollInData.RollInLeft.duration
  },
  RollInRight: {
    style: (0, _animationParser.convertAnimationObjectToKeyframes)(RollInData.RollInRight),
    duration: RollInData.RollInRight.duration
  }
};
var RollOut = exports.RollOut = {
  RollOutLeft: {
    style: (0, _animationParser.convertAnimationObjectToKeyframes)(RollOutData.RollOutLeft),
    duration: RollOutData.RollOutLeft.duration
  },
  RollOutRight: {
    style: (0, _animationParser.convertAnimationObjectToKeyframes)(RollOutData.RollOutRight),
    duration: RollOutData.RollOutRight.duration
  }
};
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJPYmplY3QiLCJkZWZpbmVQcm9wZXJ0eSIsImV4cG9ydHMiLCJ2YWx1ZSIsIlJvbGxPdXREYXRhIiwiUm9sbE91dCIsIlJvbGxJbkRhdGEiLCJSb2xsSW4iLCJfYW5pbWF0aW9uUGFyc2VyIiwicmVxdWlyZSIsIkRFRkFVTFRfUk9MTF9USU1FIiwiUm9sbEluTGVmdCIsIm5hbWUiLCJzdHlsZSIsInRyYW5zZm9ybSIsInRyYW5zbGF0ZVgiLCJyb3RhdGUiLCJkdXJhdGlvbiIsIlJvbGxJblJpZ2h0IiwiUm9sbE91dExlZnQiLCJSb2xsT3V0UmlnaHQiLCJjb252ZXJ0QW5pbWF0aW9uT2JqZWN0VG9LZXlmcmFtZXMiXSwic291cmNlcyI6WyIuLi8uLi8uLi8uLi8uLi9zcmMvbGF5b3V0UmVhbmltYXRpb24vd2ViL2FuaW1hdGlvbi9Sb2xsLndlYi50cyJdLCJzb3VyY2VzQ29udGVudCI6W251bGxdLCJtYXBwaW5ncyI6IkFBQUEsWUFBWTs7QUFBQUEsTUFBQSxDQUFBQyxjQUFBLENBQUFDLE9BQUE7RUFBQUMsS0FBQTtBQUFBO0FBQUFELE9BQUEsQ0FBQUUsV0FBQSxHQUFBRixPQUFBLENBQUFHLE9BQUEsR0FBQUgsT0FBQSxDQUFBSSxVQUFBLEdBQUFKLE9BQUEsQ0FBQUssTUFBQTtBQUNaLElBQUFDLGdCQUFBLEdBQUFDLE9BQUE7QUFFQSxJQUFNQyxpQkFBaUIsR0FBRyxHQUFHO0FBRXRCLElBQU1KLFVBQVUsR0FBQUosT0FBQSxDQUFBSSxVQUFBLEdBQUc7RUFDeEJLLFVBQVUsRUFBRTtJQUNWQyxJQUFJLEVBQUUsWUFBWTtJQUNsQkMsS0FBSyxFQUFFO01BQ0wsQ0FBQyxFQUFFO1FBQ0RDLFNBQVMsRUFBRSxDQUFDO1VBQUVDLFVBQVUsRUFBRSxRQUFRO1VBQUVDLE1BQU0sRUFBRTtRQUFVLENBQUM7TUFDekQsQ0FBQztNQUNELEdBQUcsRUFBRTtRQUNIRixTQUFTLEVBQUUsQ0FBQztVQUFFQyxVQUFVLEVBQUUsS0FBSztVQUFFQyxNQUFNLEVBQUU7UUFBTyxDQUFDO01BQ25EO0lBQ0YsQ0FBQztJQUNEQyxRQUFRLEVBQUVQO0VBQ1osQ0FBQztFQUVEUSxXQUFXLEVBQUU7SUFDWE4sSUFBSSxFQUFFLGFBQWE7SUFDbkJDLEtBQUssRUFBRTtNQUNMLENBQUMsRUFBRTtRQUNEQyxTQUFTLEVBQUUsQ0FBQztVQUFFQyxVQUFVLEVBQUUsT0FBTztVQUFFQyxNQUFNLEVBQUU7UUFBUyxDQUFDO01BQ3ZELENBQUM7TUFDRCxHQUFHLEVBQUU7UUFDSEYsU0FBUyxFQUFFLENBQUM7VUFBRUMsVUFBVSxFQUFFLEtBQUs7VUFBRUMsTUFBTSxFQUFFO1FBQU8sQ0FBQztNQUNuRDtJQUNGLENBQUM7SUFDREMsUUFBUSxFQUFFUDtFQUNaO0FBQ0YsQ0FBQztBQUVNLElBQU1OLFdBQVcsR0FBQUYsT0FBQSxDQUFBRSxXQUFBLEdBQUc7RUFDekJlLFdBQVcsRUFBRTtJQUNYUCxJQUFJLEVBQUUsYUFBYTtJQUNuQkMsS0FBSyxFQUFFO01BQ0wsQ0FBQyxFQUFFO1FBQ0RDLFNBQVMsRUFBRSxDQUFDO1VBQUVDLFVBQVUsRUFBRSxLQUFLO1VBQUVDLE1BQU0sRUFBRTtRQUFPLENBQUM7TUFDbkQsQ0FBQztNQUNELEdBQUcsRUFBRTtRQUNIRixTQUFTLEVBQUUsQ0FBQztVQUFFQyxVQUFVLEVBQUUsUUFBUTtVQUFFQyxNQUFNLEVBQUU7UUFBVSxDQUFDO01BQ3pEO0lBQ0YsQ0FBQztJQUNEQyxRQUFRLEVBQUVQO0VBQ1osQ0FBQztFQUVEVSxZQUFZLEVBQUU7SUFDWlIsSUFBSSxFQUFFLGNBQWM7SUFDcEJDLEtBQUssRUFBRTtNQUNMLENBQUMsRUFBRTtRQUNEQyxTQUFTLEVBQUUsQ0FBQztVQUFFQyxVQUFVLEVBQUUsS0FBSztVQUFFQyxNQUFNLEVBQUU7UUFBTyxDQUFDO01BQ25ELENBQUM7TUFDRCxHQUFHLEVBQUU7UUFDSEYsU0FBUyxFQUFFLENBQUM7VUFBRUMsVUFBVSxFQUFFLE9BQU87VUFBRUMsTUFBTSxFQUFFO1FBQVMsQ0FBQztNQUN2RDtJQUNGLENBQUM7SUFDREMsUUFBUSxFQUFFUDtFQUNaO0FBQ0YsQ0FBQztBQUVNLElBQU1ILE1BQU0sR0FBQUwsT0FBQSxDQUFBSyxNQUFBLEdBQUc7RUFDcEJJLFVBQVUsRUFBRTtJQUNWRSxLQUFLLEVBQUUsSUFBQVEsa0RBQWlDLEVBQUNmLFVBQVUsQ0FBQ0ssVUFBVSxDQUFDO0lBQy9ETSxRQUFRLEVBQUVYLFVBQVUsQ0FBQ0ssVUFBVSxDQUFDTTtFQUNsQyxDQUFDO0VBQ0RDLFdBQVcsRUFBRTtJQUNYTCxLQUFLLEVBQUUsSUFBQVEsa0RBQWlDLEVBQUNmLFVBQVUsQ0FBQ1ksV0FBVyxDQUFDO0lBQ2hFRCxRQUFRLEVBQUVYLFVBQVUsQ0FBQ1ksV0FBVyxDQUFDRDtFQUNuQztBQUNGLENBQUM7QUFFTSxJQUFNWixPQUFPLEdBQUFILE9BQUEsQ0FBQUcsT0FBQSxHQUFHO0VBQ3JCYyxXQUFXLEVBQUU7SUFDWE4sS0FBSyxFQUFFLElBQUFRLGtEQUFpQyxFQUFDakIsV0FBVyxDQUFDZSxXQUFXLENBQUM7SUFDakVGLFFBQVEsRUFBRWIsV0FBVyxDQUFDZSxXQUFXLENBQUNGO0VBQ3BDLENBQUM7RUFDREcsWUFBWSxFQUFFO0lBQ1pQLEtBQUssRUFBRSxJQUFBUSxrREFBaUMsRUFBQ2pCLFdBQVcsQ0FBQ2dCLFlBQVksQ0FBQztJQUNsRUgsUUFBUSxFQUFFYixXQUFXLENBQUNnQixZQUFZLENBQUNIO0VBQ3JDO0FBQ0YsQ0FBQyIsImlnbm9yZUxpc3QiOltdfQ==