{"version": 3, "names": ["Object", "defineProperty", "exports", "value", "RollOutData", "RollOut", "RollInData", "RollIn", "_animation<PERSON><PERSON>er", "require", "DEFAULT_ROLL_TIME", "RollInLeft", "name", "style", "transform", "translateX", "rotate", "duration", "RollInRight", "RollOutLeft", "RollOutRight", "convertAnimationObjectToKeyframes"], "sources": ["../../../../../src/layoutReanimation/web/animation/Roll.web.ts"], "sourcesContent": [null], "mappings": "AAAA,YAAY;;AAAAA,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,KAAA;AAAA;AAAAD,OAAA,CAAAE,WAAA,GAAAF,OAAA,CAAAG,OAAA,GAAAH,OAAA,CAAAI,UAAA,GAAAJ,OAAA,CAAAK,MAAA;AACZ,IAAAC,gBAAA,GAAAC,OAAA;AAEA,IAAMC,iBAAiB,GAAG,GAAG;AAEtB,IAAMJ,UAAU,GAAAJ,OAAA,CAAAI,UAAA,GAAG;EACxBK,UAAU,EAAE;IACVC,IAAI,EAAE,YAAY;IAClBC,KAAK,EAAE;MACL,CAAC,EAAE;QACDC,SAAS,EAAE,CAAC;UAAEC,UAAU,EAAE,QAAQ;UAAEC,MAAM,EAAE;QAAU,CAAC;MACzD,CAAC;MACD,GAAG,EAAE;QACHF,SAAS,EAAE,CAAC;UAAEC,UAAU,EAAE,KAAK;UAAEC,MAAM,EAAE;QAAO,CAAC;MACnD;IACF,CAAC;IACDC,QAAQ,EAAEP;EACZ,CAAC;EAEDQ,WAAW,EAAE;IACXN,IAAI,EAAE,aAAa;IACnBC,KAAK,EAAE;MACL,CAAC,EAAE;QACDC,SAAS,EAAE,CAAC;UAAEC,UAAU,EAAE,OAAO;UAAEC,MAAM,EAAE;QAAS,CAAC;MACvD,CAAC;MACD,GAAG,EAAE;QACHF,SAAS,EAAE,CAAC;UAAEC,UAAU,EAAE,KAAK;UAAEC,MAAM,EAAE;QAAO,CAAC;MACnD;IACF,CAAC;IACDC,QAAQ,EAAEP;EACZ;AACF,CAAC;AAEM,IAAMN,WAAW,GAAAF,OAAA,CAAAE,WAAA,GAAG;EACzBe,WAAW,EAAE;IACXP,IAAI,EAAE,aAAa;IACnBC,KAAK,EAAE;MACL,CAAC,EAAE;QACDC,SAAS,EAAE,CAAC;UAAEC,UAAU,EAAE,KAAK;UAAEC,MAAM,EAAE;QAAO,CAAC;MACnD,CAAC;MACD,GAAG,EAAE;QACHF,SAAS,EAAE,CAAC;UAAEC,UAAU,EAAE,QAAQ;UAAEC,MAAM,EAAE;QAAU,CAAC;MACzD;IACF,CAAC;IACDC,QAAQ,EAAEP;EACZ,CAAC;EAEDU,YAAY,EAAE;IACZR,IAAI,EAAE,cAAc;IACpBC,KAAK,EAAE;MACL,CAAC,EAAE;QACDC,SAAS,EAAE,CAAC;UAAEC,UAAU,EAAE,KAAK;UAAEC,MAAM,EAAE;QAAO,CAAC;MACnD,CAAC;MACD,GAAG,EAAE;QACHF,SAAS,EAAE,CAAC;UAAEC,UAAU,EAAE,OAAO;UAAEC,MAAM,EAAE;QAAS,CAAC;MACvD;IACF,CAAC;IACDC,QAAQ,EAAEP;EACZ;AACF,CAAC;AAEM,IAAMH,MAAM,GAAAL,OAAA,CAAAK,MAAA,GAAG;EACpBI,UAAU,EAAE;IACVE,KAAK,EAAE,IAAAQ,kDAAiC,EAACf,UAAU,CAACK,UAAU,CAAC;IAC/DM,QAAQ,EAAEX,UAAU,CAACK,UAAU,CAACM;EAClC,CAAC;EACDC,WAAW,EAAE;IACXL,KAAK,EAAE,IAAAQ,kDAAiC,EAACf,UAAU,CAACY,WAAW,CAAC;IAChED,QAAQ,EAAEX,UAAU,CAACY,WAAW,CAACD;EACnC;AACF,CAAC;AAEM,IAAMZ,OAAO,GAAAH,OAAA,CAAAG,OAAA,GAAG;EACrBc,WAAW,EAAE;IACXN,KAAK,EAAE,IAAAQ,kDAAiC,EAACjB,WAAW,CAACe,WAAW,CAAC;IACjEF,QAAQ,EAAEb,WAAW,CAACe,WAAW,CAACF;EACpC,CAAC;EACDG,YAAY,EAAE;IACZP,KAAK,EAAE,IAAAQ,kDAAiC,EAACjB,WAAW,CAACgB,YAAY,CAAC;IAClEH,QAAQ,EAAEb,WAAW,CAACgB,YAAY,CAACH;EACrC;AACF,CAAC", "ignoreList": []}