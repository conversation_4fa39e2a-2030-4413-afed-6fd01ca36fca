{"version": 3, "names": ["_interopRequireDefault", "require", "Object", "defineProperty", "exports", "value", "EntryExitTransition", "_slicedToArray2", "_config", "ExitingFinalStep", "EnteringStartStep", "addTransformToKeepPosition", "keyframeStyleData", "animationStyle", "transformData", "isExiting", "_ref", "entries", "_ref2", "default", "timestamp", "styles", "transform", "undefined", "unshift", "newTimestamp", "parseInt", "index", "Math", "min", "hideComponentBetweenAnimations", "opacityInStep", "Map", "opacity", "set", "_ref3", "_ref4", "step", "assign", "name", "transitionData", "exitingAnimationData", "structuredClone", "AnimationsData", "exiting", "enteringAnimationData", "entering", "additionalExitingData", "translateX", "translateY", "scale", "scaleX", "scaleY", "additionalEnteringData", "keyframeData", "style", "duration"], "sources": ["../../../../../src/layoutReanimation/web/transition/EntryExit.web.ts"], "sourcesContent": [null], "mappings": "AAAA,YAAY;;AAAA,IAAAA,sBAAA,GAAAC,OAAA;AAAAC,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,KAAA;AAAA;AAAAD,OAAA,CAAAE,mBAAA,GAAAA,mBAAA;AAAA,IAAAC,eAAA,GAAAP,sBAAA,CAAAC,OAAA;AAMZ,IAAAO,OAAA,GAAAP,OAAA;AAEA,IAAMQ,gBAAgB,GAAG,EAAE;AAC3B,IAAMC,iBAAiB,GAAG,EAAE;AAY5B,SAASC,0BAA0BA,CACjCC,iBAAiD,EACjDC,cAA8C,EAC9CC,aAA4B,EAC5BC,SAAkB,EAClB;EACA,SAAAC,IAAA,IAAkCd,MAAM,CAACe,OAAO,CAACJ,cAAc,CAAC,EAAE;IAAA,IAAAK,KAAA,OAAAX,eAAA,CAAAY,OAAA,EAAAH,IAAA;IAAA,IAAtDI,SAAS,GAAAF,KAAA;IAAA,IAAEG,MAAM,GAAAH,KAAA;IAC3B,IAAIG,MAAM,CAACC,SAAS,KAAKC,SAAS,EAAE;MAElCF,MAAM,CAACC,SAAS,CAACE,OAAO,CAACV,aAAa,CAAC;IACzC,CAAC,MAAM;MAELO,MAAM,CAACC,SAAS,GAAG,CAACR,aAAa,CAAC;IACpC;IAEA,IAAMW,YAAY,GAAGC,QAAQ,CAACN,SAAS,CAAC,GAAG,CAAC;IAC5C,IAAMO,KAAK,GAAGZ,SAAS,GACnBa,IAAI,CAACC,GAAG,CAACJ,YAAY,EAAEhB,gBAAgB,CAAC,GACxCgB,YAAY,GAAGf,iBAAiB;IAEpCE,iBAAiB,CAAC,GAAGe,KAAK,EAAE,CAAC,GAAGN,MAAM;EACxC;AACF;AAQA,SAASS,8BAA8BA,CACrClB,iBAAiD,EACjD;EAGA,IAAMmB,aAAa,GAAG,IAAIC,GAAG,CAAiB,CAAC;EAE/C,IAAIpB,iBAAiB,CAAC,CAAC,CAAC,CAACqB,OAAO,KAAKV,SAAS,EAAE;IAC9CQ,aAAa,CAACG,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC;IACxBH,aAAa,CAACG,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC;EAC1B;EAEA,IAAItB,iBAAiB,CAAC,EAAE,CAAC,CAACqB,OAAO,KAAKV,SAAS,EAAE;IAC/CQ,aAAa,CAACG,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC;IACxBH,aAAa,CAACG,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC;EAC1B;EAEA,SAAAC,KAAA,IAA8BJ,aAAa,EAAE;IAAA,IAAAK,KAAA,OAAA7B,eAAA,CAAAY,OAAA,EAAAgB,KAAA;IAAA,IAAjCE,IAAI,GAAAD,KAAA;IAAA,IAAEH,OAAO,GAAAG,KAAA;IACvBxB,iBAAiB,CAACyB,IAAI,CAAC,GAAAnC,MAAA,CAAAoC,MAAA,KAClB1B,iBAAiB,CAACyB,IAAI,CAAC;MAC1BJ,OAAA,EAAAA;IAAA,EACD;EACH;AACF;AAEO,SAAS3B,mBAAmBA,CACjCiC,IAAY,EACZC,cAA8B,EAC9B;EACA,IAAMC,oBAAoB,GAAGC,eAAe,CAC1CC,sBAAc,CAACH,cAAc,CAACI,OAAO,CACvC,CAAC;EACD,IAAMC,qBAAqB,GAAGH,eAAe,CAC3CC,sBAAc,CAACH,cAAc,CAACM,QAAQ,CACxC,CAAC;EAED,IAAMC,qBAAoC,GAAG;IAC3CC,UAAU,EAAE,GAAGR,cAAc,CAACQ,UAAU,IAAI;IAC5CC,UAAU,EAAE,GAAGT,cAAc,CAACS,UAAU,IAAI;IAC5CC,KAAK,EAAE,GAAGV,cAAc,CAACW,MAAM,IAAIX,cAAc,CAACY,MAAM;EAC1D,CAAC;EAED,IAAMC,sBAAqC,GAAG;IAC5CL,UAAU,EAAE,KAAK;IACjBC,UAAU,EAAE,KAAK;IACjBC,KAAK,EAAE;EACT,CAAC;EAED,IAAMI,YAA2B,GAAG;IAClCf,IAAI,EAAJA,IAAI;IACJgB,KAAK,EAAE,CAAC,CAAC;IACTC,QAAQ,EAAE;EACZ,CAAC;EAED7C,0BAA0B,CACxB2C,YAAY,CAACC,KAAK,EAClBd,oBAAoB,CAACc,KAAK,EAC1BR,qBAAqB,EACrB,IACF,CAAC;EAEDpC,0BAA0B,CACxB2C,YAAY,CAACC,KAAK,EAClBV,qBAAqB,CAACU,KAAK,EAC3BF,sBAAsB,EACtB,KACF,CAAC;EAEDvB,8BAA8B,CAACwB,YAAY,CAACC,KAAK,CAAC;EAElD,OAAOD,YAAY;AACrB", "ignoreList": []}