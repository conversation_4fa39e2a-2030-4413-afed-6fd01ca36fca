{"version": 3, "names": ["Object", "defineProperty", "exports", "value", "FlipOutData", "FlipOut", "FlipInData", "FlipIn", "_animation<PERSON><PERSON>er", "require", "DEFAULT_FLIP_TIME", "FlipInYRight", "name", "style", "transform", "perspective", "rotateY", "translateX", "duration", "FlipInYLeft", "FlipInXUp", "rotateX", "translateY", "FlipInXDown", "FlipInEasyX", "FlipInEasyY", "FlipOutYRight", "FlipOutYLeft", "FlipOutXUp", "FlipOutXDown", "FlipOutEasyX", "FlipOutEasyY", "convertAnimationObjectToKeyframes"], "sources": ["../../../../../src/layoutReanimation/web/animation/Flip.web.ts"], "sourcesContent": [null], "mappings": "AAAA,YAAY;;AAAAA,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,KAAA;AAAA;AAAAD,OAAA,CAAAE,WAAA,GAAAF,OAAA,CAAAG,OAAA,GAAAH,OAAA,CAAAI,UAAA,GAAAJ,OAAA,CAAAK,MAAA;AACZ,IAAAC,gBAAA,GAAAC,OAAA;AAEA,IAAMC,iBAAiB,GAAG,GAAG;AAEtB,IAAMJ,UAAU,GAAAJ,OAAA,CAAAI,UAAA,GAAG;EACxBK,YAAY,EAAE;IACZC,IAAI,EAAE,cAAc;IACpBC,KAAK,EAAE;MACL,CAAC,EAAE;QACDC,SAAS,EAAE,CACT;UACEC,WAAW,EAAE,OAAO;UACpBC,OAAO,EAAE,OAAO;UAChBC,UAAU,EAAE;QACd,CAAC;MAEL,CAAC;MACD,GAAG,EAAE;QACHH,SAAS,EAAE,CACT;UACEC,WAAW,EAAE,OAAO;UACpBC,OAAO,EAAE,MAAM;UACfC,UAAU,EAAE;QACd,CAAC;MAEL;IACF,CAAC;IACDC,QAAQ,EAAER;EACZ,CAAC;EAEDS,WAAW,EAAE;IACXP,IAAI,EAAE,aAAa;IACnBC,KAAK,EAAE;MACL,CAAC,EAAE;QACDC,SAAS,EAAE,CACT;UACEC,WAAW,EAAE,OAAO;UACpBC,OAAO,EAAE,QAAQ;UACjBC,UAAU,EAAE;QACd,CAAC;MAEL,CAAC;MACD,GAAG,EAAE;QACHH,SAAS,EAAE,CACT;UACEC,WAAW,EAAE,OAAO;UACpBC,OAAO,EAAE,MAAM;UACfC,UAAU,EAAE;QACd,CAAC;MAEL;IACF,CAAC;IACDC,QAAQ,EAAER;EACZ,CAAC;EAEDU,SAAS,EAAE;IACTR,IAAI,EAAE,WAAW;IACjBC,KAAK,EAAE;MACL,CAAC,EAAE;QACDC,SAAS,EAAE,CACT;UACEC,WAAW,EAAE,OAAO;UACpBM,OAAO,EAAE,OAAO;UAChBC,UAAU,EAAE;QACd,CAAC;MAEL,CAAC;MACD,GAAG,EAAE;QACHR,SAAS,EAAE,CACT;UACEC,WAAW,EAAE,OAAO;UACpBM,OAAO,EAAE,MAAM;UACfC,UAAU,EAAE;QACd,CAAC;MAEL;IACF,CAAC;IACDJ,QAAQ,EAAER;EACZ,CAAC;EAEDa,WAAW,EAAE;IACXX,IAAI,EAAE,aAAa;IACnBC,KAAK,EAAE;MACL,CAAC,EAAE;QACDC,SAAS,EAAE,CACT;UACEC,WAAW,EAAE,OAAO;UACpBM,OAAO,EAAE,QAAQ;UACjBC,UAAU,EAAE;QACd,CAAC;MAEL,CAAC;MACD,GAAG,EAAE;QACHR,SAAS,EAAE,CACT;UACEC,WAAW,EAAE,OAAO;UACpBM,OAAO,EAAE,MAAM;UACfC,UAAU,EAAE;QACd,CAAC;MAEL;IACF,CAAC;IACDJ,QAAQ,EAAER;EACZ,CAAC;EAEDc,WAAW,EAAE;IACXZ,IAAI,EAAE,aAAa;IACnBC,KAAK,EAAE;MACL,CAAC,EAAE;QAAEC,SAAS,EAAE,CAAC;UAAEC,WAAW,EAAE,OAAO;UAAEM,OAAO,EAAE;QAAQ,CAAC;MAAE,CAAC;MAC9D,GAAG,EAAE;QAAEP,SAAS,EAAE,CAAC;UAAEC,WAAW,EAAE,OAAO;UAAEM,OAAO,EAAE;QAAO,CAAC;MAAE;IAChE,CAAC;IACDH,QAAQ,EAAER;EACZ,CAAC;EAEDe,WAAW,EAAE;IACXb,IAAI,EAAE,aAAa;IACnBC,KAAK,EAAE;MACL,CAAC,EAAE;QAAEC,SAAS,EAAE,CAAC;UAAEC,WAAW,EAAE,OAAO;UAAEC,OAAO,EAAE;QAAQ,CAAC;MAAE,CAAC;MAC9D,GAAG,EAAE;QAAEF,SAAS,EAAE,CAAC;UAAEC,WAAW,EAAE,OAAO;UAAEC,OAAO,EAAE;QAAO,CAAC;MAAE;IAChE,CAAC;IACDE,QAAQ,EAAER;EACZ;AACF,CAAC;AAEM,IAAMN,WAAW,GAAAF,OAAA,CAAAE,WAAA,GAAG;EACzBsB,aAAa,EAAE;IACbd,IAAI,EAAE,eAAe;IACrBC,KAAK,EAAE;MACL,CAAC,EAAE;QACDC,SAAS,EAAE,CACT;UACEC,WAAW,EAAE,OAAO;UACpBC,OAAO,EAAE,MAAM;UACfC,UAAU,EAAE;QACd,CAAC;MAEL,CAAC;MACD,GAAG,EAAE;QACHH,SAAS,EAAE,CACT;UACEC,WAAW,EAAE,OAAO;UACpBC,OAAO,EAAE,OAAO;UAChBC,UAAU,EAAE;QACd,CAAC;MAEL;IACF,CAAC;IACDC,QAAQ,EAAER;EACZ,CAAC;EAEDiB,YAAY,EAAE;IACZf,IAAI,EAAE,cAAc;IACpBC,KAAK,EAAE;MACL,CAAC,EAAE;QACDC,SAAS,EAAE,CACT;UACEC,WAAW,EAAE,OAAO;UACpBC,OAAO,EAAE,MAAM;UACfC,UAAU,EAAE;QACd,CAAC;MAEL,CAAC;MACD,GAAG,EAAE;QACHH,SAAS,EAAE,CACT;UACEC,WAAW,EAAE,OAAO;UACpBC,OAAO,EAAE,QAAQ;UACjBC,UAAU,EAAE;QACd,CAAC;MAEL;IACF,CAAC;IACDC,QAAQ,EAAER;EACZ,CAAC;EAEDkB,UAAU,EAAE;IACVhB,IAAI,EAAE,YAAY;IAClBC,KAAK,EAAE;MACL,CAAC,EAAE;QACDC,SAAS,EAAE,CACT;UACEC,WAAW,EAAE,OAAO;UACpBM,OAAO,EAAE,MAAM;UACfC,UAAU,EAAE;QACd,CAAC;MAEL,CAAC;MACD,GAAG,EAAE;QACHR,SAAS,EAAE,CACT;UACEC,WAAW,EAAE,OAAO;UACpBM,OAAO,EAAE,OAAO;UAChBC,UAAU,EAAE;QACd,CAAC;MAEL;IACF,CAAC;IACDJ,QAAQ,EAAER;EACZ,CAAC;EAEDmB,YAAY,EAAE;IACZjB,IAAI,EAAE,cAAc;IACpBC,KAAK,EAAE;MACL,CAAC,EAAE;QACDC,SAAS,EAAE,CACT;UACEC,WAAW,EAAE,OAAO;UACpBM,OAAO,EAAE,MAAM;UACfC,UAAU,EAAE;QACd,CAAC;MAEL,CAAC;MACD,GAAG,EAAE;QACHR,SAAS,EAAE,CACT;UACEC,WAAW,EAAE,OAAO;UACpBM,OAAO,EAAE,QAAQ;UACjBC,UAAU,EAAE;QACd,CAAC;MAEL;IACF,CAAC;IACDJ,QAAQ,EAAER;EACZ,CAAC;EAEDoB,YAAY,EAAE;IACZlB,IAAI,EAAE,cAAc;IACpBC,KAAK,EAAE;MACL,CAAC,EAAE;QAAEC,SAAS,EAAE,CAAC;UAAEC,WAAW,EAAE,OAAO;UAAEM,OAAO,EAAE;QAAO,CAAC;MAAE,CAAC;MAC7D,GAAG,EAAE;QAAEP,SAAS,EAAE,CAAC;UAAEC,WAAW,EAAE,OAAO;UAAEM,OAAO,EAAE;QAAQ,CAAC;MAAE;IACjE,CAAC;IACDH,QAAQ,EAAER;EACZ,CAAC;EAEDqB,YAAY,EAAE;IACZnB,IAAI,EAAE,cAAc;IACpBC,KAAK,EAAE;MACL,CAAC,EAAE;QAAEC,SAAS,EAAE,CAAC;UAAEC,WAAW,EAAE,OAAO;UAAEC,OAAO,EAAE;QAAO,CAAC;MAAE,CAAC;MAC7D,GAAG,EAAE;QAAEF,SAAS,EAAE,CAAC;UAAEC,WAAW,EAAE,OAAO;UAAEC,OAAO,EAAE;QAAQ,CAAC;MAAE;IACjE,CAAC;IACDE,QAAQ,EAAER;EACZ;AACF,CAAC;AAEM,IAAMH,MAAM,GAAAL,OAAA,CAAAK,MAAA,GAAG;EACpBI,YAAY,EAAE;IACZE,KAAK,EAAE,IAAAmB,kDAAiC,EAAC1B,UAAU,CAACK,YAAY,CAAC;IACjEO,QAAQ,EAAEZ,UAAU,CAACK,YAAY,CAACO;EACpC,CAAC;EACDC,WAAW,EAAE;IACXN,KAAK,EAAE,IAAAmB,kDAAiC,EAAC1B,UAAU,CAACa,WAAW,CAAC;IAChED,QAAQ,EAAEZ,UAAU,CAACa,WAAW,CAACD;EACnC,CAAC;EACDE,SAAS,EAAE;IACTP,KAAK,EAAE,IAAAmB,kDAAiC,EAAC1B,UAAU,CAACc,SAAS,CAAC;IAC9DF,QAAQ,EAAEZ,UAAU,CAACc,SAAS,CAACF;EACjC,CAAC;EACDK,WAAW,EAAE;IACXV,KAAK,EAAE,IAAAmB,kDAAiC,EAAC1B,UAAU,CAACiB,WAAW,CAAC;IAChEL,QAAQ,EAAEZ,UAAU,CAACiB,WAAW,CAACL;EACnC,CAAC;EACDM,WAAW,EAAE;IACXX,KAAK,EAAE,IAAAmB,kDAAiC,EAAC1B,UAAU,CAACkB,WAAW,CAAC;IAChEN,QAAQ,EAAEZ,UAAU,CAACkB,WAAW,CAACN;EACnC,CAAC;EACDO,WAAW,EAAE;IACXZ,KAAK,EAAE,IAAAmB,kDAAiC,EAAC1B,UAAU,CAACmB,WAAW,CAAC;IAChEP,QAAQ,EAAEZ,UAAU,CAACmB,WAAW,CAACP;EACnC;AACF,CAAC;AAEM,IAAMb,OAAO,GAAAH,OAAA,CAAAG,OAAA,GAAG;EACrBqB,aAAa,EAAE;IACbb,KAAK,EAAE,IAAAmB,kDAAiC,EAAC5B,WAAW,CAACsB,aAAa,CAAC;IACnER,QAAQ,EAAEd,WAAW,CAACsB,aAAa,CAACR;EACtC,CAAC;EACDS,YAAY,EAAE;IACZd,KAAK,EAAE,IAAAmB,kDAAiC,EAAC5B,WAAW,CAACuB,YAAY,CAAC;IAClET,QAAQ,EAAEd,WAAW,CAACuB,YAAY,CAACT;EACrC,CAAC;EACDU,UAAU,EAAE;IACVf,KAAK,EAAE,IAAAmB,kDAAiC,EAAC5B,WAAW,CAACwB,UAAU,CAAC;IAChEV,QAAQ,EAAEd,WAAW,CAACwB,UAAU,CAACV;EACnC,CAAC;EACDW,YAAY,EAAE;IACZhB,KAAK,EAAE,IAAAmB,kDAAiC,EAAC5B,WAAW,CAACyB,YAAY,CAAC;IAClEX,QAAQ,EAAEd,WAAW,CAACyB,YAAY,CAACX;EACrC,CAAC;EACDY,YAAY,EAAE;IACZjB,KAAK,EAAE,IAAAmB,kDAAiC,EAAC5B,WAAW,CAAC0B,YAAY,CAAC;IAClEZ,QAAQ,EAAEd,WAAW,CAAC0B,YAAY,CAACZ;EACrC,CAAC;EACDa,YAAY,EAAE;IACZlB,KAAK,EAAE,IAAAmB,kDAAiC,EAAC5B,WAAW,CAAC2B,YAAY,CAAC;IAClEb,QAAQ,EAAEd,WAAW,CAAC2B,YAAY,CAACb;EACrC;AACF,CAAC", "ignoreList": []}