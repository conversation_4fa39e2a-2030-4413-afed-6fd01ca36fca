{"version": 3, "names": ["_interopRequireDefault", "require", "Object", "defineProperty", "exports", "value", "FadingTransition", "_classCallCheck2", "_createClass2", "_possibleConstructorReturn2", "_getPrototypeOf2", "_inherits2", "_index", "_index2", "_callSuper", "t", "o", "e", "default", "_isNativeReflectConstruct", "Reflect", "construct", "constructor", "apply", "Boolean", "prototype", "valueOf", "call", "_BaseAnimationBuilder", "_this", "_len", "arguments", "length", "args", "Array", "_key", "concat", "build", "_this$durationV", "delayFunction", "getDelayFunction", "callback", "callbackV", "delay", "get<PERSON>elay", "halfDuration", "durationV", "values", "initialValues", "opacity", "originX", "currentOriginX", "originY", "currentOriginY", "width", "currentWidth", "height", "currentHeight", "animations", "withSequence", "withTiming", "duration", "<PERSON><PERSON><PERSON><PERSON>", "targetOriginX", "targetOriginY", "targetWidth", "targetHeight", "key", "createInstance", "BaseAnimationBuilder", "presetName"], "sources": ["../../../../src/layoutReanimation/defaultTransitions/FadingTransition.ts"], "sourcesContent": [null], "mappings": "AAAA,YAAY;;AAAA,IAAAA,sBAAA,GAAAC,OAAA;AAAAC,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,KAAA;AAAA;AAAAD,OAAA,CAAAE,gBAAA;AAAA,IAAAC,gBAAA,GAAAP,sBAAA,CAAAC,OAAA;AAAA,IAAAO,aAAA,GAAAR,sBAAA,CAAAC,OAAA;AAAA,IAAAQ,2BAAA,GAAAT,sBAAA,CAAAC,OAAA;AAAA,IAAAS,gBAAA,GAAAV,sBAAA,CAAAC,OAAA;AAAA,IAAAU,UAAA,GAAAX,sBAAA,CAAAC,OAAA;AACZ,IAAAW,MAAA,GAAAX,OAAA;AAKA,IAAAY,OAAA,GAAAZ,OAAA;AAA0D,SAAAa,WAAAC,CAAA,EAAAC,CAAA,EAAAC,CAAA,WAAAD,CAAA,OAAAN,gBAAA,CAAAQ,OAAA,EAAAF,CAAA,OAAAP,2BAAA,CAAAS,OAAA,EAAAH,CAAA,EAAAI,yBAAA,KAAAC,OAAA,CAAAC,SAAA,CAAAL,CAAA,EAAAC,CAAA,YAAAP,gBAAA,CAAAQ,OAAA,EAAAH,CAAA,EAAAO,WAAA,IAAAN,CAAA,CAAAO,KAAA,CAAAR,CAAA,EAAAE,CAAA;AAAA,SAAAE,0BAAA,cAAAJ,CAAA,IAAAS,OAAA,CAAAC,SAAA,CAAAC,OAAA,CAAAC,IAAA,CAAAP,OAAA,CAAAC,SAAA,CAAAG,OAAA,iCAAAT,CAAA,aAAAI,yBAAA,YAAAA,0BAAA,aAAAJ,CAAA;AAAA,IAY7CT,gBAAgB,GAAAF,OAAA,CAAAE,gBAAA,aAAAsB,qBAAA;EAAA,SAAAtB,iBAAA;IAAA,IAAAuB,KAAA;IAAA,IAAAtB,gBAAA,CAAAW,OAAA,QAAAZ,gBAAA;IAAA,SAAAwB,IAAA,GAAAC,SAAA,CAAAC,MAAA,EAAAC,IAAA,OAAAC,KAAA,CAAAJ,IAAA,GAAAK,IAAA,MAAAA,IAAA,GAAAL,IAAA,EAAAK,IAAA;MAAAF,IAAA,CAAAE,IAAA,IAAAJ,SAAA,CAAAI,IAAA;IAAA;IAAAN,KAAA,GAAAf,UAAA,OAAAR,gBAAA,KAAA8B,MAAA,CAAAH,IAAA;IAAAJ,KAAA,CAY3BQ,KAAK,GAAG,YAA+B;MAAA,IAAAC,eAAA;MACrC,IAAMC,aAAa,GAAGV,KAAA,CAAKW,gBAAgB,CAAC,CAAC;MAC7C,IAAMC,QAAQ,GAAGZ,KAAA,CAAKa,SAAS;MAC/B,IAAMC,KAAK,GAAGd,KAAA,CAAKe,QAAQ,CAAC,CAAC;MAC7B,IAAMC,YAAY,GAAG,EAAAP,eAAA,GAACT,KAAA,CAAKiB,SAAS,YAAAR,eAAA,GAAI,GAAG,IAAI,CAAC;MAEhD,OAAQ,UAAAS,MAAM,EAAK;QACjB,SAAS;;QACT,OAAO;UACLC,aAAa,EAAE;YACbC,OAAO,EAAE,CAAC;YACVC,OAAO,EAAEH,MAAM,CAACI,cAAc;YAC9BC,OAAO,EAAEL,MAAM,CAACM,cAAc;YAC9BC,KAAK,EAAEP,MAAM,CAACQ,YAAY;YAC1BC,MAAM,EAAET,MAAM,CAACU;UACjB,CAAC;UACDC,UAAU,EAAE;YACVT,OAAO,EAAEV,aAAa,CACpBI,KAAK,EACL,IAAAgB,mBAAY,EACV,IAAAC,iBAAU,EAAC,CAAC,EAAE;cAAEC,QAAQ,EAAEhB;YAAa,CAAC,CAAC,EACzC,IAAAe,iBAAU,EAAC,CAAC,EAAE;cAAEC,QAAQ,EAAEhB;YAAa,CAAC,CAC1C,CACF,CAAC;YACDK,OAAO,EAAE,IAAAY,gBAAS,EAChBnB,KAAK,GAAGE,YAAY,EACpB,IAAAe,iBAAU,EAACb,MAAM,CAACgB,aAAa,EAAE;cAAEF,QAAQ,EAAE;YAAE,CAAC,CAClD,CAAC;YACDT,OAAO,EAAE,IAAAU,gBAAS,EAChBnB,KAAK,GAAGE,YAAY,EACpB,IAAAe,iBAAU,EAACb,MAAM,CAACiB,aAAa,EAAE;cAAEH,QAAQ,EAAE;YAAE,CAAC,CAClD,CAAC;YACDP,KAAK,EAAE,IAAAQ,gBAAS,EACdnB,KAAK,GAAGE,YAAY,EACpB,IAAAe,iBAAU,EAACb,MAAM,CAACkB,WAAW,EAAE;cAAEJ,QAAQ,EAAE;YAAE,CAAC,CAChD,CAAC;YACDL,MAAM,EAAE,IAAAM,gBAAS,EACfnB,KAAK,GAAGE,YAAY,EACpB,IAAAe,iBAAU,EAACb,MAAM,CAACmB,YAAY,EAAE;cAAEL,QAAQ,EAAE;YAAE,CAAC,CACjD;UACF,CAAC;UACDpB,QAAA,EAAAA;QACF,CAAC;MACH,CAAC;IACH,CAAC;IAAA,OAAAZ,KAAA;EAAA;EAAA,IAAAlB,UAAA,CAAAO,OAAA,EAAAZ,gBAAA,EAAAsB,qBAAA;EAAA,WAAApB,aAAA,CAAAU,OAAA,EAAAZ,gBAAA;IAAA6D,GAAA;IAAA9D,KAAA,EAlDD,SAAO+D,cAAcA,CAAA,EAEF;MACjB,OAAO,IAAI9D,gBAAgB,CAAC,CAAC;IAC/B;EAAA;AAAA,EATQ+D,4BAAoB;AADjB/D,gBAAgB,CAIpBgE,UAAU,GAAG,kBAAkB", "ignoreList": []}