3b8475b891f1ee9c180f1fbeb23333fb
'use strict';

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.FlipOutData = exports.FlipOut = exports.FlipInData = exports.FlipIn = void 0;
var _animationParser = require("../animationParser.js");
var DEFAULT_FLIP_TIME = 0.3;
var FlipInData = exports.FlipInData = {
  FlipInYRight: {
    name: 'FlipInYRight',
    style: {
      0: {
        transform: [{
          perspective: '500px',
          rotateY: '90deg',
          translateX: '100%'
        }]
      },
      100: {
        transform: [{
          perspective: '500px',
          rotateY: '0deg',
          translateX: '0%'
        }]
      }
    },
    duration: DEFAULT_FLIP_TIME
  },
  FlipInYLeft: {
    name: 'FlipInYLeft',
    style: {
      0: {
        transform: [{
          perspective: '500px',
          rotateY: '-90deg',
          translateX: '-100%'
        }]
      },
      100: {
        transform: [{
          perspective: '500px',
          rotateY: '0deg',
          translateX: '0%'
        }]
      }
    },
    duration: DEFAULT_FLIP_TIME
  },
  FlipInXUp: {
    name: 'FlipInXUp',
    style: {
      0: {
        transform: [{
          perspective: '500px',
          rotateX: '90deg',
          translateY: '-100%'
        }]
      },
      100: {
        transform: [{
          perspective: '500px',
          rotateX: '0deg',
          translateY: '0%'
        }]
      }
    },
    duration: DEFAULT_FLIP_TIME
  },
  FlipInXDown: {
    name: 'FlipInXDown',
    style: {
      0: {
        transform: [{
          perspective: '500px',
          rotateX: '-90deg',
          translateY: '100%'
        }]
      },
      100: {
        transform: [{
          perspective: '500px',
          rotateX: '0deg',
          translateY: '0%'
        }]
      }
    },
    duration: DEFAULT_FLIP_TIME
  },
  FlipInEasyX: {
    name: 'FlipInEasyX',
    style: {
      0: {
        transform: [{
          perspective: '500px',
          rotateX: '90deg'
        }]
      },
      100: {
        transform: [{
          perspective: '500px',
          rotateX: '0deg'
        }]
      }
    },
    duration: DEFAULT_FLIP_TIME
  },
  FlipInEasyY: {
    name: 'FlipInEasyY',
    style: {
      0: {
        transform: [{
          perspective: '500px',
          rotateY: '90deg'
        }]
      },
      100: {
        transform: [{
          perspective: '500px',
          rotateY: '0deg'
        }]
      }
    },
    duration: DEFAULT_FLIP_TIME
  }
};
var FlipOutData = exports.FlipOutData = {
  FlipOutYRight: {
    name: 'FlipOutYRight',
    style: {
      0: {
        transform: [{
          perspective: '500px',
          rotateY: '0deg',
          translateX: '0%'
        }]
      },
      100: {
        transform: [{
          perspective: '500px',
          rotateY: '90deg',
          translateX: '100%'
        }]
      }
    },
    duration: DEFAULT_FLIP_TIME
  },
  FlipOutYLeft: {
    name: 'FlipOutYLeft',
    style: {
      0: {
        transform: [{
          perspective: '500px',
          rotateY: '0deg',
          translateX: '0%'
        }]
      },
      100: {
        transform: [{
          perspective: '500px',
          rotateY: '-90deg',
          translateX: '-100%'
        }]
      }
    },
    duration: DEFAULT_FLIP_TIME
  },
  FlipOutXUp: {
    name: 'FlipOutXUp',
    style: {
      0: {
        transform: [{
          perspective: '500px',
          rotateX: '0deg',
          translateY: '0%'
        }]
      },
      100: {
        transform: [{
          perspective: '500px',
          rotateX: '90deg',
          translateY: '-100%'
        }]
      }
    },
    duration: DEFAULT_FLIP_TIME
  },
  FlipOutXDown: {
    name: 'FlipOutXDown',
    style: {
      0: {
        transform: [{
          perspective: '500px',
          rotateX: '0deg',
          translateY: '0%'
        }]
      },
      100: {
        transform: [{
          perspective: '500px',
          rotateX: '-90deg',
          translateY: '100%'
        }]
      }
    },
    duration: DEFAULT_FLIP_TIME
  },
  FlipOutEasyX: {
    name: 'FlipOutEasyX',
    style: {
      0: {
        transform: [{
          perspective: '500px',
          rotateX: '0deg'
        }]
      },
      100: {
        transform: [{
          perspective: '500px',
          rotateX: '90deg'
        }]
      }
    },
    duration: DEFAULT_FLIP_TIME
  },
  FlipOutEasyY: {
    name: 'FlipOutEasyY',
    style: {
      0: {
        transform: [{
          perspective: '500px',
          rotateY: '0deg'
        }]
      },
      100: {
        transform: [{
          perspective: '500px',
          rotateY: '90deg'
        }]
      }
    },
    duration: DEFAULT_FLIP_TIME
  }
};
var FlipIn = exports.FlipIn = {
  FlipInYRight: {
    style: (0, _animationParser.convertAnimationObjectToKeyframes)(FlipInData.FlipInYRight),
    duration: FlipInData.FlipInYRight.duration
  },
  FlipInYLeft: {
    style: (0, _animationParser.convertAnimationObjectToKeyframes)(FlipInData.FlipInYLeft),
    duration: FlipInData.FlipInYLeft.duration
  },
  FlipInXUp: {
    style: (0, _animationParser.convertAnimationObjectToKeyframes)(FlipInData.FlipInXUp),
    duration: FlipInData.FlipInXUp.duration
  },
  FlipInXDown: {
    style: (0, _animationParser.convertAnimationObjectToKeyframes)(FlipInData.FlipInXDown),
    duration: FlipInData.FlipInXDown.duration
  },
  FlipInEasyX: {
    style: (0, _animationParser.convertAnimationObjectToKeyframes)(FlipInData.FlipInEasyX),
    duration: FlipInData.FlipInEasyX.duration
  },
  FlipInEasyY: {
    style: (0, _animationParser.convertAnimationObjectToKeyframes)(FlipInData.FlipInEasyY),
    duration: FlipInData.FlipInEasyY.duration
  }
};
var FlipOut = exports.FlipOut = {
  FlipOutYRight: {
    style: (0, _animationParser.convertAnimationObjectToKeyframes)(FlipOutData.FlipOutYRight),
    duration: FlipOutData.FlipOutYRight.duration
  },
  FlipOutYLeft: {
    style: (0, _animationParser.convertAnimationObjectToKeyframes)(FlipOutData.FlipOutYLeft),
    duration: FlipOutData.FlipOutYLeft.duration
  },
  FlipOutXUp: {
    style: (0, _animationParser.convertAnimationObjectToKeyframes)(FlipOutData.FlipOutXUp),
    duration: FlipOutData.FlipOutXUp.duration
  },
  FlipOutXDown: {
    style: (0, _animationParser.convertAnimationObjectToKeyframes)(FlipOutData.FlipOutXDown),
    duration: FlipOutData.FlipOutXDown.duration
  },
  FlipOutEasyX: {
    style: (0, _animationParser.convertAnimationObjectToKeyframes)(FlipOutData.FlipOutEasyX),
    duration: FlipOutData.FlipOutEasyX.duration
  },
  FlipOutEasyY: {
    style: (0, _animationParser.convertAnimationObjectToKeyframes)(FlipOutData.FlipOutEasyY),
    duration: FlipOutData.FlipOutEasyY.duration
  }
};
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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