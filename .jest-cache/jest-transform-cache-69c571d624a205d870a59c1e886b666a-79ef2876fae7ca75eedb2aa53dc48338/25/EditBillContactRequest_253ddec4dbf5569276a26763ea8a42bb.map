{"version": 3, "names": [], "sources": ["/Users/<USER>/projects/react-native/payment-module/src/data/models/edit-bill-contact/EditBillContactRequest.ts"], "sourcesContent": ["export interface EditBillContactRequest {\n  name: string;\n  alias: string;\n  category: string;\n  accounts: AccountRequest[];\n  additions?: AdditionsRequest;\n  id: string;\n}\n\nexport interface AccountRequest {\n  bankName: string;\n  accountType: string;\n  accountNumber: string;\n  bankCode: string | undefined;\n  externalId?: string;\n}\n\nexport interface AdditionsRequest {\n  favoriteStatus: 'ACTIVE' | 'INACTIVE';\n  reminderStatus: 'ACTIVE' | 'INACTIVE';\n  payableAmount: string;\n}\n"], "mappings": "", "ignoreList": []}