6030bfb3620333f1b27de60315535549
"use strict";

/* istanbul ignore next */
function cov_1l1mybiwt4() {
  var path = "/Users/<USER>/projects/react-native/payment-module/src/data/models/edit-bill-contact/EditBillContactRequest.ts";
  var hash = "59e4a003afa186d52fc6f13ec3cfa82578938ac7";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/projects/react-native/payment-module/src/data/models/edit-bill-contact/EditBillContactRequest.ts",
    statementMap: {
      "0": {
        start: {
          line: 3,
          column: 0
        },
        end: {
          line: 5,
          column: 3
        }
      }
    },
    fnMap: {},
    branchMap: {},
    s: {
      "0": 0
    },
    f: {},
    b: {},
    inputSourceMap: {
      version: 3,
      file: undefined,
      names: [],
      sourceRoot: undefined,
      sources: ["/Users/<USER>/projects/react-native/payment-module/src/data/models/edit-bill-contact/EditBillContactRequest.ts"],
      sourcesContent: ["export interface EditBillContactRequest {\n  name: string;\n  alias: string;\n  category: string;\n  accounts: AccountRequest[];\n  additions?: AdditionsRequest;\n  id: string;\n}\n\nexport interface AccountRequest {\n  bankName: string;\n  accountType: string;\n  accountNumber: string;\n  bankCode: string | undefined;\n  externalId?: string;\n}\n\nexport interface AdditionsRequest {\n  favoriteStatus: 'ACTIVE' | 'INACTIVE';\n  reminderStatus: 'ACTIVE' | 'INACTIVE';\n  payableAmount: string;\n}\n"],
      mappings: "",
      ignoreList: []
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "59e4a003afa186d52fc6f13ec3cfa82578938ac7"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_1l1mybiwt4 = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_1l1mybiwt4();
cov_1l1mybiwt4().s[0]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6W10sInNvdXJjZXMiOlsiL1VzZXJzL3NvbnNhbXNldC9wcm9qZWN0cy9yZWFjdC1uYXRpdmUvcGF5bWVudC1tb2R1bGUvc3JjL2RhdGEvbW9kZWxzL2VkaXQtYmlsbC1jb250YWN0L0VkaXRCaWxsQ29udGFjdFJlcXVlc3QudHMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGludGVyZmFjZSBFZGl0QmlsbENvbnRhY3RSZXF1ZXN0IHtcbiAgbmFtZTogc3RyaW5nO1xuICBhbGlhczogc3RyaW5nO1xuICBjYXRlZ29yeTogc3RyaW5nO1xuICBhY2NvdW50czogQWNjb3VudFJlcXVlc3RbXTtcbiAgYWRkaXRpb25zPzogQWRkaXRpb25zUmVxdWVzdDtcbiAgaWQ6IHN0cmluZztcbn1cblxuZXhwb3J0IGludGVyZmFjZSBBY2NvdW50UmVxdWVzdCB7XG4gIGJhbmtOYW1lOiBzdHJpbmc7XG4gIGFjY291bnRUeXBlOiBzdHJpbmc7XG4gIGFjY291bnROdW1iZXI6IHN0cmluZztcbiAgYmFua0NvZGU6IHN0cmluZyB8IHVuZGVmaW5lZDtcbiAgZXh0ZXJuYWxJZD86IHN0cmluZztcbn1cblxuZXhwb3J0IGludGVyZmFjZSBBZGRpdGlvbnNSZXF1ZXN0IHtcbiAgZmF2b3JpdGVTdGF0dXM6ICdBQ1RJVkUnIHwgJ0lOQUNUSVZFJztcbiAgcmVtaW5kZXJTdGF0dXM6ICdBQ1RJVkUnIHwgJ0lOQUNUSVZFJztcbiAgcGF5YWJsZUFtb3VudDogc3RyaW5nO1xufVxuIl0sIm1hcHBpbmdzIjoiIiwiaWdub3JlTGlzdCI6W119