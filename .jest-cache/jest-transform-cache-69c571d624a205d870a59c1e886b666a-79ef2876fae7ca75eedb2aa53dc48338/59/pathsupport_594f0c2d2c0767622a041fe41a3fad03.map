{"version": 3, "names": ["androidScaleSuffix", "ANDROID_BASE_DENSITY", "getAndroidAssetSuffix", "scale", "toString", "Number", "isFinite", "Math", "round", "Error", "drawableFileTypes", "Set", "getAndroidResourceFolderName", "asset", "has", "type", "suffix", "JSON", "stringify", "getAndroidResourceIdentifier", "get<PERSON><PERSON><PERSON><PERSON>", "name", "toLowerCase", "replace", "basePath", "httpServerLocation", "startsWith", "slice", "module", "exports"], "sources": ["path-support.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @flow strict\n * @format\n */\n\n'use strict';\n\n/*:: import type {PackagerAsset} from './registry.js'; */\n\nconst androidScaleSuffix = {\n  '0.75': 'ldpi',\n  '1': 'mdpi',\n  '1.5': 'hdpi',\n  '2': 'xhdpi',\n  '3': 'xxhdpi',\n  '4': 'xxxhdpi',\n};\n\nconst ANDROID_BASE_DENSITY = 160;\n\n/**\n * FIXME: using number to represent discrete scale numbers is fragile in essence because of\n * floating point numbers imprecision.\n */\nfunction getAndroidAssetSuffix(scale /*: number */) /*: string */ {\n  if (scale.toString() in androidScaleSuffix) {\n    // $FlowFixMe[invalid-computed-prop]\n    return androidScaleSuffix[scale.toString()];\n  }\n  // NOTE: Android Gradle Plugin does not fully support the nnndpi format.\n  // See https://issuetracker.google.com/issues/72884435\n  if (Number.isFinite(scale) && scale > 0) {\n    return Math.round(scale * ANDROID_BASE_DENSITY) + 'dpi';\n  }\n  throw new Error('no such scale ' + scale.toString());\n}\n\n// See https://developer.android.com/guide/topics/resources/drawable-resource.html\nconst drawableFileTypes = new Set([\n  'gif',\n  'jpeg',\n  'jpg',\n  'ktx',\n  'png',\n  'svg',\n  'webp',\n  'xml',\n]);\n\nfunction getAndroidResourceFolderName(\n  asset /*: PackagerAsset */,\n  scale /*: number */,\n) /*: string */ {\n  if (!drawableFileTypes.has(asset.type)) {\n    return 'raw';\n  }\n  const suffix = getAndroidAssetSuffix(scale);\n  if (!suffix) {\n    throw new Error(\n      \"Don't know which android drawable suffix to use for scale: \" +\n        scale +\n        '\\nAsset: ' +\n        JSON.stringify(asset, null, '\\t') +\n        '\\nPossible scales are:' +\n        JSON.stringify(androidScaleSuffix, null, '\\t'),\n    );\n  }\n  return 'drawable-' + suffix;\n}\n\nfunction getAndroidResourceIdentifier(\n  asset /*: PackagerAsset */,\n) /*: string */ {\n  return (getBasePath(asset) + '/' + asset.name)\n    .toLowerCase()\n    .replace(/\\//g, '_') // Encode folder structure in file name\n    .replace(/([^a-z0-9_])/g, '') // Remove illegal chars\n    .replace(/^(?:assets|assetsunstable_path)_/, ''); // Remove \"assets_\" or \"assetsunstable_path_\" prefix\n}\n\nfunction getBasePath(asset /*: PackagerAsset */) /*: string */ {\n  const basePath = asset.httpServerLocation;\n  return basePath.startsWith('/') ? basePath.slice(1) : basePath;\n}\n\nmodule.exports = {\n  getAndroidResourceFolderName,\n  getAndroidResourceIdentifier,\n  getBasePath,\n};\n"], "mappings": "AAUA,YAAY;;AAIZ,IAAMA,kBAAkB,GAAG;EACzB,MAAM,EAAE,MAAM;EACd,GAAG,EAAE,MAAM;EACX,KAAK,EAAE,MAAM;EACb,GAAG,EAAE,OAAO;EACZ,GAAG,EAAE,QAAQ;EACb,GAAG,EAAE;AACP,CAAC;AAED,IAAMC,oBAAoB,GAAG,GAAG;AAMhC,SAASC,qBAAqBA,CAACC,KAAK,EAA8B;EAChE,IAAIA,KAAK,CAACC,QAAQ,CAAC,CAAC,IAAIJ,kBAAkB,EAAE;IAE1C,OAAOA,kBAAkB,CAACG,KAAK,CAACC,QAAQ,CAAC,CAAC,CAAC;EAC7C;EAGA,IAAIC,MAAM,CAACC,QAAQ,CAACH,KAAK,CAAC,IAAIA,KAAK,GAAG,CAAC,EAAE;IACvC,OAAOI,IAAI,CAACC,KAAK,CAACL,KAAK,GAAGF,oBAAoB,CAAC,GAAG,KAAK;EACzD;EACA,MAAM,IAAIQ,KAAK,CAAC,gBAAgB,GAAGN,KAAK,CAACC,QAAQ,CAAC,CAAC,CAAC;AACtD;AAGA,IAAMM,iBAAiB,GAAG,IAAIC,GAAG,CAAC,CAChC,KAAK,EACL,MAAM,EACN,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,MAAM,EACN,KAAK,CACN,CAAC;AAEF,SAASC,4BAA4BA,CACnCC,KAAK,EACLV,KAAK,EACS;EACd,IAAI,CAACO,iBAAiB,CAACI,GAAG,CAACD,KAAK,CAACE,IAAI,CAAC,EAAE;IACtC,OAAO,KAAK;EACd;EACA,IAAMC,MAAM,GAAGd,qBAAqB,CAACC,KAAK,CAAC;EAC3C,IAAI,CAACa,MAAM,EAAE;IACX,MAAM,IAAIP,KAAK,CACb,6DAA6D,GAC3DN,KAAK,GACL,WAAW,GACXc,IAAI,CAACC,SAAS,CAACL,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC,GACjC,wBAAwB,GACxBI,IAAI,CAACC,SAAS,CAAClB,kBAAkB,EAAE,IAAI,EAAE,IAAI,CACjD,CAAC;EACH;EACA,OAAO,WAAW,GAAGgB,MAAM;AAC7B;AAEA,SAASG,4BAA4BA,CACnCN,KAAK,EACS;EACd,OAAO,CAACO,WAAW,CAACP,KAAK,CAAC,GAAG,GAAG,GAAGA,KAAK,CAACQ,IAAI,EAC1CC,WAAW,CAAC,CAAC,CACbC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CACnBA,OAAO,CAAC,eAAe,EAAE,EAAE,CAAC,CAC5BA,OAAO,CAAC,kCAAkC,EAAE,EAAE,CAAC;AACpD;AAEA,SAASH,WAAWA,CAACP,KAAK,EAAqC;EAC7D,IAAMW,QAAQ,GAAGX,KAAK,CAACY,kBAAkB;EACzC,OAAOD,QAAQ,CAACE,UAAU,CAAC,GAAG,CAAC,GAAGF,QAAQ,CAACG,KAAK,CAAC,CAAC,CAAC,GAAGH,QAAQ;AAChE;AAEAI,MAAM,CAACC,OAAO,GAAG;EACfjB,4BAA4B,EAA5BA,4BAA4B;EAC5BO,4BAA4B,EAA5BA,4BAA4B;EAC5BC,WAAW,EAAXA;AACF,CAAC", "ignoreList": []}