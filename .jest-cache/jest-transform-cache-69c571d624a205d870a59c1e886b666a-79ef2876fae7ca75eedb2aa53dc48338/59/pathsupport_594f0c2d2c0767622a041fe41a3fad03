8df4de64a3e431e6828d202c2594c3b9
'use strict';

var androidScaleSuffix = {
  '0.75': 'ldpi',
  '1': 'mdpi',
  '1.5': 'hdpi',
  '2': 'xhdpi',
  '3': 'xxhdpi',
  '4': 'xxxhdpi'
};
var ANDROID_BASE_DENSITY = 160;
function getAndroidAssetSuffix(scale) {
  if (scale.toString() in androidScaleSuffix) {
    return androidScaleSuffix[scale.toString()];
  }
  if (Number.isFinite(scale) && scale > 0) {
    return Math.round(scale * ANDROID_BASE_DENSITY) + 'dpi';
  }
  throw new Error('no such scale ' + scale.toString());
}
var drawableFileTypes = new Set(['gif', 'jpeg', 'jpg', 'ktx', 'png', 'svg', 'webp', 'xml']);
function getAndroidResourceFolderName(asset, scale) {
  if (!drawableFileTypes.has(asset.type)) {
    return 'raw';
  }
  var suffix = getAndroidAssetSuffix(scale);
  if (!suffix) {
    throw new Error("Don't know which android drawable suffix to use for scale: " + scale + '\nAsset: ' + JSON.stringify(asset, null, '\t') + '\nPossible scales are:' + JSON.stringify(androidScaleSuffix, null, '\t'));
  }
  return 'drawable-' + suffix;
}
function getAndroidResourceIdentifier(asset) {
  return (getBasePath(asset) + '/' + asset.name).toLowerCase().replace(/\//g, '_').replace(/([^a-z0-9_])/g, '').replace(/^(?:assets|assetsunstable_path)_/, '');
}
function getBasePath(asset) {
  var basePath = asset.httpServerLocation;
  return basePath.startsWith('/') ? basePath.slice(1) : basePath;
}
module.exports = {
  getAndroidResourceFolderName: getAndroidResourceFolderName,
  getAndroidResourceIdentifier: getAndroidResourceIdentifier,
  getBasePath: getBasePath
};
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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