{"version": 3, "names": ["NativeComponentRegistry", "_interopRequireWildcard", "require", "_getRequireWildcardCache", "e", "WeakMap", "r", "t", "__esModule", "default", "has", "get", "n", "__proto__", "a", "Object", "defineProperty", "getOwnPropertyDescriptor", "u", "hasOwnProperty", "call", "i", "set", "__INTERNAL_VIEW_CONFIG", "exports", "uiViewClassName", "bubblingEventTypes", "directEventTypes", "validAttributes", "decelerationRate", "disableIntervalMomentum", "maintainVisibleContentPosition", "endFillColor", "process", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nestedScrollEnabled", "overScrollMode", "pagingEnabled", "persistentScrollbar", "horizontal", "scrollEnabled", "scrollEventThrottle", "scrollPerfTag", "sendMomentumEvents", "showsHorizontalScrollIndicator", "snapToAlignment", "snapToEnd", "snapToInterval", "snapToStart", "snapToOffsets", "contentOffset", "borderBottomLeftRadius", "borderBottomRightRadius", "borderRadius", "borderStyle", "borderRightColor", "borderColor", "borderBottomColor", "borderTopLeftRadius", "borderTopColor", "removeClippedSubviews", "borderTopRightRadius", "borderLeftColor", "pointerEvents", "AndroidHorizontalScrollViewNativeComponent", "_default"], "sources": ["AndroidHorizontalScrollViewNativeComponent.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @flow strict-local\n * @format\n */\n\nimport type {\n  HostComponent,\n  PartialViewConfig,\n} from '../../Renderer/shims/ReactNativeTypes';\nimport type {ScrollViewNativeProps as Props} from './ScrollViewNativeComponentType';\n\nimport * as NativeComponentRegistry from '../../NativeComponent/NativeComponentRegistry';\n\nexport const __INTERNAL_VIEW_CONFIG: PartialViewConfig = {\n  uiViewClassName: 'AndroidHorizontalScrollView',\n  bubblingEventTypes: {},\n  directEventTypes: {},\n  validAttributes: {\n    decelerationRate: true,\n    disableIntervalMomentum: true,\n    maintainVisibleContentPosition: true,\n    endFillColor: {process: require('../../StyleSheet/processColor').default},\n    fadingEdgeLength: true,\n    nestedScrollEnabled: true,\n    overScrollMode: true,\n    pagingEnabled: true,\n    persistentScrollbar: true,\n    horizontal: true,\n    scrollEnabled: true,\n    scrollEventThrottle: true,\n    scrollPerfTag: true,\n    sendMomentumEvents: true,\n    showsHorizontalScrollIndicator: true,\n    snapToAlignment: true,\n    snapToEnd: true,\n    snapToInterval: true,\n    snapToStart: true,\n    snapToOffsets: true,\n    contentOffset: true,\n    borderBottomLeftRadius: true,\n    borderBottomRightRadius: true,\n    borderRadius: true,\n    borderStyle: true,\n    borderRightColor: {\n      process: require('../../StyleSheet/processColor').default,\n    },\n    borderColor: {process: require('../../StyleSheet/processColor').default},\n    borderBottomColor: {\n      process: require('../../StyleSheet/processColor').default,\n    },\n    borderTopLeftRadius: true,\n    borderTopColor: {process: require('../../StyleSheet/processColor').default},\n    removeClippedSubviews: true,\n    borderTopRightRadius: true,\n    borderLeftColor: {\n      process: require('../../StyleSheet/processColor').default,\n    },\n    pointerEvents: true,\n  },\n};\n\nconst AndroidHorizontalScrollViewNativeComponent: HostComponent<Props> =\n  NativeComponentRegistry.get<Props>(\n    'AndroidHorizontalScrollView',\n    () => __INTERNAL_VIEW_CONFIG,\n  );\n\nexport default AndroidHorizontalScrollViewNativeComponent;\n"], "mappings": ";;;;AAgBA,IAAAA,uBAAA,GAAAC,uBAAA,CAAAC,OAAA;AAAyF,SAAAC,yBAAAC,CAAA,6BAAAC,OAAA,mBAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,yBAAAC,CAAA,WAAAA,CAAA,GAAAG,CAAA,GAAAD,CAAA,KAAAF,CAAA;AAAA,SAAAH,wBAAAG,CAAA,EAAAE,CAAA,SAAAA,CAAA,IAAAF,CAAA,IAAAA,CAAA,CAAAI,UAAA,SAAAJ,CAAA,eAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,WAAAK,OAAA,EAAAL,CAAA,QAAAG,CAAA,GAAAJ,wBAAA,CAAAG,CAAA,OAAAC,CAAA,IAAAA,CAAA,CAAAG,GAAA,CAAAN,CAAA,UAAAG,CAAA,CAAAI,GAAA,CAAAP,CAAA,OAAAQ,CAAA,KAAAC,SAAA,UAAAC,CAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,CAAA,IAAAd,CAAA,oBAAAc,CAAA,OAAAC,cAAA,CAAAC,IAAA,CAAAhB,CAAA,EAAAc,CAAA,SAAAG,CAAA,GAAAP,CAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAb,CAAA,EAAAc,CAAA,UAAAG,CAAA,KAAAA,CAAA,CAAAV,GAAA,IAAAU,CAAA,CAAAC,GAAA,IAAAP,MAAA,CAAAC,cAAA,CAAAJ,CAAA,EAAAM,CAAA,EAAAG,CAAA,IAAAT,CAAA,CAAAM,CAAA,IAAAd,CAAA,CAAAc,CAAA,YAAAN,CAAA,CAAAH,OAAA,GAAAL,CAAA,EAAAG,CAAA,IAAAA,CAAA,CAAAe,GAAA,CAAAlB,CAAA,EAAAQ,CAAA,GAAAA,CAAA;AAElF,IAAMW,sBAAyC,GAAAC,OAAA,CAAAD,sBAAA,GAAG;EACvDE,eAAe,EAAE,6BAA6B;EAC9CC,kBAAkB,EAAE,CAAC,CAAC;EACtBC,gBAAgB,EAAE,CAAC,CAAC;EACpBC,eAAe,EAAE;IACfC,gBAAgB,EAAE,IAAI;IACtBC,uBAAuB,EAAE,IAAI;IAC7BC,8BAA8B,EAAE,IAAI;IACpCC,YAAY,EAAE;MAACC,OAAO,EAAE/B,OAAO,CAAC,+BAA+B,CAAC,CAACO;IAAO,CAAC;IACzEyB,gBAAgB,EAAE,IAAI;IACtBC,mBAAmB,EAAE,IAAI;IACzBC,cAAc,EAAE,IAAI;IACpBC,aAAa,EAAE,IAAI;IACnBC,mBAAmB,EAAE,IAAI;IACzBC,UAAU,EAAE,IAAI;IAChBC,aAAa,EAAE,IAAI;IACnBC,mBAAmB,EAAE,IAAI;IACzBC,aAAa,EAAE,IAAI;IACnBC,kBAAkB,EAAE,IAAI;IACxBC,8BAA8B,EAAE,IAAI;IACpCC,eAAe,EAAE,IAAI;IACrBC,SAAS,EAAE,IAAI;IACfC,cAAc,EAAE,IAAI;IACpBC,WAAW,EAAE,IAAI;IACjBC,aAAa,EAAE,IAAI;IACnBC,aAAa,EAAE,IAAI;IACnBC,sBAAsB,EAAE,IAAI;IAC5BC,uBAAuB,EAAE,IAAI;IAC7BC,YAAY,EAAE,IAAI;IAClBC,WAAW,EAAE,IAAI;IACjBC,gBAAgB,EAAE;MAChBtB,OAAO,EAAE/B,OAAO,CAAC,+BAA+B,CAAC,CAACO;IACpD,CAAC;IACD+C,WAAW,EAAE;MAACvB,OAAO,EAAE/B,OAAO,CAAC,+BAA+B,CAAC,CAACO;IAAO,CAAC;IACxEgD,iBAAiB,EAAE;MACjBxB,OAAO,EAAE/B,OAAO,CAAC,+BAA+B,CAAC,CAACO;IACpD,CAAC;IACDiD,mBAAmB,EAAE,IAAI;IACzBC,cAAc,EAAE;MAAC1B,OAAO,EAAE/B,OAAO,CAAC,+BAA+B,CAAC,CAACO;IAAO,CAAC;IAC3EmD,qBAAqB,EAAE,IAAI;IAC3BC,oBAAoB,EAAE,IAAI;IAC1BC,eAAe,EAAE;MACf7B,OAAO,EAAE/B,OAAO,CAAC,+BAA+B,CAAC,CAACO;IACpD,CAAC;IACDsD,aAAa,EAAE;EACjB;AACF,CAAC;AAED,IAAMC,0CAAgE,GACpEhE,uBAAuB,CAACW,GAAG,CACzB,6BAA6B,EAC7B;EAAA,OAAMY,sBAAsB;AAAA,CAC9B,CAAC;AAAC,IAAA0C,QAAA,GAAAzC,OAAA,CAAAf,OAAA,GAEWuD,0CAA0C", "ignoreList": []}