65dca1b2074c8f49a21f99dbfb18a1b1
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = exports.__INTERNAL_VIEW_CONFIG = void 0;
var NativeComponentRegistry = _interopRequireWildcard(require("../../NativeComponent/NativeComponentRegistry"));
function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
var __INTERNAL_VIEW_CONFIG = exports.__INTERNAL_VIEW_CONFIG = {
  uiViewClassName: 'AndroidHorizontalScrollView',
  bubblingEventTypes: {},
  directEventTypes: {},
  validAttributes: {
    decelerationRate: true,
    disableIntervalMomentum: true,
    maintainVisibleContentPosition: true,
    endFillColor: {
      process: require('../../StyleSheet/processColor').default
    },
    fadingEdgeLength: true,
    nestedScrollEnabled: true,
    overScrollMode: true,
    pagingEnabled: true,
    persistentScrollbar: true,
    horizontal: true,
    scrollEnabled: true,
    scrollEventThrottle: true,
    scrollPerfTag: true,
    sendMomentumEvents: true,
    showsHorizontalScrollIndicator: true,
    snapToAlignment: true,
    snapToEnd: true,
    snapToInterval: true,
    snapToStart: true,
    snapToOffsets: true,
    contentOffset: true,
    borderBottomLeftRadius: true,
    borderBottomRightRadius: true,
    borderRadius: true,
    borderStyle: true,
    borderRightColor: {
      process: require('../../StyleSheet/processColor').default
    },
    borderColor: {
      process: require('../../StyleSheet/processColor').default
    },
    borderBottomColor: {
      process: require('../../StyleSheet/processColor').default
    },
    borderTopLeftRadius: true,
    borderTopColor: {
      process: require('../../StyleSheet/processColor').default
    },
    removeClippedSubviews: true,
    borderTopRightRadius: true,
    borderLeftColor: {
      process: require('../../StyleSheet/processColor').default
    },
    pointerEvents: true
  }
};
var AndroidHorizontalScrollViewNativeComponent = NativeComponentRegistry.get('AndroidHorizontalScrollView', function () {
  return __INTERNAL_VIEW_CONFIG;
});
var _default = exports.default = AndroidHorizontalScrollViewNativeComponent;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJOYXRpdmVDb21wb25lbnRSZWdpc3RyeSIsIl9pbnRlcm9wUmVxdWlyZVdpbGRjYXJkIiwicmVxdWlyZSIsIl9nZXRSZXF1aXJlV2lsZGNhcmRDYWNoZSIsImUiLCJXZWFrTWFwIiwiciIsInQiLCJfX2VzTW9kdWxlIiwiZGVmYXVsdCIsImhhcyIsImdldCIsIm4iLCJfX3Byb3RvX18iLCJhIiwiT2JqZWN0IiwiZGVmaW5lUHJvcGVydHkiLCJnZXRPd25Qcm9wZXJ0eURlc2NyaXB0b3IiLCJ1IiwiaGFzT3duUHJvcGVydHkiLCJjYWxsIiwiaSIsInNldCIsIl9fSU5URVJOQUxfVklFV19DT05GSUciLCJleHBvcnRzIiwidWlWaWV3Q2xhc3NOYW1lIiwiYnViYmxpbmdFdmVudFR5cGVzIiwiZGlyZWN0RXZlbnRUeXBlcyIsInZhbGlkQXR0cmlidXRlcyIsImRlY2VsZXJhdGlvblJhdGUiLCJkaXNhYmxlSW50ZXJ2YWxNb21lbnR1bSIsIm1haW50YWluVmlzaWJsZUNvbnRlbnRQb3NpdGlvbiIsImVuZEZpbGxDb2xvciIsInByb2Nlc3MiLCJmYWRpbmdFZGdlTGVuZ3RoIiwibmVzdGVkU2Nyb2xsRW5hYmxlZCIsIm92ZXJTY3JvbGxNb2RlIiwicGFnaW5nRW5hYmxlZCIsInBlcnNpc3RlbnRTY3JvbGxiYXIiLCJob3Jpem9udGFsIiwic2Nyb2xsRW5hYmxlZCIsInNjcm9sbEV2ZW50VGhyb3R0bGUiLCJzY3JvbGxQZXJmVGFnIiwic2VuZE1vbWVudHVtRXZlbnRzIiwic2hvd3NIb3Jpem9udGFsU2Nyb2xsSW5kaWNhdG9yIiwic25hcFRvQWxpZ25tZW50Iiwic25hcFRvRW5kIiwic25hcFRvSW50ZXJ2YWwiLCJzbmFwVG9TdGFydCIsInNuYXBUb09mZnNldHMiLCJjb250ZW50T2Zmc2V0IiwiYm9yZGVyQm90dG9tTGVmdFJhZGl1cyIsImJvcmRlckJvdHRvbVJpZ2h0UmFkaXVzIiwiYm9yZGVyUmFkaXVzIiwiYm9yZGVyU3R5bGUiLCJib3JkZXJSaWdodENvbG9yIiwiYm9yZGVyQ29sb3IiLCJib3JkZXJCb3R0b21Db2xvciIsImJvcmRlclRvcExlZnRSYWRpdXMiLCJib3JkZXJUb3BDb2xvciIsInJlbW92ZUNsaXBwZWRTdWJ2aWV3cyIsImJvcmRlclRvcFJpZ2h0UmFkaXVzIiwiYm9yZGVyTGVmdENvbG9yIiwicG9pbnRlckV2ZW50cyIsIkFuZHJvaWRIb3Jpem9udGFsU2Nyb2xsVmlld05hdGl2ZUNvbXBvbmVudCIsIl9kZWZhdWx0Il0sInNvdXJjZXMiOlsiQW5kcm9pZEhvcml6b250YWxTY3JvbGxWaWV3TmF0aXZlQ29tcG9uZW50LmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQ29weXJpZ2h0IChjKSBNZXRhIFBsYXRmb3JtcywgSW5jLiBhbmQgYWZmaWxpYXRlcy5cbiAqXG4gKiBUaGlzIHNvdXJjZSBjb2RlIGlzIGxpY2Vuc2VkIHVuZGVyIHRoZSBNSVQgbGljZW5zZSBmb3VuZCBpbiB0aGVcbiAqIExJQ0VOU0UgZmlsZSBpbiB0aGUgcm9vdCBkaXJlY3Rvcnkgb2YgdGhpcyBzb3VyY2UgdHJlZS5cbiAqXG4gKiBAZmxvdyBzdHJpY3QtbG9jYWxcbiAqIEBmb3JtYXRcbiAqL1xuXG5pbXBvcnQgdHlwZSB7XG4gIEhvc3RDb21wb25lbnQsXG4gIFBhcnRpYWxWaWV3Q29uZmlnLFxufSBmcm9tICcuLi8uLi9SZW5kZXJlci9zaGltcy9SZWFjdE5hdGl2ZVR5cGVzJztcbmltcG9ydCB0eXBlIHtTY3JvbGxWaWV3TmF0aXZlUHJvcHMgYXMgUHJvcHN9IGZyb20gJy4vU2Nyb2xsVmlld05hdGl2ZUNvbXBvbmVudFR5cGUnO1xuXG5pbXBvcnQgKiBhcyBOYXRpdmVDb21wb25lbnRSZWdpc3RyeSBmcm9tICcuLi8uLi9OYXRpdmVDb21wb25lbnQvTmF0aXZlQ29tcG9uZW50UmVnaXN0cnknO1xuXG5leHBvcnQgY29uc3QgX19JTlRFUk5BTF9WSUVXX0NPTkZJRzogUGFydGlhbFZpZXdDb25maWcgPSB7XG4gIHVpVmlld0NsYXNzTmFtZTogJ0FuZHJvaWRIb3Jpem9udGFsU2Nyb2xsVmlldycsXG4gIGJ1YmJsaW5nRXZlbnRUeXBlczoge30sXG4gIGRpcmVjdEV2ZW50VHlwZXM6IHt9LFxuICB2YWxpZEF0dHJpYnV0ZXM6IHtcbiAgICBkZWNlbGVyYXRpb25SYXRlOiB0cnVlLFxuICAgIGRpc2FibGVJbnRlcnZhbE1vbWVudHVtOiB0cnVlLFxuICAgIG1haW50YWluVmlzaWJsZUNvbnRlbnRQb3NpdGlvbjogdHJ1ZSxcbiAgICBlbmRGaWxsQ29sb3I6IHtwcm9jZXNzOiByZXF1aXJlKCcuLi8uLi9TdHlsZVNoZWV0L3Byb2Nlc3NDb2xvcicpLmRlZmF1bHR9LFxuICAgIGZhZGluZ0VkZ2VMZW5ndGg6IHRydWUsXG4gICAgbmVzdGVkU2Nyb2xsRW5hYmxlZDogdHJ1ZSxcbiAgICBvdmVyU2Nyb2xsTW9kZTogdHJ1ZSxcbiAgICBwYWdpbmdFbmFibGVkOiB0cnVlLFxuICAgIHBlcnNpc3RlbnRTY3JvbGxiYXI6IHRydWUsXG4gICAgaG9yaXpvbnRhbDogdHJ1ZSxcbiAgICBzY3JvbGxFbmFibGVkOiB0cnVlLFxuICAgIHNjcm9sbEV2ZW50VGhyb3R0bGU6IHRydWUsXG4gICAgc2Nyb2xsUGVyZlRhZzogdHJ1ZSxcbiAgICBzZW5kTW9tZW50dW1FdmVudHM6IHRydWUsXG4gICAgc2hvd3NIb3Jpem9udGFsU2Nyb2xsSW5kaWNhdG9yOiB0cnVlLFxuICAgIHNuYXBUb0FsaWdubWVudDogdHJ1ZSxcbiAgICBzbmFwVG9FbmQ6IHRydWUsXG4gICAgc25hcFRvSW50ZXJ2YWw6IHRydWUsXG4gICAgc25hcFRvU3RhcnQ6IHRydWUsXG4gICAgc25hcFRvT2Zmc2V0czogdHJ1ZSxcbiAgICBjb250ZW50T2Zmc2V0OiB0cnVlLFxuICAgIGJvcmRlckJvdHRvbUxlZnRSYWRpdXM6IHRydWUsXG4gICAgYm9yZGVyQm90dG9tUmlnaHRSYWRpdXM6IHRydWUsXG4gICAgYm9yZGVyUmFkaXVzOiB0cnVlLFxuICAgIGJvcmRlclN0eWxlOiB0cnVlLFxuICAgIGJvcmRlclJpZ2h0Q29sb3I6IHtcbiAgICAgIHByb2Nlc3M6IHJlcXVpcmUoJy4uLy4uL1N0eWxlU2hlZXQvcHJvY2Vzc0NvbG9yJykuZGVmYXVsdCxcbiAgICB9LFxuICAgIGJvcmRlckNvbG9yOiB7cHJvY2VzczogcmVxdWlyZSgnLi4vLi4vU3R5bGVTaGVldC9wcm9jZXNzQ29sb3InKS5kZWZhdWx0fSxcbiAgICBib3JkZXJCb3R0b21Db2xvcjoge1xuICAgICAgcHJvY2VzczogcmVxdWlyZSgnLi4vLi4vU3R5bGVTaGVldC9wcm9jZXNzQ29sb3InKS5kZWZhdWx0LFxuICAgIH0sXG4gICAgYm9yZGVyVG9wTGVmdFJhZGl1czogdHJ1ZSxcbiAgICBib3JkZXJUb3BDb2xvcjoge3Byb2Nlc3M6IHJlcXVpcmUoJy4uLy4uL1N0eWxlU2hlZXQvcHJvY2Vzc0NvbG9yJykuZGVmYXVsdH0sXG4gICAgcmVtb3ZlQ2xpcHBlZFN1YnZpZXdzOiB0cnVlLFxuICAgIGJvcmRlclRvcFJpZ2h0UmFkaXVzOiB0cnVlLFxuICAgIGJvcmRlckxlZnRDb2xvcjoge1xuICAgICAgcHJvY2VzczogcmVxdWlyZSgnLi4vLi4vU3R5bGVTaGVldC9wcm9jZXNzQ29sb3InKS5kZWZhdWx0LFxuICAgIH0sXG4gICAgcG9pbnRlckV2ZW50czogdHJ1ZSxcbiAgfSxcbn07XG5cbmNvbnN0IEFuZHJvaWRIb3Jpem9udGFsU2Nyb2xsVmlld05hdGl2ZUNvbXBvbmVudDogSG9zdENvbXBvbmVudDxQcm9wcz4gPVxuICBOYXRpdmVDb21wb25lbnRSZWdpc3RyeS5nZXQ8UHJvcHM+KFxuICAgICdBbmRyb2lkSG9yaXpvbnRhbFNjcm9sbFZpZXcnLFxuICAgICgpID0+IF9fSU5URVJOQUxfVklFV19DT05GSUcsXG4gICk7XG5cbmV4cG9ydCBkZWZhdWx0IEFuZHJvaWRIb3Jpem9udGFsU2Nyb2xsVmlld05hdGl2ZUNvbXBvbmVudDtcbiJdLCJtYXBwaW5ncyI6Ijs7OztBQWdCQSxJQUFBQSx1QkFBQSxHQUFBQyx1QkFBQSxDQUFBQyxPQUFBO0FBQXlGLFNBQUFDLHlCQUFBQyxDQUFBLDZCQUFBQyxPQUFBLG1CQUFBQyxDQUFBLE9BQUFELE9BQUEsSUFBQUUsQ0FBQSxPQUFBRixPQUFBLFlBQUFGLHdCQUFBLFlBQUFBLHlCQUFBQyxDQUFBLFdBQUFBLENBQUEsR0FBQUcsQ0FBQSxHQUFBRCxDQUFBLEtBQUFGLENBQUE7QUFBQSxTQUFBSCx3QkFBQUcsQ0FBQSxFQUFBRSxDQUFBLFNBQUFBLENBQUEsSUFBQUYsQ0FBQSxJQUFBQSxDQUFBLENBQUFJLFVBQUEsU0FBQUosQ0FBQSxlQUFBQSxDQUFBLHVCQUFBQSxDQUFBLHlCQUFBQSxDQUFBLFdBQUFLLE9BQUEsRUFBQUwsQ0FBQSxRQUFBRyxDQUFBLEdBQUFKLHdCQUFBLENBQUFHLENBQUEsT0FBQUMsQ0FBQSxJQUFBQSxDQUFBLENBQUFHLEdBQUEsQ0FBQU4sQ0FBQSxVQUFBRyxDQUFBLENBQUFJLEdBQUEsQ0FBQVAsQ0FBQSxPQUFBUSxDQUFBLEtBQUFDLFNBQUEsVUFBQUMsQ0FBQSxHQUFBQyxNQUFBLENBQUFDLGNBQUEsSUFBQUQsTUFBQSxDQUFBRSx3QkFBQSxXQUFBQyxDQUFBLElBQUFkLENBQUEsb0JBQUFjLENBQUEsT0FBQUMsY0FBQSxDQUFBQyxJQUFBLENBQUFoQixDQUFBLEVBQUFjLENBQUEsU0FBQUcsQ0FBQSxHQUFBUCxDQUFBLEdBQUFDLE1BQUEsQ0FBQUUsd0JBQUEsQ0FBQWIsQ0FBQSxFQUFBYyxDQUFBLFVBQUFHLENBQUEsS0FBQUEsQ0FBQSxDQUFBVixHQUFBLElBQUFVLENBQUEsQ0FBQUMsR0FBQSxJQUFBUCxNQUFBLENBQUFDLGNBQUEsQ0FBQUosQ0FBQSxFQUFBTSxDQUFBLEVBQUFHLENBQUEsSUFBQVQsQ0FBQSxDQUFBTSxDQUFBLElBQUFkLENBQUEsQ0FBQWMsQ0FBQSxZQUFBTixDQUFBLENBQUFILE9BQUEsR0FBQUwsQ0FBQSxFQUFBRyxDQUFBLElBQUFBLENBQUEsQ0FBQWUsR0FBQSxDQUFBbEIsQ0FBQSxFQUFBUSxDQUFBLEdBQUFBLENBQUE7QUFFbEYsSUFBTVcsc0JBQXlDLEdBQUFDLE9BQUEsQ0FBQUQsc0JBQUEsR0FBRztFQUN2REUsZUFBZSxFQUFFLDZCQUE2QjtFQUM5Q0Msa0JBQWtCLEVBQUUsQ0FBQyxDQUFDO0VBQ3RCQyxnQkFBZ0IsRUFBRSxDQUFDLENBQUM7RUFDcEJDLGVBQWUsRUFBRTtJQUNmQyxnQkFBZ0IsRUFBRSxJQUFJO0lBQ3RCQyx1QkFBdUIsRUFBRSxJQUFJO0lBQzdCQyw4QkFBOEIsRUFBRSxJQUFJO0lBQ3BDQyxZQUFZLEVBQUU7TUFBQ0MsT0FBTyxFQUFFL0IsT0FBTyxDQUFDLCtCQUErQixDQUFDLENBQUNPO0lBQU8sQ0FBQztJQUN6RXlCLGdCQUFnQixFQUFFLElBQUk7SUFDdEJDLG1CQUFtQixFQUFFLElBQUk7SUFDekJDLGNBQWMsRUFBRSxJQUFJO0lBQ3BCQyxhQUFhLEVBQUUsSUFBSTtJQUNuQkMsbUJBQW1CLEVBQUUsSUFBSTtJQUN6QkMsVUFBVSxFQUFFLElBQUk7SUFDaEJDLGFBQWEsRUFBRSxJQUFJO0lBQ25CQyxtQkFBbUIsRUFBRSxJQUFJO0lBQ3pCQyxhQUFhLEVBQUUsSUFBSTtJQUNuQkMsa0JBQWtCLEVBQUUsSUFBSTtJQUN4QkMsOEJBQThCLEVBQUUsSUFBSTtJQUNwQ0MsZUFBZSxFQUFFLElBQUk7SUFDckJDLFNBQVMsRUFBRSxJQUFJO0lBQ2ZDLGNBQWMsRUFBRSxJQUFJO0lBQ3BCQyxXQUFXLEVBQUUsSUFBSTtJQUNqQkMsYUFBYSxFQUFFLElBQUk7SUFDbkJDLGFBQWEsRUFBRSxJQUFJO0lBQ25CQyxzQkFBc0IsRUFBRSxJQUFJO0lBQzVCQyx1QkFBdUIsRUFBRSxJQUFJO0lBQzdCQyxZQUFZLEVBQUUsSUFBSTtJQUNsQkMsV0FBVyxFQUFFLElBQUk7SUFDakJDLGdCQUFnQixFQUFFO01BQ2hCdEIsT0FBTyxFQUFFL0IsT0FBTyxDQUFDLCtCQUErQixDQUFDLENBQUNPO0lBQ3BELENBQUM7SUFDRCtDLFdBQVcsRUFBRTtNQUFDdkIsT0FBTyxFQUFFL0IsT0FBTyxDQUFDLCtCQUErQixDQUFDLENBQUNPO0lBQU8sQ0FBQztJQUN4RWdELGlCQUFpQixFQUFFO01BQ2pCeEIsT0FBTyxFQUFFL0IsT0FBTyxDQUFDLCtCQUErQixDQUFDLENBQUNPO0lBQ3BELENBQUM7SUFDRGlELG1CQUFtQixFQUFFLElBQUk7SUFDekJDLGNBQWMsRUFBRTtNQUFDMUIsT0FBTyxFQUFFL0IsT0FBTyxDQUFDLCtCQUErQixDQUFDLENBQUNPO0lBQU8sQ0FBQztJQUMzRW1ELHFCQUFxQixFQUFFLElBQUk7SUFDM0JDLG9CQUFvQixFQUFFLElBQUk7SUFDMUJDLGVBQWUsRUFBRTtNQUNmN0IsT0FBTyxFQUFFL0IsT0FBTyxDQUFDLCtCQUErQixDQUFDLENBQUNPO0lBQ3BELENBQUM7SUFDRHNELGFBQWEsRUFBRTtFQUNqQjtBQUNGLENBQUM7QUFFRCxJQUFNQywwQ0FBZ0UsR0FDcEVoRSx1QkFBdUIsQ0FBQ1csR0FBRyxDQUN6Qiw2QkFBNkIsRUFDN0I7RUFBQSxPQUFNWSxzQkFBc0I7QUFBQSxDQUM5QixDQUFDO0FBQUMsSUFBQTBDLFFBQUEsR0FBQXpDLE9BQUEsQ0FBQWYsT0FBQSxHQUVXdUQsMENBQTBDIiwiaWdub3JlTGlzdCI6W119