{"version": 3, "names": ["cov_1oduokolsx", "actualCoverage", "GetMyBillHistoryListMapper_1", "s", "require", "GetMyBillContactRecentListMapper_1", "MyBillContactListMapper_1", "EditBillContactMapper_1", "DeleteBillContactMapper_1", "SaveBillContactMapper_1", "HandleData_1", "BillContactRepository", "f", "remoteDataSource", "_classCallCheck2", "default", "_createClass2", "key", "value", "_saveBillContact", "_asyncToGenerator2", "request", "handleData", "saveBillContact", "mapSaveBillContactResponseToModel", "_x", "apply", "arguments", "_deleteBillContact", "deleteBillContact", "mapDeleteBillContactResponseToModel", "_x2", "_editBillContact", "editBillContact", "mapEditBillContactResponseToModel", "_x3", "_myBillContactList", "myBillContactList", "mapMyBillContactListResponseToModel", "_getMyBillContactRecentList", "getMyBillContactRecentList", "mapGetMyBillContactRecentListResponseToModel", "_x4", "_getMyBillHistoryList", "getMyBillHistoryList", "mapGetMyBillHistoryListResponseToModel", "_x5", "exports"], "sources": ["/Users/<USER>/projects/react-native/payment-module/src/data/repositories/BillContactRepository.ts"], "sourcesContent": ["import {mapGetMyBillHistoryListResponseToModel} from '../mappers/get-my-bill-history-list/GetMyBillHistoryListMapper';\nimport {BillHistoryModel} from '../../domain/entities/get-my-bill-history-list/GetMyBillHistoryListModel';\nimport {GetMyBillHistoryListRequest} from '../models/get-my-bill-history-list/GetMyBillHistoryListRequest';\nimport {mapGetMyBillContactRecentListResponseToModel} from '../mappers/get-my-bill-contact-recent-list/GetMyBillContactRecentListMapper';\nimport {GetMyBillContactRecentListModel} from '../../domain/entities/get-my-bill-contact-recent-list/GetMyBillContactRecentListModel';\nimport {GetMyBillContactRecentListRequest} from '../models/get-my-bill-contact-recent-list/GetMyBillContactRecentListRequest';\nimport {mapMyBillContactListResponseToModel} from '../mappers/my-bill-contact-list/MyBillContactListMapper';\nimport {MyBillContactListModel} from '../../domain/entities/my-bill-contact-list/MyBillContactListModel';\nimport {mapEditBillContactResponseToModel} from '../mappers/edit-bill-contact/EditBillContactMapper';\nimport {EditBillContactModel} from '../../domain/entities/edit-bill-contact/EditBillContactModel';\nimport {EditBillContactRequest} from '../models/edit-bill-contact/EditBillContactRequest';\nimport {mapDeleteBillContactResponseToModel} from '../mappers/delete-bill-contact/DeleteBillContactMapper';\nimport {DeleteBillContactModel} from '../../domain/entities/delete-bill-contact/DeleteBillContactModel';\nimport {DeleteBillContactRequest} from '../models/delete-bill-contact/DeleteBillContactRequest';\nimport {mapSaveBillContactResponseToModel} from '../mappers/save-bill-contact/SaveBillContactMapper';\nimport {handleData} from '../../utils/HandleData';\nimport {SaveBillContactModel} from '../../domain/entities/save-bill-contact/SaveBillContactModel';\nimport {SaveBillContactRequest} from '../models/save-bill-contact/SaveBillContactRequest';\nimport {BaseResponse} from '../../core/BaseResponse';\nimport {IBillContactRepository} from '../../domain/repositories/IBillContactRepository';\nimport {IBillContactDataSource} from '../datasources/IBillContactDataSource';\n\nexport class BillContactRepository implements IBillContactRepository {\n  private remoteDataSource: IBillContactDataSource;\n\n  constructor(remoteDataSource: IBillContactDataSource) {\n    this.remoteDataSource = remoteDataSource;\n  }\n\n  async saveBillContact(request: SaveBillContactRequest): Promise<BaseResponse<SaveBillContactModel>> {\n    return handleData<SaveBillContactModel>(\n      this.remoteDataSource.saveBillContact(request),\n      mapSaveBillContactResponseToModel,\n    );\n  }\n\n  async deleteBillContact(request: DeleteBillContactRequest): Promise<BaseResponse<DeleteBillContactModel>> {\n    return handleData<DeleteBillContactModel>(\n      this.remoteDataSource.deleteBillContact(request),\n      mapDeleteBillContactResponseToModel,\n    );\n  }\n\n  async editBillContact(request: EditBillContactRequest): Promise<BaseResponse<EditBillContactModel>> {\n    return handleData<EditBillContactModel>(\n      this.remoteDataSource.editBillContact(request),\n      mapEditBillContactResponseToModel,\n    );\n  }\n\n  async myBillContactList(): Promise<BaseResponse<MyBillContactListModel>> {\n    return handleData<MyBillContactListModel>(\n      this.remoteDataSource.myBillContactList(),\n      mapMyBillContactListResponseToModel,\n    );\n  }\n\n  async getMyBillContactRecentList(\n    request: GetMyBillContactRecentListRequest,\n  ): Promise<BaseResponse<GetMyBillContactRecentListModel>> {\n    return handleData<GetMyBillContactRecentListModel>(\n      this.remoteDataSource.getMyBillContactRecentList(request),\n      mapGetMyBillContactRecentListResponseToModel,\n    );\n  }\n\n  async getMyBillHistoryList(request: GetMyBillHistoryListRequest): Promise<BaseResponse<BillHistoryModel>> {\n    return handleData<BillHistoryModel>(\n      this.remoteDataSource.getMyBillHistoryList(request),\n      mapGetMyBillHistoryListResponseToModel,\n    );\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAcA;IAAAA,cAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,cAAA;;;;;;;;;;;;;;;;;;;;;AAdA,IAAAE,4BAAA;AAAA;AAAA,CAAAF,cAAA,GAAAG,CAAA,OAAAC,OAAA;AAGA,IAAAC,kCAAA;AAAA;AAAA,CAAAL,cAAA,GAAAG,CAAA,OAAAC,OAAA;AAGA,IAAAE,yBAAA;AAAA;AAAA,CAAAN,cAAA,GAAAG,CAAA,OAAAC,OAAA;AAEA,IAAAG,uBAAA;AAAA;AAAA,CAAAP,cAAA,GAAAG,CAAA,OAAAC,OAAA;AAGA,IAAAI,yBAAA;AAAA;AAAA,CAAAR,cAAA,GAAAG,CAAA,QAAAC,OAAA;AAGA,IAAAK,uBAAA;AAAA;AAAA,CAAAT,cAAA,GAAAG,CAAA,QAAAC,OAAA;AACA,IAAAM,YAAA;AAAA;AAAA,CAAAV,cAAA,GAAAG,CAAA,QAAAC,OAAA;AAAkD,IAOrCO,qBAAqB;AAAA;AAAA,CAAAX,cAAA,GAAAG,CAAA;EAAA;EAAAH,cAAA,GAAAY,CAAA;EAGhC,SAAAD,sBAAYE,gBAAwC;IAAA;IAAAb,cAAA,GAAAY,CAAA;IAAAZ,cAAA,GAAAG,CAAA;IAAA,IAAAW,gBAAA,CAAAC,OAAA,QAAAJ,qBAAA;IAAA;IAAAX,cAAA,GAAAG,CAAA;IAClD,IAAI,CAACU,gBAAgB,GAAGA,gBAAgB;EAC1C;EAAA;EAAAb,cAAA,GAAAG,CAAA;EAAC,WAAAa,aAAA,CAAAD,OAAA,EAAAJ,qBAAA;IAAAM,GAAA;IAAAC,KAAA;MAAA;MAAAlB,cAAA,GAAAY,CAAA;MAAA,IAAAO,gBAAA;MAAA;MAAA,CAAAnB,cAAA,GAAAG,CAAA,YAAAiB,kBAAA,CAAAL,OAAA,EAED,WAAsBM,OAA+B;QAAA;QAAArB,cAAA,GAAAY,CAAA;QAAAZ,cAAA,GAAAG,CAAA;QACnD,OAAO,IAAAO,YAAA,CAAAY,UAAU,EACf,IAAI,CAACT,gBAAgB,CAACU,eAAe,CAACF,OAAO,CAAC,EAC9CZ,uBAAA,CAAAe,iCAAiC,CAClC;MACH,CAAC;MAAA,SALKD,eAAeA,CAAAE,EAAA;QAAA;QAAAzB,cAAA,GAAAY,CAAA;QAAAZ,cAAA,GAAAG,CAAA;QAAA,OAAAgB,gBAAA,CAAAO,KAAA,OAAAC,SAAA;MAAA;MAAA;MAAA3B,cAAA,GAAAG,CAAA;MAAA,OAAfoB,eAAe;IAAA;EAAA;IAAAN,GAAA;IAAAC,KAAA;MAAA;MAAAlB,cAAA,GAAAY,CAAA;MAAA,IAAAgB,kBAAA;MAAA;MAAA,CAAA5B,cAAA,GAAAG,CAAA,YAAAiB,kBAAA,CAAAL,OAAA,EAOrB,WAAwBM,OAAiC;QAAA;QAAArB,cAAA,GAAAY,CAAA;QAAAZ,cAAA,GAAAG,CAAA;QACvD,OAAO,IAAAO,YAAA,CAAAY,UAAU,EACf,IAAI,CAACT,gBAAgB,CAACgB,iBAAiB,CAACR,OAAO,CAAC,EAChDb,yBAAA,CAAAsB,mCAAmC,CACpC;MACH,CAAC;MAAA,SALKD,iBAAiBA,CAAAE,GAAA;QAAA;QAAA/B,cAAA,GAAAY,CAAA;QAAAZ,cAAA,GAAAG,CAAA;QAAA,OAAAyB,kBAAA,CAAAF,KAAA,OAAAC,SAAA;MAAA;MAAA;MAAA3B,cAAA,GAAAG,CAAA;MAAA,OAAjB0B,iBAAiB;IAAA;EAAA;IAAAZ,GAAA;IAAAC,KAAA;MAAA;MAAAlB,cAAA,GAAAY,CAAA;MAAA,IAAAoB,gBAAA;MAAA;MAAA,CAAAhC,cAAA,GAAAG,CAAA,YAAAiB,kBAAA,CAAAL,OAAA,EAOvB,WAAsBM,OAA+B;QAAA;QAAArB,cAAA,GAAAY,CAAA;QAAAZ,cAAA,GAAAG,CAAA;QACnD,OAAO,IAAAO,YAAA,CAAAY,UAAU,EACf,IAAI,CAACT,gBAAgB,CAACoB,eAAe,CAACZ,OAAO,CAAC,EAC9Cd,uBAAA,CAAA2B,iCAAiC,CAClC;MACH,CAAC;MAAA,SALKD,eAAeA,CAAAE,GAAA;QAAA;QAAAnC,cAAA,GAAAY,CAAA;QAAAZ,cAAA,GAAAG,CAAA;QAAA,OAAA6B,gBAAA,CAAAN,KAAA,OAAAC,SAAA;MAAA;MAAA;MAAA3B,cAAA,GAAAG,CAAA;MAAA,OAAf8B,eAAe;IAAA;EAAA;IAAAhB,GAAA;IAAAC,KAAA;MAAA;MAAAlB,cAAA,GAAAY,CAAA;MAAA,IAAAwB,kBAAA;MAAA;MAAA,CAAApC,cAAA,GAAAG,CAAA,YAAAiB,kBAAA,CAAAL,OAAA,EAOrB,aAAuB;QAAA;QAAAf,cAAA,GAAAY,CAAA;QAAAZ,cAAA,GAAAG,CAAA;QACrB,OAAO,IAAAO,YAAA,CAAAY,UAAU,EACf,IAAI,CAACT,gBAAgB,CAACwB,iBAAiB,EAAE,EACzC/B,yBAAA,CAAAgC,mCAAmC,CACpC;MACH,CAAC;MAAA,SALKD,iBAAiBA,CAAA;QAAA;QAAArC,cAAA,GAAAY,CAAA;QAAAZ,cAAA,GAAAG,CAAA;QAAA,OAAAiC,kBAAA,CAAAV,KAAA,OAAAC,SAAA;MAAA;MAAA;MAAA3B,cAAA,GAAAG,CAAA;MAAA,OAAjBkC,iBAAiB;IAAA;EAAA;IAAApB,GAAA;IAAAC,KAAA;MAAA;MAAAlB,cAAA,GAAAY,CAAA;MAAA,IAAA2B,2BAAA;MAAA;MAAA,CAAAvC,cAAA,GAAAG,CAAA,YAAAiB,kBAAA,CAAAL,OAAA,EAOvB,WACEM,OAA0C;QAAA;QAAArB,cAAA,GAAAY,CAAA;QAAAZ,cAAA,GAAAG,CAAA;QAE1C,OAAO,IAAAO,YAAA,CAAAY,UAAU,EACf,IAAI,CAACT,gBAAgB,CAAC2B,0BAA0B,CAACnB,OAAO,CAAC,EACzDhB,kCAAA,CAAAoC,4CAA4C,CAC7C;MACH,CAAC;MAAA,SAPKD,0BAA0BA,CAAAE,GAAA;QAAA;QAAA1C,cAAA,GAAAY,CAAA;QAAAZ,cAAA,GAAAG,CAAA;QAAA,OAAAoC,2BAAA,CAAAb,KAAA,OAAAC,SAAA;MAAA;MAAA;MAAA3B,cAAA,GAAAG,CAAA;MAAA,OAA1BqC,0BAA0B;IAAA;EAAA;IAAAvB,GAAA;IAAAC,KAAA;MAAA;MAAAlB,cAAA,GAAAY,CAAA;MAAA,IAAA+B,qBAAA;MAAA;MAAA,CAAA3C,cAAA,GAAAG,CAAA,YAAAiB,kBAAA,CAAAL,OAAA,EAShC,WAA2BM,OAAoC;QAAA;QAAArB,cAAA,GAAAY,CAAA;QAAAZ,cAAA,GAAAG,CAAA;QAC7D,OAAO,IAAAO,YAAA,CAAAY,UAAU,EACf,IAAI,CAACT,gBAAgB,CAAC+B,oBAAoB,CAACvB,OAAO,CAAC,EACnDnB,4BAAA,CAAA2C,sCAAsC,CACvC;MACH,CAAC;MAAA,SALKD,oBAAoBA,CAAAE,GAAA;QAAA;QAAA9C,cAAA,GAAAY,CAAA;QAAAZ,cAAA,GAAAG,CAAA;QAAA,OAAAwC,qBAAA,CAAAjB,KAAA,OAAAC,SAAA;MAAA;MAAA;MAAA3B,cAAA,GAAAG,CAAA;MAAA,OAApByC,oBAAoB;IAAA;EAAA;AAAA;AAAA;AAAA5C,cAAA,GAAAG,CAAA;AA5C5B4C,OAAA,CAAApC,qBAAA,GAAAA,qBAAA", "ignoreList": []}