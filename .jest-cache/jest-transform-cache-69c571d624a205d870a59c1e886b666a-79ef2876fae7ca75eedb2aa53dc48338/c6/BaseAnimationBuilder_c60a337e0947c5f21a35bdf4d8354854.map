{"version": 3, "names": ["_interopRequireDefault", "require", "Object", "defineProperty", "exports", "value", "BaseAnimationBuilder", "_classCallCheck2", "_createClass2", "_index", "_commonTypes", "_util", "_errors", "default", "reduceMotionV", "ReduceMotion", "System", "randomizeDelay", "build", "ReanimatedError", "key", "duration", "durationMs", "durationV", "delay", "delayMs", "delayV", "<PERSON><PERSON><PERSON><PERSON>", "callback", "callbackV", "reduceMotion", "getDuration", "_this$durationV", "randomDelay", "get<PERSON>elay", "_this$delayV", "_this$delayV2", "Math", "random", "getReduceMotion", "getDelayFunction", "isDelayProvided", "animation", "<PERSON><PERSON><PERSON><PERSON>", "_", "getReduceMotionFromConfig", "instance", "createInstance"], "sources": ["../../../../src/layoutReanimation/animationBuilder/BaseAnimationBuilder.ts"], "sourcesContent": [null], "mappings": "AAAA,YAAY;;AAAA,IAAAA,sBAAA,GAAAC,OAAA;AAAAC,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,KAAA;AAAA;AAAAD,OAAA,CAAAE,oBAAA;AAAA,IAAAC,gBAAA,GAAAP,sBAAA,CAAAC,OAAA;AAAA,IAAAO,aAAA,GAAAR,sBAAA,CAAAC,OAAA;AACZ,IAAAQ,MAAA,GAAAR,OAAA;AAOA,IAAAS,YAAA,GAAAT,OAAA;AACA,IAAAU,KAAA,GAAAV,OAAA;AACA,IAAAW,OAAA,GAAAX,OAAA;AAA8C,IAEjCK,oBAAoB,GAAAF,OAAA,CAAAE,oBAAA;EAAA,SAAAA,qBAAA;IAAA,IAAAC,gBAAA,CAAAM,OAAA,QAAAP,oBAAA;IAAA,KAG/BQ,aAAa,GAAiBC,yBAAY,CAACC,MAAM;IAAA,KACjDC,cAAc,GAAG,KAAK;IAAA,KAOtBC,KAAK,GAAG,YAA4D;MAClE,MAAM,IAAIC,uBAAe,CAAC,sCAAsC,CAAC;IACnE,CAAC;EAAA;EAAA,WAAAX,aAAA,CAAAK,OAAA,EAAAP,oBAAA;IAAAc,GAAA;IAAAf,KAAA,EAiBD,SAAAgB,QAAQA,CAACC,UAAkB,EAAQ;MACjC,IAAI,CAACC,SAAS,GAAGD,UAAU;MAC3B,OAAO,IAAI;IACb;EAAA;IAAAF,GAAA;IAAAf,KAAA,EAiBA,SAAAmB,KAAKA,CAACC,OAAe,EAAQ;MAC3B,IAAI,CAACC,MAAM,GAAGD,OAAO;MACrB,OAAO,IAAI;IACb;EAAA;IAAAL,GAAA;IAAAf,KAAA,EAiBA,SAAAsB,YAAYA,CAACC,QAAqC,EAAQ;MACxD,IAAI,CAACC,SAAS,GAAGD,QAAQ;MACzB,OAAO,IAAI;IACb;EAAA;IAAAR,GAAA;IAAAf,KAAA,EAmBA,SAAAyB,YAAYA,CAAChB,aAA2B,EAAQ;MAC9C,IAAI,CAACA,aAAa,GAAGA,aAAa;MAClC,OAAO,IAAI;IACb;EAAA;IAAAM,GAAA;IAAAf,KAAA,EAOA,SAAA0B,WAAWA,CAAA,EAAW;MAAA,IAAAC,eAAA;MACpB,QAAAA,eAAA,GAAO,IAAI,CAACT,SAAS,YAAAS,eAAA,GAAI,GAAG;IAC9B;EAAA;IAAAZ,GAAA;IAAAf,KAAA,EAUA,SAAA4B,WAAWA,CAAA,EAAS;MAClB,IAAI,CAAChB,cAAc,GAAG,IAAI;MAC1B,OAAO,IAAI;IACb;EAAA;IAAAG,GAAA;IAAAf,KAAA,EAGA,SAAA6B,QAAQA,CAAA,EAAW;MAAA,IAAAC,YAAA,EAAAC,aAAA;MACjB,OAAO,IAAI,CAACnB,cAAc,GACtBoB,IAAI,CAACC,MAAM,CAAC,CAAC,KAAAH,YAAA,GAAI,IAAI,CAACT,MAAM,YAAAS,YAAA,GAAI,IAAI,CAAC,IAAAC,aAAA,GACpC,IAAI,CAACV,MAAM,YAAAU,aAAA,GAAI,CAAE;IACxB;EAAA;IAAAhB,GAAA;IAAAf,KAAA,EAEA,SAAAkC,eAAeA,CAAA,EAAiB;MAC9B,OAAO,IAAI,CAACzB,aAAa;IAC3B;EAAA;IAAAM,GAAA;IAAAf,KAAA,EAEA,SAAAmC,gBAAgBA,CAAA,EAAsB;MACpC,IAAMC,eAAe,GAAG,IAAI,CAACxB,cAAc,IAAI,IAAI,CAACS,MAAM;MAC1D,IAAMI,YAAY,GAAG,IAAI,CAACS,eAAe,CAAC,CAAC;MAC3C,OAAOE,eAAe,GAClB,UAACjB,KAAK,EAAEkB,SAAS,EAAK;QACpB,SAAS;;QACT,OAAO,IAAAC,gBAAS,EAACnB,KAAK,EAAEkB,SAAS,EAAEZ,YAAY,CAAC;MAClD,CAAC,GACD,UAACc,CAAC,EAAEF,SAAS,EAAK;QAChB,SAAS;;QACTA,SAAS,CAACZ,YAAY,GAAG,IAAAe,+BAAyB,EAACf,YAAY,CAAC;QAChE,OAAOY,SAAS;MAClB,CAAC;IACP;EAAA;IAAAtB,GAAA;IAAAf,KAAA,EAzHA,SAAOgB,QAAQA,CAEbC,UAAkB,EACD;MACjB,IAAMwB,QAAQ,GAAG,IAAI,CAACC,cAAc,CAAC,CAAC;MACtC,OAAOD,QAAQ,CAACzB,QAAQ,CAACC,UAAU,CAAC;IACtC;EAAA;IAAAF,GAAA;IAAAf,KAAA,EAcA,SAAOmB,KAAKA,CAEVC,OAAe,EACE;MACjB,IAAMqB,QAAQ,GAAG,IAAI,CAACC,cAAc,CAAC,CAAC;MACtC,OAAOD,QAAQ,CAACtB,KAAK,CAACC,OAAO,CAAC;IAChC;EAAA;IAAAL,GAAA;IAAAf,KAAA,EAcA,SAAOsB,YAAYA,CAEjBC,QAAqC,EACpB;MACjB,IAAMkB,QAAQ,GAAG,IAAI,CAACC,cAAc,CAAC,CAAC;MACtC,OAAOD,QAAQ,CAACnB,YAAY,CAACC,QAAQ,CAAC;IACxC;EAAA;IAAAR,GAAA;IAAAf,KAAA,EAgBA,SAAOyB,YAAYA,CAEjBA,aAA0B,EACT;MACjB,IAAMgB,QAAQ,GAAG,IAAI,CAACC,cAAc,CAAC,CAAC;MACtC,OAAOD,QAAQ,CAAChB,YAAY,CAACA,aAAY,CAAC;IAC5C;EAAA;IAAAV,GAAA;IAAAf,KAAA,EAQA,SAAO0B,WAAWA,CAAA,EAAW;MAC3B,OAAO,GAAG;IACZ;EAAA;IAAAX,GAAA;IAAAf,KAAA,EAOA,SAAO4B,WAAWA,CAAA,EAEC;MACjB,IAAMa,QAAQ,GAAG,IAAI,CAACC,cAAc,CAAC,CAAC;MACtC,OAAOD,QAAQ,CAACb,WAAW,CAAC,CAAC;IAC/B;EAAA;IAAAb,GAAA;IAAAf,KAAA,EAiCA,SAAOa,KAAKA,CAAA,EAE4C;MACtD,IAAM4B,QAAQ,GAAG,IAAI,CAACC,cAAc,CAAC,CAAC;MACtC,OAAOD,QAAQ,CAAC5B,KAAK,CAAC,CAAC;IACzB;EAAA;AAAA", "ignoreList": []}