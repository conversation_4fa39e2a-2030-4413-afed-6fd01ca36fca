{"version": 3, "names": ["cov_v4plo5k52", "actualCoverage", "PathResolver_1", "s", "require", "ResponseHandler_1", "MSBCustomError_1", "BillPayRemoteDataSource", "f", "httpClient", "_classCallCheck2", "default", "_createClass2", "key", "value", "_categoryList", "_asyncToGenerator2", "url", "PathResolver", "billPay", "categoryList", "response", "get", "handleResponse", "error", "CustomError", "b", "createError", "apply", "arguments", "_providerList", "request", "providerList", "code", "_x", "_myBillList", "myBillList", "post", "_x2", "_getBillDetail", "getBillDetail", "_x3", "_billValidate", "billValidate", "_x4", "exports"], "sources": ["/Users/<USER>/projects/react-native/payment-module/src/data/datasources/remote/BillPayRemoteDataSource.ts"], "sourcesContent": ["import {GetBillDetailResponse} from '../../models/get-bill-detail/GetBillDetailResponse';\nimport {GetBillDetailRequest} from '../../models/get-bill-detail/GetBillDetailRequest';\nimport {BillValidateResponse} from '../../models/bill-validate/BillValidateResponse';\nimport {MyBillListResponse} from '../../models/my-bill-list/MyBillListResponse';\nimport {MyBillListRequest} from '../../models/my-bill-list/MyBillListRequest';\nimport {ProviderListResponse} from '../../models/provider-list/ProviderListResponse';\nimport {ProviderListRequest} from '../../models/provider-list/ProviderListRequest';\nimport {CategoryListResponse} from '../../models/category-list/CategoryListResponse';\nimport {BaseResponse} from '../../../core/BaseResponse';\nimport {PathResolver} from '../../../utils/PathResolver';\nimport {IHttpClient} from 'msb-host-shared-module';\nimport {handleResponse} from '../../../utils/ResponseHandler';\nimport {IBillPayDataSource} from '../IBillPayDataSource';\nimport {BillValidateRequest} from '../../models/bill-validate/BillValidateRequest';\nimport {createError, CustomError} from '../../../core/MSBCustomError';\n\nexport class BillPayRemoteDataSource implements IBillPayDataSource {\n  constructor(private httpClient: IHttpClient) {}\n\n  async categoryList(): Promise<BaseResponse<CategoryListResponse>> {\n    try {\n      const url = PathResolver.billPay.categoryList();\n      const response = await this.httpClient.get(url);\n      return handleResponse(response);\n    } catch (error: any) {\n      if (error instanceof CustomError) {\n        throw error;\n      }\n      throw createError();\n    }\n  }\n\n  async providerList(request: ProviderListRequest): Promise<BaseResponse<ProviderListResponse>> {\n    try {\n      const url = PathResolver.billPay.providerList(request.code);\n      const response = await this.httpClient.get(url);\n      return handleResponse(response);\n    } catch (error: any) {\n      if (error instanceof CustomError) {\n        throw error;\n      }\n      throw createError();\n    }\n  }\n\n  async myBillList(request: MyBillListRequest): Promise<BaseResponse<MyBillListResponse>> {\n    try {\n      const url = PathResolver.billPay.myBillList();\n      const response = await this.httpClient.post(url, request);\n      return handleResponse(response);\n    } catch (error: any) {\n      if (error instanceof CustomError) {\n        throw error;\n      }\n      throw createError();\n    }\n  }\n\n  async getBillDetail(request: GetBillDetailRequest): Promise<BaseResponse<GetBillDetailResponse>> {\n    try {\n      const url = PathResolver.billPay.getBillDetail();\n      const response = await this.httpClient.post(url, request);\n      return handleResponse(response);\n    } catch (error: any) {\n      if (error instanceof CustomError) {\n        throw error;\n      }\n      throw createError();\n    }\n  }\n\n  async billValidate(request: BillValidateRequest): Promise<BaseResponse<BillValidateResponse>> {\n    try {\n      const url = PathResolver.billPay.billValidate();\n      const response = await this.httpClient.post(url, request);\n      return handleResponse(response);\n    } catch (error: any) {\n      if (error instanceof CustomError) {\n        throw error;\n      }\n      throw createError();\n    }\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAiB6C;IAAAA,aAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,aAAA;;;;;;;;;;;;;;;;;;;;;AAR7C,IAAAE,cAAA;AAAA;AAAA,CAAAF,aAAA,GAAAG,CAAA,OAAAC,OAAA;AAEA,IAAAC,iBAAA;AAAA;AAAA,CAAAL,aAAA,GAAAG,CAAA,OAAAC,OAAA;AAGA,IAAAE,gBAAA;AAAA;AAAA,CAAAN,aAAA,GAAAG,CAAA,OAAAC,OAAA;AAAsE,IAEzDG,uBAAuB;AAAA;AAAA,CAAAP,aAAA,GAAAG,CAAA;EAAA;EAAAH,aAAA,GAAAQ,CAAA;EAClC,SAAAD,wBAAoBE,UAAuB;IAAA;IAAAT,aAAA,GAAAQ,CAAA;IAAAR,aAAA,GAAAG,CAAA;IAAA,IAAAO,gBAAA,CAAAC,OAAA,QAAAJ,uBAAA;IAAA;IAAAP,aAAA,GAAAG,CAAA;IAAvB,KAAAM,UAAU,GAAVA,UAAU;EAAgB;EAAA;EAAAT,aAAA,GAAAG,CAAA;EAAC,WAAAS,aAAA,CAAAD,OAAA,EAAAJ,uBAAA;IAAAM,GAAA;IAAAC,KAAA;MAAA;MAAAd,aAAA,GAAAQ,CAAA;MAAA,IAAAO,aAAA;MAAA;MAAA,CAAAf,aAAA,GAAAG,CAAA,YAAAa,kBAAA,CAAAL,OAAA,EAE/C,aAAkB;QAAA;QAAAX,aAAA,GAAAQ,CAAA;QAAAR,aAAA,GAAAG,CAAA;QAChB,IAAI;UACF,IAAMc,GAAG;UAAA;UAAA,CAAAjB,aAAA,GAAAG,CAAA,QAAGD,cAAA,CAAAgB,YAAY,CAACC,OAAO,CAACC,YAAY,EAAE;UAC/C,IAAMC,QAAQ;UAAA;UAAA,CAAArB,aAAA,GAAAG,CAAA,cAAS,IAAI,CAACM,UAAU,CAACa,GAAG,CAACL,GAAG,CAAC;UAAA;UAAAjB,aAAA,GAAAG,CAAA;UAC/C,OAAO,IAAAE,iBAAA,CAAAkB,cAAc,EAACF,QAAQ,CAAC;QACjC,CAAC,CAAC,OAAOG,KAAU,EAAE;UAAA;UAAAxB,aAAA,GAAAG,CAAA;UACnB,IAAIqB,KAAK,YAAYlB,gBAAA,CAAAmB,WAAW,EAAE;YAAA;YAAAzB,aAAA,GAAA0B,CAAA;YAAA1B,aAAA,GAAAG,CAAA;YAChC,MAAMqB,KAAK;UACb;UAAA;UAAA;YAAAxB,aAAA,GAAA0B,CAAA;UAAA;UAAA1B,aAAA,GAAAG,CAAA;UACA,MAAM,IAAAG,gBAAA,CAAAqB,WAAW,GAAE;QACrB;MACF,CAAC;MAAA,SAXKP,YAAYA,CAAA;QAAA;QAAApB,aAAA,GAAAQ,CAAA;QAAAR,aAAA,GAAAG,CAAA;QAAA,OAAAY,aAAA,CAAAa,KAAA,OAAAC,SAAA;MAAA;MAAA;MAAA7B,aAAA,GAAAG,CAAA;MAAA,OAAZiB,YAAY;IAAA;EAAA;IAAAP,GAAA;IAAAC,KAAA;MAAA;MAAAd,aAAA,GAAAQ,CAAA;MAAA,IAAAsB,aAAA;MAAA;MAAA,CAAA9B,aAAA,GAAAG,CAAA,YAAAa,kBAAA,CAAAL,OAAA,EAalB,WAAmBoB,OAA4B;QAAA;QAAA/B,aAAA,GAAAQ,CAAA;QAAAR,aAAA,GAAAG,CAAA;QAC7C,IAAI;UACF,IAAMc,GAAG;UAAA;UAAA,CAAAjB,aAAA,GAAAG,CAAA,QAAGD,cAAA,CAAAgB,YAAY,CAACC,OAAO,CAACa,YAAY,CAACD,OAAO,CAACE,IAAI,CAAC;UAC3D,IAAMZ,QAAQ;UAAA;UAAA,CAAArB,aAAA,GAAAG,CAAA,cAAS,IAAI,CAACM,UAAU,CAACa,GAAG,CAACL,GAAG,CAAC;UAAA;UAAAjB,aAAA,GAAAG,CAAA;UAC/C,OAAO,IAAAE,iBAAA,CAAAkB,cAAc,EAACF,QAAQ,CAAC;QACjC,CAAC,CAAC,OAAOG,KAAU,EAAE;UAAA;UAAAxB,aAAA,GAAAG,CAAA;UACnB,IAAIqB,KAAK,YAAYlB,gBAAA,CAAAmB,WAAW,EAAE;YAAA;YAAAzB,aAAA,GAAA0B,CAAA;YAAA1B,aAAA,GAAAG,CAAA;YAChC,MAAMqB,KAAK;UACb;UAAA;UAAA;YAAAxB,aAAA,GAAA0B,CAAA;UAAA;UAAA1B,aAAA,GAAAG,CAAA;UACA,MAAM,IAAAG,gBAAA,CAAAqB,WAAW,GAAE;QACrB;MACF,CAAC;MAAA,SAXKK,YAAYA,CAAAE,EAAA;QAAA;QAAAlC,aAAA,GAAAQ,CAAA;QAAAR,aAAA,GAAAG,CAAA;QAAA,OAAA2B,aAAA,CAAAF,KAAA,OAAAC,SAAA;MAAA;MAAA;MAAA7B,aAAA,GAAAG,CAAA;MAAA,OAAZ6B,YAAY;IAAA;EAAA;IAAAnB,GAAA;IAAAC,KAAA;MAAA;MAAAd,aAAA,GAAAQ,CAAA;MAAA,IAAA2B,WAAA;MAAA;MAAA,CAAAnC,aAAA,GAAAG,CAAA,YAAAa,kBAAA,CAAAL,OAAA,EAalB,WAAiBoB,OAA0B;QAAA;QAAA/B,aAAA,GAAAQ,CAAA;QAAAR,aAAA,GAAAG,CAAA;QACzC,IAAI;UACF,IAAMc,GAAG;UAAA;UAAA,CAAAjB,aAAA,GAAAG,CAAA,QAAGD,cAAA,CAAAgB,YAAY,CAACC,OAAO,CAACiB,UAAU,EAAE;UAC7C,IAAMf,QAAQ;UAAA;UAAA,CAAArB,aAAA,GAAAG,CAAA,cAAS,IAAI,CAACM,UAAU,CAAC4B,IAAI,CAACpB,GAAG,EAAEc,OAAO,CAAC;UAAA;UAAA/B,aAAA,GAAAG,CAAA;UACzD,OAAO,IAAAE,iBAAA,CAAAkB,cAAc,EAACF,QAAQ,CAAC;QACjC,CAAC,CAAC,OAAOG,KAAU,EAAE;UAAA;UAAAxB,aAAA,GAAAG,CAAA;UACnB,IAAIqB,KAAK,YAAYlB,gBAAA,CAAAmB,WAAW,EAAE;YAAA;YAAAzB,aAAA,GAAA0B,CAAA;YAAA1B,aAAA,GAAAG,CAAA;YAChC,MAAMqB,KAAK;UACb;UAAA;UAAA;YAAAxB,aAAA,GAAA0B,CAAA;UAAA;UAAA1B,aAAA,GAAAG,CAAA;UACA,MAAM,IAAAG,gBAAA,CAAAqB,WAAW,GAAE;QACrB;MACF,CAAC;MAAA,SAXKS,UAAUA,CAAAE,GAAA;QAAA;QAAAtC,aAAA,GAAAQ,CAAA;QAAAR,aAAA,GAAAG,CAAA;QAAA,OAAAgC,WAAA,CAAAP,KAAA,OAAAC,SAAA;MAAA;MAAA;MAAA7B,aAAA,GAAAG,CAAA;MAAA,OAAViC,UAAU;IAAA;EAAA;IAAAvB,GAAA;IAAAC,KAAA;MAAA;MAAAd,aAAA,GAAAQ,CAAA;MAAA,IAAA+B,cAAA;MAAA;MAAA,CAAAvC,aAAA,GAAAG,CAAA,YAAAa,kBAAA,CAAAL,OAAA,EAahB,WAAoBoB,OAA6B;QAAA;QAAA/B,aAAA,GAAAQ,CAAA;QAAAR,aAAA,GAAAG,CAAA;QAC/C,IAAI;UACF,IAAMc,GAAG;UAAA;UAAA,CAAAjB,aAAA,GAAAG,CAAA,QAAGD,cAAA,CAAAgB,YAAY,CAACC,OAAO,CAACqB,aAAa,EAAE;UAChD,IAAMnB,QAAQ;UAAA;UAAA,CAAArB,aAAA,GAAAG,CAAA,cAAS,IAAI,CAACM,UAAU,CAAC4B,IAAI,CAACpB,GAAG,EAAEc,OAAO,CAAC;UAAA;UAAA/B,aAAA,GAAAG,CAAA;UACzD,OAAO,IAAAE,iBAAA,CAAAkB,cAAc,EAACF,QAAQ,CAAC;QACjC,CAAC,CAAC,OAAOG,KAAU,EAAE;UAAA;UAAAxB,aAAA,GAAAG,CAAA;UACnB,IAAIqB,KAAK,YAAYlB,gBAAA,CAAAmB,WAAW,EAAE;YAAA;YAAAzB,aAAA,GAAA0B,CAAA;YAAA1B,aAAA,GAAAG,CAAA;YAChC,MAAMqB,KAAK;UACb;UAAA;UAAA;YAAAxB,aAAA,GAAA0B,CAAA;UAAA;UAAA1B,aAAA,GAAAG,CAAA;UACA,MAAM,IAAAG,gBAAA,CAAAqB,WAAW,GAAE;QACrB;MACF,CAAC;MAAA,SAXKa,aAAaA,CAAAC,GAAA;QAAA;QAAAzC,aAAA,GAAAQ,CAAA;QAAAR,aAAA,GAAAG,CAAA;QAAA,OAAAoC,cAAA,CAAAX,KAAA,OAAAC,SAAA;MAAA;MAAA;MAAA7B,aAAA,GAAAG,CAAA;MAAA,OAAbqC,aAAa;IAAA;EAAA;IAAA3B,GAAA;IAAAC,KAAA;MAAA;MAAAd,aAAA,GAAAQ,CAAA;MAAA,IAAAkC,aAAA;MAAA;MAAA,CAAA1C,aAAA,GAAAG,CAAA,YAAAa,kBAAA,CAAAL,OAAA,EAanB,WAAmBoB,OAA4B;QAAA;QAAA/B,aAAA,GAAAQ,CAAA;QAAAR,aAAA,GAAAG,CAAA;QAC7C,IAAI;UACF,IAAMc,GAAG;UAAA;UAAA,CAAAjB,aAAA,GAAAG,CAAA,QAAGD,cAAA,CAAAgB,YAAY,CAACC,OAAO,CAACwB,YAAY,EAAE;UAC/C,IAAMtB,QAAQ;UAAA;UAAA,CAAArB,aAAA,GAAAG,CAAA,cAAS,IAAI,CAACM,UAAU,CAAC4B,IAAI,CAACpB,GAAG,EAAEc,OAAO,CAAC;UAAA;UAAA/B,aAAA,GAAAG,CAAA;UACzD,OAAO,IAAAE,iBAAA,CAAAkB,cAAc,EAACF,QAAQ,CAAC;QACjC,CAAC,CAAC,OAAOG,KAAU,EAAE;UAAA;UAAAxB,aAAA,GAAAG,CAAA;UACnB,IAAIqB,KAAK,YAAYlB,gBAAA,CAAAmB,WAAW,EAAE;YAAA;YAAAzB,aAAA,GAAA0B,CAAA;YAAA1B,aAAA,GAAAG,CAAA;YAChC,MAAMqB,KAAK;UACb;UAAA;UAAA;YAAAxB,aAAA,GAAA0B,CAAA;UAAA;UAAA1B,aAAA,GAAAG,CAAA;UACA,MAAM,IAAAG,gBAAA,CAAAqB,WAAW,GAAE;QACrB;MACF,CAAC;MAAA,SAXKgB,YAAYA,CAAAC,GAAA;QAAA;QAAA5C,aAAA,GAAAQ,CAAA;QAAAR,aAAA,GAAAG,CAAA;QAAA,OAAAuC,aAAA,CAAAd,KAAA,OAAAC,SAAA;MAAA;MAAA;MAAA7B,aAAA,GAAAG,CAAA;MAAA,OAAZwC,YAAY;IAAA;EAAA;AAAA;AAAA;AAAA3C,aAAA,GAAAG,CAAA;AAvDpB0C,OAAA,CAAAtC,uBAAA,GAAAA,uBAAA", "ignoreList": []}