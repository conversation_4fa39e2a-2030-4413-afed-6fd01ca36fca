f10fd9d1bb529001c12e5153eab2915c
"use strict";

/* istanbul ignore next */
function cov_178tk27m6x() {
  var path = "/Users/<USER>/projects/react-native/payment-module/src/data/repositories/CustomerRepository.ts";
  var hash = "075a845a19fe2b3fd35ffd09f0e1d2c7c02f39df";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/projects/react-native/payment-module/src/data/repositories/CustomerRepository.ts",
    statementMap: {
      "0": {
        start: {
          line: 3,
          column: 29
        },
        end: {
          line: 3,
          column: 84
        }
      },
      "1": {
        start: {
          line: 4,
          column: 25
        },
        end: {
          line: 4,
          column: 99
        }
      },
      "2": {
        start: {
          line: 5,
          column: 23
        },
        end: {
          line: 5,
          column: 95
        }
      },
      "3": {
        start: {
          line: 6,
          column: 20
        },
        end: {
          line: 6,
          column: 89
        }
      },
      "4": {
        start: {
          line: 7,
          column: 0
        },
        end: {
          line: 9,
          column: 3
        }
      },
      "5": {
        start: {
          line: 10,
          column: 0
        },
        end: {
          line: 10,
          column: 36
        }
      },
      "6": {
        start: {
          line: 11,
          column: 25
        },
        end: {
          line: 11,
          column: 75
        }
      },
      "7": {
        start: {
          line: 12,
          column: 19
        },
        end: {
          line: 12,
          column: 52
        }
      },
      "8": {
        start: {
          line: 13,
          column: 25
        },
        end: {
          line: 30,
          column: 3
        }
      },
      "9": {
        start: {
          line: 15,
          column: 4
        },
        end: {
          line: 15,
          column: 60
        }
      },
      "10": {
        start: {
          line: 16,
          column: 4
        },
        end: {
          line: 16,
          column: 45
        }
      },
      "11": {
        start: {
          line: 18,
          column: 2
        },
        end: {
          line: 29,
          column: 6
        }
      },
      "12": {
        start: {
          line: 21,
          column: 24
        },
        end: {
          line: 23,
          column: 8
        }
      },
      "13": {
        start: {
          line: 22,
          column: 8
        },
        end: {
          line: 22,
          column: 129
        }
      },
      "14": {
        start: {
          line: 25,
          column: 8
        },
        end: {
          line: 25,
          column: 50
        }
      },
      "15": {
        start: {
          line: 27,
          column: 6
        },
        end: {
          line: 27,
          column: 24
        }
      },
      "16": {
        start: {
          line: 31,
          column: 0
        },
        end: {
          line: 31,
          column: 48
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 13,
            column: 25
          },
          end: {
            line: 13,
            column: 26
          }
        },
        loc: {
          start: {
            line: 13,
            column: 37
          },
          end: {
            line: 30,
            column: 1
          }
        },
        line: 13
      },
      "1": {
        name: "CustomerRepository",
        decl: {
          start: {
            line: 14,
            column: 11
          },
          end: {
            line: 14,
            column: 29
          }
        },
        loc: {
          start: {
            line: 14,
            column: 48
          },
          end: {
            line: 17,
            column: 3
          }
        },
        line: 14
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 20,
            column: 11
          },
          end: {
            line: 20,
            column: 12
          }
        },
        loc: {
          start: {
            line: 20,
            column: 23
          },
          end: {
            line: 28,
            column: 5
          }
        },
        line: 20
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 21,
            column: 56
          },
          end: {
            line: 21,
            column: 57
          }
        },
        loc: {
          start: {
            line: 21,
            column: 69
          },
          end: {
            line: 23,
            column: 7
          }
        },
        line: 21
      },
      "4": {
        name: "getProfile",
        decl: {
          start: {
            line: 24,
            column: 15
          },
          end: {
            line: 24,
            column: 25
          }
        },
        loc: {
          start: {
            line: 24,
            column: 28
          },
          end: {
            line: 26,
            column: 7
          }
        },
        line: 24
      }
    },
    branchMap: {},
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0
    },
    b: {},
    inputSourceMap: {
      version: 3,
      file: undefined,
      names: ["GetProfileMapper_1", "require", "HandleData_1", "CustomerRepository", "remoteDataSource", "_classCallCheck2", "default", "_createClass2", "key", "value", "_getProfile", "_asyncToGenerator2", "handleData", "getProfile", "mapGetProfileResponseToModel", "apply", "arguments", "exports"],
      sourceRoot: undefined,
      sources: ["/Users/<USER>/projects/react-native/payment-module/src/data/repositories/CustomerRepository.ts"],
      sourcesContent: ["import {mapGetProfileResponseToModel} from '../mappers/get-profile/GetProfileMapper';\nimport {handleData} from '../../utils/HandleData';\nimport {GetProfileModel} from '../../domain/entities/get-profile/GetProfileModel';\nimport {ICustomerDataSource} from '../datasources/ICustomerDataSource';\nimport {BaseResponse} from '../../core/BaseResponse';\nimport {ICustomerRepository} from '../../domain/repositories/ICustomerRepository';\n\nexport class CustomerRepository implements ICustomerRepository {\n  private remoteDataSource: ICustomerDataSource;\n\n  constructor(remoteDataSource: ICustomerDataSource) {\n    this.remoteDataSource = remoteDataSource;\n  }\n\n  async getProfile(): Promise<BaseResponse<GetProfileModel>> {\n    return handleData<GetProfileModel>(this.remoteDataSource.getProfile(), mapGetProfileResponseToModel);\n  }\n}\n"],
      mappings: ";;;;;;;;;;AAAA,IAAAA,kBAAA,GAAAC,OAAA;AACA,IAAAC,YAAA,GAAAD,OAAA;AAAkD,IAMrCE,kBAAkB;EAG7B,SAAAA,mBAAYC,gBAAqC;IAAA,IAAAC,gBAAA,CAAAC,OAAA,QAAAH,kBAAA;IAC/C,IAAI,CAACC,gBAAgB,GAAGA,gBAAgB;EAC1C;EAAC,WAAAG,aAAA,CAAAD,OAAA,EAAAH,kBAAA;IAAAK,GAAA;IAAAC,KAAA;MAAA,IAAAC,WAAA,OAAAC,kBAAA,CAAAL,OAAA,EAED,aAAgB;QACd,OAAO,IAAAJ,YAAA,CAAAU,UAAU,EAAkB,IAAI,CAACR,gBAAgB,CAACS,UAAU,EAAE,EAAEb,kBAAA,CAAAc,4BAA4B,CAAC;MACtG,CAAC;MAAA,SAFKD,UAAUA,CAAA;QAAA,OAAAH,WAAA,CAAAK,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAVH,UAAU;IAAA;EAAA;AAAA;AAPlBI,OAAA,CAAAd,kBAAA,GAAAA,kBAAA",
      ignoreList: []
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "075a845a19fe2b3fd35ffd09f0e1d2c7c02f39df"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_178tk27m6x = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_178tk27m6x();
var _interopRequireDefault =
/* istanbul ignore next */
(cov_178tk27m6x().s[0]++, require("@babel/runtime/helpers/interopRequireDefault"));
var _asyncToGenerator2 =
/* istanbul ignore next */
(cov_178tk27m6x().s[1]++, _interopRequireDefault(require("@babel/runtime/helpers/asyncToGenerator")));
var _classCallCheck2 =
/* istanbul ignore next */
(cov_178tk27m6x().s[2]++, _interopRequireDefault(require("@babel/runtime/helpers/classCallCheck")));
var _createClass2 =
/* istanbul ignore next */
(cov_178tk27m6x().s[3]++, _interopRequireDefault(require("@babel/runtime/helpers/createClass")));
/* istanbul ignore next */
cov_178tk27m6x().s[4]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_178tk27m6x().s[5]++;
exports.CustomerRepository = void 0;
var GetProfileMapper_1 =
/* istanbul ignore next */
(cov_178tk27m6x().s[6]++, require("../mappers/get-profile/GetProfileMapper"));
var HandleData_1 =
/* istanbul ignore next */
(cov_178tk27m6x().s[7]++, require("../../utils/HandleData"));
var CustomerRepository =
/* istanbul ignore next */
(cov_178tk27m6x().s[8]++, function () {
  /* istanbul ignore next */
  cov_178tk27m6x().f[0]++;
  function CustomerRepository(remoteDataSource) {
    /* istanbul ignore next */
    cov_178tk27m6x().f[1]++;
    cov_178tk27m6x().s[9]++;
    (0, _classCallCheck2.default)(this, CustomerRepository);
    /* istanbul ignore next */
    cov_178tk27m6x().s[10]++;
    this.remoteDataSource = remoteDataSource;
  }
  /* istanbul ignore next */
  cov_178tk27m6x().s[11]++;
  return (0, _createClass2.default)(CustomerRepository, [{
    key: "getProfile",
    value: function () {
      /* istanbul ignore next */
      cov_178tk27m6x().f[2]++;
      var _getProfile =
      /* istanbul ignore next */
      (cov_178tk27m6x().s[12]++, (0, _asyncToGenerator2.default)(function* () {
        /* istanbul ignore next */
        cov_178tk27m6x().f[3]++;
        cov_178tk27m6x().s[13]++;
        return (0, HandleData_1.handleData)(this.remoteDataSource.getProfile(), GetProfileMapper_1.mapGetProfileResponseToModel);
      }));
      function getProfile() {
        /* istanbul ignore next */
        cov_178tk27m6x().f[4]++;
        cov_178tk27m6x().s[14]++;
        return _getProfile.apply(this, arguments);
      }
      /* istanbul ignore next */
      cov_178tk27m6x().s[15]++;
      return getProfile;
    }()
  }]);
}());
/* istanbul ignore next */
cov_178tk27m6x().s[16]++;
exports.CustomerRepository = CustomerRepository;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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