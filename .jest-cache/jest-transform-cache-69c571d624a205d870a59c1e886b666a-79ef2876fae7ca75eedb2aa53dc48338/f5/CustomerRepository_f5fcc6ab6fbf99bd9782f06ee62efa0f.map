{"version": 3, "names": ["cov_178tk27m6x", "actualCoverage", "GetProfileMapper_1", "s", "require", "HandleData_1", "CustomerRepository", "f", "remoteDataSource", "_classCallCheck2", "default", "_createClass2", "key", "value", "_getProfile", "_asyncToGenerator2", "handleData", "getProfile", "mapGetProfileResponseToModel", "apply", "arguments", "exports"], "sources": ["/Users/<USER>/projects/react-native/payment-module/src/data/repositories/CustomerRepository.ts"], "sourcesContent": ["import {mapGetProfileResponseToModel} from '../mappers/get-profile/GetProfileMapper';\nimport {handleData} from '../../utils/HandleData';\nimport {GetProfileModel} from '../../domain/entities/get-profile/GetProfileModel';\nimport {ICustomerDataSource} from '../datasources/ICustomerDataSource';\nimport {BaseResponse} from '../../core/BaseResponse';\nimport {ICustomerRepository} from '../../domain/repositories/ICustomerRepository';\n\nexport class CustomerRepository implements ICustomerRepository {\n  private remoteDataSource: ICustomerDataSource;\n\n  constructor(remoteDataSource: ICustomerDataSource) {\n    this.remoteDataSource = remoteDataSource;\n  }\n\n  async getProfile(): Promise<BaseResponse<GetProfileModel>> {\n    return handleData<GetProfileModel>(this.remoteDataSource.getProfile(), mapGetProfileResponseToModel);\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAWS;IAAAA,cAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,cAAA;;;;;;;;;;;;;;;;;;;;;AAXT,IAAAE,kBAAA;AAAA;AAAA,CAAAF,cAAA,GAAAG,CAAA,OAAAC,OAAA;AACA,IAAAC,YAAA;AAAA;AAAA,CAAAL,cAAA,GAAAG,CAAA,OAAAC,OAAA;AAAkD,IAMrCE,kBAAkB;AAAA;AAAA,CAAAN,cAAA,GAAAG,CAAA;EAAA;EAAAH,cAAA,GAAAO,CAAA;EAG7B,SAAAD,mBAAYE,gBAAqC;IAAA;IAAAR,cAAA,GAAAO,CAAA;IAAAP,cAAA,GAAAG,CAAA;IAAA,IAAAM,gBAAA,CAAAC,OAAA,QAAAJ,kBAAA;IAAA;IAAAN,cAAA,GAAAG,CAAA;IAC/C,IAAI,CAACK,gBAAgB,GAAGA,gBAAgB;EAC1C;EAAA;EAAAR,cAAA,GAAAG,CAAA;EAAC,WAAAQ,aAAA,CAAAD,OAAA,EAAAJ,kBAAA;IAAAM,GAAA;IAAAC,KAAA;MAAA;MAAAb,cAAA,GAAAO,CAAA;MAAA,IAAAO,WAAA;MAAA;MAAA,CAAAd,cAAA,GAAAG,CAAA,YAAAY,kBAAA,CAAAL,OAAA,EAED,aAAgB;QAAA;QAAAV,cAAA,GAAAO,CAAA;QAAAP,cAAA,GAAAG,CAAA;QACd,OAAO,IAAAE,YAAA,CAAAW,UAAU,EAAkB,IAAI,CAACR,gBAAgB,CAACS,UAAU,EAAE,EAAEf,kBAAA,CAAAgB,4BAA4B,CAAC;MACtG,CAAC;MAAA,SAFKD,UAAUA,CAAA;QAAA;QAAAjB,cAAA,GAAAO,CAAA;QAAAP,cAAA,GAAAG,CAAA;QAAA,OAAAW,WAAA,CAAAK,KAAA,OAAAC,SAAA;MAAA;MAAA;MAAApB,cAAA,GAAAG,CAAA;MAAA,OAAVc,UAAU;IAAA;EAAA;AAAA;AAAA;AAAAjB,cAAA,GAAAG,CAAA;AAPlBkB,OAAA,CAAAf,kBAAA,GAAAA,kBAAA", "ignoreList": []}