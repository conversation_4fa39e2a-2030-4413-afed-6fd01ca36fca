{"version": 3, "names": ["Object", "defineProperty", "exports", "value", "useAnimatedProps", "_useAnimatedStyle", "require", "_PlatformChecker", "useAnimatedPropsJS", "updater", "deps", "adapters", "useAnimatedStyle", "useAnimatedPropsNative", "shouldBeUseWeb"], "sources": ["../../../src/hook/useAnimatedProps.ts"], "sourcesContent": [null], "mappings": "AAAA,YAAY;;AAAAA,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,KAAA;AAAA;AAAAD,OAAA,CAAAE,gBAAA;AACZ,IAAAC,iBAAA,GAAAC,OAAA;AAEA,IAAAC,gBAAA,GAAAD,OAAA;AAeA,SAASE,kBAAkBA,CACzBC,OAAoB,EACpBC,IAA4B,EAC5BC,QAGQ,EACR;EACA,OAAQ,IAAAC,kCAAgB,EACtBH,OAAO,EACPC,IAAI,EACJC,QAAQ,EACR,IACF,CAAC;AACH;AAEA,IAAME,sBAAsB,GAAGD,kCAAgB;AAgBxC,IAAMR,gBAAkC,GAAAF,OAAA,CAAAE,gBAAA,GAAG,IAAAU,+BAAc,EAAC,CAAC,GAC7DN,kBAAkB,GACnBK,sBAAsB", "ignoreList": []}