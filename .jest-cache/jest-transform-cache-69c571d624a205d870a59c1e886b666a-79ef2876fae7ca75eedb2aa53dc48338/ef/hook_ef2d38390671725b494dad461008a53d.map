{"version": 3, "names": ["cov_24y73qvb38", "actualCoverage", "s", "native_1", "require", "react_1", "Constants_1", "usePaymentResultDetail", "f", "_paymentInfo$paymentV", "route", "useRoute", "navigation", "useNavigation", "paymentInfo", "params", "paymentType", "paymentValidate", "b", "isTopupAccount", "PAYMENT_TYPE", "TOPUP_ACCOUNT", "totalAmount", "useMemo", "_paymentInfo$paymentV2", "_paymentInfo$paymentV3", "_paymentInfo$paymentV4", "_paymentInfo$billInfo", "TOPUP_CREDIT", "QR_PAYMENT", "_paymentInfo$paymentV5", "transferTransactionInformation", "instructedAmount", "amount", "_totalAmount", "billInfo", "billList", "reduce", "sum", "bill", "goBack", "exports", "default"], "sources": ["/Users/<USER>/projects/react-native/payment-module/src/presentation/payment-result-detail/hook.ts"], "sourcesContent": ["import {RouteProp, useNavigation, useRoute, NavigationProp} from '@react-navigation/native';\nimport {PaymentStackParamList} from '../../navigation/PaymentStack';\nimport {useMemo} from 'react';\nimport {PAYMENT_TYPE} from '../../commons/Constants';\n\nconst usePaymentResultDetail = () => {\n  const route = useRoute<RouteProp<PaymentStackParamList, 'PaymentResultDetailScreen'>>();\n  const navigation = useNavigation<NavigationProp<PaymentStackParamList, 'PaymentResultDetailScreen'>>();\n  const {paymentInfo} = route.params;\n  const paymentType = paymentInfo.paymentValidate?.paymentType;\n  const isTopupAccount = paymentType === PAYMENT_TYPE.TOPUP_ACCOUNT;\n\n  const totalAmount = useMemo(() => {\n    if (\n      paymentInfo.paymentValidate?.paymentType === PAYMENT_TYPE.TOPUP_ACCOUNT ||\n      paymentInfo.paymentValidate?.paymentType === PAYMENT_TYPE.TOPUP_CREDIT ||\n      paymentInfo.paymentValidate?.paymentType === PAYMENT_TYPE.QR_PAYMENT\n    ) {\n      return paymentInfo.paymentValidate.transferTransactionInformation?.instructedAmount?.amount;\n    }\n    const _totalAmount = paymentInfo?.billInfo?.billList?.reduce((sum, bill) => sum + (bill.amount || 0), 0);\n    return _totalAmount;\n  }, [paymentInfo]);\n\n  const goBack = () => {\n    navigation.goBack();\n  };\n\n  return {\n    paymentInfo,\n    totalAmount,\n    goBack,\n    isTopupAccount,\n  };\n};\n\nexport type Props = ReturnType<typeof usePaymentResultDetail>;\n\nexport default usePaymentResultDetail;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAYQ;IAAAA,cAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,cAAA;AAAAA,cAAA,GAAAE,CAAA;;;;AAZR,IAAAC,QAAA;AAAA;AAAA,CAAAH,cAAA,GAAAE,CAAA,OAAAE,OAAA;AAEA,IAAAC,OAAA;AAAA;AAAA,CAAAL,cAAA,GAAAE,CAAA,OAAAE,OAAA;AACA,IAAAE,WAAA;AAAA;AAAA,CAAAN,cAAA,GAAAE,CAAA,OAAAE,OAAA;AAAA;AAAAJ,cAAA,GAAAE,CAAA;AAEA,IAAMK,sBAAsB,GAAG,SAAzBA,sBAAsBA,CAAA,EAAQ;EAAA;EAAAP,cAAA,GAAAQ,CAAA;EAAA,IAAAC,qBAAA;EAClC,IAAMC,KAAK;EAAA;EAAA,CAAAV,cAAA,GAAAE,CAAA,OAAG,IAAAC,QAAA,CAAAQ,QAAQ,GAAiE;EACvF,IAAMC,UAAU;EAAA;EAAA,CAAAZ,cAAA,GAAAE,CAAA,OAAG,IAAAC,QAAA,CAAAU,aAAa,GAAsE;EACtG,IAAOC,WAAW;EAAA;EAAA,CAAAd,cAAA,GAAAE,CAAA,OAAIQ,KAAK,CAACK,MAAM,CAA3BD,WAAW;EAClB,IAAME,WAAW;EAAA;EAAA,CAAAhB,cAAA,GAAAE,CAAA,QAAAO,qBAAA,GAAGK,WAAW,CAACG,eAAe;EAAA;EAAA,CAAAjB,cAAA,GAAAkB,CAAA;EAAA;EAAA,CAAAlB,cAAA,GAAAkB,CAAA,UAA3BT,qBAAA,CAA6BO,WAAW;EAC5D,IAAMG,cAAc;EAAA;EAAA,CAAAnB,cAAA,GAAAE,CAAA,OAAGc,WAAW,KAAKV,WAAA,CAAAc,YAAY,CAACC,aAAa;EAEjE,IAAMC,WAAW;EAAA;EAAA,CAAAtB,cAAA,GAAAE,CAAA,QAAG,IAAAG,OAAA,CAAAkB,OAAO,EAAC,YAAK;IAAA;IAAAvB,cAAA,GAAAQ,CAAA;IAAA,IAAAgB,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,qBAAA;IAAA;IAAA3B,cAAA,GAAAE,CAAA;IAC/B;IACE;IAAA,CAAAF,cAAA,GAAAkB,CAAA,YAAAM,sBAAA,GAAAV,WAAW,CAACG,eAAe;IAAA;IAAA,CAAAjB,cAAA,GAAAkB,CAAA;IAAA;IAAA,CAAAlB,cAAA,GAAAkB,CAAA,UAA3BM,sBAAA,CAA6BR,WAAW,OAAKV,WAAA,CAAAc,YAAY,CAACC,aAAa;IAAA;IAAA,CAAArB,cAAA,GAAAkB,CAAA,UACvE,EAAAO,sBAAA,GAAAX,WAAW,CAACG,eAAe;IAAA;IAAA,CAAAjB,cAAA,GAAAkB,CAAA;IAAA;IAAA,CAAAlB,cAAA,GAAAkB,CAAA,UAA3BO,sBAAA,CAA6BT,WAAW,OAAKV,WAAA,CAAAc,YAAY,CAACQ,YAAY;IAAA;IAAA,CAAA5B,cAAA,GAAAkB,CAAA,UACtE,EAAAQ,sBAAA,GAAAZ,WAAW,CAACG,eAAe;IAAA;IAAA,CAAAjB,cAAA,GAAAkB,CAAA;IAAA;IAAA,CAAAlB,cAAA,GAAAkB,CAAA,UAA3BQ,sBAAA,CAA6BV,WAAW,OAAKV,WAAA,CAAAc,YAAY,CAACS,UAAU,GACpE;MAAA;MAAA7B,cAAA,GAAAkB,CAAA;MAAA,IAAAY,sBAAA;MAAA;MAAA9B,cAAA,GAAAE,CAAA;MACA,kCAAAF,cAAA,GAAAkB,CAAA,WAAAY,sBAAA,GAAOhB,WAAW,CAACG,eAAe,CAACc,8BAA8B;MAAA;MAAA,CAAA/B,cAAA,GAAAkB,CAAA,WAAAY,sBAAA,GAA1DA,sBAAA,CAA4DE,gBAAgB;MAAA;MAAA,CAAAhC,cAAA,GAAAkB,CAAA;MAAA;MAAA,CAAAlB,cAAA,GAAAkB,CAAA,UAA5EY,sBAAA,CAA8EG,MAAM;IAC7F;IAAA;IAAA;MAAAjC,cAAA,GAAAkB,CAAA;IAAA;IACA,IAAMgB,YAAY;IAAA;IAAA,CAAAlC,cAAA,GAAAE,CAAA;IAAG;IAAA,CAAAF,cAAA,GAAAkB,CAAA,UAAAJ,WAAW;IAAA;IAAA,CAAAd,cAAA,GAAAkB,CAAA,WAAAS,qBAAA,GAAXb,WAAW,CAAEqB,QAAQ;IAAA;IAAA,CAAAnC,cAAA,GAAAkB,CAAA,WAAAS,qBAAA,GAArBA,qBAAA,CAAuBS,QAAQ;IAAA;IAAA,CAAApC,cAAA,GAAAkB,CAAA;IAAA;IAAA,CAAAlB,cAAA,GAAAkB,CAAA,UAA/BS,qBAAA,CAAiCU,MAAM,CAAC,UAACC,GAAG,EAAEC,IAAI;MAAA;MAAAvC,cAAA,GAAAQ,CAAA;MAAAR,cAAA,GAAAE,CAAA;MAAA,OAAKoC,GAAG;MAAI;MAAA,CAAAtC,cAAA,GAAAkB,CAAA,WAAAqB,IAAI,CAACN,MAAM;MAAA;MAAA,CAAAjC,cAAA,GAAAkB,CAAA,WAAI,CAAC,EAAC;IAAA,GAAE,CAAC,CAAC;IAAA;IAAAlB,cAAA,GAAAE,CAAA;IACxG,OAAOgC,YAAY;EACrB,CAAC,EAAE,CAACpB,WAAW,CAAC,CAAC;EAAA;EAAAd,cAAA,GAAAE,CAAA;EAEjB,IAAMsC,MAAM,GAAG,SAATA,MAAMA,CAAA,EAAQ;IAAA;IAAAxC,cAAA,GAAAQ,CAAA;IAAAR,cAAA,GAAAE,CAAA;IAClBU,UAAU,CAAC4B,MAAM,EAAE;EACrB,CAAC;EAAA;EAAAxC,cAAA,GAAAE,CAAA;EAED,OAAO;IACLY,WAAW,EAAXA,WAAW;IACXQ,WAAW,EAAXA,WAAW;IACXkB,MAAM,EAANA,MAAM;IACNrB,cAAc,EAAdA;GACD;AACH,CAAC;AAAA;AAAAnB,cAAA,GAAAE,CAAA;AAIDuC,OAAA,CAAAC,OAAA,GAAenC,sBAAsB", "ignoreList": []}