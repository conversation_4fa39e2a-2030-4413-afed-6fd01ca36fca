9d14a8e15c71131ca43b7453224102a5
'use strict';

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.useAnimatedProps = void 0;
var _useAnimatedStyle = require("./useAnimatedStyle.js");
var _PlatformChecker = require("../PlatformChecker.js");
function useAnimatedPropsJS(updater, deps, adapters) {
  return (0, _useAnimatedStyle.useAnimatedStyle)(updater, deps, adapters, true);
}
var useAnimatedPropsNative = _useAnimatedStyle.useAnimatedStyle;
var useAnimatedProps = exports.useAnimatedProps = (0, _PlatformChecker.shouldBeUseWeb)() ? useAnimatedPropsJS : useAnimatedPropsNative;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJPYmplY3QiLCJkZWZpbmVQcm9wZXJ0eSIsImV4cG9ydHMiLCJ2YWx1ZSIsInVzZUFuaW1hdGVkUHJvcHMiLCJfdXNlQW5pbWF0ZWRTdHlsZSIsInJlcXVpcmUiLCJfUGxhdGZvcm1DaGVja2VyIiwidXNlQW5pbWF0ZWRQcm9wc0pTIiwidXBkYXRlciIsImRlcHMiLCJhZGFwdGVycyIsInVzZUFuaW1hdGVkU3R5bGUiLCJ1c2VBbmltYXRlZFByb3BzTmF0aXZlIiwic2hvdWxkQmVVc2VXZWIiXSwic291cmNlcyI6WyIuLi8uLi8uLi9zcmMvaG9vay91c2VBbmltYXRlZFByb3BzLnRzIl0sInNvdXJjZXNDb250ZW50IjpbbnVsbF0sIm1hcHBpbmdzIjoiQUFBQSxZQUFZOztBQUFBQSxNQUFBLENBQUFDLGNBQUEsQ0FBQUMsT0FBQTtFQUFBQyxLQUFBO0FBQUE7QUFBQUQsT0FBQSxDQUFBRSxnQkFBQTtBQUNaLElBQUFDLGlCQUFBLEdBQUFDLE9BQUE7QUFFQSxJQUFBQyxnQkFBQSxHQUFBRCxPQUFBO0FBZUEsU0FBU0Usa0JBQWtCQSxDQUN6QkMsT0FBb0IsRUFDcEJDLElBQTRCLEVBQzVCQyxRQUdRLEVBQ1I7RUFDQSxPQUFRLElBQUFDLGtDQUFnQixFQUN0QkgsT0FBTyxFQUNQQyxJQUFJLEVBQ0pDLFFBQVEsRUFDUixJQUNGLENBQUM7QUFDSDtBQUVBLElBQU1FLHNCQUFzQixHQUFHRCxrQ0FBZ0I7QUFnQnhDLElBQU1SLGdCQUFrQyxHQUFBRixPQUFBLENBQUFFLGdCQUFBLEdBQUcsSUFBQVUsK0JBQWMsRUFBQyxDQUFDLEdBQzdETixrQkFBa0IsR0FDbkJLLHNCQUFzQiIsImlnbm9yZUxpc3QiOltdfQ==