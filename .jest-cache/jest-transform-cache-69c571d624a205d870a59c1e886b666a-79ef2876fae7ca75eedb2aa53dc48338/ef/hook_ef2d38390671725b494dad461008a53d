65fd25d26ec0fccfc85487da1ca3c084
"use strict";

/* istanbul ignore next */
function cov_24y73qvb38() {
  var path = "/Users/<USER>/projects/react-native/payment-module/src/presentation/payment-result-detail/hook.ts";
  var hash = "27908470b6a35df8c7343652d58f811f9e7c979b";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/projects/react-native/payment-module/src/presentation/payment-result-detail/hook.ts",
    statementMap: {
      "0": {
        start: {
          line: 3,
          column: 0
        },
        end: {
          line: 5,
          column: 3
        }
      },
      "1": {
        start: {
          line: 6,
          column: 15
        },
        end: {
          line: 6,
          column: 50
        }
      },
      "2": {
        start: {
          line: 7,
          column: 14
        },
        end: {
          line: 7,
          column: 30
        }
      },
      "3": {
        start: {
          line: 8,
          column: 18
        },
        end: {
          line: 8,
          column: 52
        }
      },
      "4": {
        start: {
          line: 9,
          column: 29
        },
        end: {
          line: 36,
          column: 1
        }
      },
      "5": {
        start: {
          line: 11,
          column: 14
        },
        end: {
          line: 11,
          column: 38
        }
      },
      "6": {
        start: {
          line: 12,
          column: 19
        },
        end: {
          line: 12,
          column: 48
        }
      },
      "7": {
        start: {
          line: 13,
          column: 20
        },
        end: {
          line: 13,
          column: 44
        }
      },
      "8": {
        start: {
          line: 14,
          column: 20
        },
        end: {
          line: 14,
          column: 126
        }
      },
      "9": {
        start: {
          line: 15,
          column: 23
        },
        end: {
          line: 15,
          column: 77
        }
      },
      "10": {
        start: {
          line: 16,
          column: 20
        },
        end: {
          line: 26,
          column: 19
        }
      },
      "11": {
        start: {
          line: 18,
          column: 4
        },
        end: {
          line: 21,
          column: 5
        }
      },
      "12": {
        start: {
          line: 20,
          column: 6
        },
        end: {
          line: 20,
          column: 226
        }
      },
      "13": {
        start: {
          line: 22,
          column: 23
        },
        end: {
          line: 24,
          column: 9
        }
      },
      "14": {
        start: {
          line: 23,
          column: 6
        },
        end: {
          line: 23,
          column: 38
        }
      },
      "15": {
        start: {
          line: 25,
          column: 4
        },
        end: {
          line: 25,
          column: 24
        }
      },
      "16": {
        start: {
          line: 27,
          column: 15
        },
        end: {
          line: 29,
          column: 3
        }
      },
      "17": {
        start: {
          line: 28,
          column: 4
        },
        end: {
          line: 28,
          column: 24
        }
      },
      "18": {
        start: {
          line: 30,
          column: 2
        },
        end: {
          line: 35,
          column: 4
        }
      },
      "19": {
        start: {
          line: 37,
          column: 0
        },
        end: {
          line: 37,
          column: 41
        }
      }
    },
    fnMap: {
      "0": {
        name: "usePaymentResultDetail",
        decl: {
          start: {
            line: 9,
            column: 38
          },
          end: {
            line: 9,
            column: 60
          }
        },
        loc: {
          start: {
            line: 9,
            column: 63
          },
          end: {
            line: 36,
            column: 1
          }
        },
        line: 9
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 16,
            column: 41
          },
          end: {
            line: 16,
            column: 42
          }
        },
        loc: {
          start: {
            line: 16,
            column: 53
          },
          end: {
            line: 26,
            column: 3
          }
        },
        line: 16
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 22,
            column: 209
          },
          end: {
            line: 22,
            column: 210
          }
        },
        loc: {
          start: {
            line: 22,
            column: 230
          },
          end: {
            line: 24,
            column: 5
          }
        },
        line: 22
      },
      "3": {
        name: "goBack",
        decl: {
          start: {
            line: 27,
            column: 24
          },
          end: {
            line: 27,
            column: 30
          }
        },
        loc: {
          start: {
            line: 27,
            column: 33
          },
          end: {
            line: 29,
            column: 3
          }
        },
        line: 27
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 14,
            column: 20
          },
          end: {
            line: 14,
            column: 126
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 14,
            column: 84
          },
          end: {
            line: 14,
            column: 90
          }
        }, {
          start: {
            line: 14,
            column: 93
          },
          end: {
            line: 14,
            column: 126
          }
        }],
        line: 14
      },
      "1": {
        loc: {
          start: {
            line: 18,
            column: 4
          },
          end: {
            line: 21,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 18,
            column: 4
          },
          end: {
            line: 21,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 18
      },
      "2": {
        loc: {
          start: {
            line: 18,
            column: 8
          },
          end: {
            line: 18,
            column: 471
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 18,
            column: 8
          },
          end: {
            line: 18,
            column: 161
          }
        }, {
          start: {
            line: 18,
            column: 165
          },
          end: {
            line: 18,
            column: 317
          }
        }, {
          start: {
            line: 18,
            column: 321
          },
          end: {
            line: 18,
            column: 471
          }
        }],
        line: 18
      },
      "3": {
        loc: {
          start: {
            line: 18,
            column: 9
          },
          end: {
            line: 18,
            column: 117
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 18,
            column: 74
          },
          end: {
            line: 18,
            column: 80
          }
        }, {
          start: {
            line: 18,
            column: 83
          },
          end: {
            line: 18,
            column: 117
          }
        }],
        line: 18
      },
      "4": {
        loc: {
          start: {
            line: 18,
            column: 166
          },
          end: {
            line: 18,
            column: 274
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 18,
            column: 231
          },
          end: {
            line: 18,
            column: 237
          }
        }, {
          start: {
            line: 18,
            column: 240
          },
          end: {
            line: 18,
            column: 274
          }
        }],
        line: 18
      },
      "5": {
        loc: {
          start: {
            line: 18,
            column: 322
          },
          end: {
            line: 18,
            column: 430
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 18,
            column: 387
          },
          end: {
            line: 18,
            column: 393
          }
        }, {
          start: {
            line: 18,
            column: 396
          },
          end: {
            line: 18,
            column: 430
          }
        }],
        line: 18
      },
      "6": {
        loc: {
          start: {
            line: 20,
            column: 13
          },
          end: {
            line: 20,
            column: 225
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 20,
            column: 187
          },
          end: {
            line: 20,
            column: 193
          }
        }, {
          start: {
            line: 20,
            column: 196
          },
          end: {
            line: 20,
            column: 225
          }
        }],
        line: 20
      },
      "7": {
        loc: {
          start: {
            line: 20,
            column: 13
          },
          end: {
            line: 20,
            column: 184
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 20,
            column: 13
          },
          end: {
            line: 20,
            column: 106
          }
        }, {
          start: {
            line: 20,
            column: 110
          },
          end: {
            line: 20,
            column: 184
          }
        }],
        line: 20
      },
      "8": {
        loc: {
          start: {
            line: 22,
            column: 23
          },
          end: {
            line: 24,
            column: 9
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 22,
            column: 171
          },
          end: {
            line: 22,
            column: 177
          }
        }, {
          start: {
            line: 22,
            column: 180
          },
          end: {
            line: 24,
            column: 9
          }
        }],
        line: 22
      },
      "9": {
        loc: {
          start: {
            line: 22,
            column: 23
          },
          end: {
            line: 22,
            column: 168
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 22,
            column: 23
          },
          end: {
            line: 22,
            column: 42
          }
        }, {
          start: {
            line: 22,
            column: 46
          },
          end: {
            line: 22,
            column: 100
          }
        }, {
          start: {
            line: 22,
            column: 104
          },
          end: {
            line: 22,
            column: 168
          }
        }],
        line: 22
      },
      "10": {
        loc: {
          start: {
            line: 23,
            column: 20
          },
          end: {
            line: 23,
            column: 36
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 23,
            column: 20
          },
          end: {
            line: 23,
            column: 31
          }
        }, {
          start: {
            line: 23,
            column: 35
          },
          end: {
            line: 23,
            column: 36
          }
        }],
        line: 23
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0
    },
    b: {
      "0": [0, 0],
      "1": [0, 0],
      "2": [0, 0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0, 0],
      "10": [0, 0]
    },
    inputSourceMap: {
      version: 3,
      file: undefined,
      names: ["native_1", "require", "react_1", "Constants_1", "usePaymentResultDetail", "_paymentInfo$paymentV", "route", "useRoute", "navigation", "useNavigation", "paymentInfo", "params", "paymentType", "paymentValidate", "isTopupAccount", "PAYMENT_TYPE", "TOPUP_ACCOUNT", "totalAmount", "useMemo", "_paymentInfo$paymentV2", "_paymentInfo$paymentV3", "_paymentInfo$paymentV4", "_paymentInfo$billInfo", "TOPUP_CREDIT", "QR_PAYMENT", "_paymentInfo$paymentV5", "transferTransactionInformation", "instructedAmount", "amount", "_totalAmount", "billInfo", "billList", "reduce", "sum", "bill", "goBack", "exports", "default"],
      sourceRoot: undefined,
      sources: ["/Users/<USER>/projects/react-native/payment-module/src/presentation/payment-result-detail/hook.ts"],
      sourcesContent: ["import {RouteProp, useNavigation, useRoute, NavigationProp} from '@react-navigation/native';\nimport {PaymentStackParamList} from '../../navigation/PaymentStack';\nimport {useMemo} from 'react';\nimport {PAYMENT_TYPE} from '../../commons/Constants';\n\nconst usePaymentResultDetail = () => {\n  const route = useRoute<RouteProp<PaymentStackParamList, 'PaymentResultDetailScreen'>>();\n  const navigation = useNavigation<NavigationProp<PaymentStackParamList, 'PaymentResultDetailScreen'>>();\n  const {paymentInfo} = route.params;\n  const paymentType = paymentInfo.paymentValidate?.paymentType;\n  const isTopupAccount = paymentType === PAYMENT_TYPE.TOPUP_ACCOUNT;\n\n  const totalAmount = useMemo(() => {\n    if (\n      paymentInfo.paymentValidate?.paymentType === PAYMENT_TYPE.TOPUP_ACCOUNT ||\n      paymentInfo.paymentValidate?.paymentType === PAYMENT_TYPE.TOPUP_CREDIT ||\n      paymentInfo.paymentValidate?.paymentType === PAYMENT_TYPE.QR_PAYMENT\n    ) {\n      return paymentInfo.paymentValidate.transferTransactionInformation?.instructedAmount?.amount;\n    }\n    const _totalAmount = paymentInfo?.billInfo?.billList?.reduce((sum, bill) => sum + (bill.amount || 0), 0);\n    return _totalAmount;\n  }, [paymentInfo]);\n\n  const goBack = () => {\n    navigation.goBack();\n  };\n\n  return {\n    paymentInfo,\n    totalAmount,\n    goBack,\n    isTopupAccount,\n  };\n};\n\nexport type Props = ReturnType<typeof usePaymentResultDetail>;\n\nexport default usePaymentResultDetail;\n"],
      mappings: ";;;;;AAAA,IAAAA,QAAA,GAAAC,OAAA;AAEA,IAAAC,OAAA,GAAAD,OAAA;AACA,IAAAE,WAAA,GAAAF,OAAA;AAEA,IAAMG,sBAAsB,GAAG,SAAzBA,sBAAsBA,CAAA,EAAQ;EAAA,IAAAC,qBAAA;EAClC,IAAMC,KAAK,GAAG,IAAAN,QAAA,CAAAO,QAAQ,GAAiE;EACvF,IAAMC,UAAU,GAAG,IAAAR,QAAA,CAAAS,aAAa,GAAsE;EACtG,IAAOC,WAAW,GAAIJ,KAAK,CAACK,MAAM,CAA3BD,WAAW;EAClB,IAAME,WAAW,IAAAP,qBAAA,GAAGK,WAAW,CAACG,eAAe,qBAA3BR,qBAAA,CAA6BO,WAAW;EAC5D,IAAME,cAAc,GAAGF,WAAW,KAAKT,WAAA,CAAAY,YAAY,CAACC,aAAa;EAEjE,IAAMC,WAAW,GAAG,IAAAf,OAAA,CAAAgB,OAAO,EAAC,YAAK;IAAA,IAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,qBAAA;IAC/B,IACE,EAAAH,sBAAA,GAAAT,WAAW,CAACG,eAAe,qBAA3BM,sBAAA,CAA6BP,WAAW,MAAKT,WAAA,CAAAY,YAAY,CAACC,aAAa,IACvE,EAAAI,sBAAA,GAAAV,WAAW,CAACG,eAAe,qBAA3BO,sBAAA,CAA6BR,WAAW,MAAKT,WAAA,CAAAY,YAAY,CAACQ,YAAY,IACtE,EAAAF,sBAAA,GAAAX,WAAW,CAACG,eAAe,qBAA3BQ,sBAAA,CAA6BT,WAAW,MAAKT,WAAA,CAAAY,YAAY,CAACS,UAAU,EACpE;MAAA,IAAAC,sBAAA;MACA,QAAAA,sBAAA,GAAOf,WAAW,CAACG,eAAe,CAACa,8BAA8B,cAAAD,sBAAA,GAA1DA,sBAAA,CAA4DE,gBAAgB,qBAA5EF,sBAAA,CAA8EG,MAAM;IAC7F;IACA,IAAMC,YAAY,GAAGnB,WAAW,aAAAY,qBAAA,GAAXZ,WAAW,CAAEoB,QAAQ,cAAAR,qBAAA,GAArBA,qBAAA,CAAuBS,QAAQ,qBAA/BT,qBAAA,CAAiCU,MAAM,CAAC,UAACC,GAAG,EAAEC,IAAI;MAAA,OAAKD,GAAG,IAAIC,IAAI,CAACN,MAAM,IAAI,CAAC,CAAC;IAAA,GAAE,CAAC,CAAC;IACxG,OAAOC,YAAY;EACrB,CAAC,EAAE,CAACnB,WAAW,CAAC,CAAC;EAEjB,IAAMyB,MAAM,GAAG,SAATA,MAAMA,CAAA,EAAQ;IAClB3B,UAAU,CAAC2B,MAAM,EAAE;EACrB,CAAC;EAED,OAAO;IACLzB,WAAW,EAAXA,WAAW;IACXO,WAAW,EAAXA,WAAW;IACXkB,MAAM,EAANA,MAAM;IACNrB,cAAc,EAAdA;GACD;AACH,CAAC;AAIDsB,OAAA,CAAAC,OAAA,GAAejC,sBAAsB",
      ignoreList: []
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "27908470b6a35df8c7343652d58f811f9e7c979b"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_24y73qvb38 = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_24y73qvb38();
cov_24y73qvb38().s[0]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
var native_1 =
/* istanbul ignore next */
(cov_24y73qvb38().s[1]++, require("@react-navigation/native"));
var react_1 =
/* istanbul ignore next */
(cov_24y73qvb38().s[2]++, require("react"));
var Constants_1 =
/* istanbul ignore next */
(cov_24y73qvb38().s[3]++, require("../../commons/Constants"));
/* istanbul ignore next */
cov_24y73qvb38().s[4]++;
var usePaymentResultDetail = function usePaymentResultDetail() {
  /* istanbul ignore next */
  cov_24y73qvb38().f[0]++;
  var _paymentInfo$paymentV;
  var route =
  /* istanbul ignore next */
  (cov_24y73qvb38().s[5]++, (0, native_1.useRoute)());
  var navigation =
  /* istanbul ignore next */
  (cov_24y73qvb38().s[6]++, (0, native_1.useNavigation)());
  var paymentInfo =
  /* istanbul ignore next */
  (cov_24y73qvb38().s[7]++, route.params.paymentInfo);
  var paymentType =
  /* istanbul ignore next */
  (cov_24y73qvb38().s[8]++, (_paymentInfo$paymentV = paymentInfo.paymentValidate) == null ?
  /* istanbul ignore next */
  (cov_24y73qvb38().b[0][0]++, void 0) :
  /* istanbul ignore next */
  (cov_24y73qvb38().b[0][1]++, _paymentInfo$paymentV.paymentType));
  var isTopupAccount =
  /* istanbul ignore next */
  (cov_24y73qvb38().s[9]++, paymentType === Constants_1.PAYMENT_TYPE.TOPUP_ACCOUNT);
  var totalAmount =
  /* istanbul ignore next */
  (cov_24y73qvb38().s[10]++, (0, react_1.useMemo)(function () {
    /* istanbul ignore next */
    cov_24y73qvb38().f[1]++;
    var _paymentInfo$paymentV2, _paymentInfo$paymentV3, _paymentInfo$paymentV4, _paymentInfo$billInfo;
    /* istanbul ignore next */
    cov_24y73qvb38().s[11]++;
    if (
    /* istanbul ignore next */
    (cov_24y73qvb38().b[2][0]++, ((_paymentInfo$paymentV2 = paymentInfo.paymentValidate) == null ?
    /* istanbul ignore next */
    (cov_24y73qvb38().b[3][0]++, void 0) :
    /* istanbul ignore next */
    (cov_24y73qvb38().b[3][1]++, _paymentInfo$paymentV2.paymentType)) === Constants_1.PAYMENT_TYPE.TOPUP_ACCOUNT) ||
    /* istanbul ignore next */
    (cov_24y73qvb38().b[2][1]++, ((_paymentInfo$paymentV3 = paymentInfo.paymentValidate) == null ?
    /* istanbul ignore next */
    (cov_24y73qvb38().b[4][0]++, void 0) :
    /* istanbul ignore next */
    (cov_24y73qvb38().b[4][1]++, _paymentInfo$paymentV3.paymentType)) === Constants_1.PAYMENT_TYPE.TOPUP_CREDIT) ||
    /* istanbul ignore next */
    (cov_24y73qvb38().b[2][2]++, ((_paymentInfo$paymentV4 = paymentInfo.paymentValidate) == null ?
    /* istanbul ignore next */
    (cov_24y73qvb38().b[5][0]++, void 0) :
    /* istanbul ignore next */
    (cov_24y73qvb38().b[5][1]++, _paymentInfo$paymentV4.paymentType)) === Constants_1.PAYMENT_TYPE.QR_PAYMENT)) {
      /* istanbul ignore next */
      cov_24y73qvb38().b[1][0]++;
      var _paymentInfo$paymentV5;
      /* istanbul ignore next */
      cov_24y73qvb38().s[12]++;
      return /* istanbul ignore next */(cov_24y73qvb38().b[7][0]++, (_paymentInfo$paymentV5 = paymentInfo.paymentValidate.transferTransactionInformation) == null) ||
      /* istanbul ignore next */
      (cov_24y73qvb38().b[7][1]++, (_paymentInfo$paymentV5 = _paymentInfo$paymentV5.instructedAmount) == null) ?
      /* istanbul ignore next */
      (cov_24y73qvb38().b[6][0]++, void 0) :
      /* istanbul ignore next */
      (cov_24y73qvb38().b[6][1]++, _paymentInfo$paymentV5.amount);
    } else
    /* istanbul ignore next */
    {
      cov_24y73qvb38().b[1][1]++;
    }
    var _totalAmount =
    /* istanbul ignore next */
    (cov_24y73qvb38().s[13]++,
    /* istanbul ignore next */
    (cov_24y73qvb38().b[9][0]++, paymentInfo == null) ||
    /* istanbul ignore next */
    (cov_24y73qvb38().b[9][1]++, (_paymentInfo$billInfo = paymentInfo.billInfo) == null) ||
    /* istanbul ignore next */
    (cov_24y73qvb38().b[9][2]++, (_paymentInfo$billInfo = _paymentInfo$billInfo.billList) == null) ?
    /* istanbul ignore next */
    (cov_24y73qvb38().b[8][0]++, void 0) :
    /* istanbul ignore next */
    (cov_24y73qvb38().b[8][1]++, _paymentInfo$billInfo.reduce(function (sum, bill) {
      /* istanbul ignore next */
      cov_24y73qvb38().f[2]++;
      cov_24y73qvb38().s[14]++;
      return sum + (
      /* istanbul ignore next */
      (cov_24y73qvb38().b[10][0]++, bill.amount) ||
      /* istanbul ignore next */
      (cov_24y73qvb38().b[10][1]++, 0));
    }, 0)));
    /* istanbul ignore next */
    cov_24y73qvb38().s[15]++;
    return _totalAmount;
  }, [paymentInfo]));
  /* istanbul ignore next */
  cov_24y73qvb38().s[16]++;
  var goBack = function goBack() {
    /* istanbul ignore next */
    cov_24y73qvb38().f[3]++;
    cov_24y73qvb38().s[17]++;
    navigation.goBack();
  };
  /* istanbul ignore next */
  cov_24y73qvb38().s[18]++;
  return {
    paymentInfo: paymentInfo,
    totalAmount: totalAmount,
    goBack: goBack,
    isTopupAccount: isTopupAccount
  };
};
/* istanbul ignore next */
cov_24y73qvb38().s[19]++;
exports.default = usePaymentResultDetail;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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