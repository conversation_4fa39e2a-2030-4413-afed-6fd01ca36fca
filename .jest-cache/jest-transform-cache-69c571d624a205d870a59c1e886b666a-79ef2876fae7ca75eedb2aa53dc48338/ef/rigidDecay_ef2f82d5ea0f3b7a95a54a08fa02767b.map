{"version": 3, "names": ["Object", "defineProperty", "exports", "value", "rigidDecay", "_utils", "require", "animation", "now", "config", "lastTimestamp", "startTimestamp", "initialVelocity", "current", "velocity", "deltaTime", "Math", "min", "v", "exp", "deceleration", "SLOPE_FACTOR", "velocityFactor", "clamp", "abs", "VELOCITY_EPS"], "sources": ["../../../../src/animation/decay/rigidDecay.ts"], "sourcesContent": [null], "mappings": "AAAA,YAAY;;AAAAA,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,KAAA;AAAA;AAAAD,OAAA,CAAAE,UAAA,GAAAA,UAAA;AACZ,IAAAC,MAAA,GAAAC,OAAA;AAGO,SAASF,UAAUA,CACxBG,SAA8B,EAC9BC,GAAW,EACXC,MAA0B,EACjB;EACT,SAAS;;EACT,IAAQC,aAAa,GACnBH,SAAS,CADHG,aAAa;IAAEC,cAAc,GACnCJ,SAAS,CADYI,cAAc;IAAEC,eAAe,GACpDL,SAAS,CAD4BK,eAAe;IAAEC,OAAO,GAC7DN,SAAS,CAD6CM,OAAO;IAAEC,QAAA,GAC/DP,SAAS,CADsDO,QAAA;EAGjE,IAAMC,SAAS,GAAGC,IAAI,CAACC,GAAG,CAACT,GAAG,GAAGE,aAAa,EAAE,EAAE,CAAC;EACnD,IAAMQ,CAAC,GACLJ,QAAQ,GACRE,IAAI,CAACG,GAAG,CACN,EAAE,CAAC,GAAGV,MAAM,CAACW,YAAY,CAAC,IAAIZ,GAAG,GAAGG,cAAc,CAAC,GAAGU,mBACxD,CAAC;EACHd,SAAS,CAACM,OAAO,GAAGA,OAAO,GAAIK,CAAC,GAAGT,MAAM,CAACa,cAAc,GAAGP,SAAS,GAAI,IAAI;EAC5ER,SAAS,CAACO,QAAQ,GAAGI,CAAC;EACtBX,SAAS,CAACG,aAAa,GAAGF,GAAG;EAE7B,IAAIC,MAAM,CAACc,KAAK,EAAE;IAChB,IAAIX,eAAe,GAAG,CAAC,IAAIL,SAAS,CAACM,OAAO,IAAIJ,MAAM,CAACc,KAAK,CAAC,CAAC,CAAC,EAAE;MAC/DhB,SAAS,CAACM,OAAO,GAAGJ,MAAM,CAACc,KAAK,CAAC,CAAC,CAAC;MACnC,OAAO,IAAI;IACb,CAAC,MAAM,IAAIX,eAAe,GAAG,CAAC,IAAIL,SAAS,CAACM,OAAO,IAAIJ,MAAM,CAACc,KAAK,CAAC,CAAC,CAAC,EAAE;MACtEhB,SAAS,CAACM,OAAO,GAAGJ,MAAM,CAACc,KAAK,CAAC,CAAC,CAAC;MACnC,OAAO,IAAI;IACb;EACF;EACA,OAAOP,IAAI,CAACQ,GAAG,CAACN,CAAC,CAAC,GAAGO,mBAAY;AACnC", "ignoreList": []}