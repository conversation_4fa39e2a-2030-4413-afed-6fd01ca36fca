bc06aaf1e1bdaa92d0fa120710ff693c
'use strict';

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.rigidDecay = rigidDecay;
var _utils = require("./utils.js");
function rigidDecay(animation, now, config) {
  'worklet';

  var lastTimestamp = animation.lastTimestamp,
    startTimestamp = animation.startTimestamp,
    initialVelocity = animation.initialVelocity,
    current = animation.current,
    velocity = animation.velocity;
  var deltaTime = Math.min(now - lastTimestamp, 64);
  var v = velocity * Math.exp(-(1 - config.deceleration) * (now - startTimestamp) * _utils.SLOPE_FACTOR);
  animation.current = current + v * config.velocityFactor * deltaTime / 1000;
  animation.velocity = v;
  animation.lastTimestamp = now;
  if (config.clamp) {
    if (initialVelocity < 0 && animation.current <= config.clamp[0]) {
      animation.current = config.clamp[0];
      return true;
    } else if (initialVelocity > 0 && animation.current >= config.clamp[1]) {
      animation.current = config.clamp[1];
      return true;
    }
  }
  return Math.abs(v) < _utils.VELOCITY_EPS;
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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