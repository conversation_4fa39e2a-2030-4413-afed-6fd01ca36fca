21b9f7ea0a63045ab28810e08a9071f3
"use strict";

/* istanbul ignore next */
function cov_15utz0lkr() {
  var path = "/Users/<USER>/projects/react-native/payment-module/src/data/models/save-bill-contact/SaveBillContactRequest.ts";
  var hash = "800bc64b46d0aa365121bb2cabe1ca99f5862aec";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/projects/react-native/payment-module/src/data/models/save-bill-contact/SaveBillContactRequest.ts",
    statementMap: {
      "0": {
        start: {
          line: 3,
          column: 0
        },
        end: {
          line: 5,
          column: 3
        }
      }
    },
    fnMap: {},
    branchMap: {},
    s: {
      "0": 0
    },
    f: {},
    b: {},
    inputSourceMap: {
      version: 3,
      file: undefined,
      names: [],
      sourceRoot: undefined,
      sources: ["/Users/<USER>/projects/react-native/payment-module/src/data/models/save-bill-contact/SaveBillContactRequest.ts"],
      sourcesContent: ["export interface SaveBillContactRequest {\n  // TODO: define fields\n}\n"],
      mappings: "",
      ignoreList: []
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "800bc64b46d0aa365121bb2cabe1ca99f5862aec"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_15utz0lkr = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_15utz0lkr();
cov_15utz0lkr().s[0]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6W10sInNvdXJjZXMiOlsiL1VzZXJzL3NvbnNhbXNldC9wcm9qZWN0cy9yZWFjdC1uYXRpdmUvcGF5bWVudC1tb2R1bGUvc3JjL2RhdGEvbW9kZWxzL3NhdmUtYmlsbC1jb250YWN0L1NhdmVCaWxsQ29udGFjdFJlcXVlc3QudHMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGludGVyZmFjZSBTYXZlQmlsbENvbnRhY3RSZXF1ZXN0IHtcbiAgLy8gVE9ETzogZGVmaW5lIGZpZWxkc1xufVxuIl0sIm1hcHBpbmdzIjoiIiwiaWdub3JlTGlzdCI6W119