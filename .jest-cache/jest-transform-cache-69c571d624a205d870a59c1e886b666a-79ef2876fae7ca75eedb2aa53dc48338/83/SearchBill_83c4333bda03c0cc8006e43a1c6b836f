bb38076183bbfcb5da2afec1a5d84247
"use strict";

/* istanbul ignore next */
function cov_17i0wsvd8z() {
  var path = "/Users/<USER>/projects/react-native/payment-module/src/presentation/payment-home/components/bill-list/SearchBill.tsx";
  var hash = "7fce7942b4923fdd89b23842b988ba5a7e794724";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/projects/react-native/payment-module/src/presentation/payment-home/components/bill-list/SearchBill.tsx",
    statementMap: {
      "0": {
        start: {
          line: 3,
          column: 29
        },
        end: {
          line: 3,
          column: 84
        }
      },
      "1": {
        start: {
          line: 4,
          column: 22
        },
        end: {
          line: 4,
          column: 93
        }
      },
      "2": {
        start: {
          line: 5,
          column: 22
        },
        end: {
          line: 9,
          column: 1
        }
      },
      "3": {
        start: {
          line: 6,
          column: 2
        },
        end: {
          line: 8,
          column: 4
        }
      },
      "4": {
        start: {
          line: 10,
          column: 0
        },
        end: {
          line: 12,
          column: 3
        }
      },
      "5": {
        start: {
          line: 13,
          column: 0
        },
        end: {
          line: 13,
          column: 48
        }
      },
      "6": {
        start: {
          line: 14,
          column: 29
        },
        end: {
          line: 14,
          column: 60
        }
      },
      "7": {
        start: {
          line: 15,
          column: 21
        },
        end: {
          line: 15,
          column: 44
        }
      },
      "8": {
        start: {
          line: 16,
          column: 16
        },
        end: {
          line: 16,
          column: 54
        }
      },
      "9": {
        start: {
          line: 17,
          column: 14
        },
        end: {
          line: 17,
          column: 30
        }
      },
      "10": {
        start: {
          line: 18,
          column: 14
        },
        end: {
          line: 18,
          column: 47
        }
      },
      "11": {
        start: {
          line: 19,
          column: 17
        },
        end: {
          line: 43,
          column: 1
        }
      },
      "12": {
        start: {
          line: 20,
          column: 13
        },
        end: {
          line: 20,
          column: 38
        }
      },
      "13": {
        start: {
          line: 21,
          column: 12
        },
        end: {
          line: 21,
          column: 49
        }
      },
      "14": {
        start: {
          line: 22,
          column: 17
        },
        end: {
          line: 22,
          column: 25
        }
      },
      "15": {
        start: {
          line: 23,
          column: 20
        },
        end: {
          line: 23,
          column: 28
        }
      },
      "16": {
        start: {
          line: 24,
          column: 14
        },
        end: {
          line: 24,
          column: 73
        }
      },
      "17": {
        start: {
          line: 25,
          column: 13
        },
        end: {
          line: 25,
          column: 25
        }
      },
      "18": {
        start: {
          line: 26,
          column: 25
        },
        end: {
          line: 29,
          column: 3
        }
      },
      "19": {
        start: {
          line: 27,
          column: 4
        },
        end: {
          line: 27,
          column: 24
        }
      },
      "20": {
        start: {
          line: 28,
          column: 4
        },
        end: {
          line: 28,
          column: 25
        }
      },
      "21": {
        start: {
          line: 30,
          column: 29
        },
        end: {
          line: 32,
          column: 3
        }
      },
      "22": {
        start: {
          line: 31,
          column: 4
        },
        end: {
          line: 31,
          column: 78
        }
      },
      "23": {
        start: {
          line: 33,
          column: 2
        },
        end: {
          line: 42,
          column: 6
        }
      },
      "24": {
        start: {
          line: 44,
          column: 0
        },
        end: {
          line: 44,
          column: 32
        }
      },
      "25": {
        start: {
          line: 45,
          column: 0
        },
        end: {
          line: 72,
          column: 3
        }
      },
      "26": {
        start: {
          line: 46,
          column: 19
        },
        end: {
          line: 46,
          column: 35
        }
      },
      "27": {
        start: {
          line: 47,
          column: 17
        },
        end: {
          line: 47,
          column: 33
        }
      },
      "28": {
        start: {
          line: 48,
          column: 16
        },
        end: {
          line: 48,
          column: 31
        }
      },
      "29": {
        start: {
          line: 49,
          column: 2
        },
        end: {
          line: 71,
          column: 4
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 5,
            column: 54
          },
          end: {
            line: 5,
            column: 55
          }
        },
        loc: {
          start: {
            line: 5,
            column: 69
          },
          end: {
            line: 9,
            column: 1
          }
        },
        line: 5
      },
      "1": {
        name: "SearchBill",
        decl: {
          start: {
            line: 19,
            column: 26
          },
          end: {
            line: 19,
            column: 36
          }
        },
        loc: {
          start: {
            line: 19,
            column: 44
          },
          end: {
            line: 43,
            column: 1
          }
        },
        line: 19
      },
      "2": {
        name: "handleTextChange",
        decl: {
          start: {
            line: 26,
            column: 34
          },
          end: {
            line: 26,
            column: 50
          }
        },
        loc: {
          start: {
            line: 26,
            column: 57
          },
          end: {
            line: 29,
            column: 3
          }
        },
        line: 26
      },
      "3": {
        name: "handleOpenAddContact",
        decl: {
          start: {
            line: 30,
            column: 38
          },
          end: {
            line: 30,
            column: 58
          }
        },
        loc: {
          start: {
            line: 30,
            column: 61
          },
          end: {
            line: 32,
            column: 3
          }
        },
        line: 30
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 45,
            column: 68
          },
          end: {
            line: 45,
            column: 69
          }
        },
        loc: {
          start: {
            line: 45,
            column: 85
          },
          end: {
            line: 72,
            column: 1
          }
        },
        line: 45
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 5,
            column: 22
          },
          end: {
            line: 9,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 5,
            column: 22
          },
          end: {
            line: 5,
            column: 26
          }
        }, {
          start: {
            line: 5,
            column: 30
          },
          end: {
            line: 5,
            column: 50
          }
        }, {
          start: {
            line: 5,
            column: 54
          },
          end: {
            line: 9,
            column: 1
          }
        }],
        line: 5
      },
      "1": {
        loc: {
          start: {
            line: 6,
            column: 9
          },
          end: {
            line: 8,
            column: 3
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 6,
            column: 33
          },
          end: {
            line: 6,
            column: 36
          }
        }, {
          start: {
            line: 6,
            column: 39
          },
          end: {
            line: 8,
            column: 3
          }
        }],
        line: 6
      },
      "2": {
        loc: {
          start: {
            line: 6,
            column: 9
          },
          end: {
            line: 6,
            column: 30
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 6,
            column: 9
          },
          end: {
            line: 6,
            column: 12
          }
        }, {
          start: {
            line: 6,
            column: 16
          },
          end: {
            line: 6,
            column: 30
          }
        }],
        line: 6
      },
      "3": {
        loc: {
          start: {
            line: 31,
            column: 4
          },
          end: {
            line: 31,
            column: 77
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 31,
            column: 4
          },
          end: {
            line: 31,
            column: 17
          }
        }, {
          start: {
            line: 31,
            column: 21
          },
          end: {
            line: 31,
            column: 50
          }
        }, {
          start: {
            line: 31,
            column: 54
          },
          end: {
            line: 31,
            column: 77
          }
        }],
        line: 31
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0
    },
    b: {
      "0": [0, 0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0, 0]
    },
    inputSourceMap: {
      version: 3,
      file: undefined,
      names: ["msb_shared_component_1", "require", "react_native_1", "i18n_ts_1", "react_1", "react_2", "__importDefault", "SearchBill", "props", "_ref", "useState", "_ref2", "_slicedToArray2", "default", "searchText", "setSearchText", "_ref3", "useMSBStyles", "exports", "makeStyle", "styles", "handleTextChange", "text", "onSearch", "handleOpenAddContact", "onAddNewContact", "createElement", "View", "style", "container", "MSBSearchInput", "testID", "value", "placeholder", "translate", "onChangeText", "containerSearchStyle", "containerSearchInput", "maxLength", "createMSBStyleSheet", "_ref4", "SizeGlobal", "ColorAlias", "SizeAlias", "alignItems", "borderTopWidth", "Size25", "borderBottomWidth", "borderTopColor", "BorderDefault", "borderBottomColor", "flexDirection", "width", "flex", "padding", "SpacingSmall", "searchInput", "marginEnd", "searchIconSize", "height", "IconLarge"],
      sourceRoot: undefined,
      sources: ["/Users/<USER>/projects/react-native/payment-module/src/presentation/payment-home/components/bill-list/SearchBill.tsx"],
      sourcesContent: ["import {createMSBStyleSheet, MSBSearchInput, useMSBStyles} from 'msb-shared-component';\nimport {View} from 'react-native';\n\nimport {translate} from '../../../../locales/i18n.ts';\nimport {useState} from 'react';\nimport React from 'react';\n\ninterface SearchBillProps {\n  isShowAddContact: boolean | undefined;\n  onAddNewContact?: () => void;\n  onSearch: (text: string) => void;\n}\n\nexport const SearchBill = (props: SearchBillProps) => {\n  const [searchText, setSearchText] = useState('');\n  const {styles} = useMSBStyles(makeStyle);\n\n  const handleTextChange = (text: string) => {\n    setSearchText(text);\n    props.onSearch(text);\n  };\n\n  const handleOpenAddContact = () => {\n    props?.onAddNewContact?.();\n  };\n\n  return (\n    <View style={styles.container}>\n      <MSBSearchInput\n        testID={'payment.SavedTab.enterSearch'}\n        value={searchText}\n        placeholder={translate('billingTab.hintSearch')}\n        onChangeText={handleTextChange}\n        containerSearchStyle={styles.containerSearchInput}\n        maxLength={255}\n      />\n      {/* {props.isShowAddContact && (\n        // <MSBIcon\n        //   testID={'payment.SavedTab.pressAddContact'}\n        //   icon={MSBIcons.IconUserPlusAdd}\n        //   styleContainer={styles.searchInput}\n        //   onIconClick={handleOpenAddContact}\n        // />\n        <MSBTouchable\n          testID={'payment.SavedTab.pressAddContact'}\n          onPress={handleOpenAddContact}\n          style={styles.searchInput}>\n          <Image source={Images.icAddBill} style={styles.searchIconSize} />\n        </MSBTouchable>\n      )} */}\n    </View>\n  );\n};\n\nexport const makeStyle = createMSBStyleSheet(({SizeGlobal, ColorAlias, SizeAlias}) => {\n  return {\n    container: {\n      alignItems: 'center',\n      borderTopWidth: SizeGlobal.Size25,\n      borderBottomWidth: SizeGlobal.Size25,\n      borderTopColor: ColorAlias.BorderDefault,\n      borderBottomColor: ColorAlias.BorderDefault,\n      flexDirection: 'row',\n      width: '100%',\n    },\n    containerSearchInput: {\n      flex: 1,\n      padding: SizeAlias.SpacingSmall,\n      width: '100%',\n    },\n    searchInput: {\n      marginEnd: SizeAlias.SpacingSmall,\n    },\n    searchIconSize: {\n      height: SizeAlias.IconLarge,\n      width: SizeAlias.IconLarge,\n    },\n  };\n});\n"],
      mappings: ";;;;;;;;;;;;;AAAA,IAAAA,sBAAA,GAAAC,OAAA;AACA,IAAAC,cAAA,GAAAD,OAAA;AAEA,IAAAE,SAAA,GAAAF,OAAA;AACA,IAAAG,OAAA,GAAAH,OAAA;AACA,IAAAI,OAAA,GAAAC,eAAA,CAAAL,OAAA;AAQO,IAAMM,UAAU,GAAG,SAAbA,UAAUA,CAAIC,KAAsB,EAAI;EACnD,IAAAC,IAAA,GAAoC,IAAAL,OAAA,CAAAM,QAAQ,EAAC,EAAE,CAAC;IAAAC,KAAA,OAAAC,eAAA,CAAAC,OAAA,EAAAJ,IAAA;IAAzCK,UAAU,GAAAH,KAAA;IAAEI,aAAa,GAAAJ,KAAA;EAChC,IAAAK,KAAA,GAAiB,IAAAhB,sBAAA,CAAAiB,YAAY,EAACC,OAAA,CAAAC,SAAS,CAAC;IAAjCC,MAAM,GAAAJ,KAAA,CAANI,MAAM;EAEb,IAAMC,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAAIC,IAAY,EAAI;IACxCP,aAAa,CAACO,IAAI,CAAC;IACnBd,KAAK,CAACe,QAAQ,CAACD,IAAI,CAAC;EACtB,CAAC;EAED,IAAME,oBAAoB,GAAG,SAAvBA,oBAAoBA,CAAA,EAAQ;IAChChB,KAAK,YAALA,KAAK,CAAEiB,eAAe,YAAtBjB,KAAK,CAAEiB,eAAe,CAAE,CAAE;EAC5B,CAAC;EAED,OACEpB,OAAA,CAAAQ,OAAA,CAAAa,aAAA,CAACxB,cAAA,CAAAyB,IAAI;IAACC,KAAK,EAAER,MAAM,CAACS;EAAS,GAC3BxB,OAAA,CAAAQ,OAAA,CAAAa,aAAA,CAAC1B,sBAAA,CAAA8B,cAAc;IACbC,MAAM,EAAE,8BAA8B;IACtCC,KAAK,EAAElB,UAAU;IACjBmB,WAAW,EAAE,IAAA9B,SAAA,CAAA+B,SAAS,EAAC,uBAAuB,CAAC;IAC/CC,YAAY,EAAEd,gBAAgB;IAC9Be,oBAAoB,EAAEhB,MAAM,CAACiB,oBAAoB;IACjDC,SAAS,EAAE;EAAG,EACd,CAeG;AAEX,CAAC;AAvCYpB,OAAA,CAAAX,UAAU,GAAAA,UAAA;AAyCVW,OAAA,CAAAC,SAAS,GAAG,IAAAnB,sBAAA,CAAAuC,mBAAmB,EAAC,UAAAC,KAAA,EAAwC;EAAA,IAAtCC,UAAU,GAAAD,KAAA,CAAVC,UAAU;IAAEC,UAAU,GAAAF,KAAA,CAAVE,UAAU;IAAEC,SAAS,GAAAH,KAAA,CAATG,SAAS;EAC9E,OAAO;IACLd,SAAS,EAAE;MACTe,UAAU,EAAE,QAAQ;MACpBC,cAAc,EAAEJ,UAAU,CAACK,MAAM;MACjCC,iBAAiB,EAAEN,UAAU,CAACK,MAAM;MACpCE,cAAc,EAAEN,UAAU,CAACO,aAAa;MACxCC,iBAAiB,EAAER,UAAU,CAACO,aAAa;MAC3CE,aAAa,EAAE,KAAK;MACpBC,KAAK,EAAE;KACR;IACDf,oBAAoB,EAAE;MACpBgB,IAAI,EAAE,CAAC;MACPC,OAAO,EAAEX,SAAS,CAACY,YAAY;MAC/BH,KAAK,EAAE;KACR;IACDI,WAAW,EAAE;MACXC,SAAS,EAAEd,SAAS,CAACY;KACtB;IACDG,cAAc,EAAE;MACdC,MAAM,EAAEhB,SAAS,CAACiB,SAAS;MAC3BR,KAAK,EAAET,SAAS,CAACiB;;GAEpB;AACH,CAAC,CAAC",
      ignoreList: []
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "7fce7942b4923fdd89b23842b988ba5a7e794724"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_17i0wsvd8z = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_17i0wsvd8z();
var _interopRequireDefault =
/* istanbul ignore next */
(cov_17i0wsvd8z().s[0]++, require("@babel/runtime/helpers/interopRequireDefault"));
var _slicedToArray2 =
/* istanbul ignore next */
(cov_17i0wsvd8z().s[1]++, _interopRequireDefault(require("@babel/runtime/helpers/slicedToArray")));
var __importDefault =
/* istanbul ignore next */
(cov_17i0wsvd8z().s[2]++,
/* istanbul ignore next */
(cov_17i0wsvd8z().b[0][0]++, this) &&
/* istanbul ignore next */
(cov_17i0wsvd8z().b[0][1]++, this.__importDefault) ||
/* istanbul ignore next */
(cov_17i0wsvd8z().b[0][2]++, function (mod) {
  /* istanbul ignore next */
  cov_17i0wsvd8z().f[0]++;
  cov_17i0wsvd8z().s[3]++;
  return /* istanbul ignore next */(cov_17i0wsvd8z().b[2][0]++, mod) &&
  /* istanbul ignore next */
  (cov_17i0wsvd8z().b[2][1]++, mod.__esModule) ?
  /* istanbul ignore next */
  (cov_17i0wsvd8z().b[1][0]++, mod) :
  /* istanbul ignore next */
  (cov_17i0wsvd8z().b[1][1]++, {
    "default": mod
  });
}));
/* istanbul ignore next */
cov_17i0wsvd8z().s[4]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_17i0wsvd8z().s[5]++;
exports.makeStyle = exports.SearchBill = void 0;
var msb_shared_component_1 =
/* istanbul ignore next */
(cov_17i0wsvd8z().s[6]++, require("msb-shared-component"));
var react_native_1 =
/* istanbul ignore next */
(cov_17i0wsvd8z().s[7]++, require("react-native"));
var i18n_ts_1 =
/* istanbul ignore next */
(cov_17i0wsvd8z().s[8]++, require("../../../../locales/i18n.ts"));
var react_1 =
/* istanbul ignore next */
(cov_17i0wsvd8z().s[9]++, require("react"));
var react_2 =
/* istanbul ignore next */
(cov_17i0wsvd8z().s[10]++, __importDefault(require("react")));
/* istanbul ignore next */
cov_17i0wsvd8z().s[11]++;
var SearchBill = function SearchBill(props) {
  /* istanbul ignore next */
  cov_17i0wsvd8z().f[1]++;
  var _ref =
    /* istanbul ignore next */
    (cov_17i0wsvd8z().s[12]++, (0, react_1.useState)('')),
    _ref2 =
    /* istanbul ignore next */
    (cov_17i0wsvd8z().s[13]++, (0, _slicedToArray2.default)(_ref, 2)),
    searchText =
    /* istanbul ignore next */
    (cov_17i0wsvd8z().s[14]++, _ref2[0]),
    setSearchText =
    /* istanbul ignore next */
    (cov_17i0wsvd8z().s[15]++, _ref2[1]);
  var _ref3 =
    /* istanbul ignore next */
    (cov_17i0wsvd8z().s[16]++, (0, msb_shared_component_1.useMSBStyles)(exports.makeStyle)),
    styles =
    /* istanbul ignore next */
    (cov_17i0wsvd8z().s[17]++, _ref3.styles);
  /* istanbul ignore next */
  cov_17i0wsvd8z().s[18]++;
  var handleTextChange = function handleTextChange(text) {
    /* istanbul ignore next */
    cov_17i0wsvd8z().f[2]++;
    cov_17i0wsvd8z().s[19]++;
    setSearchText(text);
    /* istanbul ignore next */
    cov_17i0wsvd8z().s[20]++;
    props.onSearch(text);
  };
  /* istanbul ignore next */
  cov_17i0wsvd8z().s[21]++;
  var handleOpenAddContact = function handleOpenAddContact() {
    /* istanbul ignore next */
    cov_17i0wsvd8z().f[3]++;
    cov_17i0wsvd8z().s[22]++;
    /* istanbul ignore next */
    (cov_17i0wsvd8z().b[3][0]++, props == null) ||
    /* istanbul ignore next */
    (cov_17i0wsvd8z().b[3][1]++, props.onAddNewContact == null) ||
    /* istanbul ignore next */
    (cov_17i0wsvd8z().b[3][2]++, props.onAddNewContact());
  };
  /* istanbul ignore next */
  cov_17i0wsvd8z().s[23]++;
  return react_2.default.createElement(react_native_1.View, {
    style: styles.container
  }, react_2.default.createElement(msb_shared_component_1.MSBSearchInput, {
    testID: 'payment.SavedTab.enterSearch',
    value: searchText,
    placeholder: (0, i18n_ts_1.translate)('billingTab.hintSearch'),
    onChangeText: handleTextChange,
    containerSearchStyle: styles.containerSearchInput,
    maxLength: 255
  }));
};
/* istanbul ignore next */
cov_17i0wsvd8z().s[24]++;
exports.SearchBill = SearchBill;
/* istanbul ignore next */
cov_17i0wsvd8z().s[25]++;
exports.makeStyle = (0, msb_shared_component_1.createMSBStyleSheet)(function (_ref4) {
  /* istanbul ignore next */
  cov_17i0wsvd8z().f[4]++;
  var SizeGlobal =
    /* istanbul ignore next */
    (cov_17i0wsvd8z().s[26]++, _ref4.SizeGlobal),
    ColorAlias =
    /* istanbul ignore next */
    (cov_17i0wsvd8z().s[27]++, _ref4.ColorAlias),
    SizeAlias =
    /* istanbul ignore next */
    (cov_17i0wsvd8z().s[28]++, _ref4.SizeAlias);
  /* istanbul ignore next */
  cov_17i0wsvd8z().s[29]++;
  return {
    container: {
      alignItems: 'center',
      borderTopWidth: SizeGlobal.Size25,
      borderBottomWidth: SizeGlobal.Size25,
      borderTopColor: ColorAlias.BorderDefault,
      borderBottomColor: ColorAlias.BorderDefault,
      flexDirection: 'row',
      width: '100%'
    },
    containerSearchInput: {
      flex: 1,
      padding: SizeAlias.SpacingSmall,
      width: '100%'
    },
    searchInput: {
      marginEnd: SizeAlias.SpacingSmall
    },
    searchIconSize: {
      height: SizeAlias.IconLarge,
      width: SizeAlias.IconLarge
    }
  };
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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