{"version": 3, "names": [], "sources": ["/Users/<USER>/projects/react-native/payment-module/src/domain/repositories/IPaymentRepository.ts"], "sourcesContent": ["import {ValidateModel} from '../entities/validate/ValidateModel';\nimport {ValidateRequest} from '../../data/models/validate/ValidateRequest';\nimport {BaseResponse} from '../../core/BaseResponse';\n\nexport interface IPaymentRepository {\n  validate(request: ValidateRequest): Promise<BaseResponse<ValidateModel>>;\n}\n"], "mappings": "", "ignoreList": []}