8a2b1dffbb5d80c31083066baae3d03b
"use strict";

/* istanbul ignore next */
function cov_qkd3382fb() {
  var path = "/Users/<USER>/projects/react-native/payment-module/src/domain/repositories/IPaymentRepository.ts";
  var hash = "d2d3c4d8cf2c654bdf554385c52c5585d1c9daa6";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/projects/react-native/payment-module/src/domain/repositories/IPaymentRepository.ts",
    statementMap: {
      "0": {
        start: {
          line: 3,
          column: 0
        },
        end: {
          line: 5,
          column: 3
        }
      }
    },
    fnMap: {},
    branchMap: {},
    s: {
      "0": 0
    },
    f: {},
    b: {},
    inputSourceMap: {
      version: 3,
      file: undefined,
      names: [],
      sourceRoot: undefined,
      sources: ["/Users/<USER>/projects/react-native/payment-module/src/domain/repositories/IPaymentRepository.ts"],
      sourcesContent: ["import {ValidateModel} from '../entities/validate/ValidateModel';\nimport {ValidateRequest} from '../../data/models/validate/ValidateRequest';\nimport {BaseResponse} from '../../core/BaseResponse';\n\nexport interface IPaymentRepository {\n  validate(request: ValidateRequest): Promise<BaseResponse<ValidateModel>>;\n}\n"],
      mappings: "",
      ignoreList: []
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "d2d3c4d8cf2c654bdf554385c52c5585d1c9daa6"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_qkd3382fb = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_qkd3382fb();
cov_qkd3382fb().s[0]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6W10sInNvdXJjZXMiOlsiL1VzZXJzL3NvbnNhbXNldC9wcm9qZWN0cy9yZWFjdC1uYXRpdmUvcGF5bWVudC1tb2R1bGUvc3JjL2RvbWFpbi9yZXBvc2l0b3JpZXMvSVBheW1lbnRSZXBvc2l0b3J5LnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7VmFsaWRhdGVNb2RlbH0gZnJvbSAnLi4vZW50aXRpZXMvdmFsaWRhdGUvVmFsaWRhdGVNb2RlbCc7XG5pbXBvcnQge1ZhbGlkYXRlUmVxdWVzdH0gZnJvbSAnLi4vLi4vZGF0YS9tb2RlbHMvdmFsaWRhdGUvVmFsaWRhdGVSZXF1ZXN0JztcbmltcG9ydCB7QmFzZVJlc3BvbnNlfSBmcm9tICcuLi8uLi9jb3JlL0Jhc2VSZXNwb25zZSc7XG5cbmV4cG9ydCBpbnRlcmZhY2UgSVBheW1lbnRSZXBvc2l0b3J5IHtcbiAgdmFsaWRhdGUocmVxdWVzdDogVmFsaWRhdGVSZXF1ZXN0KTogUHJvbWlzZTxCYXNlUmVzcG9uc2U8VmFsaWRhdGVNb2RlbD4+O1xufVxuIl0sIm1hcHBpbmdzIjoiIiwiaWdub3JlTGlzdCI6W119