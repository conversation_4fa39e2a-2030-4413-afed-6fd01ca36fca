{"version": 3, "names": ["cov_17i0wsvd8z", "actualCoverage", "msb_shared_component_1", "s", "require", "react_native_1", "i18n_ts_1", "react_1", "react_2", "__importDefault", "SearchBill", "props", "f", "_ref", "useState", "_ref2", "_slicedToArray2", "default", "searchText", "setSearchText", "_ref3", "useMSBStyles", "exports", "makeStyle", "styles", "handleTextChange", "text", "onSearch", "handleOpenAddContact", "b", "onAddNewContact", "createElement", "View", "style", "container", "MSBSearchInput", "testID", "value", "placeholder", "translate", "onChangeText", "containerSearchStyle", "containerSearchInput", "max<PERSON><PERSON><PERSON>", "createMSBStyleSheet", "_ref4", "SizeGlobal", "ColorAlias", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "alignItems", "borderTopWidth", "Size25", "borderBottomWidth", "borderTopColor", "BorderDefault", "borderBottomColor", "flexDirection", "width", "flex", "padding", "SpacingSmall", "searchInput", "marginEnd", "searchIconSize", "height", "IconLarge"], "sources": ["/Users/<USER>/projects/react-native/payment-module/src/presentation/payment-home/components/bill-list/SearchBill.tsx"], "sourcesContent": ["import {createMSBStyleSheet, MSBSearchInput, useMSBStyles} from 'msb-shared-component';\nimport {View} from 'react-native';\n\nimport {translate} from '../../../../locales/i18n.ts';\nimport {useState} from 'react';\nimport React from 'react';\n\ninterface SearchBillProps {\n  isShowAddContact: boolean | undefined;\n  onAddNewContact?: () => void;\n  onSearch: (text: string) => void;\n}\n\nexport const SearchBill = (props: SearchBillProps) => {\n  const [searchText, setSearchText] = useState('');\n  const {styles} = useMSBStyles(makeStyle);\n\n  const handleTextChange = (text: string) => {\n    setSearchText(text);\n    props.onSearch(text);\n  };\n\n  const handleOpenAddContact = () => {\n    props?.onAddNewContact?.();\n  };\n\n  return (\n    <View style={styles.container}>\n      <MSBSearchInput\n        testID={'payment.SavedTab.enterSearch'}\n        value={searchText}\n        placeholder={translate('billingTab.hintSearch')}\n        onChangeText={handleTextChange}\n        containerSearchStyle={styles.containerSearchInput}\n        maxLength={255}\n      />\n      {/* {props.isShowAddContact && (\n        // <MSBIcon\n        //   testID={'payment.SavedTab.pressAddContact'}\n        //   icon={MSBIcons.IconUserPlusAdd}\n        //   styleContainer={styles.searchInput}\n        //   onIconClick={handleOpenAddContact}\n        // />\n        <MSBTouchable\n          testID={'payment.SavedTab.pressAddContact'}\n          onPress={handleOpenAddContact}\n          style={styles.searchInput}>\n          <Image source={Images.icAddBill} style={styles.searchIconSize} />\n        </MSBTouchable>\n      )} */}\n    </View>\n  );\n};\n\nexport const makeStyle = createMSBStyleSheet(({SizeGlobal, ColorAlias, SizeAlias}) => {\n  return {\n    container: {\n      alignItems: 'center',\n      borderTopWidth: SizeGlobal.Size25,\n      borderBottomWidth: SizeGlobal.Size25,\n      borderTopColor: ColorAlias.BorderDefault,\n      borderBottomColor: ColorAlias.BorderDefault,\n      flexDirection: 'row',\n      width: '100%',\n    },\n    containerSearchInput: {\n      flex: 1,\n      padding: SizeAlias.SpacingSmall,\n      width: '100%',\n    },\n    searchInput: {\n      marginEnd: SizeAlias.SpacingSmall,\n    },\n    searchIconSize: {\n      height: SizeAlias.IconLarge,\n      width: SizeAlias.IconLarge,\n    },\n  };\n});\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAGA;IAAAA,cAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,cAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAHA,IAAAE,sBAAA;AAAA;AAAA,CAAAF,cAAA,GAAAG,CAAA,OAAAC,OAAA;AACA,IAAAC,cAAA;AAAA;AAAA,CAAAL,cAAA,GAAAG,CAAA,OAAAC,OAAA;AAEA,IAAAE,SAAA;AAAA;AAAA,CAAAN,cAAA,GAAAG,CAAA,OAAAC,OAAA;AACA,IAAAG,OAAA;AAAA;AAAA,CAAAP,cAAA,GAAAG,CAAA,OAAAC,OAAA;AACA,IAAAI,OAAA;AAAA;AAAA,CAAAR,cAAA,GAAAG,CAAA,QAAAM,eAAA,CAAAL,OAAA;AAAA;AAAAJ,cAAA,GAAAG,CAAA;AAQO,IAAMO,UAAU,GAAG,SAAbA,UAAUA,CAAIC,KAAsB,EAAI;EAAA;EAAAX,cAAA,GAAAY,CAAA;EACnD,IAAAC,IAAA;IAAA;IAAA,CAAAb,cAAA,GAAAG,CAAA,QAAoC,IAAAI,OAAA,CAAAO,QAAQ,EAAC,EAAE,CAAC;IAAAC,KAAA;IAAA;IAAA,CAAAf,cAAA,GAAAG,CAAA,YAAAa,eAAA,CAAAC,OAAA,EAAAJ,IAAA;IAAzCK,UAAU;IAAA;IAAA,CAAAlB,cAAA,GAAAG,CAAA,QAAAY,KAAA;IAAEI,aAAa;IAAA;IAAA,CAAAnB,cAAA,GAAAG,CAAA,QAAAY,KAAA;EAChC,IAAAK,KAAA;IAAA;IAAA,CAAApB,cAAA,GAAAG,CAAA,QAAiB,IAAAD,sBAAA,CAAAmB,YAAY,EAACC,OAAA,CAAAC,SAAS,CAAC;IAAjCC,MAAM;IAAA;IAAA,CAAAxB,cAAA,GAAAG,CAAA,QAAAiB,KAAA,CAANI,MAAM;EAAA;EAAAxB,cAAA,GAAAG,CAAA;EAEb,IAAMsB,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAAIC,IAAY,EAAI;IAAA;IAAA1B,cAAA,GAAAY,CAAA;IAAAZ,cAAA,GAAAG,CAAA;IACxCgB,aAAa,CAACO,IAAI,CAAC;IAAA;IAAA1B,cAAA,GAAAG,CAAA;IACnBQ,KAAK,CAACgB,QAAQ,CAACD,IAAI,CAAC;EACtB,CAAC;EAAA;EAAA1B,cAAA,GAAAG,CAAA;EAED,IAAMyB,oBAAoB,GAAG,SAAvBA,oBAAoBA,CAAA,EAAQ;IAAA;IAAA5B,cAAA,GAAAY,CAAA;IAAAZ,cAAA,GAAAG,CAAA;IAChC;IAAA,CAAAH,cAAA,GAAA6B,CAAA,UAAAlB,KAAK;IAAA;IAAA,CAAAX,cAAA,GAAA6B,CAAA,UAALlB,KAAK,CAAEmB,eAAe;IAAA;IAAA,CAAA9B,cAAA,GAAA6B,CAAA,UAAtBlB,KAAK,CAAEmB,eAAe,CAAE,CAAE;EAC5B,CAAC;EAAA;EAAA9B,cAAA,GAAAG,CAAA;EAED,OACEK,OAAA,CAAAS,OAAA,CAAAc,aAAA,CAAC1B,cAAA,CAAA2B,IAAI;IAACC,KAAK,EAAET,MAAM,CAACU;EAAS,GAC3B1B,OAAA,CAAAS,OAAA,CAAAc,aAAA,CAAC7B,sBAAA,CAAAiC,cAAc;IACbC,MAAM,EAAE,8BAA8B;IACtCC,KAAK,EAAEnB,UAAU;IACjBoB,WAAW,EAAE,IAAAhC,SAAA,CAAAiC,SAAS,EAAC,uBAAuB,CAAC;IAC/CC,YAAY,EAAEf,gBAAgB;IAC9BgB,oBAAoB,EAAEjB,MAAM,CAACkB,oBAAoB;IACjDC,SAAS,EAAE;EAAG,EACd,CAeG;AAEX,CAAC;AAAA;AAAA3C,cAAA,GAAAG,CAAA;AAvCYmB,OAAA,CAAAZ,UAAU,GAAAA,UAAA;AAAA;AAAAV,cAAA,GAAAG,CAAA;AAyCVmB,OAAA,CAAAC,SAAS,GAAG,IAAArB,sBAAA,CAAA0C,mBAAmB,EAAC,UAAAC,KAAA,EAAwC;EAAA;EAAA7C,cAAA,GAAAY,CAAA;EAAA,IAAtCkC,UAAU;IAAA;IAAA,CAAA9C,cAAA,GAAAG,CAAA,QAAA0C,KAAA,CAAVC,UAAU;IAAEC,UAAU;IAAA;IAAA,CAAA/C,cAAA,GAAAG,CAAA,QAAA0C,KAAA,CAAVE,UAAU;IAAEC,SAAS;IAAA;IAAA,CAAAhD,cAAA,GAAAG,CAAA,QAAA0C,KAAA,CAATG,SAAS;EAAA;EAAAhD,cAAA,GAAAG,CAAA;EAC9E,OAAO;IACL+B,SAAS,EAAE;MACTe,UAAU,EAAE,QAAQ;MACpBC,cAAc,EAAEJ,UAAU,CAACK,MAAM;MACjCC,iBAAiB,EAAEN,UAAU,CAACK,MAAM;MACpCE,cAAc,EAAEN,UAAU,CAACO,aAAa;MACxCC,iBAAiB,EAAER,UAAU,CAACO,aAAa;MAC3CE,aAAa,EAAE,KAAK;MACpBC,KAAK,EAAE;KACR;IACDf,oBAAoB,EAAE;MACpBgB,IAAI,EAAE,CAAC;MACPC,OAAO,EAAEX,SAAS,CAACY,YAAY;MAC/BH,KAAK,EAAE;KACR;IACDI,WAAW,EAAE;MACXC,SAAS,EAAEd,SAAS,CAACY;KACtB;IACDG,cAAc,EAAE;MACdC,MAAM,EAAEhB,SAAS,CAACiB,SAAS;MAC3BR,KAAK,EAAET,SAAS,CAACiB;;GAEpB;AACH,CAAC,CAAC", "ignoreList": []}