0314edfabc8e433df27c57cc0b0c686e
'use strict';

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.Extrapolate = exports.ColorSpace = void 0;
exports.interpolateColor = interpolateColor;
exports.useInterpolateConfig = useInterpolateConfig;
var _Colors = require("./Colors.js");
var _core = require("./core.js");
var _interpolation = require("./interpolation.js");
var _useSharedValue = require("./hook/useSharedValue.js");
var _errors = require("./errors.js");
var Extrapolate = exports.Extrapolate = _interpolation.Extrapolation;
var interpolateColorsHSV = function interpolateColorsHSV(value, inputRange, colors, options) {
  'worklet';

  var h = 0;
  var _options$useCorrected = options.useCorrectedHSVInterpolation,
    useCorrectedHSVInterpolation = _options$useCorrected === void 0 ? true : _options$useCorrected;
  if (useCorrectedHSVInterpolation) {
    var correctedInputRange = [inputRange[0]];
    var originalH = colors.h;
    var correctedH = [originalH[0]];
    for (var i = 1; i < originalH.length; ++i) {
      var d = originalH[i] - originalH[i - 1];
      if (originalH[i] > originalH[i - 1] && d > 0.5) {
        correctedInputRange.push(inputRange[i]);
        correctedInputRange.push(inputRange[i] + 0.00001);
        correctedH.push(originalH[i] - 1);
        correctedH.push(originalH[i]);
      } else if (originalH[i] < originalH[i - 1] && d < -0.5) {
        correctedInputRange.push(inputRange[i]);
        correctedInputRange.push(inputRange[i] + 0.00001);
        correctedH.push(originalH[i] + 1);
        correctedH.push(originalH[i]);
      } else {
        correctedInputRange.push(inputRange[i]);
        correctedH.push(originalH[i]);
      }
    }
    h = ((0, _interpolation.interpolate)(value, correctedInputRange, correctedH, _interpolation.Extrapolation.CLAMP) + 1) % 1;
  } else {
    h = (0, _interpolation.interpolate)(value, inputRange, colors.h, _interpolation.Extrapolation.CLAMP);
  }
  var s = (0, _interpolation.interpolate)(value, inputRange, colors.s, _interpolation.Extrapolation.CLAMP);
  var v = (0, _interpolation.interpolate)(value, inputRange, colors.v, _interpolation.Extrapolation.CLAMP);
  var a = (0, _interpolation.interpolate)(value, inputRange, colors.a, _interpolation.Extrapolation.CLAMP);
  return (0, _Colors.hsvToColor)(h, s, v, a);
};
var toLinearSpace = function toLinearSpace(x, gamma) {
  'worklet';

  return x.map(function (v) {
    return Math.pow(v / 255, gamma);
  });
};
var toGammaSpace = function toGammaSpace(x, gamma) {
  'worklet';

  return Math.round(Math.pow(x, 1 / gamma) * 255);
};
var interpolateColorsRGB = function interpolateColorsRGB(value, inputRange, colors, options) {
  'worklet';

  var _options$gamma = options.gamma,
    gamma = _options$gamma === void 0 ? 2.2 : _options$gamma;
  var outputR = colors.r,
    outputG = colors.g,
    outputB = colors.b;
  if (gamma !== 1) {
    outputR = toLinearSpace(outputR, gamma);
    outputG = toLinearSpace(outputG, gamma);
    outputB = toLinearSpace(outputB, gamma);
  }
  var r = (0, _interpolation.interpolate)(value, inputRange, outputR, _interpolation.Extrapolation.CLAMP);
  var g = (0, _interpolation.interpolate)(value, inputRange, outputG, _interpolation.Extrapolation.CLAMP);
  var b = (0, _interpolation.interpolate)(value, inputRange, outputB, _interpolation.Extrapolation.CLAMP);
  var a = (0, _interpolation.interpolate)(value, inputRange, colors.a, _interpolation.Extrapolation.CLAMP);
  if (gamma === 1) {
    return (0, _Colors.rgbaColor)(r, g, b, a);
  }
  return (0, _Colors.rgbaColor)(toGammaSpace(r, gamma), toGammaSpace(g, gamma), toGammaSpace(b, gamma), a);
};
var getInterpolateRGB = function getInterpolateRGB(colors) {
  'worklet';

  var r = [];
  var g = [];
  var b = [];
  var a = [];
  for (var i = 0; i < colors.length; ++i) {
    var color = colors[i];
    var processedColor = (0, _Colors.processColor)(color);
    if (processedColor !== null && processedColor !== undefined) {
      r.push((0, _Colors.red)(processedColor));
      g.push((0, _Colors.green)(processedColor));
      b.push((0, _Colors.blue)(processedColor));
      a.push((0, _Colors.opacity)(processedColor));
    }
  }
  return {
    r: r,
    g: g,
    b: b,
    a: a
  };
};
var getInterpolateHSV = function getInterpolateHSV(colors) {
  'worklet';

  var h = [];
  var s = [];
  var v = [];
  var a = [];
  for (var i = 0; i < colors.length; ++i) {
    var color = colors[i];
    var processedColor = (0, _Colors.processColor)(color);
    if (typeof processedColor === 'number') {
      var processedHSVColor = (0, _Colors.RGBtoHSV)((0, _Colors.red)(processedColor), (0, _Colors.green)(processedColor), (0, _Colors.blue)(processedColor));
      h.push(processedHSVColor.h);
      s.push(processedHSVColor.s);
      v.push(processedHSVColor.v);
      a.push((0, _Colors.opacity)(processedColor));
    }
  }
  return {
    h: h,
    s: s,
    v: v,
    a: a
  };
};
function interpolateColor(value, inputRange, outputRange) {
  'worklet';

  var colorSpace = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : 'RGB';
  var options = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : {};
  if (colorSpace === 'HSV') {
    return interpolateColorsHSV(value, inputRange, getInterpolateHSV(outputRange), options);
  } else if (colorSpace === 'RGB') {
    return interpolateColorsRGB(value, inputRange, getInterpolateRGB(outputRange), options);
  }
  throw new _errors.ReanimatedError(`Invalid color space provided: ${colorSpace}. Supported values are: ['RGB', 'HSV'].`);
}
var ColorSpace = exports.ColorSpace = function (ColorSpace) {
  ColorSpace[ColorSpace["RGB"] = 0] = "RGB";
  ColorSpace[ColorSpace["HSV"] = 1] = "HSV";
  return ColorSpace;
}({});
function useInterpolateConfig(inputRange, outputRange) {
  var colorSpace = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : ColorSpace.RGB;
  var options = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : {};
  return (0, _useSharedValue.useSharedValue)({
    inputRange: inputRange,
    outputRange: outputRange,
    colorSpace: colorSpace,
    cache: (0, _core.makeMutable)(null),
    options: options
  });
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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