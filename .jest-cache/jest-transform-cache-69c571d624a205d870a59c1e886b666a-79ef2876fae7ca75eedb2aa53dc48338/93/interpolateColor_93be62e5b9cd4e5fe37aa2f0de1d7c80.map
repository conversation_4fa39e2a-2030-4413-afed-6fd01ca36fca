{"version": 3, "names": ["Object", "defineProperty", "exports", "value", "Extrapolate", "ColorSpace", "interpolateColor", "useInterpolateConfig", "_Colors", "require", "_core", "_interpolation", "_useSharedValue", "_errors", "Extrapolation", "interpolateColorsHSV", "inputRange", "colors", "options", "h", "_options$useCorrected", "useCorrectedHSVInterpolation", "correctedInputRange", "originalH", "correctedH", "i", "length", "d", "push", "interpolate", "CLAMP", "s", "v", "a", "hsvToColor", "toLinearSpace", "x", "gamma", "map", "Math", "pow", "toGammaSpace", "round", "interpolateColorsRGB", "_options$gamma", "outputR", "r", "outputG", "g", "outputB", "b", "rgbaColor", "getInterpolateRGB", "color", "processedColor", "processColor", "undefined", "red", "green", "blue", "opacity", "getInterpolateHSV", "processedHSVColor", "RGBtoHSV", "outputRange", "colorSpace", "arguments", "ReanimatedError", "RGB", "useSharedValue", "cache", "makeMutable"], "sources": ["../../src/interpolateColor.ts"], "sourcesContent": [null], "mappings": "AAAA,YAAY;;AAAAA,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,KAAA;AAAA;AAAAD,OAAA,CAAAE,WAAA,GAAAF,OAAA,CAAAG,UAAA;AAAAH,OAAA,CAAAI,gBAAA,GAAAA,gBAAA;AAAAJ,OAAA,CAAAK,oBAAA,GAAAA,oBAAA;AACZ,IAAAC,OAAA,GAAAC,OAAA;AAUA,IAAAC,KAAA,GAAAD,OAAA;AACA,IAAAE,cAAA,GAAAF,OAAA;AAEA,IAAAG,eAAA,GAAAH,OAAA;AACA,IAAAI,OAAA,GAAAJ,OAAA;AAGO,IAAML,WAAW,GAAAF,OAAA,CAAAE,WAAA,GAAGU,4BAAa;AAcxC,IAAMC,oBAAoB,GAAG,SAAvBA,oBAAoBA,CACxBZ,KAAa,EACba,UAA6B,EAC7BC,MAAsB,EACtBC,OAA6B,EAC1B;EACH,SAAS;;EACT,IAAIC,CAAC,GAAG,CAAC;EACT,IAAAC,qBAAA,GAAgDF,OAAO,CAA/CG,4BAA4B;IAA5BA,4BAA4B,GAAAD,qBAAA,cAAG,OAAAA,qBAAA;EACvC,IAAIC,4BAA4B,EAAE;IAKhC,IAAMC,mBAAmB,GAAG,CAACN,UAAU,CAAC,CAAC,CAAC,CAAC;IAC3C,IAAMO,SAAS,GAAGN,MAAM,CAACE,CAAC;IAC1B,IAAMK,UAAU,GAAG,CAACD,SAAS,CAAC,CAAC,CAAC,CAAC;IAEjC,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,SAAS,CAACG,MAAM,EAAE,EAAED,CAAC,EAAE;MACzC,IAAME,CAAC,GAAGJ,SAAS,CAACE,CAAC,CAAC,GAAGF,SAAS,CAACE,CAAC,GAAG,CAAC,CAAC;MACzC,IAAIF,SAAS,CAACE,CAAC,CAAC,GAAGF,SAAS,CAACE,CAAC,GAAG,CAAC,CAAC,IAAIE,CAAC,GAAG,GAAG,EAAE;QAC9CL,mBAAmB,CAACM,IAAI,CAACZ,UAAU,CAACS,CAAC,CAAC,CAAC;QACvCH,mBAAmB,CAACM,IAAI,CAACZ,UAAU,CAACS,CAAC,CAAC,GAAG,OAAO,CAAC;QACjDD,UAAU,CAACI,IAAI,CAACL,SAAS,CAACE,CAAC,CAAC,GAAG,CAAC,CAAC;QACjCD,UAAU,CAACI,IAAI,CAACL,SAAS,CAACE,CAAC,CAAC,CAAC;MAC/B,CAAC,MAAM,IAAIF,SAAS,CAACE,CAAC,CAAC,GAAGF,SAAS,CAACE,CAAC,GAAG,CAAC,CAAC,IAAIE,CAAC,GAAG,CAAC,GAAG,EAAE;QACtDL,mBAAmB,CAACM,IAAI,CAACZ,UAAU,CAACS,CAAC,CAAC,CAAC;QACvCH,mBAAmB,CAACM,IAAI,CAACZ,UAAU,CAACS,CAAC,CAAC,GAAG,OAAO,CAAC;QACjDD,UAAU,CAACI,IAAI,CAACL,SAAS,CAACE,CAAC,CAAC,GAAG,CAAC,CAAC;QACjCD,UAAU,CAACI,IAAI,CAACL,SAAS,CAACE,CAAC,CAAC,CAAC;MAC/B,CAAC,MAAM;QACLH,mBAAmB,CAACM,IAAI,CAACZ,UAAU,CAACS,CAAC,CAAC,CAAC;QACvCD,UAAU,CAACI,IAAI,CAACL,SAAS,CAACE,CAAC,CAAC,CAAC;MAC/B;IACF;IACAN,CAAC,GACC,CAAC,IAAAU,0BAAW,EACV1B,KAAK,EACLmB,mBAAmB,EACnBE,UAAU,EACVV,4BAAa,CAACgB,KAChB,CAAC,GACC,CAAC,IACH,CAAC;EACL,CAAC,MAAM;IACLX,CAAC,GAAG,IAAAU,0BAAW,EAAC1B,KAAK,EAAEa,UAAU,EAAEC,MAAM,CAACE,CAAC,EAAEL,4BAAa,CAACgB,KAAK,CAAC;EACnE;EACA,IAAMC,CAAC,GAAG,IAAAF,0BAAW,EAAC1B,KAAK,EAAEa,UAAU,EAAEC,MAAM,CAACc,CAAC,EAAEjB,4BAAa,CAACgB,KAAK,CAAC;EACvE,IAAME,CAAC,GAAG,IAAAH,0BAAW,EAAC1B,KAAK,EAAEa,UAAU,EAAEC,MAAM,CAACe,CAAC,EAAElB,4BAAa,CAACgB,KAAK,CAAC;EACvE,IAAMG,CAAC,GAAG,IAAAJ,0BAAW,EAAC1B,KAAK,EAAEa,UAAU,EAAEC,MAAM,CAACgB,CAAC,EAAEnB,4BAAa,CAACgB,KAAK,CAAC;EACvE,OAAO,IAAAI,kBAAU,EAACf,CAAC,EAAEY,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAAC;AAC/B,CAAC;AAED,IAAME,aAAa,GAAG,SAAhBA,aAAaA,CAAIC,CAAW,EAAEC,KAAa,EAAe;EAC9D,SAAS;;EACT,OAAOD,CAAC,CAACE,GAAG,CAAE,UAAAN,CAAC;IAAA,OAAKO,IAAI,CAACC,GAAG,CAACR,CAAC,GAAG,GAAG,EAAEK,KAAK,CAAC;EAAA,EAAC;AAC/C,CAAC;AAED,IAAMI,YAAY,GAAG,SAAfA,YAAYA,CAAIL,CAAS,EAAEC,KAAa,EAAa;EACzD,SAAS;;EACT,OAAOE,IAAI,CAACG,KAAK,CAACH,IAAI,CAACC,GAAG,CAACJ,CAAC,EAAE,CAAC,GAAGC,KAAK,CAAC,GAAG,GAAG,CAAC;AACjD,CAAC;AAED,IAAMM,oBAAoB,GAAG,SAAvBA,oBAAoBA,CACxBxC,KAAa,EACba,UAA6B,EAC7BC,MAAsB,EACtBC,OAA6B,EAC1B;EACH,SAAS;;EACT,IAAA0B,cAAA,GAAwB1B,OAAO,CAAvBmB,KAAK;IAALA,KAAK,GAAAO,cAAA,cAAG,MAAAA,cAAA;EAChB,IAASC,OAAO,GAA6B5B,MAAM,CAA7C6B,CAAC;IAAcC,OAAO,GAAiB9B,MAAM,CAAjC+B,CAAC;IAAcC,OAAA,GAAYhC,MAAM,CAArBiC,CAAC;EAC/B,IAAIb,KAAK,KAAK,CAAC,EAAE;IACfQ,OAAO,GAAGV,aAAa,CAACU,OAAO,EAAER,KAAK,CAAC;IACvCU,OAAO,GAAGZ,aAAa,CAACY,OAAO,EAAEV,KAAK,CAAC;IACvCY,OAAO,GAAGd,aAAa,CAACc,OAAO,EAAEZ,KAAK,CAAC;EACzC;EACA,IAAMS,CAAC,GAAG,IAAAjB,0BAAW,EAAC1B,KAAK,EAAEa,UAAU,EAAE6B,OAAO,EAAE/B,4BAAa,CAACgB,KAAK,CAAC;EACtE,IAAMkB,CAAC,GAAG,IAAAnB,0BAAW,EAAC1B,KAAK,EAAEa,UAAU,EAAE+B,OAAO,EAAEjC,4BAAa,CAACgB,KAAK,CAAC;EACtE,IAAMoB,CAAC,GAAG,IAAArB,0BAAW,EAAC1B,KAAK,EAAEa,UAAU,EAAEiC,OAAO,EAAEnC,4BAAa,CAACgB,KAAK,CAAC;EACtE,IAAMG,CAAC,GAAG,IAAAJ,0BAAW,EAAC1B,KAAK,EAAEa,UAAU,EAAEC,MAAM,CAACgB,CAAC,EAAEnB,4BAAa,CAACgB,KAAK,CAAC;EACvE,IAAIO,KAAK,KAAK,CAAC,EAAE;IACf,OAAO,IAAAc,iBAAS,EAACL,CAAC,EAAEE,CAAC,EAAEE,CAAC,EAAEjB,CAAC,CAAC;EAC9B;EACA,OAAO,IAAAkB,iBAAS,EACdV,YAAY,CAACK,CAAC,EAAET,KAAK,CAAC,EACtBI,YAAY,CAACO,CAAC,EAAEX,KAAK,CAAC,EACtBI,YAAY,CAACS,CAAC,EAAEb,KAAK,CAAC,EACtBJ,CACF,CAAC;AACH,CAAC;AASD,IAAMmB,iBAAiB,GACrB,SADIA,iBAAiBA,CACrBnC,MAAoC,EACjB;EACnB,SAAS;;EAET,IAAM6B,CAAC,GAAG,EAAE;EACZ,IAAME,CAAC,GAAG,EAAE;EACZ,IAAME,CAAC,GAAG,EAAE;EACZ,IAAMjB,CAAC,GAAG,EAAE;EACZ,KAAK,IAAIR,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGR,MAAM,CAACS,MAAM,EAAE,EAAED,CAAC,EAAE;IACtC,IAAM4B,KAAK,GAAGpC,MAAM,CAACQ,CAAC,CAAC;IACvB,IAAM6B,cAAc,GAAG,IAAAC,oBAAY,EAACF,KAAK,CAAC;IAE1C,IAAIC,cAAc,KAAK,IAAI,IAAIA,cAAc,KAAKE,SAAS,EAAE;MAC3DV,CAAC,CAAClB,IAAI,CAAC,IAAA6B,WAAG,EAACH,cAAc,CAAC,CAAC;MAC3BN,CAAC,CAACpB,IAAI,CAAC,IAAA8B,aAAK,EAACJ,cAAc,CAAC,CAAC;MAC7BJ,CAAC,CAACtB,IAAI,CAAC,IAAA+B,YAAI,EAACL,cAAc,CAAC,CAAC;MAC5BrB,CAAC,CAACL,IAAI,CAAC,IAAAgC,eAAO,EAACN,cAAc,CAAC,CAAC;IACjC;EACF;EACA,OAAO;IAAER,CAAC,EAADA,CAAC;IAAEE,CAAC,EAADA,CAAC;IAAEE,CAAC,EAADA,CAAC;IAAEjB,CAAA,EAAAA;EAAE,CAAC;AACvB,CAAC;AASD,IAAM4B,iBAAiB,GACrB,SADIA,iBAAiBA,CACrB5C,MAAoC,EACjB;EACnB,SAAS;;EACT,IAAME,CAAC,GAAG,EAAE;EACZ,IAAMY,CAAC,GAAG,EAAE;EACZ,IAAMC,CAAC,GAAG,EAAE;EACZ,IAAMC,CAAC,GAAG,EAAE;EACZ,KAAK,IAAIR,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGR,MAAM,CAACS,MAAM,EAAE,EAAED,CAAC,EAAE;IACtC,IAAM4B,KAAK,GAAGpC,MAAM,CAACQ,CAAC,CAAC;IACvB,IAAM6B,cAAc,GAAG,IAAAC,oBAAY,EAACF,KAAK,CAAQ;IACjD,IAAI,OAAOC,cAAc,KAAK,QAAQ,EAAE;MACtC,IAAMQ,iBAAiB,GAAG,IAAAC,gBAAQ,EAChC,IAAAN,WAAG,EAACH,cAAc,CAAC,EACnB,IAAAI,aAAK,EAACJ,cAAc,CAAC,EACrB,IAAAK,YAAI,EAACL,cAAc,CACrB,CAAC;MAEDnC,CAAC,CAACS,IAAI,CAACkC,iBAAiB,CAAC3C,CAAC,CAAC;MAC3BY,CAAC,CAACH,IAAI,CAACkC,iBAAiB,CAAC/B,CAAC,CAAC;MAC3BC,CAAC,CAACJ,IAAI,CAACkC,iBAAiB,CAAC9B,CAAC,CAAC;MAC3BC,CAAC,CAACL,IAAI,CAAC,IAAAgC,eAAO,EAACN,cAAc,CAAC,CAAC;IACjC;EACF;EACA,OAAO;IAAEnC,CAAC,EAADA,CAAC;IAAEY,CAAC,EAADA,CAAC;IAAEC,CAAC,EAADA,CAAC;IAAEC,CAAA,EAAAA;EAAE,CAAC;AACvB,CAAC;AAoCM,SAAS3B,gBAAgBA,CAC9BH,KAAa,EACba,UAA6B,EAC7BgD,WAAyC,EAGxB;EACjB,SAAS;;EAAA,IAHTC,UAAyB,GAAAC,SAAA,CAAAxC,MAAA,QAAAwC,SAAA,QAAAV,SAAA,GAAAU,SAAA,MAAG,KAAK;EAAA,IACjChD,OAA6B,GAAAgD,SAAA,CAAAxC,MAAA,QAAAwC,SAAA,QAAAV,SAAA,GAAAU,SAAA,MAAG,CAAC,CAAC;EAGlC,IAAID,UAAU,KAAK,KAAK,EAAE;IACxB,OAAOlD,oBAAoB,CACzBZ,KAAK,EACLa,UAAU,EACV6C,iBAAiB,CAACG,WAAW,CAAC,EAC9B9C,OACF,CAAC;EACH,CAAC,MAAM,IAAI+C,UAAU,KAAK,KAAK,EAAE;IAC/B,OAAOtB,oBAAoB,CACzBxC,KAAK,EACLa,UAAU,EACVoC,iBAAiB,CAACY,WAAW,CAAC,EAC9B9C,OACF,CAAC;EACH;EACA,MAAM,IAAIiD,uBAAe,CACvB,iCACEF,UAAU,yCAEd,CAAC;AACH;AAEA,IAAY5D,UAAU,GAAAH,OAAA,CAAAG,UAAA,aAAVA,UAAU;EAAVA,UAAU,CAAVA,UAAU;EAAVA,UAAU,CAAVA,UAAU;EAAA,OAAVA,UAAU;AAAA;AAaf,SAASE,oBAAoBA,CAClCS,UAA6B,EAC7BgD,WAAyC,EAGT;EAAA,IAFhCC,UAAU,GAAAC,SAAA,CAAAxC,MAAA,QAAAwC,SAAA,QAAAV,SAAA,GAAAU,SAAA,MAAG7D,UAAU,CAAC+D,GAAG;EAAA,IAC3BlD,OAA6B,GAAAgD,SAAA,CAAAxC,MAAA,QAAAwC,SAAA,QAAAV,SAAA,GAAAU,SAAA,MAAG,CAAC,CAAC;EAElC,OAAO,IAAAG,8BAAc,EAAoB;IACvCrD,UAAU,EAAVA,UAAU;IACVgD,WAAW,EAAXA,WAAW;IACXC,UAAU,EAAVA,UAAU;IACVK,KAAK,EAAE,IAAAC,iBAAW,EAAyC,IAAI,CAAC;IAChErD,OAAA,EAAAA;EACF,CAAC,CAAC;AACJ", "ignoreList": []}