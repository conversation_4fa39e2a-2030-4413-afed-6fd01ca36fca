ac5b101c00728612f9337a5b595553e5
"use strict";

/* istanbul ignore next */
function cov_2mib2v16p4() {
  var path = "/Users/<USER>/projects/react-native/payment-module/src/presentation/payment-result/action-data.ts";
  var hash = "3f36dd94a5475a0fe8083e5514f65bc8722b5a1c";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/projects/react-native/payment-module/src/presentation/payment-result/action-data.ts",
    statementMap: {
      "0": {
        start: {
          line: 3,
          column: 0
        },
        end: {
          line: 5,
          column: 3
        }
      },
      "1": {
        start: {
          line: 6,
          column: 0
        },
        end: {
          line: 6,
          column: 116
        }
      },
      "2": {
        start: {
          line: 7,
          column: 18
        },
        end: {
          line: 7,
          column: 52
        }
      },
      "3": {
        start: {
          line: 8,
          column: 0
        },
        end: {
          line: 20,
          column: 3
        }
      },
      "4": {
        start: {
          line: 21,
          column: 0
        },
        end: {
          line: 29,
          column: 3
        }
      },
      "5": {
        start: {
          line: 30,
          column: 0
        },
        end: {
          line: 42,
          column: 3
        }
      },
      "6": {
        start: {
          line: 43,
          column: 0
        },
        end: {
          line: 47,
          column: 3
        }
      }
    },
    fnMap: {},
    branchMap: {},
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0
    },
    f: {},
    b: {},
    inputSourceMap: {
      version: 3,
      file: undefined,
      names: ["Constants_1", "require", "exports", "actionsSuccess", "type", "TRANSFER_RESULT_ACTION", "TRANSFER_SHARE", "title", "icon", "TRANSER_SAVE_TEMPLATE", "TRANSER_SAVE_BENEFICIARY", "actionsSuccessUserInternal", "actionsPending", "TRANSFER_MANAGER", "actionsFail", "TRANSFER_SUPPORT"],
      sourceRoot: undefined,
      sources: ["/Users/<USER>/projects/react-native/payment-module/src/presentation/payment-result/action-data.ts"],
      sourcesContent: ["// import Images from '../../assets/images/Images';\nimport {TRANSFER_RESULT_ACTION} from '../../commons/Constants';\nimport {ActionProps} from './components/transfer-result-action/types';\n\nexport const actionsSuccess: ActionProps[] = [\n  {\n    type: TRANSFER_RESULT_ACTION.TRANSFER_SHARE,\n    title: 'Chia s\u1EBB',\n    icon: 'tone-share',\n  },\n  {\n    type: TRANSFER_RESULT_ACTION.TRANSER_SAVE_TEMPLATE,\n    title: 'Thanh to\xE1n t\u1EF1 \u0111\u1ED9ng',\n    icon: 'tone-save',\n  },\n  {\n    type: TRANSFER_RESULT_ACTION.TRANSER_SAVE_BENEFICIARY,\n    title: 'L\u01B0u ho\xE1 \u0111\u01A1n',\n    icon: 'tone-user-plus-add',\n  },\n];\n\nexport const actionsSuccessUserInternal: ActionProps[] = [\n  {\n    type: TRANSFER_RESULT_ACTION.TRANSFER_SHARE,\n    title: 'Chia s\u1EBB',\n    icon: 'tone-share',\n  },\n  {\n    type: TRANSFER_RESULT_ACTION.TRANSER_SAVE_TEMPLATE,\n    title: 'L\u01B0u m\u1EABu',\n    icon: 'tone-save',\n  },\n];\n\nexport const actionsPending: ActionProps[] = [\n  {\n    type: TRANSFER_RESULT_ACTION.TRANSFER_SHARE,\n    title: 'Share',\n    icon: 'tone-share',\n  },\n  {\n    type: TRANSFER_RESULT_ACTION.TRANSER_SAVE_TEMPLATE,\n    title: 'L\u01B0u m\u1EABu',\n    icon: 'tone-save',\n  },\n  {\n    type: TRANSFER_RESULT_ACTION.TRANSFER_MANAGER,\n    title: 'Qu\u1EA3n l\xFD l\u1EC7nh chuy\u1EC3n ti\u1EC1n',\n    icon: 'tone-folder',\n  },\n];\n\nexport const actionsFail: ActionProps[] = [\n  {\n    type: TRANSFER_RESULT_ACTION.TRANSFER_SUPPORT,\n    title: 'Trung t\xE2m h\u1ED7 tr\u1EE3',\n    icon: 'tone-support-headphone',\n  },\n];\n"],
      mappings: ";;;;;;AACA,IAAAA,WAAA,GAAAC,OAAA;AAGaC,OAAA,CAAAC,cAAc,GAAkB,CAC3C;EACEC,IAAI,EAAEJ,WAAA,CAAAK,sBAAsB,CAACC,cAAc;EAC3CC,KAAK,EAAE,SAAS;EAChBC,IAAI,EAAE;CACP,EACD;EACEJ,IAAI,EAAEJ,WAAA,CAAAK,sBAAsB,CAACI,qBAAqB;EAClDF,KAAK,EAAE,oBAAoB;EAC3BC,IAAI,EAAE;CACP,EACD;EACEJ,IAAI,EAAEJ,WAAA,CAAAK,sBAAsB,CAACK,wBAAwB;EACrDH,KAAK,EAAE,aAAa;EACpBC,IAAI,EAAE;CACP,CACF;AAEYN,OAAA,CAAAS,0BAA0B,GAAkB,CACvD;EACEP,IAAI,EAAEJ,WAAA,CAAAK,sBAAsB,CAACC,cAAc;EAC3CC,KAAK,EAAE,SAAS;EAChBC,IAAI,EAAE;CACP,EACD;EACEJ,IAAI,EAAEJ,WAAA,CAAAK,sBAAsB,CAACI,qBAAqB;EAClDF,KAAK,EAAE,SAAS;EAChBC,IAAI,EAAE;CACP,CACF;AAEYN,OAAA,CAAAU,cAAc,GAAkB,CAC3C;EACER,IAAI,EAAEJ,WAAA,CAAAK,sBAAsB,CAACC,cAAc;EAC3CC,KAAK,EAAE,OAAO;EACdC,IAAI,EAAE;CACP,EACD;EACEJ,IAAI,EAAEJ,WAAA,CAAAK,sBAAsB,CAACI,qBAAqB;EAClDF,KAAK,EAAE,SAAS;EAChBC,IAAI,EAAE;CACP,EACD;EACEJ,IAAI,EAAEJ,WAAA,CAAAK,sBAAsB,CAACQ,gBAAgB;EAC7CN,KAAK,EAAE,0BAA0B;EACjCC,IAAI,EAAE;CACP,CACF;AAEYN,OAAA,CAAAY,WAAW,GAAkB,CACxC;EACEV,IAAI,EAAEJ,WAAA,CAAAK,sBAAsB,CAACU,gBAAgB;EAC7CR,KAAK,EAAE,kBAAkB;EACzBC,IAAI,EAAE;CACP,CACF",
      ignoreList: []
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "3f36dd94a5475a0fe8083e5514f65bc8722b5a1c"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_2mib2v16p4 = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_2mib2v16p4();
cov_2mib2v16p4().s[0]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_2mib2v16p4().s[1]++;
exports.actionsFail = exports.actionsPending = exports.actionsSuccessUserInternal = exports.actionsSuccess = void 0;
var Constants_1 =
/* istanbul ignore next */
(cov_2mib2v16p4().s[2]++, require("../../commons/Constants"));
/* istanbul ignore next */
cov_2mib2v16p4().s[3]++;
exports.actionsSuccess = [{
  type: Constants_1.TRANSFER_RESULT_ACTION.TRANSFER_SHARE,
  title: 'Chia sẻ',
  icon: 'tone-share'
}, {
  type: Constants_1.TRANSFER_RESULT_ACTION.TRANSER_SAVE_TEMPLATE,
  title: 'Thanh toán tự động',
  icon: 'tone-save'
}, {
  type: Constants_1.TRANSFER_RESULT_ACTION.TRANSER_SAVE_BENEFICIARY,
  title: 'Lưu hoá đơn',
  icon: 'tone-user-plus-add'
}];
/* istanbul ignore next */
cov_2mib2v16p4().s[4]++;
exports.actionsSuccessUserInternal = [{
  type: Constants_1.TRANSFER_RESULT_ACTION.TRANSFER_SHARE,
  title: 'Chia sẻ',
  icon: 'tone-share'
}, {
  type: Constants_1.TRANSFER_RESULT_ACTION.TRANSER_SAVE_TEMPLATE,
  title: 'Lưu mẫu',
  icon: 'tone-save'
}];
/* istanbul ignore next */
cov_2mib2v16p4().s[5]++;
exports.actionsPending = [{
  type: Constants_1.TRANSFER_RESULT_ACTION.TRANSFER_SHARE,
  title: 'Share',
  icon: 'tone-share'
}, {
  type: Constants_1.TRANSFER_RESULT_ACTION.TRANSER_SAVE_TEMPLATE,
  title: 'Lưu mẫu',
  icon: 'tone-save'
}, {
  type: Constants_1.TRANSFER_RESULT_ACTION.TRANSFER_MANAGER,
  title: 'Quản lý lệnh chuyển tiền',
  icon: 'tone-folder'
}];
/* istanbul ignore next */
cov_2mib2v16p4().s[6]++;
exports.actionsFail = [{
  type: Constants_1.TRANSFER_RESULT_ACTION.TRANSFER_SUPPORT,
  title: 'Trung tâm hỗ trợ',
  icon: 'tone-support-headphone'
}];
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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