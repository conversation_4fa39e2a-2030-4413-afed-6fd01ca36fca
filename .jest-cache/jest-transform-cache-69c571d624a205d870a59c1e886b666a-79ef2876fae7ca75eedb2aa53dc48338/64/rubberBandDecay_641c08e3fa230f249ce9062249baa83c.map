{"version": 3, "names": ["Object", "defineProperty", "exports", "value", "rubberBandDecay", "_utils", "require", "DERIVATIVE_EPS", "animation", "now", "config", "lastTimestamp", "startTimestamp", "current", "velocity", "deltaTime", "Math", "min", "clampIndex", "abs", "clamp", "derivative", "v", "exp", "deceleration", "SLOPE_FACTOR", "rubberBandFactor", "springActive", "VELOCITY_EPS", "velocityFactor"], "sources": ["../../../../src/animation/decay/rubberBandDecay.ts"], "sourcesContent": [null], "mappings": "AAAA,YAAY;;AAAAA,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,KAAA;AAAA;AAAAD,OAAA,CAAAE,eAAA,GAAAA,eAAA;AAEZ,IAAAC,MAAA,GAAAC,OAAA;AAEA,IAAMC,cAAc,GAAG,GAAG;AAEnB,SAASH,eAAeA,CAC7BI,SAA8B,EAC9BC,GAAW,EACXC,MAA6B,EACpB;EACT,SAAS;;EACT,IAAQC,aAAa,GAAwCH,SAAS,CAA9DG,aAAa;IAAEC,cAAc,GAAwBJ,SAAS,CAA/CI,cAAc;IAAEC,OAAO,GAAeL,SAAS,CAA/BK,OAAO;IAAEC,QAAA,GAAaN,SAAS,CAAtBM,QAAA;EAEhD,IAAMC,SAAS,GAAGC,IAAI,CAACC,GAAG,CAACR,GAAG,GAAGE,aAAa,EAAE,EAAE,CAAC;EACnD,IAAMO,UAAU,GACdF,IAAI,CAACG,GAAG,CAACN,OAAO,GAAGH,MAAM,CAACU,KAAK,CAAC,CAAC,CAAC,CAAC,GAAGJ,IAAI,CAACG,GAAG,CAACN,OAAO,GAAGH,MAAM,CAACU,KAAK,CAAC,CAAC,CAAC,CAAC,GACrE,CAAC,GACD,CAAC;EAEP,IAAIC,UAAU,GAAG,CAAC;EAClB,IAAIR,OAAO,GAAGH,MAAM,CAACU,KAAK,CAAC,CAAC,CAAC,IAAIP,OAAO,GAAGH,MAAM,CAACU,KAAK,CAAC,CAAC,CAAC,EAAE;IAC1DC,UAAU,GAAGR,OAAO,GAAGH,MAAM,CAACU,KAAK,CAACF,UAAU,CAAC;EACjD;EAEA,IAAMI,CAAC,GACLR,QAAQ,GACNE,IAAI,CAACO,GAAG,CACN,EAAE,CAAC,GAAGb,MAAM,CAACc,YAAY,CAAC,IAAIf,GAAG,GAAGG,cAAc,CAAC,GAAGa,mBACxD,CAAC,GACHJ,UAAU,GAAGX,MAAM,CAACgB,gBAAgB;EAEtC,IAAIV,IAAI,CAACG,GAAG,CAACE,UAAU,CAAC,GAAGd,cAAc,EAAE;IACzCC,SAAS,CAACmB,YAAY,GAAG,IAAI;EAC/B,CAAC,MAAM,IAAInB,SAAS,CAACmB,YAAY,EAAE;IACjCnB,SAAS,CAACK,OAAO,GAAGH,MAAM,CAACU,KAAK,CAACF,UAAU,CAAC;IAC5C,OAAO,IAAI;EACb,CAAC,MAAM,IAAIF,IAAI,CAACG,GAAG,CAACG,CAAC,CAAC,GAAGM,mBAAY,EAAE;IACrC,OAAO,IAAI;EACb;EAEApB,SAAS,CAACK,OAAO,GAAGA,OAAO,GAAIS,CAAC,GAAGZ,MAAM,CAACmB,cAAc,GAAGd,SAAS,GAAI,IAAI;EAC5EP,SAAS,CAACM,QAAQ,GAAGQ,CAAC;EACtBd,SAAS,CAACG,aAAa,GAAGF,GAAG;EAC7B,OAAO,KAAK;AACd", "ignoreList": []}