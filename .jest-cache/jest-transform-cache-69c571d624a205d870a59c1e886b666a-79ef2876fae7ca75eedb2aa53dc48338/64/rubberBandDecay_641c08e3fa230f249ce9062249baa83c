df853679f76b4d788ec429fa7f43215e
'use strict';

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.rubberBandDecay = rubberBandDecay;
var _utils = require("./utils.js");
var DERIVATIVE_EPS = 0.1;
function rubberBandDecay(animation, now, config) {
  'worklet';

  var lastTimestamp = animation.lastTimestamp,
    startTimestamp = animation.startTimestamp,
    current = animation.current,
    velocity = animation.velocity;
  var deltaTime = Math.min(now - lastTimestamp, 64);
  var clampIndex = Math.abs(current - config.clamp[0]) < Math.abs(current - config.clamp[1]) ? 0 : 1;
  var derivative = 0;
  if (current < config.clamp[0] || current > config.clamp[1]) {
    derivative = current - config.clamp[clampIndex];
  }
  var v = velocity * Math.exp(-(1 - config.deceleration) * (now - startTimestamp) * _utils.SLOPE_FACTOR) - derivative * config.rubberBandFactor;
  if (Math.abs(derivative) > DERIVATIVE_EPS) {
    animation.springActive = true;
  } else if (animation.springActive) {
    animation.current = config.clamp[clampIndex];
    return true;
  } else if (Math.abs(v) < _utils.VELOCITY_EPS) {
    return true;
  }
  animation.current = current + v * config.velocityFactor * deltaTime / 1000;
  animation.velocity = v;
  animation.lastTimestamp = now;
  return false;
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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