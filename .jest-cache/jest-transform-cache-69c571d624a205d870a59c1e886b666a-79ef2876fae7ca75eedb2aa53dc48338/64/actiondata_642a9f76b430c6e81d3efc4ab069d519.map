{"version": 3, "names": ["cov_2mib2v16p4", "actualCoverage", "s", "Constants_1", "require", "exports", "actionsSuccess", "type", "TRANSFER_RESULT_ACTION", "TRANSFER_SHARE", "title", "icon", "TRANSER_SAVE_TEMPLATE", "TRANSER_SAVE_BENEFICIARY", "actionsSuccessUserInternal", "actionsPending", "TRANSFER_MANAGER", "actionsFail", "TRANSFER_SUPPORT"], "sources": ["/Users/<USER>/projects/react-native/payment-module/src/presentation/payment-result/action-data.ts"], "sourcesContent": ["// import Images from '../../assets/images/Images';\nimport {TRANSFER_RESULT_ACTION} from '../../commons/Constants';\nimport {ActionProps} from './components/transfer-result-action/types';\n\nexport const actionsSuccess: ActionProps[] = [\n  {\n    type: TRANSFER_RESULT_ACTION.TRANSFER_SHARE,\n    title: 'Chia sẻ',\n    icon: 'tone-share',\n  },\n  {\n    type: TRANSFER_RESULT_ACTION.TRANSER_SAVE_TEMPLATE,\n    title: 'Thanh toán tự động',\n    icon: 'tone-save',\n  },\n  {\n    type: TRANSFER_RESULT_ACTION.TRANSER_SAVE_BENEFICIARY,\n    title: '<PERSON><PERSON><PERSON> hoá đơn',\n    icon: 'tone-user-plus-add',\n  },\n];\n\nexport const actionsSuccessUserInternal: ActionProps[] = [\n  {\n    type: TRANSFER_RESULT_ACTION.TRANSFER_SHARE,\n    title: 'Chia sẻ',\n    icon: 'tone-share',\n  },\n  {\n    type: TRANSFER_RESULT_ACTION.TRANSER_SAVE_TEMPLATE,\n    title: '<PERSON><PERSON><PERSON> mẫu',\n    icon: 'tone-save',\n  },\n];\n\nexport const actionsPending: ActionProps[] = [\n  {\n    type: TRANSFER_RESULT_ACTION.TRANSFER_SHARE,\n    title: 'Share',\n    icon: 'tone-share',\n  },\n  {\n    type: TRANSFER_RESULT_ACTION.TRANSER_SAVE_TEMPLATE,\n    title: 'Lưu mẫu',\n    icon: 'tone-save',\n  },\n  {\n    type: TRANSFER_RESULT_ACTION.TRANSFER_MANAGER,\n    title: 'Quản lý lệnh chuyển tiền',\n    icon: 'tone-folder',\n  },\n];\n\nexport const actionsFail: ActionProps[] = [\n  {\n    type: TRANSFER_RESULT_ACTION.TRANSFER_SUPPORT,\n    title: 'Trung tâm hỗ trợ',\n    icon: 'tone-support-headphone',\n  },\n];\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAeE;IAAAA,cAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,cAAA;AAAAA,cAAA,GAAAE,CAAA;;;;;;;AAdF,IAAAC,WAAA;AAAA;AAAA,CAAAH,cAAA,GAAAE,CAAA,OAAAE,OAAA;AAAA;AAAAJ,cAAA,GAAAE,CAAA;AAGaG,OAAA,CAAAC,cAAc,GAAkB,CAC3C;EACEC,IAAI,EAAEJ,WAAA,CAAAK,sBAAsB,CAACC,cAAc;EAC3CC,KAAK,EAAE,SAAS;EAChBC,IAAI,EAAE;CACP,EACD;EACEJ,IAAI,EAAEJ,WAAA,CAAAK,sBAAsB,CAACI,qBAAqB;EAClDF,KAAK,EAAE,oBAAoB;EAC3BC,IAAI,EAAE;CACP,EACD;EACEJ,IAAI,EAAEJ,WAAA,CAAAK,sBAAsB,CAACK,wBAAwB;EACrDH,KAAK,EAAE,aAAa;EACpBC,IAAI,EAAE;CACP,CACF;AAAA;AAAAX,cAAA,GAAAE,CAAA;AAEYG,OAAA,CAAAS,0BAA0B,GAAkB,CACvD;EACEP,IAAI,EAAEJ,WAAA,CAAAK,sBAAsB,CAACC,cAAc;EAC3CC,KAAK,EAAE,SAAS;EAChBC,IAAI,EAAE;CACP,EACD;EACEJ,IAAI,EAAEJ,WAAA,CAAAK,sBAAsB,CAACI,qBAAqB;EAClDF,KAAK,EAAE,SAAS;EAChBC,IAAI,EAAE;CACP,CACF;AAAA;AAAAX,cAAA,GAAAE,CAAA;AAEYG,OAAA,CAAAU,cAAc,GAAkB,CAC3C;EACER,IAAI,EAAEJ,WAAA,CAAAK,sBAAsB,CAACC,cAAc;EAC3CC,KAAK,EAAE,OAAO;EACdC,IAAI,EAAE;CACP,EACD;EACEJ,IAAI,EAAEJ,WAAA,CAAAK,sBAAsB,CAACI,qBAAqB;EAClDF,KAAK,EAAE,SAAS;EAChBC,IAAI,EAAE;CACP,EACD;EACEJ,IAAI,EAAEJ,WAAA,CAAAK,sBAAsB,CAACQ,gBAAgB;EAC7CN,KAAK,EAAE,0BAA0B;EACjCC,IAAI,EAAE;CACP,CACF;AAAA;AAAAX,cAAA,GAAAE,CAAA;AAEYG,OAAA,CAAAY,WAAW,GAAkB,CACxC;EACEV,IAAI,EAAEJ,WAAA,CAAAK,sBAAsB,CAACU,gBAAgB;EAC7CR,KAAK,EAAE,kBAAkB;EACzBC,IAAI,EAAE;CACP,CACF", "ignoreList": []}