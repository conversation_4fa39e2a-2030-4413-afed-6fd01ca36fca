7159e4ce8cd4f2fadffe52f18587da2f
'use strict';

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.EasingNameSymbol = exports.Easing = void 0;
var _slicedToArray2 = _interopRequireDefault(require("@babel/runtime/helpers/slicedToArray"));
var _Bezier = require("./Bezier.js");
function linear(t) {
  'worklet';

  return t;
}
function ease(t) {
  'worklet';

  return (0, _Bezier.Bezier)(0.42, 0, 1, 1)(t);
}
function quad(t) {
  'worklet';

  return t * t;
}
function cubic(t) {
  'worklet';

  return t * t * t;
}
function poly(n) {
  'worklet';

  return function (t) {
    'worklet';

    return Math.pow(t, n);
  };
}
function sin(t) {
  'worklet';

  return 1 - Math.cos(t * Math.PI / 2);
}
function circle(t) {
  'worklet';

  return 1 - Math.sqrt(1 - t * t);
}
function exp(t) {
  'worklet';

  return Math.pow(2, 10 * (t - 1));
}
function elastic() {
  'worklet';

  var bounciness = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 1;
  var p = bounciness * Math.PI;
  return function (t) {
    'worklet';

    return 1 - Math.pow(Math.cos(t * Math.PI / 2), 3) * Math.cos(t * p);
  };
}
function back() {
  'worklet';

  var s = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 1.70158;
  return function (t) {
    'worklet';

    return t * t * ((s + 1) * t - s);
  };
}
function bounce(t) {
  'worklet';

  if (t < 1 / 2.75) {
    return 7.5625 * t * t;
  }
  if (t < 2 / 2.75) {
    var _t = t - 1.5 / 2.75;
    return 7.5625 * _t * _t + 0.75;
  }
  if (t < 2.5 / 2.75) {
    var _t2 = t - 2.25 / 2.75;
    return 7.5625 * _t2 * _t2 + 0.9375;
  }
  var t2 = t - 2.625 / 2.75;
  return 7.5625 * t2 * t2 + 0.984375;
}
function bezier(x1, y1, x2, y2) {
  'worklet';

  return {
    factory: function factory() {
      'worklet';

      return (0, _Bezier.Bezier)(x1, y1, x2, y2);
    }
  };
}
function bezierFn(x1, y1, x2, y2) {
  'worklet';

  return (0, _Bezier.Bezier)(x1, y1, x2, y2);
}
function in_(easing) {
  'worklet';

  return easing;
}
function out(easing) {
  'worklet';

  return function (t) {
    'worklet';

    return 1 - easing(1 - t);
  };
}
function inOut(easing) {
  'worklet';

  return function (t) {
    'worklet';

    if (t < 0.5) {
      return easing(t * 2) / 2;
    }
    return 1 - easing((1 - t) * 2) / 2;
  };
}
function steps() {
  'worklet';

  var n = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 10;
  var roundToNextStep = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : true;
  return function (t) {
    'worklet';

    var value = Math.min(Math.max(t, 0), 1) * n;
    if (roundToNextStep) {
      return Math.ceil(value) / n;
    }
    return Math.floor(value) / n;
  };
}
var EasingObject = {
  linear: linear,
  ease: ease,
  quad: quad,
  cubic: cubic,
  poly: poly,
  sin: sin,
  circle: circle,
  exp: exp,
  elastic: elastic,
  back: back,
  bounce: bounce,
  bezier: bezier,
  bezierFn: bezierFn,
  steps: steps,
  in: in_,
  out: out,
  inOut: inOut
};
var EasingNameSymbol = exports.EasingNameSymbol = Symbol('easingName');
for (var _ref of Object.entries(EasingObject)) {
  var _ref2 = (0, _slicedToArray2.default)(_ref, 2);
  var easingName = _ref2[0];
  var easing = _ref2[1];
  Object.defineProperty(easing, EasingNameSymbol, {
    value: easingName,
    configurable: false,
    enumerable: false,
    writable: false
  });
}
var Easing = exports.Easing = EasingObject;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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