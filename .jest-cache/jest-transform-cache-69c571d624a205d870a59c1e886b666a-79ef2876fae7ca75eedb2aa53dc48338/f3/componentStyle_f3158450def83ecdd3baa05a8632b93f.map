{"version": 3, "names": ["Object", "defineProperty", "exports", "value", "makeElementVisible", "setElementPosition", "snapshots", "_index", "require", "WeakMap", "element", "delay", "_updatePropsJS", "visibility", "setTimeout", "fixElementPosition", "parent", "snapshot", "parentRect", "getBoundingClientRect", "parentBorderTopValue", "parseInt", "getComputedStyle", "borderTopWidth", "parentBorderLeftValue", "borderLeftWidth", "dummyRect", "top", "style", "left", "transform", "position", "width", "height", "margin", "parentElement"], "sources": ["../../../../src/layoutReanimation/web/componentStyle.ts"], "sourcesContent": [null], "mappings": "AAAA,YAAY;;AAAAA,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,KAAA;AAAA;AAAAD,OAAA,CAAAE,kBAAA,GAAAA,kBAAA;AAAAF,OAAA,CAAAG,kBAAA,GAAAA,kBAAA;AAAAH,OAAA,CAAAI,SAAA;AAEZ,IAAAC,MAAA,GAAAC,OAAA;AAgBO,IAAMF,SAAS,GAAAJ,OAAA,CAAAI,SAAA,GAAG,IAAIG,OAAO,CAAkC,CAAC;AAEhE,SAASL,kBAAkBA,CAACM,OAAoB,EAAEC,KAAa,EAAE;EACtE,IAAIA,KAAK,KAAK,CAAC,EAAE;IACf,IAAAC,qBAAc,EAAC;MAAEC,UAAU,EAAE;IAAU,CAAC,EAAEH,OAAgC,CAAC;EAC7E,CAAC,MAAM;IACLI,UAAU,CAAC,YAAM;MACf,IAAAF,qBAAc,EACZ;QAAEC,UAAU,EAAE;MAAU,CAAC,EACzBH,OACF,CAAC;IACH,CAAC,EAAEC,KAAK,GAAG,IAAI,CAAC;EAClB;AACF;AAEA,SAASI,kBAAkBA,CACzBL,OAAoB,EACpBM,MAAmB,EACnBC,QAA4B,EAC5B;EACA,IAAMC,UAAU,GAAGF,MAAM,CAACG,qBAAqB,CAAC,CAAC;EAEjD,IAAMC,oBAAoB,GAAGC,QAAQ,CACnCC,gBAAgB,CAACN,MAAM,CAAC,CAACO,cAC3B,CAAC;EAED,IAAMC,qBAAqB,GAAGH,QAAQ,CACpCC,gBAAgB,CAACN,MAAM,CAAC,CAACS,eAC3B,CAAC;EAED,IAAMC,SAAS,GAAGhB,OAAO,CAACS,qBAAqB,CAAC,CAAC;EAIjD,IAAIO,SAAS,CAACC,GAAG,KAAKV,QAAQ,CAACU,GAAG,EAAE;IAClCjB,OAAO,CAACkB,KAAK,CAACD,GAAG,GAAG,GAClBV,QAAQ,CAACU,GAAG,GAAGT,UAAU,CAACS,GAAG,GAAGP,oBAAoB,IAClD;EACN;EAEA,IAAIM,SAAS,CAACG,IAAI,KAAKZ,QAAQ,CAACY,IAAI,EAAE;IACpCnB,OAAO,CAACkB,KAAK,CAACC,IAAI,GAAG,GACnBZ,QAAQ,CAACY,IAAI,GAAGX,UAAU,CAACW,IAAI,GAAGL,qBAAqB,IACrD;EACN;AACF;AAEO,SAASnB,kBAAkBA,CAChCK,OAAoB,EACpBO,QAA4B,EAC5B;EACAP,OAAO,CAACkB,KAAK,CAACE,SAAS,GAAG,EAAE;EAC5BpB,OAAO,CAACkB,KAAK,CAACG,QAAQ,GAAG,UAAU;EACnCrB,OAAO,CAACkB,KAAK,CAACD,GAAG,GAAG,GAAGV,QAAQ,CAACU,GAAG,IAAI;EACvCjB,OAAO,CAACkB,KAAK,CAACC,IAAI,GAAG,GAAGZ,QAAQ,CAACY,IAAI,IAAI;EACzCnB,OAAO,CAACkB,KAAK,CAACI,KAAK,GAAG,GAAGf,QAAQ,CAACe,KAAK,IAAI;EAC3CtB,OAAO,CAACkB,KAAK,CAACK,MAAM,GAAG,GAAGhB,QAAQ,CAACgB,MAAM,IAAI;EAC7CvB,OAAO,CAACkB,KAAK,CAACM,MAAM,GAAG,KAAK;EAE5B,IAAIxB,OAAO,CAACyB,aAAa,EAAE;IACzBpB,kBAAkB,CAACL,OAAO,EAAEA,OAAO,CAACyB,aAAa,EAAElB,QAAQ,CAAC;EAC9D;AACF", "ignoreList": []}