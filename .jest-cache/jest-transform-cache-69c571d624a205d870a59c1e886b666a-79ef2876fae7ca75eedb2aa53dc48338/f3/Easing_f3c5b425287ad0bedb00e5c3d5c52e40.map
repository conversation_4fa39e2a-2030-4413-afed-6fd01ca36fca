{"version": 3, "names": ["_interopRequireDefault", "require", "Object", "defineProperty", "exports", "value", "EasingNameSymbol", "Easing", "_slicedToArray2", "_Bezier", "linear", "t", "ease", "<PERSON><PERSON>", "quad", "cubic", "poly", "n", "Math", "pow", "sin", "cos", "PI", "circle", "sqrt", "exp", "elastic", "bounciness", "arguments", "length", "undefined", "p", "back", "s", "bounce", "t2", "bezier", "x1", "y1", "x2", "y2", "factory", "bezierFn", "in_", "easing", "out", "inOut", "steps", "roundToNextStep", "min", "max", "ceil", "floor", "EasingObject", "in", "Symbol", "_ref", "entries", "_ref2", "default", "easingName", "configurable", "enumerable", "writable"], "sources": ["../../src/Easing.ts"], "sourcesContent": [null], "mappings": "AAAA,YAAY;;AAAA,IAAAA,sBAAA,GAAAC,OAAA;AAAAC,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,KAAA;AAAA;AAAAD,OAAA,CAAAE,gBAAA,GAAAF,OAAA,CAAAG,MAAA;AAAA,IAAAC,eAAA,GAAAR,sBAAA,CAAAC,OAAA;AACZ,IAAAQ,OAAA,GAAAR,OAAA;AA8DA,SAASS,MAAMA,CAACC,CAAS,EAAU;EACjC,SAAS;;EACT,OAAOA,CAAC;AACV;AAQA,SAASC,IAAIA,CAACD,CAAS,EAAU;EAC/B,SAAS;;EACT,OAAO,IAAAE,cAAM,EAAC,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAACF,CAAC,CAAC;AACjC;AAQA,SAASG,IAAIA,CAACH,CAAS,EAAU;EAC/B,SAAS;;EACT,OAAOA,CAAC,GAAGA,CAAC;AACd;AAQA,SAASI,KAAKA,CAACJ,CAAS,EAAU;EAChC,SAAS;;EACT,OAAOA,CAAC,GAAGA,CAAC,GAAGA,CAAC;AAClB;AAOA,SAASK,IAAIA,CAACC,CAAS,EAAkB;EACvC,SAAS;;EACT,OAAQ,UAAAN,CAAC,EAAK;IACZ,SAAS;;IACT,OAAOO,IAAI,CAACC,GAAG,CAACR,CAAC,EAAEM,CAAC,CAAC;EACvB,CAAC;AACH;AAOA,SAASG,GAAGA,CAACT,CAAS,EAAU;EAC9B,SAAS;;EACT,OAAO,CAAC,GAAGO,IAAI,CAACG,GAAG,CAAEV,CAAC,GAAGO,IAAI,CAACI,EAAE,GAAI,CAAC,CAAC;AACxC;AAOA,SAASC,MAAMA,CAACZ,CAAS,EAAU;EACjC,SAAS;;EACT,OAAO,CAAC,GAAGO,IAAI,CAACM,IAAI,CAAC,CAAC,GAAGb,CAAC,GAAGA,CAAC,CAAC;AACjC;AAOA,SAASc,GAAGA,CAACd,CAAS,EAAU;EAC9B,SAAS;;EACT,OAAOO,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,EAAE,IAAIR,CAAC,GAAG,CAAC,CAAC,CAAC;AAClC;AAWA,SAASe,OAAOA,CAAA,EAAiC;EAC/C,SAAS;;EAAA,IADMC,UAAU,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC;EAE7B,IAAMG,CAAC,GAAGJ,UAAU,GAAGT,IAAI,CAACI,EAAE;EAC9B,OAAQ,UAAAX,CAAC,EAAK;IACZ,SAAS;;IACT,OAAO,CAAC,GAAGO,IAAI,CAACC,GAAG,CAACD,IAAI,CAACG,GAAG,CAAEV,CAAC,GAAGO,IAAI,CAACI,EAAE,GAAI,CAAC,CAAC,EAAE,CAAC,CAAC,GAAGJ,IAAI,CAACG,GAAG,CAACV,CAAC,GAAGoB,CAAC,CAAC;EACvE,CAAC;AACH;AAUA,SAASC,IAAIA,CAAA,EAAqC;EAChD,SAAS;;EAAA,IADGC,CAAC,GAAAL,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,OAAO;EAEvB,OAAQ,UAAAjB,CAAC,EAAK;IACZ,SAAS;;IACT,OAAOA,CAAC,GAAGA,CAAC,IAAI,CAACsB,CAAC,GAAG,CAAC,IAAItB,CAAC,GAAGsB,CAAC,CAAC;EAClC,CAAC;AACH;AAOA,SAASC,MAAMA,CAACvB,CAAS,EAAU;EACjC,SAAS;;EACT,IAAIA,CAAC,GAAG,CAAC,GAAG,IAAI,EAAE;IAChB,OAAO,MAAM,GAAGA,CAAC,GAAGA,CAAC;EACvB;EAEA,IAAIA,CAAC,GAAG,CAAC,GAAG,IAAI,EAAE;IAChB,IAAMwB,EAAE,GAAGxB,CAAC,GAAG,GAAG,GAAG,IAAI;IACzB,OAAO,MAAM,GAAGwB,EAAE,GAAGA,EAAE,GAAG,IAAI;EAChC;EAEA,IAAIxB,CAAC,GAAG,GAAG,GAAG,IAAI,EAAE;IAClB,IAAMwB,GAAE,GAAGxB,CAAC,GAAG,IAAI,GAAG,IAAI;IAC1B,OAAO,MAAM,GAAGwB,GAAE,GAAGA,GAAE,GAAG,MAAM;EAClC;EAEA,IAAMA,EAAE,GAAGxB,CAAC,GAAG,KAAK,GAAG,IAAI;EAC3B,OAAO,MAAM,GAAGwB,EAAE,GAAGA,EAAE,GAAG,QAAQ;AACpC;AASA,SAASC,MAAMA,CACbC,EAAU,EACVC,EAAU,EACVC,EAAU,EACVC,EAAU,EACgC;EAC1C,SAAS;;EACT,OAAO;IACLC,OAAO,EAAE,SAATA,OAAOA,CAAA,EAAQ;MACb,SAAS;;MACT,OAAO,IAAA5B,cAAM,EAACwB,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,CAAC;IAC/B;EACF,CAAC;AACH;AAEA,SAASE,QAAQA,CACfL,EAAU,EACVC,EAAU,EACVC,EAAU,EACVC,EAAU,EACa;EACvB,SAAS;;EACT,OAAO,IAAA3B,cAAM,EAACwB,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,CAAC;AAC/B;AAGA,SAASG,GAAGA,CAACC,MAAsB,EAAkB;EACnD,SAAS;;EACT,OAAOA,MAAM;AACf;AAGA,SAASC,GAAGA,CAACD,MAAsB,EAAkB;EACnD,SAAS;;EACT,OAAQ,UAAAjC,CAAC,EAAK;IACZ,SAAS;;IACT,OAAO,CAAC,GAAGiC,MAAM,CAAC,CAAC,GAAGjC,CAAC,CAAC;EAC1B,CAAC;AACH;AAMA,SAASmC,KAAKA,CAACF,MAAsB,EAAkB;EACrD,SAAS;;EACT,OAAQ,UAAAjC,CAAC,EAAK;IACZ,SAAS;;IACT,IAAIA,CAAC,GAAG,GAAG,EAAE;MACX,OAAOiC,MAAM,CAACjC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;IAC1B;IACA,OAAO,CAAC,GAAGiC,MAAM,CAAC,CAAC,CAAC,GAAGjC,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC;EACpC,CAAC;AACH;AASA,SAASoC,KAAKA,CAAA,EAAiD;EAC7D,SAAS;;EAAA,IADI9B,CAAC,GAAAW,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,EAAE;EAAA,IAAEoB,eAAe,GAAApB,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,IAAI;EAE3C,OAAQ,UAAAjB,CAAC,EAAK;IACZ,SAAS;;IACT,IAAMN,KAAK,GAAGa,IAAI,CAAC+B,GAAG,CAAC/B,IAAI,CAACgC,GAAG,CAACvC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,GAAGM,CAAC;IAC7C,IAAI+B,eAAe,EAAE;MACnB,OAAO9B,IAAI,CAACiC,IAAI,CAAC9C,KAAK,CAAC,GAAGY,CAAC;IAC7B;IACA,OAAOC,IAAI,CAACkC,KAAK,CAAC/C,KAAK,CAAC,GAAGY,CAAC;EAC9B,CAAC;AACH;AAEA,IAAMoC,YAAY,GAAG;EACnB3C,MAAM,EAANA,MAAM;EACNE,IAAI,EAAJA,IAAI;EACJE,IAAI,EAAJA,IAAI;EACJC,KAAK,EAALA,KAAK;EACLC,IAAI,EAAJA,IAAI;EACJI,GAAG,EAAHA,GAAG;EACHG,MAAM,EAANA,MAAM;EACNE,GAAG,EAAHA,GAAG;EACHC,OAAO,EAAPA,OAAO;EACPM,IAAI,EAAJA,IAAI;EACJE,MAAM,EAANA,MAAM;EACNE,MAAM,EAANA,MAAM;EACNM,QAAQ,EAARA,QAAQ;EACRK,KAAK,EAALA,KAAK;EACLO,EAAE,EAAEX,GAAG;EACPE,GAAG,EAAHA,GAAG;EACHC,KAAA,EAAAA;AACF,CAAC;AAEM,IAAMxC,gBAAgB,GAAAF,OAAA,CAAAE,gBAAA,GAAGiD,MAAM,CAAC,YAAY,CAAC;AAEpD,SAAAC,IAAA,IAAmCtD,MAAM,CAACuD,OAAO,CAACJ,YAAY,CAAC,EAAE;EAAA,IAAAK,KAAA,OAAAlD,eAAA,CAAAmD,OAAA,EAAAH,IAAA;EAAA,IAArDI,UAAU,GAAAF,KAAA;EAAA,IAAEd,MAAM,GAAAc,KAAA;EAC5BxD,MAAM,CAACC,cAAc,CAACyC,MAAM,EAAEtC,gBAAgB,EAAE;IAC9CD,KAAK,EAAEuD,UAAU;IACjBC,YAAY,EAAE,KAAK;IACnBC,UAAU,EAAE,KAAK;IACjBC,QAAQ,EAAE;EACZ,CAAC,CAAC;AACJ;AAEO,IAAMxD,MAAM,GAAAH,OAAA,CAAAG,MAAA,GAAG8C,YAAY", "ignoreList": []}