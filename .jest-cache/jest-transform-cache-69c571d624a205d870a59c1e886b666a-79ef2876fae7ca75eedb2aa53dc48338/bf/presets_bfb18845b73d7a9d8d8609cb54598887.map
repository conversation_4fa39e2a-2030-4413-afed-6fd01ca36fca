{"version": 3, "names": ["Object", "defineProperty", "exports", "value", "ScreenTransition", "SwipeRight", "topScreenStyle", "event", "transform", "translateX", "translationX", "belowTopScreenStyle", "screenSize", "width", "SwipeLeft", "SwipeDown", "translateY", "translationY", "height", "SwipeUp", "TwoDimensional", "_screenSize", "_event", "Horizontal", "Vertical", "SwipeRightFade", "opacity", "Math", "abs"], "sources": ["../../../src/screenTransition/presets.ts"], "sourcesContent": [null], "mappings": "AAAA,YAAY;;AAAAA,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,KAAA;AAAA;AAAAD,OAAA,CAAAE,gBAAA;AAIZ,IAAMC,UAAoC,GAAG;EAC3CC,cAAc,EAAG,SAAjBA,cAAcA,CAAGC,KAAK,EAAK;IACzB,SAAS;;IACT,OAAO;MACLC,SAAS,EAAE,CAAC;QAAEC,UAAU,EAAEF,KAAK,CAACG;MAAa,CAAC;IAChD,CAAC;EACH,CAAC;EACDC,mBAAmB,EAAE,SAArBA,mBAAmBA,CAAGJ,KAAK,EAAEK,UAAU,EAAK;IAC1C,SAAS;;IACT,OAAO;MACLJ,SAAS,EAAE,CACT;QAAEC,UAAU,EAAE,CAACF,KAAK,CAACG,YAAY,GAAGE,UAAU,CAACC,KAAK,IAAI;MAAI,CAAC;IAEjE,CAAC;EACH;AACF,CAAC;AAED,IAAMC,SAAmC,GAAG;EAC1CR,cAAc,EAAG,SAAjBA,cAAcA,CAAGC,KAAK,EAAK;IACzB,SAAS;;IACT,OAAO;MACLC,SAAS,EAAE,CAAC;QAAEC,UAAU,EAAEF,KAAK,CAACG;MAAa,CAAC;IAChD,CAAC;EACH,CAAC;EACDC,mBAAmB,EAAE,SAArBA,mBAAmBA,CAAGJ,KAAK,EAAEK,UAAU,EAAK;IAC1C,SAAS;;IACT,OAAO;MACLJ,SAAS,EAAE,CACT;QAAEC,UAAU,EAAE,CAACF,KAAK,CAACG,YAAY,GAAGE,UAAU,CAACC,KAAK,IAAI;MAAI,CAAC;IAEjE,CAAC;EACH;AACF,CAAC;AAED,IAAME,SAAmC,GAAG;EAC1CT,cAAc,EAAG,SAAjBA,cAAcA,CAAGC,KAAK,EAAK;IACzB,SAAS;;IACT,OAAO;MACLC,SAAS,EAAE,CAAC;QAAEQ,UAAU,EAAET,KAAK,CAACU;MAAa,CAAC;IAChD,CAAC;EACH,CAAC;EACDN,mBAAmB,EAAE,SAArBA,mBAAmBA,CAAGJ,KAAK,EAAEK,UAAU,EAAK;IAC1C,SAAS;;IACT,OAAO;MACLJ,SAAS,EAAE,CACT;QAAEQ,UAAU,EAAE,CAACT,KAAK,CAACU,YAAY,GAAGL,UAAU,CAACM,MAAM,IAAI;MAAI,CAAC;IAElE,CAAC;EACH;AACF,CAAC;AAED,IAAMC,OAAiC,GAAG;EACxCb,cAAc,EAAG,SAAjBA,cAAcA,CAAGC,KAAK,EAAK;IACzB,SAAS;;IACT,OAAO;MACLC,SAAS,EAAE,CAAC;QAAEQ,UAAU,EAAET,KAAK,CAACU;MAAa,CAAC;IAChD,CAAC;EACH,CAAC;EACDN,mBAAmB,EAAE,SAArBA,mBAAmBA,CAAGJ,KAAK,EAAEK,UAAU,EAAK;IAC1C,SAAS;;IACT,OAAO;MACLJ,SAAS,EAAE,CACT;QAAEQ,UAAU,EAAE,CAACT,KAAK,CAACU,YAAY,GAAGL,UAAU,CAACM,MAAM,IAAI;MAAI,CAAC;IAElE,CAAC;EACH;AACF,CAAC;AAED,IAAME,cAAwC,GAAG;EAC/Cd,cAAc,EAAE,SAAhBA,cAAcA,CAAGC,KAAK,EAAEc,WAAW,EAAK;IACtC,SAAS;;IACT,OAAO;MACLb,SAAS,EAAE,CACT;QAAEC,UAAU,EAAEF,KAAK,CAACG;MAAa,CAAC,EAClC;QAAEM,UAAU,EAAET,KAAK,CAACU;MAAa,CAAC;IAEtC,CAAC;EACH,CAAC;EACDN,mBAAmB,EAAE,SAArBA,mBAAmBA,CAAGW,MAAM,EAAED,WAAW,EAAK;IAC5C,SAAS;;IACT,OAAO,CAAC,CAAC;EACX;AACF,CAAC;AAED,IAAME,UAAoC,GAAG;EAC3CjB,cAAc,EAAE,SAAhBA,cAAcA,CAAGC,KAAK,EAAEc,WAAW,EAAK;IACtC,SAAS;;IACT,OAAO;MACLb,SAAS,EAAE,CAAC;QAAEC,UAAU,EAAEF,KAAK,CAACG;MAAa,CAAC;IAChD,CAAC;EACH,CAAC;EACDC,mBAAmB,EAAE,SAArBA,mBAAmBA,CAAGW,MAAM,EAAED,WAAW,EAAK;IAC5C,SAAS;;IACT,OAAO,CAAC,CAAC;EACX;AACF,CAAC;AAED,IAAMG,QAAkC,GAAG;EACzClB,cAAc,EAAE,SAAhBA,cAAcA,CAAGC,KAAK,EAAEc,WAAW,EAAK;IACtC,SAAS;;IACT,OAAO;MACLb,SAAS,EAAE,CAAC;QAAEQ,UAAU,EAAET,KAAK,CAACU;MAAa,CAAC;IAChD,CAAC;EACH,CAAC;EACDN,mBAAmB,EAAE,SAArBA,mBAAmBA,CAAGW,MAAM,EAAED,WAAW,EAAK;IAC5C,SAAS;;IACT,OAAO,CAAC,CAAC;EACX;AACF,CAAC;AAED,IAAMI,cAAwC,GAAG;EAC/CnB,cAAc,EAAE,SAAhBA,cAAcA,CAAGC,KAAK,EAAEK,UAAU,EAAK;IACrC,SAAS;;IACT,OAAO;MACLc,OAAO,EAAE,CAAC,GAAGC,IAAI,CAACC,GAAG,CAACrB,KAAK,CAACG,YAAY,GAAGE,UAAU,CAACC,KAAK;IAC7D,CAAC;EACH,CAAC;EACDF,mBAAmB,EAAE,SAArBA,mBAAmBA,CAAGW,MAAM,EAAED,WAAW,EAAK;IAC5C,SAAS;;IACT,OAAO,CAAC,CAAC;EACX;AACF,CAAC;AAEM,IAAMjB,gBAAgB,GAAAF,OAAA,CAAAE,gBAAA,GAAG;EAC9BC,UAAU,EAAVA,UAAU;EACVS,SAAS,EAATA,SAAS;EACTC,SAAS,EAATA,SAAS;EACTI,OAAO,EAAPA,OAAO;EACPI,UAAU,EAAVA,UAAU;EACVC,QAAQ,EAARA,QAAQ;EACRJ,cAAc,EAAdA,cAAc;EACdK,cAAA,EAAAA;AACF,CAAC", "ignoreList": []}