{"version": 3, "names": ["cov_1n9aohmgt1", "actualCoverage", "s", "msb_shared_component_1", "require", "exports", "makeStyle", "createMSBStyleSheet", "_ref", "f", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Typography", "Shadow", "accountInfo", "Object", "assign", "backgroundColor", "ColorAlias", "<PERSON><PERSON><PERSON><PERSON>", "borderRadius", "SpacingXSmall", "paddingHorizontal", "SpacingSmall", "paddingVertical", "center", "amountInput", "b", "base_medium", "marginTop", "btnContinue", "marginHorizontal", "container", "flex", "contentContainerScrollView", "padding", "contentContainer", "Radius3", "SpacingXLarge", "contentLabel", "color", "ColorLabelCaption", "TextMain", "marginRight", "Spacing4xSmall", "paddingBottom", "flexDirectionRow", "flexDirection", "imageBackground", "resizeMode", "scrollViewContentContainer", "Spacing3xLarge"], "sources": ["/Users/<USER>/projects/react-native/payment-module/src/presentation/payment-info/style.ts"], "sourcesContent": ["import {ColorAlias, ColorLabelCaption, createMSBStyleSheet} from 'msb-shared-component';\n\nimport DimensionUtils from '../../utils/DimensionUtils';\n\nexport const makeStyle = createMSBStyleSheet(({Size<PERSON>lia<PERSON>, Typography, Shadow}) => {\n  return {\n    accountInfo: {\n      backgroundColor: ColorAlias.BackgroundWhite,\n      borderRadius: SizeAlias.SpacingXSmall,\n      paddingHorizontal: SizeAlias.SpacingSmall,\n      paddingVertical: SizeAlias.SpacingXSmall,\n      ...Shadow.center,\n    },\n    amountInput: {\n      ...Typography?.base_medium,\n      marginTop: SizeAlias.SpacingSmall,\n    },\n    btnContinue: {\n      marginHorizontal: SizeAlias.SpacingSmall,\n    },\n    container: {\n      flex: 1,\n    },\n\n    contentContainerScrollView: {\n      padding: SizeAlias.SpacingSmall,\n    },\n\n    contentContainer: {\n      backgroundColor: ColorAlias.BackgroundWhite,\n      borderRadius: SizeAlias.Radius3,\n      marginTop: SizeAlias.SpacingXLarge,\n      padding: SizeAlias.SpacingSmall,\n      ...Shadow.center,\n    },\n    contentLabel: {\n      color: ColorLabelCaption.TextMain,\n      marginRight: SizeAlias.Spacing4xSmall,\n      paddingBottom: SizeAlias.Spacing4xSmall,\n    },\n    flexDirectionRow: {\n      flexDirection: 'row',\n    },\n    imageBackground: {\n      flex: 1,\n      resizeMode: 'stretch',\n    },\n    scrollViewContentContainer: {\n      paddingBottom: SizeAlias.Spacing3xLarge,\n    },\n  };\n});\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IASM;IAAAA,cAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,cAAA;AAAAA,cAAA,GAAAE,CAAA;;;;;;;AATN,IAAAC,sBAAA;AAAA;AAAA,CAAAH,cAAA,GAAAE,CAAA,OAAAE,OAAA;AAAA;AAAAJ,cAAA,GAAAE,CAAA;AAIaG,OAAA,CAAAC,SAAS,GAAG,IAAAH,sBAAA,CAAAI,mBAAmB,EAAC,UAAAC,IAAA,EAAoC;EAAA;EAAAR,cAAA,GAAAS,CAAA;EAAA,IAAlCC,SAAS;IAAA;IAAA,CAAAV,cAAA,GAAAE,CAAA,OAAAM,IAAA,CAATE,SAAS;IAAEC,UAAU;IAAA;IAAA,CAAAX,cAAA,GAAAE,CAAA,OAAAM,IAAA,CAAVG,UAAU;IAAEC,MAAM;IAAA;IAAA,CAAAZ,cAAA,GAAAE,CAAA,OAAAM,IAAA,CAANI,MAAM;EAAA;EAAAZ,cAAA,GAAAE,CAAA;EAC1E,OAAO;IACLW,WAAW,EAAAC,MAAA,CAAAC,MAAA;MACTC,eAAe,EAAEb,sBAAA,CAAAc,UAAU,CAACC,eAAe;MAC3CC,YAAY,EAAET,SAAS,CAACU,aAAa;MACrCC,iBAAiB,EAAEX,SAAS,CAACY,YAAY;MACzCC,eAAe,EAAEb,SAAS,CAACU;IAAa,GACrCR,MAAM,CAACY,MAAM,CACjB;IACDC,WAAW,EAAAX,MAAA,CAAAC,MAAA,KACNJ,UAAU;IAAA;IAAA,CAAAX,cAAA,GAAA0B,CAAA;IAAA;IAAA,CAAA1B,cAAA,GAAA0B,CAAA,UAAVf,UAAU,CAAEgB,WAAW;MAC1BC,SAAS,EAAElB,SAAS,CAACY;IAAY,EAClC;IACDO,WAAW,EAAE;MACXC,gBAAgB,EAAEpB,SAAS,CAACY;KAC7B;IACDS,SAAS,EAAE;MACTC,IAAI,EAAE;KACP;IAEDC,0BAA0B,EAAE;MAC1BC,OAAO,EAAExB,SAAS,CAACY;KACpB;IAEDa,gBAAgB,EAAArB,MAAA,CAAAC,MAAA;MACdC,eAAe,EAAEb,sBAAA,CAAAc,UAAU,CAACC,eAAe;MAC3CC,YAAY,EAAET,SAAS,CAAC0B,OAAO;MAC/BR,SAAS,EAAElB,SAAS,CAAC2B,aAAa;MAClCH,OAAO,EAAExB,SAAS,CAACY;IAAY,GAC5BV,MAAM,CAACY,MAAM,CACjB;IACDc,YAAY,EAAE;MACZC,KAAK,EAAEpC,sBAAA,CAAAqC,iBAAiB,CAACC,QAAQ;MACjCC,WAAW,EAAEhC,SAAS,CAACiC,cAAc;MACrCC,aAAa,EAAElC,SAAS,CAACiC;KAC1B;IACDE,gBAAgB,EAAE;MAChBC,aAAa,EAAE;KAChB;IACDC,eAAe,EAAE;MACff,IAAI,EAAE,CAAC;MACPgB,UAAU,EAAE;KACb;IACDC,0BAA0B,EAAE;MAC1BL,aAAa,EAAElC,SAAS,CAACwC;;GAE5B;AACH,CAAC,CAAC", "ignoreList": []}