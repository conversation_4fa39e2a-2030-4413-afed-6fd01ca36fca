{"version": 3, "names": ["_interopRequireDefault", "require", "Object", "defineProperty", "exports", "value", "SensorContainer", "_classCallCheck2", "_createClass2", "_Sensor", "default", "nativeSensors", "Map", "key", "getSensorId", "sensorType", "config", "iosReferenceFrame", "Number", "adjustToInterfaceOrientation", "initializeSensor", "sensorId", "has", "newSensor", "Sensor", "set", "sensor", "get", "getSharedValue", "registerSensor", "handler", "isAvailable", "isRunning", "register", "listenersNumber", "unregisterSensor", "unregister"], "sources": ["../../src/SensorContainer.ts"], "sourcesContent": [null], "mappings": "AAAA,YAAY;;AAAA,IAAAA,sBAAA,GAAAC,OAAA;AAAAC,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,KAAA;AAAA;AAAAD,OAAA,CAAAE,eAAA;AAAA,IAAAC,gBAAA,GAAAP,sBAAA,CAAAC,OAAA;AAAA,IAAAO,aAAA,GAAAR,sBAAA,CAAAC,OAAA;AASZ,IAAAQ,OAAA,GAAAT,sBAAA,CAAAC,OAAA;AAA6B,IAEhBK,eAAe,GAAAF,OAAA,CAAAE,eAAA;EAAA,SAAAA,gBAAA;IAAA,IAAAC,gBAAA,CAAAG,OAAA,QAAAJ,eAAA;IAAA,KAClBK,aAAa,GAAwB,IAAIC,GAAG,CAAC,CAAC;EAAA;EAAA,WAAAJ,aAAA,CAAAE,OAAA,EAAAJ,eAAA;IAAAO,GAAA;IAAAR,KAAA,EAEtD,SAAAS,WAAWA,CAACC,UAAsB,EAAEC,MAAoB,EAAE;MACxD,OACED,UAAU,GAAG,GAAG,GAChBC,MAAM,CAACC,iBAAiB,GAAG,EAAE,GAC7BC,MAAM,CAACF,MAAM,CAACG,4BAA4B,CAAC;IAE/C;EAAA;IAAAN,GAAA;IAAAR,KAAA,EAEA,SAAAe,gBAAgBA,CACdL,UAAsB,EACtBC,MAAoB,EACkB;MACtC,IAAMK,QAAQ,GAAG,IAAI,CAACP,WAAW,CAACC,UAAU,EAAEC,MAAM,CAAC;MAErD,IAAI,CAAC,IAAI,CAACL,aAAa,CAACW,GAAG,CAACD,QAAQ,CAAC,EAAE;QACrC,IAAME,SAAS,GAAG,IAAIC,eAAM,CAACT,UAAU,EAAEC,MAAM,CAAC;QAChD,IAAI,CAACL,aAAa,CAACc,GAAG,CAACJ,QAAQ,EAAEE,SAAS,CAAC;MAC7C;MAEA,IAAMG,MAAM,GAAG,IAAI,CAACf,aAAa,CAACgB,GAAG,CAACN,QAAQ,CAAC;MAC/C,OAAOK,MAAM,CAAEE,cAAc,CAAC,CAAC;IACjC;EAAA;IAAAf,GAAA;IAAAR,KAAA,EAEA,SAAAwB,cAAcA,CACZd,UAAsB,EACtBC,MAAoB,EACpBc,OAA8D,EACtD;MACR,IAAMT,QAAQ,GAAG,IAAI,CAACP,WAAW,CAACC,UAAU,EAAEC,MAAM,CAAC;MAErD,IAAI,CAAC,IAAI,CAACL,aAAa,CAACW,GAAG,CAACD,QAAQ,CAAC,EAAE;QACrC,OAAO,CAAC,CAAC;MACX;MAEA,IAAMK,MAAM,GAAG,IAAI,CAACf,aAAa,CAACgB,GAAG,CAACN,QAAQ,CAAC;MAC/C,IACEK,MAAM,IACNA,MAAM,CAACK,WAAW,CAAC,CAAC,KACnBL,MAAM,CAACM,SAAS,CAAC,CAAC,IAAIN,MAAM,CAACO,QAAQ,CAACH,OAAO,CAAC,CAAC,EAChD;QACAJ,MAAM,CAACQ,eAAe,EAAE;QACxB,OAAOb,QAAQ;MACjB;MACA,OAAO,CAAC,CAAC;IACX;EAAA;IAAAR,GAAA;IAAAR,KAAA,EAEA,SAAA8B,gBAAgBA,CAACd,QAAgB,EAAE;MACjC,IAAI,IAAI,CAACV,aAAa,CAACW,GAAG,CAACD,QAAQ,CAAC,EAAE;QACpC,IAAMK,MAAM,GAAG,IAAI,CAACf,aAAa,CAACgB,GAAG,CAACN,QAAQ,CAAC;QAC/C,IAAIK,MAAM,IAAIA,MAAM,CAACM,SAAS,CAAC,CAAC,EAAE;UAChCN,MAAM,CAACQ,eAAe,EAAE;UACxB,IAAIR,MAAM,CAACQ,eAAe,KAAK,CAAC,EAAE;YAChCR,MAAM,CAACU,UAAU,CAAC,CAAC;UACrB;QACF;MACF;IACF;EAAA;AAAA", "ignoreList": []}