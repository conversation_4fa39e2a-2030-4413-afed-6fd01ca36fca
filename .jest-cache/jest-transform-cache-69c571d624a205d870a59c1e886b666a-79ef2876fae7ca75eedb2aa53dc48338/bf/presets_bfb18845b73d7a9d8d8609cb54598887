0468efda8512e64c5ee3280fb9663829
'use strict';

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.ScreenTransition = void 0;
var SwipeRight = {
  topScreenStyle: function topScreenStyle(event) {
    'worklet';

    return {
      transform: [{
        translateX: event.translationX
      }]
    };
  },
  belowTopScreenStyle: function belowTopScreenStyle(event, screenSize) {
    'worklet';

    return {
      transform: [{
        translateX: (event.translationX - screenSize.width) * 0.3
      }]
    };
  }
};
var SwipeLeft = {
  topScreenStyle: function topScreenStyle(event) {
    'worklet';

    return {
      transform: [{
        translateX: event.translationX
      }]
    };
  },
  belowTopScreenStyle: function belowTopScreenStyle(event, screenSize) {
    'worklet';

    return {
      transform: [{
        translateX: (event.translationX + screenSize.width) * 0.3
      }]
    };
  }
};
var SwipeDown = {
  topScreenStyle: function topScreenStyle(event) {
    'worklet';

    return {
      transform: [{
        translateY: event.translationY
      }]
    };
  },
  belowTopScreenStyle: function belowTopScreenStyle(event, screenSize) {
    'worklet';

    return {
      transform: [{
        translateY: (event.translationY - screenSize.height) * 0.3
      }]
    };
  }
};
var SwipeUp = {
  topScreenStyle: function topScreenStyle(event) {
    'worklet';

    return {
      transform: [{
        translateY: event.translationY
      }]
    };
  },
  belowTopScreenStyle: function belowTopScreenStyle(event, screenSize) {
    'worklet';

    return {
      transform: [{
        translateY: (event.translationY + screenSize.height) * 0.3
      }]
    };
  }
};
var TwoDimensional = {
  topScreenStyle: function topScreenStyle(event, _screenSize) {
    'worklet';

    return {
      transform: [{
        translateX: event.translationX
      }, {
        translateY: event.translationY
      }]
    };
  },
  belowTopScreenStyle: function belowTopScreenStyle(_event, _screenSize) {
    'worklet';

    return {};
  }
};
var Horizontal = {
  topScreenStyle: function topScreenStyle(event, _screenSize) {
    'worklet';

    return {
      transform: [{
        translateX: event.translationX
      }]
    };
  },
  belowTopScreenStyle: function belowTopScreenStyle(_event, _screenSize) {
    'worklet';

    return {};
  }
};
var Vertical = {
  topScreenStyle: function topScreenStyle(event, _screenSize) {
    'worklet';

    return {
      transform: [{
        translateY: event.translationY
      }]
    };
  },
  belowTopScreenStyle: function belowTopScreenStyle(_event, _screenSize) {
    'worklet';

    return {};
  }
};
var SwipeRightFade = {
  topScreenStyle: function topScreenStyle(event, screenSize) {
    'worklet';

    return {
      opacity: 1 - Math.abs(event.translationX / screenSize.width)
    };
  },
  belowTopScreenStyle: function belowTopScreenStyle(_event, _screenSize) {
    'worklet';

    return {};
  }
};
var ScreenTransition = exports.ScreenTransition = {
  SwipeRight: SwipeRight,
  SwipeLeft: SwipeLeft,
  SwipeDown: SwipeDown,
  SwipeUp: SwipeUp,
  Horizontal: Horizontal,
  Vertical: Vertical,
  TwoDimensional: TwoDimensional,
  SwipeRightFade: SwipeRightFade
};
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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