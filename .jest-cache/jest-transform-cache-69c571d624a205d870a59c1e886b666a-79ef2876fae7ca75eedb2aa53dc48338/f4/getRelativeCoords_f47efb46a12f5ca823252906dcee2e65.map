{"version": 3, "names": ["Object", "defineProperty", "exports", "value", "getRelativeCoords", "_measure", "require", "animatedRef", "absoluteX", "absoluteY", "parentCoords", "measure", "x", "pageX", "y", "pageY"], "sources": ["../../../src/platformFunctions/getRelativeCoords.ts"], "sourcesContent": [null], "mappings": "AAAA,YAAY;;AAAAA,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,KAAA;AAAA;AAAAD,OAAA,CAAAE,iBAAA,GAAAA,iBAAA;AAEZ,IAAAC,QAAA,GAAAC,OAAA;AAqBO,SAASF,iBAAiBA,CAC/BG,WAAmC,EACnCC,SAAiB,EACjBC,SAAiB,EACO;EACxB,SAAS;;EACT,IAAMC,YAAY,GAAG,IAAAC,gBAAO,EAACJ,WAAW,CAAC;EACzC,IAAIG,YAAY,KAAK,IAAI,EAAE;IACzB,OAAO,IAAI;EACb;EACA,OAAO;IACLE,CAAC,EAAEJ,SAAS,GAAGE,YAAY,CAACG,KAAK;IACjCC,CAAC,EAAEL,SAAS,GAAGC,YAAY,CAACK;EAC9B,CAAC;AACH", "ignoreList": []}