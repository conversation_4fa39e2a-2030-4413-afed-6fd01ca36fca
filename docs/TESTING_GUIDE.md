# Payment Module Testing Guide

## Tổng quan

Dự án Payment Module sử dụng một hệ thống testing toàn diện được thiết kế dựa trên cấu trúc testing của Card Module. Hướng dẫn này sẽ giúp team hi<PERSON><PERSON> c<PERSON>ch viết và chạy tests hiệu quả.

## Cấu trúc Testing

### 1. <PERSON><PERSON><PERSON> hình Jest

- **jest.config.js**: <PERSON><PERSON><PERSON> hình chính cho Jest với React Native preset
- **jest-setup.ts**: Setup global cho testing environment
- **tsconfig.spec.json**: TypeScript configuration riêng cho tests

### 2. T<PERSON><PERSON> mục __mocks__

Chứa các mock files cho:
- React Native libraries (gesture handler, safe area, async storage, etc.)
- MSB-specific modules (host-shared-module, communication-lib)
- External libraries (unistyles, keyboard controller, etc.)

### 3. Test Utilities

- **src/presentation/__test__/test-utils.tsx**: Custom render functions và helper utilities

## C<PERSON>c loại Tests

### 1. Unit Tests - Utility Functions

**Vị trí**: `src/utils/__test__/`

**Ví dụ**: `PaymentUtils.test.ts`

```typescript
import {describe, it, expect} from '@jest/globals';
import {formatCurrency, validateAmount} from '../PaymentUtils';

describe('PaymentUtils', () => {
  describe('formatCurrency', () => {
    it('should format VND currency correctly', () => {
      expect(formatCurrency(100000, 'VND')).toBe('100.000 ₫');
    });
  });
});
```

**Best Practices**:
- Test tất cả edge cases
- Sử dụng descriptive test names
- Group related tests với `describe`

### 2. Service Layer Tests

**Vị trí**: `src/data/__test__/`

**Ví dụ**: `PaymentService.test.ts`

```typescript
import {describe, it, expect, beforeEach, jest} from '@jest/globals';

describe('PaymentService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should fetch payment methods successfully', async () => {
    // Mock fetch response
    (global.fetch as jest.Mock).mockResolvedValueOnce({
      json: () => Promise.resolve({
        success: true,
        data: mockData,
      }),
      ok: true,
      status: 200,
    });

    const result = await paymentService.getPaymentMethods();
    expect(result).toEqual(expectedData);
  });
});
```

**Best Practices**:
- Mock HTTP requests với global.fetch
- Test cả success và error cases
- Verify API calls với correct parameters

### 3. Domain Layer Tests

**Vị trí**: `src/domain/__test__/`

**Ví dụ**: `ProcessPaymentUseCase.test.ts`

```typescript
describe('ProcessPaymentUseCase', () => {
  let useCase: ProcessPaymentUseCase;
  let mockRepository: jest.Mocked<PaymentRepository>;

  beforeEach(() => {
    mockRepository = {
      processPayment: jest.fn(),
      validatePaymentMethod: jest.fn(),
    };
    useCase = new ProcessPaymentUseCase(mockRepository);
  });

  it('should process payment successfully', async () => {
    mockRepository.validatePaymentMethod.mockResolvedValue(true);
    mockRepository.processPayment.mockResolvedValue(expectedResult);

    const result = await useCase.execute(validRequest);
    expect(result).toEqual(expectedResult);
  });
});
```

**Best Practices**:
- Mock dependencies với jest.Mocked
- Test business logic validation
- Verify repository method calls

### 4. Component Tests

**Vị trí**: `src/presentation/__test__/`

**Ví dụ**: `PaymentButton.test.tsx`

```typescript
import {render, screen, fireEvent} from './test-utils';
import PaymentButton from '../PaymentButton';

describe('PaymentButton', () => {
  const mockOnPress = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should render with correct amount and currency', () => {
    render(<PaymentButton amount={100000} currency="VND" onPress={mockOnPress} />);
    
    expect(screen.getByText('Pay 100.000 ₫')).toBeTruthy();
  });

  it('should call onPress when pressed', () => {
    render(<PaymentButton amount={100000} currency="VND" onPress={mockOnPress} />);
    
    fireEvent.press(screen.getByTestId('payment-button'));
    expect(mockOnPress).toHaveBeenCalledTimes(1);
  });
});
```

**Best Practices**:
- Sử dụng custom render từ test-utils
- Test user interactions
- Verify component behavior với props changes

## Scripts Testing

### Chạy Tests

```bash
# Chạy tất cả tests với coverage
npm test

# Chạy tests ở watch mode
npm run test:watch

# Chạy tests với verbose output
npm run test:debug

# Chạy tests cho CI
npm run test:ci

# Clear Jest cache
npm run test:clear-cache
```

### Coverage Requirements

- **Statements**: Tối thiểu 80%
- **Branches**: Tối thiểu 75%
- **Functions**: Tối thiểu 80%
- **Lines**: Tối thiểu 80%

## Mocking Strategies

### 1. React Native Libraries

Đã được setup sẵn trong `jest-setup.ts`:
- react-native-gesture-handler
- react-native-safe-area-context
- @react-native-async-storage/async-storage

### 2. MSB Modules

```typescript
// __mocks__/msb-host-shared-module.ts
export const MSBHttpService = {
  get: jest.fn(),
  post: jest.fn(),
};
```

### 3. External APIs

```typescript
// Mock fetch globally
global.fetch = jest.fn(() =>
  Promise.resolve({
    json: () => Promise.resolve(mockData),
    ok: true,
    status: 200,
  })
) as jest.Mock;
```

## Debugging Tests

### 1. Debug Mode

```bash
npm run test:debug -- --testNamePattern="specific test"
```

### 2. Console Logging

```typescript
it('should debug test', () => {
  console.log('Debug info:', testData);
  // Test logic
});
```

### 3. Jest Debugging

```typescript
// Add to test file
import {screen} from '@testing-library/react-native';

it('should debug component', () => {
  render(<Component />);
  screen.debug(); // Prints component tree
});
```

## Best Practices

### 1. Test Organization

- Một test file cho mỗi source file
- Group related tests với `describe`
- Sử dụng descriptive test names
- Setup và cleanup trong `beforeEach`/`afterEach`

### 2. Test Data

- Tạo mock data constants
- Sử dụng factory functions cho complex objects
- Avoid hardcoded values trong tests

### 3. Assertions

- Sử dụng specific matchers (`toEqual`, `toHaveBeenCalledWith`)
- Test cả positive và negative cases
- Verify side effects (API calls, state changes)

### 4. Performance

- Mock expensive operations
- Sử dụng `jest.clearAllMocks()` trong `beforeEach`
- Avoid unnecessary re-renders trong component tests

## Troubleshooting

### Common Issues

1. **Module not found**: Kiểm tra moduleNameMapper trong jest.config.js
2. **Transform errors**: Thêm modules vào transformIgnorePatterns
3. **Mock not working**: Verify mock placement và import order
4. **Timeout errors**: Tăng timeout trong jest.config.js

### Debug Commands

```bash
# Clear cache khi có issues
npm run test:clear-cache

# Run specific test file
npm test -- PaymentUtils.test.ts

# Run tests matching pattern
npm test -- --testNamePattern="should format currency"
```

## Kết luận

Testing infrastructure này cung cấp foundation mạnh mẽ cho việc maintain code quality. Team nên:

1. Viết tests cho tất cả new features
2. Maintain coverage requirements
3. Update tests khi refactor code
4. Review test cases trong code reviews

Để biết thêm chi tiết, tham khảo các example tests trong project.
