{"success": true, "data": {"contacts": [{"id": "contact_001", "name": "TRAN THI B", "type": "TRANSFER", "accountNumber": "************", "bankCode": "VCB", "bankName": "Vietcombank", "branchName": "District 1 Branch", "isRecent": true, "isFavorite": true, "lastUsed": "2024-07-01T10:30:00Z", "createdAt": "2024-06-15T09:00:00Z", "avatar": "https://example.com/avatars/tran_thi_b.jpg"}, {"id": "contact_002", "name": "LE VAN C", "type": "TRANSFER", "accountNumber": "************", "bankCode": "TCB", "bankName": "Techcombank", "branchName": "Tan Binh Branch", "isRecent": true, "isFavorite": false, "lastUsed": "2024-06-30T15:45:00Z", "createdAt": "2024-06-10T14:20:00Z", "avatar": "https://example.com/avatars/le_van_c.jpg"}, {"id": "contact_003", "name": "NGUYEN THI D", "type": "BILL_PAYMENT", "providerId": "evn", "providerName": "EVN - Electricity Vietnam", "customerCode": "*********", "customerName": "NGUYEN THI D", "address": "456 Le Loi, District 3, Ho Chi Minh City", "isRecent": true, "isFavorite": true, "lastUsed": "2024-07-02T08:15:00Z", "createdAt": "2024-05-20T11:30:00Z"}, {"id": "contact_004", "name": "PHAM VAN E", "type": "TRANSFER", "accountNumber": "************", "bankCode": "MSB", "bankName": "Maritime Bank", "branchName": "District 7 Branch", "isRecent": false, "isFavorite": true, "lastUsed": "2024-06-20T12:00:00Z", "createdAt": "2024-05-15T16:45:00Z", "avatar": "https://example.com/avatars/pham_van_e.jpg"}, {"id": "contact_005", "name": "Mobile Top-up - Viettel", "type": "MOBILE_TOPUP", "providerId": "viettel", "providerName": "Viettel Mobile", "phoneNumber": "0*********", "isRecent": true, "isFavorite": false, "lastUsed": "2024-07-01T18:30:00Z", "createdAt": "2024-06-25T10:15:00Z"}, {"id": "contact_006", "name": "Water Bill - SAWACO", "type": "BILL_PAYMENT", "providerId": "saigon_water", "providerName": "Saigon Water Corporation", "customerCode": "*********", "customerName": "HOANG VAN F", "address": "789 <PERSON><PERSON><PERSON>, District 1, Ho Chi Minh City", "isRecent": false, "isFavorite": true, "lastUsed": "2024-06-28T14:20:00Z", "createdAt": "2024-06-01T09:30:00Z"}, {"id": "contact_007", "name": "TRAN VAN G", "type": "TRANSFER", "accountNumber": "************", "bankCode": "ACB", "bankName": "Asia Commercial Bank", "branchName": "<PERSON>u <PERSON> Branch", "isRecent": false, "isFavorite": false, "lastUsed": "2024-06-15T11:45:00Z", "createdAt": "2024-05-10T13:20:00Z", "avatar": "https://example.com/avatars/tran_van_g.jpg"}, {"id": "contact_008", "name": "Mobile Top-up - VinaPhone", "type": "MOBILE_TOPUP", "providerId": "vinaphone", "providerName": "VinaPhone", "phoneNumber": "**********", "isRecent": false, "isFavorite": false, "lastUsed": "2024-06-10T16:00:00Z", "createdAt": "2024-05-25T12:10:00Z"}]}, "message": "Contacts retrieved successfully"}