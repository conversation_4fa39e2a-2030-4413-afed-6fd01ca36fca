{"valid": {"success": true, "data": {"isValid": true, "customerInfo": {"customerCode": "123456789", "customerName": "NGUYEN VAN A", "address": "123 Nguyen Trai, District 1, Ho Chi Minh City", "billAmount": 250000, "dueDate": "2024-08-15", "billPeriod": "07/2024", "billDetails": [{"description": "Electricity consumption", "amount": 230000, "unit": "kWh", "quantity": 150}, {"description": "Service fee", "amount": 20000, "unit": "VND", "quantity": 1}]}, "fees": {"serviceFee": 2000, "totalAmount": 252000}}, "message": "Bill validation successful"}, "invalid": {"success": false, "errors": [{"key": "invalid_customer_code", "message": "Customer code not found or invalid", "context": ["validation"]}], "message": "Bill validation failed"}, "expired": {"success": false, "errors": [{"key": "bill_expired", "message": "Bill has expired and cannot be paid", "context": ["validation"]}], "message": "Bill has expired"}, "already_paid": {"success": false, "errors": [{"key": "bill_already_paid", "message": "Bill has already been paid", "context": ["validation"]}], "message": "Bill already paid"}}