import {BILL_PAY_API, createInternalServerError, server} from '../msw-node';
import {http, HttpResponse} from 'msw';
import * as mockBillValidationResponseData from './data-sources/bill-validation.json';

export const mockResponseForValidateBill = () => {
  server.use(
    http.post(`${BILL_PAY_API}/validate`, async ({request}) => {
      const body = (await request.json()) as any;
      const {providerId, customerCode} = body;

      // Simulate validation logic
      if (customerCode === '123456789') {
        return HttpResponse.json((mockBillValidationResponseData as any).valid, {status: 200});
      } else if (customerCode === 'INVALID') {
        return HttpResponse.json((mockBillValidationResponseData as any).invalid, {status: 400});
      }

      // Default valid response
      return HttpResponse.json(
        {
          ...(mockBillValidationResponseData as any).valid,
          customerInfo: {
            ...(mockBillValidationResponseData as any).valid.customerInfo,
            customerCode,
          },
        },
        {status: 200},
      );
    }),
  );
};

export const mockResponseForValidateBillInvalid = () => {
  server.use(
    http.post(`${BILL_PAY_API}/validate`, () => {
      return HttpResponse.json(
        {
          success: false,
          error: 'Invalid customer code',
        },
        {status: 400},
      );
    }),
  );
};

export const mockServerFailureForValidateBill = () => {
  server.use(
    http.post(`${BILL_PAY_API}/validate`, () => {
      const error = createInternalServerError();
      return HttpResponse.json(error, {status: 500});
    }),
  );
};

export const mockBillValidationResponse = mockBillValidationResponseData;
