import {BILL_PAY_API, createInternalServerError, server} from '../msw-node';
import {http, HttpResponse} from 'msw';
import * as mockBillProvidersResponseRaw from './data-sources/bill-providers.json';

const mockBillProvidersResponse = (mockBillProvidersResponseRaw as any).default || mockBillProvidersResponseRaw;

export const mockResponseForGetBillProviders = () => {
  server.use(
    http.get(`${BILL_PAY_API}/providers`, () => {
      return HttpResponse.json(mockBillProvidersResponse, {status: 200});
    }),
  );
};

export const mockResponseForGetBillProvidersByCategory = (categoryId: string) => {
  server.use(
    http.get(`${BILL_PAY_API}/providers`, ({request}) => {
      const url = new URL(request.url);
      const category = url.searchParams.get('category');

      if (category === categoryId) {
        const filteredProviders = mockBillProvidersResponse.filter(
          (provider: any) => provider.categoryId === categoryId,
        );
        return HttpResponse.json(filteredProviders, {status: 200});
      }

      return HttpResponse.json(mockBillProvidersResponse, {status: 200});
    }),
  );
};

export const mockServerFailureForGetBillProviders = () => {
  server.use(
    http.get(`${BILL_PAY_API}/providers`, () => {
      const error = createInternalServerError();
      return HttpResponse.json(error, {status: 500});
    }),
  );
};

export {mockBillProvidersResponse};
