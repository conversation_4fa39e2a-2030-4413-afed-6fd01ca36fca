import {PAYMENT_API, createInternalServerError, server} from '../msw-node';
import {http, HttpResponse} from 'msw';
import * as mockSourceAccountsResponseRaw from './data-sources/source-accounts.json';

const mockSourceAccountsResponse = (mockSourceAccountsResponseRaw as any).default || mockSourceAccountsResponseRaw;

export const mockResponseForGetSourceAccounts = () => {
  server.use(
    http.get(`${PAYMENT_API}/arrangement/accounts`, () => {
      return HttpResponse.json(mockSourceAccountsResponse, {status: 200});
    }),
  );
};

export const mockServerFailureForGetSourceAccounts = () => {
  server.use(
    http.get(`${PAYMENT_API}/arrangement/accounts`, () => {
      const error = createInternalServerError();
      return HttpResponse.json(error, {status: 500});
    }),
  );
};

export {mockSourceAccountsResponse};
