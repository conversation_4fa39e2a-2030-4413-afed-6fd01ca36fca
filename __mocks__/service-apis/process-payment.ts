import {PAYMENT_API, createInternalServerError, server} from '../msw-node';
import {http, HttpResponse} from 'msw';
import * as mockPaymentResponseRaw from './data-sources/payment-response.json';

const mockPaymentResponse = (mockPaymentResponseRaw as any).default || mockPaymentResponseRaw;

export const mockResponseForProcessPayment = () => {
  server.use(
    http.post(`${PAYMENT_API}/process`, () => {
      return HttpResponse.json(mockPaymentResponse, {status: 200});
    }),
  );
};

export const mockServerFailureForProcessPayment = () => {
  server.use(
    http.post(`${PAYMENT_API}/process`, () => {
      const error = createInternalServerError();
      return HttpResponse.json(error, {status: 500});
    }),
  );
};

export const mockErrorResponseForProcessPayment = () => {
  server.use(
    http.post(`${PAYMENT_API}/process`, () => {
      return HttpResponse.json(
        {
          success: false,
          message: 'Payment processing failed',
          error: 'PAYMENT_FAILED',
        },
        {status: 400},
      );
    }),
  );
};

export {mockPaymentResponse};
