import {CARDS_API, createInternalServerError, server} from '../msw-node';
import {http, HttpResponse} from 'msw';

export const mockResponseForCreateNewPin = () => {
  server.use(
    http.patch(`${CARDS_API}/:id/pin`, () => {
      return HttpResponse.json({}, {status: 200});
    }),
  );
};

export const mockServerFailureForCreateNewPin = () => {
  server.use(
    http.patch(`${CARDS_API}/:id/pin`, () => {
      const error = createInternalServerError();
      return HttpResponse.json(error, {status: 500});
    }),
  );
};
