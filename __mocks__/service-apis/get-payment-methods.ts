import {PAYMENT_API, createInternalServerError, server} from '../msw-node';
import {http, HttpResponse} from 'msw';
import * as mockPaymentMethodsResponseRaw from './data-sources/payment-methods.json';

const mockPaymentMethodsResponse = (mockPaymentMethodsResponseRaw as any).default || mockPaymentMethodsResponseRaw;

export const mockResponseForGetPaymentMethods = () => {
  server.use(
    http.get(`${PAYMENT_API}/payment-methods`, () => {
      return HttpResponse.json(mockPaymentMethodsResponse, {status: 200});
    }),
  );
};

export const mockServerFailureForGetPaymentMethods = () => {
  server.use(
    http.get(`${PAYMENT_API}/payment-methods`, () => {
      const error = createInternalServerError();
      return HttpResponse.json(error, {status: 500});
    }),
  );
};

export {mockPaymentMethodsResponse};
