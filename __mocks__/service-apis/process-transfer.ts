import {PAYMENT_API, createInternalServerError, server} from '../msw-node';
import {http, HttpResponse} from 'msw';
import * as mockTransferResponseRaw from './data-sources/transfer-response.json';

const mockTransferResponse = (mockTransferResponseRaw as any).default || mockTransferResponseRaw;

export const mockResponseForProcessTransfer = () => {
  server.use(
    http.post(`${PAYMENT_API}/transfer/process`, () => {
      return HttpResponse.json(mockTransferResponse, {status: 200});
    }),
  );
};

export const mockServerFailureForProcessTransfer = () => {
  server.use(
    http.post(`${PAYMENT_API}/transfer/process`, () => {
      const error = createInternalServerError();
      return HttpResponse.json(error, {status: 500});
    }),
  );
};

export {mockTransferResponse};
