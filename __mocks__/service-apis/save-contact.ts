import {ADDRESS_BOOK_API, createInternalServerError, createBadRequestError, server} from '../msw-node';
import {http, HttpResponse} from 'msw';

export const mockResponseForSaveContact = () => {
  server.use(
    http.post(`${ADDRESS_BOOK_API}/contacts`, async ({request}) => {
      const body = await request.json() as any;
      const {name, type} = body;
      
      if (!name || !type) {
        const error = createBadRequestError('Name and type are required');
        return HttpResponse.json(error, {status: 400});
      }
      
      const newContact = {
        id: `contact_${Date.now()}`,
        ...body,
        createdAt: new Date().toISOString(),
        lastUsed: new Date().toISOString(),
        isRecent: true,
        isFavorite: false,
      };
      
      return HttpResponse.json({
        success: true,
        data: {
          contact: newContact,
        },
        message: 'Contact saved successfully',
      }, {status: 201});
    }),
  );
};

export const mockResponseForSaveContactDuplicate = () => {
  server.use(
    http.post(`${ADDRESS_BOOK_API}/contacts`, () => {
      const error = createBadRequestError('Contact already exists');
      return HttpResponse.json(error, {status: 400});
    }),
  );
};

export const mockServerFailureForSaveContact = () => {
  server.use(
    http.post(`${ADDRESS_BOOK_API}/contacts`, () => {
      const error = createInternalServerError();
      return HttpResponse.json(error, {status: 500});
    }),
  );
};

export const mockResponseForUpdateContact = () => {
  server.use(
    http.put(`${ADDRESS_BOOK_API}/contacts/:id`, async ({request, params}) => {
      const body = await request.json() as any;
      const {id} = params;
      
      const updatedContact = {
        id,
        ...body,
        updatedAt: new Date().toISOString(),
      };
      
      return HttpResponse.json({
        success: true,
        data: {
          contact: updatedContact,
        },
        message: 'Contact updated successfully',
      }, {status: 200});
    }),
  );
};

export const mockResponseForDeleteContact = () => {
  server.use(
    http.delete(`${ADDRESS_BOOK_API}/contacts/:id`, ({params}) => {
      const {id} = params;
      
      return HttpResponse.json({
        success: true,
        data: {
          contactId: id,
        },
        message: 'Contact deleted successfully',
      }, {status: 200});
    }),
  );
};
