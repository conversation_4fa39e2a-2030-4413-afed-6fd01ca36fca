import {PAYMENT_API, createInternalServerError, server} from '../msw-node';
import {http, HttpResponse} from 'msw';
import * as mockTransactionHistoryResponseRaw from './data-sources/transaction-history.json';

const mockTransactionHistoryResponse =
  (mockTransactionHistoryResponseRaw as any).default || mockTransactionHistoryResponseRaw;

export const mockResponseForGetTransactionHistory = () => {
  server.use(
    http.get(`${PAYMENT_API}/payment/transactions`, () => {
      return HttpResponse.json(mockTransactionHistoryResponse, {status: 200});
    }),
  );
};

export const mockServerFailureForGetTransactionHistory = () => {
  server.use(
    http.get(`${PAYMENT_API}/payment/transactions`, () => {
      const error = createInternalServerError();
      return HttpResponse.json(error, {status: 500});
    }),
  );
};

export {mockTransactionHistoryResponse};
