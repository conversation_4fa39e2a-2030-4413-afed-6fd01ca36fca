import {PAYMENT_API, createInternalServerError, server} from '../msw-node';
import {http, HttpResponse} from 'msw';
import * as mockContactsResponseRaw from './data-sources/contacts.json';

const mockContactsResponse = (mockContactsResponseRaw as any).default || mockContactsResponseRaw;

export const mockResponseForGetContacts = () => {
  server.use(
    http.get(`${PAYMENT_API}/address-book/contacts`, () => {
      return HttpResponse.json(mockContactsResponse, {status: 200});
    }),
  );
};

export const mockServerFailureForGetContacts = () => {
  server.use(
    http.get(`${PAYMENT_API}/address-book/contacts`, () => {
      const error = createInternalServerError();
      return HttpResponse.json(error, {status: 500});
    }),
  );
};

export const mockResponseForSaveContact = () => {
  server.use(
    http.post(`${PAYMENT_API}/address-book/contacts`, () => {
      return HttpResponse.json(
        {
          success: true,
          message: 'Contact saved successfully',
          data: {
            contact: {
              id: 'contact_123',
              name: 'NEW CONTACT',
              type: 'TRANSFER',
              accountNumber: '************',
              bankCode: 'MSB',
            },
          },
        },
        {status: 200},
      );
    }),
  );
};

export {mockContactsResponse};
