import {PAYMENT_API, createInternalServerError, server} from '../msw-node';
import {http, HttpResponse} from 'msw';
import * as mockTransferValidationResponseRaw from './data-sources/transfer-validation.json';

const mockTransferValidationResponse =
  (mockTransferValidationResponseRaw as any).default || mockTransferValidationResponseRaw;

export const mockResponseForValidateTransfer = () => {
  server.use(
    http.post(`${PAYMENT_API}/transfer/validate`, () => {
      return HttpResponse.json(mockTransferValidationResponse, {status: 200});
    }),
  );
};

export const mockServerFailureForValidateTransfer = () => {
  server.use(
    http.post(`${PAYMENT_API}/transfer/validate`, () => {
      const error = createInternalServerError();
      return HttpResponse.json(error, {status: 500});
    }),
  );
};

export {mockTransferValidationResponse};
