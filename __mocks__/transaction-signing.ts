// import {ConfirmationStatus} from '@data/models/transaction-signing/ConfirmationStatus';
// import {
//   TractionSigningConfirmationStatus,
//   TransactionSigningState,
// } from '@data/models/transaction-signing/TransactionSigningState';
// import {hostSharedModule} from 'msb-host-shared-module';
// import {mockTransactionStatusResponse} from './service-apis/check-transaction-status';

// export const signingDataText: TractionSigningConfirmationStatus = {
//   confirmation_status: ConfirmationStatus.CONFIRMED,
//   data: {
//     'confirmation-id': mockTransactionStatusResponse.confirmationId,
//     'confirmation-type': 'get-secret-info',
//     'confirmation-flow-external-id': '1234567890-1234-1234-1234-123456789012',
//   },
// };

// export function mockTransactionSigningSuccess() {
//   const signingResult: TransactionSigningState = {
//     status: 'success',
//     data: JSON.stringify(signingDataText),
//   };
//   (hostSharedModule.d.domainService.onTransfer as jest.Mock).mockReturnValue(signingResult);
// }

// export function mockTransactionSigningNotConfirmed() {
//   const signingResult: TransactionSigningState = {
//     status: 'success',
//     data: JSON.stringify({confirmation_status: 'NOT_CONFIRMED', data: {}}),
//   };
//   (hostSharedModule.d.domainService.onTransfer as jest.Mock).mockReturnValue(signingResult);
// }

// export function mockTransactionSigningMissingData() {
//   const signingResult: TransactionSigningState = {
//     status: 'success',
//     data: JSON.stringify({confirmation_status: ConfirmationStatus.CONFIRMED, data: {}}),
//   };
//   (hostSharedModule.d.domainService.onTransfer as jest.Mock).mockReturnValue(signingResult);
// }

// export function mockTransactionSigningThrowError() {
//   (hostSharedModule.d.domainService.onTransfer as jest.Mock).mockImplementation(() => {
//     throw new Error('Something went wrong');
//   });
// }

// export function mockBackTransactionSigning() {
//   const signingResult: TransactionSigningState = {
//     status: 'back',
//   };
//   (hostSharedModule.d.domainService.onTransfer as jest.Mock).mockReturnValue(signingResult);
// }

// export const mockErrorSigningResult: TransactionSigningState = {
//   status: 'error',
//   code: '500',
//   message: 'Internal Server Error',
// };
// export function mockErrorTransactionSigning() {
//   (hostSharedModule.d.domainService.onTransfer as jest.Mock).mockReturnValue(mockErrorSigningResult);
// }
