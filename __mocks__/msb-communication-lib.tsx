import React from 'react';

export const i18n = {
  getLocale: () => 'vi',
  t: (key: string, options?: Record<string, any>) => {
    if (!options) {
      return `i18n:${key}`;
    }

    const interpolatedKeys = Object.keys(options)
      .filter(optionKey => optionKey !== 'defaultValue')
      .map(optionKey => `${optionKey}:${options[optionKey]}`)
      .join(':');

    return [`i18n:${key}`, ...interpolatedKeys].join(':');
  },
};

export const LocaleProvider: React.FC<{
  children: React.ReactNode;
}> = ({children}) => <>{children}</>;

export const useLocale = () => ({
  locale: 'vi',
  changeLocale: () => {},
});
