import {currentTheme, defaultColor, ThemeMainType, themeMFirst} from 'msb-shared-component';
import {UnistylesRegistry} from 'react-native-unistyles';

export type AppThemes = {
  defaultTheme: ThemeMainType;
  currentApiTheme: ThemeMainType;
};

declare module 'react-native-unistyles' {
  export interface UnistylesThemes extends AppThemes {}
}

UnistylesRegistry.addThemes({
  defaultTheme: currentTheme(defaultColor),
  currentApiTheme: currentTheme(themeMFirst),
}).addConfig({
  initialTheme: 'defaultTheme',
});
