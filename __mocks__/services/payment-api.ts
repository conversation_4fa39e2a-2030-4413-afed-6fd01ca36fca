// import {http, HttpResponse} from 'msw';

// // Mock data sources
// export const mockPaymentData = {
//   paymentMethods: [
//     {
//       id: '1',
//       name: 'Credit Card',
//       type: 'CREDIT_CARD',
//       isActive: true,
//     },
//     {
//       id: '2',
//       name: 'Bank Transfer',
//       type: 'BANK_TRANSFER',
//       isActive: true,
//     },
//   ],
//   transactions: [
//     {
//       id: 'txn_001',
//       amount: 100000,
//       currency: 'VND',
//       status: 'SUCCESS',
//       createdAt: '2024-01-01T00:00:00Z',
//     },
//   ],
// };

// // Payment API handlers
// export const paymentHandlers = [
//   // Get payment methods
//   http.get('*/api/payment/methods', () => {
//     return HttpResponse.json({
//       success: true,
//       data: mockPaymentData.paymentMethods,
//       message: 'Success',
//     });
//   }),

//   // Get payment transactions
//   http.get('*/api/payment/transactions', () => {
//     return HttpResponse.json({
//       success: true,
//       data: mockPaymentData.transactions,
//       message: 'Success',
//     });
//   }),

//   // Create payment
//   http.post('*/api/payment/create', async ({request}) => {
//     const body = await request.json();
//     return HttpResponse.json({
//       success: true,
//       data: {
//         transactionId: 'txn_new_001',
//         status: 'PENDING',
//         ...body,
//       },
//       message: 'Payment created successfully',
//     });
//   }),

//   // Process payment
//   http.post('*/api/payment/process', async ({request}) => {
//     const body = await request.json();
//     return HttpResponse.json({
//       success: true,
//       data: {
//         transactionId: body.transactionId || 'txn_001',
//         status: 'SUCCESS',
//         processedAt: new Date().toISOString(),
//       },
//       message: 'Payment processed successfully',
//     });
//   }),
// ];
