import {ToastActions, ToastProps, ToastType} from 'msb-host-shared-module';
import {MSBIcon, MSBIconSize, MSBTextBase, useMSBStyles} from 'msb-shared-component';
import React, {RefAttributes, useImperativeHandle, useState} from 'react';
import {View} from 'react-native';
import * as Animatable from 'react-native-animatable';
import {makeStyles} from './style';

const MSBToast = ({ref}: RefAttributes<ToastActions>) => {
  const {
    styles,
    theme: {Typography},
  } = useMSBStyles(makeStyles);
  const [visible, setVisible] = useState(false);
  const [message, setMessage] = useState('');
  const [type, setType] = useState<ToastType>(ToastType.SUCCESS);

  const showToast = (data?: ToastProps) => {
    setMessage(data?.message ?? '');
    setType(data?.type ?? ToastType.SUCCESS);
    setVisible(true);

    setTimeout(() => {
      setVisible(false);
    }, data?.duration ?? 3000);
  };

  const show = (data?: ToastProps) => {
    showToast(data);
  };

  const hide = () => {
    setVisible(false);
  };

  useImperativeHandle(ref, () => ({
    show,
    hide,
  }));

  const renderIcon = () => {
    if (type === ToastType.ERROR) {
      return 'toast-cancel-clear-circle';
    }
    return 'toast_check_circle';
  };

  if (!visible) {
    return null;
  }

  return (
    <Animatable.View
      testID="msb.toast"
      animation="fadeInLeft"
      duration={150}
      style={[styles.toastContainer, styles[type]]}>
      <MSBIcon icon={renderIcon()} iconSize={MSBIconSize.SIZE_24} />

      <View style={styles.txtContainer}>
        <MSBTextBase numberOfLines={2} content={message} type={Typography?.base_medium} style={styles.text} />
      </View>

      <MSBIcon onIconClick={hide} icon={'tone-close'} iconSize={MSBIconSize.SIZE_24} />
    </Animatable.View>
  );
};

export default MSBToast;
