import {createMSBStyleSheet} from 'msb-shared-component';

export const makeStyles = createMSBStyleSheet(() => {
  return {
    error: {
      backgroundColor: '#EF4444',
    },
    success: {
      backgroundColor: '#10B981',
    },
    text: {
      color: '#FFFFFF',
      width: '90%',
    },
    toastContainer: {
      alignItems: 'center',
      borderRadius: 8,
      bottom: 50,
      flexDirection: 'row',
      gap: 8,
      maxHeight: 96,
      left: 16,
      paddingHorizontal: 16,
      paddingVertical: 12,
      position: 'absolute',
      right: 16,
    },
    txtContainer: {
      flex: 1,
    },
  };
});
