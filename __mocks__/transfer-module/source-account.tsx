// // import {useErrorMessageTranslation} from '@presentation/hooks/useErrorMessageTranslation';
// // import {formatMoney} from '@utils/Utils';
// import React, {useCallback} from 'react';
// import {FieldPath, FieldValues, useController} from 'react-hook-form';
// import {Text, View} from 'react-native';
// import FormatUtils from '../../src/utils/FormatUtils';

// interface SourceAccountProps<T extends FieldValues> extends FormControl<T> {}

// interface FormAdditionalInformation {
//   sourceAccount: SourceAccountModel | null;
// }

// const SourceAccount = ({testID, control, nameTrigger, trigger}: SourceAccountProps<FormAdditionalInformation>) => {
//   const {
//     field,
//     fieldState: {error},
//   } = useController({
//     control,
//     name: nameTrigger as FieldPath<FormAdditionalInformation>,
//   });
//   const message = useErrorMessageTranslation(error?.message);

//   const onSelectAccount = useCallback(
//     (value: SourceAccountModel) => {
//       field.onChange(value);
//       trigger?.(nameTrigger);
//     },
//     [field],
//   );
//   return React.cloneElement(
//     <View testID="tm.mock-source-account">
//       <Text>{field.value?.BBAN}</Text>
//       <Text>{field.value?.bankAlias}</Text>
//       <Text>{field.value?.availableBalance ? FormatUtils.formatMoney(field.value.availableBalance) : ''}</Text>
//       <Text>{message}</Text>
//     </View>,
//     {
//       testId: testID,
//       onSelectAccount: onSelectAccount,
//     },
//   );
// };

// export default SourceAccount;
