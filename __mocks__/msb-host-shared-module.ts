/**
 * <PERSON><PERSON> dụng http client để đảm bảo vẫn sử dụng fetch api và msw để mock api
 */
class HttpClient {
  private static instance: HttpClient;
  private defaultHeaders: Record<string, string>;

  private constructor() {
    this.defaultHeaders = {
      'Content-Type': 'application/json',
    };
  }

  public static getInstance(): HttpClient {
    if (!HttpClient.instance) {
      HttpClient.instance = new HttpClient();
    }
    return HttpClient.instance;
  }

  public setDefaultHeaders(headers: Record<string, string>): void {
    this.defaultHeaders = {...this.defaultHeaders, ...headers};
  }

  private async request(
    url: string,
    options: RequestInit = {},
    __?: number,
    ___: number = 0,
    ____: number = 1000,
  ): Promise<Response> {
    return await fetch(url, options);
  }

  public get(
    url: string,
    options?: RequestInit,
    timeout?: number,
    retryCount?: number,
    retryDelay?: number,
  ): Promise<Response> {
    return this.request(url, {...options, method: 'GET'}, timeout, retryCount, retryDelay);
  }

  public post(
    url: string,
    body: any,
    options?: RequestInit,
    timeout?: number,
    retryCount?: number,
    retryDelay?: number,
  ): Promise<Response> {
    return this.request(
      url,
      {
        ...options,
        method: 'POST',
        body: JSON.stringify(body),
      },
      timeout,
      retryCount,
      retryDelay,
    );
  }

  public put(
    url: string,
    body: any,
    options?: RequestInit,
    timeout?: number,
    retryCount?: number,
    retryDelay?: number,
  ): Promise<Response> {
    return this.request(
      url,
      {
        ...options,
        method: 'PUT',
        body: JSON.stringify(body),
      },
      timeout,
      retryCount,
      retryDelay,
    );
  }

  public delete(
    url: string,
    options?: RequestInit,
    timeout?: number,
    retryCount?: number,
    retryDelay?: number,
  ): Promise<Response> {
    return this.request(url, {...options, method: 'DELETE'}, timeout, retryCount, retryDelay);
  }

  public patch(
    url: string,
    body: any,
    options?: RequestInit,
    timeout?: number,
    retryCount?: number,
    retryDelay?: number,
  ): Promise<Response> {
    return this.request(
      url,
      {
        ...options,
        method: 'PATCH',
        body: JSON.stringify(body),
      },
      timeout,
      retryCount,
      retryDelay,
    );
  }

  public head(
    url: string,
    options?: RequestInit,
    timeout?: number,
    retryCount?: number,
    retryDelay?: number,
  ): Promise<Response> {
    return this.request(url, {...options, method: 'HEAD'}, timeout, retryCount, retryDelay);
  }

  public options(
    url: string,
    options?: RequestInit,
    timeout?: number,
    retryCount?: number,
    retryDelay?: number,
  ): Promise<Response> {
    return this.request(url, {...options, method: 'OPTIONS'}, timeout, retryCount, retryDelay);
  }
}

export const hostSharedModule = {
  d: {
    domainService: {
      showPopup: jest.fn().mockImplementation(props => {
        global._MSB_TEST_POPUP?.current?.show(props);
      }),
      hidePopup: jest.fn().mockImplementation(() => {
        global._MSB_TEST_POPUP?.current?.hide();
      }),
      showToast: jest.fn().mockImplementation(props => {
        global._MSB_TEST_TOAST?.current?.show(props);
      }),
      hideToast: jest.fn().mockImplementation(() => {
        global._MSB_TEST_TOAST?.current?.hide();
      }),
      showBottomSheet: jest.fn().mockImplementation(props => {
        global._MSB_TEST_BOTTOM_SHEET?.current?.show?.(props);
      }),
      hideBottomSheet: jest.fn().mockImplementation(() => {
        global._MSB_TEST_BOTTOM_SHEET?.current?.hide?.();
      }),
      addSpinnerRequest: jest.fn(),
      addSpinnerCompleted: jest.fn(),
      onTransfer: jest.fn().mockReturnValue({
        status: 'success',
        data: JSON.stringify({
          confirmation_status: 'confirmed',
          data: {
            'confirmation-id': '1234567890-1234-1234-1234-123456789012',
            'confirmation-type': 'get-secret-info',
            'confirmation-flow-external-id': '1234567890-1234-1234-1234-123456789012',
          },
        }),
      }),
      getImageUrlByTheme: jest.fn().mockReturnValue(''),
      undevelopedFeature: jest.fn(),
    },
    httpClient: HttpClient.getInstance(),
  },
};
export const PopupProps = {};
export const ToastType = {};
export const BottomSheetProps = {};
export const BottomSheetActions = {};
export const useHostInjection = jest.fn().mockReturnValue({
  locale: 'vi',
});
