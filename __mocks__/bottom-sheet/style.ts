import {createMSBStyleSheet, SizeBottomSheet} from 'msb-shared-component';

export const styleSheet = createMSBStyleSheet(({ColorGlobal, ColorBottomSheet, SizeAlias, SizeGlobal}) => {
  return {
    container: {
      flex: 1,
      shadowOffset: {
        width: 0,
        height: -4,
      },
      shadowOpacity: 0.25,
      shadowRadius: 4,
      elevation: 4,
      borderTopLeftRadius: 16,
      borderTopRightRadius: 16,
      backgroundColor: ColorGlobal.NeutralWhite,
    },
    contentContainer: {
      backgroundColor: ColorGlobal.NeutralWhite,
      borderTopLeftRadius: 16,
      borderTopRightRadius: 16,
    },
    headerContainer: {
      alignItems: 'center',
      backgroundColor: ColorBottomSheet.SurfaceDefault,
      borderBottomColor: ColorBottomSheet.BorderDefault,
      borderBottomWidth: SizeBottomSheet.BorderStroke,
      flexDirection: 'row',
      borderTopLeftRadius: SizeAlias.Radius4,
      borderTopRightRadius: SizeAlias.Radius4,
      height: 56,
      justifyContent: 'space-between',
      paddingHorizontal: 16,
    },
    titleHeader: {
      flex: 1,
      marginRight: 16,
    },
    childrenContainer: {
      width: '100%',
    },
    buttonContainer: {
      paddingHorizontal: SizeGlobal.Size600,
      paddingBottom: SizeGlobal.Size600,
      paddingTop: SizeGlobal.Size400,
    },
    buttonHorizontal: {
      flexDirection: 'row',
      justifyContent: 'space-between',
    },
  };
});
