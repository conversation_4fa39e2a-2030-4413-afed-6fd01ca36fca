import {createMSBStyleSheet} from 'msb-shared-component';

export const styleSheet = createMSBStyleSheet(() => ({
  container: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 16,
  },
  contentWrap: {
    gap: 16,
    paddingTop: 4,
    paddingBottom: 8,
  },
  contentContainer: {},
  errorContainer: {
    marginTop: 8,
  },
  iconContainer: {
    alignItems: 'center',
    marginBottom: 8,
  },
  txtContent: {
    color: '#374151',
    fontSize: 16,
    fontWeight: '400',
    textAlign: 'center',
  },
  txtErrorCode: {
    color: '#EF4444',
    fontSize: 16,
    fontWeight: '400',
    textAlign: 'center',
  },
  txtTitle: {
    color: '#111827',
    fontSize: 18,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  btn: {
    marginTop: 16,
  },
}));
