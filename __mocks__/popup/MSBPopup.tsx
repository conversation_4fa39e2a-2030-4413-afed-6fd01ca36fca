import {PopupActions, PopupProps} from 'msb-host-shared-module';
import {MSBGroupButton, MSBTextBase, useMSBStyles} from 'msb-shared-component';
import React, {RefAttributes, useImperativeHandle, useState, useEffect, useRef} from 'react';
import {View} from 'react-native';
import Modal from 'react-native-modal';
import {styleSheet} from './style';

export const MSB_POPUP_CANCEL_TEST_ID = 'msb-popup-cancel';
export const MSB_POPUP_CONFIRM_TEST_ID = 'msb-popup-confirm';

const MSBPopup = ({ref}: RefAttributes<PopupActions>) => {
  const {styles, theme} = useMSBStyles(styleSheet);
  const modalRef = useRef<Modal>(null);
  const [data, setData] = useState<PopupProps & {onConfirmAfterAnimation?: () => void}>();
  const [defaultContent] = useState(<></>);

  const show = (dataShow?: PopupProps) => {
    setData(dataShow as PopupProps);
  };

  const hide = () => {
    setData(undefined);
  };

  useEffect(() => {
    // Mock implementation - no need to call open
  }, [data]);

  useImperativeHandle(ref, () => ({
    show,
    hide,
  }));

  const _onClose = () => {
    data?.onCancel?.();
    if (!data?.isKeepPopupWhenCancel) {
      hide();
    }
  };

  const _onConfirm = () => {
    data?.onConfirm?.();
    if (!data?.isKeepPopupWhenConfirm) {
      data?.onConfirmAfterAnimation?.();
      setData(undefined);
    }
  };

  const renderIcon = () => {
    return <View style={styles.iconContainer} />;
  };

  const renderTitle = () => {
    return <MSBTextBase content={data?.title} type={theme.Typography?.title_bold} style={styles.txtTitle} />;
  };

  const renderContent = () => {
    if (data?.content || data?.contentCustom) {
      return (
        <View style={styles.contentContainer}>
          {data?.content ? <MSBTextBase content={data?.content} style={styles.txtContent} /> : data?.contentCustom}
        </View>
      );
    }

    return defaultContent;
  };

  const renderErrorContent = () => {
    if (data?.errorCode) {
      return (
        <View style={styles.errorContainer}>
          <MSBTextBase content={`Error: ${data.errorCode}`} style={styles.txtErrorCode} />
        </View>
      );
    }
    return null;
  };

  if (!data) {
    return null;
  }

  return (
    <Modal
      ref={modalRef}
      testID="msb.popup"
      isVisible={!!data}
      backdropColor={theme.ColorGlobal?.Overlay60 || 'rgba(0,0,0,0.6)'}
      backdropOpacity={0.6}
      animationIn="slideInUp"
      useNativeDriver={true}
      onBackdropPress={() => {
        if (data?.isBackdropPress) {
          hide();
        }
      }}
      avoidKeyboard={true}
      hideModalContentWhileAnimating>
      <View style={styles.container}>
        <View style={styles.contentWrap}>
          {renderIcon()}
          {renderTitle()}
          {renderContent()}
          {renderErrorContent()}
        </View>
        <MSBGroupButton
          testIDClose={MSB_POPUP_CANCEL_TEST_ID}
          testIDConfirm={MSB_POPUP_CONFIRM_TEST_ID}
          confirmBtnText={data?.confirmBtnText}
          cancelBtnText={data?.cancelBtnText}
          verticalAlign={data?.verticalAlign}
          onConfirm={_onConfirm}
          onClose={_onClose}
          style={styles.btn}
        />
      </View>
    </Modal>
  );
};

export default MSBPopup;
