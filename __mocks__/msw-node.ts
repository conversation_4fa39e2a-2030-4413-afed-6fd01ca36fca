// import {ResponseErrors} from '@core/ResponseErrors';
import {http, HttpResponse} from 'msw';
import {setupServer} from 'msw/node';
import {BaseResponse} from '../src/core/BaseResponse';

export const createInternalServerError = (): BaseResponse<any> => {
  return {
    message: 'Internal Server Error',
    errors: [
      {
        key: 'internal_server_error',
        message: 'Internal Server Error',
        context: ['context'],
      },
    ],
  };
};

const BASE_API_URL = 'https://gateway.msb.com.vn/api';
export const CARD_JOURNEY_API = `${BASE_API_URL}/card-journey/client-api/v1`;
export const CARDS_API = `${CARD_JOURNEY_API}/cards`;
export const ARRANGEMENT_API = `${BASE_API_URL}/arrangement-manager-extension/client-api/v1/arrangements`;
export const ADDRESS_BOOK_API = `${BASE_API_URL}/address-book/client-api/v1`;
export const BILL_PAY_API = `${BASE_API_URL}/payment/client-api/v1/bill-pay`;
export const PAYMENT_API = `${BASE_API_URL}/payment/client-api/v1`;

// Define request handlers and response resolvers for random user API.
// By default, we always return the happy path response.
const handlers = [
  http.get(`${BASE_API_URL}/*`, () => {
    return HttpResponse.json();
  }),
];

export const server = setupServer(...handlers);
export {http, HttpResponse};
